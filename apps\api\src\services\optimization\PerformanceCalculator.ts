/**
 * Performance Calculator
 * 
 * @fileoverview Comprehensive performance metrics calculation for strategy optimization
 * Includes risk-adjusted returns, drawdown analysis, and statistical measures
 */

import type { PerformanceMetrics } from '@golddaddy/types/optimization';

// ===== Trade Data Interfaces =====

interface Trade {
  entryTime: Date;
  exitTime: Date;
  type: 'buy' | 'sell';
  entryPrice: number;
  exitPrice: number;
  quantity: number;
  profit: number;
  commission: number;
  swap?: number;
  duration: number; // in hours
}

interface EquityCurvePoint {
  timestamp: Date;
  equity: number;
  drawdown: number;
  runningReturn: number;
}

interface PerformanceAnalysis {
  metrics: PerformanceMetrics;
  equityCurve: EquityCurvePoint[];
  tradeStatistics: {
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
    consecutiveWins: number;
    consecutiveLosses: number;
    largestWin: number;
    largestLoss: number;
    averageWinAmount: number;
    averageLossAmount: number;
    averageTradeDuration: number;
    profitFactorComponents: {
      grossProfit: number;
      grossLoss: number;
    };
  };
  riskyMetrics: {
    valueAtRisk95: number;
    valueAtRisk99: number;
    conditionalValueAtRisk95: number;
    ulcerIndex: number;
    gainToPainRatio: number;
    recoveryFactor: number;
    lakePlatooRatio: number;
  };
  periods: {
    daily: PerformanceMetrics;
    weekly: PerformanceMetrics;
    monthly: PerformanceMetrics;
  };
}

// ===== Performance Calculator Class =====

export class PerformanceCalculator {
  private riskFreeRate: number;
  private tradingDaysPerYear: number;

  constructor(riskFreeRate = 0.02, tradingDaysPerYear = 252) {
    this.riskFreeRate = riskFreeRate;
    this.tradingDaysPerYear = tradingDaysPerYear;
  }

  // ===== Main Calculation Methods =====

  /**
   * Calculate comprehensive performance metrics from trade history
   */
  public calculatePerformance(
    trades: Trade[],
    initialCapital: number,
    startDate: Date,
    endDate: Date
  ): PerformanceAnalysis {
    
    // Build equity curve
    const equityCurve = this.buildEquityCurve(trades, initialCapital, startDate, endDate);
    
    // Calculate basic metrics
    const basicMetrics = this.calculateBasicMetrics(trades, equityCurve, initialCapital);
    
    // Calculate advanced risk metrics
    const riskyMetrics = this.calculateAdvancedRiskMetrics(equityCurve, trades);
    
    // Calculate trade statistics
    const tradeStatistics = this.calculateTradeStatistics(trades);
    
    // Calculate period-based metrics
    const periods = this.calculatePeriodMetrics(equityCurve, startDate, endDate);

    return {
      metrics: basicMetrics,
      equityCurve,
      tradeStatistics,
      riskyMetrics,
      periods
    };
  }

  /**
   * Calculate basic performance metrics
   */
  public calculateBasicMetrics(
    trades: Trade[],
    equityCurve: EquityCurvePoint[],
    initialCapital: number
  ): PerformanceMetrics {
    
    if (trades.length === 0 || equityCurve.length === 0) {
      return this.getEmptyMetrics();
    }

    const finalEquity = equityCurve[equityCurve.length - 1].equity;
    const totalReturn = (finalEquity - initialCapital) / initialCapital;
    
    // Calculate time period
    const startDate = equityCurve[0].timestamp;
    const endDate = equityCurve[equityCurve.length - 1].timestamp;
    const totalDays = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
    const totalYears = totalDays / 365;

    // Calculate returns series for volatility calculation
    const returns = this.calculateReturns(equityCurve);
    const volatility = this.calculateVolatility(returns);
    
    // Annualize metrics
    const annualizedReturn = totalYears > 0 ? Math.pow(1 + totalReturn, 1 / totalYears) - 1 : totalReturn;
    const annualizedVolatility = volatility * Math.sqrt(this.tradingDaysPerYear);
    
    // Risk-adjusted metrics
    const sharpeRatio = annualizedVolatility > 0 
      ? (annualizedReturn - this.riskFreeRate) / annualizedVolatility 
      : 0;
    
    const sortinoRatio = this.calculateSortinoRatio(returns, annualizedReturn);
    const calmarRatio = this.calculateCalmarRatio(annualizedReturn, equityCurve);
    
    // Trade-based metrics
    const winningTrades = trades.filter(t => t.profit > 0);
    const losingTrades = trades.filter(t => t.profit < 0);
    const winRate = trades.length > 0 ? winningTrades.length / trades.length : 0;
    
    const grossProfit = winningTrades.reduce((sum, t) => sum + t.profit, 0);
    const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.profit, 0));
    const profitFactor = grossLoss > 0 ? grossProfit / grossLoss : grossProfit > 0 ? Infinity : 0;
    
    // Drawdown analysis
    const maxDrawdown = this.calculateMaxDrawdown(equityCurve);
    const averageTradeReturn = trades.length > 0 
      ? trades.reduce((sum, t) => sum + t.profit, 0) / trades.length / initialCapital
      : 0;

    return {
      totalReturn: totalReturn * 100, // Convert to percentage
      sharpeRatio,
      profitFactor,
      winRate: winRate * 100, // Convert to percentage
      maxDrawdown: maxDrawdown * 100, // Convert to percentage
      tradeCount: trades.length,
      averageTradeReturn: averageTradeReturn * 100, // Convert to percentage
      volatility: annualizedVolatility * 100, // Convert to percentage
      calmarRatio,
      sortinoRatio
    };
  }

  // ===== Equity Curve Construction =====

  /**
   * Build equity curve from trade history
   */
  private buildEquityCurve(
    trades: Trade[],
    initialCapital: number,
    startDate: Date,
    endDate: Date
  ): EquityCurvePoint[] {
    
    const equityCurve: EquityCurvePoint[] = [];
    let currentEquity = initialCapital;
    let peakEquity = initialCapital;
    
    // Sort trades by exit time
    const sortedTrades = [...trades].sort((a, b) => a.exitTime.getTime() - b.exitTime.getTime());
    
    // Add initial point
    equityCurve.push({
      timestamp: new Date(startDate),
      equity: currentEquity,
      drawdown: 0,
      runningReturn: 0
    });
    
    // Process each trade
    for (const trade of sortedTrades) {
      currentEquity += trade.profit - trade.commission - (trade.swap || 0);
      
      // Update peak equity
      if (currentEquity > peakEquity) {
        peakEquity = currentEquity;
      }
      
      // Calculate drawdown
      const drawdown = peakEquity > 0 ? (peakEquity - currentEquity) / peakEquity : 0;
      const runningReturn = (currentEquity - initialCapital) / initialCapital;
      
      equityCurve.push({
        timestamp: new Date(trade.exitTime),
        equity: currentEquity,
        drawdown,
        runningReturn
      });
    }
    
    // Add final point if no trades at end date
    if (equityCurve.length === 1 || equityCurve[equityCurve.length - 1].timestamp < endDate) {
      const lastPoint = equityCurve[equityCurve.length - 1];
      equityCurve.push({
        timestamp: new Date(endDate),
        equity: lastPoint.equity,
        drawdown: lastPoint.drawdown,
        runningReturn: lastPoint.runningReturn
      });
    }
    
    return equityCurve;
  }

  // ===== Return Calculations =====

  /**
   * Calculate return series from equity curve
   */
  private calculateReturns(equityCurve: EquityCurvePoint[]): number[] {
    const returns: number[] = [];
    
    for (let i = 1; i < equityCurve.length; i++) {
      const prevEquity = equityCurve[i - 1].equity;
      const currentEquity = equityCurve[i].equity;
      
      if (prevEquity > 0) {
        const returnValue = (currentEquity - prevEquity) / prevEquity;
        returns.push(returnValue);
      }
    }
    
    return returns;
  }

  /**
   * Calculate volatility from returns
   */
  private calculateVolatility(returns: number[]): number {
    if (returns.length < 2) return 0;
    
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / (returns.length - 1);
    
    return Math.sqrt(variance);
  }

  // ===== Risk-Adjusted Metrics =====

  /**
   * Calculate Sortino ratio
   */
  private calculateSortinoRatio(returns: number[], annualizedReturn: number): number {
    if (returns.length === 0) return 0;
    
    // Calculate downside deviation
    const downsideReturns = returns.filter(r => r < 0);
    if (downsideReturns.length === 0) return Infinity;
    
    const downsideVariance = downsideReturns.reduce((sum, r) => sum + Math.pow(r, 2), 0) / downsideReturns.length;
    const downsideDeviation = Math.sqrt(downsideVariance) * Math.sqrt(this.tradingDaysPerYear);
    
    return downsideDeviation > 0 ? (annualizedReturn - this.riskFreeRate) / downsideDeviation : 0;
  }

  /**
   * Calculate Calmar ratio
   */
  private calculateCalmarRatio(annualizedReturn: number, equityCurve: EquityCurvePoint[]): number {
    const maxDrawdown = this.calculateMaxDrawdown(equityCurve);
    return maxDrawdown > 0 ? annualizedReturn / maxDrawdown : annualizedReturn > 0 ? Infinity : 0;
  }

  /**
   * Calculate maximum drawdown
   */
  private calculateMaxDrawdown(equityCurve: EquityCurvePoint[]): number {
    if (equityCurve.length === 0) return 0;
    
    let maxDrawdown = 0;
    
    for (const point of equityCurve) {
      if (point.drawdown > maxDrawdown) {
        maxDrawdown = point.drawdown;
      }
    }
    
    return maxDrawdown;
  }

  // ===== Advanced Risk Metrics =====

  /**
   * Calculate advanced risk metrics
   */
  private calculateAdvancedRiskMetrics(
    equityCurve: EquityCurvePoint[],
    trades: Trade[]
  ): {
    valueAtRisk95: number;
    valueAtRisk99: number;
    conditionalValueAtRisk95: number;
    ulcerIndex: number;
    gainToPainRatio: number;
    recoveryFactor: number;
    lakePlatooRatio: number;
  } {
    
    const returns = this.calculateReturns(equityCurve);
    
    // Value at Risk calculations
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const var95Index = Math.floor(sortedReturns.length * 0.05);
    const var99Index = Math.floor(sortedReturns.length * 0.01);
    
    const valueAtRisk95 = sortedReturns.length > 0 ? Math.abs(sortedReturns[var95Index] || 0) * 100 : 0;
    const valueAtRisk99 = sortedReturns.length > 0 ? Math.abs(sortedReturns[var99Index] || 0) * 100 : 0;
    
    // Conditional Value at Risk (Expected Shortfall)
    const tailReturns = sortedReturns.slice(0, var95Index + 1);
    const conditionalValueAtRisk95 = tailReturns.length > 0 
      ? Math.abs(tailReturns.reduce((sum, r) => sum + r, 0) / tailReturns.length) * 100
      : 0;
    
    // Ulcer Index
    const ulcerIndex = this.calculateUlcerIndex(equityCurve);
    
    // Gain-to-Pain Ratio
    const gainToPainRatio = this.calculateGainToPainRatio(returns);
    
    // Recovery Factor
    const recoveryFactor = this.calculateRecoveryFactor(equityCurve, trades);
    
    // Lake Platoo Ratio (time in drawdown vs. time at peak)
    const lakePlatooRatio = this.calculateLakePlatooRatio(equityCurve);
    
    return {
      valueAtRisk95,
      valueAtRisk99,
      conditionalValueAtRisk95,
      ulcerIndex,
      gainToPainRatio,
      recoveryFactor,
      lakePlatooRatio
    };
  }

  /**
   * Calculate Ulcer Index
   */
  private calculateUlcerIndex(equityCurve: EquityCurvePoint[]): number {
    if (equityCurve.length === 0) return 0;
    
    const sumSquaredDrawdowns = equityCurve.reduce((sum, point) => 
      sum + Math.pow(point.drawdown, 2), 0
    );
    
    return Math.sqrt(sumSquaredDrawdowns / equityCurve.length) * 100;
  }

  /**
   * Calculate Gain-to-Pain Ratio
   */
  private calculateGainToPainRatio(returns: number[]): number {
    if (returns.length === 0) return 0;
    
    const totalGain = returns.filter(r => r > 0).reduce((sum, r) => sum + r, 0);
    const totalPain = Math.abs(returns.filter(r => r < 0).reduce((sum, r) => sum + r, 0));
    
    return totalPain > 0 ? totalGain / totalPain : totalGain > 0 ? Infinity : 0;
  }

  /**
   * Calculate Recovery Factor
   */
  private calculateRecoveryFactor(equityCurve: EquityCurvePoint[], trades: Trade[]): number {
    if (trades.length === 0) return 0;
    
    const totalProfit = trades.reduce((sum, t) => sum + t.profit, 0);
    const maxDrawdownAmount = this.calculateMaxDrawdownAmount(equityCurve);
    
    return maxDrawdownAmount > 0 ? totalProfit / maxDrawdownAmount : totalProfit > 0 ? Infinity : 0;
  }

  /**
   * Calculate maximum drawdown in absolute terms
   */
  private calculateMaxDrawdownAmount(equityCurve: EquityCurvePoint[]): number {
    if (equityCurve.length === 0) return 0;
    
    let peak = equityCurve[0].equity;
    let maxDrawdownAmount = 0;
    
    for (const point of equityCurve) {
      if (point.equity > peak) {
        peak = point.equity;
      }
      
      const drawdownAmount = peak - point.equity;
      if (drawdownAmount > maxDrawdownAmount) {
        maxDrawdownAmount = drawdownAmount;
      }
    }
    
    return maxDrawdownAmount;
  }

  /**
   * Calculate Lake Platoo Ratio
   */
  private calculateLakePlatooRatio(equityCurve: EquityCurvePoint[]): number {
    if (equityCurve.length === 0) return 0;
    
    let timeInDrawdown = 0;
    let timeAtPeak = 0;
    
    for (const point of equityCurve) {
      if (point.drawdown > 0.001) { // 0.1% threshold
        timeInDrawdown++;
      } else {
        timeAtPeak++;
      }
    }
    
    return timeAtPeak > 0 ? timeInDrawdown / timeAtPeak : timeInDrawdown > 0 ? Infinity : 0;
  }

  // ===== Trade Statistics =====

  /**
   * Calculate detailed trade statistics
   */
  private calculateTradeStatistics(trades: Trade[]): {
    totalTrades: number;
    winningTrades: number;
    losingTrades: number;
    consecutiveWins: number;
    consecutiveLosses: number;
    largestWin: number;
    largestLoss: number;
    averageWinAmount: number;
    averageLossAmount: number;
    averageTradeDuration: number;
    profitFactorComponents: {
      grossProfit: number;
      grossLoss: number;
    };
  } {
    
    if (trades.length === 0) {
      return {
        totalTrades: 0,
        winningTrades: 0,
        losingTrades: 0,
        consecutiveWins: 0,
        consecutiveLosses: 0,
        largestWin: 0,
        largestLoss: 0,
        averageWinAmount: 0,
        averageLossAmount: 0,
        averageTradeDuration: 0,
        profitFactorComponents: {
          grossProfit: 0,
          grossLoss: 0
        }
      };
    }

    const winningTrades = trades.filter(t => t.profit > 0);
    const losingTrades = trades.filter(t => t.profit < 0);
    
    const largestWin = winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.profit)) : 0;
    const largestLoss = losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.profit)) : 0;
    
    const averageWinAmount = winningTrades.length > 0 
      ? winningTrades.reduce((sum, t) => sum + t.profit, 0) / winningTrades.length
      : 0;
      
    const averageLossAmount = losingTrades.length > 0
      ? losingTrades.reduce((sum, t) => sum + t.profit, 0) / losingTrades.length
      : 0;
    
    const averageTradeDuration = trades.reduce((sum, t) => sum + t.duration, 0) / trades.length;
    
    // Calculate consecutive wins/losses
    const { consecutiveWins, consecutiveLosses } = this.calculateConsecutiveStats(trades);
    
    const grossProfit = winningTrades.reduce((sum, t) => sum + t.profit, 0);
    const grossLoss = Math.abs(losingTrades.reduce((sum, t) => sum + t.profit, 0));
    
    return {
      totalTrades: trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      consecutiveWins,
      consecutiveLosses,
      largestWin,
      largestLoss,
      averageWinAmount,
      averageLossAmount,
      averageTradeDuration,
      profitFactorComponents: {
        grossProfit,
        grossLoss
      }
    };
  }

  /**
   * Calculate consecutive wins and losses
   */
  private calculateConsecutiveStats(trades: Trade[]): {
    consecutiveWins: number;
    consecutiveLosses: number;
  } {
    
    let maxConsecutiveWins = 0;
    let maxConsecutiveLosses = 0;
    let currentConsecutiveWins = 0;
    let currentConsecutiveLosses = 0;
    
    for (const trade of trades) {
      if (trade.profit > 0) {
        currentConsecutiveWins++;
        currentConsecutiveLosses = 0;
        maxConsecutiveWins = Math.max(maxConsecutiveWins, currentConsecutiveWins);
      } else if (trade.profit < 0) {
        currentConsecutiveLosses++;
        currentConsecutiveWins = 0;
        maxConsecutiveLosses = Math.max(maxConsecutiveLosses, currentConsecutiveLosses);
      }
    }
    
    return {
      consecutiveWins: maxConsecutiveWins,
      consecutiveLosses: maxConsecutiveLosses
    };
  }

  // ===== Period-Based Analysis =====

  /**
   * Calculate metrics for different time periods
   */
  private calculatePeriodMetrics(
    equityCurve: EquityCurvePoint[],
    startDate: Date,
    endDate: Date
  ): {
    daily: PerformanceMetrics;
    weekly: PerformanceMetrics;
    monthly: PerformanceMetrics;
  } {
    
    const dailyReturns = this.calculatePeriodReturns(equityCurve, 'daily');
    const weeklyReturns = this.calculatePeriodReturns(equityCurve, 'weekly');
    const monthlyReturns = this.calculatePeriodReturns(equityCurve, 'monthly');
    
    return {
      daily: this.calculateMetricsFromReturns(dailyReturns, 252),
      weekly: this.calculateMetricsFromReturns(weeklyReturns, 52),
      monthly: this.calculateMetricsFromReturns(monthlyReturns, 12)
    };
  }

  /**
   * Calculate returns for specific period
   */
  private calculatePeriodReturns(
    equityCurve: EquityCurvePoint[],
    period: 'daily' | 'weekly' | 'monthly'
  ): number[] {
    
    const returns: number[] = [];
    const periodMs = period === 'daily' ? 24 * 60 * 60 * 1000 :
                    period === 'weekly' ? 7 * 24 * 60 * 60 * 1000 :
                    30 * 24 * 60 * 60 * 1000; // Approximate month
    
    for (let i = 1; i < equityCurve.length; i++) {
      const timeDiff = equityCurve[i].timestamp.getTime() - equityCurve[i - 1].timestamp.getTime();
      
      if (timeDiff >= periodMs * 0.5) { // At least half the period
        const prevEquity = equityCurve[i - 1].equity;
        const currentEquity = equityCurve[i].equity;
        
        if (prevEquity > 0) {
          returns.push((currentEquity - prevEquity) / prevEquity);
        }
      }
    }
    
    return returns;
  }

  /**
   * Calculate metrics from return series
   */
  private calculateMetricsFromReturns(returns: number[], periodsPerYear: number): PerformanceMetrics {
    if (returns.length === 0) return this.getEmptyMetrics();
    
    const totalReturn = returns.reduce((product, r) => product * (1 + r), 1) - 1;
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const annualizedReturn = Math.pow(1 + avgReturn, periodsPerYear) - 1;
    
    const volatility = this.calculateVolatility(returns);
    const annualizedVolatility = volatility * Math.sqrt(periodsPerYear);
    
    const sharpeRatio = annualizedVolatility > 0 
      ? (annualizedReturn - this.riskFreeRate) / annualizedVolatility 
      : 0;
    
    const sortinoRatio = this.calculateSortinoRatio(returns, annualizedReturn);
    
    return {
      totalReturn: totalReturn * 100,
      sharpeRatio,
      profitFactor: 0, // Not applicable for period returns
      winRate: 0, // Not applicable for period returns
      maxDrawdown: 0, // Would need equity curve analysis
      tradeCount: 0, // Not applicable for period returns
      averageTradeReturn: avgReturn * 100,
      volatility: annualizedVolatility * 100,
      calmarRatio: 0, // Would need drawdown calculation
      sortinoRatio
    };
  }

  // ===== Utility Methods =====

  /**
   * Get empty metrics object
   */
  private getEmptyMetrics(): PerformanceMetrics {
    return {
      totalReturn: 0,
      sharpeRatio: 0,
      profitFactor: 0,
      winRate: 0,
      maxDrawdown: 0,
      tradeCount: 0,
      averageTradeReturn: 0,
      volatility: 0,
      calmarRatio: 0,
      sortinoRatio: 0
    };
  }

  /**
   * Set risk-free rate
   */
  public setRiskFreeRate(rate: number): void {
    this.riskFreeRate = rate;
  }

  /**
   * Set trading days per year
   */
  public setTradingDaysPerYear(days: number): void {
    this.tradingDaysPerYear = days;
  }
}