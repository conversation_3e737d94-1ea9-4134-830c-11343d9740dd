import { PrismaClient } from '@prisma/client';
import { ConfidenceAssessmentService } from '../confidence/ConfidenceAssessmentService';
import { PaperTradingAnalyticsService } from '../trading/PaperTradingAnalyticsService';
import { PaperTradingRequirementsService } from '../trading/PaperTradingRequirementsService';
import { ConfidenceStage } from '@golddaddy/types';

export interface PaperTradingConfidenceMapping {
  sessionId: string;
  userId: string;
  confidenceStage: ConfidenceStage;
  paperTradingMetrics: {
    winRate: number;
    riskManagementScore: number;
    consistencyScore: number;
    sharpeRatio: number;
    maxDrawdown: number;
    totalTrades: number;
    profitability: number;
  };
  confidenceScoreUpdate: {
    performanceEvaluation: {
      paperTradingWinRate: number;
      riskManagementScore: number;
      strategyAdherence: number;
      consistencyRating: number;
    };
    overallImpact: number;
  };
}

export interface GraduationProgressUpdate {
  userId: string;
  sessionId: string;
  currentStage: ConfidenceStage;
  readyForNextStage: boolean;
  completedRequirements: string[];
  pendingRequirements: string[];
  recommendedActions: string[];
  timeToNextStage?: number;
}

export class PaperTradingConfidenceIntegration {
  private prisma: PrismaClient;
  private confidenceService: ConfidenceAssessmentService;
  private analyticsService: PaperTradingAnalyticsService;
  private requirementsService: PaperTradingRequirementsService;

  // Mapping thresholds for different confidence levels
  private readonly CONFIDENCE_THRESHOLDS = {
    excellent: { winRate: 0.65, riskScore: 85, consistency: 80, sharpe: 1.5 },
    good: { winRate: 0.55, riskScore: 75, consistency: 70, sharpe: 1.0 },
    adequate: { winRate: 0.45, riskScore: 65, consistency: 60, sharpe: 0.5 },
    poor: { winRate: 0.35, riskScore: 50, consistency: 50, sharpe: 0.0 },
  };

  constructor(
    prisma: PrismaClient,
    confidenceService: ConfidenceAssessmentService,
    analyticsService: PaperTradingAnalyticsService,
    requirementsService: PaperTradingRequirementsService
  ) {
    this.prisma = prisma;
    this.confidenceService = confidenceService;
    this.analyticsService = analyticsService;
    this.requirementsService = requirementsService;
  }

  /**
   * Updates confidence assessment based on paper trading performance
   */
  async updateConfidenceFromPaperTrading(
    userId: string,
    sessionId: string
  ): Promise<PaperTradingConfidenceMapping> {
    try {
      // Get paper trading analytics
      const analytics = await this.analyticsService.generateAnalytics(userId, sessionId);
      const requirements = await this.requirementsService.evaluateGraduationCriteria(userId, sessionId);

      // Extract key metrics
      const paperTradingMetrics = {
        winRate: analytics.performanceMetrics.winRate,
        riskManagementScore: requirements.riskManagementScore,
        consistencyScore: requirements.consistencyScore,
        sharpeRatio: analytics.performanceMetrics.sharpeRatio,
        maxDrawdown: analytics.performanceMetrics.maxDrawdown,
        totalTrades: analytics.tradeStatistics.totalTrades,
        profitability: analytics.performanceMetrics.totalReturn,
      };

      // Calculate confidence score updates
      const confidenceScoreUpdate = this.calculateConfidenceScoreUpdate(paperTradingMetrics);

      // Update confidence assessment with paper trading performance
      await this.updatePerformanceEvaluation(
        userId,
        confidenceScoreUpdate.performanceEvaluation
      );

      // Determine current stage (paper trading should be PAPER_TRADING stage)
      const confidenceStage = ConfidenceStage.PAPER_TRADING;

      // Create integration mapping
      const mapping: PaperTradingConfidenceMapping = {
        sessionId,
        userId,
        confidenceStage,
        paperTradingMetrics,
        confidenceScoreUpdate,
      };

      // Log the integration update
      await this.logConfidenceIntegration(mapping);

      return mapping;

    } catch (error) {
      console.error('Error updating confidence from paper trading:', error);
      throw new Error(`Failed to update confidence assessment: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Evaluates if user is ready to graduate from paper trading to live trading
   */
  async evaluateGraduationReadiness(
    userId: string,
    sessionId: string
  ): Promise<GraduationProgressUpdate> {
    try {
      // Get graduation criteria evaluation
      const graduationCriteria = await this.requirementsService.evaluateGraduationCriteria(userId, sessionId);
      
      // Get confidence assessment
      const confidenceAssessment = await this.getCurrentConfidenceAssessment(userId);
      
      // Get paper trading analytics for additional context
      const analytics = await this.analyticsService.generateAnalytics(userId, sessionId);

      // Determine completed and pending requirements
      const completedRequirements: string[] = [];
      const pendingRequirements: string[] = [];
      const recommendedActions: string[] = [];

      // Check time requirement
      if (graduationCriteria.timeRequirementMet) {
        completedRequirements.push(`Time requirement: ${graduationCriteria.daysActive}/${graduationCriteria.minimumDays} days`);
      } else {
        pendingRequirements.push(`Time requirement: ${graduationCriteria.daysActive}/${graduationCriteria.minimumDays} days`);
        const remainingDays = graduationCriteria.minimumDays - graduationCriteria.daysActive;
        recommendedActions.push(`Continue paper trading for ${remainingDays} more days`);
      }

      // Check trade requirement
      if (graduationCriteria.tradeRequirementMet) {
        completedRequirements.push(`Trade requirement: ${graduationCriteria.totalTrades}/${graduationCriteria.minimumTrades} trades`);
      } else {
        pendingRequirements.push(`Trade requirement: ${graduationCriteria.totalTrades}/${graduationCriteria.minimumTrades} trades`);
        const remainingTrades = graduationCriteria.minimumTrades - graduationCriteria.totalTrades;
        recommendedActions.push(`Execute ${remainingTrades} more paper trades`);
      }

      // Check performance requirement
      if (graduationCriteria.performanceRequirementMet) {
        completedRequirements.push('Performance requirement: Passed');
      } else {
        pendingRequirements.push('Performance requirement: Not met');
        
        if (graduationCriteria.riskManagementScore < 75) {
          recommendedActions.push('Improve risk management practices (use stop losses, position sizing)');
        }
        if (graduationCriteria.consistencyScore < 70) {
          recommendedActions.push('Focus on consistent trading strategy execution');
        }
      }

      // Check confidence score
      const minConfidenceScore = 85;
      if (confidenceAssessment.overallConfidenceScore >= minConfidenceScore) {
        completedRequirements.push(`Confidence assessment: ${confidenceAssessment.overallConfidenceScore}%`);
      } else {
        pendingRequirements.push(`Confidence assessment: ${confidenceAssessment.overallConfidenceScore}%/${minConfidenceScore}%`);
        recommendedActions.push('Complete additional confidence assessments to improve overall score');
      }

      // Check for advanced performance criteria
      if (analytics.performanceMetrics.winRate < 0.5) {
        recommendedActions.push('Improve trade selection - current win rate too low for live trading');
      }
      
      if (analytics.performanceMetrics.maxDrawdown > 0.15) {
        recommendedActions.push('Reduce maximum drawdown through better risk management');
      }

      // Determine overall readiness
      const readyForNextStage = 
        graduationCriteria.overallEligible && 
        confidenceAssessment.overallConfidenceScore >= minConfidenceScore &&
        analytics.performanceMetrics.winRate >= 0.45 &&
        analytics.performanceMetrics.maxDrawdown <= 0.20;

      // Estimate time to next stage
      let timeToNextStage: number | undefined;
      if (!readyForNextStage) {
        const factors = [];
        
        if (!graduationCriteria.timeRequirementMet) {
          factors.push(graduationCriteria.minimumDays - graduationCriteria.daysActive);
        }
        
        if (!graduationCriteria.tradeRequirementMet) {
          const tradesPerDay = graduationCriteria.totalTrades / Math.max(graduationCriteria.daysActive, 1);
          const remainingTrades = graduationCriteria.minimumTrades - graduationCriteria.totalTrades;
          factors.push(Math.ceil(remainingTrades / Math.max(tradesPerDay, 1)));
        }
        
        if (factors.length > 0) {
          timeToNextStage = Math.max(...factors);
        }
      }

      const progressUpdate: GraduationProgressUpdate = {
        userId,
        sessionId,
        currentStage: ConfidenceStage.PAPER_TRADING,
        readyForNextStage,
        completedRequirements,
        pendingRequirements,
        recommendedActions,
        timeToNextStage,
      };

      // Log graduation progress
      await this.logGraduationProgress(progressUpdate);

      return progressUpdate;

    } catch (error) {
      console.error('Error evaluating graduation readiness:', error);
      throw new Error(`Failed to evaluate graduation readiness: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handles automated progression from paper trading to live trading readiness
   */
  async processAutomatedGraduation(
    userId: string,
    sessionId: string
  ): Promise<{
    graduated: boolean;
    newStage?: ConfidenceStage;
    message: string;
    nextSteps: string[];
  }> {
    try {
      const progressUpdate = await this.evaluateGraduationReadiness(userId, sessionId);

      if (progressUpdate.readyForNextStage) {
        // Update confidence stage to LIVE_READY
        await this.updateConfidenceStage(userId, ConfidenceStage.LIVE_READY);

        // Update session status to completed/graduated
        await this.markSessionGraduated(sessionId);

        // Generate graduation certificate/achievement
        await this.generateGraduationAchievement(userId, sessionId);

        return {
          graduated: true,
          newStage: ConfidenceStage.LIVE_READY,
          message: 'Congratulations! You have successfully graduated from paper trading and are ready for live trading.',
          nextSteps: [
            'Set up your live trading account',
            'Start with small position sizes',
            'Continue applying the risk management principles you learned',
            'Monitor your performance closely',
          ],
        };
      } else {
        return {
          graduated: false,
          message: 'Additional requirements must be met before graduation to live trading.',
          nextSteps: progressUpdate.recommendedActions,
        };
      }

    } catch (error) {
      console.error('Error processing automated graduation:', error);
      throw new Error(`Failed to process graduation: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Integrates paper trading insights into story mode progression
   */
  async updateStoryModeProgression(
    userId: string,
    sessionId: string
  ): Promise<{
    milestoneCompleted?: string;
    newInsights: string[];
    recommendedContent: string[];
    nextMilestone?: string;
  }> {
    try {
      const analytics = await this.analyticsService.generateAnalytics(userId, sessionId);
      const requirements = await this.requirementsService.evaluateGraduationCriteria(userId, sessionId);

      const newInsights: string[] = [];
      const recommendedContent: string[] = [];
      let milestoneCompleted: string | undefined;
      let nextMilestone: string | undefined;

      // Generate insights based on performance
      if (analytics.performanceMetrics.winRate > 0.6) {
        newInsights.push('You demonstrate strong trade selection skills');
      } else if (analytics.performanceMetrics.winRate < 0.4) {
        newInsights.push('Your trade selection needs improvement');
        recommendedContent.push('Review market analysis techniques');
      }

      if (analytics.performanceMetrics.sharpeRatio > 1.5) {
        newInsights.push('Excellent risk-adjusted returns');
      } else if (analytics.performanceMetrics.sharpeRatio < 0.5) {
        newInsights.push('Focus on improving risk-adjusted performance');
        recommendedContent.push('Study risk-reward ratio optimization');
      }

      if (requirements.riskManagementScore > 80) {
        newInsights.push('Strong risk management discipline');
      } else {
        newInsights.push('Risk management needs attention');
        recommendedContent.push('Practice with stop loss strategies');
      }

      // Check for milestone completions
      if (requirements.timeRequirementMet && requirements.tradeRequirementMet) {
        milestoneCompleted = 'Paper Trading Completion';
      } else if (requirements.tradeRequirementMet) {
        milestoneCompleted = 'Minimum Trades Executed';
        nextMilestone = 'Complete Time Requirement';
      } else if (analytics.tradeStatistics.totalTrades >= 10) {
        milestoneCompleted = 'First 10 Paper Trades';
        nextMilestone = 'Reach Minimum Trade Count';
      }

      // Determine next milestone
      if (!nextMilestone) {
        if (requirements.overallEligible) {
          nextMilestone = 'Graduate to Live Trading';
        } else if (requirements.performanceRequirementMet) {
          nextMilestone = 'Complete Time and Trade Requirements';
        } else {
          nextMilestone = 'Improve Performance Metrics';
        }
      }

      return {
        milestoneCompleted,
        newInsights,
        recommendedContent,
        nextMilestone,
      };

    } catch (error) {
      console.error('Error updating story mode progression:', error);
      throw new Error(`Failed to update story mode progression: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private calculateConfidenceScoreUpdate(metrics: any): {
    performanceEvaluation: {
      paperTradingWinRate: number;
      riskManagementScore: number;
      strategyAdherence: number;
      consistencyRating: number;
    };
    overallImpact: number;
  } {
    // Convert metrics to 0-100 scale for confidence assessment
    const paperTradingWinRate = Math.round(metrics.winRate * 100);
    const riskManagementScore = Math.round(metrics.riskManagementScore);
    const consistencyRating = Math.round(metrics.consistencyScore);

    // Calculate strategy adherence based on risk-adjusted performance
    let strategyAdherence = 50; // Default baseline
    
    if (metrics.sharpeRatio > 1.0) strategyAdherence += 20;
    if (metrics.maxDrawdown < 0.10) strategyAdherence += 15;
    if (metrics.profitability > 0) strategyAdherence += 15;
    
    strategyAdherence = Math.min(100, strategyAdherence);

    // Calculate overall impact on confidence score
    const performanceComponents = [
      paperTradingWinRate,
      riskManagementScore,
      strategyAdherence,
      consistencyRating
    ];
    
    const overallImpact = Math.round(
      performanceComponents.reduce((sum, score) => sum + score, 0) / performanceComponents.length
    );

    return {
      performanceEvaluation: {
        paperTradingWinRate,
        riskManagementScore,
        strategyAdherence,
        consistencyRating,
      },
      overallImpact,
    };
  }

  private async getCurrentConfidenceAssessment(userId: string): Promise<any> {
    const assessment = await this.prisma.confidenceAssessment.findFirst({
      where: { userId },
      orderBy: { updatedAt: 'desc' },
    });

    if (!assessment) {
      // Create default assessment if none exists
      return await this.prisma.confidenceAssessment.create({
        data: {
          userId,
          currentStage: ConfidenceStage.PAPER_TRADING,
          overallConfidenceScore: 0,
          assessmentScores: {
            knowledgeQuiz: { score: 0, completedAt: new Date(), attempts: 0, weakAreas: [] },
            behavioralAssessment: { 
              riskTolerance: 50, 
              decisionConsistency: 50, 
              emotionalStability: 50, 
              lastAssessed: new Date() 
            },
            performanceEvaluation: { 
              paperTradingWinRate: 0, 
              riskManagementScore: 0, 
              strategyAdherence: 0, 
              consistencyRating: 0 
            }
          },
          progressHistory: [],
          graduationCriteria: {
            nextStage: 'live_ready',
            requirements: {
              minimumConfidenceScore: 85,
              requiredAssessments: ['paper_trading_performance'],
              minimumTimeInStage: 30,
            },
          },
        },
      });
    }

    return assessment;
  }

  private async updatePerformanceEvaluation(
    userId: string,
    performanceEvaluation: any
  ): Promise<void> {
    const assessment = await this.getCurrentConfidenceAssessment(userId);

    const updatedScores = {
      ...assessment.assessmentScores,
      performanceEvaluation: {
        ...assessment.assessmentScores.performanceEvaluation,
        ...performanceEvaluation,
        lastEvaluated: new Date(),
      },
    };

    // Recalculate overall confidence score
    const overallScore = this.calculateOverallConfidenceScore(updatedScores);

    await this.prisma.confidenceAssessment.update({
      where: { id: assessment.id },
      data: {
        overallConfidenceScore: overallScore,
        assessmentScores: updatedScores,
        updatedAt: new Date(),
      },
    });
  }

  private calculateOverallConfidenceScore(assessmentScores: any): number {
    const weights = {
      knowledgeQuiz: 0.3,
      behavioralAssessment: 0.3,
      performanceEvaluation: 0.4, // Higher weight for actual trading performance
    };

    const knowledgeScore = assessmentScores.knowledgeQuiz?.score || 0;
    
    const behavioralComponents = assessmentScores.behavioralAssessment;
    const behavioralScore = behavioralComponents 
      ? (behavioralComponents.riskTolerance + behavioralComponents.decisionConsistency + behavioralComponents.emotionalStability) / 3
      : 0;

    const performanceComponents = assessmentScores.performanceEvaluation;
    const performanceScore = performanceComponents
      ? (performanceComponents.paperTradingWinRate + performanceComponents.riskManagementScore + 
         performanceComponents.strategyAdherence + performanceComponents.consistencyRating) / 4
      : 0;

    return Math.round(
      knowledgeScore * weights.knowledgeQuiz +
      behavioralScore * weights.behavioralAssessment +
      performanceScore * weights.performanceEvaluation
    );
  }

  private async updateConfidenceStage(userId: string, newStage: ConfidenceStage): Promise<void> {
    await this.prisma.confidenceAssessment.updateMany({
      where: { userId },
      data: {
        currentStage: newStage,
        updatedAt: new Date(),
      },
    });
  }

  private async markSessionGraduated(sessionId: string): Promise<void> {
    await this.prisma.paperTradingSession.update({
      where: { id: sessionId },
      data: {
        status: 'completed',
        completedAt: new Date(),
        graduatedAt: new Date(),
      },
    });
  }

  private async generateGraduationAchievement(userId: string, sessionId: string): Promise<void> {
    // This would integrate with an achievements/badges system
    await this.prisma.userAchievement.create({
      data: {
        userId,
        achievementType: 'PAPER_TRADING_GRADUATION',
        achievementData: {
          sessionId,
          completedAt: new Date(),
          title: 'Paper Trading Graduate',
          description: 'Successfully completed paper trading requirements and ready for live trading',
        },
        earnedAt: new Date(),
      },
    });
  }

  private async logConfidenceIntegration(mapping: PaperTradingConfidenceMapping): Promise<void> {
    await this.prisma.integrationLog.create({
      data: {
        userId: mapping.userId,
        integrationType: 'PAPER_TRADING_CONFIDENCE',
        sourceSystem: 'paper_trading',
        targetSystem: 'confidence_assessment',
        integrationData: mapping,
        processedAt: new Date(),
      },
    });
  }

  private async logGraduationProgress(progress: GraduationProgressUpdate): Promise<void> {
    await this.prisma.integrationLog.create({
      data: {
        userId: progress.userId,
        integrationType: 'GRADUATION_PROGRESS',
        sourceSystem: 'paper_trading',
        targetSystem: 'confidence_assessment',
        integrationData: progress,
        processedAt: new Date(),
      },
    });
  }

  /**
   * Gets comprehensive integration status for a user
   */
  async getIntegrationStatus(userId: string): Promise<{
    currentStage: ConfidenceStage;
    paperTradingSessions: any[];
    confidenceScore: number;
    graduationReadiness: any;
    recentIntegrations: any[];
  }> {
    try {
      const [assessment, sessions, recentLogs] = await Promise.all([
        this.getCurrentConfidenceAssessment(userId),
        this.prisma.paperTradingSession.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          take: 5,
        }),
        this.prisma.integrationLog.findMany({
          where: { 
            userId,
            integrationType: { in: ['PAPER_TRADING_CONFIDENCE', 'GRADUATION_PROGRESS'] }
          },
          orderBy: { processedAt: 'desc' },
          take: 10,
        }),
      ]);

      // Get graduation readiness for active session
      let graduationReadiness = null;
      const activeSession = sessions.find(s => s.status === 'active');
      if (activeSession) {
        graduationReadiness = await this.evaluateGraduationReadiness(userId, activeSession.id);
      }

      return {
        currentStage: assessment.currentStage,
        paperTradingSessions: sessions,
        confidenceScore: assessment.overallConfidenceScore,
        graduationReadiness,
        recentIntegrations: recentLogs,
      };

    } catch (error) {
      console.error('Error getting integration status:', error);
      throw new Error(`Failed to get integration status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}

export default PaperTradingConfidenceIntegration;