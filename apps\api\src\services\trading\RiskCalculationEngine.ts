/**
 * Risk Calculation Engine Service
 * 
 * Implements dynamic position sizing calculation based on account balance and risk tolerance.
 * Supports Kelly Criterion and risk-adjusted position calculations with correlation analysis.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';

// Risk Management Types
export interface RiskCalculationParams {
  accountBalance: Decimal.Instance;
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  marketVolatility: number; // Percentage
  correlationRisk?: number; // 0-1 scale for correlation with existing positions
  userExperienceLevel: 'beginner' | 'intermediate' | 'advanced';
  maxPositionSize?: Decimal.Instance; // Maximum position size limit
}

export interface PositionSizeResult {
  recommendedSize: Decimal.Instance;
  maxAllowedSize: Decimal.Instance;
  riskPercentage: number;
  kellyRecommendation: Decimal.Instance;
  volatilityAdjustment: number;
  correlationAdjustment: number;
  experienceAdjustment: number;
  reasoning: string[];
}

export interface KellyCriterionParams {
  winProbability: number; // 0-1 scale
  averageWin: Decimal.Instance;
  averageLoss: Decimal.Instance;
  accountBalance: Decimal.Instance;
  maxKellyPercentage?: number; // Default: 25%
}

export interface RiskMetrics {
  currentRiskExposure: Decimal.Instance;
  maxDailyRisk: Decimal.Instance;
  portfolioVaR: Decimal.Instance; // Value at Risk
  diversificationRatio: number;
  correlationMatrix?: number[][];
  riskBudgetUtilization: number; // 0-100%
}

/**
 * RiskCalculationEngine - Core service for position sizing and risk calculations
 */
export class RiskCalculationEngine {
  private readonly MAX_RISK_PERCENTAGES = {
    conservative: 1.0, // 1% per position
    moderate: 2.5,     // 2.5% per position
    aggressive: 5.0    // 5% per position
  };

  private readonly EXPERIENCE_MULTIPLIERS = {
    beginner: 0.5,     // 50% of standard risk
    intermediate: 0.8, // 80% of standard risk
    advanced: 1.0      // 100% of standard risk
  };

  private readonly VOLATILITY_THRESHOLDS = {
    low: 15,      // < 15% volatility
    medium: 25,   // 15-25% volatility
    high: 35      // > 35% volatility
  };

  /**
   * Calculate optimal position size based on risk parameters
   */
  public calculatePositionSize(params: RiskCalculationParams): PositionSizeResult {
    const {
      accountBalance,
      riskTolerance,
      marketVolatility,
      correlationRisk = 0,
      userExperienceLevel,
      maxPositionSize
    } = params;

    const reasoning: string[] = [];

    // Base risk percentage based on tolerance
    const baseRiskPercentage = this.MAX_RISK_PERCENTAGES[riskTolerance];
    reasoning.push(`Base risk tolerance: ${riskTolerance} (${baseRiskPercentage}%)`);

    // Experience adjustment
    const experienceMultiplier = this.EXPERIENCE_MULTIPLIERS[userExperienceLevel];
    const experienceAdjustedRisk = baseRiskPercentage * experienceMultiplier;
    reasoning.push(`Experience adjustment: ${userExperienceLevel} (${experienceMultiplier}x)`);

    // Volatility adjustment
    const volatilityAdjustment = this.calculateVolatilityAdjustment(marketVolatility);
    const volatilityAdjustedRisk = experienceAdjustedRisk * volatilityAdjustment;
    reasoning.push(`Volatility adjustment: ${marketVolatility}% volatility (${volatilityAdjustment}x)`);

    // Correlation adjustment
    const correlationAdjustment = 1 - (correlationRisk * 0.5); // Reduce up to 50% for high correlation
    const finalRiskPercentage = volatilityAdjustedRisk * correlationAdjustment;
    reasoning.push(`Correlation adjustment: ${correlationRisk} correlation (${correlationAdjustment}x)`);

    // Calculate position sizes
    const riskAmount = accountBalance.mul(finalRiskPercentage).div(100);
    const recommendedSize = riskAmount;
    
    // Apply maximum position size constraint if specified
    const maxAllowedSize = maxPositionSize 
      ? Decimal.min(recommendedSize, maxPositionSize)
      : recommendedSize;

    reasoning.push(`Final risk amount: $${riskAmount.toFixed(2)} (${finalRiskPercentage.toFixed(2)}%)`);

    return {
      recommendedSize: Decimal.min(recommendedSize, maxAllowedSize),
      maxAllowedSize,
      riskPercentage: finalRiskPercentage,
      kellyRecommendation: new Decimal(0), // Will be calculated separately
      volatilityAdjustment,
      correlationAdjustment,
      experienceAdjustment: experienceMultiplier,
      reasoning
    };
  }

  /**
   * Calculate Kelly Criterion optimal position size
   */
  public calculateKellyPosition(params: KellyCriterionParams): Decimal {
    const { winProbability, averageWin, averageLoss, accountBalance, maxKellyPercentage = 25 } = params;

    // Kelly formula: f* = (bp - q) / b
    // where: b = odds received on wager (averageWin/averageLoss), p = probability of win, q = probability of loss
    const oddsRatio = averageWin.div(averageLoss.abs());
    const probabilityOfLoss = 1 - winProbability;
    
    const kellyFraction = oddsRatio.mul(winProbability).sub(probabilityOfLoss).div(oddsRatio);
    
    // Convert to percentage and apply maximum constraint
    const kellyPercentage = Math.min(kellyFraction.toNumber() * 100, maxKellyPercentage);
    
    // Return position size (ensure non-negative)
    return accountBalance.mul(Math.max(kellyPercentage, 0)).div(100);
  }

  /**
   * Calculate real-time risk exposure metrics
   */
  public calculateRiskMetrics(
    accountBalance: Decimal,
    openPositions: Array<{ size: Decimal.Instance; symbol: string; unrealizedPnL: Decimal }>,
    riskTolerance: 'conservative' | 'moderate' | 'aggressive'
  ): RiskMetrics {
    const totalExposure = openPositions.reduce(
      (sum, position) => sum.add(position.size.abs()),
      new Decimal(0)
    );


    const maxDailyRisk = accountBalance.mul(this.MAX_RISK_PERCENTAGES[riskTolerance]).div(100);
    
    // Simple VaR calculation (1-day, 95% confidence)
    const portfolioVaR = totalExposure.mul(0.02); // Simplified 2% daily volatility assumption

    const riskBudgetUtilization = totalExposure.div(maxDailyRisk).mul(100).toNumber();

    return {
      currentRiskExposure: totalExposure,
      maxDailyRisk,
      portfolioVaR,
      diversificationRatio: this.calculateDiversificationRatio(openPositions),
      riskBudgetUtilization: Math.min(riskBudgetUtilization, 100)
    };
  }

  /**
   * Calculate volatility adjustment factor
   */
  private calculateVolatilityAdjustment(volatility: number): number {
    if (volatility <= this.VOLATILITY_THRESHOLDS.low) {
      return 1.0; // No adjustment for low volatility
    } else if (volatility <= this.VOLATILITY_THRESHOLDS.medium) {
      // Linear decrease from 1.0 to 0.7 between low and medium thresholds
      const ratio = (volatility - this.VOLATILITY_THRESHOLDS.low) / 
                   (this.VOLATILITY_THRESHOLDS.medium - this.VOLATILITY_THRESHOLDS.low);
      return 1.0 - (ratio * 0.3);
    } else if (volatility <= this.VOLATILITY_THRESHOLDS.high) {
      // Linear decrease from 0.7 to 0.5 between medium and high thresholds
      const ratio = (volatility - this.VOLATILITY_THRESHOLDS.medium) / 
                   (this.VOLATILITY_THRESHOLDS.high - this.VOLATILITY_THRESHOLDS.medium);
      return 0.7 - (ratio * 0.2);
    } else {
      // Very high volatility: maximum reduction
      return 0.5;
    }
  }

  /**
   * Calculate diversification ratio for position concentration risk
   */
  private calculateDiversificationRatio(
    positions: Array<{ size: Decimal.Instance; symbol: string }>
  ): number {
    if (positions.length === 0) return 1;
    if (positions.length === 1) return 0;

    const totalSize = positions.reduce((sum, pos) => sum.add(pos.size.abs()), new Decimal(0));
    
    // Calculate Herfindahl-Hirschman Index (HHI) for concentration
    const hhi = positions.reduce((sum, pos) => {
      const weight = pos.size.abs().div(totalSize).toNumber();
      return sum + (weight * weight);
    }, 0);

    // Convert HHI to diversification ratio (1 = perfectly diversified, 0 = fully concentrated)
    const maxHHI = 1; // Maximum concentration (all in one position)
    const minHHI = 1 / positions.length; // Perfect diversification
    
    return Math.max(0, (maxHHI - hhi) / (maxHHI - minHHI));
  }

  /**
   * Validate risk calculation parameters
   */
  public validateParams(params: RiskCalculationParams): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (params.accountBalance.lte(0)) {
      errors.push('Account balance must be positive');
    }

    if (params.marketVolatility < 0 || params.marketVolatility > 100) {
      errors.push('Market volatility must be between 0 and 100 percent');
    }

    if (params.correlationRisk && (params.correlationRisk < 0 || params.correlationRisk > 1)) {
      errors.push('Correlation risk must be between 0 and 1');
    }

    if (params.maxPositionSize && params.maxPositionSize.lte(0)) {
      errors.push('Maximum position size must be positive if specified');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

/**
 * Factory function to create RiskCalculationEngine instance
 */
export function createRiskCalculationEngine(): RiskCalculationEngine {
  return new RiskCalculationEngine();
}

/**
 * Default export for convenience
 */
export default RiskCalculationEngine;