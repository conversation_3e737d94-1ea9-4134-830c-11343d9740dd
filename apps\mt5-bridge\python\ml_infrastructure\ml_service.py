"""
Main ML Service for GoldDaddy Trading Platform
Orchestrates all ML components including training, inference, and optimization
"""

import asyncio
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from loguru import logger
import redis
import json
from pathlib import Path

from .model_registry import ModelRegistry, ModelStatus, ModelType
from .training_pipeline import TrainingPipeline, TrainingConfig
from .inference_service import InferenceService, PredictionRequest
from .strategy_optimizer import StrategyOptimizationEngine, ParameterRange, OptimizationConfig
from .feature_engineering import FinancialFeatureEngineer, FeatureConfig

class MLService:
    """
    Main ML service orchestrating all machine learning operations
    """
    
    def __init__(self, redis_url: Optional[str] = None, models_path: str = "./models"):
        # Initialize components
        self.model_registry = ModelRegistry(models_path)
        self.redis_client = redis.from_url(redis_url) if redis_url else None
        self.inference_service = InferenceService(self.model_registry, self.redis_client)
        self.optimization_engine = StrategyOptimizationEngine()
        
        # Training pipelines for different model types
        self.training_pipelines: Dict[str, TrainingPipeline] = {}
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.is_running = False
        
        logger.info("ML Service initialized successfully")
    
    async def start(self):
        """Start the ML service and background tasks"""
        self.is_running = True
        
        # Start background tasks
        self.background_tasks = [
            asyncio.create_task(self._model_health_monitor()),
            asyncio.create_task(self._retraining_scheduler()),
            asyncio.create_task(self._performance_monitor())
        ]
        
        # Warm up deployed models
        deployed_models = self.model_registry.get_deployed_models()
        model_ids = [model.id for model in deployed_models]
        await self.inference_service.warm_up_models(model_ids)
        
        logger.info("ML Service started with background monitoring")
    
    async def stop(self):
        """Stop the ML service and cleanup"""
        self.is_running = False
        
        # Cancel background tasks
        for task in self.background_tasks:
            task.cancel()
        
        await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        logger.info("ML Service stopped")
    
    # Training Methods
    async def train_market_prediction_model(self, 
                                          market_data: pd.DataFrame,
                                          model_name: str = "market_predictor",
                                          config: Optional[TrainingConfig] = None) -> str:
        """
        Train a market prediction transformer model
        """
        logger.info(f"Training market prediction model: {model_name}")
        
        if not config:
            config = TrainingConfig(model_type="market")
        
        pipeline = TrainingPipeline(config, self.model_registry)
        model_id = await pipeline.train_model(market_data, model_name, "returns")
        
        logger.info(f"Market prediction model trained successfully: {model_id}")
        return model_id
    
    async def train_regime_detection_model(self,
                                         market_data: pd.DataFrame,
                                         model_name: str = "regime_detector",
                                         config: Optional[TrainingConfig] = None) -> str:
        """
        Train a market regime detection model
        """
        logger.info(f"Training regime detection model: {model_name}")
        
        if not config:
            config = TrainingConfig(model_type="regime_detection")
        
        # Create regime labels (simplified)
        market_data = self._create_regime_labels(market_data)
        
        pipeline = TrainingPipeline(config, self.model_registry)
        model_id = await pipeline.train_model(market_data, model_name, "regime")
        
        logger.info(f"Regime detection model trained successfully: {model_id}")
        return model_id
    
    async def train_strategy_optimization_model(self,
                                              strategy_data: pd.DataFrame,
                                              model_name: str = "strategy_optimizer",
                                              config: Optional[TrainingConfig] = None) -> str:
        """
        Train a strategy optimization model
        """
        logger.info(f"Training strategy optimization model: {model_name}")
        
        if not config:
            config = TrainingConfig(model_type="strategy_optimization")
        
        pipeline = TrainingPipeline(config, self.model_registry)
        model_id = await pipeline.train_model(strategy_data, model_name, "strategy_performance")
        
        logger.info(f"Strategy optimization model trained successfully: {model_id}")
        return model_id
    
    # Inference Methods
    async def predict_market_direction(self, 
                                     market_data: pd.DataFrame,
                                     model_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Predict market direction using the best available model
        """
        if not model_id:
            # Get the best market prediction model
            market_models = self.model_registry.get_models_by_type(ModelType.TRANSFORMER)
            deployed_models = [m for m in market_models if m.deployment.status == ModelStatus.DEPLOYED]
            
            if not deployed_models:
                raise ValueError("No deployed market prediction models available")
            
            # Select best performing model
            model_id = max(deployed_models, key=lambda m: m.performance.test_accuracy).id
        
        return await self.inference_service.predict_market_direction(model_id, market_data)
    
    async def detect_market_regime(self,
                                 market_data: pd.DataFrame,
                                 model_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Detect current market regime
        """
        if not model_id:
            # Get the best regime detection model
            regime_models = [m for m in self.model_registry.models.values() 
                           if "regime" in m.name.lower() and m.deployment.status == ModelStatus.DEPLOYED]
            
            if not regime_models:
                raise ValueError("No deployed regime detection models available")
            
            model_id = max(regime_models, key=lambda m: m.performance.test_accuracy).id
        
        request = PredictionRequest(
            model_id=model_id,
            market_data=market_data,
            prediction_type="regime"
        )
        
        response = await self.inference_service.predict(request)
        
        # Convert to human-readable format
        regime_map = {0: "trending", 1: "sideways", 2: "volatile", 3: "crisis"}
        predicted_regime = int(np.argmax(list(response.prediction.values())))
        
        return {
            "regime": regime_map.get(predicted_regime, "unknown"),
            "probabilities": response.prediction,
            "confidence": response.confidence,
            "processing_time_ms": response.processing_time_ms
        }
    
    # Strategy Optimization Methods
    async def optimize_strategy_parameters(self,
                                         strategy_function: Callable,
                                         parameter_ranges: List[ParameterRange],
                                         market_data: pd.DataFrame,
                                         optimization_method: str = "genetic") -> Dict[str, Any]:
        """
        Optimize strategy parameters using genetic algorithm or Bayesian optimization
        """
        logger.info(f"Optimizing strategy parameters using {optimization_method}")
        
        # Create fitness function that includes market context
        def enhanced_fitness_function(parameters: Dict[str, Any]) -> Dict[str, Any]:
            # Run strategy backtest
            backtest_results = strategy_function(parameters, market_data)
            
            # Calculate fitness score (can be customized)
            fitness = self._calculate_strategy_fitness(backtest_results)
            
            return {
                'fitness': fitness,
                'backtest_results': backtest_results
            }
        
        config = OptimizationConfig()
        result = await self.optimization_engine.optimize_strategy(
            enhanced_fitness_function,
            parameter_ranges,
            optimization_method,
            config
        )
        
        logger.info(f"Strategy optimization completed with fitness: {result['best_fitness']:.6f}")
        return result
    
    # Model Management Methods
    async def deploy_model(self, model_id: str) -> bool:
        """Deploy a model for inference"""
        try:
            self.model_registry.update_model_status(model_id, ModelStatus.DEPLOYED)
            
            # Warm up the model
            await self.inference_service.warm_up_models([model_id])
            
            logger.info(f"Model {model_id} deployed successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to deploy model {model_id}: {e}")
            return False
    
    async def retire_model(self, model_id: str) -> bool:
        """Retire a model from active use"""
        try:
            self.model_registry.update_model_status(model_id, ModelStatus.ARCHIVED)
            logger.info(f"Model {model_id} retired successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to retire model {model_id}: {e}")
            return False
    
    def get_model_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary of all models"""
        models = list(self.model_registry.models.values())
        
        if not models:
            return {"total_models": 0}
        
        deployed_models = [m for m in models if m.deployment.status == ModelStatus.DEPLOYED]
        
        return {
            "total_models": len(models),
            "deployed_models": len(deployed_models),
            "model_types": {
                model_type.value: len([m for m in models if m.model_type == model_type])
                for model_type in ModelType
            },
            "average_accuracy": np.mean([m.performance.test_accuracy for m in models]),
            "best_model": max(models, key=lambda m: m.performance.test_accuracy).name if models else None,
            "inference_metrics": self.inference_service.get_performance_metrics()
        }
    
    # Background Tasks
    async def _model_health_monitor(self):
        """Monitor model health and performance"""
        while self.is_running:
            try:
                deployed_models = self.model_registry.get_deployed_models()
                
                for model in deployed_models:
                    # Check model age
                    days_since_training = (datetime.now() - model.training.last_trained).days
                    
                    if days_since_training > 30:  # Retrain after 30 days
                        self.model_registry.schedule_retraining(
                            model.id, 
                            datetime.now() + timedelta(days=1)
                        )
                        logger.info(f"Scheduled retraining for model {model.id}")
                
                await asyncio.sleep(3600)  # Check every hour
                
            except Exception as e:
                logger.error(f"Model health monitoring error: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes on error
    
    async def _retraining_scheduler(self):
        """Schedule and execute model retraining"""
        while self.is_running:
            try:
                models_to_retrain = self.model_registry.get_models_for_retraining()
                
                for model in models_to_retrain:
                    logger.info(f"Starting scheduled retraining for model {model.id}")
                    
                    # This would trigger retraining with new data
                    # For now, just update the schedule
                    next_retrain = datetime.now() + timedelta(days=30)
                    self.model_registry.schedule_retraining(model.id, next_retrain)
                
                await asyncio.sleep(1800)  # Check every 30 minutes
                
            except Exception as e:
                logger.error(f"Retraining scheduler error: {e}")
                await asyncio.sleep(300)
    
    async def _performance_monitor(self):
        """Monitor inference performance and system metrics"""
        while self.is_running:
            try:
                metrics = self.inference_service.get_performance_metrics()
                
                # Log performance metrics
                if metrics['total_predictions'] > 0:
                    logger.info(f"Inference metrics: {metrics}")
                
                # Clear cache if error rate is high
                if metrics['error_rate'] > 0.1:
                    logger.warning("High error rate detected, clearing model cache")
                    self.inference_service.clear_cache()
                
                await asyncio.sleep(600)  # Check every 10 minutes
                
            except Exception as e:
                logger.error(f"Performance monitoring error: {e}")
                await asyncio.sleep(300)
    
    # Helper Methods
    def _create_regime_labels(self, data: pd.DataFrame) -> pd.DataFrame:
        """Create regime labels for training (simplified implementation)"""
        data = data.copy()
        
        # Calculate volatility
        data['volatility'] = data['close'].pct_change().rolling(20).std()
        
        # Create simple regime labels based on volatility quantiles
        data['regime'] = pd.qcut(data['volatility'], q=4, labels=[0, 1, 2, 3], duplicates='drop')
        data['regime'] = data['regime'].fillna(1)  # Default to sideways
        
        return data
    
    def _calculate_strategy_fitness(self, backtest_results: Dict[str, Any]) -> float:
        """Calculate fitness score for strategy optimization"""
        # This is a simplified fitness function
        # In practice, you'd want to consider multiple metrics
        
        total_return = backtest_results.get('total_return', 0.0)
        sharpe_ratio = backtest_results.get('sharpe_ratio', 0.0)
        max_drawdown = backtest_results.get('max_drawdown', 1.0)
        win_rate = backtest_results.get('win_rate', 0.0)
        
        # Weighted fitness score
        fitness = (
            total_return * 0.3 +
            sharpe_ratio * 0.3 +
            (1.0 - max_drawdown) * 0.2 +
            win_rate * 0.2
        )
        
        return fitness
