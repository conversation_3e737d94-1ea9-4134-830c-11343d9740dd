import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Decimal } from 'decimal.js';
import { LossLimitEnforcer } from '../../LossLimitEnforcer';
import { RiskManager } from '../../RiskManager';
import { MT5BridgeService } from '../../MT5BridgeService';
import { Position, EmergencyLiquidationEvent, LiquidationResult } from '@golddaddy/types';

describe('Emergency Liquidation Integration', () => {
  let lossLimitEnforcer: LossLimitEnforcer;
  let riskManager: RiskManager;
  let mt5Bridge: MT5BridgeService;
  
  const mockUserId = '123e4567-e89b-12d3-a456-************';
  const mockPortfolioValue = new Decimal(75000);

  beforeEach(() => {
    lossLimitEnforcer = new LossLimitEnforcer();
    riskManager = new RiskManager();
    mt5Bridge = new MT5BridgeService();
    
    // Mock MT5 bridge methods
    vi.spyOn(mt5Bridge, 'closePosition').mockImplementation(async (positionId: string) => ({
      success: true,
      positionId,
      closedAt: new Date(),
      finalPrice: new Decimal(1.1950),
      realizedPnl: new Decimal(-500)
    }));
    
    vi.spyOn(mt5Bridge, 'getAccountInfo').mockImplementation(async (userId: string) => ({
      balance: new Decimal(74500),
      equity: new Decimal(74200),
      margin: new Decimal(2000),
      freeMargin: new Decimal(72200),
      marginLevel: new Decimal(3710) // 3710%
    }));
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete Liquidation Process', () => {
    it('should execute full liquidation sequence when multiple limits are breached', async () => {
      // Set strict limits to ensure violation
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(2000),
        maxDrawdownLimit: new Decimal(0.05), // 5%
        positionSizeLimit: new Decimal(0.15) // 15%
      });

      // Create positions that violate multiple limits
      const criticalPositions: Position[] = [
        {
          id: 'crit-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(200000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1920),
          unrealizedPnl: new Decimal(-16000), // Massive loss
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1850),
          takeProfit: new Decimal(1.2200)
        },
        {
          id: 'crit-2',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(150000),
          entryPrice: new Decimal(1.3000),
          currentPrice: new Decimal(1.2850),
          unrealizedPnl: new Decimal(-22500), // Another massive loss
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.2800),
          takeProfit: new Decimal(1.3300)
        }
      ];

      // Step 1: Risk assessment should identify critical risk
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        criticalPositions,
        mockPortfolioValue
      );

      expect(portfolioRisk.riskScore).toBeGreaterThan(90); // Critical risk
      expect(portfolioRisk.maxDrawdown.toNumber()).toBeGreaterThan(0.3); // >30% drawdown

      // Step 2: Loss limit enforcer should trigger emergency liquidation
      const shouldLiquidate = await lossLimitEnforcer.checkEmergencyLiquidation(
        mockUserId,
        criticalPositions,
        mockPortfolioValue
      );

      expect(shouldLiquidate).toBe(true);

      // Step 3: Execute emergency liquidation
      const liquidationResult = await lossLimitEnforcer.executeEmergencyLiquidation(
        mockUserId,
        criticalPositions,
        mockPortfolioValue,
        ['DAILY_LOSS_LIMIT', 'MAX_DRAWDOWN', 'CRITICAL_RISK_SCORE']
      );

      expect(liquidationResult.success).toBe(true);
      expect(liquidationResult.positionsLiquidated.length).toBe(2);
      expect(liquidationResult.totalLossAtLiquidation.toNumber()).toBeLessThan(-30000);

      // Step 4: Verify MT5 bridge was called for each position
      expect(mt5Bridge.closePosition).toHaveBeenCalledTimes(2);
      expect(mt5Bridge.closePosition).toHaveBeenCalledWith('crit-1');
      expect(mt5Bridge.closePosition).toHaveBeenCalledWith('crit-2');
    });

    it('should prioritize liquidation by risk contribution', async () => {
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(8000),
        maxDrawdownLimit: new Decimal(0.12),
        positionSizeLimit: new Decimal(0.25)
      });

      // Mixed risk positions
      const mixedPositions: Position[] = [
        {
          id: 'low-risk',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(30000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1990),
          unrealizedPnl: new Decimal(-300), // Small loss
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1970),
          takeProfit: new Decimal(1.2050)
        },
        {
          id: 'high-risk',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(250000),
          entryPrice: new Decimal(1.3000),
          currentPrice: new Decimal(1.2850),
          unrealizedPnl: new Decimal(-37500), // Large loss, high risk
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.2750),
          takeProfit: new Decimal(1.3400)
        },
        {
          id: 'medium-risk',
          userId: mockUserId,
          symbol: 'USDJPY',
          size: new Decimal(100000),
          entryPrice: new Decimal(150.00),
          currentPrice: new Decimal(148.50),
          unrealizedPnl: new Decimal(-10000), // Medium loss
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(147.00),
          takeProfit: new Decimal(152.00)
        }
      ];

      // Identify positions for liquidation based on risk contribution
      const positionsToLiquidate = await lossLimitEnforcer.identifyPositionsForLiquidation(
        mockUserId,
        mixedPositions,
        mockPortfolioValue
      );

      // Should prioritize highest risk position first
      expect(positionsToLiquidate[0].id).toBe('high-risk');
      expect(positionsToLiquidate.length).toBeGreaterThan(0);

      // Execute prioritized liquidation
      const liquidationResult = await lossLimitEnforcer.executeEmergencyLiquidation(
        mockUserId,
        positionsToLiquidate,
        mockPortfolioValue,
        ['DAILY_LOSS_LIMIT']
      );

      expect(liquidationResult.success).toBe(true);
      
      // Verify liquidation order prioritizes risk
      const firstLiquidated = liquidationResult.positionsLiquidated[0];
      expect(firstLiquidated.unrealizedPnl.toNumber()).toBeLessThan(-30000);
    });
  });

  describe('Partial Liquidation Scenarios', () => {
    it('should perform partial liquidation to bring risk within acceptable limits', async () => {
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(10000),
        maxDrawdownLimit: new Decimal(0.15),
        positionSizeLimit: new Decimal(0.30)
      });

      const overexposedPositions: Position[] = [
        {
          id: 'keep-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(50000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2010),
          unrealizedPnl: new Decimal(500), // Profitable
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        },
        {
          id: 'liquidate-1',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(300000),
          entryPrice: new Decimal(1.3000),
          currentPrice: new Decimal(1.2950),
          unrealizedPnl: new Decimal(-15000), // Exceeds daily limit
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.2850),
          takeProfit: new Decimal(1.3200)
        },
        {
          id: 'keep-2',
          userId: mockUserId,
          symbol: 'USDJPY',
          size: new Decimal(75000),
          entryPrice: new Decimal(150.00),
          currentPrice: new Decimal(150.30),
          unrealizedPnl: new Decimal(1500), // Small profit
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(149.00),
          takeProfit: new Decimal(152.00)
        }
      ];

      // Should identify only the problematic position
      const positionsToLiquidate = await lossLimitEnforcer.identifyPositionsForLiquidation(
        mockUserId,
        overexposedPositions,
        mockPortfolioValue
      );

      expect(positionsToLiquidate.length).toBe(1);
      expect(positionsToLiquidate[0].id).toBe('liquidate-1');

      // After liquidation, remaining positions should be within limits
      const remainingPositions = overexposedPositions.filter(
        pos => !positionsToLiquidate.find(liq => liq.id === pos.id)
      );

      const postLiquidationRisk = await riskManager.calculatePortfolioRisk(
        remainingPositions,
        mockPortfolioValue
      );

      expect(postLiquidationRisk.riskScore).toBeLessThan(70); // Acceptable risk level
    });

    it('should handle position reduction instead of full liquidation when possible', async () => {
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(5000),
        maxDrawdownLimit: new Decimal(0.08),
        positionSizeLimit: new Decimal(0.20) // 20% max position size
      });

      const oversizedPosition: Position = {
        id: 'oversize-1',
        userId: mockUserId,
        symbol: 'EURUSD',
        size: new Decimal(200000), // Large position
        entryPrice: new Decimal(1.2000),
        currentPrice: new Decimal(1.1980),
        unrealizedPnl: new Decimal(-4000), // Within daily limit but oversized
        direction: 'buy',
        openTime: new Date(),
        stopLoss: new Decimal(1.1950),
        takeProfit: new Decimal(1.2100)
      };

      // Should suggest position size reduction rather than full liquidation
      const reduction = await lossLimitEnforcer.calculatePositionReduction(
        mockUserId,
        oversizedPosition,
        mockPortfolioValue
      );

      expect(reduction.shouldReduce).toBe(true);
      expect(reduction.recommendedSize.toNumber()).toBeLessThan(200000);
      expect(reduction.recommendedSize.toNumber()).toBeGreaterThan(0);

      // Recommended size should be within 20% limit
      const maxAllowedValue = mockPortfolioValue.mul(0.20);
      expect(reduction.recommendedSize.toNumber()).toBeLessThanOrEqual(maxAllowedValue.toNumber());
    });
  });

  describe('Market Conditions Impact', () => {
    it('should adjust liquidation strategy based on market volatility', async () => {
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(6000),
        maxDrawdownLimit: new Decimal(0.10),
        positionSizeLimit: new Decimal(0.25)
      });

      // High volatility scenario
      const volatilePositions: Position[] = [
        {
          id: 'vol-1',
          userId: mockUserId,
          symbol: 'GBPJPY', // Typically high volatility pair
          size: new Decimal(100000),
          entryPrice: new Decimal(180.00),
          currentPrice: new Decimal(177.50),
          unrealizedPnl: new Decimal(-13888.89), // Large loss due to volatility
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(175.00),
          takeProfit: new Decimal(185.00)
        }
      ];

      // Market conditions with high volatility
      const marketConditions = {
        volatilityIndex: 0.85, // High volatility
        liquidityScore: 0.30, // Poor liquidity
        spreadWidening: 2.5 // Spreads 2.5x normal
      };

      // Should adjust liquidation approach for volatile market
      const liquidationStrategy = await lossLimitEnforcer.planLiquidationStrategy(
        mockUserId,
        volatilePositions,
        mockPortfolioValue,
        marketConditions
      );

      expect(liquidationStrategy.isGradual).toBe(true); // Gradual liquidation in volatile markets
      expect(liquidationStrategy.maxSlippageTolerance.toNumber()).toBeGreaterThan(0.01); // Higher slippage tolerance
      expect(liquidationStrategy.timeDelayBetweenOrders).toBeGreaterThan(5000); // Delays between orders
    });

    it('should handle illiquid market conditions during emergency liquidation', async () => {
      const illiquidPositions: Position[] = [
        {
          id: 'illiquid-1',
          userId: mockUserId,
          symbol: 'EXOTIC/USD', // Exotic currency pair
          size: new Decimal(150000),
          entryPrice: new Decimal(1.5000),
          currentPrice: new Decimal(1.4700),
          unrealizedPnl: new Decimal(-30000),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.4500),
          takeProfit: new Decimal(1.5500)
        }
      ];

      // Illiquid market conditions
      const illiquidMarketConditions = {
        volatilityIndex: 0.60,
        liquidityScore: 0.15, // Very poor liquidity
        spreadWidening: 5.0 // Spreads 5x wider
      };

      // Mock MT5 bridge to simulate execution difficulties
      vi.spyOn(mt5Bridge, 'closePosition').mockImplementation(async (positionId: string) => ({
        success: false,
        error: 'Insufficient liquidity',
        positionId,
        partiallyFilled: true,
        filledAmount: new Decimal(75000), // Only half filled
        finalPrice: new Decimal(1.4650), // Worse price due to slippage
        realizedPnl: new Decimal(-35000) // Worse than expected
      }));

      const liquidationResult = await lossLimitEnforcer.executeEmergencyLiquidation(
        mockUserId,
        illiquidPositions,
        mockPortfolioValue,
        ['DAILY_LOSS_LIMIT'],
        illiquidMarketConditions
      );

      // Should handle partial execution
      expect(liquidationResult.hasPartialExecutions).toBe(true);
      expect(liquidationResult.totalSlippage.toNumber()).toBeGreaterThan(0);
      expect(liquidationResult.executionSummary.averageSlippage).toBeGreaterThan(0.01);
    });
  });

  describe('Post-Liquidation Recovery', () => {
    it('should implement account restrictions after emergency liquidation', async () => {
      const massiveLossPositions: Position[] = [
        {
          id: 'massive-loss',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(500000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1700),
          unrealizedPnl: new Decimal(-125000), // 125k loss
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1600),
          takeProfit: new Decimal(1.2500)
        }
      ];

      // Execute catastrophic liquidation
      const liquidationResult = await lossLimitEnforcer.executeEmergencyLiquidation(
        mockUserId,
        massiveLossPositions,
        mockPortfolioValue,
        ['CRITICAL_RISK_SCORE', 'ACCOUNT_PROTECTION']
      );

      expect(liquidationResult.success).toBe(true);

      // Should implement post-liquidation restrictions
      const restrictions = await lossLimitEnforcer.getPostLiquidationRestrictions(mockUserId);
      
      expect(restrictions.tradingDisabled).toBe(true);
      expect(restrictions.maxPositionSize.toNumber()).toBeLessThan(5000); // Very small positions only
      expect(restrictions.requiredCoolingOffPeriod).toBeGreaterThan(24 * 60 * 60 * 1000); // At least 24 hours
      expect(restrictions.mandatoryRiskAssessment).toBe(true);

      // Should require explicit re-enabling of trading
      const canTrade = await lossLimitEnforcer.canOpenNewPosition(
        mockUserId,
        'EURUSD',
        new Decimal(1000),
        [],
        new Decimal(10000) // Even with recovered balance
      );

      expect(canTrade).toBe(false);
    });

    it('should provide liquidation analysis and recommendations', async () => {
      const analyzedPositions: Position[] = [
        {
          id: 'analysis-pos',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(200000),
          entryPrice: new Decimal(1.3000),
          currentPrice: new Decimal(1.2800),
          unrealizedPnl: new Decimal(-40000),
          direction: 'buy',
          openTime: new Date('2024-01-01'),
          stopLoss: new Decimal(1.2700),
          takeProfit: new Decimal(1.3400)
        }
      ];

      const liquidationResult = await lossLimitEnforcer.executeEmergencyLiquidation(
        mockUserId,
        analyzedPositions,
        mockPortfolioValue,
        ['MAX_DRAWDOWN']
      );

      // Generate post-liquidation analysis
      const analysis = await lossLimitEnforcer.generateLiquidationAnalysis(
        mockUserId,
        liquidationResult
      );

      expect(analysis.rootCauses).toBeDefined();
      expect(analysis.rootCauses.length).toBeGreaterThan(0);
      expect(analysis.recommendations).toBeDefined();
      expect(analysis.recommendations.length).toBeGreaterThan(0);
      
      // Should identify specific issues
      expect(analysis.rootCauses).toContain('OVERSIZED_POSITION');
      expect(analysis.recommendations.some(rec => 
        rec.includes('position size') || rec.includes('risk management')
      )).toBe(true);
      
      expect(analysis.preventiveMeasures.length).toBeGreaterThan(2);
    });
  });

  describe('Audit Trail and Compliance', () => {
    it('should maintain comprehensive audit trail for all liquidation events', async () => {
      const auditPositions: Position[] = [
        {
          id: 'audit-pos-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1900),
          unrealizedPnl: new Decimal(-10000),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1850),
          takeProfit: new Decimal(1.2150)
        }
      ];

      const liquidationResult = await lossLimitEnforcer.executeEmergencyLiquidation(
        mockUserId,
        auditPositions,
        mockPortfolioValue,
        ['DAILY_LOSS_LIMIT']
      );

      // Retrieve audit trail
      const auditTrail = await lossLimitEnforcer.getAuditTrail(
        mockUserId,
        liquidationResult.liquidationId
      );

      expect(auditTrail.events.length).toBeGreaterThan(3);
      
      // Should have key audit events
      const eventTypes = auditTrail.events.map(e => e.type);
      expect(eventTypes).toContain('RISK_LIMIT_BREACH_DETECTED');
      expect(eventTypes).toContain('EMERGENCY_LIQUIDATION_INITIATED');
      expect(eventTypes).toContain('POSITION_LIQUIDATED');
      expect(eventTypes).toContain('LIQUIDATION_COMPLETED');

      // Each event should have complete details
      auditTrail.events.forEach(event => {
        expect(event.timestamp).toBeInstanceOf(Date);
        expect(event.userId).toBe(mockUserId);
        expect(event.details).toBeDefined();
        expect(event.systemState).toBeDefined();
      });
    });

    it('should generate regulatory compliance reports', async () => {
      const compliancePositions: Position[] = [
        {
          id: 'compliance-pos',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(150000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1850),
          unrealizedPnl: new Decimal(-22500),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1800),
          takeProfit: new Decimal(1.2300)
        }
      ];

      const liquidationResult = await lossLimitEnforcer.executeEmergencyLiquidation(
        mockUserId,
        compliancePositions,
        mockPortfolioValue,
        ['REGULATORY_CAPITAL_REQUIREMENT']
      );

      // Generate compliance report
      const complianceReport = await lossLimitEnforcer.generateComplianceReport(
        mockUserId,
        liquidationResult,
        {
          jurisdiction: 'EU',
          regulatoryFramework: 'MiFID II',
          reportingPeriod: '2024-Q1'
        }
      );

      expect(complianceReport.clientProtectionMeasures).toBeDefined();
      expect(complianceReport.riskManagementCompliance).toBe(true);
      expect(complianceReport.appropriatenessAssessment).toBeDefined();
      expect(complianceReport.executionQuality.bestExecution).toBe(true);
      
      // Should include required regulatory fields
      expect(complianceReport.regulatoryReferences.length).toBeGreaterThan(0);
      expect(complianceReport.clientNotification.sent).toBe(true);
      expect(complianceReport.clientNotification.timestamp).toBeInstanceOf(Date);
    });
  });
});