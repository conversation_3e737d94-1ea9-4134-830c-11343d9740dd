/**
 * Manual Intervention Service
 * 
 * Provides manual override capabilities for the broker failover system
 * Part of Task 8: Manual Intervention System
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import type { 
  ManualInterventionRequest,
  InterventionType,
  InterventionStatus,
  InterventionResult
} from '@golddaddy/types';

interface InterventionSession {
  id: string;
  userId: string;
  type: InterventionType;
  status: InterventionStatus;
  startedAt: Date;
  expiresAt: Date;
  reason: string;
  approvedBy?: string;
  approvedAt?: Date;
  metadata: Record<string, any>;
}

export class ManualInterventionService extends EventEmitter {
  private prisma: PrismaClient;
  private activeSessions: Map<string, InterventionSession> = new Map();
  private approvalRequiredTypes: Set<InterventionType> = new Set([
    'FORCE_FAILOVER',
    'DISABLE_AUTO_FAILOVER',
    'BYPASS_CIRCUIT_BREAKER',
    'EMERGENCY_SHUTDOWN'
  ]);

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  /**
   * Request manual intervention
   */
  async requestIntervention(request: ManualInterventionRequest): Promise<InterventionResult> {
    try {
      // Validate request
      this.validateInterventionRequest(request);

      // Create intervention session
      const sessionId = uuidv4();
      const session: InterventionSession = {
        id: sessionId,
        userId: request.userId,
        type: request.type,
        status: this.requiresApproval(request.type) ? 'PENDING_APPROVAL' : 'ACTIVE',
        startedAt: new Date(),
        expiresAt: new Date(Date.now() + (request.durationMinutes || 60) * 60 * 1000),
        reason: request.reason,
        metadata: request.metadata || {}
      };

      // Store session
      this.activeSessions.set(sessionId, session);

      // Log intervention request
      await this.logInterventionEvent('INTERVENTION_REQUESTED', session);

      // If approval required, notify administrators
      if (this.requiresApproval(request.type)) {
        await this.notifyAdministrators(session);
        
        return {
          success: true,
          sessionId,
          status: 'PENDING_APPROVAL',
          message: 'Intervention request submitted for approval',
          expiresAt: session.expiresAt
        };
      }

      // Execute intervention immediately if no approval required
      const result = await this.executeIntervention(session);
      return result;

    } catch (error) {
      console.error('Manual intervention request failed:', error);
      
      return {
        success: false,
        error: error.message,
        message: 'Failed to process intervention request'
      };
    }
  }

  /**
   * Approve pending intervention
   */
  async approveIntervention(
    sessionId: string, 
    approverUserId: string, 
    approved: boolean,
    comments?: string
  ): Promise<InterventionResult> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return {
          success: false,
          error: 'SESSION_NOT_FOUND',
          message: 'Intervention session not found'
        };
      }

      if (session.status !== 'PENDING_APPROVAL') {
        return {
          success: false,
          error: 'INVALID_STATUS',
          message: 'Intervention is not pending approval'
        };
      }

      // Check if session has expired
      if (new Date() > session.expiresAt) {
        session.status = 'EXPIRED';
        await this.logInterventionEvent('INTERVENTION_EXPIRED', session);
        return {
          success: false,
          error: 'SESSION_EXPIRED',
          message: 'Intervention session has expired'
        };
      }

      if (approved) {
        // Approve and execute
        session.status = 'ACTIVE';
        session.approvedBy = approverUserId;
        session.approvedAt = new Date();
        session.metadata.approvalComments = comments;

        await this.logInterventionEvent('INTERVENTION_APPROVED', session, {
          approvedBy: approverUserId,
          comments
        });

        const result = await this.executeIntervention(session);
        return result;
      } else {
        // Reject intervention
        session.status = 'REJECTED';
        session.metadata.rejectionComments = comments;

        await this.logInterventionEvent('INTERVENTION_REJECTED', session, {
          rejectedBy: approverUserId,
          comments
        });

        this.activeSessions.delete(sessionId);

        return {
          success: true,
          sessionId,
          status: 'REJECTED',
          message: 'Intervention request rejected'
        };
      }

    } catch (error) {
      console.error('Intervention approval failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to process intervention approval'
      };
    }
  }

  /**
   * Execute the intervention
   */
  private async executeIntervention(session: InterventionSession): Promise<InterventionResult> {
    try {
      session.status = 'EXECUTING';
      
      let result: InterventionResult;

      switch (session.type) {
        case 'FORCE_FAILOVER':
          result = await this.executeForceFailover(session);
          break;

        case 'DISABLE_AUTO_FAILOVER':
          result = await this.executeDisableAutoFailover(session);
          break;

        case 'ENABLE_AUTO_FAILOVER':
          result = await this.executeEnableAutoFailover(session);
          break;

        case 'BYPASS_CIRCUIT_BREAKER':
          result = await this.executeBypassCircuitBreaker(session);
          break;

        case 'RESET_BROKER_STATUS':
          result = await this.executeResetBrokerStatus(session);
          break;

        case 'EMERGENCY_SHUTDOWN':
          result = await this.executeEmergencyShutdown(session);
          break;

        case 'MANUAL_HEALTH_CHECK':
          result = await this.executeManualHealthCheck(session);
          break;

        default:
          throw new Error(`Unsupported intervention type: ${session.type}`);
      }

      // Update session status based on result
      session.status = result.success ? 'COMPLETED' : 'FAILED';
      
      await this.logInterventionEvent(
        result.success ? 'INTERVENTION_COMPLETED' : 'INTERVENTION_FAILED', 
        session,
        { result }
      );

      // Schedule session cleanup
      setTimeout(() => {
        this.activeSessions.delete(session.id);
      }, 300000); // Clean up after 5 minutes

      return {
        ...result,
        sessionId: session.id,
        status: session.status
      };

    } catch (error) {
      console.error('Intervention execution failed:', error);
      session.status = 'FAILED';
      
      await this.logInterventionEvent('INTERVENTION_FAILED', session, {
        error: error.message
      });

      return {
        success: false,
        sessionId: session.id,
        status: 'FAILED',
        error: error.message,
        message: 'Intervention execution failed'
      };
    }
  }

  /**
   * Force failover to specific broker
   */
  private async executeForceFailover(session: InterventionSession): Promise<InterventionResult> {
    const { fromBrokerId, toBrokerId } = session.metadata;

    if (!fromBrokerId || !toBrokerId) {
      throw new Error('fromBrokerId and toBrokerId are required for force failover');
    }

    // Emit failover event for the failover engine to handle
    this.emit('forceFailover', {
      sessionId: session.id,
      userId: session.userId,
      fromBrokerId,
      toBrokerId,
      trigger: 'MANUAL_TRIGGER',
      reason: session.reason
    });

    return {
      success: true,
      message: `Force failover initiated from ${fromBrokerId} to ${toBrokerId}`,
      details: {
        fromBroker: fromBrokerId,
        toBroker: toBrokerId,
        trigger: 'MANUAL_TRIGGER'
      }
    };
  }

  /**
   * Disable automatic failover
   */
  private async executeDisableAutoFailover(session: InterventionSession): Promise<InterventionResult> {
    const { brokerId } = session.metadata;

    // Emit event to disable auto-failover
    this.emit('disableAutoFailover', {
      sessionId: session.id,
      brokerId: brokerId || 'ALL',
      reason: session.reason,
      expiresAt: session.expiresAt
    });

    return {
      success: true,
      message: `Automatic failover disabled${brokerId ? ` for broker ${brokerId}` : ' system-wide'}`,
      details: {
        scope: brokerId || 'SYSTEM_WIDE',
        expiresAt: session.expiresAt
      }
    };
  }

  /**
   * Enable automatic failover
   */
  private async executeEnableAutoFailover(session: InterventionSession): Promise<InterventionResult> {
    const { brokerId } = session.metadata;

    // Emit event to enable auto-failover
    this.emit('enableAutoFailover', {
      sessionId: session.id,
      brokerId: brokerId || 'ALL',
      reason: session.reason
    });

    return {
      success: true,
      message: `Automatic failover enabled${brokerId ? ` for broker ${brokerId}` : ' system-wide'}`,
      details: {
        scope: brokerId || 'SYSTEM_WIDE'
      }
    };
  }

  /**
   * Bypass circuit breaker
   */
  private async executeBypassCircuitBreaker(session: InterventionSession): Promise<InterventionResult> {
    const { brokerId, service } = session.metadata;

    if (!brokerId || !service) {
      throw new Error('brokerId and service are required for circuit breaker bypass');
    }

    // Emit event to bypass circuit breaker
    this.emit('bypassCircuitBreaker', {
      sessionId: session.id,
      brokerId,
      service,
      reason: session.reason,
      expiresAt: session.expiresAt
    });

    return {
      success: true,
      message: `Circuit breaker bypassed for ${service} on broker ${brokerId}`,
      details: {
        brokerId,
        service,
        expiresAt: session.expiresAt
      }
    };
  }

  /**
   * Reset broker status
   */
  private async executeResetBrokerStatus(session: InterventionSession): Promise<InterventionResult> {
    const { brokerId } = session.metadata;

    if (!brokerId) {
      throw new Error('brokerId is required for broker status reset');
    }

    try {
      // Reset broker in database
      await this.prisma.brokerConfiguration.update({
        where: { id: brokerId },
        data: {
          status: 'ACTIVE',
          isHealthy: true,
          failureCount: 0,
          lastConnectionTest: new Date()
        }
      });

      // Emit event for other services
      this.emit('brokerStatusReset', {
        sessionId: session.id,
        brokerId,
        reason: session.reason
      });

      return {
        success: true,
        message: `Broker ${brokerId} status reset to active`,
        details: { brokerId }
      };

    } catch (error) {
      throw new Error(`Failed to reset broker status: ${error.message}`);
    }
  }

  /**
   * Emergency shutdown
   */
  private async executeEmergencyShutdown(session: InterventionSession): Promise<InterventionResult> {
    const { scope } = session.metadata; // 'BROKER' | 'SYSTEM'

    // Emit emergency shutdown event
    this.emit('emergencyShutdown', {
      sessionId: session.id,
      scope: scope || 'SYSTEM',
      reason: session.reason,
      initiatedBy: session.userId
    });

    return {
      success: true,
      message: `Emergency shutdown initiated (${scope || 'SYSTEM'} scope)`,
      details: {
        scope: scope || 'SYSTEM',
        critical: true
      }
    };
  }

  /**
   * Manual health check
   */
  private async executeManualHealthCheck(session: InterventionSession): Promise<InterventionResult> {
    const { brokerId } = session.metadata;

    // Emit event to trigger immediate health check
    this.emit('manualHealthCheck', {
      sessionId: session.id,
      brokerId: brokerId || 'ALL',
      reason: session.reason
    });

    return {
      success: true,
      message: `Manual health check initiated${brokerId ? ` for broker ${brokerId}` : ' for all brokers'}`,
      details: {
        scope: brokerId || 'ALL_BROKERS'
      }
    };
  }

  /**
   * Get active intervention sessions
   */
  getActiveSessions(userId?: string): InterventionSession[] {
    const sessions = Array.from(this.activeSessions.values());
    
    if (userId) {
      return sessions.filter(session => session.userId === userId);
    }
    
    return sessions;
  }

  /**
   * Cancel intervention session
   */
  async cancelIntervention(sessionId: string, userId: string, reason?: string): Promise<InterventionResult> {
    try {
      const session = this.activeSessions.get(sessionId);
      if (!session) {
        return {
          success: false,
          error: 'SESSION_NOT_FOUND',
          message: 'Intervention session not found'
        };
      }

      // Check if user can cancel (owner or admin)
      if (session.userId !== userId && !await this.isAdminUser(userId)) {
        return {
          success: false,
          error: 'UNAUTHORIZED',
          message: 'Not authorized to cancel this intervention'
        };
      }

      session.status = 'CANCELLED';
      session.metadata.cancellationReason = reason;

      await this.logInterventionEvent('INTERVENTION_CANCELLED', session, {
        cancelledBy: userId,
        reason
      });

      this.activeSessions.delete(sessionId);

      return {
        success: true,
        sessionId,
        status: 'CANCELLED',
        message: 'Intervention cancelled successfully'
      };

    } catch (error) {
      console.error('Intervention cancellation failed:', error);
      return {
        success: false,
        error: error.message,
        message: 'Failed to cancel intervention'
      };
    }
  }

  /**
   * Validate intervention request
   */
  private validateInterventionRequest(request: ManualInterventionRequest): void {
    if (!request.userId) {
      throw new Error('userId is required');
    }

    if (!request.type) {
      throw new Error('intervention type is required');
    }

    if (!request.reason || request.reason.length < 10) {
      throw new Error('reason is required and must be at least 10 characters');
    }

    // Validate type-specific requirements
    switch (request.type) {
      case 'FORCE_FAILOVER':
        if (!request.metadata?.fromBrokerId || !request.metadata?.toBrokerId) {
          throw new Error('fromBrokerId and toBrokerId are required for force failover');
        }
        break;

      case 'BYPASS_CIRCUIT_BREAKER':
        if (!request.metadata?.brokerId || !request.metadata?.service) {
          throw new Error('brokerId and service are required for circuit breaker bypass');
        }
        break;

      case 'RESET_BROKER_STATUS':
        if (!request.metadata?.brokerId) {
          throw new Error('brokerId is required for broker status reset');
        }
        break;
    }
  }

  /**
   * Check if intervention type requires approval
   */
  private requiresApproval(type: InterventionType): boolean {
    return this.approvalRequiredTypes.has(type);
  }

  /**
   * Check if user is admin
   */
  private async isAdminUser(userId: string): Promise<boolean> {
    // In a real implementation, this would check user roles
    // For now, we'll use a simple check
    return process.env.ADMIN_USER_IDS?.split(',').includes(userId) || false;
  }

  /**
   * Notify administrators of pending approval
   */
  private async notifyAdministrators(session: InterventionSession): Promise<void> {
    // Emit notification event for external systems to handle
    this.emit('approvalRequired', {
      sessionId: session.id,
      type: session.type,
      userId: session.userId,
      reason: session.reason,
      expiresAt: session.expiresAt,
      metadata: session.metadata
    });
  }

  /**
   * Log intervention event to audit trail
   */
  private async logInterventionEvent(
    eventType: string, 
    session: InterventionSession, 
    additionalData?: Record<string, any>
  ): Promise<void> {
    try {
      const logData = {
        sessionId: session.id,
        userId: session.userId,
        interventionType: session.type,
        status: session.status,
        reason: session.reason,
        ...additionalData
      };

      // Emit audit event
      this.emit('auditEvent', {
        actionType: eventType,
        userId: session.userId,
        message: `Manual intervention: ${eventType.toLowerCase().replace('_', ' ')}`,
        details: logData,
        severity: this.getEventSeverity(eventType),
        complianceRelevant: true
      });

    } catch (error) {
      console.error('Failed to log intervention event:', error);
    }
  }

  /**
   * Get event severity for logging
   */
  private getEventSeverity(eventType: string): 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL' {
    const criticalEvents = ['EMERGENCY_SHUTDOWN', 'INTERVENTION_FAILED'];
    const highEvents = ['FORCE_FAILOVER', 'BYPASS_CIRCUIT_BREAKER', 'INTERVENTION_REJECTED'];
    const mediumEvents = ['DISABLE_AUTO_FAILOVER', 'INTERVENTION_APPROVED'];

    if (criticalEvents.includes(eventType)) return 'CRITICAL';
    if (highEvents.includes(eventType)) return 'HIGH';
    if (mediumEvents.includes(eventType)) return 'MEDIUM';
    return 'LOW';
  }

  /**
   * Clean up expired sessions
   */
  cleanupExpiredSessions(): void {
    const now = new Date();
    const expiredSessions: string[] = [];

    for (const [sessionId, session] of this.activeSessions) {
      if (now > session.expiresAt && session.status !== 'COMPLETED') {
        session.status = 'EXPIRED';
        expiredSessions.push(sessionId);
        
        this.logInterventionEvent('INTERVENTION_EXPIRED', session);
      }
    }

    // Remove expired sessions
    expiredSessions.forEach(sessionId => {
      this.activeSessions.delete(sessionId);
    });

    if (expiredSessions.length > 0) {
      console.log(`🧹 Cleaned up ${expiredSessions.length} expired intervention sessions`);
    }
  }

  /**
   * Start cleanup timer
   */
  startCleanupTimer(intervalMs: number = 300000): void { // 5 minutes
    setInterval(() => {
      this.cleanupExpiredSessions();
    }, intervalMs);
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down Manual Intervention Service...');
    
    // Cancel all active sessions that haven't been executed
    const activeSessions = Array.from(this.activeSessions.values());
    for (const session of activeSessions) {
      if (session.status === 'PENDING_APPROVAL' || session.status === 'ACTIVE') {
        session.status = 'CANCELLED';
        await this.logInterventionEvent('INTERVENTION_CANCELLED_ON_SHUTDOWN', session);
      }
    }
    
    // Clear all sessions
    this.activeSessions.clear();
    
    console.log('✅ Manual Intervention Service shut down gracefully');
  }
}

export default ManualInterventionService;