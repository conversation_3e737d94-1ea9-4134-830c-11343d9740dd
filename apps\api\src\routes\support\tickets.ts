import { Router, Request, Response } from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { SupportTicketService, CreateSupportTicketRequest } from '../../services/support/SupportTicketService'
import { authenticateToken } from '../../middleware/auth'
import { rateLimiter } from '../../middleware/rateLimiter'

const router = Router()
const supportTicketService = new SupportTicketService()

// Rate limiting for support tickets
const createTicketLimiter = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // limit each IP to 3 ticket creations per windowMs
  message: { error: 'Too many support tickets created. Please try again later.' }
})

const generalLimiter = rateLimiter({
  windowMs: 15 * 60 * 1000,
  max: 100
})

// Validation middleware
const validateCreateTicket = [
  body('category')
    .isIn(['technical', 'trading', 'account', 'billing', 'feature', 'general'])
    .withMessage('Invalid category'),
  body('priority')
    .isIn(['low', 'medium', 'high', 'critical'])
    .withMessage('Invalid priority'),
  body('subject')
    .isLength({ min: 5, max: 200 })
    .withMessage('Subject must be between 5 and 200 characters'),
  body('description')
    .isLength({ min: 20, max: 2000 })
    .withMessage('Description must be between 20 and 2000 characters'),
  body('userEmail')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('userName')
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  body('systemInfo.userAgent')
    .optional()
    .isString(),
  body('systemInfo.url')
    .optional()
    .isURL(),
  body('systemInfo.timestamp')
    .optional()
    .isISO8601(),
  body('systemInfo.accountId')
    .optional()
    .isString()
]

const validateTicketNumber = [
  param('ticketNumber')
    .matches(/^GD-[A-Z0-9]+-[A-Z0-9]+$/)
    .withMessage('Invalid ticket number format')
]

const validateUpdateStatus = [
  ...validateTicketNumber,
  body('status')
    .isIn(['open', 'in_progress', 'waiting_for_response', 'resolved', 'closed'])
    .withMessage('Invalid status'),
  body('resolutionNotes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Resolution notes must be less than 1000 characters')
]

const validateAddMessage = [
  ...validateTicketNumber,
  body('message')
    .isLength({ min: 1, max: 2000 })
    .withMessage('Message must be between 1 and 2000 characters'),
  body('authorName')
    .isLength({ min: 2, max: 100 })
    .withMessage('Author name must be between 2 and 100 characters'),
  body('authorEmail')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
  body('isInternal')
    .optional()
    .isBoolean()
]

const validateFeedback = [
  ...validateTicketNumber,
  body('rating')
    .isInt({ min: 1, max: 5 })
    .withMessage('Rating must be between 1 and 5'),
  body('feedback')
    .isLength({ min: 1, max: 500 })
    .withMessage('Feedback must be between 1 and 500 characters')
]

const handleValidationErrors = (req: Request, res: Response, next: Function) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    })
  }
  next()
}

// Create a new support ticket
router.post('/',
  createTicketLimiter,
  supportTicketService.upload.array('attachments', 5),
  validateCreateTicket,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const createRequest: CreateSupportTicketRequest = {
        ...req.body,
        attachments: req.files as Express.Multer.File[]
      }

      const ticket = await supportTicketService.createTicket(createRequest)
      
      res.status(201).json({
        success: true,
        data: {
          ticketNumber: ticket.ticketNumber,
          status: ticket.status,
          createdAt: ticket.createdAt
        },
        message: 'Support ticket created successfully'
      })
    } catch (error) {
      console.error('Error creating support ticket:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to create support ticket'
      })
    }
  }
)

// Get tickets (with filtering and pagination)
router.get('/',
  generalLimiter,
  authenticateToken,
  [
    query('page').optional().isInt({ min: 1 }).toInt(),
    query('pageSize').optional().isInt({ min: 1, max: 100 }).toInt(),
    query('status').optional().isString(),
    query('category').optional().isString(),
    query('priority').optional().isString(),
    query('search').optional().isString().isLength({ max: 200 })
  ],
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const page = req.query.page as number || 1
      const pageSize = req.query.pageSize as number || 20
      
      const filters: any = {}
      
      if (req.query.status) {
        filters.status = (req.query.status as string).split(',')
      }
      
      if (req.query.category) {
        filters.category = (req.query.category as string).split(',')
      }
      
      if (req.query.priority) {
        filters.priority = (req.query.priority as string).split(',')
      }
      
      if (req.query.search) {
        filters.searchQuery = req.query.search as string
      }
      
      const result = await supportTicketService.getTickets(filters, page, pageSize)
      
      res.json({
        success: true,
        data: result.tickets,
        pagination: {
          page,
          pageSize,
          total: result.total,
          pages: Math.ceil(result.total / pageSize)
        }
      })
    } catch (error) {
      console.error('Error getting tickets:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to get tickets'
      })
    }
  }
)

// Get a specific ticket by ticket number
router.get('/:ticketNumber',
  generalLimiter,
  validateTicketNumber,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const ticket = await supportTicketService.getTicketByNumber(req.params.ticketNumber)
      
      if (!ticket) {
        return res.status(404).json({
          success: false,
          error: 'Ticket not found'
        })
      }
      
      res.json({
        success: true,
        data: ticket
      })
    } catch (error) {
      console.error('Error getting ticket:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to get ticket'
      })
    }
  }
)

// Get ticket messages
router.get('/:ticketNumber/messages',
  generalLimiter,
  validateTicketNumber,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const ticket = await supportTicketService.getTicketByNumber(req.params.ticketNumber)
      
      if (!ticket) {
        return res.status(404).json({
          success: false,
          error: 'Ticket not found'
        })
      }
      
      const messages = await supportTicketService.getTicketMessages(ticket.id)
      
      res.json({
        success: true,
        data: messages
      })
    } catch (error) {
      console.error('Error getting ticket messages:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to get ticket messages'
      })
    }
  }
)

// Update ticket status (support staff only)
router.patch('/:ticketNumber/status',
  generalLimiter,
  authenticateToken,
  validateUpdateStatus,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      // Check if user has support role (implement role checking as needed)
      const userRole = (req as any).user?.role
      if (userRole !== 'support' && userRole !== 'admin') {
        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions'
        })
      }
      
      const ticket = await supportTicketService.getTicketByNumber(req.params.ticketNumber)
      
      if (!ticket) {
        return res.status(404).json({
          success: false,
          error: 'Ticket not found'
        })
      }
      
      const updatedTicket = await supportTicketService.updateTicketStatus(
        ticket.id,
        req.body.status,
        req.body.resolutionNotes
      )
      
      res.json({
        success: true,
        data: updatedTicket,
        message: 'Ticket status updated successfully'
      })
    } catch (error) {
      console.error('Error updating ticket status:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to update ticket status'
      })
    }
  }
)

// Add a message to a ticket
router.post('/:ticketNumber/messages',
  generalLimiter,
  supportTicketService.upload.array('attachments', 3),
  validateAddMessage,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const ticket = await supportTicketService.getTicketByNumber(req.params.ticketNumber)
      
      if (!ticket) {
        return res.status(404).json({
          success: false,
          error: 'Ticket not found'
        })
      }
      
      // Determine if message is from support
      const userRole = (req as any).user?.role
      const fromSupport = userRole === 'support' || userRole === 'admin'
      
      const message = await supportTicketService.addMessage(
        ticket.id,
        req.body.message,
        fromSupport,
        req.body.authorName,
        req.body.authorEmail,
        req.files as Express.Multer.File[],
        req.body.isInternal || false
      )
      
      res.status(201).json({
        success: true,
        data: message,
        message: 'Message added successfully'
      })
    } catch (error) {
      console.error('Error adding message:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to add message'
      })
    }
  }
)

// Submit customer feedback for a ticket
router.post('/:ticketNumber/feedback',
  generalLimiter,
  validateFeedback,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const ticket = await supportTicketService.getTicketByNumber(req.params.ticketNumber)
      
      if (!ticket) {
        return res.status(404).json({
          success: false,
          error: 'Ticket not found'
        })
      }
      
      await supportTicketService.submitFeedback(
        req.params.ticketNumber,
        req.body.rating,
        req.body.feedback
      )
      
      res.json({
        success: true,
        message: 'Feedback submitted successfully'
      })
    } catch (error) {
      console.error('Error submitting feedback:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to submit feedback'
      })
    }
  }
)

// Get support statistics (support staff only)
router.get('/stats/overview',
  generalLimiter,
  authenticateToken,
  async (req: Request, res: Response) => {
    try {
      const userRole = (req as any).user?.role
      if (userRole !== 'support' && userRole !== 'admin') {
        return res.status(403).json({
          success: false,
          error: 'Insufficient permissions'
        })
      }
      
      const dateRange = req.query.dateRange ? {
        start: new Date(req.query.start as string),
        end: new Date(req.query.end as string)
      } : undefined
      
      const stats = await supportTicketService.getStats(dateRange)
      
      res.json({
        success: true,
        data: stats
      })
    } catch (error) {
      console.error('Error getting stats:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to get statistics'
      })
    }
  }
)

export default router