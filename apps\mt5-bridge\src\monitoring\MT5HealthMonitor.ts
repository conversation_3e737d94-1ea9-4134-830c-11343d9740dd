/**
 * MT5 Health Monitor
 * 
 * Comprehensive health monitoring for MT5 connections:
 * - 99%+ uptime monitoring with health check endpoints
 * - Performance metrics collection (latency, success rate, connection health)
 * - Alert generation for degraded performance or connection issues
 */

import { EventEmitter } from 'events';
import type { StandardBrokerAdapter } from '../adapters/StandardBrokerAdapter';

export interface HealthMetrics {
  uptime: number;
  connectionHealth: number;
  averageLatency: number;
  successRate: number;
  errorRate: number;
  lastCheck: Date;
}

export interface AlertCondition {
  condition: 'uptime' | 'latency' | 'error_rate' | 'connection_health';
  threshold: number;
  operator: 'gt' | 'lt' | 'eq';
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export class MT5HealthMonitor extends EventEmitter {
  private healthMetrics: Map<string, HealthMetrics> = new Map();
  private alertConditions: AlertCondition[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(private config: {
    checkInterval?: number;
    uptimeThreshold?: number;
    latencyThreshold?: number;
    errorRateThreshold?: number;
  } = {}) {
    super();
    
    this.config = {
      checkInterval: 30000,  // 30 seconds
      uptimeThreshold: 0.99, // 99%
      latencyThreshold: 1000, // 1 second
      errorRateThreshold: 0.05, // 5%
      ...config
    };

    this.initializeAlertConditions();
    this.startMonitoring();
  }

  async checkHealth(adapter: StandardBrokerAdapter): Promise<HealthMetrics> {
    const brokerId = adapter.getConfig().id;
    const startTime = Date.now();
    
    try {
      const isHealthy = await adapter.testConnection();
      const stats = adapter.getConnectionStats();
      const healthScore = await adapter.getHealthScore();
      
      const metrics: HealthMetrics = {
        uptime: stats.uptime,
        connectionHealth: healthScore,
        averageLatency: stats.averageResponseTime,
        successRate: stats.totalRequests > 0 ? stats.successfulRequests / stats.totalRequests : 1,
        errorRate: stats.totalRequests > 0 ? stats.failedRequests / stats.totalRequests : 0,
        lastCheck: new Date()
      };

      this.healthMetrics.set(brokerId, metrics);
      this.checkAlertConditions(brokerId, metrics);
      
      return metrics;
    } catch (error) {
      console.error(`Health check failed for ${brokerId}:`, error);
      const errorMetrics: HealthMetrics = {
        uptime: 0,
        connectionHealth: 0,
        averageLatency: Date.now() - startTime,
        successRate: 0,
        errorRate: 1,
        lastCheck: new Date()
      };
      
      this.healthMetrics.set(brokerId, errorMetrics);
      this.emit('healthCheckFailed', { brokerId, error });
      
      return errorMetrics;
    }
  }

  getHealthMetrics(brokerId?: string): Map<string, HealthMetrics> | HealthMetrics | null {
    if (brokerId) {
      return this.healthMetrics.get(brokerId) || null;
    }
    return new Map(this.healthMetrics);
  }

  addAlertCondition(condition: AlertCondition): void {
    this.alertConditions.push(condition);
  }

  private initializeAlertConditions(): void {
    this.alertConditions = [
      { condition: 'uptime', threshold: this.config.uptimeThreshold!, operator: 'lt', severity: 'critical' },
      { condition: 'latency', threshold: this.config.latencyThreshold!, operator: 'gt', severity: 'high' },
      { condition: 'error_rate', threshold: this.config.errorRateThreshold!, operator: 'gt', severity: 'medium' },
      { condition: 'connection_health', threshold: 0.5, operator: 'lt', severity: 'high' }
    ];
  }

  private checkAlertConditions(brokerId: string, metrics: HealthMetrics): void {
    for (const condition of this.alertConditions) {
      const value = this.getMetricValue(metrics, condition.condition);
      const shouldAlert = this.evaluateCondition(value, condition.threshold, condition.operator);
      
      if (shouldAlert) {
        this.emit('alert', {
          brokerId,
          condition: condition.condition,
          value,
          threshold: condition.threshold,
          severity: condition.severity,
          timestamp: new Date()
        });
      }
    }
  }

  private getMetricValue(metrics: HealthMetrics, condition: string): number {
    switch (condition) {
      case 'uptime': return metrics.uptime / (24 * 60 * 60 * 1000); // Convert to percentage
      case 'latency': return metrics.averageLatency;
      case 'error_rate': return metrics.errorRate;
      case 'connection_health': return metrics.connectionHealth;
      default: return 0;
    }
  }

  private evaluateCondition(value: number, threshold: number, operator: string): boolean {
    switch (operator) {
      case 'gt': return value > threshold;
      case 'lt': return value < threshold;
      case 'eq': return value === threshold;
      default: return false;
    }
  }

  private startMonitoring(): void {
    this.monitoringInterval = setInterval(() => {
      this.emit('healthCheckRequired');
    }, this.config.checkInterval);
  }

  async shutdown(): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
  }
}