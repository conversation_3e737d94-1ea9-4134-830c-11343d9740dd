import { describe, it, expect, beforeEach } from 'vitest';
import { BacktestingExecutionEngine, BacktestTradeOrder, DEFAULT_EXECUTION_CONFIG } from '../BacktestingExecutionEngine';
import { TradeType, MarketRegime } from '@prisma/client';
import { HistoricalDataPoint } from '../BacktestingDataService';

describe('BacktestingExecutionEngine', () => {
  let engine: BacktestingExecutionEngine;

  beforeEach(() => {
    engine = new BacktestingExecutionEngine(DEFAULT_EXECUTION_CONFIG);
  });

  describe('executeOrder', () => {
    it('should execute a buy order successfully', async () => {
      // Seed Math.random for deterministic test results
      const originalRandom = Math.random;
      let callCount = 0;
      Math.random = () => {
        // Return deterministic values that ensure reasonable execution prices
        const values = [0.5, 0.6, 0.3]; // Moderate values for slippage calculations
        return values[callCount++ % values.length];
      };

      const order: BacktestTradeOrder = {
        id: 'test-order-1',
        type: TradeType.BUY,
        instrument: 'EURUSD',
        quantity: 10000,
        requestedPrice: 1.0500,
        timestamp: new Date('2023-01-01T10:00:00Z'),
        marketConditions: {
          regime: MarketRegime.TRENDING,
          volatility: 0.01,
          spread: 0.8,
          liquidityScore: 0.8,
        },
      };

      const marketData: HistoricalDataPoint = {
        timestamp: new Date('2023-01-01T10:00:00Z'),
        instrument: 'EURUSD',
        timeframe: 'H1' as any,
        open: 1.0495,
        high: 1.0505,
        low: 1.0490,
        close: 1.0500,
        volume: 1000000,
        source: 'MT5' as any,
      };

      try {
        const result = await engine.executeOrder(order, marketData);

        expect(result.success).toBe(true);
        expect(result.orderId).toBe('test-order-1');
        expect(result.executedPrice).toBeGreaterThan(0);
        expect(result.executedQuantity).toBe(10000);
        expect(result.spread).toBeGreaterThan(0);
        expect(result.commission).toBeGreaterThan(0);
        expect(result.executionTime).toBeInstanceOf(Date);
      } finally {
        // Restore original Math.random
        Math.random = originalRandom;
      }
    });

    it('should execute a sell order successfully', async () => {
      // Seed Math.random for deterministic test results
      const originalRandom = Math.random;
      let callCount = 0;
      Math.random = () => {
        // Return deterministic values that ensure reasonable execution prices
        const values = [0.4, 0.7, 0.2]; // Different values for sell order
        return values[callCount++ % values.length];
      };

      const order: BacktestTradeOrder = {
        id: 'test-order-2',
        type: TradeType.SELL,
        instrument: 'EURUSD',
        quantity: 10000,
        requestedPrice: 1.0500,
        timestamp: new Date('2023-01-01T10:00:00Z'),
        marketConditions: {
          regime: MarketRegime.RANGING,
          volatility: 0.005,
          spread: 0.6,
          liquidityScore: 0.9,
        },
      };

      const marketData: HistoricalDataPoint = {
        timestamp: new Date('2023-01-01T10:00:00Z'),
        instrument: 'EURUSD',
        timeframe: 'H1' as any,
        open: 1.0495,
        high: 1.0505,
        low: 1.0490,
        close: 1.0500,
        volume: 1000000,
        source: 'MT5' as any,
      };

      try {
        const result = await engine.executeOrder(order, marketData);

        expect(result.success).toBe(true);
        expect(result.orderId).toBe('test-order-2');
        expect(result.executedPrice).toBeGreaterThan(0);
        expect(result.executedQuantity).toBe(10000);
      } finally {
        // Restore original Math.random
        Math.random = originalRandom;
      }
    });

    it('should handle execution failure', async () => {
      // Create an engine with high failure rate for testing
      const failureEngine = new BacktestingExecutionEngine({
        ...DEFAULT_EXECUTION_CONFIG,
        executionFailureRate: 1.0, // 100% failure rate
      });

      const order: BacktestTradeOrder = {
        id: 'test-order-3',
        type: TradeType.BUY,
        instrument: 'EURUSD',
        quantity: 10000,
        requestedPrice: 1.0500,
        timestamp: new Date('2023-01-01T10:00:00Z'),
      };

      const marketData: HistoricalDataPoint = {
        timestamp: new Date('2023-01-01T10:00:00Z'),
        instrument: 'EURUSD',
        timeframe: 'H1' as any,
        open: 1.0495,
        high: 1.0505,
        low: 1.0490,
        close: 1.0500,
        volume: 1000000,
        source: 'MT5' as any,
      };

      const result = await failureEngine.executeOrder(order, marketData);

      expect(result.success).toBe(false);
      expect(result.failureReason).toContain('Network timeout or broker rejection');
      expect(result.executedPrice).toBe(0);
      expect(result.executedQuantity).toBe(0);
    });

    it('should apply higher spreads during volatile market conditions', async () => {
      const volatileOrder: BacktestTradeOrder = {
        id: 'test-order-4',
        type: TradeType.BUY,
        instrument: 'EURUSD',
        quantity: 10000,
        requestedPrice: 1.0500,
        timestamp: new Date('2023-01-01T10:00:00Z'),
        marketConditions: {
          regime: MarketRegime.VOLATILE,
          volatility: 0.05, // High volatility
          spread: 0.8,
          liquidityScore: 0.5,
        },
      };

      const normalOrder: BacktestTradeOrder = {
        ...volatileOrder,
        id: 'test-order-5',
        marketConditions: {
          regime: MarketRegime.RANGING,
          volatility: 0.005, // Low volatility
          spread: 0.8,
          liquidityScore: 0.9,
        },
      };

      const marketData: HistoricalDataPoint = {
        timestamp: new Date('2023-01-01T10:00:00Z'),
        instrument: 'EURUSD',
        timeframe: 'H1' as any,
        open: 1.0495,
        high: 1.0505,
        low: 1.0490,
        close: 1.0500,
        volume: 1000000,
        source: 'MT5' as any,
      };

      const volatileResult = await engine.executeOrder(volatileOrder, marketData);
      const normalResult = await engine.executeOrder(normalOrder, marketData);

      expect(volatileResult.spread).toBeGreaterThan(normalResult.spread);
    });

    it('should apply larger slippage for larger order sizes', async () => {
      const largeOrder: BacktestTradeOrder = {
        id: 'test-order-6',
        type: TradeType.BUY,
        instrument: 'EURUSD',
        quantity: 100000, // Large size
        requestedPrice: 1.0500,
        timestamp: new Date('2023-01-01T10:00:00Z'),
      };

      const smallOrder: BacktestTradeOrder = {
        ...largeOrder,
        id: 'test-order-7',
        quantity: 10000, // Small size
      };

      const marketData: HistoricalDataPoint = {
        timestamp: new Date('2023-01-01T10:00:00Z'),
        instrument: 'EURUSD',
        timeframe: 'H1' as any,
        open: 1.0495,
        high: 1.0505,
        low: 1.0490,
        close: 1.0500,
        volume: 1000000,
        source: 'MT5' as any,
      };

      const largeResult = await engine.executeOrder(largeOrder, marketData);
      const smallResult = await engine.executeOrder(smallOrder, marketData);

      // Large orders should have higher execution costs on average
      expect(Math.abs(largeResult.slippage)).toBeGreaterThanOrEqual(Math.abs(smallResult.slippage) * 0.8);
    });
  });

  describe('executeBatch', () => {
    it('should execute multiple orders in batch', async () => {
      const orders: BacktestTradeOrder[] = [
        {
          id: 'batch-order-1',
          type: TradeType.BUY,
          instrument: 'EURUSD',
          quantity: 10000,
          requestedPrice: 1.0500,
          timestamp: new Date('2023-01-01T10:00:00Z'),
        },
        {
          id: 'batch-order-2',
          type: TradeType.SELL,
          instrument: 'GBPUSD',
          quantity: 10000,
          requestedPrice: 1.2500,
          timestamp: new Date('2023-01-01T10:00:00Z'),
        },
      ];

      const marketDataMap = new Map([
        ['EURUSD_1672567200000', {
          timestamp: new Date('2023-01-01T10:00:00Z'),
          instrument: 'EURUSD',
          timeframe: 'H1' as any,
          open: 1.0495,
          high: 1.0505,
          low: 1.0490,
          close: 1.0500,
          volume: 1000000,
          source: 'MT5' as any,
        } as HistoricalDataPoint],
        ['GBPUSD_1672567200000', {
          timestamp: new Date('2023-01-01T10:00:00Z'),
          instrument: 'GBPUSD',
          timeframe: 'H1' as any,
          open: 1.2495,
          high: 1.2505,
          low: 1.2490,
          close: 1.2500,
          volume: 800000,
          source: 'MT5' as any,
        } as HistoricalDataPoint],
      ]);

      const results = await engine.executeBatch(orders, marketDataMap);

      expect(results).toHaveLength(2);
      expect(results[0].orderId).toBe('batch-order-1');
      expect(results[1].orderId).toBe('batch-order-2');
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(true);
    });

    it('should handle missing market data in batch execution', async () => {
      const orders: BacktestTradeOrder[] = [
        {
          id: 'missing-data-order',
          type: TradeType.BUY,
          instrument: 'EURUSD',
          quantity: 10000,
          requestedPrice: 1.0500,
          timestamp: new Date('2023-01-01T10:00:00Z'),
        },
      ];

      const emptyMarketDataMap = new Map();

      const results = await engine.executeBatch(orders, emptyMarketDataMap);

      expect(results).toHaveLength(1);
      expect(results[0].success).toBe(false);
      expect(results[0].failureReason).toBe('Market data not available');
    });
  });

  describe('Configuration Management', () => {
    it('should update execution configuration', () => {
      const newConfig = {
        baseSpread: 1.2,
        baseSlippage: 0.08,
        commissionPerLot: 10.0,
      };

      engine.updateConfig(newConfig);
      const currentConfig = engine.getConfig();

      expect(currentConfig.baseSpread).toBe(1.2);
      expect(currentConfig.baseSlippage).toBe(0.08);
      expect(currentConfig.commissionPerLot).toBe(10.0);
      expect(currentConfig.volatilitySpreadMultiplier).toBe(DEFAULT_EXECUTION_CONFIG.volatilitySpreadMultiplier);
    });

    it('should return current configuration', () => {
      const config = engine.getConfig();

      expect(config).toEqual(DEFAULT_EXECUTION_CONFIG);
    });
  });

  describe('Spread Calculation', () => {
    it('should calculate base spread correctly', async () => {
      const order: BacktestTradeOrder = {
        id: 'spread-test',
        type: TradeType.BUY,
        instrument: 'EURUSD',
        quantity: 10000,
        requestedPrice: 1.0500,
        timestamp: new Date('2023-01-01T10:00:00Z'),
      };

      const marketData: HistoricalDataPoint = {
        timestamp: new Date('2023-01-01T10:00:00Z'),
        instrument: 'EURUSD',
        timeframe: 'H1' as any,
        open: 1.0500,
        high: 1.0500,
        low: 1.0500,
        close: 1.0500,
        volume: 1000000,
        source: 'MT5' as any,
      };

      const result = await engine.executeOrder(order, marketData);

      expect(result.spread).toBeGreaterThanOrEqual(DEFAULT_EXECUTION_CONFIG.baseSpread * 0.8);
      expect(result.spread).toBeLessThanOrEqual(DEFAULT_EXECUTION_CONFIG.baseSpread * 1.3);
    });
  });

  describe('Commission Calculation', () => {
    it('should calculate commission based on lot size', async () => {
      const smallOrder: BacktestTradeOrder = {
        id: 'commission-small',
        type: TradeType.BUY,
        instrument: 'EURUSD',
        quantity: 10000, // 0.1 lots
        requestedPrice: 1.0500,
        timestamp: new Date('2023-01-01T10:00:00Z'),
      };

      const largeOrder: BacktestTradeOrder = {
        id: 'commission-large',
        type: TradeType.BUY,
        instrument: 'EURUSD',
        quantity: 100000, // 1.0 lots
        requestedPrice: 1.0500,
        timestamp: new Date('2023-01-01T10:00:00Z'),
      };

      const marketData: HistoricalDataPoint = {
        timestamp: new Date('2023-01-01T10:00:00Z'),
        instrument: 'EURUSD',
        timeframe: 'H1' as any,
        open: 1.0500,
        high: 1.0500,
        low: 1.0500,
        close: 1.0500,
        volume: 1000000,
        source: 'MT5' as any,
      };

      const smallResult = await engine.executeOrder(smallOrder, marketData);
      const largeResult = await engine.executeOrder(largeOrder, marketData);

      expect(largeResult.commission).toBeGreaterThan(smallResult.commission);
      expect(smallResult.commission).toBeGreaterThanOrEqual(DEFAULT_EXECUTION_CONFIG.minimumCommission);
    });
  });

  describe('Liquidity Adjustment', () => {
    it('should apply liquidity multipliers based on trading hours', async () => {
      // Test during high liquidity hours (London-NY overlap: 13:00-17:00 UTC)
      const highLiquidityOrder: BacktestTradeOrder = {
        id: 'high-liquidity',
        type: TradeType.BUY,
        instrument: 'EURUSD',
        quantity: 10000,
        requestedPrice: 1.0500,
        timestamp: new Date('2023-01-01T15:00:00Z'), // 15:00 UTC - high liquidity
      };

      // Test during low liquidity hours
      const lowLiquidityOrder: BacktestTradeOrder = {
        id: 'low-liquidity',
        type: TradeType.BUY,
        instrument: 'EURUSD',
        quantity: 10000,
        requestedPrice: 1.0500,
        timestamp: new Date('2023-01-01T02:00:00Z'), // 02:00 UTC - low liquidity
      };

      const marketDataHigh: HistoricalDataPoint = {
        timestamp: new Date('2023-01-01T15:00:00Z'),
        instrument: 'EURUSD',
        timeframe: 'H1' as any,
        open: 1.0500,
        high: 1.0500,
        low: 1.0500,
        close: 1.0500,
        volume: 1000000,
        source: 'MT5' as any,
      };

      const marketDataLow: HistoricalDataPoint = {
        timestamp: new Date('2023-01-01T02:00:00Z'),
        instrument: 'EURUSD',
        timeframe: 'H1' as any,
        open: 1.0500,
        high: 1.0500,
        low: 1.0500,
        close: 1.0500,
        volume: 1000000,
        source: 'MT5' as any,
      };

      const highLiquidityResult = await engine.executeOrder(highLiquidityOrder, marketDataHigh);
      const lowLiquidityResult = await engine.executeOrder(lowLiquidityOrder, marketDataLow);

      // Low liquidity periods should generally have higher spreads
      expect(lowLiquidityResult.spread).toBeGreaterThanOrEqual(highLiquidityResult.spread * 0.9);
    });
  });
});