#!/usr/bin/env python3
"""
Simple MT5 Connection Test
Direct test of MT5 integration without complex dependencies
"""

import MetaTrader5 as mt5
from datetime import datetime, timedelta
import asyncio
import sys

def test_mt5_connection():
    """Test basic MT5 connection and functionality"""
    print("🚀 Starting Simple MT5 Connection Test")
    print("=" * 50)
    
    # Initialize MT5
    print("\n📡 Step 1: Initializing MT5 connection...")
    if not mt5.initialize():
        error = mt5.last_error()
        print(f"❌ MT5 initialization failed: {error}")
        return False
    
    print("✅ MT5 initialized successfully!")
    
    # Test 1: Account Information
    print("\n💰 Step 2: Getting account information...")
    account_info = mt5.account_info()
    
    if account_info is None:
        print("❌ Failed to get account information")
        return False
    
    print("✅ Account Information:")
    print(f"   Server: {account_info.server}")
    print(f"   Account: {account_info.login}")
    print(f"   Name: {account_info.name}")
    print(f"   Company: {account_info.company}")
    print(f"   Currency: {account_info.currency}")
    print(f"   Balance: {account_info.balance:.2f}")
    print(f"   Equity: {account_info.equity:.2f}")
    print(f"   Leverage: 1:{account_info.leverage}")
    
    # Check if demo account
    if account_info.trade_mode == mt5.ACCOUNT_TRADE_MODE_DEMO:
        print("✅ Demo account confirmed - safe for testing")
    else:
        print("⚠️  WARNING: This is not a demo account!")
    
    # Test 2: Terminal Information
    print("\n💻 Step 3: Getting terminal information...")
    terminal_info = mt5.terminal_info()
    
    if terminal_info is not None:
        print("✅ Terminal Information:")
        print(f"   Version: {terminal_info.build}")
        print(f"   Path: {terminal_info.path}")
        print(f"   Data Path: {terminal_info.data_path}")
        print(f"   Connected: {terminal_info.connected}")
        print(f"   Experts Enabled: {terminal_info.dlls_allowed}")
    
    # Test 3: Symbol Information
    print("\n📈 Step 4: Testing symbol information...")
    
    # Test EURUSD
    symbol = "EURUSD"
    symbol_info = mt5.symbol_info(symbol)
    
    if symbol_info is not None:
        print(f"✅ {symbol} Information:")
        print(f"   Bid: {symbol_info.bid:.5f}")
        print(f"   Ask: {symbol_info.ask:.5f}")
        print(f"   Spread: {symbol_info.spread} points")
        print(f"   Digits: {symbol_info.digits}")
        print(f"   Point Value: {symbol_info.point}")
        print(f"   Volume Min: {symbol_info.volume_min}")
        print(f"   Volume Max: {symbol_info.volume_max}")
    else:
        print(f"❌ {symbol} not available")
        return False
    
    # Test 4: Current Tick
    print("\n🔄 Step 5: Getting current tick data...")
    tick = mt5.symbol_info_tick(symbol)
    
    if tick is not None:
        print(f"✅ Current {symbol} Tick:")
        print(f"   Time: {datetime.fromtimestamp(tick.time)}")
        print(f"   Bid: {tick.bid:.5f}")
        print(f"   Ask: {tick.ask:.5f}")
        print(f"   Volume: {tick.volume}")
        print(f"   Spread: {(tick.ask - tick.bid):.5f}")
    else:
        print(f"❌ Failed to get {symbol} tick data")
        return False
    
    # Test 5: Historical Data
    print("\n📊 Step 6: Testing historical data download...")
    
    # Get last 10 daily bars
    rates_daily = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_D1, 0, 10)
    
    if rates_daily is not None and len(rates_daily) > 0:
        print(f"✅ Daily Data - Retrieved {len(rates_daily)} bars:")
        latest = rates_daily[-1]
        print(f"   Latest Date: {datetime.fromtimestamp(latest['time'])}")
        print(f"   OHLC: O:{latest['open']:.5f} H:{latest['high']:.5f} L:{latest['low']:.5f} C:{latest['close']:.5f}")
        print(f"   Volume: {latest['tick_volume']}")
    else:
        print("❌ Failed to get daily historical data")
        return False
    
    # Get last 100 hourly bars
    rates_hourly = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_H1, 0, 100)
    
    if rates_hourly is not None and len(rates_hourly) > 0:
        print(f"✅ Hourly Data - Retrieved {len(rates_hourly)} bars")
        latest = rates_hourly[-1]
        print(f"   Latest Time: {datetime.fromtimestamp(latest['time'])}")
        print(f"   Close: {latest['close']:.5f}")
    else:
        print("❌ Failed to get hourly historical data")
        return False
    
    # Test 6: Multiple Price Updates (Streaming Simulation)
    print("\n🔄 Step 7: Testing price streaming (5 updates)...")
    
    for i in range(5):
        tick = mt5.symbol_info_tick(symbol)
        if tick is not None:
            spread = tick.ask - tick.bid
            print(f"   Update {i+1}: {tick.bid:.5f}/{tick.ask:.5f} (spread: {spread:.5f})")
        else:
            print(f"   ❌ Update {i+1}: Failed to get tick")
            
        # Small delay
        import time
        time.sleep(0.5)
    
    print("✅ Price streaming test completed")
    
    # Test 7: Available Symbols
    print("\n📋 Step 8: Checking available symbols...")
    symbols = mt5.symbols_get()
    
    if symbols is not None:
        symbol_count = len(symbols)
        print(f"✅ Found {symbol_count} available symbols")
        
        # Show major forex pairs
        major_pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']
        available_majors = []
        
        for pair in major_pairs:
            if mt5.symbol_info(pair) is not None:
                available_majors.append(pair)
        
        print(f"   Major pairs available: {', '.join(available_majors)}")
        
    else:
        print("❌ Failed to get symbols list")
    
    return True

def main():
    """Main test function"""
    try:
        success = test_mt5_connection()
        
        if success:
            print("\n" + "=" * 50)
            print("🎉 ALL TESTS PASSED!")
            print("✅ MT5 connection is working properly")
            print("✅ Account information accessible")
            print("✅ Historical data download working")
            print("✅ Real-time price data available")
            print("✅ Ready for full integration!")
            print("=" * 50)
            return True
        else:
            print("\n" + "=" * 50)
            print("❌ SOME TESTS FAILED")
            print("Please check MT5 connection and try again")
            print("=" * 50)
            return False
            
    except Exception as e:
        print(f"\n❌ Test failed with exception: {e}")
        return False
        
    finally:
        # Always cleanup
        print("\n🔌 Shutting down MT5 connection...")
        mt5.shutdown()
        print("✅ MT5 connection closed")

if __name__ == "__main__":
    print("Make sure MetaTrader 5 is running with your demo account!")
    print("Press Ctrl+C to cancel if needed...\n")
    
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ Test cancelled by user")
        mt5.shutdown()
        sys.exit(1)