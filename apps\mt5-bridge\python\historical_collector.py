"""
Historical Data Collection Service
Handles MT5 historical data retrieval, synchronization, and backfill processes
"""

import asyncio
import MetaTrader5 as mt5
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import time
from loguru import logger

from mt5_connection import get_mt5_connection
from database import get_db_manager, MarketDataPoint
from data_transformer import DataTransformer
from performance_monitor import get_performance_profiler

@dataclass
class CollectionConfig:
    """Configuration for historical data collection"""
    batch_size: int = 10000  # Records per batch
    max_concurrent_instruments: int = 5
    rate_limit_delay: float = 0.1  # Delay between requests
    max_retries: int = 3
    retry_delay: float = 5.0
    default_lookback_days: int = 90  # 3 months default

@dataclass
class CollectionJob:
    """Historical data collection job"""
    instrument: str
    timeframe: str
    start_time: datetime
    end_time: datetime
    priority: int = 1
    retry_count: int = 0

class HistoricalDataCollector:
    """
    Collects and synchronizes historical market data from MT5
    """
    
    def __init__(self, config: CollectionConfig = None):
        self.config = config or CollectionConfig()
        self.mt5_connection = get_mt5_connection()
        self.db_manager = get_db_manager()
        self.data_transformer = DataTransformer()
        self.performance_profiler = get_performance_profiler()
        
        # Collection state
        self.is_running = False
        self.active_jobs: List[CollectionJob] = []
        self.job_queue = asyncio.Queue()
        self.worker_tasks: List[asyncio.Task] = []
        
        # Statistics
        self.stats = {
            'total_records_collected': 0,
            'successful_batches': 0,
            'failed_batches': 0,
            'instruments_processed': set(),
            'last_collection_time': None,
            'collection_start_time': None
        }
        
        # MT5 timeframe mapping
        self.timeframe_map = {
            '1m': mt5.TIMEFRAME_M1,
            '5m': mt5.TIMEFRAME_M5,
            '15m': mt5.TIMEFRAME_M15,
            '30m': mt5.TIMEFRAME_M30,
            '1h': mt5.TIMEFRAME_H1,
            '4h': mt5.TIMEFRAME_H4,
            '1d': mt5.TIMEFRAME_D1
        }
        
    async def start(self):
        """Start the historical data collection service"""
        if self.is_running:
            logger.warning("Historical data collector already running")
            return
            
        logger.info("🚀 Starting historical data collection service...")
        self.is_running = True
        self.stats['collection_start_time'] = datetime.now()
        
        # Start worker tasks
        for i in range(self.config.max_concurrent_instruments):
            task = asyncio.create_task(self._worker(f"worker-{i}"))
            self.worker_tasks.append(task)
        
        logger.info(f"✅ Started {len(self.worker_tasks)} collection workers")
        
    async def stop(self):
        """Stop the historical data collection service"""
        if not self.is_running:
            return
            
        logger.info("🔌 Stopping historical data collection service...")
        self.is_running = False
        
        # Cancel all worker tasks
        for task in self.worker_tasks:
            task.cancel()
            
        # Wait for tasks to complete
        await asyncio.gather(*self.worker_tasks, return_exceptions=True)
        self.worker_tasks.clear()
        
        logger.info("✅ Historical data collection service stopped")
        
    async def collect_instrument_history(self, 
                                       instrument: str, 
                                       timeframes: List[str],
                                       start_date: Optional[datetime] = None,
                                       end_date: Optional[datetime] = None) -> Dict[str, Any]:
        """Collect historical data for an instrument across multiple timeframes"""
        
        if not start_date:
            start_date = datetime.now() - timedelta(days=self.config.default_lookback_days)
        if not end_date:
            end_date = datetime.now()
            
        logger.info(f"📊 Starting collection for {instrument} from {start_date} to {end_date}")
        
        results = {}
        
        for timeframe in timeframes:
            try:
                logger.info(f"📈 Collecting {instrument} {timeframe} data...")
                
                # Update sync status
                await self.db_manager.update_sync_status(
                    instrument, timeframe, 'IN_PROGRESS'
                )
                
                # Collect data for this timeframe
                result = await self._collect_timeframe_data(
                    instrument, timeframe, start_date, end_date
                )
                
                results[timeframe] = result
                
                # Update sync status on success
                await self.db_manager.update_sync_status(
                    instrument, timeframe, 'COMPLETED',
                    last_sync_timestamp=end_date,
                    records_synced=result['records_collected']
                )
                
                self.stats['instruments_processed'].add(instrument)
                
            except Exception as e:
                logger.error(f"❌ Failed to collect {instrument} {timeframe}: {e}")
                
                # Update sync status on failure
                await self.db_manager.update_sync_status(
                    instrument, timeframe, 'FAILED',
                    error_message=str(e)
                )
                
                results[timeframe] = {'error': str(e), 'records_collected': 0}
        
        return results
        
    async def backfill_missing_data(self, 
                                  instrument: str, 
                                  timeframe: str,
                                  start_date: datetime,
                                  end_date: datetime) -> Dict[str, Any]:
        """Backfill missing data by identifying and filling gaps"""
        
        logger.info(f"🔍 Identifying gaps for {instrument} {timeframe}...")
        
        # Get data gaps
        gaps = await self.db_manager.get_data_gaps(instrument, timeframe, start_date, end_date)
        
        if not gaps:
            logger.info(f"✅ No gaps found for {instrument} {timeframe}")
            return {'gaps_found': 0, 'records_collected': 0}
        
        logger.info(f"📊 Found {len(gaps)} gaps to backfill for {instrument} {timeframe}")
        
        total_records = 0
        
        for gap_start, gap_end in gaps:
            try:
                logger.info(f"🔧 Backfilling gap: {gap_start} to {gap_end}")
                
                result = await self._collect_timeframe_data(
                    instrument, timeframe, gap_start, gap_end
                )
                
                total_records += result['records_collected']
                
            except Exception as e:
                logger.error(f"❌ Failed to backfill gap {gap_start}-{gap_end}: {e}")
        
        return {
            'gaps_found': len(gaps),
            'records_collected': total_records
        }
        
    async def sync_recent_data(self, 
                             instruments: List[str], 
                             timeframes: List[str],
                             hours_back: int = 24) -> Dict[str, Any]:
        """Synchronize recent data for multiple instruments"""
        
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours_back)
        
        logger.info(f"🔄 Syncing recent data for {len(instruments)} instruments...")
        
        results = {}
        
        for instrument in instruments:
            for timeframe in timeframes:
                try:
                    # Get latest timestamp from database
                    latest_db_time = await self.db_manager.get_latest_timestamp(instrument, timeframe)
                    
                    # Determine sync start time
                    sync_start = latest_db_time if latest_db_time and latest_db_time > start_time else start_time
                    
                    logger.info(f"📈 Syncing {instrument} {timeframe} from {sync_start}")
                    
                    result = await self._collect_timeframe_data(
                        instrument, timeframe, sync_start, end_time
                    )
                    
                    results[f"{instrument}_{timeframe}"] = result
                    
                except Exception as e:
                    logger.error(f"❌ Failed to sync {instrument} {timeframe}: {e}")
                    results[f"{instrument}_{timeframe}"] = {'error': str(e)}
        
        return results
        
    async def _collect_timeframe_data(self, 
                                    instrument: str, 
                                    timeframe: str,
                                    start_time: datetime, 
                                    end_time: datetime) -> Dict[str, Any]:
        """Collect data for a specific instrument and timeframe"""
        
        if not await self.mt5_connection.ensure_connection():
            raise Exception("MT5 connection not available")
        
        mt5_timeframe = self.timeframe_map.get(timeframe)
        if not mt5_timeframe:
            raise ValueError(f"Unsupported timeframe: {timeframe}")
        
        total_records = 0
        batch_count = 0
        
        # Calculate total bars needed
        time_delta = end_time - start_time
        timeframe_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30, 
            '1h': 60, '4h': 240, '1d': 1440
        }
        
        total_bars_needed = int(time_delta.total_seconds() / (timeframe_minutes[timeframe] * 60))
        
        logger.info(f"📊 Collecting ~{total_bars_needed} bars for {instrument} {timeframe}")
        
        # Collect data in batches
        current_time = start_time
        
        while current_time < end_time and self.is_running:
            try:
                # Calculate batch end time
                batch_end = min(current_time + timedelta(minutes=timeframe_minutes[timeframe] * self.config.batch_size), end_time)
                
                # Get data from MT5
                rates = mt5.copy_rates_range(
                    instrument, 
                    mt5_timeframe, 
                    current_time, 
                    batch_end
                )
                
                if rates is None or len(rates) == 0:
                    logger.warning(f"⚠️ No data returned for {instrument} {timeframe} from {current_time}")
                    current_time = batch_end
                    continue
                
                # Transform data to platform format
                data_points = []
                for rate in rates:
                    try:
                        point = MarketDataPoint(
                            timestamp=datetime.fromtimestamp(rate['time']),
                            instrument=instrument,
                            timeframe=timeframe,
                            open=float(rate['open']),
                            high=float(rate['high']),
                            low=float(rate['low']),
                            close=float(rate['close']),
                            volume=int(rate['tick_volume']),
                            source='mt5',
                            metadata={
                                'collection_time': datetime.now().isoformat(),
                                'batch_id': f"{instrument}_{timeframe}_{batch_count}",
                                'mt5_spread': float(rate.get('spread', 0))
                            }
                        )
                        data_points.append(point)
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to process rate data: {e}")
                        continue
                
                # Insert data into database
                if data_points:
                    inserted_count = await self.db_manager.insert_market_data(data_points)
                    total_records += inserted_count
                    batch_count += 1
                    
                    self.stats['total_records_collected'] += inserted_count
                    self.stats['successful_batches'] += 1
                    
                    logger.info(f"📊 Batch {batch_count}: Inserted {inserted_count} records for {instrument} {timeframe}")
                
                # Update current time
                current_time = batch_end
                
                # Rate limiting
                if self.config.rate_limit_delay > 0:
                    await asyncio.sleep(self.config.rate_limit_delay)
                
            except Exception as e:
                logger.error(f"❌ Error in batch collection: {e}")
                self.stats['failed_batches'] += 1
                
                # Move to next batch on error
                current_time = min(current_time + timedelta(minutes=timeframe_minutes[timeframe] * self.config.batch_size), end_time)
                
                # Brief delay before retry
                await asyncio.sleep(1)
        
        self.stats['last_collection_time'] = datetime.now()
        
        return {
            'records_collected': total_records,
            'batches_processed': batch_count,
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': (datetime.now() - start_time).total_seconds()
        }
        
    async def _worker(self, worker_name: str):
        """Worker task for processing collection jobs"""
        logger.info(f"👷 Worker {worker_name} started")
        
        while self.is_running:
            try:
                # Get job from queue
                job = await asyncio.wait_for(self.job_queue.get(), timeout=1.0)
                
                logger.info(f"👷 {worker_name} processing job: {job.instrument} {job.timeframe}")
                
                # Process the job
                await self._collect_timeframe_data(
                    job.instrument, job.timeframe, job.start_time, job.end_time
                )
                
                logger.info(f"✅ {worker_name} completed job: {job.instrument} {job.timeframe}")
                
            except asyncio.TimeoutError:
                # No job available, continue
                continue
            except Exception as e:
                logger.error(f"❌ {worker_name} error: {e}")
                
                # Retry job if within retry limit
                if hasattr(job, 'retry_count') and job.retry_count < self.config.max_retries:
                    job.retry_count += 1
                    await asyncio.sleep(self.config.retry_delay)
                    await self.job_queue.put(job)
                    logger.info(f"🔄 Retrying job {job.instrument} {job.timeframe} (attempt {job.retry_count})")
                
        logger.info(f"👷 Worker {worker_name} stopped")
        
    async def queue_collection_job(self, job: CollectionJob):
        """Add a collection job to the queue"""
        await self.job_queue.put(job)
        logger.info(f"📋 Queued collection job: {job.instrument} {job.timeframe}")
        
    def get_stats(self) -> Dict[str, Any]:
        """Get collection statistics"""
        uptime = None
        if self.stats['collection_start_time']:
            uptime = (datetime.now() - self.stats['collection_start_time']).total_seconds()
            
        return {
            **self.stats,
            'instruments_processed': list(self.stats['instruments_processed']),
            'is_running': self.is_running,
            'active_workers': len(self.worker_tasks),
            'queue_size': self.job_queue.qsize(),
            'uptime_seconds': uptime
        }

# Global instance
_historical_collector: Optional[HistoricalDataCollector] = None

def get_historical_collector() -> HistoricalDataCollector:
    """Get global historical data collector instance"""
    global _historical_collector
    if _historical_collector is None:
        _historical_collector = HistoricalDataCollector()
    return _historical_collector