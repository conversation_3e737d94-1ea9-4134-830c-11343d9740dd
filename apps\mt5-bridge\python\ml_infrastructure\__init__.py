"""
ML Infrastructure Package for GoldDaddy Trading Platform
Provides comprehensive machine learning capabilities for trading strategy optimization
"""

from .model_registry import (
    ModelRegistry, 
    MLModel, 
    ModelPerformance, 
    ModelDeployment, 
    ModelTraining,
    ModelStatus,
    ModelType
)

from .transformer_models import (
    MarketTransformer,
    StrategyOptimizationTransformer,
    MarketRegimeDetector,
    EnsembleTransformer,
    ModelConfig,
    create_model,
    count_parameters,
    get_model_summary
)

from .feature_engineering import (
    FinancialFeatureEngineer,
    FeatureConfig,
    create_market_regime_features,
    process_market_data_async
)

from .training_pipeline import (
    TrainingPipeline,
    TrainingConfig,
    MarketDataset
)

from .inference_service import (
    InferenceService,
    PredictionRequest,
    PredictionResponse,
    ModelCache
)

from .strategy_optimizer import (
    GeneticAlgorithmOptimizer,
    OptunaBayesianOptimizer,
    StrategyOptimizationEngine,
    ParameterRange,
    OptimizationConfig,
    StrategyParameters
)

from .ml_service import MLService

__version__ = "1.0.0"
__author__ = "GoldDaddy Development Team"

# Package-level configuration
DEFAULT_MODEL_CONFIG = ModelConfig(
    sequence_length=128,
    feature_dim=64,
    num_heads=8,
    num_layers=6,
    hidden_dim=512,
    dropout=0.1,
    learning_rate=1e-4,
    batch_size=32,
    num_epochs=100
)

DEFAULT_FEATURE_CONFIG = FeatureConfig(
    lookback_periods=[5, 10, 20, 50, 100],
    technical_indicators=['sma', 'ema', 'rsi', 'macd', 'bollinger', 'atr'],
    price_features=True,
    volume_features=True,
    volatility_features=True,
    momentum_features=True,
    trend_features=True,
    pattern_features=True,
    market_microstructure=True,
    normalize_features=True
)

DEFAULT_TRAINING_CONFIG = TrainingConfig(
    model_type="market",
    model_config=DEFAULT_MODEL_CONFIG,
    feature_config=DEFAULT_FEATURE_CONFIG,
    train_split=0.7,
    val_split=0.15,
    test_split=0.15,
    batch_size=32,
    num_epochs=100,
    learning_rate=1e-4,
    early_stopping_patience=10,
    use_mixed_precision=True
)

DEFAULT_OPTIMIZATION_CONFIG = OptimizationConfig(
    population_size=50,
    generations=100,
    mutation_rate=0.1,
    crossover_rate=0.8,
    elite_size=5,
    tournament_size=3,
    use_ml_guidance=True,
    parallel_evaluation=True,
    max_workers=4
)

# Convenience functions
def create_ml_service(redis_url: str = None, models_path: str = "./models") -> MLService:
    """
    Create and configure ML service with default settings
    """
    return MLService(redis_url=redis_url, models_path=models_path)

def create_market_prediction_model(config: ModelConfig = None) -> MarketTransformer:
    """
    Create a market prediction transformer model
    """
    if config is None:
        config = DEFAULT_MODEL_CONFIG
    return MarketTransformer(config)

def create_strategy_optimizer(config: OptimizationConfig = None) -> StrategyOptimizationEngine:
    """
    Create a strategy optimization engine
    """
    return StrategyOptimizationEngine()

def create_feature_engineer(config: FeatureConfig = None) -> FinancialFeatureEngineer:
    """
    Create a financial feature engineer
    """
    if config is None:
        config = DEFAULT_FEATURE_CONFIG
    return FinancialFeatureEngineer(config)

# Export all public components
__all__ = [
    # Core classes
    'MLService',
    'ModelRegistry',
    'TrainingPipeline',
    'InferenceService',
    'StrategyOptimizationEngine',
    'FinancialFeatureEngineer',
    
    # Model classes
    'MarketTransformer',
    'StrategyOptimizationTransformer',
    'MarketRegimeDetector',
    'EnsembleTransformer',
    
    # Configuration classes
    'ModelConfig',
    'FeatureConfig',
    'TrainingConfig',
    'OptimizationConfig',
    
    # Data classes
    'MLModel',
    'ModelPerformance',
    'ModelDeployment',
    'ModelTraining',
    'PredictionRequest',
    'PredictionResponse',
    'ParameterRange',
    'StrategyParameters',
    
    # Enums
    'ModelStatus',
    'ModelType',
    
    # Utility functions
    'create_model',
    'count_parameters',
    'get_model_summary',
    'create_market_regime_features',
    'process_market_data_async',
    
    # Convenience functions
    'create_ml_service',
    'create_market_prediction_model',
    'create_strategy_optimizer',
    'create_feature_engineer',
    
    # Default configurations
    'DEFAULT_MODEL_CONFIG',
    'DEFAULT_FEATURE_CONFIG',
    'DEFAULT_TRAINING_CONFIG',
    'DEFAULT_OPTIMIZATION_CONFIG'
]
