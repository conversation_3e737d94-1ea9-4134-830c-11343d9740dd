import { EventEmitter } from 'events';
import Decimal from 'decimal.js';
import {
  TradeModification,
  ModificationType,
  ModificationStatus,
  TradeModificationRequest,
  Position,
  ExecutionError,
  ExecutionErrorCategory
} from '@golddaddy/types';
import { RequestDeduplicationService } from './RequestDeduplicationService.js';

interface RiskValidationResult {
  isValid: boolean;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  warnings: string[];
  blockingErrors: string[];
  maxExposure: Decimal.Instance;
  currentExposure: Decimal.Instance;
  newExposure: Decimal.Instance;
}

interface ModificationRollbackData {
  modificationId: string;
  originalPosition: Position;
  originalTradeState: any;
  brokerOperations: BrokerOperation[];
  timestamp: Date;
}

interface BrokerOperation {
  operationId: string;
  brokerId: string;
  operationType: 'MODIFY_STOP_LOSS' | 'MODIFY_TAKE_PROFIT' | 'MODIFY_SIZE' | 'CLOSE_POSITION';
  originalValue?: Decimal.Instance;
  newValue?: Decimal.Instance;
  status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'REVERSED';
  brokerOrderId?: string;
  timestamp: Date;
  error?: ExecutionError;
}

export class TradeModificationService extends EventEmitter {
  private pendingModifications: Map<string, TradeModification> = new Map();
  private rollbackData: Map<string, ModificationRollbackData> = new Map();
  private riskThresholds: RiskThresholds;
  private auditService: any; // Will be injected
  private positionManager: any; // Will be injected
  private brokerService: any; // Will be injected
  private deduplicationService: RequestDeduplicationService;

  constructor(
    auditService: any,
    positionManager: any,
    brokerService: any,
    customRiskThresholds?: Partial<RiskThresholds>
  ) {
    super();
    this.auditService = auditService;
    this.positionManager = positionManager;
    this.brokerService = brokerService;
    this.deduplicationService = new RequestDeduplicationService({
      windowMs: 300000,     // 5 minutes for trade modifications
      maxCacheSize: 5000,   // Smaller cache for modifications
      cleanupIntervalMs: 60000
    });
    
    this.riskThresholds = {
      maxPositionSize: new Decimal(100000), // $100,000 maximum position size
      maxExposurePercentage: new Decimal(5), // 5% of account max exposure
      stopLossMinDistance: new Decimal(10), // 10 pips minimum stop distance
      takeProfitMinDistance: new Decimal(15), // 15 pips minimum TP distance
      maxSlippagePercent: new Decimal(2), // 2% max slippage tolerance
      maxModificationFrequency: 5, // Max 5 modifications per position per hour
      criticalRiskThreshold: new Decimal(90), // 90% exposure triggers critical risk
      ...customRiskThresholds
    };
  }

  async requestModification(
    request: TradeModificationRequest,
    userId: string
  ): Promise<TradeModification> {
    try {
      // Check for duplicate requests
      const deduplicationResult = this.deduplicationService.checkTradeModificationDuplicate(
        userId,
        request.tradeId,
        request.modificationType,
        request.newValue,
        request.reason
      );

      if (deduplicationResult.isDuplicate) {
        console.warn(`Duplicate modification request blocked for user ${userId}, trade ${request.tradeId}`);
        throw new Error(`Duplicate modification request detected. Original request submitted at ${deduplicationResult.originalTimestamp}`);
      }

      // Validate request
      await this.validateModificationRequest(request, userId);

      // Get current position state
      const position = await this.positionManager.getPosition(request.tradeId);
      if (!position) {
        throw new Error(`Position not found for trade ${request.tradeId}`);
      }

      // Perform risk validation
      const riskValidation = await this.validateModificationRisk(request, position, userId);
      
      if (!riskValidation.isValid) {
        throw new Error(`Risk validation failed: ${riskValidation.blockingErrors.join(', ')}`);
      }

      // Create modification record
      const modification: TradeModification = {
        id: this.generateModificationId(),
        tradeId: request.tradeId,
        modificationType: request.modificationType,
        originalValue: await this.getCurrentValue(position, request.modificationType),
        newValue: request.newValue,
        status: ModificationStatus.PENDING,
        requestedBy: userId,
        reason: request.reason,
        timestamp: new Date(),
        auditTrailId: await this.auditService.createAuditEntry({
          action: 'TRADE_MODIFICATION_REQUESTED',
          userId,
          tradeId: request.tradeId,
          data: {
            modificationType: request.modificationType,
            originalValue: await this.getCurrentValue(position, request.modificationType),
            newValue: request.newValue,
            reason: request.reason,
            riskValidation
          }
        })
      };

      // Store pending modification
      this.pendingModifications.set(modification.id, modification);

      // Store rollback data
      await this.createRollbackData(modification, position);

      this.emit('modificationRequested', {
        modification,
        riskValidation,
        position
      });

      // Process modification asynchronously
      this.processModification(modification, position, deduplicationResult.requestId).catch((error) => {
        this.handleModificationError(modification.id, error);
      });

      return modification;

    } catch (error) {
      await this.auditService.createAuditEntry({
        action: 'TRADE_MODIFICATION_FAILED',
        userId,
        tradeId: request.tradeId,
        error: error.message,
        data: request
      });

      this.emit('modificationFailed', {
        request,
        userId,
        error: error.message
      });

      throw error;
    }
  }

  async getModificationStatus(modificationId: string): Promise<TradeModification | null> {
    return this.pendingModifications.get(modificationId) || null;
  }

  async getAllModifications(
    filters?: {
      tradeId?: string;
      userId?: string;
      status?: ModificationStatus;
      startDate?: Date;
      endDate?: Date;
    }
  ): Promise<TradeModification[]> {
    let modifications = Array.from(this.pendingModifications.values());

    if (filters) {
      if (filters.tradeId) {
        modifications = modifications.filter(m => m.tradeId === filters.tradeId);
      }
      if (filters.userId) {
        modifications = modifications.filter(m => m.requestedBy === filters.userId);
      }
      if (filters.status) {
        modifications = modifications.filter(m => m.status === filters.status);
      }
      if (filters.startDate) {
        modifications = modifications.filter(m => m.timestamp >= filters.startDate!);
      }
      if (filters.endDate) {
        modifications = modifications.filter(m => m.timestamp <= filters.endDate!);
      }
    }

    return modifications;
  }

  async rollbackModification(
    modificationId: string,
    userId: string,
    reason: string
  ): Promise<boolean> {
    try {
      const modification = this.pendingModifications.get(modificationId);
      if (!modification) {
        throw new Error(`Modification ${modificationId} not found`);
      }

      if (modification.status === ModificationStatus.APPLIED && !modification.appliedAt) {
        throw new Error(`Cannot rollback modification ${modificationId}: not yet applied`);
      }

      const rollbackData = this.rollbackData.get(modificationId);
      if (!rollbackData) {
        throw new Error(`No rollback data found for modification ${modificationId}`);
      }

      // Restore original position state
      await this.restorePositionState(rollbackData);

      // Reverse broker operations
      await this.reverseBrokerOperations(rollbackData);

      // Update modification status
      modification.status = ModificationStatus.ROLLED_BACK;
      this.pendingModifications.set(modificationId, modification);

      // Create audit trail
      await this.auditService.createAuditEntry({
        action: 'TRADE_MODIFICATION_ROLLED_BACK',
        userId,
        tradeId: modification.tradeId,
        data: {
          modificationId,
          reason,
          rollbackData: {
            originalValue: modification.originalValue,
            attemptedValue: modification.newValue,
            modificationType: modification.modificationType
          }
        }
      });

      // Clean up rollback data
      this.rollbackData.delete(modificationId);

      this.emit('modificationRolledBack', {
        modificationId,
        modification,
        userId,
        reason
      });

      return true;

    } catch (error) {
      await this.auditService.createAuditEntry({
        action: 'TRADE_MODIFICATION_ROLLBACK_FAILED',
        userId,
        data: {
          modificationId,
          reason,
          error: error.message
        }
      });

      this.emit('rollbackFailed', {
        modificationId,
        userId,
        error: error.message
      });

      throw error;
    }
  }

  async cancelModification(
    modificationId: string,
    userId: string,
    reason: string
  ): Promise<boolean> {
    try {
      const modification = this.pendingModifications.get(modificationId);
      if (!modification) {
        throw new Error(`Modification ${modificationId} not found`);
      }

      if (modification.status !== ModificationStatus.PENDING) {
        throw new Error(`Cannot cancel modification ${modificationId}: status is ${modification.status}`);
      }

      // Update status
      modification.status = ModificationStatus.REJECTED;
      this.pendingModifications.set(modificationId, modification);

      // Create audit trail
      await this.auditService.createAuditEntry({
        action: 'TRADE_MODIFICATION_CANCELLED',
        userId,
        tradeId: modification.tradeId,
        data: {
          modificationId,
          reason,
          originalRequest: {
            modificationType: modification.modificationType,
            originalValue: modification.originalValue,
            newValue: modification.newValue
          }
        }
      });

      // Clean up rollback data
      this.rollbackData.delete(modificationId);

      this.emit('modificationCancelled', {
        modificationId,
        modification,
        userId,
        reason
      });

      return true;

    } catch (error) {
      this.emit('cancellationFailed', {
        modificationId,
        userId,
        error: error.message
      });

      throw error;
    }
  }

  async getModificationHistory(
    tradeId: string,
    limit = 50,
    offset = 0
  ): Promise<{ modifications: TradeModification[]; total: number }> {
    // This would typically query a database
    const allModifications = Array.from(this.pendingModifications.values())
      .filter(m => m.tradeId === tradeId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    const modifications = allModifications.slice(offset, offset + limit);
    
    return {
      modifications,
      total: allModifications.length
    };
  }

  private async processModification(
    modification: TradeModification,
    position: Position,
    requestId?: string
  ): Promise<void> {
    try {
      const brokerOperations: BrokerOperation[] = [];

      // Execute modification based on type
      switch (modification.modificationType) {
        case ModificationType.STOP_LOSS:
          await this.modifyStopLoss(modification, position, brokerOperations);
          break;

        case ModificationType.TAKE_PROFIT:
          await this.modifyTakeProfit(modification, position, brokerOperations);
          break;

        case ModificationType.POSITION_SIZE:
          await this.modifyPositionSize(modification, position, brokerOperations);
          break;

        case ModificationType.CLOSE_POSITION:
          await this.closePosition(modification, position, brokerOperations);
          break;

        default:
          throw new Error(`Unsupported modification type: ${modification.modificationType}`);
      }

      // Update rollback data with broker operations
      const rollbackData = this.rollbackData.get(modification.id);
      if (rollbackData) {
        rollbackData.brokerOperations = brokerOperations;
        this.rollbackData.set(modification.id, rollbackData);
      }

      // Mark as applied
      modification.status = ModificationStatus.APPLIED;
      modification.appliedAt = new Date();
      this.pendingModifications.set(modification.id, modification);

      // Update audit trail
      await this.auditService.updateAuditEntry(modification.auditTrailId, {
        status: 'COMPLETED',
        appliedAt: modification.appliedAt,
        brokerOperations
      });

      this.emit('modificationApplied', {
        modification,
        position,
        brokerOperations
      });

      // Mark request as completed in deduplication service
      if (requestId) {
        this.deduplicationService.markCompleted(requestId);
      }

    } catch (error) {
      await this.handleModificationError(modification.id, error);
    }
  }

  private async modifyStopLoss(
    modification: TradeModification,
    position: Position,
    brokerOperations: BrokerOperation[]
  ): Promise<void> {
    const operation: BrokerOperation = {
      operationId: this.generateOperationId(),
      brokerId: position.accountId, // Assuming accountId maps to brokerId
      operationType: 'MODIFY_STOP_LOSS',
      originalValue: position.stopLoss,
      newValue: modification.newValue,
      status: 'PENDING',
      timestamp: new Date()
    };

    try {
      // Execute broker modification
      const result = await this.brokerService.modifyStopLoss(
        position.id,
        modification.newValue
      );

      operation.status = 'COMPLETED';
      operation.brokerOrderId = result.brokerOrderId;

      // Update position
      await this.positionManager.updatePosition(position.id, {
        stopLoss: modification.newValue,
        updatedAt: new Date()
      });

      brokerOperations.push(operation);

    } catch (error) {
      operation.status = 'FAILED';
      operation.error = this.createExecutionError(error, operation.brokerId);
      brokerOperations.push(operation);
      throw error;
    }
  }

  private async modifyTakeProfit(
    modification: TradeModification,
    position: Position,
    brokerOperations: BrokerOperation[]
  ): Promise<void> {
    const operation: BrokerOperation = {
      operationId: this.generateOperationId(),
      brokerId: position.accountId,
      operationType: 'MODIFY_TAKE_PROFIT',
      originalValue: position.takeProfit,
      newValue: modification.newValue,
      status: 'PENDING',
      timestamp: new Date()
    };

    try {
      const result = await this.brokerService.modifyTakeProfit(
        position.id,
        modification.newValue
      );

      operation.status = 'COMPLETED';
      operation.brokerOrderId = result.brokerOrderId;

      await this.positionManager.updatePosition(position.id, {
        takeProfit: modification.newValue,
        updatedAt: new Date()
      });

      brokerOperations.push(operation);

    } catch (error) {
      operation.status = 'FAILED';
      operation.error = this.createExecutionError(error, operation.brokerId);
      brokerOperations.push(operation);
      throw error;
    }
  }

  private async modifyPositionSize(
    modification: TradeModification,
    position: Position,
    brokerOperations: BrokerOperation[]
  ): Promise<void> {
    const operation: BrokerOperation = {
      operationId: this.generateOperationId(),
      brokerId: position.accountId,
      operationType: 'MODIFY_SIZE',
      originalValue: position.size,
      newValue: modification.newValue,
      status: 'PENDING',
      timestamp: new Date()
    };

    try {
      const sizeDifference = modification.newValue.minus(position.size);
      
      if (sizeDifference.gt(0)) {
        // Increase position size
        const result = await this.brokerService.increasePosition(
          position.id,
          sizeDifference
        );
        operation.brokerOrderId = result.brokerOrderId;
      } else if (sizeDifference.lt(0)) {
        // Decrease position size
        const result = await this.brokerService.decreasePosition(
          position.id,
          sizeDifference.abs()
        );
        operation.brokerOrderId = result.brokerOrderId;
      } else {
        throw new Error('No size change requested');
      }

      operation.status = 'COMPLETED';

      // Update position with new size and recalculated metrics
      await this.positionManager.updatePosition(position.id, {
        size: modification.newValue,
        updatedAt: new Date()
      });

      brokerOperations.push(operation);

    } catch (error) {
      operation.status = 'FAILED';
      operation.error = this.createExecutionError(error, operation.brokerId);
      brokerOperations.push(operation);
      throw error;
    }
  }

  private async closePosition(
    modification: TradeModification,
    position: Position,
    brokerOperations: BrokerOperation[]
  ): Promise<void> {
    const operation: BrokerOperation = {
      operationId: this.generateOperationId(),
      brokerId: position.accountId,
      operationType: 'CLOSE_POSITION',
      originalValue: position.size,
      newValue: new Decimal(0),
      status: 'PENDING',
      timestamp: new Date()
    };

    try {
      const result = await this.brokerService.closePosition(position.id);

      operation.status = 'COMPLETED';
      operation.brokerOrderId = result.brokerOrderId;

      await this.positionManager.closePosition(position.id);

      brokerOperations.push(operation);

    } catch (error) {
      operation.status = 'FAILED';
      operation.error = this.createExecutionError(error, operation.brokerId);
      brokerOperations.push(operation);
      throw error;
    }
  }

  private async validateModificationRequest(
    request: TradeModificationRequest,
    userId: string
  ): Promise<void> {
    if (!request.tradeId) {
      throw new Error('Trade ID is required');
    }

    if (!request.modificationType) {
      throw new Error('Modification type is required');
    }

    if (!request.newValue) {
      throw new Error('New value is required');
    }

    if (!request.reason?.trim()) {
      throw new Error('Modification reason is required');
    }

    if (!request.confirmRisk) {
      throw new Error('Risk confirmation is required');
    }

    // Check modification frequency
    const recentModifications = await this.getRecentModifications(request.tradeId, 3600000); // 1 hour
    if (recentModifications.length >= this.riskThresholds.maxModificationFrequency) {
      throw new Error('Maximum modification frequency exceeded');
    }
  }

  private async validateModificationRisk(
    request: TradeModificationRequest,
    position: Position,
    userId: string
  ): Promise<RiskValidationResult> {
    const result: RiskValidationResult = {
      isValid: true,
      riskLevel: 'LOW',
      warnings: [],
      blockingErrors: [],
      maxExposure: this.riskThresholds.maxPositionSize,
      currentExposure: position.size.times(position.currentPrice),
      newExposure: new Decimal(0)
    };

    // Calculate new exposure
    switch (request.modificationType) {
      case ModificationType.POSITION_SIZE:
        result.newExposure = request.newValue.times(position.currentPrice);
        break;
      case ModificationType.CLOSE_POSITION:
        result.newExposure = new Decimal(0);
        break;
      default:
        result.newExposure = result.currentExposure;
    }

    // Check position size limits
    if (request.modificationType === ModificationType.POSITION_SIZE) {
      if (request.newValue.gt(this.riskThresholds.maxPositionSize)) {
        result.blockingErrors.push(`Position size exceeds maximum allowed: ${this.riskThresholds.maxPositionSize}`);
        result.isValid = false;
      }

      if (request.newValue.lte(0)) {
        result.blockingErrors.push('Position size must be greater than zero');
        result.isValid = false;
      }
    }

    // Check stop loss distance
    if (request.modificationType === ModificationType.STOP_LOSS) {
      const stopDistance = position.currentPrice.minus(request.newValue).abs();
      const minDistance = position.currentPrice.times(this.riskThresholds.stopLossMinDistance.div(10000));
      
      if (stopDistance.lt(minDistance)) {
        result.warnings.push(`Stop loss distance (${stopDistance.toFixed(4)}) is below recommended minimum`);
        result.riskLevel = 'MEDIUM';
      }
    }

    // Check take profit distance
    if (request.modificationType === ModificationType.TAKE_PROFIT) {
      const tpDistance = request.newValue.minus(position.currentPrice).abs();
      const minDistance = position.currentPrice.times(this.riskThresholds.takeProfitMinDistance.div(10000));
      
      if (tpDistance.lt(minDistance)) {
        result.warnings.push(`Take profit distance (${tpDistance.toFixed(4)}) is below recommended minimum`);
        result.riskLevel = 'MEDIUM';
      }
    }

    // Check exposure percentage
    const exposurePercentage = result.newExposure.div(this.riskThresholds.maxPositionSize).times(100);
    if (exposurePercentage.gt(this.riskThresholds.maxExposurePercentage)) {
      result.warnings.push(`New exposure (${exposurePercentage.toFixed(1)}%) exceeds recommended limit`);
      result.riskLevel = 'HIGH';
    }

    if (exposurePercentage.gt(this.riskThresholds.criticalRiskThreshold)) {
      result.blockingErrors.push('Exposure exceeds critical risk threshold');
      result.isValid = false;
      result.riskLevel = 'CRITICAL';
    }

    return result;
  }

  private async getCurrentValue(position: Position, modificationType: ModificationType): Promise<Decimal.Instance> {
    switch (modificationType) {
      case ModificationType.STOP_LOSS:
        return position.stopLoss || new Decimal(0);
      case ModificationType.TAKE_PROFIT:
        return position.takeProfit || new Decimal(0);
      case ModificationType.POSITION_SIZE:
        return position.size;
      case ModificationType.CLOSE_POSITION:
        return position.size;
      default:
        throw new Error(`Unknown modification type: ${modificationType}`);
    }
  }

  private async createRollbackData(
    modification: TradeModification,
    position: Position
  ): Promise<void> {
    const rollbackData: ModificationRollbackData = {
      modificationId: modification.id,
      originalPosition: { ...position },
      originalTradeState: await this.captureTradeState(position),
      brokerOperations: [],
      timestamp: new Date()
    };

    this.rollbackData.set(modification.id, rollbackData);
  }

  private async captureTradeState(position: Position): Promise<any> {
    // Capture current trade state for rollback
    return {
      positionId: position.id,
      size: position.size,
      stopLoss: position.stopLoss,
      takeProfit: position.takeProfit,
      status: position.status,
      riskMetrics: { ...position.riskMetrics }
    };
  }

  private async restorePositionState(rollbackData: ModificationRollbackData): Promise<void> {
    await this.positionManager.updatePosition(
      rollbackData.originalPosition.id,
      rollbackData.originalPosition
    );
  }

  private async reverseBrokerOperations(rollbackData: ModificationRollbackData): Promise<void> {
    for (const operation of rollbackData.brokerOperations) {
      if (operation.status === 'COMPLETED') {
        try {
          await this.reverseBrokerOperation(operation);
          operation.status = 'REVERSED';
        } catch (error) {
          operation.error = this.createExecutionError(error, operation.brokerId);
        }
      }
    }
  }

  private async reverseBrokerOperation(operation: BrokerOperation): Promise<void> {
    switch (operation.operationType) {
      case 'MODIFY_STOP_LOSS':
        if (operation.originalValue) {
          await this.brokerService.modifyStopLoss(
            operation.brokerOrderId,
            operation.originalValue
          );
        }
        break;

      case 'MODIFY_TAKE_PROFIT':
        if (operation.originalValue) {
          await this.brokerService.modifyTakeProfit(
            operation.brokerOrderId,
            operation.originalValue
          );
        }
        break;

      case 'MODIFY_SIZE':
        if (operation.originalValue) {
          const sizeDifference = operation.originalValue.minus(operation.newValue || new Decimal(0));
          if (sizeDifference.gt(0)) {
            await this.brokerService.increasePosition(operation.brokerOrderId, sizeDifference);
          } else if (sizeDifference.lt(0)) {
            await this.brokerService.decreasePosition(operation.brokerOrderId, sizeDifference.abs());
          }
        }
        break;

      case 'CLOSE_POSITION':
        // Position closing cannot be reversed
        throw new Error('Position closure cannot be reversed');

      default:
        throw new Error(`Unknown operation type for reversal: ${operation.operationType}`);
    }
  }

  private async handleModificationError(modificationId: string, error: any): Promise<void> {
    const modification = this.pendingModifications.get(modificationId);
    if (modification) {
      modification.status = ModificationStatus.FAILED;
      this.pendingModifications.set(modificationId, modification);

      await this.auditService.updateAuditEntry(modification.auditTrailId, {
        status: 'FAILED',
        error: error.message,
        failedAt: new Date()
      });

      this.emit('modificationFailed', {
        modificationId,
        modification,
        error: error.message
      });
    }
  }

  private async getRecentModifications(tradeId: string, timeWindowMs: number): Promise<TradeModification[]> {
    const cutoff = new Date(Date.now() - timeWindowMs);
    return Array.from(this.pendingModifications.values()).filter(
      m => m.tradeId === tradeId && m.timestamp >= cutoff
    );
  }

  private createExecutionError(error: any, brokerId: string): ExecutionError {
    return {
      code: error.code || 'MODIFICATION_ERROR',
      message: error.message || 'Unknown modification error',
      category: this.categorizeError(error),
      retryable: this.isRetryableError(error),
      brokerId,
      timestamp: new Date(),
      details: { originalError: error }
    };
  }

  private categorizeError(error: any): ExecutionErrorCategory {
    if (error.code?.includes('NETWORK') || error.message?.includes('network')) {
      return ExecutionErrorCategory.NETWORK_ERROR;
    }
    if (error.code?.includes('TIMEOUT') || error.message?.includes('timeout')) {
      return ExecutionErrorCategory.TIMEOUT_ERROR;
    }
    if (error.code?.includes('VALIDATION') || error.message?.includes('validation')) {
      return ExecutionErrorCategory.VALIDATION_ERROR;
    }
    if (error.code?.includes('BROKER') || error.message?.includes('broker')) {
      return ExecutionErrorCategory.BROKER_ERROR;
    }
    return ExecutionErrorCategory.UNKNOWN_ERROR;
  }

  private isRetryableError(error: any): boolean {
    const retryableCategories = [
      ExecutionErrorCategory.NETWORK_ERROR,
      ExecutionErrorCategory.TIMEOUT_ERROR
    ];
    return retryableCategories.includes(this.categorizeError(error));
  }

  private generateModificationId(): string {
    return `mod_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }
}

interface RiskThresholds {
  maxPositionSize: Decimal.Instance;
  maxExposurePercentage: Decimal.Instance;
  stopLossMinDistance: Decimal.Instance;
  takeProfitMinDistance: Decimal.Instance;
  maxSlippagePercent: Decimal.Instance;
  maxModificationFrequency: number;
  criticalRiskThreshold: Decimal.Instance;
}