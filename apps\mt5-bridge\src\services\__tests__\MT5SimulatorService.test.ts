import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { MT5SimulatorService, type SimulatorConfig } from '../MT5SimulatorService';
import type { MarketData, Trade } from '@golddaddy/types';

describe('MT5SimulatorService', () => {
  let simulator: MT5SimulatorService;
  let mockConfig: Partial<SimulatorConfig>;

  beforeEach(() => {
    mockConfig = {
      enableSlippage: true,
      slippageRange: [0.1, 1.0],
      latencyMs: [10, 50], // Reduced for testing
      enableErrorSimulation: false,
      errorRate: 0,
      brokerProfile: 'default',
      initialBalance: 10000,
      leverage: 100,
      currency: 'USD'
    };

    simulator = new MT5SimulatorService(mockConfig);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Connection Management', () => {
    it('should successfully connect to MT5', async () => {
      const result = await simulator.connect();
      expect(result).toBe(true);
      expect(simulator.isTerminalConnected()).toBe(true);
    });

    it('should successfully disconnect from MT5', async () => {
      await simulator.connect();
      const result = await simulator.disconnect();
      expect(result).toBe(true);
      expect(simulator.isTerminalConnected()).toBe(false);
    });

    it('should emit connected event on successful connection', async () => {
      let eventEmitted = false;
      simulator.on('connected', () => {
        eventEmitted = true;
      });

      await simulator.connect();
      expect(eventEmitted).toBe(true);
    });

    it('should emit disconnected event on disconnect', async () => {
      let eventEmitted = false;
      simulator.on('disconnected', () => {
        eventEmitted = true;
      });

      await simulator.connect();
      await simulator.disconnect();
      expect(eventEmitted).toBe(true);
    });
  });

  describe('Account Information', () => {
    beforeEach(async () => {
      await simulator.connect();
    });

    it('should return account information when connected', async () => {
      const accountInfo = await simulator.getAccountInfo();
      
      expect(accountInfo).toMatchObject({
        login: expect.any(Number),
        server: expect.any(String),
        name: expect.any(String),
        company: expect.any(String),
        currency: 'USD',
        leverage: 100,
        balance: 10000,
        equity: 10000,
        margin: 0,
        margin_free: 10000,
        margin_level: 0,
        profit: 0,
        trade_allowed: true,
        trade_expert: true
      });
    });

    it('should throw error when getting account info while disconnected', async () => {
      await simulator.disconnect();
      
      await expect(simulator.getAccountInfo()).rejects.toThrow(
        'Not connected to MT5 terminal'
      );
    });

    it('should update account equity when positions change', async () => {
      // Place a buy order
      const orderRequest = {
        action: 1, // TRADE_ACTION_DEAL
        magic: 12345,
        order: 0,
        symbol: 'EURUSD',
        volume: 0.1,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 0, // ORDER_TYPE_BUY
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Test order',
        position: 0,
        position_by: 0
      };

      await simulator.orderSend(orderRequest);
      
      const accountInfo = await simulator.getAccountInfo();
      expect(accountInfo.margin).toBeGreaterThan(0);
      expect(accountInfo.margin_free).toBeLessThan(10000);
    });
  });

  describe('Symbol Management', () => {
    beforeEach(async () => {
      await simulator.connect();
    });

    it('should return available symbols', async () => {
      const symbols = await simulator.getSymbols();
      
      expect(symbols).toHaveLength(7); // Default broker has 7 symbols
      expect(symbols[0]).toMatchObject({
        symbol: expect.any(String),
        description: expect.any(String),
        currency_base: expect.any(String),
        currency_profit: expect.any(String),
        point: expect.any(Number),
        digits: expect.any(Number),
        spread: expect.any(Number),
        volume_min: 0.01,
        volume_max: 100.0
      });
    });

    it('should return specific symbol information', async () => {
      const symbolInfo = await simulator.getSymbolInfo('EURUSD');
      
      expect(symbolInfo).toMatchObject({
        symbol: 'EURUSD',
        description: 'Euro vs US Dollar',
        currency_base: 'EUR',
        currency_profit: 'USD',
        digits: 5,
        point: 0.00001
      });
    });

    it('should return null for non-existent symbol', async () => {
      const symbolInfo = await simulator.getSymbolInfo('INVALID');
      expect(symbolInfo).toBeNull();
    });

    it('should return current market data for symbol', async () => {
      const marketData = await simulator.getSymbolTick('EURUSD');
      
      expect(marketData).toMatchObject({
        symbol: 'EURUSD',
        bid: expect.any(Number),
        ask: expect.any(Number),
        spread: expect.any(Number),
        timestamp: expect.any(Date)
      });
      
      expect(marketData!.ask).toBeGreaterThan(marketData!.bid);
    });

    it('should emit market data events', (done) => {
      simulator.on('marketData', (data: MarketData) => {
        expect(data).toMatchObject({
          symbol: expect.any(String),
          bid: expect.any(Number),
          ask: expect.any(Number),
          spread: expect.any(Number),
          timestamp: expect.any(Date)
        });
        done();
      });

      // Market data should be emitted automatically
      setTimeout(() => {
        done(new Error('Market data event not emitted within timeout'));
      }, 1000);
    });
  });

  describe('Historical Data', () => {
    beforeEach(async () => {
      await simulator.connect();
    });

    it('should return historical data for valid symbol and timeframe', async () => {
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
      const endDate = new Date();
      
      const historicalData = await simulator.getHistoricalData(
        'EURUSD',
        'H1',
        startDate,
        endDate
      );
      
      expect(historicalData).toBeInstanceOf(Array);
      expect(historicalData.length).toBeGreaterThan(0);
      
      const firstCandle = historicalData[0];
      expect(firstCandle).toMatchObject({
        symbol: 'EURUSD',
        timeframe: 'H1',
        open: expect.any(Number),
        high: expect.any(Number),
        low: expect.any(Number),
        close: expect.any(Number),
        volume: expect.any(Number),
        timestamp: expect.any(Date)
      });
      
      // OHLC validation
      expect(firstCandle.high).toBeGreaterThanOrEqual(firstCandle.open);
      expect(firstCandle.high).toBeGreaterThanOrEqual(firstCandle.close);
      expect(firstCandle.low).toBeLessThanOrEqual(firstCandle.open);
      expect(firstCandle.low).toBeLessThanOrEqual(firstCandle.close);
    });

    it('should throw error when requesting historical data while disconnected', async () => {
      await simulator.disconnect();
      
      await expect(simulator.getHistoricalData(
        'EURUSD',
        'H1',
        new Date(),
        new Date()
      )).rejects.toThrow('Not connected to MT5 terminal');
    });
  });

  describe('Order Execution', () => {
    beforeEach(async () => {
      await simulator.connect();
    });

    it('should successfully execute a buy order', async () => {
      const orderRequest = {
        action: 1,
        magic: 12345,
        order: 0,
        symbol: 'EURUSD',
        volume: 0.1,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 0, // BUY
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Test buy order',
        position: 0,
        position_by: 0
      };

      const result = await simulator.orderSend(orderRequest);
      
      expect(result.retcode).toBe(10009); // TRADE_RETCODE_DONE
      expect(result.deal).toBeGreaterThan(0);
      expect(result.order).toBeGreaterThan(0);
      expect(result.volume).toBe(0.1);
      expect(result.price).toBeGreaterThan(0);
    });

    it('should successfully execute a sell order', async () => {
      const orderRequest = {
        action: 1,
        magic: 12345,
        order: 0,
        symbol: 'EURUSD',
        volume: 0.1,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 1, // SELL
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Test sell order',
        position: 0,
        position_by: 0
      };

      const result = await simulator.orderSend(orderRequest);
      
      expect(result.retcode).toBe(10009);
      expect(result.volume).toBe(0.1);
    });

    it('should reject order with invalid symbol', async () => {
      const orderRequest = {
        action: 1,
        magic: 12345,
        order: 0,
        symbol: 'INVALID',
        volume: 0.1,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 0,
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Invalid symbol',
        position: 0,
        position_by: 0
      };

      const result = await simulator.orderSend(orderRequest);
      
      expect(result.retcode).toBe(10014); // TRADE_RETCODE_INVALID_SYMBOL
    });

    it('should reject order with invalid volume', async () => {
      const orderRequest = {
        action: 1,
        magic: 12345,
        order: 0,
        symbol: 'EURUSD',
        volume: 1000, // Too large
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 0,
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Invalid volume',
        position: 0,
        position_by: 0
      };

      const result = await simulator.orderSend(orderRequest);
      
      expect(result.retcode).toBe(10015); // TRADE_RETCODE_INVALID_VOLUME
    });

    it('should apply slippage when enabled', async () => {
      const marketData = await simulator.getSymbolTick('EURUSD');
      const expectedPrice = marketData!.ask;

      const orderRequest = {
        action: 1,
        magic: 12345,
        order: 0,
        symbol: 'EURUSD',
        volume: 0.1,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 0, // BUY
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Slippage test',
        position: 0,
        position_by: 0
      };

      const result = await simulator.orderSend(orderRequest);
      
      // With slippage enabled, execution price should be different from ask price
      expect(result.price).not.toBe(expectedPrice);
      expect(Math.abs(result.price - expectedPrice)).toBeLessThan(0.01); // Reasonable slippage
    });

    it('should emit trade update event on successful execution', (done) => {
      simulator.on('tradeUpdate', (trade: Trade) => {
        expect(trade).toMatchObject({
          id: expect.any(String),
          accountId: expect.any(String),
          symbol: 'EURUSD',
          type: 'BUY',
          volume: 0.1,
          openPrice: expect.any(Number),
          status: 'OPEN'
        });
        done();
      });

      const orderRequest = {
        action: 1,
        magic: 12345,
        order: 0,
        symbol: 'EURUSD',
        volume: 0.1,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 0,
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Event test',
        position: 0,
        position_by: 0
      };

      simulator.orderSend(orderRequest);
    });
  });

  describe('Position Management', () => {
    beforeEach(async () => {
      await simulator.connect();
    });

    it('should track positions after order execution', async () => {
      const orderRequest = {
        action: 1,
        magic: 12345,
        order: 0,
        symbol: 'EURUSD',
        volume: 0.1,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 0, // BUY
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Position test',
        position: 0,
        position_by: 0
      };

      const result = await simulator.orderSend(orderRequest);
      const positions = await simulator.getPositions();
      
      expect(positions).toHaveLength(1);
      expect(positions[0]).toMatchObject({
        ticket: result.order,
        symbol: 'EURUSD',
        type: 0,
        volume: 0.1,
        price_open: expect.any(Number),
        profit: expect.any(Number)
      });
    });

    it('should close position successfully', async () => {
      // Open position
      const orderRequest = {
        action: 1,
        magic: 12345,
        order: 0,
        symbol: 'EURUSD',
        volume: 0.1,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 0,
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Close test',
        position: 0,
        position_by: 0
      };

      const openResult = await simulator.orderSend(orderRequest);
      
      // Close position
      const closeResult = await simulator.closePosition(openResult.order);
      
      expect(closeResult.retcode).toBe(10009);
      expect(closeResult.order).toBe(openResult.order);
      
      // Position should be removed
      const positions = await simulator.getPositions();
      expect(positions).toHaveLength(0);
    });

    it('should return error when closing non-existent position', async () => {
      const result = await simulator.closePosition(999999);
      
      expect(result.retcode).toBe(10013); // TRADE_RETCODE_INVALID
      expect(result.comment).toBe('Position not found');
    });
  });

  describe('Broker Profiles', () => {
    it('should initialize with default broker profile', () => {
      const status = simulator.getStatus();
      expect(status.brokerProfile).toBe('default');
    });

    it('should switch broker profiles', () => {
      simulator.configure({ brokerProfile: 'ecn' });
      const status = simulator.getStatus();
      expect(status.brokerProfile).toBe('ecn');
    });

    it('should adjust spreads based on broker profile', async () => {
      await simulator.connect();
      
      // Default profile
      let marketData = await simulator.getSymbolTick('EURUSD');
      const defaultSpread = marketData!.spread;
      
      // Switch to ECN (lower spreads)
      simulator.configure({ brokerProfile: 'ecn' });
      simulator.reset(); // Reinitialize with new profile
      await simulator.connect();
      
      marketData = await simulator.getSymbolTick('EURUSD');
      const ecnSpread = marketData!.spread;
      
      expect(ecnSpread).toBeLessThan(defaultSpread);
    });
  });

  describe('Error Simulation', () => {
    beforeEach(() => {
      simulator.configure({
        enableErrorSimulation: true,
        errorRate: 1.0 // Force errors for testing
      });
    });

    it('should simulate connection errors', async () => {
      await expect(simulator.connect()).rejects.toThrow();
    });

    it('should simulate order execution errors', async () => {
      // First create a simulator without error simulation to establish connection
      const testSimulator = new MT5SimulatorService({
        enableErrorSimulation: false,
        errorRate: 0
      });
      await testSimulator.connect();
      
      // Then enable error simulation
      testSimulator.configure({
        enableErrorSimulation: true,
        errorRate: 1.0
      });

      const orderRequest = {
        action: 1,
        magic: 12345,
        order: 0,
        symbol: 'EURUSD',
        volume: 0.1,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 0,
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Error test',
        position: 0,
        position_by: 0
      };

      await expect(testSimulator.orderSend(orderRequest)).rejects.toThrow();
    });
  });

  describe('Simulator Configuration', () => {
    it('should update configuration', () => {
      const newConfig = {
        enableSlippage: false,
        latencyMs: [0, 0] as [number, number],
        brokerProfile: 'retail'
      };

      simulator.configure(newConfig);
      const status = simulator.getStatus();
      
      expect(status.config.enableSlippage).toBe(false);
      expect(status.config.latencyMs).toEqual([0, 0]);
      expect(status.brokerProfile).toBe('retail');
    });

    it('should reset to initial state', async () => {
      await simulator.connect();
      
      // Create some positions
      const orderRequest = {
        action: 1,
        magic: 12345,
        order: 0,
        symbol: 'EURUSD',
        volume: 0.1,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: 0,
        type_filling: 0,
        type_time: 0,
        expiration: 0,
        comment: 'Reset test',
        position: 0,
        position_by: 0
      };

      await simulator.orderSend(orderRequest);
      let positions = await simulator.getPositions();
      expect(positions).toHaveLength(1);
      
      // Reset simulator
      simulator.reset();
      await simulator.connect();
      
      positions = await simulator.getPositions();
      expect(positions).toHaveLength(0);
      
      const accountInfo = await simulator.getAccountInfo();
      expect(accountInfo.balance).toBe(10000);
      expect(accountInfo.equity).toBe(10000);
    });

    it('should provide status information', () => {
      const status = simulator.getStatus();
      
      expect(status).toMatchObject({
        connected: expect.any(Boolean),
        positionsCount: expect.any(Number),
        accountEquity: expect.any(Number),
        totalProfit: expect.any(Number),
        brokerProfile: expect.any(String),
        config: expect.any(Object)
      });
    });
  });

  describe('Performance and Latency', () => {
    it('should respect latency configuration', async () => {
      simulator.configure({ latencyMs: [100, 200] });
      
      const startTime = Date.now();
      await simulator.connect();
      const endTime = Date.now();
      
      const latency = endTime - startTime;
      expect(latency).toBeGreaterThanOrEqual(100);
      expect(latency).toBeLessThan(300); // Account for test overhead
    });

    it('should handle high-frequency operations', async () => {
      await simulator.connect();
      simulator.configure({ latencyMs: [0, 0] }); // Disable latency for speed
      
      const promises = [];
      const startTime = Date.now();
      
      // Execute 100 rapid operations
      for (let i = 0; i < 100; i++) {
        promises.push(simulator.getSymbolTick('EURUSD'));
      }
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      expect(results).toHaveLength(100);
      expect(endTime - startTime).toBeLessThan(1000); // Should complete quickly
    });
  });
});