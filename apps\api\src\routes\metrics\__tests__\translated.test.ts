/**
 * Integration tests for translated metrics API routes
 */

import request from 'supertest';
import express from 'express';
import { vi } from 'vitest';
import { getTranslatedMetrics, invalidateStrategyCache } from '../translated';

// Mock the metrics service
vi.mock('../../services/metrics/MetricsTranslationService');

const app = express();
app.use(express.json());
app.get('/api/metrics/translated/:strategyId', getTranslatedMetrics);
app.delete('/api/metrics/cache/invalidate/:strategyId', invalidateStrategyCache);

describe('Translated Metrics API', () => {
  describe('GET /api/metrics/translated/:strategyId', () => {
    it('returns translated metrics successfully', async () => {
      const response = await request(app)
        .get('/api/metrics/translated/test-strategy-1')
        .set('x-user-experience', 'intermediate')
        .set('x-user-risk-tolerance', 'moderate')
        .set('x-user-id', 'test-user')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        data: expect.objectContaining({
          metrics: expect.any(Array),
          healthScore: expect.any(Object),
          healthExplanation: expect.any(Object),
        }),
      });
    });

    it('validates user experience header', async () => {
      const response = await request(app)
        .get('/api/metrics/translated/test-strategy-1')
        .set('x-user-experience', 'invalid-level')
        .set('x-user-risk-tolerance', 'moderate')
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'Invalid user context',
      });
    });

    it('validates risk tolerance header', async () => {
      const response = await request(app)
        .get('/api/metrics/translated/test-strategy-1')
        .set('x-user-experience', 'intermediate')
        .set('x-user-risk-tolerance', 'invalid-tolerance')
        .expect(400);

      expect(response.body).toMatchObject({
        success: false,
        error: 'Invalid user context',
      });
    });

    it('handles missing strategy ID', async () => {
      await request(app)
        .get('/api/metrics/translated/')
        .set('x-user-experience', 'intermediate')
        .set('x-user-risk-tolerance', 'moderate')
        .expect(404);
    });

    it('filters metrics by type when requested', async () => {
      const response = await request(app)
        .get('/api/metrics/translated/test-strategy-1?metricTypes=win_rate,profit_factor')
        .set('x-user-experience', 'intermediate')
        .set('x-user-risk-tolerance', 'moderate')
        .expect(200);

      expect(response.body.success).toBe(true);
    });

    it('uses default user context when headers missing', async () => {
      const response = await request(app)
        .get('/api/metrics/translated/test-strategy-1')
        .expect(200);

      expect(response.body.success).toBe(true);
    });
  });

  describe('DELETE /api/metrics/cache/invalidate/:strategyId', () => {
    it('invalidates cache successfully', async () => {
      const response = await request(app)
        .delete('/api/metrics/cache/invalidate/test-strategy-1')
        .set('x-user-id', 'test-user')
        .expect(200);

      expect(response.body).toMatchObject({
        success: true,
        message: expect.stringContaining('invalidated'),
      });
    });

    it('handles missing strategy ID for cache invalidation', async () => {
      await request(app)
        .delete('/api/metrics/cache/invalidate/')
        .expect(404);
    });
  });

  describe('Error handling', () => {
    it('handles service errors gracefully', async () => {
      // This would require mocking the service to throw an error
      // For now, we'll test the basic structure
      const response = await request(app)
        .get('/api/metrics/translated/error-strategy')
        .set('x-user-experience', 'intermediate')
        .set('x-user-risk-tolerance', 'moderate');

      // Should not crash and should return proper error structure
      expect(response.body).toHaveProperty('success');
    });
  });
});