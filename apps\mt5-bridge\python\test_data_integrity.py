"""
Test Suite for Data Integrity and Quality Validation System
Comprehensive tests for data integrity validation, anomaly detection, and quality reporting
"""

import pytest
import asyncio
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any
from unittest.mock import Mock, patch
import hashlib

from data_integrity import (
    DataIntegrityValidator, 
    IntegrityStatus, 
    AnomalyType,
    IntegrityCheck,
    AnomalyDetection,
    QualityReport,
    get_data_integrity_validator
)
from database import get_db_manager, MarketDataPoint
from config import get_config

class TestDataIntegrityValidator:
    """Test class for DataIntegrityValidator"""
    
    @pytest.fixture
    def validator(self):
        """Create a data integrity validator instance for testing"""
        return DataIntegrityValidator()
    
    @pytest.fixture
    def sample_market_data(self):
        """Generate sample market data for testing"""
        base_time = datetime.now() - timedelta(hours=1)
        data = []
        
        for i in range(100):
            timestamp = base_time + timedelta(minutes=i)
            data.append({
                'timestamp': timestamp,
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000 + (i * 0.0001),
                'high': 1.1000 + (i * 0.0001) + 0.0005,
                'low': 1.1000 + (i * 0.0001) - 0.0005,
                'close': 1.1000 + (i * 0.0001) + 0.0002,
                'volume': 1000 + (i * 10),
                'source': 'mt5'
            })
        
        return data
    
    @pytest.fixture
    def corrupted_market_data(self):
        """Generate corrupted market data for testing validation"""
        base_time = datetime.now() - timedelta(hours=1)
        data = []
        
        for i in range(50):
            timestamp = base_time + timedelta(minutes=i)
            
            # Add various types of corruption
            if i == 10:
                # Missing required field
                data.append({
                    'timestamp': timestamp,
                    'instrument': 'EURUSD',
                    'timeframe': '1m',
                    # Missing 'open' field
                    'high': 1.1005,
                    'low': 1.0995,
                    'close': 1.1002,
                    'volume': 1000
                })
            elif i == 20:
                # Invalid price relationships (high < low)
                data.append({
                    'timestamp': timestamp,
                    'instrument': 'EURUSD',
                    'timeframe': '1m',
                    'open': 1.1000,
                    'high': 1.0995,  # High less than low
                    'low': 1.1005,
                    'close': 1.1002,
                    'volume': 1000
                })
            elif i == 30:
                # Negative volume
                data.append({
                    'timestamp': timestamp,
                    'instrument': 'EURUSD',
                    'timeframe': '1m',
                    'open': 1.1000,
                    'high': 1.1005,
                    'low': 1.0995,
                    'close': 1.1002,
                    'volume': -500  # Negative volume
                })
            elif i == 40:
                # Price spike (unrealistic change)
                data.append({
                    'timestamp': timestamp,
                    'instrument': 'EURUSD',
                    'timeframe': '1m',
                    'open': 2.0000,  # Massive price spike
                    'high': 2.0005,
                    'low': 1.9995,
                    'close': 2.0002,
                    'volume': 1000
                })
            else:
                # Normal data
                data.append({
                    'timestamp': timestamp,
                    'instrument': 'EURUSD',
                    'timeframe': '1m',
                    'open': 1.1000 + (i * 0.0001),
                    'high': 1.1000 + (i * 0.0001) + 0.0005,
                    'low': 1.1000 + (i * 0.0001) - 0.0005,
                    'close': 1.1000 + (i * 0.0001) + 0.0002,
                    'volume': 1000 + (i * 10)
                })
        
        return data

class TestDataFormatIntegrity:
    """Test data format integrity validation"""
    
    @pytest.mark.asyncio
    async def test_valid_data_format(self, validator, sample_market_data):
        """Test validation of properly formatted data"""
        
        checks = await validator._check_data_format_integrity(
            sample_market_data, 'EURUSD', '1m'
        )
        
        # Should have one check per record plus summary
        assert len(checks) >= len(sample_market_data)
        
        # All checks should be valid for good data
        valid_checks = [c for c in checks if c.status == IntegrityStatus.VALID]
        assert len(valid_checks) >= len(sample_market_data)
    
    @pytest.mark.asyncio
    async def test_missing_fields_detection(self, validator):
        """Test detection of missing required fields"""
        
        corrupted_data = [
            {
                'timestamp': datetime.now(),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                # Missing 'open', 'high', 'low', 'close', 'volume'
            }
        ]
        
        checks = await validator._check_data_format_integrity(
            corrupted_data, 'EURUSD', '1m'
        )
        
        # Should detect corruption
        corrupt_checks = [c for c in checks if c.status == IntegrityStatus.CORRUPT]
        assert len(corrupt_checks) > 0
        
        # Check that missing fields are identified
        for check in corrupt_checks:
            if 'missing_fields' in check.details:
                assert len(check.details['missing_fields']) > 0
    
    @pytest.mark.asyncio
    async def test_data_type_validation(self, validator):
        """Test validation of data types"""
        
        invalid_data = [
            {
                'timestamp': datetime.now(),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 'invalid_price',  # String instead of float
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1002,
                'volume': 'invalid_volume'  # String instead of int
            }
        ]
        
        checks = await validator._check_data_format_integrity(
            invalid_data, 'EURUSD', '1m'
        )
        
        # Should detect type errors
        corrupt_checks = [c for c in checks if c.status == IntegrityStatus.CORRUPT]
        assert len(corrupt_checks) > 0

class TestPriceDataIntegrity:
    """Test price data integrity validation"""
    
    @pytest.mark.asyncio
    async def test_valid_price_relationships(self, validator, sample_market_data):
        """Test validation of proper OHLC relationships"""
        
        checks = await validator._check_price_data_integrity(
            sample_market_data, 'EURUSD', '1m'
        )
        
        # All checks should be valid for good data
        valid_checks = [c for c in checks if c.status == IntegrityStatus.VALID]
        assert len(valid_checks) == len(sample_market_data)
    
    @pytest.mark.asyncio
    async def test_invalid_price_relationships(self, validator):
        """Test detection of invalid OHLC relationships"""
        
        invalid_data = [
            {
                'timestamp': datetime.now(),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000,
                'high': 1.0990,  # High less than open
                'low': 1.1010,   # Low greater than open
                'close': 1.1002,
                'volume': 1000
            }
        ]
        
        checks = await validator._check_price_data_integrity(
            invalid_data, 'EURUSD', '1m'
        )
        
        # Should detect price violations
        corrupt_checks = [c for c in checks if c.status == IntegrityStatus.CORRUPT]
        assert len(corrupt_checks) > 0
        
        # Check violation details
        for check in corrupt_checks:
            assert 'violations' in check.details
            assert len(check.details['violations']) > 0
    
    @pytest.mark.asyncio
    async def test_negative_price_detection(self, validator):
        """Test detection of negative or zero prices"""
        
        invalid_data = [
            {
                'timestamp': datetime.now(),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 0.0,     # Zero price
                'high': -1.1005, # Negative price
                'low': 1.0995,
                'close': 1.1002,
                'volume': 1000
            }
        ]
        
        checks = await validator._check_price_data_integrity(
            invalid_data, 'EURUSD', '1m'
        )
        
        # Should detect violations
        corrupt_checks = [c for c in checks if c.status == IntegrityStatus.CORRUPT]
        assert len(corrupt_checks) > 0

class TestTemporalIntegrity:
    """Test temporal data integrity validation"""
    
    @pytest.mark.asyncio
    async def test_valid_temporal_sequence(self, validator, sample_market_data):
        """Test validation of proper temporal sequence"""
        
        checks = await validator._check_temporal_integrity(
            sample_market_data, 'EURUSD', '1m'
        )
        
        # Most checks should be valid for properly sequenced data
        valid_checks = [c for c in checks if c.status == IntegrityStatus.VALID]
        assert len(valid_checks) > len(sample_market_data) * 0.8
    
    @pytest.mark.asyncio
    async def test_duplicate_timestamp_detection(self, validator):
        """Test detection of duplicate timestamps"""
        
        base_time = datetime.now()
        duplicate_data = [
            {
                'timestamp': base_time,
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000,
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1002,
                'volume': 1000
            },
            {
                'timestamp': base_time,  # Duplicate timestamp
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1001,
                'high': 1.1006,
                'low': 1.0996,
                'close': 1.1003,
                'volume': 1100
            }
        ]
        
        checks = await validator._check_temporal_integrity(
            duplicate_data, 'EURUSD', '1m'
        )
        
        # Should detect temporal issues
        issue_checks = [c for c in checks if c.status != IntegrityStatus.VALID]
        assert len(issue_checks) > 0
    
    @pytest.mark.asyncio
    async def test_large_time_gap_detection(self, validator):
        """Test detection of large time gaps"""
        
        base_time = datetime.now()
        gap_data = [
            {
                'timestamp': base_time,
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000,
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1002,
                'volume': 1000
            },
            {
                'timestamp': base_time + timedelta(hours=2),  # Large gap
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1001,
                'high': 1.1006,
                'low': 1.0996,
                'close': 1.1003,
                'volume': 1100
            }
        ]
        
        checks = await validator._check_temporal_integrity(
            gap_data, 'EURUSD', '1m'
        )
        
        # Should detect gap issues
        warning_checks = [c for c in checks if c.status == IntegrityStatus.WARNING]
        assert len(warning_checks) > 0

class TestVolumeDataIntegrity:
    """Test volume data integrity validation"""
    
    @pytest.mark.asyncio
    async def test_valid_volume_data(self, validator, sample_market_data):
        """Test validation of proper volume data"""
        
        checks = await validator._check_volume_data_integrity(
            sample_market_data, 'EURUSD', '1m'
        )
        
        # All checks should be valid for good data
        valid_checks = [c for c in checks if c.status == IntegrityStatus.VALID]
        assert len(valid_checks) == len(sample_market_data)
    
    @pytest.mark.asyncio
    async def test_negative_volume_detection(self, validator):
        """Test detection of negative volumes"""
        
        invalid_data = [
            {
                'timestamp': datetime.now(),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000,
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1002,
                'volume': -500  # Negative volume
            }
        ]
        
        checks = await validator._check_volume_data_integrity(
            invalid_data, 'EURUSD', '1m'
        )
        
        # Should detect negative volume
        corrupt_checks = [c for c in checks if c.status == IntegrityStatus.CORRUPT]
        assert len(corrupt_checks) > 0
    
    @pytest.mark.asyncio
    async def test_volume_spike_detection(self, validator):
        """Test detection of extreme volume spikes"""
        
        base_time = datetime.now()
        spike_data = []
        
        # Generate normal volume data
        for i in range(20):
            spike_data.append({
                'timestamp': base_time + timedelta(minutes=i),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000,
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1002,
                'volume': 1000  # Normal volume
            })
        
        # Add volume spike
        spike_data.append({
            'timestamp': base_time + timedelta(minutes=20),
            'instrument': 'EURUSD',
            'timeframe': '1m',
            'open': 1.1000,
            'high': 1.1005,
            'low': 1.0995,
            'close': 1.1002,
            'volume': 50000  # Volume spike
        })
        
        checks = await validator._check_volume_data_integrity(
            spike_data, 'EURUSD', '1m'
        )
        
        # Should detect volume spike warning
        warning_checks = [c for c in checks if c.status == IntegrityStatus.WARNING]
        assert len(warning_checks) > 0

class TestChecksumCalculation:
    """Test checksum calculation and verification"""
    
    @pytest.mark.asyncio
    async def test_checksum_calculation(self, validator, sample_market_data):
        """Test data checksum calculation"""
        
        checks = await validator._calculate_data_checksums(
            sample_market_data, 'EURUSD', '1m'
        )
        
        # Should have checksum calculations
        assert len(checks) >= 2  # Overall and sample checksums
        
        # Checksums should be valid
        for check in checks:
            assert check.status == IntegrityStatus.VALID
            assert check.checksum is not None
            assert len(check.checksum) == 64  # SHA256 hex length
    
    @pytest.mark.asyncio
    async def test_checksum_consistency(self, validator):
        """Test checksum consistency for identical data"""
        
        data = [
            {
                'timestamp': datetime.now(),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000,
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1002,
                'volume': 1000
            }
        ]
        
        # Calculate checksum twice
        checks1 = await validator._calculate_data_checksums(data, 'EURUSD', '1m')
        checks2 = await validator._calculate_data_checksums(data, 'EURUSD', '1m')
        
        # Checksums should be identical
        assert checks1[0].checksum == checks2[0].checksum

class TestAnomalyDetection:
    """Test anomaly detection functionality"""
    
    @pytest.mark.asyncio
    async def test_price_spike_detection(self, validator):
        """Test detection of price spikes"""
        
        base_time = datetime.now()
        spike_data = []
        
        # Generate normal price data
        for i in range(20):
            spike_data.append({
                'timestamp': base_time + timedelta(minutes=i),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000,
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1000 + (i * 0.0001),  # Gradual change
                'volume': 1000
            })
        
        # Add price spike
        spike_data.append({
            'timestamp': base_time + timedelta(minutes=20),
            'instrument': 'EURUSD',
            'timeframe': '1m',
            'open': 1.1020,
            'high': 1.1025,
            'low': 1.1015,
            'close': 1.1500,  # 4% spike
            'volume': 1000
        })
        
        anomalies = await validator._detect_price_anomalies(
            spike_data, 'EURUSD', '1m'
        )
        
        # Should detect price spike
        price_spikes = [a for a in anomalies if a.anomaly_type == AnomalyType.PRICE_SPIKE]
        assert len(price_spikes) > 0
        
        # Check spike properties
        for spike in price_spikes:
            assert spike.severity > 0
            assert spike.confidence > 0
    
    @pytest.mark.asyncio
    async def test_volume_anomaly_detection(self, validator):
        """Test detection of volume anomalies"""
        
        base_time = datetime.now()
        volume_data = []
        
        # Generate normal volume data
        for i in range(20):
            volume_data.append({
                'timestamp': base_time + timedelta(minutes=i),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000,
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1002,
                'volume': 1000  # Normal volume
            })
        
        # Add volume anomaly
        volume_data.append({
            'timestamp': base_time + timedelta(minutes=20),
            'instrument': 'EURUSD',
            'timeframe': '1m',
            'open': 1.1000,
            'high': 1.1005,
            'low': 1.0995,
            'close': 1.1002,
            'volume': 50000  # 50x normal volume
        })
        
        anomalies = await validator._detect_volume_anomalies(
            volume_data, 'EURUSD', '1m'
        )
        
        # Should detect volume anomaly
        volume_anomalies = [a for a in anomalies if a.anomaly_type == AnomalyType.VOLUME_ANOMALY]
        assert len(volume_anomalies) > 0
    
    @pytest.mark.asyncio
    async def test_data_gap_detection(self, validator):
        """Test detection of data gaps"""
        
        base_time = datetime.now()
        gap_data = []
        
        # Generate data with gap
        for i in range(10):
            gap_data.append({
                'timestamp': base_time + timedelta(minutes=i),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000,
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1002,
                'volume': 1000
            })
        
        # Add data after large gap
        for i in range(10, 20):
            gap_data.append({
                'timestamp': base_time + timedelta(hours=2, minutes=i),  # 2-hour gap
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000,
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1002,
                'volume': 1000
            })
        
        anomalies = await validator._detect_temporal_anomalies(
            gap_data, 'EURUSD', '1m'
        )
        
        # Should detect data gaps
        gap_anomalies = [a for a in anomalies if a.anomaly_type == AnomalyType.DATA_GAP]
        assert len(gap_anomalies) > 0

class TestComprehensiveValidation:
    """Test comprehensive validation functionality"""
    
    @pytest.mark.asyncio
    async def test_comprehensive_validation_good_data(self, validator, sample_market_data):
        """Test comprehensive validation with good data"""
        
        # Mock database manager to return sample data
        with patch.object(validator.db_manager, 'get_market_data', return_value=sample_market_data):
            with patch.object(validator, '_store_validation_results', return_value=None):
                start_date = datetime.now() - timedelta(hours=2)
                end_date = datetime.now()
                
                report = await validator.perform_comprehensive_validation(
                    'EURUSD', '1m', start_date, end_date
                )
                
                # Check report structure
                assert isinstance(report, QualityReport)
                assert report.instrument == 'EURUSD'
                assert report.timeframe == '1m'
                assert report.total_records == len(sample_market_data)
                assert report.integrity_score > 0.8  # Should be high for good data
                assert report.data_completeness > 0.8
                assert len(report.checks_performed) > 0
                assert len(report.recommendations) > 0
    
    @pytest.mark.asyncio
    async def test_comprehensive_validation_corrupted_data(self, validator, corrupted_market_data):
        """Test comprehensive validation with corrupted data"""
        
        # Mock database manager to return corrupted data
        with patch.object(validator.db_manager, 'get_market_data', return_value=corrupted_market_data):
            with patch.object(validator, '_store_validation_results', return_value=None):
                start_date = datetime.now() - timedelta(hours=2)
                end_date = datetime.now()
                
                report = await validator.perform_comprehensive_validation(
                    'EURUSD', '1m', start_date, end_date
                )
                
                # Check report indicates issues
                assert report.integrity_score < 1.0  # Should be lower for corrupted data
                assert report.anomalies_detected > 0
                assert len([c for c in report.checks_performed if c.status == IntegrityStatus.CORRUPT]) > 0
                assert any('Address' in rec for rec in report.recommendations)
    
    @pytest.mark.asyncio
    async def test_comprehensive_validation_no_data(self, validator):
        """Test comprehensive validation with no data"""
        
        # Mock database manager to return empty data
        with patch.object(validator.db_manager, 'get_market_data', return_value=[]):
            start_date = datetime.now() - timedelta(hours=2)
            end_date = datetime.now()
            
            report = await validator.perform_comprehensive_validation(
                'NONEXISTENT', '1m', start_date, end_date
            )
            
            # Check empty data handling
            assert report.total_records == 0
            assert report.valid_records == 0
            assert report.integrity_score == 0.0
            assert report.data_completeness == 0.0
            assert "No data found" in report.recommendations[0]

class TestQualityMetrics:
    """Test quality metrics calculation"""
    
    @pytest.mark.asyncio
    async def test_expected_records_calculation(self, validator):
        """Test calculation of expected record counts"""
        
        start_date = datetime.now() - timedelta(hours=1)
        end_date = datetime.now()
        
        # Test 1-minute timeframe
        expected_1m = await validator._calculate_expected_records(
            'EURUSD', '1m', start_date, end_date
        )
        assert expected_1m > 0
        
        # Test daily timeframe
        expected_1d = await validator._calculate_expected_records(
            'EURUSD', '1d', start_date, end_date
        )
        assert expected_1d >= 0
        
        # 1-minute should have more records than daily
        if expected_1d > 0:
            assert expected_1m > expected_1d
    
    @pytest.mark.asyncio
    async def test_recommendations_generation(self, validator):
        """Test generation of quality recommendations"""
        
        # Create sample checks with issues
        integrity_checks = [
            IntegrityCheck(
                check_id="test_1",
                instrument="EURUSD",
                timeframe="1m",
                timestamp=datetime.now(),
                status=IntegrityStatus.CORRUPT,
                message="Test corruption"
            ),
            IntegrityCheck(
                check_id="test_2",
                instrument="EURUSD",
                timeframe="1m",
                timestamp=datetime.now(),
                status=IntegrityStatus.WARNING,
                message="Test warning"
            )
        ]
        
        # Create sample anomalies
        anomalies = [
            AnomalyDetection(
                anomaly_id="anomaly_1",
                instrument="EURUSD",
                timeframe="1m",
                anomaly_type=AnomalyType.PRICE_SPIKE,
                severity=0.8,
                timestamp=datetime.now(),
                affected_records=1,
                description="Test anomaly"
            )
        ]
        
        recommendations = await validator._generate_recommendations(
            integrity_checks, anomalies, 0.5  # 50% completeness
        )
        
        assert len(recommendations) > 0
        assert any("critical" in rec.lower() for rec in recommendations)
        assert any("warning" in rec.lower() for rec in recommendations)
        assert any("completeness" in rec.lower() for rec in recommendations)
        assert any("anomalies" in rec.lower() for rec in recommendations)

class TestGlobalInstance:
    """Test global instance management"""
    
    def test_get_data_integrity_validator(self):
        """Test global validator instance"""
        
        validator1 = get_data_integrity_validator()
        validator2 = get_data_integrity_validator()
        
        # Should return the same instance
        assert validator1 is validator2
        assert isinstance(validator1, DataIntegrityValidator)

# Performance and Integration Tests
class TestPerformanceAndIntegration:
    """Test performance and integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_large_dataset_validation(self, validator):
        """Test validation performance with large datasets"""
        
        # Generate large dataset
        base_time = datetime.now() - timedelta(hours=24)
        large_data = []
        
        for i in range(1000):  # 1000 records
            large_data.append({
                'timestamp': base_time + timedelta(minutes=i),
                'instrument': 'EURUSD',
                'timeframe': '1m',
                'open': 1.1000 + (i * 0.00001),
                'high': 1.1000 + (i * 0.00001) + 0.0005,
                'low': 1.1000 + (i * 0.00001) - 0.0005,
                'close': 1.1000 + (i * 0.00001) + 0.0002,
                'volume': 1000 + (i % 100)
            })
        
        # Test format integrity on large dataset
        start_time = datetime.now()
        checks = await validator._check_data_format_integrity(
            large_data, 'EURUSD', '1m'
        )
        end_time = datetime.now()
        
        # Should complete in reasonable time (less than 5 seconds)
        processing_time = (end_time - start_time).total_seconds()
        assert processing_time < 5.0
        assert len(checks) > 0
    
    @pytest.mark.asyncio
    async def test_concurrent_validation(self, validator):
        """Test concurrent validation operations"""
        
        # Create different datasets
        datasets = []
        for j in range(3):
            data = []
            base_time = datetime.now() - timedelta(hours=j+1)
            
            for i in range(100):
                data.append({
                    'timestamp': base_time + timedelta(minutes=i),
                    'instrument': f'PAIR{j}',
                    'timeframe': '1m',
                    'open': 1.1000 + (i * 0.0001),
                    'high': 1.1000 + (i * 0.0001) + 0.0005,
                    'low': 1.1000 + (i * 0.0001) - 0.0005,
                    'close': 1.1000 + (i * 0.0001) + 0.0002,
                    'volume': 1000 + (i * 10)
                })
            datasets.append(data)
        
        # Run validations concurrently
        tasks = []
        for i, data in enumerate(datasets):
            task = validator._check_data_format_integrity(data, f'PAIR{i}', '1m')
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        # All validations should complete successfully
        assert len(results) == 3
        for result in results:
            assert len(result) > 0

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])