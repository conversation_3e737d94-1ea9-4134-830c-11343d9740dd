/**
 * WebSocket Security Middleware
 * 
 * Comprehensive security layer for WebSocket connections including
 * authentication, authorization, rate limiting, and audit logging
 */

import { EventEmitter } from 'events';
import * as jwt from 'jsonwebtoken';
import { 
  validateIncomingMessage, 
  validateSubscriptionSecurity, 
  validateInstrumentAccess,
  IncomingMessage,
  SecurityAlert,
  securityAlertSchema,
} from './schemas';
import { auditLogger, AUDIT_ACTIONS } from '../../lib/audit';

// ===== Security Configuration =====

export interface SecurityConfig {
  jwtSecret: string;
  rateLimiting: {
    tokensPerInterval: number;
    interval: number; // milliseconds
    burstCapacity: number;
  };
  connectionLimits: {
    maxConnectionsPerUser: number;
    maxConnectionsPerIP: number;
    maxGlobalConnections: number;
  };
  validation: {
    enableStrictValidation: boolean;
    maxMessageSize: number;
    allowedOrigins?: string[];
  };
  monitoring: {
    enableAuditLogging: boolean;
    enableSecurityAlerts: boolean;
    suspiciousActivityThreshold: number;
  };
}

export interface SecurityContext {
  userId: string;
  permissions: string[];
  region: string;
  userTier: 'basic' | 'premium' | 'enterprise';
  ipAddress: string;
  userAgent?: string;
  connectionTime: Date;
  lastActivity: Date;
}

export interface ConnectionMetrics {
  messageCount: number;
  errorCount: number;
  rateLimitViolations: number;
  lastViolation?: Date;
  suspiciousActivityScore: number;
}

// ===== Rate Limiter Implementation =====

class AdvancedRateLimiter {
  private tokensPerInterval: number;
  private interval: number;
  private burstCapacity: number;
  private tokens: number;
  private lastRefill: number;

  constructor(
    tokensPerInterval: number, 
    interval: number, 
    burstCapacity: number = tokensPerInterval
  ) {
    this.tokensPerInterval = tokensPerInterval;
    this.interval = interval;
    this.burstCapacity = burstCapacity;
    this.tokens = burstCapacity;
    this.lastRefill = Date.now();
  }

  async canMakeRequest(): Promise<{
    allowed: boolean;
    remainingRequests: number;
    resetTime: Date;
  }> {
    const now = Date.now();
    
    // Refill tokens based on time passed
    const timePassed = now - this.lastRefill;
    const tokensToAdd = Math.floor(timePassed / this.interval * this.tokensPerInterval);
    
    if (tokensToAdd > 0) {
      this.tokens = Math.min(this.burstCapacity, this.tokens + tokensToAdd);
      this.lastRefill = now;
    }

    // Check if request can be made
    const canProceed = this.tokens > 0;

    if (canProceed) {
      this.tokens--;
    }

    // Calculate next reset time
    const nextRefillTime = this.lastRefill + this.interval;

    return {
      allowed: canProceed,
      remainingRequests: this.tokens,
      resetTime: new Date(nextRefillTime),
    };
  }
}

// ===== Main Security Middleware Class =====

export class WebSocketSecurityMiddleware extends EventEmitter {
  private config: SecurityConfig;
  private connections: Map<string, SecurityContext> = new Map();
  private connectionMetrics: Map<string, ConnectionMetrics> = new Map();
  private rateLimiters: Map<string, AdvancedRateLimiter> = new Map();
  private ipConnections: Map<string, Set<string>> = new Map();
  private userConnections: Map<string, Set<string>> = new Map();
  private blockedIPs: Set<string> = new Set();
  private blockedUsers: Set<string> = new Set();

  constructor(config: SecurityConfig) {
    super();
    this.config = config;
    this.setupSecurityMonitoring();
  }

  // ===== Authentication & Authorization =====

  /**
   * Verify JWT token and extract security context
   */
  public verifyToken(token: string): {
    valid: true;
    context: Partial<SecurityContext>;
  } | {
    valid: false;
    error: string;
  } {
    try {
      const decoded = jwt.verify(token, this.config.jwtSecret) as any;
      
      if (!decoded.sub) {
        return { valid: false, error: 'Invalid token: missing subject' };
      }

      // Extract permissions and user information
      const context: Partial<SecurityContext> = {
        userId: decoded.sub,
        permissions: decoded.permissions || ['market_data_access'],
        region: decoded.region || 'US',
        userTier: decoded.tier || 'basic',
      };

      return { valid: true, context };
      
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        return { valid: false, error: 'Token expired' };
      } else if (error instanceof jwt.JsonWebTokenError) {
        return { valid: false, error: 'Invalid token format' };
      }
      return { valid: false, error: 'Token verification failed' };
    }
  }

  /**
   * Register new WebSocket connection with security context
   */
  public async registerConnection(
    connectionId: string,
    context: SecurityContext
  ): Promise<{ success: true } | { success: false; error: string }> {
    
    // Check if user/IP is blocked
    if (this.blockedUsers.has(context.userId)) {
      return { success: false, error: 'User account is blocked' };
    }

    if (this.blockedIPs.has(context.ipAddress)) {
      return { success: false, error: 'IP address is blocked' };
    }

    // Check connection limits
    const connectionLimitResult = this.checkConnectionLimits(context);
    if (!connectionLimitResult.allowed) {
      return { success: false, error: connectionLimitResult.reason };
    }

    // Initialize connection tracking
    this.connections.set(connectionId, context);
    this.connectionMetrics.set(connectionId, {
      messageCount: 0,
      errorCount: 0,
      rateLimitViolations: 0,
      suspiciousActivityScore: 0,
    });

    // Track IP and user connections
    this.trackConnection(context, connectionId);

    // Create rate limiter for this connection
    this.rateLimiters.set(connectionId, new AdvancedRateLimiter(
      this.config.rateLimiting.tokensPerInterval,
      this.config.rateLimiting.interval,
      this.config.rateLimiting.burstCapacity
    ));

    // Audit log
    if (this.config.monitoring.enableAuditLogging) {
      await auditLogger.logAuditEvent({
        userId: context.userId,
        action: 'websocket_connection_established',
        ipAddress: context.ipAddress,
        userAgent: context.userAgent,
        metadata: {
          connectionId,
          userTier: context.userTier,
          permissions: context.permissions,
        },
      });
    }

    this.emit('connection_registered', { connectionId, context });
    return { success: true };
  }

  /**
   * Validate incoming WebSocket message
   */
  public async validateMessage(
    connectionId: string,
    rawMessage: unknown
  ): Promise<{
    valid: true;
    message: IncomingMessage;
    context: SecurityContext;
  } | {
    valid: false;
    error: string;
    shouldDisconnect?: boolean;
  }> {

    const context = this.connections.get(connectionId);
    const metrics = this.connectionMetrics.get(connectionId);
    
    if (!context || !metrics) {
      return { 
        valid: false, 
        error: 'Connection not found', 
        shouldDisconnect: true 
      };
    }

    // Update activity tracking
    context.lastActivity = new Date();
    metrics.messageCount++;

    // Check rate limiting
    const rateLimiter = this.rateLimiters.get(connectionId);
    if (rateLimiter) {
      const rateLimitResult = await rateLimiter.canMakeRequest();
      
      if (!rateLimitResult.allowed) {
        metrics.rateLimitViolations++;
        metrics.suspiciousActivityScore += 5;
        
        await this.logSecurityEvent(connectionId, 'rate_limit_exceeded', {
          remainingRequests: rateLimitResult.remainingRequests,
          resetTime: rateLimitResult.resetTime,
        });

        return {
          valid: false,
          error: 'Rate limit exceeded',
          shouldDisconnect: metrics.rateLimitViolations > 5,
        };
      }
    }

    // Validate message size
    const messageSize = JSON.stringify(rawMessage).length;
    if (messageSize > this.config.validation.maxMessageSize) {
      metrics.suspiciousActivityScore += 10;
      await this.logSecurityEvent(connectionId, 'oversized_message', {
        messageSize,
        maxAllowed: this.config.validation.maxMessageSize,
      });
      
      return {
        valid: false,
        error: 'Message too large',
        shouldDisconnect: messageSize > this.config.validation.maxMessageSize * 2,
      };
    }

    // Validate message structure using Zod
    const validationResult = validateIncomingMessage(rawMessage);
    if (!validationResult.success) {
      metrics.errorCount++;
      metrics.suspiciousActivityScore += 2;
      
      await this.logSecurityEvent(connectionId, 'malformed_request', {
        error: validationResult.error,
        details: validationResult.details,
      });
      
      return {
        valid: false,
        error: validationResult.error,
        shouldDisconnect: metrics.errorCount > 10,
      };
    }

    // Additional validation for subscription messages
    if (validationResult.data.type === 'subscribe' || validationResult.data.type === 'unsubscribe') {
      const subscriptionSecurity = validateSubscriptionSecurity(
        validationResult.data as any,
        context.permissions,
        new Set() // Current subscriptions would be tracked elsewhere
      );

      if (!subscriptionSecurity.valid) {
        await this.logSecurityEvent(connectionId, 'unauthorized_subscription', {
          reason: subscriptionSecurity.reason,
          instruments: (validationResult.data as any).instruments,
        });
        
        return {
          valid: false,
          error: subscriptionSecurity.reason,
        };
      }

      // Validate instrument access
      const instrumentAccess = validateInstrumentAccess(
        (validationResult.data as any).instruments,
        context.region,
        context.permissions
      );

      if (!instrumentAccess.valid) {
        await this.logSecurityEvent(connectionId, 'restricted_instrument_access', {
          reason: instrumentAccess.reason,
          restrictedInstruments: instrumentAccess.restrictedInstruments,
        });
        
        return {
          valid: false,
          error: instrumentAccess.reason,
        };
      }
    }

    return {
      valid: true,
      message: validationResult.data,
      context,
    };
  }

  /**
   * Handle connection cleanup
   */
  public async disconnectConnection(
    connectionId: string,
    reason: string = 'normal_closure'
  ): Promise<void> {
    const context = this.connections.get(connectionId);
    const metrics = this.connectionMetrics.get(connectionId);

    if (context) {
      // Update tracking maps
      const userConnections = this.userConnections.get(context.userId);
      if (userConnections) {
        userConnections.delete(connectionId);
        if (userConnections.size === 0) {
          this.userConnections.delete(context.userId);
        }
      }

      const ipConnections = this.ipConnections.get(context.ipAddress);
      if (ipConnections) {
        ipConnections.delete(connectionId);
        if (ipConnections.size === 0) {
          this.ipConnections.delete(context.ipAddress);
        }
      }

      // Audit log
      if (this.config.monitoring.enableAuditLogging) {
        await auditLogger.logAuditEvent({
          userId: context.userId,
          action: 'websocket_connection_closed',
          ipAddress: context.ipAddress,
          metadata: {
            connectionId,
            reason,
            duration: Date.now() - context.connectionTime.getTime(),
            messageCount: metrics?.messageCount || 0,
            errorCount: metrics?.errorCount || 0,
          },
        });
      }
    }

    // Cleanup
    this.connections.delete(connectionId);
    this.connectionMetrics.delete(connectionId);
    this.rateLimiters.delete(connectionId);

    this.emit('connection_disconnected', { connectionId, reason, context });
  }

  // ===== Security Monitoring =====

  /**
   * Get security statistics for monitoring
   */
  public getSecurityStats(): {
    totalConnections: number;
    connectionsByUser: Record<string, number>;
    connectionsByIP: Record<string, number>;
    blockedUsers: number;
    blockedIPs: number;
    suspiciousConnections: Array<{
      connectionId: string;
      userId: string;
      suspiciousActivityScore: number;
      violations: {
        rateLimitViolations: number;
        errorCount: number;
      };
    }>;
  } {
    const connectionsByUser: Record<string, number> = {};
    const connectionsByIP: Record<string, number> = {};
    const suspiciousConnections: any[] = [];

    this.userConnections.forEach((connections, userId) => {
      connectionsByUser[userId] = connections.size;
    });

    this.ipConnections.forEach((connections, ip) => {
      connectionsByIP[ip] = connections.size;
    });

    this.connectionMetrics.forEach((metrics, connectionId) => {
      if (metrics.suspiciousActivityScore > this.config.monitoring.suspiciousActivityThreshold) {
        const context = this.connections.get(connectionId);
        if (context) {
          suspiciousConnections.push({
            connectionId,
            userId: context.userId,
            suspiciousActivityScore: metrics.suspiciousActivityScore,
            violations: {
              rateLimitViolations: metrics.rateLimitViolations,
              errorCount: metrics.errorCount,
            },
          });
        }
      }
    });

    return {
      totalConnections: this.connections.size,
      connectionsByUser,
      connectionsByIP,
      blockedUsers: this.blockedUsers.size,
      blockedIPs: this.blockedIPs.size,
      suspiciousConnections,
    };
  }

  /**
   * Block user or IP address
   */
  public blockUser(userId: string, reason: string): void {
    this.blockedUsers.add(userId);
    this.emit('user_blocked', { userId, reason });
    
    // Disconnect all connections for this user
    const userConnections = this.userConnections.get(userId);
    if (userConnections) {
      userConnections.forEach(connectionId => {
        this.emit('force_disconnect', { connectionId, reason: 'user_blocked' });
      });
    }
  }

  public blockIP(ipAddress: string, reason: string): void {
    this.blockedIPs.add(ipAddress);
    this.emit('ip_blocked', { ipAddress, reason });
    
    // Disconnect all connections from this IP
    const ipConnections = this.ipConnections.get(ipAddress);
    if (ipConnections) {
      ipConnections.forEach(connectionId => {
        this.emit('force_disconnect', { connectionId, reason: 'ip_blocked' });
      });
    }
  }

  public unblockUser(userId: string): void {
    this.blockedUsers.delete(userId);
    this.emit('user_unblocked', { userId });
  }

  public unblockIP(ipAddress: string): void {
    this.blockedIPs.delete(ipAddress);
    this.emit('ip_unblocked', { ipAddress });
  }

  // ===== Private Helper Methods =====

  private checkConnectionLimits(context: SecurityContext): {
    allowed: boolean;
    reason?: string;
  } {
    // Check global connection limit
    if (this.connections.size >= this.config.connectionLimits.maxGlobalConnections) {
      return { allowed: false, reason: 'Global connection limit exceeded' };
    }

    // Check per-user limit
    const userConnections = this.userConnections.get(context.userId);
    if (userConnections && userConnections.size >= this.config.connectionLimits.maxConnectionsPerUser) {
      return { allowed: false, reason: 'User connection limit exceeded' };
    }

    // Check per-IP limit
    const ipConnections = this.ipConnections.get(context.ipAddress);
    if (ipConnections && ipConnections.size >= this.config.connectionLimits.maxConnectionsPerIP) {
      return { allowed: false, reason: 'IP connection limit exceeded' };
    }

    return { allowed: true };
  }

  private trackConnection(context: SecurityContext, connectionId: string): void {
    // Track user connections
    if (!this.userConnections.has(context.userId)) {
      this.userConnections.set(context.userId, new Set());
    }
    this.userConnections.get(context.userId)!.add(connectionId);

    // Track IP connections
    if (!this.ipConnections.has(context.ipAddress)) {
      this.ipConnections.set(context.ipAddress, new Set());
    }
    this.ipConnections.get(context.ipAddress)!.add(connectionId);
  }

  private async logSecurityEvent(
    connectionId: string,
    eventType: string,
    details: any
  ): Promise<void> {
    const context = this.connections.get(connectionId);
    
    if (this.config.monitoring.enableAuditLogging && context) {
      await auditLogger.logSecurityEvent(eventType, {
        ip: context.ipAddress,
        method: 'WebSocket',
        originalUrl: '/prices',
      }, {
        connectionId,
        userId: context.userId,
        userAgent: context.userAgent,
        ...details,
      });
    }

    if (this.config.monitoring.enableSecurityAlerts) {
      const alert: SecurityAlert = {
        type: 'security_alert',
        alertType: eventType as any,
        clientId: connectionId,
        timestamp: new Date(),
        details,
      };

      this.emit('security_alert', alert);
    }
  }

  private setupSecurityMonitoring(): void {
    // Periodic cleanup of expired rate limiters
    setInterval(() => {
      this.cleanupExpiredConnections();
    }, 60000); // Every minute

    // Monitor suspicious activity
    setInterval(() => {
      this.monitorSuspiciousActivity();
    }, 30000); // Every 30 seconds
  }

  private cleanupExpiredConnections(): void {
    const now = Date.now();
    const timeoutThreshold = 5 * 60 * 1000; // 5 minutes

    this.connections.forEach((context, connectionId) => {
      if (now - context.lastActivity.getTime() > timeoutThreshold) {
        this.emit('force_disconnect', { 
          connectionId, 
          reason: 'inactivity_timeout' 
        });
      }
    });
  }

  private monitorSuspiciousActivity(): void {
    this.connectionMetrics.forEach((metrics, connectionId) => {
      if (metrics.suspiciousActivityScore > this.config.monitoring.suspiciousActivityThreshold) {
        const context = this.connections.get(connectionId);
        if (context) {
          this.emit('suspicious_activity_detected', {
            connectionId,
            userId: context.userId,
            score: metrics.suspiciousActivityScore,
            metrics,
          });
        }
      }
    });
  }

  /**
   * Manually trigger suspicious activity monitoring (for testing)
   */
  public checkForSuspiciousActivity(): void {
    this.monitorSuspiciousActivity();
  }
}