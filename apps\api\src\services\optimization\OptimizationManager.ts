/**
 * Optimization Manager
 * 
 * @fileoverview Main orchestration layer for strategy parameter optimization
 * Coordinates genetic algorithm, parameter analysis, walk-forward testing, and performance calculation
 */

import { randomUUID } from 'crypto';
import type {
  GeneticAlgorithmConfig,
  WalkForwardConfig,
  FitnessFunction,
  OptimizationResult,
  OptimizationProgress,
  OptimizationStatus,
  StrategyParameters,
  PerformanceMetrics,
  ParameterDefinition,
  StrategyType,
  MarketRegime,
  OptimizationError,
  OptimizationErrorCode
} from '@golddaddy/types/optimization';

import { GeneticAlgorithm } from './GeneticAlgorithm.js';
import { ParameterRangeAnalyzer } from './ParameterRangeAnalyzer.js';
import { WalkForwardEngine } from './WalkForwardEngine.js';
import { PerformanceCalculator } from './PerformanceCalculator.js';

// ===== Strategy Interface =====

interface Strategy {
  id: string;
  name: string;
  type: StrategyType;
  instruments: string[];
  marketRegime: MarketRegime;
  currentParameters: StrategyParameters;
}

// ===== Optimization Manager Class =====

export class OptimizationManager {
  private activeOptimizations: Map<string, {
    algorithm: GeneticAlgorithm;
    status: OptimizationStatus;
    startTime: Date;
    config: GeneticAlgorithmConfig;
  }> = new Map();

  private parameterAnalyzer: ParameterRangeAnalyzer;
  private walkForwardEngine: WalkForwardEngine;
  private performanceCalculator: PerformanceCalculator;

  constructor() {
    this.parameterAnalyzer = new ParameterRangeAnalyzer();
    this.walkForwardEngine = new WalkForwardEngine({
      trainingWindowMonths: 12,
      testingWindowMonths: 3,
      stepSizeMonths: 1,
      minTrainingPeriods: 5,
      outOfSampleRatio: 0.3,
      crossValidationFolds: 5,
      parameterStabilityThreshold: 0.1,
      performanceStabilityThreshold: 0.05
    });
    this.performanceCalculator = new PerformanceCalculator();
  }

  // ===== Main Optimization Interface =====

  /**
   * Start a new optimization process
   */
  public async startOptimization(
    strategy: Strategy,
    config: Partial<GeneticAlgorithmConfig> = {},
    walkForwardConfig?: Partial<WalkForwardConfig>,
    fitnessFunction?: Partial<FitnessFunction>,
    onProgress?: (progress: OptimizationProgress) => void
  ): Promise<{
    optimizationId: string;
    estimatedTimeMinutes: number;
    parameterDefinitions: ParameterDefinition[];
  }> {

    const optimizationId = randomUUID();

    try {
      // Analyze parameter ranges for the strategy
      const parameterDefinitions = this.parameterAnalyzer.analyzeParameterRanges(
        strategy.type,
        strategy.marketRegime,
        strategy.instruments
      );

      // Validate current parameters
      const validation = this.parameterAnalyzer.validateParameters(
        strategy.currentParameters,
        parameterDefinitions
      );

      if (!validation.isValid) {
        throw this.createOptimizationError(
          'PARAMETER_VALIDATION_FAILED',
          'Current strategy parameters are invalid',
          { violations: validation.violations }
        );
      }

      // Create genetic algorithm configuration
      const fullConfig = this.createGeneticAlgorithmConfig(config, parameterDefinitions.length);

      // Create fitness function
      const fullFitnessFunction = this.createFitnessFunction(fitnessFunction);

      // Estimate optimization time
      const estimatedTimeMinutes = this.estimateOptimizationTime(fullConfig, parameterDefinitions);

      // Create genetic algorithm instance
      const geneticAlgorithm = new GeneticAlgorithm(
        fullConfig,
        parameterDefinitions,
        fullFitnessFunction
      );

      // Store optimization state
      this.activeOptimizations.set(optimizationId, {
        algorithm: geneticAlgorithm,
        status: 'pending',
        startTime: new Date(),
        config: fullConfig
      });

      // Start optimization in background
      this.executeOptimization(
        optimizationId,
        strategy,
        geneticAlgorithm,
        parameterDefinitions,
        walkForwardConfig,
        onProgress
      ).catch(error => {
        console.error(`Optimization ${optimizationId} failed:`, error);
        const optimization = this.activeOptimizations.get(optimizationId);
        if (optimization) {
          optimization.status = 'failed';
        }
      });

      return {
        optimizationId,
        estimatedTimeMinutes,
        parameterDefinitions
      };

    } catch (error) {
      throw error instanceof Error 
        ? this.createOptimizationError('SYSTEM_ERROR', error.message)
        : this.createOptimizationError('SYSTEM_ERROR', 'Unknown error occurred');
    }
  }

  /**
   * Get optimization progress
   */
  public getOptimizationProgress(optimizationId: string): OptimizationProgress | null {
    const optimization = this.activeOptimizations.get(optimizationId);
    if (!optimization) return null;

    return optimization.algorithm.getProgress();
  }

  /**
   * Cancel an active optimization
   */
  public async cancelOptimization(optimizationId: string): Promise<boolean> {
    const optimization = this.activeOptimizations.get(optimizationId);
    if (!optimization) return false;

    optimization.algorithm.cancel();
    optimization.status = 'cancelled';

    // Cleanup resources
    setTimeout(() => {
      this.activeOptimizations.delete(optimizationId);
    }, 5000); // Keep for 5 seconds to allow final status check

    return true;
  }

  /**
   * Get optimization status
   */
  public getOptimizationStatus(optimizationId: string): OptimizationStatus | null {
    const optimization = this.activeOptimizations.get(optimizationId);
    return optimization?.status || null;
  }

  // ===== Private Optimization Execution =====

  /**
   * Execute the optimization process
   */
  private async executeOptimization(
    optimizationId: string,
    strategy: Strategy,
    geneticAlgorithm: GeneticAlgorithm,
    parameterDefinitions: ParameterDefinition[],
    walkForwardConfig?: Partial<WalkForwardConfig>,
    onProgress?: (progress: OptimizationProgress) => void
  ): Promise<void> {

    const optimization = this.activeOptimizations.get(optimizationId);
    if (!optimization) return;

    try {
      optimization.status = 'running';

      // Create performance evaluation function
      const evaluatePerformance = this.createPerformanceEvaluator(strategy);

      // Execute genetic algorithm optimization
      const bestIndividual = await geneticAlgorithm.optimize(
        evaluatePerformance,
        onProgress
      );

      // Execute walk-forward analysis if requested
      let walkForwardResults;
      if (walkForwardConfig) {
        const wfEngine = new WalkForwardEngine({
          ...this.walkForwardEngine['config'], // Access private config
          ...walkForwardConfig
        });

        walkForwardResults = await wfEngine.executeWalkForwardAnalysis(
          strategy.instruments,
          async (params, startDate, endDate) => {
            const performance = await evaluatePerformance(params);
            return {
              parameters: params,
              performance,
              tradeLog: [] // Would be populated by actual backtesting
            };
          },
          async (trainingData, startDate, endDate) => {
            // Quick optimization for walk-forward (smaller population)
            const quickConfig = {
              ...optimization.config,
              populationSize: Math.min(30, optimization.config.populationSize),
              maxGenerations: Math.min(100, optimization.config.maxGenerations)
            };

            const quickGA = new GeneticAlgorithm(
              quickConfig,
              parameterDefinitions,
              geneticAlgorithm['fitnessFunction'] // Access private fitness function
            );

            const result = await quickGA.optimize(evaluatePerformance);
            return result.parameters;
          }
        );
      }

      // Create optimization result
      const result: OptimizationResult = {
        id: optimizationId,
        strategyId: strategy.id,
        userId: 'user-id', // Would come from authentication context
        
        config: optimization.config,
        walkForwardConfig: walkForwardConfig ? {
          ...this.walkForwardEngine['config'],
          ...walkForwardConfig
        } : undefined,
        
        bestParameters: bestIndividual.parameters,
        bestPerformance: await evaluatePerformance(bestIndividual.parameters),
        finalPopulation: geneticAlgorithm['currentPopulation']!, // Access private population
        
        walkForwardResults: walkForwardResults?.periods,
        parameterSensitivity: await this.calculateParameterSensitivity(
          bestIndividual.parameters,
          parameterDefinitions,
          evaluatePerformance
        ),
        robustnessScore: walkForwardResults?.overallStability || 0,
        confidenceIntervals: this.calculateConfidenceIntervals(
          bestIndividual.parameters,
          parameterDefinitions
        ),
        
        status: 'completed',
        startTime: optimization.startTime,
        endTime: new Date(),
        executionTimeSeconds: Math.floor((Date.now() - optimization.startTime.getTime()) / 1000),
        convergenceGeneration: geneticAlgorithm['currentPopulation']?.generation,
        
        overfittingDetected: walkForwardResults ? walkForwardResults.overfittingScore > 0.3 : false,
        statisticalSignificance: 0.05, // Would be calculated from actual statistics
        outOfSampleSharpe: walkForwardResults?.periods?.[0]?.outOfSamplePerformance.sharpeRatio || 0,
        
        version: '1.0',
        previousResultId: undefined
      };

      // Store result (in real implementation, this would go to database)
      optimization.status = 'completed';

      // Final progress update
      if (onProgress) {
        const finalProgress = geneticAlgorithm.getProgress();
        finalProgress.status = 'completed';
        onProgress(finalProgress);
      }

    } catch (error) {
      optimization.status = 'failed';
      console.error(`Optimization ${optimizationId} execution failed:`, error);
      
      if (onProgress) {
        const errorProgress = geneticAlgorithm.getProgress();
        errorProgress.status = 'failed';
        onProgress(errorProgress);
      }
    }
  }

  // ===== Configuration Helpers =====

  /**
   * Create genetic algorithm configuration with defaults
   */
  private createGeneticAlgorithmConfig(
    config: Partial<GeneticAlgorithmConfig>,
    parameterCount: number
  ): GeneticAlgorithmConfig {
    
    // Adaptive population size based on parameter count
    const basePopulationSize = Math.max(50, Math.min(100, parameterCount * 5));
    
    return {
      populationSize: config.populationSize || basePopulationSize,
      maxGenerations: config.maxGenerations || 500,
      convergenceThreshold: config.convergenceThreshold || 0.001,
      convergenceGenerations: config.convergenceGenerations || 10,
      
      selectionMethod: config.selectionMethod || 'tournament',
      tournamentSize: config.tournamentSize || 3,
      
      crossoverRate: config.crossoverRate || 0.8,
      crossoverMethod: config.crossoverMethod || 'uniform',
      
      mutationRate: config.mutationRate || 0.1,
      mutationStrength: config.mutationStrength || 0.1,
      adaptiveMutation: config.adaptiveMutation ?? true,
      
      elitismRate: config.elitismRate || 0.1,
      replacementRate: config.replacementRate || 0.2,
      diversityThreshold: config.diversityThreshold || 0.1,
      
      timeoutMinutes: config.timeoutMinutes || 15,
      stallGenerations: config.stallGenerations || 20
    };
  }

  /**
   * Create fitness function with defaults
   */
  private createFitnessFunction(fitnessFunction?: Partial<FitnessFunction>): FitnessFunction {
    return {
      weights: {
        sharpeRatio: fitnessFunction?.weights?.sharpeRatio || 0.4,
        profitFactor: fitnessFunction?.weights?.profitFactor || 0.3,
        maxDrawdownPenalty: fitnessFunction?.weights?.maxDrawdownPenalty || 0.3
      },
      penalties: {
        maxDrawdownThreshold: fitnessFunction?.penalties?.maxDrawdownThreshold || 0.2,
        minTradeCount: fitnessFunction?.penalties?.minTradeCount || 10,
        minWinRate: fitnessFunction?.penalties?.minWinRate || 0.3
      }
    };
  }

  /**
   * Create performance evaluation function
   */
  private createPerformanceEvaluator(strategy: Strategy) {
    return async (parameters: StrategyParameters): Promise<PerformanceMetrics> => {
      // This would integrate with the actual backtesting engine
      // For now, we'll generate realistic mock performance based on parameters
      
      const riskPerTrade = (parameters.riskPerTrade as number) || 2;
      const stopLoss = (parameters.stopLoss as number) || 30;
      const takeProfit = (parameters.takeProfit as number) || 60;
      
      // Mock performance calculation based on risk/reward ratio
      const riskRewardRatio = takeProfit / stopLoss;
      const baseWinRate = Math.min(0.7, 0.3 + (riskRewardRatio * 0.1));
      const winRate = baseWinRate + (Math.random() - 0.5) * 0.2;
      
      const avgWin = takeProfit * 0.8; // Account for slippage
      const avgLoss = stopLoss * 0.8;
      
      const profitFactor = (winRate * avgWin) / ((1 - winRate) * avgLoss);
      const totalReturn = (winRate * avgWin - (1 - winRate) * avgLoss) * 0.1; // Simulate 10 trades
      
      // Risk-adjusted metrics
      const volatility = Math.sqrt(riskPerTrade / 100) * 0.2;
      const sharpeRatio = volatility > 0 ? (totalReturn * 0.01) / volatility : 0;
      
      const maxDrawdown = Math.min(0.5, (1 - winRate) * avgLoss * 0.002);
      const calmarRatio = maxDrawdown > 0 ? (totalReturn * 0.01) / maxDrawdown : 0;
      
      return {
        totalReturn,
        sharpeRatio,
        profitFactor,
        winRate: winRate * 100,
        maxDrawdown: maxDrawdown * 100,
        tradeCount: 10,
        averageTradeReturn: totalReturn / 10,
        volatility: volatility * 100,
        calmarRatio,
        sortinoRatio: sharpeRatio * 1.2 // Approximate
      };
    };
  }

  // ===== Analysis Methods =====

  /**
   * Calculate parameter sensitivity analysis
   */
  private async calculateParameterSensitivity(
    baseParameters: StrategyParameters,
    parameterDefinitions: ParameterDefinition[],
    evaluatePerformance: (params: StrategyParameters) => Promise<PerformanceMetrics>
  ): Promise<Record<string, number>> {
    
    const sensitivity: Record<string, number> = {};
    const basePerformance = await evaluatePerformance(baseParameters);
    
    for (const paramDef of parameterDefinitions) {
      const paramName = paramDef.name;
      const baseValue = baseParameters[paramName] as number;
      
      if (typeof baseValue === 'number') {
        // Test +/- 10% variation
        const range = paramDef.constraint.max - paramDef.constraint.min;
        const variation = Math.min(range * 0.1, Math.abs(baseValue * 0.1));
        
        const upValue = Math.min(paramDef.constraint.max, baseValue + variation);
        const downValue = Math.max(paramDef.constraint.min, baseValue - variation);
        
        const upParams = { ...baseParameters, [paramName]: upValue };
        const downParams = { ...baseParameters, [paramName]: downValue };
        
        try {
          const upPerformance = await evaluatePerformance(upParams);
          const downPerformance = await evaluatePerformance(downParams);
          
          // Calculate sensitivity as change in Sharpe ratio per unit parameter change
          const upChange = upPerformance.sharpeRatio - basePerformance.sharpeRatio;
          const downChange = basePerformance.sharpeRatio - downPerformance.sharpeRatio;
          const avgChange = (Math.abs(upChange) + Math.abs(downChange)) / 2;
          const paramChange = variation / Math.abs(baseValue);
          
          sensitivity[paramName] = paramChange > 0 ? avgChange / paramChange : 0;
          
        } catch (error) {
          sensitivity[paramName] = 0;
        }
      }
    }
    
    return sensitivity;
  }

  /**
   * Calculate confidence intervals for parameters
   */
  private calculateConfidenceIntervals(
    parameters: StrategyParameters,
    parameterDefinitions: ParameterDefinition[]
  ): Record<string, { lower: number; upper: number }> {
    
    const intervals: Record<string, { lower: number; upper: number }> = {};
    
    for (const paramDef of parameterDefinitions) {
      const paramName = paramDef.name;
      const value = parameters[paramName] as number;
      
      if (typeof value === 'number') {
        // Simple confidence interval based on parameter range
        const range = paramDef.constraint.max - paramDef.constraint.min;
        const margin = range * 0.05; // 5% of range
        
        intervals[paramName] = {
          lower: Math.max(paramDef.constraint.min, value - margin),
          upper: Math.min(paramDef.constraint.max, value + margin)
        };
      }
    }
    
    return intervals;
  }

  /**
   * Estimate optimization time
   */
  private estimateOptimizationTime(
    config: GeneticAlgorithmConfig,
    parameterDefinitions: ParameterDefinition[]
  ): number {
    
    // Base estimation formula
    const baseTimePerGeneration = 0.5; // seconds
    const populationFactor = config.populationSize / 50;
    const parameterFactor = parameterDefinitions.length / 10;
    
    const estimatedGenerations = Math.min(config.maxGenerations, 200); // Assume early convergence
    const totalSeconds = estimatedGenerations * baseTimePerGeneration * populationFactor * parameterFactor;
    
    return Math.ceil(totalSeconds / 60); // Convert to minutes
  }

  // ===== Error Handling =====

  /**
   * Create optimization error
   */
  private createOptimizationError(
    code: OptimizationErrorCode,
    message: string,
    details?: Record<string, any>
  ): OptimizationError {
    return {
      code,
      message,
      details,
      recoverable: code !== 'SYSTEM_ERROR'
    };
  }

  // ===== Cleanup Methods =====

  /**
   * Cleanup completed optimizations
   */
  public cleanupCompletedOptimizations(): void {
    const now = Date.now();
    const maxAge = 60 * 60 * 1000; // 1 hour
    
    for (const [id, optimization] of this.activeOptimizations.entries()) {
      if (optimization.status === 'completed' || optimization.status === 'failed') {
        const age = now - optimization.startTime.getTime();
        if (age > maxAge) {
          this.activeOptimizations.delete(id);
        }
      }
    }
  }

  /**
   * Get active optimization count
   */
  public getActiveOptimizationCount(): number {
    let count = 0;
    for (const optimization of this.activeOptimizations.values()) {
      if (optimization.status === 'running' || optimization.status === 'pending') {
        count++;
      }
    }
    return count;
  }
}