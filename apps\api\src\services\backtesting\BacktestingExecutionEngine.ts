// import { Decimal } from '@prisma/client/runtime/library';
import { MarketRegime, TradeType } from '@prisma/client';
import { HistoricalDataPoint } from './BacktestingDataService';

/**
 * Trade order for backtesting execution
 */
export interface BacktestTradeOrder {
  id: string;
  type: TradeType;
  instrument: string;
  quantity: number;
  requestedPrice: number;
  stopLoss?: number;
  takeProfit?: number;
  timestamp: Date;
  marketConditions?: {
    regime: MarketRegime;
    volatility: number;
    spread: number;
    liquidityScore: number;
  };
}

/**
 * Execution result for a backtested trade
 */
export interface BacktestExecutionResult {
  orderId: string;
  executedPrice: number;
  executedQuantity: number;
  executionTime: Date;
  slippage: number;
  spread: number;
  commission: number;
  marketImpact: number;
  executionCost: number;
  success: boolean;
  failureReason?: string;
}

/**
 * Execution configuration parameters
 */
export interface ExecutionConfig {
  // Spread modeling
  baseSpread: number; // Base spread in pips/points
  volatilitySpreadMultiplier: number; // How volatility affects spread
  
  // Slippage modeling
  baseSlippage: number; // Base slippage in percentage
  volumeSlippageMultiplier: number; // How order size affects slippage
  volatilitySlippageMultiplier: number; // How volatility affects slippage
  
  // Market impact
  marketImpactFactor: number; // Market impact based on order size
  liquidityAdjustment: boolean; // Adjust execution based on market hours
  
  // Commission structure
  commissionPerLot: number;
  minimumCommission: number;
  
  // Execution timing
  maxExecutionDelay: number; // Maximum delay in milliseconds
  executionFailureRate: number; // Percentage of orders that fail
}

/**
 * Default execution configuration for realistic backtesting
 */
export const DEFAULT_EXECUTION_CONFIG: ExecutionConfig = {
  baseSpread: 0.8, // 0.8 pips average
  volatilitySpreadMultiplier: 2.0,
  baseSlippage: 0.05, // 0.05% base slippage
  volumeSlippageMultiplier: 0.1,
  volatilitySlippageMultiplier: 1.5,
  marketImpactFactor: 0.02,
  liquidityAdjustment: true,
  commissionPerLot: 7.0, // $7 per standard lot
  minimumCommission: 1.0,
  maxExecutionDelay: 500,
  executionFailureRate: 0.01, // 1% failure rate
};

/**
 * Realistic execution engine for backtesting with spreads, slippage, and market impact
 */
export class BacktestingExecutionEngine {
  private readonly config: ExecutionConfig;

  constructor(config: ExecutionConfig = DEFAULT_EXECUTION_CONFIG) {
    this.config = config;
  }

  /**
   * Execute a trade order with realistic execution modeling
   */
  async executeOrder(
    order: BacktestTradeOrder,
    marketData: HistoricalDataPoint
  ): Promise<BacktestExecutionResult> {
    try {
      // Check for execution failure
      if (this.shouldExecutionFail()) {
        return {
          orderId: order.id,
          executedPrice: 0,
          executedQuantity: 0,
          executionTime: order.timestamp,
          slippage: 0,
          spread: 0,
          commission: 0,
          marketImpact: 0,
          executionCost: 0,
          success: false,
          failureReason: 'Network timeout or broker rejection',
        };
      }

      // Calculate spread
      const spread = this.calculateSpread(marketData, order.marketConditions);
      
      // Calculate slippage
      const slippage = this.calculateSlippage(order, marketData, order.marketConditions);
      
      // Calculate market impact
      const marketImpact = this.calculateMarketImpact(order, marketData);
      
      // Determine execution price
      const executionPrice = this.calculateExecutionPrice(
        order,
        marketData,
        spread,
        slippage,
        marketImpact
      );
      
      // Calculate commission
      const commission = this.calculateCommission(order.quantity);
      
      // Calculate total execution cost
      const executionCost = commission + (spread * order.quantity) + (Math.abs(slippage) * order.quantity);
      
      // Add execution delay
      const executionTime = new Date(order.timestamp.getTime() + this.getExecutionDelay());

      return {
        orderId: order.id,
        executedPrice: executionPrice,
        executedQuantity: order.quantity,
        executionTime,
        slippage,
        spread,
        commission,
        marketImpact,
        executionCost,
        success: true,
      };
    } catch (error) {
      return {
        orderId: order.id,
        executedPrice: 0,
        executedQuantity: 0,
        executionTime: order.timestamp,
        slippage: 0,
        spread: 0,
        commission: 0,
        marketImpact: 0,
        executionCost: 0,
        success: false,
        failureReason: `Execution error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Execute multiple orders in batch
   */
  async executeBatch(
    orders: BacktestTradeOrder[],
    marketDataMap: Map<string, HistoricalDataPoint>
  ): Promise<BacktestExecutionResult[]> {
    const results: BacktestExecutionResult[] = [];
    
    for (const order of orders) {
      const marketData = marketDataMap.get(this.getMarketDataKey(order.timestamp, order.instrument));
      
      if (!marketData) {
        results.push({
          orderId: order.id,
          executedPrice: 0,
          executedQuantity: 0,
          executionTime: order.timestamp,
          slippage: 0,
          spread: 0,
          commission: 0,
          marketImpact: 0,
          executionCost: 0,
          success: false,
          failureReason: 'Market data not available',
        });
        continue;
      }
      
      const result = await this.executeOrder(order, marketData);
      results.push(result);
    }
    
    return results;
  }

  /**
   * Calculate dynamic spread based on market conditions
   */
  private calculateSpread(
    marketData: HistoricalDataPoint,
    marketConditions?: BacktestTradeOrder['marketConditions']
  ): number {
    let spread = this.config.baseSpread;
    
    // Adjust for volatility
    if (marketConditions?.volatility) {
      spread *= (1 + marketConditions.volatility * this.config.volatilitySpreadMultiplier);
    }
    
    // Adjust for market regime
    if (marketConditions?.regime === MarketRegime.VOLATILE) {
      spread *= 1.5;
    } else if (marketConditions?.regime === MarketRegime.RANGING) {
      spread *= 0.8;
    }
    
    // Adjust for liquidity during market hours
    if (this.config.liquidityAdjustment) {
      const liquidityMultiplier = this.getLiquidityMultiplier(marketData.timestamp);
      spread *= liquidityMultiplier;
    }
    
    return spread;
  }

  /**
   * Calculate slippage based on order size and market conditions
   */
  private calculateSlippage(
    order: BacktestTradeOrder,
    marketData: HistoricalDataPoint,
    marketConditions?: BacktestTradeOrder['marketConditions']
  ): number {
    let slippage = this.config.baseSlippage;
    
    // Volume-based slippage
    const volumeMultiplier = 1 + (Math.abs(order.quantity) * this.config.volumeSlippageMultiplier);
    slippage *= volumeMultiplier;
    
    // Volatility-based slippage
    if (marketConditions?.volatility) {
      slippage *= (1 + marketConditions.volatility * this.config.volatilitySlippageMultiplier);
    }
    
    // Market regime adjustment
    if (marketConditions?.regime === MarketRegime.VOLATILE) {
      slippage *= 2.0;
    } else if (marketConditions?.regime === MarketRegime.RANGING) {
      slippage *= 0.5;
    }
    
    // Random component for realism
    const randomFactor = 0.8 + (Math.random() * 0.4); // 80% to 120%
    slippage *= randomFactor;
    
    // Apply direction (negative slippage for favorable execution)
    const direction = Math.random() < 0.7 ? 1 : -1; // 70% unfavorable, 30% favorable
    
    return slippage * direction * marketData.close;
  }

  /**
   * Calculate market impact based on order size
   */
  private calculateMarketImpact(order: BacktestTradeOrder, marketData: HistoricalDataPoint): number {
    // Market impact is typically small for retail orders
    const orderSizeRatio = Math.abs(order.quantity) / 1000; // Assume 1000 is standard size
    const impact = orderSizeRatio * this.config.marketImpactFactor * marketData.close;
    
    // Impact direction depends on trade type
    const impactDirection = order.type === TradeType.BUY ? 1 : -1;
    
    return impact * impactDirection;
  }

  /**
   * Calculate final execution price including all costs
   */
  private calculateExecutionPrice(
    order: BacktestTradeOrder,
    marketData: HistoricalDataPoint,
    spread: number,
    slippage: number,
    marketImpact: number
  ): number {
    let basePrice: number;
    
    // Start with appropriate market price
    if (order.type === TradeType.BUY) {
      basePrice = marketData.close + (spread / 2); // Use ask price
    } else {
      basePrice = marketData.close - (spread / 2); // Use bid price
    }
    
    // Apply slippage and market impact
    const totalAdjustment = slippage + marketImpact;
    
    return Math.max(0, basePrice + totalAdjustment);
  }

  /**
   * Calculate commission based on order size
   */
  private calculateCommission(quantity: number): number {
    const standardLots = Math.abs(quantity) / 100000; // Convert to standard lots
    const commission = standardLots * this.config.commissionPerLot;
    
    return Math.max(commission, this.config.minimumCommission);
  }

  /**
   * Get execution delay in milliseconds
   */
  private getExecutionDelay(): number {
    // Random delay between 10ms and maxExecutionDelay
    return Math.floor(Math.random() * (this.config.maxExecutionDelay - 10) + 10);
  }

  /**
   * Check if execution should fail
   */
  private shouldExecutionFail(): boolean {
    return Math.random() < this.config.executionFailureRate;
  }

  /**
   * Get liquidity multiplier based on market hours
   */
  private getLiquidityMultiplier(timestamp: Date): number {
    const hour = timestamp.getUTCHours();
    
    // Major trading sessions (simplified)
    // Sydney: 22:00-07:00 UTC
    // Tokyo: 00:00-09:00 UTC  
    // London: 08:00-17:00 UTC
    // New York: 13:00-22:00 UTC
    
    // High liquidity hours (London-NY overlap)
    if (hour >= 13 && hour <= 17) {
      return 0.8; // Lower spread during high liquidity
    }
    
    // Medium liquidity hours
    if ((hour >= 8 && hour <= 12) || (hour >= 18 && hour <= 21)) {
      return 1.0; // Normal spread
    }
    
    // Low liquidity hours
    return 1.3; // Higher spread during low liquidity
  }

  /**
   * Generate market data key for timestamp and instrument
   */
  private getMarketDataKey(timestamp: Date, instrument: string): string {
    return `${instrument}_${timestamp.getTime()}`;
  }

  /**
   * Update execution configuration
   */
  updateConfig(newConfig: Partial<ExecutionConfig>): void {
    Object.assign(this.config, newConfig);
  }

  /**
   * Get current execution configuration
   */
  getConfig(): ExecutionConfig {
    return { ...this.config };
  }
}