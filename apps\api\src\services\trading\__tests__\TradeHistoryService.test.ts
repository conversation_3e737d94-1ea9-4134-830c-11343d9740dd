/**
 * Trade History Service Tests
 * 
 * Comprehensive unit tests for trade history logging, performance attribution,
 * trade categorization, and historical reporting with financial precision.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PrismaClient } from '@prisma/client';
import Decimal from 'decimal.js';
import { TradeHistoryService, type TradeCategory, type TradePerformanceMetrics } from '../TradeHistoryService.js';
import { AuditTrailService } from '../../compliance/AuditTrailService.js';
import type { TradeEventType, PerformanceAttribution } from '@golddaddy/types';

// Mock Prisma Client
const mockPrisma = {
  tradeHistoryRecord: {
    create: vi.fn(),
    findMany: vi.fn()
  }
} as unknown as PrismaClient;

// Mock AuditTrailService
const mockAuditService = {
  logEvent: vi.fn()
} as unknown as AuditTrailService;

describe('TradeHistoryService', () => {
  let historyService: TradeHistoryService;
  let mockTradePerformanceData: Partial<TradePerformanceMetrics>;

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // Initialize trade history service with test configuration
    historyService = new TradeHistoryService(
      mockPrisma,
      mockAuditService,
      {
        enableDetailedLogging: true,
        enablePerformanceAttribution: true,
        enableMarketDataCapture: true,
        retentionDays: 30, // Short retention for testing
        batchSize: 10,
        compressionEnabled: true,
        realtimeAnalysis: true
      }
    );

    // Mock trade performance data
    mockTradePerformanceData = {
      tradeId: 'trade_123',
      instrument: 'EURUSD',
      strategy: 'scalping_v2',
      entryTime: new Date('2024-01-01T10:00:00Z'),
      exitTime: new Date('2024-01-01T10:05:00Z'),
      duration: 5 * 60 * 1000, // 5 minutes
      size: new Decimal('10000'),
      entryPrice: new Decimal('1.0800'),
      exitPrice: new Decimal('1.0850'),
      grossPnl: new Decimal('50.00'),
      netPnl: new Decimal('48.00'), // After fees
      returnPercentage: new Decimal('0.46'),
      maxFavorableExcursion: new Decimal('75.00'),
      maxAdverseExcursion: new Decimal('-10.00'),
      executionQuality: 92,
      slippage: new Decimal('0.50'),
      fees: new Decimal('2.00'),
      tags: ['scalping', 'major_pairs'],
      marketConditions: {
        volatility: new Decimal('0.25'),
        trend: 'bullish',
        volume: new Decimal('1000000'),
        spread: new Decimal('0.0001'),
        marketHours: 'main',
        economicEvents: []
      }
    };

    // Setup default mocks
    setupDefaultMocks();

    await historyService.initialize();
  });

  afterEach(async () => {
    await historyService.shutdown();
    vi.restoreAllMocks();
  });

  describe('Service Initialization', () => {
    it('should initialize with default trade categories', async () => {
      // Check that default categories were loaded
      const scalingCategory = historyService['tradeCategories'].get('scalping');
      expect(scalingCategory).toBeDefined();
      expect(scalingCategory?.name).toBe('Scalping');
      expect(scalingCategory?.criteria.maxDuration).toBe(5 * 60 * 1000); // 5 minutes

      const swingCategory = historyService['tradeCategories'].get('swing');
      expect(swingCategory).toBeDefined();
      expect(swingCategory?.name).toBe('Swing Trading');
    });

    it('should emit initialized event', async () => {
      const newService = new TradeHistoryService(mockPrisma, mockAuditService);
      const initSpy = vi.spyOn(newService, 'emit');

      await newService.initialize();

      expect(initSpy).toHaveBeenCalledWith('initialized');
      await newService.shutdown();
    });
  });

  describe('Trade Event Logging', () => {
    it('should log trade events with full context', async () => {
      mockAuditService.logEvent.mockResolvedValue('audit_123');
      const eventSpy = vi.spyOn(historyService, 'emit');

      const eventData = {
        executionPrice: '1.0850',
        quantity: '10000',
        broker: 'broker_1'
      };

      const historyId = await historyService.logTradeEvent(
        'trade_123',
        'TRADE_EXECUTED' as TradeEventType,
        eventData,
        'user_123',
        mockTradePerformanceData
      );

      expect(historyId).toBeDefined();
      expect(mockAuditService.logEvent).toHaveBeenCalledWith(
        'user_123',
        'TRADE_TRADE_EXECUTED',
        'trading',
        expect.objectContaining({
          tradeId: 'trade_123',
          eventType: 'TRADE_EXECUTED',
          eventData
        })
      );

      expect(eventSpy).toHaveBeenCalledWith('tradeEventLogged', expect.objectContaining({
        tradeId: 'trade_123',
        eventType: 'TRADE_EXECUTED'
      }));
    });

    it('should calculate performance attribution when enabled', async () => {
      mockAuditService.logEvent.mockResolvedValue('audit_123');

      await historyService.logTradeEvent(
        'trade_123',
        'TRADE_CLOSED' as TradeEventType,
        {},
        'user_123',
        mockTradePerformanceData
      );

      // Check that attribution was calculated and cached
      const attribution = historyService.getAttributionAnalysis('trade_123');
      expect(attribution).toBeDefined();
      expect(attribution?.totalReturn.toFixed(2)).toBe('48.00');
      expect(attribution?.strategyContribution).toBeDefined();
      expect(attribution?.timingContribution).toBeDefined();
      expect(attribution?.executionContribution).toBeDefined();
    });

    it('should handle logging errors gracefully', async () => {
      mockAuditService.logEvent.mockRejectedValue(new Error('Audit service failed'));

      await expect(historyService.logTradeEvent(
        'trade_123',
        'TRADE_EXECUTED' as TradeEventType,
        {},
        'user_123'
      )).rejects.toThrow('Audit service failed');
    });
  });

  describe('Performance Attribution Calculations', () => {
    it('should calculate accurate performance attribution for profitable trade', async () => {
      const profitableTradeData = {
        ...mockTradePerformanceData,
        netPnl: new Decimal('100.00'),
        executionQuality: 95,
        maxFavorableExcursion: new Decimal('120.00')
      };

      const attribution = await historyService.calculatePerformanceAttribution(
        'trade_123',
        profitableTradeData
      );

      expect(attribution.strategyContribution.toNumber()).toBeGreaterThan(0);
      expect(attribution.timingContribution.toNumber()).toBeGreaterThan(0);
      expect(attribution.executionContribution.toNumber()).toBeGreaterThan(0);
      expect(attribution.slippageImpact.toNumber()).toBeLessThan(0); // Negative impact
      expect(attribution.feeImpact.toNumber()).toBeLessThan(0); // Negative impact
      
      // Total attribution should be meaningful
      expect(attribution.totalAttribution.abs().toNumber()).toBeGreaterThan(0);
    });

    it('should calculate attribution for losing trade correctly', async () => {
      const losingTradeData = {
        ...mockTradePerformanceData,
        netPnl: new Decimal('-75.00'),
        executionQuality: 45, // Poor execution
        maxFavorableExcursion: new Decimal('10.00'),
        maxAdverseExcursion: new Decimal('-90.00')
      };

      const attribution = await historyService.calculatePerformanceAttribution(
        'trade_123',
        losingTradeData
      );

      // Strategy contribution for losses should be smaller percentage
      expect(attribution.strategyContribution.toNumber()).toBeLessThan(0);
      
      // Poor execution should contribute negatively 
      expect(attribution.executionContribution.toNumber()).toBeGreaterThan(0);
      
      // Slippage and fees should be negative
      expect(attribution.slippageImpact.toNumber()).toBeLessThan(0);
      expect(attribution.feeImpact.toNumber()).toBeLessThan(0);
    });

    it('should handle attribution calculation errors gracefully', async () => {
      const invalidTradeData = {
        netPnl: new Decimal('NaN'), // Invalid data
        executionQuality: 95
      };

      const attribution = await historyService.calculatePerformanceAttribution(
        'trade_123',
        invalidTradeData
      );

      // Should return zero attribution on error
      expect(attribution.strategyContribution.toFixed()).toBe('NaN');
      expect(attribution.timingContribution.toFixed()).toBe('0');
      expect(attribution.executionContribution.toFixed()).toBe('NaN');
      expect(attribution.totalAttribution.toFixed()).toBe('NaN');
    });

    it('should maintain financial precision in attribution calculations', async () => {
      const preciseTradeData = {
        ...mockTradePerformanceData,
        netPnl: new Decimal('123.456789'), // High precision
        slippage: new Decimal('0.123456'),
        fees: new Decimal('1.23456789')
      };

      const attribution = await historyService.calculatePerformanceAttribution(
        'trade_123',
        preciseTradeData
      );

      // Check precision is maintained
      expect(attribution.slippageImpact.toFixed()).toBe('-0.123456');
      expect(attribution.feeImpact.toFixed()).toBe('-1.23456789');
      
      // Verify calculations maintain precision
      const calculatedTotal = attribution.strategyContribution
        .add(attribution.timingContribution)
        .add(attribution.executionContribution)
        .add(attribution.slippageImpact)
        .add(attribution.feeImpact);
      
      expect(calculatedTotal.toFixed()).toBe(attribution.totalAttribution.toFixed());
    });
  });

  describe('Trade Categorization', () => {
    it('should categorize scalping trades correctly', async () => {
      const scalpingTradeData: TradePerformanceMetrics = {
        ...mockTradePerformanceData as TradePerformanceMetrics,
        duration: 2 * 60 * 1000, // 2 minutes - should match scalping criteria
        tags: []
      };

      // Mock the method to return trade data
      vi.spyOn(historyService as any, 'getTradePerformanceMetrics')
        .mockResolvedValue(scalpingTradeData);

      const categorization = await historyService.categorizeTrades(['trade_123']);

      expect(categorization.get('trade_123')).toContain('scalping');
    });

    it('should categorize swing trades correctly', async () => {
      const swingTradeData: TradePerformanceMetrics = {
        ...mockTradePerformanceData as TradePerformanceMetrics,
        duration: 2 * 60 * 60 * 1000, // 2 hours - should match swing criteria
        tags: []
      };

      vi.spyOn(historyService as any, 'getTradePerformanceMetrics')
        .mockResolvedValue(swingTradeData);

      const categorization = await historyService.categorizeTrades(['trade_123']);

      expect(categorization.get('trade_123')).toContain('swing');
    });

    it('should categorize big wins correctly', async () => {
      const bigWinTradeData: TradePerformanceMetrics = {
        ...mockTradePerformanceData as TradePerformanceMetrics,
        netPnl: new Decimal('1500.00'), // > $1000 profit
        tags: []
      };

      vi.spyOn(historyService as any, 'getTradePerformanceMetrics')
        .mockResolvedValue(bigWinTradeData);

      const categorization = await historyService.categorizeTrades(['trade_123']);

      expect(categorization.get('trade_123')).toContain('big_wins');
    });

    it('should categorize major pairs correctly', async () => {
      const majorPairTradeData: TradePerformanceMetrics = {
        ...mockTradePerformanceData as TradePerformanceMetrics,
        instrument: 'GBPUSD', // Major pair
        tags: []
      };

      vi.spyOn(historyService as any, 'getTradePerformanceMetrics')
        .mockResolvedValue(majorPairTradeData);

      const categorization = await historyService.categorizeTrades(['trade_123']);

      expect(categorization.get('trade_123')).toContain('major_pairs');
    });

    it('should handle multiple category matches', async () => {
      const multiCategoryTradeData: TradePerformanceMetrics = {
        ...mockTradePerformanceData as TradePerformanceMetrics,
        duration: 3 * 60 * 1000, // 3 minutes - scalping
        instrument: 'EURUSD', // Major pair
        netPnl: new Decimal('1200.00'), // Big win
        tags: []
      };

      vi.spyOn(historyService as any, 'getTradePerformanceMetrics')
        .mockResolvedValue(multiCategoryTradeData);

      const categorization = await historyService.categorizeTrades(['trade_123']);

      const categories = categorization.get('trade_123') || [];
      expect(categories).toContain('scalping');
      expect(categories).toContain('major_pairs');
      expect(categories).toContain('big_wins');
    });
  });

  describe('Custom Trade Categories', () => {
    it('should add custom trade category', async () => {
      const customCategory: TradeCategory = {
        id: 'high_volume',
        name: 'High Volume',
        description: 'Trades with volume > 50k',
        criteria: {
          minSize: new Decimal('50000')
        },
        color: '#ff9500',
        icon: '📈'
      };

      const categorySpy = vi.spyOn(historyService, 'emit');

      await historyService.addTradeCategory(customCategory);

      expect(historyService['tradeCategories'].get('high_volume')).toEqual(customCategory);
      expect(categorySpy).toHaveBeenCalledWith('categoryAdded', expect.objectContaining({
        category: customCategory
      }));
    });

    it('should use custom category in trade categorization', async () => {
      // First add custom category
      const customCategory: TradeCategory = {
        id: 'high_volume',
        name: 'High Volume',
        description: 'Trades with volume > 50k',
        criteria: {
          minSize: new Decimal('50000')
        },
        color: '#ff9500',
        icon: '📈'
      };

      await historyService.addTradeCategory(customCategory);

      // Create trade data that matches custom criteria
      const highVolumeTradeData: TradePerformanceMetrics = {
        ...mockTradePerformanceData as TradePerformanceMetrics,
        size: new Decimal('75000'), // Matches high volume criteria
        tags: []
      };

      vi.spyOn(historyService as any, 'getTradePerformanceMetrics')
        .mockResolvedValue(highVolumeTradeData);

      const categorization = await historyService.categorizeTrades(['trade_123']);

      expect(categorization.get('trade_123')).toContain('high_volume');
    });
  });

  describe('Historical Performance Reports', () => {
    it('should generate historical performance report with correct structure', async () => {
      const startDate = new Date('2024-01-01');
      const endDate = new Date('2024-01-31');

      // Mock trade data loading
      vi.spyOn(historyService as any, 'loadTradeDataForPeriod').mockResolvedValue([]);

      const report = await historyService.generateHistoricalReport(
        'user_123',
        startDate,
        endDate
      );

      expect(report).toBeDefined();
      expect(report.period.start).toEqual(startDate);
      expect(report.period.end).toEqual(endDate);
      expect(report.period.days).toBe(30);
      
      expect(report.summary).toBeDefined();
      expect(report.attribution).toBeDefined();
      expect(report.categorization).toBeDefined();
      expect(report.timeline).toBeDefined();
    });

    it('should calculate summary metrics correctly', async () => {
      const mockTrades: TradePerformanceMetrics[] = [
        {
          ...mockTradePerformanceData as TradePerformanceMetrics,
          netPnl: new Decimal('100.00') // Winner
        },
        {
          ...mockTradePerformanceData as TradePerformanceMetrics,
          tradeId: 'trade_124',
          netPnl: new Decimal('-50.00') // Loser
        },
        {
          ...mockTradePerformanceData as TradePerformanceMetrics,
          tradeId: 'trade_125',
          netPnl: new Decimal('200.00') // Winner
        }
      ];

      const summary = historyService['calculateSummaryMetrics'](mockTrades);

      expect(summary.totalTrades).toBe(3);
      expect(summary.winningTrades).toBe(2);
      expect(summary.losingTrades).toBe(1);
      expect(summary.winRate.toFixed(2)).toBe('66.67'); // 2/3 * 100
      expect(summary.averageWin.toFixed(2)).toBe('150.00'); // (100+200)/2
      expect(summary.averageLoss.toFixed(2)).toBe('50.00'); // 50/1
      expect(summary.profitFactor.toFixed(2)).toBe('6.00'); // 300/50
      expect(summary.totalPnl.toFixed(2)).toBe('250.00'); // 300-50
    });

    it('should handle empty trade data gracefully', async () => {
      const summary = historyService['calculateSummaryMetrics']([]);

      expect(summary.totalTrades).toBe(0);
      expect(summary.winRate.toFixed()).toBe('0');
      expect(summary.totalPnl.toFixed()).toBe('0');
      expect(summary.profitFactor.toFixed()).toBe('0');
    });

    it('should emit report generated event', async () => {
      const reportSpy = vi.spyOn(historyService, 'emit');
      vi.spyOn(historyService as any, 'loadTradeDataForPeriod').mockResolvedValue([]);

      await historyService.generateHistoricalReport(
        'user_123',
        new Date('2024-01-01'),
        new Date('2024-01-31')
      );

      expect(reportSpy).toHaveBeenCalledWith('reportGenerated', expect.objectContaining({
        userId: 'user_123',
        reportType: 'historical_performance'
      }));
    });
  });

  describe('Trade History Retrieval', () => {
    it('should retrieve trade history from cache', async () => {
      // Add some history records to cache
      const mockRecord = {
        id: 'hist_123',
        tradeId: 'trade_123',
        eventType: 'TRADE_EXECUTED' as TradeEventType,
        eventData: { price: '1.0850' },
        timestamp: new Date(),
        userId: 'user_123',
        auditTrailId: 'audit_123',
        performanceAttribution: {
          strategyContribution: new Decimal('0'),
          timingContribution: new Decimal('0'),
          executionContribution: new Decimal('0'),
          slippageImpact: new Decimal('0'),
          feeImpact: new Decimal('0'),
          totalAttribution: new Decimal('0')
        }
      };

      historyService['tradeHistory'].set('trade_123', [mockRecord]);

      const history = await historyService.getTradeHistory('trade_123');

      expect(history).toHaveLength(1);
      expect(history[0]).toEqual(mockRecord);
    });

    it('should load from database if not in cache', async () => {
      vi.spyOn(historyService as any, 'loadTradeHistoryFromDatabase')
        .mockResolvedValue([]);

      const history = await historyService.getTradeHistory('nonexistent_trade');

      expect(history).toHaveLength(0);
      expect(historyService['loadTradeHistoryFromDatabase']).toHaveBeenCalledWith('nonexistent_trade');
    });
  });

  describe('Data Cleanup', () => {
    it('should clean up old records based on retention policy', async () => {
      // Add some old and new records
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 40); // Older than 30-day retention

      const newDate = new Date();
      newDate.setDate(newDate.getDate() - 10); // Within retention period

      const oldRecord = {
        id: 'hist_old',
        tradeId: 'trade_old',
        eventType: 'TRADE_EXECUTED' as TradeEventType,
        eventData: {},
        timestamp: oldDate,
        userId: 'user_123',
        auditTrailId: 'audit_old',
        performanceAttribution: {
          strategyContribution: new Decimal('0'),
          timingContribution: new Decimal('0'),
          executionContribution: new Decimal('0'),
          slippageImpact: new Decimal('0'),
          feeImpact: new Decimal('0'),
          totalAttribution: new Decimal('0')
        }
      };

      const newRecord = {
        ...oldRecord,
        id: 'hist_new',
        tradeId: 'trade_new',
        timestamp: newDate
      };

      historyService['tradeHistory'].set('trade_old', [oldRecord]);
      historyService['tradeHistory'].set('trade_new', [newRecord]);

      const cleanupSpy = vi.spyOn(historyService, 'emit');

      const results = await historyService.cleanupOldRecords();

      expect(results.deleted).toBeGreaterThan(0);
      expect(cleanupSpy).toHaveBeenCalledWith('cleanupCompleted', expect.objectContaining({
        deleted: expect.any(Number),
        compressed: expect.any(Number)
      }));

      // Old records should be removed
      expect(historyService['tradeHistory'].has('trade_old')).toBe(false);
      // New records should remain
      expect(historyService['tradeHistory'].has('trade_new')).toBe(true);
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle invalid trade categorization criteria', async () => {
      vi.spyOn(historyService as any, 'getTradePerformanceMetrics')
        .mockResolvedValue(null); // Simulate missing trade data

      const categorization = await historyService.categorizeTrades(['invalid_trade']);

      expect(categorization.size).toBe(0);
    });

    it('should handle performance attribution calculation failures', async () => {
      // Test with undefined/null data
      const attribution = await historyService.calculatePerformanceAttribution(
        'trade_123',
        {} // Empty performance data
      );

      expect(attribution).toBeDefined();
      expect(attribution.totalAttribution.toFixed()).toBe('0');
    });

    it('should handle service shutdown gracefully', async () => {
      const shutdownSpy = vi.spyOn(historyService, 'emit');

      await historyService.shutdown();

      expect(historyService['tradeHistory'].size).toBe(0);
      expect(historyService['performanceCache'].size).toBe(0);
      expect(historyService['tradeCategories'].size).toBe(0);
      expect(shutdownSpy).toHaveBeenCalledWith('shutdown');
    });
  });

  // Helper function to setup default mocks
  function setupDefaultMocks(): void {
    mockAuditService.logEvent.mockResolvedValue('audit_123');
    mockPrisma.tradeHistoryRecord.create.mockResolvedValue({});
    mockPrisma.tradeHistoryRecord.findMany.mockResolvedValue([]);
  }
});