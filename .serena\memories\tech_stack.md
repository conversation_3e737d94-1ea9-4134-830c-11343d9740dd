# GoldDaddy Tech Stack

## Frontend Architecture  
- Next.js 14 with App Router
- TypeScript with strict mode enabled
- Tailwind CSS for styling
- S<PERSON> for data fetching and caching
- Component library using Radix UI primitives

## Backend Architecture
- RESTful API with Express.js backend
- PostgreSQL with TimescaleDB for time-series market data
- Redis for caching and real-time features
- JWT authentication with refresh tokens

## MT5 Integration
- Hybrid Python/TypeScript architecture
- Python bridge for MetaTrader 5 API communication
- TypeScript service layer for business logic
- WebSocket connections for real-time data streaming

## Database
- PostgreSQL + TimescaleDB for time-series data
- Key entities: Users & Authentication, Trading Accounts, Trades, Market Data (hypertable), OHLC Data (hypertable), Strategies

## Testing Stack
- **Unit Tests**: Jest/Vitest for business logic
- **Integration Tests**: API endpoint testing
- **E2E Tests**: Playwright for user workflows
- **Component Tests**: React Testing Library

## Development Tools
- TypeScript 5.3+ with strict mode
- ESLint + Prettier for code quality
- <PERSON>sky + lint-staged for pre-commit hooks
- Docker + Docker Compose for containerization