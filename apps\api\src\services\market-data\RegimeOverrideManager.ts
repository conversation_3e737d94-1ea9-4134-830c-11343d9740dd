/**
 * Regime Override Manager Service
 * 
 * Handles manual overrides of regime detection results, allowing users to:
 * - Override current regime classifications
 * - Set temporary regime states with duration
 * - Track override accuracy and performance
 * - Learn from override patterns to improve detection
 */

import { EventEmitter } from 'events';
import Decimal from 'decimal.js';
import {
  MarketRegime,
  RegimeDetectionResult,
  TimeFrame,
  DataSource,
} from '@golddaddy/types';

// Override request from user
export interface RegimeOverrideRequest {
  id: string;
  instrument: string;
  timeframe: TimeFrame;
  userId: string;
  requestedRegime: MarketRegime;
  reason: OverrideReason;
  confidence: number; // User confidence 1-100
  duration?: number; // Duration in minutes (optional)
  supportingData?: {
    priceTargets?: { upper?: number; lower?: number };
    technicalIndicators?: Record<string, number>;
    fundamentalFactors?: string[];
    newsEvents?: string[];
  };
  comment?: string;
  timestamp: Date;
}

// Override result record
export interface RegimeOverride {
  id: string;
  request: RegimeOverrideRequest;
  originalDetection: RegimeDetectionResult;
  overriddenRegime: MarketRegime;
  status: OverrideStatus;
  effectiveness?: OverrideEffectiveness;
  expiresAt?: Date;
  actualOutcome?: {
    regime: MarketRegime;
    timestamp: Date;
    accuracy: number; // How accurate was the override 0-1
  };
  createdAt: Date;
  updatedAt: Date;
}

// Override reasons
export enum OverrideReason {
  NEWS_EVENT = 'news_event',
  TECHNICAL_ANALYSIS = 'technical_analysis',
  FUNDAMENTAL_ANALYSIS = 'fundamental_analysis',
  MARKET_SENTIMENT = 'market_sentiment',
  ECONOMIC_DATA = 'economic_data',
  INSIDER_KNOWLEDGE = 'insider_knowledge',
  RISK_MANAGEMENT = 'risk_management',
  ALGORITHM_ERROR = 'algorithm_error',
  MANUAL_VALIDATION = 'manual_validation',
  OTHER = 'other'
}

// Override status
export enum OverrideStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  VALIDATED = 'validated',
  INVALIDATED = 'invalidated',
  CANCELLED = 'cancelled'
}

// Override effectiveness assessment
interface OverrideEffectiveness {
  accuracyScore: number; // 0-1
  timeToValidation: number; // minutes
  impactScore: number; // how much it improved vs original
  confidenceCalibration: number; // user confidence vs actual accuracy
  learningValue: number; // how much this taught the system
}

// User override statistics
export interface UserOverrideStats {
  userId: string;
  totalOverrides: number;
  activeOverrides: number;
  averageAccuracy: number;
  bestPerformingReason: OverrideReason;
  overrideFrequencyByInstrument: Record<string, number>;
  calibrationScore: number; // how well-calibrated user confidence is
  contributionScore: number; // overall value to system
}

// Override configuration
interface OverrideConfig {
  maxActiveOverridesPerUser: number;
  maxOverrideDurationMinutes: number;
  minConfidenceRequired: number;
  autoExpirationEnabled: boolean;
  learningEnabled: boolean;
  validateOverridesEnabled: boolean;
  notificationEnabled: boolean;
  overrideHistoryRetentionDays: number;
}

/**
 * Regime Override Manager - Manual regime override handling
 */
export class RegimeOverrideManager extends EventEmitter {
  private config: OverrideConfig;
  private activeOverrides = new Map<string, RegimeOverride>();
  private overrideHistory = new Map<string, RegimeOverride[]>();
  private userStats = new Map<string, UserOverrideStats>();
  
  // Performance tracking
  private stats = {
    totalOverrides: 0,
    activeOverrideCount: 0,
    averageOverrideAccuracy: 0,
    successfulOverrides: 0,
    learningEvents: 0,
    systemImprovements: 0,
  };

  constructor(config?: Partial<OverrideConfig>) {
    super();
    
    this.config = {
      maxActiveOverridesPerUser: 10,
      maxOverrideDurationMinutes: 1440, // 24 hours
      minConfidenceRequired: 60, // 60%
      autoExpirationEnabled: true,
      learningEnabled: true,
      validateOverridesEnabled: true,
      notificationEnabled: true,
      overrideHistoryRetentionDays: 90,
      ...config,
    };

    // Set up auto-expiration timer if enabled
    if (this.config.autoExpirationEnabled) {
      setInterval(() => this.processExpirations(), 60000); // Check every minute
    }
  }

  /**
   * Request a manual regime override
   */
  public async requestOverride(
    request: RegimeOverrideRequest,
    originalDetection: RegimeDetectionResult
  ): Promise<RegimeOverride> {
    try {
      // Validate override request
      this.validateOverrideRequest(request);
      
      // Check user limits
      await this.checkUserLimits(request.userId);
      
      // Create override record
      const override = this.createOverrideRecord(request, originalDetection);
      
      // Store active override
      this.activeOverrides.set(override.id, override);
      
      // Update history
      this.addToHistory(request.userId, override);
      
      // Update user statistics
      this.updateUserStats(request.userId, override);
      
      // Update global statistics
      this.updateGlobalStats();
      
      // Set expiration if specified
      if (request.duration) {
        this.scheduleExpiration(override);
      }
      
      this.emit('override_created', override);
      
      // Learn from override if enabled
      if (this.config.learningEnabled) {
        await this.learnFromOverride(override);
      }
      
      return override;

    } catch (error) {
      this.emit('override_error', {
        requestId: request.id,
        userId: request.userId,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Validate an override against actual market outcome
   */
  public async validateOverride(
    overrideId: string,
    actualRegime: MarketRegime,
    timestamp: Date
  ): Promise<void> {
    const override = this.activeOverrides.get(overrideId) || 
                     this.findInHistory(overrideId);
    
    if (!override) {
      throw new Error(`Override ${overrideId} not found`);
    }
    
    // Calculate accuracy
    const accuracy = this.calculateOverrideAccuracy(override, actualRegime);
    
    // Update override with validation results
    override.actualOutcome = {
      regime: actualRegime,
      timestamp,
      accuracy,
    };
    
    // Calculate effectiveness
    override.effectiveness = this.calculateEffectiveness(override, timestamp);
    
    // Update status
    override.status = accuracy > 0.7 ? OverrideStatus.VALIDATED : OverrideStatus.INVALIDATED;
    override.updatedAt = new Date();
    
    // Update user stats
    this.updateUserStatsWithValidation(override);
    
    // Update global stats
    this.updateGlobalStatsWithValidation(override);
    
    this.emit('override_validated', {
      overrideId,
      accuracy,
      wasCorrect: accuracy > 0.7,
      effectiveness: override.effectiveness,
    });
    
    // Learn from validation if enabled
    if (this.config.learningEnabled) {
      await this.learnFromValidation(override);
    }
  }

  /**
   * Cancel an active override
   */
  public async cancelOverride(overrideId: string, userId: string): Promise<void> {
    const override = this.activeOverrides.get(overrideId);
    
    if (!override) {
      throw new Error(`Override ${overrideId} not found or not active`);
    }
    
    if (override.request.userId !== userId) {
      throw new Error('User not authorized to cancel this override');
    }
    
    // Update status
    override.status = OverrideStatus.CANCELLED;
    override.updatedAt = new Date();
    
    // Remove from active overrides
    this.activeOverrides.delete(overrideId);
    
    this.emit('override_cancelled', { overrideId, userId });
  }

  /**
   * Get active overrides for an instrument/timeframe
   */
  public getActiveOverrides(
    instrument?: string,
    timeframe?: TimeFrame
  ): RegimeOverride[] {
    let overrides = Array.from(this.activeOverrides.values());
    
    if (instrument) {
      overrides = overrides.filter(o => o.request.instrument === instrument);
    }
    
    if (timeframe) {
      overrides = overrides.filter(o => o.request.timeframe === timeframe);
    }
    
    return overrides.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  /**
   * Get override history for a user
   */
  public getUserOverrideHistory(
    userId: string,
    limit = 50
  ): RegimeOverride[] {
    const userHistory = this.overrideHistory.get(userId) || [];
    return userHistory
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, limit);
  }

  /**
   * Get user override statistics
   */
  public getUserStats(userId: string): UserOverrideStats | null {
    return this.userStats.get(userId) || null;
  }

  /**
   * Get system override statistics
   */
  public getSystemStats() {
    return {
      ...this.stats,
      activeOverrides: this.activeOverrides.size,
      totalUsers: this.userStats.size,
      averageUserAccuracy: this.calculateAverageUserAccuracy(),
      topPerformers: this.getTopPerformers(),
    };
  }

  // ===== Private Methods =====

  private validateOverrideRequest(request: RegimeOverrideRequest): void {
    if (!request.instrument || !request.timeframe) {
      throw new Error('Instrument and timeframe are required');
    }
    
    if (request.confidence < this.config.minConfidenceRequired) {
      throw new Error(`Confidence must be at least ${this.config.minConfidenceRequired}%`);
    }
    
    if (request.duration && request.duration > this.config.maxOverrideDurationMinutes) {
      throw new Error(`Duration cannot exceed ${this.config.maxOverrideDurationMinutes} minutes`);
    }
    
    if (!Object.values(MarketRegime).includes(request.requestedRegime)) {
      throw new Error('Invalid regime requested');
    }
    
    if (!Object.values(OverrideReason).includes(request.reason)) {
      throw new Error('Invalid override reason');
    }
  }

  private async checkUserLimits(userId: string): Promise<void> {
    const userActiveOverrides = Array.from(this.activeOverrides.values())
      .filter(o => o.request.userId === userId);
    
    if (userActiveOverrides.length >= this.config.maxActiveOverridesPerUser) {
      throw new Error(`User has reached maximum active overrides (${this.config.maxActiveOverridesPerUser})`);
    }
  }

  private createOverrideRecord(
    request: RegimeOverrideRequest,
    originalDetection: RegimeDetectionResult
  ): RegimeOverride {
    const now = new Date();
    
    return {
      id: request.id,
      request,
      originalDetection,
      overriddenRegime: request.requestedRegime,
      status: OverrideStatus.ACTIVE,
      expiresAt: request.duration ? 
        new Date(now.getTime() + request.duration * 60 * 1000) : undefined,
      createdAt: now,
      updatedAt: now,
    };
  }

  private addToHistory(userId: string, override: RegimeOverride): void {
    const history = this.overrideHistory.get(userId) || [];
    history.push(override);
    
    // Trim history to retention limit
    const retentionMs = this.config.overrideHistoryRetentionDays * 24 * 60 * 60 * 1000;
    const cutoffDate = new Date(Date.now() - retentionMs);
    
    const filteredHistory = history.filter(o => o.createdAt > cutoffDate);
    this.overrideHistory.set(userId, filteredHistory);
  }

  private updateUserStats(userId: string, override: RegimeOverride): void {
    let stats = this.userStats.get(userId);
    
    if (!stats) {
      stats = {
        userId,
        totalOverrides: 0,
        activeOverrides: 0,
        averageAccuracy: 0,
        bestPerformingReason: OverrideReason.TECHNICAL_ANALYSIS,
        overrideFrequencyByInstrument: {},
        calibrationScore: 0.5,
        contributionScore: 0,
      };
    }
    
    stats.totalOverrides++;
    stats.activeOverrides++;
    
    // Update instrument frequency
    const instrument = override.request.instrument;
    stats.overrideFrequencyByInstrument[instrument] = 
      (stats.overrideFrequencyByInstrument[instrument] || 0) + 1;
    
    this.userStats.set(userId, stats);
  }

  private updateGlobalStats(): void {
    this.stats.totalOverrides++;
    this.stats.activeOverrideCount = this.activeOverrides.size;
  }

  private scheduleExpiration(override: RegimeOverride): void {
    if (!override.expiresAt) return;
    
    const timeToExpiry = override.expiresAt.getTime() - Date.now();
    
    if (timeToExpiry > 0) {
      setTimeout(() => {
        this.expireOverride(override.id);
      }, timeToExpiry);
    }
  }

  private processExpirations(): void {
    const now = new Date();
    const expiredOverrides: string[] = [];
    
    for (const [id, override] of this.activeOverrides.entries()) {
      if (override.expiresAt && override.expiresAt <= now) {
        expiredOverrides.push(id);
      }
    }
    
    expiredOverrides.forEach(id => this.expireOverride(id));
  }

  private expireOverride(overrideId: string): void {
    const override = this.activeOverrides.get(overrideId);
    
    if (override) {
      override.status = OverrideStatus.EXPIRED;
      override.updatedAt = new Date();
      
      // Remove from active overrides
      this.activeOverrides.delete(overrideId);
      
      // Update user stats
      const userStats = this.userStats.get(override.request.userId);
      if (userStats) {
        userStats.activeOverrides = Math.max(0, userStats.activeOverrides - 1);
      }
      
      this.emit('override_expired', { overrideId, override });
    }
  }

  private calculateOverrideAccuracy(
    override: RegimeOverride,
    actualRegime: MarketRegime
  ): number {
    const predicted = override.overriddenRegime;
    const original = override.originalDetection.regime;
    
    // Perfect match
    if (predicted === actualRegime) return 1.0;
    
    // Partial credit for being closer than original
    const predictedScore = this.getRegimeDistance(predicted, actualRegime);
    const originalScore = this.getRegimeDistance(original, actualRegime);
    
    if (predictedScore < originalScore) {
      return 0.7; // Better than original algorithm
    } else if (predictedScore === originalScore) {
      return 0.5; // Same as original
    } else {
      return 0.3; // Worse than original
    }
  }

  private getRegimeDistance(regime1: MarketRegime, regime2: MarketRegime): number {
    // Distance matrix between regimes (0 = same, 1 = opposite)
    const distances: Record<MarketRegime, Record<MarketRegime, number>> = {
      [MarketRegime.TRENDING_UP]: {
        [MarketRegime.TRENDING_UP]: 0,
        [MarketRegime.TRENDING_DOWN]: 1.0,
        [MarketRegime.SIDEWAYS]: 0.5,
        [MarketRegime.VOLATILE]: 0.6,
        [MarketRegime.LOW_VOLATILITY]: 0.7,
        [MarketRegime.UNKNOWN]: 0.8,
      },
      [MarketRegime.TRENDING_DOWN]: {
        [MarketRegime.TRENDING_UP]: 1.0,
        [MarketRegime.TRENDING_DOWN]: 0,
        [MarketRegime.SIDEWAYS]: 0.5,
        [MarketRegime.VOLATILE]: 0.6,
        [MarketRegime.LOW_VOLATILITY]: 0.7,
        [MarketRegime.UNKNOWN]: 0.8,
      },
      [MarketRegime.SIDEWAYS]: {
        [MarketRegime.TRENDING_UP]: 0.5,
        [MarketRegime.TRENDING_DOWN]: 0.5,
        [MarketRegime.SIDEWAYS]: 0,
        [MarketRegime.VOLATILE]: 0.4,
        [MarketRegime.LOW_VOLATILITY]: 0.3,
        [MarketRegime.UNKNOWN]: 0.6,
      },
      [MarketRegime.VOLATILE]: {
        [MarketRegime.TRENDING_UP]: 0.6,
        [MarketRegime.TRENDING_DOWN]: 0.6,
        [MarketRegime.SIDEWAYS]: 0.4,
        [MarketRegime.VOLATILE]: 0,
        [MarketRegime.LOW_VOLATILITY]: 1.0,
        [MarketRegime.UNKNOWN]: 0.5,
      },
      [MarketRegime.LOW_VOLATILITY]: {
        [MarketRegime.TRENDING_UP]: 0.7,
        [MarketRegime.TRENDING_DOWN]: 0.7,
        [MarketRegime.SIDEWAYS]: 0.3,
        [MarketRegime.VOLATILE]: 1.0,
        [MarketRegime.LOW_VOLATILITY]: 0,
        [MarketRegime.UNKNOWN]: 0.5,
      },
      [MarketRegime.UNKNOWN]: {
        [MarketRegime.TRENDING_UP]: 0.8,
        [MarketRegime.TRENDING_DOWN]: 0.8,
        [MarketRegime.SIDEWAYS]: 0.6,
        [MarketRegime.VOLATILE]: 0.5,
        [MarketRegime.LOW_VOLATILITY]: 0.5,
        [MarketRegime.UNKNOWN]: 0,
      },
    };
    
    return distances[regime1]?.[regime2] || 0.8;
  }

  private calculateEffectiveness(
    override: RegimeOverride,
    validationTime: Date
  ): OverrideEffectiveness {
    const accuracy = override.actualOutcome?.accuracy || 0;
    const timeToValidation = (validationTime.getTime() - override.createdAt.getTime()) / (60 * 1000); // minutes
    
    // Calculate impact vs original detection
    const originalAccuracy = this.calculateOverrideAccuracy(
      { ...override, overriddenRegime: override.originalDetection.regime },
      override.actualOutcome!.regime
    );
    const impactScore = Math.max(0, accuracy - originalAccuracy);
    
    // Calculate confidence calibration
    const userConfidence = override.request.confidence / 100;
    const confidenceError = Math.abs(userConfidence - accuracy);
    const confidenceCalibration = 1 - confidenceError;
    
    // Calculate learning value
    const learningValue = this.calculateLearningValue(override);
    
    return {
      accuracyScore: accuracy,
      timeToValidation,
      impactScore,
      confidenceCalibration: Math.max(0, confidenceCalibration),
      learningValue,
    };
  }

  private calculateLearningValue(override: RegimeOverride): number {
    // Higher learning value for:
    // - Novel patterns not seen before
    // - Corrections of systematic algorithm errors
    // - High-confidence accurate overrides
    
    let learningValue = 0.5; // Base value
    
    // Boost for high accuracy + high confidence
    if (override.actualOutcome && override.actualOutcome.accuracy > 0.8) {
      if (override.request.confidence > 80) {
        learningValue += 0.3;
      }
    }
    
    // Boost for correcting algorithm errors
    if (override.request.reason === OverrideReason.ALGORITHM_ERROR) {
      learningValue += 0.2;
    }
    
    // Boost for providing additional context
    if (override.request.supportingData) {
      learningValue += 0.1;
    }
    
    return Math.min(1.0, learningValue);
  }

  private updateUserStatsWithValidation(override: RegimeOverride): void {
    const userStats = this.userStats.get(override.request.userId);
    if (!userStats || !override.actualOutcome) return;
    
    const accuracy = override.actualOutcome.accuracy;
    
    // Update average accuracy
    const totalValidated = userStats.totalOverrides - userStats.activeOverrides;
    if (totalValidated > 0) {
      userStats.averageAccuracy = 
        ((userStats.averageAccuracy * (totalValidated - 1)) + accuracy) / totalValidated;
    } else {
      userStats.averageAccuracy = accuracy;
    }
    
    // Update calibration score
    const userConfidence = override.request.confidence / 100;
    const calibrationError = Math.abs(userConfidence - accuracy);
    userStats.calibrationScore = 
      ((userStats.calibrationScore * (totalValidated - 1)) + (1 - calibrationError)) / totalValidated;
    
    // Update contribution score
    if (override.effectiveness) {
      userStats.contributionScore = 
        ((userStats.contributionScore * (totalValidated - 1)) + override.effectiveness.learningValue) / totalValidated;
    }
    
    // Decrease active override count
    userStats.activeOverrides = Math.max(0, userStats.activeOverrides - 1);
  }

  private updateGlobalStatsWithValidation(override: RegimeOverride): void {
    if (!override.actualOutcome) return;
    
    const accuracy = override.actualOutcome.accuracy;
    
    if (accuracy > 0.7) {
      this.stats.successfulOverrides++;
    }
    
    // Update average accuracy
    const totalValidated = this.stats.totalOverrides - this.stats.activeOverrideCount;
    if (totalValidated > 0) {
      this.stats.averageOverrideAccuracy = 
        ((this.stats.averageOverrideAccuracy * (totalValidated - 1)) + accuracy) / totalValidated;
    }
    
    if (override.effectiveness && override.effectiveness.learningValue > 0.7) {
      this.stats.learningEvents++;
    }
  }

  private findInHistory(overrideId: string): RegimeOverride | null {
    for (const history of this.overrideHistory.values()) {
      const found = history.find(o => o.id === overrideId);
      if (found) return found;
    }
    return null;
  }

  private calculateAverageUserAccuracy(): number {
    const accuracies = Array.from(this.userStats.values()).map(s => s.averageAccuracy);
    return accuracies.length > 0 ? 
      accuracies.reduce((sum, acc) => sum + acc, 0) / accuracies.length : 0;
  }

  private getTopPerformers(): Array<{ userId: string; accuracy: number; contributions: number }> {
    return Array.from(this.userStats.entries())
      .map(([userId, stats]) => ({
        userId,
        accuracy: stats.averageAccuracy,
        contributions: stats.contributionScore,
      }))
      .sort((a, b) => (b.accuracy + b.contributions) - (a.accuracy + a.contributions))
      .slice(0, 10);
  }

  private async learnFromOverride(override: RegimeOverride): Promise<void> {
    // This would integrate with the pattern matcher and regime detector
    // to learn from user corrections and overrides
    this.emit('learning_event', {
      type: 'override_created',
      override,
      learningData: {
        regime: override.overriddenRegime,
        reason: override.request.reason,
        confidence: override.request.confidence,
        supportingData: override.request.supportingData,
      },
    });
  }

  private async learnFromValidation(override: RegimeOverride): Promise<void> {
    if (!override.actualOutcome || !override.effectiveness) return;
    
    // Learn from successful overrides
    if (override.actualOutcome.accuracy > 0.7) {
      this.emit('learning_event', {
        type: 'override_validated',
        override,
        learningData: {
          accuracy: override.actualOutcome.accuracy,
          effectiveness: override.effectiveness,
          reason: override.request.reason,
          patterns: override.request.supportingData,
        },
      });
    }
  }

  /**
   * Get override suggestions based on current market state
   */
  public getOverrideSuggestions(
    currentDetection: RegimeDetectionResult,
    marketContext?: {
      newsEvents: string[];
      technicalSignals: Record<string, number>;
      marketSentiment: number;
    }
  ): Array<{
    suggestedRegime: MarketRegime;
    reason: OverrideReason;
    confidence: number;
    explanation: string;
  }> {
    const suggestions: Array<{
      suggestedRegime: MarketRegime;
      reason: OverrideReason;
      confidence: number;
      explanation: string;
    }> = [];
    
    // Analyze current detection confidence
    if (currentDetection.confidence < 60) {
      // Suggest alternative regimes when confidence is low
      const alternatives = this.getAlternativeRegimes(currentDetection.regime);
      
      alternatives.forEach(alt => {
        suggestions.push({
          suggestedRegime: alt,
          reason: OverrideReason.ALGORITHM_ERROR,
          confidence: 65,
          explanation: `Low algorithm confidence (${currentDetection.confidence}%) suggests ${alt} might be more accurate`,
        });
      });
    }
    
    // Market context based suggestions
    if (marketContext) {
      // News events
      if (marketContext.newsEvents.length > 0) {
        suggestions.push({
          suggestedRegime: MarketRegime.VOLATILE,
          reason: OverrideReason.NEWS_EVENT,
          confidence: 70,
          explanation: `Active news events may increase volatility: ${marketContext.newsEvents.join(', ')}`,
        });
      }
      
      // Technical signals
      const technicalSignal = this.interpretTechnicalSignals(marketContext.technicalSignals);
      if (technicalSignal) {
        suggestions.push(technicalSignal);
      }
    }
    
    return suggestions.slice(0, 3); // Return top 3 suggestions
  }

  private getAlternativeRegimes(currentRegime: MarketRegime): MarketRegime[] {
    const alternatives: Record<MarketRegime, MarketRegime[]> = {
      [MarketRegime.TRENDING_UP]: [MarketRegime.SIDEWAYS, MarketRegime.VOLATILE],
      [MarketRegime.TRENDING_DOWN]: [MarketRegime.SIDEWAYS, MarketRegime.VOLATILE],
      [MarketRegime.SIDEWAYS]: [MarketRegime.LOW_VOLATILITY, MarketRegime.TRENDING_UP],
      [MarketRegime.VOLATILE]: [MarketRegime.TRENDING_UP, MarketRegime.TRENDING_DOWN],
      [MarketRegime.LOW_VOLATILITY]: [MarketRegime.SIDEWAYS, MarketRegime.TRENDING_UP],
      [MarketRegime.UNKNOWN]: [MarketRegime.SIDEWAYS, MarketRegime.VOLATILE],
    };
    
    return alternatives[currentRegime] || [MarketRegime.SIDEWAYS];
  }

  private interpretTechnicalSignals(signals: Record<string, number>): {
    suggestedRegime: MarketRegime;
    reason: OverrideReason;
    confidence: number;
    explanation: string;
  } | null {
    // Simple technical signal interpretation
    const rsi = signals.rsi14;
    const atr = signals.atr14;
    
    if (rsi && rsi > 80) {
      return {
        suggestedRegime: MarketRegime.TRENDING_DOWN,
        reason: OverrideReason.TECHNICAL_ANALYSIS,
        confidence: 75,
        explanation: `RSI overbought (${rsi.toFixed(1)}) suggests potential downward movement`,
      };
    }
    
    if (rsi && rsi < 20) {
      return {
        suggestedRegime: MarketRegime.TRENDING_UP,
        reason: OverrideReason.TECHNICAL_ANALYSIS,
        confidence: 75,
        explanation: `RSI oversold (${rsi.toFixed(1)}) suggests potential upward movement`,
      };
    }
    
    if (atr && atr > 0.005) { // High ATR suggests volatility
      return {
        suggestedRegime: MarketRegime.VOLATILE,
        reason: OverrideReason.TECHNICAL_ANALYSIS,
        confidence: 70,
        explanation: `High ATR (${(atr * 10000).toFixed(1)} pips) indicates volatile conditions`,
      };
    }
    
    return null;
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<OverrideConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config_updated', this.config);
  }

  /**
   * Get current configuration
   */
  public getConfig(): OverrideConfig {
    return { ...this.config };
  }

  /**
   * Clear all data and reset
   */
  public clearAll(): void {
    this.activeOverrides.clear();
    this.overrideHistory.clear();
    this.userStats.clear();
    
    this.stats = {
      totalOverrides: 0,
      activeOverrideCount: 0,
      averageOverrideAccuracy: 0,
      successfulOverrides: 0,
      learningEvents: 0,
      systemImprovements: 0,
    };
    
    this.emit('data_cleared');
  }

  /**
   * Shutdown and cleanup
   */
  public shutdown(): void {
    this.clearAll();
    this.removeAllListeners();
  }
}