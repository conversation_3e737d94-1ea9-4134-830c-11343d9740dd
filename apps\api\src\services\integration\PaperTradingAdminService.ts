import { PrismaClient } from '@prisma/client';
import { PaperTradingAnalyticsService } from '../trading/PaperTradingAnalyticsService';
import { PaperTradingFeatureFlags } from './PaperTradingFeatureFlags';
import { AuditService } from '../services/compliance/AuditTrailService';

export interface AdminDashboardStats {
  totalUsers: number;
  activeUsers: number;
  totalSessions: number;
  activeSessions: number;
  totalTrades: number;
  graduationRate: number;
  averageSessionDuration: number;
  topPerformers: Array<{
    userId: string;
    username: string;
    totalReturn: number;
    winRate: number;
    tradeCount: number;
  }>;
  performanceDistribution: {
    excellent: number;
    good: number;
    average: number;
    poor: number;
  };
  featureFlagUsage: Array<{
    flagKey: string;
    totalChecks: number;
    enabledPercentage: number;
  }>;
}

export interface UserSessionDetails {
  sessionId: string;
  userId: string;
  username: string;
  status: string;
  startDate: Date;
  duration: number;
  tradeCount: number;
  performance: {
    totalReturn: number;
    winRate: number;
    sharpeRatio: number;
    maxDrawdown: number;
  };
  graduationStatus: {
    eligible: boolean;
    completedRequirements: string[];
    pendingRequirements: string[];
  };
  riskFlags: string[];
}

export interface SystemHealthMetrics {
  apiResponseTime: number;
  databaseConnectionTime: number;
  activeConnections: number;
  errorRate: number;
  featureFlagLatency: number;
  cacheHitRate: number;
  systemAlerts: Array<{
    level: 'info' | 'warning' | 'error' | 'critical';
    message: string;
    timestamp: Date;
    component: string;
  }>;
}

export class PaperTradingAdminService {
  private prisma: PrismaClient;
  private analyticsService: PaperTradingAnalyticsService;
  private featureFlagsService: PaperTradingFeatureFlags;
  private auditService: AuditService;

  constructor(
    prisma: PrismaClient,
    analyticsService: PaperTradingAnalyticsService,
    featureFlagsService: PaperTradingFeatureFlags,
    auditService: AuditService
  ) {
    this.prisma = prisma;
    this.analyticsService = analyticsService;
    this.featureFlagsService = featureFlagsService;
    this.auditService = auditService;
  }

  /**
   * Get comprehensive dashboard statistics
   */
  async getDashboardStats(): Promise<AdminDashboardStats> {
    try {
      const [
        userStats,
        sessionStats,
        tradeStats,
        graduationStats,
        topPerformers,
        performanceDistribution,
        featureFlagUsage
      ] = await Promise.all([
        this.getUserStatistics(),
        this.getSessionStatistics(),
        this.getTradeStatistics(),
        this.getGraduationStatistics(),
        this.getTopPerformers(),
        this.getPerformanceDistribution(),
        this.getFeatureFlagUsage(),
      ]);

      return {
        ...userStats,
        ...sessionStats,
        ...tradeStats,
        ...graduationStats,
        topPerformers,
        performanceDistribution,
        featureFlagUsage,
      };

    } catch (error) {
      console.error('Error fetching dashboard stats:', error);
      throw new Error(`Failed to fetch dashboard statistics: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get detailed information about all user sessions
   */
  async getUserSessions(filters: {
    status?: string;
    userId?: string;
    fromDate?: Date;
    toDate?: Date;
    minTradeCount?: number;
    riskFlagged?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<{ sessions: UserSessionDetails[]; total: number }> {
    try {
      const whereClause: any = {};

      if (filters.status) {
        whereClause.status = filters.status;
      }

      if (filters.userId) {
        whereClause.userId = filters.userId;
      }

      if (filters.fromDate || filters.toDate) {
        whereClause.createdAt = {};
        if (filters.fromDate) whereClause.createdAt.gte = filters.fromDate;
        if (filters.toDate) whereClause.createdAt.lte = filters.toDate;
      }

      const [sessions, total] = await Promise.all([
        this.prisma.paperTradingSession.findMany({
          where: whereClause,
          include: {
            user: { select: { id: true, name: true, email: true } },
            trades: {
              select: {
                id: true,
                executionPrice: true,
                quantity: true,
                realizedPnL: true,
                status: true,
              },
            },
            virtualPortfolio: {
              select: {
                totalPnL: true,
                balance: true,
                equity: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          take: filters.limit || 50,
          skip: filters.offset || 0,
        }),
        this.prisma.paperTradingSession.count({ where: whereClause }),
      ]);

      const sessionDetails = await Promise.all(
        sessions.map(async (session) => {
          // Calculate performance metrics
          const analytics = await this.analyticsService.generateAnalytics(
            session.userId,
            session.id
          );

          // Get graduation status
          const graduationStatus = await this.getSessionGraduationStatus(session.id);

          // Check for risk flags
          const riskFlags = this.identifyRiskFlags(session, analytics);

          // Calculate duration
          const duration = session.completedAt
            ? session.completedAt.getTime() - session.createdAt.getTime()
            : Date.now() - session.createdAt.getTime();

          return {
            sessionId: session.id,
            userId: session.userId,
            username: session.user.name || session.user.email,
            status: session.status,
            startDate: session.createdAt,
            duration: Math.round(duration / (1000 * 60 * 60)), // hours
            tradeCount: session.trades.length,
            performance: {
              totalReturn: analytics.performanceMetrics.totalReturn,
              winRate: analytics.performanceMetrics.winRate,
              sharpeRatio: analytics.performanceMetrics.sharpeRatio,
              maxDrawdown: analytics.performanceMetrics.maxDrawdown,
            },
            graduationStatus,
            riskFlags,
          };
        })
      );

      // Apply additional filters that require calculated fields
      let filteredSessions = sessionDetails;

      if (filters.minTradeCount) {
        filteredSessions = filteredSessions.filter(s => s.tradeCount >= filters.minTradeCount!);
      }

      if (filters.riskFlagged !== undefined) {
        if (filters.riskFlagged) {
          filteredSessions = filteredSessions.filter(s => s.riskFlags.length > 0);
        } else {
          filteredSessions = filteredSessions.filter(s => s.riskFlags.length === 0);
        }
      }

      return {
        sessions: filteredSessions,
        total, // Total from database count, not filtered count
      };

    } catch (error) {
      console.error('Error fetching user sessions:', error);
      throw new Error(`Failed to fetch user sessions: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get system health metrics
   */
  async getSystemHealth(): Promise<SystemHealthMetrics> {
    try {
      const startTime = Date.now();

      // Test database connection
      const dbStartTime = Date.now();
      await this.prisma.$queryRaw`SELECT 1`;
      const databaseConnectionTime = Date.now() - dbStartTime;

      // Get active connections count
      const activeConnections = await this.getActiveConnectionsCount();

      // Calculate error rate from recent logs
      const errorRate = await this.calculateErrorRate();

      // Test feature flag performance
      const flagStartTime = Date.now();
      await this.featureFlagsService.isFeatureEnabled('paper_trading_enabled', {
        userId: 'test-user',
      });
      const featureFlagLatency = Date.now() - flagStartTime;

      // Get cache hit rate (mock data - would integrate with actual cache)
      const cacheHitRate = 0.95; // 95%

      // Get system alerts
      const systemAlerts = await this.getSystemAlerts();

      const apiResponseTime = Date.now() - startTime;

      return {
        apiResponseTime,
        databaseConnectionTime,
        activeConnections,
        errorRate,
        featureFlagLatency,
        cacheHitRate,
        systemAlerts,
      };

    } catch (error) {
      console.error('Error fetching system health:', error);
      throw new Error(`Failed to fetch system health: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Suspend a user's paper trading session
   */
  async suspendUserSession(
    sessionId: string,
    adminUserId: string,
    reason: string
  ): Promise<void> {
    try {
      await this.prisma.paperTradingSession.update({
        where: { id: sessionId },
        data: {
          status: 'suspended',
          suspendedAt: new Date(),
          suspensionReason: reason,
          suspendedBy: adminUserId,
        },
      });

      // Audit the suspension
      await this.auditService.logActivity({
        userId: adminUserId,
        action: 'PAPER_TRADING_SESSION_SUSPENDED',
        details: {
          sessionId,
          reason,
          targetUserId: await this.getSessionUserId(sessionId),
        },
        ipAddress: '',
        userAgent: 'Admin Interface',
      });

    } catch (error) {
      console.error('Error suspending user session:', error);
      throw new Error(`Failed to suspend session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Reactivate a suspended session
   */
  async reactivateUserSession(
    sessionId: string,
    adminUserId: string
  ): Promise<void> {
    try {
      await this.prisma.paperTradingSession.update({
        where: { id: sessionId },
        data: {
          status: 'active',
          suspendedAt: null,
          suspensionReason: null,
          suspendedBy: null,
        },
      });

      // Audit the reactivation
      await this.auditService.logActivity({
        userId: adminUserId,
        action: 'PAPER_TRADING_SESSION_REACTIVATED',
        details: {
          sessionId,
          targetUserId: await this.getSessionUserId(sessionId),
        },
        ipAddress: '',
        userAgent: 'Admin Interface',
      });

    } catch (error) {
      console.error('Error reactivating user session:', error);
      throw new Error(`Failed to reactivate session: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update feature flag configuration
   */
  async updateFeatureFlag(
    flagKey: string,
    updates: {
      enabled?: boolean;
      rolloutPercentage?: number;
      targetAudience?: string[];
    },
    adminUserId: string
  ): Promise<void> {
    try {
      if (updates.enabled !== undefined) {
        await this.featureFlagsService.toggleFeatureFlag(flagKey, updates.enabled);
      }

      if (updates.rolloutPercentage !== undefined) {
        await this.featureFlagsService.updateRolloutPercentage(flagKey, updates.rolloutPercentage);
      }

      // Audit the change
      await this.auditService.logActivity({
        userId: adminUserId,
        action: 'FEATURE_FLAG_UPDATED',
        details: {
          flagKey,
          updates,
        },
        ipAddress: '',
        userAgent: 'Admin Interface',
      });

    } catch (error) {
      console.error('Error updating feature flag:', error);
      throw new Error(`Failed to update feature flag: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate system report
   */
  async generateSystemReport(
    reportType: 'performance' | 'usage' | 'security' | 'graduation',
    filters: {
      fromDate: Date;
      toDate: Date;
      includeUserData?: boolean;
    }
  ): Promise<{
    reportId: string;
    reportType: string;
    generatedAt: Date;
    data: any;
    summary: any;
  }> {
    try {
      const reportId = `report_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      let reportData: any;
      let summary: any;

      switch (reportType) {
        case 'performance':
          reportData = await this.generatePerformanceReport(filters);
          summary = this.summarizePerformanceReport(reportData);
          break;
        
        case 'usage':
          reportData = await this.generateUsageReport(filters);
          summary = this.summarizeUsageReport(reportData);
          break;
        
        case 'security':
          reportData = await this.generateSecurityReport(filters);
          summary = this.summarizeSecurityReport(reportData);
          break;
        
        case 'graduation':
          reportData = await this.generateGraduationReport(filters);
          summary = this.summarizeGraduationReport(reportData);
          break;
        
        default:
          throw new Error(`Unknown report type: ${reportType}`);
      }

      return {
        reportId,
        reportType,
        generatedAt: new Date(),
        data: reportData,
        summary,
      };

    } catch (error) {
      console.error('Error generating system report:', error);
      throw new Error(`Failed to generate report: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async getUserStatistics(): Promise<{ totalUsers: number; activeUsers: number }> {
    const [totalUsers, activeUsers] = await Promise.all([
      this.prisma.user.count({
        where: {
          paperTradingSessions: {
            some: {},
          },
        },
      }),
      this.prisma.user.count({
        where: {
          paperTradingSessions: {
            some: {
              status: 'active',
            },
          },
        },
      }),
    ]);

    return { totalUsers, activeUsers };
  }

  private async getSessionStatistics(): Promise<{
    totalSessions: number;
    activeSessions: number;
    averageSessionDuration: number;
  }> {
    const [totalSessions, activeSessions, completedSessions] = await Promise.all([
      this.prisma.paperTradingSession.count(),
      this.prisma.paperTradingSession.count({ where: { status: 'active' } }),
      this.prisma.paperTradingSession.findMany({
        where: { status: 'completed' },
        select: { createdAt: true, completedAt: true },
      }),
    ]);

    const averageSessionDuration = completedSessions.length > 0
      ? completedSessions.reduce((sum, session) => {
          const duration = session.completedAt!.getTime() - session.createdAt.getTime();
          return sum + duration;
        }, 0) / completedSessions.length / (1000 * 60 * 60) // Convert to hours
      : 0;

    return {
      totalSessions,
      activeSessions,
      averageSessionDuration: Math.round(averageSessionDuration),
    };
  }

  private async getTradeStatistics(): Promise<{ totalTrades: number }> {
    const totalTrades = await this.prisma.paperTrade.count();
    return { totalTrades };
  }

  private async getGraduationStatistics(): Promise<{ graduationRate: number }> {
    const [completedSessions, graduatedSessions] = await Promise.all([
      this.prisma.paperTradingSession.count({ where: { status: 'completed' } }),
      this.prisma.paperTradingSession.count({
        where: {
          status: 'completed',
          graduatedAt: { not: null },
        },
      }),
    ]);

    const graduationRate = completedSessions > 0 ? (graduatedSessions / completedSessions) * 100 : 0;
    return { graduationRate: Math.round(graduationRate) };
  }

  private async getTopPerformers(): Promise<Array<{
    userId: string;
    username: string;
    totalReturn: number;
    winRate: number;
    tradeCount: number;
  }>> {
    // This would require complex aggregation - simplified for now
    return [];
  }

  private async getPerformanceDistribution(): Promise<{
    excellent: number;
    good: number;
    average: number;
    poor: number;
  }> {
    // Mock data - would calculate actual performance distribution
    return {
      excellent: 15,
      good: 35,
      average: 40,
      poor: 10,
    };
  }

  private async getFeatureFlagUsage(): Promise<Array<{
    flagKey: string;
    totalChecks: number;
    enabledPercentage: number;
  }>> {
    // This would integrate with actual usage analytics
    return [];
  }

  private async getSessionGraduationStatus(_sessionId: string): Promise<{
    eligible: boolean;
    completedRequirements: string[];
    pendingRequirements: string[];
  }> {
    // Mock implementation - would integrate with requirements service
    return {
      eligible: false,
      completedRequirements: [],
      pendingRequirements: ['Complete minimum trades', 'Meet time requirement'],
    };
  }

  private identifyRiskFlags(session: any, analytics: any): string[] {
    const flags: string[] = [];

    // High frequency trading flag
    const avgTradesPerDay = session.trades.length / Math.max(1, 
      (Date.now() - session.createdAt.getTime()) / (1000 * 60 * 60 * 24)
    );
    if (avgTradesPerDay > 50) {
      flags.push('HIGH_FREQUENCY_TRADING');
    }

    // High risk trading flag
    if (analytics.performanceMetrics.maxDrawdown > 0.5) {
      flags.push('EXCESSIVE_DRAWDOWN');
    }

    // Suspicious performance flag
    if (analytics.performanceMetrics.winRate > 0.95 && session.trades.length > 10) {
      flags.push('SUSPICIOUS_WIN_RATE');
    }

    return flags;
  }

  private async getActiveConnectionsCount(): Promise<number> {
    // Mock implementation - would integrate with actual connection monitoring
    return Math.floor(Math.random() * 100) + 50;
  }

  private async calculateErrorRate(): Promise<number> {
    // Mock implementation - would calculate from actual error logs
    return Math.random() * 5; // 0-5% error rate
  }

  private async getSystemAlerts(): Promise<Array<{
    level: 'info' | 'warning' | 'error' | 'critical';
    message: string;
    timestamp: Date;
    component: string;
  }>> {
    // Mock implementation - would fetch actual system alerts
    return [
      {
        level: 'info',
        message: 'Paper trading system running normally',
        timestamp: new Date(),
        component: 'trading_engine',
      },
    ];
  }

  private async getSessionUserId(sessionId: string): Promise<string> {
    const session = await this.prisma.paperTradingSession.findUnique({
      where: { id: sessionId },
      select: { userId: true },
    });
    return session?.userId || '';
  }

  private async generatePerformanceReport(_filters: any): Promise<any> {
    // Mock implementation - would generate actual performance report
    return { message: 'Performance report data would go here' };
  }

  private async generateUsageReport(_filters: any): Promise<any> {
    return { message: 'Usage report data would go here' };
  }

  private async generateSecurityReport(_filters: any): Promise<any> {
    return { message: 'Security report data would go here' };
  }

  private async generateGraduationReport(_filters: any): Promise<any> {
    return { message: 'Graduation report data would go here' };
  }

  private summarizePerformanceReport(_data: any): any {
    return { summary: 'Performance report summary' };
  }

  private summarizeUsageReport(_data: any): any {
    return { summary: 'Usage report summary' };
  }

  private summarizeSecurityReport(_data: any): any {
    return { summary: 'Security report summary' };
  }

  private summarizeGraduationReport(_data: any): any {
    return { summary: 'Graduation report summary' };
  }
}

export default PaperTradingAdminService;