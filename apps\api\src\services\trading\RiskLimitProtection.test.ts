/**
 * RiskLimitProtection Service Tests
 * 
 * Comprehensive test suite for risk limit protection functionality.
 * Tests parameter protection, admin overrides, and validation.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  RiskLimitProtection, 
  type ProtectedParameter,
  type ModificationResult,
  type AdminOverride,
  type ValidationError
} from './RiskLimitProtection';

describe('RiskLimitProtection', () => {
  let protection: RiskLimitProtection;
  const testUserId = 'test-user-123';

  beforeEach(() => {
    protection = new RiskLimitProtection();
    vi.clearAllMocks();
  });

  afterEach(() => {
    protection.removeAllListeners();
  });

  describe('User Initialization', () => {
    it('should initialize user with conservative risk tolerance', () => {
      protection.initializeUser(testUserId, 'conservative');
      
      const dailyLimit = protection.getParameterValue(testUserId, 'daily_loss_limit_percentage');
      const weeklyLimit = protection.getParameterValue(testUserId, 'weekly_loss_limit_percentage');
      
      // Conservative should be 30% more restrictive (0.7x)
      expect(dailyLimit).toBe(1.4); // 2.0 * 0.7
      expect(weeklyLimit).toBe(3.5); // 5.0 * 0.7
    });

    it('should initialize user with moderate risk tolerance', () => {
      protection.initializeUser(testUserId, 'moderate');
      
      const dailyLimit = protection.getParameterValue(testUserId, 'daily_loss_limit_percentage');
      const weeklyLimit = protection.getParameterValue(testUserId, 'weekly_loss_limit_percentage');
      
      // Moderate should use default values (1.0x)
      expect(dailyLimit).toBe(2.0);
      expect(weeklyLimit).toBe(5.0);
    });

    it('should initialize user with aggressive risk tolerance', () => {
      protection.initializeUser(testUserId, 'aggressive');
      
      const dailyLimit = protection.getParameterValue(testUserId, 'daily_loss_limit_percentage');
      const maxPositionSize = protection.getParameterValue(testUserId, 'max_position_size_percentage');
      
      // Aggressive should be 50% more permissive (1.5x)
      expect(dailyLimit).toBe(3.0); // 2.0 * 1.5
      expect(maxPositionSize).toBe(15.0); // 10.0 * 1.5
    });

    it('should initialize all default protected parameters', () => {
      protection.initializeUser(testUserId, 'moderate');
      
      const allParams = protection.getAllParameters(testUserId);
      
      expect(allParams).toHaveProperty('daily_loss_limit_percentage');
      expect(allParams).toHaveProperty('weekly_loss_limit_percentage');
      expect(allParams).toHaveProperty('max_position_size_percentage');
      expect(allParams).toHaveProperty('emergency_liquidation_threshold');
      expect(allParams).toHaveProperty('stop_loss_mandatory');
      expect(allParams).toHaveProperty('minimum_stop_loss_percentage');
      expect(allParams).toHaveProperty('correlation_risk_limit');
      expect(allParams).toHaveProperty('max_daily_trades');
    });

    it('should set correct protection levels and requirements', () => {
      protection.initializeUser(testUserId, 'moderate');
      
      const allParams = protection.getAllParameters(testUserId);
      
      expect(allParams.daily_loss_limit_percentage.protectionLevel).toBe('critical');
      expect(allParams.daily_loss_limit_percentage.requiresAdminOverride).toBe(true);
      expect(allParams.correlation_risk_limit.protectionLevel).toBe('basic');
      expect(allParams.correlation_risk_limit.requiresAdminOverride).toBe(false);
    });
  });

  describe('Parameter Modification - User Requests', () => {
    beforeEach(() => {
      protection.initializeUser(testUserId, 'moderate');
    });

    it('should allow modification of basic level parameters by user', () => {
      const result = protection.attemptModification(
        testUserId,
        'correlation_risk_limit',
        0.5,
        {
          requestedBy: 'user',
          reason: 'Adjust correlation sensitivity',
          sessionId: 'session-123'
        }
      );
      
      expect(result.success).toBe(true);
      expect(result.finalValue).toBe(0.5);
      expect(result.requiresApproval).toBe(false);
      
      const updatedValue = protection.getParameterValue(testUserId, 'correlation_risk_limit');
      expect(updatedValue).toBe(0.5);
    });

    it('should require admin override for critical parameters', () => {
      const result = protection.attemptModification(
        testUserId,
        'daily_loss_limit_percentage',
        1.5,
        {
          requestedBy: 'user',
          reason: 'Want lower daily limit'
        }
      );
      
      expect(result.success).toBe(false);
      expect(result.requiresApproval).toBe(true);
      expect(result.reason).toBe('Administrative override required for this parameter change');
      expect(result.warningMessage).toContain('Override request');
      
      // Original value should remain unchanged
      const currentValue = protection.getParameterValue(testUserId, 'daily_loss_limit_percentage');
      expect(currentValue).toBe(2.0);
    });

    it('should reject out-of-range values', () => {
      const result = protection.attemptModification(
        testUserId,
        'correlation_risk_limit',
        1.5, // Max allowed is 0.9
        {
          requestedBy: 'user',
          reason: 'Set high correlation limit'
        }
      );
      
      expect(result.success).toBe(false);
      expect(result.reason).toContain('Validation failed');
      expect(result.warningMessage).toContain('Suggested value');
    });

    it('should reject invalid type values', () => {
      const result = protection.attemptModification(
        testUserId,
        'stop_loss_mandatory',
        'yes', // Should be boolean
        {
          requestedBy: 'user',
          reason: 'Enable mandatory stops'
        }
      );
      
      expect(result.success).toBe(false);
      expect(result.reason).toContain('Expected type boolean');
    });

    it('should emit modificationBlocked event for invalid requests', () => {
      const eventSpy = vi.fn();
      protection.on('modificationBlocked', eventSpy);
      
      protection.attemptModification(
        testUserId,
        'max_daily_trades',
        -5, // Invalid negative value
        {
          requestedBy: 'user',
          reason: 'Test invalid value'
        }
      );
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          attempt: expect.objectContaining({
            userId: testUserId,
            parameterName: 'max_daily_trades',
            requestedValue: -5
          }),
          validationResult: expect.objectContaining({
            isValid: false,
            errors: expect.arrayContaining([
              expect.objectContaining({
                errorType: 'out_of_range'
              })
            ])
          })
        })
      );
    });

    it('should emit parameterModified event for successful changes', () => {
      const eventSpy = vi.fn();
      protection.on('parameterModified', eventSpy);
      
      protection.attemptModification(
        testUserId,
        'max_daily_trades',
        25,
        {
          requestedBy: 'user',
          reason: 'Reduce daily trade limit'
        }
      );
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          parameterName: 'max_daily_trades',
          oldValue: 50,
          newValue: 25
        })
      );
    });
  });

  describe('Parameter Modification - Admin Requests', () => {
    beforeEach(() => {
      protection.initializeUser(testUserId, 'moderate');
    });

    it('should allow admin to modify critical parameters directly', () => {
      const result = protection.attemptModification(
        testUserId,
        'daily_loss_limit_percentage',
        1.5,
        {
          requestedBy: 'admin',
          reason: 'Risk assessment adjustment',
          sessionId: 'admin-session-456'
        }
      );
      
      expect(result.success).toBe(true);
      expect(result.finalValue).toBe(1.5);
      expect(result.requiresApproval).toBe(false);
      
      const updatedValue = protection.getParameterValue(testUserId, 'daily_loss_limit_percentage');
      expect(updatedValue).toBe(1.5);
    });

    it('should still validate range for admin requests', () => {
      const result = protection.attemptModification(
        testUserId,
        'daily_loss_limit_percentage',
        60.0, // Exceeds max range of 10.0
        {
          requestedBy: 'admin',
          reason: 'Emergency override'
        }
      );
      
      expect(result.success).toBe(false);
      expect(result.reason).toContain('Validation failed');
    });

    it('should allow system modifications without restrictions', () => {
      const result = protection.attemptModification(
        testUserId,
        'emergency_liquidation_threshold',
        15.0,
        {
          requestedBy: 'system',
          reason: 'Automatic market condition adjustment'
        }
      );
      
      expect(result.success).toBe(true);
      expect(result.finalValue).toBe(15.0);
    });
  });

  describe('Admin Override System', () => {
    beforeEach(() => {
      protection.initializeUser(testUserId, 'moderate');
    });

    it('should create override request when user modifies critical parameter', () => {
      const eventSpy = vi.fn();
      protection.on('overrideRequested', eventSpy);
      
      protection.attemptModification(
        testUserId,
        'weekly_loss_limit_percentage',
        3.0,
        {
          requestedBy: 'user',
          reason: 'Need lower weekly limit for conservative trading'
        }
      );
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          parameterName: 'weekly_loss_limit_percentage',
          originalValue: 5.0,
          overrideValue: 3.0,
          reason: 'Need lower weekly limit for conservative trading',
          status: 'pending'
        })
      );
    });

    it('should allow direct override request', () => {
      const overrideId = protection.requestAdminOverride(
        testUserId,
        'emergency_liquidation_threshold',
        8.0,
        'User needs more aggressive liquidation threshold',
        48 // 48 hours expiration
      );
      
      expect(overrideId).toBeDefined();
      expect(overrideId).toMatch(/^override_/);
      
      const pendingOverrides = protection.getPendingOverrides();
      expect(pendingOverrides.length).toBe(1);
      expect(pendingOverrides[0].userId).toBe(testUserId);
      expect(pendingOverrides[0].parameterName).toBe('emergency_liquidation_threshold');
      expect(pendingOverrides[0].overrideValue).toBe(8.0);
    });

    it('should approve override and apply changes', () => {
      const overrideId = protection.requestAdminOverride(
        testUserId,
        'max_position_size_percentage',
        20.0,
        'Experienced trader needs higher position sizes'
      );
      
      const result = protection.approveAdminOverride(overrideId, 'admin-john');
      
      expect(result.success).toBe(true);
      expect(result.finalValue).toBe(20.0);
      
      const updatedValue = protection.getParameterValue(testUserId, 'max_position_size_percentage');
      expect(updatedValue).toBe(20.0);
      
      // Check override status
      const pendingOverrides = protection.getPendingOverrides();
      expect(pendingOverrides.length).toBe(0); // Should be approved, not pending
    });

    it('should emit overrideApproved event', () => {
      const eventSpy = vi.fn();
      protection.on('overrideApproved', eventSpy);
      
      const overrideId = protection.requestAdminOverride(
        testUserId,
        'minimum_stop_loss_percentage',
        0.8,
        'Trader needs tighter stops'
      );
      
      protection.approveAdminOverride(overrideId, 'admin-jane');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          override: expect.objectContaining({
            userId: testUserId,
            parameterName: 'minimum_stop_loss_percentage',
            status: 'approved',
            reviewedBy: 'admin-jane'
          }),
          approvedBy: 'admin-jane'
        })
      );
    });

    it('should reject approval of non-existent override', () => {
      expect(() => {
        protection.approveAdminOverride('non-existent-override', 'admin-test');
      }).toThrow('Override request non-existent-override not found');
    });

    it('should reject approval of already processed override', () => {
      const overrideId = protection.requestAdminOverride(
        testUserId,
        'correlation_risk_limit',
        0.4,
        'Test override'
      );
      
      protection.approveAdminOverride(overrideId, 'admin-1');
      
      expect(() => {
        protection.approveAdminOverride(overrideId, 'admin-2');
      }).toThrow('is not pending approval');
    });

    it('should handle expired override requests', () => {
      vi.useFakeTimers();
      
      const now = new Date();
      vi.setSystemTime(now);
      
      const overrideId = protection.requestAdminOverride(
        testUserId,
        'daily_loss_limit_percentage',
        1.0,
        'Test expiration',
        1 // 1 hour expiration
      );
      
      // Fast forward past expiration
      vi.advanceTimersByTime(2 * 60 * 60 * 1000); // 2 hours
      
      expect(() => {
        protection.approveAdminOverride(overrideId, 'admin-late');
      }).toThrow('has expired');
      
      vi.useRealTimers();
    });
  });

  describe('Emergency Mode', () => {
    beforeEach(() => {
      protection.initializeUser(testUserId, 'moderate');
    });

    it('should enable emergency mode', () => {
      const eventSpy = vi.fn();
      protection.on('emergencyModeEnabled', eventSpy);
      
      protection.enableEmergencyMode(testUserId, 'admin-emergency', 'Market crash response');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          enabledBy: 'admin-emergency',
          reason: 'Market crash response'
        })
      );
      
      // Check audit trail
      const audit = protection.getAuditTrail(testUserId);
      const emergencyEntry = audit.find(entry => 
        entry.action === 'parameter_change' && 
        entry.parameterName === 'emergency_mode'
      );
      
      expect(emergencyEntry).toBeDefined();
      expect(emergencyEntry!.details.enabled).toBe(true);
    });

    it('should disable emergency mode', () => {
      const eventSpy = vi.fn();
      protection.on('emergencyModeDisabled', eventSpy);
      
      protection.enableEmergencyMode(testUserId, 'admin-1', 'Enable for test');
      protection.disableEmergencyMode(testUserId, 'admin-2');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          disabledBy: 'admin-2'
        })
      );
    });

    it('should throw error for uninitialized user', () => {
      expect(() => {
        protection.enableEmergencyMode('uninitialized-user', 'admin', 'Test');
      }).toThrow('not initialized for risk protection');
    });
  });

  describe('Audit Trail', () => {
    beforeEach(() => {
      protection.initializeUser(testUserId, 'moderate');
    });

    it('should record parameter changes in audit trail', () => {
      protection.attemptModification(
        testUserId,
        'max_daily_trades',
        30,
        {
          requestedBy: 'user',
          reason: 'Reduce trading frequency',
          sessionId: 'session-789',
          ipAddress: '*************'
        }
      );
      
      const audit = protection.getAuditTrail(testUserId);
      const changeEntry = audit.find(entry => 
        entry.action === 'parameter_change' && 
        entry.parameterName === 'max_daily_trades'
      );
      
      expect(changeEntry).toBeDefined();
      expect(changeEntry!.userId).toBe(testUserId);
      expect(changeEntry!.parameterName).toBe('max_daily_trades');
      expect(changeEntry!.details.oldValue).toBe(50);
      expect(changeEntry!.details.newValue).toBe(30);
      expect(changeEntry!.sessionId).toBe('session-789');
      expect(changeEntry!.ipAddress).toBe('*************');
    });

    it('should record blocked modifications', () => {
      protection.attemptModification(
        testUserId,
        'correlation_risk_limit',
        1.2, // Invalid value
        {
          requestedBy: 'user',
          reason: 'Test invalid value'
        }
      );
      
      const audit = protection.getAuditTrail(testUserId);
      const blockedEntry = audit.find(entry => entry.action === 'modification_blocked');
      
      expect(blockedEntry).toBeDefined();
      expect(blockedEntry!.details.validationErrors).toBeDefined();
      expect(blockedEntry!.details.validationErrors.length).toBeGreaterThan(0);
    });

    it('should record override requests and approvals', () => {
      const overrideId = protection.requestAdminOverride(
        testUserId,
        'daily_loss_limit_percentage',
        1.5,
        'Test override audit'
      );
      
      protection.approveAdminOverride(overrideId, 'admin-audit-test');
      
      const audit = protection.getAuditTrail(testUserId);
      
      const requestEntry = audit.find(entry => entry.action === 'override_requested');
      const approvalEntry = audit.find(entry => entry.action === 'override_approved');
      
      expect(requestEntry).toBeDefined();
      expect(approvalEntry).toBeDefined();
      expect(requestEntry!.details.overrideId).toBe(overrideId);
      expect(approvalEntry!.details.approvedBy).toBe('admin-audit-test');
    });

    it('should filter audit trail by user', () => {
      const otherUserId = 'other-user-456';
      protection.initializeUser(otherUserId, 'conservative');
      
      protection.attemptModification(testUserId, 'max_daily_trades', 25, { requestedBy: 'user' });
      protection.attemptModification(otherUserId, 'max_daily_trades', 15, { requestedBy: 'user' });
      
      const user1Audit = protection.getAuditTrail(testUserId);
      const user2Audit = protection.getAuditTrail(otherUserId);
      
      expect(user1Audit.every(entry => entry.userId === testUserId)).toBe(true);
      expect(user2Audit.every(entry => entry.userId === otherUserId)).toBe(true);
    });

    it('should limit audit trail results', () => {
      // Generate multiple audit entries
      for (let i = 0; i < 10; i++) {
        protection.attemptModification(
          testUserId,
          'max_daily_trades',
          40 + i,
          { requestedBy: 'user', reason: `Change ${i}` }
        );
      }
      
      const limitedAudit = protection.getAuditTrail(testUserId, 5);
      expect(limitedAudit.length).toBeLessThanOrEqual(5);
    });

    it('should emit auditEntryCreated event', () => {
      const eventSpy = vi.fn();
      protection.on('auditEntryCreated', eventSpy);
      
      protection.attemptModification(
        testUserId,
        'correlation_risk_limit',
        0.6,
        { requestedBy: 'user', reason: 'Test audit event' }
      );
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          action: 'parameter_change',
          parameterName: 'correlation_risk_limit'
        })
      );
    });
  });

  describe('Parameter Retrieval', () => {
    beforeEach(() => {
      protection.initializeUser(testUserId, 'moderate');
    });

    it('should retrieve single parameter value', () => {
      const value = protection.getParameterValue(testUserId, 'daily_loss_limit_percentage');
      expect(value).toBe(2.0);
    });

    it('should retrieve all parameters with metadata', () => {
      const allParams = protection.getAllParameters(testUserId);
      
      expect(allParams).toHaveProperty('daily_loss_limit_percentage');
      expect(allParams.daily_loss_limit_percentage).toHaveProperty('value', 2.0);
      expect(allParams.daily_loss_limit_percentage).toHaveProperty('category', 'risk_management');
      expect(allParams.daily_loss_limit_percentage).toHaveProperty('protectionLevel', 'critical');
      expect(allParams.daily_loss_limit_percentage).toHaveProperty('requiresAdminOverride', true);
      expect(allParams.daily_loss_limit_percentage).toHaveProperty('lastModified');
      expect(allParams.daily_loss_limit_percentage).toHaveProperty('modifiedBy', 'system');
    });

    it('should throw error for uninitialized user', () => {
      expect(() => {
        protection.getParameterValue('uninitialized-user', 'daily_loss_limit_percentage');
      }).toThrow('not initialized for risk protection');
    });

    it('should throw error for non-existent parameter', () => {
      expect(() => {
        protection.getParameterValue(testUserId, 'non_existent_parameter');
      }).toThrow('Parameter non_existent_parameter not found');
    });
  });

  describe('Validation Logic', () => {
    beforeEach(() => {
      protection.initializeUser(testUserId, 'moderate');
    });

    it('should detect critical parameter protection violation', () => {
      // Try to change critical parameter by >50%
      const result = protection.attemptModification(
        testUserId,
        'daily_loss_limit_percentage',
        0.5, // 75% decrease from 2.0
        { requestedBy: 'user', reason: 'Drastic reduction' }
      );
      
      expect(result.success).toBe(false);
      expect(result.reason).toContain('Administrative override required');
    });

    it('should validate parameter ranges correctly', () => {
      // Test minimum boundary
      const minResult = protection.attemptModification(
        testUserId,
        'correlation_risk_limit',
        0.2, // Below minimum of 0.3
        { requestedBy: 'user', reason: 'Test min boundary' }
      );
      
      expect(minResult.success).toBe(false);
      expect(minResult.reason).toContain('below minimum allowed value');
      
      // Test maximum boundary  
      const maxResult = protection.attemptModification(
        testUserId,
        'correlation_risk_limit',
        0.95, // Above maximum of 0.9
        { requestedBy: 'user', reason: 'Test max boundary' }
      );
      
      expect(maxResult.success).toBe(false);
      expect(maxResult.reason).toContain('above maximum allowed value');
    });

    it('should provide suggested values for invalid ranges', () => {
      const result = protection.attemptModification(
        testUserId,
        'minimum_stop_loss_percentage',
        0.1, // Below minimum of 0.5
        { requestedBy: 'user', reason: 'Very tight stop' }
      );
      
      expect(result.success).toBe(false);
      expect(result.warningMessage).toContain('Suggested value: 0.5');
    });

    it('should validate types correctly', () => {
      const result = protection.attemptModification(
        testUserId,
        'max_daily_trades',
        '25', // String instead of number
        { requestedBy: 'user', reason: 'String value test' }
      );
      
      expect(result.success).toBe(false);
      expect(result.reason).toContain('Expected type number');
    });
  });

  describe('Configuration Validation', () => {
    it('should validate correct parameter configuration', () => {
      const validParams: ProtectedParameter[] = [
        {
          name: 'test_param',
          category: 'risk_management',
          currentValue: 5.0,
          defaultValue: 5.0,
          allowedRange: { min: 1.0, max: 10.0 },
          requiresAdminOverride: true,
          protectionLevel: 'enhanced'
        }
      ];
      
      const result = RiskLimitProtection.validateConfiguration(validParams);
      expect(result.isValid).toBe(true);
      expect(result.errors.length).toBe(0);
    });

    it('should reject empty parameter names', () => {
      const invalidParams: ProtectedParameter[] = [
        {
          name: '',
          category: 'risk_management',
          currentValue: 5.0,
          defaultValue: 5.0,
          requiresAdminOverride: true,
          protectionLevel: 'basic'
        }
      ];
      
      const result = RiskLimitProtection.validateConfiguration(invalidParams);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Parameter name cannot be empty');
    });

    it('should reject invalid categories', () => {
      const invalidParams: ProtectedParameter[] = [
        {
          name: 'test_param',
          category: 'invalid_category' as any,
          currentValue: 5.0,
          defaultValue: 5.0,
          requiresAdminOverride: true,
          protectionLevel: 'basic'
        }
      ];
      
      const result = RiskLimitProtection.validateConfiguration(invalidParams);
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Invalid category');
    });

    it('should reject invalid protection levels', () => {
      const invalidParams: ProtectedParameter[] = [
        {
          name: 'test_param',
          category: 'risk_management',
          currentValue: 5.0,
          defaultValue: 5.0,
          requiresAdminOverride: true,
          protectionLevel: 'invalid_level' as any
        }
      ];
      
      const result = RiskLimitProtection.validateConfiguration(invalidParams);
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Invalid protection level');
    });

    it('should reject default values outside allowed range', () => {
      const invalidParams: ProtectedParameter[] = [
        {
          name: 'test_param',
          category: 'risk_management',
          currentValue: 15.0,
          defaultValue: 15.0, // Outside range of 1.0-10.0
          allowedRange: { min: 1.0, max: 10.0 },
          requiresAdminOverride: true,
          protectionLevel: 'basic'
        }
      ];
      
      const result = RiskLimitProtection.validateConfiguration(invalidParams);
      expect(result.isValid).toBe(false);
      expect(result.errors[0]).toContain('Default value for test_param is outside allowed range');
    });
  });

  describe('Error Handling', () => {
    it('should handle uninitialized user gracefully', () => {
      expect(() => {
        protection.attemptModification(
          'uninitialized-user',
          'daily_loss_limit_percentage',
          1.0,
          { requestedBy: 'user', reason: 'Test' }
        );
      }).toThrow('not initialized for risk protection');
    });

    it('should handle non-existent parameters gracefully', () => {
      protection.initializeUser(testUserId, 'moderate');
      
      expect(() => {
        protection.attemptModification(
          testUserId,
          'non_existent_parameter',
          1.0,
          { requestedBy: 'user', reason: 'Test' }
        );
      }).toThrow('Parameter non_existent_parameter not found');
    });

    it('should handle invalid override operations', () => {
      protection.initializeUser(testUserId, 'moderate');
      
      expect(() => {
        protection.requestAdminOverride('uninitialized-user', 'daily_loss_limit_percentage', 1.0, 'Test');
      }).toThrow('not found');
      
      expect(() => {
        protection.approveAdminOverride('invalid-override-id', 'admin');
      }).toThrow('not found');
    });

    it('should handle emergency mode operations on uninitialized user', () => {
      expect(() => {
        protection.enableEmergencyMode('uninitialized-user', 'admin', 'Test');
      }).toThrow('not initialized');
      
      expect(() => {
        protection.disableEmergencyMode('uninitialized-user', 'admin');
      }).toThrow('not initialized');
    });
  });

  describe('Complex Scenarios', () => {
    beforeEach(() => {
      protection.initializeUser(testUserId, 'moderate');
    });

    it('should handle multiple override requests for same parameter', () => {
      const override1 = protection.requestAdminOverride(
        testUserId,
        'daily_loss_limit_percentage',
        1.5,
        'First override request'
      );
      
      const override2 = protection.requestAdminOverride(
        testUserId,
        'daily_loss_limit_percentage',
        3.0,
        'Second override request'
      );
      
      const pendingOverrides = protection.getPendingOverrides();
      expect(pendingOverrides.length).toBe(2);
      
      // Approve first override
      protection.approveAdminOverride(override1, 'admin-1');
      expect(protection.getParameterValue(testUserId, 'daily_loss_limit_percentage')).toBe(1.5);
      
      // Second override should still be pending for the original value (2.0), not the modified value (1.5)
      const stillPending = protection.getPendingOverrides();
      expect(stillPending.length).toBe(1);
      expect(stillPending[0].id).toBe(override2);
      expect(stillPending[0].originalValue).toBe(2.0); // Original default value
    });

    it('should maintain audit trail across multiple operations', () => {
      // Series of operations
      protection.attemptModification(testUserId, 'max_daily_trades', 30, { requestedBy: 'user' });
      protection.requestAdminOverride(testUserId, 'daily_loss_limit_percentage', 1.0, 'Reduce limit');
      protection.enableEmergencyMode(testUserId, 'admin', 'Market volatility');
      
      const audit = protection.getAuditTrail(testUserId);
      
      // Should have entries for: initialization, parameter change, override request, emergency mode
      expect(audit.length).toBeGreaterThanOrEqual(4);
      
      const actionTypes = audit.map(entry => entry.action);
      expect(actionTypes).toContain('parameter_change');
      expect(actionTypes).toContain('override_requested');
    });

    it('should handle concurrent parameter modifications correctly', () => {
      // Modify multiple parameters
      const result1 = protection.attemptModification(
        testUserId,
        'max_daily_trades',
        25,
        { requestedBy: 'user', sessionId: 'session-1' }
      );
      
      const result2 = protection.attemptModification(
        testUserId,
        'correlation_risk_limit',
        0.6,
        { requestedBy: 'user', sessionId: 'session-2' }
      );
      
      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      
      expect(protection.getParameterValue(testUserId, 'max_daily_trades')).toBe(25);
      expect(protection.getParameterValue(testUserId, 'correlation_risk_limit')).toBe(0.6);
    });
  });
});