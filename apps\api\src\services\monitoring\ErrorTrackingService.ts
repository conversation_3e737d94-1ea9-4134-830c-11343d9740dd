import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';

// Types for error tracking
interface ErrorEvent {
  id: string;
  instanceId: string;
  timestamp: Date;
  level: 'error' | 'warning' | 'info' | 'debug';
  message: string;
  stack?: string;
  context?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  component: string;
  tags?: string[];
}

interface ErrorAggregation {
  errorHash: string;
  count: number;
  firstSeen: Date;
  lastSeen: Date;
  message: string;
  stack?: string;
  component: string;
  level: string;
  instances: string[];
  userCount: number;
  tags: string[];
}

interface LogEntry {
  timestamp: Date;
  level: string;
  message: string;
  instanceId: string;
  component: string;
  metadata?: Record<string, any>;
}

/**
 * Error Tracking and Logging Aggregation Service
 * 
 * Centralizes error tracking, logging, and aggregation for production deployment
 * Integrates with external services like Sentry, LogRocket, or Datadog
 */
export class ErrorTrackingService extends EventEmitter {
  private prisma: PrismaClient;
  private instanceId: string;
  private isProduction: boolean;
  private errorAggregations: Map<string, ErrorAggregation> = new Map();
  private logBuffer: LogEntry[] = [];
  
  // Configuration
  private readonly MAX_LOG_BUFFER_SIZE = 1000;
  private readonly ERROR_AGGREGATION_WINDOW = 5 * 60 * 1000; // 5 minutes
  private readonly LOG_FLUSH_INTERVAL = 30 * 1000; // 30 seconds
  
  // External services configuration
  private sentryDsn?: string;
  private logRocketAppId?: string;
  private webhookUrl?: string;
  private slackWebhookUrl?: string;
  
  private flushInterval: NodeJS.Timeout | null = null;

  constructor(prisma: PrismaClient) {
    super();
    
    this.prisma = prisma;
    this.instanceId = process.env.INSTANCE_ID || 'unknown';
    this.isProduction = process.env.NODE_ENV === 'production';
    
    // External services configuration
    this.sentryDsn = process.env.SENTRY_DSN;
    this.logRocketAppId = process.env.LOGROCKET_APP_ID;
    this.webhookUrl = process.env.ERROR_WEBHOOK_URL;
    this.slackWebhookUrl = process.env.SLACK_WEBHOOK_URL;
    
    this.setupGlobalErrorHandlers();
    this.startLogFlushing();
    
    this.emit('initialized', { instanceId: this.instanceId });
  }

  /**
   * Track an error event
   */
  public async trackError(
    error: Error | string,
    context: {
      component: string;
      userId?: string;
      sessionId?: string;
      requestId?: string;
      level?: 'error' | 'warning' | 'info' | 'debug';
      tags?: string[];
      metadata?: Record<string, any>;
    }
  ): Promise<string> {
    const errorId = this.generateErrorId();
    const timestamp = new Date();
    
    const errorEvent: ErrorEvent = {
      id: errorId,
      instanceId: this.instanceId,
      timestamp,
      level: context.level || 'error',
      message: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined,
      context: context.metadata,
      userId: context.userId,
      sessionId: context.sessionId,
      requestId: context.requestId,
      component: context.component,
      tags: context.tags || []
    };
    
    // Store error in database
    await this.storeError(errorEvent);
    
    // Aggregate error for pattern detection
    await this.aggregateError(errorEvent);
    
    // Send to external services
    await this.sendToExternalServices(errorEvent);
    
    // Emit error event for other services
    this.emit('errorTracked', errorEvent);
    
    // Check if this is a critical error that needs immediate attention
    if (this.isCriticalError(errorEvent)) {
      await this.handleCriticalError(errorEvent);
    }
    
    return errorId;
  }

  /**
   * Log an event
   */
  public log(
    level: 'error' | 'warning' | 'info' | 'debug',
    message: string,
    component: string,
    metadata?: Record<string, any>
  ): void {
    const logEntry: LogEntry = {
      timestamp: new Date(),
      level,
      message,
      instanceId: this.instanceId,
      component,
      metadata
    };
    
    // Add to buffer for batch processing
    this.logBuffer.push(logEntry);
    
    // Emit log event immediately for real-time processing
    this.emit('logEntry', logEntry);
    
    // If buffer is full, flush immediately
    if (this.logBuffer.length >= this.MAX_LOG_BUFFER_SIZE) {
      this.flushLogs();
    }
    
    // Console log in development
    if (!this.isProduction) {
      const timestamp = logEntry.timestamp.toISOString();
      console.log(`[${timestamp}] ${level.toUpperCase()} [${component}] ${message}`, metadata || '');
    }
  }

  /**
   * Get error statistics
   */
  public async getErrorStatistics(hours: number = 24): Promise<any> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hours);
    
    const [errorCounts, errorsByComponent, recentErrors] = await Promise.all([
      // Error counts by level
      this.prisma.errorLog.groupBy({
        by: ['level'],
        where: {
          instanceId: this.instanceId,
          timestamp: { gte: cutoffDate }
        },
        _count: {
          id: true
        }
      }),
      
      // Errors by component
      this.prisma.errorLog.groupBy({
        by: ['component'],
        where: {
          instanceId: this.instanceId,
          timestamp: { gte: cutoffDate }
        },
        _count: {
          id: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 10
      }),
      
      // Recent critical errors
      this.prisma.errorLog.findMany({
        where: {
          instanceId: this.instanceId,
          level: 'error',
          timestamp: { gte: cutoffDate }
        },
        orderBy: { timestamp: 'desc' },
        take: 20,
        select: {
          id: true,
          timestamp: true,
          message: true,
          component: true,
          userId: true
        }
      })
    ]);
    
    return {
      instanceId: this.instanceId,
      period: `${hours} hours`,
      errorCounts: errorCounts.reduce((acc, item) => {
        acc[item.level] = item._count.id;
        return acc;
      }, {} as Record<string, number>),
      errorsByComponent: errorsByComponent.map(item => ({
        component: item.component,
        count: item._count.id
      })),
      recentErrors,
      aggregations: Array.from(this.errorAggregations.values()).slice(0, 10)
    };
  }

  /**
   * Get logs with filtering
   */
  public async getLogs(options: {
    level?: string;
    component?: string;
    hours?: number;
    limit?: number;
  } = {}): Promise<LogEntry[]> {
    const {
      level,
      component,
      hours = 24,
      limit = 100
    } = options;
    
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hours);
    
    const whereClause: any = {
      instanceId: this.instanceId,
      timestamp: { gte: cutoffDate }
    };
    
    if (level) whereClause.level = level;
    if (component) whereClause.component = component;
    
    const logs = await this.prisma.logEntry.findMany({
      where: whereClause,
      orderBy: { timestamp: 'desc' },
      take: limit
    });
    
    return logs.map(log => ({
      timestamp: log.timestamp,
      level: log.level,
      message: log.message,
      instanceId: log.instanceId,
      component: log.component,
      metadata: log.metadata as Record<string, any> || undefined
    }));
  }

  /**
   * Store error in database
   */
  private async storeError(errorEvent: ErrorEvent): Promise<void> {
    try {
      await this.prisma.errorLog.create({
        data: {
          id: errorEvent.id,
          instanceId: errorEvent.instanceId,
          timestamp: errorEvent.timestamp,
          level: errorEvent.level,
          message: errorEvent.message,
          stack: errorEvent.stack,
          context: errorEvent.context as any,
          userId: errorEvent.userId,
          sessionId: errorEvent.sessionId,
          requestId: errorEvent.requestId,
          component: errorEvent.component,
          tags: errorEvent.tags,
          createdAt: new Date()
        }
      });
    } catch (error) {
      console.error('[ErrorTracking] Failed to store error:', error);
    }
  }

  /**
   * Aggregate errors for pattern detection
   */
  private async aggregateError(errorEvent: ErrorEvent): Promise<void> {
    const errorHash = this.generateErrorHash(errorEvent);
    
    let aggregation = this.errorAggregations.get(errorHash);
    
    if (!aggregation) {
      aggregation = {
        errorHash,
        count: 0,
        firstSeen: errorEvent.timestamp,
        lastSeen: errorEvent.timestamp,
        message: errorEvent.message,
        stack: errorEvent.stack,
        component: errorEvent.component,
        level: errorEvent.level,
        instances: [],
        userCount: 0,
        tags: [...(errorEvent.tags || [])]
      };
    }
    
    // Update aggregation
    aggregation.count++;
    aggregation.lastSeen = errorEvent.timestamp;
    
    if (!aggregation.instances.includes(this.instanceId)) {
      aggregation.instances.push(this.instanceId);
    }
    
    if (errorEvent.userId) {
      // This is a simplified user count - in production you'd track unique users
      aggregation.userCount++;
    }
    
    this.errorAggregations.set(errorHash, aggregation);
    
    // Clean up old aggregations
    this.cleanupOldAggregations();
    
    // Check for error patterns that need attention
    if (aggregation.count >= 5 && aggregation.count % 5 === 0) {
      this.emit('errorPattern', aggregation);
      await this.handleErrorPattern(aggregation);
    }
  }

  /**
   * Send error to external services
   */
  private async sendToExternalServices(errorEvent: ErrorEvent): Promise<void> {
    // Send to Sentry
    if (this.sentryDsn) {
      await this.sendToSentry(errorEvent).catch(console.error);
    }
    
    // Send to LogRocket
    if (this.logRocketAppId) {
      await this.sendToLogRocket(errorEvent).catch(console.error);
    }
    
    // Send to webhook
    if (this.webhookUrl) {
      await this.sendToWebhook(errorEvent).catch(console.error);
    }
  }

  /**
   * Send error to Sentry
   */
  private async sendToSentry(errorEvent: ErrorEvent): Promise<void> {
    try {
      const sentryPayload = {
        message: errorEvent.message,
        level: errorEvent.level,
        timestamp: errorEvent.timestamp.toISOString(),
        extra: {
          instanceId: errorEvent.instanceId,
          component: errorEvent.component,
          context: errorEvent.context,
          tags: errorEvent.tags
        },
        user: errorEvent.userId ? { id: errorEvent.userId } : undefined,
        request: errorEvent.requestId ? { id: errorEvent.requestId } : undefined
      };
      
      // In a real implementation, you would use the Sentry SDK
      // await Sentry.captureException(sentryPayload);
      
      console.log('[ErrorTracking] Would send to Sentry:', sentryPayload);
    } catch (error) {
      console.error('[ErrorTracking] Failed to send to Sentry:', error);
    }
  }

  /**
   * Send error to LogRocket
   */
  private async sendToLogRocket(errorEvent: ErrorEvent): Promise<void> {
    try {
      const logRocketPayload = {
        message: errorEvent.message,
        level: errorEvent.level,
        timestamp: errorEvent.timestamp,
        metadata: {
          instanceId: errorEvent.instanceId,
          component: errorEvent.component,
          context: errorEvent.context
        }
      };
      
      // In a real implementation, you would use the LogRocket SDK
      // LogRocket.captureException(logRocketPayload);
      
      console.log('[ErrorTracking] Would send to LogRocket:', logRocketPayload);
    } catch (error) {
      console.error('[ErrorTracking] Failed to send to LogRocket:', error);
    }
  }

  /**
   * Send error to webhook
   */
  private async sendToWebhook(errorEvent: ErrorEvent): Promise<void> {
    try {
      const webhookPayload = {
        timestamp: errorEvent.timestamp,
        instanceId: errorEvent.instanceId,
        level: errorEvent.level,
        message: errorEvent.message,
        component: errorEvent.component,
        context: errorEvent.context,
        tags: errorEvent.tags
      };
      
      const response = await fetch(this.webhookUrl!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'GoldDaddy-ErrorTracking/1.0'
        },
        body: JSON.stringify(webhookPayload)
      });
      
      if (!response.ok) {
        throw new Error(`Webhook responded with status: ${response.status}`);
      }
      
    } catch (error) {
      console.error('[ErrorTracking] Failed to send to webhook:', error);
    }
  }

  /**
   * Handle critical errors that need immediate attention
   */
  private async handleCriticalError(errorEvent: ErrorEvent): Promise<void> {
    this.emit('criticalError', errorEvent);
    
    // Send Slack notification for critical errors
    if (this.slackWebhookUrl) {
      await this.sendSlackNotification(
        `🚨 Critical Error in ${errorEvent.component}`,
        `**Instance:** ${errorEvent.instanceId}\n**Error:** ${errorEvent.message}\n**Time:** ${errorEvent.timestamp.toISOString()}`,
        'danger'
      ).catch(console.error);
    }
    
    // Store as alert in database
    await this.prisma.alert.create({
      data: {
        instanceId: this.instanceId,
        alertType: 'CRITICAL_ERROR',
        severity: 'CRITICAL',
        message: `Critical error in ${errorEvent.component}: ${errorEvent.message}`,
        metadata: JSON.stringify(errorEvent),
        createdAt: new Date()
      }
    }).catch(console.error);
  }

  /**
   * Handle error patterns that indicate systemic issues
   */
  private async handleErrorPattern(aggregation: ErrorAggregation): Promise<void> {
    this.emit('errorPattern', aggregation);
    
    // Send notification about error pattern
    if (this.slackWebhookUrl && aggregation.count >= 10) {
      await this.sendSlackNotification(
        `⚠️ Error Pattern Detected`,
        `**Component:** ${aggregation.component}\n**Error:** ${aggregation.message}\n**Count:** ${aggregation.count}\n**Instances:** ${aggregation.instances.join(', ')}`,
        'warning'
      ).catch(console.error);
    }
  }

  /**
   * Send Slack notification
   */
  private async sendSlackNotification(
    title: string,
    message: string,
    color: 'good' | 'warning' | 'danger' = 'warning'
  ): Promise<void> {
    try {
      const slackPayload = {
        attachments: [
          {
            color,
            title,
            text: message,
            timestamp: Math.floor(Date.now() / 1000),
            fields: [
              {
                title: 'Instance',
                value: this.instanceId,
                short: true
              },
              {
                title: 'Environment',
                value: process.env.NODE_ENV || 'unknown',
                short: true
              }
            ]
          }
        ]
      };
      
      const response = await fetch(this.slackWebhookUrl!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(slackPayload)
      });
      
      if (!response.ok) {
        throw new Error(`Slack webhook responded with status: ${response.status}`);
      }
      
    } catch (error) {
      console.error('[ErrorTracking] Failed to send Slack notification:', error);
    }
  }

  /**
   * Setup global error handlers
   */
  private setupGlobalErrorHandlers(): void {
    // Uncaught exceptions
    process.on('uncaughtException', (error) => {
      this.trackError(error, {
        component: 'global',
        level: 'error',
        tags: ['uncaught-exception']
      }).finally(() => {
        if (this.isProduction) {
          process.exit(1);
        }
      });
    });
    
    // Unhandled promise rejections
    process.on('unhandledRejection', (reason) => {
      this.trackError(reason as Error, {
        component: 'global',
        level: 'error',
        tags: ['unhandled-rejection']
      });
    });
    
    // Warning events
    process.on('warning', (warning) => {
      this.log('warning', warning.message, 'global', {
        name: warning.name,
        stack: warning.stack
      });
    });
  }

  /**
   * Start log flushing interval
   */
  private startLogFlushing(): void {
    this.flushInterval = setInterval(() => {
      this.flushLogs();
    }, this.LOG_FLUSH_INTERVAL);
  }

  /**
   * Flush logs to database
   */
  private async flushLogs(): Promise<void> {
    if (this.logBuffer.length === 0) return;
    
    const logsToFlush = this.logBuffer.splice(0);
    
    try {
      await this.prisma.logEntry.createMany({
        data: logsToFlush.map(log => ({
          timestamp: log.timestamp,
          level: log.level,
          message: log.message,
          instanceId: log.instanceId,
          component: log.component,
          metadata: log.metadata as any,
          createdAt: new Date()
        }))
      });
      
      this.emit('logsFlushed', { count: logsToFlush.length });
      
    } catch (error) {
      console.error('[ErrorTracking] Failed to flush logs:', error);
      // Put logs back in buffer to retry
      this.logBuffer.unshift(...logsToFlush);
    }
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate error hash for aggregation
   */
  private generateErrorHash(errorEvent: ErrorEvent): string {
    const hashInput = `${errorEvent.message}_${errorEvent.component}_${errorEvent.level}`;
    return Buffer.from(hashInput).toString('base64').substr(0, 20);
  }

  /**
   * Check if error is critical
   */
  private isCriticalError(errorEvent: ErrorEvent): boolean {
    const criticalKeywords = [
      'database connection',
      'payment',
      'trading',
      'security',
      'authentication',
      'out of memory',
      'disk full'
    ];
    
    const message = errorEvent.message.toLowerCase();
    const component = errorEvent.component.toLowerCase();
    
    return (
      errorEvent.level === 'error' &&
      (criticalKeywords.some(keyword => message.includes(keyword)) ||
       criticalKeywords.some(keyword => component.includes(keyword)) ||
       (errorEvent.tags && errorEvent.tags.includes('critical')))
    );
  }

  /**
   * Clean up old error aggregations
   */
  private cleanupOldAggregations(): void {
    const cutoffTime = Date.now() - this.ERROR_AGGREGATION_WINDOW;
    
    for (const [hash, aggregation] of this.errorAggregations.entries()) {
      if (aggregation.lastSeen.getTime() < cutoffTime) {
        this.errorAggregations.delete(hash);
      }
    }
  }

  /**
   * Shutdown error tracking service
   */
  public async shutdown(): Promise<void> {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
    
    // Flush remaining logs
    await this.flushLogs();
    
    this.emit('shutdown');
  }
}

export { ErrorEvent, ErrorAggregation, LogEntry };