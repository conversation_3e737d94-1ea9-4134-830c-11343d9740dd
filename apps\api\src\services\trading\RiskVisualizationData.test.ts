/**
 * Risk Visualization Data Service Tests
 * 
 * Comprehensive test suite for the Risk Visualization Data service
 * covering data preparation, visualization formatting, and explanations.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { RiskVisualizationData } from './RiskVisualizationData';
import { Position, PortfolioRiskMetrics } from '../../types/trading';

// Mock data
const mockPositions: Position[] = [
  {
    id: 'pos_1',
    accountId: 'acc_1',
    userId: 'user_1',
    symbol: 'EUR/USD',
    side: 'long',
    size: new Decimal('100000'),
    entryPrice: new Decimal('1.1200'),
    currentPrice: new Decimal('1.1250'),
    unrealizedPnl: new Decimal('500'),
    realizedPnl: new Decimal('0'),
    marginUsed: new Decimal('2240'),
    riskScore: 1.2,
    priorityLevel: 'medium',
    timestamp: new Date(),
    lastUpdate: new Date(),
  },
  {
    id: 'pos_2',
    accountId: 'acc_1',
    userId: 'user_1',
    symbol: 'GBP/USD',
    side: 'long',
    size: new Decimal('75000'),
    entryPrice: new Decimal('1.2800'),
    currentPrice: new Decimal('1.2750'),
    unrealizedPnl: new Decimal('-375'),
    realizedPnl: new Decimal('0'),
    marginUsed: new Decimal('1912.50'),
    riskScore: 1.5,
    priorityLevel: 'high',
    timestamp: new Date(),
    lastUpdate: new Date(),
  },
  {
    id: 'pos_3',
    accountId: 'acc_1',
    userId: 'user_1',
    symbol: 'GOLD',
    side: 'long',
    size: new Decimal('10'),
    entryPrice: new Decimal('2000'),
    currentPrice: new Decimal('2050'),
    unrealizedPnl: new Decimal('500'),
    realizedPnl: new Decimal('0'),
    marginUsed: new Decimal('4100'),
    riskScore: 0.8,
    priorityLevel: 'low',
    timestamp: new Date(),
    lastUpdate: new Date(),
  },
];

const mockPortfolioRisk: PortfolioRiskMetrics = {
  totalValue: new Decimal('150000'),
  totalUnrealizedPnl: new Decimal('625'),
  valueAtRisk: {
    daily: {
      confidence95: 2500,
      confidence99: 3500,
    },
    weekly: {
      confidence95: 5500,
      confidence99: 7500,
    },
  },
  expectedShortfall: {
    daily: {
      confidence95: 3000,
      confidence99: 4500,
    },
    weekly: {
      confidence95: 6500,
      confidence99: 9000,
    },
  },
  concentrationRisk: {
    concentrationScore: 75,
    largestPosition: new Decimal('112500'), // EUR/USD position value
    topPositionsWeight: 0.85,
    diversificationIndex: 0.75,
  },
  correlationRisk: {
    averageCorrelation: 0.65,
    maxCorrelation: 0.85,
    correlationMatrix: {
      'EUR/USD': {
        'EUR/USD': 1.0,
        'GBP/USD': 0.85,
        'GOLD': 0.15,
      },
      'GBP/USD': {
        'EUR/USD': 0.85,
        'GBP/USD': 1.0,
        'GOLD': 0.20,
      },
      'GOLD': {
        'EUR/USD': 0.15,
        'GBP/USD': 0.20,
        'GOLD': 1.0,
      },
    },
  },
  leverageMetrics: {
    grossLeverage: 1.5,
    netLeverage: 1.2,
    marginUtilization: 0.55,
  },
  lastCalculated: new Date(),
};

describe('RiskVisualizationData', () => {
  let service: RiskVisualizationData;

  beforeEach(() => {
    service = new RiskVisualizationData();
    vi.clearAllMocks();
  });

  describe('generateDashboardData', () => {
    it('should generate comprehensive dashboard data', async () => {
      const dashboardData = await service.generateDashboardData(
        'user_1',
        '1d',
        mockPortfolioRisk,
        mockPositions
      );

      expect(dashboardData).toMatchObject({
        overview: {
          riskScore: {
            current: expect.any(Number),
            grade: expect.stringMatching(/^[A-F]$/),
            change24h: expect.any(Number),
            trend: expect.stringMatching(/^(improving|declining|stable)$/),
          },
          portfolioValue: {
            total: mockPortfolioRisk.totalValue.toString(),
            unrealizedPnl: mockPortfolioRisk.totalUnrealizedPnl.toString(),
            riskExposure: expect.any(String),
            marginUsed: expect.any(String),
          },
          limits: {
            dailyLoss: {
              limit: expect.any(String),
              used: expect.any(String),
              remaining: expect.any(String),
              percentage: expect.any(Number),
            },
            weeklyLoss: {
              limit: expect.any(String),
              used: expect.any(String),
              remaining: expect.any(String),
              percentage: expect.any(Number),
            },
          },
        },
        heatmap: expect.any(Object),
        timeline: expect.any(Object),
        alerts: expect.any(Object),
        metrics: {
          var: {
            daily95: mockPortfolioRisk.valueAtRisk.daily.confidence95.toString(),
            daily99: mockPortfolioRisk.valueAtRisk.daily.confidence99.toString(),
            weekly95: mockPortfolioRisk.valueAtRisk.weekly.confidence95.toString(),
            weekly99: mockPortfolioRisk.valueAtRisk.weekly.confidence99.toString(),
          },
          diversification: {
            score: Math.round(mockPortfolioRisk.concentrationRisk.diversificationIndex * 100),
            grade: expect.any(String),
            description: expect.any(String),
          },
          correlation: {
            averageCorrelation: mockPortfolioRisk.correlationRisk.averageCorrelation,
            maxCorrelation: mockPortfolioRisk.correlationRisk.maxCorrelation,
            riskiest: expect.any(String),
          },
        },
      });
    });

    it('should calculate risk score correctly', async () => {
      const dashboardData = await service.generateDashboardData(
        'user_1',
        '1d',
        mockPortfolioRisk,
        mockPositions
      );

      const riskScore = dashboardData.overview.riskScore.current;
      expect(riskScore).toBeGreaterThanOrEqual(0);
      expect(riskScore).toBeLessThanOrEqual(100);

      // Risk score should reflect the portfolio's characteristics
      // With good diversification (0.75) and moderate leverage (1.5), expect reasonable score
      expect(riskScore).toBeGreaterThan(30); // Adjusted expectation
    });

    it('should assign appropriate risk grade', async () => {
      const dashboardData = await service.generateDashboardData(
        'user_1',
        '1d',
        mockPortfolioRisk,
        mockPositions
      );

      const grade = dashboardData.overview.riskScore.grade;
      expect(['A', 'B', 'C', 'D', 'F']).toContain(grade);
    });
  });

  describe('generateRiskHeatmap', () => {
    it('should generate risk heatmap for positions', async () => {
      const heatmapData = await service.generateRiskHeatmap(mockPositions, mockPortfolioRisk);

      expect(heatmapData).toMatchObject({
        positions: expect.arrayContaining([
          expect.objectContaining({
            symbol: 'EUR/USD',
            size: mockPositions[0].size.toString(),
            riskContribution: expect.any(Number),
            correlation: expect.any(Number),
            volatility: expect.any(Number),
            riskLevel: expect.stringMatching(/^(low|medium|high|critical)$/),
            color: expect.stringMatching(/^#[0-9a-f]{6}$/),
            coordinates: {
              x: expect.any(Number),
              y: expect.any(Number),
            },
          }),
        ]),
        portfolioLevel: {
          overallRisk: expect.any(Number),
          diversificationScore: Math.round(mockPortfolioRisk.concentrationRisk.diversificationIndex * 100),
          concentrationRisk: Math.round(mockPortfolioRisk.concentrationRisk.concentrationScore * 100),
        },
        colorScale: {
          low: '#22c55e',
          medium: '#eab308',
          high: '#f97316',
          critical: '#ef4444',
        },
      });
    });

    it('should calculate position risk contributions correctly', async () => {
      const heatmapData = await service.generateRiskHeatmap(mockPositions, mockPortfolioRisk);

      // EUR/USD should have highest risk contribution due to largest position size and risk score
      const eurPosition = heatmapData.positions.find(p => p.symbol === 'EUR/USD');
      const gbpPosition = heatmapData.positions.find(p => p.symbol === 'GBP/USD');
      const goldPosition = heatmapData.positions.find(p => p.symbol === 'GOLD');

      expect(eurPosition).toBeDefined();
      expect(gbpPosition).toBeDefined();
      expect(goldPosition).toBeDefined();

      // EUR/USD has largest position value, should have high risk contribution
      expect(eurPosition!.riskContribution).toBeGreaterThan(goldPosition!.riskContribution);
    });

    it('should assign risk levels based on multiple factors', async () => {
      const heatmapData = await service.generateRiskHeatmap(mockPositions, mockPortfolioRisk);

      heatmapData.positions.forEach(position => {
        expect(['low', 'medium', 'high', 'critical']).toContain(position.riskLevel);
        
        // High correlation positions should generally have higher risk levels
        if (position.correlation > 0.8) {
          expect(['medium', 'high', 'critical']).toContain(position.riskLevel);
        }
      });
    });

    it('should generate valid heatmap coordinates', async () => {
      const heatmapData = await service.generateRiskHeatmap(mockPositions, mockPortfolioRisk);

      heatmapData.positions.forEach(position => {
        expect(position.coordinates.x).toBeGreaterThanOrEqual(5);
        expect(position.coordinates.x).toBeLessThanOrEqual(95);
        expect(position.coordinates.y).toBeGreaterThanOrEqual(5);
        expect(position.coordinates.y).toBeLessThanOrEqual(95);
      });
    });
  });

  describe('generateRiskTimeline', () => {
    it('should generate timeline data for different timeframes', async () => {
      const timeframes = ['1d', '1w', '1m'] as const;

      for (const timeframe of timeframes) {
        const timelineData = await service.generateRiskTimeline('user_1', timeframe);

        expect(timelineData).toMatchObject({
          timePoints: expect.arrayContaining([
            expect.objectContaining({
              timestamp: expect.any(String),
              portfolioValue: expect.any(String),
              riskScore: expect.any(Number),
              volatility: expect.any(Number),
              drawdown: expect.any(Number),
              varBreach: expect.any(Boolean),
              events: expect.any(Array),
            }),
          ]),
          trends: {
            riskTrend: expect.stringMatching(/^(increasing|decreasing|stable)$/),
            volatilityTrend: expect.stringMatching(/^(increasing|decreasing|stable)$/),
            performanceTrend: expect.stringMatching(/^(improving|declining|stable)$/),
          },
        });

        // Timeline should have appropriate number of points
        const expectedPoints = timeframe === '1d' ? 24 : timeframe === '1w' ? 7 : 30;
        expect(timelineData.timePoints).toHaveLength(expectedPoints);
      }
    });

    it('should generate chronologically ordered timeline', async () => {
      const timelineData = await service.generateRiskTimeline('user_1', '1d');

      for (let i = 1; i < timelineData.timePoints.length; i++) {
        const current = new Date(timelineData.timePoints[i].timestamp);
        const previous = new Date(timelineData.timePoints[i - 1].timestamp);
        expect(current.getTime()).toBeGreaterThan(previous.getTime());
      }
    });

    it('should include risk events in timeline', async () => {
      const timelineData = await service.generateRiskTimeline('user_1', '1w');

      const pointsWithEvents = timelineData.timePoints.filter(point => point.events.length > 0);
      
      if (pointsWithEvents.length > 0) {
        pointsWithEvents.forEach(point => {
          point.events.forEach(event => {
            expect(['position_opened', 'position_closed', 'limit_breach', 'alert_triggered'])
              .toContain(event.type);
            expect(event.description).toBeTruthy();
          });
        });
      }
    });
  });

  describe('generateAlertVisualization', () => {
    it('should generate alert visualization data', async () => {
      const alertData = await service.generateAlertVisualization('user_1');

      expect(alertData).toMatchObject({
        activeAlerts: expect.arrayContaining([
          expect.objectContaining({
            id: expect.any(String),
            severity: expect.stringMatching(/^(info|warning|critical)$/),
            title: expect.any(String),
            message: expect.any(String),
            actionRequired: expect.any(Boolean),
            suggestedActions: expect.any(Array),
            icon: expect.any(String),
            color: expect.stringMatching(/^#[0-9a-f]{6}$/),
            timestamp: expect.any(String),
          }),
        ]),
        alertSummary: {
          total: expect.any(Number),
          critical: expect.any(Number),
          warning: expect.any(Number),
          info: expect.any(Number),
        },
      });
    });

    it('should calculate alert summary correctly', async () => {
      const alertData = await service.generateAlertVisualization('user_1');

      const totalCount = alertData.alertSummary.critical + 
                        alertData.alertSummary.warning + 
                        alertData.alertSummary.info;

      expect(alertData.alertSummary.total).toBe(totalCount);
      expect(alertData.activeAlerts).toHaveLength(alertData.alertSummary.total);
    });

    it('should provide actionable suggestions for alerts', async () => {
      const alertData = await service.generateAlertVisualization('user_1');

      alertData.activeAlerts.forEach(alert => {
        if (alert.actionRequired) {
          expect(alert.suggestedActions.length).toBeGreaterThan(0);
          alert.suggestedActions.forEach(action => {
            expect(action).toBeTruthy();
            expect(typeof action).toBe('string');
          });
        }
      });
    });
  });

  describe('generateRiskExplanations', () => {
    it('should generate user-friendly risk explanations', async () => {
      const dashboardData = await service.generateDashboardData(
        'user_1',
        '1d',
        mockPortfolioRisk,
        mockPositions
      );

      const explanations = service.generateRiskExplanations(dashboardData);

      expect(explanations).toMatchObject({
        riskScore: expect.stringContaining('risk score'),
        diversification: expect.stringContaining('diversif'),
        var: expect.stringContaining('Value at Risk'),
        correlation: expect.stringContaining('correlation'),
      });

      // Each explanation should be informative and actionable
      Object.values(explanations).forEach(explanation => {
        expect(explanation.length).toBeGreaterThan(20);
        expect(explanation).toBeTruthy();
      });
    });

    it('should provide grade-appropriate explanations', async () => {
      const dashboardData = await service.generateDashboardData(
        'user_1',
        '1d',
        mockPortfolioRisk,
        mockPositions
      );

      const explanations = service.generateRiskExplanations(dashboardData);
      const grade = dashboardData.overview.riskScore.grade;

      // Explanation should reference the grade
      expect(explanations.riskScore).toContain(`Grade ${grade}`);

      // High-grade portfolios should have positive language
      if (['A', 'B'].includes(grade)) {
        expect(explanations.riskScore).toMatch(/(excellent|good|strong)/i);
      }
    });

    it('should explain correlation risks appropriately', async () => {
      const dashboardData = await service.generateDashboardData(
        'user_1',
        '1d',
        mockPortfolioRisk,
        mockPositions
      );

      const explanations = service.generateRiskExplanations(dashboardData);
      const maxCorrelation = dashboardData.metrics.correlation.maxCorrelation;

      if (maxCorrelation > 0.8) {
        expect(explanations.correlation).toMatch(/(high correlation|increases.*risk)/i);
      } else if (maxCorrelation < 0.6) {
        expect(explanations.correlation).toMatch(/(low correlation|good diversification)/i);
      }
    });
  });

  describe('Helper Methods', () => {
    it('should calculate overall risk score based on multiple factors', async () => {
      // Test with high-risk portfolio
      const highRiskPortfolio = {
        ...mockPortfolioRisk,
        concentrationRisk: {
          ...mockPortfolioRisk.concentrationRisk,
          diversificationIndex: 0.3, // Poor diversification
        },
        leverageMetrics: {
          ...mockPortfolioRisk.leverageMetrics,
          grossLeverage: 5.0, // High leverage
        },
        correlationRisk: {
          ...mockPortfolioRisk.correlationRisk,
          averageCorrelation: 0.9, // Very high correlation
        },
      };

      const dashboardData = await service.generateDashboardData(
        'user_1',
        '1d',
        highRiskPortfolio,
        mockPositions
      );

      // Should result in lower risk score
      expect(dashboardData.overview.riskScore.current).toBeLessThan(70);
      expect(['D', 'F']).toContain(dashboardData.overview.riskScore.grade);
    });

    it('should determine risk levels correctly', async () => {
      const heatmapData = await service.generateRiskHeatmap(mockPositions, mockPortfolioRisk);

      heatmapData.positions.forEach(position => {
        // Positions with high volatility and correlation should be higher risk
        if (position.volatility > 0.2 && position.correlation > 0.7) {
          expect(['high', 'critical']).toContain(position.riskLevel);
        }

        // Low volatility, low correlation should be lower risk
        if (position.volatility < 0.15 && position.correlation < 0.3) {
          expect(['low', 'medium']).toContain(position.riskLevel);
        }
      });
    });

    it('should generate diversification descriptions correctly', async () => {
      const testCases = [
        { score: 0.95, expectedGrade: 'Excellent', expectedKeywords: ['well diversified'] },
        { score: 0.75, expectedGrade: 'Good', expectedKeywords: ['good diversification'] },
        { score: 0.55, expectedGrade: 'Fair', expectedKeywords: ['moderate diversification'] },
        { score: 0.35, expectedGrade: 'Poor', expectedKeywords: ['poor diversification', 'high concentration'] },
      ];

      for (const testCase of testCases) {
        const testPortfolio = {
          ...mockPortfolioRisk,
          concentrationRisk: {
            ...mockPortfolioRisk.concentrationRisk,
            diversificationIndex: testCase.score,
          },
        };

        const dashboardData = await service.generateDashboardData(
          'user_1',
          '1d',
          testPortfolio,
          mockPositions
        );

        expect(dashboardData.metrics.diversification.grade).toBe(testCase.expectedGrade);
        
        const description = dashboardData.metrics.diversification.description.toLowerCase();
        testCase.expectedKeywords.forEach(keyword => {
          expect(description).toContain(keyword.toLowerCase());
        });
      }
    });

    it('should find riskiest correlation pairs', async () => {
      const dashboardData = await service.generateDashboardData(
        'user_1',
        '1d',
        mockPortfolioRisk,
        mockPositions
      );

      const riskiestPair = dashboardData.metrics.correlation.riskiest;
      
      // Should identify EUR/USD-GBP/USD as the riskiest pair (0.85 correlation)
      expect(riskiestPair).toContain('EUR/USD');
      expect(riskiestPair).toContain('GBP/USD');
    });

    it('should handle edge cases gracefully', async () => {
      // Test with empty positions array
      const emptyHeatmapData = await service.generateRiskHeatmap([], mockPortfolioRisk);
      expect(emptyHeatmapData.positions).toHaveLength(0);
      expect(emptyHeatmapData.portfolioLevel).toBeDefined();

      // Test with single position
      const singlePositionData = await service.generateRiskHeatmap(
        [mockPositions[0]], 
        mockPortfolioRisk
      );
      expect(singlePositionData.positions).toHaveLength(1);
      expect(singlePositionData.positions[0].coordinates).toBeDefined();
    });
  });

  describe('Performance', () => {
    it('should generate dashboard data efficiently', async () => {
      const startTime = Date.now();

      await service.generateDashboardData(
        'user_1',
        '1d',
        mockPortfolioRisk,
        mockPositions
      );

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      // Should complete within reasonable time (less than 1 second)
      expect(executionTime).toBeLessThan(1000);
    });

    it('should handle large position arrays', async () => {
      // Create large array of positions
      const largePositionArray = Array.from({ length: 50 }, (_, i) => ({
        ...mockPositions[0],
        id: `pos_${i}`,
        symbol: `PAIR_${i}`,
        size: new Decimal(Math.random() * 100000 + 10000),
        currentPrice: new Decimal(Math.random() * 10 + 1),
      }));

      const startTime = Date.now();

      const heatmapData = await service.generateRiskHeatmap(largePositionArray, mockPortfolioRisk);

      const endTime = Date.now();
      const executionTime = endTime - startTime;

      expect(heatmapData.positions).toHaveLength(50);
      expect(executionTime).toBeLessThan(500); // Should handle 50 positions quickly
    });
  });
});