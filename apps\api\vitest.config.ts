import { defineConfig } from 'vitest/config'
import path from 'path'

export default defineConfig({
  test: {
    environment: 'node',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
    coverage: {
      provider: 'v8',
      reporter: ['text', 'html', 'lcov'],
      include: ['src/**/*.ts'],
      exclude: [
        'src/**/*.test.ts',
        'src/**/*.spec.ts',
        'src/test/**',
        'src/**/*.d.ts',
      ],
      threshold: {
        global: {
          branches: 60,
          functions: 70,
          lines: 70,
          statements: 70,
        },
        'src/services/': {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@golddaddy/types': path.resolve(__dirname, '../../packages/types/src'),
      '@golddaddy/config': path.resolve(__dirname, '../../packages/config/src'),
    },
  },
})