import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { ConfidenceAssessmentService } from '../../services/confidence/ConfidenceAssessmentService.js';
import { QuizFeedbackService } from '../../services/confidence/QuizFeedbackService.js';
import { ProgressTrackingService } from '../../services/confidence/ProgressTrackingService.js';
import { QuizContentService } from '../../services/confidence/QuizContentService.js';
import { ConfidenceStage, QuizDifficulty } from '@golddaddy/types';

const router = Router();
const prisma = new PrismaClient();

// Initialize services
const confidenceService = new ConfidenceAssessmentService(prisma);
const feedbackService = new QuizFeedbackService(prisma);
const progressService = new ProgressTrackingService(prisma);
const contentService = new QuizContentService(prisma);

// Validation schemas
const startQuizSchema = z.object({
  userId: z.string().min(1),
  stage: z.nativeEnum(ConfidenceStage),
  difficulty: z.nativeEnum(QuizDifficulty).optional(),
  categoryFocus: z.array(z.string()).optional()
});

const submitAnswerSchema = z.object({
  sessionId: z.string().min(1),
  userId: z.string().min(1),
  questionId: z.string().min(1),
  selectedAnswerIds: z.array(z.string()),
  confidenceLevel: z.number().min(1).max(5),
  timeSpent: z.number().min(0)
});

const statusSchema = z.object({
  userId: z.string().min(1),
  sessionId: z.string().min(1).optional()
});

const historySchema = z.object({
  userId: z.string().min(1),
  stage: z.nativeEnum(ConfidenceStage).optional(),
  limit: z.number().min(1).max(100).optional(),
  offset: z.number().min(0).optional()
});

// Error handling middleware
const handleAsync = (fn: (...args: any[]) => any) => (req: any, res: any, next: any) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * POST /api/confidence/quiz/start
 * Start a new quiz session
 */
router.post('/start', handleAsync(async (req: any, res: any) => {
  try {
    const { userId, stage, difficulty, categoryFocus } = startQuizSchema.parse(req.body);

    // Check if user has an active session
    const existingSession = await prisma.quizSession.findFirst({
      where: {
        userId,
        status: 'IN_PROGRESS'
      }
    });

    if (existingSession) {
      return res.status(400).json({
        error: 'User already has an active quiz session',
        activeSessionId: existingSession.id
      });
    }

    // Generate quiz questions based on user's stage and preferences
    const questions = await contentService.generateQuizForStage(
      stage, 
      userId, 
      difficulty,
      categoryFocus
    );

    if (questions.length === 0) {
      return res.status(404).json({
        error: 'No quiz questions available for the specified criteria'
      });
    }

    // Create quiz session
    const session = await prisma.quizSession.create({
      data: {
        userId,
        stage,
        difficulty: difficulty || QuizDifficulty.INTERMEDIATE,
        status: 'IN_PROGRESS',
        totalQuestions: questions.length,
        currentQuestion: 0,
        startedAt: new Date(),
        timeLimit: 30 * 60, // 30 minutes in seconds
        metadata: {
          categoryFocus: categoryFocus || [],
          questionsGenerated: questions.length
        }
      }
    });

    // Store question associations
    await prisma.$transaction(
      questions.map((question, index) =>
        prisma.quizSessionQuestion.create({
          data: {
            sessionId: session.id,
            questionId: question.id,
            orderIndex: index
          }
        })
      )
    );

    res.status(201).json({
      success: true,
      session: {
        id: session.id,
        userId: session.userId,
        stage: session.stage,
        totalQuestions: session.totalQuestions,
        timeLimit: session.timeLimit,
        startedAt: session.startedAt
      },
      questions: questions.map(q => ({
        id: q.id,
        category: q.category,
        difficulty: q.difficulty,
        topic: q.topic,
        question: q.question,
        options: q.options,
        metadata: {
          estimatedDuration: q.metadata.estimatedDuration,
          tags: q.metadata.tags
        }
      })) // Don't include correct answers or explanations
    });
  } catch (error) {
    console.error('Error starting quiz:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to start quiz session'
    });
  }
}));

/**
 * POST /api/confidence/quiz/submit
 * Submit an answer for a quiz question
 */
router.post('/submit', handleAsync(async (req: any, res: any) => {
  try {
    const { sessionId, userId, questionId, selectedAnswerIds, confidenceLevel, timeSpent } = 
      submitAnswerSchema.parse(req.body);

    // Validate session and ownership
    const session = await prisma.quizSession.findUnique({
      where: { id: sessionId },
      include: {
        questions: {
          include: {
            question: true
          }
        }
      }
    });

    if (!session) {
      return res.status(404).json({ error: 'Quiz session not found' });
    }

    if (session.userId !== userId) {
      return res.status(403).json({ error: 'Unauthorized access to quiz session' });
    }

    if (session.status !== 'IN_PROGRESS') {
      return res.status(400).json({ error: 'Quiz session is not active' });
    }

    // Find the question
    const question = await prisma.quizQuestion.findUnique({
      where: { id: questionId },
      include: { options: true }
    });

    if (!question) {
      return res.status(404).json({ error: 'Question not found' });
    }

    // Check if question belongs to this session
    const sessionQuestion = session.questions.find(sq => sq.questionId === questionId);
    if (!sessionQuestion) {
      return res.status(400).json({ error: 'Question not part of this quiz session' });
    }

    // Check if already answered
    const existingResponse = await prisma.quizResponse.findFirst({
      where: {
        sessionId,
        questionId
      }
    });

    if (existingResponse) {
      return res.status(400).json({ error: 'Question already answered' });
    }

    // Evaluate answer
    const isCorrect = selectedAnswerIds.length === question.correctAnswerIds.length &&
      selectedAnswerIds.every(id => question.correctAnswerIds.includes(id));

    // Create response record
    const response = await prisma.quizResponse.create({
      data: {
        sessionId,
        userId,
        questionId,
        selectedAnswerIds,
        isCorrect,
        timeSpent,
        confidenceLevel,
        submittedAt: new Date()
      }
    });

    // Generate immediate feedback
    const userExperienceLevel = await confidenceService.getUserExperienceLevel(userId);
    const feedback = await feedbackService.generateImmediateFeedback(
      response,
      question,
      userExperienceLevel
    );

    // Update session progress
    const responsesCount = await prisma.quizResponse.count({
      where: { sessionId }
    });

    const sessionUpdate: any = {
      currentQuestion: responsesCount,
      updatedAt: new Date()
    };

    // Check if quiz is completed
    if (responsesCount >= session.totalQuestions) {
      sessionUpdate.status = 'COMPLETED';
      sessionUpdate.completedAt = new Date();

      // Calculate final score and create quiz attempt record
      const allResponses = await prisma.quizResponse.findMany({
        where: { sessionId }
      });

      const correctCount = allResponses.filter(r => r.isCorrect).length;
      const overallScore = (correctCount / session.totalQuestions) * 100;
      const totalTime = allResponses.reduce((sum, r) => sum + r.timeSpent, 0);

      await prisma.quizAttempt.create({
        data: {
          userId,
          sessionId,
          stage: session.stage,
          overallScore,
          totalQuestions: session.totalQuestions,
          correctAnswers: correctCount,
          timeSpent: totalTime,
          completedAt: new Date(),
          categoryScores: {}, // TODO: Calculate category-specific scores
          attemptNumber: await confidenceService.getUserAttemptCount(userId, session.stage) + 1
        }
      });

      // Update user's confidence assessment
      await confidenceService.updateConfidenceScore(userId, {
        quizResults: {
          stage: session.stage,
          score: overallScore,
          timeSpent: totalTime,
          attempts: await confidenceService.getUserAttemptCount(userId, session.stage) + 1
        }
      });
    }

    // Update session
    await prisma.quizSession.update({
      where: { id: sessionId },
      data: sessionUpdate
    });

    res.json({
      success: true,
      responseId: response.id,
      isCorrect,
      feedback,
      sessionProgress: {
        currentQuestion: responsesCount,
        totalQuestions: session.totalQuestions,
        completed: responsesCount >= session.totalQuestions
      }
    });
  } catch (error) {
    console.error('Error submitting answer:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to submit answer'
    });
  }
}));

/**
 * GET /api/confidence/quiz/status
 * Get current quiz session status
 */
router.get('/status', handleAsync(async (req: any, res: any) => {
  try {
    const { userId, sessionId } = statusSchema.parse(req.query);

    let session;
    if (sessionId) {
      // Get specific session
      session = await prisma.quizSession.findUnique({
        where: { id: sessionId },
        include: {
          responses: true,
          questions: {
            include: {
              question: {
                select: {
                  id: true,
                  category: true,
                  difficulty: true,
                  topic: true
                }
              }
            }
          }
        }
      });
    } else {
      // Get most recent active session
      session = await prisma.quizSession.findFirst({
        where: {
          userId,
          status: 'IN_PROGRESS'
        },
        include: {
          responses: true,
          questions: {
            include: {
              question: {
                select: {
                  id: true,
                  category: true,
                  difficulty: true,
                  topic: true
                }
              }
            }
          }
        },
        orderBy: {
          startedAt: 'desc'
        }
      });
    }

    if (!session) {
      return res.json({
        hasActiveSession: false,
        message: 'No active quiz session found'
      });
    }

    if (session.userId !== userId) {
      return res.status(403).json({ error: 'Unauthorized access' });
    }

    // Calculate progress metrics
    const correctAnswers = session.responses.filter(r => r.isCorrect).length;
    const currentScore = session.responses.length > 0 
      ? (correctAnswers / session.responses.length) * 100 
      : 0;

    const timeElapsed = session.completedAt
      ? new Date(session.completedAt).getTime() - new Date(session.startedAt).getTime()
      : Date.now() - new Date(session.startedAt).getTime();

    const timeRemaining = session.timeLimit
      ? Math.max(0, session.timeLimit - Math.floor(timeElapsed / 1000))
      : null;

    // Get overall confidence assessment progress
    const progressSummary = await progressService.getProgressSummary(userId);

    res.json({
      hasActiveSession: true,
      session: {
        id: session.id,
        stage: session.stage,
        status: session.status,
        currentQuestion: session.currentQuestion + 1,
        totalQuestions: session.totalQuestions,
        progress: Math.round((session.currentQuestion / session.totalQuestions) * 100),
        currentScore: Math.round(currentScore),
        correctAnswers,
        timeElapsed: Math.floor(timeElapsed / 1000),
        timeRemaining,
        startedAt: session.startedAt,
        completedAt: session.completedAt
      },
      progressSummary: {
        currentStage: progressSummary.currentStage,
        overallProgress: progressSummary.overallProgress,
        readyForAdvancement: progressSummary.readyForAdvancement,
        timeInCurrentStage: progressSummary.timeInCurrentStage
      }
    });
  } catch (error) {
    console.error('Error getting quiz status:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to get quiz status'
    });
  }
}));

/**
 * GET /api/confidence/quiz/history
 * Get user's quiz attempt history
 */
router.get('/history', handleAsync(async (req: any, res: any) => {
  try {
    const { userId, stage, limit = 20, offset = 0 } = historySchema.parse(req.query);

    const whereClause: any = { userId };
    if (stage) {
      whereClause.stage = stage;
    }

    const attempts = await prisma.quizAttempt.findMany({
      where: whereClause,
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset,
      include: {
        session: {
          select: {
            id: true,
            difficulty: true,
            metadata: true
          }
        }
      }
    });

    const totalCount = await prisma.quizAttempt.count({
      where: whereClause
    });

    // Calculate summary statistics
    const summary = {
      totalAttempts: totalCount,
      averageScore: attempts.length > 0
        ? Math.round(attempts.reduce((sum, attempt) => sum + attempt.overallScore, 0) / attempts.length)
        : 0,
      bestScore: attempts.length > 0
        ? Math.max(...attempts.map(a => a.overallScore))
        : 0,
      recentTrend: attempts.length >= 3
        ? attempts.slice(0, 3).reduce((sum, a, i) => sum + (a.overallScore * (3 - i)), 0) / 6
        : null,
      stageProgress: {} as Record<string, any>
    };

    // Group by stage for progress tracking
    const stageGroups = attempts.reduce((groups, attempt) => {
      const stage = attempt.stage;
      if (!groups[stage]) {
        groups[stage] = [];
      }
      groups[stage].push(attempt);
      return groups;
    }, {} as Record<string, typeof attempts>);

    Object.keys(stageGroups).forEach(stage => {
      const stageAttempts = stageGroups[stage];
      summary.stageProgress[stage] = {
        attempts: stageAttempts.length,
        bestScore: Math.max(...stageAttempts.map(a => a.overallScore)),
        averageScore: Math.round(stageAttempts.reduce((sum, a) => sum + a.overallScore, 0) / stageAttempts.length),
        lastAttempt: stageAttempts[0].createdAt,
        improvement: stageAttempts.length > 1
          ? stageAttempts[0].overallScore - stageAttempts[stageAttempts.length - 1].overallScore
          : 0
      };
    });

    res.json({
      success: true,
      attempts: attempts.map(attempt => ({
        id: attempt.id,
        stage: attempt.stage,
        score: Math.round(attempt.overallScore),
        correctAnswers: attempt.correctAnswers,
        totalQuestions: attempt.totalQuestions,
        timeSpent: attempt.timeSpent,
        completedAt: attempt.completedAt,
        attemptNumber: attempt.attemptNumber,
        difficulty: attempt.session?.difficulty,
        categoryScores: attempt.categoryScores,
        weakAreas: attempt.weakAreas,
        strongAreas: attempt.strongAreas
      })),
      summary,
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      }
    });
  } catch (error) {
    console.error('Error getting quiz history:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to get quiz history'
    });
  }
}));

export default router;