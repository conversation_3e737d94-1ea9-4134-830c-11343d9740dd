"""
Training Pipeline for Transformer Models
Handles data preprocessing, model training, validation, and versioning
"""

import os
import json
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset, random_split
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import asyncio
import hashlib
from pathlib import Path
from loguru import logger
import mlflow
import mlflow.pytorch

from .model_registry import ModelRegistry, MLModel, ModelPerformance, ModelDeployment, ModelTraining, ModelStatus, ModelType
from .transformer_models import create_model, ModelConfig, count_parameters
from .feature_engineering import FinancialFeatureEngineer, FeatureConfig

@dataclass
class TrainingConfig:
    """Configuration for training pipeline"""
    model_type: str = "market"
    model_config: ModelConfig = None
    feature_config: FeatureConfig = None
    train_split: float = 0.7
    val_split: float = 0.15
    test_split: float = 0.15
    batch_size: int = 32
    num_epochs: int = 100
    learning_rate: float = 1e-4
    weight_decay: float = 0.01
    early_stopping_patience: int = 10
    save_best_model: bool = True
    use_mixed_precision: bool = True
    gradient_clip_norm: float = 1.0
    scheduler_type: str = "cosine"
    warmup_epochs: int = 5
    
    def __post_init__(self):
        if self.model_config is None:
            self.model_config = ModelConfig()
        if self.feature_config is None:
            self.feature_config = FeatureConfig()

class MarketDataset(Dataset):
    """Dataset class for market data"""
    
    def __init__(self, features: np.ndarray, targets: np.ndarray, sequence_length: int = 128):
        self.features = torch.FloatTensor(features)
        self.targets = torch.FloatTensor(targets)
        self.sequence_length = sequence_length
        
    def __len__(self):
        return len(self.features)
    
    def __getitem__(self, idx):
        return {
            'features': self.features[idx],
            'targets': self.targets[idx]
        }

class TrainingPipeline:
    """
    Comprehensive training pipeline for transformer models
    """
    
    def __init__(self, config: TrainingConfig, model_registry: ModelRegistry):
        self.config = config
        self.model_registry = model_registry
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.feature_engineer = FinancialFeatureEngineer(config.feature_config)
        
        # Initialize MLflow
        mlflow.set_experiment("golddaddy_ml_training")
        
        logger.info(f"Training pipeline initialized with device: {self.device}")
    
    async def train_model(self, data: pd.DataFrame, model_name: str, 
                         target_column: str = 'returns') -> str:
        """
        Train a new model
        Args:
            data: Training data
            model_name: Name for the model
            target_column: Target column for prediction
        Returns:
            Model ID
        """
        logger.info(f"Starting training for model: {model_name}")
        
        with mlflow.start_run():
            # Log configuration
            mlflow.log_params(asdict(self.config))
            
            # Prepare data
            features_df = await self._prepare_data(data)
            X, y = self._create_sequences(features_df, target_column)
            
            # Split data
            train_loader, val_loader, test_loader = self._create_data_loaders(X, y)
            
            # Create model
            model = self._create_model()
            model.to(self.device)
            
            # Log model info
            mlflow.log_param("model_parameters", count_parameters(model))
            
            # Train model
            training_history = await self._train_model_loop(model, train_loader, val_loader)
            
            # Evaluate model
            test_metrics = await self._evaluate_model(model, test_loader)
            
            # Save model
            model_id = await self._save_model(model, model_name, training_history, test_metrics, data)
            
            # Log metrics to MLflow
            mlflow.log_metrics(test_metrics)
            mlflow.pytorch.log_model(model, "model")
            
            logger.info(f"Training completed for model: {model_name} (ID: {model_id})")
            return model_id
    
    async def _prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """Prepare and engineer features from raw data"""
        logger.info("Preparing data and engineering features...")
        
        # Engineer features
        features_df = self.feature_engineer.extract_features(data)
        
        # Handle missing values
        features_df = features_df.fillna(method='ffill').fillna(0)
        
        logger.info(f"Feature engineering completed. Shape: {features_df.shape}")
        return features_df
    
    def _create_sequences(self, data: pd.DataFrame, target_column: str) -> Tuple[np.ndarray, np.ndarray]:
        """Create sequences for transformer training"""
        return self.feature_engineer.create_sequences(
            data, 
            self.config.model_config.sequence_length, 
            target_column
        )
    
    def _create_data_loaders(self, X: np.ndarray, y: np.ndarray) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """Create data loaders for training, validation, and testing"""
        dataset = MarketDataset(X, y, self.config.model_config.sequence_length)
        
        # Calculate split sizes
        total_size = len(dataset)
        train_size = int(self.config.train_split * total_size)
        val_size = int(self.config.val_split * total_size)
        test_size = total_size - train_size - val_size
        
        # Split dataset
        train_dataset, val_dataset, test_dataset = random_split(
            dataset, [train_size, val_size, test_size]
        )
        
        # Create data loaders
        train_loader = DataLoader(
            train_dataset, 
            batch_size=self.config.batch_size, 
            shuffle=True, 
            num_workers=2
        )
        val_loader = DataLoader(
            val_dataset, 
            batch_size=self.config.batch_size, 
            shuffle=False, 
            num_workers=2
        )
        test_loader = DataLoader(
            test_dataset, 
            batch_size=self.config.batch_size, 
            shuffle=False, 
            num_workers=2
        )
        
        logger.info(f"Data split - Train: {len(train_dataset)}, Val: {len(val_dataset)}, Test: {len(test_dataset)}")
        return train_loader, val_loader, test_loader
    
    def _create_model(self) -> nn.Module:
        """Create model based on configuration"""
        return create_model(self.config.model_type, self.config.model_config)
    
    async def _train_model_loop(self, model: nn.Module, train_loader: DataLoader, 
                               val_loader: DataLoader) -> Dict[str, List[float]]:
        """Main training loop"""
        optimizer = optim.AdamW(
            model.parameters(), 
            lr=self.config.learning_rate, 
            weight_decay=self.config.weight_decay
        )
        
        # Learning rate scheduler
        if self.config.scheduler_type == "cosine":
            scheduler = optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=self.config.num_epochs)
        else:
            scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=30, gamma=0.1)
        
        # Loss function
        criterion = nn.MSELoss()
        
        # Training history
        history = {
            'train_loss': [],
            'val_loss': [],
            'learning_rate': []
        }
        
        best_val_loss = float('inf')
        patience_counter = 0
        
        # Mixed precision training
        scaler = torch.cuda.amp.GradScaler() if self.config.use_mixed_precision else None
        
        for epoch in range(self.config.num_epochs):
            # Training phase
            model.train()
            train_loss = 0.0
            
            for batch in train_loader:
                features = batch['features'].to(self.device)
                targets = batch['targets'].to(self.device)
                
                optimizer.zero_grad()
                
                if self.config.use_mixed_precision:
                    with torch.cuda.amp.autocast():
                        outputs = model(features)
                        loss = criterion(outputs['price_change'].squeeze(), targets)
                    
                    scaler.scale(loss).backward()
                    scaler.unscale_(optimizer)
                    torch.nn.utils.clip_grad_norm_(model.parameters(), self.config.gradient_clip_norm)
                    scaler.step(optimizer)
                    scaler.update()
                else:
                    outputs = model(features)
                    loss = criterion(outputs['price_change'].squeeze(), targets)
                    loss.backward()
                    torch.nn.utils.clip_grad_norm_(model.parameters(), self.config.gradient_clip_norm)
                    optimizer.step()
                
                train_loss += loss.item()
            
            # Validation phase
            model.eval()
            val_loss = 0.0
            
            with torch.no_grad():
                for batch in val_loader:
                    features = batch['features'].to(self.device)
                    targets = batch['targets'].to(self.device)
                    
                    outputs = model(features)
                    loss = criterion(outputs['price_change'].squeeze(), targets)
                    val_loss += loss.item()
            
            # Calculate average losses
            avg_train_loss = train_loss / len(train_loader)
            avg_val_loss = val_loss / len(val_loader)
            
            # Update history
            history['train_loss'].append(avg_train_loss)
            history['val_loss'].append(avg_val_loss)
            history['learning_rate'].append(optimizer.param_groups[0]['lr'])
            
            # Log to MLflow
            mlflow.log_metrics({
                'train_loss': avg_train_loss,
                'val_loss': avg_val_loss,
                'learning_rate': optimizer.param_groups[0]['lr']
            }, step=epoch)
            
            # Early stopping
            if avg_val_loss < best_val_loss:
                best_val_loss = avg_val_loss
                patience_counter = 0
                if self.config.save_best_model:
                    torch.save(model.state_dict(), 'best_model.pth')
            else:
                patience_counter += 1
                if patience_counter >= self.config.early_stopping_patience:
                    logger.info(f"Early stopping at epoch {epoch}")
                    break
            
            # Update learning rate
            scheduler.step()
            
            if epoch % 10 == 0:
                logger.info(f"Epoch {epoch}: Train Loss: {avg_train_loss:.6f}, Val Loss: {avg_val_loss:.6f}")
        
        # Load best model if saved
        if self.config.save_best_model and os.path.exists('best_model.pth'):
            model.load_state_dict(torch.load('best_model.pth'))
            os.remove('best_model.pth')
        
        return history
    
    async def _evaluate_model(self, model: nn.Module, test_loader: DataLoader) -> Dict[str, float]:
        """Evaluate model on test set"""
        model.eval()
        
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for batch in test_loader:
                features = batch['features'].to(self.device)
                targets = batch['targets'].to(self.device)
                
                outputs = model(features)
                predictions = outputs['price_change'].squeeze()
                
                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
        
        # Calculate metrics
        predictions = np.array(all_predictions)
        targets = np.array(all_targets)
        
        mse = np.mean((predictions - targets) ** 2)
        mae = np.mean(np.abs(predictions - targets))
        rmse = np.sqrt(mse)
        
        # Direction accuracy
        pred_direction = np.sign(predictions)
        true_direction = np.sign(targets)
        direction_accuracy = np.mean(pred_direction == true_direction)
        
        # Correlation
        correlation = np.corrcoef(predictions, targets)[0, 1]
        
        metrics = {
            'mse': float(mse),
            'mae': float(mae),
            'rmse': float(rmse),
            'direction_accuracy': float(direction_accuracy),
            'correlation': float(correlation) if not np.isnan(correlation) else 0.0
        }
        
        logger.info(f"Test metrics: {metrics}")
        return metrics
    
    async def _save_model(self, model: nn.Module, model_name: str, 
                         training_history: Dict, test_metrics: Dict, 
                         training_data: pd.DataFrame) -> str:
        """Save model to registry"""
        # Generate model ID
        model_id = hashlib.md5(f"{model_name}_{datetime.now().isoformat()}".encode()).hexdigest()
        
        # Create model paths
        model_dir = Path(f"./models/{model_id}")
        model_dir.mkdir(parents=True, exist_ok=True)
        
        model_path = str(model_dir / "model.pth")
        config_path = str(model_dir / "config.json")
        
        # Save model
        torch.save(model.state_dict(), model_path)
        
        # Save configuration
        config_dict = {
            'training_config': asdict(self.config),
            'model_config': asdict(self.config.model_config),
            'feature_config': asdict(self.config.feature_config),
            'training_history': training_history,
            'feature_names': self.feature_engineer.feature_names
        }
        
        with open(config_path, 'w') as f:
            json.dump(config_dict, f, indent=2, default=str)
        
        # Create model registry entry
        performance = ModelPerformance(
            training_accuracy=1.0 - training_history['train_loss'][-1],
            validation_accuracy=1.0 - training_history['val_loss'][-1],
            test_accuracy=1.0 - test_metrics['mse'],
            precision=test_metrics['direction_accuracy'],
            recall=test_metrics['direction_accuracy'],
            f1_score=test_metrics['direction_accuracy'],
            backtest_return=0.0,  # To be calculated later
            sharpe_ratio=0.0,     # To be calculated later
            max_drawdown=0.0,     # To be calculated later
            win_rate=test_metrics['direction_accuracy'],
            profit_factor=1.0     # To be calculated later
        )
        
        deployment = ModelDeployment(
            status=ModelStatus.TESTING
        )
        
        training_info = ModelTraining(
            dataset_size=len(training_data),
            features_used=self.feature_engineer.feature_names,
            hyperparameters=asdict(self.config.model_config),
            training_duration=0.0,  # To be calculated
            last_trained=datetime.now(),
            training_data_hash=hashlib.md5(str(training_data.values).encode()).hexdigest()
        )
        
        ml_model = MLModel(
            id=model_id,
            name=model_name,
            version="1.0.0",
            model_type=ModelType.TRANSFORMER,
            description=f"Transformer model for {self.config.model_type}",
            performance=performance,
            deployment=deployment,
            training=training_info,
            created_at=datetime.now(),
            updated_at=datetime.now(),
            model_path=model_path,
            config_path=config_path,
            metadata={'test_metrics': test_metrics}
        )
        
        # Register model
        self.model_registry.register_model(ml_model)
        
        return model_id
