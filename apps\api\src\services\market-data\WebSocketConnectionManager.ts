/**
 * WebSocket Connection Manager for Market Data
 * 
 * Manages WebSocket connections to market data providers with automatic
 * reconnection, health monitoring, and rate limiting integration.
 * Implements connection pool management for multiple data sources.
 */

import { WebSocket } from 'ws';
import { EventEmitter } from 'events';

export interface ConnectionConfig {
  url: string;
  protocol?: string;
  headers?: Record<string, string>;
  timeout?: number;
  maxReconnectAttempts?: number;
  reconnectInterval?: number;
  maxReconnectInterval?: number;
  backoffMultiplier?: number;
  heartbeatInterval?: number;
  connectionId?: string;
}

export interface ConnectionStats {
  connectionId: string;
  url: string;
  status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'reconnecting';
  connectedAt?: Date;
  lastHeartbeat?: Date;
  reconnectAttempts: number;
  messagesSent: number;
  messagesReceived: number;
  lastError?: Error;
}

export interface MarketDataMessage {
  type: 'price' | 'heartbeat' | 'error' | 'subscription_ack';
  timestamp: Date;
  data: any;
  source: string;
}

/**
 * WebSocket Connection Manager with exponential backoff and health monitoring
 */
export class WebSocketConnectionManager extends EventEmitter {
  private connections: Map<string, WebSocket> = new Map();
  private connectionConfigs: Map<string, ConnectionConfig> = new Map();
  private connectionStats: Map<string, ConnectionStats> = new Map();
  private reconnectTimeouts: Map<string, NodeJS.Timeout> = new Map();
  private heartbeatIntervals: Map<string, NodeJS.Timeout> = new Map();
  private isShuttingDown: boolean = false;

  constructor() {
    super();
    this.setMaxListeners(100); // Allow many connections
  }

  /**
   * Add a new connection configuration to the pool
   */
  public addConnection(config: ConnectionConfig): string {
    const connectionId = config.connectionId || `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const fullConfig: ConnectionConfig = {
      timeout: 30000,
      maxReconnectAttempts: 10,
      reconnectInterval: 1000,
      maxReconnectInterval: 60000,
      backoffMultiplier: 2,
      heartbeatInterval: 30000,
      ...config,
      connectionId,
    };

    this.connectionConfigs.set(connectionId, fullConfig);
    this.initializeConnectionStats(connectionId, fullConfig);
    
    return connectionId;
  }

  /**
   * Initialize connection statistics
   */
  private initializeConnectionStats(connectionId: string, config: ConnectionConfig): void {
    this.connectionStats.set(connectionId, {
      connectionId,
      url: config.url,
      status: 'disconnected',
      reconnectAttempts: 0,
      messagesSent: 0,
      messagesReceived: 0,
    });
  }

  /**
   * Connect to a specific data source
   */
  public async connect(connectionId: string): Promise<void> {
    if (this.isShuttingDown) {
      throw new Error('Connection manager is shutting down');
    }

    const config = this.connectionConfigs.get(connectionId);
    if (!config) {
      throw new Error(`Connection config not found for ID: ${connectionId}`);
    }

    const stats = this.connectionStats.get(connectionId)!;
    stats.status = 'connecting';

    try {
      await this.establishConnection(connectionId, config);
      this.emit('connection_established', { connectionId, url: config.url });
    } catch (error) {
      this.handleConnectionError(connectionId, error as Error);
      throw error;
    }
  }

  /**
   * Establish WebSocket connection with timeout handling
   */
  private async establishConnection(connectionId: string, config: ConnectionConfig): Promise<void> {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(config.url, config.protocol, {
        headers: config.headers,
        handshakeTimeout: config.timeout,
      });

      const timeout = setTimeout(() => {
        ws.terminate();
        reject(new Error(`Connection timeout for ${config.url}`));
      }, config.timeout);

      ws.on('open', () => {
        clearTimeout(timeout);
        this.handleConnectionOpen(connectionId, ws);
        resolve();
      });

      ws.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * Handle successful connection establishment
   */
  private handleConnectionOpen(connectionId: string, ws: WebSocket): void {
    const config = this.connectionConfigs.get(connectionId)!;
    const stats = this.connectionStats.get(connectionId)!;

    // Store connection and update stats
    this.connections.set(connectionId, ws);
    stats.status = 'connected';
    stats.connectedAt = new Date();
    stats.reconnectAttempts = 0;
    stats.lastError = undefined;

    // Set up event handlers
    this.setupConnectionEventHandlers(connectionId, ws);

    // Start heartbeat monitoring
    this.startHeartbeat(connectionId);

    this.emit('connected', { connectionId, url: config.url });
  }

  /**
   * Set up WebSocket event handlers for a connection
   */
  private setupConnectionEventHandlers(connectionId: string, ws: WebSocket): void {
    const stats = this.connectionStats.get(connectionId)!;

    ws.on('message', (data: Buffer) => {
      try {
        stats.messagesReceived++;
        stats.lastHeartbeat = new Date();
        
        const message = JSON.parse(data.toString()) as MarketDataMessage;
        message.timestamp = new Date();
        message.source = connectionId;

        this.emit('message', { connectionId, message });
      } catch (error) {
        this.emit('parse_error', { connectionId, error, data: data.toString() });
      }
    });

    ws.on('close', (code: number, reason: Buffer) => {
      this.handleConnectionClose(connectionId, code, reason.toString());
    });

    ws.on('error', (error: Error) => {
      this.handleConnectionError(connectionId, error);
    });

    ws.on('pong', () => {
      stats.lastHeartbeat = new Date();
    });
  }

  /**
   * Handle connection close event
   */
  private handleConnectionClose(connectionId: string, code: number, reason: string): void {
    const stats = this.connectionStats.get(connectionId)!;
    const config = this.connectionConfigs.get(connectionId)!;

    this.cleanup(connectionId);
    stats.status = 'disconnected';

    this.emit('disconnected', { connectionId, code, reason, url: config.url });

    // Attempt reconnection if not shutting down
    if (!this.isShuttingDown && code !== 1000) { // 1000 = normal closure
      this.scheduleReconnect(connectionId);
    }
  }

  /**
   * Handle connection error
   */
  private handleConnectionError(connectionId: string, error: Error): void {
    const stats = this.connectionStats.get(connectionId)!;
    const config = this.connectionConfigs.get(connectionId)!;

    stats.status = 'error';
    stats.lastError = error;
    
    this.cleanup(connectionId);
    this.emit('error', { connectionId, error, url: config.url });

    // Schedule reconnection attempt
    if (!this.isShuttingDown) {
      this.scheduleReconnect(connectionId);
    }
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  private scheduleReconnect(connectionId: string): void {
    const config = this.connectionConfigs.get(connectionId)!;
    const stats = this.connectionStats.get(connectionId)!;

    if (stats.reconnectAttempts >= config.maxReconnectAttempts!) {
      this.emit('max_reconnect_attempts_reached', { connectionId, url: config.url });
      return;
    }

    stats.status = 'reconnecting';
    stats.reconnectAttempts++;

    // Calculate backoff delay
    const baseDelay = config.reconnectInterval!;
    const maxDelay = config.maxReconnectInterval!;
    const backoffMultiplier = config.backoffMultiplier!;
    
    const delay = Math.min(
      baseDelay * Math.pow(backoffMultiplier, stats.reconnectAttempts - 1),
      maxDelay
    );

    this.emit('reconnecting', { 
      connectionId, 
      attempt: stats.reconnectAttempts, 
      delay,
      url: config.url 
    });

    const timeout = setTimeout(async () => {
      this.reconnectTimeouts.delete(connectionId);
      
      try {
        await this.connect(connectionId);
      } catch (error) {
        // Error handling is done in connect method
      }
    }, delay);

    this.reconnectTimeouts.set(connectionId, timeout);
  }

  /**
   * Start heartbeat monitoring for connection health
   */
  private startHeartbeat(connectionId: string): void {
    const config = this.connectionConfigs.get(connectionId)!;
    
    const interval = setInterval(() => {
      const ws = this.connections.get(connectionId);
      if (ws && ws.readyState === WebSocket.OPEN) {
        ws.ping();
      }
    }, config.heartbeatInterval!);

    this.heartbeatIntervals.set(connectionId, interval);
  }

  /**
   * Send message through specific connection
   */
  public sendMessage(connectionId: string, message: any): boolean {
    const ws = this.connections.get(connectionId);
    const stats = this.connectionStats.get(connectionId);

    if (!ws || ws.readyState !== WebSocket.OPEN || !stats) {
      return false;
    }

    try {
      const messageStr = typeof message === 'string' ? message : JSON.stringify(message);
      ws.send(messageStr);
      stats.messagesSent++;
      return true;
    } catch (error) {
      this.emit('send_error', { connectionId, error, message });
      return false;
    }
  }

  /**
   * Disconnect specific connection
   */
  public disconnect(connectionId: string, code: number = 1000, reason: string = 'Normal closure'): void {
    const ws = this.connections.get(connectionId);
    if (ws) {
      ws.close(code, reason);
    }
    this.cleanup(connectionId);
  }

  /**
   * Disconnect all connections
   */
  public disconnectAll(): void {
    this.isShuttingDown = true;
    
    for (const connectionId of this.connections.keys()) {
      this.disconnect(connectionId);
    }
  }

  /**
   * Clean up connection resources
   */
  private cleanup(connectionId: string): void {
    // Clear reconnect timeout
    const reconnectTimeout = this.reconnectTimeouts.get(connectionId);
    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout);
      this.reconnectTimeouts.delete(connectionId);
    }

    // Clear heartbeat interval
    const heartbeatInterval = this.heartbeatIntervals.get(connectionId);
    if (heartbeatInterval) {
      clearInterval(heartbeatInterval);
      this.heartbeatIntervals.delete(connectionId);
    }

    // Remove connection reference
    this.connections.delete(connectionId);
  }

  /**
   * Get connection statistics
   */
  public getConnectionStats(connectionId?: string): ConnectionStats | ConnectionStats[] {
    if (connectionId) {
      const stats = this.connectionStats.get(connectionId);
      if (!stats) {
        throw new Error(`Connection stats not found for ID: ${connectionId}`);
      }
      return stats;
    }

    return Array.from(this.connectionStats.values());
  }

  /**
   * Get healthy connections count
   */
  public getHealthyConnectionsCount(): number {
    return Array.from(this.connectionStats.values())
      .filter(stats => stats.status === 'connected').length;
  }

  /**
   * Check if connection is healthy
   */
  public isConnectionHealthy(connectionId: string): boolean {
    const stats = this.connectionStats.get(connectionId);
    if (!stats || stats.status !== 'connected') {
      return false;
    }

    // Check if heartbeat is recent (within 2x heartbeat interval)
    const config = this.connectionConfigs.get(connectionId);
    if (config && stats.lastHeartbeat) {
      const maxHeartbeatAge = (config.heartbeatInterval! * 2);
      const heartbeatAge = Date.now() - stats.lastHeartbeat.getTime();
      return heartbeatAge < maxHeartbeatAge;
    }

    return true;
  }

  /**
   * Remove connection configuration and cleanup
   */
  public removeConnection(connectionId: string): void {
    this.disconnect(connectionId);
    this.connectionConfigs.delete(connectionId);
    this.connectionStats.delete(connectionId);
  }

  /**
   * Get all connection IDs
   */
  public getConnectionIds(): string[] {
    return Array.from(this.connectionConfigs.keys());
  }

  /**
   * Shutdown the connection manager
   */
  public shutdown(): void {
    this.disconnectAll();
    this.removeAllListeners();
  }
}