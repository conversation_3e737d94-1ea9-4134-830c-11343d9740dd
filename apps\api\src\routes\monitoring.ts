/**
 * Monitoring and Alerting API Routes
 * 
 * API endpoints for real-time monitoring and alerting
 * Part of Task 4: Real-time Monitoring and Alerting
 */

import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { BrokerMonitoringService } from '../services/monitoring/BrokerMonitoringService.js';
import { authMiddleware } from '../middleware/auth.js';
import { rateLimiter } from '../middleware/rate-limit.js';
import type {
  GetMonitoringDashboardResponse,
  GetAlertsResponse,
  GetMetricsResponse,
  UpdateAlertResponse,
  AlertFilters
} from '@golddaddy/types';

const router = Router();
const prisma = new PrismaClient();

// Note: BrokerMonitoringService will be injected via dependency injection
// For now, we'll simulate with a placeholder
let monitoringService: BrokerMonitoringService;

// Apply authentication to all routes
router.use(authMiddleware);

// Rate limiting for monitoring operations
const monitoringRateLimit = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 200, // limit each IP to 200 requests per windowMs
  message: 'Too many monitoring requests'
});

router.use(monitoringRateLimit);

// Middleware to inject monitoring service (to be updated with proper DI)
router.use((req, res, next) => {
  if (!monitoringService) {
    return res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Monitoring service not initialized'
      }
    });
  }
  next();
});

/**
 * GET /api/monitoring/dashboard
 * Get comprehensive monitoring dashboard data
 */
router.get('/dashboard', async (req, res) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    const dashboardData = await monitoringService.getDashboardData(userId);
    
    const response: GetMonitoringDashboardResponse = {
      success: true,
      data: {
        metrics: dashboardData.metrics,
        recentAlerts: dashboardData.recentAlerts,
        brokerStatuses: dashboardData.brokerStatuses,
        systemHealth: {
          overallStatus: dashboardData.metrics.unhealthyBrokers > 0 ? 'degraded' : 'healthy',
          uptime: Date.now() - dashboardData.metrics.lastUpdateTime.getTime(),
          lastUpdate: dashboardData.metrics.lastUpdateTime
        }
      }
    };

    res.status(200).json(response);
    
  } catch (error) {
    console.error('Error getting monitoring dashboard:', error);
    const response: GetMonitoringDashboardResponse = {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve monitoring dashboard'
      }
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/monitoring/metrics
 * Get current monitoring metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    const metrics = monitoringService.getCurrentMetrics();
    
    const response: GetMetricsResponse = {
      success: true,
      data: metrics
    };

    res.status(200).json(response);
    
  } catch (error) {
    console.error('Error getting metrics:', error);
    const response: GetMetricsResponse = {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve metrics'
      }
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/monitoring/alerts
 * Get alerts with optional filtering
 */
router.get('/alerts', async (req, res) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    // Parse query parameters for filtering
    const filters: AlertFilters = {
      severity: req.query.severity as any,
      type: req.query.type as any,
      brokerId: req.query.brokerId as string,
      acknowledged: req.query.acknowledged === 'true' ? true : 
                     req.query.acknowledged === 'false' ? false : undefined,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
      offset: req.query.offset ? parseInt(req.query.offset as string) : 0
    };

    // Validate limit and offset
    if (filters.limit && (filters.limit < 1 || filters.limit > 200)) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_LIMIT',
          message: 'Limit must be between 1 and 200'
        }
      });
    }

    if (filters.offset && filters.offset < 0) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_OFFSET',
          message: 'Offset must be non-negative'
        }
      });
    }

    const alerts = monitoringService.getAlertHistory(filters);
    
    // Get total count for pagination (simplified approach)
    const totalAlerts = monitoringService.getAlertHistory({ brokerId: filters.brokerId });
    
    const response: GetAlertsResponse = {
      success: true,
      data: {
        alerts,
        totalCount: totalAlerts.length,
        hasMore: (filters.offset || 0) + alerts.length < totalAlerts.length
      }
    };

    res.status(200).json(response);
    
  } catch (error) {
    console.error('Error getting alerts:', error);
    const response: GetAlertsResponse = {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve alerts'
      }
    };
    res.status(500).json(response);
  }
});

/**
 * PUT /api/monitoring/alerts/:alertId/acknowledge
 * Acknowledge a specific alert
 */
router.put('/alerts/:alertId/acknowledge', async (req, res) => {
  try {
    const userId = req.user?.id;
    const alertId = req.params.alertId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    if (!alertId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Alert ID is required'
        }
      });
    }

    // Find the alert in the service
    const alerts = monitoringService.getAlertHistory({ limit: 1000 });
    const alert = alerts.find(a => a.id === alertId);

    if (!alert) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ALERT_NOT_FOUND',
          message: 'Alert not found'
        }
      });
    }

    // Acknowledge the alert (this would trigger internal service updates)
    alert.acknowledged = true;

    // Update in database
    await prisma.systemError.update({
      where: { id: alertId },
      data: { resolved: true }
    });

    const response: UpdateAlertResponse = {
      success: true,
      data: alert
    };

    res.status(200).json(response);
    
  } catch (error) {
    console.error('Error acknowledging alert:', error);
    const response: UpdateAlertResponse = {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to acknowledge alert'
      }
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/monitoring/alerts/:alertId
 * Get a specific alert by ID
 */
router.get('/alerts/:alertId', async (req, res) => {
  try {
    const userId = req.user?.id;
    const alertId = req.params.alertId;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    if (!alertId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Alert ID is required'
        }
      });
    }

    const alerts = monitoringService.getAlertHistory({ limit: 1000 });
    const alert = alerts.find(a => a.id === alertId);

    if (!alert) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'ALERT_NOT_FOUND',
          message: 'Alert not found'
        }
      });
    }

    const response: UpdateAlertResponse = {
      success: true,
      data: alert
    };

    res.status(200).json(response);
    
  } catch (error) {
    console.error('Error getting alert:', error);
    const response: UpdateAlertResponse = {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve alert'
      }
    };
    res.status(500).json(response);
  }
});

/**
 * GET /api/monitoring/health
 * Get system health status
 */
router.get('/health', async (req, res) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    const metrics = monitoringService.getCurrentMetrics();
    
    const systemHealth = {
      status: metrics.unhealthyBrokers === 0 ? 'healthy' : 
              metrics.unhealthyBrokers < metrics.totalBrokers / 2 ? 'degraded' : 'critical',
      timestamp: new Date(),
      metrics: {
        totalBrokers: metrics.totalBrokers,
        healthyBrokers: metrics.healthyBrokers,
        unhealthyBrokers: metrics.unhealthyBrokers,
        averageLatency: metrics.averageLatency,
        errorRate: metrics.errorRate,
        alertCount: metrics.alertCount
      },
      uptime: Date.now() - metrics.lastUpdateTime.getTime()
    };

    res.status(200).json({
      success: true,
      data: systemHealth
    });
    
  } catch (error) {
    console.error('Error getting system health:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve system health'
      }
    });
  }
});

/**
 * WebSocket endpoint info (for client connection)
 */
router.get('/websocket-info', (req, res) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    res.status(200).json({
      success: true,
      data: {
        websocketUrl: process.env.WEBSOCKET_URL || 'ws://localhost:8080',
        protocols: ['monitoring'],
        reconnectInterval: 5000,
        maxReconnectAttempts: 10
      }
    });
    
  } catch (error) {
    console.error('Error getting WebSocket info:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve WebSocket info'
      }
    });
  }
});

// Function to inject monitoring service (to be called from main app)
export const setMonitoringService = (service: BrokerMonitoringService) => {
  monitoringService = service;
};

export default router;