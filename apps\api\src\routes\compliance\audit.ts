import { Router, Request, Response } from 'express'
import { body, param, query, validationResult } from 'express-validator'
import { 
  AuditLoggingService, 
  AuditEventType, 
  AuditCategory,
  AuditLogEntry 
} from '../../services/compliance/AuditLoggingService'
import { authenticateToken } from '../../middleware/auth'
import { rateLimiter } from '../../middleware/rateLimiter'

const router = Router()
const auditService = new AuditLoggingService()

// Rate limiting for audit endpoints
const adminLimiter = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 1000, // Higher limit for audit operations
  message: { error: 'Too many audit requests. Please try again later.' }
})

// Middleware to check admin/compliance role
const requireComplianceRole = (req: Request, res: Response, next: Function) => {
  const userRole = (req as any).user?.role
  if (userRole !== 'admin' && userRole !== 'compliance') {
    return res.status(403).json({
      success: false,
      error: 'Insufficient permissions. Compliance or admin role required.'
    })
  }
  next()
}

// Validation middleware
const validateAuditSearch = [
  query('userId').optional().isUUID().withMessage('Invalid user ID format'),
  query('eventType').optional().isArray().withMessage('Event type must be an array'),
  query('category').optional().isArray().withMessage('Category must be an array'),
  query('startDate').optional().isISO8601().withMessage('Invalid start date format'),
  query('endDate').optional().isISO8601().withMessage('Invalid end date format'),
  query('page').optional().isInt({ min: 1 }).toInt(),
  query('pageSize').optional().isInt({ min: 1, max: 1000 }).toInt(),
  query('ipAddress').optional().isIP().withMessage('Invalid IP address format'),
  query('outcome').optional().isIn(['success', 'failure', 'warning']),
  query('sensitiveData').optional().isBoolean().toBoolean(),
  query('searchText').optional().isLength({ max: 200 }).withMessage('Search text too long')
]

const validateComplianceReport = [
  query('regulation').isIn(['GDPR', 'CCPA', 'SOX', 'MIFID_II', 'FINRA', 'AML', 'KYC', 'PCI_DSS'])
    .withMessage('Invalid regulation type'),
  query('startDate').isISO8601().withMessage('Valid start date required'),
  query('endDate').isISO8601().withMessage('Valid end date required')
]

const handleValidationErrors = (req: Request, res: Response, next: Function) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      errors: errors.array()
    })
  }
  next()
}

// Middleware to log API access for audit trails
const logApiAccess = async (req: Request, res: Response, next: Function) => {
  try {
    const user = (req as any).user
    await auditService.logEvent(AuditEventType.DATA_VIEWED, {
      userId: user?.id,
      action: `Accessed audit logs API: ${req.method} ${req.path}`,
      resource: 'audit_logs',
      details: {
        method: req.method,
        path: req.path,
        query: req.query,
        userRole: user?.role
      },
      ipAddress: req.ip || req.connection.remoteAddress || '127.0.0.1',
      userAgent: req.get('User-Agent') || 'unknown',
      requestId: (req as any).requestId
    })
  } catch (error) {
    console.error('Failed to log audit API access:', error)
    // Don't fail the request if audit logging fails
  }
  next()
}

// Search audit logs with filters and pagination
router.get('/',
  adminLimiter,
  authenticateToken,
  requireComplianceRole,
  logApiAccess,
  validateAuditSearch,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const page = req.query.page as number || 1
      const pageSize = req.query.pageSize as number || 50

      const filters: any = {}

      if (req.query.userId) {
        filters.userId = req.query.userId as string
      }

      if (req.query.eventType) {
        filters.eventType = Array.isArray(req.query.eventType) 
          ? req.query.eventType as AuditEventType[]
          : [req.query.eventType as AuditEventType]
      }

      if (req.query.category) {
        filters.category = Array.isArray(req.query.category) 
          ? req.query.category as AuditCategory[]
          : [req.query.category as AuditCategory]
      }

      if (req.query.startDate && req.query.endDate) {
        filters.dateRange = {
          start: new Date(req.query.startDate as string),
          end: new Date(req.query.endDate as string)
        }
      }

      if (req.query.ipAddress) {
        filters.ipAddress = req.query.ipAddress as string
      }

      if (req.query.outcome) {
        filters.outcome = Array.isArray(req.query.outcome) 
          ? req.query.outcome
          : [req.query.outcome]
      }

      if (req.query.sensitiveData !== undefined) {
        filters.sensitiveData = req.query.sensitiveData as boolean
      }

      if (req.query.complianceFlags) {
        filters.complianceFlags = Array.isArray(req.query.complianceFlags) 
          ? req.query.complianceFlags as string[]
          : [req.query.complianceFlags as string]
      }

      if (req.query.searchText) {
        filters.searchText = req.query.searchText as string
      }

      const result = await auditService.searchAuditLogs(filters, page, pageSize)

      res.json({
        success: true,
        data: {
          logs: result.logs,
          pagination: {
            page,
            pageSize,
            total: result.total,
            pages: Math.ceil(result.total / pageSize)
          },
          filters
        }
      })

    } catch (error) {
      console.error('Error searching audit logs:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to search audit logs'
      })
    }
  }
)

// Get audit statistics and dashboard data
router.get('/statistics',
  adminLimiter,
  authenticateToken,
  requireComplianceRole,
  logApiAccess,
  [
    query('startDate').optional().isISO8601(),
    query('endDate').optional().isISO8601()
  ],
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      let dateRange: { start: Date; end: Date } | undefined

      if (req.query.startDate && req.query.endDate) {
        dateRange = {
          start: new Date(req.query.startDate as string),
          end: new Date(req.query.endDate as string)
        }
      }

      const statistics = await auditService.getAuditStatistics(dateRange)

      res.json({
        success: true,
        data: {
          statistics,
          dateRange
        }
      })

    } catch (error) {
      console.error('Error getting audit statistics:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to get audit statistics'
      })
    }
  }
)

// Get specific audit log entry by ID
router.get('/:id',
  adminLimiter,
  authenticateToken,
  requireComplianceRole,
  logApiAccess,
  [param('id').isUUID().withMessage('Invalid audit log ID')],
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const { data, error } = await auditService['supabase']
        .from('audit_logs')
        .select('*')
        .eq('id', req.params.id)
        .single()

      if (error) {
        return res.status(404).json({
          success: false,
          error: 'Audit log entry not found'
        })
      }

      // Additional logging for sensitive data access
      if (data.sensitive_data) {
        await auditService.logEvent(AuditEventType.DATA_VIEWED, {
          userId: (req as any).user?.id,
          action: `Viewed sensitive audit log entry: ${req.params.id}`,
          resource: 'audit_logs',
          resourceId: req.params.id,
          details: {
            auditEntryType: data.event_type,
            complianceFlags: data.compliance_flags
          },
          ipAddress: req.ip || '127.0.0.1',
          userAgent: req.get('User-Agent') || 'unknown',
          outcome: 'success',
          requestId: (req as any).requestId
        })
      }

      res.json({
        success: true,
        data: data as AuditLogEntry
      })

    } catch (error) {
      console.error('Error getting audit log entry:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to get audit log entry'
      })
    }
  }
)

// Generate compliance report for specific regulation
router.get('/compliance/report',
  adminLimiter,
  authenticateToken,
  requireComplianceRole,
  logApiAccess,
  validateComplianceReport,
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const regulation = req.query.regulation as string
      const startDate = new Date(req.query.startDate as string)
      const endDate = new Date(req.query.endDate as string)

      const report = await auditService.generateComplianceReport(regulation, {
        start: startDate,
        end: endDate
      })

      // Log compliance report generation
      await auditService.logEvent(AuditEventType.DATA_EXPORTED, {
        userId: (req as any).user?.id,
        action: `Generated compliance report for ${regulation}`,
        resource: 'compliance_report',
        details: {
          regulation,
          dateRange: { startDate, endDate },
          totalEvents: report.totalEvents,
          criticalEvents: report.criticalEvents,
          violations: report.violations
        },
        ipAddress: req.ip || '127.0.0.1',
        userAgent: req.get('User-Agent') || 'unknown',
        outcome: 'success',
        requestId: (req as any).requestId
      })

      res.json({
        success: true,
        data: report
      })

    } catch (error) {
      console.error('Error generating compliance report:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to generate compliance report'
      })
    }
  }
)

// Export audit logs (with strict access controls)
router.post('/export',
  adminLimiter,
  authenticateToken,
  requireComplianceRole,
  logApiAccess,
  [
    body('filters').isObject().withMessage('Filters must be an object'),
    body('format').isIn(['json', 'csv']).withMessage('Format must be json or csv'),
    body('maxRecords').optional().isInt({ min: 1, max: 10000 }).toInt()
  ],
  handleValidationErrors,
  async (req: Request, res: Response) => {
    try {
      const { filters, format = 'json', maxRecords = 1000 } = req.body
      const user = (req as any).user

      // Additional validation for export operations
      if (!filters.dateRange || !filters.dateRange.start || !filters.dateRange.end) {
        return res.status(400).json({
          success: false,
          error: 'Date range is required for audit log exports'
        })
      }

      const result = await auditService.searchAuditLogs(filters, 1, maxRecords)

      // Log the export operation
      await auditService.logEvent(AuditEventType.DATA_EXPORTED, {
        userId: user.id,
        action: `Exported ${result.logs.length} audit log entries`,
        resource: 'audit_logs',
        details: {
          format,
          recordCount: result.logs.length,
          filters,
          exportedBy: user.email
        },
        ipAddress: req.ip || '127.0.0.1',
        userAgent: req.get('User-Agent') || 'unknown',
        outcome: 'success',
        requestId: (req as any).requestId
      })

      if (format === 'csv') {
        // Convert to CSV format
        const csv = convertToCSV(result.logs)
        res.setHeader('Content-Type', 'text/csv')
        res.setHeader('Content-Disposition', `attachment; filename="audit_logs_${Date.now()}.csv"`)
        res.send(csv)
      } else {
        res.json({
          success: true,
          data: {
            logs: result.logs,
            exportInfo: {
              timestamp: new Date().toISOString(),
              exportedBy: user.email,
              recordCount: result.logs.length,
              format
            }
          }
        })
      }

    } catch (error) {
      console.error('Error exporting audit logs:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to export audit logs'
      })
    }
  }
)

// Apply retention policies (admin only)
router.post('/retention/apply',
  adminLimiter,
  authenticateToken,
  requireComplianceRole,
  logApiAccess,
  async (req: Request, res: Response) => {
    try {
      const user = (req as any).user
      
      // Additional check for admin role for retention operations
      if (user.role !== 'admin') {
        return res.status(403).json({
          success: false,
          error: 'Admin role required for retention policy operations'
        })
      }

      const result = await auditService.applyRetentionPolicies()

      res.json({
        success: true,
        data: {
          deleted: result.deleted,
          errors: result.errors,
          timestamp: new Date().toISOString()
        },
        message: `Successfully applied retention policies. Deleted ${result.deleted} audit logs.`
      })

    } catch (error) {
      console.error('Error applying retention policies:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to apply retention policies'
      })
    }
  }
)

// Helper function to convert audit logs to CSV
function convertToCSV(logs: AuditLogEntry[]): string {
  if (logs.length === 0) return 'No data available'

  const headers = [
    'Timestamp',
    'Event Type',
    'Category',
    'User ID',
    'Action',
    'Resource',
    'Resource ID',
    'IP Address',
    'Outcome',
    'Compliance Flags',
    'Sensitive Data'
  ]

  const rows = logs.map(log => [
    log.timestamp,
    log.eventType,
    log.category,
    log.userId || '',
    log.action,
    log.resource,
    log.resourceId || '',
    log.ipAddress,
    log.outcome,
    log.complianceFlags.join(';'),
    log.sensitiveData
  ])

  const csvContent = [headers, ...rows]
    .map(row => row.map(field => `"${field}"`).join(','))
    .join('\n')

  return csvContent
}

// Event types and categories for frontend
router.get('/metadata/types',
  adminLimiter,
  authenticateToken,
  requireComplianceRole,
  async (req: Request, res: Response) => {
    res.json({
      success: true,
      data: {
        eventTypes: Object.values(AuditEventType),
        categories: Object.values(AuditCategory),
        outcomes: ['success', 'failure', 'warning'],
        complianceFlags: ['GDPR', 'CCPA', 'SOX', 'MIFID_II', 'FINRA', 'AML', 'KYC', 'PCI_DSS']
      }
    })
  }
)

export default router