/**
 * Monitoring WebSocket Client Example
 * 
 * Example WebSocket client for testing real-time monitoring
 * Part of Task 4: Real-time Monitoring and Alerting
 */

import WebSocket from 'ws';
import type { WebSocketMessage, DashboardUpdate, AlertNotification, HealthUpdateMessage } from '@golddaddy/types';

class MonitoringWebSocketClient {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 10;
  private reconnectInterval = 5000;
  private isConnecting = false;

  constructor(private url: string) {}

  /**
   * Connect to the monitoring WebSocket server
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting) {
        return;
      }

      this.isConnecting = true;
      console.log(`📡 Connecting to monitoring WebSocket: ${this.url}`);

      this.ws = new WebSocket(this.url);

      this.ws.on('open', () => {
        console.log('✅ WebSocket connected successfully');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        
        // Request initial dashboard data
        this.requestDashboardData();
        
        resolve();
      });

      this.ws.on('message', (data) => {
        this.handleMessage(data.toString());
      });

      this.ws.on('close', (code, reason) => {
        console.log(`📡 WebSocket closed: ${code} - ${reason}`);
        this.isConnecting = false;
        this.scheduleReconnect();
      });

      this.ws.on('error', (error) => {
        console.error('📡 WebSocket error:', error);
        this.isConnecting = false;
        
        if (this.reconnectAttempts === 0) {
          reject(error);
        } else {
          this.scheduleReconnect();
        }
      });
    });
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(message: string): void {
    try {
      const parsedMessage = JSON.parse(message);
      
      switch (parsedMessage.type) {
        case 'dashboard_init':
          this.handleDashboardInit(parsedMessage as DashboardUpdate);
          break;
          
        case 'new_alert':
          this.handleNewAlert(parsedMessage as AlertNotification);
          break;
          
        case 'health_update':
          this.handleHealthUpdate(parsedMessage as HealthUpdateMessage);
          break;
          
        case 'metrics_update':
          this.handleMetricsUpdate(parsedMessage as DashboardUpdate);
          break;
          
        case 'alert_acknowledged':
          this.handleAlertAcknowledged(parsedMessage);
          break;
          
        default:
          console.log('📡 Unknown message type:', parsedMessage.type);
      }
    } catch (error) {
      console.error('📡 Failed to parse WebSocket message:', error);
    }
  }

  /**
   * Handle dashboard initialization
   */
  private handleDashboardInit(message: DashboardUpdate): void {
    console.log('📊 Dashboard initialized:', {
      metrics: message.data.metrics,
      alertCount: message.data.recentAlerts?.length || 0,
      brokerCount: message.data.brokerStatuses?.length || 0
    });

    // Display key metrics
    if (message.data.metrics) {
      const metrics = message.data.metrics;
      console.log(`📈 System Metrics:
        Total Brokers: ${metrics.totalBrokers}
        Healthy: ${metrics.healthyBrokers}
        Unhealthy: ${metrics.unhealthyBrokers}
        Avg Latency: ${metrics.averageLatency.toFixed(2)}ms
        Error Rate: ${(metrics.errorRate * 100).toFixed(2)}%
        Alert Count: ${metrics.alertCount}`);
    }

    // Display recent alerts
    if (message.data.recentAlerts) {
      console.log(`🚨 Recent Alerts (${message.data.recentAlerts.length}):`);
      message.data.recentAlerts.slice(0, 3).forEach(alert => {
        console.log(`  - [${alert.severity.toUpperCase()}] ${alert.message} (${alert.timestamp})`);
      });
    }
  }

  /**
   * Handle new alert notifications
   */
  private handleNewAlert(message: AlertNotification): void {
    const alert = message.alert;
    const severityIcon = this.getSeverityIcon(alert.severity);
    
    console.log(`🚨 NEW ALERT ${severityIcon} [${alert.severity.toUpperCase()}] ${alert.message}`);
    
    if (alert.brokerId) {
      console.log(`   Broker: ${alert.brokerId}`);
    }
    
    if (alert.metadata) {
      console.log(`   Metadata:`, alert.metadata);
    }

    // Auto-acknowledge info alerts for demo
    if (alert.severity === 'info') {
      setTimeout(() => {
        this.acknowledgeAlert(alert.id);
      }, 2000);
    }
  }

  /**
   * Handle health updates
   */
  private handleHealthUpdate(message: HealthUpdateMessage): void {
    const data = message.data;
    const statusIcon = data.healthy ? '✅' : '❌';
    
    console.log(`${statusIcon} Health Update - Broker ${data.brokerId}: ${data.healthy ? 'HEALTHY' : 'UNHEALTHY'} (${data.latency}ms)`);
    
    if (!data.healthy && data.error) {
      console.log(`   Error: ${data.error}`);
    }
  }

  /**
   * Handle metrics updates
   */
  private handleMetricsUpdate(message: DashboardUpdate): void {
    console.log('📊 Metrics updated:', message.data.metrics);
  }

  /**
   * Handle alert acknowledgment
   */
  private handleAlertAcknowledged(message: any): void {
    console.log(`✅ Alert acknowledged: ${message.alertId}`);
  }

  /**
   * Send a message to the WebSocket server
   */
  private sendMessage(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.error('📡 WebSocket not connected');
    }
  }

  /**
   * Request dashboard data
   */
  requestDashboardData(): void {
    this.sendMessage({
      type: 'request_metrics',
      timestamp: new Date()
    });
  }

  /**
   * Request alerts
   */
  requestAlerts(limit: number = 50): void {
    this.sendMessage({
      type: 'request_alerts',
      limit,
      timestamp: new Date()
    });
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string): void {
    console.log(`👍 Acknowledging alert: ${alertId}`);
    this.sendMessage({
      type: 'acknowledge_alert',
      alertId,
      timestamp: new Date()
    });
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('📡 Max reconnection attempts reached. Giving up.');
      return;
    }

    this.reconnectAttempts++;
    console.log(`📡 Reconnecting in ${this.reconnectInterval}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      this.connect().catch(error => {
        console.error('📡 Reconnection failed:', error);
      });
    }, this.reconnectInterval);
  }

  /**
   * Get severity icon
   */
  private getSeverityIcon(severity: string): string {
    switch (severity) {
      case 'critical': return '🔴';
      case 'high': return '🟠';
      case 'medium': return '🟡';
      case 'low': return '🟢';
      case 'info': return '🔵';
      default: return '⚪';
    }
  }

  /**
   * Close the WebSocket connection
   */
  close(): void {
    if (this.ws) {
      this.ws.close();
      console.log('📡 WebSocket connection closed');
    }
  }
}

// Example usage
async function runMonitoringClient() {
  const wsUrl = process.env.WEBSOCKET_URL || 'ws://localhost:8080';
  const client = new MonitoringWebSocketClient(wsUrl);

  try {
    await client.connect();
    
    // Request alerts periodically
    setInterval(() => {
      client.requestAlerts(10);
    }, 30000);

    // Request metrics periodically
    setInterval(() => {
      client.requestDashboardData();
    }, 60000);

  } catch (error) {
    console.error('Failed to connect to monitoring WebSocket:', error);
    process.exit(1);
  }

  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('\n📡 Shutting down monitoring client...');
    client.close();
    process.exit(0);
  });
}

// Run the client if this file is executed directly
if (require.main === module) {
  runMonitoringClient().catch(console.error);
}

export { MonitoringWebSocketClient };