# API Production Environment Variables
NODE_ENV=production

# Server Configuration
PORT=3001
API_VERSION=v1

# Database Configuration (Production)
DATABASE_URL=***************************************************************/golddaddy_prod?schema=public&connection_limit=20&pool_timeout=20
DIRECT_URL=***************************************************************/golddaddy_prod?schema=public

# Redis Configuration (Production)
REDIS_URL=redis://production-redis:6379
REDIS_PASSWORD=secure_redis_password
REDIS_TTL=3600

# JWT Configuration
JWT_SECRET=your-production-jwt-secret-256-bit-key
JWT_EXPIRES_IN=1h
REFRESH_TOKEN_SECRET=your-production-refresh-token-secret
REFRESH_TOKEN_EXPIRES_IN=30d

# Supabase Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_KEY=your-supabase-service-key

# Security Configuration
CORS_ORIGIN=https://golddaddy.app,https://www.golddaddy.app
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
BCRYPT_ROUNDS=12

# Monitoring and Logging
LOG_LEVEL=warn
SENTRY_DSN=https://<EMAIL>
ENABLE_REQUEST_LOGGING=true

# MT5 Bridge Configuration
MT5_BRIDGE_URL=http://mt5-bridge:3002
MT5_BRIDGE_SECRET=your-mt5-bridge-secret

# External Services
EMAIL_SERVICE_API_KEY=your-email-service-api-key
SMS_SERVICE_API_KEY=your-sms-service-api-key

# Feature Flags
ENABLE_PAPER_TRADING=true
ENABLE_LIVE_TRADING=true
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true

# Performance
CONNECTION_POOL_SIZE=20
QUERY_TIMEOUT_MS=10000