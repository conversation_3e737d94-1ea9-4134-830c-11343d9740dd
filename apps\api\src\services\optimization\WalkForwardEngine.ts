/**
 * Walk-Forward Optimization Engine
 * 
 * @fileoverview Walk-forward analysis framework for strategy optimization validation
 * Includes out-of-sample testing, overfitting detection, and robustness analysis
 */

import type {
  WalkForwardConfig,
  WalkForwardPeriod,
  WalkForwardResult,
  StrategyParameters,
  PerformanceMetrics,
  OptimizationResult
} from '@golddaddy/types/optimization';

// ===== Market Data Interface =====

interface MarketDataPoint {
  timestamp: Date;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

interface StrategyBacktestResult {
  parameters: StrategyParameters;
  performance: PerformanceMetrics;
  tradeLog: {
    entryTime: Date;
    exitTime: Date;
    type: 'buy' | 'sell';
    entryPrice: number;
    exitPrice: number;
    profit: number;
    commission: number;
  }[];
}

// ===== Walk-Forward Engine Class =====

export class WalkForwardEngine {
  private config: WalkForwardConfig;
  private marketData: Map<string, MarketDataPoint[]> = new Map();

  constructor(config: WalkForwardConfig) {
    this.config = this.validateConfig(config);
  }

  // ===== Main Walk-Forward Methods =====

  /**
   * Execute walk-forward optimization analysis
   */
  public async executeWalkForwardAnalysis(
    instruments: string[],
    optimizationFunction: (
      parameters: StrategyParameters,
      startDate: Date,
      endDate: Date
    ) => Promise<StrategyBacktestResult>,
    geneticAlgorithmOptimize: (
      trainingData: MarketDataPoint[],
      startDate: Date,
      endDate: Date
    ) => Promise<StrategyParameters>
  ): Promise<{
    periods: WalkForwardResult[];
    overallStability: number;
    overfittingScore: number;
    robustnessMetrics: {
      parameterStability: number;
      performanceConsistency: number;
      outOfSampleDegradation: number;
      forwardLookingBias: number;
    };
    recommendations: string[];
  }> {

    // Generate walk-forward periods
    const periods = this.generateWalkForwardPeriods();
    
    // Execute optimization for each period
    const results: WalkForwardResult[] = [];
    const parameterHistory: StrategyParameters[] = [];
    const performanceHistory: {
      inSample: PerformanceMetrics;
      outOfSample: PerformanceMetrics;
    }[] = [];

    for (let i = 0; i < periods.length; i++) {
      const period = periods[i];
      
      try {
        // Load market data for this period
        await this.loadMarketDataForPeriod(instruments, period);
        
        // Optimize parameters on training data
        const trainingData = this.getMarketDataForPeriod(
          instruments[0], // Use first instrument for now
          period.trainingStartDate,
          period.trainingEndDate
        );
        
        const optimizedParameters = await geneticAlgorithmOptimize(
          trainingData,
          period.trainingStartDate,
          period.trainingEndDate
        );

        // Test on in-sample data
        const inSampleResult = await optimizationFunction(
          optimizedParameters,
          period.trainingStartDate,
          period.trainingEndDate
        );

        // Test on out-of-sample data
        const outOfSampleResult = await optimizationFunction(
          optimizedParameters,
          period.testingStartDate,
          period.testingEndDate
        );

        // Calculate stability metrics
        const parameterStability = i > 0 
          ? this.calculateParameterStability(optimizedParameters, parameterHistory[i - 1])
          : 1.0;

        const performanceStability = i > 0
          ? this.calculatePerformanceStability(inSampleResult.performance, performanceHistory[i - 1].inSample)
          : 1.0;

        // Calculate overfitting score
        const overfittingScore = this.calculateOverfittingScore(
          inSampleResult.performance,
          outOfSampleResult.performance
        );

        const walkForwardResult: WalkForwardResult = {
          period,
          optimizedParameters,
          inSamplePerformance: inSampleResult.performance,
          outOfSamplePerformance: outOfSampleResult.performance,
          parameterStability,
          performanceStability,
          overfittingScore
        };

        results.push(walkForwardResult);
        parameterHistory.push(optimizedParameters);
        performanceHistory.push({
          inSample: inSampleResult.performance,
          outOfSample: outOfSampleResult.performance
        });

      } catch (error) {
        console.error(`Walk-forward period ${i + 1} failed:`, error);
        // Continue with next period
      }
    }

    // Calculate overall metrics
    const overallStability = this.calculateOverallStability(results);
    const overallOverfitting = this.calculateOverallOverfitting(results);
    const robustnessMetrics = this.calculateRobustnessMetrics(results);
    const recommendations = this.generateRecommendations(results, robustnessMetrics);

    return {
      periods: results,
      overallStability,
      overfittingScore: overallOverfitting,
      robustnessMetrics,
      recommendations
    };
  }

  /**
   * Validate optimization results against walk-forward analysis
   */
  public validateOptimizationResults(
    optimizationResult: OptimizationResult,
    walkForwardResults: WalkForwardResult[]
  ): {
    isValid: boolean;
    confidenceLevel: number;
    warnings: string[];
    adjustedParameters?: StrategyParameters;
  } {
    const warnings: string[] = [];
    let confidenceLevel = 1.0;

    // Check overfitting
    const avgOverfitting = walkForwardResults.reduce((sum, result) => 
      sum + result.overfittingScore, 0) / walkForwardResults.length;
    
    if (avgOverfitting > 0.3) {
      warnings.push('High overfitting detected in walk-forward analysis');
      confidenceLevel *= 0.7;
    }

    // Check parameter stability
    const avgParameterStability = walkForwardResults.reduce((sum, result) => 
      sum + result.parameterStability, 0) / walkForwardResults.length;
    
    if (avgParameterStability < 0.7) {
      warnings.push('Low parameter stability across time periods');
      confidenceLevel *= 0.8;
    }

    // Check performance consistency
    const outOfSampleSharpes = walkForwardResults.map(r => r.outOfSamplePerformance.sharpeRatio);
    const sharpeStdDev = this.calculateStandardDeviation(outOfSampleSharpes);
    const avgSharpe = outOfSampleSharpes.reduce((sum, val) => sum + val, 0) / outOfSampleSharpes.length;
    
    if (sharpeStdDev / Math.abs(avgSharpe) > 0.5) {
      warnings.push('High variability in out-of-sample performance');
      confidenceLevel *= 0.9;
    }

    // Generate adjusted parameters if needed
    let adjustedParameters: StrategyParameters | undefined;
    if (confidenceLevel < 0.8) {
      adjustedParameters = this.generateRobustParameters(walkForwardResults);
    }

    return {
      isValid: confidenceLevel >= 0.7,
      confidenceLevel,
      warnings,
      adjustedParameters
    };
  }

  // ===== Period Generation =====

  /**
   * Generate walk-forward time periods
   */
  private generateWalkForwardPeriods(): WalkForwardPeriod[] {
    const periods: WalkForwardPeriod[] = [];
    const today = new Date();
    
    // Calculate total time span needed
    const totalMonths = this.config.trainingWindowMonths + this.config.testingWindowMonths;
    const totalDays = totalMonths * 30; // Approximate
    
    // Start from far enough back to have sufficient data
    const dataStartDate = new Date(today.getTime() - (totalDays + 365) * 24 * 60 * 60 * 1000);
    
    let currentDate = new Date(dataStartDate);
    
    while (currentDate < today) {
      // Training period
      const trainingStartDate = new Date(currentDate);
      const trainingEndDate = new Date(currentDate.getTime() + this.config.trainingWindowMonths * 30 * 24 * 60 * 60 * 1000);
      
      // Testing period
      const testingStartDate = new Date(trainingEndDate);
      const testingEndDate = new Date(testingStartDate.getTime() + this.config.testingWindowMonths * 30 * 24 * 60 * 60 * 1000);
      
      // Overall period
      const startDate = new Date(trainingStartDate);
      const endDate = new Date(testingEndDate);
      
      // Don't include periods that extend beyond today
      if (testingEndDate <= today) {
        periods.push({
          startDate,
          endDate,
          trainingStartDate,
          trainingEndDate,
          testingStartDate,
          testingEndDate
        });
      }
      
      // Move to next period
      currentDate = new Date(currentDate.getTime() + this.config.stepSizeMonths * 30 * 24 * 60 * 60 * 1000);
    }
    
    return periods.slice(0, Math.max(this.config.minTrainingPeriods, periods.length));
  }

  // ===== Data Management =====

  /**
   * Load market data for a specific period
   */
  private async loadMarketDataForPeriod(instruments: string[], period: WalkForwardPeriod): Promise<void> {
    // This would typically load from a database or API
    // For now, we'll generate mock data
    
    for (const instrument of instruments) {
      const data = this.generateMockMarketData(instrument, period.startDate, period.endDate);
      this.marketData.set(`${instrument}_${period.startDate.toISOString()}`, data);
    }
  }

  /**
   * Get market data for a specific period
   */
  private getMarketDataForPeriod(instrument: string, startDate: Date, endDate: Date): MarketDataPoint[] {
    const key = `${instrument}_${startDate.toISOString()}`;
    const allData = this.marketData.get(key) || [];
    
    return allData.filter(point => 
      point.timestamp >= startDate && point.timestamp <= endDate
    );
  }

  /**
   * Generate mock market data (replace with real data loading)
   */
  private generateMockMarketData(instrument: string, startDate: Date, endDate: Date): MarketDataPoint[] {
    const data: MarketDataPoint[] = [];
    const currentDate = new Date(startDate);
    let currentPrice = 1.1000; // Starting price for EURUSD-like pair
    
    while (currentDate <= endDate) {
      // Simple random walk with slight trend
      const volatility = 0.001;
      const trend = 0.00001;
      const change = (Math.random() - 0.5) * volatility + trend;
      
      currentPrice += change;
      
      const high = currentPrice + Math.random() * volatility * 0.5;
      const low = currentPrice - Math.random() * volatility * 0.5;
      const open = currentPrice - (Math.random() - 0.5) * volatility * 0.3;
      const close = currentPrice;
      
      data.push({
        timestamp: new Date(currentDate),
        open,
        high,
        low,
        close,
        volume: Math.floor(Math.random() * 1000000) + 100000
      });
      
      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return data;
  }

  // ===== Stability Calculations =====

  /**
   * Calculate parameter stability between two parameter sets
   */
  private calculateParameterStability(
    currentParams: StrategyParameters,
    previousParams: StrategyParameters
  ): number {
    const paramNames = Object.keys(currentParams);
    let totalStability = 0;
    
    for (const paramName of paramNames) {
      const currentValue = currentParams[paramName] as number;
      const previousValue = previousParams[paramName] as number;
      
      if (typeof currentValue === 'number' && typeof previousValue === 'number') {
        // Calculate relative change
        const relativeChange = Math.abs(currentValue - previousValue) / Math.abs(previousValue);
        const stability = Math.max(0, 1 - relativeChange);
        totalStability += stability;
      }
    }
    
    return totalStability / paramNames.length;
  }

  /**
   * Calculate performance stability between two performance metrics
   */
  private calculatePerformanceStability(
    currentPerf: PerformanceMetrics,
    previousPerf: PerformanceMetrics
  ): number {
    const metrics = ['sharpeRatio', 'totalReturn', 'maxDrawdown', 'winRate'] as const;
    let totalStability = 0;
    
    for (const metric of metrics) {
      const currentValue = currentPerf[metric];
      const previousValue = previousPerf[metric];
      
      if (typeof currentValue === 'number' && typeof previousValue === 'number' && previousValue !== 0) {
        const relativeChange = Math.abs(currentValue - previousValue) / Math.abs(previousValue);
        const stability = Math.max(0, 1 - relativeChange);
        totalStability += stability;
      }
    }
    
    return totalStability / metrics.length;
  }

  /**
   * Calculate overfitting score
   */
  private calculateOverfittingScore(
    inSamplePerf: PerformanceMetrics,
    outOfSamplePerf: PerformanceMetrics
  ): number {
    // Compare key metrics between in-sample and out-of-sample
    const inSampleSharpe = inSamplePerf.sharpeRatio;
    const outOfSampleSharpe = outOfSamplePerf.sharpeRatio;
    
    const inSampleReturn = inSamplePerf.totalReturn;
    const outOfSampleReturn = outOfSamplePerf.totalReturn;
    
    // Calculate degradation
    const sharpeDegradation = inSampleSharpe !== 0 
      ? Math.max(0, (inSampleSharpe - outOfSampleSharpe) / Math.abs(inSampleSharpe))
      : 0;
      
    const returnDegradation = inSampleReturn !== 0
      ? Math.max(0, (inSampleReturn - outOfSampleReturn) / Math.abs(inSampleReturn))
      : 0;
    
    // Overfitting score (0 = no overfitting, 1 = severe overfitting)
    return (sharpeDegradation + returnDegradation) / 2;
  }

  // ===== Overall Analysis =====

  /**
   * Calculate overall stability across all periods
   */
  private calculateOverallStability(results: WalkForwardResult[]): number {
    if (results.length === 0) return 0;
    
    const avgParameterStability = results.reduce((sum, result) => 
      sum + result.parameterStability, 0) / results.length;
      
    const avgPerformanceStability = results.reduce((sum, result) => 
      sum + result.performanceStability, 0) / results.length;
    
    return (avgParameterStability + avgPerformanceStability) / 2;
  }

  /**
   * Calculate overall overfitting score
   */
  private calculateOverallOverfitting(results: WalkForwardResult[]): number {
    if (results.length === 0) return 0;
    
    return results.reduce((sum, result) => sum + result.overfittingScore, 0) / results.length;
  }

  /**
   * Calculate comprehensive robustness metrics
   */
  private calculateRobustnessMetrics(results: WalkForwardResult[]): {
    parameterStability: number;
    performanceConsistency: number;
    outOfSampleDegradation: number;
    forwardLookingBias: number;
  } {
    if (results.length === 0) {
      return {
        parameterStability: 0,
        performanceConsistency: 0,
        outOfSampleDegradation: 0,
        forwardLookingBias: 0
      };
    }

    // Parameter stability
    const parameterStability = results.reduce((sum, result) => 
      sum + result.parameterStability, 0) / results.length;

    // Performance consistency (inverse of standard deviation)
    const outOfSampleSharpes = results.map(r => r.outOfSamplePerformance.sharpeRatio);
    const sharpeStdDev = this.calculateStandardDeviation(outOfSampleSharpes);
    const avgSharpe = outOfSampleSharpes.reduce((sum, val) => sum + val, 0) / outOfSampleSharpes.length;
    const performanceConsistency = Math.max(0, 1 - (sharpeStdDev / Math.max(0.1, Math.abs(avgSharpe))));

    // Out-of-sample degradation
    const outOfSampleDegradation = results.reduce((sum, result) => 
      sum + result.overfittingScore, 0) / results.length;

    // Forward-looking bias (check if later periods perform consistently better)
    const firstHalf = results.slice(0, Math.floor(results.length / 2));
    const secondHalf = results.slice(Math.floor(results.length / 2));
    
    const firstHalfAvg = firstHalf.reduce((sum, r) => sum + r.outOfSamplePerformance.sharpeRatio, 0) / firstHalf.length;
    const secondHalfAvg = secondHalf.reduce((sum, r) => sum + r.outOfSamplePerformance.sharpeRatio, 0) / secondHalf.length;
    
    const forwardLookingBias = Math.abs(secondHalfAvg - firstHalfAvg) / Math.max(0.1, Math.abs(firstHalfAvg));

    return {
      parameterStability,
      performanceConsistency,
      outOfSampleDegradation,
      forwardLookingBias
    };
  }

  /**
   * Generate recommendations based on walk-forward analysis
   */
  private generateRecommendations(
    results: WalkForwardResult[],
    robustnessMetrics: {
      parameterStability: number;
      performanceConsistency: number;
      outOfSampleDegradation: number;
      forwardLookingBias: number;
    }
  ): string[] {
    const recommendations: string[] = [];

    if (robustnessMetrics.parameterStability < 0.7) {
      recommendations.push('Consider wider parameter ranges or different strategy logic to improve parameter stability');
    }

    if (robustnessMetrics.performanceConsistency < 0.6) {
      recommendations.push('Strategy shows inconsistent performance across time periods - consider regime-based parameter adjustment');
    }

    if (robustnessMetrics.outOfSampleDegradation > 0.3) {
      recommendations.push('High overfitting detected - use simpler strategy logic or reduce optimization complexity');
    }

    if (robustnessMetrics.forwardLookingBias > 0.4) {
      recommendations.push('Performance varies significantly across time - consider market regime detection');
    }

    if (results.length < this.config.minTrainingPeriods) {
      recommendations.push('Insufficient data for reliable walk-forward analysis - gather more historical data');
    }

    // If all metrics are good
    if (recommendations.length === 0) {
      recommendations.push('Strategy shows good robustness across time periods - suitable for live trading');
    }

    return recommendations;
  }

  /**
   * Generate robust parameters based on walk-forward results
   */
  private generateRobustParameters(results: WalkForwardResult[]): StrategyParameters {
    const robustParams: StrategyParameters = {};
    
    // Get all parameter names
    const paramNames = new Set<string>();
    results.forEach(result => {
      Object.keys(result.optimizedParameters).forEach(name => paramNames.add(name));
    });

    // For each parameter, find the median value (more robust than mean)
    for (const paramName of paramNames) {
      const values: number[] = [];
      
      results.forEach(result => {
        const value = result.optimizedParameters[paramName];
        if (typeof value === 'number') {
          values.push(value);
        }
      });

      if (values.length > 0) {
        values.sort((a, b) => a - b);
        const medianIndex = Math.floor(values.length / 2);
        robustParams[paramName] = values.length % 2 === 0
          ? (values[medianIndex - 1] + values[medianIndex]) / 2
          : values[medianIndex];
      }
    }

    return robustParams;
  }

  // ===== Utility Methods =====

  /**
   * Calculate standard deviation
   */
  private calculateStandardDeviation(values: number[]): number {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    
    return Math.sqrt(variance);
  }

  /**
   * Validate walk-forward configuration
   */
  private validateConfig(config: WalkForwardConfig): WalkForwardConfig {
    const validated = { ...config };
    
    // Ensure minimum values
    validated.trainingWindowMonths = Math.max(6, validated.trainingWindowMonths);
    validated.testingWindowMonths = Math.max(1, validated.testingWindowMonths);
    validated.stepSizeMonths = Math.max(1, validated.stepSizeMonths);
    validated.minTrainingPeriods = Math.max(3, validated.minTrainingPeriods);
    
    // Ensure ratios are valid
    validated.outOfSampleRatio = Math.max(0.1, Math.min(0.5, validated.outOfSampleRatio));
    validated.crossValidationFolds = Math.max(3, Math.min(10, validated.crossValidationFolds));
    
    // Ensure thresholds are reasonable
    validated.parameterStabilityThreshold = Math.max(0.05, Math.min(0.5, validated.parameterStabilityThreshold));
    validated.performanceStabilityThreshold = Math.max(0.02, Math.min(0.2, validated.performanceStabilityThreshold));
    
    return validated;
  }
}