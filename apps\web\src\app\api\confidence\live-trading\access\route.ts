import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@golddaddy/types';
import {
  LiveTradingProgress,
  LIVE_TRADING_RESTRICTIONS,
  evaluateLevelUpEligibility,
  generateLiveTradingReport,
  initializeNewLiveTrader,
  validateTradeAgainstRestrictions,
  TradingViolation
} from '@/lib/graduated-live-access';

// Mock live trading progress data (in production, this would be in database)
const mockLiveTradingProgress: Record<string, LiveTradingProgress> = {
  'demo-user': {
    userId: 'demo-user',
    currentLevel: 'developing',
    levelStartDate: new Date('2025-01-01'),
    nextLevelEligibleDate: new Date('2025-03-15'),
    totalLiveTrades: 45,
    liveTradingDays: 42,
    currentMonthPnL: 245.50,
    last3MonthsAvgPnL: 187.25,
    maxDrawdownThisMonth: 3.2,
    riskManagementViolations: 1,
    levelUpRequirements: {
      minTradingDays: 60,
      minTotalTrades: 75,
      minWinRate: 60,
      maxDrawdownLimit: 8.0,
      riskViolationLimit: 1,
      profitabilityRequired: true
    },
    lastReviewDate: new Date('2025-01-15'),
    nextReviewDate: new Date('2025-02-15'),
    mentorAssigned: 'mentor-001',
    performanceNotes: [
      'Upgraded from new_trader after demonstrating consistent profitability',
      'Excellent risk management discipline shown',
      'Ready for intermediate level pending trade count milestone'
    ]
  }
};

// Mock recent performance data
const mockPerformanceData: Record<string, any> = {
  'demo-user': {
    winRate: 64.2,
    maxDrawdown: 4.8,
    profitableDays: 28,
    totalTradingDays: 42,
    avgTradesPerDay: 1.07,
    sharpeRatio: 1.23
  }
};

// Mock trading violations (in production, this would be stored in database)
const tradingViolations: Record<string, TradingViolation[]> = {};

/**
 * GET /api/confidence/live-trading/access
 * Get current live trading access status and restrictions
 */
export async function GET(
  request: NextRequest
): Promise<NextResponse<ApiResponse<any>>> {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    const action = searchParams.get('action') || 'status';

    if (action === 'status') {
      // Get current status and restrictions
      const progress = mockLiveTradingProgress[userId];
      
      if (!progress) {
        return NextResponse.json({
          success: false,
          error: 'No live trading access',
          message: 'User has not been granted live trading access yet'
        }, { status: 404 });
      }

      const currentRestrictions = LIVE_TRADING_RESTRICTIONS[progress.currentLevel];
      const recentPerformance = mockPerformanceData[userId];
      
      const report = generateLiveTradingReport(progress, recentPerformance);

      return NextResponse.json({
        success: true,
        data: {
          progress,
          restrictions: currentRestrictions,
          report,
          violations: tradingViolations[userId] || []
        },
        message: `Live trading access status for level: ${progress.currentLevel}`
      });
    }

    if (action === 'validate_trade') {
      // Validate a proposed trade against current restrictions
      const tradeData = JSON.parse(searchParams.get('trade') || '{}');
      const progress = mockLiveTradingProgress[userId];
      
      if (!progress) {
        return NextResponse.json({
          success: false,
          error: 'No live trading access',
          message: 'User has not been granted live trading access'
        }, { status: 403 });
      }

      // Mock daily stats
      const dailyStats = {
        tradesCount: 2,
        currentDrawdown: 1.5
      };

      const validation = validateTradeAgainstRestrictions(
        tradeData,
        progress.currentLevel,
        dailyStats
      );

      return NextResponse.json({
        success: true,
        data: validation,
        message: validation.allowed ? 'Trade allowed' : 'Trade blocked due to restrictions'
      });
    }

    if (action === 'level_eligibility') {
      // Check eligibility for next level
      const progress = mockLiveTradingProgress[userId];
      const recentPerformance = mockPerformanceData[userId];
      
      if (!progress) {
        return NextResponse.json({
          success: false,
          error: 'No live trading access',
          message: 'User has not been granted live trading access'
        }, { status: 404 });
      }

      const eligibility = evaluateLevelUpEligibility(progress, recentPerformance);

      return NextResponse.json({
        success: true,
        data: eligibility,
        message: eligibility.eligible 
          ? `Ready for upgrade to ${eligibility.nextLevel}` 
          : 'Not yet eligible for level upgrade'
      });
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action',
      message: 'Supported actions: status, validate_trade, level_eligibility'
    }, { status: 400 });

  } catch (error) {
    console.error('Error in live trading access GET:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Access check failed',
        message: 'Failed to check live trading access status'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/confidence/live-trading/access
 * Initialize live trading access or record violations
 */
export async function POST(
  request: NextRequest
): Promise<NextResponse<ApiResponse<any>>> {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    const action = body.action;

    if (action === 'initialize') {
      // Initialize new live trader
      const graduationData = body.graduationData;
      
      if (!graduationData || !graduationData.finalAssessmentScore) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid graduation data',
            message: 'Final assessment score is required for initialization'
          },
          { status: 400 }
        );
      }

      const newProgress = initializeNewLiveTrader(userId, graduationData);
      mockLiveTradingProgress[userId] = newProgress;

      // Initialize empty violations array
      tradingViolations[userId] = [];

      return NextResponse.json({
        success: true,
        data: {
          progress: newProgress,
          restrictions: LIVE_TRADING_RESTRICTIONS[newProgress.currentLevel]
        },
        message: 'Live trading access initialized successfully'
      });
    }

    if (action === 'record_violation') {
      // Record a trading violation
      const violation = body.violation;
      
      if (!tradingViolations[userId]) {
        tradingViolations[userId] = [];
      }
      
      tradingViolations[userId].push({
        ...violation,
        id: `violation_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        timestamp: new Date(),
        resolved: false
      });

      // Update violation count in progress
      if (mockLiveTradingProgress[userId]) {
        mockLiveTradingProgress[userId].riskManagementViolations += 1;
      }

      return NextResponse.json({
        success: true,
        data: { violation_recorded: true },
        message: 'Trading violation recorded'
      });
    }

    if (action === 'update_performance') {
      // Update live trading performance data
      const performanceData = body.performanceData;
      const progress = mockLiveTradingProgress[userId];
      
      if (!progress) {
        return NextResponse.json({
          success: false,
          error: 'No live trading access',
          message: 'User must be initialized first'
        }, { status: 404 });
      }

      // Update progress metrics
      progress.totalLiveTrades = performanceData.totalLiveTrades || progress.totalLiveTrades;
      progress.liveTradingDays = performanceData.liveTradingDays || progress.liveTradingDays;
      progress.currentMonthPnL = performanceData.currentMonthPnL || progress.currentMonthPnL;
      progress.last3MonthsAvgPnL = performanceData.last3MonthsAvgPnL || progress.last3MonthsAvgPnL;
      progress.maxDrawdownThisMonth = performanceData.maxDrawdownThisMonth || progress.maxDrawdownThisMonth;

      // Update performance data
      if (performanceData.recentPerformance) {
        mockPerformanceData[userId] = {
          ...mockPerformanceData[userId],
          ...performanceData.recentPerformance
        };
      }

      return NextResponse.json({
        success: true,
        data: { updated: true, progress },
        message: 'Performance data updated successfully'
      });
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Invalid action',
        message: 'Supported actions: initialize, record_violation, update_performance'
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in live trading access POST:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Operation failed',
        message: 'Failed to process live trading access request'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/confidence/live-trading/access
 * Level up user or modify restrictions
 */
export async function PUT(
  request: NextRequest
): Promise<NextResponse<ApiResponse<any>>> {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    const action = body.action;

    if (action === 'level_up') {
      // Process level upgrade
      const progress = mockLiveTradingProgress[userId];
      const recentPerformance = mockPerformanceData[userId];
      
      if (!progress) {
        return NextResponse.json({
          success: false,
          error: 'No live trading access',
          message: 'User has not been granted live trading access'
        }, { status: 404 });
      }

      const eligibility = evaluateLevelUpEligibility(progress, recentPerformance);
      
      if (!eligibility.eligible) {
        return NextResponse.json({
          success: false,
          error: 'Not eligible for level up',
          message: `Missing requirements: ${eligibility.missingRequirements.join(', ')}`
        }, { status: 400 });
      }

      // Perform level upgrade
      const oldLevel = progress.currentLevel;
      progress.currentLevel = eligibility.nextLevel!;
      progress.levelStartDate = new Date();
      progress.riskManagementViolations = 0; // Reset violations on level up
      progress.performanceNotes.push(
        `Upgraded from ${oldLevel} to ${progress.currentLevel} on ${new Date().toISOString().split('T')[0]}`
      );

      // Update requirements for new level
      const newRequirements = progress.currentLevel !== 'unrestricted' 
        ? require('@/lib/graduated-live-access').LEVEL_UP_REQUIREMENTS[progress.currentLevel]
        : null;
      
      if (newRequirements) {
        progress.levelUpRequirements = newRequirements;
        const nextEligibleDate = new Date();
        nextEligibleDate.setDate(nextEligibleDate.getDate() + newRequirements.minTradingDays);
        progress.nextLevelEligibleDate = nextEligibleDate;
      }

      return NextResponse.json({
        success: true,
        data: {
          upgraded: true,
          oldLevel,
          newLevel: progress.currentLevel,
          newRestrictions: LIVE_TRADING_RESTRICTIONS[progress.currentLevel],
          progress
        },
        message: `Successfully upgraded to ${progress.currentLevel} level`
      });
    }

    if (action === 'resolve_violations') {
      // Resolve trading violations
      const violationIds = body.violationIds;
      const userViolations = tradingViolations[userId] || [];
      
      let resolvedCount = 0;
      userViolations.forEach(violation => {
        if (violationIds.includes(violation.id)) {
          violation.resolved = true;
          resolvedCount++;
        }
      });

      return NextResponse.json({
        success: true,
        data: { resolved_count: resolvedCount },
        message: `${resolvedCount} violations marked as resolved`
      });
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Invalid action',
        message: 'Supported actions: level_up, resolve_violations'
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in live trading access PUT:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Update failed',
        message: 'Failed to update live trading access'
      },
      { status: 500 }
    );
  }
}