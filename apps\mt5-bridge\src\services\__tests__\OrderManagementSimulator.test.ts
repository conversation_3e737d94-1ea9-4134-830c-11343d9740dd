import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  OrderManagementSimulator, 
  type OrderManagerConfig, 
  type OrderRequest,
  type OrderModification,
  OrderType,
  OrderState,
  FillPolicy,
  ExpirationType 
} from '../OrderManagementSimulator';
import type { MarketData } from '@golddaddy/types';

describe('OrderManagementSimulator', () => {
  let simulator: OrderManagementSimulator;
  let mockConfig: Partial<OrderManagerConfig>;

  beforeEach(() => {
    mockConfig = {
      enablePartialFills: true,
      partialFillProbability: 0.1, // Low for predictable testing
      minPartialFillSize: 0.01,
      enableOrderExpiration: true,
      enableSlippage: true,
      enableOrderQueue: true,
      queueProcessingInterval: 10, // Fast processing for tests
      maxOrdersPerSymbol: 100,
      enableOrderModification: true,
      enableAdvancedOrderTypes: true
    };

    simulator = new OrderManagementSimulator(mockConfig);
  });

  afterEach(() => {
    simulator.dispose();
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with default configuration', () => {
      const defaultSimulator = new OrderManagementSimulator();
      expect(defaultSimulator).toBeDefined();
    });

    it('should apply custom configuration', () => {
      expect(simulator).toBeDefined();
    });

    it('should start with empty order lists', () => {
      expect(simulator.getOrders()).toHaveLength(0);
      expect(simulator.getPendingOrders()).toHaveLength(0);
      expect(simulator.getOrderHistory()).toHaveLength(0);
    });
  });

  describe('Market Data Integration', () => {
    it('should accept market data updates', () => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };

      expect(() => simulator.updateMarketData(marketData)).not.toThrow();
    });

    it('should process pending orders when market data updates', async () => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };

      // Update market data first so the symbol is available
      simulator.updateMarketData(marketData);

      // Place a limit order below current market
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0840, // Below current ask
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_LIMIT,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Limit order test'
      };

      const result = await simulator.sendOrder(orderRequest);
      expect(result.retcode).toBe(10009); // Should be placed

      // Update market data to trigger the order
      const updatedMarketData: MarketData = {
        ...marketData,
        ask: 1.0840, // Now at limit price
        bid: 1.0838
      };

      simulator.updateMarketData(updatedMarketData);

      // Give time for processing
      await new Promise(resolve => setTimeout(resolve, 50));

      const orders = simulator.getOrders();
      expect(orders.length).toBeGreaterThan(0);
    });
  });

  describe('Market Orders', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should execute market buy orders immediately', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 0, // Market price
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.BUY,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Market buy test'
      };

      const result = await simulator.sendOrder(orderRequest);
      
      expect(result.retcode).toBe(10009);
      expect(result.deal).toBeGreaterThan(0);
      expect(result.volume).toBe(1.0);
      expect(result.price).toBeGreaterThan(0);

      const orders = simulator.getOrders();
      expect(orders).toHaveLength(1);
      expect(orders[0].state).toBe(OrderState.FILLED);
    });

    it('should execute market sell orders immediately', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 0.5,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.SELL,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Market sell test'
      };

      const result = await simulator.sendOrder(orderRequest);
      
      expect(result.retcode).toBe(10009);
      expect(result.volume).toBe(0.5);

      const orders = simulator.getOrders();
      expect(orders).toHaveLength(1);
      expect(orders[0].type).toBe(OrderType.SELL);
    });

    it('should apply slippage to market orders', async () => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      
      const expectedPrice = marketData.ask;

      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.BUY,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Slippage test'
      };

      const result = await simulator.sendOrder(orderRequest);
      
      // With slippage enabled, execution price should differ from ask
      expect(Math.abs(result.price - expectedPrice)).toBeGreaterThan(0);
    });
  });

  describe('Pending Orders', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should place limit orders as pending', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0840, // Below current market
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_LIMIT,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Limit order test'
      };

      const result = await simulator.sendOrder(orderRequest);
      
      expect(result.retcode).toBe(10009);
      expect(result.deal).toBe(0); // No immediate execution

      const pendingOrders = simulator.getPendingOrders();
      expect(pendingOrders).toHaveLength(1);
      expect(pendingOrders[0].state).toBe(OrderState.PLACED);
    });

    it('should place stop orders as pending', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0860, // Above current market
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_STOP,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Stop order test'
      };

      const result = await simulator.sendOrder(orderRequest);
      
      expect(result.retcode).toBe(10009);

      const pendingOrders = simulator.getPendingOrders();
      expect(pendingOrders).toHaveLength(1);
      expect(pendingOrders[0].type).toBe(OrderType.BUY_STOP);
    });

    it('should activate limit orders when price is reached', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0840,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_LIMIT,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Activation test'
      };

      await simulator.sendOrder(orderRequest);

      // Update market to trigger the order
      const triggerMarketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0838,
        ask: 1.0840, // At limit price
        spread: 0.0002,
        timestamp: new Date()
      };

      simulator.updateMarketData(triggerMarketData);

      // Give time for processing
      await new Promise(resolve => setTimeout(resolve, 50));

      const orders = simulator.getOrders();
      const pendingOrders = simulator.getPendingOrders();
      
      expect(orders.length).toBeGreaterThan(0);
      expect(pendingOrders.length).toBe(0);
    });

    it('should activate stop orders when price is reached', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0860,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_STOP,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Stop activation test'
      };

      await simulator.sendOrder(orderRequest);

      // Update market to trigger the stop order
      const triggerMarketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0858,
        ask: 1.0860, // At stop price
        spread: 0.0002,
        timestamp: new Date()
      };

      simulator.updateMarketData(triggerMarketData);

      // Give time for processing
      await new Promise(resolve => setTimeout(resolve, 50));

      const orders = simulator.getOrders();
      expect(orders.length).toBeGreaterThan(0);
      expect(orders[0].type).toBe(OrderType.BUY_STOP);
    });
  });

  describe('Order Validation', () => {
    it('should reject orders with invalid symbols', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'INVALID',
        volume: 1.0,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.BUY,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Invalid symbol test'
      };

      const result = await simulator.sendOrder(orderRequest);
      expect(result.retcode).toBe(10014); // TRADE_RETCODE_INVALID_SYMBOL
    });

    it('should reject orders with invalid volume', async () => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);

      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: -1.0, // Invalid negative volume
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.BUY,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Invalid volume test'
      };

      const result = await simulator.sendOrder(orderRequest);
      expect(result.retcode).toBe(10015); // TRADE_RETCODE_INVALID_VOLUME
    });

    it('should reject advanced order types when disabled', async () => {
      simulator.configure({ enableAdvancedOrderTypes: false });

      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);

      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0860,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_STOP_LIMIT, // Advanced order type
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Advanced order type test'
      };

      const result = await simulator.sendOrder(orderRequest);
      expect(result.retcode).toBe(10016); // Order type not supported
    });
  });

  describe('Order Modification', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should modify pending orders', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0840,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_LIMIT,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Modification test'
      };

      const result = await simulator.sendOrder(orderRequest);
      const orderTicket = result.order;

      const modification: OrderModification = {
        ticket: orderTicket,
        price: 1.0835, // New price
        sl: 1.0800,    // New stop loss
        tp: 1.0900,    // New take profit
        comment: 'Modified order'
      };

      const modifyResult = await simulator.modifyOrder(modification);
      expect(modifyResult.retcode).toBe(10009);

      const order = simulator.getOrder(orderTicket);
      expect(order).not.toBeNull();
      expect(order!.priceOpen).toBe(1.0835);
      expect(order!.priceStopLoss).toBe(1.0800);
      expect(order!.priceTakeProfit).toBe(1.0900);
    });

    it('should reject modification of non-existent orders', async () => {
      const modification: OrderModification = {
        ticket: 999999, // Non-existent ticket
        price: 1.0835
      };

      const result = await simulator.modifyOrder(modification);
      expect(result.retcode).toBe(10013); // Order not found
    });

    it('should reject modification when disabled', async () => {
      simulator.configure({ enableOrderModification: false });

      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0840,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_LIMIT,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Modification disabled test'
      };

      const result = await simulator.sendOrder(orderRequest);

      const modification: OrderModification = {
        ticket: result.order,
        price: 1.0835
      };

      const modifyResult = await simulator.modifyOrder(modification);
      expect(modifyResult.retcode).toBe(10013); // Modification not supported
    });
  });

  describe('Order Cancellation', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should cancel pending orders', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0840,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_LIMIT,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Cancellation test'
      };

      const result = await simulator.sendOrder(orderRequest);
      const orderTicket = result.order;

      const cancelResult = await simulator.cancelOrder(orderTicket);
      expect(cancelResult.retcode).toBe(10009);

      const order = simulator.getOrder(orderTicket);
      expect(order).toBeNull(); // Should be removed from active/pending

      const history = simulator.getOrderHistory();
      expect(history.some(o => o.ticket === orderTicket && o.state === OrderState.CANCELED)).toBe(true);
    });

    it('should reject cancellation of non-existent orders', async () => {
      const result = await simulator.cancelOrder(999999);
      expect(result.retcode).toBe(10013); // Order not found
    });

    it('should reject cancellation of filled orders', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.BUY,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Filled order test'
      };

      const result = await simulator.sendOrder(orderRequest);
      const orderTicket = result.order;

      const cancelResult = await simulator.cancelOrder(orderTicket);
      expect(cancelResult.retcode).toBe(10013); // Cannot cancel filled order
    });
  });

  describe('Partial Fills', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);

      // Configure for more predictable partial fills
      simulator.configure({ 
        enablePartialFills: true, 
        partialFillProbability: 1.0, // Force partial fills
        partialFillThreshold: 0.5 
      });
    });

    it('should handle partial fills for large market orders', async () => {
      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 2.0, // Large order to trigger partial fill
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.BUY,
        typeFilling: FillPolicy.RETURN, // Allow partial fills
        typeTime: ExpirationType.GTC,
        comment: 'Partial fill test'
      };

      let partialFillReceived = false;
      simulator.on('partialFill', (partialFill) => {
        partialFillReceived = true;
        expect(partialFill.orderId).toBeDefined();
        expect(partialFill.volume).toBeGreaterThan(0);
        expect(partialFill.remaining).toBeGreaterThan(0);
      });

      const result = await simulator.sendOrder(orderRequest);
      
      // Give time for partial fill processing
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(partialFillReceived).toBe(true);
    });

    it('should emit partial fill events', (done) => {
      simulator.configure({ 
        enablePartialFills: true, 
        partialFillProbability: 1.0,
        partialFillThreshold: 0.5 
      });

      simulator.on('partialFill', (partialFill) => {
        expect(partialFill.volume).toBeGreaterThan(0);
        expect(partialFill.price).toBeGreaterThan(0);
        expect(partialFill.timestamp).toBeInstanceOf(Date);
        done();
      });

      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 2.0,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.BUY,
        typeFilling: FillPolicy.RETURN,
        typeTime: ExpirationType.GTC,
        comment: 'Partial fill event test'
      };

      simulator.sendOrder(orderRequest);
    });
  });

  describe('Order Expiration', () => {
    beforeEach(() => {
      vi.useFakeTimers();
      
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should expire orders after expiration time', async () => {
      const expiration = new Date(Date.now() + 1000); // 1 second from now

      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0840,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_LIMIT,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.SPECIFIED,
        expiration,
        comment: 'Expiration test'
      };

      const result = await simulator.sendOrder(orderRequest);
      const orderTicket = result.order;

      // Fast-forward past expiration
      vi.advanceTimersByTime(2000);

      // Manually trigger expiration check for testing
      simulator.checkExpirations();

      // Check that order was expired
      const order = simulator.getOrder(orderTicket);
      expect(order).toBeNull();

      const history = simulator.getOrderHistory();
      expect(history.some(o => o.ticket === orderTicket && o.state === OrderState.EXPIRED)).toBe(true);
    });

    it('should emit expiration events', () => {
      const expiration = new Date(Date.now() + 500);

      let expirationEventReceived = false;
      simulator.on('orderExpired', (order) => {
        expirationEventReceived = true;
        expect(order.state).toBe(OrderState.EXPIRED);
      });

      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0840,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_LIMIT,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.SPECIFIED,
        expiration,
        comment: 'Expiration event test'
      };

      simulator.sendOrder(orderRequest);

      // Fast-forward past expiration
      vi.advanceTimersByTime(1000);

      // Manually trigger expiration check for testing
      simulator.checkExpirations();

      expect(expirationEventReceived).toBe(true);
    });
  });

  describe('Statistics', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should track order statistics', async () => {
      // Place multiple orders
      const orderRequests = Array.from({ length: 5 }, (_, i) => ({
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.BUY,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: `Stats test ${i}`
      }));

      await Promise.all(orderRequests.map(req => simulator.sendOrder(req)));

      const stats = simulator.getStatistics();
      
      expect(stats.totalOrders).toBe(5);
      expect(stats.activeOrders).toBe(5);
      expect(stats.filledOrders).toBe(5);
      expect(stats.avgFillTime).toBeGreaterThanOrEqual(0);
    });

    it('should track mixed order outcomes', async () => {
      // Market orders (should fill)
      const marketOrder: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.BUY,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Market order'
      };

      // Pending order
      const pendingOrder: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0800, // Far from market
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_LIMIT,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Pending order'
      };

      await simulator.sendOrder(marketOrder);
      const pendingResult = await simulator.sendOrder(pendingOrder);
      
      // Cancel the pending order
      await simulator.cancelOrder(pendingResult.order);

      const stats = simulator.getStatistics();
      
      expect(stats.totalOrders).toBe(2);
      expect(stats.filledOrders).toBe(1);
      expect(stats.canceledOrders).toBe(1);
      expect(stats.pendingOrders).toBe(0);
    });
  });

  describe('Events', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should emit order placed events', async () => {
      let orderPlacedReceived = false;
      
      simulator.on('orderPlaced', (order) => {
        orderPlacedReceived = true;
        expect(order.state).toBe(OrderState.PLACED);
      });

      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 1.0840,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 0,
        type: OrderType.BUY_LIMIT,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Event test'
      };

      await simulator.sendOrder(orderRequest);
      expect(orderPlacedReceived).toBe(true);
    });

    it('should emit order filled events', async () => {
      let orderFilledReceived = false;
      
      simulator.on('orderFilled', (order) => {
        orderFilledReceived = true;
        expect(order.state).toBe(OrderState.FILLED);
      });

      const orderRequest: OrderRequest = {
        action: 1,
        magic: 12345,
        symbol: 'EURUSD',
        volume: 1.0,
        price: 0,
        stoplimit: 0,
        sl: 0,
        tp: 0,
        deviation: 10,
        type: OrderType.BUY,
        typeFilling: FillPolicy.FOK,
        typeTime: ExpirationType.GTC,
        comment: 'Fill event test'
      };

      await simulator.sendOrder(orderRequest);
      expect(orderFilledReceived).toBe(true);
    });
  });

  describe('Configuration and Cleanup', () => {
    it('should update configuration at runtime', () => {
      const newConfig = {
        enablePartialFills: false,
        maxOrdersPerSymbol: 50,
        queueProcessingInterval: 20
      };

      expect(() => simulator.configure(newConfig)).not.toThrow();
    });

    it('should reset state correctly', () => {
      simulator.reset();
      
      expect(simulator.getOrders()).toHaveLength(0);
      expect(simulator.getPendingOrders()).toHaveLength(0);
      expect(simulator.getOrderHistory()).toHaveLength(0);
      
      const stats = simulator.getStatistics();
      expect(stats.totalOrders).toBe(0);
    });

    it('should dispose resources properly', () => {
      expect(() => simulator.dispose()).not.toThrow();
    });
  });
});