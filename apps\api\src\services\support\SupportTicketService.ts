import { EventEmitter } from 'events'
import { createClient } from '@supabase/supabase-js'
import nodemailer from 'nodemailer'
import multer from 'multer'
import path from 'path'
import fs from 'fs/promises'
import { v4 as uuidv4 } from 'uuid'

export interface SupportTicket {
  id: string
  ticketNumber: string
  category: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  subject: string
  description: string
  status: 'open' | 'in_progress' | 'waiting_for_response' | 'resolved' | 'closed'
  userEmail: string
  userName: string
  userId?: string
  assignedTo?: string
  attachments: SupportTicketAttachment[]
  systemInfo: {
    userAgent: string
    url: string
    timestamp: string
    accountId?: string
  }
  tags: string[]
  createdAt: Date
  updatedAt: Date
  resolvedAt?: Date
  firstResponseAt?: Date
  lastResponseAt?: Date
  resolutionNotes?: string
  customerSatisfactionRating?: number
  customerFeedback?: string
}

export interface SupportTicketAttachment {
  id: string
  ticketId: string
  filename: string
  originalName: string
  mimeType: string
  size: number
  url: string
  uploadedAt: Date
}

export interface SupportTicketMessage {
  id: string
  ticketId: string
  fromSupport: boolean
  authorName: string
  authorEmail: string
  message: string
  attachments: SupportTicketAttachment[]
  isInternal: boolean
  createdAt: Date
}

export interface CreateSupportTicketRequest {
  category: string
  priority: 'low' | 'medium' | 'high' | 'critical'
  subject: string
  description: string
  userEmail: string
  userName: string
  userId?: string
  attachments?: Express.Multer.File[]
  systemInfo: {
    userAgent: string
    url: string
    timestamp: string
    accountId?: string
  }
}

export interface SupportTicketFilters {
  status?: string[]
  category?: string[]
  priority?: string[]
  assignedTo?: string
  dateRange?: {
    start: Date
    end: Date
  }
  searchQuery?: string
}

export interface SupportTicketStats {
  totalTickets: number
  openTickets: number
  avgResolutionTime: number
  avgResponseTime: number
  satisfactionRating: number
  ticketsByCategory: Record<string, number>
  ticketsByPriority: Record<string, number>
  resolutionTrends: Array<{
    date: string
    resolved: number
    created: number
  }>
}

export class SupportTicketService extends EventEmitter {
  private supabase = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  )
  
  private emailTransporter = nodemailer.createTransporter({
    host: process.env.SMTP_HOST,
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS
    }
  })

  private storage = multer.diskStorage({
    destination: async (req, file, cb) => {
      const uploadDir = path.join(process.cwd(), 'uploads', 'support-tickets')
      await fs.mkdir(uploadDir, { recursive: true })
      cb(null, uploadDir)
    },
    filename: (req, file, cb) => {
      const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1E9)}`
      const ext = path.extname(file.originalname)
      cb(null, `${file.fieldname}-${uniqueSuffix}${ext}`)
    }
  })

  public upload = multer({
    storage: this.storage,
    limits: {
      fileSize: 10 * 1024 * 1024, // 10MB
      files: 5
    },
    fileFilter: (req, file, cb) => {
      const allowedMimeTypes = [
        'image/jpeg', 'image/png', 'image/gif', 'image/webp',
        'application/pdf', 'text/plain', 'application/json',
        'video/mp4', 'video/webm'
      ]
      
      if (allowedMimeTypes.includes(file.mimetype)) {
        cb(null, true)
      } else {
        cb(new Error(`File type ${file.mimetype} not allowed`))
      }
    }
  })

  private generateTicketNumber(): string {
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substring(2, 8)
    return `GD-${timestamp}-${random}`.toUpperCase()
  }

  private async uploadAttachment(
    ticketId: string,
    file: Express.Multer.File
  ): Promise<SupportTicketAttachment> {
    const attachmentId = uuidv4()
    const filename = `${attachmentId}-${file.filename}`
    
    // Upload to Supabase Storage
    const { data: uploadData, error: uploadError } = await this.supabase.storage
      .from('support-attachments')
      .upload(filename, file.buffer || await fs.readFile(file.path), {
        contentType: file.mimetype
      })
    
    if (uploadError) {
      throw new Error(`Failed to upload attachment: ${uploadError.message}`)
    }
    
    // Get public URL
    const { data: { publicUrl } } = this.supabase.storage
      .from('support-attachments')
      .getPublicUrl(filename)
    
    const attachment: SupportTicketAttachment = {
      id: attachmentId,
      ticketId,
      filename,
      originalName: file.originalname,
      mimeType: file.mimetype,
      size: file.size,
      url: publicUrl,
      uploadedAt: new Date()
    }
    
    // Save attachment metadata to database
    const { error: dbError } = await this.supabase
      .from('support_ticket_attachments')
      .insert(attachment)
    
    if (dbError) {
      throw new Error(`Failed to save attachment metadata: ${dbError.message}`)
    }
    
    // Clean up local file if it exists
    if (file.path) {
      await fs.unlink(file.path).catch(() => {})
    }
    
    return attachment
  }

  private async sendNotificationEmail(
    ticket: SupportTicket,
    type: 'ticket_created' | 'ticket_updated' | 'ticket_resolved'
  ): Promise<void> {
    const emailTemplates = {
      ticket_created: {
        subject: `Support Ticket Created - ${ticket.ticketNumber}`,
        html: `
          <h2>Your support ticket has been created</h2>
          <p><strong>Ticket Number:</strong> ${ticket.ticketNumber}</p>
          <p><strong>Subject:</strong> ${ticket.subject}</p>
          <p><strong>Priority:</strong> ${ticket.priority.toUpperCase()}</p>
          <p><strong>Status:</strong> ${ticket.status}</p>
          <p>We've received your support request and will respond within 24 hours.</p>
          <p>You can track your ticket status at: <a href="${process.env.FRONTEND_URL}/support/ticket/${ticket.ticketNumber}">View Ticket</a></p>
        `
      },
      ticket_updated: {
        subject: `Support Ticket Updated - ${ticket.ticketNumber}`,
        html: `
          <h2>Your support ticket has been updated</h2>
          <p><strong>Ticket Number:</strong> ${ticket.ticketNumber}</p>
          <p><strong>Subject:</strong> ${ticket.subject}</p>
          <p><strong>Status:</strong> ${ticket.status}</p>
          <p>View the latest update: <a href="${process.env.FRONTEND_URL}/support/ticket/${ticket.ticketNumber}">View Ticket</a></p>
        `
      },
      ticket_resolved: {
        subject: `Support Ticket Resolved - ${ticket.ticketNumber}`,
        html: `
          <h2>Your support ticket has been resolved</h2>
          <p><strong>Ticket Number:</strong> ${ticket.ticketNumber}</p>
          <p><strong>Subject:</strong> ${ticket.subject}</p>
          <p><strong>Resolution:</strong> ${ticket.resolutionNotes}</p>
          <p>If you're satisfied with the resolution, please <a href="${process.env.FRONTEND_URL}/support/feedback/${ticket.ticketNumber}">rate our support</a>.</p>
        `
      }
    }
    
    const template = emailTemplates[type]
    
    await this.emailTransporter.sendMail({
      from: process.env.SUPPORT_EMAIL || '<EMAIL>',
      to: ticket.userEmail,
      subject: template.subject,
      html: template.html
    })
  }

  async createTicket(request: CreateSupportTicketRequest): Promise<SupportTicket> {
    const ticketId = uuidv4()
    const ticketNumber = this.generateTicketNumber()
    
    // Process attachments if any
    const attachments: SupportTicketAttachment[] = []
    if (request.attachments && request.attachments.length > 0) {
      for (const file of request.attachments) {
        try {
          const attachment = await this.uploadAttachment(ticketId, file)
          attachments.push(attachment)
        } catch (error) {
          console.error('Failed to upload attachment:', error)
          // Continue with ticket creation even if attachment upload fails
        }
      }
    }
    
    // Auto-assign tags based on category and content
    const tags = this.generateAutoTags(request)
    
    const ticket: SupportTicket = {
      id: ticketId,
      ticketNumber,
      category: request.category,
      priority: request.priority,
      subject: request.subject,
      description: request.description,
      status: 'open',
      userEmail: request.userEmail,
      userName: request.userName,
      userId: request.userId,
      attachments,
      systemInfo: request.systemInfo,
      tags,
      createdAt: new Date(),
      updatedAt: new Date()
    }
    
    // Save to database
    const { error } = await this.supabase
      .from('support_tickets')
      .insert(ticket)
    
    if (error) {
      throw new Error(`Failed to create support ticket: ${error.message}`)
    }
    
    // Send notification email
    try {
      await this.sendNotificationEmail(ticket, 'ticket_created')
    } catch (error) {
      console.error('Failed to send notification email:', error)
      // Don't fail ticket creation if email fails
    }
    
    // Emit event for real-time updates
    this.emit('ticket_created', ticket)
    
    // Auto-escalate critical tickets
    if (ticket.priority === 'critical') {
      this.emit('critical_ticket_created', ticket)
      // Could integrate with Slack, Teams, or pager duty here
    }
    
    return ticket
  }

  async updateTicketStatus(
    ticketId: string,
    status: SupportTicket['status'],
    resolutionNotes?: string
  ): Promise<SupportTicket> {
    const updateData: any = {
      status,
      updatedAt: new Date()
    }
    
    if (status === 'resolved' || status === 'closed') {
      updateData.resolvedAt = new Date()
      if (resolutionNotes) {
        updateData.resolutionNotes = resolutionNotes
      }
    }
    
    const { data, error } = await this.supabase
      .from('support_tickets')
      .update(updateData)
      .eq('id', ticketId)
      .select('*')
      .single()
    
    if (error) {
      throw new Error(`Failed to update ticket: ${error.message}`)
    }
    
    const ticket = data as SupportTicket
    
    // Send notification email
    try {
      const emailType = status === 'resolved' || status === 'closed' 
        ? 'ticket_resolved' 
        : 'ticket_updated'
      await this.sendNotificationEmail(ticket, emailType)
    } catch (error) {
      console.error('Failed to send notification email:', error)
    }
    
    this.emit('ticket_updated', ticket)
    
    return ticket
  }

  async addMessage(
    ticketId: string,
    message: string,
    fromSupport: boolean,
    authorName: string,
    authorEmail: string,
    attachments?: Express.Multer.File[],
    isInternal = false
  ): Promise<SupportTicketMessage> {
    const messageId = uuidv4()
    
    // Process attachments if any
    const messageAttachments: SupportTicketAttachment[] = []
    if (attachments && attachments.length > 0) {
      for (const file of attachments) {
        try {
          const attachment = await this.uploadAttachment(ticketId, file)
          messageAttachments.push(attachment)
        } catch (error) {
          console.error('Failed to upload message attachment:', error)
        }
      }
    }
    
    const ticketMessage: SupportTicketMessage = {
      id: messageId,
      ticketId,
      fromSupport,
      authorName,
      authorEmail,
      message,
      attachments: messageAttachments,
      isInternal,
      createdAt: new Date()
    }
    
    // Save message to database
    const { error } = await this.supabase
      .from('support_ticket_messages')
      .insert(ticketMessage)
    
    if (error) {
      throw new Error(`Failed to add message: ${error.message}`)
    }
    
    // Update ticket timestamps
    const updateData: any = {
      updatedAt: new Date()
    }
    
    if (fromSupport) {
      updateData.lastResponseAt = new Date()
      
      // Set first response time if not already set
      const { data: ticket } = await this.supabase
        .from('support_tickets')
        .select('firstResponseAt')
        .eq('id', ticketId)
        .single()
      
      if (ticket && !ticket.firstResponseAt) {
        updateData.firstResponseAt = new Date()
      }
    }
    
    await this.supabase
      .from('support_tickets')
      .update(updateData)
      .eq('id', ticketId)
    
    this.emit('message_added', ticketMessage)
    
    return ticketMessage
  }

  async getTickets(
    filters: SupportTicketFilters = {},
    page = 1,
    pageSize = 50
  ): Promise<{ tickets: SupportTicket[], total: number }> {
    let query = this.supabase
      .from('support_tickets')
      .select('*, attachments:support_ticket_attachments(*)', { count: 'exact' })
    
    // Apply filters
    if (filters.status?.length) {
      query = query.in('status', filters.status)
    }
    
    if (filters.category?.length) {
      query = query.in('category', filters.category)
    }
    
    if (filters.priority?.length) {
      query = query.in('priority', filters.priority)
    }
    
    if (filters.assignedTo) {
      query = query.eq('assignedTo', filters.assignedTo)
    }
    
    if (filters.dateRange) {
      query = query
        .gte('createdAt', filters.dateRange.start.toISOString())
        .lte('createdAt', filters.dateRange.end.toISOString())
    }
    
    if (filters.searchQuery) {
      query = query.or(`subject.ilike.%${filters.searchQuery}%,description.ilike.%${filters.searchQuery}%,userEmail.ilike.%${filters.searchQuery}%`)
    }
    
    // Apply pagination
    const offset = (page - 1) * pageSize
    query = query
      .range(offset, offset + pageSize - 1)
      .order('createdAt', { ascending: false })
    
    const { data, error, count } = await query
    
    if (error) {
      throw new Error(`Failed to get tickets: ${error.message}`)
    }
    
    return {
      tickets: data as SupportTicket[],
      total: count || 0
    }
  }

  async getTicketByNumber(ticketNumber: string): Promise<SupportTicket | null> {
    const { data, error } = await this.supabase
      .from('support_tickets')
      .select('*, attachments:support_ticket_attachments(*)')
      .eq('ticketNumber', ticketNumber)
      .single()
    
    if (error && error.code !== 'PGRST116') {
      throw new Error(`Failed to get ticket: ${error.message}`)
    }
    
    return data as SupportTicket || null
  }

  async getTicketMessages(ticketId: string): Promise<SupportTicketMessage[]> {
    const { data, error } = await this.supabase
      .from('support_ticket_messages')
      .select('*, attachments:support_ticket_attachments(*)')
      .eq('ticketId', ticketId)
      .eq('isInternal', false)
      .order('createdAt', { ascending: true })
    
    if (error) {
      throw new Error(`Failed to get ticket messages: ${error.message}`)
    }
    
    return data as SupportTicketMessage[]
  }

  async getStats(dateRange?: { start: Date; end: Date }): Promise<SupportTicketStats> {
    let baseQuery = this.supabase.from('support_tickets')
    
    if (dateRange) {
      baseQuery = baseQuery
        .gte('createdAt', dateRange.start.toISOString())
        .lte('createdAt', dateRange.end.toISOString())
    }
    
    // Get basic counts
    const [
      totalResult,
      openResult,
      avgResolutionResult,
      avgResponseResult,
      satisfactionResult,
      categoryResult,
      priorityResult
    ] = await Promise.all([
      baseQuery.select('*', { count: 'exact', head: true }),
      baseQuery.select('*', { count: 'exact', head: true }).eq('status', 'open'),
      this.supabase.rpc('avg_resolution_time'),
      this.supabase.rpc('avg_response_time'),
      baseQuery.select('customerSatisfactionRating').not('customerSatisfactionRating', 'is', null),
      baseQuery.select('category', { count: 'exact' }),
      baseQuery.select('priority', { count: 'exact' })
    ])
    
    const stats: SupportTicketStats = {
      totalTickets: totalResult.count || 0,
      openTickets: openResult.count || 0,
      avgResolutionTime: avgResolutionResult.data || 0,
      avgResponseTime: avgResponseResult.data || 0,
      satisfactionRating: satisfactionResult.data?.length 
        ? satisfactionResult.data.reduce((sum, r) => sum + r.customerSatisfactionRating, 0) / satisfactionResult.data.length
        : 0,
      ticketsByCategory: {},
      ticketsByPriority: {},
      resolutionTrends: []
    }
    
    // Process category and priority counts
    if (categoryResult.data) {
      stats.ticketsByCategory = categoryResult.data.reduce((acc, item) => {
        acc[item.category] = (acc[item.category] || 0) + 1
        return acc
      }, {})
    }
    
    if (priorityResult.data) {
      stats.ticketsByPriority = priorityResult.data.reduce((acc, item) => {
        acc[item.priority] = (acc[item.priority] || 0) + 1
        return acc
      }, {})
    }
    
    return stats
  }

  private generateAutoTags(request: CreateSupportTicketRequest): string[] {
    const tags: string[] = []
    
    // Add category-based tags
    tags.push(request.category)
    
    // Add priority-based tags
    if (request.priority === 'critical' || request.priority === 'high') {
      tags.push('urgent')
    }
    
    // Add content-based tags
    const content = `${request.subject} ${request.description}`.toLowerCase()
    
    if (content.includes('login') || content.includes('password') || content.includes('authentication')) {
      tags.push('auth')
    }
    
    if (content.includes('payment') || content.includes('billing') || content.includes('subscription')) {
      tags.push('billing')
    }
    
    if (content.includes('mt5') || content.includes('metatrader') || content.includes('trading')) {
      tags.push('trading')
    }
    
    if (content.includes('bug') || content.includes('error') || content.includes('broken')) {
      tags.push('bug')
    }
    
    if (content.includes('feature') || content.includes('suggestion') || content.includes('improvement')) {
      tags.push('enhancement')
    }
    
    return [...new Set(tags)] // Remove duplicates
  }

  async submitFeedback(
    ticketNumber: string,
    rating: number,
    feedback: string
  ): Promise<void> {
    const { error } = await this.supabase
      .from('support_tickets')
      .update({
        customerSatisfactionRating: rating,
        customerFeedback: feedback,
        updatedAt: new Date()
      })
      .eq('ticketNumber', ticketNumber)
    
    if (error) {
      throw new Error(`Failed to submit feedback: ${error.message}`)
    }
    
    this.emit('feedback_submitted', { ticketNumber, rating, feedback })
  }
}