/**
 * Performance Middleware
 * 
 * Express middleware for request performance monitoring, automatic response
 * compression, priority queuing, and client capability detection.
 */

import { Request, Response, NextFunction } from 'express';
import { RequestOptimizationService } from '../../services/performance/RequestOptimizationService';

interface PerformanceMetrics {
  requestId: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  method: string;
  path: string;
  statusCode?: number;
  responseSize?: number;
  compressionRatio?: number;
  clientCapabilities?: ClientCapabilities;
  cached: boolean;
  optimized: boolean;
}

interface ClientCapabilities {
  hardwareTier: 'low' | 'medium' | 'high';
  connectionType: 'slow-2g' | '2g' | '3g' | '4g' | 'wifi' | 'unknown';
  memoryLimit: number;
  compressionSupport: string[];
  preferredFormat: 'minimal' | 'standard' | 'detailed';
}

interface PerformanceConfig {
  enableCompression: boolean;
  enableCaching: boolean;
  enablePriorityQueuing: boolean;
  enableMetricsCollection: boolean;
  compressionThreshold: number; // bytes
  slowRequestThreshold: number; // ms
  enableClientCapabilityDetection: boolean;
  maxRequestQueueSize: number;
  requestTimeoutMs: number;
}

interface QueuedRequest {
  id: string;
  priority: number;
  timestamp: number;
  req: Request;
  res: Response;
  next: NextFunction;
  timeout: NodeJS.Timeout;
}

export class PerformanceMiddleware {
  private requestOptimizationService: RequestOptimizationService;
  private config: PerformanceConfig;
  private requestMetrics = new Map<string, PerformanceMetrics>();
  private requestQueue: QueuedRequest[] = [];
  private activeRequests = new Set<string>();
  private slowRequestLog: PerformanceMetrics[] = [];
  private maxSlowRequestLog = 100;

  constructor(config: Partial<PerformanceConfig> = {}) {
    this.requestOptimizationService = new RequestOptimizationService();
    this.config = {
      enableCompression: true,
      enableCaching: true,
      enablePriorityQueuing: true,
      enableMetricsCollection: true,
      compressionThreshold: 1024, // 1KB
      slowRequestThreshold: 1000, // 1 second
      enableClientCapabilityDetection: true,
      maxRequestQueueSize: 100,
      requestTimeoutMs: 30000, // 30 seconds
      ...config
    };

    this.startQueueProcessor();
  }

  /**
   * Main middleware function
   */
  middleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      const requestId = this.generateRequestId();
      const startTime = Date.now();

      // Attach request ID to request object
      (req as Request & { requestId: string }).requestId = requestId;

      // Detect client capabilities
      const clientCapabilities = this.config.enableClientCapabilityDetection 
        ? this.detectClientCapabilities(req)
        : this.getDefaultCapabilities();

      // Store in request for later use
      (req as Request & { clientCapabilities: unknown }).clientCapabilities = clientCapabilities;

      // Initialize metrics
      const metrics: PerformanceMetrics = {
        requestId,
        startTime,
        method: req.method,
        path: req.path,
        clientCapabilities,
        cached: false,
        optimized: false
      };

      if (this.config.enableMetricsCollection) {
        this.requestMetrics.set(requestId, metrics);
      }

      // Check if request should be queued based on priority
      if (this.config.enablePriorityQueuing && this.shouldQueueRequest(req)) {
        this.queueRequest(req, res, next, metrics);
        return;
      }

      // Process request immediately
      this.processRequest(req, res, next, metrics);
    };
  }

  /**
   * Response optimization middleware
   */
  responseOptimization() {
    return (req: Request, res: Response, next: NextFunction) => {
      const originalSend = res.send;
      const originalJson = res.json;

      // Override res.send to apply optimizations
      res.send = function(body: unknown) {
        return originalSend.call(this, this.optimizeResponse(body, req));
      };

      // Override res.json to apply optimizations
      res.json = function(obj: unknown) {
        return originalJson.call(this, this.optimizeResponse(obj, req));
      };

      // Add optimization method to response object
      (res as Response & { optimizeResponse: (data: unknown, request: Request) => unknown }).optimizeResponse = (data: unknown, request: Request) => {
        const clientCapabilities = (request as Request & { clientCapabilities?: unknown }).clientCapabilities;
        if (!clientCapabilities) return data;

        return this.optimizeResponseData(data, clientCapabilities);
      };

      next();
    };
  }

  /**
   * Compression middleware
   */
  compressionMiddleware() {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!this.config.enableCompression) {
        return next();
      }

      const originalSend = res.send;
      const originalJson = res.json;

      res.send = function(body: unknown) {
        const compressed = this.compressResponse(body, req, res);
        return originalSend.call(this, compressed);
      }.bind(this);

      res.json = function(obj: unknown) {
        const compressed = this.compressResponse(obj, req, res);
        return originalJson.call(this, compressed);
      }.bind(this);

      next();
    };
  }

  /**
   * Metrics collection middleware (should be last)
   */
  metricsCollection() {
    return (req: Request, res: Response, next: NextFunction) => {
      if (!this.config.enableMetricsCollection) {
        return next();
      }

      const requestId = (req as any).requestId;
      const metrics = this.requestMetrics.get(requestId);

      if (metrics) {
        // Track response completion
        res.on('finish', () => {
          this.finalizeMetrics(requestId, res);
        });

        // Track response errors
        res.on('error', (error) => {
          console.error(`Request ${requestId} error:`, error);
          this.finalizeMetrics(requestId, res);
        });
      }

      next();
    };
  }

  /**
   * Get performance statistics
   */
  getStats(): {
    totalRequests: number;
    activeRequests: number;
    queuedRequests: number;
    averageResponseTime: number;
    slowRequests: number;
    compressionRatio: number;
    cacheHitRate: number;
    optimizationMetrics: any;
  } {
    const metrics = Array.from(this.requestMetrics.values()).filter(m => m.duration !== undefined);
    const totalRequests = metrics.length;
    
    const averageResponseTime = totalRequests > 0
      ? metrics.reduce((sum, m) => sum + (m.duration || 0), 0) / totalRequests
      : 0;

    const slowRequests = metrics.filter(m => (m.duration || 0) > this.config.slowRequestThreshold).length;

    const compressedRequests = metrics.filter(m => m.compressionRatio && m.compressionRatio > 0);
    const compressionRatio = compressedRequests.length > 0
      ? compressedRequests.reduce((sum, m) => sum + (m.compressionRatio || 0), 0) / compressedRequests.length
      : 0;

    const cachedRequests = metrics.filter(m => m.cached).length;
    const cacheHitRate = totalRequests > 0 ? (cachedRequests / totalRequests) * 100 : 0;

    return {
      totalRequests,
      activeRequests: this.activeRequests.size,
      queuedRequests: this.requestQueue.length,
      averageResponseTime,
      slowRequests,
      compressionRatio,
      cacheHitRate,
      optimizationMetrics: this.requestOptimizationService.getMetrics()
    };
  }

  /**
   * Get slow request log
   */
  getSlowRequests(): PerformanceMetrics[] {
    return [...this.slowRequestLog];
  }

  /**
   * Clear metrics and logs
   */
  clearMetrics(): void {
    this.requestMetrics.clear();
    this.slowRequestLog = [];
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private detectClientCapabilities(req: Request): ClientCapabilities {
    const userAgent = req.get('User-Agent') || '';
    const acceptEncoding = req.get('Accept-Encoding') || '';
    const connection = req.get('Connection-Type') || 'unknown';
    
    // Hardware tier detection based on User-Agent
    const hardwareTier = this.detectHardwareTier(userAgent);
    
    // Connection type from header or User-Agent analysis
    const connectionType = this.detectConnectionType(connection, userAgent);
    
    // Memory estimation based on device type
    const memoryLimit = this.estimateMemoryLimit(hardwareTier, userAgent);
    
    // Compression support
    const compressionSupport = this.parseCompressionSupport(acceptEncoding);
    
    // Preferred format based on capabilities
    const preferredFormat = this.determinePreferredFormat(hardwareTier, connectionType);

    return {
      hardwareTier,
      connectionType,
      memoryLimit,
      compressionSupport,
      preferredFormat
    };
  }

  private detectHardwareTier(userAgent: string): 'low' | 'medium' | 'high' {
    const ua = userAgent.toLowerCase();
    
    // Low-tier indicators
    if (ua.includes('android 4.') || 
        ua.includes('android 5.') || 
        ua.includes('opera mini') ||
        ua.includes('blackberry') ||
        ua.includes('symbian')) {
      return 'low';
    }
    
    // High-tier indicators
    if (ua.includes('chrome') && (ua.includes('android 1') || ua.includes('android 9.') || ua.includes('android 10')) ||
        ua.includes('safari') && ua.includes('version/1') ||
        ua.includes('firefox') && ua.includes('mobile')) {
      return 'high';
    }
    
    return 'medium';
  }

  private detectConnectionType(connectionHeader: string, userAgent: string): ClientCapabilities['connectionType'] {
    const connection = connectionHeader.toLowerCase();
    
    if (connection.includes('slow-2g')) return 'slow-2g';
    if (connection.includes('2g')) return '2g';
    if (connection.includes('3g')) return '3g';
    if (connection.includes('4g') || connection.includes('lte')) return '4g';
    if (connection.includes('wifi')) return 'wifi';
    
    // Fallback to User-Agent analysis
    const ua = userAgent.toLowerCase();
    if (ua.includes('mobile') && !ua.includes('wifi')) {
      return '3g'; // Conservative estimate for mobile
    }
    
    return 'unknown';
  }

  private estimateMemoryLimit(hardwareTier: string, userAgent: string): number {
    const ua = userAgent.toLowerCase();
    
    // Specific device memory hints
    if (ua.includes('android go') || ua.includes('android lite')) return 512;
    if (ua.includes('iphone') && (ua.includes('5') || ua.includes('6'))) return 1024;
    if (ua.includes('ipad pro')) return 4096;
    
    // Tier-based estimation
    switch (hardwareTier) {
      case 'low': return 512;
      case 'medium': return 2048;
      case 'high': return 4096;
      default: return 2048;
    }
  }

  private parseCompressionSupport(acceptEncoding: string): string[] {
    const supported: string[] = [];
    const encoding = acceptEncoding.toLowerCase();
    
    if (encoding.includes('gzip')) supported.push('gzip');
    if (encoding.includes('deflate')) supported.push('deflate');
    if (encoding.includes('br')) supported.push('brotli');
    if (encoding.includes('compress')) supported.push('compress');
    
    return supported;
  }

  private determinePreferredFormat(
    hardwareTier: string, 
    connectionType: string
  ): 'minimal' | 'standard' | 'detailed' {
    if (hardwareTier === 'low' || connectionType === 'slow-2g' || connectionType === '2g') {
      return 'minimal';
    }
    
    if (hardwareTier === 'high' && (connectionType === 'wifi' || connectionType === '4g')) {
      return 'detailed';
    }
    
    return 'standard';
  }

  private getDefaultCapabilities(): ClientCapabilities {
    return {
      hardwareTier: 'medium',
      connectionType: 'unknown',
      memoryLimit: 2048,
      compressionSupport: ['gzip'],
      preferredFormat: 'standard'
    };
  }

  private shouldQueueRequest(req: Request): boolean {
    const priority = this.getRequestPriority(req);
    const currentLoad = this.activeRequests.size;
    const maxConcurrent = this.getMaxConcurrentRequests(req);
    
    // Queue non-critical requests when at capacity
    return priority < 5 && currentLoad >= maxConcurrent;
  }

  private getRequestPriority(req: Request): number {
    const path = req.path.toLowerCase();
    const method = req.method.toLowerCase();
    
    // Critical paths (highest priority)
    if (path.includes('/auth') || path.includes('/login')) return 10;
    if (path.includes('/emergency') || path.includes('/alert')) return 9;
    
    // High priority
    if (method === 'post' || method === 'put' || method === 'delete') return 8;
    if (path.includes('/realtime') || path.includes('/live')) return 7;
    
    // Medium priority
    if (path.includes('/api/v1')) return 6;
    if (method === 'get' && path.includes('/data')) return 5;
    
    // Low priority
    if (path.includes('/analytics') || path.includes('/reporting')) return 3;
    if (path.includes('/static') || path.includes('/assets')) return 2;
    
    return 4; // Default medium-low priority
  }

  private getMaxConcurrentRequests(req: Request): number {
    const capabilities = (req as any).clientCapabilities;
    if (!capabilities) return 5;
    
    const base = {
      'low': 2,
      'medium': 5,
      'high': 10
    }[capabilities.hardwareTier];
    
    return base;
  }

  private queueRequest(req: Request, res: Response, next: NextFunction, _metrics: PerformanceMetrics): void {
    const priority = this.getRequestPriority(req);
    const requestId = (req as any).requestId;
    
    // Check queue capacity
    if (this.requestQueue.length >= this.config.maxRequestQueueSize) {
      // Remove lowest priority request
      this.requestQueue.sort((a, b) => b.priority - a.priority);
      const removed = this.requestQueue.pop();
      if (removed) {
        clearTimeout(removed.timeout);
        removed.res.status(503).json({ error: 'Server overloaded, please retry later' });
      }
    }
    
    // Set timeout for queued request
    const timeout = setTimeout(() => {
      this.removeFromQueue(requestId);
      res.status(408).json({ error: 'Request timeout' });
    }, this.config.requestTimeoutMs);
    
    const queuedRequest: QueuedRequest = {
      id: requestId,
      priority,
      timestamp: Date.now(),
      req,
      res,
      next,
      timeout
    };
    
    this.requestQueue.push(queuedRequest);
    this.requestQueue.sort((a, b) => b.priority - a.priority); // Highest priority first
  }

  private removeFromQueue(requestId: string): boolean {
    const index = this.requestQueue.findIndex(r => r.id === requestId);
    if (index !== -1) {
      const request = this.requestQueue[index];
      clearTimeout(request.timeout);
      this.requestQueue.splice(index, 1);
      return true;
    }
    return false;
  }

  private processRequest(req: Request, res: Response, next: NextFunction, metrics: PerformanceMetrics): void {
    const requestId = (req as any).requestId;
    this.activeRequests.add(requestId);
    
    // Mark as processing
    metrics.startTime = Date.now();
    
    next();
  }

  private startQueueProcessor(): void {
    setInterval(() => {
      if (this.requestQueue.length === 0) return;
      
      const availableSlots = Math.max(0, 10 - this.activeRequests.size); // Max 10 concurrent
      const toProcess = this.requestQueue.splice(0, availableSlots);
      
      toProcess.forEach(queuedRequest => {
        clearTimeout(queuedRequest.timeout);
        const metrics = this.requestMetrics.get(queuedRequest.id);
        if (metrics) {
          this.processRequest(queuedRequest.req, queuedRequest.res, queuedRequest.next, metrics);
        }
      });
    }, 100); // Check every 100ms
  }

  private optimizeResponseData(data: any, capabilities: ClientCapabilities): any {
    if (!data || typeof data !== 'object') return data;
    
    const optimized = { ...data };
    
    switch (capabilities.preferredFormat) {
      case 'minimal':
        // Remove non-essential fields
        delete optimized.debug;
        delete optimized.metadata;
        delete optimized.trace;
        
        // Limit array sizes
        if (Array.isArray(optimized.data)) {
          optimized.data = optimized.data.slice(0, 50);
        }
        
        // Simplify nested objects
        if (optimized.details && typeof optimized.details === 'object') {
          optimized.details = {
            essential: optimized.details.essential,
            summary: optimized.details.summary
          };
        }
        break;
        
      case 'standard':
        // Moderate optimization
        if (Array.isArray(optimized.data)) {
          optimized.data = optimized.data.slice(0, 200);
        }
        break;
        
      case 'detailed':
        // No optimization needed
        break;
    }
    
    return optimized;
  }

  private compressResponse(data: any, req: Request, res: Response): any {
    const capabilities = (req as any).clientCapabilities;
    if (!capabilities || capabilities.compressionSupport.length === 0) {
      return data;
    }
    
    const dataString = typeof data === 'string' ? data : JSON.stringify(data);
    const dataSize = Buffer.byteLength(dataString, 'utf8');
    
    // Only compress if above threshold
    if (dataSize < this.config.compressionThreshold) {
      return data;
    }
    
    // Set compression headers (actual compression would be handled by express compression middleware)
    if (capabilities.compressionSupport.includes('gzip')) {
      res.set('Content-Encoding', 'gzip');
    } else if (capabilities.compressionSupport.includes('deflate')) {
      res.set('Content-Encoding', 'deflate');
    }
    
    // Mark metrics
    const requestId = (req as any).requestId;
    const metrics = this.requestMetrics.get(requestId);
    if (metrics) {
      metrics.compressionRatio = 0.7; // Estimated compression ratio
    }
    
    return data;
  }

  private finalizeMetrics(requestId: string, res: Response): void {
    const metrics = this.requestMetrics.get(requestId);
    if (!metrics) return;
    
    metrics.endTime = Date.now();
    metrics.duration = metrics.endTime - metrics.startTime;
    metrics.statusCode = res.statusCode;
    
    // Remove from active requests
    this.activeRequests.delete(requestId);
    
    // Check if request was slow
    if (metrics.duration > this.config.slowRequestThreshold) {
      this.slowRequestLog.push({ ...metrics });
      
      // Trim slow request log
      if (this.slowRequestLog.length > this.maxSlowRequestLog) {
        this.slowRequestLog = this.slowRequestLog.slice(-this.maxSlowRequestLog);
      }
    }
    
    // Cleanup old metrics (keep only last 1000)
    if (this.requestMetrics.size > 1000) {
      const oldestEntries = Array.from(this.requestMetrics.entries())
        .sort(([, a], [, b]) => a.startTime - b.startTime)
        .slice(0, 200);
      
      oldestEntries.forEach(([id]) => this.requestMetrics.delete(id));
    }
  }
}

// Export middleware factory function
export function createPerformanceMiddleware(config?: Partial<PerformanceConfig>) {
  const middleware = new PerformanceMiddleware(config);
  
  return {
    // Core middleware
    performance: middleware.middleware(),
    responseOptimization: middleware.responseOptimization(),
    compression: middleware.compressionMiddleware(),
    metrics: middleware.metricsCollection(),
    
    // Utility methods
    getStats: () => middleware.getStats(),
    getSlowRequests: () => middleware.getSlowRequests(),
    clearMetrics: () => middleware.clearMetrics(),
    
    // Access to middleware instance
    instance: middleware
  };
}