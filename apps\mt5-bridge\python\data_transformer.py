"""
Data Transformation Layer
Converts MT5 data formats to GoldDaddy platform format
"""

from typing import Dict, Any, Optional, List
from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP
from dataclasses import dataclass
import json

@dataclass
class TransformationConfig:
    """Configuration for data transformation"""
    decimal_places: int = 5
    volume_decimal_places: int = 2
    timestamp_format: str = "iso"  # 'iso', 'unix', 'string'
    include_metadata: bool = True
    normalize_symbols: bool = True

class DataTransformer:
    """
    Transforms MT5 data to GoldDaddy platform format
    """
    
    def __init__(self, config: TransformationConfig = None):
        self.config = config or TransformationConfig()
        
    def transform_price_update(self, mt5_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform MT5 price tick to platform format
        
        Input format (MT5):
        {
            'symbol': 'EURUSD',
            'bid': 1.08425,
            'ask': 1.08435,
            'timestamp': datetime,
            'volume': 100,
            'spread': 0.0001,
            'source': 'mt5'
        }
        
        Output format (Platform):
        {
            'type': 'price_update',
            'instrument': 'EURUSD',
            'bid': '1.08425',
            'ask': '1.08435', 
            'mid': '1.08430',
            'spread': '0.00010',
            'timestamp': '2025-08-18T12:34:56.789Z',
            'volume': '100.00',
            'source': 'mt5',
            'metadata': {...}
        }
        """
        try:
            symbol = self._normalize_symbol(mt5_data['symbol'])
            bid = self._format_price(mt5_data['bid'])
            ask = self._format_price(mt5_data['ask'])
            mid = self._format_price((mt5_data['bid'] + mt5_data['ask']) / 2)
            spread = self._format_price(mt5_data.get('spread', mt5_data['ask'] - mt5_data['bid']))
            
            transformed = {
                'type': 'price_update',
                'instrument': symbol,
                'bid': bid,
                'ask': ask,
                'mid': mid,
                'spread': spread,
                'timestamp': self._format_timestamp(mt5_data['timestamp']),
                'volume': self._format_volume(mt5_data.get('volume', 0)),
                'source': mt5_data.get('source', 'mt5')
            }
            
            # Add metadata if enabled
            if self.config.include_metadata:
                transformed['metadata'] = {
                    'symbol_raw': mt5_data['symbol'],
                    'transformation_time': self._format_timestamp(datetime.now()),
                    'decimal_places': self.config.decimal_places,
                    'platform_version': '1.0.0'
                }
                
            return transformed
            
        except Exception as e:
            raise ValueError(f"Failed to transform price update: {e}")
            
    def transform_market_data(self, mt5_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform MT5 historical data to platform format
        
        Input format (MT5):
        {
            'symbol': 'EURUSD',
            'timeframe': '1m',
            'timestamp': datetime,
            'open': 1.08420,
            'high': 1.08450,
            'low': 1.08400,
            'close': 1.08430,
            'volume': 1500,
            'source': 'mt5'
        }
        
        Output format (Platform):
        {
            'type': 'market_data',
            'instrument': 'EURUSD',
            'timeframe': '1m',
            'timestamp': '2025-08-18T12:34:00.000Z',
            'open': '1.08420',
            'high': '1.08450', 
            'low': '1.08400',
            'close': '1.08430',
            'volume': '1500.00',
            'source': 'mt5',
            'technical_indicators': {...}
        }
        """
        try:
            symbol = self._normalize_symbol(mt5_data['symbol'])
            
            transformed = {
                'type': 'market_data',
                'instrument': symbol,
                'timeframe': mt5_data['timeframe'],
                'timestamp': self._format_timestamp(mt5_data['timestamp']),
                'open': self._format_price(mt5_data['open']),
                'high': self._format_price(mt5_data['high']),
                'low': self._format_price(mt5_data['low']),
                'close': self._format_price(mt5_data['close']),
                'volume': self._format_volume(mt5_data['volume']),
                'source': mt5_data.get('source', 'mt5')
            }
            
            # Add technical indicators if available
            indicators = self._extract_technical_indicators(mt5_data)
            if indicators:
                transformed['technical_indicators'] = indicators
                
            # Add metadata if enabled
            if self.config.include_metadata:
                transformed['metadata'] = {
                    'symbol_raw': mt5_data['symbol'],
                    'transformation_time': self._format_timestamp(datetime.now()),
                    'candle_duration': self._get_candle_duration(mt5_data['timeframe']),
                    'platform_version': '1.0.0'
                }
                
            return transformed
            
        except Exception as e:
            raise ValueError(f"Failed to transform market data: {e}")
            
    def transform_trade_data(self, mt5_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform MT5 trade data to platform format
        
        Input format (MT5):
        {
            'ticket': 123456,
            'symbol': 'EURUSD',
            'type': 0,  # 0=buy, 1=sell
            'volume': 0.1,
            'open_price': 1.08420,
            'close_price': 1.08450,
            'open_time': datetime,
            'close_time': datetime,
            'profit': 3.0,
            'comment': 'test trade'
        }
        
        Output format (Platform):
        {
            'type': 'trade_data',
            'id': '123456',
            'instrument': 'EURUSD',
            'side': 'buy',
            'quantity': '0.10',
            'entry_price': '1.08420',
            'exit_price': '1.08450',
            'open_time': '2025-08-18T12:34:56.789Z',
            'close_time': '2025-08-18T12:35:26.789Z',
            'pnl': '3.00',
            'status': 'closed',
            'comment': 'test trade'
        }
        """
        try:
            symbol = self._normalize_symbol(mt5_data['symbol'])
            trade_type = 'buy' if mt5_data['type'] == 0 else 'sell'
            
            transformed = {
                'type': 'trade_data',
                'id': str(mt5_data['ticket']),
                'instrument': symbol,
                'side': trade_type,
                'quantity': self._format_volume(mt5_data['volume']),
                'entry_price': self._format_price(mt5_data['open_price']),
                'open_time': self._format_timestamp(mt5_data['open_time']),
                'status': 'open' if mt5_data.get('close_time') is None else 'closed',
                'comment': mt5_data.get('comment', '')
            }
            
            # Add exit data if trade is closed
            if mt5_data.get('close_price') is not None:
                transformed['exit_price'] = self._format_price(mt5_data['close_price'])
                
            if mt5_data.get('close_time') is not None:
                transformed['close_time'] = self._format_timestamp(mt5_data['close_time'])
                
            if mt5_data.get('profit') is not None:
                transformed['pnl'] = self._format_currency(mt5_data['profit'])
                
            # Add metadata if enabled
            if self.config.include_metadata:
                transformed['metadata'] = {
                    'ticket_raw': mt5_data['ticket'],
                    'type_raw': mt5_data['type'],
                    'transformation_time': self._format_timestamp(datetime.now()),
                    'platform_version': '1.0.0'
                }
                
            return transformed
            
        except Exception as e:
            raise ValueError(f"Failed to transform trade data: {e}")
            
    def _normalize_symbol(self, symbol: str) -> str:
        """Normalize symbol format"""
        if not self.config.normalize_symbols:
            return symbol
            
        # Remove common suffixes and normalize format
        normalized = symbol.upper().replace('.', '').replace('_', '')
        
        # Map common variations
        symbol_map = {
            'EURUSD': 'EURUSD',
            'GBPUSD': 'GBPUSD', 
            'USDJPY': 'USDJPY',
            'USDCHF': 'USDCHF',
            'AUDUSD': 'AUDUSD',
            'USDCAD': 'USDCAD',
            'NZDUSD': 'NZDUSD',
            'EURGBP': 'EURGBP',
            'EURJPY': 'EURJPY',
            'GBPJPY': 'GBPJPY'
        }
        
        return symbol_map.get(normalized, normalized)
        
    def _format_price(self, price: float) -> str:
        """Format price with appropriate decimal places"""
        if price is None:
            return "0.00000"
            
        decimal_price = Decimal(str(price))
        quantized = decimal_price.quantize(
            Decimal('0.' + '0' * self.config.decimal_places),
            rounding=ROUND_HALF_UP
        )
        return str(quantized)
        
    def _format_volume(self, volume: float) -> str:
        """Format volume with appropriate decimal places"""
        if volume is None:
            return "0.00"
            
        decimal_volume = Decimal(str(volume))
        quantized = decimal_volume.quantize(
            Decimal('0.' + '0' * self.config.volume_decimal_places),
            rounding=ROUND_HALF_UP
        )
        return str(quantized)
        
    def _format_currency(self, amount: float) -> str:
        """Format currency amount"""
        if amount is None:
            return "0.00"
            
        decimal_amount = Decimal(str(amount))
        quantized = decimal_amount.quantize(
            Decimal('0.01'),
            rounding=ROUND_HALF_UP
        )
        return str(quantized)
        
    def _format_timestamp(self, timestamp: datetime) -> str:
        """Format timestamp according to configuration"""
        if timestamp is None:
            return datetime.now().isoformat() + 'Z'
            
        if self.config.timestamp_format == 'iso':
            return timestamp.isoformat() + 'Z'
        elif self.config.timestamp_format == 'unix':
            return str(int(timestamp.timestamp()))
        elif self.config.timestamp_format == 'string':
            return timestamp.strftime('%Y-%m-%d %H:%M:%S')
        else:
            return timestamp.isoformat() + 'Z'
            
    def _extract_technical_indicators(self, mt5_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract technical indicators from MT5 data if available"""
        indicators = {}
        
        # Check for common indicators in the data
        indicator_fields = [
            'sma_20', 'sma_50', 'sma_200',
            'ema_12', 'ema_26',
            'rsi_14', 'rsi_value',
            'macd_line', 'macd_signal', 'macd_histogram',
            'bb_upper', 'bb_middle', 'bb_lower',
            'atr_14'
        ]
        
        for field in indicator_fields:
            if field in mt5_data and mt5_data[field] is not None:
                indicators[field] = self._format_price(mt5_data[field])
                
        return indicators if indicators else None
        
    def _get_candle_duration(self, timeframe: str) -> int:
        """Get candle duration in seconds"""
        duration_map = {
            '1m': 60,
            '5m': 300,
            '15m': 900,
            '30m': 1800,
            '1h': 3600,
            '4h': 14400,
            '1d': 86400
        }
        return duration_map.get(timeframe, 60)
        
    def batch_transform(self, data_list: List[Dict[str, Any]], data_type: str) -> List[Dict[str, Any]]:
        """Transform multiple data items in batch"""
        transformed_list = []
        
        for data_item in data_list:
            try:
                if data_type == 'price_update':
                    transformed = self.transform_price_update(data_item)
                elif data_type == 'market_data':
                    transformed = self.transform_market_data(data_item)
                elif data_type == 'trade_data':
                    transformed = self.transform_trade_data(data_item)
                else:
                    raise ValueError(f"Unknown data type: {data_type}")
                    
                transformed_list.append(transformed)
                
            except Exception as e:
                # Log error but continue processing other items
                print(f"Failed to transform item: {e}")
                continue
                
        return transformed_list
        
    def validate_transformed_data(self, data: Dict[str, Any], data_type: str) -> bool:
        """Validate transformed data format"""
        try:
            required_fields = {
                'price_update': ['type', 'instrument', 'bid', 'ask', 'timestamp'],
                'market_data': ['type', 'instrument', 'timeframe', 'timestamp', 'open', 'high', 'low', 'close', 'volume'],
                'trade_data': ['type', 'id', 'instrument', 'side', 'quantity', 'entry_price', 'open_time', 'status']
            }
            
            if data_type not in required_fields:
                return False
                
            for field in required_fields[data_type]:
                if field not in data:
                    return False
                    
            return True
            
        except Exception:
            return False