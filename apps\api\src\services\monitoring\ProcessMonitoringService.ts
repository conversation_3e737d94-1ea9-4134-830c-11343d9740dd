import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import os from 'os';
import process from 'process';
import { performance } from 'perf_hooks';

/**
 * Process Monitoring Service for tracking Node.js process performance
 * Monitors memory usage, CPU usage, event loop health, and garbage collection
 */
export class ProcessMonitoringService extends EventEmitter {
  private prisma: PrismaClient;
  private processMetrics: ProcessMetrics[] = [];
  private gcMetrics: GCMetrics[] = [];
  private eventLoopMetrics: EventLoopMetrics[] = [];
  private isMonitoring = false;
  private monitoringIntervals: NodeJS.Timeout[] = [];
  private readonly MONITORING_INTERVAL = 30000; // 30 seconds
  private readonly METRICS_RETENTION_LIMIT = 200; // Keep last 200 metrics

  // Alert thresholds
  private readonly THRESHOLDS = {
    memory: {
      warning: 80, // 80% of heap limit
      critical: 95, // 95% of heap limit
    },
    cpu: {
      warning: 70, // 70% CPU usage
      critical: 90, // 90% CPU usage
    },
    eventLoop: {
      warning: 100, // 100ms event loop lag
      critical: 500, // 500ms event loop lag
    },
    gc: {
      warning: 50, // 50ms GC time
      critical: 200, // 200ms GC time
    },
  };

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
    this.setupGCMonitoring();
  }

  /**
   * Start process monitoring
   */
  async startMonitoring(config: ProcessMonitoringConfig = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('Process monitoring already active');
      return;
    }

    console.log('⚡ Starting Node.js process monitoring...');
    this.isMonitoring = true;

    const interval = config.intervalMs || this.MONITORING_INTERVAL;

    // Start process metrics collection
    const processInterval = setInterval(async () => {
      await this.collectProcessMetrics();
    }, interval);
    this.monitoringIntervals.push(processInterval);

    // Start event loop monitoring (more frequent)
    const eventLoopInterval = setInterval(() => {
      this.measureEventLoopLag();
    }, interval / 2); // Half the main interval
    this.monitoringIntervals.push(eventLoopInterval);

    // Start periodic analysis
    const analysisInterval = setInterval(async () => {
      await this.analyzePerformanceTrends();
    }, interval * 4); // Every 2 minutes
    this.monitoringIntervals.push(analysisInterval);

    console.log('✅ Process monitoring started');
    this.emit('monitoringStarted', { interval });
  }

  /**
   * Stop process monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ Stopping process monitoring...');
    this.isMonitoring = false;

    // Clear all monitoring intervals
    this.monitoringIntervals.forEach(interval => clearInterval(interval));
    this.monitoringIntervals = [];

    console.log('✅ Process monitoring stopped');
    this.emit('monitoringStopped');
  }

  /**
   * Setup garbage collection monitoring
   */
  private setupGCMonitoring(): void {
    // Enable GC monitoring if available
    if (process.env.NODE_ENV !== 'production') {
      // In development, we can use performanceObserver
      try {
        const { PerformanceObserver } = require('perf_hooks');
        
        const gcObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          for (const entry of entries) {
            if (entry.entryType === 'gc') {
              this.recordGCEvent({
                type: this.getGCTypeName(entry.detail?.kind || 0),
                duration: entry.duration,
                timestamp: new Date(entry.startTime + performance.timeOrigin),
              });
            }
          }
        });
        
        gcObserver.observe({ entryTypes: ['gc'] });
      } catch (error) {
        console.warn('GC monitoring not available:', error);
      }
    }
  }

  /**
   * Get GC type name from kind number
   */
  private getGCTypeName(kind: number): string {
    switch (kind) {
      case 1: return 'Scavenge';
      case 2: return 'MarkSweepCompact';
      case 4: return 'IncrementalMarking';
      case 8: return 'ProcessWeakCallbacks';
      case 15: return 'All';
      default: return 'Unknown';
    }
  }

  /**
   * Record garbage collection event
   */
  private recordGCEvent(event: GCEvent): void {
    const gcMetric: GCMetrics = {
      timestamp: event.timestamp,
      type: event.type,
      duration: event.duration,
      memoryBefore: process.memoryUsage(),
      memoryAfter: process.memoryUsage(), // In real implementation, this would be actual after values
    };

    this.gcMetrics.push(gcMetric);

    // Keep only recent GC metrics
    if (this.gcMetrics.length > this.METRICS_RETENTION_LIMIT) {
      this.gcMetrics = this.gcMetrics.slice(-this.METRICS_RETENTION_LIMIT);
    }

    // Check for GC performance issues
    if (event.duration > this.THRESHOLDS.gc.critical) {
      this.emit('alert', {
        type: 'gc_performance',
        severity: 'critical',
        message: `Long GC pause: ${event.duration.toFixed(1)}ms (${event.type})`,
        timestamp: event.timestamp,
        metadata: { duration: event.duration, gcType: event.type },
      });
    } else if (event.duration > this.THRESHOLDS.gc.warning) {
      this.emit('alert', {
        type: 'gc_performance',
        severity: 'warning',
        message: `Slow GC: ${event.duration.toFixed(1)}ms (${event.type})`,
        timestamp: event.timestamp,
        metadata: { duration: event.duration, gcType: event.type },
      });
    }

    this.emit('gcEvent', gcMetric);
  }

  /**
   * Collect comprehensive process metrics
   */
  private async collectProcessMetrics(): Promise<void> {
    const timestamp = new Date();
    const startTime = performance.now();

    try {
      // Get memory usage
      const memoryUsage = process.memoryUsage();
      const memoryPercent = this.calculateMemoryPercentage(memoryUsage);

      // Get CPU usage
      const cpuUsage = process.cpuUsage();
      const cpuPercent = this.calculateCpuPercentage(cpuUsage);

      // Get system information
      const systemInfo = this.getSystemInfo();

      // Get process information
      const processInfo = this.getProcessInfo();

      // Create metrics record
      const metrics: ProcessMetrics = {
        timestamp,
        pid: process.pid,
        memory: {
          heapUsed: memoryUsage.heapUsed,
          heapTotal: memoryUsage.heapTotal,
          external: memoryUsage.external,
          rss: memoryUsage.rss,
          arrayBuffers: memoryUsage.arrayBuffers || 0,
          heapUsagePercent: memoryPercent.heapPercent,
          rssPercent: memoryPercent.rssPercent,
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system,
          percent: cpuPercent,
        },
        system: systemInfo,
        process: processInfo,
        uptime: process.uptime(),
        collectionTime: performance.now() - startTime,
        healthStatus: this.determineProcessHealth(memoryPercent, cpuPercent),
      };

      // Store metrics
      this.processMetrics.push(metrics);

      // Keep only recent metrics
      if (this.processMetrics.length > this.METRICS_RETENTION_LIMIT) {
        this.processMetrics = this.processMetrics.slice(-this.METRICS_RETENTION_LIMIT);
      }

      // Check for alerts
      await this.checkProcessAlerts(metrics);

      // Emit metrics event
      this.emit('processMetrics', metrics);

      console.log(`📊 Process metrics: Memory ${memoryPercent.heapPercent.toFixed(1)}%, CPU ${cpuPercent.toFixed(1)}%`);

    } catch (error) {
      console.error('Failed to collect process metrics:', error);
      this.emit('error', error);
    }
  }

  /**
   * Calculate memory usage percentage
   */
  private calculateMemoryPercentage(memoryUsage: NodeJS.MemoryUsage): {
    heapPercent: number;
    rssPercent: number;
  } {
    const totalSystemMemory = os.totalmem();
    const heapPercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;
    const rssPercent = (memoryUsage.rss / totalSystemMemory) * 100;

    return { heapPercent, rssPercent };
  }

  /**
   * Calculate CPU usage percentage (approximation)
   */
  private calculateCpuPercentage(cpuUsage: NodeJS.CpuUsage): number {
    // This is a simplified calculation
    // In production, you'd want to compare with previous measurements
    const totalCpu = cpuUsage.user + cpuUsage.system;
    const totalCpuSeconds = totalCpu / 1000000; // Convert microseconds to seconds
    const uptimeSeconds = process.uptime();
    
    return Math.min((totalCpuSeconds / uptimeSeconds) * 100, 100);
  }

  /**
   * Get system information
   */
  private getSystemInfo(): SystemInfo {
    const loadAvg = os.loadavg();
    
    return {
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      totalMemory: os.totalmem(),
      freeMemory: os.freememory(),
      cpuCount: os.cpus().length,
      loadAverage: {
        oneMinute: loadAvg[0],
        fiveMinutes: loadAvg[1],
        fifteenMinutes: loadAvg[2],
      },
      uptime: os.uptime(),
    };
  }

  /**
   * Get process-specific information
   */
  private getProcessInfo(): ProcessInfo {
    return {
      pid: process.pid,
      ppid: process.ppid,
      title: process.title,
      argv: process.argv.length,
      execPath: process.execPath,
      cwd: process.cwd(),
      uid: process.getuid ? process.getuid() : undefined,
      gid: process.getgid ? process.getgid() : undefined,
      env: Object.keys(process.env).length,
    };
  }

  /**
   * Determine process health status
   */
  private determineProcessHealth(
    memoryPercent: { heapPercent: number; rssPercent: number },
    cpuPercent: number
  ): 'healthy' | 'warning' | 'critical' {
    // Critical conditions
    if (memoryPercent.heapPercent > this.THRESHOLDS.memory.critical ||
        cpuPercent > this.THRESHOLDS.cpu.critical) {
      return 'critical';
    }

    // Warning conditions
    if (memoryPercent.heapPercent > this.THRESHOLDS.memory.warning ||
        cpuPercent > this.THRESHOLDS.cpu.warning ||
        memoryPercent.rssPercent > 50) { // RSS using more than 50% of system memory
      return 'warning';
    }

    return 'healthy';
  }

  /**
   * Check for process-related alerts
   */
  private async checkProcessAlerts(metrics: ProcessMetrics): Promise<void> {
    const alerts: ProcessAlert[] = [];

    // Memory alerts
    if (metrics.memory.heapUsagePercent > this.THRESHOLDS.memory.critical) {
      alerts.push({
        type: 'memory_critical',
        severity: 'critical',
        message: `Critical heap memory usage: ${metrics.memory.heapUsagePercent.toFixed(1)}%`,
        timestamp: metrics.timestamp,
        metadata: { 
          heapUsed: metrics.memory.heapUsed,
          heapTotal: metrics.memory.heapTotal,
          percent: metrics.memory.heapUsagePercent,
        },
      });
    } else if (metrics.memory.heapUsagePercent > this.THRESHOLDS.memory.warning) {
      alerts.push({
        type: 'memory_warning',
        severity: 'warning',
        message: `High heap memory usage: ${metrics.memory.heapUsagePercent.toFixed(1)}%`,
        timestamp: metrics.timestamp,
        metadata: { percent: metrics.memory.heapUsagePercent },
      });
    }

    // CPU alerts
    if (metrics.cpu.percent > this.THRESHOLDS.cpu.critical) {
      alerts.push({
        type: 'cpu_critical',
        severity: 'critical',
        message: `Critical CPU usage: ${metrics.cpu.percent.toFixed(1)}%`,
        timestamp: metrics.timestamp,
        metadata: { percent: metrics.cpu.percent },
      });
    } else if (metrics.cpu.percent > this.THRESHOLDS.cpu.warning) {
      alerts.push({
        type: 'cpu_warning',
        severity: 'warning',
        message: `High CPU usage: ${metrics.cpu.percent.toFixed(1)}%`,
        timestamp: metrics.timestamp,
        metadata: { percent: metrics.cpu.percent },
      });
    }

    // System load alerts
    if (metrics.system.loadAverage.oneMinute > metrics.system.cpuCount * 2) {
      alerts.push({
        type: 'system_load',
        severity: 'warning',
        message: `High system load: ${metrics.system.loadAverage.oneMinute.toFixed(2)}`,
        timestamp: metrics.timestamp,
        metadata: { 
          load: metrics.system.loadAverage.oneMinute,
          cpuCount: metrics.system.cpuCount,
        },
      });
    }

    // Memory leak detection (simplified)
    if (this.processMetrics.length > 10) {
      const recentMetrics = this.processMetrics.slice(-10);
      const memoryTrend = this.calculateMemoryTrend(recentMetrics);
      
      if (memoryTrend.isIncreasing && memoryTrend.growthRate > 5) { // 5% growth over 10 samples
        alerts.push({
          type: 'memory_leak_suspected',
          severity: 'warning',
          message: `Potential memory leak detected: ${memoryTrend.growthRate.toFixed(1)}% growth`,
          timestamp: metrics.timestamp,
          metadata: { 
            growthRate: memoryTrend.growthRate,
            samples: recentMetrics.length,
          },
        });
      }
    }

    // Emit alerts
    for (const alert of alerts) {
      this.emit('alert', alert);
    }
  }

  /**
   * Calculate memory growth trend
   */
  private calculateMemoryTrend(metrics: ProcessMetrics[]): {
    isIncreasing: boolean;
    growthRate: number;
  } {
    if (metrics.length < 2) {
      return { isIncreasing: false, growthRate: 0 };
    }

    const first = metrics[0];
    const last = metrics[metrics.length - 1];
    
    const growthRate = ((last.memory.heapUsed - first.memory.heapUsed) / first.memory.heapUsed) * 100;
    
    return {
      isIncreasing: growthRate > 0,
      growthRate: Math.abs(growthRate),
    };
  }

  /**
   * Measure event loop lag
   */
  private measureEventLoopLag(): void {
    const start = process.hrtime.bigint();
    
    setImmediate(() => {
      const lag = Number(process.hrtime.bigint() - start) / 1000000; // Convert to milliseconds
      
      const eventLoopMetric: EventLoopMetrics = {
        timestamp: new Date(),
        lag,
        isHealthy: lag < this.THRESHOLDS.eventLoop.warning,
      };

      this.eventLoopMetrics.push(eventLoopMetric);

      // Keep only recent event loop metrics
      if (this.eventLoopMetrics.length > this.METRICS_RETENTION_LIMIT) {
        this.eventLoopMetrics = this.eventLoopMetrics.slice(-this.METRICS_RETENTION_LIMIT);
      }

      // Check for event loop alerts
      if (lag > this.THRESHOLDS.eventLoop.critical) {
        this.emit('alert', {
          type: 'event_loop_blocked',
          severity: 'critical',
          message: `Event loop severely blocked: ${lag.toFixed(1)}ms`,
          timestamp: eventLoopMetric.timestamp,
          metadata: { lag },
        });
      } else if (lag > this.THRESHOLDS.eventLoop.warning) {
        this.emit('alert', {
          type: 'event_loop_slow',
          severity: 'warning',
          message: `Event loop slow: ${lag.toFixed(1)}ms`,
          timestamp: eventLoopMetric.timestamp,
          metadata: { lag },
        });
      }

      this.emit('eventLoopMetrics', eventLoopMetric);
    });
  }

  /**
   * Analyze performance trends
   */
  private async analyzePerformanceTrends(): Promise<void> {
    if (this.processMetrics.length < 10) {
      return; // Need sufficient data for trend analysis
    }

    const recentMetrics = this.processMetrics.slice(-20); // Last 20 samples
    const analysis = this.performTrendAnalysis(recentMetrics);

    this.emit('trendAnalysis', {
      timestamp: new Date(),
      analysis,
      sampleSize: recentMetrics.length,
      timeSpan: recentMetrics[recentMetrics.length - 1].timestamp.getTime() - recentMetrics[0].timestamp.getTime(),
    });

    // Log significant trends
    if (analysis.memory.trend === 'increasing' && analysis.memory.strength > 0.7) {
      console.warn(`📈 Memory trend: ${analysis.memory.trend} (strength: ${analysis.memory.strength.toFixed(2)})`);
    }

    if (analysis.cpu.trend === 'increasing' && analysis.cpu.strength > 0.7) {
      console.warn(`📈 CPU trend: ${analysis.cpu.trend} (strength: ${analysis.cpu.strength.toFixed(2)})`);
    }
  }

  /**
   * Perform trend analysis on metrics
   */
  private performTrendAnalysis(metrics: ProcessMetrics[]): TrendAnalysis {
    const memoryValues = metrics.map(m => m.memory.heapUsagePercent);
    const cpuValues = metrics.map(m => m.cpu.percent);
    const eventLoopValues = this.eventLoopMetrics.slice(-metrics.length).map(e => e.lag);

    return {
      memory: this.calculateTrend(memoryValues),
      cpu: this.calculateTrend(cpuValues),
      eventLoop: this.calculateTrend(eventLoopValues),
      stability: this.calculateStability(metrics),
    };
  }

  /**
   * Calculate trend for a series of values
   */
  private calculateTrend(values: number[]): TrendInfo {
    if (values.length < 3) {
      return { trend: 'stable', strength: 0, slope: 0 };
    }

    // Simple linear regression to find trend
    const n = values.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = values.reduce((a, b) => a + b, 0);
    const sumXY = x.reduce((sum, xi, i) => sum + xi * values[i], 0);
    const sumXX = x.reduce((sum, xi) => sum + xi * xi, 0);

    const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
    const strength = Math.abs(slope) / (Math.max(...values) - Math.min(...values));

    let trend: 'increasing' | 'decreasing' | 'stable';
    if (Math.abs(slope) < 0.01) {
      trend = 'stable';
    } else if (slope > 0) {
      trend = 'increasing';
    } else {
      trend = 'decreasing';
    }

    return { trend, strength: Math.min(1, strength), slope };
  }

  /**
   * Calculate system stability
   */
  private calculateStability(metrics: ProcessMetrics[]): number {
    if (metrics.length < 5) {
      return 1; // Assume stable with insufficient data
    }

    const memoryCV = this.calculateCoefficientOfVariation(metrics.map(m => m.memory.heapUsagePercent));
    const cpuCV = this.calculateCoefficientOfVariation(metrics.map(m => m.cpu.percent));
    
    // Lower coefficient of variation = higher stability
    const stability = 1 - Math.min(1, (memoryCV + cpuCV) / 2);
    
    return Math.max(0, stability);
  }

  /**
   * Calculate coefficient of variation
   */
  private calculateCoefficientOfVariation(values: number[]): number {
    const mean = values.reduce((a, b) => a + b, 0) / values.length;
    const variance = values.reduce((sum, value) => sum + Math.pow(value - mean, 2), 0) / values.length;
    const standardDeviation = Math.sqrt(variance);
    
    return mean !== 0 ? standardDeviation / mean : 0;
  }

  /**
   * Get current process summary
   */
  getProcessSummary(): ProcessSummary {
    const recent = this.processMetrics.slice(-10);
    const recentEventLoop = this.eventLoopMetrics.slice(-10);
    const recentGC = this.gcMetrics.slice(-10);

    if (recent.length === 0) {
      return {
        isHealthy: false,
        memory: { current: 0, trend: 'stable', peak: 0 },
        cpu: { current: 0, trend: 'stable', peak: 0 },
        eventLoop: { current: 0, average: 0, isHealthy: false },
        gc: { recentCount: 0, averageDuration: 0, totalTime: 0 },
        uptime: process.uptime(),
        lastUpdate: new Date(),
        isMonitoring: this.isMonitoring,
      };
    }

    const latest = recent[recent.length - 1];
    const memoryTrend = this.calculateTrend(recent.map(m => m.memory.heapUsagePercent));
    const cpuTrend = this.calculateTrend(recent.map(m => m.cpu.percent));

    return {
      isHealthy: latest.healthStatus === 'healthy',
      memory: {
        current: latest.memory.heapUsagePercent,
        trend: memoryTrend.trend,
        peak: Math.max(...recent.map(m => m.memory.heapUsagePercent)),
      },
      cpu: {
        current: latest.cpu.percent,
        trend: cpuTrend.trend,
        peak: Math.max(...recent.map(m => m.cpu.percent)),
      },
      eventLoop: {
        current: recentEventLoop.length > 0 ? recentEventLoop[recentEventLoop.length - 1].lag : 0,
        average: recentEventLoop.length > 0 
          ? recentEventLoop.reduce((sum, e) => sum + e.lag, 0) / recentEventLoop.length 
          : 0,
        isHealthy: recentEventLoop.length > 0 
          ? recentEventLoop[recentEventLoop.length - 1].isHealthy 
          : true,
      },
      gc: {
        recentCount: recentGC.length,
        averageDuration: recentGC.length > 0 
          ? recentGC.reduce((sum, g) => sum + g.duration, 0) / recentGC.length 
          : 0,
        totalTime: recentGC.reduce((sum, g) => sum + g.duration, 0),
      },
      uptime: latest.uptime,
      lastUpdate: latest.timestamp,
      isMonitoring: this.isMonitoring,
    };
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus(): ProcessMonitoringStatus {
    return {
      isActive: this.isMonitoring,
      intervalsActive: this.monitoringIntervals.length,
      metricsCollected: this.processMetrics.length,
      gcEventsRecorded: this.gcMetrics.length,
      eventLoopSamplesCollected: this.eventLoopMetrics.length,
      lastCollection: this.processMetrics.length > 0 
        ? this.processMetrics[this.processMetrics.length - 1].timestamp 
        : null,
    };
  }

  /**
   * Clear metrics (useful for testing)
   */
  clearMetrics(): void {
    this.processMetrics = [];
    this.gcMetrics = [];
    this.eventLoopMetrics = [];
    console.log('🧹 Process metrics cleared');
  }

  /**
   * Force garbage collection (if --expose-gc flag is set)
   */
  forceGC(): boolean {
    if (global.gc) {
      console.log('🗑️ Forcing garbage collection...');
      global.gc();
      return true;
    }
    
    console.warn('Garbage collection not exposed (use --expose-gc flag)');
    return false;
  }
}

// Type definitions
interface ProcessMonitoringConfig {
  intervalMs?: number;
}

interface ProcessMetrics {
  timestamp: Date;
  pid: number;
  memory: {
    heapUsed: number;
    heapTotal: number;
    external: number;
    rss: number;
    arrayBuffers: number;
    heapUsagePercent: number;
    rssPercent: number;
  };
  cpu: {
    user: number;
    system: number;
    percent: number;
  };
  system: SystemInfo;
  process: ProcessInfo;
  uptime: number;
  collectionTime: number;
  healthStatus: 'healthy' | 'warning' | 'critical';
}

interface SystemInfo {
  platform: string;
  arch: string;
  nodeVersion: string;
  totalMemory: number;
  freeMemory: number;
  cpuCount: number;
  loadAverage: {
    oneMinute: number;
    fiveMinutes: number;
    fifteenMinutes: number;
  };
  uptime: number;
}

interface ProcessInfo {
  pid: number;
  ppid: number;
  title: string;
  argv: number;
  execPath: string;
  cwd: string;
  uid?: number;
  gid?: number;
  env: number;
}

interface GCEvent {
  type: string;
  duration: number;
  timestamp: Date;
}

interface GCMetrics {
  timestamp: Date;
  type: string;
  duration: number;
  memoryBefore: NodeJS.MemoryUsage;
  memoryAfter: NodeJS.MemoryUsage;
}

interface EventLoopMetrics {
  timestamp: Date;
  lag: number;
  isHealthy: boolean;
}

interface ProcessAlert {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

interface TrendInfo {
  trend: 'increasing' | 'decreasing' | 'stable';
  strength: number;
  slope: number;
}

interface TrendAnalysis {
  memory: TrendInfo;
  cpu: TrendInfo;
  eventLoop: TrendInfo;
  stability: number;
}

interface ProcessSummary {
  isHealthy: boolean;
  memory: {
    current: number;
    trend: 'increasing' | 'decreasing' | 'stable';
    peak: number;
  };
  cpu: {
    current: number;
    trend: 'increasing' | 'decreasing' | 'stable';
    peak: number;
  };
  eventLoop: {
    current: number;
    average: number;
    isHealthy: boolean;
  };
  gc: {
    recentCount: number;
    averageDuration: number;
    totalTime: number;
  };
  uptime: number;
  lastUpdate: Date;
  isMonitoring: boolean;
}

interface ProcessMonitoringStatus {
  isActive: boolean;
  intervalsActive: number;
  metricsCollected: number;
  gcEventsRecorded: number;
  eventLoopSamplesCollected: number;
  lastCollection: Date | null;
}

export type {
  ProcessMonitoringConfig,
  ProcessMetrics,
  SystemInfo,
  ProcessInfo,
  GCEvent,
  GCMetrics,
  EventLoopMetrics,
  ProcessAlert,
  TrendAnalysis,
  ProcessSummary,
  ProcessMonitoringStatus,
};