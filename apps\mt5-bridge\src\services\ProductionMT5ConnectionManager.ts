/**
 * Production MT5 Connection Manager
 * 
 * Handles live MT5 broker connections with production-grade reliability features:
 * - Connection pooling with configurable pool sizes
 * - Load balancing across multiple broker endpoints
 * - Automatic reconnection with exponential backoff
 * - Connection lifecycle management
 * - Health monitoring and failover
 */

import { EventEmitter } from 'events';
import type { MarketData, Trade } from '@golddaddy/types';

// Connection configuration for a single broker
export interface BrokerConnectionConfig {
  id: string;
  name: string;
  server: string;
  login: string;
  password: string;
  priority: number;
  maxConnections: number;
  features: ('streaming' | 'trading' | 'history' | 'monitoring')[];
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

// Connection state tracking
export interface ConnectionState {
  id: string;
  brokerId: string;
  status: 'disconnected' | 'connecting' | 'connected' | 'error';
  lastConnect: Date | null;
  lastDisconnect: Date | null;
  lastError: string | null;
  retryCount: number;
  failureCount: number;
  requestCount: number;
  lastRequestTime: Date | null;
  isHealthy: boolean;
  latency: number;
}

// Health check result
export interface HealthCheckResult {
  connectionId: string;
  brokerId: string;
  healthy: boolean;
  latency: number;
  error: string | null;
  timestamp: Date;
}

// Connection pool statistics
export interface ConnectionPoolStats {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  failedConnections: number;
  totalRequests: number;
  averageLatency: number;
  brokerStats: Map<string, {
    connections: number;
    healthy: number;
    failed: number;
    averageLatency: number;
  }>;
}

/**
 * Production MT5 Connection Manager
 * Manages production broker connections with pooling and load balancing
 */
export class ProductionMT5ConnectionManager extends EventEmitter {
  private connections: Map<string, ConnectionState> = new Map();
  private brokerConfigs: Map<string, BrokerConnectionConfig> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private connectionCleanupInterval: NodeJS.Timeout | null = null;
  private isShuttingDown = false;
  private metrics = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageLatency: 0,
    startTime: new Date(),
    uptime: 0
  };

  constructor(private config: {
    healthCheckInterval?: number;
    connectionTimeout?: number;
    maxRetryAttempts?: number;
    reconnectDelay?: number;
    cleanupInterval?: number;
    loadBalancingStrategy?: 'round-robin' | 'least-connections' | 'health-based';
  } = {}) {
    super();
    
    this.config = {
      healthCheckInterval: 30000, // 30 seconds
      connectionTimeout: 10000,   // 10 seconds
      maxRetryAttempts: 5,
      reconnectDelay: 5000,       // 5 seconds
      cleanupInterval: 300000,    // 5 minutes
      loadBalancingStrategy: 'health-based',
      ...config
    };

    this.startHealthChecks();
    this.startConnectionCleanup();
  }

  /**
   * Initialize connection manager with broker configurations
   */
  async initialize(brokerConfigs: BrokerConnectionConfig[]): Promise<boolean> {
    try {
      console.log('🔗 Initializing Production MT5 Connection Manager...');
      
      // Validate and store broker configurations
      for (const brokerConfig of brokerConfigs) {
        if (!this.validateBrokerConfig(brokerConfig)) {
          throw new Error(`Invalid broker configuration for: ${brokerConfig.name}`);
        }
        this.brokerConfigs.set(brokerConfig.id, brokerConfig);
        console.log(`✅ Broker configured: ${brokerConfig.name} (max connections: ${brokerConfig.maxConnections})`);
      }

      // Create initial connection pool
      for (const brokerConfig of brokerConfigs) {
        await this.createConnectionPool(brokerConfig);
      }

      console.log(`✅ Production MT5 Connection Manager initialized with ${this.connections.size} connections`);
      this.emit('initialized', { totalConnections: this.connections.size });
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Production MT5 Connection Manager:', error);
      this.emit('error', error);
      return false;
    }
  }

  /**
   * Get an optimal connection for a request based on load balancing strategy
   */
  async getConnection(brokerId?: string, feature: string = 'trading'): Promise<string | null> {
    try {
      const availableConnections = this.getAvailableConnections(brokerId, feature);
      
      if (availableConnections.length === 0) {
        console.warn('⚠️ No available connections found');
        this.emit('noConnectionsAvailable', { brokerId, feature });
        return null;
      }

      // Apply load balancing strategy
      const connectionId = this.selectConnectionByStrategy(availableConnections);
      
      if (connectionId) {
        const connection = this.connections.get(connectionId);
        if (connection) {
          connection.requestCount++;
          connection.lastRequestTime = new Date();
          this.metrics.totalRequests++;
        }
      }

      return connectionId;
    } catch (error) {
      console.error('❌ Error getting connection:', error);
      this.emit('error', error);
      return null;
    }
  }

  /**
   * Get connection pool statistics
   */
  getPoolStats(): ConnectionPoolStats {
    const stats: ConnectionPoolStats = {
      totalConnections: this.connections.size,
      activeConnections: 0,
      idleConnections: 0,
      failedConnections: 0,
      totalRequests: this.metrics.totalRequests,
      averageLatency: this.metrics.averageLatency,
      brokerStats: new Map()
    };

    // Calculate broker-specific stats
    for (const [brokerId, brokerConfig] of this.brokerConfigs) {
      const brokerConnections = Array.from(this.connections.values())
        .filter(conn => conn.brokerId === brokerId);
      
      const healthyConnections = brokerConnections.filter(conn => conn.isHealthy);
      const failedConnections = brokerConnections.filter(conn => conn.status === 'error');
      const totalLatency = brokerConnections.reduce((sum, conn) => sum + conn.latency, 0);
      
      stats.brokerStats.set(brokerId, {
        connections: brokerConnections.length,
        healthy: healthyConnections.length,
        failed: failedConnections.length,
        averageLatency: brokerConnections.length > 0 ? totalLatency / brokerConnections.length : 0
      });

      stats.activeConnections += healthyConnections.length;
      stats.failedConnections += failedConnections.length;
    }

    stats.idleConnections = stats.totalConnections - stats.activeConnections - stats.failedConnections;
    
    return stats;
  }

  /**
   * Perform health check on a specific connection
   */
  async performHealthCheck(connectionId: string): Promise<HealthCheckResult> {
    const connection = this.connections.get(connectionId);
    if (!connection) {
      return {
        connectionId,
        brokerId: '',
        healthy: false,
        latency: 0,
        error: 'Connection not found',
        timestamp: new Date()
      };
    }

    const startTime = Date.now();
    try {
      // Mock health check - in production this would ping the actual MT5 connection
      await new Promise(resolve => setTimeout(resolve, 10 + Math.random() * 50)); // Simulate network latency
      
      const latency = Date.now() - startTime;
      connection.latency = latency;
      connection.isHealthy = true;
      connection.lastError = null;

      return {
        connectionId,
        brokerId: connection.brokerId,
        healthy: true,
        latency,
        error: null,
        timestamp: new Date()
      };
    } catch (error) {
      connection.isHealthy = false;
      connection.lastError = error instanceof Error ? error.message : 'Health check failed';
      connection.failureCount++;

      return {
        connectionId,
        brokerId: connection.brokerId,
        healthy: false,
        latency: Date.now() - startTime,
        error: connection.lastError,
        timestamp: new Date()
      };
    }
  }

  /**
   * Graceful shutdown of all connections
   */
  async shutdown(): Promise<void> {
    console.log('🔌 Shutting down Production MT5 Connection Manager...');
    this.isShuttingDown = true;

    // Stop health checks and cleanup
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    if (this.connectionCleanupInterval) {
      clearInterval(this.connectionCleanupInterval);
      this.connectionCleanupInterval = null;
    }

    // Close all connections
    const shutdownPromises = Array.from(this.connections.keys()).map(connectionId => 
      this.closeConnection(connectionId)
    );

    await Promise.allSettled(shutdownPromises);
    
    this.connections.clear();
    this.brokerConfigs.clear();
    
    console.log('✅ Production MT5 Connection Manager shutdown complete');
    this.emit('shutdown');
  }

  /**
   * Validate broker configuration
   */
  private validateBrokerConfig(config: BrokerConnectionConfig): boolean {
    return !!(
      config.id &&
      config.name &&
      config.server &&
      config.login &&
      config.password &&
      config.maxConnections > 0 &&
      config.features.length > 0
    );
  }

  /**
   * Create connection pool for a broker
   */
  private async createConnectionPool(brokerConfig: BrokerConnectionConfig): Promise<void> {
    const connectionPromises: Promise<void>[] = [];
    
    for (let i = 0; i < brokerConfig.maxConnections; i++) {
      const connectionId = `${brokerConfig.id}_${i}`;
      connectionPromises.push(this.createConnection(connectionId, brokerConfig));
    }

    await Promise.allSettled(connectionPromises);
    
    const successfulConnections = Array.from(this.connections.values())
      .filter(conn => conn.brokerId === brokerConfig.id && conn.status === 'connected').length;
    
    console.log(`✅ Created ${successfulConnections}/${brokerConfig.maxConnections} connections for ${brokerConfig.name}`);
  }

  /**
   * Create a single connection
   */
  private async createConnection(connectionId: string, brokerConfig: BrokerConnectionConfig): Promise<void> {
    const connectionState: ConnectionState = {
      id: connectionId,
      brokerId: brokerConfig.id,
      status: 'disconnected',
      lastConnect: null,
      lastDisconnect: null,
      lastError: null,
      retryCount: 0,
      failureCount: 0,
      requestCount: 0,
      lastRequestTime: null,
      isHealthy: false,
      latency: 0
    };

    this.connections.set(connectionId, connectionState);

    try {
      await this.connectToMT5(connectionId, brokerConfig);
    } catch (error) {
      console.error(`❌ Failed to create connection ${connectionId}:`, error);
      connectionState.status = 'error';
      connectionState.lastError = error instanceof Error ? error.message : 'Connection failed';
      connectionState.failureCount++;
    }
  }

  /**
   * Connect to MT5 broker
   */
  private async connectToMT5(connectionId: string, brokerConfig: BrokerConnectionConfig): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    connection.status = 'connecting';
    
    try {
      // Mock connection - in production this would use the actual MT5 API
      const connectStartTime = Date.now();
      await new Promise((resolve, reject) => {
        setTimeout(() => {
          // Simulate occasional connection failures
          if (Math.random() < 0.1) {
            reject(new Error('Connection timeout'));
          } else {
            resolve(true);
          }
        }, 1000 + Math.random() * 2000); // 1-3 second connection time
      });

      connection.status = 'connected';
      connection.lastConnect = new Date();
      connection.retryCount = 0;
      connection.isHealthy = true;
      connection.latency = Date.now() - connectStartTime;

      console.log(`✅ Connected to ${brokerConfig.name} (${connectionId})`);
      this.emit('connectionEstablished', { connectionId, brokerId: brokerConfig.id });
      
    } catch (error) {
      connection.status = 'error';
      connection.lastError = error instanceof Error ? error.message : 'Connection failed';
      connection.failureCount++;
      
      console.error(`❌ Failed to connect ${connectionId}:`, error);
      this.emit('connectionFailed', { connectionId, brokerId: brokerConfig.id, error: connection.lastError });
      
      // Schedule reconnection if not shutting down
      if (!this.isShuttingDown && connection.retryCount < (this.config.maxRetryAttempts || 5)) {
        setTimeout(() => this.reconnectConnection(connectionId), this.getReconnectDelay(connection.retryCount));
      }
    }
  }

  /**
   * Reconnect a failed connection
   */
  private async reconnectConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (!connection || this.isShuttingDown) return;

    const brokerConfig = this.brokerConfigs.get(connection.brokerId);
    if (!brokerConfig) return;

    connection.retryCount++;
    console.log(`🔄 Reconnecting ${connectionId} (attempt ${connection.retryCount}/${this.config.maxRetryAttempts})`);
    
    await this.connectToMT5(connectionId, brokerConfig);
  }

  /**
   * Close a specific connection
   */
  private async closeConnection(connectionId: string): Promise<void> {
    const connection = this.connections.get(connectionId);
    if (!connection) return;

    connection.status = 'disconnected';
    connection.lastDisconnect = new Date();
    connection.isHealthy = false;

    // Mock connection cleanup - in production would close actual MT5 connection
    console.log(`🔌 Closed connection ${connectionId}`);
    this.emit('connectionClosed', { connectionId, brokerId: connection.brokerId });
  }

  /**
   * Get available connections for a feature
   */
  private getAvailableConnections(brokerId?: string, feature: string = 'trading'): string[] {
    return Array.from(this.connections.entries())
      .filter(([_, connection]) => {
        if (brokerId && connection.brokerId !== brokerId) return false;
        
        const brokerConfig = this.brokerConfigs.get(connection.brokerId);
        if (!brokerConfig) return false;
        
        return connection.status === 'connected' && 
               connection.isHealthy && 
               brokerConfig.features.includes(feature as any);
      })
      .map(([connectionId, _]) => connectionId);
  }

  /**
   * Select connection based on load balancing strategy
   */
  private selectConnectionByStrategy(availableConnections: string[]): string | null {
    if (availableConnections.length === 0) return null;

    switch (this.config.loadBalancingStrategy) {
      case 'round-robin':
        return availableConnections[this.metrics.totalRequests % availableConnections.length];
      
      case 'least-connections':
        return availableConnections.reduce((best, connectionId) => {
          const connection = this.connections.get(connectionId);
          const bestConnection = this.connections.get(best);
          
          if (!connection) return best;
          if (!bestConnection) return connectionId;
          
          return connection.requestCount < bestConnection.requestCount ? connectionId : best;
        });
      
      case 'health-based':
      default:
        return availableConnections.reduce((best, connectionId) => {
          const connection = this.connections.get(connectionId);
          const bestConnection = this.connections.get(best);
          
          if (!connection) return best;
          if (!bestConnection) return connectionId;
          
          // Prefer connection with lower latency and fewer failures
          const connectionScore = connection.latency + (connection.failureCount * 1000);
          const bestScore = bestConnection.latency + (bestConnection.failureCount * 1000);
          
          return connectionScore < bestScore ? connectionId : best;
        });
    }
  }

  /**
   * Calculate exponential backoff delay
   */
  private getReconnectDelay(retryCount: number): number {
    const baseDelay = this.config.reconnectDelay || 5000;
    const maxDelay = 60000; // 1 minute max
    const exponentialDelay = baseDelay * Math.pow(2, retryCount);
    return Math.min(exponentialDelay, maxDelay);
  }

  /**
   * Start periodic health checks
   */
  private startHealthChecks(): void {
    this.healthCheckInterval = setInterval(async () => {
      if (this.isShuttingDown) return;

      const healthCheckPromises = Array.from(this.connections.keys()).map(connectionId => 
        this.performHealthCheck(connectionId)
      );

      const results = await Promise.allSettled(healthCheckPromises);
      
      let totalLatency = 0;
      let healthyConnections = 0;
      
      results.forEach((result) => {
        if (result.status === 'fulfilled') {
          const healthCheck = result.value;
          if (healthCheck.healthy) {
            totalLatency += healthCheck.latency;
            healthyConnections++;
          }
        }
      });

      this.metrics.averageLatency = healthyConnections > 0 ? totalLatency / healthyConnections : 0;
      this.metrics.uptime = Date.now() - this.metrics.startTime.getTime();
      
      this.emit('healthCheckComplete', {
        totalConnections: this.connections.size,
        healthyConnections,
        averageLatency: this.metrics.averageLatency
      });
      
    }, this.config.healthCheckInterval);
  }

  /**
   * Start periodic connection cleanup
   */
  private startConnectionCleanup(): void {
    this.connectionCleanupInterval = setInterval(() => {
      if (this.isShuttingDown) return;

      // Clean up connections that have been failed for too long
      const failedConnections = Array.from(this.connections.entries())
        .filter(([_, connection]) => 
          connection.status === 'error' && 
          connection.retryCount >= (this.config.maxRetryAttempts || 5)
        );

      failedConnections.forEach(([connectionId, connection]) => {
        console.log(`🧹 Cleaning up failed connection: ${connectionId}`);
        this.connections.delete(connectionId);
        this.emit('connectionRemoved', { connectionId, brokerId: connection.brokerId });
      });

    }, this.config.cleanupInterval);
  }
}