import { describe, it, expect, beforeEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { TradeModificationService } from '../TradeModificationService';
import {
  TradeModificationRequest,
  ModificationType,
  ModificationStatus,
  Position,
  PositionStatus,
  SynchronizationStatus
} from '@golddaddy/types';

describe('TradeModificationService', () => {
  let service: TradeModificationService;
  let mockAuditService: any;
  let mockPositionManager: any;
  let mockBrokerService: any;

  const mockPosition: Position = {
    id: 'position-123',
    userId: 'user-123',
    accountId: 'account-123',
    strategyId: 'strategy-123',
    goalId: 'goal-123',
    instrument: 'EURUSD',
    side: 'LONG',
    size: new Decimal(10000),
    averageEntryPrice: new Decimal(1.1200),
    currentPrice: new Decimal(1.1250),
    unrealizedPnl: new Decimal(50),
    realizedPnl: new Decimal(0),
    totalPnl: new Decimal(50),
    pnlPercentage: new Decimal(0.45),
    stopLoss: new Decimal(1.1150),
    takeProfit: new Decimal(1.1300),
    status: PositionStatus.OPEN,
    riskMetrics: {
      exposure: new Decimal(11250),
      riskPercentage: new Decimal(2.5),
      maxDrawdown: new Decimal(25),
      currentDrawdown: new Decimal(0),
      volatility: new Decimal(0.8)
    },
    synchronizationStatus: {
      isSynchronized: true,
      lastSyncTime: new Date(),
      pendingSyncOperations: 0,
      syncErrors: []
    } as SynchronizationStatus,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    mockAuditService = {
      createAuditEntry: vi.fn().mockResolvedValue('audit-123'),
      updateAuditEntry: vi.fn().mockResolvedValue(true)
    };

    mockPositionManager = {
      getPosition: vi.fn().mockResolvedValue(mockPosition),
      updatePosition: vi.fn().mockResolvedValue(mockPosition),
      closePosition: vi.fn().mockResolvedValue(true)
    };

    mockBrokerService = {
      modifyStopLoss: vi.fn().mockResolvedValue({ brokerOrderId: 'broker-order-123' }),
      modifyTakeProfit: vi.fn().mockResolvedValue({ brokerOrderId: 'broker-order-124' }),
      increasePosition: vi.fn().mockResolvedValue({ brokerOrderId: 'broker-order-125' }),
      decreasePosition: vi.fn().mockResolvedValue({ brokerOrderId: 'broker-order-126' }),
      closePosition: vi.fn().mockResolvedValue({ brokerOrderId: 'broker-order-127' })
    };

    service = new TradeModificationService(
      mockAuditService,
      mockPositionManager,
      mockBrokerService,
      {
        maxPositionSize: new Decimal(50000),
        maxExposurePercentage: new Decimal(10),
        stopLossMinDistance: new Decimal(20),
        takeProfitMinDistance: new Decimal(30),
        criticalRiskThreshold: new Decimal(80)
      }
    );

    // Clear any pending modifications and reset deduplication service state
    service['pendingModifications'].clear();
    service['rollbackData'].clear();
    
    // Mock the deduplication service to always return no duplicates for tests
    if (service['deduplicationService']) {
      vi.spyOn(service['deduplicationService'], 'checkTradeModificationDuplicate')
        .mockReturnValue({ isDuplicate: false, requestId: 'test-request-id' });
    }
  });

  const createModificationRequest = (
    overrides: Partial<TradeModificationRequest> = {}
  ): TradeModificationRequest => ({
    tradeId: 'position-123',
    modificationType: ModificationType.STOP_LOSS,
    newValue: new Decimal(1.1100),
    reason: 'Risk management adjustment',
    confirmRisk: true,
    ...overrides
  });

  describe('requestModification', () => {
    it('should create a valid stop loss modification request', async () => {
      const request = createModificationRequest();
      
      const modification = await service.requestModification(request, 'user-123');

      expect(modification.tradeId).toBe(request.tradeId);
      expect(modification.modificationType).toBe(ModificationType.STOP_LOSS);
      expect(modification.newValue.toNumber()).toBe(1.1100);
      expect(modification.originalValue.toNumber()).toBe(1.1150); // Mock position's stop loss
      expect(modification.status).toBe(ModificationStatus.PENDING);
      expect(modification.requestedBy).toBe('user-123');
    });

    it('should create a valid take profit modification request', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.TAKE_PROFIT,
        newValue: new Decimal(1.1400)
      });
      
      const modification = await service.requestModification(request, 'user-123');

      expect(modification.modificationType).toBe(ModificationType.TAKE_PROFIT);
      expect(modification.newValue.toNumber()).toBe(1.1400);
      expect(modification.originalValue.toNumber()).toBe(1.1300); // Mock position's take profit
    });

    it('should create a valid position size modification request', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.POSITION_SIZE,
        newValue: new Decimal(15000)
      });
      
      const modification = await service.requestModification(request, 'user-123');

      expect(modification.modificationType).toBe(ModificationType.POSITION_SIZE);
      expect(modification.newValue.toNumber()).toBe(15000);
      expect(modification.originalValue.toNumber()).toBe(10000); // Mock position's size
    });

    it('should create a valid close position request', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.CLOSE_POSITION,
        newValue: new Decimal(0)
      });
      
      const modification = await service.requestModification(request, 'user-123');

      expect(modification.modificationType).toBe(ModificationType.CLOSE_POSITION);
      expect(modification.newValue.toNumber()).toBe(0);
    });

    it('should validate request and throw error for missing trade ID', async () => {
      const request = createModificationRequest({ tradeId: '' });
      
      await expect(service.requestModification(request, 'user-123'))
        .rejects.toThrow('Trade ID is required');
    });

    it('should validate request and throw error for missing reason', async () => {
      const request = createModificationRequest({ reason: '' });
      
      await expect(service.requestModification(request, 'user-123'))
        .rejects.toThrow('Modification reason is required');
    });

    it('should validate request and throw error for missing risk confirmation', async () => {
      const request = createModificationRequest({ confirmRisk: false });
      
      await expect(service.requestModification(request, 'user-123'))
        .rejects.toThrow('Risk confirmation is required');
    });

    it('should throw error when position is not found', async () => {
      mockPositionManager.getPosition.mockResolvedValueOnce(null);
      const request = createModificationRequest();
      
      await expect(service.requestModification(request, 'user-123'))
        .rejects.toThrow('Position not found for trade position-123');
    });

    it('should create audit trail entry for successful request', async () => {
      const request = createModificationRequest();
      
      await service.requestModification(request, 'user-123');

      expect(mockAuditService.createAuditEntry).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'TRADE_MODIFICATION_REQUESTED',
          userId: 'user-123',
          tradeId: 'position-123'
        })
      );
    });

    it('should emit modificationRequested event', async () => {
      const request = createModificationRequest();
      const eventPromise = new Promise((resolve) => {
        service.once('modificationRequested', resolve);
      });
      
      await service.requestModification(request, 'user-123');
      
      const event = await eventPromise;
      expect(event).toHaveProperty('modification');
      expect(event).toHaveProperty('riskValidation');
      expect(event).toHaveProperty('position');
    });
  });

  describe('risk validation', () => {
    it('should pass risk validation for reasonable stop loss adjustment', async () => {
      const request = createModificationRequest({
        newValue: new Decimal(1.1120) // Reasonable stop loss
      });
      
      const modification = await service.requestModification(request, 'user-123');
      expect(modification.status).toBe(ModificationStatus.PENDING);
    });

    it('should warn for stop loss too close to current price', async () => {
      const request = createModificationRequest({
        newValue: new Decimal(1.1248) // Very close to current price (1.1250)
      });
      
      // Should still succeed but with warnings
      const modification = await service.requestModification(request, 'user-123');
      expect(modification.status).toBe(ModificationStatus.PENDING);
    });

    it('should reject position size exceeding maximum limit', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.POSITION_SIZE,
        newValue: new Decimal(100000) // Exceeds maxPositionSize of 50000
      });
      
      await expect(service.requestModification(request, 'user-123'))
        .rejects.toThrow('Risk validation failed');
    });

    it('should reject invalid position size (zero or negative)', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.POSITION_SIZE,
        newValue: new Decimal(0)
      });
      
      await expect(service.requestModification(request, 'user-123'))
        .rejects.toThrow('Risk validation failed');
    });

    it('should calculate exposure correctly for position size changes', async () => {
      // Position size increase within limits
      const request = createModificationRequest({
        modificationType: ModificationType.POSITION_SIZE,
        newValue: new Decimal(20000)
      });
      
      const modification = await service.requestModification(request, 'user-123');
      expect(modification.status).toBe(ModificationStatus.PENDING);
    });
  });

  describe('modification processing', () => {
    it('should process stop loss modification successfully', async () => {
      const request = createModificationRequest();
      
      await service.requestModification(request, 'user-123');
      
      // Wait a bit for async processing
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(mockBrokerService.modifyStopLoss).toHaveBeenCalledWith(
        'position-123',
        new Decimal(1.1100)
      );
      expect(mockPositionManager.updatePosition).toHaveBeenCalledWith(
        'position-123',
        expect.objectContaining({
          stopLoss: new Decimal(1.1100)
        })
      );
    });

    it('should process take profit modification successfully', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.TAKE_PROFIT,
        newValue: new Decimal(1.1400)
      });
      
      await service.requestModification(request, 'user-123');
      
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(mockBrokerService.modifyTakeProfit).toHaveBeenCalledWith(
        'position-123',
        new Decimal(1.1400)
      );
    });

    it('should process position size increase successfully', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.POSITION_SIZE,
        newValue: new Decimal(15000) // Increase from 10000 to 15000
      });
      
      await service.requestModification(request, 'user-123');
      
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(mockBrokerService.increasePosition).toHaveBeenCalledWith(
        'position-123',
        new Decimal(5000) // Difference
      );
    });

    it('should process position size decrease successfully', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.POSITION_SIZE,
        newValue: new Decimal(7000) // Decrease from 10000 to 7000
      });
      
      await service.requestModification(request, 'user-123');
      
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(mockBrokerService.decreasePosition).toHaveBeenCalledWith(
        'position-123',
        new Decimal(3000) // Absolute difference
      );
    });

    it('should process close position successfully', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.CLOSE_POSITION,
        newValue: new Decimal(0)
      });
      
      await service.requestModification(request, 'user-123');
      
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(mockBrokerService.closePosition).toHaveBeenCalledWith('position-123');
      expect(mockPositionManager.closePosition).toHaveBeenCalledWith('position-123');
    });

    it('should handle broker service errors gracefully', async () => {
      const errorMessage = 'Broker connection failed';
      mockBrokerService.modifyStopLoss.mockRejectedValueOnce(new Error(errorMessage));
      
      const eventPromise = new Promise((resolve) => {
        service.once('modificationFailed', resolve);
      });
      
      const request = createModificationRequest();
      await service.requestModification(request, 'user-123');
      
      const event = await eventPromise;
      expect(event).toHaveProperty('error', errorMessage);
    });

    it('should emit modificationApplied event on success', async () => {
      const eventPromise = new Promise((resolve) => {
        service.once('modificationApplied', resolve);
      });
      
      const request = createModificationRequest();
      await service.requestModification(request, 'user-123');
      
      const event = await eventPromise;
      expect(event).toHaveProperty('modification');
      expect(event).toHaveProperty('position');
      expect(event).toHaveProperty('brokerOperations');
    });
  });

  describe('getModificationStatus', () => {
    it('should return modification status for existing modification', async () => {
      const request = createModificationRequest();
      const modification = await service.requestModification(request, 'user-123');
      
      const status = await service.getModificationStatus(modification.id);
      
      expect(status).toBeDefined();
      expect(status!.id).toBe(modification.id);
      expect(status!.status).toBe(ModificationStatus.PENDING);
    });

    it('should return null for non-existent modification', async () => {
      const status = await service.getModificationStatus('non-existent-id');
      
      expect(status).toBeNull();
    });
  });

  describe('getAllModifications', () => {
    beforeEach(async () => {
      // Create test modifications
      await service.requestModification(createModificationRequest({ tradeId: 'trade-1' }), 'user-1');
      await service.requestModification(createModificationRequest({ tradeId: 'trade-2' }), 'user-2');
      await service.requestModification(createModificationRequest({ tradeId: 'trade-1' }), 'user-1');
    });

    it('should return all modifications without filters', async () => {
      const modifications = await service.getAllModifications();
      
      expect(modifications.length).toBe(3);
    });

    it('should filter modifications by trade ID', async () => {
      const modifications = await service.getAllModifications({ tradeId: 'trade-1' });
      
      expect(modifications.length).toBe(2);
      expect(modifications.every(m => m.tradeId === 'trade-1')).toBe(true);
    });

    it('should filter modifications by user ID', async () => {
      const modifications = await service.getAllModifications({ userId: 'user-1' });
      
      expect(modifications.length).toBe(2);
      expect(modifications.every(m => m.requestedBy === 'user-1')).toBe(true);
    });

    it('should filter modifications by status', async () => {
      const modifications = await service.getAllModifications({ status: ModificationStatus.PENDING });
      
      expect(modifications.length).toBe(3);
      expect(modifications.every(m => m.status === ModificationStatus.PENDING)).toBe(true);
    });
  });

  describe('cancelModification', () => {
    it('should cancel pending modification successfully', async () => {
      const request = createModificationRequest();
      const modification = await service.requestModification(request, 'user-123');
      
      const result = await service.cancelModification(modification.id, 'user-123', 'Changed mind');
      
      expect(result).toBe(true);
      
      const status = await service.getModificationStatus(modification.id);
      expect(status!.status).toBe(ModificationStatus.REJECTED);
    });

    it('should throw error when trying to cancel non-existent modification', async () => {
      await expect(service.cancelModification('non-existent', 'user-123', 'test'))
        .rejects.toThrow('Modification non-existent not found');
    });

    it('should emit modificationCancelled event', async () => {
      const request = createModificationRequest();
      const modification = await service.requestModification(request, 'user-123');
      
      const eventPromise = new Promise((resolve) => {
        service.once('modificationCancelled', resolve);
      });
      
      await service.cancelModification(modification.id, 'user-123', 'Changed mind');
      
      const event = await eventPromise;
      expect(event).toHaveProperty('modificationId', modification.id);
      expect(event).toHaveProperty('reason', 'Changed mind');
    });
  });

  describe('rollbackModification', () => {
    it('should rollback applied modification successfully', async () => {
      const request = createModificationRequest();
      const modification = await service.requestModification(request, 'user-123');
      
      // Wait for processing to complete
      await new Promise(resolve => setTimeout(resolve, 50));
      
      const result = await service.rollbackModification(modification.id, 'user-123', 'System error');
      
      expect(result).toBe(true);
      expect(mockAuditService.createAuditEntry).toHaveBeenCalledWith(
        expect.objectContaining({
          action: 'TRADE_MODIFICATION_ROLLED_BACK'
        })
      );
    });

    it('should emit modificationRolledBack event', async () => {
      const request = createModificationRequest();
      const modification = await service.requestModification(request, 'user-123');
      
      await new Promise(resolve => setTimeout(resolve, 50));
      
      const eventPromise = new Promise((resolve) => {
        service.once('modificationRolledBack', resolve);
      });
      
      await service.rollbackModification(modification.id, 'user-123', 'System error');
      
      const event = await eventPromise;
      expect(event).toHaveProperty('modificationId', modification.id);
      expect(event).toHaveProperty('reason', 'System error');
    });

    it('should throw error for non-existent modification', async () => {
      await expect(service.rollbackModification('non-existent', 'user-123', 'test'))
        .rejects.toThrow('Modification non-existent not found');
    });
  });

  describe('getModificationHistory', () => {
    beforeEach(async () => {
      // Create test modifications for same trade
      await service.requestModification(createModificationRequest({ tradeId: 'trade-history' }), 'user-123');
      await service.requestModification(createModificationRequest({ tradeId: 'trade-history' }), 'user-123');
      await service.requestModification(createModificationRequest({ tradeId: 'trade-history' }), 'user-123');
    });

    it('should return modification history for trade', async () => {
      const { modifications, total } = await service.getModificationHistory('trade-history');
      
      expect(modifications.length).toBe(3);
      expect(total).toBe(3);
      expect(modifications.every(m => m.tradeId === 'trade-history')).toBe(true);
    });

    it('should respect limit and offset parameters', async () => {
      const { modifications, total } = await service.getModificationHistory('trade-history', 2, 1);
      
      expect(modifications.length).toBe(2);
      expect(total).toBe(3);
    });

    it('should return empty array for non-existent trade', async () => {
      const { modifications, total } = await service.getModificationHistory('non-existent');
      
      expect(modifications.length).toBe(0);
      expect(total).toBe(0);
    });
  });

  describe('error handling and edge cases', () => {
    it('should categorize network errors correctly', async () => {
      const networkError = new Error('Network timeout');
      networkError.code = 'NETWORK_TIMEOUT';
      
      mockBrokerService.modifyStopLoss.mockRejectedValueOnce(networkError);
      
      const eventPromise = new Promise((resolve) => {
        service.once('modificationFailed', resolve);
      });
      
      const request = createModificationRequest();
      await service.requestModification(request, 'user-123');
      
      const event = await eventPromise;
      expect(event).toHaveProperty('error');
    });

    it('should handle modification frequency limits', async () => {
      // Create multiple modifications rapidly
      const promises = [];
      for (let i = 0; i < 6; i++) {
        promises.push(service.requestModification(createModificationRequest(), 'user-123'));
      }
      
      // The 6th modification should fail due to frequency limit (max 5 per hour)
      const results = await Promise.allSettled(promises);
      const failures = results.filter(r => r.status === 'rejected');
      
      expect(failures.length).toBeGreaterThan(0);
    });

    it('should handle position size modification with no change', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.POSITION_SIZE,
        newValue: new Decimal(10000) // Same as current position size
      });
      
      const eventPromise = new Promise((resolve) => {
        service.once('modificationFailed', resolve);
      });
      
      await service.requestModification(request, 'user-123');
      
      const event = await eventPromise;
      expect(event).toHaveProperty('error');
    });

    it('should validate that position closure cannot be rolled back', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.CLOSE_POSITION,
        newValue: new Decimal(0)
      });
      
      const modification = await service.requestModification(request, 'user-123');
      
      await new Promise(resolve => setTimeout(resolve, 50));
      
      const eventPromise = new Promise((resolve) => {
        service.once('rollbackFailed', resolve);
      });
      
      service.rollbackModification(modification.id, 'user-123', 'Test rollback');
      
      const event = await eventPromise;
      expect(event).toHaveProperty('error');
    });
  });

  describe('financial precision', () => {
    it('should maintain decimal precision in calculations', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.POSITION_SIZE,
        newValue: new Decimal('12345.6789') // High precision decimal
      });
      
      const modification = await service.requestModification(request, 'user-123');
      
      expect(modification.newValue.toString()).toBe('12345.6789');
      expect(modification.newValue).toBeInstanceOf(Decimal);
    });

    it('should calculate exposure with financial precision', async () => {
      const request = createModificationRequest({
        modificationType: ModificationType.POSITION_SIZE,
        newValue: new Decimal('10000.123456')
      });
      
      // Should not throw precision-related errors
      const modification = await service.requestModification(request, 'user-123');
      expect(modification.status).toBe(ModificationStatus.PENDING);
    });

    it('should calculate stop loss distances with precision', async () => {
      const request = createModificationRequest({
        newValue: new Decimal('1.123456789') // High precision price
      });
      
      const modification = await service.requestModification(request, 'user-123');
      expect(modification.newValue.decimalPlaces()).toBeGreaterThan(4);
    });
  });
});