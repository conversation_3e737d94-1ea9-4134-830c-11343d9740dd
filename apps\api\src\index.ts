import 'dotenv/config';
import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { PrismaClient } from '@prisma/client';
import { validateEnvVars } from '@golddaddy/config';
import { ProductionHealthService } from './services/monitoring/ProductionHealthService.js';
import { MonitoringServiceManager } from './services/monitoring/MonitoringServiceManager.js';
import { ManualInterventionService } from './services/intervention/ManualInterventionService.js';
import { setInterventionService } from './routes/intervention.js';

// Validate environment variables on startup
validateEnvVars();

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize Prisma client
const prisma = new PrismaClient();

// Initialize production services
const productionHealthService = new ProductionHealthService(prisma);
const monitoringManager = new MonitoringServiceManager(prisma);
const interventionService = new ManualInterventionService(prisma);

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Enhanced health check endpoint with production monitoring
app.get('/health', async (req, res) => {
  try {
    const systemHealth = await productionHealthService.getSystemHealth();
    
    res.status(systemHealth.status === 'healthy' ? 200 : 503).json({
      status: systemHealth.status,
      timestamp: systemHealth.timestamp.toISOString(),
      service: 'golddaddy-api',
      environment: process.env.NODE_ENV || 'development',
      version: process.env.npm_package_version || '1.0.0',
      uptime: systemHealth.uptime,
      services: systemHealth.services,
      metrics: systemHealth.metrics
    });
  } catch (error) {
    console.error('Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      service: 'golddaddy-api',
      environment: process.env.NODE_ENV || 'development',
      error: error.message
    });
  }
});

// Prometheus metrics endpoint
app.get('/metrics', (req, res) => {
  try {
    const metrics = productionHealthService.getPrometheusMetrics();
    res.set('Content-Type', 'text/plain');
    res.send(metrics);
  } catch (error) {
    console.error('Metrics endpoint error:', error);
    res.status(500).send('# Error retrieving metrics\n');
  }
});

// Import routes
import auditRoutes from './routes/audit.js';
import monitoringRoutes from './routes/monitoring.js';
import interventionRoutes from './routes/intervention.js';
import confidenceRoutes from './routes/confidence/index.js';

// Set up intervention service for routes
setInterventionService(interventionService);

// API routes
app.use('/api/audit', auditRoutes);
app.use('/api/monitoring', monitoringRoutes);
app.use('/api/intervention', interventionRoutes);
app.use('/api/confidence', confidenceRoutes);

app.get('/api/v1', (req, res) => {
  res.json({
    message: 'GoldDaddy API v1',
    documentation: '/api/v1/docs',
    health: '/health',
    endpoints: {
      audit: '/api/audit',
      monitoring: '/api/monitoring',
      intervention: '/api/intervention',
      confidence: '/api/confidence'
    }
  });
});

// Error handler
app.use((err: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal Server Error',
    message: err.message,
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
  });
});

if (require.main === module) {
  const server = app.listen(PORT, async () => {
    console.log(`🚀 API Server running on port ${PORT}`);
    console.log(`📊 Health check: http://localhost:${PORT}/health`);
    console.log(`📈 Metrics: http://localhost:${PORT}/metrics`);
    console.log(`🔧 Environment: ${process.env.NODE_ENV || 'development'}`);
    
    // Start production monitoring in production environment
    if (process.env.NODE_ENV === 'production') {
      try {
        console.log('🏥 Initializing production monitoring...');
        await monitoringManager.initialize();
        await productionHealthService.startHealthMonitoring();
        console.log('✅ Production monitoring initialized');
      } catch (error) {
        console.error('❌ Failed to initialize production monitoring:', error);
      }
    }
  });

  // Graceful shutdown handler
  const gracefulShutdown = async (signal: string) => {
    console.log(`📶 Received ${signal}. Starting graceful shutdown...`);
    
    // Stop accepting new connections
    server.close(async () => {
      console.log('🔌 HTTP server closed');
      
      try {
        // Shutdown monitoring services
        if (process.env.NODE_ENV === 'production') {
          await productionHealthService.shutdown();
          await monitoringManager.shutdown();
        }
        
        // Shutdown intervention service
        await interventionService.shutdown();
        
        // Close database connection
        await prisma.$disconnect();
        console.log('🗄️ Database connection closed');
        
        console.log('✅ Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        console.error('❌ Error during shutdown:', error);
        process.exit(1);
      }
    });

    // Force shutdown after 30 seconds
    setTimeout(() => {
      console.error('⚠️ Forced shutdown after 30s timeout');
      process.exit(1);
    }, 30000);
  };

  // Handle shutdown signals
  process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  process.on('SIGHUP', () => gracefulShutdown('SIGHUP'));

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    console.error('💥 Uncaught Exception:', error);
    gracefulShutdown('UNCAUGHT_EXCEPTION');
  });

  process.on('unhandledRejection', (reason, promise) => {
    console.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown('UNHANDLED_REJECTION');
  });
}

export default app;