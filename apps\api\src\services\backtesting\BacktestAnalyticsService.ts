import { MarketRegime } from '@prisma/client';
import { 
  BacktestResult, 
  BacktestTrade, 
  RegimePerformance
} from './ComprehensiveBacktestService';

/**
 * Performance attribution breakdown
 */
export interface PerformanceAttribution {
  totalReturn: number;
  attributions: {
    marketTiming: number; // Return attributed to entry/exit timing
    marketRegime: number; // Return attributed to favorable market conditions
    riskManagement: number; // Return from stop losses and position sizing
    execution: number; // Return lost to slippage, spreads, and commissions
    alpha: number; // Excess return above market
  };
  marketConditionImpact: {
    trending: number;
    ranging: number;
    volatile: number;
  };
  timeBasedAttribution: {
    hourOfDay: Array<{ hour: number; return: number; tradeCount: number }>;
    dayOfWeek: Array<{ day: number; return: number; tradeCount: number }>;
    monthOfYear: Array<{ month: number; return: number; tradeCount: number }>;
  };
}

/**
 * Risk analysis result
 */
export interface RiskAnalysis {
  valueAtRisk: {
    var_95: number; // 95% VaR
    var_99: number; // 99% VaR
  };
  expectedShortfall: {
    es_95: number; // 95% Expected Shortfall
    es_99: number; // 99% Expected Shortfall
  };
  riskMetrics: {
    maxDrawdown: number;
    averageDrawdown: number;
    drawdownFrequency: number; // Number of drawdown periods per year
    recoveryTime: number; // Average time to recover from drawdown (hours)
    tailRatio: number; // Ratio of positive to negative tail returns
    downside_deviation: number;
    upside_deviation: number;
  };
  riskFactors: {
    marketRisk: number; // Risk from market movements
    executionRisk: number; // Risk from execution costs
    concentrationRisk: number; // Risk from position concentration
    temporalRisk: number; // Risk from timing factors
  };
}

/**
 * Trade distribution analysis
 */
export interface TradeDistributionAnalysis {
  returnDistribution: {
    bins: Array<{ range: string; count: number; percentage: number }>;
    skewness: number;
    kurtosis: number;
    normality_test: { statistic: number; p_value: number };
  };
  holdingPeriodAnalysis: {
    average: number;
    median: number;
    distribution: Array<{ period_hours: number; count: number; avg_return: number }>;
  };
  positionSizeAnalysis: {
    averageSize: number;
    sizeDistribution: Array<{ size_range: string; count: number; avg_return: number }>;
    optimal_size: number; // Kelly criterion or similar
  };
  tradingFrequency: {
    trades_per_day: number;
    trades_per_week: number;
    trading_intensity: number; // Trades per available trading hour
  };
}

/**
 * Benchmark comparison result
 */
export interface BenchmarkComparison {
  benchmark: {
    name: string;
    totalReturn: number;
    volatility: number;
    sharpeRatio: number;
    maxDrawdown: number;
  };
  strategy: {
    totalReturn: number;
    volatility: number;
    sharpeRatio: number;
    maxDrawdown: number;
  };
  comparison: {
    excess_return: number;
    tracking_error: number;
    information_ratio: number;
    alpha: number;
    beta: number;
    correlation: number;
  };
  outperformance: {
    periods_outperformed: number;
    periods_underperformed: number;
    outperformance_percentage: number;
  };
}

/**
 * Comprehensive analytics service for backtesting results
 */
export class BacktestAnalyticsService {
  
  /**
   * Perform comprehensive performance attribution analysis
   */
  analyzePerformanceAttribution(result: BacktestResult): PerformanceAttribution {
    const trades = result.trades;
    
    // Calculate market timing attribution
    const marketTimingReturn = this.calculateMarketTimingAttribution(trades);
    
    // Calculate market regime attribution
    const regimeReturn = this.calculateRegimeAttribution(trades, result.regimeAnalysis);
    
    // Calculate risk management attribution
    const riskMgmtReturn = this.calculateRiskManagementAttribution(trades);
    
    // Calculate execution cost attribution
    const executionReturn = this.calculateExecutionAttribution(trades);
    
    // Calculate alpha (residual return)
    const alpha = result.overallMetrics.totalReturn - marketTimingReturn - regimeReturn - riskMgmtReturn + executionReturn;
    
    // Calculate market condition impact
    const marketConditionImpact = this.calculateMarketConditionImpact(result.regimeAnalysis);
    
    // Calculate time-based attribution
    const timeBasedAttribution = this.calculateTimeBasedAttribution(trades);
    
    return {
      totalReturn: result.overallMetrics.totalReturn,
      attributions: {
        marketTiming: marketTimingReturn,
        marketRegime: regimeReturn,
        riskManagement: riskMgmtReturn,
        execution: executionReturn,
        alpha,
      },
      marketConditionImpact,
      timeBasedAttribution,
    };
  }

  /**
   * Perform comprehensive risk analysis
   */
  analyzeRisk(result: BacktestResult): RiskAnalysis {
    const trades = result.trades;
    const returns = trades.map(t => t.pnlPercentage / 100); // Convert to decimal
    
    // Calculate Value at Risk
    const sortedReturns = [...returns].sort((a, b) => a - b);
    const var_95 = this.calculateVaR(sortedReturns, 0.05);
    const var_99 = this.calculateVaR(sortedReturns, 0.01);
    
    // Calculate Expected Shortfall
    const es_95 = this.calculateExpectedShortfall(sortedReturns, 0.05);
    const es_99 = this.calculateExpectedShortfall(sortedReturns, 0.01);
    
    // Calculate risk metrics
    const riskMetrics = this.calculateRiskMetrics(result);
    
    // Calculate risk factors
    const riskFactors = this.calculateRiskFactors(trades);
    
    return {
      valueAtRisk: { var_95, var_99 },
      expectedShortfall: { es_95, es_99 },
      riskMetrics,
      riskFactors,
    };
  }

  /**
   * Analyze trade distribution patterns
   */
  analyzeTradeDistribution(result: BacktestResult): TradeDistributionAnalysis {
    const trades = result.trades;
    
    // Analyze return distribution
    const returnDistribution = this.analyzeReturnDistribution(trades);
    
    // Analyze holding period patterns
    const holdingPeriodAnalysis = this.analyzeHoldingPeriods(trades);
    
    // Analyze position sizing
    const positionSizeAnalysis = this.analyzePositionSizes(trades);
    
    // Calculate trading frequency
    const tradingFrequency = this.calculateTradingFrequency(trades, result.startDate, result.endDate);
    
    return {
      returnDistribution,
      holdingPeriodAnalysis,
      positionSizeAnalysis,
      tradingFrequency,
    };
  }

  /**
   * Compare strategy performance against benchmark
   */
  async compareToBenchmark(result: BacktestResult, benchmarkData?: number[]): Promise<BenchmarkComparison> {
    // Default benchmark: simple buy and hold return
    const defaultBenchmark = this.calculateBuyAndHoldBenchmark(result);
    
    const benchmark = benchmarkData ? {
      name: 'Custom Benchmark',
      totalReturn: benchmarkData[benchmarkData.length - 1] - benchmarkData[0],
      volatility: this.calculateVolatility(benchmarkData.slice(1).map((val, i) => (val - benchmarkData[i]) / benchmarkData[i])),
      sharpeRatio: 0, // Would need risk-free rate
      maxDrawdown: this.calculateMaxDrawdown(benchmarkData),
    } : defaultBenchmark;
    
    const strategy = {
      totalReturn: result.overallMetrics.totalReturn,
      volatility: result.overallMetrics.volatility,
      sharpeRatio: result.overallMetrics.sharpeRatio,
      maxDrawdown: result.overallMetrics.maxDrawdown,
    };
    
    // Calculate comparison metrics
    const excess_return = strategy.totalReturn - benchmark.totalReturn;
    const tracking_error = Math.sqrt(Math.pow(strategy.volatility, 2) + Math.pow(benchmark.volatility, 2));
    const information_ratio = tracking_error > 0 ? excess_return / tracking_error : 0;
    
    // Beta and alpha would require actual benchmark price series
    const beta = 1.0; // Simplified assumption
    const alpha = excess_return - beta * benchmark.totalReturn;
    const correlation = 0.8; // Simplified assumption
    
    const outperformance = this.calculateOutperformancePeriods(result, benchmark);
    
    return {
      benchmark,
      strategy,
      comparison: {
        excess_return,
        tracking_error,
        information_ratio,
        alpha,
        beta,
        correlation,
      },
      outperformance,
    };
  }

  /**
   * Generate visualization data for charts and graphs
   */
  generateVisualizationData(result: BacktestResult) {
    return {
      equityCurve: result.equityCurve.map(point => ({
        timestamp: point.timestamp.toISOString(),
        equity: point.equity,
        drawdown: point.drawdownPercentage,
      })),
      
      monthlyReturns: this.calculateMonthlyReturns(result.trades),
      
      rollingPerformance: this.calculateRollingMetrics(result.equityCurve, 30), // 30-day rolling
      
      tradeScatter: result.trades.map(trade => ({
        holdingPeriod: trade.holdingPeriod,
        return: trade.pnlPercentage,
        size: Math.abs(trade.quantity),
        regime: trade.marketRegime,
      })),
      
      regimePerformance: result.regimeAnalysis?.map(regime => ({
        regime: regime.regime,
        return: regime.metrics.totalReturnPercentage,
        winRate: regime.metrics.winRate,
        sharpeRatio: regime.metrics.sharpeRatio,
        tradeCount: regime.trades.length,
      })) || [],
      
      drawdownPeriods: result.drawdownAnalysis.drawdownPeriods.map(period => ({
        start: period.start.toISOString(),
        end: period.end.toISOString(),
        drawdown: period.drawdownPercentage,
        duration: period.duration,
      })),
    };
  }

  /**
   * Calculate market timing attribution
   */
  private calculateMarketTimingAttribution(trades: BacktestTrade[]): number {
    // Simplified: attribute return to entry timing quality
    // In practice, would compare to random entry times
    return trades.reduce((sum, trade) => {
      // Assume good timing contributes positively
      const timingContribution = trade.pnl * 0.3; // 30% attributed to timing
      return sum + timingContribution;
    }, 0);
  }

  /**
   * Calculate market regime attribution
   */
  private calculateRegimeAttribution(trades: BacktestTrade[], regimeAnalysis?: RegimePerformance[]): number {
    if (!regimeAnalysis) return 0;
    
    // Calculate excess return from favorable market conditions
    let regimeReturn = 0;
    
    for (const regime of regimeAnalysis) {
      // Assume trending markets provide excess return, ranging markets are neutral
      if (regime.regime === MarketRegime.TRENDING) {
        regimeReturn += regime.metrics.totalReturn * 0.2; // 20% attributed to regime
      } else if (regime.regime === MarketRegime.VOLATILE) {
        regimeReturn += regime.metrics.totalReturn * -0.1; // Volatile markets penalize
      }
    }
    
    return regimeReturn;
  }

  /**
   * Calculate risk management attribution
   */
  private calculateRiskManagementAttribution(trades: BacktestTrade[]): number {
    // Calculate return from stop losses and position sizing
    const stopLossReturn = trades.filter(t => t.stopLoss).reduce((sum, trade) => {
      // Estimate what return would have been without stop loss
      const estimatedLoss = trade.entryPrice * 0.1; // Assume 10% potential loss
      if (trade.pnl > -estimatedLoss) {
        return sum + (estimatedLoss + trade.pnl); // Saved loss
      }
      return sum;
    }, 0);
    
    return stopLossReturn;
  }

  /**
   * Calculate execution attribution (costs)
   */
  private calculateExecutionAttribution(trades: BacktestTrade[]): number {
    return trades.reduce((sum, trade) => {
      const totalExecutionCost = trade.commission + Math.abs(trade.slippage);
      return sum - totalExecutionCost; // Negative contribution
    }, 0);
  }

  /**
   * Calculate market condition impact
   */
  private calculateMarketConditionImpact(regimeAnalysis?: RegimePerformance[]) {
    if (!regimeAnalysis) {
      return { trending: 0, ranging: 0, volatile: 0 };
    }
    
    return {
      trending: regimeAnalysis.find(r => r.regime === MarketRegime.TRENDING)?.metrics.totalReturn || 0,
      ranging: regimeAnalysis.find(r => r.regime === MarketRegime.RANGING)?.metrics.totalReturn || 0,
      volatile: regimeAnalysis.find(r => r.regime === MarketRegime.VOLATILE)?.metrics.totalReturn || 0,
    };
  }

  /**
   * Calculate time-based attribution
   */
  private calculateTimeBasedAttribution(trades: BacktestTrade[]) {
    // Group by hour of day
    const hourlyReturns = new Array(24).fill(0).map((_, hour) => ({
      hour,
      return: 0,
      tradeCount: 0,
    }));
    
    // Group by day of week (0 = Sunday)
    const dailyReturns = new Array(7).fill(0).map((_, day) => ({
      day,
      return: 0,
      tradeCount: 0,
    }));
    
    // Group by month (0 = January)
    const monthlyReturns = new Array(12).fill(0).map((_, month) => ({
      month,
      return: 0,
      tradeCount: 0,
    }));
    
    for (const trade of trades) {
      const entryHour = trade.entryTime.getHours();
      const entryDay = trade.entryTime.getDay();
      const entryMonth = trade.entryTime.getMonth();
      
      hourlyReturns[entryHour].return += trade.pnl;
      hourlyReturns[entryHour].tradeCount += 1;
      
      dailyReturns[entryDay].return += trade.pnl;
      dailyReturns[entryDay].tradeCount += 1;
      
      monthlyReturns[entryMonth].return += trade.pnl;
      monthlyReturns[entryMonth].tradeCount += 1;
    }
    
    return {
      hourOfDay: hourlyReturns,
      dayOfWeek: dailyReturns,
      monthOfYear: monthlyReturns,
    };
  }

  /**
   * Calculate Value at Risk
   */
  private calculateVaR(sortedReturns: number[], alpha: number): number {
    const index = Math.floor(sortedReturns.length * alpha);
    return sortedReturns[index] || 0;
  }

  /**
   * Calculate Expected Shortfall (Conditional VaR)
   */
  private calculateExpectedShortfall(sortedReturns: number[], alpha: number): number {
    const varIndex = Math.floor(sortedReturns.length * alpha);
    const tailReturns = sortedReturns.slice(0, varIndex);
    
    if (tailReturns.length === 0) return 0;
    
    return tailReturns.reduce((sum, ret) => sum + ret, 0) / tailReturns.length;
  }

  /**
   * Calculate comprehensive risk metrics
   */
  private calculateRiskMetrics(result: BacktestResult) {
    const drawdownAnalysis = result.drawdownAnalysis;
    const trades = result.trades;
    
    const returns = trades.map(t => t.pnlPercentage / 100);
    const negativeReturns = returns.filter(ret => ret < 0);
    const positiveReturns = returns.filter(ret => ret > 0);
    
    const averageDrawdown = drawdownAnalysis.averageDrawdown;
    const drawdownFrequency = drawdownAnalysis.drawdownPeriods.length;
    const recoveryTime = drawdownAnalysis.averageDrawdownDuration;
    
    const tailRatio = positiveReturns.length > 0 && negativeReturns.length > 0
      ? (positiveReturns.reduce((sum, ret) => sum + Math.abs(ret), 0) / positiveReturns.length) /
        (negativeReturns.reduce((sum, ret) => sum + Math.abs(ret), 0) / negativeReturns.length)
      : 1;
    
    const downside_deviation = this.calculateDownsideDeviation(returns);
    const upside_deviation = this.calculateUpsideDeviation(returns);
    
    return {
      maxDrawdown: drawdownAnalysis.maxDrawdown,
      averageDrawdown,
      drawdownFrequency,
      recoveryTime,
      tailRatio,
      downside_deviation,
      upside_deviation,
    };
  }

  /**
   * Calculate risk factors
   */
  private calculateRiskFactors(trades: BacktestTrade[]) {
    const totalReturn = trades.reduce((sum, trade) => sum + trade.pnl, 0);
    
    // Market risk - risk from market movements
    const marketRisk = Math.abs(totalReturn * 0.7); // Assume 70% market risk
    
    // Execution risk - risk from execution costs
    const executionRisk = trades.reduce((sum, trade) => 
      sum + Math.abs(trade.commission) + Math.abs(trade.slippage), 0
    );
    
    // Concentration risk - risk from position concentration
    const instrumentGroups = new Set(trades.map(t => t.instrument));
    const concentrationRisk = instrumentGroups.size < 5 ? Math.abs(totalReturn * 0.2) : Math.abs(totalReturn * 0.1);
    
    // Temporal risk - risk from timing factors
    const temporalRisk = Math.abs(totalReturn * 0.1);
    
    return {
      marketRisk,
      executionRisk,
      concentrationRisk,
      temporalRisk,
    };
  }

  /**
   * Analyze return distribution
   */
  private analyzeReturnDistribution(trades: BacktestTrade[]) {
    const returns = trades.map(t => t.pnlPercentage);
    
    // Create histogram bins
    const min = Math.min(...returns);
    const max = Math.max(...returns);
    const binCount = 10;
    const binWidth = (max - min) / binCount;
    
    const bins = Array.from({ length: binCount }, (_, i) => {
      const binStart = min + i * binWidth;
      const binEnd = binStart + binWidth;
      const count = returns.filter(ret => ret >= binStart && ret < binEnd).length;
      
      return {
        range: `${binStart.toFixed(2)}% to ${binEnd.toFixed(2)}%`,
        count,
        percentage: (count / returns.length) * 100,
      };
    });
    
    // Calculate distribution statistics
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    const stdDev = Math.sqrt(variance);
    
    const skewness = returns.reduce((sum, ret) => sum + Math.pow((ret - mean) / stdDev, 3), 0) / returns.length;
    const kurtosis = returns.reduce((sum, ret) => sum + Math.pow((ret - mean) / stdDev, 4), 0) / returns.length - 3;
    
    return {
      bins,
      skewness,
      kurtosis,
      normality_test: { statistic: 0, p_value: 0 }, // Simplified
    };
  }

  /**
   * Analyze holding periods
   */
  private analyzeHoldingPeriods(trades: BacktestTrade[]) {
    const periods = trades.map(t => t.holdingPeriod);
    periods.sort((a, b) => a - b);
    
    const average = periods.reduce((sum, p) => sum + p, 0) / periods.length;
    const median = periods[Math.floor(periods.length / 2)];
    
    // Create distribution buckets
    // const _max = Math.max(...periods); // TODO: Use for dynamic bucket sizing
    const buckets = [
      { period_hours: 1, count: 0, avg_return: 0 },
      { period_hours: 4, count: 0, avg_return: 0 },
      { period_hours: 12, count: 0, avg_return: 0 },
      { period_hours: 24, count: 0, avg_return: 0 },
      { period_hours: 72, count: 0, avg_return: 0 },
      { period_hours: 168, count: 0, avg_return: 0 }, // 1 week
      { period_hours: Infinity, count: 0, avg_return: 0 },
    ];
    
    for (const trade of trades) {
      for (const bucket of buckets) {
        if (trade.holdingPeriod <= bucket.period_hours) {
          bucket.count += 1;
          bucket.avg_return += trade.pnl;
          break;
        }
      }
    }
    
    // Calculate average returns for each bucket
    buckets.forEach(bucket => {
      if (bucket.count > 0) {
        bucket.avg_return /= bucket.count;
      }
    });
    
    return {
      average,
      median,
      distribution: buckets,
    };
  }

  /**
   * Analyze position sizes
   */
  private analyzePositionSizes(trades: BacktestTrade[]) {
    const sizes = trades.map(t => Math.abs(t.quantity));
    const averageSize = sizes.reduce((sum, size) => sum + size, 0) / sizes.length;
    
    // Create size distribution
    const sizeRanges = [
      '0-10k', '10k-50k', '50k-100k', '100k-500k', '500k+'
    ];
    
    const distribution = sizeRanges.map(range => ({
      size_range: range,
      count: 0,
      avg_return: 0,
    }));
    
    for (const trade of trades) {
      const size = Math.abs(trade.quantity);
      let rangeIndex = 0;
      
      if (size > 10000) rangeIndex = 1;
      if (size > 50000) rangeIndex = 2;
      if (size > 100000) rangeIndex = 3;
      if (size > 500000) rangeIndex = 4;
      
      distribution[rangeIndex].count += 1;
      distribution[rangeIndex].avg_return += trade.pnl;
    }
    
    // Calculate average returns
    distribution.forEach(bucket => {
      if (bucket.count > 0) {
        bucket.avg_return /= bucket.count;
      }
    });
    
    // Calculate optimal size using simplified Kelly criterion
    const winRate = trades.filter(t => t.pnl > 0).length / trades.length;
    const avgWin = trades.filter(t => t.pnl > 0).reduce((sum, t) => sum + t.pnl, 0) / trades.filter(t => t.pnl > 0).length;
    const avgLoss = Math.abs(trades.filter(t => t.pnl < 0).reduce((sum, t) => sum + t.pnl, 0) / trades.filter(t => t.pnl < 0).length);
    
    const optimal_size = avgLoss > 0 ? averageSize * ((winRate * avgWin - (1 - winRate) * avgLoss) / avgWin) : averageSize;
    
    return {
      averageSize,
      sizeDistribution: distribution,
      optimal_size: Math.max(0, optimal_size),
    };
  }

  /**
   * Calculate trading frequency metrics
   */
  private calculateTradingFrequency(trades: BacktestTrade[], startDate: Date, endDate: Date) {
    const totalDays = (endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24);
    const totalWeeks = totalDays / 7;
    const totalHours = totalDays * 24;
    
    return {
      trades_per_day: trades.length / totalDays,
      trades_per_week: trades.length / totalWeeks,
      trading_intensity: trades.length / totalHours,
    };
  }

  /**
   * Calculate simple buy and hold benchmark
   */
  private calculateBuyAndHoldBenchmark(result: BacktestResult) {
    // Simplified benchmark calculation
    const totalDays = (result.endDate.getTime() - result.startDate.getTime()) / (1000 * 60 * 60 * 24);
    const annualizedMarketReturn = 0.08; // 8% annual market return assumption
    const benchmarkReturn = (annualizedMarketReturn * totalDays) / 365;
    
    return {
      name: 'Buy & Hold',
      totalReturn: result.initialCapital * benchmarkReturn,
      volatility: 15, // 15% annual volatility assumption
      sharpeRatio: (annualizedMarketReturn * 100) / 15,
      maxDrawdown: result.initialCapital * 0.2, // 20% max drawdown assumption
    };
  }

  /**
   * Calculate outperformance periods
   */
  private calculateOutperformancePeriods(result: BacktestResult, benchmark: any) {
    // Simplified calculation
    const outperformed = result.overallMetrics.totalReturn > benchmark.totalReturn ? 1 : 0;
    
    return {
      periods_outperformed: outperformed,
      periods_underperformed: 1 - outperformed,
      outperformance_percentage: outperformed * 100,
    };
  }

  /**
   * Calculate monthly returns
   */
  private calculateMonthlyReturns(trades: BacktestTrade[]) {
    const monthlyMap = new Map<string, number>();
    
    for (const trade of trades) {
      const monthKey = `${trade.exitTime.getFullYear()}-${String(trade.exitTime.getMonth() + 1).padStart(2, '0')}`;
      monthlyMap.set(monthKey, (monthlyMap.get(monthKey) || 0) + trade.pnl);
    }
    
    return Array.from(monthlyMap.entries()).map(([month, return_value]) => ({
      month,
      return: return_value,
    }));
  }

  /**
   * Calculate rolling performance metrics
   */
  private calculateRollingMetrics(equityCurve: any[], windowDays: number) {
    const windowHours = windowDays * 24;
    const rollingMetrics: any[] = [];
    
    for (let i = windowHours; i < equityCurve.length; i++) {
      const window = equityCurve.slice(i - windowHours, i);
      const startEquity = window[0].equity;
      const endEquity = window[window.length - 1].equity;
      const rollingReturn = ((endEquity - startEquity) / startEquity) * 100;
      
      rollingMetrics.push({
        date: window[window.length - 1].timestamp,
        rolling_return: rollingReturn,
      });
    }
    
    return rollingMetrics;
  }

  /**
   * Calculate downside deviation
   */
  private calculateDownsideDeviation(returns: number[]): number {
    const negativeReturns = returns.filter(ret => ret < 0);
    if (negativeReturns.length === 0) return 0;
    
    const variance = negativeReturns.reduce((sum, ret) => sum + ret * ret, 0) / negativeReturns.length;
    return Math.sqrt(variance) * 100; // Convert to percentage
  }

  /**
   * Calculate upside deviation
   */
  private calculateUpsideDeviation(returns: number[]): number {
    const positiveReturns = returns.filter(ret => ret > 0);
    if (positiveReturns.length === 0) return 0;
    
    const variance = positiveReturns.reduce((sum, ret) => sum + ret * ret, 0) / positiveReturns.length;
    return Math.sqrt(variance) * 100; // Convert to percentage
  }

  /**
   * Calculate volatility
   */
  private calculateVolatility(returns: number[]): number {
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    return Math.sqrt(variance) * Math.sqrt(252) * 100; // Annualized volatility percentage
  }

  /**
   * Calculate maximum drawdown from price series
   */
  private calculateMaxDrawdown(prices: number[]): number {
    let maxDrawdown = 0;
    let peak = prices[0];
    
    for (const price of prices) {
      if (price > peak) {
        peak = price;
      }
      
      const drawdown = (peak - price) / peak;
      maxDrawdown = Math.max(maxDrawdown, drawdown);
    }
    
    return maxDrawdown * 100; // Convert to percentage
  }
}