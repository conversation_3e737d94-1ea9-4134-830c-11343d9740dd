import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@golddaddy/types';
import { 
  ProgressionMilestone, 
  MilestoneWithProgress,
  MilestoneCategoryGroup,
  MilestoneType,
  ProgressionMilestoneCategory,
  MilestoneDifficulty,
  RewardType
} from '@golddaddy/types/confidence';

// Mock milestone data organized by categories
const milestoneCategories: MilestoneCategoryGroup[] = [
  {
    id: 'knowledge',
    title: 'Trading Knowledge',
    description: 'Master the fundamentals of trading',
    icon: '🎓',
    milestones: [
      {
        id: 'trading-basics',
        title: 'Trading Fundamentals',
        description: 'Learn the core concepts of trading',
        category: 'knowledge',
        difficulty: 'easy',
        estimatedTimeToComplete: 3,
        requirements: [
          'Complete introduction modules',
          'Pass basic trading quiz',
          'Review market structure concepts'
        ],
        rewards: [
          {
            type: 'feature_unlock',
            title: 'Strategy Library Access',
            description: 'Unlock access to beginner trading strategies'
          }
        ],
        prerequisites: [],
        status: 'completed',
        progress: 100,
        completedAt: new Date('2025-01-10'),
      },
      {
        id: 'risk-management',
        title: 'Risk Management Master',
        description: 'Master position sizing and risk control',
        category: 'knowledge',
        difficulty: 'medium',
        estimatedTimeToComplete: 5,
        requirements: [
          'Complete risk management modules',
          'Pass advanced risk quiz with 80%+',
          'Calculate position sizes correctly'
        ],
        rewards: [
          {
            type: 'recognition',
            title: 'Risk Management Badge',
            description: 'Certified risk management practitioner'
          }
        ],
        prerequisites: ['trading-basics'],
        status: 'in_progress',
        progress: 60,
        completedAt: null,
      },
    ],
  },
  {
    id: 'skills',
    title: 'Trading Skills',
    description: 'Develop practical trading abilities',
    icon: '🎯',
    milestones: [
      {
        id: 'chart-analysis',
        title: 'Chart Analysis Pro',
        description: 'Master technical analysis and chart reading',
        category: 'skills',
        difficulty: 'medium',
        estimatedTimeToComplete: 7,
        requirements: [
          'Identify 10 chart patterns correctly',
          'Complete technical analysis course',
          'Pass pattern recognition test'
        ],
        rewards: [
          {
            type: 'feature_unlock',
            title: 'Advanced Charting Tools',
            description: 'Access professional charting features'
          }
        ],
        prerequisites: ['trading-basics'],
        status: 'available',
        progress: 0,
        completedAt: null,
      },
      {
        id: 'strategy-development',
        title: 'Strategy Developer',
        description: 'Create and optimize trading strategies',
        category: 'skills',
        difficulty: 'hard',
        estimatedTimeToComplete: 14,
        requirements: [
          'Develop 3 profitable strategies',
          'Complete backtesting certification',
          'Demonstrate strategy optimization skills'
        ],
        rewards: [
          {
            type: 'strategy_access',
            title: 'Strategy Builder Access',
            description: 'Use advanced strategy development tools'
          }
        ],
        prerequisites: ['chart-analysis', 'risk-management'],
        status: 'locked',
        progress: 0,
        completedAt: null,
      },
    ],
  },
  {
    id: 'achievements',
    title: 'Trading Achievements',
    description: 'Prove your trading prowess',
    icon: '🏆',
    milestones: [
      {
        id: 'first-profit',
        title: 'First Profitable Trade',
        description: 'Execute your first winning trade',
        category: 'achievements',
        difficulty: 'easy',
        estimatedTimeToComplete: 1,
        requirements: [
          'Complete a profitable paper trade',
          'Document trade rationale',
          'Follow risk management rules'
        ],
        rewards: [
          {
            type: 'recognition',
            title: 'First Win Badge',
            description: 'Celebrate your first trading success'
          }
        ],
        prerequisites: ['trading-basics'],
        status: 'available',
        progress: 0,
        completedAt: null,
      },
      {
        id: 'consistent-profits',
        title: 'Consistent Trader',
        description: 'Achieve consistent profitability',
        category: 'achievements',
        difficulty: 'hard',
        estimatedTimeToComplete: 30,
        requirements: [
          'Maintain 60%+ win rate for 30 days',
          'Positive P&L for 4 consecutive weeks',
          'Zero violation of risk rules'
        ],
        rewards: [
          {
            type: 'feature_unlock',
            title: 'Live Trading Access',
            description: 'Qualify for live trading platform'
          }
        ],
        prerequisites: ['first-profit', 'risk-management'],
        status: 'locked',
        progress: 0,
        completedAt: null,
      },
    ],
  },
];

export async function GET(
  request: NextRequest
): Promise<NextResponse<ApiResponse<MilestoneCategoryGroup[]>>> {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    const categoryFilter = searchParams.get('category');
    const statusFilter = searchParams.get('status');

    // In production: fetch user-specific milestone progress from Supabase
    let categories = milestoneCategories;

    // Apply category filter
    if (categoryFilter) {
      categories = categories.filter(cat => cat.id === categoryFilter);
    }

    // Apply status filter to milestones within categories
    if (statusFilter) {
      categories = categories.map(cat => ({
        ...cat,
        milestones: cat.milestones.filter(milestone => milestone.status === statusFilter)
      }));
    }

    // Calculate category progress
    const categoriesWithProgress = categories.map(category => {
      const totalMilestones = category.milestones.length;
      const completedMilestones = category.milestones.filter(m => m.status === 'completed').length;
      const totalProgress = category.milestones.reduce((sum, m) => sum + m.progress, 0);
      
      return {
        ...category,
        progress: totalMilestones > 0 ? Math.round(totalProgress / totalMilestones) : 0,
        completedCount: completedMilestones,
        totalCount: totalMilestones,
      };
    });

    return NextResponse.json({
      success: true,
      data: categoriesWithProgress,
      message: 'Milestones retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching milestones:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch milestones',
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest
): Promise<NextResponse<ApiResponse<{ milestone: ProgressionMilestone; unlocked?: ProgressionMilestone[] }>>> {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';

    if (!body.milestoneId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Milestone ID is required',
        },
        { status: 400 }
      );
    }

    // Find the milestone to complete
    let targetMilestone: ProgressionMilestone | null = null;
    let targetCategory: MilestoneCategory | null = null;

    for (const category of milestoneCategories) {
      const milestone = category.milestones.find(m => m.id === body.milestoneId);
      if (milestone) {
        targetMilestone = milestone;
        targetCategory = category;
        break;
      }
    }

    if (!targetMilestone || !targetCategory) {
      return NextResponse.json(
        {
          success: false,
          error: 'Milestone not found',
        },
        { status: 404 }
      );
    }

    // Check if milestone is available for completion
    if (targetMilestone.status === 'completed') {
      return NextResponse.json(
        {
          success: false,
          error: 'Milestone already completed',
        },
        { status: 400 }
      );
    }

    if (targetMilestone.status === 'locked') {
      return NextResponse.json(
        {
          success: false,
          error: 'Milestone is locked. Complete prerequisites first.',
        },
        { status: 400 }
      );
    }

    // Mark milestone as completed
    targetMilestone.status = 'completed';
    targetMilestone.progress = 100;
    targetMilestone.completedAt = new Date();

    // Check for newly unlocked milestones
    const newlyUnlocked: ProgressionMilestone[] = [];
    
    for (const category of milestoneCategories) {
      for (const milestone of category.milestones) {
        if (milestone.status === 'locked' && milestone.prerequisites.includes(targetMilestone.id)) {
          // Check if all prerequisites are now completed
          const allPrerequisitesMet = milestone.prerequisites.every(prereqId => {
            for (const cat of milestoneCategories) {
              const prereq = cat.milestones.find(m => m.id === prereqId);
              return prereq?.status === 'completed';
            }
            return false;
          });

          if (allPrerequisitesMet) {
            milestone.status = 'available';
            newlyUnlocked.push(milestone);
          }
        }
      }
    }

    // In production: update milestone status in Supabase
    // Also trigger any associated rewards or notifications

    return NextResponse.json({
      success: true,
      data: {
        milestone: targetMilestone,
        unlocked: newlyUnlocked.length > 0 ? newlyUnlocked : undefined,
      },
      message: `Congratulations! You've completed "${targetMilestone.title}"${
        newlyUnlocked.length > 0 ? ` and unlocked ${newlyUnlocked.length} new milestone(s)` : ''
      }!`,
    });
  } catch (error) {
    console.error('Error completing milestone:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to complete milestone',
      },
      { status: 500 }
    );
  }
}