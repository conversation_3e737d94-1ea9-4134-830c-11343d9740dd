import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { PaperTradingEngine } from '../PaperTradingEngine';
import { VirtualPortfolioService } from '../VirtualPortfolioService';
import { PaperTradingAnalyticsService } from '../PaperTradingAnalyticsService';
import { RedisService } from '../../cache/RedisService';
import { TradeRequest, TradeType, TradeSide } from '@golddaddy/types';
import Decimal from 'decimal.js';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

describe('Paper Trading Security Tests', () => {
  let prisma: PrismaClient;
  let redisService: RedisService;
  let paperTradingEngine: PaperTradingEngine;
  let portfolioService: VirtualPortfolioService;
  let analyticsService: PaperTradingAnalyticsService;
  
  let testUser1Id: string;
  let testUser2Id: string;
  let testUser3Id: string; // Admin user
  let testSession1Id: string;
  let testSession2Id: string;

  beforeAll(async () => {
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_TEST_URL || 'postgresql://postgres:postgres@localhost:5432/golddaddy_security_test'
        }
      }
    });

    redisService = new RedisService({
      url: process.env.REDIS_TEST_URL || 'redis://localhost:6379/3'
    });

    portfolioService = new VirtualPortfolioService(prisma, redisService);
    analyticsService = new PaperTradingAnalyticsService(prisma, redisService);
    paperTradingEngine = new PaperTradingEngine(prisma, redisService, portfolioService);

    // Create test users with different roles
    const user1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        hashedPassword: 'secure-hash-1',
        profile: {
          create: {
            firstName: 'Security',
            lastName: 'User1',
            timezone: 'UTC',
            experienceLevel: 'INTERMEDIATE',
            riskTolerance: 'MODERATE'
          }
        }
      }
    });

    const user2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        hashedPassword: 'secure-hash-2',
        profile: {
          create: {
            firstName: 'Security',
            lastName: 'User2',
            timezone: 'UTC',
            experienceLevel: 'BEGINNER',
            riskTolerance: 'CONSERVATIVE'
          }
        }
      }
    });

    const user3 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        hashedPassword: 'admin-secure-hash',
        role: 'ADMIN',
        profile: {
          create: {
            firstName: 'Admin',
            lastName: 'User',
            timezone: 'UTC',
            experienceLevel: 'EXPERT',
            riskTolerance: 'AGGRESSIVE'
          }
        }
      }
    });

    testUser1Id = user1.id;
    testUser2Id = user2.id;
    testUser3Id = user3.id;
  });

  afterAll(async () => {
    // Cleanup test data
    await prisma.paperTrade.deleteMany({
      where: { 
        sessionId: { in: [testSession1Id, testSession2Id].filter(Boolean) }
      }
    });
    await prisma.paperTradingSession.deleteMany({
      where: { userId: { in: [testUser1Id, testUser2Id, testUser3Id] } }
    });
    await prisma.user.deleteMany({
      where: { id: { in: [testUser1Id, testUser2Id, testUser3Id] } }
    });
    
    await prisma.$disconnect();
    await redisService.disconnect();
  });

  beforeEach(async () => {
    await redisService.flushAll();
  });

  afterEach(async () => {
    // Cleanup session data after each test
    if (testSession1Id) {
      await prisma.paperTrade.deleteMany({
        where: { sessionId: testSession1Id }
      });
      await prisma.paperTradingSession.deleteMany({
        where: { id: testSession1Id }
      });
      testSession1Id = '';
    }
    if (testSession2Id) {
      await prisma.paperTrade.deleteMany({
        where: { sessionId: testSession2Id }
      });
      await prisma.paperTradingSession.deleteMany({
        where: { id: testSession2Id }
      });
      testSession2Id = '';
    }
  });

  describe('User Data Isolation', () => {
    it('should prevent users from accessing other users\' sessions', async () => {
      // Create sessions for both users
      const session1 = await paperTradingEngine.createSession(testUser1Id, {
        name: 'User 1 Private Session',
        initialBalance: new Decimal(100000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.1, maxDrawdown: 0.1, requireStopLoss: false } }
      });

      const session2 = await paperTradingEngine.createSession(testUser2Id, {
        name: 'User 2 Private Session',
        initialBalance: new Decimal(50000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.2, maxDrawdown: 0.2, requireStopLoss: false } }
      });

      testSession1Id = session1.id;
      testSession2Id = session2.id;

      // User 1 should not be able to access User 2's session
      await expect(
        portfolioService.getPortfolio(testSession2Id, testUser1Id)
      ).rejects.toThrow(/not found|access denied|unauthorized/i);

      // User 2 should not be able to access User 1's session
      await expect(
        portfolioService.getPortfolio(testSession1Id, testUser2Id)
      ).rejects.toThrow(/not found|access denied|unauthorized/i);

      // Users should be able to access their own sessions
      const user1Portfolio = await portfolioService.getPortfolio(testSession1Id, testUser1Id);
      const user2Portfolio = await portfolioService.getPortfolio(testSession2Id, testUser2Id);

      expect(user1Portfolio.sessionId).toBe(testSession1Id);
      expect(user2Portfolio.sessionId).toBe(testSession2Id);
      expect(user1Portfolio.balance).toEqual(new Decimal(100000));
      expect(user2Portfolio.balance).toEqual(new Decimal(50000));
    });

    it('should prevent users from executing trades in other users\' sessions', async () => {
      const session1 = await paperTradingEngine.createSession(testUser1Id, {
        name: 'Protected Session',
        initialBalance: new Decimal(100000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.5, maxDrawdown: 0.3, requireStopLoss: false } }
      });

      testSession1Id = session1.id;

      const tradeRequest: TradeRequest = {
        symbol: 'EURUSD',
        type: TradeType.MARKET,
        side: TradeSide.BUY,
        quantity: new Decimal(10000),
        price: undefined,
        stopLoss: undefined,
        takeProfit: undefined,
        metadata: {}
      };

      // User 2 should not be able to execute trades in User 1's session
      await expect(
        paperTradingEngine.executePaperTrade(testUser2Id, testSession1Id, tradeRequest)
      ).rejects.toThrow(/not found|access denied|unauthorized/i);

      // User 1 should be able to execute trades in their own session
      const trade = await paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, tradeRequest);
      expect(trade.status).toBe('FILLED');
    });

    it('should prevent users from accessing other users\' analytics', async () => {
      const session1 = await paperTradingEngine.createSession(testUser1Id, {
        name: 'Analytics Security Test',
        initialBalance: new Decimal(100000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.3, maxDrawdown: 0.2, requireStopLoss: false } }
      });

      testSession1Id = session1.id;

      // Execute some trades to create analytics data
      await paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
        symbol: 'EURUSD',
        type: TradeType.MARKET,
        side: TradeSide.BUY,
        quantity: new Decimal(5000),
        price: undefined,
        stopLoss: undefined,
        takeProfit: undefined,
        metadata: {}
      });

      // User 2 should not be able to access User 1's analytics
      await expect(
        analyticsService.generateAnalytics(testUser2Id, testSession1Id)
      ).rejects.toThrow(/not found|access denied|unauthorized/i);

      // User 1 should be able to access their own analytics
      const analytics = await analyticsService.generateAnalytics(testUser1Id, testSession1Id);
      expect(analytics.totalTrades).toBeGreaterThan(0);
    });
  });

  describe('Input Validation and Sanitization', () => {
    it('should prevent SQL injection in session creation', async () => {
      const maliciousName = "'; DROP TABLE users; --";
      
      const session = await paperTradingEngine.createSession(testUser1Id, {
        name: maliciousName,
        initialBalance: new Decimal(50000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.1, maxDrawdown: 0.1, requireStopLoss: false } }
      });

      testSession1Id = session.id;

      // Session should be created with sanitized name, users table should still exist
      expect(session.name).toBe(maliciousName); // Stored as-is but safely handled
      
      // Verify users table still exists by querying it
      const userCount = await prisma.user.count();
      expect(userCount).toBeGreaterThan(0);
    });

    it('should validate trade request parameters', async () => {
      const session = await paperTradingEngine.createSession(testUser1Id, {
        name: 'Validation Test Session',
        initialBalance: new Decimal(100000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.5, maxDrawdown: 0.3, requireStopLoss: false } }
      });

      testSession1Id = session.id;

      // Test invalid symbol
      await expect(
        paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
          symbol: '<script>alert("xss")</script>',
          type: TradeType.MARKET,
          side: TradeSide.BUY,
          quantity: new Decimal(1000),
          price: undefined,
          stopLoss: undefined,
          takeProfit: undefined,
          metadata: {}
        })
      ).rejects.toThrow(/invalid.*symbol/i);

      // Test negative quantity
      await expect(
        paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
          symbol: 'EURUSD',
          type: TradeType.MARKET,
          side: TradeSide.BUY,
          quantity: new Decimal(-1000),
          price: undefined,
          stopLoss: undefined,
          takeProfit: undefined,
          metadata: {}
        })
      ).rejects.toThrow(/quantity.*positive/i);

      // Test extremely large quantity
      await expect(
        paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
          symbol: 'EURUSD',
          type: TradeType.MARKET,
          side: TradeSide.BUY,
          quantity: new Decimal('999999999999999'),
          price: undefined,
          stopLoss: undefined,
          takeProfit: undefined,
          metadata: {}
        })
      ).rejects.toThrow(/quantity.*large|position.*size/i);
    });

    it('should sanitize metadata and prevent XSS', async () => {
      const session = await paperTradingEngine.createSession(testUser1Id, {
        name: 'XSS Prevention Test',
        initialBalance: new Decimal(100000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.5, maxDrawdown: 0.3, requireStopLoss: false } }
      });

      testSession1Id = session.id;

      const maliciousMetadata = {
        note: '<script>alert("xss")</script>',
        strategy: 'javascript:void(0)',
        tags: ['<img src=x onerror=alert("xss")>', 'normal-tag']
      };

      const trade = await paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
        symbol: 'EURUSD',
        type: TradeType.MARKET,
        side: TradeSide.BUY,
        quantity: new Decimal(1000),
        price: undefined,
        stopLoss: undefined,
        takeProfit: undefined,
        metadata: maliciousMetadata
      });

      // Verify trade was created but malicious content is neutralized
      expect(trade.status).toBe('FILLED');
      
      // Check that metadata doesn't contain executable scripts
      const storedTrade = await prisma.paperTrade.findUnique({
        where: { id: trade.id! }
      });

      expect(JSON.stringify(storedTrade?.metadata)).not.toContain('<script>');
      expect(JSON.stringify(storedTrade?.metadata)).not.toContain('javascript:');
    });
  });

  describe('Rate Limiting and DoS Protection', () => {
    it('should handle rapid fire requests without system degradation', async () => {
      const session = await paperTradingEngine.createSession(testUser1Id, {
        name: 'Rate Limit Test Session',
        initialBalance: new Decimal(1000000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 1.0, maxDrawdown: 0.5, requireStopLoss: false } }
      });

      testSession1Id = session.id;

      // Simulate rapid-fire requests
      const rapidRequests = Array.from({ length: 100 }, (_, i) => 
        paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
          symbol: 'EURUSD',
          type: TradeType.MARKET,
          side: i % 2 === 0 ? TradeSide.BUY : TradeSide.SELL,
          quantity: new Decimal(100),
          price: undefined,
          stopLoss: undefined,
          takeProfit: undefined,
          metadata: { requestId: i }
        }).catch(error => ({ error: error.message, requestId: i }))
      );

      const results = await Promise.all(rapidRequests);
      
      // System should handle the load gracefully
      const successfulTrades = results.filter(r => !('error' in r));
      const errors = results.filter(r => 'error' in r);

      // At least some trades should succeed
      expect(successfulTrades.length).toBeGreaterThan(0);
      
      // If there are errors, they should be rate limit errors, not system crashes
      errors.forEach(error => {
        expect(error.error).toMatch(/rate.*limit|too.*many.*requests|throttle/i);
      });
    });

    it('should prevent resource exhaustion through large data requests', async () => {
      const session = await paperTradingEngine.createSession(testUser1Id, {
        name: 'Resource Protection Test',
        initialBalance: new Decimal(100000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.3, maxDrawdown: 0.2, requireStopLoss: false } }
      });

      testSession1Id = session.id;

      // Create some trade history
      await Promise.all(
        Array.from({ length: 50 }, (_, i) => 
          paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
            symbol: 'EURUSD',
            type: TradeType.MARKET,
            side: TradeSide.BUY,
            quantity: new Decimal(1000),
            price: undefined,
            stopLoss: undefined,
            takeProfit: undefined,
            metadata: {}
          })
        )
      );

      // Request should complete within reasonable time even with substantial data
      const startTime = Date.now();
      const analytics = await analyticsService.generateAnalytics(testUser1Id, testSession1Id);
      const duration = Date.now() - startTime;

      expect(duration).toBeLessThan(10000); // Should complete within 10 seconds
      expect(analytics.totalTrades).toBe(50);
    });
  });

  describe('Session Security', () => {
    it('should validate session ownership on all operations', async () => {
      const session = await paperTradingEngine.createSession(testUser1Id, {
        name: 'Ownership Validation Test',
        initialBalance: new Decimal(100000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.3, maxDrawdown: 0.2, requireStopLoss: false } }
      });

      testSession1Id = session.id;

      // Execute a trade as the owner
      const trade = await paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
        symbol: 'EURUSD',
        type: TradeType.MARKET,
        side: TradeSide.BUY,
        quantity: new Decimal(1000),
        price: undefined,
        stopLoss: undefined,
        takeProfit: undefined,
        metadata: {}
      });

      // User 2 should not be able to close User 1's position
      await expect(
        paperTradingEngine.closePosition(testUser2Id, testSession1Id, trade.id!)
      ).rejects.toThrow(/not found|access denied|unauthorized/i);

      // User 1 should be able to close their own position
      const closedTrade = await paperTradingEngine.closePosition(testUser1Id, testSession1Id, trade.id!);
      expect(closedTrade.status).toBe('CLOSED');
    });

    it('should handle session state tampering attempts', async () => {
      const session = await paperTradingEngine.createSession(testUser1Id, {
        name: 'State Tampering Test',
        initialBalance: new Decimal(10000), // Small balance
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.1, maxDrawdown: 0.1, requireStopLoss: false } }
      });

      testSession1Id = session.id;

      // Attempt to directly modify session balance in database
      await prisma.paperTradingSession.update({
        where: { id: testSession1Id },
        data: { balance: new Decimal(1000000) } // Artificially increase balance
      });

      // System should detect the inconsistency and prevent exploitation
      const portfolio = await portfolioService.getPortfolio(testSession1Id, testUser1Id);
      
      // The portfolio service should recalculate balance from actual trades
      // Rather than trusting the database value directly
      expect(portfolio.balance).toEqual(new Decimal(10000));
    });

    it('should prevent concurrent session manipulation', async () => {
      const session = await paperTradingEngine.createSession(testUser1Id, {
        name: 'Concurrent Manipulation Test',
        initialBalance: new Decimal(50000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.5, maxDrawdown: 0.3, requireStopLoss: false } }
      });

      testSession1Id = session.id;

      // Attempt concurrent operations that could cause race conditions
      const operations = [
        paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
          symbol: 'EURUSD',
          type: TradeType.MARKET,
          side: TradeSide.BUY,
          quantity: new Decimal(10000),
          price: undefined,
          stopLoss: undefined,
          takeProfit: undefined,
          metadata: {}
        }),
        portfolioService.getPortfolio(testSession1Id, testUser1Id),
        portfolioService.getPortfolio(testSession1Id, testUser1Id),
        paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
          symbol: 'GBPUSD',
          type: TradeType.MARKET,
          side: TradeSide.SELL,
          quantity: new Decimal(5000),
          price: undefined,
          stopLoss: undefined,
          takeProfit: undefined,
          metadata: {}
        })
      ];

      const results = await Promise.all(operations);
      
      // All operations should complete successfully
      expect(results[0].status).toBe('FILLED'); // First trade
      expect(results[1].sessionId).toBe(testSession1Id); // First portfolio read
      expect(results[2].sessionId).toBe(testSession1Id); // Second portfolio read
      expect(results[3].status).toBe('FILLED'); // Second trade

      // Final state should be consistent
      const finalPortfolio = await portfolioService.getPortfolio(testSession1Id, testUser1Id);
      expect(finalPortfolio.positions.length).toBe(2);
    });
  });

  describe('Data Encryption and Privacy', () => {
    it('should not expose sensitive data in error messages', async () => {
      try {
        // Attempt to access non-existent session
        await portfolioService.getPortfolio('non-existent-session-id', testUser1Id);
      } catch (error: any) {
        // Error message should not expose internal system details
        expect(error.message).not.toContain('SELECT');
        expect(error.message).not.toContain('FROM');
        expect(error.message).not.toContain('WHERE');
        expect(error.message).not.toContain(testUser1Id);
        expect(error.message).not.toContain('database');
        expect(error.message).not.toContain('prisma');
      }

      try {
        // Attempt unauthorized trade execution
        await paperTradingEngine.executePaperTrade(testUser2Id, 'invalid-session', {
          symbol: 'EURUSD',
          type: TradeType.MARKET,
          side: TradeSide.BUY,
          quantity: new Decimal(1000),
          price: undefined,
          stopLoss: undefined,
          takeProfit: undefined,
          metadata: {}
        });
      } catch (error: any) {
        // Error should be generic and not expose system internals
        expect(error.message).toMatch(/not found|access denied|unauthorized/i);
        expect(error.message).not.toContain('JOIN');
        expect(error.message).not.toContain('prisma');
      }
    });

    it('should handle PII data appropriately', async () => {
      const session = await paperTradingEngine.createSession(testUser1Id, {
        name: 'PII Test Session',
        initialBalance: new Decimal(50000),
        settings: { 
          enableSlippage: false, 
          enableSpread: false, 
          riskManagement: { maxPositionSize: 0.3, maxDrawdown: 0.2, requireStopLoss: false },
          personalNotes: 'Contains sensitive personal information about trading strategy'
        }
      });

      testSession1Id = session.id;

      // Execute trade with personal metadata
      await paperTradingEngine.executePaperTrade(testUser1Id, testSession1Id, {
        symbol: 'EURUSD',
        type: TradeType.MARKET,
        side: TradeSide.BUY,
        quantity: new Decimal(1000),
        price: undefined,
        stopLoss: undefined,
        takeProfit: undefined,
        metadata: {
          personalNote: 'Trading based on my salary expectations',
          ipAddress: '*************' // Potentially sensitive
        }
      });

      const analytics = await analyticsService.generateAnalytics(testUser1Id, testSession1Id);
      
      // Analytics should not expose raw personal data
      expect(JSON.stringify(analytics)).not.toContain('salary');
      expect(JSON.stringify(analytics)).not.toContain('*************');
      
      // But should contain aggregated trading data
      expect(analytics.totalTrades).toBe(1);
      expect(analytics.performanceMetrics).toBeDefined();
    });
  });

  describe('Admin Access Controls', () => {
    it('should allow admin users to access system-wide data', async () => {
      // Create sessions for regular users
      const session1 = await paperTradingEngine.createSession(testUser1Id, {
        name: 'User 1 Session for Admin Test',
        initialBalance: new Decimal(100000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.3, maxDrawdown: 0.2, requireStopLoss: false } }
      });

      const session2 = await paperTradingEngine.createSession(testUser2Id, {
        name: 'User 2 Session for Admin Test',
        initialBalance: new Decimal(75000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.2, maxDrawdown: 0.15, requireStopLoss: false } }
      });

      testSession1Id = session1.id;
      testSession2Id = session2.id;

      // Admin should be able to access both sessions (if implemented)
      // This would typically be through an admin service, not direct portfolio access
      const allSessions = await prisma.paperTradingSession.findMany({
        where: {
          id: { in: [testSession1Id, testSession2Id] }
        }
      });

      expect(allSessions).toHaveLength(2);
      expect(allSessions.find(s => s.userId === testUser1Id)).toBeDefined();
      expect(allSessions.find(s => s.userId === testUser2Id)).toBeDefined();
    });

    it('should prevent privilege escalation attempts', async () => {
      const session = await paperTradingEngine.createSession(testUser1Id, {
        name: 'Privilege Escalation Test',
        initialBalance: new Decimal(50000),
        settings: { enableSlippage: false, enableSpread: false, riskManagement: { maxPositionSize: 0.3, maxDrawdown: 0.2, requireStopLoss: false } }
      });

      testSession1Id = session.id;

      // Regular user should not be able to modify other users' data
      await expect(
        prisma.user.update({
          where: { id: testUser2Id },
          data: { role: 'ADMIN' }
        })
      ).rejects.toThrow(); // Should fail due to RLS policies or application logic

      // Regular user should not be able to access admin-only functions
      // (This would be tested if admin-only endpoints existed)
    });
  });
});