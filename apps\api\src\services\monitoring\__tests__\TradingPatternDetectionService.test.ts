import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { TradingPatternDetectionService } from '../TradingPatternDetectionService';

// Mock Prisma
const mockPrisma = {
  trade: {
    findMany: vi.fn(),
  },
  user: {
    findMany: vi.fn(),
  },
  alert: {
    create: vi.fn(),
  },
} as unknown as PrismaClient;

describe('TradingPatternDetectionService', () => {
  let service: TradingPatternDetectionService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new TradingPatternDetectionService(mockPrisma);
  });

  afterEach(async () => {
    if (service) {
      service.stopMonitoring();
    }
  });

  describe('startMonitoring', () => {
    it('should start monitoring successfully', async () => {
      await service.startMonitoring();
      
      expect(service.getMonitoringStatus().isActive).toBe(true);
    });

    it('should emit monitoringStarted event', async () => {
      const eventSpy = vi.fn();
      service.on('monitoringStarted', eventSpy);
      
      await service.startMonitoring();
      
      expect(eventSpy).toHaveBeenCalled();
    });

    it('should not start if already active', async () => {
      await service.startMonitoring();
      
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      await service.startMonitoring();
      
      expect(consoleSpy).toHaveBeenCalledWith('Trading pattern detection already active');
      consoleSpy.mockRestore();
    });
  });

  describe('stopMonitoring', () => {
    it('should stop monitoring successfully', async () => {
      await service.startMonitoring();
      service.stopMonitoring();
      
      expect(service.getMonitoringStatus().isActive).toBe(false);
    });

    it('should emit monitoringStopped event', async () => {
      const eventSpy = vi.fn();
      service.on('monitoringStopped', eventSpy);
      
      await service.startMonitoring();
      service.stopMonitoring();
      
      expect(eventSpy).toHaveBeenCalled();
    });
  });

  describe('detectSuspiciousPatterns', () => {
    const mockTrades = [
      {
        id: 'trade-1',
        userId: 'user-123',
        symbol: 'EURUSD',
        side: 'buy',
        volume: 1000,
        openPrice: 1.0850,
        closePrice: 1.0900,
        openTime: new Date('2023-01-01T09:00:00Z'),
        closeTime: new Date('2023-01-01T09:30:00Z'),
        profit: 500,
        status: 'closed',
      },
      {
        id: 'trade-2',
        userId: 'user-123',
        symbol: 'EURUSD',
        side: 'buy',
        volume: 2000,
        openPrice: 1.0870,
        closePrice: 1.0920,
        openTime: new Date('2023-01-01T09:35:00Z'),
        closeTime: new Date('2023-01-01T10:05:00Z'),
        profit: 1000,
        status: 'closed',
      },
    ];

    beforeEach(() => {
      vi.mocked(mockPrisma.trade.findMany).mockResolvedValue(mockTrades as any);
      vi.mocked(mockPrisma.user.findMany).mockResolvedValue([
        { id: 'user-123', email: '<EMAIL>' }
      ] as any);
    });

    it('should detect suspicious patterns successfully', async () => {
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for pattern detection
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(mockPrisma.trade.findMany).toHaveBeenCalled();
      expect(mockPrisma.user.findMany).toHaveBeenCalled();
    });

    it('should detect high-frequency trading pattern', async () => {
      const eventSpy = vi.fn();
      service.on('suspiciousPattern', eventSpy);

      // Mock high-frequency trades (many trades in short time)
      const highFreqTrades = Array.from({ length: 50 }, (_, i) => ({
        id: `trade-${i}`,
        userId: 'user-123',
        symbol: 'EURUSD',
        side: i % 2 === 0 ? 'buy' : 'sell',
        volume: 100,
        openTime: new Date(Date.now() - (50 - i) * 60000), // 1 minute apart
        closeTime: new Date(Date.now() - (50 - i) * 60000 + 30000),
        profit: Math.random() * 100,
        status: 'closed',
      }));

      vi.mocked(mockPrisma.trade.findMany).mockResolvedValue(highFreqTrades as any);

      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for pattern detection
      await new Promise(resolve => setTimeout(resolve, 150));

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'high_frequency_trading',
          severity: 'high',
          userId: 'user-123',
        })
      );
    });

    it('should detect unusual profit pattern', async () => {
      const eventSpy = vi.fn();
      service.on('suspiciousPattern', eventSpy);

      // Mock trades with consistently high profits (suspicious)
      const highProfitTrades = Array.from({ length: 10 }, (_, i) => ({
        id: `trade-${i}`,
        userId: 'user-123',
        symbol: 'EURUSD',
        side: 'buy',
        volume: 1000,
        openTime: new Date(Date.now() - (10 - i) * 3600000), // 1 hour apart
        closeTime: new Date(Date.now() - (10 - i) * 3600000 + 1800000),
        profit: 5000 + Math.random() * 1000, // Consistently high profits
        status: 'closed',
      }));

      vi.mocked(mockPrisma.trade.findMany).mockResolvedValue(highProfitTrades as any);

      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for pattern detection
      await new Promise(resolve => setTimeout(resolve, 150));

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'unusual_profit_pattern',
          severity: 'high',
          userId: 'user-123',
        })
      );
    });

    it('should detect unusual volume pattern', async () => {
      const eventSpy = vi.fn();
      service.on('suspiciousPattern', eventSpy);

      // Mock trades with extremely large volumes
      const largeVolumeTrades = Array.from({ length: 5 }, (_, i) => ({
        id: `trade-${i}`,
        userId: 'user-123',
        symbol: 'EURUSD',
        side: 'buy',
        volume: 1000000, // Very large volume
        openTime: new Date(Date.now() - (5 - i) * 3600000),
        closeTime: new Date(Date.now() - (5 - i) * 3600000 + 1800000),
        profit: 1000,
        status: 'closed',
      }));

      vi.mocked(mockPrisma.trade.findMany).mockResolvedValue(largeVolumeTrades as any);

      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for pattern detection
      await new Promise(resolve => setTimeout(resolve, 150));

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'unusual_volume_pattern',
          severity: 'medium',
          userId: 'user-123',
        })
      );
    });

    it('should detect wash trading pattern', async () => {
      const eventSpy = vi.fn();
      service.on('suspiciousPattern', eventSpy);

      // Mock wash trading pattern (buy and sell same amount quickly)
      const washTrades = [
        {
          id: 'trade-1',
          userId: 'user-123',
          symbol: 'EURUSD',
          side: 'buy',
          volume: 10000,
          openTime: new Date('2023-01-01T10:00:00Z'),
          closeTime: new Date('2023-01-01T10:01:00Z'),
          profit: 0,
          status: 'closed',
        },
        {
          id: 'trade-2',
          userId: 'user-123',
          symbol: 'EURUSD',
          side: 'sell',
          volume: 10000,
          openTime: new Date('2023-01-01T10:02:00Z'),
          closeTime: new Date('2023-01-01T10:03:00Z'),
          profit: 0,
          status: 'closed',
        },
      ];

      vi.mocked(mockPrisma.trade.findMany).mockResolvedValue(washTrades as any);

      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for pattern detection
      await new Promise(resolve => setTimeout(resolve, 150));

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'wash_trading',
          severity: 'critical',
          userId: 'user-123',
        })
      );
    });
  });

  describe('analyzeUserPatterns', () => {
    it('should analyze patterns for specific user', async () => {
      const mockUserTrades = [
        {
          id: 'trade-1',
          userId: 'user-123',
          symbol: 'EURUSD',
          side: 'buy',
          volume: 1000,
          openTime: new Date('2023-01-01T09:00:00Z'),
          closeTime: new Date('2023-01-01T09:30:00Z'),
          profit: 500,
          status: 'closed',
        },
      ];

      vi.mocked(mockPrisma.trade.findMany).mockResolvedValue(mockUserTrades as any);

      const patterns = await service.analyzeUserPatterns('user-123', '24h');

      expect(mockPrisma.trade.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user-123',
          openTime: {
            gte: expect.any(Date),
          },
        },
        orderBy: { openTime: 'desc' },
      });

      expect(patterns).toHaveProperty('userId', 'user-123');
      expect(patterns).toHaveProperty('timeRange', '24h');
      expect(patterns).toHaveProperty('totalTrades');
      expect(patterns).toHaveProperty('suspiciousPatterns');
      expect(patterns).toHaveProperty('riskScore');
    });

    it('should calculate risk score correctly', async () => {
      const riskTrades = Array.from({ length: 100 }, (_, i) => ({
        id: `trade-${i}`,
        userId: 'user-123',
        symbol: 'EURUSD',
        side: 'buy',
        volume: 100000, // Large volume
        openTime: new Date(Date.now() - i * 60000), // High frequency
        closeTime: new Date(Date.now() - i * 60000 + 30000),
        profit: 10000, // High profit
        status: 'closed',
      }));

      vi.mocked(mockPrisma.trade.findMany).mockResolvedValue(riskTrades as any);

      const patterns = await service.analyzeUserPatterns('user-123', '1h');

      expect(patterns.riskScore).toBeGreaterThan(70); // High risk score
    });
  });

  describe('createAlert', () => {
    it('should create alert for suspicious pattern', async () => {
      const mockAlert = {
        id: 'alert-123',
        type: 'suspicious_trading_pattern',
        severity: 'high',
        message: 'High frequency trading pattern detected',
        userId: 'user-123',
        createdAt: new Date(),
      };

      vi.mocked(mockPrisma.alert.create).mockResolvedValue(mockAlert as any);

      const patternData = {
        type: 'high_frequency_trading',
        severity: 'high',
        userId: 'user-123',
        details: {
          tradesCount: 50,
          timeWindow: '1h',
          riskScore: 85,
        },
      };

      const result = await service.createAlert(patternData);

      expect(mockPrisma.alert.create).toHaveBeenCalledWith({
        data: {
          type: 'suspicious_trading_pattern',
          severity: 'high',
          category: 'trading_pattern',
          source: 'trading_pattern_detection',
          title: 'Suspicious Trading Pattern Detected',
          message: expect.stringContaining('High frequency trading pattern detected'),
          userId: 'user-123',
          metadata: {
            patternType: 'high_frequency_trading',
            details: patternData.details,
            detectedAt: expect.any(String),
          },
          status: 'active',
        },
      });

      expect(result).toEqual(mockAlert);
    });

    it('should handle alert creation errors', async () => {
      vi.mocked(mockPrisma.alert.create).mockRejectedValue(new Error('Alert creation failed'));

      const patternData = {
        type: 'wash_trading',
        severity: 'critical',
        userId: 'user-123',
        details: {},
      };

      await expect(service.createAlert(patternData)).rejects.toThrow('Alert creation failed');
    });
  });

  describe('pattern analysis algorithms', () => {
    it('should correctly identify high frequency trading', () => {
      const testService = service as any;
      
      const trades = Array.from({ length: 30 }, (_, i) => ({
        openTime: new Date(Date.now() - i * 60000), // 1 minute apart
        volume: 1000,
        profit: 100,
      }));

      const isHighFreq = testService.isHighFrequencyTrading(trades, '1h');
      expect(isHighFreq).toBe(true);
    });

    it('should correctly identify unusual profit patterns', () => {
      const testService = service as any;
      
      const consistentProfitTrades = Array.from({ length: 10 }, () => ({
        profit: 5000, // Consistently high profits
        volume: 1000,
      }));

      const isUnusualProfit = testService.hasUnusualProfitPattern(consistentProfitTrades);
      expect(isUnusualProfit).toBe(true);
    });

    it('should correctly identify wash trading', () => {
      const testService = service as any;
      
      const washTrades = [
        { side: 'buy', volume: 10000, symbol: 'EURUSD', openTime: new Date('2023-01-01T10:00:00Z') },
        { side: 'sell', volume: 10000, symbol: 'EURUSD', openTime: new Date('2023-01-01T10:02:00Z') },
      ];

      const isWashTrading = testService.isWashTrading(washTrades);
      expect(isWashTrading).toBe(true);
    });

    it('should calculate risk score correctly', () => {
      const testService = service as any;
      
      const riskFactors = {
        tradesCount: 100, // High frequency
        avgVolume: 100000, // Large volume
        profitConsistency: 0.95, // Very consistent profits
        washTradingScore: 0.8, // High wash trading likelihood
      };

      const riskScore = testService.calculateRiskScore(riskFactors);
      expect(riskScore).toBeGreaterThan(70);
      expect(riskScore).toBeLessThanOrEqual(100);
    });
  });

  describe('getPatternStatistics', () => {
    it('should return pattern statistics', async () => {
      const mockPatterns = [
        { type: 'high_frequency_trading', count: 5 },
        { type: 'unusual_profit_pattern', count: 3 },
        { type: 'wash_trading', count: 2 },
      ];

      // Mock the internal method that would be called
      const testService = service as any;
      testService.detectedPatterns = mockPatterns;

      const stats = await service.getPatternStatistics('24h');

      expect(stats).toHaveProperty('timeRange', '24h');
      expect(stats).toHaveProperty('totalPatternsDetected');
      expect(stats).toHaveProperty('patternsByType');
      expect(stats).toHaveProperty('severityDistribution');
    });
  });

  describe('getMonitoringStatus', () => {
    it('should return correct monitoring status', () => {
      const status = service.getMonitoringStatus();
      
      expect(status).toHaveProperty('isActive');
      expect(status).toHaveProperty('startTime');
      expect(status).toHaveProperty('patternsDetected');
      expect(status).toHaveProperty('usersAnalyzed');
      expect(status).toHaveProperty('lastAnalysisTime');
    });
  });

  describe('error handling', () => {
    it('should emit error event on monitoring failure', async () => {
      const errorSpy = vi.fn();
      service.on('error', errorSpy);

      vi.mocked(mockPrisma.trade.findMany).mockRejectedValue(new Error('Database connection lost'));

      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for error to occur
      await new Promise(resolve => setTimeout(resolve, 150));

      expect(errorSpy).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should continue monitoring after recoverable errors', async () => {
      vi.mocked(mockPrisma.trade.findMany)
        .mockRejectedValueOnce(new Error('Temporary error'))
        .mockResolvedValue([]);

      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for recovery
      await new Promise(resolve => setTimeout(resolve, 250));

      expect(service.getMonitoringStatus().isActive).toBe(true);
    });
  });
});