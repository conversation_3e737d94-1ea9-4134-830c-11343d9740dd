"""
Performance Monitoring and Optimization System
Comprehensive performance tracking, measurement, and optimization for MT5 Bridge Service
"""

import asyncio
import time
import psutil
import statistics
from typing import Dict, List, Any, Optional, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import deque, defaultdict
import json
import threading
from concurrent.futures import ThreadPoolExecutor
from loguru import logger
import numpy as np

from config import get_config

@dataclass
class PerformanceMetric:
    """Performance metric data structure"""
    name: str
    value: float
    unit: str
    timestamp: datetime
    category: str
    tags: Dict[str, str] = field(default_factory=dict)
    
@dataclass
class LatencyMeasurement:
    """Latency measurement result"""
    operation: str
    latency_ms: float
    timestamp: datetime
    success: bool
    error_message: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ThroughputMeasurement:
    """Throughput measurement result"""
    operation: str
    operations_per_second: float
    total_operations: int
    duration_seconds: float
    timestamp: datetime
    context: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ResourceUsage:
    """System resource usage measurement"""
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    disk_io_read_mb: float
    disk_io_write_mb: float
    network_io_sent_mb: float
    network_io_recv_mb: float
    timestamp: datetime
    process_specific: bool = True

class PerformanceProfiler:
    """Performance profiling and measurement utilities"""
    
    def __init__(self):
        self.measurements: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.active_timers: Dict[str, float] = {}
        self.config = get_config()
        
        # Performance thresholds
        self.thresholds = {
            'api_response_ms': 200,
            'data_processing_ms': 100,
            'database_query_ms': 50,
            'websocket_broadcast_ms': 10,
            'price_update_ms': 5,
            'cpu_percent': 80,
            'memory_percent': 85,
            'throughput_ops_per_sec': 1000
        }
        
        # Resource monitoring
        self.process = psutil.Process()
        self.system_baseline = None
        self.monitoring_active = False
        self.monitoring_thread = None
        
    def start_monitoring(self, interval_seconds: float = 1.0):
        """Start continuous performance monitoring"""
        
        self.monitoring_active = True
        
        def monitor_resources():
            """Background thread for resource monitoring"""
            
            while self.monitoring_active:
                try:
                    # System-wide metrics
                    cpu_percent = psutil.cpu_percent(interval=None)
                    memory = psutil.virtual_memory()
                    disk_io = psutil.disk_io_counters()
                    network_io = psutil.net_io_counters()
                    
                    # Process-specific metrics
                    process_cpu = self.process.cpu_percent()
                    process_memory = self.process.memory_info()
                    
                    # Create resource usage measurement
                    usage = ResourceUsage(
                        cpu_percent=process_cpu,
                        memory_percent=memory.percent,
                        memory_mb=process_memory.rss / 1024 / 1024,
                        disk_io_read_mb=disk_io.read_bytes / 1024 / 1024 if disk_io else 0,
                        disk_io_write_mb=disk_io.write_bytes / 1024 / 1024 if disk_io else 0,
                        network_io_sent_mb=network_io.bytes_sent / 1024 / 1024 if network_io else 0,
                        network_io_recv_mb=network_io.bytes_recv / 1024 / 1024 if network_io else 0,
                        timestamp=datetime.now(),
                        process_specific=True
                    )
                    
                    self.record_resource_usage(usage)
                    
                    # Check thresholds
                    self._check_performance_thresholds(usage)
                    
                    time.sleep(interval_seconds)
                    
                except Exception as e:
                    logger.error(f"Resource monitoring error: {e}")
                    time.sleep(interval_seconds)
        
        self.monitoring_thread = threading.Thread(target=monitor_resources, daemon=True)
        self.monitoring_thread.start()
        logger.info("🔍 Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop continuous performance monitoring"""
        
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
        logger.info("⏹️ Performance monitoring stopped")
    
    def measure_latency(self, operation: str):
        """Context manager for measuring operation latency"""
        
        class LatencyContext:
            def __init__(self, profiler, op_name):
                self.profiler = profiler
                self.operation = op_name
                self.start_time = None
                
            def __enter__(self):
                self.start_time = time.perf_counter()
                return self
                
            def __exit__(self, exc_type, exc_val, exc_tb):
                end_time = time.perf_counter()
                latency_ms = (end_time - self.start_time) * 1000
                
                measurement = LatencyMeasurement(
                    operation=self.operation,
                    latency_ms=latency_ms,
                    timestamp=datetime.now(),
                    success=exc_type is None,
                    error_message=str(exc_val) if exc_val else None
                )
                
                self.profiler.record_latency(measurement)
        
        return LatencyContext(self, operation)
    
    async def measure_async_latency(self, operation: str, func: Callable, *args, **kwargs):
        """Measure latency of an async function"""
        
        start_time = time.perf_counter()
        error_message = None
        success = True
        
        try:
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)
            return result
        except Exception as e:
            success = False
            error_message = str(e)
            raise
        finally:
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000
            
            measurement = LatencyMeasurement(
                operation=operation,
                latency_ms=latency_ms,
                timestamp=datetime.now(),
                success=success,
                error_message=error_message
            )
            
            self.record_latency(measurement)
    
    def measure_throughput(self, operation: str, operation_count: int, duration_seconds: float, 
                          context: Optional[Dict[str, Any]] = None):
        """Record throughput measurement"""
        
        ops_per_second = operation_count / duration_seconds if duration_seconds > 0 else 0
        
        measurement = ThroughputMeasurement(
            operation=operation,
            operations_per_second=ops_per_second,
            total_operations=operation_count,
            duration_seconds=duration_seconds,
            timestamp=datetime.now(),
            context=context or {}
        )
        
        self.record_throughput(measurement)
        return measurement
    
    def record_latency(self, measurement: LatencyMeasurement):
        """Record a latency measurement"""
        
        self.measurements[f"latency_{measurement.operation}"].append(measurement)
        
        # Check threshold
        threshold_key = f"{measurement.operation.lower()}_ms"
        if threshold_key in self.thresholds:
            if measurement.latency_ms > self.thresholds[threshold_key]:
                logger.warning(
                    f"⚠️ High latency detected: {measurement.operation} took {measurement.latency_ms:.2f}ms "
                    f"(threshold: {self.thresholds[threshold_key]}ms)"
                )
    
    def record_throughput(self, measurement: ThroughputMeasurement):
        """Record a throughput measurement"""
        
        self.measurements[f"throughput_{measurement.operation}"].append(measurement)
        
        # Check threshold
        if measurement.operations_per_second < self.thresholds.get('throughput_ops_per_sec', 0):
            logger.warning(
                f"⚠️ Low throughput detected: {measurement.operation} at {measurement.operations_per_second:.2f} ops/sec"
            )
    
    def record_resource_usage(self, usage: ResourceUsage):
        """Record resource usage measurement"""
        
        self.measurements["resource_usage"].append(usage)
    
    def _check_performance_thresholds(self, usage: ResourceUsage):
        """Check if resource usage exceeds thresholds"""
        
        if usage.cpu_percent > self.thresholds['cpu_percent']:
            logger.warning(f"⚠️ High CPU usage: {usage.cpu_percent:.1f}%")
        
        if usage.memory_percent > self.thresholds['memory_percent']:
            logger.warning(f"⚠️ High memory usage: {usage.memory_percent:.1f}%")
    
    def get_performance_summary(self, hours_back: int = 1) -> Dict[str, Any]:
        """Get performance summary for the specified time period"""
        
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        summary = {
            'period_hours': hours_back,
            'generated_at': datetime.now().isoformat(),
            'latency_stats': {},
            'throughput_stats': {},
            'resource_stats': {},
            'threshold_violations': 0
        }
        
        # Latency statistics
        for key, measurements in self.measurements.items():
            if key.startswith('latency_'):
                operation = key[8:]  # Remove 'latency_' prefix
                recent_measurements = [
                    m for m in measurements 
                    if m.timestamp > cutoff_time
                ]
                
                if recent_measurements:
                    latencies = [m.latency_ms for m in recent_measurements]
                    summary['latency_stats'][operation] = {
                        'count': len(latencies),
                        'mean_ms': statistics.mean(latencies),
                        'median_ms': statistics.median(latencies),
                        'p95_ms': np.percentile(latencies, 95) if len(latencies) >= 20 else max(latencies),
                        'p99_ms': np.percentile(latencies, 99) if len(latencies) >= 100 else max(latencies),
                        'min_ms': min(latencies),
                        'max_ms': max(latencies),
                        'success_rate': sum(1 for m in recent_measurements if m.success) / len(recent_measurements)
                    }
        
        # Throughput statistics
        for key, measurements in self.measurements.items():
            if key.startswith('throughput_'):
                operation = key[11:]  # Remove 'throughput_' prefix
                recent_measurements = [
                    m for m in measurements 
                    if m.timestamp > cutoff_time
                ]
                
                if recent_measurements:
                    throughputs = [m.operations_per_second for m in recent_measurements]
                    total_ops = sum(m.total_operations for m in recent_measurements)
                    
                    summary['throughput_stats'][operation] = {
                        'count': len(throughputs),
                        'mean_ops_per_sec': statistics.mean(throughputs),
                        'max_ops_per_sec': max(throughputs),
                        'min_ops_per_sec': min(throughputs),
                        'total_operations': total_ops
                    }
        
        # Resource statistics
        resource_measurements = [
            m for m in self.measurements["resource_usage"] 
            if m.timestamp > cutoff_time
        ]
        
        if resource_measurements:
            cpu_values = [m.cpu_percent for m in resource_measurements]
            memory_values = [m.memory_mb for m in resource_measurements]
            
            summary['resource_stats'] = {
                'cpu_percent': {
                    'mean': statistics.mean(cpu_values),
                    'max': max(cpu_values),
                    'min': min(cpu_values)
                },
                'memory_mb': {
                    'mean': statistics.mean(memory_values),
                    'max': max(memory_values),
                    'min': min(memory_values)
                },
                'sample_count': len(resource_measurements)
            }
        
        return summary
    
    def get_real_time_metrics(self) -> Dict[str, Any]:
        """Get current real-time performance metrics"""
        
        recent_window = datetime.now() - timedelta(seconds=30)
        metrics = {
            'timestamp': datetime.now().isoformat(),
            'current_resource_usage': {},
            'recent_latencies': {},
            'recent_throughput': {}
        }
        
        # Current resource usage
        if self.measurements["resource_usage"]:
            latest_usage = list(self.measurements["resource_usage"])[-1]
            metrics['current_resource_usage'] = {
                'cpu_percent': latest_usage.cpu_percent,
                'memory_mb': latest_usage.memory_mb,
                'timestamp': latest_usage.timestamp.isoformat()
            }
        
        # Recent latencies (last 30 seconds)
        for key, measurements in self.measurements.items():
            if key.startswith('latency_'):
                operation = key[8:]
                recent = [m for m in measurements if m.timestamp > recent_window]
                if recent:
                    latencies = [m.latency_ms for m in recent if m.success]
                    if latencies:
                        metrics['recent_latencies'][operation] = {
                            'mean_ms': statistics.mean(latencies),
                            'count': len(latencies)
                        }
        
        return metrics
    
    def export_metrics(self, format_type: str = 'json') -> str:
        """Export performance metrics in specified format"""
        
        if format_type == 'json':
            summary = self.get_performance_summary(hours_back=24)
            return json.dumps(summary, indent=2, default=str)
        else:
            raise ValueError(f"Unsupported format: {format_type}")

class PerformanceTestSuite:
    """Comprehensive performance test suite"""
    
    def __init__(self):
        self.profiler = PerformanceProfiler()
        self.config = get_config()
        self.test_results: List[Dict[str, Any]] = []
        
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run all performance tests"""
        
        logger.info("🏃 Starting comprehensive performance tests...")
        
        # Start monitoring
        self.profiler.start_monitoring()
        
        try:
            results = {
                'test_suite': 'MT5 Bridge Performance Tests',
                'started_at': datetime.now().isoformat(),
                'tests': {}
            }
            
            # Run individual test suites
            results['tests']['latency'] = await self._test_latency_performance()
            results['tests']['throughput'] = await self._test_throughput_performance()
            results['tests']['stress'] = await self._test_stress_performance()
            results['tests']['memory'] = await self._test_memory_performance()
            results['tests']['concurrent'] = await self._test_concurrent_performance()
            
            results['completed_at'] = datetime.now().isoformat()
            results['overall_summary'] = self._generate_overall_summary(results['tests'])
            
            return results
            
        finally:
            self.profiler.stop_monitoring()
    
    async def _test_latency_performance(self) -> Dict[str, Any]:
        """Test latency performance of key operations"""
        
        logger.info("📊 Testing latency performance...")
        
        results = {
            'test_name': 'Latency Performance Test',
            'operations': {}
        }
        
        # Test database operations
        from database import get_db_manager
        db_manager = get_db_manager()
        
        if hasattr(db_manager, 'connection_pool') and db_manager.connection_pool:
            # Test simple query
            for i in range(10):
                with self.profiler.measure_latency('database_simple_query'):
                    try:
                        async with db_manager.connection_pool.acquire() as conn:
                            await conn.fetchval("SELECT 1")
                    except Exception as e:
                        logger.warning(f"Database query test failed: {e}")
        
        # Test data transformation
        from data_transformer import DataTransformer
        transformer = DataTransformer()
        
        sample_data = {
            'symbol': 'EURUSD',
            'timeframe': '1m',
            'timestamp': datetime.now(),
            'open': 1.1000,
            'high': 1.1005,
            'low': 1.0995,
            'close': 1.1002,
            'volume': 1000,
            'source': 'mt5'
        }
        
        for i in range(100):
            with self.profiler.measure_latency('data_transformation'):
                transformed = transformer.transform_market_data(sample_data)
        
        # Compile results
        for operation in ['database_simple_query', 'data_transformation']:
            measurements = [
                m for m in self.profiler.measurements[f'latency_{operation}']
                if isinstance(m, LatencyMeasurement)
            ]
            
            if measurements:
                latencies = [m.latency_ms for m in measurements if m.success]
                if latencies:
                    results['operations'][operation] = {
                        'count': len(latencies),
                        'mean_ms': statistics.mean(latencies),
                        'median_ms': statistics.median(latencies),
                        'max_ms': max(latencies),
                        'min_ms': min(latencies),
                        'success_rate': len(latencies) / len(measurements)
                    }
        
        return results
    
    async def _test_throughput_performance(self) -> Dict[str, Any]:
        """Test throughput performance"""
        
        logger.info("🚀 Testing throughput performance...")
        
        results = {
            'test_name': 'Throughput Performance Test',
            'operations': {}
        }
        
        # Test data processing throughput
        from data_transformer import DataTransformer
        transformer = DataTransformer()
        
        # Generate test data
        test_data = []
        for i in range(1000):
            test_data.append({
                'symbol': 'EURUSD',
                'timeframe': '1m',
                'timestamp': datetime.now() + timedelta(minutes=i),
                'open': 1.1000 + (i * 0.0001),
                'high': 1.1000 + (i * 0.0001) + 0.0005,
                'low': 1.1000 + (i * 0.0001) - 0.0005,
                'close': 1.1000 + (i * 0.0001) + 0.0002,
                'volume': 1000 + i,
                'source': 'mt5'
            })
        
        # Test transformation throughput
        start_time = time.perf_counter()
        for data in test_data:
            transformer.transform_market_data(data)
        end_time = time.perf_counter()
        
        duration = end_time - start_time
        throughput = self.profiler.measure_throughput(
            'data_transformation_batch',
            len(test_data),
            duration,
            {'batch_size': len(test_data)}
        )
        
        results['operations']['data_transformation_batch'] = {
            'total_operations': throughput.total_operations,
            'duration_seconds': throughput.duration_seconds,
            'operations_per_second': throughput.operations_per_second
        }
        
        # Test concurrent processing
        async def process_batch(batch):
            return [transformer.transform_market_data(item) for item in batch]
        
        batch_size = 100
        batches = [test_data[i:i+batch_size] for i in range(0, len(test_data), batch_size)]
        
        start_time = time.perf_counter()
        tasks = [process_batch(batch) for batch in batches]
        await asyncio.gather(*tasks)
        end_time = time.perf_counter()
        
        concurrent_duration = end_time - start_time
        concurrent_throughput = self.profiler.measure_throughput(
            'concurrent_data_processing',
            len(test_data),
            concurrent_duration,
            {'batch_count': len(batches), 'batch_size': batch_size}
        )
        
        results['operations']['concurrent_data_processing'] = {
            'total_operations': concurrent_throughput.total_operations,
            'duration_seconds': concurrent_throughput.duration_seconds,
            'operations_per_second': concurrent_throughput.operations_per_second,
            'speedup_factor': duration / concurrent_duration if concurrent_duration > 0 else 0
        }
        
        return results
    
    async def _test_stress_performance(self) -> Dict[str, Any]:
        """Test system performance under stress"""
        
        logger.info("💪 Testing stress performance...")
        
        results = {
            'test_name': 'Stress Performance Test',
            'scenarios': {}
        }
        
        # High-frequency data processing
        high_freq_data = []
        for i in range(10000):  # 10K records
            high_freq_data.append({
                'symbol': f'PAIR{i % 10}',
                'timeframe': '1m',
                'timestamp': datetime.now() + timedelta(seconds=i),
                'open': 1.0000 + (i * 0.00001),
                'high': 1.0000 + (i * 0.00001) + 0.00005,
                'low': 1.0000 + (i * 0.00001) - 0.00005,
                'close': 1.0000 + (i * 0.00001) + 0.00002,
                'volume': 1000 + (i % 1000),
                'source': 'mt5'
            })
        
        # Measure resource usage during stress test
        start_usage = list(self.profiler.measurements["resource_usage"])[-1] if self.profiler.measurements["resource_usage"] else None
        
        from data_transformer import DataTransformer
        transformer = DataTransformer()
        
        start_time = time.perf_counter()
        transformed_count = 0
        
        # Process in chunks to simulate real-world conditions
        chunk_size = 100
        for i in range(0, len(high_freq_data), chunk_size):
            chunk = high_freq_data[i:i+chunk_size]
            for item in chunk:
                transformer.transform_market_data(item)
                transformed_count += 1
            
            # Small delay to prevent overwhelming the system
            await asyncio.sleep(0.001)
        
        end_time = time.perf_counter()
        
        # Measure final resource usage
        end_usage = list(self.profiler.measurements["resource_usage"])[-1] if self.profiler.measurements["resource_usage"] else None
        
        stress_duration = end_time - start_time
        stress_throughput = self.profiler.measure_throughput(
            'stress_test_processing',
            transformed_count,
            stress_duration,
            {'total_records': len(high_freq_data), 'chunk_size': chunk_size}
        )
        
        results['scenarios']['high_frequency_processing'] = {
            'total_records': len(high_freq_data),
            'processed_records': transformed_count,
            'duration_seconds': stress_duration,
            'operations_per_second': stress_throughput.operations_per_second,
            'resource_impact': {
                'cpu_increase': (end_usage.cpu_percent - start_usage.cpu_percent) if start_usage and end_usage else 0,
                'memory_increase_mb': (end_usage.memory_mb - start_usage.memory_mb) if start_usage and end_usage else 0
            }
        }
        
        return results
    
    async def _test_memory_performance(self) -> Dict[str, Any]:
        """Test memory usage and garbage collection performance"""
        
        logger.info("🧠 Testing memory performance...")
        
        results = {
            'test_name': 'Memory Performance Test',
            'scenarios': {}
        }
        
        import gc
        
        # Baseline memory usage
        gc.collect()
        baseline_memory = self.profiler.process.memory_info().rss / 1024 / 1024
        
        # Create large dataset in memory
        large_dataset = []
        for i in range(50000):  # 50K records
            large_dataset.append({
                'id': i,
                'symbol': f'SYM{i % 100}',
                'data': [j for j in range(100)],  # Some bulk data
                'timestamp': datetime.now() + timedelta(seconds=i),
                'metadata': {'test': True, 'batch': i // 1000}
            })
        
        peak_memory = self.profiler.process.memory_info().rss / 1024 / 1024
        memory_used = peak_memory - baseline_memory
        
        # Test data processing with memory monitoring
        processed_count = 0
        start_time = time.perf_counter()
        
        for item in large_dataset:
            # Simulate processing
            processed_item = {
                'processed_id': item['id'],
                'symbol': item['symbol'],
                'processed_at': datetime.now()
            }
            processed_count += 1
            
            # Trigger GC periodically
            if processed_count % 10000 == 0:
                gc.collect()
        
        end_time = time.perf_counter()
        
        # Final memory cleanup
        del large_dataset
        gc.collect()
        final_memory = self.profiler.process.memory_info().rss / 1024 / 1024
        
        processing_duration = end_time - start_time
        
        results['scenarios']['large_dataset_processing'] = {
            'dataset_size': 50000,
            'baseline_memory_mb': baseline_memory,
            'peak_memory_mb': peak_memory,
            'memory_used_mb': memory_used,
            'final_memory_mb': final_memory,
            'memory_efficiency': (final_memory - baseline_memory) / memory_used if memory_used > 0 else 0,
            'processing_duration_seconds': processing_duration,
            'records_per_second': processed_count / processing_duration if processing_duration > 0 else 0
        }
        
        return results
    
    async def _test_concurrent_performance(self) -> Dict[str, Any]:
        """Test concurrent operation performance"""
        
        logger.info("🔄 Testing concurrent performance...")
        
        results = {
            'test_name': 'Concurrent Performance Test',
            'scenarios': {}
        }
        
        from data_transformer import DataTransformer
        
        # Test different concurrency levels
        concurrency_levels = [1, 2, 4, 8, 16]
        
        for concurrency in concurrency_levels:
            transformer = DataTransformer()
            
            # Generate test data
            test_items = []
            for i in range(1000):
                test_items.append({
                    'symbol': 'EURUSD',
                    'timeframe': '1m',
                    'timestamp': datetime.now() + timedelta(minutes=i),
                    'open': 1.1000 + (i * 0.0001),
                    'high': 1.1000 + (i * 0.0001) + 0.0005,
                    'low': 1.1000 + (i * 0.0001) - 0.0005,
                    'close': 1.1000 + (i * 0.0001) + 0.0002,
                    'volume': 1000 + i,
                    'source': 'mt5'
                })
            
            async def process_items(items):
                return [transformer.transform_market_data(item) for item in items]
            
            # Split work across concurrent tasks
            chunk_size = len(test_items) // concurrency
            chunks = [test_items[i:i+chunk_size] for i in range(0, len(test_items), chunk_size)]
            
            start_time = time.perf_counter()
            tasks = [process_items(chunk) for chunk in chunks[:concurrency]]
            await asyncio.gather(*tasks)
            end_time = time.perf_counter()
            
            duration = end_time - start_time
            throughput = len(test_items) / duration if duration > 0 else 0
            
            results['scenarios'][f'concurrency_{concurrency}'] = {
                'concurrency_level': concurrency,
                'total_items': len(test_items),
                'duration_seconds': duration,
                'throughput_ops_per_sec': throughput,
                'chunks_processed': len(chunks[:concurrency])
            }
        
        # Calculate scaling efficiency
        if 'concurrency_1' in results['scenarios'] and 'concurrency_8' in results['scenarios']:
            baseline_throughput = results['scenarios']['concurrency_1']['throughput_ops_per_sec']
            parallel_throughput = results['scenarios']['concurrency_8']['throughput_ops_per_sec']
            scaling_efficiency = (parallel_throughput / baseline_throughput) / 8 if baseline_throughput > 0 else 0
            
            results['scaling_analysis'] = {
                'baseline_throughput': baseline_throughput,
                'parallel_throughput': parallel_throughput,
                'scaling_efficiency': scaling_efficiency,
                'speedup_factor': parallel_throughput / baseline_throughput if baseline_throughput > 0 else 0
            }
        
        return results
    
    def _generate_overall_summary(self, test_results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate overall performance summary"""
        
        summary = {
            'total_tests_run': len(test_results),
            'performance_score': 0,
            'recommendations': [],
            'key_metrics': {}
        }
        
        # Analyze latency results
        if 'latency' in test_results:
            latency_ops = test_results['latency'].get('operations', {})
            if latency_ops:
                avg_latencies = [op.get('mean_ms', 0) for op in latency_ops.values()]
                if avg_latencies:
                    summary['key_metrics']['average_latency_ms'] = statistics.mean(avg_latencies)
        
        # Analyze throughput results
        if 'throughput' in test_results:
            throughput_ops = test_results['throughput'].get('operations', {})
            throughputs = [op.get('operations_per_second', 0) for op in throughput_ops.values()]
            if throughputs:
                summary['key_metrics']['max_throughput_ops_per_sec'] = max(throughputs)
        
        # Generate recommendations
        avg_latency = summary['key_metrics'].get('average_latency_ms', 0)
        if avg_latency > 100:
            summary['recommendations'].append("Consider optimizing high-latency operations")
        
        max_throughput = summary['key_metrics'].get('max_throughput_ops_per_sec', 0)
        if max_throughput < 1000:
            summary['recommendations'].append("Consider improving data processing throughput")
        
        # Calculate overall performance score (0-100)
        latency_score = max(0, 100 - (avg_latency / 10)) if avg_latency > 0 else 100
        throughput_score = min(100, max_throughput / 100) if max_throughput > 0 else 0
        
        summary['performance_score'] = (latency_score + throughput_score) / 2
        
        if not summary['recommendations']:
            summary['recommendations'].append("Performance metrics are within acceptable ranges")
        
        return summary

# Global instance
_performance_profiler: Optional[PerformanceProfiler] = None

def get_performance_profiler() -> PerformanceProfiler:
    """Get global performance profiler instance"""
    global _performance_profiler
    if _performance_profiler is None:
        _performance_profiler = PerformanceProfiler()
    return _performance_profiler