/**
 * Results Storage and Caching System
 * 
 * @fileoverview Manages optimization results storage, caching, and retrieval
 * Includes database operations, intelligent caching, and result comparison
 */

import { randomUUID } from 'crypto';
import type {
  OptimizationResult,
  OptimizationCache,
  OptimizationQueue,
  StrategyParameters,
  PerformanceMetrics,
  WalkForwardResult
} from '@golddaddy/types/optimization';

// ===== Storage Interfaces =====

interface StorageOptions {
  enableCaching: boolean;
  cacheExpirationHours: number;
  maxCacheSize: number;
  enableCompression: boolean;
  retentionDays: number;
}

interface ResultQuery {
  strategyId?: string;
  userId?: string;
  status?: string;
  startDate?: Date;
  endDate?: Date;
  limit?: number;
  offset?: number;
}

interface CacheKey {
  strategyId: string;
  parameterHash: string;
  configHash: string;
}

interface ResultComparison {
  currentResult: OptimizationResult;
  previousResult?: OptimizationResult;
  improvement: {
    sharpeRatio: number;
    totalReturn: number;
    maxDrawdown: number;
    profitFactor: number;
    overallScore: number;
  };
  isSignificantImprovement: boolean;
  recommendations: string[];
}

// ===== Results Storage Class =====

export class ResultsStorage {
  private cache: Map<string, OptimizationCache> = new Map();
  private options: StorageOptions;
  private compressionEnabled: boolean;

  constructor(options: Partial<StorageOptions> = {}) {
    this.options = {
      enableCaching: true,
      cacheExpirationHours: 24,
      maxCacheSize: 1000,
      enableCompression: false,
      retentionDays: 365,
      ...options
    };

    this.compressionEnabled = this.options.enableCompression;
    
    // Start cache cleanup interval
    setInterval(() => this.cleanupExpiredCache(), 60 * 60 * 1000); // Every hour
  }

  // ===== Main Storage Operations =====

  /**
   * Store optimization result
   */
  public async storeResult(result: OptimizationResult): Promise<string> {
    try {
      // Generate storage ID if not provided
      if (!result.id) {
        result.id = randomUUID();
      }

      // Compress data if enabled
      const storedResult = this.compressionEnabled 
        ? await this.compressResult(result)
        : result;

      // Store in database (mock implementation)
      await this.saveToDatabase(storedResult);

      // Update cache if enabled
      if (this.options.enableCaching) {
        await this.updateCache(result);
      }

      // Link to previous results for comparison
      await this.linkToPreviousResults(result);

      return result.id;

    } catch (error) {
      console.error('Failed to store optimization result:', error);
      throw new Error('Storage operation failed');
    }
  }

  /**
   * Retrieve optimization result by ID
   */
  public async getResult(resultId: string): Promise<OptimizationResult | null> {
    try {
      // Check cache first
      if (this.options.enableCaching) {
        const cached = await this.getFromCache(resultId);
        if (cached) {
          return cached;
        }
      }

      // Load from database
      const result = await this.loadFromDatabase(resultId);
      if (!result) return null;

      // Decompress if needed
      const decompressedResult = this.compressionEnabled
        ? await this.decompressResult(result)
        : result;

      // Update cache
      if (this.options.enableCaching) {
        await this.addToCache(resultId, decompressedResult);
      }

      return decompressedResult;

    } catch (error) {
      console.error('Failed to retrieve optimization result:', error);
      return null;
    }
  }

  /**
   * Query optimization results
   */
  public async queryResults(query: ResultQuery): Promise<{
    results: OptimizationResult[];
    totalCount: number;
    hasMore: boolean;
  }> {
    try {
      const { results, totalCount } = await this.queryDatabase(query);
      const hasMore = (query.offset || 0) + results.length < totalCount;

      return {
        results: await Promise.all(
          results.map(result => 
            this.compressionEnabled 
              ? this.decompressResult(result)
              : result
          )
        ),
        totalCount,
        hasMore
      };

    } catch (error) {
      console.error('Failed to query optimization results:', error);
      throw new Error('Query operation failed');
    }
  }

  /**
   * Delete optimization result
   */
  public async deleteResult(resultId: string): Promise<boolean> {
    try {
      // Remove from cache
      this.removeFromCache(resultId);

      // Remove from database
      const deleted = await this.deleteFromDatabase(resultId);

      return deleted;

    } catch (error) {
      console.error('Failed to delete optimization result:', error);
      return false;
    }
  }

  // ===== Caching Operations =====

  /**
   * Check if optimization is cached
   */
  public async isCached(strategyId: string, parameters: StrategyParameters, configHash: string): Promise<OptimizationResult | null> {
    if (!this.options.enableCaching) return null;

    const parameterHash = this.hashParameters(parameters);
    const cacheKey = this.generateCacheKey({ strategyId, parameterHash, configHash });

    const cached = this.cache.get(cacheKey);
    if (!cached) return null;

    // Check expiration
    if (this.isCacheExpired(cached)) {
      this.cache.delete(cacheKey);
      return null;
    }

    // Update access count and timestamp
    cached.accessCount++;
    cached.lastAccessedAt = new Date();

    return cached.result;
  }

  /**
   * Update cache with new result
   */
  private async updateCache(result: OptimizationResult): Promise<void> {
    if (!this.options.enableCaching) return;

    const parameterHash = this.hashParameters(result.bestParameters);
    const configHash = this.hashConfig(result.config);
    const cacheKey = this.generateCacheKey({
      strategyId: result.strategyId,
      parameterHash,
      configHash
    });

    const cacheEntry: OptimizationCache = {
      strategyId: result.strategyId,
      parameterHash,
      result,
      createdAt: new Date(),
      accessCount: 1,
      lastAccessedAt: new Date(),
      expiresAt: new Date(Date.now() + this.options.cacheExpirationHours * 60 * 60 * 1000)
    };

    this.cache.set(cacheKey, cacheEntry);

    // Enforce cache size limit
    if (this.cache.size > this.options.maxCacheSize) {
      this.evictOldestCacheEntries();
    }
  }

  /**
   * Get result from cache
   */
  private async getFromCache(resultId: string): Promise<OptimizationResult | null> {
    for (const cached of this.cache.values()) {
      if (cached.result.id === resultId && !this.isCacheExpired(cached)) {
        cached.accessCount++;
        cached.lastAccessedAt = new Date();
        return cached.result;
      }
    }
    return null;
  }

  /**
   * Add result to cache
   */
  private async addToCache(resultId: string, result: OptimizationResult): Promise<void> {
    const parameterHash = this.hashParameters(result.bestParameters);
    const configHash = this.hashConfig(result.config);
    const cacheKey = this.generateCacheKey({
      strategyId: result.strategyId,
      parameterHash,
      configHash
    });

    const cacheEntry: OptimizationCache = {
      strategyId: result.strategyId,
      parameterHash,
      result,
      createdAt: new Date(),
      accessCount: 1,
      lastAccessedAt: new Date(),
      expiresAt: new Date(Date.now() + this.options.cacheExpirationHours * 60 * 60 * 1000)
    };

    this.cache.set(cacheKey, cacheEntry);
  }

  /**
   * Remove result from cache
   */
  private removeFromCache(resultId: string): void {
    for (const [key, cached] of this.cache.entries()) {
      if (cached.result.id === resultId) {
        this.cache.delete(key);
        break;
      }
    }
  }

  // ===== Result Comparison =====

  /**
   * Compare optimization results
   */
  public async compareResults(
    currentResultId: string,
    previousResultId?: string
  ): Promise<ResultComparison> {
    const currentResult = await this.getResult(currentResultId);
    if (!currentResult) {
      throw new Error('Current result not found');
    }

    let previousResult: OptimizationResult | undefined;
    
    if (previousResultId) {
      previousResult = await this.getResult(previousResultId) || undefined;
    } else {
      // Find most recent result for same strategy
      const query: ResultQuery = {
        strategyId: currentResult.strategyId,
        status: 'completed',
        limit: 1,
        offset: 0
      };
      
      const { results } = await this.queryResults(query);
      previousResult = results.find(r => r.id !== currentResultId);
    }

    return this.calculateComparison(currentResult, previousResult);
  }

  /**
   * Calculate improvement metrics
   */
  private calculateComparison(
    current: OptimizationResult,
    previous?: OptimizationResult
  ): ResultComparison {
    const improvement = {
      sharpeRatio: 0,
      totalReturn: 0,
      maxDrawdown: 0,
      profitFactor: 0,
      overallScore: 0
    };

    const recommendations: string[] = [];

    if (previous) {
      // Calculate improvements
      improvement.sharpeRatio = ((current.bestPerformance.sharpeRatio - previous.bestPerformance.sharpeRatio) / Math.abs(previous.bestPerformance.sharpeRatio)) * 100;
      improvement.totalReturn = ((current.bestPerformance.totalReturn - previous.bestPerformance.totalReturn) / Math.abs(previous.bestPerformance.totalReturn)) * 100;
      improvement.maxDrawdown = ((previous.bestPerformance.maxDrawdown - current.bestPerformance.maxDrawdown) / Math.abs(previous.bestPerformance.maxDrawdown)) * 100; // Improvement = reduction in drawdown
      improvement.profitFactor = ((current.bestPerformance.profitFactor - previous.bestPerformance.profitFactor) / Math.abs(previous.bestPerformance.profitFactor)) * 100;
      
      // Calculate overall score
      improvement.overallScore = (
        improvement.sharpeRatio * 0.4 +
        improvement.totalReturn * 0.3 +
        improvement.maxDrawdown * 0.2 +
        improvement.profitFactor * 0.1
      );

      // Generate recommendations
      if (improvement.sharpeRatio < 5) {
        recommendations.push('Consider adjusting risk management parameters to improve risk-adjusted returns');
      }
      
      if (improvement.maxDrawdown < 10) {
        recommendations.push('Focus on drawdown reduction through position sizing or stop loss optimization');
      }
      
      if (improvement.totalReturn < 10) {
        recommendations.push('Explore different parameter ranges to improve overall profitability');
      }
    } else {
      recommendations.push('No previous results available for comparison');
    }

    const isSignificantImprovement = improvement.overallScore > 5; // 5% overall improvement threshold

    return {
      currentResult: current,
      previousResult: previous,
      improvement,
      isSignificantImprovement,
      recommendations
    };
  }

  // ===== Database Operations (Mock Implementation) =====

  /**
   * Save result to database
   */
  private async saveToDatabase(result: OptimizationResult): Promise<void> {
    // Mock database save
    console.log(`Saving optimization result ${result.id} to database`);
    
    // In real implementation, this would use Prisma/SQL:
    // await prisma.optimizationResult.create({ data: result });
  }

  /**
   * Load result from database
   */
  private async loadFromDatabase(resultId: string): Promise<OptimizationResult | null> {
    // Mock database load
    console.log(`Loading optimization result ${resultId} from database`);
    
    // In real implementation:
    // return await prisma.optimizationResult.findUnique({ where: { id: resultId } });
    
    return null; // Mock return
  }

  /**
   * Query database for results
   */
  private async queryDatabase(query: ResultQuery): Promise<{
    results: OptimizationResult[];
    totalCount: number;
  }> {
    // Mock database query
    console.log('Querying database with:', query);
    
    // In real implementation:
    // const results = await prisma.optimizationResult.findMany({
    //   where: {
    //     strategyId: query.strategyId,
    //     userId: query.userId,
    //     status: query.status,
    //     createdAt: {
    //       gte: query.startDate,
    //       lte: query.endDate
    //     }
    //   },
    //   take: query.limit,
    //   skip: query.offset,
    //   orderBy: { createdAt: 'desc' }
    // });
    
    return {
      results: [],
      totalCount: 0
    };
  }

  /**
   * Delete result from database
   */
  private async deleteFromDatabase(resultId: string): Promise<boolean> {
    // Mock database delete
    console.log(`Deleting optimization result ${resultId} from database`);
    
    // In real implementation:
    // const deleted = await prisma.optimizationResult.delete({ where: { id: resultId } });
    // return !!deleted;
    
    return true;
  }

  // ===== Utility Methods =====

  /**
   * Generate cache key
   */
  private generateCacheKey(key: CacheKey): string {
    return `${key.strategyId}:${key.parameterHash}:${key.configHash}`;
  }

  /**
   * Hash parameters for caching
   */
  private hashParameters(parameters: StrategyParameters): string {
    const sortedParams = Object.keys(parameters)
      .sort()
      .map(key => `${key}:${parameters[key]}`)
      .join('|');
    
    // Simple hash implementation (use crypto for production)
    let hash = 0;
    for (let i = 0; i < sortedParams.length; i++) {
      const char = sortedParams.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Hash configuration for caching
   */
  private hashConfig(config: any): string {
    const configString = JSON.stringify(config, Object.keys(config).sort());
    
    // Simple hash implementation
    let hash = 0;
    for (let i = 0; i < configString.length; i++) {
      const char = configString.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash;
    }
    
    return Math.abs(hash).toString(36);
  }

  /**
   * Check if cache entry is expired
   */
  private isCacheExpired(cached: OptimizationCache): boolean {
    return new Date() > cached.expiresAt;
  }

  /**
   * Evict oldest cache entries
   */
  private evictOldestCacheEntries(): void {
    const entries = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => a.lastAccessedAt.getTime() - b.lastAccessedAt.getTime());

    const toRemove = Math.ceil(this.options.maxCacheSize * 0.1); // Remove 10% of entries
    
    for (let i = 0; i < toRemove && entries.length > 0; i++) {
      const [key] = entries[i];
      this.cache.delete(key);
    }
  }

  /**
   * Clean up expired cache entries
   */
  private cleanupExpiredCache(): void {
    const now = new Date();
    
    for (const [key, cached] of this.cache.entries()) {
      if (now > cached.expiresAt) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Link result to previous results
   */
  private async linkToPreviousResults(result: OptimizationResult): Promise<void> {
    try {
      const query: ResultQuery = {
        strategyId: result.strategyId,
        status: 'completed',
        limit: 1,
        offset: 0
      };
      
      const { results } = await this.queryResults(query);
      const previousResult = results.find(r => r.id !== result.id);
      
      if (previousResult) {
        result.previousResultId = previousResult.id;
        await this.saveToDatabase(result);
      }
    } catch (error) {
      console.error('Failed to link to previous results:', error);
    }
  }

  // ===== Compression Methods =====

  /**
   * Compress optimization result
   */
  private async compressResult(result: OptimizationResult): Promise<OptimizationResult> {
    // Mock compression - in production use zlib or similar
    console.log(`Compressing result ${result.id}`);
    return result;
  }

  /**
   * Decompress optimization result
   */
  private async decompressResult(result: OptimizationResult): Promise<OptimizationResult> {
    // Mock decompression
    console.log(`Decompressing result ${result.id}`);
    return result;
  }

  // ===== Cleanup and Maintenance =====

  /**
   * Clean up old results based on retention policy
   */
  public async cleanupOldResults(): Promise<number> {
    const cutoffDate = new Date(Date.now() - this.options.retentionDays * 24 * 60 * 60 * 1000);
    
    const query: ResultQuery = {
      endDate: cutoffDate
    };

    const { results } = await this.queryResults(query);
    let deletedCount = 0;

    for (const result of results) {
      if (await this.deleteResult(result.id)) {
        deletedCount++;
      }
    }

    console.log(`Cleaned up ${deletedCount} old optimization results`);
    return deletedCount;
  }

  /**
   * Get storage statistics
   */
  public getStorageStats(): {
    cacheSize: number;
    cacheHitRate: number;
    totalCacheAccesses: number;
    averageAccessCount: number;
  } {
    const totalAccesses = Array.from(this.cache.values())
      .reduce((sum, cached) => sum + cached.accessCount, 0);
    
    const averageAccessCount = this.cache.size > 0 
      ? totalAccesses / this.cache.size 
      : 0;

    return {
      cacheSize: this.cache.size,
      cacheHitRate: 0, // Would be calculated from actual hit/miss counts
      totalCacheAccesses: totalAccesses,
      averageAccessCount
    };
  }

  /**
   * Export results for analysis
   */
  public async exportResults(query: ResultQuery): Promise<string> {
    const { results } = await this.queryResults(query);
    
    // Convert to CSV or JSON format
    const csvData = this.convertToCSV(results);
    
    return csvData;
  }

  /**
   * Convert results to CSV format
   */
  private convertToCSV(results: OptimizationResult[]): string {
    if (results.length === 0) return '';

    const headers = [
      'id',
      'strategyId',
      'userId',
      'status',
      'startTime',
      'endTime',
      'executionTimeSeconds',
      'sharpeRatio',
      'totalReturn',
      'maxDrawdown',
      'profitFactor',
      'robustnessScore'
    ];

    const rows = results.map(result => [
      result.id,
      result.strategyId,
      result.userId,
      result.status,
      result.startTime.toISOString(),
      result.endTime?.toISOString() || '',
      result.executionTimeSeconds,
      result.bestPerformance.sharpeRatio,
      result.bestPerformance.totalReturn,
      result.bestPerformance.maxDrawdown,
      result.bestPerformance.profitFactor,
      result.robustnessScore
    ]);

    return [headers, ...rows]
      .map(row => row.map(cell => `"${cell}"`).join(','))
      .join('\n');
  }
}