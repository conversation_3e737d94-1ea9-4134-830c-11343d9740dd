/**
 * Request Deduplication Service
 * 
 * Prevents duplicate trade modification requests and ensures idempotency
 * for critical trading operations.
 */

import { createHash } from 'crypto';
import Decimal from 'decimal.js';

export interface DeduplicationConfig {
  windowMs: number;           // Time window for deduplication in ms
  maxCacheSize: number;       // Maximum number of requests to cache
  cleanupIntervalMs: number;  // Cleanup interval in ms
}

export interface RequestMetadata {
  timestamp: Date;
  userId: string;
  tradeId: string;
  requestType: string;
  requestHash: string;
  parameters: any;
}

export interface DeduplicationResult {
  isDuplicate: boolean;
  originalRequestId?: string;
  originalTimestamp?: Date;
  requestId: string;
}

export class RequestDeduplicationService {
  private requestCache: Map<string, RequestMetadata> = new Map();
  private config: DeduplicationConfig;
  private cleanupTimer: NodeJS.Timeout;

  constructor(config: Partial<DeduplicationConfig> = {}) {
    this.config = {
      windowMs: 300000,        // 5 minutes
      maxCacheSize: 10000,     // 10k requests
      cleanupIntervalMs: 60000, // 1 minute cleanup
      ...config
    };

    // Start periodic cleanup
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupIntervalMs);
  }

  /**
   * Check if a trade modification request is a duplicate
   */
  checkTradeModificationDuplicate(
    userId: string,
    tradeId: string,
    modificationType: string,
    newValue: Decimal,
    reason: string
  ): DeduplicationResult {
    const requestType = `trade_modification_${modificationType}`;
    const parameters = {
      tradeId,
      modificationType,
      newValue: newValue.toString(),
      reason
    };

    return this.checkDuplicate(userId, requestType, parameters);
  }

  /**
   * Check if a trade execution request is a duplicate
   */
  checkTradeExecutionDuplicate(
    userId: string,
    strategyId: string,
    goalId: string,
    instrument: string,
    type: string,
    quantity: Decimal
  ): DeduplicationResult {
    const requestType = 'trade_execution';
    const parameters = {
      strategyId,
      goalId,
      instrument,
      type,
      quantity: quantity.toString()
    };

    return this.checkDuplicate(userId, requestType, parameters);
  }

  /**
   * Generic duplicate check for any request type
   */
  checkDuplicate(
    userId: string,
    requestType: string,
    parameters: any
  ): DeduplicationResult {
    const requestHash = this.generateRequestHash(userId, requestType, parameters);
    const requestId = `${requestType}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Check for existing request within the time window
    const existingRequest = this.findDuplicateRequest(requestHash, userId, requestType);

    if (existingRequest) {
      console.warn(`Duplicate request detected: ${requestType} for user ${userId}`);
      return {
        isDuplicate: true,
        originalRequestId: existingRequest.requestHash,
        originalTimestamp: existingRequest.timestamp,
        requestId
      };
    }

    // Store new request
    const metadata: RequestMetadata = {
      timestamp: new Date(),
      userId,
      tradeId: parameters.tradeId || 'N/A',
      requestType,
      requestHash,
      parameters
    };

    this.requestCache.set(requestId, metadata);
    
    // Enforce cache size limit
    if (this.requestCache.size > this.config.maxCacheSize) {
      this.enforceMaxCacheSize();
    }

    return {
      isDuplicate: false,
      requestId
    };
  }

  /**
   * Mark a request as completed (for cleanup purposes)
   */
  markCompleted(requestId: string): void {
    const request = this.requestCache.get(requestId);
    if (request) {
      // Add completion marker
      request.parameters._completed = true;
      request.parameters._completedAt = new Date();
    }
  }

  /**
   * Get deduplication statistics
   */
  getStats(): {
    totalRequests: number;
    activeRequests: number;
    completedRequests: number;
    duplicatesBlocked: number;
    oldestRequest?: Date;
    newestRequest?: Date;
  } {
    const requests = Array.from(this.requestCache.values());
    const completedRequests = requests.filter(r => r.parameters._completed);
    const activeRequests = requests.filter(r => !r.parameters._completed);

    const timestamps = requests.map(r => r.timestamp);
    const oldestRequest = timestamps.length > 0 ? new Date(Math.min(...timestamps.map(t => t.getTime()))) : undefined;
    const newestRequest = timestamps.length > 0 ? new Date(Math.max(...timestamps.map(t => t.getTime()))) : undefined;

    return {
      totalRequests: this.requestCache.size,
      activeRequests: activeRequests.length,
      completedRequests: completedRequests.length,
      duplicatesBlocked: 0, // Would need separate counter in production
      oldestRequest,
      newestRequest
    };
  }

  /**
   * Clear all cached requests
   */
  clear(): void {
    this.requestCache.clear();
    console.log('Request deduplication cache cleared');
  }

  /**
   * Shutdown the service
   */
  shutdown(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.requestCache.clear();
  }

  // === Private Methods ===

  private generateRequestHash(
    userId: string,
    requestType: string,
    parameters: any
  ): string {
    // Create deterministic hash from request parameters
    const normalizedParams = this.normalizeParameters(parameters);
    const hashInput = `${userId}:${requestType}:${JSON.stringify(normalizedParams)}`;
    
    return createHash('sha256')
      .update(hashInput)
      .digest('hex')
      .substr(0, 16); // Use first 16 characters for performance
  }

  private normalizeParameters(parameters: any): any {
    // Sort keys and normalize values for consistent hashing
    const normalized: any = {};
    const sortedKeys = Object.keys(parameters).sort();
    
    for (const key of sortedKeys) {
      const value = parameters[key];
      if (typeof value === 'string') {
        normalized[key] = value.trim().toLowerCase();
      } else if (value instanceof Decimal) {
        normalized[key] = value.toFixed();
      } else if (typeof value === 'number') {
        normalized[key] = value.toString();
      } else {
        normalized[key] = value;
      }
    }
    
    return normalized;
  }

  private findDuplicateRequest(
    requestHash: string,
    userId: string,
    requestType: string
  ): RequestMetadata | null {
    const cutoffTime = new Date(Date.now() - this.config.windowMs);
    
    for (const [requestId, metadata] of this.requestCache.entries()) {
      if (
        metadata.requestHash === requestHash &&
        metadata.userId === userId &&
        metadata.requestType === requestType &&
        metadata.timestamp > cutoffTime &&
        !metadata.parameters._completed
      ) {
        return metadata;
      }
    }
    
    return null;
  }

  private cleanup(): void {
    const cutoffTime = new Date(Date.now() - this.config.windowMs);
    const initialSize = this.requestCache.size;
    let removedCount = 0;

    for (const [requestId, metadata] of this.requestCache.entries()) {
      // Remove requests older than the window or completed requests older than 1 minute
      const isOld = metadata.timestamp < cutoffTime;
      const isCompletedAndOld = metadata.parameters._completed && 
        metadata.parameters._completedAt < new Date(Date.now() - 60000);

      if (isOld || isCompletedAndOld) {
        this.requestCache.delete(requestId);
        removedCount++;
      }
    }

    if (removedCount > 0) {
      console.log(`Cleaned up ${removedCount} old requests from deduplication cache (${initialSize} -> ${this.requestCache.size})`);
    }
  }

  private enforceMaxCacheSize(): void {
    if (this.requestCache.size <= this.config.maxCacheSize) return;

    // Remove oldest requests
    const requests = Array.from(this.requestCache.entries())
      .sort(([, a], [, b]) => a.timestamp.getTime() - b.timestamp.getTime());

    const toRemove = this.requestCache.size - this.config.maxCacheSize;
    for (let i = 0; i < toRemove; i++) {
      this.requestCache.delete(requests[i][0]);
    }

    console.log(`Enforced max cache size, removed ${toRemove} oldest requests`);
  }
}