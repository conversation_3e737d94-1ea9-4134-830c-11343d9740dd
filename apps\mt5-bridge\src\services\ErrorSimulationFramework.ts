import { EventEmitter } from 'events';

export interface ErrorSimulationConfig {
  enabled: boolean;
  globalErrorRate: number;
  networkErrorRate: number;
  brokerErrorRate: number;
  systemErrorRate: number;
  timeoutRate: number;
  errorBurstEnabled: boolean;
  burstIntensity: number;
  burstDuration: number;
  errorRecoveryTime: number;
  customErrorScenarios: ErrorScenario[];
}

export interface ErrorScenario {
  id: string;
  name: string;
  description: string;
  probability: number;
  duration: number;
  errorType: ErrorType;
  errorCode: number;
  errorMessage: string;
  conditions?: ErrorCondition[];
  recovery?: RecoveryStrategy;
}

export interface ErrorCondition {
  type: 'time' | 'symbol' | 'volume' | 'operation' | 'random';
  operator: 'equals' | 'greater' | 'less' | 'contains' | 'between';
  value: any;
  value2?: any; // For 'between' operator
}

export interface RecoveryStrategy {
  type: 'immediate' | 'gradual' | 'manual';
  successRate: number;
  retryDelay: number;
  maxRetries: number;
}

export enum ErrorType {
  NETWORK = 'network',
  BROKER = 'broker',
  SYSTEM = 'system',
  TIMEOUT = 'timeout',
  VALIDATION = 'validation',
  INSUFFICIENT_FUNDS = 'insufficient_funds',
  MARKET_CLOSED = 'market_closed',
  INVALID_SYMBOL = 'invalid_symbol',
  TRADING_DISABLED = 'trading_disabled',
  CONNECTION_LOST = 'connection_lost',
  SERVER_MAINTENANCE = 'server_maintenance'
}

export interface SimulatedError {
  id: string;
  type: ErrorType;
  code: number;
  message: string;
  timestamp: Date;
  context: Record<string, any>;
  scenarioId?: string;
  recovery?: RecoveryStrategy;
}

export interface ErrorEvent {
  error: SimulatedError;
  operation: string;
  symbol?: string;
  context: Record<string, any>;
}

export interface ErrorStatistics {
  totalErrors: number;
  errorsByType: Record<ErrorType, number>;
  errorsByCode: Record<number, number>;
  averageRecoveryTime: number;
  activeScenarios: string[];
  errorRate: number;
  burstEvents: number;
}

/**
 * Error Simulation Framework
 * Provides comprehensive error simulation for MT5 operations including
 * network failures, broker errors, timeouts, and custom scenarios
 */
export class ErrorSimulationFramework extends EventEmitter {
  private config: ErrorSimulationConfig;
  private errorHistory: SimulatedError[] = [];
  private activeScenarios: Map<string, { scenario: ErrorScenario; startTime: Date; endTime: Date }> = new Map();
  private burstModeActive: boolean = false;
  private burstStartTime: Date | null = null;
  private errorCounter: number = 0;
  private operationCounter: number = 0;
  private recoveryTimers: Map<string, NodeJS.Timeout> = new Map();

  constructor(config: Partial<ErrorSimulationConfig> = {}) {
    super();
    
    this.config = {
      enabled: true,
      globalErrorRate: 0.02, // 2% global error rate
      networkErrorRate: 0.01,
      brokerErrorRate: 0.005,
      systemErrorRate: 0.003,
      timeoutRate: 0.007,
      errorBurstEnabled: true,
      burstIntensity: 0.15, // 15% error rate during bursts
      burstDuration: 30000, // 30 seconds
      errorRecoveryTime: 5000, // 5 seconds
      customErrorScenarios: [],
      ...config
    };

    this.initializeDefaultScenarios();
    this.startErrorBurstSimulation();
  }

  /**
   * Initialize default error scenarios
   */
  private initializeDefaultScenarios(): void {
    const defaultScenarios: ErrorScenario[] = [
      {
        id: 'network_timeout',
        name: 'Network Timeout',
        description: 'Simulates network connectivity timeouts',
        probability: 0.01,
        duration: 5000,
        errorType: ErrorType.TIMEOUT,
        errorCode: 4014,
        errorMessage: 'Request timeout',
        recovery: {
          type: 'gradual',
          successRate: 0.8,
          retryDelay: 1000,
          maxRetries: 3
        }
      },
      {
        id: 'broker_maintenance',
        name: 'Broker Server Maintenance',
        description: 'Simulates broker server maintenance periods',
        probability: 0.002,
        duration: 60000, // 1 minute
        errorType: ErrorType.SERVER_MAINTENANCE,
        errorCode: 4051,
        errorMessage: 'Server is under maintenance',
        conditions: [
          {
            type: 'time',
            operator: 'between',
            value: '02:00',
            value2: '04:00'
          }
        ],
        recovery: {
          type: 'immediate',
          successRate: 1.0,
          retryDelay: 60000,
          maxRetries: 1
        }
      },
      {
        id: 'insufficient_margin',
        name: 'Insufficient Margin',
        description: 'Simulates insufficient margin errors for large orders',
        probability: 0.05,
        duration: 0,
        errorType: ErrorType.INSUFFICIENT_FUNDS,
        errorCode: 10019,
        errorMessage: 'Not enough money to execute order',
        conditions: [
          {
            type: 'volume',
            operator: 'greater',
            value: 10.0
          }
        ]
      },
      {
        id: 'market_closed',
        name: 'Market Closed',
        description: 'Simulates market closed errors during off-hours',
        probability: 0.8,
        duration: 0,
        errorType: ErrorType.MARKET_CLOSED,
        errorCode: 10018,
        errorMessage: 'Market is closed',
        conditions: [
          {
            type: 'time',
            operator: 'between',
            value: '22:00',
            value2: '06:00'
          }
        ]
      },
      {
        id: 'connection_lost',
        name: 'Connection Lost',
        description: 'Simulates sudden connection loss',
        probability: 0.003,
        duration: 10000,
        errorType: ErrorType.CONNECTION_LOST,
        errorCode: 4006,
        errorMessage: 'Connection to trading server lost',
        recovery: {
          type: 'gradual',
          successRate: 0.9,
          retryDelay: 2000,
          maxRetries: 5
        }
      },
      {
        id: 'invalid_symbol',
        name: 'Invalid Symbol',
        description: 'Simulates invalid symbol errors',
        probability: 0.01,
        duration: 0,
        errorType: ErrorType.INVALID_SYMBOL,
        errorCode: 10014,
        errorMessage: 'Invalid symbol',
        conditions: [
          {
            type: 'symbol',
            operator: 'contains',
            value: 'INVALID'
          }
        ]
      },
      {
        id: 'trading_disabled',
        name: 'Trading Disabled',
        description: 'Simulates trading disabled for specific symbols',
        probability: 0.001,
        duration: 30000,
        errorType: ErrorType.TRADING_DISABLED,
        errorCode: 10017,
        errorMessage: 'Trading is disabled for this symbol',
        recovery: {
          type: 'immediate',
          successRate: 1.0,
          retryDelay: 30000,
          maxRetries: 1
        }
      }
    ];

    this.config.customErrorScenarios = [
      ...this.config.customErrorScenarios,
      ...defaultScenarios
    ];
  }

  /**
   * Start error burst simulation
   */
  private startErrorBurstSimulation(): void {
    if (!this.config.errorBurstEnabled) return;

    // Trigger error bursts randomly
    setInterval(() => {
      if (!this.burstModeActive && Math.random() < 0.01) { // 1% chance every interval
        this.triggerErrorBurst();
      }
    }, 10000); // Check every 10 seconds
  }

  /**
   * Trigger an error burst
   */
  private triggerErrorBurst(): void {
    if (this.burstModeActive) return;

    this.burstModeActive = true;
    this.burstStartTime = new Date();

    console.log('🔥 Error burst simulation started');
    this.emit('burstStart', { intensity: this.config.burstIntensity, duration: this.config.burstDuration });

    // End burst after duration
    setTimeout(() => {
      this.burstModeActive = false;
      this.burstStartTime = null;
      console.log('✅ Error burst simulation ended');
      this.emit('burstEnd');
    }, this.config.burstDuration);
  }

  /**
   * Check if an operation should encounter an error
   */
  shouldSimulateError(operation: string, context: Record<string, any> = {}): SimulatedError | null {
    if (!this.config.enabled) return null;

    this.operationCounter++;

    // Check for active scenario errors first
    const scenarioError = this.checkActiveScenarios(operation, context);
    if (scenarioError) return scenarioError;

    // Check for new scenario triggers
    const triggeredScenario = this.checkScenarioTriggers(operation, context);
    if (triggeredScenario) return triggeredScenario;

    // Check for random errors based on configuration
    const randomError = this.checkRandomErrors(operation, context);
    if (randomError) return randomError;

    return null;
  }

  /**
   * Check active scenarios for errors
   */
  private checkActiveScenarios(operation: string, context: Record<string, any>): SimulatedError | null {
    for (const [scenarioId, activeScenario] of this.activeScenarios) {
      const now = new Date();
      
      // Check if scenario has expired
      if (now > activeScenario.endTime) {
        this.activeScenarios.delete(scenarioId);
        this.emit('scenarioEnd', { scenarioId, scenario: activeScenario.scenario });
        continue;
      }

      // Generate error for active scenario
      if (Math.random() < activeScenario.scenario.probability) {
        return this.createSimulatedError(activeScenario.scenario, operation, context);
      }
    }

    return null;
  }

  /**
   * Check if any scenarios should be triggered
   */
  private checkScenarioTriggers(operation: string, context: Record<string, any>): SimulatedError | null {
    for (const scenario of this.config.customErrorScenarios) {
      // Skip if already active
      if (this.activeScenarios.has(scenario.id)) continue;

      // Check conditions
      if (scenario.conditions && !this.evaluateConditions(scenario.conditions, context)) {
        continue;
      }

      // Check probability
      if (Math.random() > scenario.probability) continue;

      // Trigger scenario
      this.activateScenario(scenario);
      return this.createSimulatedError(scenario, operation, context);
    }

    return null;
  }

  /**
   * Check for random errors based on global rates
   */
  private checkRandomErrors(operation: string, context: Record<string, any>): SimulatedError | null {
    const effectiveErrorRate = this.burstModeActive ? 
      this.config.burstIntensity : 
      this.config.globalErrorRate;

    if (Math.random() > effectiveErrorRate) return null;

    // Determine error type based on individual rates
    const errorType = this.selectRandomErrorType();
    const errorCode = this.getErrorCodeForType(errorType);
    const errorMessage = this.getErrorMessageForType(errorType);

    return {
      id: this.generateErrorId(),
      type: errorType,
      code: errorCode,
      message: errorMessage,
      timestamp: new Date(),
      context
    };
  }

  /**
   * Activate a scenario
   */
  private activateScenario(scenario: ErrorScenario): void {
    const startTime = new Date();
    const endTime = new Date(startTime.getTime() + scenario.duration);

    this.activeScenarios.set(scenario.id, {
      scenario,
      startTime,
      endTime
    });

    console.log(`🚨 Activated error scenario: ${scenario.name}`);
    this.emit('scenarioStart', { scenarioId: scenario.id, scenario, startTime, endTime });
  }

  /**
   * Evaluate scenario conditions
   */
  private evaluateConditions(conditions: ErrorCondition[], context: Record<string, any>): boolean {
    return conditions.every(condition => this.evaluateCondition(condition, context));
  }

  /**
   * Evaluate a single condition
   */
  private evaluateCondition(condition: ErrorCondition, context: Record<string, any>): boolean {
    let contextValue: any;

    switch (condition.type) {
      case 'time':
        contextValue = new Date().toTimeString().slice(0, 5); // HH:MM format
        break;
      case 'symbol':
        contextValue = context.symbol || '';
        break;
      case 'volume':
        contextValue = context.volume || 0;
        break;
      case 'operation':
        contextValue = context.operation || '';
        break;
      case 'random':
        contextValue = Math.random();
        break;
      default:
        return false;
    }

    switch (condition.operator) {
      case 'equals':
        return contextValue === condition.value;
      case 'greater':
        return contextValue > condition.value;
      case 'less':
        return contextValue < condition.value;
      case 'contains':
        return String(contextValue).includes(String(condition.value));
      case 'between':
        return contextValue >= condition.value && contextValue <= condition.value2;
      default:
        return false;
    }
  }

  /**
   * Create a simulated error from a scenario
   */
  private createSimulatedError(
    scenario: ErrorScenario, 
    operation: string, 
    context: Record<string, any>
  ): SimulatedError {
    const error: SimulatedError = {
      id: this.generateErrorId(),
      type: scenario.errorType,
      code: scenario.errorCode,
      message: scenario.errorMessage,
      timestamp: new Date(),
      context: { ...context, operation },
      scenarioId: scenario.id,
      recovery: scenario.recovery
    };

    this.recordError(error);
    return error;
  }

  /**
   * Select random error type based on configured rates
   */
  private selectRandomErrorType(): ErrorType {
    const totalRate = this.config.networkErrorRate + 
                     this.config.brokerErrorRate + 
                     this.config.systemErrorRate + 
                     this.config.timeoutRate;

    const random = Math.random() * totalRate;
    let cumulative = 0;

    cumulative += this.config.networkErrorRate;
    if (random <= cumulative) return ErrorType.NETWORK;

    cumulative += this.config.brokerErrorRate;
    if (random <= cumulative) return ErrorType.BROKER;

    cumulative += this.config.systemErrorRate;
    if (random <= cumulative) return ErrorType.SYSTEM;

    return ErrorType.TIMEOUT;
  }

  /**
   * Get error code for error type
   */
  private getErrorCodeForType(errorType: ErrorType): number {
    const errorCodes: Record<ErrorType, number> = {
      [ErrorType.NETWORK]: 4001,
      [ErrorType.BROKER]: 4051,
      [ErrorType.SYSTEM]: 5001,
      [ErrorType.TIMEOUT]: 4014,
      [ErrorType.VALIDATION]: 10013,
      [ErrorType.INSUFFICIENT_FUNDS]: 10019,
      [ErrorType.MARKET_CLOSED]: 10018,
      [ErrorType.INVALID_SYMBOL]: 10014,
      [ErrorType.TRADING_DISABLED]: 10017,
      [ErrorType.CONNECTION_LOST]: 4006,
      [ErrorType.SERVER_MAINTENANCE]: 4051
    };

    return errorCodes[errorType] || 5000;
  }

  /**
   * Get error message for error type
   */
  private getErrorMessageForType(errorType: ErrorType): string {
    const errorMessages: Record<ErrorType, string> = {
      [ErrorType.NETWORK]: 'Network connection error',
      [ErrorType.BROKER]: 'Broker server error',
      [ErrorType.SYSTEM]: 'System error occurred',
      [ErrorType.TIMEOUT]: 'Operation timed out',
      [ErrorType.VALIDATION]: 'Invalid request parameters',
      [ErrorType.INSUFFICIENT_FUNDS]: 'Insufficient funds for operation',
      [ErrorType.MARKET_CLOSED]: 'Market is currently closed',
      [ErrorType.INVALID_SYMBOL]: 'Invalid or unknown symbol',
      [ErrorType.TRADING_DISABLED]: 'Trading is disabled',
      [ErrorType.CONNECTION_LOST]: 'Connection to server lost',
      [ErrorType.SERVER_MAINTENANCE]: 'Server maintenance in progress'
    };

    return errorMessages[errorType] || 'Unknown error occurred';
  }

  /**
   * Record an error in history
   */
  private recordError(error: SimulatedError): void {
    this.errorHistory.push(error);
    this.errorCounter++;

    // Emit error event
    this.emit('error', {
      error,
      operation: error.context.operation || 'unknown',
      symbol: error.context.symbol,
      context: error.context
    } as ErrorEvent);

    // Handle recovery if specified
    if (error.recovery) {
      this.scheduleRecovery(error);
    }

    console.log(`❌ Simulated error: ${error.type} - ${error.message}`);
  }

  /**
   * Schedule error recovery
   */
  private scheduleRecovery(error: SimulatedError): void {
    if (!error.recovery) return;

    const recovery = error.recovery;
    const timerId = setTimeout(() => {
      if (Math.random() < recovery.successRate) {
        console.log(`✅ Recovered from error: ${error.type}`);
        this.emit('recovery', { errorId: error.id, successful: true });
      } else if (recovery.maxRetries > 0) {
        // Schedule retry
        const retryError = { ...error, recovery: { ...recovery, maxRetries: recovery.maxRetries - 1 } };
        setTimeout(() => this.scheduleRecovery(retryError), recovery.retryDelay);
      } else {
        console.log(`❌ Failed to recover from error: ${error.type}`);
        this.emit('recovery', { errorId: error.id, successful: false });
      }
      
      this.recoveryTimers.delete(error.id);
    }, recovery.retryDelay);

    this.recoveryTimers.set(error.id, timerId);
  }

  /**
   * Generate unique error ID
   */
  private generateErrorId(): string {
    // Use performance.now() or a counter for better test compatibility
    let timestamp: number;
    if (typeof performance !== 'undefined' && performance.now) {
      timestamp = performance.now();
    } else {
      // Use a simple counter for fake timer environments
      timestamp = this.operationCounter * 1000 + Math.floor(Math.random() * 1000);
    }
    return `err_${Math.floor(timestamp)}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Add custom error scenario
   */
  addErrorScenario(scenario: ErrorScenario): void {
    this.config.customErrorScenarios.push(scenario);
  }

  /**
   * Remove error scenario
   */
  removeErrorScenario(scenarioId: string): boolean {
    const index = this.config.customErrorScenarios.findIndex(s => s.id === scenarioId);
    if (index >= 0) {
      this.config.customErrorScenarios.splice(index, 1);
      this.activeScenarios.delete(scenarioId);
      return true;
    }
    return false;
  }

  /**
   * Configure error simulation settings
   */
  configure(newConfig: Partial<ErrorSimulationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get error statistics
   */
  getStatistics(): ErrorStatistics {
    const errorsByType: Record<ErrorType, number> = {
      [ErrorType.NETWORK]: 0,
      [ErrorType.BROKER]: 0,
      [ErrorType.SYSTEM]: 0,
      [ErrorType.TIMEOUT]: 0,
      [ErrorType.VALIDATION]: 0,
      [ErrorType.INSUFFICIENT_FUNDS]: 0,
      [ErrorType.MARKET_CLOSED]: 0,
      [ErrorType.INVALID_SYMBOL]: 0,
      [ErrorType.TRADING_DISABLED]: 0,
      [ErrorType.CONNECTION_LOST]: 0,
      [ErrorType.SERVER_MAINTENANCE]: 0
    };

    const errorsByCode: Record<number, number> = {};

    this.errorHistory.forEach(error => {
      errorsByType[error.type]++;
      errorsByCode[error.code] = (errorsByCode[error.code] || 0) + 1;
    });

    const errorRate = this.operationCounter > 0 ? this.errorCounter / this.operationCounter : 0;

    return {
      totalErrors: this.errorCounter,
      errorsByType,
      errorsByCode,
      averageRecoveryTime: this.config.errorRecoveryTime,
      activeScenarios: Array.from(this.activeScenarios.keys()),
      errorRate,
      burstEvents: 0 // TODO: Track burst events
    };
  }

  /**
   * Reset error simulation state
   */
  reset(): void {
    this.errorHistory = [];
    this.activeScenarios.clear();
    this.errorCounter = 0;
    this.operationCounter = 0;
    this.burstModeActive = false;
    this.burstStartTime = null;
    
    // Clear recovery timers
    this.recoveryTimers.forEach(timer => clearTimeout(timer));
    this.recoveryTimers.clear();
  }

  /**
   * Enable/disable error simulation
   */
  setEnabled(enabled: boolean): void {
    this.config.enabled = enabled;
    if (!enabled) {
      this.reset();
    }
  }

  /**
   * Get current configuration
   */
  getConfig(): ErrorSimulationConfig {
    return { ...this.config };
  }
}