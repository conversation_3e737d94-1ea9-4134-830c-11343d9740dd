import express from 'express';
import cors from 'cors';
import helmet from 'helmet';

// Import routes
import auditRoutes from './routes/audit.js';
import monitoringRoutes from './routes/monitoring.js';
import interventionRoutes from './routes/intervention.js';
import confidenceRoutes from './routes/confidence/index.js';

const app = express();

// Middleware
app.use(helmet());
app.use(cors());
app.use(express.json());

// Basic health check for testing
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// API routes
app.use('/api/audit', auditRoutes);
app.use('/api/monitoring', monitoringRoutes);
app.use('/api/intervention', interventionRoutes);
app.use('/api/confidence', confidenceRoutes);

// API info endpoint
app.get('/api/v1', (req, res) => {
  res.json({
    message: 'GoldDaddy API v1 - Test Mode',
    endpoints: {
      audit: '/api/audit',
      monitoring: '/api/monitoring',
      intervention: '/api/intervention',
      confidence: '/api/confidence'
    }
  });
});

// Error handler
app.use((err: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({
    success: false,
    error: 'Internal Server Error',
    message: err.message,
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not Found',
    message: `Route ${req.originalUrl} not found`,
  });
});

export default app;