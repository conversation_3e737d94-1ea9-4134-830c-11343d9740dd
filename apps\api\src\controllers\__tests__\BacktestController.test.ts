import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { BacktestController } from '../BacktestController';

// Mock the services
vi.mock('../services/backtesting/ComprehensiveBacktestService');
vi.mock('../services/backtesting/BacktestAnalyticsService');
vi.mock('../services/backtesting/BacktestResultService');

const mockPrisma = {} as PrismaClient;

describe('BacktestController', () => {
  let controller: BacktestController;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    controller = new BacktestController(mockPrisma);
    
    mockRequest = {
      user: { id: 'user-123' },
      body: {},
      params: {},
      query: {},
    };

    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
      send: vi.fn().mockReturnThis(),
      setHeader: vi.fn().mockReturnThis(),
    };

    vi.clearAllMocks();
  });

  describe('runBacktest', () => {
    it('should require authentication', async () => {
      mockRequest.user = undefined;

      await controller.runBacktest(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Authentication required' });
    });

    it('should validate request body', async () => {
      mockRequest.body = {
        // Missing required fields
        strategyId: '',
        instruments: [],
      };

      await controller.runBacktest(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Validation failed',
          details: expect.any(Array),
        })
      );
    });

    it('should validate date range', async () => {
      mockRequest.body = {
        strategyId: 'strategy-1',
        instruments: ['EURUSD'],
        timeframes: ['H1'],
        startDate: '2023-01-02T00:00:00Z',
        endDate: '2023-01-01T00:00:00Z', // End before start
        initialCapital: 10000,
      };

      await controller.runBacktest(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'End date must be after start date' });
    });

    it('should validate maximum date range', async () => {
      const startDate = new Date('2000-01-01T00:00:00Z');
      const endDate = new Date('2025-01-01T00:00:00Z'); // More than 10 years

      mockRequest.body = {
        strategyId: 'strategy-1',
        instruments: ['EURUSD'],
        timeframes: ['H1'],
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        initialCapital: 10000,
      };

      await controller.runBacktest(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Date range cannot exceed 10 years' });
    });

    it('should run backtest successfully with valid data', async () => {
      const validRequest = {
        strategyId: 'strategy-1',
        instruments: ['EURUSD'],
        timeframes: ['H1'],
        startDate: '2023-01-01T00:00:00Z',
        endDate: '2023-01-05T00:00:00Z',
        initialCapital: 10000,
      };

      const mockBacktestResult = {
        id: 'backtest-123',
        overallMetrics: {
          totalReturn: 500,
          totalReturnPercentage: 5.0,
          sharpeRatio: 1.5,
          maxDrawdownPercentage: 8.0,
          winRate: 65,
          totalTrades: 25,
        },
        backtestDuration: 5000,
        timeframeAnalysis: [{
          timeframe: 'H1',
          metrics: {
            totalReturnPercentage: 5.0,
            sharpeRatio: 1.5,
          },
          trades: Array(25).fill({}),
        }],
        regimeAnalysis: [{
          regime: 'TRENDING',
          metrics: { totalReturnPercentage: 6.0 },
          durationPercentage: 60,
        }],
        mlInsights: ['Strategy performs well in trending markets'],
        coachingAdvice: ['Consider increasing position size during trending periods'],
      };

      // Mock the backtestService.runBacktest method
      vi.spyOn(controller as any, 'backtestService', 'get').mockReturnValue({
        runBacktest: vi.fn().mockResolvedValue(mockBacktestResult),
      });

      // Mock the resultService.storeResult method
      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        storeResult: vi.fn().mockResolvedValue('backtest-123'),
      });

      mockRequest.body = validRequest;

      await controller.runBacktest(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          backtestId: 'backtest-123',
          summary: expect.objectContaining({
            totalReturn: 500,
            totalReturnPercentage: 5.0,
            sharpeRatio: 1.5,
            maxDrawdown: 8.0,
            winRate: 65,
            totalTrades: 25,
          }),
          timeframes: expect.arrayContaining([
            expect.objectContaining({
              timeframe: 'H1',
              return: 5.0,
              sharpeRatio: 1.5,
              trades: 25,
            }),
          ]),
        }),
        message: 'Backtest completed successfully',
      });
    });

    it('should handle backtest service errors', async () => {
      const validRequest = {
        strategyId: 'strategy-1',
        instruments: ['EURUSD'],
        timeframes: ['H1'],
        startDate: '2023-01-01T00:00:00Z',
        endDate: '2023-01-05T00:00:00Z',
        initialCapital: 10000,
      };

      // Mock service to throw error
      vi.spyOn(controller as any, 'backtestService', 'get').mockReturnValue({
        runBacktest: vi.fn().mockRejectedValue(new Error('Strategy not found')),
      });

      mockRequest.body = validRequest;

      await controller.runBacktest(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        error: 'Backtesting failed',
        message: 'Strategy not found',
      });
    });
  });

  describe('getBacktestResult', () => {
    it('should require authentication', async () => {
      mockRequest.user = undefined;
      mockRequest.params = { id: 'backtest-123' };

      await controller.getBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Authentication required' });
    });

    it('should require backtest ID', async () => {
      mockRequest.params = {};

      await controller.getBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Backtest ID is required' });
    });

    it('should return 404 for non-existent backtest', async () => {
      mockRequest.params = { id: 'non-existent' };

      // Mock service to return null
      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        getResult: vi.fn().mockResolvedValue(null),
      });

      await controller.getBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Backtest result not found' });
    });

    it('should return backtest result successfully', async () => {
      const mockResult = {
        id: 'backtest-123',
        strategyId: 'strategy-1',
        userId: 'user-123',
        overallMetrics: {
          totalReturn: 500,
          sharpeRatio: 1.5,
        },
        trades: [],
        equityCurve: [],
      };

      mockRequest.params = { id: 'backtest-123' };
      mockRequest.query = {};

      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        getResult: vi.fn().mockResolvedValue(mockResult),
        storeAnalytics: vi.fn().mockResolvedValue(undefined),
      });

      await controller.getBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          id: 'backtest-123',
          strategyId: 'strategy-1',
          userId: 'user-123',
        }),
      });
    });

    it('should include analytics when requested', async () => {
      const mockResult = {
        id: 'backtest-123',
        overallMetrics: {},
        trades: [],
        equityCurve: [],
      };

      mockRequest.params = { id: 'backtest-123' };
      mockRequest.query = { includeAnalytics: 'true' };

      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        getResult: vi.fn().mockResolvedValue(mockResult),
        storeAnalytics: vi.fn().mockResolvedValue(undefined),
      });

      vi.spyOn(controller as any, 'analyticsService', 'get').mockReturnValue({
        analyzePerformanceAttribution: vi.fn().mockReturnValue({}),
        analyzeRisk: vi.fn().mockReturnValue({}),
        analyzeTradeDistribution: vi.fn().mockReturnValue({}),
        generateVisualizationData: vi.fn().mockReturnValue({}),
      });

      await controller.getBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          analytics: expect.objectContaining({
            performanceAttribution: {},
            riskAnalysis: {},
            tradeDistribution: {},
            visualization: {},
          }),
        }),
      });
    });
  });

  describe('getBacktestHistory', () => {
    it('should require authentication', async () => {
      mockRequest.user = undefined;

      await controller.getBacktestHistory(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Authentication required' });
    });

    it('should validate query parameters', async () => {
      mockRequest.query = {
        limit: 'invalid',
        sortBy: 'invalid_field',
      };

      await controller.getBacktestHistory(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Invalid search parameters',
          details: expect.any(Array),
        })
      );
    });

    it('should return backtest history successfully', async () => {
      const mockHistory = {
        results: [
          {
            id: 'backtest-1',
            name: 'Test Backtest 1',
            createdAt: new Date(),
            summary: {
              totalReturn: 500,
              totalReturnPercentage: 5.0,
              sharpeRatio: 1.5,
              maxDrawdown: 8.0,
              winRate: 65,
              totalTrades: 25,
              instruments: ['EURUSD'],
              dateRange: {
                start: new Date('2023-01-01'),
                end: new Date('2023-01-05'),
              },
            },
          },
        ],
        totalCount: 1,
      };

      mockRequest.query = {
        limit: '10',
        offset: '0',
        sortBy: 'createdAt',
        sortOrder: 'desc',
      };

      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        searchResults: vi.fn().mockResolvedValue(mockHistory),
      });

      await controller.getBacktestHistory(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockHistory.results,
        pagination: {
          totalCount: 1,
          limit: 10,
          offset: 0,
          hasMore: false,
        },
      });
    });
  });

  describe('exportBacktestResult', () => {
    it('should require authentication', async () => {
      mockRequest.user = undefined;

      await controller.exportBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Authentication required' });
    });

    it('should validate export request', async () => {
      mockRequest.body = {
        backtestId: '',
        format: 'invalid_format',
      };

      await controller.exportBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Invalid export request',
          details: expect.any(Array),
        })
      );
    });

    it('should export backtest result successfully', async () => {
      const mockExportResult = {
        filename: 'backtest_123_2023-01-01.json',
        contentType: 'application/json',
        data: Buffer.from('{"test": "data"}'),
        size: 16,
      };

      mockRequest.body = {
        backtestId: 'backtest-123',
        format: 'json',
        includeSections: {
          summary: true,
          trades: true,
        },
      };

      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        exportResult: vi.fn().mockResolvedValue(mockExportResult),
      });

      await controller.exportBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.setHeader).toHaveBeenCalledWith('Content-Type', 'application/json');
      expect(mockResponse.setHeader).toHaveBeenCalledWith(
        'Content-Disposition',
        'attachment; filename="backtest_123_2023-01-01.json"'
      );
      expect(mockResponse.setHeader).toHaveBeenCalledWith('Content-Length', '16');
      expect(mockResponse.send).toHaveBeenCalledWith(mockExportResult.data);
    });
  });

  describe('deleteBacktestResult', () => {
    it('should require authentication', async () => {
      mockRequest.user = undefined;
      mockRequest.params = { id: 'backtest-123' };

      await controller.deleteBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Authentication required' });
    });

    it('should require backtest ID', async () => {
      mockRequest.params = {};

      await controller.deleteBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Backtest ID is required' });
    });

    it('should delete backtest result successfully', async () => {
      mockRequest.params = { id: 'backtest-123' };

      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        deleteResult: vi.fn().mockResolvedValue(true),
      });

      await controller.deleteBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: 'Backtest result deleted successfully',
      });
    });

    it('should return 404 for non-existent backtest', async () => {
      mockRequest.params = { id: 'non-existent' };

      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        deleteResult: vi.fn().mockResolvedValue(false),
      });

      await controller.deleteBacktestResult(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Backtest result not found' });
    });
  });

  describe('getBacktestStatistics', () => {
    it('should require authentication', async () => {
      mockRequest.user = undefined;

      await controller.getBacktestStatistics(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Authentication required' });
    });

    it('should return statistics successfully', async () => {
      const mockStatistics = {
        totalBacktests: 15,
        avgReturn: 6.5,
        avgSharpeRatio: 1.3,
        bestBacktest: { id: 'backtest-best', return: 12.5 },
        worstBacktest: { id: 'backtest-worst', return: -2.1 },
        recentActivity: [
          { date: new Date(), backtestCount: 3 },
        ],
      };

      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        getStatistics: vi.fn().mockResolvedValue(mockStatistics),
      });

      await controller.getBacktestStatistics(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockStatistics,
      });
    });
  });

  describe('compareBacktests', () => {
    it('should require authentication', async () => {
      mockRequest.user = undefined;

      await controller.compareBacktests(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(401);
      expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Authentication required' });
    });

    it('should require at least 2 backtest IDs', async () => {
      mockRequest.body = { backtestIds: ['backtest-1'] };

      await controller.compareBacktests(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'At least 2 backtest IDs are required for comparison',
      });
    });

    it('should limit comparison to 10 backtests', async () => {
      const manyBacktestIds = Array.from({ length: 11 }, (_, i) => `backtest-${i}`);
      mockRequest.body = { backtestIds: manyBacktestIds };

      await controller.compareBacktests(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Cannot compare more than 10 backtests at once',
      });
    });

    it('should compare backtests successfully', async () => {
      const mockBacktests = [
        {
          id: 'backtest-1',
          strategyId: 'strategy-1',
          overallMetrics: {
            totalReturnPercentage: 5.0,
            sharpeRatio: 1.5,
            maxDrawdownPercentage: 8.0,
            winRate: 65,
            totalTrades: 25,
          },
        },
        {
          id: 'backtest-2',
          strategyId: 'strategy-2',
          overallMetrics: {
            totalReturnPercentage: 3.0,
            sharpeRatio: 1.2,
            maxDrawdownPercentage: 6.0,
            winRate: 58,
            totalTrades: 30,
          },
        },
      ];

      mockRequest.body = { backtestIds: ['backtest-1', 'backtest-2'] };

      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        getResult: vi.fn()
          .mockResolvedValueOnce(mockBacktests[0])
          .mockResolvedValueOnce(mockBacktests[1]),
      });

      await controller.compareBacktests(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: expect.objectContaining({
          summary: expect.arrayContaining([
            expect.objectContaining({
              backtestId: 'backtest-1',
              totalReturn: 5.0,
              sharpeRatio: 1.5,
            }),
            expect.objectContaining({
              backtestId: 'backtest-2',
              totalReturn: 3.0,
              sharpeRatio: 1.2,
            }),
          ]),
          bestPerforming: expect.objectContaining({
            byReturn: 'backtest-1',
            bySharpeRatio: 'backtest-1',
            byDrawdown: 'backtest-2',
          }),
          averages: expect.objectContaining({
            totalReturn: 4.0,
            sharpeRatio: 1.35,
            maxDrawdown: 7.0,
            winRate: 61.5,
          }),
        }),
      });
    });

    it('should handle insufficient valid results', async () => {
      mockRequest.body = { backtestIds: ['backtest-1', 'backtest-2'] };

      vi.spyOn(controller as any, 'resultService', 'get').mockReturnValue({
        getResult: vi.fn()
          .mockResolvedValueOnce(null)
          .mockResolvedValueOnce(null),
      });

      await controller.compareBacktests(mockRequest as Request, mockResponse as Response);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        error: 'Not enough valid backtest results found for comparison',
      });
    });
  });
});