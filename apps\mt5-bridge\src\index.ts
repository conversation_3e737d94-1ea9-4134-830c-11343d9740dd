import WebSocket from 'ws';
import { validateEnvVars } from '@golddaddy/config';
import type { MarketData, Trade } from '@golddaddy/types';
import { getPythonBridge, PythonBridge } from './python-bridge';

// Validate environment variables on startup
validateEnvVars();

const PORT = process.env.MT5_BRIDGE_PORT || 3002;

/**
 * MT5 Bridge Service
 * Handles communication between the GoldDaddy platform and MetaTrader 5
 */
class MT5Bridge {
  private wss: WebSocket.Server;
  private pythonBridge: PythonBridge;

  constructor() {
    this.wss = new WebSocket.Server({ port: Number(PORT) });
    this.pythonBridge = getPythonBridge();
    this.setupWebSocketServer();
    this.initializePythonBridge();
  }

  private setupWebSocketServer(): void {
    this.wss.on('connection', (ws: WebSocket) => {
      console.log('🔗 MT5 Client connected');

      ws.on('message', (message: string) => {
        try {
          const data = JSON.parse(message);
          this.handleMessage(ws, data);
        } catch (error) {
          console.error('❌ Invalid message format:', error);
          ws.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format',
          }));
        }
      });

      ws.on('close', () => {
        console.log('🔌 MT5 Client disconnected');
      });

      // Send welcome message
      ws.send(JSON.stringify({
        type: 'connected',
        message: 'Connected to GoldDaddy MT5 Bridge',
        timestamp: new Date().toISOString(),
      }));
    });

    console.log(`🌉 MT5 Bridge Server running on port ${PORT}`);
  }

  private async handleMessage(ws: WebSocket, data: unknown): Promise<void> {
    const messageData = data as { type?: string; payload?: unknown };
    
    try {
      switch (messageData.type) {
        case 'market_data':
          this.handleMarketData(messageData.payload as MarketData);
          break;
          
        case 'trade_update':
          this.handleTradeUpdate(messageData.payload as Trade);
          break;
          
        case 'ping':
          ws.send(JSON.stringify({ type: 'pong', timestamp: new Date().toISOString() }));
          break;
          
        case 'get_mt5_status':
          await this.handleGetMT5Status(ws);
          break;
          
        case 'get_symbols':
          await this.handleGetSymbols(ws);
          break;
          
        case 'subscribe_symbol':
          await this.handleSubscribeSymbol(ws, messageData.payload as { symbol: string });
          break;
          
        case 'unsubscribe_symbol':
          await this.handleUnsubscribeSymbol(ws, messageData.payload as { symbol: string });
          break;
          
        case 'execute_trade':
          await this.handleExecuteTrade(ws, messageData.payload);
          break;
          
        default:
          console.warn('⚠️ Unknown message type:', messageData.type);
          ws.send(JSON.stringify({
            type: 'error',
            message: `Unknown message type: ${messageData.type}`,
          }));
      }
    } catch (error) {
      console.error('Error handling message:', error);
      ws.send(JSON.stringify({
        type: 'error',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      }));
    }
  }

  private async handleGetMT5Status(ws: WebSocket): Promise<void> {
    try {
      const status = await this.pythonBridge.getMT5Status();
      ws.send(JSON.stringify({
        type: 'mt5_status_response',
        data: status
      }));
    } catch (error) {
      ws.send(JSON.stringify({
        type: 'error',
        message: `Failed to get MT5 status: ${error}`
      }));
    }
  }

  private async handleGetSymbols(ws: WebSocket): Promise<void> {
    try {
      const symbols = await this.pythonBridge.getSymbols();
      ws.send(JSON.stringify({
        type: 'symbols_response',
        data: symbols
      }));
    } catch (error) {
      ws.send(JSON.stringify({
        type: 'error',
        message: `Failed to get symbols: ${error}`
      }));
    }
  }

  private async handleSubscribeSymbol(ws: WebSocket, payload: { symbol: string }): Promise<void> {
    try {
      const success = this.pythonBridge.subscribeToSymbol(payload.symbol);
      ws.send(JSON.stringify({
        type: 'subscribe_response',
        symbol: payload.symbol,
        success
      }));
    } catch (error) {
      ws.send(JSON.stringify({
        type: 'error',
        message: `Failed to subscribe to ${payload.symbol}: ${error}`
      }));
    }
  }

  private async handleUnsubscribeSymbol(ws: WebSocket, payload: { symbol: string }): Promise<void> {
    try {
      const success = this.pythonBridge.unsubscribeFromSymbol(payload.symbol);
      ws.send(JSON.stringify({
        type: 'unsubscribe_response',
        symbol: payload.symbol,
        success
      }));
    } catch (error) {
      ws.send(JSON.stringify({
        type: 'error',
        message: `Failed to unsubscribe from ${payload.symbol}: ${error}`
      }));
    }
  }

  private async handleExecuteTrade(ws: WebSocket, payload: unknown): Promise<void> {
    try {
      const trade = await this.pythonBridge.executeTrade(payload);
      ws.send(JSON.stringify({
        type: 'trade_response',
        data: trade
      }));
    } catch (error) {
      ws.send(JSON.stringify({
        type: 'error',
        message: `Failed to execute trade: ${error}`
      }));
    }
  }

  private async initializePythonBridge(): Promise<void> {
    try {
      console.log('🐍 Initializing Python bridge...');
      
      // Setup event listeners for Python bridge
      this.pythonBridge.on('connected', () => {
        console.log('✅ Python bridge connected');
        this.broadcast({ type: 'python_status', status: 'connected' });
      });

      this.pythonBridge.on('disconnected', () => {
        console.log('🔌 Python bridge disconnected');
        this.broadcast({ type: 'python_status', status: 'disconnected' });
      });

      this.pythonBridge.on('marketData', (data: MarketData) => {
        this.handleMarketData(data);
      });

      this.pythonBridge.on('tradeUpdate', (data: Trade) => {
        this.handleTradeUpdate(data);
      });

      this.pythonBridge.on('error', (error: Error) => {
        console.error('Python bridge error:', error);
        this.broadcast({ type: 'error', message: error.message });
      });

      // Initialize the Python bridge
      const initialized = await this.pythonBridge.initialize();
      if (!initialized) {
        console.warn('⚠️ Python bridge initialization failed - running in limited mode');
      }

    } catch (error) {
      console.error('❌ Failed to initialize Python bridge:', error);
    }
  }

  private handleMarketData(marketData: MarketData): void {
    console.log(`📈 Market Data: ${marketData.symbol} - Bid: ${marketData.bid}, Ask: ${marketData.ask}`);
    // Broadcast to all connected WebSocket clients
    this.broadcast({
      type: 'market_data',
      data: marketData
    });
  }

  private handleTradeUpdate(trade: Trade): void {
    console.log(`💰 Trade Update: ${trade.symbol} - ${trade.type} ${trade.volume} lots at ${trade.openPrice}`);
    // Broadcast to all connected WebSocket clients
    this.broadcast({
      type: 'trade_update',
      data: trade
    });
  }

  public broadcast(message: unknown): void {
    this.wss.clients.forEach((client) => {
      if (client.readyState === WebSocket.OPEN) {
        client.send(JSON.stringify(message));
      }
    });
  }
}

// Start the MT5 Bridge service
if (require.main === module) {
  new MT5Bridge();
}

export { MT5Bridge };