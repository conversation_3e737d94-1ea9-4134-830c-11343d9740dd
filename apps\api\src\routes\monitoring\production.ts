import express from 'express';
import { ProductionMonitoringIntegration } from '../../services/monitoring/ProductionMonitoringIntegration.js';

const router = express.Router();

// Production monitoring integration instance will be injected
let productionMonitoring: ProductionMonitoringIntegration;

/**
 * Set the production monitoring integration instance
 */
export const setProductionMonitoring = (integration: ProductionMonitoringIntegration) => {
  productionMonitoring = integration;
};

/**
 * Get current production metrics
 * GET /api/monitoring/production/metrics
 */
router.get('/metrics', async (req, res) => {
  try {
    if (!productionMonitoring) {
      return res.status(503).json({
        error: 'Production monitoring not initialized',
        message: 'Production monitoring integration is not available'
      });
    }
    
    const metrics = await productionMonitoring.getCurrentMetrics();
    
    // Format metrics for Prometheus if requested
    if (req.query.format === 'prometheus') {
      const prometheusMetrics = formatPrometheusMetrics(metrics);
      res.set('Content-Type', 'text/plain');
      return res.send(prometheusMetrics);
    }
    
    res.json({
      success: true,
      data: metrics,
      timestamp: new Date()
    });
    
  } catch (error) {
    console.error('[ProductionMonitoring] Failed to get metrics:', error);
    res.status(500).json({
      error: 'Failed to retrieve production metrics',
      message: error.message
    });
  }
});

/**
 * Get production monitoring status
 * GET /api/monitoring/production/status
 */
router.get('/status', (req, res) => {
  try {
    if (!productionMonitoring) {
      return res.status(503).json({
        error: 'Production monitoring not initialized',
        message: 'Production monitoring integration is not available'
      });
    }
    
    const status = productionMonitoring.getMonitoringStatus();
    
    res.json({
      success: true,
      data: {
        ...status,
        environment: process.env.NODE_ENV,
        version: process.env.npm_package_version || '1.0.0',
        deployment: {
          loadBalanced: true,
          instanceCount: process.env.INSTANCE_COUNT || '3',
          deploymentDate: process.env.DEPLOYMENT_DATE || new Date().toISOString()
        }
      }
    });
    
  } catch (error) {
    console.error('[ProductionMonitoring] Failed to get status:', error);
    res.status(500).json({
      error: 'Failed to retrieve monitoring status',
      message: error.message
    });
  }
});

/**
 * Get production monitoring statistics
 * GET /api/monitoring/production/statistics
 */
router.get('/statistics', async (req, res) => {
  try {
    if (!productionMonitoring) {
      return res.status(503).json({
        error: 'Production monitoring not initialized',
        message: 'Production monitoring integration is not available'
      });
    }
    
    const hours = parseInt(req.query.hours as string) || 24;
    const statistics = await productionMonitoring.getMonitoringStatistics(hours);
    
    res.json({
      success: true,
      data: statistics,
      timestamp: new Date()
    });
    
  } catch (error) {
    console.error('[ProductionMonitoring] Failed to get statistics:', error);
    res.status(500).json({
      error: 'Failed to retrieve monitoring statistics',
      message: error.message
    });
  }
});

/**
 * Get uptime and health status
 * GET /api/monitoring/production/uptime
 */
router.get('/uptime', async (req, res) => {
  try {
    if (!productionMonitoring) {
      return res.status(503).json({
        error: 'Production monitoring not initialized',
        message: 'Production monitoring integration is not available',
        uptime: process.uptime(),
        healthy: false
      });
    }
    
    const status = productionMonitoring.getMonitoringStatus();
    const metrics = await productionMonitoring.getCurrentMetrics();
    
    const healthStatus = {
      healthy: true,
      uptime: process.uptime(),
      instanceId: status.instanceId,
      lastUpdate: status.lastMetricsUpdate,
      checks: {
        monitoring: status.isActive,
        cpu: metrics.cpuUsage < 90,
        memory: metrics.memoryUsage < 90,
        disk: metrics.diskUsage < 95,
        responseTime: metrics.responseTime < 5000
      }
    };
    
    // Determine overall health
    healthStatus.healthy = Object.values(healthStatus.checks).every(check => check === true);
    
    const httpStatus = healthStatus.healthy ? 200 : 503;
    
    res.status(httpStatus).json({
      success: healthStatus.healthy,
      data: healthStatus,
      timestamp: new Date()
    });
    
  } catch (error) {
    console.error('[ProductionMonitoring] Failed to get uptime:', error);
    res.status(503).json({
      error: 'Health check failed',
      message: error.message,
      healthy: false,
      uptime: process.uptime(),
      timestamp: new Date()
    });
  }
});

/**
 * Get load balancer health check endpoint
 * GET /api/monitoring/production/health
 * 
 * This endpoint is used by Traefik load balancer for health checks
 */
router.get('/health', async (req, res) => {
  try {
    const basicHealth = {
      status: 'healthy',
      timestamp: new Date(),
      uptime: process.uptime(),
      instanceId: process.env.INSTANCE_ID || 'unknown',
      environment: process.env.NODE_ENV
    };
    
    // If production monitoring is available, include more detailed health
    if (productionMonitoring) {
      const status = productionMonitoring.getMonitoringStatus();
      const metrics = await productionMonitoring.getCurrentMetrics();
      
      // Check critical thresholds
      const isCritical = 
        metrics.cpuUsage > 95 || 
        metrics.memoryUsage > 95 || 
        metrics.diskUsage > 98 ||
        metrics.errorRate > 0.1;
      
      if (isCritical) {
        return res.status(503).json({
          ...basicHealth,
          status: 'unhealthy',
          reason: 'Critical resource thresholds exceeded',
          metrics: {
            cpuUsage: metrics.cpuUsage,
            memoryUsage: metrics.memoryUsage,
            diskUsage: metrics.diskUsage,
            errorRate: metrics.errorRate
          }
        });
      }
      
      basicHealth['monitoring'] = status.isActive;
      basicHealth['lastUpdate'] = status.lastMetricsUpdate;
    }
    
    res.json(basicHealth);
    
  } catch (error) {
    console.error('[ProductionMonitoring] Health check failed:', error);
    res.status(503).json({
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date(),
      uptime: process.uptime(),
      instanceId: process.env.INSTANCE_ID || 'unknown'
    });
  }
});

/**
 * Trigger manual health check of all endpoints
 * POST /api/monitoring/production/health-check
 */
router.post('/health-check', async (req, res) => {
  try {
    if (!productionMonitoring) {
      return res.status(503).json({
        error: 'Production monitoring not initialized',
        message: 'Production monitoring integration is not available'
      });
    }
    
    // Trigger manual health check by emitting event
    productionMonitoring.emit('manualHealthCheck', {
      triggeredBy: req.ip,
      timestamp: new Date()
    });
    
    // Get current statistics to return
    const statistics = await productionMonitoring.getMonitoringStatistics(1); // Last hour
    
    res.json({
      success: true,
      message: 'Manual health check triggered',
      data: {
        healthCheckResults: statistics.healthChecks,
        lastUpdate: new Date()
      }
    });
    
  } catch (error) {
    console.error('[ProductionMonitoring] Manual health check failed:', error);
    res.status(500).json({
      error: 'Manual health check failed',
      message: error.message
    });
  }
});

/**
 * Get monitoring alerts
 * GET /api/monitoring/production/alerts
 */
router.get('/alerts', async (req, res) => {
  try {
    if (!productionMonitoring) {
      return res.status(503).json({
        error: 'Production monitoring not initialized',
        message: 'Production monitoring integration is not available'
      });
    }
    
    const hours = parseInt(req.query.hours as string) || 24;
    const severity = req.query.severity as string;
    
    const statistics = await productionMonitoring.getMonitoringStatistics(hours);
    
    let alerts = statistics.alerts;
    
    // Filter by severity if specified
    if (severity) {
      alerts = {
        ...alerts,
        bySevenity: { [severity]: alerts.bySevenity[severity] || 0 }
      };
    }
    
    res.json({
      success: true,
      data: {
        period: `${hours} hours`,
        alerts,
        summary: {
          totalAlerts: alerts.total,
          criticalAlerts: alerts.bySevenity.critical || 0,
          highAlerts: alerts.bySevenity.high || 0,
          mediumAlerts: alerts.bySevenity.medium || 0,
          lowAlerts: alerts.bySevenity.low || 0
        }
      }
    });
    
  } catch (error) {
    console.error('[ProductionMonitoring] Failed to get alerts:', error);
    res.status(500).json({
      error: 'Failed to retrieve alerts',
      message: error.message
    });
  }
});

/**
 * Format metrics for Prometheus exposition format
 */
function formatPrometheusMetrics(metrics: any): string {
  const instanceId = metrics.instanceId || 'unknown';
  const timestamp = Math.floor(metrics.timestamp.getTime() / 1000);
  
  return `
# HELP golddaddy_cpu_usage_percent CPU usage percentage
# TYPE golddaddy_cpu_usage_percent gauge
golddaddy_cpu_usage_percent{instance_id="${instanceId}"} ${metrics.cpuUsage} ${timestamp}

# HELP golddaddy_memory_usage_percent Memory usage percentage  
# TYPE golddaddy_memory_usage_percent gauge
golddaddy_memory_usage_percent{instance_id="${instanceId}"} ${metrics.memoryUsage} ${timestamp}

# HELP golddaddy_disk_usage_percent Disk usage percentage
# TYPE golddaddy_disk_usage_percent gauge
golddaddy_disk_usage_percent{instance_id="${instanceId}"} ${metrics.diskUsage} ${timestamp}

# HELP golddaddy_active_connections_total Active connections count
# TYPE golddaddy_active_connections_total gauge
golddaddy_active_connections_total{instance_id="${instanceId}"} ${metrics.activeConnections} ${timestamp}

# HELP golddaddy_requests_per_minute_total Requests per minute
# TYPE golddaddy_requests_per_minute_total gauge
golddaddy_requests_per_minute_total{instance_id="${instanceId}"} ${metrics.requestsPerMinute} ${timestamp}

# HELP golddaddy_error_rate_percent Error rate percentage
# TYPE golddaddy_error_rate_percent gauge
golddaddy_error_rate_percent{instance_id="${instanceId}"} ${metrics.errorRate * 100} ${timestamp}

# HELP golddaddy_response_time_ms Average response time in milliseconds
# TYPE golddaddy_response_time_ms gauge
golddaddy_response_time_ms{instance_id="${instanceId}"} ${metrics.responseTime} ${timestamp}

# HELP golddaddy_uptime_seconds Process uptime in seconds
# TYPE golddaddy_uptime_seconds gauge
golddaddy_uptime_seconds{instance_id="${instanceId}"} ${metrics.uptime} ${timestamp}
`.trim();
}

export default router;