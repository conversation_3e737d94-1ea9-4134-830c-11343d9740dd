/**
 * Trade Execution Engine Tests
 * 
 * Comprehensive unit tests for trade execution with performance optimization,
 * focusing on financial precision, latency monitoring, and error handling.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PrismaClient } from '@prisma/client';
import Decimal from 'decimal.js';
import { TradeExecutionEngine } from '../TradeExecutionEngine.js';
import { BrokerFailoverEngine } from '../BrokerFailoverEngine.js';
import type { LiveTradeRequest, ExecutionStatus } from '@golddaddy/types';

// Mock Prisma Client
const mockPrisma = {
  tradeExecution: {
    create: vi.fn(),
    update: vi.fn(),
    findMany: vi.fn()
  },
  brokerConfiguration: {
    findMany: vi.fn(),
    findUnique: vi.fn()
  }
} as unknown as PrismaClient;

// Mock BrokerFailoverEngine
const mockBrokerFailover = {
  on: vi.fn(),
  evaluateFailoverDecision: vi.fn()
} as unknown as BrokerFailoverEngine;

// Mock AuditTrailService
const mockAuditService = {
  logEvent: vi.fn().mockResolvedValue('audit_123')
};

// Mock BrokerConfigurationService
const mockBrokerConfigService = {
  getBrokersForTrading: vi.fn(),
  getAllBrokers: vi.fn(),
  getBrokerConfiguration: vi.fn()
};

describe('TradeExecutionEngine', () => {
  let executionEngine: TradeExecutionEngine;
  let mockTradeRequest: LiveTradeRequest;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Initialize execution engine with test configuration
    executionEngine = new TradeExecutionEngine(
      mockPrisma,
      mockBrokerFailover,
      {
        maxRetryAttempts: 2,
        retryBackoffMs: 100,
        maxLatencyThresholdMs: 1000,
        slippageTolerancePercent: new Decimal('0.5'),
        qualityScoreThreshold: 75,
        enableBrokerFailover: true,
        executionTimeoutMs: 5000
      }
    );

    // Mock the services
    executionEngine['auditService'] = mockAuditService as any;
    executionEngine['brokerConfigService'] = mockBrokerConfigService as any;

    // Mock trade request
    mockTradeRequest = {
      strategyId: 'strategy_123',
      goalId: 'goal_456',
      instrument: 'EURUSD',
      type: 'buy',
      quantity: new Decimal('10000'), // 0.1 lot
      stopLoss: new Decimal('1.0800'),
      takeProfit: new Decimal('1.0900'),
      confirmRisk: true,
      urgency: 'normal',
      maxSlippagePercent: new Decimal('0.5')
    };

    // Setup default mocks
    setupDefaultMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize successfully with default configuration', async () => {
      // Mock broker configuration service
      vi.spyOn(executionEngine as any, 'initializeExecutionMetrics').mockResolvedValue(undefined);
      
      await executionEngine.initialize();
      
      expect(executionEngine['isInitialized']).toBe(true);
    });

    it('should not initialize twice', async () => {
      vi.spyOn(executionEngine as any, 'initializeExecutionMetrics').mockResolvedValue(undefined);
      
      await executionEngine.initialize();
      await executionEngine.initialize(); // Second call
      
      // Should only call initializeExecutionMetrics once
      expect(executionEngine['initializeExecutionMetrics']).toHaveBeenCalledTimes(1);
    });
  });

  describe('Trade Execution', () => {
    beforeEach(async () => {
      vi.spyOn(executionEngine as any, 'initializeExecutionMetrics').mockResolvedValue(undefined);
      await executionEngine.initialize();
    });

    it('should execute trade successfully with valid request', async () => {
      // Mock successful execution flow
      const mockExecution = {
        id: 'exec_123',
        tradeId: 'trade_123',
        brokerId: 'broker_1',
        status: 'FILLED' as ExecutionStatus,
        executedPrice: new Decimal('1.0850'),
        slippage: new Decimal('0.0005'),
        latency: 150
      };

      mockPrisma.tradeExecution.create.mockResolvedValue(mockExecution);
      mockPrisma.tradeExecution.update.mockResolvedValue(mockExecution);

      vi.spyOn(executionEngine as any, 'validateTradeRequest').mockResolvedValue(undefined);
      vi.spyOn(executionEngine as any, 'selectOptimalBroker').mockResolvedValue({ id: 'broker_1' });
      vi.spyOn(executionEngine as any, 'executeTradeWithRetry').mockResolvedValue({
        brokerOrderId: 'order_123',
        executedPrice: new Decimal('1.0850'),
        slippage: new Decimal('0.0005')
      });

      // auditService is already mocked

      const result = await executionEngine.executeTrade(mockTradeRequest, 'user_123');

      expect(result).toBeDefined();
      expect(result.trade).toBeDefined();
      expect(result.execution.brokerOrderId).toBe('order_123');
      expect(result.compliance.riskChecksPass).toBe(true);
    });

    it('should validate financial precision in slippage calculations', async () => {
      const mockExecution = {
        id: 'exec_123',
        tradeId: 'trade_123',
        brokerId: 'broker_1',
        status: 'FILLED' as ExecutionStatus,
        executedPrice: new Decimal('1.0850125'), // Precise to 7 decimal places
        slippage: new Decimal('0.00051234'), // Precise slippage
        latency: 150
      };

      mockPrisma.tradeExecution.create.mockResolvedValue(mockExecution);
      mockPrisma.tradeExecution.update.mockResolvedValue(mockExecution);

      vi.spyOn(executionEngine as any, 'validateTradeRequest').mockResolvedValue(undefined);
      vi.spyOn(executionEngine as any, 'selectOptimalBroker').mockResolvedValue({ id: 'broker_1' });
      vi.spyOn(executionEngine as any, 'executeTradeWithRetry').mockResolvedValue({
        brokerOrderId: 'order_123',
        executedPrice: new Decimal('1.0850125'),
        slippage: new Decimal('0.00051234')
      });

      // auditService is already mocked

      const result = await executionEngine.executeTrade(mockTradeRequest, 'user_123');

      // Verify precision is maintained
      expect(result.execution.executionPrice.toFixed()).toBe('1.0850125');
      expect(result.execution.actualSlippage.toFixed()).toBe('0.00051234');
    });

    it('should handle execution timeout gracefully', async () => {
      vi.spyOn(executionEngine as any, 'validateTradeRequest').mockResolvedValue(undefined);
      vi.spyOn(executionEngine as any, 'selectOptimalBroker').mockResolvedValue({ id: 'broker_1' });
      vi.spyOn(executionEngine as any, 'executeTradeWithRetry').mockRejectedValue(new Error('Execution timeout'));

      // auditService is already mocked

      await expect(
        executionEngine.executeTrade(mockTradeRequest, 'user_123')
      ).rejects.toThrow();

      // Verify the failure audit log was called (the error might prevent the second call)
      expect(mockAuditService.logEvent).toHaveBeenCalled();
    });

    it('should enforce risk confirmation requirement', async () => {
      const invalidRequest = { ...mockTradeRequest, confirmRisk: false };

      await expect(
        executionEngine.executeTrade(invalidRequest, 'user_123')
      ).rejects.toThrow('Risk confirmation required for trade execution');
    });

    it('should validate positive trade quantity', async () => {
      const invalidRequest = { ...mockTradeRequest, quantity: new Decimal('-1000') };

      await expect(
        executionEngine.executeTrade(invalidRequest, 'user_123')
      ).rejects.toThrow('Trade quantity must be positive');
    });
  });

  describe('Execution Quality Calculation', () => {
    beforeEach(async () => {
      vi.spyOn(executionEngine as any, 'initializeExecutionMetrics').mockResolvedValue(undefined);
      await executionEngine.initialize();
    });

    it('should calculate quality score correctly for excellent execution', () => {
      const quality = executionEngine['calculateExecutionQuality'](
        new Decimal('0.0001'), // 1 basis point slippage
        50, // 50ms latency
        true // filled
      );

      expect(quality.score).toBeGreaterThanOrEqual(90);
      expect(quality.slippageScore).toBeGreaterThanOrEqual(95);
      expect(quality.latencyScore).toBeGreaterThanOrEqual(95);
      expect(quality.fillRateScore).toBe(100);
    });

    it('should penalize high slippage in quality score', () => {
      const lowSlippageQuality = executionEngine['calculateExecutionQuality'](
        new Decimal('0.0001'), // 1 basis point
        100,
        true
      );

      const highSlippageQuality = executionEngine['calculateExecutionQuality'](
        new Decimal('0.005'), // 50 basis points
        100,
        true
      );

      expect(lowSlippageQuality.score).toBeGreaterThan(highSlippageQuality.score);
      expect(lowSlippageQuality.slippageScore).toBeGreaterThan(highSlippageQuality.slippageScore);
    });

    it('should penalize high latency in quality score', () => {
      const lowLatencyQuality = executionEngine['calculateExecutionQuality'](
        new Decimal('0.0002'),
        50, // 50ms
        true
      );

      const highLatencyQuality = executionEngine['calculateExecutionQuality'](
        new Decimal('0.0002'),
        800, // 800ms
        true
      );

      expect(lowLatencyQuality.score).toBeGreaterThan(highLatencyQuality.score);
      expect(lowLatencyQuality.latencyScore).toBeGreaterThan(highLatencyQuality.latencyScore);
    });
  });

  describe('Retry Logic', () => {
    beforeEach(async () => {
      vi.spyOn(executionEngine as any, 'initializeExecutionMetrics').mockResolvedValue(undefined);
      await executionEngine.initialize();
    });

    it('should retry failed executions with exponential backoff', async () => {
      const mockUpdateStatus = vi.spyOn(executionEngine as any, 'updateExecutionStatus').mockResolvedValue(undefined);
      const mockSimulateExecution = vi.spyOn(executionEngine as any, 'simulateTradeExecution');
      
      // Mock circuit breaker to avoid fallback execution
      const mockCircuitBreaker = vi.spyOn(executionEngine['circuitBreaker'], 'execute');
      
      // First two calls fail, third succeeds
      mockSimulateExecution
        .mockRejectedValueOnce(new Error('Network timeout'))
        .mockRejectedValueOnce(new Error('Connection failed'))
        .mockResolvedValue({
          brokerOrderId: 'order_123',
          executedPrice: new Decimal('1.0850'),
          slippage: new Decimal('0.0005')
        });
      
      // Mock circuit breaker to work with the retry mechanism
      mockCircuitBreaker.mockImplementation(async (id, fn, fallback) => {
        // The circuit breaker should call the function directly
        // and let the retry logic in executeTradeWithRetry handle the failures
        return await fn();
      });

      const result = await executionEngine['executeTradeWithRetry'](
        'exec_123',
        'broker_1',
        mockTradeRequest,
        Date.now()
      );

      expect(result.brokerOrderId).toBe('order_123');
      expect(mockSimulateExecution).toHaveBeenCalledTimes(3);
      expect(mockUpdateStatus).toHaveBeenCalledWith('exec_123', 'EXECUTING');
    });

    it('should not retry non-retryable errors', async () => {
      const mockSimulateExecution = vi.spyOn(executionEngine as any, 'simulateTradeExecution');
      mockSimulateExecution.mockRejectedValue(new Error('Insufficient margin'));
      
      // Mock circuit breaker to avoid fallback execution  
      const mockCircuitBreaker = vi.spyOn(executionEngine['circuitBreaker'], 'execute');
      mockCircuitBreaker.mockImplementation(async (id, fn, fallback) => {
        return await fn();
      });

      const mockClassifyError = vi.spyOn(executionEngine as any, 'classifyExecutionError');
      mockClassifyError.mockReturnValue({
        code: 'INSUFFICIENT_MARGIN_123',
        message: 'Insufficient margin',
        category: 'INSUFFICIENT_MARGIN',
        retryable: false,
        brokerId: 'broker_1',
        timestamp: new Date(),
        details: {}
      });

      await expect(
        executionEngine['executeTradeWithRetry'](
          'exec_123',
          'broker_1',
          mockTradeRequest,
          Date.now()
        )
      ).rejects.toThrow('Insufficient margin');

      // Should only try once for non-retryable errors
      expect(mockSimulateExecution).toHaveBeenCalledTimes(1);
    });
  });

  describe('Broker Selection', () => {
    beforeEach(async () => {
      vi.spyOn(executionEngine as any, 'initializeExecutionMetrics').mockResolvedValue(undefined);
      await executionEngine.initialize();
    });

    it('should select broker with highest quality score', async () => {
      const mockBrokers = [
        { id: 'broker_1', name: 'Broker A' },
        { id: 'broker_2', name: 'Broker B' },
        { id: 'broker_3', name: 'Broker C' }
      ];

      // brokerConfigService is already mocked
      mockBrokerConfigService.getBrokersForTrading.mockResolvedValue(mockBrokers);

      // Setup metrics to favor broker_2
      executionEngine['executionMetrics'].set('broker_1', {
        totalExecutions: 100,
        successfulExecutions: 85,
        failedExecutions: 15,
        averageLatency: 200,
        averageSlippage: new Decimal('0.003'),
        successRate: new Decimal('0.85'),
        qualityScore: 75
      });

      executionEngine['executionMetrics'].set('broker_2', {
        totalExecutions: 120,
        successfulExecutions: 115,
        failedExecutions: 5,
        averageLatency: 100,
        averageSlippage: new Decimal('0.001'),
        successRate: new Decimal('0.96'),
        qualityScore: 95
      });

      executionEngine['executionMetrics'].set('broker_3', {
        totalExecutions: 80,
        successfulExecutions: 70,
        failedExecutions: 10,
        averageLatency: 300,
        averageSlippage: new Decimal('0.004'),
        successRate: new Decimal('0.875'),
        qualityScore: 70
      });

      const selectedBroker = await executionEngine['selectOptimalBroker']('user_123', mockTradeRequest);

      expect(selectedBroker.id).toBe('broker_2');
    });

    it('should handle no available brokers', async () => {
      // brokerConfigService is already mocked
      mockBrokerConfigService.getBrokersForTrading.mockResolvedValue([]);

      await expect(
        executionEngine['selectOptimalBroker']('user_123', mockTradeRequest)
      ).rejects.toThrow('No active brokers available for trading');
    });
  });

  describe('Execution Quality Monitoring', () => {
    beforeEach(async () => {
      vi.spyOn(executionEngine as any, 'initializeExecutionMetrics').mockResolvedValue(undefined);
      await executionEngine.initialize();
    });

    it('should trigger quality degradation alert when score falls below threshold', async () => {
      const mockEmit = vi.spyOn(executionEngine, 'emit');
      
      // Set up broker with poor quality score
      executionEngine['executionMetrics'].set('broker_1', {
        totalExecutions: 100,
        successfulExecutions: 60,
        failedExecutions: 40,
        averageLatency: 800,
        averageSlippage: new Decimal('0.01'),
        successRate: new Decimal('0.6'),
        qualityScore: 45 // Below threshold of 75
      });

      await executionEngine.monitorExecutionQuality();

      expect(mockEmit).toHaveBeenCalledWith('qualityDegradation', expect.objectContaining({
        brokerId: 'broker_1',
        currentScore: 45,
        threshold: 75
      }));
    });

    it('should trigger failover for severely degraded quality', async () => {
      const mockFailoverEvaluate = vi.spyOn(executionEngine['brokerFailover'], 'evaluateFailoverDecision');
      
      // Set up broker with very poor quality score
      executionEngine['executionMetrics'].set('broker_1', {
        totalExecutions: 100,
        successfulExecutions: 30,
        failedExecutions: 70,
        averageLatency: 1200,
        averageSlippage: new Decimal('0.02'),
        successRate: new Decimal('0.3'),
        qualityScore: 25 // Very poor quality
      });

      await executionEngine.monitorExecutionQuality();

      expect(mockFailoverEvaluate).toHaveBeenCalledWith(
        'broker_1',
        'PERFORMANCE_DEGRADATION',
        'Execution quality score fell to 25'
      );
    });
  });

  // Helper function to setup default mocks
  function setupDefaultMocks(): void {
    mockPrisma.brokerConfiguration.findMany.mockResolvedValue([
      { id: 'broker_1', name: 'Test Broker 1', status: 'ACTIVE' },
      { id: 'broker_2', name: 'Test Broker 2', status: 'ACTIVE' }
    ]);

    mockPrisma.tradeExecution.findMany.mockResolvedValue([]);
    
    // Setup broker service mocks
    mockBrokerConfigService.getBrokersForTrading.mockResolvedValue([
      { id: 'broker_1', name: 'Test Broker 1', status: 'ACTIVE' },
      { id: 'broker_2', name: 'Test Broker 2', status: 'ACTIVE' }
    ]);
    
    mockBrokerConfigService.getAllBrokers.mockResolvedValue([
      { id: 'broker_1', name: 'Test Broker 1', status: 'ACTIVE' },
      { id: 'broker_2', name: 'Test Broker 2', status: 'ACTIVE' }
    ]);
    
    mockBrokerConfigService.getBrokerConfiguration.mockResolvedValue({
      data: { id: 'broker_1', name: 'Test Broker 1' }
    });
  }
});