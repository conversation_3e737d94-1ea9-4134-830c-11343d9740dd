-- Migration: Create TimescaleDB hypertables for monitoring data
-- This migration optimizes time-series data storage for system metrics and user activity logs

-- Enable TimescaleDB extension (if not already enabled)
CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;

-- Convert system_metrics to hypertable
-- This optimizes storage and querying of time-series system metrics
SELECT create_hypertable('system_metrics', 'timestamp', 
  chunk_time_interval => INTERVAL '1 hour',
  if_not_exists => TRUE
);

-- Convert user_activity_log to hypertable  
-- This optimizes storage and querying of user activity time-series data
SELECT create_hypertable('user_activity_log', 'timestamp',
  chunk_time_interval => INTERVAL '1 day',
  if_not_exists => TRUE
);

-- Create indexes for optimal query performance on hypertables

-- System metrics indexes
CREATE INDEX IF NOT EXISTS ix_system_metrics_service_time 
  ON system_metrics (service_type, timestamp DESC);

CREATE INDEX IF NOT EXISTS ix_system_metrics_health_time 
  ON system_metrics (health_status, timestamp DESC);

-- User activity log indexes  
CREATE INDEX IF NOT EXISTS ix_user_activity_user_time 
  ON user_activity_log (user_id, timestamp DESC);

CREATE INDEX IF NOT EXISTS ix_user_activity_type_time 
  ON user_activity_log (activity_type, timestamp DESC);

CREATE INDEX IF NOT EXISTS ix_user_activity_anomaly_time 
  ON user_activity_log (anomaly_score DESC, timestamp DESC) 
  WHERE flagged_for_review = true;

CREATE INDEX IF NOT EXISTS ix_user_activity_session_time 
  ON user_activity_log (session_id, timestamp DESC);

-- Create compression policy for older data to save storage
-- Compress system metrics older than 7 days
SELECT add_compression_policy('system_metrics', INTERVAL '7 days');

-- Compress user activity logs older than 30 days
SELECT add_compression_policy('user_activity_log', INTERVAL '30 days');

-- Create data retention policy
-- Keep system metrics for 90 days (configurable based on requirements)
SELECT add_retention_policy('system_metrics', INTERVAL '90 days');

-- Keep user activity logs for 1 year for compliance
SELECT add_retention_policy('user_activity_log', INTERVAL '365 days');

-- Create continuous aggregates for common queries
-- Hourly system metrics aggregate for dashboards
CREATE MATERIALIZED VIEW IF NOT EXISTS system_metrics_hourly
WITH (timescaledb.continuous) AS
SELECT 
    service_type,
    time_bucket('1 hour', timestamp) AS hour,
    AVG((metrics->>'cpuUsage')::numeric) AS avg_cpu_usage,
    AVG((metrics->>'memoryUsage')::numeric) AS avg_memory_usage,
    AVG((metrics->>'responseTime')::numeric) AS avg_response_time,
    AVG((metrics->>'errorRate')::numeric) AS avg_error_rate,
    AVG((metrics->>'throughput')::numeric) AS avg_throughput,
    COUNT(*) AS metric_count,
    MAX((metrics->>'cpuUsage')::numeric) AS max_cpu_usage,
    MAX((metrics->>'memoryUsage')::numeric) AS max_memory_usage,
    MAX((metrics->>'responseTime')::numeric) AS max_response_time
FROM system_metrics
GROUP BY service_type, hour;

-- Daily user activity aggregate for analytics  
CREATE MATERIALIZED VIEW IF NOT EXISTS user_activity_daily
WITH (timescaledb.continuous) AS
SELECT 
    user_id,
    activity_type,
    time_bucket('1 day', timestamp) AS day,
    COUNT(*) AS activity_count,
    AVG(anomaly_score) AS avg_anomaly_score,
    COUNT(*) FILTER (WHERE flagged_for_review = true) AS flagged_count,
    COUNT(DISTINCT session_id) AS unique_sessions,
    MAX(anomaly_score) AS max_anomaly_score
FROM user_activity_log
GROUP BY user_id, activity_type, day;

-- Add refresh policies for continuous aggregates
-- Refresh hourly system metrics every 5 minutes
SELECT add_continuous_aggregate_policy('system_metrics_hourly',
  start_offset => INTERVAL '1 hour',
  end_offset => INTERVAL '5 minutes',
  schedule_interval => INTERVAL '5 minutes');

-- Refresh daily user activity every hour
SELECT add_continuous_aggregate_policy('user_activity_daily',
  start_offset => INTERVAL '1 day', 
  end_offset => INTERVAL '1 hour',
  schedule_interval => INTERVAL '1 hour');

-- Create alerts table compression and retention (if not using hypertable)
-- Note: Alerts are not converted to hypertable as they're not typically time-series queries

-- Create additional indexes for monitoring queries
CREATE INDEX IF NOT EXISTS ix_alerts_status_created 
  ON alerts (status, created_at DESC);

CREATE INDEX IF NOT EXISTS ix_alerts_severity_created 
  ON alerts (severity DESC, created_at DESC);

CREATE INDEX IF NOT EXISTS ix_alerts_category_created 
  ON alerts (category, created_at DESC);

CREATE INDEX IF NOT EXISTS ix_alerts_user_created 
  ON alerts (user_id, created_at DESC)
  WHERE user_id IS NOT NULL;

CREATE INDEX IF NOT EXISTS ix_alerts_correlation 
  ON alerts (correlation_key, created_at DESC)
  WHERE correlation_key IS NOT NULL;

-- Create support tickets indexes for quick lookups
CREATE INDEX IF NOT EXISTS ix_support_tickets_user_created 
  ON support_tickets (user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS ix_support_tickets_status_priority 
  ON support_tickets (status, priority DESC, created_at DESC);

CREATE INDEX IF NOT EXISTS ix_support_tickets_assigned_created 
  ON support_tickets (assigned_to, created_at DESC)
  WHERE assigned_to IS NOT NULL;

-- Add comments for documentation
COMMENT ON TABLE system_metrics IS 'TimescaleDB hypertable for system performance metrics with 1-hour chunks';
COMMENT ON TABLE user_activity_log IS 'TimescaleDB hypertable for user activity tracking with 1-day chunks';
COMMENT ON MATERIALIZED VIEW system_metrics_hourly IS 'Hourly aggregated system metrics for dashboard performance';
COMMENT ON MATERIALIZED VIEW user_activity_daily IS 'Daily aggregated user activity for analytics and reporting';