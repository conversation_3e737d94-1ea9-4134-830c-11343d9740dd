#!/usr/bin/env python3
"""
Live MT5 Connection Test
Test connection, data download, and streaming with actual MetaQuotes demo account
"""

import asyncio
import sys
import os
from pathlib import Path
from datetime import datetime, timedelta
import MetaTrader5 as mt5
from loguru import logger

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

# Import our modules
from mt5_connection import MT5Connection
from historical_collector import HistoricalDataCollector
from price_streamer import PriceStreamer
from database import TimescaleDBManager
from config import Config

# Configure logging for test
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <white>{message}</white>",
    level="INFO",
    colorize=True
)

class MT5LiveTester:
    """Live MT5 integration tester"""
    
    def __init__(self):
        self.mt5_connection = None
        self.test_results = {}
        
    async def run_all_tests(self):
        """Run comprehensive MT5 integration tests"""
        logger.info("🚀 Starting MT5 Live Integration Tests")
        logger.info("=" * 60)
        
        # Test 1: Basic MT5 Connection
        await self.test_mt5_connection()
        
        # Test 2: Account Information
        await self.test_account_info()
        
        # Test 3: Symbol Information
        await self.test_symbol_info()
        
        # Test 4: Historical Data Download
        await self.test_historical_data()
        
        # Test 5: Real-time Price Streaming Test
        await self.test_price_streaming()
        
        # Test 6: Database Integration (if available)
        await self.test_database_integration()
        
        # Summary
        await self.print_test_summary()
        
        return self.test_results
    
    async def test_mt5_connection(self):
        """Test basic MT5 connection"""
        logger.info("📡 Test 1: MT5 Connection")
        
        try:
            # Initialize MT5
            if not mt5.initialize():
                error_code = mt5.last_error()
                logger.error(f"❌ MT5 initialization failed: {error_code}")
                self.test_results['connection'] = {'success': False, 'error': f"Init failed: {error_code}"}
                return
            
            # Check if we're connected to a demo account
            account_info = mt5.account_info()
            if account_info is None:
                logger.error("❌ Failed to get account info")
                self.test_results['connection'] = {'success': False, 'error': "No account info"}
                return
            
            logger.info(f"✅ Connected to MT5!")
            logger.info(f"   Server: {account_info.server}")
            logger.info(f"   Account: {account_info.login}")
            logger.info(f"   Name: {account_info.name}")
            logger.info(f"   Company: {account_info.company}")
            logger.info(f"   Currency: {account_info.currency}")
            logger.info(f"   Balance: {account_info.balance}")
            logger.info(f"   Leverage: 1:{account_info.leverage}")
            
            # Test if it's a demo account
            if account_info.trade_mode == mt5.ACCOUNT_TRADE_MODE_DEMO:
                logger.info("✅ Demo account confirmed")
            else:
                logger.warning("⚠️ Not a demo account - be careful with live trading!")
            
            self.test_results['connection'] = {
                'success': True,
                'server': account_info.server,
                'account': account_info.login,
                'balance': account_info.balance,
                'is_demo': account_info.trade_mode == mt5.ACCOUNT_TRADE_MODE_DEMO
            }
            
        except Exception as e:
            logger.error(f"❌ Connection test failed: {e}")
            self.test_results['connection'] = {'success': False, 'error': str(e)}
    
    async def test_account_info(self):
        """Test account information retrieval"""
        logger.info("\n💰 Test 2: Account Information")
        
        try:
            account_info = mt5.account_info()
            if account_info is None:
                logger.error("❌ Failed to get account info")
                self.test_results['account_info'] = {'success': False}
                return
            
            # Display detailed account info
            account_dict = account_info._asdict()
            
            logger.info("✅ Account Information:")
            for key, value in account_dict.items():
                if key in ['balance', 'equity', 'margin', 'margin_free', 'margin_level']:
                    logger.info(f"   {key}: {value:.2f}")
                else:
                    logger.info(f"   {key}: {value}")
            
            self.test_results['account_info'] = {
                'success': True,
                'data': account_dict
            }
            
        except Exception as e:
            logger.error(f"❌ Account info test failed: {e}")
            self.test_results['account_info'] = {'success': False, 'error': str(e)}
    
    async def test_symbol_info(self):
        """Test symbol information and available instruments"""
        logger.info("\n📈 Test 3: Symbol Information")
        
        try:
            # Get all symbols
            symbols = mt5.symbols_get()
            if symbols is None:
                logger.error("❌ Failed to get symbols")
                self.test_results['symbols'] = {'success': False}
                return
            
            logger.info(f"✅ Found {len(symbols)} symbols")
            
            # Test major forex pairs
            major_pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']
            available_pairs = []
            
            for pair in major_pairs:
                symbol_info = mt5.symbol_info(pair)
                if symbol_info is not None:
                    available_pairs.append(pair)
                    logger.info(f"   {pair}: Spread {symbol_info.spread}, Digits {symbol_info.digits}")
            
            logger.info(f"✅ Available major pairs: {available_pairs}")
            
            # Test specific symbol details (EURUSD)
            eurusd = mt5.symbol_info("EURUSD")
            if eurusd:
                logger.info("✅ EURUSD Details:")
                logger.info(f"   Bid: {eurusd.bid}")
                logger.info(f"   Ask: {eurusd.ask}")
                logger.info(f"   Spread: {eurusd.spread}")
                logger.info(f"   Volume Min: {eurusd.volume_min}")
                logger.info(f"   Volume Max: {eurusd.volume_max}")
            
            self.test_results['symbols'] = {
                'success': True,
                'total_symbols': len(symbols),
                'major_pairs': available_pairs,
                'eurusd_available': eurusd is not None
            }
            
        except Exception as e:
            logger.error(f"❌ Symbol info test failed: {e}")
            self.test_results['symbols'] = {'success': False, 'error': str(e)}
    
    async def test_historical_data(self):
        """Test historical data download"""
        logger.info("\n📊 Test 4: Historical Data Download")
        
        try:
            # Test different timeframes
            timeframes = [
                (mt5.TIMEFRAME_M1, "1 minute"),
                (mt5.TIMEFRAME_M5, "5 minutes"), 
                (mt5.TIMEFRAME_H1, "1 hour"),
                (mt5.TIMEFRAME_D1, "1 day")
            ]
            
            symbol = "EURUSD"
            rates_data = {}
            
            for tf, tf_name in timeframes:
                logger.info(f"   Testing {tf_name} data for {symbol}...")
                
                # Get last 100 bars
                rates = mt5.copy_rates_from_pos(symbol, tf, 0, 100)
                
                if rates is not None and len(rates) > 0:
                    logger.info(f"   ✅ {tf_name}: {len(rates)} bars retrieved")
                    logger.info(f"      Latest: {datetime.fromtimestamp(rates[-1]['time'])}")
                    logger.info(f"      OHLC: {rates[-1]['open']:.5f} | {rates[-1]['high']:.5f} | {rates[-1]['low']:.5f} | {rates[-1]['close']:.5f}")
                    
                    rates_data[tf_name] = {
                        'count': len(rates),
                        'latest_time': datetime.fromtimestamp(rates[-1]['time']).isoformat(),
                        'latest_ohlc': {
                            'open': float(rates[-1]['open']),
                            'high': float(rates[-1]['high']),
                            'low': float(rates[-1]['low']),
                            'close': float(rates[-1]['close'])
                        }
                    }
                else:
                    logger.error(f"   ❌ {tf_name}: No data retrieved")
                    rates_data[tf_name] = {'count': 0, 'error': 'No data'}
            
            # Test date range request
            logger.info("   Testing date range request...")
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)  # Last 7 days
            
            rates_range = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_H1, start_date, end_date)
            
            if rates_range is not None and len(rates_range) > 0:
                logger.info(f"   ✅ Date range: {len(rates_range)} hourly bars for last 7 days")
                rates_data['date_range'] = {'count': len(rates_range)}
            else:
                logger.error("   ❌ Date range request failed")
                rates_data['date_range'] = {'count': 0, 'error': 'Failed'}
            
            self.test_results['historical_data'] = {
                'success': True,
                'symbol': symbol,
                'data': rates_data
            }
            
        except Exception as e:
            logger.error(f"❌ Historical data test failed: {e}")
            self.test_results['historical_data'] = {'success': False, 'error': str(e)}
    
    async def test_price_streaming(self):
        """Test real-time price streaming simulation"""
        logger.info("\n🔄 Test 5: Real-time Price Streaming")
        
        try:
            symbol = "EURUSD"
            logger.info(f"   Testing price updates for {symbol}...")
            
            # Simulate streaming by getting prices multiple times
            prices = []
            for i in range(5):
                tick = mt5.symbol_info_tick(symbol)
                if tick is not None:
                    prices.append({
                        'time': datetime.fromtimestamp(tick.time),
                        'bid': tick.bid,
                        'ask': tick.ask,
                        'spread': tick.ask - tick.bid
                    })
                    logger.info(f"   Tick {i+1}: {tick.bid:.5f} / {tick.ask:.5f} (spread: {(tick.ask - tick.bid):.5f})")
                else:
                    logger.error(f"   ❌ Failed to get tick {i+1}")
                
                # Small delay between requests
                await asyncio.sleep(0.5)
            
            if len(prices) > 0:
                logger.info(f"✅ Successfully retrieved {len(prices)} price updates")
                
                # Calculate some basic statistics
                avg_spread = sum(p['spread'] for p in prices) / len(prices)
                logger.info(f"   Average spread: {avg_spread:.5f}")
                
                self.test_results['price_streaming'] = {
                    'success': True,
                    'symbol': symbol,
                    'updates_count': len(prices),
                    'avg_spread': avg_spread,
                    'latest_price': {
                        'bid': prices[-1]['bid'],
                        'ask': prices[-1]['ask'],
                        'time': prices[-1]['time'].isoformat()
                    }
                }
            else:
                logger.error("❌ No price updates received")
                self.test_results['price_streaming'] = {'success': False, 'error': 'No updates'}
            
        except Exception as e:
            logger.error(f"❌ Price streaming test failed: {e}")
            self.test_results['price_streaming'] = {'success': False, 'error': str(e)}
    
    async def test_database_integration(self):
        """Test database integration (optional)"""
        logger.info("\n🗄️ Test 6: Database Integration (Optional)")
        
        try:
            # This is optional - only test if database is available
            logger.info("   Database integration test skipped (optional)")
            logger.info("   To test: Configure DATABASE_URL in environment")
            
            self.test_results['database'] = {
                'success': True,
                'message': 'Skipped - optional test'
            }
            
        except Exception as e:
            logger.error(f"❌ Database test failed: {e}")
            self.test_results['database'] = {'success': False, 'error': str(e)}
    
    async def print_test_summary(self):
        """Print comprehensive test summary"""
        logger.info("\n" + "=" * 60)
        logger.info("📋 TEST SUMMARY")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        logger.info("\nDetailed Results:")
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result.get('success', False) else "❌ FAIL"
            logger.info(f"  {test_name}: {status}")
            if not result.get('success', False) and 'error' in result:
                logger.info(f"    Error: {result['error']}")
        
        # MT5 Connection Summary
        if 'connection' in self.test_results and self.test_results['connection'].get('success'):
            conn_info = self.test_results['connection']
            logger.info(f"\n🔗 MT5 Connection Active:")
            logger.info(f"  Server: {conn_info.get('server', 'Unknown')}")
            logger.info(f"  Account: {conn_info.get('account', 'Unknown')}")
            logger.info(f"  Demo Account: {conn_info.get('is_demo', False)}")
        
        # Data Availability Summary
        if 'historical_data' in self.test_results and self.test_results['historical_data'].get('success'):
            logger.info(f"\n📊 Data Availability: ✅ CONFIRMED")
        
        if 'price_streaming' in self.test_results and self.test_results['price_streaming'].get('success'):
            logger.info(f"🔄 Price Streaming: ✅ CONFIRMED")
        
        logger.info("\n🎉 Live MT5 Integration Test Complete!")
    
    def __del__(self):
        """Cleanup MT5 connection"""
        try:
            mt5.shutdown()
        except:
            pass

async def main():
    """Main test runner"""
    tester = MT5LiveTester()
    
    try:
        results = await tester.run_all_tests()
        return results
    except KeyboardInterrupt:
        logger.info("\n⚠️ Test interrupted by user")
        return None
    except Exception as e:
        logger.error(f"\n❌ Test runner failed: {e}")
        return None
    finally:
        # Always cleanup
        try:
            mt5.shutdown()
        except:
            pass

if __name__ == "__main__":
    # Run the live integration test
    logger.info("Starting MT5 Live Integration Test...")
    logger.info("Make sure MT5 is running with your MetaQuotes demo account!")
    
    results = asyncio.run(main())
    
    if results:
        logger.info("\n✅ Test completed successfully!")
    else:
        logger.error("\n❌ Test failed or was interrupted")
        sys.exit(1)