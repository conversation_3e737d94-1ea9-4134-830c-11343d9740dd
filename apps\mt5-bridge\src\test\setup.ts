/**
 * Test setup configuration for MT5 Bridge
 */

import { vi } from 'vitest';

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.MT5_BRIDGE_PORT = '3002';
process.env.PYTHON_API_URL = 'http://localhost:8001';
process.env.PYTHON_WS_URL = 'ws://localhost:8002';

// Mock required database environment variables for config validation
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '5432';
process.env.DB_NAME = 'test_db';
process.env.DB_USER = 'test_user';
process.env.DB_PASSWORD = 'test_password';

// Mock @golddaddy/config module
vi.mock('@golddaddy/config', () => ({
  validateEnvVars: vi.fn(), // Mock the validation function to prevent errors
}));

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
  info: vi.fn(),
};

// Mock WebSocket global
global.WebSocket = vi.fn();

// Global test helpers
global.createMockWebSocket = () => ({
  send: vi.fn(),
  on: vi.fn(),
  close: vi.fn(),
  readyState: 1, // WebSocket.OPEN
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
});

// Clean up after each test
afterEach(() => {
  vi.clearAllMocks();
});