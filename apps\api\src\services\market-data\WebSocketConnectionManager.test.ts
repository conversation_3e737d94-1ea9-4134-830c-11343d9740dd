/**
 * Unit tests for WebSocketConnectionManager
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { WebSocket } from 'ws';
import { WebSocketConnectionManager, ConnectionConfig } from './WebSocketConnectionManager';

// Mock WebSocket
vi.mock('ws', () => ({
  WebSocket: vi.fn().mockImplementation(() => ({
    readyState: 1, // OPEN
    on: vi.fn(),
    close: vi.fn(),
    terminate: vi.fn(),
    send: vi.fn(),
    ping: vi.fn(),
  }))
}));

describe('WebSocketConnectionManager', () => {
  let manager: WebSocketConnectionManager;
  let mockWebSocket: any;

  beforeEach(() => {
    vi.clearAllMocks();
    manager = new WebSocketConnectionManager();
    
    // Create a mock WebSocket instance
    mockWebSocket = {
      readyState: WebSocket.OPEN,
      on: vi.fn(),
      close: vi.fn(),
      terminate: vi.fn(),
      send: vi.fn(),
      ping: vi.fn(),
    };

    (WebSocket as any).mockImplementation(() => mockWebSocket);
  });

  afterEach(() => {
    manager.shutdown();
  });

  describe('Connection Management', () => {
    it('should add connection configuration', () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
        connectionId: 'test-conn',
      };

      const connectionId = manager.addConnection(config);
      
      expect(connectionId).toBe('test-conn');
      
      const stats = manager.getConnectionStats(connectionId) as any;
      expect(stats.connectionId).toBe('test-conn');
      expect(stats.url).toBe('ws://test.example.com');
      expect(stats.status).toBe('disconnected');
    });

    it('should generate connection ID when not provided', () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
      };

      const connectionId = manager.addConnection(config);
      
      expect(connectionId).toMatch(/^conn_\d+_[a-z0-9]{9}$/);
    });

    it('should apply default configuration values', () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
      };

      const connectionId = manager.addConnection(config);
      
      // Test that defaults are applied by checking behavior
      expect(() => manager.getConnectionStats(connectionId)).not.toThrow();
    });
  });

  describe('Connection Establishment', () => {
    it('should throw error for non-existent connection ID', async () => {
      await expect(manager.connect('non-existent')).rejects.toThrow(
        'Connection config not found for ID: non-existent'
      );
    });

    it('should handle connection establishment successfully', async () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
        connectionId: 'test-conn',
        timeout: 1000,
      };

      const connectionId = manager.addConnection(config);
      
      // Mock successful connection
      mockWebSocket.on.mockImplementation((event: string, handler: Function) => {
        if (event === 'open') {
          setTimeout(() => handler(), 0);
        }
      });

      const connectPromise = manager.connect(connectionId);
      
      await expect(connectPromise).resolves.toBeUndefined();
    });

    it('should handle connection timeout', async () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
        connectionId: 'test-conn',
        timeout: 100, // Very short timeout
      };

      const connectionId = manager.addConnection(config);
      
      // Mock no response (timeout scenario)
      mockWebSocket.on.mockImplementation(() => {});

      await expect(manager.connect(connectionId)).rejects.toThrow('Connection timeout');
    });
  });

  describe('Message Handling', () => {
    let connectionId: string;
    let connectedMockWs: any;

    beforeEach(async () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
        connectionId: 'test-conn',
      };

      connectionId = manager.addConnection(config);
      
      // Create a fresh mock for each test
      connectedMockWs = {
        readyState: WebSocket.OPEN,
        on: vi.fn(),
        close: vi.fn(),
        terminate: vi.fn(),
        send: vi.fn(),
        ping: vi.fn(),
      };

      (WebSocket as any).mockImplementation(() => connectedMockWs);
      
      // Mock successful connection
      connectedMockWs.on.mockImplementation((event: string, handler: Function) => {
        if (event === 'open') {
          setTimeout(() => handler(), 0);
        }
      });

      await manager.connect(connectionId);
    });

    it('should send message successfully', () => {
      const message = { type: 'subscribe', symbol: 'EURUSD' };
      
      const result = manager.sendMessage(connectionId, message);
      
      expect(result).toBe(true);
      expect(connectedMockWs.send).toHaveBeenCalledWith(JSON.stringify(message));
    });

    it('should handle string messages', () => {
      const message = 'test message';
      
      const result = manager.sendMessage(connectionId, message);
      
      expect(result).toBe(true);
      expect(connectedMockWs.send).toHaveBeenCalledWith(message);
    });

    it('should return false for non-existent connection', () => {
      const result = manager.sendMessage('non-existent', 'test');
      
      expect(result).toBe(false);
    });

    it('should return false when WebSocket is not open', () => {
      connectedMockWs.readyState = 3; // WebSocket.CLOSED
      
      const result = manager.sendMessage(connectionId, 'test');
      
      expect(result).toBe(false);
    });
  });

  describe('Connection Health', () => {
    let connectionId: string;
    let healthMockWs: any;

    beforeEach(async () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
        connectionId: 'test-conn',
        heartbeatInterval: 1000,
      };

      connectionId = manager.addConnection(config);
      
      // Create a fresh mock for each test
      healthMockWs = {
        readyState: WebSocket.OPEN,
        on: vi.fn(),
        close: vi.fn(),
        terminate: vi.fn(),
        send: vi.fn(),
        ping: vi.fn(),
      };

      (WebSocket as any).mockImplementation(() => healthMockWs);
      
      // Mock successful connection
      healthMockWs.on.mockImplementation((event: string, handler: Function) => {
        if (event === 'open') {
          setTimeout(() => handler(), 0);
        }
      });

      await manager.connect(connectionId);
    });

    it('should report healthy connection when recently connected', () => {
      const isHealthy = manager.isConnectionHealthy(connectionId);
      expect(isHealthy).toBe(true);
    });

    it('should report unhealthy for disconnected connection', () => {
      // Simulate disconnection by updating stats directly
      const stats = manager.getConnectionStats(connectionId) as any;
      stats.status = 'disconnected';
      
      const isHealthy = manager.isConnectionHealthy(connectionId);
      expect(isHealthy).toBe(false);
    });

    it('should count healthy connections correctly', () => {
      const count = manager.getHealthyConnectionsCount();
      expect(count).toBe(1);
    });
  });

  describe('Connection Statistics', () => {
    it('should provide connection statistics', () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
        connectionId: 'test-conn',
      };

      const connectionId = manager.addConnection(config);
      
      const stats = manager.getConnectionStats(connectionId) as any;
      
      expect(stats).toMatchObject({
        connectionId: 'test-conn',
        url: 'ws://test.example.com',
        status: 'disconnected',
        reconnectAttempts: 0,
        messagesSent: 0,
        messagesReceived: 0,
      });
    });

    it('should provide all connection statistics', () => {
      const config1: ConnectionConfig = { url: 'ws://test1.com', connectionId: 'conn1' };
      const config2: ConnectionConfig = { url: 'ws://test2.com', connectionId: 'conn2' };

      manager.addConnection(config1);
      manager.addConnection(config2);
      
      const allStats = manager.getConnectionStats() as any[];
      
      expect(allStats).toHaveLength(2);
      expect(allStats[0].connectionId).toBe('conn1');
      expect(allStats[1].connectionId).toBe('conn2');
    });

    it('should throw error for non-existent connection stats', () => {
      expect(() => manager.getConnectionStats('non-existent')).toThrow(
        'Connection stats not found for ID: non-existent'
      );
    });
  });

  describe('Connection Cleanup', () => {
    it('should disconnect specific connection', async () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
        connectionId: 'test-conn',
      };

      const connectionId = manager.addConnection(config);
      
      // Create mock for this test
      const cleanupMockWs = {
        readyState: WebSocket.OPEN,
        on: vi.fn(),
        close: vi.fn(),
        terminate: vi.fn(),
        send: vi.fn(),
        ping: vi.fn(),
      };

      (WebSocket as any).mockImplementation(() => cleanupMockWs);
      
      // Mock successful connection
      cleanupMockWs.on.mockImplementation((event: string, handler: Function) => {
        if (event === 'open') {
          setTimeout(() => handler(), 0);
        }
      });

      await manager.connect(connectionId);
      manager.disconnect(connectionId);
      
      expect(cleanupMockWs.close).toHaveBeenCalledWith(1000, 'Normal closure');
    });

    it('should disconnect all connections', async () => {
      const config1: ConnectionConfig = { url: 'ws://test1.com', connectionId: 'conn1' };
      const config2: ConnectionConfig = { url: 'ws://test2.com', connectionId: 'conn2' };

      const connId1 = manager.addConnection(config1);
      const connId2 = manager.addConnection(config2);
      
      // Create mocks for both connections
      const mockWs1 = {
        readyState: WebSocket.OPEN,
        on: vi.fn(),
        close: vi.fn(),
        terminate: vi.fn(),
        send: vi.fn(),
        ping: vi.fn(),
      };
      
      const mockWs2 = {
        readyState: WebSocket.OPEN,
        on: vi.fn(),
        close: vi.fn(),
        terminate: vi.fn(),
        send: vi.fn(),
        ping: vi.fn(),
      };

      let callCount = 0;
      (WebSocket as any).mockImplementation(() => {
        callCount++;
        return callCount === 1 ? mockWs1 : mockWs2;
      });
      
      // Mock successful connections
      mockWs1.on.mockImplementation((event: string, handler: Function) => {
        if (event === 'open') setTimeout(() => handler(), 0);
      });
      mockWs2.on.mockImplementation((event: string, handler: Function) => {
        if (event === 'open') setTimeout(() => handler(), 0);
      });

      await manager.connect(connId1);
      await manager.connect(connId2);
      
      manager.disconnectAll();
      
      expect(mockWs1.close).toHaveBeenCalledTimes(1);
      expect(mockWs2.close).toHaveBeenCalledTimes(1);
    });

    it('should remove connection configuration', () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
        connectionId: 'test-conn',
      };

      const connectionId = manager.addConnection(config);
      manager.removeConnection(connectionId);
      
      expect(() => manager.getConnectionStats(connectionId)).toThrow();
    });
  });

  describe('Event Handling', () => {
    it('should emit connection events', async () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
        connectionId: 'test-conn',
      };

      const connectionId = manager.addConnection(config);
      
      const connectionEstablishedSpy = vi.fn();
      const connectedSpy = vi.fn();
      
      manager.on('connection_established', connectionEstablishedSpy);
      manager.on('connected', connectedSpy);
      
      // Mock successful connection
      mockWebSocket.on.mockImplementation((event: string, handler: Function) => {
        if (event === 'open') {
          setTimeout(() => handler(), 0);
        }
      });

      await manager.connect(connectionId);
      
      expect(connectionEstablishedSpy).toHaveBeenCalledWith({
        connectionId: 'test-conn',
        url: 'ws://test.example.com'
      });
      
      expect(connectedSpy).toHaveBeenCalledWith({
        connectionId: 'test-conn',
        url: 'ws://test.example.com'
      });
    });
  });

  describe('Utility Methods', () => {
    it('should return connection IDs', () => {
      const config1: ConnectionConfig = { url: 'ws://test1.com', connectionId: 'conn1' };
      const config2: ConnectionConfig = { url: 'ws://test2.com', connectionId: 'conn2' };

      manager.addConnection(config1);
      manager.addConnection(config2);
      
      const connectionIds = manager.getConnectionIds();
      
      expect(connectionIds).toEqual(['conn1', 'conn2']);
    });

    it('should shutdown gracefully', () => {
      const config: ConnectionConfig = {
        url: 'ws://test.example.com',
        connectionId: 'test-conn',
      };

      manager.addConnection(config);
      
      expect(() => manager.shutdown()).not.toThrow();
    });
  });
});