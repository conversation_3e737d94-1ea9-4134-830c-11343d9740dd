import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { SafetyChecklistService } from '../SafetyChecklistService';
import { 
  SafetyValidationSession,
  SafetyChecklistCategory,
  SafetyPriority,
  SafetyItemType,
  SafetyVerificationMethod,
  SafetyValidationStatus
} from '@golddaddy/types';

describe('SafetyChecklistService', () => {
  let service: SafetyChecklistService;
  let mockLogger: any;
  let mockAuditService: any;
  let mockNotificationService: any;

  beforeEach(() => {
    mockLogger = {
      info: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn()
    };

    mockAuditService = {
      log: vi.fn()
    };

    mockNotificationService = {
      send: vi.fn()
    };

    service = new SafetyChecklistService({
      loggerService: mockLogger,
      auditService: mockAuditService,
      notificationService: mockNotificationService
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('createSafetyValidationSession', () => {
    it('should create a new safety validation session successfully', async () => {
      const userId = 'test-user-id';

      const result = await service.createSafetyValidationSession(userId);

      expect(result).toBeDefined();
      expect(result.userId).toBe(userId);
      expect(result.status).toBe(SafetyValidationStatus.IN_PROGRESS);
      expect(result.sections).toHaveLength(5); // Emergency, Risk, Contact, Backup, Compliance
      expect(result.overallProgress).toBe(0);
      expect(mockAuditService.log).toHaveBeenCalledWith(
        expect.objectContaining({
          userId,
          action: 'safety_validation_started'
        })
      );
    });

    it('should return existing active session if one exists', async () => {
      const userId = 'test-user-id';

      vi.spyOn(service as any, 'getActiveSession').mockResolvedValue({
        id: 'existing-session',
        userId,
        status: SafetyValidationStatus.IN_PROGRESS
      });

      const result = await service.createSafetyValidationSession(userId);

      expect(result.id).toBe('existing-session');
      expect(mockLogger.info).toHaveBeenCalledWith(
        'User already has active safety validation session',
        expect.any(Object)
      );
    });

    it('should handle session creation failure', async () => {
      const userId = 'test-user-id';

      vi.spyOn(service as any, 'storeSession').mockRejectedValue(
        new Error('Database connection failed')
      );

      await expect(service.createSafetyValidationSession(userId))
        .rejects
        .toThrow('Database connection failed');
    });
  });

  describe('startSafetyValidation', () => {
    it('should start safety validation for existing session', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';

      const mockSession: SafetyValidationSession = {
        id: sessionId,
        userId,
        status: SafetyValidationStatus.CREATED,
        sections: [
          {
            id: 'emergency_procedures',
            title: 'Emergency Procedures',
            category: SafetyChecklistCategory.EMERGENCY_PROCEDURES,
            priority: SafetyPriority.CRITICAL,
            items: [
              {
                id: 'emergency_1',
                text: 'I know how to halt all trading immediately',
                type: SafetyItemType.ACKNOWLEDGMENT,
                priority: SafetyPriority.CRITICAL,
                verificationMethod: SafetyVerificationMethod.CHECKBOX_CONFIRMATION,
                completed: false,
                required: true
              }
            ],
            completed: false,
            progress: 0
          }
        ],
        startedAt: null,
        completedAt: null,
        overallProgress: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(mockSession);
      vi.spyOn(service as any, 'updateSession').mockResolvedValue(mockSession);

      const result = await service.startSafetyValidation(userId, sessionId);

      expect(result.success).toBe(true);
      expect(result.session?.status).toBe(SafetyValidationStatus.IN_PROGRESS);
      expect(result.nextSection).toBeDefined();
    });

    it('should handle non-existent session', async () => {
      const userId = 'test-user-id';
      const sessionId = 'non-existent-session';

      vi.spyOn(service as any, 'getSession').mockResolvedValue(null);

      await expect(service.startSafetyValidation(userId, sessionId))
        .rejects
        .toThrow('Safety validation session not found');
    });

    it('should handle wrong user for session', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';

      vi.spyOn(service as any, 'getSession').mockResolvedValue({
        id: sessionId,
        userId: 'different-user-id',
        status: SafetyValidationStatus.CREATED
      });

      await expect(service.startSafetyValidation(userId, sessionId))
        .rejects
        .toThrow('Safety validation session not found');
    });
  });

  describe('updateSafetyItem', () => {
    let mockSession: SafetyValidationSession;

    beforeEach(() => {
      mockSession = {
        id: 'session-123',
        userId: 'test-user-id',
        status: SafetyValidationStatus.IN_PROGRESS,
        sections: [
          {
            id: 'emergency_procedures',
            title: 'Emergency Procedures',
            category: SafetyChecklistCategory.EMERGENCY_PROCEDURES,
            priority: SafetyPriority.CRITICAL,
            items: [
              {
                id: 'emergency_1',
                text: 'I know how to halt all trading immediately',
                type: SafetyItemType.ACKNOWLEDGMENT,
                priority: SafetyPriority.CRITICAL,
                verificationMethod: SafetyVerificationMethod.CHECKBOX_CONFIRMATION,
                completed: false,
                required: true
              },
              {
                id: 'emergency_2',
                text: 'What is the emergency halt hotkey?',
                type: SafetyItemType.QUIZ_QUESTION,
                priority: SafetyPriority.HIGH,
                verificationMethod: SafetyVerificationMethod.QUIZ_QUESTION,
                completed: false,
                required: true,
                quizOptions: ['Ctrl+Alt+H', 'F12', 'Esc', 'Ctrl+Shift+X'],
                correctAnswer: 'Ctrl+Alt+H'
              }
            ],
            completed: false,
            progress: 0
          }
        ],
        startedAt: new Date(),
        completedAt: null,
        overallProgress: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    });

    it('should update acknowledgment item successfully', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';
      const itemId = 'emergency_1';
      const response = { acknowledged: true };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(mockSession);
      vi.spyOn(service as any, 'updateSession').mockResolvedValue(mockSession);

      const result = await service.updateSafetyItem(userId, sessionId, itemId, response);

      expect(result.success).toBe(true);
      expect(result.itemCompleted).toBe(true);
      expect(result.sectionProgress).toBeGreaterThan(0);
    });

    it('should update quiz question correctly', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';
      const itemId = 'emergency_2';
      const response = { answer: 'Ctrl+Alt+H' };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(mockSession);
      vi.spyOn(service as any, 'updateSession').mockResolvedValue(mockSession);

      const result = await service.updateSafetyItem(userId, sessionId, itemId, response);

      expect(result.success).toBe(true);
      expect(result.itemCompleted).toBe(true);
      expect(result.correct).toBe(true);
    });

    it('should handle incorrect quiz answer', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';
      const itemId = 'emergency_2';
      const response = { answer: 'F12' }; // Incorrect answer

      vi.spyOn(service as any, 'getSession').mockResolvedValue(mockSession);
      vi.spyOn(service as any, 'updateSession').mockResolvedValue(mockSession);

      const result = await service.updateSafetyItem(userId, sessionId, itemId, response);

      expect(result.success).toBe(true);
      expect(result.itemCompleted).toBe(false);
      expect(result.correct).toBe(false);
      expect(result.feedback).toContain('incorrect');
    });

    it('should handle contact information validation', async () => {
      const contactSection = {
        id: 'contact_information',
        title: 'Contact Information',
        category: SafetyChecklistCategory.CONTACT_INFORMATION,
        priority: SafetyPriority.HIGH,
        items: [
          {
            id: 'contact_1',
            text: 'Emergency contact phone number',
            type: SafetyItemType.CONTACT_INFO,
            priority: SafetyPriority.HIGH,
            verificationMethod: SafetyVerificationMethod.CONTACT_VERIFICATION,
            completed: false,
            required: true
          }
        ],
        completed: false,
        progress: 0
      };

      mockSession.sections.push(contactSection);

      const userId = 'test-user-id';
      const sessionId = 'session-123';
      const itemId = 'contact_1';
      const response = { phoneNumber: '******-0123', verified: true };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(mockSession);
      vi.spyOn(service as any, 'updateSession').mockResolvedValue(mockSession);

      const result = await service.updateSafetyItem(userId, sessionId, itemId, response);

      expect(result.success).toBe(true);
      expect(result.itemCompleted).toBe(true);
    });

    it('should handle non-existent item', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';
      const itemId = 'non-existent-item';
      const response = { acknowledged: true };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(mockSession);

      await expect(service.updateSafetyItem(userId, sessionId, itemId, response))
        .rejects
        .toThrow('Safety item not found');
    });
  });

  describe('completeSafetyValidation', () => {
    it('should complete safety validation when all items are done', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';

      const completedSession: SafetyValidationSession = {
        id: sessionId,
        userId,
        status: SafetyValidationStatus.IN_PROGRESS,
        sections: [
          {
            id: 'emergency_procedures',
            title: 'Emergency Procedures',
            category: SafetyChecklistCategory.EMERGENCY_PROCEDURES,
            priority: SafetyPriority.CRITICAL,
            items: [
              {
                id: 'emergency_1',
                text: 'I know how to halt all trading immediately',
                type: SafetyItemType.ACKNOWLEDGMENT,
                priority: SafetyPriority.CRITICAL,
                verificationMethod: SafetyVerificationMethod.CHECKBOX_CONFIRMATION,
                completed: true,
                required: true,
                response: { acknowledged: true },
                completedAt: new Date()
              }
            ],
            completed: true,
            progress: 100
          }
        ],
        startedAt: new Date(),
        completedAt: null,
        overallProgress: 100,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(completedSession);
      vi.spyOn(service as any, 'updateSession').mockResolvedValue(completedSession);

      const result = await service.completeSafetyValidation(userId, sessionId);

      expect(result.success).toBe(true);
      expect(result.allRequiredCompleted).toBe(true);
      expect(result.overallScore).toBeGreaterThan(0);
      expect(mockAuditService.log).toHaveBeenCalledWith(
        expect.objectContaining({
          userId,
          action: 'safety_validation_completed'
        })
      );
    });

    it('should handle incomplete validation', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';

      const incompleteSession: SafetyValidationSession = {
        id: sessionId,
        userId,
        status: SafetyValidationStatus.IN_PROGRESS,
        sections: [
          {
            id: 'emergency_procedures',
            title: 'Emergency Procedures',
            category: SafetyChecklistCategory.EMERGENCY_PROCEDURES,
            priority: SafetyPriority.CRITICAL,
            items: [
              {
                id: 'emergency_1',
                text: 'I know how to halt all trading immediately',
                type: SafetyItemType.ACKNOWLEDGMENT,
                priority: SafetyPriority.CRITICAL,
                verificationMethod: SafetyVerificationMethod.CHECKBOX_CONFIRMATION,
                completed: false, // Not completed
                required: true
              }
            ],
            completed: false,
            progress: 0
          }
        ],
        startedAt: new Date(),
        completedAt: null,
        overallProgress: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(incompleteSession);

      const result = await service.completeSafetyValidation(userId, sessionId);

      expect(result.success).toBe(false);
      expect(result.allRequiredCompleted).toBe(false);
      expect(result.missingItems).toContain('emergency_1');
    });
  });

  describe('getSafetyValidationStatus', () => {
    it('should return current safety validation status', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';

      const mockSession: SafetyValidationSession = {
        id: sessionId,
        userId,
        status: SafetyValidationStatus.IN_PROGRESS,
        sections: [],
        startedAt: new Date(),
        completedAt: null,
        overallProgress: 45,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(mockSession);

      const result = await service.getSafetyValidationStatus(userId, sessionId);

      expect(result).toBeDefined();
      expect(result.status).toBe(SafetyValidationStatus.IN_PROGRESS);
      expect(result.overallProgress).toBe(45);
    });

    it('should handle non-existent session', async () => {
      const userId = 'test-user-id';
      const sessionId = 'non-existent-session';

      vi.spyOn(service as any, 'getSession').mockResolvedValue(null);

      await expect(service.getSafetyValidationStatus(userId, sessionId))
        .rejects
        .toThrow('Safety validation session not found');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle session expiration', async () => {
      const userId = 'test-user-id';
      const sessionId = 'expired-session';

      const expiredSession: SafetyValidationSession = {
        id: sessionId,
        userId,
        status: SafetyValidationStatus.IN_PROGRESS,
        sections: [],
        startedAt: new Date(Date.now() - 25 * 60 * 60 * 1000), // 25 hours ago
        completedAt: null,
        overallProgress: 50,
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: new Date(Date.now() - 1000) // Expired
      };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(expiredSession);

      await expect(service.getSafetyValidationStatus(userId, sessionId))
        .rejects
        .toThrow('Safety validation session has expired');
    });

    it('should handle malformed response data', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';
      const itemId = 'emergency_1';
      const malformedResponse = { invalid: 'data' };

      const mockSession: SafetyValidationSession = {
        id: sessionId,
        userId,
        status: SafetyValidationStatus.IN_PROGRESS,
        sections: [
          {
            id: 'emergency_procedures',
            title: 'Emergency Procedures',
            category: SafetyChecklistCategory.EMERGENCY_PROCEDURES,
            priority: SafetyPriority.CRITICAL,
            items: [
              {
                id: 'emergency_1',
                text: 'I know how to halt all trading immediately',
                type: SafetyItemType.ACKNOWLEDGMENT,
                priority: SafetyPriority.CRITICAL,
                verificationMethod: SafetyVerificationMethod.CHECKBOX_CONFIRMATION,
                completed: false,
                required: true
              }
            ],
            completed: false,
            progress: 0
          }
        ],
        startedAt: new Date(),
        completedAt: null,
        overallProgress: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(mockSession);

      const result = await service.updateSafetyItem(userId, sessionId, itemId, malformedResponse);

      expect(result.success).toBe(false);
      expect(result.feedback).toContain('Invalid response data');
    });

    it('should handle database failures gracefully', async () => {
      const userId = 'test-user-id';

      vi.spyOn(service as any, 'getActiveSession').mockRejectedValue(
        new Error('Database connection failed')
      );

      await expect(service.createSafetyValidationSession(userId))
        .rejects
        .toThrow('Database connection failed');

      expect(mockLogger.error).toHaveBeenCalledWith(
        'Failed to create safety validation session',
        expect.any(Object)
      );
    });

    it('should validate required critical items completion', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';

      const sessionWithMissingCritical: SafetyValidationSession = {
        id: sessionId,
        userId,
        status: SafetyValidationStatus.IN_PROGRESS,
        sections: [
          {
            id: 'emergency_procedures',
            title: 'Emergency Procedures',
            category: SafetyChecklistCategory.EMERGENCY_PROCEDURES,
            priority: SafetyPriority.CRITICAL,
            items: [
              {
                id: 'critical_1',
                text: 'Critical emergency procedure',
                type: SafetyItemType.ACKNOWLEDGMENT,
                priority: SafetyPriority.CRITICAL,
                verificationMethod: SafetyVerificationMethod.CHECKBOX_CONFIRMATION,
                completed: false, // Critical item not completed
                required: true
              },
              {
                id: 'optional_1',
                text: 'Optional item',
                type: SafetyItemType.ACKNOWLEDGMENT,
                priority: SafetyPriority.LOW,
                verificationMethod: SafetyVerificationMethod.CHECKBOX_CONFIRMATION,
                completed: true,
                required: false
              }
            ],
            completed: false,
            progress: 50
          }
        ],
        startedAt: new Date(),
        completedAt: null,
        overallProgress: 50,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(sessionWithMissingCritical);

      const result = await service.completeSafetyValidation(userId, sessionId);

      expect(result.success).toBe(false);
      expect(result.allRequiredCompleted).toBe(false);
      expect(result.criticalItemsMissing).toBe(true);
    });
  });

  describe('Performance and Concurrency', () => {
    it('should handle concurrent item updates', async () => {
      const userId = 'test-user-id';
      const sessionId = 'session-123';

      const mockSession: SafetyValidationSession = {
        id: sessionId,
        userId,
        status: SafetyValidationStatus.IN_PROGRESS,
        sections: [
          {
            id: 'emergency_procedures',
            title: 'Emergency Procedures',
            category: SafetyChecklistCategory.EMERGENCY_PROCEDURES,
            priority: SafetyPriority.CRITICAL,
            items: [
              {
                id: 'item_1',
                text: 'Item 1',
                type: SafetyItemType.ACKNOWLEDGMENT,
                priority: SafetyPriority.HIGH,
                verificationMethod: SafetyVerificationMethod.CHECKBOX_CONFIRMATION,
                completed: false,
                required: true
              },
              {
                id: 'item_2',
                text: 'Item 2',
                type: SafetyItemType.ACKNOWLEDGMENT,
                priority: SafetyPriority.HIGH,
                verificationMethod: SafetyVerificationMethod.CHECKBOX_CONFIRMATION,
                completed: false,
                required: true
              }
            ],
            completed: false,
            progress: 0
          }
        ],
        startedAt: new Date(),
        completedAt: null,
        overallProgress: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      vi.spyOn(service as any, 'getSession').mockResolvedValue(mockSession);
      vi.spyOn(service as any, 'updateSession').mockResolvedValue(mockSession);

      // Simulate concurrent updates
      const promise1 = service.updateSafetyItem(userId, sessionId, 'item_1', { acknowledged: true });
      const promise2 = service.updateSafetyItem(userId, sessionId, 'item_2', { acknowledged: true });

      const [result1, result2] = await Promise.all([promise1, promise2]);

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
    });
  });
});