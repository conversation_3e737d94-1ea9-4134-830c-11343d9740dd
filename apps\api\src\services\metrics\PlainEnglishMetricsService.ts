/**
 * Plain English Metrics Service
 * 
 * Translates technical trading metrics into user-friendly explanations
 * with personalized content based on user experience level and preferences.
 */

import {
  MetricType,
  UserExperienceLevel,
  RiskTolerance,
  PerformanceLevel,
  TrendDirection,
  MetricTranslation,
  VisualMetadata,
  ContextualAdvice,
  PlainEnglishMetric,
  StrategyHealthScore,
  HealthScoreExplanation,
  MetricTranslationContext,
  PerformanceMetrics,
  MetricBenchmark,
} from '@golddaddy/types';

export class PlainEnglishMetricsService {
  private benchmarks: Map<MetricType, MetricBenchmark>;

  constructor() {
    this.benchmarks = new Map();
    this.initializeBenchmarks();
  }

  /**
   * Translate win rate to plain English
   */
  public translateWinRate(
    winRate: number,
    context: MetricTranslationContext
  ): PlainEnglishMetric {
    const outOf10 = Math.round(winRate * 10);
    const performance = this.getPerformanceLevel('win_rate', winRate);
    
    const translation: MetricTranslation = {
      primary: `${outOf10} out of 10 trades were profitable`,
      secondary: this.getWinRateSecondary(winRate, context.userExperience),
      comparison: this.getWinRateComparison(winRate),
    };

    const visualMetadata: VisualMetadata = {
      color: this.getPerformanceColor(performance),
      iconType: performance === 'excellent' || performance === 'good' ? 'thumbs_up' : 
                performance === 'poor' || performance === 'concerning' ? 'thumbs_down' : 'warning',
      progressBar: {
        value: winRate * 100,
        ranges: {
          poor: { min: 0, max: 40 },
          average: { min: 40, max: 60 },
          good: { min: 60, max: 100 },
        },
      },
      badgeType: this.getBadgeType(performance),
    };

    const contextualAdvice: ContextualAdvice = {
      interpretation: this.getWinRateInterpretation(winRate, context),
      nextSteps: this.getWinRateNextSteps(winRate, context),
      warnings: this.getWinRateWarnings(winRate),
      celebration: this.getWinRateCelebration(winRate),
      educationalTip: this.getWinRateEducationalTip(context.userExperience),
    };

    return {
      id: `${context.strategyId}_win_rate_${Date.now()}`,
      metricType: 'win_rate',
      originalValue: winRate,
      translation,
      visualMetadata,
      contextualAdvice,
      userPersonalization: {
        experienceLevel: context.userExperience,
        riskTolerance: context.userRiskTolerance,
      },
      calculatedAt: new Date(),
      validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    };
  }

  /**
   * Translate Sharpe ratio to plain English ("profit smoothness")
   */
  public translateSharpeRatio(
    sharpeRatio: number,
    context: MetricTranslationContext
  ): PlainEnglishMetric {
    const performance = this.getPerformanceLevel('sharpe_ratio', sharpeRatio);
    
    const translation: MetricTranslation = {
      primary: this.getSharpeRatioPrimary(sharpeRatio, context.userExperience),
      secondary: this.getSharpeRatioSecondary(sharpeRatio, context.userExperience),
      comparison: this.getSharpeRatioComparison(sharpeRatio),
    };

    const visualMetadata: VisualMetadata = {
      color: this.getPerformanceColor(performance),
      iconType: this.getPerformanceIcon(performance),
      progressBar: {
        value: Math.min(Math.max((sharpeRatio + 1) * 25, 0), 100), // Scale for UI
        ranges: {
          poor: { min: 0, max: 25 },
          average: { min: 25, max: 50 },
          good: { min: 50, max: 100 },
        },
      },
      badgeType: this.getBadgeType(performance),
    };

    const contextualAdvice: ContextualAdvice = {
      interpretation: this.getSharpeRatioInterpretation(sharpeRatio, context),
      nextSteps: this.getSharpeRatioNextSteps(sharpeRatio, context),
      warnings: this.getSharpeRatioWarnings(sharpeRatio),
      celebration: this.getSharpeRatioCelebration(sharpeRatio),
      educationalTip: this.getSharpeRatioEducationalTip(context.userExperience),
    };

    return {
      id: `${context.strategyId}_sharpe_ratio_${Date.now()}`,
      metricType: 'sharpe_ratio',
      originalValue: sharpeRatio,
      translation,
      visualMetadata,
      contextualAdvice,
      userPersonalization: {
        experienceLevel: context.userExperience,
        riskTolerance: context.userRiskTolerance,
      },
      calculatedAt: new Date(),
      validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000),
    };
  }

  /**
   * Translate profit factor to plain English ("win/loss ratio")
   */
  public translateProfitFactor(
    profitFactor: number,
    context: MetricTranslationContext
  ): PlainEnglishMetric {
    const performance = this.getPerformanceLevel('profit_factor', profitFactor);
    
    const translation: MetricTranslation = {
      primary: this.getProfitFactorPrimary(profitFactor, context.userExperience),
      secondary: this.getProfitFactorSecondary(profitFactor, context.userExperience),
      comparison: this.getProfitFactorComparison(profitFactor),
    };

    const visualMetadata: VisualMetadata = {
      color: this.getPerformanceColor(performance),
      iconType: this.getPerformanceIcon(performance),
      progressBar: {
        value: Math.min(Math.max((profitFactor - 0.5) * 50, 0), 100),
        ranges: {
          poor: { min: 0, max: 50 },
          average: { min: 50, max: 75 },
          good: { min: 75, max: 100 },
        },
      },
      badgeType: this.getBadgeType(performance),
    };

    const contextualAdvice: ContextualAdvice = {
      interpretation: this.getProfitFactorInterpretation(profitFactor, context),
      nextSteps: this.getProfitFactorNextSteps(profitFactor, context),
      warnings: this.getProfitFactorWarnings(profitFactor),
      celebration: this.getProfitFactorCelebration(profitFactor),
      educationalTip: this.getProfitFactorEducationalTip(context.userExperience),
    };

    return {
      id: `${context.strategyId}_profit_factor_${Date.now()}`,
      metricType: 'profit_factor',
      originalValue: profitFactor,
      translation,
      visualMetadata,
      contextualAdvice,
      userPersonalization: {
        experienceLevel: context.userExperience,
        riskTolerance: context.userRiskTolerance,
      },
      calculatedAt: new Date(),
      validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000),
    };
  }

  /**
   * Calculate strategy health score combining multiple metrics
   */
  public calculateHealthScore(
    metrics: PerformanceMetrics,
    context: MetricTranslationContext
  ): StrategyHealthScore {
    const profitability = this.calculateProfitabilityScore(metrics);
    const consistency = this.calculateConsistencyScore(metrics);
    const riskManagement = this.calculateRiskManagementScore(metrics);
    const robustness = this.calculateRobustnessScore(metrics);

    // Weighted average with equal weights for now
    const overall = Math.round(
      (profitability * 0.3 + consistency * 0.25 + riskManagement * 0.25 + robustness * 0.2)
    );

    const performance = this.getHealthPerformanceLevel(overall);
    const trend = this.calculateTrend(overall); // Simplified for now

    return {
      overall,
      components: {
        profitability,
        consistency,
        riskManagement,
        robustness,
      },
      performance,
      trend,
      lastCalculated: new Date(),
    };
  }

  /**
   * Generate health score explanation in plain English
   */
  public generateHealthScoreExplanation(
    healthScore: StrategyHealthScore,
    context: MetricTranslationContext
  ): HealthScoreExplanation {
    const summary = this.generateHealthSummary(healthScore, context);
    const strengths = this.identifyStrengths(healthScore, context);
    const weaknesses = this.identifyWeaknesses(healthScore, context);
    const recommendations = this.generateRecommendations(healthScore, context);
    const riskWarnings = this.generateRiskWarnings(healthScore, context);

    return {
      summary,
      strengths,
      weaknesses,
      recommendations,
      riskWarnings,
    };
  }

  // ===== Private Helper Methods =====

  private initializeBenchmarks(): void {
    // Initialize industry-standard benchmarks for each metric
    this.benchmarks.set('win_rate', {
      metricType: 'win_rate',
      strategyType: 'general',
      timeframe: 'any',
      benchmarks: {
        excellent: 0.7,
        good: 0.6,
        average: 0.5,
        poor: 0.4,
      },
      source: 'industry_standard',
      lastUpdated: new Date(),
    });

    this.benchmarks.set('sharpe_ratio', {
      metricType: 'sharpe_ratio',
      strategyType: 'general',
      timeframe: 'any',
      benchmarks: {
        excellent: 2.0,
        good: 1.5,
        average: 1.0,
        poor: 0.5,
      },
      source: 'industry_standard',
      lastUpdated: new Date(),
    });

    this.benchmarks.set('profit_factor', {
      metricType: 'profit_factor',
      strategyType: 'general',
      timeframe: 'any',
      benchmarks: {
        excellent: 2.0,
        good: 1.5,
        average: 1.2,
        poor: 1.0,
      },
      source: 'industry_standard',
      lastUpdated: new Date(),
    });
  }

  private getPerformanceLevel(metricType: MetricType, value: number): PerformanceLevel {
    const benchmark = this.benchmarks.get(metricType);
    if (!benchmark) return 'average';

    if (value >= benchmark.benchmarks.excellent) return 'excellent';
    if (value >= benchmark.benchmarks.good) return 'good';
    if (value >= benchmark.benchmarks.average) return 'average';
    if (value >= benchmark.benchmarks.poor) return 'poor';
    return 'concerning';
  }

  private getPerformanceColor(performance: PerformanceLevel): 'green' | 'yellow' | 'red' | 'blue' | 'gray' {
    switch (performance) {
      case 'excellent':
      case 'good':
        return 'green';
      case 'average':
        return 'yellow';
      case 'poor':
      case 'concerning':
        return 'red';
      default:
        return 'gray';
    }
  }

  private getPerformanceIcon(performance: PerformanceLevel): VisualMetadata['iconType'] {
    switch (performance) {
      case 'excellent':
      case 'good':
        return 'thumbs_up';
      case 'average':
        return 'warning';
      case 'poor':
      case 'concerning':
        return 'thumbs_down';
      default:
        return 'info';
    }
  }

  private getBadgeType(performance: PerformanceLevel): VisualMetadata['badgeType'] {
    switch (performance) {
      case 'excellent':
      case 'good':
        return 'success';
      case 'average':
        return 'warning';
      case 'poor':
      case 'concerning':
        return 'error';
      default:
        return 'info';
    }
  }

  // ===== Win Rate Helper Methods =====

  private getWinRateSecondary(winRate: number, experience: UserExperienceLevel): string {
    if (experience === 'beginner') {
      return winRate >= 0.6 ? 'This is very good!' : 
             winRate >= 0.5 ? 'This is decent performance.' : 
             'This needs improvement.';
    } else if (experience === 'intermediate') {
      return winRate >= 0.7 ? 'Excellent win rate for systematic trading.' :
             winRate >= 0.6 ? 'Good win rate, above market average.' :
             winRate >= 0.5 ? 'Average win rate, focus on trade quality.' :
             'Below average win rate requires strategy review.';
    } else {
      return winRate >= 0.7 ? 'Superior win rate indicating strong edge and execution.' :
             winRate >= 0.6 ? 'Solid win rate with good risk-adjusted returns potential.' :
             winRate >= 0.5 ? 'Acceptable win rate, evaluate profit factor correlation.' :
             'Suboptimal win rate may indicate poor market timing or entry signals.';
    }
  }

  private getWinRateComparison(winRate: number): string {
    if (winRate >= 0.7) return 'Better than 80% of trading strategies';
    if (winRate >= 0.6) return 'Better than 60% of trading strategies';
    if (winRate >= 0.5) return 'Average compared to most strategies';
    return 'Below average performance';
  }

  private getWinRateInterpretation(winRate: number, context: MetricTranslationContext): string {
    const performance = this.getPerformanceLevel('win_rate', winRate);
    
    if (performance === 'excellent') {
      return 'Your strategy has exceptional accuracy in identifying profitable trades.';
    } else if (performance === 'good') {
      return 'Your strategy shows good trade selection with more wins than losses.';
    } else if (performance === 'average') {
      return 'Your strategy has balanced performance with room for improvement.';
    } else {
      return 'Your strategy needs refinement to improve trade accuracy.';
    }
  }

  private getWinRateNextSteps(winRate: number, context: MetricTranslationContext): string[] {
    const steps: string[] = [];
    
    if (winRate < 0.5) {
      steps.push('Review entry signals for better market timing');
      steps.push('Consider tighter stop-loss parameters');
      steps.push('Analyze losing trades for common patterns');
    } else if (winRate < 0.6) {
      steps.push('Fine-tune entry conditions');
      steps.push('Review position sizing strategy');
    } else {
      steps.push('Maintain current approach');
      steps.push('Monitor for consistency over time');
    }

    return steps;
  }

  private getWinRateWarnings(winRate: number): string[] {
    const warnings: string[] = [];
    
    if (winRate < 0.3) {
      warnings.push('Critically low win rate requires immediate strategy review');
    } else if (winRate < 0.4) {
      warnings.push('Low win rate may indicate poor market conditions or strategy fit');
    }

    return warnings;
  }

  private getWinRateCelebration(winRate: number): string | undefined {
    if (winRate >= 0.8) {
      return '🎉 Outstanding trading accuracy! You\'re in the top 10% of traders.';
    } else if (winRate >= 0.7) {
      return '🎊 Excellent win rate! You\'re beating most professional strategies.';
    }
    return undefined;
  }

  private getWinRateEducationalTip(experience: UserExperienceLevel): string {
    if (experience === 'beginner') {
      return 'Win rate shows how often your trades are profitable. Higher is generally better, but profit size matters too!';
    } else if (experience === 'intermediate') {
      return 'Win rate should be balanced with profit factor. A 60% win rate with 2:1 profit factor is often better than 80% with 1.1:1.';
    } else {
      return 'High win rates may indicate curve-fitting. Ensure robust out-of-sample testing and consider the win rate/profit factor trade-off.';
    }
  }

  // ===== Sharpe Ratio Helper Methods =====

  private getSharpeRatioPrimary(sharpeRatio: number, experience: UserExperienceLevel): string {
    if (experience === 'beginner') {
      if (sharpeRatio >= 2.0) return 'Your profits are very smooth and consistent';
      if (sharpeRatio >= 1.5) return 'Your profits are quite smooth with manageable ups and downs';
      if (sharpeRatio >= 1.0) return 'Your profits have moderate smoothness';
      if (sharpeRatio >= 0.5) return 'Your profits are somewhat bumpy';
      return 'Your profits are quite volatile and unpredictable';
    } else {
      return `Sharpe ratio of ${sharpeRatio.toFixed(2)} indicates ${this.getSharpeRatioDescription(sharpeRatio)} risk-adjusted returns`;
    }
  }

  private getSharpeRatioDescription(sharpeRatio: number): string {
    if (sharpeRatio >= 2.0) return 'excellent';
    if (sharpeRatio >= 1.5) return 'very good';
    if (sharpeRatio >= 1.0) return 'good';
    if (sharpeRatio >= 0.5) return 'acceptable';
    return 'poor';
  }

  private getSharpeRatioSecondary(sharpeRatio: number, experience: UserExperienceLevel): string {
    if (experience === 'beginner') {
      return 'This measures how smooth your profit journey is compared to the bumps along the way.';
    } else {
      return 'This measures excess return per unit of risk, accounting for volatility.';
    }
  }

  private getSharpeRatioComparison(sharpeRatio: number): string {
    if (sharpeRatio >= 2.0) return 'Exceptional - better than most hedge funds';
    if (sharpeRatio >= 1.5) return 'Very good - institutional quality';
    if (sharpeRatio >= 1.0) return 'Good - above market average';
    if (sharpeRatio >= 0.5) return 'Below average but acceptable';
    return 'Poor - needs significant improvement';
  }

  private getSharpeRatioInterpretation(sharpeRatio: number, context: MetricTranslationContext): string {
    if (sharpeRatio >= 1.5) {
      return 'Your strategy delivers consistent returns with well-controlled risk.';
    } else if (sharpeRatio >= 1.0) {
      return 'Your strategy has decent risk-adjusted returns with moderate volatility.';
    } else if (sharpeRatio >= 0.5) {
      return 'Your strategy shows positive returns but with higher volatility than ideal.';
    } else {
      return 'Your strategy has poor risk-adjusted returns and needs significant improvement.';
    }
  }

  private getSharpeRatioNextSteps(sharpeRatio: number, context: MetricTranslationContext): string[] {
    const steps: string[] = [];
    
    if (sharpeRatio < 0.5) {
      steps.push('Reduce position sizes to lower volatility');
      steps.push('Improve risk management rules');
      steps.push('Consider strategy diversification');
    } else if (sharpeRatio < 1.0) {
      steps.push('Fine-tune risk management parameters');
      steps.push('Review position sizing methodology');
    } else {
      steps.push('Maintain current risk management approach');
      steps.push('Monitor consistency over different market conditions');
    }

    return steps;
  }

  private getSharpeRatioWarnings(sharpeRatio: number): string[] {
    const warnings: string[] = [];
    
    if (sharpeRatio < 0) {
      warnings.push('Negative Sharpe ratio indicates returns worse than risk-free rate');
    } else if (sharpeRatio < 0.5) {
      warnings.push('Low Sharpe ratio suggests poor risk-adjusted performance');
    }

    return warnings;
  }

  private getSharpeRatioCelebration(sharpeRatio: number): string | undefined {
    if (sharpeRatio >= 3.0) {
      return '🏆 Extraordinary Sharpe ratio! This is world-class performance.';
    } else if (sharpeRatio >= 2.0) {
      return '🎯 Excellent Sharpe ratio! You\'re achieving institutional-quality returns.';
    }
    return undefined;
  }

  private getSharpeRatioEducationalTip(experience: UserExperienceLevel): string {
    if (experience === 'beginner') {
      return 'Sharpe ratio measures how much extra return you get for the risk you take. Higher is better!';
    } else if (experience === 'intermediate') {
      return 'Sharpe ratio above 1.0 is good, above 2.0 is excellent. It helps compare strategies with different risk levels.';
    } else {
      return 'Sharpe ratio accounts for risk-free rate and volatility. Consider using Calmar or Sortino ratios for additional insights.';
    }
  }

  // ===== Profit Factor Helper Methods =====

  private getProfitFactorPrimary(profitFactor: number, experience: UserExperienceLevel): string {
    if (experience === 'beginner') {
      if (profitFactor >= 2.0) return `Your winning trades make ${profitFactor.toFixed(1)} times more than your losing trades cost`;
      if (profitFactor >= 1.5) return `Your winning trades make ${profitFactor.toFixed(1)} times more than your losing trades cost`;
      if (profitFactor >= 1.0) return `Your winning trades barely cover your losing trades`;
      return `Your losing trades cost more than your winning trades make`;
    } else {
      return `Profit factor of ${profitFactor.toFixed(2)} - ${this.getProfitFactorDescription(profitFactor)} gross profitability`;
    }
  }

  private getProfitFactorDescription(profitFactor: number): string {
    if (profitFactor >= 2.0) return 'excellent';
    if (profitFactor >= 1.5) return 'very good';
    if (profitFactor >= 1.2) return 'good';
    if (profitFactor >= 1.0) return 'marginal';
    return 'unprofitable';
  }

  private getProfitFactorSecondary(profitFactor: number, experience: UserExperienceLevel): string {
    if (experience === 'beginner') {
      return 'This shows the relationship between your wins and losses.';
    } else {
      return 'This measures gross profit divided by gross loss across all trades.';
    }
  }

  private getProfitFactorComparison(profitFactor: number): string {
    if (profitFactor >= 2.0) return 'Excellent - top tier performance';
    if (profitFactor >= 1.5) return 'Very good - above average';
    if (profitFactor >= 1.2) return 'Good - profitable strategy';
    if (profitFactor >= 1.0) return 'Marginal - barely profitable';
    return 'Unprofitable - losing money';
  }

  private getProfitFactorInterpretation(profitFactor: number, context: MetricTranslationContext): string {
    if (profitFactor >= 2.0) {
      return 'Your strategy generates excellent gross profits relative to losses.';
    } else if (profitFactor >= 1.5) {
      return 'Your strategy shows strong profitability with good win/loss ratio.';
    } else if (profitFactor >= 1.2) {
      return 'Your strategy is profitable but could benefit from optimization.';
    } else if (profitFactor >= 1.0) {
      return 'Your strategy is marginally profitable and needs improvement.';
    } else {
      return 'Your strategy is losing money and requires significant changes.';
    }
  }

  private getProfitFactorNextSteps(profitFactor: number, context: MetricTranslationContext): string[] {
    const steps: string[] = [];
    
    if (profitFactor < 1.0) {
      steps.push('Immediate strategy overhaul required');
      steps.push('Reduce position sizes until profitable');
      steps.push('Review all entry and exit criteria');
    } else if (profitFactor < 1.2) {
      steps.push('Improve take-profit levels');
      steps.push('Tighten stop-loss criteria');
      steps.push('Consider trade filtering improvements');
    } else if (profitFactor < 1.5) {
      steps.push('Optimize position sizing');
      steps.push('Fine-tune exit strategies');
    } else {
      steps.push('Maintain current approach');
      steps.push('Monitor for consistency');
    }

    return steps;
  }

  private getProfitFactorWarnings(profitFactor: number): string[] {
    const warnings: string[] = [];
    
    if (profitFactor < 1.0) {
      warnings.push('Strategy is losing money - immediate action required');
    } else if (profitFactor < 1.1) {
      warnings.push('Very thin profit margins - high risk of becoming unprofitable');
    }

    return warnings;
  }

  private getProfitFactorCelebration(profitFactor: number): string | undefined {
    if (profitFactor >= 3.0) {
      return '🚀 Phenomenal profit factor! Your wins absolutely dominate your losses.';
    } else if (profitFactor >= 2.0) {
      return '💰 Excellent profit factor! Your strategy is highly profitable.';
    }
    return undefined;
  }

  private getProfitFactorEducationalTip(experience: UserExperienceLevel): string {
    if (experience === 'beginner') {
      return 'Profit factor above 1.0 means you\'re making money. Above 1.5 is good, above 2.0 is excellent!';
    } else if (experience === 'intermediate') {
      return 'Profit factor complements win rate. You can be profitable with 40% wins if profit factor is above 1.5.';
    } else {
      return 'Profit factor is gross profit/gross loss. Consider net profit factor including costs for more realistic assessment.';
    }
  }

  // ===== Health Score Calculation Methods =====

  private calculateProfitabilityScore(metrics: PerformanceMetrics): number {
    const profitFactorScore = Math.min((metrics.profitFactor / 2.0) * 100, 100);
    const returnScore = Math.min((metrics.totalReturn / 0.2) * 100, 100); // 20% annual return as max
    return Math.round((profitFactorScore * 0.6 + returnScore * 0.4));
  }

  private calculateConsistencyScore(metrics: PerformanceMetrics): number {
    const winRateScore = metrics.winRate * 100;
    const drawdownScore = Math.max(100 - (metrics.maxDrawdown * 100 * 5), 0); // 20% drawdown = 0 points
    return Math.round((winRateScore * 0.4 + drawdownScore * 0.6));
  }

  private calculateRiskManagementScore(metrics: PerformanceMetrics): number {
    const sharpeScore = Math.min((metrics.sharpeRatio / 2.0) * 100, 100);
    const volatilityScore = Math.max(100 - (metrics.volatility * 100 * 2), 0); // 50% volatility = 0 points
    return Math.round((sharpeScore * 0.7 + volatilityScore * 0.3));
  }

  private calculateRobustnessScore(metrics: PerformanceMetrics): number {
    const tradeCountScore = Math.min((metrics.tradeCount / 100) * 100, 100); // 100 trades as good sample
    const calmarScore = Math.min((metrics.calmarRatio / 1.0) * 100, 100); // Calmar ratio of 1.0 as good
    return Math.round((tradeCountScore * 0.3 + calmarScore * 0.7));
  }

  private getHealthPerformanceLevel(score: number): PerformanceLevel {
    if (score >= 85) return 'excellent';
    if (score >= 70) return 'good';
    if (score >= 55) return 'average';
    if (score >= 40) return 'poor';
    return 'concerning';
  }

  private calculateTrend(score: number): TrendDirection {
    // Simplified trend calculation - in real implementation, this would use historical data
    return 'stable';
  }

  // ===== Health Score Explanation Methods =====

  private generateHealthSummary(healthScore: StrategyHealthScore, context: MetricTranslationContext): string {
    const { overall, performance } = healthScore;
    const { userExperience } = context;
    
    // Adapt explanation complexity based on user experience level
    if (performance === 'excellent') {
      if (userExperience === 'beginner') {
        return `Excellent! Your strategy scored ${overall}/100. Everything looks great - keep it up!`;
      } else if (userExperience === 'intermediate') {
        return `Outstanding strategy health with a score of ${overall}/100. Your strategy demonstrates exceptional performance across all key areas.`;
      } else {
        return `Exceptional strategy health (${overall}/100). Strong profitability (${healthScore.components.profitability}), consistency (${healthScore.components.consistency}), and risk management (${healthScore.components.riskManagement}).`;
      }
    } else if (performance === 'good') {
      if (userExperience === 'beginner') {
        return `Good job! Your strategy scored ${overall}/100. It's working well with just small things to improve.`;
      } else if (userExperience === 'intermediate') {
        return `Strong strategy health with a score of ${overall}/100. Your strategy shows solid performance with minor areas for improvement.`;
      } else {
        return `Solid strategy health (${overall}/100). Component breakdown: profitability ${healthScore.components.profitability}, consistency ${healthScore.components.consistency}, risk management ${healthScore.components.riskManagement}.`;
      }
    } else if (performance === 'average') {
      if (userExperience === 'beginner') {
        return `Your strategy scored ${overall}/100. It's doing okay, but we can make it better together!`;
      } else if (userExperience === 'intermediate') {
        return `Moderate strategy health with a score of ${overall}/100. Your strategy has balanced performance with opportunities for enhancement.`;
      } else {
        return `Average strategy health (${overall}/100). Mixed performance across components requires targeted optimization.`;
      }
    } else if (performance === 'poor') {
      if (userExperience === 'beginner') {
        return `Your strategy scored ${overall}/100. Don't worry - we'll help you improve step by step.`;
      } else if (userExperience === 'intermediate') {
        return `Below-average strategy health with a score of ${overall}/100. Your strategy needs attention in several key areas.`;
      } else {
        return `Poor strategy health (${overall}/100). Critical weaknesses in core components require immediate attention.`;
      }
    } else {
      if (userExperience === 'beginner') {
        return `Your strategy scored ${overall}/100. We need to make some important changes to keep your money safe.`;
      } else if (userExperience === 'intermediate') {
        return `Concerning strategy health with a score of ${overall}/100. Immediate improvements are needed across multiple areas.`;
      } else {
        return `Critical strategy health issues (${overall}/100). Severe deficiencies across all components demand comprehensive restructuring.`;
      }
    }
  }

  private identifyStrengths(healthScore: StrategyHealthScore, context: MetricTranslationContext): string[] {
    const strengths: string[] = [];
    const { components } = healthScore;

    if (components.profitability >= 70) {
      strengths.push('Strong profitability with excellent profit generation');
    }
    if (components.consistency >= 70) {
      strengths.push('Consistent performance with good win rate and controlled drawdowns');
    }
    if (components.riskManagement >= 70) {
      strengths.push('Excellent risk management with good risk-adjusted returns');
    }
    if (components.robustness >= 70) {
      strengths.push('Robust strategy with sufficient trade sample and stable returns');
    }

    return strengths;
  }

  private identifyWeaknesses(healthScore: StrategyHealthScore, context: MetricTranslationContext): string[] {
    const weaknesses: string[] = [];
    const { components } = healthScore;

    if (components.profitability < 50) {
      weaknesses.push('Low profitability requiring strategy optimization');
    }
    if (components.consistency < 50) {
      weaknesses.push('Inconsistent performance with irregular returns or high drawdowns');
    }
    if (components.riskManagement < 50) {
      weaknesses.push('Poor risk management with suboptimal risk-adjusted returns');
    }
    if (components.robustness < 50) {
      weaknesses.push('Limited robustness due to insufficient data or unstable parameters');
    }

    return weaknesses;
  }

  private generateRecommendations(healthScore: StrategyHealthScore, context: MetricTranslationContext): string[] {
    const recommendations: string[] = [];
    const { components } = healthScore;

    if (components.profitability < 60) {
      recommendations.push('Optimize entry and exit criteria to improve profit factor');
      recommendations.push('Review position sizing to maximize returns');
    }
    if (components.consistency < 60) {
      recommendations.push('Implement stricter risk controls to reduce drawdowns');
      recommendations.push('Consider trade filtering to improve win rate');
    }
    if (components.riskManagement < 60) {
      recommendations.push('Reduce position sizes to improve Sharpe ratio');
      recommendations.push('Implement dynamic stop-loss levels');
    }
    if (components.robustness < 60) {
      recommendations.push('Gather more trade data for statistical significance');
      recommendations.push('Test strategy across different market conditions');
    }

    // Always provide at least one recommendation, even for excellent strategies
    if (recommendations.length === 0) {
      if (context.userExperience === 'beginner') {
        recommendations.push('Keep monitoring your strategy performance daily');
        recommendations.push('Consider learning about position sizing techniques');
      } else if (context.userExperience === 'intermediate') {
        recommendations.push('Consider diversifying with additional strategy variants');
        recommendations.push('Monitor performance during different market conditions');
      } else {
        recommendations.push('Analyze strategy performance attribution by market regime');
        recommendations.push('Consider dynamic parameter optimization');
      }
    }

    return recommendations;
  }

  private generateRiskWarnings(healthScore: StrategyHealthScore, context: MetricTranslationContext): string[] {
    const warnings: string[] = [];
    const { overall, components } = healthScore;

    if (overall < 40) {
      warnings.push('Strategy has fundamental issues requiring immediate attention');
    }
    if (components.profitability < 30) {
      warnings.push('Strategy may be unprofitable - consider stopping live trading');
    }
    if (components.riskManagement < 30) {
      warnings.push('High risk levels may lead to significant losses');
    }
    if (components.consistency < 30) {
      warnings.push('Extreme inconsistency may indicate unreliable strategy');
    }

    return warnings;
  }
}