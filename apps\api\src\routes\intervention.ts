/**
 * Manual Intervention API Routes
 * 
 * Provides API endpoints for manual intervention in the broker failover system
 * Part of Task 8: Manual Intervention System
 */

import { Router } from 'express';
import { ManualInterventionService } from '../services/intervention/ManualInterventionService.js';
import { authMiddleware } from '../middleware/auth.js';
import { rateLimiter } from '../middleware/rate-limit.js';
import type { ManualInterventionRequest } from '@golddaddy/types';

const router = Router();
let interventionService: ManualInterventionService;

/**
 * Set intervention service instance
 */
export function setInterventionService(service: ManualInterventionService): void {
  interventionService = service;
}

// Apply authentication and rate limiting to all intervention routes
router.use(authMiddleware);
router.use(rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  maxRequests: 20, // More restrictive for intervention endpoints
  message: 'Too many intervention requests'
}));

/**
 * Request manual intervention
 * POST /api/intervention/request
 */
router.post('/request', async (req, res) => {
  try {
    if (!interventionService) {
      return res.status(500).json({
        success: false,
        error: 'SERVICE_NOT_INITIALIZED',
        message: 'Intervention service not initialized'
      });
    }

    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User authentication required'
      });
    }

    const interventionRequest: ManualInterventionRequest = {
      userId,
      type: req.body.type,
      reason: req.body.reason,
      durationMinutes: req.body.durationMinutes,
      metadata: req.body.metadata || {}
    };

    const result = await interventionService.requestIntervention(interventionRequest);

    res.status(result.success ? 200 : 400).json(result);

  } catch (error) {
    console.error('Intervention request error:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Internal server error'
    });
  }
});

/**
 * Approve or reject pending intervention
 * POST /api/intervention/:sessionId/approve
 */
router.post('/:sessionId/approve', async (req, res) => {
  try {
    if (!interventionService) {
      return res.status(500).json({
        success: false,
        error: 'SERVICE_NOT_INITIALIZED',
        message: 'Intervention service not initialized'
      });
    }

    const userId = req.user?.id;
    const { sessionId } = req.params;
    const { approved, comments } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User authentication required'
      });
    }

    if (typeof approved !== 'boolean') {
      return res.status(400).json({
        success: false,
        error: 'INVALID_REQUEST',
        message: 'approved field must be a boolean'
      });
    }

    const result = await interventionService.approveIntervention(
      sessionId,
      userId,
      approved,
      comments
    );

    res.status(result.success ? 200 : 400).json(result);

  } catch (error) {
    console.error('Intervention approval error:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Internal server error'
    });
  }
});

/**
 * Cancel intervention session
 * POST /api/intervention/:sessionId/cancel
 */
router.post('/:sessionId/cancel', async (req, res) => {
  try {
    if (!interventionService) {
      return res.status(500).json({
        success: false,
        error: 'SERVICE_NOT_INITIALIZED',
        message: 'Intervention service not initialized'
      });
    }

    const userId = req.user?.id;
    const { sessionId } = req.params;
    const { reason } = req.body;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User authentication required'
      });
    }

    const result = await interventionService.cancelIntervention(
      sessionId,
      userId,
      reason
    );

    res.status(result.success ? 200 : 400).json(result);

  } catch (error) {
    console.error('Intervention cancellation error:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Internal server error'
    });
  }
});

/**
 * Get active intervention sessions
 * GET /api/intervention/sessions
 */
router.get('/sessions', async (req, res) => {
  try {
    if (!interventionService) {
      return res.status(500).json({
        success: false,
        error: 'SERVICE_NOT_INITIALIZED',
        message: 'Intervention service not initialized'
      });
    }

    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User authentication required'
      });
    }

    // Regular users can only see their own sessions
    // Admins can see all sessions (determined by query param)
    const showAll = req.query.all === 'true' && await isAdminUser(userId);
    const sessions = interventionService.getActiveSessions(showAll ? undefined : userId);

    res.json({
      success: true,
      data: sessions,
      meta: {
        total: sessions.length,
        showingAll: showAll
      }
    });

  } catch (error) {
    console.error('Get sessions error:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Internal server error'
    });
  }
});

/**
 * Get intervention session details
 * GET /api/intervention/sessions/:sessionId
 */
router.get('/sessions/:sessionId', async (req, res) => {
  try {
    if (!interventionService) {
      return res.status(500).json({
        success: false,
        error: 'SERVICE_NOT_INITIALIZED',
        message: 'Intervention service not initialized'
      });
    }

    const userId = req.user?.id;
    const { sessionId } = req.params;

    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User authentication required'
      });
    }

    const sessions = interventionService.getActiveSessions();
    const session = sessions.find(s => s.id === sessionId);

    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'SESSION_NOT_FOUND',
        message: 'Intervention session not found'
      });
    }

    // Check if user can view this session
    const isOwner = session.userId === userId;
    const isAdmin = await isAdminUser(userId);

    if (!isOwner && !isAdmin) {
      return res.status(403).json({
        success: false,
        error: 'FORBIDDEN',
        message: 'Not authorized to view this session'
      });
    }

    res.json({
      success: true,
      data: session
    });

  } catch (error) {
    console.error('Get session details error:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Internal server error'
    });
  }
});

/**
 * Get available intervention types
 * GET /api/intervention/types
 */
router.get('/types', async (req, res) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: 'UNAUTHORIZED',
        message: 'User authentication required'
      });
    }

    const isAdmin = await isAdminUser(userId);

    const interventionTypes = [
      {
        type: 'MANUAL_HEALTH_CHECK',
        name: 'Manual Health Check',
        description: 'Trigger immediate health check for broker(s)',
        requiresApproval: false,
        adminOnly: false,
        parameters: [
          { name: 'brokerId', type: 'string', required: false, description: 'Specific broker ID (optional)' }
        ]
      },
      {
        type: 'RESET_BROKER_STATUS',
        name: 'Reset Broker Status',
        description: 'Reset broker status to active and clear failure count',
        requiresApproval: false,
        adminOnly: false,
        parameters: [
          { name: 'brokerId', type: 'string', required: true, description: 'Broker ID to reset' }
        ]
      },
      {
        type: 'ENABLE_AUTO_FAILOVER',
        name: 'Enable Auto-Failover',
        description: 'Re-enable automatic failover that was previously disabled',
        requiresApproval: false,
        adminOnly: false,
        parameters: [
          { name: 'brokerId', type: 'string', required: false, description: 'Specific broker ID (optional)' }
        ]
      },
      {
        type: 'FORCE_FAILOVER',
        name: 'Force Failover',
        description: 'Force immediate failover from one broker to another',
        requiresApproval: true,
        adminOnly: true,
        parameters: [
          { name: 'fromBrokerId', type: 'string', required: true, description: 'Source broker ID' },
          { name: 'toBrokerId', type: 'string', required: true, description: 'Target broker ID' }
        ]
      },
      {
        type: 'DISABLE_AUTO_FAILOVER',
        name: 'Disable Auto-Failover',
        description: 'Temporarily disable automatic failover',
        requiresApproval: true,
        adminOnly: true,
        parameters: [
          { name: 'brokerId', type: 'string', required: false, description: 'Specific broker ID (optional)' }
        ]
      },
      {
        type: 'BYPASS_CIRCUIT_BREAKER',
        name: 'Bypass Circuit Breaker',
        description: 'Temporarily bypass circuit breaker for a service',
        requiresApproval: true,
        adminOnly: true,
        parameters: [
          { name: 'brokerId', type: 'string', required: true, description: 'Broker ID' },
          { name: 'service', type: 'string', required: true, description: 'Service name' }
        ]
      },
      {
        type: 'EMERGENCY_SHUTDOWN',
        name: 'Emergency Shutdown',
        description: 'Emergency shutdown of broker system or entire system',
        requiresApproval: true,
        adminOnly: true,
        parameters: [
          { name: 'scope', type: 'string', required: true, description: 'BROKER or SYSTEM' }
        ]
      }
    ];

    // Filter types based on user permissions
    const availableTypes = isAdmin 
      ? interventionTypes 
      : interventionTypes.filter(t => !t.adminOnly);

    res.json({
      success: true,
      data: availableTypes,
      meta: {
        isAdmin,
        total: availableTypes.length
      }
    });

  } catch (error) {
    console.error('Get intervention types error:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Internal server error'
    });
  }
});

/**
 * Get intervention system status
 * GET /api/intervention/status
 */
router.get('/status', async (req, res) => {
  try {
    if (!interventionService) {
      return res.status(500).json({
        success: false,
        error: 'SERVICE_NOT_INITIALIZED',
        message: 'Intervention service not initialized'
      });
    }

    const allSessions = interventionService.getActiveSessions();
    const pendingApprovals = allSessions.filter(s => s.status === 'PENDING_APPROVAL');
    const activeSessions = allSessions.filter(s => s.status === 'ACTIVE');

    res.json({
      success: true,
      data: {
        systemStatus: 'OPERATIONAL',
        timestamp: new Date().toISOString(),
        statistics: {
          totalActiveSessions: activeSessions.length,
          pendingApprovals: pendingApprovals.length,
          activeInterventions: activeSessions.length
        },
        recentActivity: activeSessions.slice(-5).map(session => ({
          id: session.id,
          type: session.type,
          status: session.status,
          startedAt: session.startedAt,
          expiresAt: session.expiresAt
        }))
      }
    });

  } catch (error) {
    console.error('Get intervention status error:', error);
    res.status(500).json({
      success: false,
      error: 'INTERNAL_ERROR',
      message: 'Internal server error'
    });
  }
});

/**
 * Helper function to check if user is admin
 */
async function isAdminUser(userId: string): Promise<boolean> {
  // In a real implementation, this would check user roles from database
  return process.env.ADMIN_USER_IDS?.split(',').includes(userId) || false;
}

export default router;