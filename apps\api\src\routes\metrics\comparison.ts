/**
 * Strategy Comparison API Route
 * 
 * Provides strategy comparison functionality with plain English explanations
 */

import { Request, Response } from 'express';
import { z } from 'zod';
import {
  GetStrategyComparisonResponse,
  StrategyComparison,
  MetricType,
  UserExperienceLevel,
  RiskTolerance,
  ApiResponse,
} from '@golddaddy/types';

import { MetricsTranslationService } from '../../services/metrics/MetricsTranslationService.js';

// Request validation schema
const GetStrategyComparisonSchema = z.object({
  strategyIds: z.array(z.string()).min(2, 'At least 2 strategies required for comparison').max(10, 'Maximum 10 strategies allowed'),
  metricTypes: z.array(z.enum([
    'win_rate',
    'profit_factor',
    'sharpe_ratio',
    'max_drawdown',
    'total_return',
    'calmar_ratio',
    'sortino_ratio',
    'volatility',
    'trade_count',
    'avg_trade_return',
    'health_score'
  ] as const)).optional(),
  sortBy: z.enum([
    'win_rate',
    'profit_factor',
    'sharpe_ratio',
    'max_drawdown',
    'total_return',
    'health_score'
  ] as const).optional().default('health_score'),
  sortOrder: z.enum(['asc', 'desc'] as const).optional().default('desc'),
  includeHealthScore: z.boolean().optional().default(true),
});

// User context validation schema
const UserContextSchema = z.object({
  experienceLevel: z.enum(['beginner', 'intermediate', 'advanced'] as const).default('intermediate'),
  riskTolerance: z.enum(['conservative', 'moderate', 'aggressive'] as const).default('moderate'),
});

// Initialize service
const metricsTranslationService = new MetricsTranslationService();

/**
 * POST /api/metrics/comparison
 * Compare multiple strategies with plain English explanations
 */
export async function compareStrategies(req: Request, res: Response): Promise<void> {
  try {
    // Validate request body
    const bodyValidation = GetStrategyComparisonSchema.safeParse(req.body);
    if (!bodyValidation.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid request body',
        message: bodyValidation.error.errors.map(e => e.message).join(', '),
      } as ApiResponse);
      return;
    }

    // Get user context
    const userContextValidation = UserContextSchema.safeParse({
      experienceLevel: req.headers['x-user-experience'],
      riskTolerance: req.headers['x-user-risk-tolerance'],
    });

    if (!userContextValidation.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid user context',
        message: userContextValidation.error.errors.map(e => e.message).join(', '),
      } as ApiResponse);
      return;
    }

    const userId = req.headers['x-user-id'] as string || 'mock-user-id';
    const userExperience = userContextValidation.data.experienceLevel;
    const userRiskTolerance = userContextValidation.data.riskTolerance;

    // Get translated metrics for each strategy
    const strategies: StrategyComparison[] = [];
    const errors: { strategyId: string; error: string }[] = [];

    for (const strategyId of bodyValidation.data.strategyIds) {
      try {
        const metricsResult = await metricsTranslationService.getTranslatedMetrics(
          {
            strategyId,
            metricTypes: bodyValidation.data.metricTypes,
          },
          userId,
          userExperience,
          userRiskTolerance
        );

        // Build strategy comparison object
        const comparison: StrategyComparison = {
          strategyId,
          strategyName: metricsResult.strategyName,
          healthScore: metricsResult.healthScore,
          keyMetrics: {
            winRate: metricsResult.metrics.find(m => m.metricType === 'win_rate') || { metricType: 'win_rate' as const, value: 0, displayValue: '0%', explanation: 'No data available' },
            profitFactor: metricsResult.metrics.find(m => m.metricType === 'profit_factor') || { metricType: 'profit_factor' as const, value: 1, displayValue: '1.0', explanation: 'No data available' },
            sharpeRatio: metricsResult.metrics.find(m => m.metricType === 'sharpe_ratio') || { metricType: 'sharpe_ratio' as const, value: 0, displayValue: '0.0', explanation: 'No data available' },
            maxDrawdown: metricsResult.metrics.find(m => m.metricType === 'max_drawdown') || { metricType: 'max_drawdown' as const, value: 0, displayValue: '0%', explanation: 'No data available' },
          },
          ranking: {
            position: 0, // Will be set after sorting
            totalStrategies: bodyValidation.data.strategyIds.length,
            percentile: 0, // Will be calculated after sorting
          },
          suitability: {
            marketRegimes: determineSuitableMarketRegimes(metricsResult.healthScore),
            riskProfile: determineRiskProfile(metricsResult.healthScore, metricsResult.metrics),
            timeCommitment: determineTimeCommitment(
              metricsResult.metrics.find(m => m.metricType === 'trade_count')?.originalValue || 0
            ),
          },
        };

        strategies.push(comparison);
      } catch (error) {
        console.error(`Error processing strategy ${strategyId}:`, error);
        errors.push({
          strategyId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    if (strategies.length === 0) {
      res.status(400).json({
        success: false,
        error: 'No strategies could be processed',
        data: { errors },
      } as ApiResponse);
      return;
    }

    // Sort strategies
    const sortedStrategies = sortStrategiesByMetric(
      strategies,
      bodyValidation.data.sortBy,
      bodyValidation.data.sortOrder
    );

    // Update rankings and percentiles
    sortedStrategies.forEach((strategy, index) => {
      strategy.ranking.position = index + 1;
      strategy.ranking.percentile = Math.round(((strategies.length - index) / strategies.length) * 100);
    });

    // Generate summary
    const summary = generateComparisonSummary(sortedStrategies, userExperience);

    // Build response
    const response: GetStrategyComparisonResponse = {
      strategies: sortedStrategies,
      summary,
      generatedAt: new Date(),
    };

    res.status(200).json({
      success: true,
      data: response,
      errors: errors.length > 0 ? errors : undefined,
    } as ApiResponse<GetStrategyComparisonResponse>);

  } catch (error) {
    console.error('Error comparing strategies:', error);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    } as ApiResponse);
  }
}

/**
 * GET /api/metrics/comparison/quick/:strategyId1/:strategyId2
 * Quick comparison between two strategies
 */
export async function quickCompareStrategies(req: Request, res: Response): Promise<void> {
  try {
    const { strategyId1, strategyId2 } = req.params;
    
    if (!strategyId1 || !strategyId2) {
      res.status(400).json({
        success: false,
        error: 'Both strategy IDs are required',
      } as ApiResponse);
      return;
    }

    // Use the main comparison endpoint logic
    req.body = {
      strategyIds: [strategyId1, strategyId2],
      sortBy: 'health_score',
      sortOrder: 'desc',
      includeHealthScore: true,
    };

    await compareStrategies(req, res);

  } catch (error) {
    console.error('Error in quick strategy comparison:', error);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    } as ApiResponse);
  }
}

// ===== Helper Functions =====

function sortStrategiesByMetric(
  strategies: StrategyComparison[],
  sortBy: MetricType,
  sortOrder: 'asc' | 'desc'
): StrategyComparison[] {
  return strategies.sort((a, b) => {
    let valueA: number;
    let valueB: number;

    if (sortBy === 'health_score') {
      valueA = a.healthScore.overall;
      valueB = b.healthScore.overall;
    } else {
      const metricA = a.keyMetrics[sortBy as keyof typeof a.keyMetrics];
      const metricB = b.keyMetrics[sortBy as keyof typeof b.keyMetrics];
      
      if (!metricA || !metricB) {
        return 0; // Keep original order if metrics not found
      }
      
      valueA = metricA.originalValue;
      valueB = metricB.originalValue;
    }

    // For max_drawdown, lower is better (reverse the comparison)
    if (sortBy === 'max_drawdown') {
      return sortOrder === 'desc' ? valueA - valueB : valueB - valueA;
    }

    return sortOrder === 'desc' ? valueB - valueA : valueA - valueB;
  });
}

function determineSuitableMarketRegimes(healthScore: StrategyHealthScore): string[] {
  const regimes: string[] = [];
  const { components } = healthScore;

  if (components.consistency >= 70) {
    regimes.push('ranging markets');
  }
  if (components.profitability >= 70) {
    regimes.push('trending markets');
  }
  if (components.riskManagement >= 80) {
    regimes.push('volatile markets');
  }
  if (regimes.length === 0) {
    regimes.push('stable markets');
  }

  return regimes;
}

function determineRiskProfile(
  healthScore: StrategyHealthScore,
  metrics: any[]
): RiskTolerance {
  const maxDrawdownMetric = metrics.find(m => m.metricType === 'max_drawdown');
  const volatilityMetric = metrics.find(m => m.metricType === 'volatility');
  
  const maxDrawdown = maxDrawdownMetric?.originalValue || 0.15;
  const volatility = volatilityMetric?.originalValue || 0.20;

  if (maxDrawdown <= 0.05 && volatility <= 0.10) {
    return 'conservative';
  } else if (maxDrawdown <= 0.15 && volatility <= 0.20) {
    return 'moderate';
  } else {
    return 'aggressive';
  }
}

function determineTimeCommitment(tradeCount: number): 'low' | 'medium' | 'high' {
  if (tradeCount <= 20) {
    return 'low';
  } else if (tradeCount <= 100) {
    return 'medium';
  } else {
    return 'high';
  }
}

function generateComparisonSummary(
  strategies: StrategyComparison[],
  userExperience: UserExperienceLevel
): {
  topPerformer: string;
  mostConsistent: string;
  lowestRisk: string;
  recommendations: string[];
} {
  const topPerformer = strategies[0]; // First after sorting by health score
  
  const mostConsistent = strategies.reduce((prev, current) => 
    prev.healthScore.components.consistency > current.healthScore.components.consistency ? prev : current
  );
  
  const lowestRisk = strategies.reduce((prev, current) => {
    const prevDrawdown = prev.keyMetrics.maxDrawdown.originalValue;
    const currentDrawdown = current.keyMetrics.maxDrawdown.originalValue;
    return prevDrawdown < currentDrawdown ? prev : current;
  });

  const recommendations: string[] = [];

  if (userExperience === 'beginner') {
    recommendations.push(`Start with ${lowestRisk.strategyName} for lower risk`);
    recommendations.push(`Consider ${mostConsistent.strategyName} for steady performance`);
    if (topPerformer.strategyId !== lowestRisk.strategyId) {
      recommendations.push(`Graduate to ${topPerformer.strategyName} as you gain experience`);
    }
  } else if (userExperience === 'intermediate') {
    recommendations.push(`${topPerformer.strategyName} offers the best overall balance`);
    recommendations.push(`Compare ${topPerformer.strategyName} with ${mostConsistent.strategyName} based on your risk tolerance`);
    recommendations.push('Consider combining strategies for diversification');
  } else {
    recommendations.push(`${topPerformer.strategyName} shows superior risk-adjusted returns`);
    recommendations.push('Analyze correlation between strategies for portfolio construction');
    recommendations.push('Consider dynamic allocation based on market regime detection');
  }

  return {
    topPerformer: topPerformer.strategyName,
    mostConsistent: mostConsistent.strategyName,
    lowestRisk: lowestRisk.strategyName,
    recommendations,
  };
}

// Export route handlers
export const comparisonRoutes = {
  compareStrategies,
  quickCompareStrategies,
};