import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { ExecutionQualityAnalyzer } from '../ExecutionQualityAnalyzer';
import { ExecutionQuality } from '@golddaddy/types';

describe('ExecutionQualityAnalyzer', () => {
  let analyzer: ExecutionQualityAnalyzer;

  beforeEach(() => {
    analyzer = new ExecutionQualityAnalyzer();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
    analyzer.stopContinuousMonitoring();
  });

  const createMockExecution = (overrides: Partial<ExecutionQuality> = {}): ExecutionQuality => ({
    executionId: 'exec-123',
    tradeId: 'trade-123',
    brokerId: 'broker-1',
    instrument: 'EURUSD',
    latency: 50,
    slippage: new Decimal(0.5),
    executedPrice: new Decimal(1.1234),
    requestedPrice: new Decimal(1.1230),
    volume: new Decimal(10000),
    success: true,
    overallScore: new Decimal(85),
    speedScore: new Decimal(90),
    priceImprovementScore: new Decimal(80),
    marketVolatility: new Decimal(0.8),
    timestamp: new Date(),
    ...overrides
  });

  describe('analyzeExecutionQuality', () => {
    it('should analyze execution quality for a single broker', async () => {
      const execution = createMockExecution();
      
      const metrics = await analyzer.analyzeExecutionQuality('broker-1', execution);

      expect(metrics.brokerId).toBe('broker-1');
      expect(metrics.totalExecutions).toBe(1);
      expect(metrics.averageLatency.toNumber()).toBe(50);
      expect(metrics.averageSlippage.toNumber()).toBe(0.5);
      expect(metrics.successRate.toNumber()).toBe(100);
      expect(metrics.qualityScore.toNumber()).toBe(85);
    });

    it('should update running averages correctly', async () => {
      const execution1 = createMockExecution({
        latency: 40,
        slippage: new Decimal(0.3),
        overallScore: new Decimal(90)
      });
      const execution2 = createMockExecution({
        latency: 60,
        slippage: new Decimal(0.7),
        overallScore: new Decimal(80)
      });

      await analyzer.analyzeExecutionQuality('broker-1', execution1);
      const metrics = await analyzer.analyzeExecutionQuality('broker-1', execution2);

      expect(metrics.totalExecutions).toBe(2);
      expect(metrics.averageLatency.toNumber()).toBe(50); // (40 + 60) / 2
      expect(metrics.averageSlippage.toNumber()).toBe(0.5); // (0.3 + 0.7) / 2
      expect(metrics.qualityScore.toNumber()).toBe(85); // (90 + 80) / 2
    });

    it('should calculate success rate correctly', async () => {
      const successExecution = createMockExecution({ success: true });
      const failedExecution = createMockExecution({ success: false });

      await analyzer.analyzeExecutionQuality('broker-1', successExecution);
      const metrics = await analyzer.analyzeExecutionQuality('broker-1', failedExecution);

      expect(metrics.successRate.toNumber()).toBe(50); // 1 success out of 2 total
    });

    it('should limit execution history to 1000 entries per broker', async () => {
      const execution = createMockExecution();

      // Add 1001 executions
      for (let i = 0; i < 1001; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', {
          ...execution,
          executionId: `exec-${i}`,
          timestamp: new Date(Date.now() + i * 1000)
        });
      }

      const metrics = await analyzer.analyzeExecutionQuality('broker-1', execution);
      expect(metrics.totalExecutions).toBe(1002);
      
      // History should be limited to 1000 (internal check)
      const efficiency = await analyzer.getExecutionEfficiencyMetrics('broker-1');
      expect(efficiency.totalExecutions).toBe(1000); // Limited to last 1000 executions
    });
  });

  describe('benchmarkBrokers', () => {
    beforeEach(async () => {
      // Setup test data for multiple brokers
      const timeWindow = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      // Broker 1 - Good performance
      for (let i = 0; i < 100; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
          executionId: `broker1-exec-${i}`,
          latency: 30 + Math.random() * 20, // 30-50ms
          slippage: new Decimal((Math.random() * 0.5).toFixed(4)), // 0-0.5 pips
          overallScore: new Decimal(85 + Math.random() * 10), // 85-95
          timestamp: new Date('2024-01-15')
        }));
      }

      // Broker 2 - Poor performance
      for (let i = 0; i < 80; i++) {
        await analyzer.analyzeExecutionQuality('broker-2', createMockExecution({
          brokerId: 'broker-2',
          executionId: `broker2-exec-${i}`,
          latency: 80 + Math.random() * 40, // 80-120ms
          slippage: new Decimal((1 + Math.random() * 1).toFixed(4)), // 1-2 pips
          overallScore: new Decimal(60 + Math.random() * 20), // 60-80
          success: Math.random() > 0.1, // 90% success rate
          timestamp: new Date('2024-01-15')
        }));
      }
    });

    it('should benchmark multiple brokers and rank them', async () => {
      const timeWindow = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      const benchmarks = await analyzer.benchmarkBrokers(['broker-1', 'broker-2'], timeWindow);

      expect(benchmarks).toHaveLength(2);
      expect(benchmarks[0].ranking).toBe(1); // Best performer
      expect(benchmarks[1].ranking).toBe(2); // Worst performer
      
      // Broker 1 should outperform Broker 2
      expect(benchmarks[0].brokerId).toBe('broker-1');
      expect(benchmarks[1].brokerId).toBe('broker-2');
      expect(benchmarks[0].compositeScore.gt(benchmarks[1].compositeScore)).toBe(true);
    });

    it('should calculate comprehensive latency metrics', async () => {
      const timeWindow = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      const benchmarks = await analyzer.benchmarkBrokers(['broker-1'], timeWindow);
      const broker1Benchmark = benchmarks[0];

      expect(broker1Benchmark.latencyMetrics.average.toNumber()).toBeGreaterThan(30);
      expect(broker1Benchmark.latencyMetrics.average.toNumber()).toBeLessThan(50);
      expect(broker1Benchmark.latencyMetrics.median.toNumber()).toBeGreaterThan(0);
      expect(broker1Benchmark.latencyMetrics.p95.toNumber()).toBeGreaterThan(broker1Benchmark.latencyMetrics.median.toNumber());
      expect(broker1Benchmark.latencyMetrics.p99.toNumber()).toBeGreaterThan(broker1Benchmark.latencyMetrics.p95.toNumber());
    });

    it('should calculate slippage metrics accurately', async () => {
      const timeWindow = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      const benchmarks = await analyzer.benchmarkBrokers(['broker-1'], timeWindow);
      const broker1Benchmark = benchmarks[0];

      expect(broker1Benchmark.slippageMetrics.average.toNumber()).toBeGreaterThan(0);
      expect(broker1Benchmark.slippageMetrics.average.toNumber()).toBeLessThan(0.5);
      expect(broker1Benchmark.slippageMetrics.positiveSlippageRate.toNumber()).toBeGreaterThan(0);
    });

    it('should emit brokerBenchmarkCompleted event', async () => {
      const eventPromise = new Promise((resolve) => {
        analyzer.once('brokerBenchmarkCompleted', resolve);
      });

      const timeWindow = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      await analyzer.benchmarkBrokers(['broker-1', 'broker-2'], timeWindow);
      
      const event = await eventPromise;
      expect(event).toMatchObject({
        timeWindow,
        totalBrokers: 2
      });
    });
  });

  describe('getExecutionEfficiencyMetrics', () => {
    beforeEach(async () => {
      // Setup test data with varying performance
      const executions = [
        createMockExecution({ latency: 30, slippage: new Decimal(0.2) }),
        createMockExecution({ latency: 50, slippage: new Decimal(0.8) }),
        createMockExecution({ latency: 40, slippage: new Decimal(-0.3) }),
        createMockExecution({ latency: 70, slippage: new Decimal(1.2) }),
        createMockExecution({ latency: 35, slippage: new Decimal(0.5) })
      ];

      for (const execution of executions) {
        await analyzer.analyzeExecutionQuality('broker-1', execution);
      }
    });

    it('should calculate comprehensive efficiency metrics', async () => {
      const metrics = await analyzer.getExecutionEfficiencyMetrics('broker-1');

      expect(metrics.brokerId).toBe('broker-1');
      expect(metrics.totalExecutions).toBe(5);
      expect(metrics.averageLatency.toNumber()).toBe(45); // (30+50+40+70+35)/5
      expect(metrics.successRate.toNumber()).toBe(100);
      expect(metrics.analysisTimestamp).toBeInstanceOf(Date);
    });

    it('should calculate median and percentile values correctly', async () => {
      const metrics = await analyzer.getExecutionEfficiencyMetrics('broker-1');

      // Latencies: [30, 35, 40, 50, 70] (sorted)
      expect(metrics.medianLatency.toNumber()).toBe(40);
      expect(metrics.p95Latency.toNumber()).toBe(70); // 95th percentile
      expect(metrics.p99Latency.toNumber()).toBe(70); // 99th percentile
    });

    it('should calculate positive slippage rate', async () => {
      const metrics = await analyzer.getExecutionEfficiencyMetrics('broker-1');

      // Slippages: [0.2, 0.8, -0.3, 1.2, 0.5] - 4 out of 5 are positive
      expect(metrics.positiveSlippageRate.toNumber()).toBe(80);
    });

    it('should throw error for broker with no execution history', async () => {
      await expect(analyzer.getExecutionEfficiencyMetrics('nonexistent-broker'))
        .rejects.toThrow('No execution history found for broker nonexistent-broker');
    });
  });

  describe('compareExecutionAcrossBrokers', () => {
    beforeEach(async () => {
      const timeWindow = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      // Setup different performance for EURUSD vs GBPUSD
      const eurExecution = createMockExecution({
        instrument: 'EURUSD',
        latency: 30,
        timestamp: new Date('2024-01-15')
      });
      const gbpExecution = createMockExecution({
        instrument: 'GBPUSD',
        latency: 60,
        timestamp: new Date('2024-01-15')
      });

      await analyzer.analyzeExecutionQuality('broker-1', eurExecution);
      await analyzer.analyzeExecutionQuality('broker-1', gbpExecution);
    });

    it('should compare execution across brokers for specific instrument', async () => {
      const timeWindow = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      const comparison = await analyzer.compareExecutionAcrossBrokers(
        ['broker-1'],
        'EURUSD',
        timeWindow
      );

      expect(comparison).toHaveProperty('broker-1');
      expect(comparison['broker-1'].totalExecutions).toBe(1);
      expect(comparison['broker-1'].averageLatency.toNumber()).toBe(30);
    });

    it('should emit brokerComparisonCompleted event', async () => {
      const eventPromise = new Promise((resolve) => {
        analyzer.once('brokerComparisonCompleted', resolve);
      });

      const timeWindow = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      await analyzer.compareExecutionAcrossBrokers(['broker-1'], 'EURUSD', timeWindow);
      
      const event = await eventPromise;
      expect(event).toMatchObject({
        instrument: 'EURUSD',
        timeWindow,
        brokers: ['broker-1']
      });
    });
  });

  describe('continuous monitoring', () => {
    it('should start and stop continuous monitoring', async () => {
      const startEventPromise = new Promise((resolve) => {
        analyzer.once('monitoringStarted', resolve);
      });
      const stopEventPromise = new Promise((resolve) => {
        analyzer.once('monitoringStopped', resolve);
      });

      await analyzer.startContinuousMonitoring(1000);
      
      const startEvent = await startEventPromise;
      expect(startEvent).toMatchObject({ interval: 1000 });

      await analyzer.stopContinuousMonitoring();
      
      const stopEvent = await stopEventPromise;
      expect(stopEvent).toHaveProperty('timestamp');
    });

    it('should not start monitoring twice', async () => {
      await analyzer.startContinuousMonitoring();
      await analyzer.startContinuousMonitoring(); // Should not throw or create duplicate intervals
      
      await analyzer.stopContinuousMonitoring();
    });
  });

  describe('quality degradation detection', () => {
    it('should detect latency degradation', async () => {
      const alertPromise = new Promise((resolve) => {
        analyzer.once('qualityDegradationAlert', resolve);
      });

      // Create baseline performance (good)
      for (let i = 0; i < 20; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
          executionId: `baseline-${i}`,
          latency: 30 + Math.random() * 5, // 30-35ms
          timestamp: new Date(Date.now() - (20 - i) * 1000)
        }));
      }

      // Create recent poor performance (latency spike)
      for (let i = 0; i < 15; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
          executionId: `recent-${i}`,
          latency: 80 + Math.random() * 10, // 80-90ms (>100% increase)
          timestamp: new Date(Date.now() - i * 1000)
        }));
      }

      const alert = await alertPromise;
      expect(alert).toMatchObject({
        type: 'LATENCY_DEGRADATION',
        brokerId: 'broker-1',
        severity: expect.stringMatching(/LOW|MEDIUM|HIGH/)
      });
    });

    it('should detect slippage degradation', async () => {
      const alertPromise = new Promise((resolve) => {
        analyzer.once('qualityDegradationAlert', resolve);
      });

      // Create baseline performance (low slippage)
      for (let i = 0; i < 20; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
          executionId: `baseline-${i}`,
          slippage: new Decimal((0.1 + Math.random() * 0.1).toFixed(4)), // 0.1-0.2 pips
          timestamp: new Date(Date.now() - (20 - i) * 1000)
        }));
      }

      // Create recent poor performance (high slippage)
      for (let i = 0; i < 15; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
          executionId: `recent-${i}`,
          slippage: new Decimal((2.0 + Math.random() * 0.5).toFixed(4)), // 2.0-2.5 pips (>1000% increase)
          timestamp: new Date(Date.now() - i * 1000)
        }));
      }

      const alert = await alertPromise;
      expect(alert).toMatchObject({
        type: 'SLIPPAGE_DEGRADATION',
        brokerId: 'broker-1',
        severity: 'HIGH'
      });
    });

    it('should detect success rate degradation', async () => {
      const alertPromise = new Promise((resolve) => {
        analyzer.once('qualityDegradationAlert', resolve);
      });

      // Create baseline performance (100% success)
      for (let i = 0; i < 20; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
          executionId: `baseline-${i}`,
          success: true,
          timestamp: new Date(Date.now() - (20 - i) * 1000)
        }));
      }

      // Create recent poor performance (70% success)
      for (let i = 0; i < 15; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
          executionId: `recent-${i}`,
          success: i < 10, // 10 out of 15 successful (66.7%)
          timestamp: new Date(Date.now() - i * 1000)
        }));
      }

      const alert = await alertPromise;
      expect(alert).toMatchObject({
        type: 'SUCCESS_RATE_DEGRADATION',
        brokerId: 'broker-1'
      });
    });
  });

  describe('statistical calculations', () => {
    it('should calculate volume-weighted average price correctly', async () => {
      const executions = [
        createMockExecution({
          executedPrice: new Decimal(1.1200),
          volume: new Decimal(100000)
        }),
        createMockExecution({
          executedPrice: new Decimal(1.1250),
          volume: new Decimal(200000)
        }),
        createMockExecution({
          executedPrice: new Decimal(1.1300),
          volume: new Decimal(300000)
        })
      ];

      for (const execution of executions) {
        await analyzer.analyzeExecutionQuality('broker-1', execution);
      }

      const benchmarks = await analyzer.benchmarkBrokers(['broker-1'], {
        start: new Date(Date.now() - 86400000),
        end: new Date()
      });

      // VWAP = (1.12*100000 + 1.125*200000 + 1.13*300000) / (100000+200000+300000)
      const expectedVWAP = new Decimal(1.12).times(100000)
        .plus(new Decimal(1.125).times(200000))
        .plus(new Decimal(1.13).times(300000))
        .div(600000);

      expect(benchmarks[0].volumeWeightedMetrics.averagePrice.toNumber())
        .toBeCloseTo(expectedVWAP.toNumber(), 4);
      expect(benchmarks[0].volumeWeightedMetrics.totalVolume.toNumber()).toBe(600000);
    });

    it('should calculate quality consistency correctly', async () => {
      // Create executions with consistent quality scores
      const consistentExecutions = [
        createMockExecution({ overallScore: new Decimal(85) }),
        createMockExecution({ overallScore: new Decimal(87) }),
        createMockExecution({ overallScore: new Decimal(83) }),
        createMockExecution({ overallScore: new Decimal(86) }),
        createMockExecution({ overallScore: new Decimal(84) })
      ];

      for (const execution of consistentExecutions) {
        await analyzer.analyzeExecutionQuality('broker-1', execution);
      }

      const metrics = await analyzer.getExecutionEfficiencyMetrics('broker-1');
      
      // Quality consistency should be high (low coefficient of variation)
      expect(metrics.qualityConsistency.toNumber()).toBeGreaterThan(90);
    });
  });

  describe('trend analysis', () => {
    it('should detect improving trend', async () => {
      // Create improving latency trend
      for (let i = 0; i < 30; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
          executionId: `trend-${i}`,
          latency: 100 - i * 2, // Improving from 100ms to 40ms
          timestamp: new Date(Date.now() - (30 - i) * 1000)
        }));
      }

      const metrics = await analyzer.getExecutionEfficiencyMetrics('broker-1');
      expect(metrics.latencyTrend).toBe('IMPROVING');
    });

    it('should detect stable trend', async () => {
      // Create stable performance
      for (let i = 0; i < 30; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
          executionId: `stable-${i}`,
          latency: 50 + (Math.random() - 0.5) * 2, // 49-51ms (very stable)
          timestamp: new Date(Date.now() - (30 - i) * 1000)
        }));
      }

      const metrics = await analyzer.getExecutionEfficiencyMetrics('broker-1');
      expect(metrics.latencyTrend).toBe('STABLE');
    });

    it('should detect degrading trend', async () => {
      // Create degrading latency trend
      for (let i = 0; i < 30; i++) {
        await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
          executionId: `degrading-${i}`,
          latency: 40 + i * 3, // Degrading from 40ms to 130ms
          timestamp: new Date(Date.now() - (30 - i) * 1000)
        }));
      }

      const metrics = await analyzer.getExecutionEfficiencyMetrics('broker-1');
      expect(metrics.latencyTrend).toBe('DEGRADING');
    });
  });

  describe('market condition analysis', () => {
    beforeEach(async () => {
      // Setup executions with different market volatilities
      const executions = [
        // Low volatility executions
        ...Array.from({ length: 10 }, (_, i) => createMockExecution({
          executionId: `low-vol-${i}`,
          marketVolatility: new Decimal(0.3),
          latency: 40 + Math.random() * 10
        })),
        // Medium volatility executions
        ...Array.from({ length: 15 }, (_, i) => createMockExecution({
          executionId: `med-vol-${i}`,
          marketVolatility: new Decimal(1.0),
          latency: 50 + Math.random() * 15
        })),
        // High volatility executions
        ...Array.from({ length: 8 }, (_, i) => createMockExecution({
          executionId: `high-vol-${i}`,
          marketVolatility: new Decimal(2.0),
          latency: 70 + Math.random() * 20
        }))
      ];

      for (const execution of executions) {
        await analyzer.analyzeExecutionQuality('broker-1', execution);
      }
    });

    it('should group performance by market volatility', async () => {
      const metrics = await analyzer.getExecutionEfficiencyMetrics('broker-1');

      expect(metrics.performanceByVolatility.low).toBeDefined();
      expect(metrics.performanceByVolatility.low.count).toBe(10);
      expect(metrics.performanceByVolatility.low.averageLatency.toNumber()).toBeLessThan(50);

      expect(metrics.performanceByVolatility.medium).toBeDefined();
      expect(metrics.performanceByVolatility.medium.count).toBe(15);

      expect(metrics.performanceByVolatility.high).toBeDefined();
      expect(metrics.performanceByVolatility.high.count).toBe(8);
      expect(metrics.performanceByVolatility.high.averageLatency.toNumber()).toBeGreaterThan(70);
    });

    it('should group performance by time of day', async () => {
      // Add executions at different times
      await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
        executionId: 'morning',
        timestamp: new Date('2024-01-01T09:00:00Z')
      }));
      
      await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
        executionId: 'afternoon',
        timestamp: new Date('2024-01-01T15:00:00Z')
      }));

      await analyzer.analyzeExecutionQuality('broker-1', createMockExecution({
        executionId: 'evening',
        timestamp: new Date('2024-01-01T21:00:00Z')
      }));

      const metrics = await analyzer.getExecutionEfficiencyMetrics('broker-1');

      expect(metrics.performanceByTimeOfDay.morning).toBeDefined();
      expect(metrics.performanceByTimeOfDay.afternoon).toBeDefined();
      expect(metrics.performanceByTimeOfDay.evening).toBeDefined();
    });
  });
});