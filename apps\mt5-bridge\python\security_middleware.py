"""
Production Security Middleware
Enhanced rate limiting, API security, and access control for production environment
"""

import time
import hashlib
import asyncio
from typing import Dict, Optional, List, Tuple, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque
from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from loguru import logger
import redis.asyncio as redis
import jwt
import ipaddress

@dataclass
class RateLimitRule:
    """Rate limiting rule configuration"""
    name: str
    requests_per_minute: int
    requests_per_hour: int
    requests_per_day: int
    burst_limit: int  # Maximum requests in burst window
    burst_window_seconds: int = 60
    enabled: bool = True
    priority: int = 1  # Higher priority rules checked first

@dataclass
class SecurityConfig:
    """Security configuration"""
    jwt_secret: str
    jwt_algorithm: str = "HS256"
    jwt_expiry_hours: int = 24
    api_key_header: str = "X-API-Key"
    rate_limit_header_prefix: str = "X-RateLimit"
    blocked_ips: List[str] = None
    allowed_ips: List[str] = None  # If set, only these IPs allowed
    max_request_size_mb: int = 10
    enable_cors: bool = True
    cors_origins: List[str] = None

class SecurityMiddleware:
    """Advanced security middleware for production API protection"""
    
    def __init__(self, config: SecurityConfig, redis_client: Optional[redis.Redis] = None):
        self.config = config
        self.redis_client = redis_client
        
        # In-memory rate limiting (fallback if Redis unavailable)
        self.memory_limits: Dict[str, deque] = defaultdict(lambda: deque(maxlen=10000))
        self.blocked_ips: set = set(config.blocked_ips or [])
        self.allowed_ips: set = set(config.allowed_ips or []) if config.allowed_ips else None
        
        # Rate limiting rules by endpoint pattern
        self.rate_limit_rules: Dict[str, RateLimitRule] = {}
        self._setup_default_rate_limits()
        
        # Security statistics
        self.security_stats = {
            'requests_blocked': 0,
            'rate_limits_triggered': 0,
            'invalid_auth_attempts': 0,
            'suspicious_activity_detected': 0,
            'last_reset': datetime.now()
        }
        
    def _setup_default_rate_limits(self):
        """Setup default rate limiting rules for different endpoint types"""
        
        # Authentication endpoints - very strict
        self.rate_limit_rules['auth'] = RateLimitRule(
            name='authentication',
            requests_per_minute=5,
            requests_per_hour=20,
            requests_per_day=100,
            burst_limit=3,
            burst_window_seconds=30,
            priority=1
        )
        
        # Trading endpoints - strict for safety
        self.rate_limit_rules['trading'] = RateLimitRule(
            name='trading_operations',
            requests_per_minute=30,
            requests_per_hour=500,
            requests_per_day=2000,
            burst_limit=10,
            burst_window_seconds=60,
            priority=2
        )
        
        # Market data endpoints - moderate limits
        self.rate_limit_rules['market_data'] = RateLimitRule(
            name='market_data_access',
            requests_per_minute=100,
            requests_per_hour=2000,
            requests_per_day=10000,
            burst_limit=50,
            burst_window_seconds=60,
            priority=3
        )
        
        # WebSocket connections - connection limits
        self.rate_limit_rules['websocket'] = RateLimitRule(
            name='websocket_connections',
            requests_per_minute=10,  # New connections
            requests_per_hour=50,
            requests_per_day=200,
            burst_limit=5,
            burst_window_seconds=60,
            priority=2
        )
        
        # General API endpoints
        self.rate_limit_rules['general'] = RateLimitRule(
            name='general_api_access',
            requests_per_minute=200,
            requests_per_hour=5000,
            requests_per_day=50000,
            burst_limit=100,
            burst_window_seconds=60,
            priority=4
        )
        
    def get_endpoint_category(self, path: str) -> str:
        """Determine rate limit category based on endpoint path"""
        if '/auth' in path or '/login' in path or '/token' in path:
            return 'auth'
        elif '/trading' in path or '/trade' in path or '/order' in path:
            return 'trading'
        elif '/market' in path or '/price' in path or '/historical' in path:
            return 'market_data'
        elif '/ws' in path or 'websocket' in path:
            return 'websocket'
        else:
            return 'general'
    
    def get_client_identifier(self, request: Request) -> str:
        """Generate unique client identifier for rate limiting"""
        # Use API key if available, otherwise IP + User-Agent
        api_key = request.headers.get(self.config.api_key_header)
        if api_key:
            return f"api_key:{hashlib.sha256(api_key.encode()).hexdigest()[:16]}"
        
        client_ip = self._get_client_ip(request)
        user_agent = request.headers.get('user-agent', 'unknown')
        ua_hash = hashlib.sha256(user_agent.encode()).hexdigest()[:8]
        
        return f"ip:{client_ip}:ua:{ua_hash}"
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP considering proxy headers"""
        # Check common proxy headers
        forwarded_for = request.headers.get('x-forwarded-for')
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(',')[0].strip()
        
        real_ip = request.headers.get('x-real-ip')
        if real_ip:
            return real_ip.strip()
        
        # Fallback to direct client
        return request.client.host if request.client else 'unknown'
    
    async def check_ip_allowlist(self, request: Request) -> bool:
        """Check if client IP is allowed"""
        client_ip = self._get_client_ip(request)
        
        try:
            client_addr = ipaddress.ip_address(client_ip)
            
            # Check blocked IPs
            for blocked_ip in self.blocked_ips:
                if client_addr in ipaddress.ip_network(blocked_ip, strict=False):
                    logger.warning(f"Blocked IP attempted access: {client_ip}")
                    self.security_stats['requests_blocked'] += 1
                    return False
            
            # Check allowed IPs (if whitelist is configured)
            if self.allowed_ips:
                for allowed_ip in self.allowed_ips:
                    if client_addr in ipaddress.ip_network(allowed_ip, strict=False):
                        return True
                
                logger.warning(f"IP not in allowlist attempted access: {client_ip}")
                self.security_stats['requests_blocked'] += 1
                return False
            
            return True
            
        except ValueError as e:
            logger.error(f"Invalid IP address: {client_ip} - {e}")
            return False
    
    async def check_rate_limit(self, request: Request) -> Tuple[bool, Dict[str, Any]]:
        """Check rate limits for the request"""
        client_id = self.get_client_identifier(request)
        endpoint_category = self.get_endpoint_category(request.url.path)
        rule = self.rate_limit_rules.get(endpoint_category, self.rate_limit_rules['general'])
        
        if not rule.enabled:
            return True, {}
        
        current_time = time.time()
        
        # Use Redis if available, otherwise in-memory
        if self.redis_client:
            return await self._check_redis_rate_limit(client_id, rule, current_time)
        else:
            return await self._check_memory_rate_limit(client_id, rule, current_time)
    
    async def _check_redis_rate_limit(self, client_id: str, rule: RateLimitRule, current_time: float) -> Tuple[bool, Dict[str, Any]]:
        """Redis-based rate limiting with sliding window"""
        
        # Keys for different time windows
        minute_key = f"rate_limit:{client_id}:minute:{int(current_time // 60)}"
        hour_key = f"rate_limit:{client_id}:hour:{int(current_time // 3600)}"
        day_key = f"rate_limit:{client_id}:day:{int(current_time // 86400)}"
        burst_key = f"rate_limit:{client_id}:burst:{int(current_time // rule.burst_window_seconds)}"
        
        try:
            # Pipeline for atomic operations
            pipe = self.redis_client.pipeline()
            
            # Get current counts
            pipe.get(minute_key)
            pipe.get(hour_key)
            pipe.get(day_key)
            pipe.get(burst_key)
            
            results = await pipe.execute()
            
            minute_count = int(results[0] or 0)
            hour_count = int(results[1] or 0)
            day_count = int(results[2] or 0)
            burst_count = int(results[3] or 0)
            
            # Check limits
            if (burst_count >= rule.burst_limit or
                minute_count >= rule.requests_per_minute or
                hour_count >= rule.requests_per_hour or
                day_count >= rule.requests_per_day):
                
                self.security_stats['rate_limits_triggered'] += 1
                
                return False, {
                    'minute_count': minute_count,
                    'hour_count': hour_count,
                    'day_count': day_count,
                    'burst_count': burst_count,
                    'rule': rule.name,
                    'reset_time': int((current_time // 60 + 1) * 60)  # Next minute
                }
            
            # Increment counters
            pipe = self.redis_client.pipeline()
            pipe.incr(minute_key)
            pipe.expire(minute_key, 120)  # 2 minutes TTL
            pipe.incr(hour_key)
            pipe.expire(hour_key, 7200)  # 2 hours TTL
            pipe.incr(day_key)
            pipe.expire(day_key, 172800)  # 2 days TTL
            pipe.incr(burst_key)
            pipe.expire(burst_key, rule.burst_window_seconds * 2)
            
            await pipe.execute()
            
            return True, {
                'minute_count': minute_count + 1,
                'hour_count': hour_count + 1,
                'day_count': day_count + 1,
                'burst_count': burst_count + 1,
                'rule': rule.name
            }
            
        except Exception as e:
            logger.error(f"Redis rate limit check failed: {e}")
            # Fallback to memory-based rate limiting
            return await self._check_memory_rate_limit(client_id, rule, current_time)
    
    async def _check_memory_rate_limit(self, client_id: str, rule: RateLimitRule, current_time: float) -> Tuple[bool, Dict[str, Any]]:
        """In-memory rate limiting with sliding window"""
        
        key = f"{client_id}:{rule.name}"
        request_times = self.memory_limits[key]
        
        # Clean old requests
        cutoff_day = current_time - 86400  # 24 hours
        while request_times and request_times[0] < cutoff_day:
            request_times.popleft()
        
        # Count requests in different windows
        minute_cutoff = current_time - 60
        hour_cutoff = current_time - 3600
        burst_cutoff = current_time - rule.burst_window_seconds
        
        minute_count = sum(1 for t in request_times if t >= minute_cutoff)
        hour_count = sum(1 for t in request_times if t >= hour_cutoff)
        day_count = len(request_times)
        burst_count = sum(1 for t in request_times if t >= burst_cutoff)
        
        # Check limits
        if (burst_count >= rule.burst_limit or
            minute_count >= rule.requests_per_minute or
            hour_count >= rule.requests_per_hour or
            day_count >= rule.requests_per_day):
            
            self.security_stats['rate_limits_triggered'] += 1
            
            return False, {
                'minute_count': minute_count,
                'hour_count': hour_count,
                'day_count': day_count,
                'burst_count': burst_count,
                'rule': rule.name,
                'reset_time': int(minute_cutoff + 60)
            }
        
        # Record this request
        request_times.append(current_time)
        
        return True, {
            'minute_count': minute_count + 1,
            'hour_count': hour_count + 1,
            'day_count': day_count + 1,
            'burst_count': burst_count + 1,
            'rule': rule.name
        }
    
    async def validate_api_key(self, request: Request) -> Optional[Dict[str, Any]]:
        """Validate API key from request headers"""
        api_key = request.headers.get(self.config.api_key_header)
        
        if not api_key:
            return None
        
        try:
            # Decode JWT API key
            payload = jwt.decode(
                api_key, 
                self.config.jwt_secret, 
                algorithms=[self.config.jwt_algorithm]
            )
            
            # Check expiry
            if datetime.utcnow().timestamp() > payload.get('exp', 0):
                logger.warning("Expired API key used")
                self.security_stats['invalid_auth_attempts'] += 1
                return None
            
            return payload
            
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid API key: {e}")
            self.security_stats['invalid_auth_attempts'] += 1
            return None
    
    async def detect_suspicious_activity(self, request: Request, client_id: str) -> bool:
        """Detect suspicious activity patterns"""
        
        # Check for rapid sequential requests (possible bot)
        if self.redis_client:
            rapid_key = f"rapid_requests:{client_id}"
            current_count = await self.redis_client.incr(rapid_key)
            await self.redis_client.expire(rapid_key, 10)  # 10 second window
            
            if current_count > 50:  # More than 50 requests in 10 seconds
                logger.warning(f"Rapid request pattern detected for {client_id}")
                self.security_stats['suspicious_activity_detected'] += 1
                return True
        
        # Check User-Agent patterns
        user_agent = request.headers.get('user-agent', '').lower()
        suspicious_patterns = ['bot', 'crawler', 'spider', 'scraper', 'curl', 'wget']
        
        if any(pattern in user_agent for pattern in suspicious_patterns):
            if not any(allowed in user_agent for allowed in ['googlebot', 'bingbot']):
                logger.info(f"Suspicious user agent: {user_agent}")
                # Don't block, but monitor
        
        # Check for unusual request patterns
        path = request.url.path.lower()
        suspicious_paths = ['.env', 'wp-admin', 'phpmyadmin', '.git', 'config']
        
        if any(pattern in path for pattern in suspicious_paths):
            logger.warning(f"Suspicious path access: {path} from {client_id}")
            self.security_stats['suspicious_activity_detected'] += 1
            return True
        
        return False
    
    async def add_security_headers(self, response_headers: Dict[str, str], rate_limit_info: Dict[str, Any]):
        """Add security headers to response"""
        
        # Rate limiting headers
        if rate_limit_info:
            rule_name = rate_limit_info.get('rule', 'unknown')
            response_headers[f"{self.config.rate_limit_header_prefix}-Rule"] = rule_name
            response_headers[f"{self.config.rate_limit_header_prefix}-Remaining-Minute"] = str(
                self.rate_limit_rules.get(rule_name, self.rate_limit_rules['general']).requests_per_minute - 
                rate_limit_info.get('minute_count', 0)
            )
            
            if 'reset_time' in rate_limit_info:
                response_headers[f"{self.config.rate_limit_header_prefix}-Reset"] = str(rate_limit_info['reset_time'])
        
        # Security headers
        response_headers['X-Content-Type-Options'] = 'nosniff'
        response_headers['X-Frame-Options'] = 'DENY'
        response_headers['X-XSS-Protection'] = '1; mode=block'
        response_headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        response_headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    
    def get_security_stats(self) -> Dict[str, Any]:
        """Get security statistics"""
        return {
            **self.security_stats,
            'uptime_hours': (datetime.now() - self.security_stats['last_reset']).total_seconds() / 3600,
            'rate_limit_rules': {name: {
                'enabled': rule.enabled,
                'requests_per_minute': rule.requests_per_minute,
                'burst_limit': rule.burst_limit
            } for name, rule in self.rate_limit_rules.items()}
        }
    
    def update_rate_limit_rule(self, category: str, rule: RateLimitRule):
        """Update rate limiting rule"""
        self.rate_limit_rules[category] = rule
        logger.info(f"Updated rate limit rule for {category}: {rule.requests_per_minute} req/min")
    
    def block_ip(self, ip: str):
        """Add IP to block list"""
        self.blocked_ips.add(ip)
        logger.warning(f"Blocked IP: {ip}")
    
    def unblock_ip(self, ip: str):
        """Remove IP from block list"""
        self.blocked_ips.discard(ip)
        logger.info(f"Unblocked IP: {ip}")

# Global security middleware instance
_security_middleware: Optional[SecurityMiddleware] = None

def get_security_middleware(config: SecurityConfig, redis_client: Optional[redis.Redis] = None) -> SecurityMiddleware:
    """Get global security middleware instance"""
    global _security_middleware
    if _security_middleware is None:
        _security_middleware = SecurityMiddleware(config, redis_client)
    return _security_middleware