/**
 * Real-Time Risk Monitor Service
 * 
 * Provides continuous risk exposure calculation and monitoring with real-time alerts.
 * Integrates with existing real-time market data patterns and WebSocket updates.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';
import { EventEmitter } from 'events';

// Real-time Risk Types
export interface RiskAlert {
  id: string;
  type: 'warning' | 'critical' | 'info';
  category: 'exposure' | 'loss_limit' | 'volatility' | 'correlation' | 'margin' | 'drawdown';
  message: string;
  currentValue: Decimal.Instance;
  threshold: Decimal.Instance;
  severity: number; // 1-10 scale
  timestamp: Date;
  positions?: string[]; // Affected positions
  recommendedActions: string[];
  autoResolvable: boolean;
}

export interface RealTimeRiskData {
  userId: string;
  timestamp: Date;
  accountBalance: Decimal.Instance;
  totalExposure: Decimal.Instance;
  availableMargin: Decimal.Instance;
  marginUtilization: number; // 0-100%
  dailyPnL: Decimal.Instance;
  weeklyPnL: Decimal.Instance;
  portfolioVaR: Decimal.Instance; // Value at Risk
  maxDrawdown: Decimal.Instance;
  riskScore: number; // 0-100 scale
  activeAlerts: RiskAlert[];
  positionRisks: PositionRiskData[];
}

export interface PositionRiskData {
  symbol: string;
  size: Decimal.Instance;
  marketValue: Decimal.Instance;
  unrealizedPnL: Decimal.Instance;
  dailyVolatility: number;
  beta: number; // Market beta
  contribution: number; // Risk contribution to portfolio
  stopLossDistance: Decimal.Instance;
  takeProfitDistance?: Decimal.Instance;
  timeInPosition: number; // Hours
  riskRating: 'low' | 'medium' | 'high' | 'critical';
}

export interface RiskThresholds {
  maxDailyLossPercentage: number;
  maxWeeklyLossPercentage: number;
  maxPortfolioRisk: number;
  maxPositionConcentration: number;
  maxMarginUtilization: number;
  maxDrawdownPercentage: number;
  volatilityThreshold: number;
  correlationThreshold: number;
}

export interface MonitoringConfig {
  updateIntervalMs: number; // Risk calculation frequency
  alertThresholds: RiskThresholds;
  enableRealTimeAlerts: boolean;
  enableAutoLiquidation: boolean;
  maxAlertHistory: number;
  riskCalculationMethod: 'simple' | 'monte_carlo' | 'historical_simulation';
  confidenceLevel: number; // For VaR calculations (e.g., 0.95)
  lookbackDays: number; // For historical risk calculations
}

export interface MarketDataUpdate {
  symbol: string;
  price: Decimal.Instance;
  bid: Decimal.Instance;
  ask: Decimal.Instance;
  timestamp: Date;
  volatility?: number;
}

/**
 * RealTimeRiskMonitor - Continuous risk monitoring and alert system
 */
export class RealTimeRiskMonitor extends EventEmitter {
  private config: MonitoringConfig;
  private riskData: Map<string, RealTimeRiskData> = new Map();
  private alertHistory: Map<string, RiskAlert[]> = new Map();
  private monitoringActive: boolean = false;
  private updateInterval?: NodeJS.Timeout;
  private lastUpdateTime: Map<string, Date> = new Map();

  constructor(config: MonitoringConfig) {
    super();
    this.config = config;
    this.validateConfig();
  }

  /**
   * Start real-time risk monitoring for a user
   */
  public startMonitoring(userId: string, initialRiskData: RealTimeRiskData): void {
    this.riskData.set(userId, initialRiskData);
    this.alertHistory.set(userId, []);
    this.lastUpdateTime.set(userId, new Date());

    if (!this.monitoringActive) {
      this.startMonitoringLoop();
    }

    this.emit('monitoring_started', { userId, timestamp: new Date() });
  }

  /**
   * Stop risk monitoring for a user
   */
  public stopMonitoring(userId: string): void {
    this.riskData.delete(userId);
    this.alertHistory.delete(userId);
    this.lastUpdateTime.delete(userId);

    if (this.riskData.size === 0 && this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = undefined;
      this.monitoringActive = false;
    }

    this.emit('monitoring_stopped', { userId, timestamp: new Date() });
  }

  /**
   * Update real-time risk data
   */
  public updateRiskData(userId: string, updatedData: Partial<RealTimeRiskData>): void {
    const currentData = this.riskData.get(userId);
    if (!currentData) {
      throw new Error(`Risk monitoring not active for user ${userId}`);
    }

    const newRiskData: RealTimeRiskData = {
      ...currentData,
      ...updatedData,
      timestamp: new Date()
    };

    this.riskData.set(userId, newRiskData);
    this.lastUpdateTime.set(userId, new Date());

    // Check for new alerts
    this.evaluateRiskAlerts(userId, newRiskData);

    this.emit('risk_data_updated', { userId, riskData: newRiskData });
  }

  /**
   * Process market data update and recalculate risk
   */
  public processMarketDataUpdate(updates: MarketDataUpdate[]): void {
    for (const [userId, riskData] of this.riskData.entries()) {
      let needsUpdate = false;
      const updatedPositions = riskData.positionRisks.map(position => {
        const marketUpdate = updates.find(update => update.symbol === position.symbol);
        if (marketUpdate) {
          needsUpdate = true;
          return this.updatePositionRisk(position, marketUpdate);
        }
        return position;
      });

      if (needsUpdate) {
        const recalculatedRiskData = this.recalculatePortfolioRisk({
          ...riskData,
          positionRisks: updatedPositions,
          timestamp: new Date()
        });

        this.riskData.set(userId, recalculatedRiskData);
        this.evaluateRiskAlerts(userId, recalculatedRiskData);
        
        this.emit('market_risk_update', { 
          userId, 
          riskData: recalculatedRiskData,
          marketUpdates: updates.map(u => u.symbol)
        });
      }
    }
  }

  /**
   * Get current risk data for a user
   */
  public getRiskData(userId: string): RealTimeRiskData | undefined {
    return this.riskData.get(userId);
  }

  /**
   * Get risk alerts for a user
   */
  public getActiveAlerts(userId: string): RiskAlert[] {
    const riskData = this.riskData.get(userId);
    return riskData?.activeAlerts || [];
  }

  /**
   * Get alert history for a user
   */
  public getAlertHistory(userId: string, limit?: number): RiskAlert[] {
    const history = this.alertHistory.get(userId) || [];
    return limit ? history.slice(-limit) : history;
  }

  /**
   * Acknowledge an alert
   */
  public acknowledgeAlert(userId: string, alertId: string): boolean {
    const riskData = this.riskData.get(userId);
    if (!riskData) return false;

    const alertIndex = riskData.activeAlerts.findIndex(alert => alert.id === alertId);
    if (alertIndex === -1) return false;

    const acknowledgedAlert = riskData.activeAlerts[alertIndex];
    riskData.activeAlerts.splice(alertIndex, 1);

    // Add to history
    const history = this.alertHistory.get(userId) || [];
    history.push(acknowledgedAlert);
    
    // Maintain history limit
    if (history.length > this.config.maxAlertHistory) {
      history.splice(0, history.length - this.config.maxAlertHistory);
    }
    
    this.alertHistory.set(userId, history);
    this.emit('alert_acknowledged', { userId, alertId, timestamp: new Date() });

    return true;
  }

  /**
   * Calculate risk score (0-100 scale)
   */
  public calculateRiskScore(riskData: RealTimeRiskData): number {
    let riskScore = 0;

    // Account balance risk (30% weight)
    const balanceRisk = Math.min(100, Math.max(0, 
      (riskData.totalExposure.div(riskData.accountBalance).toNumber() * 100) / 50 * 100
    ));
    riskScore += balanceRisk * 0.3;

    // Daily P&L risk (25% weight)
    const dailyLossPercentage = riskData.dailyPnL.div(riskData.accountBalance).mul(-100).toNumber();
    const dailyRisk = Math.min(100, Math.max(0, 
      (dailyLossPercentage / this.config.alertThresholds.maxDailyLossPercentage) * 100
    ));
    riskScore += dailyRisk * 0.25;

    // Margin utilization risk (20% weight)
    const marginRisk = Math.min(100, Math.max(0, 
      (riskData.marginUtilization / this.config.alertThresholds.maxMarginUtilization) * 100
    ));
    riskScore += marginRisk * 0.2;

    // Portfolio concentration risk (15% weight)
    const concentrationRisk = this.calculateConcentrationRisk(riskData.positionRisks);
    riskScore += concentrationRisk * 0.15;

    // Volatility risk (10% weight)
    const avgVolatility = riskData.positionRisks.reduce((sum, pos) => 
      sum + pos.dailyVolatility, 0) / Math.max(1, riskData.positionRisks.length);
    const volatilityRisk = Math.min(100, Math.max(0, 
      (avgVolatility / this.config.alertThresholds.volatilityThreshold) * 100
    ));
    riskScore += volatilityRisk * 0.1;

    return Math.round(Math.min(100, Math.max(0, riskScore)));
  }

  /**
   * Private method: Start the monitoring loop
   */
  private startMonitoringLoop(): void {
    this.monitoringActive = true;
    this.updateInterval = setInterval(() => {
      this.performRiskUpdates();
    }, this.config.updateIntervalMs);
  }

  /**
   * Private method: Perform periodic risk updates
   */
  private performRiskUpdates(): void {
    const currentTime = new Date();
    
    for (const [userId, riskData] of this.riskData.entries()) {
      const lastUpdate = this.lastUpdateTime.get(userId) || currentTime;
      const timeSinceUpdate = currentTime.getTime() - lastUpdate.getTime();

      // Only update if significant time has passed or risk thresholds are being approached
      if (timeSinceUpdate > this.config.updateIntervalMs || this.hasHighRisk(riskData)) {
        const updatedRiskData = this.recalculatePortfolioRisk({
          ...riskData,
          timestamp: currentTime
        });

        this.riskData.set(userId, updatedRiskData);
        this.lastUpdateTime.set(userId, currentTime);
        this.evaluateRiskAlerts(userId, updatedRiskData);
      }
    }
  }

  /**
   * Private method: Check if risk data indicates high risk
   */
  private hasHighRisk(riskData: RealTimeRiskData): boolean {
    return riskData.riskScore > 70 || 
           riskData.activeAlerts.some(alert => alert.type === 'critical') ||
           riskData.marginUtilization > 80;
  }

  /**
   * Private method: Evaluate risk alerts for a user
   */
  private evaluateRiskAlerts(userId: string, riskData: RealTimeRiskData): void {
    const newAlerts: RiskAlert[] = [];
    const thresholds = this.config.alertThresholds;

    // Daily loss limit check
    const dailyLossPercentage = riskData.dailyPnL.div(riskData.accountBalance).mul(-100).toNumber();
    if (dailyLossPercentage > thresholds.maxDailyLossPercentage * 0.8) {
      newAlerts.push(this.createAlert({
        type: dailyLossPercentage > thresholds.maxDailyLossPercentage ? 'critical' : 'warning',
        category: 'loss_limit',
        message: `Daily loss approaching limit: ${dailyLossPercentage.toFixed(2)}%`,
        currentValue: new Decimal(dailyLossPercentage),
        threshold: new Decimal(thresholds.maxDailyLossPercentage),
        severity: Math.min(10, Math.round(dailyLossPercentage / thresholds.maxDailyLossPercentage * 10)),
        recommendedActions: ['Consider reducing position sizes', 'Review stop losses'],
        autoResolvable: false
      }));
    }

    // Margin utilization check
    if (riskData.marginUtilization > thresholds.maxMarginUtilization * 0.8) {
      newAlerts.push(this.createAlert({
        type: riskData.marginUtilization > thresholds.maxMarginUtilization ? 'critical' : 'warning',
        category: 'margin',
        message: `Margin utilization high: ${riskData.marginUtilization.toFixed(1)}%`,
        currentValue: new Decimal(riskData.marginUtilization),
        threshold: new Decimal(thresholds.maxMarginUtilization),
        severity: Math.min(10, Math.round(riskData.marginUtilization / thresholds.maxMarginUtilization * 10)),
        recommendedActions: ['Add margin', 'Close some positions', 'Reduce leverage'],
        autoResolvable: false
      }));
    }

    // Portfolio concentration check
    const maxPositionWeight = Math.max(...riskData.positionRisks.map(pos => 
      pos.marketValue.div(riskData.accountBalance).mul(100).toNumber()
    ));
    
    if (maxPositionWeight > thresholds.maxPositionConcentration) {
      newAlerts.push(this.createAlert({
        type: 'warning',
        category: 'exposure',
        message: `High position concentration: ${maxPositionWeight.toFixed(1)}%`,
        currentValue: new Decimal(maxPositionWeight),
        threshold: new Decimal(thresholds.maxPositionConcentration),
        severity: Math.min(10, Math.round(maxPositionWeight / thresholds.maxPositionConcentration * 7)),
        recommendedActions: ['Diversify portfolio', 'Reduce largest positions'],
        autoResolvable: false
      }));
    }

    // Update alerts in risk data
    riskData.activeAlerts = [...riskData.activeAlerts, ...newAlerts];
    
    // Remove duplicate alerts
    riskData.activeAlerts = riskData.activeAlerts.filter((alert, index, arr) => 
      arr.findIndex(a => a.category === alert.category && a.type === alert.type) === index
    );

    // Emit new alerts
    if (newAlerts.length > 0) {
      this.emit('new_alerts', { userId, alerts: newAlerts });
    }
  }

  /**
   * Private method: Create a risk alert
   */
  private createAlert(params: Omit<RiskAlert, 'id' | 'timestamp'>): RiskAlert {
    return {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      ...params
    };
  }

  /**
   * Private method: Update position risk from market data
   */
  private updatePositionRisk(position: PositionRiskData, marketUpdate: MarketDataUpdate): PositionRiskData {
    const newMarketValue = position.size.abs().mul(marketUpdate.price);
    const unrealizedPnL = position.size.gt(0) 
      ? newMarketValue.sub(position.size.mul(marketUpdate.price)) // Long position
      : position.size.mul(marketUpdate.price).sub(newMarketValue); // Short position

    return {
      ...position,
      marketValue: newMarketValue,
      unrealizedPnL,
      dailyVolatility: marketUpdate.volatility || position.dailyVolatility
    };
  }

  /**
   * Private method: Recalculate portfolio-level risk metrics
   */
  private recalculatePortfolioRisk(riskData: RealTimeRiskData): RealTimeRiskData {
    const totalExposure = riskData.positionRisks.reduce((sum, pos) => 
      sum.add(pos.marketValue), new Decimal(0));
    
    const totalUnrealizedPnL = riskData.positionRisks.reduce((sum, pos) => 
      sum.add(pos.unrealizedPnL), new Decimal(0));

    // Simple VaR calculation (should be enhanced with proper statistical methods)
    const portfolioVaR = totalExposure.mul(0.05); // 5% of total exposure

    const riskScore = this.calculateRiskScore({
      ...riskData,
      totalExposure,
      portfolioVaR
    });

    return {
      ...riskData,
      totalExposure,
      portfolioVaR,
      riskScore,
      dailyPnL: totalUnrealizedPnL, // Simplified - should track actual daily P&L
      timestamp: new Date()
    };
  }

  /**
   * Private method: Calculate concentration risk
   */
  private calculateConcentrationRisk(positions: PositionRiskData[]): number {
    if (positions.length === 0) return 0;

    const totalValue = positions.reduce((sum, pos) => sum + pos.marketValue.toNumber(), 0);
    const weights = positions.map(pos => pos.marketValue.toNumber() / totalValue);
    
    // Herfindahl-Hirschman Index
    const hhi = weights.reduce((sum, weight) => sum + (weight * weight), 0);
    
    // Convert to risk score (0-100)
    return Math.min(100, hhi * 100);
  }

  /**
   * Private method: Validate configuration
   */
  private validateConfig(): void {
    if (this.config.updateIntervalMs < 1000) {
      throw new Error('Update interval must be at least 1000ms');
    }
    
    if (this.config.alertThresholds.maxDailyLossPercentage <= 0) {
      throw new Error('Max daily loss percentage must be positive');
    }
    
    if (this.config.confidenceLevel <= 0 || this.config.confidenceLevel >= 1) {
      throw new Error('Confidence level must be between 0 and 1');
    }
  }

  /**
   * Update monitoring configuration
   */
  public updateConfig(newConfig: Partial<MonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.validateConfig();
    
    // Restart monitoring with new interval if changed
    if (newConfig.updateIntervalMs && this.updateInterval) {
      clearInterval(this.updateInterval);
      this.startMonitoringLoop();
    }
  }

  /**
   * Get monitoring statistics
   */
  public getMonitoringStats(): {
    activeUsers: number;
    totalAlerts: number;
    averageRiskScore: number;
    lastUpdateTime: Date;
  } {
    const activeUsers = this.riskData.size;
    const allRiskData = Array.from(this.riskData.values());
    const totalAlerts = allRiskData.reduce((sum, data) => sum + data.activeAlerts.length, 0);
    const averageRiskScore = allRiskData.length > 0 
      ? allRiskData.reduce((sum, data) => sum + data.riskScore, 0) / allRiskData.length
      : 0;
    const lastUpdateTime = new Date(Math.max(...Array.from(this.lastUpdateTime.values()).map(d => d.getTime())));

    return {
      activeUsers,
      totalAlerts,
      averageRiskScore,
      lastUpdateTime
    };
  }
}

/**
 * Factory function to create RealTimeRiskMonitor with default config
 */
export function createRealTimeRiskMonitor(customConfig?: Partial<MonitoringConfig>): RealTimeRiskMonitor {
  const defaultConfig: MonitoringConfig = {
    updateIntervalMs: 5000, // 5 seconds
    alertThresholds: {
      maxDailyLossPercentage: 5,
      maxWeeklyLossPercentage: 10,
      maxPortfolioRisk: 20,
      maxPositionConcentration: 25,
      maxMarginUtilization: 80,
      maxDrawdownPercentage: 15,
      volatilityThreshold: 30,
      correlationThreshold: 0.8
    },
    enableRealTimeAlerts: true,
    enableAutoLiquidation: false,
    maxAlertHistory: 100,
    riskCalculationMethod: 'simple',
    confidenceLevel: 0.95,
    lookbackDays: 30
  };

  const config = { ...defaultConfig, ...customConfig };
  return new RealTimeRiskMonitor(config);
}

/**
 * Default export
 */
export default RealTimeRiskMonitor;