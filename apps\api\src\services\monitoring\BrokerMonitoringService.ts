/**
 * Broker Real-time Monitoring Service
 * 
 * Provides real-time monitoring and alerting for broker connections and health
 * Part of Task 4: Real-time Monitoring and Alerting
 */

import { EventEmitter } from 'events';
import { Server as WebSocketServer } from 'ws';
import { PrismaClient } from '@prisma/client';
import { BrokerHealthMonitor } from '../trading/BrokerHealthMonitor.js';
import { ErrorClassificationService } from '../trading/ErrorClassificationService.js';
import type {
  MonitoringAlert,
  AlertSeverity,
  AlertType,
  BrokerHealthStatus,
  MonitoringMetrics,
  WebSocketMessage,
  AlertNotification,
  DashboardUpdate
} from '@golddaddy/types';

export class BrokerMonitoringService extends EventEmitter {
  private healthMonitor: BrokerHealthMonitor;
  private errorClassifier: ErrorClassificationService;
  private wsServer: WebSocketServer | null = null;
  private connectedClients = new Map<string, any>();
  private alertHistory: MonitoringAlert[] = [];
  private isShuttingDown = false;
  
  // Alert thresholds
  private readonly alertThresholds = {
    healthCheckFailureThreshold: 3,
    highLatencyThreshold: 5000, // 5 seconds
    criticalLatencyThreshold: 10000, // 10 seconds
    failoverFrequencyThreshold: 5, // per hour
    errorRateThreshold: 0.1 // 10% error rate
  };

  // Metrics tracking
  private metrics: MonitoringMetrics = {
    totalBrokers: 0,
    healthyBrokers: 0,
    unhealthyBrokers: 0,
    averageLatency: 0,
    errorRate: 0,
    failoverCount: 0,
    alertCount: 0,
    lastUpdateTime: new Date()
  };

  constructor(
    private prisma: PrismaClient,
    healthMonitor: BrokerHealthMonitor,
    errorClassifier: ErrorClassificationService
  ) {
    super();
    this.healthMonitor = healthMonitor;
    this.errorClassifier = errorClassifier;
    this.setupEventListeners();
  }

  /**
   * Initialize WebSocket server for real-time updates
   */
  initializeWebSocket(port: number = 8080): void {
    try {
      this.wsServer = new WebSocketServer({ port });
      
      this.wsServer.on('connection', (ws, request) => {
        const clientId = this.generateClientId();
        this.connectedClients.set(clientId, ws);
        
        console.log(`📡 WebSocket client connected: ${clientId}`);
        
        // Send initial dashboard state
        this.sendInitialDashboardState(ws);
        
        ws.on('message', (data) => {
          this.handleWebSocketMessage(clientId, data.toString());
        });
        
        ws.on('close', () => {
          this.connectedClients.delete(clientId);
          console.log(`📡 WebSocket client disconnected: ${clientId}`);
        });

        ws.on('error', (error) => {
          console.error(`📡 WebSocket error for client ${clientId}:`, error);
          this.connectedClients.delete(clientId);
        });
      });

      console.log(`📡 WebSocket monitoring server started on port ${port}`);
      
    } catch (error) {
      console.error('Failed to initialize WebSocket server:', error);
      this.emit('error', error);
    }
  }

  /**
   * Set up event listeners for monitoring events
   */
  private setupEventListeners(): void {
    // Listen to health check events
    this.healthMonitor.on('healthCheck', (result) => {
      this.handleHealthCheckResult(result);
    });

    this.healthMonitor.on('brokerUnhealthy', (data) => {
      this.createAlert({
        type: 'broker_unhealthy',
        severity: 'high',
        message: `Broker ${data.brokerName} is unhealthy: ${data.error}`,
        brokerId: data.brokerId,
        metadata: {
          brokerName: data.brokerName,
          error: data.error,
          timestamp: data.timestamp
        }
      });
    });

    this.healthMonitor.on('brokerHealthy', (data) => {
      this.createAlert({
        type: 'broker_healthy',
        severity: 'info',
        message: `Broker ${data.brokerName} is now healthy (${data.latency}ms)`,
        brokerId: data.brokerId,
        metadata: {
          brokerName: data.brokerName,
          latency: data.latency,
          timestamp: data.timestamp
        }
      });
    });

    // Listen to error classification events
    this.errorClassifier.on('errorClassified', (data) => {
      this.handleClassifiedError(data);
    });

    this.errorClassifier.on('circuitBreakerOpened', (data) => {
      this.createAlert({
        type: 'circuit_breaker_opened',
        severity: 'critical',
        message: `Circuit breaker opened for ${data.service}: ${data.reason}`,
        brokerId: data.brokerId,
        metadata: {
          service: data.service,
          reason: data.reason,
          failureCount: data.failureCount,
          timestamp: data.timestamp
        }
      });
    });
  }

  /**
   * Handle health check results and update metrics
   */
  private handleHealthCheckResult(result: any): void {
    this.updateMetricsFromHealthCheck(result);
    
    // Check for high latency alerts
    if (result.latency > this.alertThresholds.highLatencyThreshold) {
      const severity: AlertSeverity = result.latency > this.alertThresholds.criticalLatencyThreshold 
        ? 'critical' 
        : 'high';
        
      this.createAlert({
        type: 'high_latency',
        severity,
        message: `High latency detected: ${result.latency}ms`,
        brokerId: result.brokerId,
        metadata: {
          latency: result.latency,
          threshold: this.alertThresholds.highLatencyThreshold,
          timestamp: result.timestamp
        }
      });
    }

    // Broadcast health update to WebSocket clients
    this.broadcastHealthUpdate(result);
  }

  /**
   * Handle classified errors and generate appropriate alerts
   */
  private handleClassifiedError(errorData: any): void {
    const alertSeverity = this.mapErrorSeverityToAlert(errorData.category);
    
    this.createAlert({
      type: 'system_error',
      severity: alertSeverity,
      message: errorData.message,
      brokerId: errorData.brokerId,
      metadata: {
        category: errorData.category,
        originalError: errorData.originalError,
        suggestedAction: errorData.suggestedAction,
        timestamp: errorData.timestamp
      }
    });

    // Update error rate metrics
    this.updateErrorRateMetrics(errorData);
  }

  /**
   * Create and process a monitoring alert
   */
  private createAlert(alertData: Omit<MonitoringAlert, 'id' | 'timestamp' | 'acknowledged'>): void {
    const alert: MonitoringAlert = {
      id: this.generateAlertId(),
      ...alertData,
      timestamp: new Date(),
      acknowledged: false
    };

    // Add to alert history
    this.alertHistory.unshift(alert);
    
    // Keep only last 1000 alerts
    if (this.alertHistory.length > 1000) {
      this.alertHistory = this.alertHistory.slice(0, 1000);
    }

    // Update alert count metrics
    this.metrics.alertCount++;
    this.metrics.lastUpdateTime = new Date();

    // Store alert in database
    this.storeAlert(alert);

    // Broadcast alert to WebSocket clients
    this.broadcastAlert(alert);

    // Emit alert event
    this.emit('alert', alert);

    console.log(`🚨 ${alert.severity.toUpperCase()} Alert: ${alert.message}`);
  }

  /**
   * Store alert in database
   */
  private async storeAlert(alert: MonitoringAlert): Promise<void> {
    try {
      await this.prisma.systemError.create({
        data: {
          id: alert.id,
          brokerId: alert.brokerId || null,
          errorType: alert.type as any,
          category: this.mapAlertTypeToCategory(alert.type),
          message: alert.message,
          severity: alert.severity as any,
          metadata: alert.metadata ? JSON.stringify(alert.metadata) : null,
          resolved: false,
          createdAt: alert.timestamp
        }
      });
    } catch (error) {
      console.error('Failed to store alert in database:', error);
    }
  }

  /**
   * Send initial dashboard state to new WebSocket clients
   */
  private sendInitialDashboardState(ws: any): void {
    const dashboardUpdate: DashboardUpdate = {
      type: 'dashboard_init',
      timestamp: new Date(),
      data: {
        metrics: this.metrics,
        recentAlerts: this.alertHistory.slice(0, 10),
        brokerStatuses: [] // Will be populated by health monitor
      }
    };

    this.sendWebSocketMessage(ws, dashboardUpdate);
  }

  /**
   * Handle incoming WebSocket messages from clients
   */
  private handleWebSocketMessage(clientId: string, message: string): void {
    try {
      const parsedMessage: WebSocketMessage = JSON.parse(message);
      
      switch (parsedMessage.type) {
        case 'acknowledge_alert':
          this.acknowledgeAlert(parsedMessage.alertId);
          break;
          
        case 'request_metrics':
          this.sendMetricsUpdate(clientId);
          break;
          
        case 'request_alerts':
          this.sendAlertsUpdate(clientId, parsedMessage.limit || 50);
          break;
          
        case 'subscribe_broker':
          // TODO: Implement broker-specific subscriptions
          break;
          
        default:
          console.warn(`Unknown WebSocket message type: ${parsedMessage.type}`);
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  }

  /**
   * Acknowledge an alert
   */
  private acknowledgeAlert(alertId: string): void {
    const alert = this.alertHistory.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      
      // Update in database
      this.prisma.systemError.update({
        where: { id: alertId },
        data: { resolved: true }
      }).catch(error => console.error('Failed to update alert:', error));
      
      // Broadcast acknowledgment
      this.broadcastToAllClients({
        type: 'alert_acknowledged',
        alertId,
        timestamp: new Date()
      });
    }
  }

  /**
   * Send metrics update to specific client
   */
  private sendMetricsUpdate(clientId: string): void {
    const ws = this.connectedClients.get(clientId);
    if (ws) {
      this.sendWebSocketMessage(ws, {
        type: 'metrics_update',
        timestamp: new Date(),
        data: this.metrics
      });
    }
  }

  /**
   * Send alerts update to specific client
   */
  private sendAlertsUpdate(clientId: string, limit: number): void {
    const ws = this.connectedClients.get(clientId);
    if (ws) {
      this.sendWebSocketMessage(ws, {
        type: 'alerts_update',
        timestamp: new Date(),
        data: this.alertHistory.slice(0, limit)
      });
    }
  }

  /**
   * Broadcast health update to all WebSocket clients
   */
  private broadcastHealthUpdate(healthResult: any): void {
    this.broadcastToAllClients({
      type: 'health_update',
      timestamp: new Date(),
      data: healthResult
    });
  }

  /**
   * Broadcast alert to all WebSocket clients
   */
  private broadcastAlert(alert: MonitoringAlert): void {
    const notification: AlertNotification = {
      type: 'new_alert',
      timestamp: new Date(),
      alert
    };

    this.broadcastToAllClients(notification);
  }

  /**
   * Broadcast message to all connected WebSocket clients
   */
  private broadcastToAllClients(message: any): void {
    const messageStr = JSON.stringify(message);
    
    this.connectedClients.forEach((ws, clientId) => {
      try {
        if (ws.readyState === 1) { // WebSocket.OPEN
          ws.send(messageStr);
        } else {
          this.connectedClients.delete(clientId);
        }
      } catch (error) {
        console.error(`Failed to send message to client ${clientId}:`, error);
        this.connectedClients.delete(clientId);
      }
    });
  }

  /**
   * Send WebSocket message to specific client
   */
  private sendWebSocketMessage(ws: any, message: any): void {
    try {
      if (ws.readyState === 1) { // WebSocket.OPEN
        ws.send(JSON.stringify(message));
      }
    } catch (error) {
      console.error('Failed to send WebSocket message:', error);
    }
  }

  /**
   * Update metrics from health check results
   */
  private updateMetricsFromHealthCheck(result: any): void {
    // Update average latency using exponential moving average
    const alpha = 0.1; // Smoothing factor
    this.metrics.averageLatency = 
      this.metrics.averageLatency * (1 - alpha) + result.latency * alpha;

    this.metrics.lastUpdateTime = new Date();
  }

  /**
   * Update error rate metrics
   */
  private updateErrorRateMetrics(errorData: any): void {
    // This is a simplified error rate calculation
    // In production, you'd want more sophisticated metrics tracking
    this.metrics.errorRate = Math.min(this.metrics.errorRate + 0.01, 1.0);
    this.metrics.lastUpdateTime = new Date();
  }

  /**
   * Get current monitoring dashboard data
   */
  async getDashboardData(userId: string): Promise<{
    metrics: MonitoringMetrics;
    recentAlerts: MonitoringAlert[];
    brokerStatuses: BrokerHealthStatus[];
  }> {
    try {
      // Get broker health summary from health monitor
      const healthSummary = await this.healthMonitor.getHealthSummary(userId);
      
      // Update metrics with current data
      this.metrics.totalBrokers = healthSummary.totalBrokers;
      this.metrics.healthyBrokers = healthSummary.healthyBrokers;
      this.metrics.unhealthyBrokers = healthSummary.unhealthyBrokers;
      this.metrics.averageLatency = healthSummary.averageLatency;

      // Get broker configurations for status
      const brokerConfigs = await this.prisma.brokerConfiguration.findMany({
        where: {
          userId,
          deletedAt: null
        },
        orderBy: { priority: 'asc' }
      });

      const brokerStatuses: BrokerHealthStatus[] = brokerConfigs.map(config => ({
        brokerId: config.id,
        brokerName: config.brokerName,
        priority: config.priority,
        status: config.status as any,
        isHealthy: config.isHealthy,
        lastHealthCheck: config.lastHealthCheck,
        lastError: config.lastError,
        latency: 0, // Would be updated from recent health checks
        uptime: this.calculateUptime(config.createdAt),
        errorCount: config.failureCount
      }));

      return {
        metrics: { ...this.metrics },
        recentAlerts: this.alertHistory.slice(0, 20),
        brokerStatuses
      };
      
    } catch (error) {
      console.error('Failed to get dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get alert history with filtering
   */
  getAlertHistory(filters: {
    severity?: AlertSeverity;
    type?: AlertType;
    brokerId?: string;
    limit?: number;
    offset?: number;
  } = {}): MonitoringAlert[] {
    let filteredAlerts = [...this.alertHistory];

    if (filters.severity) {
      filteredAlerts = filteredAlerts.filter(a => a.severity === filters.severity);
    }

    if (filters.type) {
      filteredAlerts = filteredAlerts.filter(a => a.type === filters.type);
    }

    if (filters.brokerId) {
      filteredAlerts = filteredAlerts.filter(a => a.brokerId === filters.brokerId);
    }

    const start = filters.offset || 0;
    const end = start + (filters.limit || 50);
    
    return filteredAlerts.slice(start, end);
  }

  /**
   * Get current metrics
   */
  getCurrentMetrics(): MonitoringMetrics {
    return { ...this.metrics };
  }

  /**
   * Shutdown monitoring service
   */
  shutdown(): void {
    this.isShuttingDown = true;
    
    if (this.wsServer) {
      this.wsServer.close();
      console.log('📡 WebSocket monitoring server closed');
    }

    this.connectedClients.clear();
    console.log('🔍 Broker monitoring service shut down');
  }

  // Helper methods
  private generateClientId(): string {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private mapErrorSeverityToAlert(category: string): AlertSeverity {
    switch (category) {
      case 'CRITICAL':
        return 'critical';
      case 'NETWORK':
      case 'CONNECTION':
        return 'high';
      case 'AUTHENTICATION':
      case 'VALIDATION':
        return 'medium';
      default:
        return 'low';
    }
  }

  private mapAlertTypeToCategory(type: AlertType): string {
    switch (type) {
      case 'broker_unhealthy':
      case 'broker_healthy':
        return 'BROKER_HEALTH';
      case 'high_latency':
        return 'PERFORMANCE';
      case 'circuit_breaker_opened':
        return 'CIRCUIT_BREAKER';
      case 'system_error':
        return 'SYSTEM_ERROR';
      default:
        return 'GENERAL';
    }
  }

  private calculateUptime(createdAt: Date): number {
    return Date.now() - createdAt.getTime();
  }
}