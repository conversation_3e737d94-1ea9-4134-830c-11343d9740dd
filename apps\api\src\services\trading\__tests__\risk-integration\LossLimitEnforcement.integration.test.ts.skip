import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Decimal } from 'decimal.js';
import { LossLimitEnforcer } from '../../LossLimitEnforcer';
import { RiskManager } from '../../RiskManager';
import { Position, LossLimits, EmergencyLiquidationEvent } from '@golddaddy/types';

describe('Loss Limit Enforcement Integration', () => {
  let lossLimitEnforcer: LossLimitEnforcer;
  let riskManager: RiskManager;
  
  const mockUserId = '123e4567-e89b-12d3-a456-************';
  const mockPortfolioValue = new Decimal(100000);

  beforeEach(() => {
    lossLimitEnforcer = new LossLimitEnforcer();
    riskManager = new RiskManager();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Daily Loss Limit Enforcement', () => {
    it('should enforce daily loss limits across multiple trades', async () => {
      // Set daily loss limit of $5000
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(5000),
        maxDrawdownLimit: new Decimal(0.15), // 15%
        positionSizeLimit: new Decimal(0.2) // 20% per position
      });

      // Create positions that approach the daily loss limit
      const positions: Position[] = [
        {
          id: 'pos-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1970),
          unrealizedPnl: new Decimal(-3000),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        },
        {
          id: 'pos-2',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(75000),
          entryPrice: new Decimal(1.3000),
          currentPrice: new Decimal(1.2970),
          unrealizedPnl: new Decimal(-2250),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.2950),
          takeProfit: new Decimal(1.3150)
        }
      ];

      // Total unrealized loss: -$5250, exceeds daily limit
      const totalLoss = positions.reduce((sum, pos) => sum.add(pos.unrealizedPnl), new Decimal(0));
      expect(totalLoss.toNumber()).toBeLessThan(-5000);

      // Check if emergency liquidation should be triggered
      const shouldLiquidate = await lossLimitEnforcer.checkEmergencyLiquidation(
        mockUserId,
        positions,
        mockPortfolioValue
      );

      expect(shouldLiquidate).toBe(true);

      // Verify the specific violation reason
      const lossData = await lossLimitEnforcer.getUserLossData(mockUserId);
      expect(lossData.currentDailyLoss.abs().toNumber()).toBeGreaterThan(5000);
    });

    it('should prevent new positions when daily loss limit is approached', async () => {
      // Set conservative daily loss limit
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(2000),
        maxDrawdownLimit: new Decimal(0.10),
        positionSizeLimit: new Decimal(0.15)
      });

      // Existing positions with significant losses
      const existingPositions: Position[] = [
        {
          id: 'existing-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(80000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1975),
          unrealizedPnl: new Decimal(-2000), // At the daily limit
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        }
      ];

      // Try to calculate position size for new trade
      const canOpenNewPosition = await lossLimitEnforcer.canOpenNewPosition(
        mockUserId,
        'GBPUSD',
        new Decimal(50000), // Proposed position size
        existingPositions,
        mockPortfolioValue
      );

      expect(canOpenNewPosition).toBe(false);
    });
  });

  describe('Maximum Drawdown Enforcement', () => {
    it('should trigger emergency liquidation when drawdown exceeds maximum', async () => {
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(20000), // High daily limit
        maxDrawdownLimit: new Decimal(0.08), // 8% max drawdown
        positionSizeLimit: new Decimal(0.25)
      });

      // Portfolio value has declined significantly
      const currentPortfolioValue = new Decimal(90000); // Down from 100k = 10% drawdown
      
      const positions: Position[] = [
        {
          id: 'drawdown-pos',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(200000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1950),
          unrealizedPnl: new Decimal(-10000),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1900),
          takeProfit: new Decimal(1.2200)
        }
      ];

      // Calculate portfolio risk to get drawdown metrics
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        positions,
        currentPortfolioValue
      );

      // Should exceed 8% drawdown limit
      expect(portfolioRisk.maxDrawdown.toNumber()).toBeGreaterThan(0.08);

      // Emergency liquidation should be triggered
      const shouldLiquidate = await lossLimitEnforcer.checkEmergencyLiquidation(
        mockUserId,
        positions,
        currentPortfolioValue
      );

      expect(shouldLiquidate).toBe(true);
    });

    it('should calculate drawdown accurately across different time periods', async () => {
      // Historical portfolio values to simulate drawdown
      const historicalValues = [
        { date: new Date('2024-01-01'), value: new Decimal(100000) },
        { date: new Date('2024-01-02'), value: new Decimal(98000) },
        { date: new Date('2024-01-03'), value: new Decimal(95000) },
        { date: new Date('2024-01-04'), value: new Decimal(92000) }, // 8% drawdown
        { date: new Date('2024-01-05'), value: new Decimal(96000) }, // Partial recovery
      ];

      // Set 6% drawdown limit
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(15000),
        maxDrawdownLimit: new Decimal(0.06),
        positionSizeLimit: new Decimal(0.3)
      });

      // Simulate checking at the worst point (8% drawdown)
      const worstValue = new Decimal(92000);
      const positions: Position[] = [
        {
          id: 'dd-test',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1920),
          unrealizedPnl: new Decimal(-8000),
          direction: 'buy',
          openTime: new Date('2024-01-04'),
          stopLoss: new Decimal(1.1850),
          takeProfit: new Decimal(1.2150)
        }
      ];

      const portfolioRisk = await riskManager.calculatePortfolioRisk(positions, worstValue);
      
      // Drawdown calculation: (100000 - 92000) / 100000 = 8%
      expect(portfolioRisk.maxDrawdown.toNumber()).toBeCloseTo(0.08, 2);
      
      // Should trigger liquidation due to exceeding 6% limit
      const shouldLiquidate = await lossLimitEnforcer.checkEmergencyLiquidation(
        mockUserId,
        positions,
        worstValue
      );

      expect(shouldLiquidate).toBe(true);
    });
  });

  describe('Position Size Limit Enforcement', () => {
    it('should prevent positions that exceed size limits', async () => {
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(10000),
        maxDrawdownLimit: new Decimal(0.20),
        positionSizeLimit: new Decimal(0.15) // 15% max per position
      });

      // Try to open a position worth 25% of portfolio
      const largePositionSize = new Decimal(25000); // 25% of 100k portfolio
      const proposedPosition: Position = {
        id: 'large-pos',
        userId: mockUserId,
        symbol: 'EURUSD',
        size: new Decimal(250000), // Large lot size
        entryPrice: new Decimal(1.2000),
        currentPrice: new Decimal(1.2000),
        unrealizedPnl: new Decimal(0),
        direction: 'buy',
        openTime: new Date(),
        stopLoss: new Decimal(1.1900),
        takeProfit: new Decimal(1.2200)
      };

      const existingPositions: Position[] = [];
      
      const canOpen = await lossLimitEnforcer.canOpenNewPosition(
        mockUserId,
        'EURUSD',
        largePositionSize,
        existingPositions,
        mockPortfolioValue
      );

      expect(canOpen).toBe(false);

      // Should be able to open smaller position within limits
      const smallerPositionSize = new Decimal(12000); // 12% of portfolio
      const canOpenSmaller = await lossLimitEnforcer.canOpenNewPosition(
        mockUserId,
        'EURUSD',
        smallerPositionSize,
        existingPositions,
        mockPortfolioValue
      );

      expect(canOpenSmaller).toBe(true);
    });

    it('should consider aggregate exposure across correlated positions', async () => {
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(15000),
        maxDrawdownLimit: new Decimal(0.25),
        positionSizeLimit: new Decimal(0.20) // 20% max per "effective" position
      });

      // Existing EUR exposure
      const existingPositions: Position[] = [
        {
          id: 'eur1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(150000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2010),
          unrealizedPnl: new Decimal(150),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        }
      ];

      // Try to add more EUR exposure via EURGBP
      const correlatedPositionSize = new Decimal(10000);
      
      // This should be limited due to correlation with existing EUR position
      const canOpenCorrelated = await lossLimitEnforcer.canOpenNewPosition(
        mockUserId,
        'EURGBP', // Correlated with existing EURUSD position
        correlatedPositionSize,
        existingPositions,
        mockPortfolioValue
      );

      // The enforcer should consider correlation in its decision
      // (Implementation may vary based on correlation matrix availability)
      expect(typeof canOpenCorrelated).toBe('boolean');
    });
  });

  describe('Emergency Liquidation Events', () => {
    it('should create detailed liquidation events with proper audit trail', async () => {
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(3000),
        maxDrawdownLimit: new Decimal(0.07),
        positionSizeLimit: new Decimal(0.18)
      });

      // Positions that violate multiple limits
      const violatingPositions: Position[] = [
        {
          id: 'viol-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(200000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1950),
          unrealizedPnl: new Decimal(-10000), // Exceeds daily limit
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1900),
          takeProfit: new Decimal(1.2150)
        }
      ];

      // Trigger emergency liquidation
      const liquidationResult = await lossLimitEnforcer.executeEmergencyLiquidation(
        mockUserId,
        violatingPositions,
        mockPortfolioValue,
        ['DAILY_LOSS_LIMIT', 'MAX_DRAWDOWN'] // Multiple violation reasons
      );

      expect(liquidationResult).toBeDefined();
      expect(liquidationResult.userId).toBe(mockUserId);
      expect(liquidationResult.positionsLiquidated.length).toBe(1);
      expect(liquidationResult.violationReasons).toContain('DAILY_LOSS_LIMIT');
      expect(liquidationResult.totalLossAtLiquidation.toNumber()).toBeLessThan(0);
      expect(liquidationResult.timestamp).toBeInstanceOf(Date);
    });

    it('should handle partial liquidation when only some positions violate limits', async () => {
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(8000),
        maxDrawdownLimit: new Decimal(0.12),
        positionSizeLimit: new Decimal(0.22)
      });

      const mixedPositions: Position[] = [
        {
          id: 'safe-pos',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(50000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2020),
          unrealizedPnl: new Decimal(1000), // Profitable
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        },
        {
          id: 'risky-pos',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(300000), // Oversized
          entryPrice: new Decimal(1.3000),
          currentPrice: new Decimal(1.2900),
          unrealizedPnl: new Decimal(-30000), // Large loss
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.2850),
          takeProfit: new Decimal(1.3200)
        }
      ];

      // Should identify which positions need liquidation
      const positionsToLiquidate = await lossLimitEnforcer.identifyPositionsForLiquidation(
        mockUserId,
        mixedPositions,
        mockPortfolioValue
      );

      expect(positionsToLiquidate.length).toBe(1);
      expect(positionsToLiquidate[0].id).toBe('risky-pos');

      // Profitable position should be preserved
      const safePosition = mixedPositions.find(p => p.id === 'safe-pos');
      expect(positionsToLiquidate.find(p => p.id === 'safe-pos')).toBeUndefined();
    });
  });

  describe('Real-time Monitoring and Alerts', () => {
    it('should generate escalating alerts as limits are approached', async () => {
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(5000),
        maxDrawdownLimit: new Decimal(0.10),
        positionSizeLimit: new Decimal(0.25)
      });

      // Position approaching but not exceeding limits
      const approachingPositions: Position[] = [
        {
          id: 'approach-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(120000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1965),
          unrealizedPnl: new Decimal(-4200), // 84% of daily limit
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1930),
          takeProfit: new Decimal(1.2080)
        }
      ];

      const alerts = await lossLimitEnforcer.generateRiskAlerts(
        mockUserId,
        approachingPositions,
        mockPortfolioValue
      );

      // Should have warning alerts for approaching limits
      const warningAlerts = alerts.filter(alert => alert.severity === 'warning');
      expect(warningAlerts.length).toBeGreaterThan(0);

      // Should contain daily loss warning
      const dailyLossAlert = alerts.find(alert => 
        alert.type === 'DAILY_LOSS_APPROACHING' || 
        alert.message.includes('daily loss')
      );
      expect(dailyLossAlert).toBeDefined();
    });

    it('should track violation frequency and adjust thresholds', async () => {
      // Set initial limits
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(4000),
        maxDrawdownLimit: new Decimal(0.08),
        positionSizeLimit: new Decimal(0.20)
      });

      // Simulate multiple violations over time
      const violationHistory = [
        { date: new Date('2024-01-01'), type: 'DAILY_LOSS_LIMIT', amount: new Decimal(4500) },
        { date: new Date('2024-01-03'), type: 'DAILY_LOSS_LIMIT', amount: new Decimal(4200) },
        { date: new Date('2024-01-07'), type: 'MAX_DRAWDOWN', amount: new Decimal(0.09) },
      ];

      // Track violations
      for (const violation of violationHistory) {
        await lossLimitEnforcer.recordViolation(mockUserId, violation);
      }

      // Should suggest tighter limits due to frequent violations
      const suggestions = await lossLimitEnforcer.suggestLimitAdjustments(mockUserId);
      
      expect(suggestions).toBeDefined();
      expect(suggestions.recommendedDailyLimit.toNumber()).toBeLessThan(4000);
      expect(suggestions.reasoning).toContain('frequent violations');
    });
  });

  describe('Performance Under Stress', () => {
    it('should handle high-frequency limit checks efficiently', async () => {
      const positions: Position[] = Array.from({ length: 50 }, (_, i) => ({
        id: `stress-${i}`,
        userId: mockUserId,
        symbol: i % 2 === 0 ? 'EURUSD' : 'GBPUSD',
        size: new Decimal(10000 + i * 500),
        entryPrice: new Decimal(1.2000),
        currentPrice: new Decimal(1.2000 + (Math.random() - 0.5) * 0.02),
        unrealizedPnl: new Decimal((Math.random() - 0.6) * 1000), // Slight bias toward losses
        direction: 'buy',
        openTime: new Date(Date.now() - i * 30000),
        stopLoss: new Decimal(1.1950),
        takeProfit: new Decimal(1.2100)
      }));

      const startTime = Date.now();

      // Perform 100 rapid limit checks
      const checkPromises = Array.from({ length: 100 }, () => 
        lossLimitEnforcer.checkEmergencyLiquidation(
          mockUserId,
          positions,
          mockPortfolioValue
        )
      );

      const results = await Promise.all(checkPromises);
      const executionTime = Date.now() - startTime;

      // Should complete all checks in under 3 seconds
      expect(executionTime).toBeLessThan(3000);
      
      // All results should be consistent
      const firstResult = results[0];
      expect(results.every(result => result === firstResult)).toBe(true);
    });
  });
});