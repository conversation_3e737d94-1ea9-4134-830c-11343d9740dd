#!/usr/bin/env python3
"""
Health Monitoring Test Script
Tests health monitoring functionality including health checks, alerts, and reconnection logic
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List
from loguru import logger
import sys

# Configure logging
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{message}</cyan>",
    level="INFO"
)

class HealthMonitoringTester:
    """Test client for health monitoring functionality"""
    
    def __init__(self, api_url: str = "http://localhost:8001"):
        self.api_url = api_url
        self.test_results = {}
        
    async def test_system_health_check(self) -> bool:
        """Test comprehensive system health check"""
        logger.info("🔍 Testing system health check...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/health/system") as response:
                    if response.status == 200:
                        data = await response.json()
                        health = data.get('health', {})
                        
                        overall_status = health.get('overall_status')
                        checks = health.get('checks', {})
                        metrics = health.get('metrics', {})
                        
                        logger.info(f"✅ System health retrieved:")
                        logger.info(f"  🏥 Overall Status: {overall_status}")
                        logger.info(f"  📊 Health Checks: {len(checks)}")
                        logger.info(f"  📈 Active Alerts: {health.get('active_alerts', 0)}")
                        logger.info(f"  ⚠️ Degraded Services: {len(health.get('degraded_services', []))}")
                        
                        # Show individual check results
                        for check_name, check_result in checks.items():
                            status_emoji = "✅" if check_result['status'] == 'HEALTHY' else "⚠️" if check_result['status'] == 'DEGRADED' else "❌"
                            logger.info(f"    {status_emoji} {check_name}: {check_result['status']} - {check_result['message']}")
                        
                        self.test_results['system_health_check'] = True
                        return True
                    else:
                        logger.error(f"❌ Failed to get system health: {response.status}")
                        self.test_results['system_health_check'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ System health check error: {e}")
            self.test_results['system_health_check'] = False
            return False
    
    async def test_connection_metrics(self) -> bool:
        """Test connection and performance metrics"""
        logger.info("📊 Testing connection metrics...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/health/metrics") as response:
                    if response.status == 200:
                        data = await response.json()
                        metrics = data.get('metrics', {})
                        
                        logger.info("✅ Connection metrics retrieved:")
                        logger.info(f"  🔌 Connection Attempts: {metrics.get('connection_attempts', 0)}")
                        logger.info(f"  ✅ Successful Connections: {metrics.get('successful_connections', 0)}")
                        logger.info(f"  ❌ Failed Connections: {metrics.get('failed_connections', 0)}")
                        logger.info(f"  🎯 Success Rate: {metrics.get('success_rate', 0):.2%}")
                        logger.info(f"  🔄 Reconnections: {metrics.get('reconnections', 0)}")
                        logger.info(f"  ⏱️ Avg Response Time: {metrics.get('avg_response_time_ms', 0):.1f}ms")
                        logger.info(f"  ⏰ Uptime: {metrics.get('uptime_seconds', 0):.0f}s")
                        
                        # Validate metric ranges
                        success_rate = metrics.get('success_rate', 0)
                        response_time = metrics.get('avg_response_time_ms', 0)
                        
                        metrics_valid = True
                        if success_rate < 0 or success_rate > 1:
                            logger.warning(f"  ⚠️ Invalid success rate: {success_rate}")
                            metrics_valid = False
                        
                        if response_time < 0:
                            logger.warning(f"  ⚠️ Invalid response time: {response_time}")
                            metrics_valid = False
                        
                        self.test_results['connection_metrics'] = metrics_valid
                        return metrics_valid
                    else:
                        logger.error(f"❌ Failed to get connection metrics: {response.status}")
                        self.test_results['connection_metrics'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Connection metrics error: {e}")
            self.test_results['connection_metrics'] = False
            return False
    
    async def test_alert_system(self) -> bool:
        """Test alert system functionality"""
        logger.info("🚨 Testing alert system...")
        
        try:
            async with aiohttp.ClientSession() as session:
                # Get active alerts
                async with session.get(f"{self.api_url}/health/alerts?active_only=true") as response:
                    if response.status == 200:
                        data = await response.json()
                        active_alerts = data.get('alerts', [])
                        
                        logger.info(f"✅ Active alerts retrieved: {len(active_alerts)} alerts")
                        
                        for i, alert in enumerate(active_alerts[:3]):  # Show first 3 alerts
                            level_emoji = "🔴" if alert['level'] == 'CRITICAL' else "🟠" if alert['level'] == 'ERROR' else "🟡" if alert['level'] == 'WARNING' else "🔵"
                            logger.info(f"  {level_emoji} Alert {i+1}: [{alert['level']}] {alert['component']}: {alert['message']}")
                        
                        # Get all alerts (including resolved)
                        async with session.get(f"{self.api_url}/health/alerts?active_only=false") as all_response:
                            if all_response.status == 200:
                                all_data = await all_response.json()
                                all_alerts = all_data.get('alerts', [])
                                
                                resolved_alerts = [a for a in all_alerts if a.get('resolved', False)]
                                logger.info(f"  📊 Total alerts: {len(all_alerts)}, Resolved: {len(resolved_alerts)}")
                                
                                self.test_results['alert_system'] = True
                                return True
                            else:
                                logger.error(f"❌ Failed to get all alerts: {all_response.status}")
                                self.test_results['alert_system'] = False
                                return False
                    else:
                        logger.error(f"❌ Failed to get active alerts: {response.status}")
                        self.test_results['alert_system'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Alert system error: {e}")
            self.test_results['alert_system'] = False
            return False
    
    async def test_component_health_checks(self) -> bool:
        """Test individual component health checks"""
        logger.info("🔧 Testing component health checks...")
        
        components_to_test = [
            'mt5_connection',
            'database_connection',
            'price_streaming',
            'system_resources',
            'service_response_times'
        ]
        
        successful_checks = 0
        
        try:
            async with aiohttp.ClientSession() as session:
                for component in components_to_test:
                    async with session.get(f"{self.api_url}/health/status/{component}") as response:
                        if response.status == 200:
                            data = await response.json()
                            status_info = data.get('status', {})
                            
                            status = status_info.get('status', 'UNKNOWN')
                            message = status_info.get('message', 'No message')
                            execution_time = status_info.get('execution_time_ms', 0)
                            
                            status_emoji = "✅" if status == 'HEALTHY' else "⚠️" if status == 'DEGRADED' else "❌"
                            logger.info(f"  {status_emoji} {component}: {status} - {message} ({execution_time:.1f}ms)")
                            
                            successful_checks += 1
                        elif response.status == 404:
                            logger.warning(f"  ❓ Component not found: {component}")
                        else:
                            logger.error(f"  ❌ Failed to check {component}: {response.status}")
            
            success_rate = successful_checks / len(components_to_test)
            logger.info(f"✅ Component health checks: {successful_checks}/{len(components_to_test)} successful ({success_rate:.0%})")
            
            self.test_results['component_health_checks'] = success_rate >= 0.8  # At least 80% should succeed
            return self.test_results['component_health_checks']
            
        except Exception as e:
            logger.error(f"❌ Component health checks error: {e}")
            self.test_results['component_health_checks'] = False
            return False
    
    async def test_manual_reconnection(self) -> bool:
        """Test manual reconnection trigger"""
        logger.info("🔄 Testing manual reconnection...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.api_url}/health/reconnect") as response:
                    if response.status == 200:
                        data = await response.json()
                        success = data.get('success', False)
                        message = data.get('message', '')
                        
                        logger.info(f"✅ Reconnection triggered: {message}")
                        
                        # Wait a bit and check if connection is healthy
                        await asyncio.sleep(5)
                        
                        # Check MT5 connection status
                        async with session.get(f"{self.api_url}/health/status/mt5_connection") as status_response:
                            if status_response.status == 200:
                                status_data = await status_response.json()
                                connection_status = status_data.get('status', {}).get('status', 'UNKNOWN')
                                
                                logger.info(f"  📊 Post-reconnection MT5 status: {connection_status}")
                                
                                self.test_results['manual_reconnection'] = True
                                return True
                            else:
                                logger.warning("⚠️ Could not verify post-reconnection status")
                                self.test_results['manual_reconnection'] = True  # Still count as success
                                return True
                    else:
                        response_text = await response.text()
                        logger.error(f"❌ Failed to trigger reconnection: {response.status} - {response_text}")
                        self.test_results['manual_reconnection'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Manual reconnection error: {e}")
            self.test_results['manual_reconnection'] = False
            return False
    
    async def test_health_monitoring_consistency(self) -> bool:
        """Test consistency of health monitoring data"""
        logger.info("🔍 Testing health monitoring consistency...")
        
        try:
            async with aiohttp.ClientSession() as session:
                # Get multiple health snapshots
                snapshots = []
                
                for i in range(3):
                    async with session.get(f"{self.api_url}/health/system") as response:
                        if response.status == 200:
                            data = await response.json()
                            snapshots.append(data.get('health', {}))
                        else:
                            logger.error(f"❌ Failed to get health snapshot {i+1}: {response.status}")
                            self.test_results['health_monitoring_consistency'] = False
                            return False
                    
                    await asyncio.sleep(2)  # Wait between snapshots
                
                if len(snapshots) < 3:
                    logger.error("❌ Insufficient health snapshots for consistency test")
                    self.test_results['health_monitoring_consistency'] = False
                    return False
                
                # Check consistency across snapshots
                consistency_checks = 0
                total_checks = 0
                
                # Check that the same health checks are present in all snapshots
                first_checks = set(snapshots[0].get('checks', {}).keys())
                for i, snapshot in enumerate(snapshots[1:], 2):
                    current_checks = set(snapshot.get('checks', {}).keys())
                    total_checks += 1
                    
                    if first_checks == current_checks:
                        consistency_checks += 1
                        logger.info(f"  ✅ Snapshot {i}: Health checks consistent")
                    else:
                        logger.warning(f"  ⚠️ Snapshot {i}: Health check mismatch")
                        missing = first_checks - current_checks
                        extra = current_checks - first_checks
                        if missing:
                            logger.warning(f"    Missing: {missing}")
                        if extra:
                            logger.warning(f"    Extra: {extra}")
                
                # Check that metrics are reasonable across snapshots
                for i, snapshot in enumerate(snapshots):
                    metrics = snapshot.get('metrics', {})
                    uptime = metrics.get('uptime_minutes', 0)
                    success_rate = metrics.get('success_rate', 0)
                    
                    total_checks += 2
                    
                    if uptime >= 0:
                        consistency_checks += 1
                    else:
                        logger.warning(f"  ⚠️ Snapshot {i+1}: Invalid uptime: {uptime}")
                    
                    if 0 <= success_rate <= 1:
                        consistency_checks += 1
                    else:
                        logger.warning(f"  ⚠️ Snapshot {i+1}: Invalid success rate: {success_rate}")
                
                consistency_rate = consistency_checks / total_checks if total_checks > 0 else 0
                logger.info(f"✅ Health monitoring consistency: {consistency_checks}/{total_checks} checks passed ({consistency_rate:.0%})")
                
                self.test_results['health_monitoring_consistency'] = consistency_rate >= 0.8
                return self.test_results['health_monitoring_consistency']
                
        except Exception as e:
            logger.error(f"❌ Health monitoring consistency error: {e}")
            self.test_results['health_monitoring_consistency'] = False
            return False
    
    async def test_performance_monitoring(self) -> bool:
        """Test performance aspects of health monitoring"""
        logger.info("⚡ Testing health monitoring performance...")
        
        try:
            async with aiohttp.ClientSession() as session:
                # Measure response times for health endpoints
                endpoints = [
                    '/health/system',
                    '/health/metrics',
                    '/health/alerts',
                    '/health/status/mt5_connection'
                ]
                
                response_times = {}
                
                for endpoint in endpoints:
                    start_time = asyncio.get_event_loop().time()
                    
                    async with session.get(f"{self.api_url}{endpoint}") as response:
                        end_time = asyncio.get_event_loop().time()
                        response_time_ms = (end_time - start_time) * 1000
                        
                        response_times[endpoint] = response_time_ms
                        
                        if response.status == 200:
                            if response_time_ms < 1000:  # Under 1 second
                                logger.info(f"  ✅ {endpoint}: {response_time_ms:.0f}ms")
                            else:
                                logger.warning(f"  ⚠️ {endpoint}: {response_time_ms:.0f}ms (slow)")
                        else:
                            logger.error(f"  ❌ {endpoint}: Failed ({response.status})")
                            response_times[endpoint] = float('inf')
                
                # Check if all endpoints respond within reasonable time
                fast_endpoints = sum(1 for rt in response_times.values() if rt < 1000 and rt != float('inf'))
                total_endpoints = len(endpoints)
                
                avg_response_time = sum(rt for rt in response_times.values() if rt != float('inf')) / max(1, len([rt for rt in response_times.values() if rt != float('inf')]))
                
                logger.info(f"  📊 Average response time: {avg_response_time:.0f}ms")
                logger.info(f"  🎯 Fast endpoints: {fast_endpoints}/{total_endpoints}")
                
                performance_score = fast_endpoints / total_endpoints
                
                self.test_results['performance_monitoring'] = performance_score >= 0.8  # At least 80% should be fast
                return self.test_results['performance_monitoring']
                
        except Exception as e:
            logger.error(f"❌ Performance monitoring error: {e}")
            self.test_results['performance_monitoring'] = False
            return False
    
    async def test_error_handling(self) -> bool:
        """Test error handling in health monitoring"""
        logger.info("🚫 Testing error handling...")
        
        try:
            async with aiohttp.ClientSession() as session:
                # Test invalid component health check
                async with session.get(f"{self.api_url}/health/status/nonexistent_component") as response:
                    if response.status == 404:
                        logger.info("  ✅ Invalid component correctly returns 404")
                        error_handling_score = 1
                    else:
                        logger.warning(f"  ⚠️ Invalid component returned {response.status} instead of 404")
                        error_handling_score = 0
                
                # Test malformed requests (if any specific cases apply)
                # For now, we'll just check the 404 case
                
                self.test_results['error_handling'] = error_handling_score >= 0.5
                return self.test_results['error_handling']
                
        except Exception as e:
            logger.error(f"❌ Error handling test error: {e}")
            self.test_results['error_handling'] = False
            return False
    
    async def test_real_time_monitoring(self) -> bool:
        """Test real-time aspects of health monitoring"""
        logger.info("⏱️ Testing real-time monitoring...")
        
        try:
            async with aiohttp.ClientSession() as session:
                # Get baseline metrics
                async with session.get(f"{self.api_url}/health/metrics") as response:
                    if response.status != 200:
                        logger.error("❌ Could not get baseline metrics")
                        self.test_results['real_time_monitoring'] = False
                        return False
                    
                    baseline_data = await response.json()
                    baseline_metrics = baseline_data.get('metrics', {})
                    baseline_uptime = baseline_metrics.get('uptime_seconds', 0)
                
                # Wait for some time
                logger.info("  ⏳ Waiting 10 seconds to check real-time updates...")
                await asyncio.sleep(10)
                
                # Get updated metrics
                async with session.get(f"{self.api_url}/health/metrics") as response:
                    if response.status != 200:
                        logger.error("❌ Could not get updated metrics")
                        self.test_results['real_time_monitoring'] = False
                        return False
                    
                    updated_data = await response.json()
                    updated_metrics = updated_data.get('metrics', {})
                    updated_uptime = updated_metrics.get('uptime_seconds', 0)
                
                # Check if uptime increased (indicating real-time updates)
                uptime_increase = updated_uptime - baseline_uptime
                
                if uptime_increase >= 8 and uptime_increase <= 12:  # Should be around 10 seconds
                    logger.info(f"  ✅ Real-time monitoring working: uptime increased by {uptime_increase:.1f}s")
                    self.test_results['real_time_monitoring'] = True
                    return True
                else:
                    logger.warning(f"  ⚠️ Unexpected uptime change: {uptime_increase:.1f}s (expected ~10s)")
                    self.test_results['real_time_monitoring'] = False
                    return False
                
        except Exception as e:
            logger.error(f"❌ Real-time monitoring error: {e}")
            self.test_results['real_time_monitoring'] = False
            return False
    
    def print_summary(self):
        """Print test results summary"""
        logger.info("=" * 60)
        logger.info("📋 HEALTH MONITORING TEST SUMMARY")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{test_name:35} {status}")
        
        logger.info("-" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        logger.info("=" * 60)

async def main():
    """Main test function"""
    logger.info("🚀 Starting Health Monitoring Tests")
    logger.info("=" * 60)
    
    tester = HealthMonitoringTester()
    
    # Run tests in sequence
    tests = [
        ("System Health Check", tester.test_system_health_check()),
        ("Connection Metrics", tester.test_connection_metrics()),
        ("Alert System", tester.test_alert_system()),
        ("Component Health Checks", tester.test_component_health_checks()),
        ("Manual Reconnection", tester.test_manual_reconnection()),
        ("Health Monitoring Consistency", tester.test_health_monitoring_consistency()),
        ("Performance Monitoring", tester.test_performance_monitoring()),
        ("Error Handling", tester.test_error_handling()),
        ("Real-time Monitoring", tester.test_real_time_monitoring())
    ]
    
    for test_name, test_coro in tests:
        logger.info(f"🧪 Running: {test_name}")
        try:
            result = await test_coro
            logger.info(f"{'✅' if result else '❌'} {test_name}: {'PASS' if result else 'FAIL'}")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            tester.test_results[test_name.lower().replace(' ', '_')] = False
        
        # Brief pause between tests
        await asyncio.sleep(2)
    
    # Print summary
    tester.print_summary()
    
    # Return overall success
    all_passed = all(tester.test_results.values())
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)