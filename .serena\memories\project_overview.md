# GoldDaddy Project Overview

## Project Purpose
GoldDaddy is a comprehensive MT5 trading platform built as a modern monorepo with automated strategies, market analysis, and risk management tools. The platform follows a phased development approach from proof-of-concept to production-ready trading system.

## Repository Structure
This is a TypeScript monorepo organized as follows:

```
golddaddy/
├── apps/                    # Applications
│   ├── web/                # Next.js 14 PWA frontend (port 3000)
│   ├── api/                # Backend API services (port 3001) 
│   └── mt5-bridge/         # MT5 integration service (port 3002)
├── packages/               # Shared packages
│   ├── types/              # TypeScript type definitions
│   ├── ui/                 # Shared UI components
│   └── config/             # Configuration utilities
├── docker/                 # Docker configurations
└── docs/                   # Comprehensive architecture documentation
```

## Key Features
- Progressive Enhancement with feature flags
- Confidence assessment system for user safety
- Plain English metrics translation for non-technical users
- Story mode for guided user onboarding
- Real-time capabilities via WebSocket connections
- Live market data, risk monitoring and alerts
- Database actions use the supabase mcp tool

## Development Approach
- Monorepo workspace architecture with independent workspaces
- Cross-workspace type safety enforced via TypeScript project references
- Phased development from proof-of-concept to production