/**
 * Audit Trail Service Tests
 * 
 * Test suite for the AuditTrailService
 * Part of Task 5: Audit Trail and Compliance
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { AuditTrailService } from '../AuditTrailService.js';
import type { 
  AuditQueryFilter
} from '@golddaddy/types';

// Mock PrismaClient
const mockPrisma = {
  auditLog: {
    create: vi.fn(),
    findMany: vi.fn(),
    count: vi.fn(),
    findFirst: vi.fn(),
    deleteMany: vi.fn(),
  },
  complianceReport: {
    create: vi.fn(),
    findMany: vi.fn(),
    findUnique: vi.fn(),
  },
  complianceIssue: {
    create: vi.fn(),
    findMany: vi.fn(),
    update: vi.fn(),
  },
  brokerConfiguration: {
    findMany: vi.fn(),
  },
  failoverEvent: {
    findMany: vi.fn(),
  },
  systemError: {
    findMany: vi.fn(),
  },
  $disconnect: vi.fn(),
} as any;

describe('AuditTrailService', () => {
  let auditService: AuditTrailService;

  beforeEach(() => {
    auditService = new AuditTrailService(mockPrisma);
    vi.clearAllMocks();
    
    // Setup default mock returns
    mockPrisma.auditLog.create.mockResolvedValue({
      id: 'audit_default',
      createdAt: new Date(),
      timestamp: new Date(),
      userId: 'test_user',
      actionType: 'SYSTEM_EVENT',
      message: 'Default test message',
      success: true
    });
  });

  describe('Service Initialization', () => {
    it('should initialize successfully', async () => {
      await expect(auditService.initialize()).resolves.not.toThrow();
    });

    it('should start data retention scheduler on initialization', async () => {
      const startSchedulerSpy = vi.spyOn(auditService as any, 'startRetentionScheduler');
      await auditService.initialize();
      expect(startSchedulerSpy).toHaveBeenCalled();
    });
  });

  describe('User Action Logging', () => {
    it('should log user actions successfully', async () => {
      const mockAuditEntry = {
        id: 'audit_123',
        timestamp: new Date(),
        actionType: 'USER_LOGIN',
        message: 'User logged in successfully',
        userId: 'user_123'
      };

      mockPrisma.auditLog.create.mockResolvedValue(mockAuditEntry);

      const result = await auditService.logUserAction({
        userId: 'user_123',
        actionType: 'USER_LOGIN',
        message: 'User logged in successfully',
        success: true,
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0'
      });

      expect(result.actionType).toBe('USER_LOGIN');
      expect(result.userId).toBe('user_123');
      expect(result.success).toBe(true);
      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          actionType: 'USER_LOGIN',
          userId: 'user_123',
          message: 'User logged in successfully',
          success: true,
          ipAddress: '***********',
          userAgent: 'Mozilla/5.0'
        })
      });
    });

    it('should generate data integrity hash for audit entries', async () => {
      const mockAuditEntry = {
        id: 'audit_123',
        timestamp: new Date(),
        actionType: 'USER_LOGIN',
        dataHash: 'mock_hash'
      };

      mockPrisma.auditLog.create.mockResolvedValue(mockAuditEntry);

      await auditService.logUserAction({
        userId: 'user_123',
        actionType: 'USER_LOGIN',
        message: 'Test action',
        success: true
      });

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          dataHash: expect.any(String),
          previousDataHash: undefined
        })
      });
    });

    it('should set compliance relevance correctly', async () => {
      mockPrisma.auditLog.create.mockResolvedValue({ id: 'audit_123' });

      await auditService.logUserAction({
        userId: 'user_123',
        actionType: 'BROKER_CONFIGURATION_DELETED',
        message: 'Broker configuration deleted',
        success: true,
        complianceRelevant: true
      });

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          complianceRelevant: true,
          retentionCategory: 'LONG_TERM'
        })
      });
    });
  });

  describe('System Action Logging', () => {
    it('should log system actions without user context', async () => {
      mockPrisma.auditLog.create.mockResolvedValue({ id: 'audit_123' });

      await auditService.logSystemAction({
        actionType: 'SYSTEM_STARTUP',
        message: 'System started successfully',
        success: true,
        complianceRelevant: false
      });

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          actionType: 'SYSTEM_STARTUP',
          userId: "",
          message: 'System started successfully',
          success: true
        })
      });
    });

    it('should include broker context when provided', async () => {
      mockPrisma.auditLog.create.mockResolvedValue({ id: 'audit_123' });

      await auditService.logSystemAction({
        actionType: 'BROKER_HEALTH_CHECK',
        message: 'Broker health check completed',
        success: true,
        brokerId: 'broker_123',
        details: { latency: 150, healthy: true }
      });

      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          actionType: 'BROKER_HEALTH_CHECK',
          message: 'Broker health check completed',
          success: true,
          details: expect.objectContaining({
            latency: 150,
            healthy: true
          })
        })
      });
    });
  });

  describe('Failover Event Logging', () => {
    it('should log successful failover events', async () => {
      mockPrisma.auditLog.create.mockResolvedValue({ id: 'audit_123' });

      const result = await auditService.logFailoverEvent('FAILOVER_COMPLETED', {
        fromBroker: 'broker_1',
        toBroker: 'broker_2',
        trigger: 'CONNECTION_TIMEOUT',
        impactedTrades: ['trade_1', 'trade_2'],
        duration: 5000,
        success: true,
        timestamp: new Date()
      });

      expect(result.actionType).toBe('FAILOVER_COMPLETED');
      expect(result.complianceRelevant).toBe(true);
      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          actionType: 'FAILOVER_COMPLETED',
          severity: 'medium',
          complianceRelevant: true,
          details: expect.objectContaining({
            fromBroker: 'broker_1',
            toBroker: 'broker_2',
            trigger: 'CONNECTION_TIMEOUT',
            impactedTrades: ['trade_1', 'trade_2'],
            duration: 5000
          })
        })
      });
    });

    it('should log failed failover events with critical severity', async () => {
      mockPrisma.auditLog.create.mockResolvedValue({ id: 'audit_123' });

      const result = await auditService.logFailoverEvent('FAILOVER_FAILED', {
        fromBroker: 'broker_1',
        toBroker: 'broker_2',
        trigger: 'NETWORK_ERROR',
        error: 'Connection refused',
        success: false,
        timestamp: new Date()
      });

      expect(result.severity).toBe('critical');
      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          actionType: 'FAILOVER_FAILED',
          severity: 'critical',
          success: false,
          errorMessage: 'Connection refused'
        })
      });
    });
  });

  describe('Audit Log Querying', () => {
    it('should query audit logs with filters', async () => {
      const mockAuditLogs = [
        {
          id: 'audit_1',
          timestamp: new Date(),
          actionType: 'USER_LOGIN',
          message: 'User logged in',
          userId: 'user_123'
        },
        {
          id: 'audit_2',
          timestamp: new Date(),
          actionType: 'BROKER_CONFIGURATION_CREATED',
          message: 'Broker config created',
          userId: 'user_123'
        }
      ];

      mockPrisma.auditLog.findMany.mockResolvedValue(mockAuditLogs);

      const filters: AuditQueryFilter = {
        userId: 'user_123',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        actionTypes: ['USER_LOGIN', 'BROKER_CONFIGURATION_CREATED'],
        limit: 10
      };

      const result = await auditService.queryAuditLogs(filters);

      expect(result).toHaveLength(2);
      expect(mockPrisma.auditLog.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user_123',
          timestamp: {
            gte: filters.startDate,
            lte: filters.endDate
          },
          actionType: { in: filters.actionTypes }
        },
        take: 10,
        skip: 0,
        orderBy: { timestamp: 'desc' }
      });
    });

    it('should handle search term filtering', async () => {
      mockPrisma.auditLog.findMany.mockResolvedValue([]);

      await auditService.queryAuditLogs({
        searchTerm: 'failover',
        limit: 5
      });

      expect(mockPrisma.auditLog.findMany).toHaveBeenCalledWith({
        where: {
          message: { contains: 'failover', mode: 'insensitive' }
        },
        take: 5,
        skip: 0,
        orderBy: { timestamp: 'desc' }
      });
    });
  });

  describe('Data Integrity Verification', () => {
    it('should verify data integrity for audit logs', async () => {
      const mockAuditLogs = [
        {
          id: 'audit_1',
          timestamp: new Date(),
          actionType: 'USER_LOGIN',
          dataHash: 'valid_hash_1',
          previousDataHash: null,
          details: { test: 'data' }
        },
        {
          id: 'audit_2',
          timestamp: new Date(),
          actionType: 'USER_LOGOUT',
          dataHash: 'valid_hash_2',
          previousDataHash: 'valid_hash_1',
          details: { test: 'data2' }
        }
      ];

      mockPrisma.auditLog.findMany.mockResolvedValue(mockAuditLogs);

      const result = await auditService.verifyDataIntegrity({
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-12-31'),
        batchSize: 100
      });

      expect(result.overall.isValid).toBeDefined();
      expect(result.summary.totalRecords).toBe(2);
      expect(result.violations).toBeInstanceOf(Array);
      expect(mockPrisma.auditLog.findMany).toHaveBeenCalled();
    });

    it('should detect integrity violations', async () => {
      const mockAuditLogs = [
        {
          id: 'audit_1',
          timestamp: new Date(),
          actionType: 'USER_LOGIN',
          dataHash: 'hash_1',
          previousDataHash: null,
          details: { test: 'data' }
        },
        {
          id: 'audit_2',
          timestamp: new Date(),
          actionType: 'USER_LOGOUT',
          dataHash: 'hash_2',
          previousDataHash: 'wrong_hash', // This should cause a violation
          details: { test: 'data2' }
        }
      ];

      mockPrisma.auditLog.findMany.mockResolvedValue(mockAuditLogs);

      const result = await auditService.verifyDataIntegrity({
        batchSize: 100
      });

      expect(result.violations.length).toBeGreaterThan(0);
      expect(result.summary.integrityViolations).toBeGreaterThan(0);
      expect(result.overall.isValid).toBe(false);
    });
  });

  describe('Compliance Reporting', () => {
    it('should generate compliance report successfully', async () => {
      const reportPeriod = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      // Mock data for report generation
      mockPrisma.auditLog.count
        .mockResolvedValueOnce(100)  // Total events
        .mockResolvedValueOnce(5)    // Critical events
        .mockResolvedValueOnce(50);  // Compliance relevant events
      mockPrisma.auditLog.findMany.mockResolvedValue([]);
      mockPrisma.failoverEvent.findMany.mockResolvedValue([
        { success: true }, { success: false }
      ]);
      mockPrisma.systemError.findMany.mockResolvedValue([
        { severity: 'high' }
      ]);
      mockPrisma.brokerConfiguration.findMany.mockResolvedValue([]);

      const mockReport = {
        id: 'report_123',
        reportType: 'MONTHLY',
        startDate: reportPeriod.start,
        endDate: reportPeriod.end,
        generatedAt: new Date(),
        summary: {
          totalEvents: 100,
          criticalEvents: 5,
          failoverEvents: 2,
          errorEvents: 3,
          complianceScore: 85.5,
          dataIntegrityScore: 98.2
        }
      };

      mockPrisma.complianceReport.create.mockResolvedValue(mockReport);

      const result = await auditService.generateComplianceReport(reportPeriod);

      expect(result.id).toMatch(/^report_\d+_[a-z0-9]+$/);
      expect(result.summary.totalEvents).toBe(100);
      expect(result.summary.complianceScore).toBeGreaterThan(0);
      expect(mockPrisma.complianceReport.create).toHaveBeenCalled();
    });

    it('should calculate compliance scores correctly', async () => {
      const reportPeriod = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      // Mock high-compliance scenario
      mockPrisma.auditLog.count
        .mockResolvedValueOnce(1000) // Total events
        .mockResolvedValueOnce(10)   // Critical events
        .mockResolvedValueOnce(100); // Compliance relevant events
      mockPrisma.auditLog.findMany.mockResolvedValue([]);
      mockPrisma.failoverEvent.findMany.mockResolvedValue([
        { success: true }, { success: true }, { success: false }
      ]);
      mockPrisma.systemError.findMany.mockResolvedValue([
        { severity: 'low' }, { severity: 'medium' }
      ]);
      mockPrisma.brokerConfiguration.findMany.mockResolvedValue([]);
      mockPrisma.complianceReport.create.mockImplementation((data) => 
        Promise.resolve({ id: 'report_123', ...data.data })
      );

      const result = await auditService.generateComplianceReport(reportPeriod);

      expect(result.summary.complianceScore).toBeGreaterThan(90);
    });
  });

  describe('Audit Log Export', () => {
    it('should export audit logs in JSON format', async () => {
      const mockAuditLogs = [
        { id: 'audit_1', message: 'Test 1' },
        { id: 'audit_2', message: 'Test 2' }
      ];

      mockPrisma.auditLog.findMany.mockResolvedValue(mockAuditLogs);

      const result = await auditService.exportAuditLogs({
        format: 'json',
        filters: { limit: 10 },
        includeMetadata: true,
        anonymizeData: false,
        compressionEnabled: false,
        encryptExport: false
      });

      expect(result.format).toBe('json');
      expect(result.recordCount).toBe(2);
      expect(result.data).toContain('audit_1');
      expect(result.metadata.exported).toBeInstanceOf(Date);
    });

    it('should anonymize data when requested', async () => {
      const mockAuditLogs = [
        { id: 'audit_1', userId: 'user_123', message: 'Test message' }
      ];

      mockPrisma.auditLog.findMany.mockResolvedValue(mockAuditLogs);

      const result = await auditService.exportAuditLogs({
        format: 'json',
        filters: {},
        anonymizeData: true,
        includeMetadata: false,
        compressionEnabled: false,
        encryptExport: false
      });

      // Should not contain the original user ID
      expect(result.data).not.toContain('user_123');
      // Should contain anonymized version
      expect(result.data).toContain('***');
    });
  });

  describe('Audit Statistics', () => {
    it('should calculate audit statistics correctly', async () => {
      const period = {
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31')
      };

      mockPrisma.auditLog.count.mockResolvedValue(1000);
      mockPrisma.auditLog.findMany.mockResolvedValue([
        { actionType: 'USER_LOGIN', success: true },
        { actionType: 'USER_LOGIN', success: true },
        { actionType: 'USER_LOGOUT', success: true },
        { actionType: 'FAILOVER_COMPLETED', success: false }
      ]);

      const result = await auditService.getAuditStatistics(period);

      expect(result.totalEvents).toBe(1000);
      expect(result.successRate).toBeGreaterThan(0);
      expect(result.eventsByAction).toHaveProperty('USER_LOGIN');
      expect(result.eventsBySeverity).toBeDefined();
      expect(result.topErrorCategories).toBeInstanceOf(Array);
    });
  });

  describe('Retention Policy Management', () => {
    it('should apply retention policies correctly', async () => {
      mockPrisma.auditLog.deleteMany.mockResolvedValue({ count: 5 });

      const result = await auditService.applyRetentionPolicies();

      expect(result.processed).toBe(5);
      expect(result.deleted.shortTerm).toBe(5);
      expect(mockPrisma.auditLog.deleteMany).toHaveBeenCalled();
    });

    it('should preserve compliance-relevant data', async () => {
      mockPrisma.auditLog.deleteMany.mockResolvedValue({ count: 0 });

      const result = await auditService.applyRetentionPolicies();

      // Should not delete compliance-relevant data
      expect(result.deleted.longTerm).toBe(0);
    });
  });

  describe('Service Shutdown', () => {
    it('should shutdown gracefully', async () => {
      mockPrisma.auditLog.create.mockResolvedValue({ id: 'final_log' });

      await expect(auditService.shutdown()).resolves.not.toThrow();

      // Should log shutdown event
      expect(mockPrisma.auditLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          actionType: 'SYSTEM_SHUTDOWN',
          message: expect.stringContaining('shutdown')
        })
      });
    });

    it('should stop retention scheduler', async () => {
      const stopSchedulerSpy = vi.spyOn(auditService as any, 'stopRetentionScheduler');
      
      await auditService.shutdown();
      
      expect(stopSchedulerSpy).toHaveBeenCalled();
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      mockPrisma.auditLog.create.mockRejectedValue(new Error('Database error'));

      await expect(
        auditService.logUserAction({
          userId: 'user_123',
          actionType: 'USER_LOGIN',
          message: 'Test',
          success: true
        })
      ).rejects.toThrow('Database error');
    });

    it('should emit error events on failures', async () => {
      mockPrisma.auditLog.create.mockRejectedValue(new Error('Test error'));

      const errorPromise = new Promise((resolve) => {
        auditService.on('error', (error) => {
          resolve(error);
        });
      });

      try {
        await auditService.logUserAction({
          userId: 'user_123',
          actionType: 'USER_LOGIN',
          message: 'Test',
          success: true
        });
      } catch (error) {
        // Expected to throw
      }

      const errorEvent = await errorPromise;
      expect(errorEvent).toBeInstanceOf(Error);
    });
  });
});