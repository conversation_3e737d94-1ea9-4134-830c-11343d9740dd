{"name": "@golddaddy/mt5-bridge", "version": "1.0.0", "private": true, "description": "GoldDaddy MT5 Bridge Service", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "python:setup": "cd python && python setup.py", "python:test": "cd python && python test_mt5_basic.py", "python:start": "cd python && python main.py"}, "dependencies": {"ws": "^8.14.0", "dotenv": "^16.0.0", "@golddaddy/types": "*", "@golddaddy/config": "*", "axios": "^1.6.0", "fastify": "^4.24.0", "@fastify/websocket": "^8.3.0"}, "devDependencies": {"@types/ws": "^8.5.0", "@types/node": "^20.0.0", "typescript": "^5.3.0", "tsx": "^4.0.0", "eslint": "^8.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "vitest": "^1.0.0", "@vitest/coverage-v8": "^1.0.0"}}