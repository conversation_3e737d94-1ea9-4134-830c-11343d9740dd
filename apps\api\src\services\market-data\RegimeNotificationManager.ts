/**
 * Regime Notification Manager
 * 
 * Manages regime change notifications, rate limiting, and delivery
 * to various channels (WebSocket, email, etc.)
 */

import { EventEmitter } from 'events';
import {
  MarketRegime,
  RegimeDetectionResult,
  RegimeTransition,
  RegimeNotificationSettings,
  TimeFrame,
} from '@golddaddy/types';

// Notification delivery result
interface NotificationDeliveryResult {
  channel: 'websocket' | 'email' | 'webhook' | 'inapp';
  success: boolean;
  error?: string;
  deliveredAt: Date;
}

// Notification queue entry
interface NotificationQueueEntry {
  id: string;
  userId: string;
  notification: RegimeChangeNotification;
  priority: 'low' | 'medium' | 'high';
  channels: Array<'websocket' | 'email' | 'webhook' | 'inapp'>;
  createdAt: Date;
  attempts: number;
  maxAttempts: number;
  nextRetryAt?: Date;
}

// Regime change notification data
export interface RegimeChangeNotification {
  id: string;
  type: 'regime_change' | 'high_confidence_alert' | 'volatility_alert';
  instrument: string;
  timeframe: TimeFrame;
  
  // Regime change details
  fromRegime: MarketRegime;
  toRegime: MarketRegime;
  confidence: number;
  changeSignificance: number;
  
  // Context
  timestamp: Date;
  priceMovement?: number;
  recommendations?: string[];
  urgency: 'low' | 'medium' | 'high';
  
  // User-specific
  title: string;
  message: string;
  actionUrl?: string;
}

// User notification state for rate limiting
interface UserNotificationState {
  userId: string;
  notificationsThisHour: number;
  lastNotificationAt: Date;
  lastResetTime: Date;
  cooldownUntil?: Date;
}

/**
 * Regime Notification Manager
 * Handles detection of significant regime changes and manages notification delivery
 */
export class RegimeNotificationManager extends EventEmitter {
  private userSettings = new Map<string, RegimeNotificationSettings>();
  private userStates = new Map<string, UserNotificationState>();
  private notificationQueue: NotificationQueueEntry[] = [];
  private isProcessingQueue = false;
  
  // Default notification settings
  private defaultSettings: RegimeNotificationSettings = {
    userId: '',
    enableRegimeChangeNotifications: true,
    enableHighConfidenceAlerts: true,
    enableVolatilityAlerts: false,
    minimumConfidenceForNotification: 0.7,
    minimumChangeSignificanceForNotification: 0.6,
    maxNotificationsPerHour: 10,
    cooldownBetweenNotifications: 15, // 15 minutes
    watchedInstruments: ['EURUSD', 'GBPUSD', 'USDJPY'],
    watchedTimeframes: [TimeFrame.H1, TimeFrame.H4, TimeFrame.D1],
    deliveryMethods: {
      inApp: true,
      email: false,
      webSocket: true,
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Configuration
  private config = {
    queueProcessingInterval: 5000, // 5 seconds
    maxQueueSize: 500,
    retryDelays: [60000, 300000, 900000], // 1min, 5min, 15min
    maxRetryAttempts: 3,
    
    // Significance thresholds for different notification types
    significanceThresholds: {
      regime_change: 0.5,
      high_confidence_alert: 0.8,
      volatility_alert: 0.7,
    },
    
    // Quiet hours enforcement
    quietHoursEnabled: true,
  };

  // Statistics
  private stats = {
    totalNotifications: 0,
    successfulDeliveries: 0,
    failedDeliveries: 0,
    notificationsByType: {
      regime_change: 0,
      high_confidence_alert: 0,
      volatility_alert: 0,
    },
    averageDeliveryTime: 0,
    lastProcessedAt: new Date(),
  };

  constructor() {
    super();
    
    // Start queue processing
    this.startQueueProcessing();
    
    // Clean up expired states periodically
    setInterval(() => this.cleanupExpiredStates(), 300000); // Every 5 minutes
  }

  /**
   * Process regime detection result and generate notifications if needed
   */
  public async processRegimeChange(result: RegimeDetectionResult): Promise<void> {
    // Only process regime changes
    if (!result.regimeChangeDetected || !result.previousRegime) {
      return;
    }

    // Get users who should be notified about this instrument/timeframe
    const interestedUsers = this.getInterestedUsers(result.instrument, result.timeframe);
    
    for (const userId of interestedUsers) {
      const settings = this.getUserSettings(userId);
      
      // Check if user should receive this notification
      if (!this.shouldNotifyUser(userId, result, settings)) {
        continue;
      }

      // Create notification
      const notification = this.createRegimeChangeNotification(result, settings);
      
      // Add to queue
      await this.addNotificationToQueue(userId, notification, settings);
    }

    this.emit('regime_change_processed', {
      instrument: result.instrument,
      timeframe: result.timeframe,
      fromRegime: result.previousRegime,
      toRegime: result.regime,
      notifiedUsers: interestedUsers.length,
    });
  }

  /**
   * Update notification settings for a user
   */
  public updateUserSettings(settings: RegimeNotificationSettings): void {
    this.userSettings.set(settings.userId, {
      ...settings,
      updatedAt: new Date(),
    });
    
    this.emit('user_settings_updated', { userId: settings.userId });
  }

  /**
   * Get notification settings for a user
   */
  public getUserSettings(userId: string): RegimeNotificationSettings {
    const settings = this.userSettings.get(userId);
    if (settings) {
      return settings;
    }
    
    // Return default settings with user ID
    return {
      ...this.defaultSettings,
      userId,
    };
  }

  /**
   * Manually trigger notification for testing
   */
  public async triggerTestNotification(
    userId: string, 
    type: 'regime_change' | 'high_confidence_alert' | 'volatility_alert' = 'regime_change'
  ): Promise<void> {
    const settings = this.getUserSettings(userId);
    
    const notification: RegimeChangeNotification = {
      id: `test_${Date.now()}_${userId}`,
      type,
      instrument: 'EURUSD',
      timeframe: TimeFrame.H1,
      fromRegime: MarketRegime.SIDEWAYS,
      toRegime: MarketRegime.TRENDING_UP,
      confidence: 0.85,
      changeSignificance: 0.8,
      timestamp: new Date(),
      urgency: 'medium',
      title: 'Test Notification',
      message: 'This is a test regime change notification.',
      priceMovement: 0.012,
      recommendations: ['Consider trend-following strategies'],
    };

    await this.addNotificationToQueue(userId, notification, settings);
  }

  /**
   * Get notification statistics
   */
  public getStats() {
    return {
      ...this.stats,
      queueSize: this.notificationQueue.length,
      activeUsers: this.userSettings.size,
      userStates: this.userStates.size,
    };
  }

  /**
   * Get notification history for a user (last 50 notifications)
   */
  public getNotificationHistory(userId: string): NotificationQueueEntry[] {
    return this.notificationQueue
      .filter(entry => entry.userId === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
      .slice(0, 50);
  }

  // ===== Private Methods =====

  private getInterestedUsers(instrument: string, timeframe: TimeFrame): string[] {
    const users: string[] = [];
    
    for (const [userId, settings] of this.userSettings.entries()) {
      if (settings.watchedInstruments.includes(instrument) &&
          settings.watchedTimeframes.includes(timeframe)) {
        users.push(userId);
      }
    }
    
    return users;
  }

  private shouldNotifyUser(
    userId: string,
    result: RegimeDetectionResult,
    settings: RegimeNotificationSettings
  ): boolean {
    // Check if regime change notifications are enabled
    if (!settings.enableRegimeChangeNotifications) {
      return false;
    }

    // Check confidence threshold
    if (result.confidence < settings.minimumConfidenceForNotification) {
      return false;
    }

    // Check change significance
    if (!result.regimeChangeMagnitude || 
        result.regimeChangeMagnitude < settings.minimumChangeSignificanceForNotification) {
      return false;
    }

    // Check rate limits
    if (!this.checkRateLimit(userId, settings)) {
      return false;
    }

    // Check quiet hours
    if (!this.isWithinAllowedHours(settings)) {
      return false;
    }

    return true;
  }

  private checkRateLimit(userId: string, settings: RegimeNotificationSettings): boolean {
    let userState = this.userStates.get(userId);
    
    if (!userState) {
      userState = {
        userId,
        notificationsThisHour: 0,
        lastNotificationAt: new Date(0),
        lastResetTime: new Date(),
      };
      this.userStates.set(userId, userState);
    }

    const now = new Date();
    
    // Reset hourly counter if needed
    if (now.getTime() - userState.lastResetTime.getTime() >= 3600000) { // 1 hour
      userState.notificationsThisHour = 0;
      userState.lastResetTime = now;
    }

    // Check hourly limit
    if (userState.notificationsThisHour >= settings.maxNotificationsPerHour) {
      return false;
    }

    // Check cooldown
    const timeSinceLastNotification = now.getTime() - userState.lastNotificationAt.getTime();
    const cooldownMs = settings.cooldownBetweenNotifications * 60 * 1000;
    
    if (timeSinceLastNotification < cooldownMs) {
      return false;
    }

    // Check if user is in cooldown period
    if (userState.cooldownUntil && now < userState.cooldownUntil) {
      return false;
    }

    return true;
  }

  private isWithinAllowedHours(settings: RegimeNotificationSettings): boolean {
    if (!settings.quietHoursStart || !settings.quietHoursEnd) {
      return true; // No quiet hours set
    }

    const now = new Date();
    const currentTime = now.toTimeString().substr(0, 5); // HH:MM format
    
    // Simple time comparison (doesn't handle timezone complexities)
    if (settings.quietHoursStart <= settings.quietHoursEnd) {
      // Quiet hours don't cross midnight
      return currentTime < settings.quietHoursStart || currentTime > settings.quietHoursEnd;
    } else {
      // Quiet hours cross midnight
      return currentTime > settings.quietHoursEnd && currentTime < settings.quietHoursStart;
    }
  }

  private createRegimeChangeNotification(
    result: RegimeDetectionResult,
    settings: RegimeNotificationSettings
  ): RegimeChangeNotification {
    const urgency = this.determineUrgency(result);
    const title = this.generateNotificationTitle(result);
    const message = this.generateNotificationMessage(result);
    const recommendations = this.generateRecommendations(result);

    return {
      id: `regime_${result.id}_${Date.now()}`,
      type: 'regime_change',
      instrument: result.instrument,
      timeframe: result.timeframe,
      fromRegime: result.previousRegime!,
      toRegime: result.regime,
      confidence: result.confidence,
      changeSignificance: result.regimeChangeMagnitude || 0,
      timestamp: result.timestamp,
      urgency,
      title,
      message,
      recommendations,
      actionUrl: `/dashboard/instruments/${result.instrument}?timeframe=${result.timeframe}`,
    };
  }

  private determineUrgency(result: RegimeDetectionResult): 'low' | 'medium' | 'high' {
    if (result.confidence >= 0.9 && (result.regimeChangeMagnitude || 0) >= 0.8) {
      return 'high';
    }
    if (result.confidence >= 0.7 && (result.regimeChangeMagnitude || 0) >= 0.6) {
      return 'medium';
    }
    return 'low';
  }

  private generateNotificationTitle(result: RegimeDetectionResult): string {
    const regimeNames = {
      [MarketRegime.TRENDING_UP]: 'Uptrend',
      [MarketRegime.TRENDING_DOWN]: 'Downtrend',
      [MarketRegime.SIDEWAYS]: 'Sideways Movement',
      [MarketRegime.VOLATILE]: 'High Volatility',
      [MarketRegime.LOW_VOLATILITY]: 'Low Volatility',
      [MarketRegime.UNKNOWN]: 'Unknown',
    };

    const fromName = regimeNames[result.previousRegime!] || 'Unknown';
    const toName = regimeNames[result.regime] || 'Unknown';

    return `${result.instrument} ${result.timeframe}: ${fromName} → ${toName}`;
  }

  private generateNotificationMessage(result: RegimeDetectionResult): string {
    const confidencePercent = Math.round(result.confidence * 100);
    const significancePercent = Math.round((result.regimeChangeMagnitude || 0) * 100);

    return `Market regime has changed from ${result.previousRegime} to ${result.regime} ` +
           `with ${confidencePercent}% confidence and ${significancePercent}% significance. ` +
           `Consider adjusting your trading strategy accordingly.`;
  }

  private generateRecommendations(result: RegimeDetectionResult): string[] {
    const recommendations: string[] = [];

    switch (result.regime) {
      case MarketRegime.TRENDING_UP:
        recommendations.push('Consider trend-following strategies');
        recommendations.push('Look for momentum-based entries');
        if (result.confidence > 0.8) {
          recommendations.push('Strong uptrend - consider increasing position size');
        }
        break;

      case MarketRegime.TRENDING_DOWN:
        recommendations.push('Consider short-selling opportunities');
        recommendations.push('Implement strict risk management');
        recommendations.push('Look for counter-trend reversal signals');
        break;

      case MarketRegime.SIDEWAYS:
        recommendations.push('Switch to range-trading strategies');
        recommendations.push('Look for support/resistance levels');
        recommendations.push('Consider mean-reversion approaches');
        break;

      case MarketRegime.VOLATILE:
        recommendations.push('Reduce position sizes');
        recommendations.push('Widen stop-loss levels');
        recommendations.push('Consider volatility-based strategies');
        break;

      case MarketRegime.LOW_VOLATILITY:
        recommendations.push('Look for breakout opportunities');
        recommendations.push('Consider carry trade strategies');
        recommendations.push('Prepare for potential volatility expansion');
        break;
    }

    return recommendations;
  }

  private async addNotificationToQueue(
    userId: string,
    notification: RegimeChangeNotification,
    settings: RegimeNotificationSettings
  ): Promise<void> {
    // Determine delivery channels
    const channels: Array<'websocket' | 'email' | 'webhook' | 'inapp'> = [];
    if (settings.deliveryMethods.webSocket) channels.push('websocket');
    if (settings.deliveryMethods.inApp) channels.push('inapp');
    if (settings.deliveryMethods.email) channels.push('email');
    if (settings.deliveryMethods.webhook) channels.push('webhook');

    if (channels.length === 0) {
      this.emit('notification_skipped', {
        userId,
        notification: notification.id,
        reason: 'No delivery channels enabled',
      });
      return;
    }

    // Create queue entry
    const queueEntry: NotificationQueueEntry = {
      id: notification.id,
      userId,
      notification,
      priority: notification.urgency as 'low' | 'medium' | 'high',
      channels,
      createdAt: new Date(),
      attempts: 0,
      maxAttempts: this.config.maxRetryAttempts,
    };

    // Check queue size limit
    if (this.notificationQueue.length >= this.config.maxQueueSize) {
      // Remove oldest low-priority entry
      const lowPriorityIndex = this.notificationQueue.findIndex(
        entry => entry.priority === 'low'
      );
      if (lowPriorityIndex !== -1) {
        this.notificationQueue.splice(lowPriorityIndex, 1);
      } else {
        this.notificationQueue.shift(); // Remove oldest entry
      }
    }

    this.notificationQueue.push(queueEntry);

    // Update user state
    this.updateUserNotificationState(userId);

    this.emit('notification_queued', {
      userId,
      notificationId: notification.id,
      channels,
      priority: notification.urgency,
    });
  }

  private updateUserNotificationState(userId: string): void {
    let userState = this.userStates.get(userId);
    
    if (!userState) {
      userState = {
        userId,
        notificationsThisHour: 0,
        lastNotificationAt: new Date(0),
        lastResetTime: new Date(),
      };
    }

    userState.notificationsThisHour++;
    userState.lastNotificationAt = new Date();
    
    this.userStates.set(userId, userState);
  }

  private startQueueProcessing(): void {
    const processQueue = async () => {
      if (this.isProcessingQueue || this.notificationQueue.length === 0) {
        setTimeout(processQueue, this.config.queueProcessingInterval);
        return;
      }

      this.isProcessingQueue = true;

      try {
        // Sort by priority and creation time
        this.notificationQueue.sort((a, b) => {
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
          if (priorityDiff !== 0) return priorityDiff;
          
          // Check retry time
          if (a.nextRetryAt && b.nextRetryAt) {
            return a.nextRetryAt.getTime() - b.nextRetryAt.getTime();
          }
          
          return a.createdAt.getTime() - b.createdAt.getTime();
        });

        // Process entries that are ready
        const now = new Date();
        const readyEntries = this.notificationQueue.filter(
          entry => !entry.nextRetryAt || entry.nextRetryAt <= now
        );

        // Process up to 5 notifications at once
        const batch = readyEntries.slice(0, 5);
        
        for (const entry of batch) {
          await this.processNotificationEntry(entry);
        }

      } catch (error) {
        this.emit('queue_processing_error', error);
      } finally {
        this.isProcessingQueue = false;
        setTimeout(processQueue, this.config.queueProcessingInterval);
      }
    };

    processQueue();
  }

  private async processNotificationEntry(entry: NotificationQueueEntry): Promise<void> {
    const startTime = Date.now();
    entry.attempts++;

    try {
      const deliveryResults: NotificationDeliveryResult[] = [];

      // Attempt delivery to each channel
      for (const channel of entry.channels) {
        const result = await this.deliverNotification(entry.notification, channel, entry.userId);
        deliveryResults.push(result);
      }

      // Check if any delivery succeeded
      const hasSuccessfulDelivery = deliveryResults.some(result => result.success);

      if (hasSuccessfulDelivery) {
        // Remove from queue on successful delivery
        const index = this.notificationQueue.findIndex(e => e.id === entry.id);
        if (index !== -1) {
          this.notificationQueue.splice(index, 1);
        }

        this.stats.totalNotifications++;
        this.stats.successfulDeliveries++;
        this.stats.notificationsByType[entry.notification.type]++;

        // Update average delivery time
        const deliveryTime = Date.now() - startTime;
        this.stats.averageDeliveryTime = 
          ((this.stats.averageDeliveryTime * (this.stats.successfulDeliveries - 1)) + deliveryTime) / 
          this.stats.successfulDeliveries;

        this.emit('notification_delivered', {
          userId: entry.userId,
          notificationId: entry.notification.id,
          channels: entry.channels,
          deliveryResults,
          attempts: entry.attempts,
        });
      } else {
        // All deliveries failed
        if (entry.attempts >= entry.maxAttempts) {
          // Remove from queue after max attempts
          const index = this.notificationQueue.findIndex(e => e.id === entry.id);
          if (index !== -1) {
            this.notificationQueue.splice(index, 1);
          }

          this.stats.failedDeliveries++;

          this.emit('notification_failed', {
            userId: entry.userId,
            notificationId: entry.notification.id,
            channels: entry.channels,
            attempts: entry.attempts,
            reason: 'Max retry attempts exceeded',
          });
        } else {
          // Schedule retry
          const retryDelay = this.config.retryDelays[entry.attempts - 1] || 
                           this.config.retryDelays[this.config.retryDelays.length - 1];
          entry.nextRetryAt = new Date(Date.now() + retryDelay);

          this.emit('notification_retry_scheduled', {
            userId: entry.userId,
            notificationId: entry.notification.id,
            attempt: entry.attempts,
            nextRetryAt: entry.nextRetryAt,
          });
        }
      }

    } catch (error) {
      this.emit('notification_processing_error', {
        userId: entry.userId,
        notificationId: entry.notification.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        attempts: entry.attempts,
      });
    }

    this.stats.lastProcessedAt = new Date();
  }

  private async deliverNotification(
    notification: RegimeChangeNotification,
    channel: 'websocket' | 'email' | 'webhook' | 'inapp',
    userId: string
  ): Promise<NotificationDeliveryResult> {
    const deliveredAt = new Date();

    try {
      switch (channel) {
        case 'websocket':
          // Emit event that WebSocket service will pick up
          this.emit('regime_change_notification', {
            userId,
            notification,
          });
          break;

        case 'inapp':
          // Emit event for in-app notification system
          this.emit('inapp_notification', {
            userId,
            notification,
          });
          break;

        case 'email':
          // Would integrate with email service
          this.emit('email_notification', {
            userId,
            notification,
          });
          break;

        case 'webhook':
          // Would call webhook URL if configured
          this.emit('webhook_notification', {
            userId,
            notification,
          });
          break;
      }

      return {
        channel,
        success: true,
        deliveredAt,
      };

    } catch (error) {
      return {
        channel,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown delivery error',
        deliveredAt,
      };
    }
  }

  private cleanupExpiredStates(): void {
    const now = Date.now();
    const expiredUserStates: string[] = [];

    // Clean up user states that haven't been active for 24 hours
    for (const [userId, state] of this.userStates.entries()) {
      const lastActivity = Math.max(
        state.lastNotificationAt.getTime(),
        state.lastResetTime.getTime()
      );

      if (now - lastActivity > 86400000) { // 24 hours
        expiredUserStates.push(userId);
      }
    }

    for (const userId of expiredUserStates) {
      this.userStates.delete(userId);
    }

    // Clean up old completed notifications from queue (keep for audit)
    const cutoffTime = new Date(now - 86400000); // 24 hours ago
    this.notificationQueue = this.notificationQueue.filter(
      entry => entry.createdAt > cutoffTime || entry.attempts < entry.maxAttempts
    );

    if (expiredUserStates.length > 0) {
      this.emit('state_cleanup', {
        expiredUserStates: expiredUserStates.length,
        remainingStates: this.userStates.size,
        queueSize: this.notificationQueue.length,
      });
    }
  }
}