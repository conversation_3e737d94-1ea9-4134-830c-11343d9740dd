import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';

/**
 * Market Data Feed Health Monitoring Service
 * Monitors the health of market data feeds from MT5
 * Tracks data freshness, feed reliability, symbol coverage, and quote quality
 */
export class MarketDataHealthService extends EventEmitter {
  private prisma: PrismaClient;
  private dataFeeds: Map<string, MarketDataFeed> = new Map();
  private symbolMetrics: Map<string, SymbolHealthMetric> = new Map();
  private healthHistory: MarketDataHealthSnapshot[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;
  private lastQuoteReceived: Map<string, Date> = new Map();

  private readonly MONITORING_INTERVAL = 15 * 1000; // 15 seconds
  private readonly HEALTH_HISTORY_LIMIT = 2000;
  private readonly MAX_DATA_AGE = 60 * 1000; // 60 seconds
  
  // Health thresholds
  private readonly HEALTH_THRESHOLDS = {
    dataFreshness: {
      good: 5 * 1000, // <5 seconds
      warning: 15 * 1000, // 5-15 seconds
      critical: 60 * 1000, // >60 seconds
    },
    updateFrequency: {
      good: 5, // >5 updates per minute
      warning: 2, // 2-5 updates per minute
      critical: 1, // <1 update per minute
    },
    spreadWidth: {
      good: 0.0002, // <0.02%
      warning: 0.0005, // 0.02-0.05%
      critical: 0.001, // >0.1%
    },
    priceGaps: {
      good: 0.001, // <0.1%
      warning: 0.005, // 0.1-0.5%
      critical: 0.01, // >1%
    },
  };

  // Major currency pairs and their expected characteristics
  private readonly MONITORED_SYMBOLS = [
    { symbol: 'EURUSD', type: 'major', expectedSpread: 0.00001, tickSize: 0.00001 },
    { symbol: 'GBPUSD', type: 'major', expectedSpread: 0.00002, tickSize: 0.00001 },
    { symbol: 'USDJPY', type: 'major', expectedSpread: 0.001, tickSize: 0.001 },
    { symbol: 'USDCHF', type: 'major', expectedSpread: 0.00002, tickSize: 0.00001 },
    { symbol: 'AUDUSD', type: 'major', expectedSpread: 0.00002, tickSize: 0.00001 },
    { symbol: 'USDCAD', type: 'major', expectedSpread: 0.00002, tickSize: 0.00001 },
    { symbol: 'NZDUSD', type: 'minor', expectedSpread: 0.00003, tickSize: 0.00001 },
    { symbol: 'XAUUSD', type: 'metal', expectedSpread: 0.03, tickSize: 0.01 },
    { symbol: 'XAGUSD', type: 'metal', expectedSpread: 0.003, tickSize: 0.001 },
  ];

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  /**
   * Start market data health monitoring
   */
  async startMonitoring(config: MarketDataMonitoringConfig = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('Market data health monitoring already active');
      return;
    }

    console.log('📈 Starting market data health monitoring...');
    this.isMonitoring = true;

    const intervalMs = config.intervalMs || this.MONITORING_INTERVAL;

    // Initialize data feeds
    await this.initializeDataFeeds();

    // Start health monitoring
    this.monitoringInterval = setInterval(async () => {
      await this.performDataHealthCheck();
    }, intervalMs);

    // Subscribe to real-time market data events
    await this.subscribeToMarketDataEvents();

    console.log('✅ Market data health monitoring started');
    this.emit('monitoringStarted', { intervalMs, symbolCount: this.dataFeeds.size });
  }

  /**
   * Stop market data health monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ Stopping market data health monitoring...');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('✅ Market data health monitoring stopped');
    this.emit('monitoringStopped');
  }

  /**
   * Initialize market data feeds
   */
  private async initializeDataFeeds(): Promise<void> {
    for (const symbolConfig of this.MONITORED_SYMBOLS) {
      const feed: MarketDataFeed = {
        symbol: symbolConfig.symbol,
        type: symbolConfig.type,
        status: 'unknown',
        lastUpdate: null,
        updateCount: 0,
        errorCount: 0,
        currentQuote: null,
        priceHistory: [],
        healthMetrics: {
          dataFreshness: 0,
          updateFrequency: 0,
          spreadWidth: 0,
          priceGaps: 0,
          volatility: 0,
          reliability: 100,
        },
        expectedSpread: symbolConfig.expectedSpread,
        tickSize: symbolConfig.tickSize,
        consecutiveErrors: 0,
        lastError: null,
        subscription: {
          isSubscribed: false,
          subscribedAt: null,
          reconnectCount: 0,
        },
      };

      this.dataFeeds.set(symbolConfig.symbol, feed);
    }

    console.log(`📊 Initialized ${this.dataFeeds.size} market data feeds`);
  }

  /**
   * Subscribe to market data events
   */
  private async subscribeToMarketDataEvents(): Promise<void> {
    try {
      // This would connect to your WebSocket or event system for real-time data
      // For now, we'll simulate subscription
      for (const [symbol, feed] of this.dataFeeds) {
        feed.subscription.isSubscribed = true;
        feed.subscription.subscribedAt = new Date();
      }

      // Set up simulated data reception
      this.simulateMarketDataFlow();
      
    } catch (error) {
      console.error('Failed to subscribe to market data events:', error);
    }
  }

  /**
   * Simulate market data flow for demonstration
   */
  private simulateMarketDataFlow(): void {
    // In a real implementation, this would receive actual market data
    setInterval(() => {
      if (!this.isMonitoring) return;

      for (const [symbol, feed] of this.dataFeeds) {
        // Simulate periodic quote updates
        if (Math.random() > 0.3) { // 70% chance of update
          this.processMarketDataUpdate(symbol, this.generateSimulatedQuote(symbol));
        }
      }
    }, 2000 + Math.random() * 3000); // Random interval 2-5 seconds
  }

  /**
   * Generate simulated market quote
   */
  private generateSimulatedQuote(symbol: string): MarketQuote {
    const feed = this.dataFeeds.get(symbol);
    const basePrice = this.getBasePrice(symbol);
    
    // Generate realistic price with small movements
    const variation = (Math.random() - 0.5) * 0.01; // ±0.5% variation
    const bid = basePrice * (1 + variation);
    const spread = feed?.expectedSpread || 0.0001;
    const ask = bid + spread;

    return {
      symbol,
      bid,
      ask,
      timestamp: new Date(),
      volume: Math.floor(Math.random() * 1000000),
      spread: ask - bid,
      source: 'MT5',
    };
  }

  /**
   * Get base price for symbol
   */
  private getBasePrice(symbol: string): number {
    const basePrices: Record<string, number> = {
      'EURUSD': 1.0850,
      'GBPUSD': 1.2650,
      'USDJPY': 149.50,
      'USDCHF': 0.8750,
      'AUDUSD': 0.6450,
      'USDCAD': 1.3650,
      'NZDUSD': 0.5950,
      'XAUUSD': 2050.00,
      'XAGUSD': 24.50,
    };
    return basePrices[symbol] || 1.0000;
  }

  /**
   * Process market data update
   */
  async processMarketDataUpdate(symbol: string, quote: MarketQuote): Promise<void> {
    const feed = this.dataFeeds.get(symbol);
    if (!feed) return;

    // Update feed data
    feed.lastUpdate = quote.timestamp;
    feed.updateCount++;
    feed.currentQuote = quote;
    feed.status = 'active';
    feed.consecutiveErrors = 0;

    // Update quote history
    feed.priceHistory.push({
      timestamp: quote.timestamp,
      bid: quote.bid,
      ask: quote.ask,
      spread: quote.spread,
    });

    // Keep only recent price history (last 100 quotes)
    if (feed.priceHistory.length > 100) {
      feed.priceHistory = feed.priceHistory.slice(-100);
    }

    // Track last quote received time
    this.lastQuoteReceived.set(symbol, quote.timestamp);

    // Update health metrics
    this.updateFeedHealthMetrics(feed);

    // Emit quote received event
    this.emit('quoteReceived', { symbol, quote, feed });
  }

  /**
   * Update feed health metrics
   */
  private updateFeedHealthMetrics(feed: MarketDataFeed): void {
    if (!feed.currentQuote || feed.priceHistory.length < 2) {
      return;
    }

    const now = Date.now();
    const lastUpdate = feed.lastUpdate?.getTime() || now;

    // Data freshness (age of last update)
    feed.healthMetrics.dataFreshness = now - lastUpdate;

    // Update frequency (updates per minute)
    const oneMinuteAgo = now - 60 * 1000;
    const recentUpdates = feed.priceHistory.filter(
      price => price.timestamp.getTime() > oneMinuteAgo
    ).length;
    feed.healthMetrics.updateFrequency = recentUpdates;

    // Spread width (current spread vs expected)
    const currentSpread = feed.currentQuote.spread;
    feed.healthMetrics.spreadWidth = currentSpread;

    // Price gaps (detect unusual price jumps)
    if (feed.priceHistory.length >= 2) {
      const recent = feed.priceHistory.slice(-2);
      const priceChange = Math.abs(recent[1].bid - recent[0].bid) / recent[0].bid;
      feed.healthMetrics.priceGaps = priceChange;
    }

    // Volatility (standard deviation of recent prices)
    if (feed.priceHistory.length >= 10) {
      const recent = feed.priceHistory.slice(-10);
      const prices = recent.map(p => p.bid);
      const mean = prices.reduce((a, b) => a + b) / prices.length;
      const variance = prices.reduce((sum, price) => sum + Math.pow(price - mean, 2), 0) / prices.length;
      feed.healthMetrics.volatility = Math.sqrt(variance);
    }

    // Reliability (based on error rate)
    const totalRequests = feed.updateCount + feed.errorCount;
    feed.healthMetrics.reliability = totalRequests > 0 
      ? (feed.updateCount / totalRequests) * 100 
      : 100;
  }

  /**
   * Perform comprehensive data health check
   */
  private async performDataHealthCheck(): Promise<void> {
    const snapshot: MarketDataHealthSnapshot = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      overallStatus: 'unknown',
      feedStatuses: new Map(),
      summary: {
        totalFeeds: this.dataFeeds.size,
        activeFeeds: 0,
        staleFeeds: 0,
        errorFeeds: 0,
        averageDataFreshness: 0,
        averageUpdateFrequency: 0,
        averageSpreadWidth: 0,
        symbolCoverage: 0,
      },
      alerts: [],
    };

    let totalFreshness = 0;
    let totalFrequency = 0;
    let totalSpreadWidth = 0;

    // Check each feed
    for (const [symbol, feed] of this.dataFeeds) {
      const feedHealth = this.assessFeedHealth(feed);
      snapshot.feedStatuses.set(symbol, feedHealth);

      // Update totals for averages
      totalFreshness += feed.healthMetrics.dataFreshness;
      totalFrequency += feed.healthMetrics.updateFrequency;
      totalSpreadWidth += feed.healthMetrics.spreadWidth;

      // Count status categories
      switch (feedHealth.status) {
        case 'active':
          snapshot.summary.activeFeeds++;
          break;
        case 'stale':
          snapshot.summary.staleFeeds++;
          break;
        case 'error':
          snapshot.summary.errorFeeds++;
          break;
      }

      // Generate alerts
      const alerts = this.generateFeedAlerts(symbol, feed, feedHealth);
      snapshot.alerts.push(...alerts);
    }

    // Calculate averages
    const totalFeeds = snapshot.summary.totalFeeds;
    snapshot.summary.averageDataFreshness = totalFeeds > 0 ? totalFreshness / totalFeeds : 0;
    snapshot.summary.averageUpdateFrequency = totalFeeds > 0 ? totalFrequency / totalFeeds : 0;
    snapshot.summary.averageSpreadWidth = totalFeeds > 0 ? totalSpreadWidth / totalFeeds : 0;
    snapshot.summary.symbolCoverage = (snapshot.summary.activeFeeds / totalFeeds) * 100;

    // Determine overall status
    snapshot.overallStatus = this.determineOverallDataStatus(snapshot.summary);

    // Store snapshot
    this.healthHistory.push(snapshot);
    if (this.healthHistory.length > this.HEALTH_HISTORY_LIMIT) {
      this.healthHistory = this.healthHistory.slice(-this.HEALTH_HISTORY_LIMIT);
    }

    // Emit health snapshot
    this.emit('dataHealthSnapshot', snapshot);

    // Emit alerts
    snapshot.alerts.forEach(alert => this.emit('alert', alert));

    // Store in database
    await this.storeHealthSnapshot(snapshot);

    console.log(`📈 Data health check: ${snapshot.overallStatus} (${snapshot.summary.activeFeeds}/${totalFeeds} active)`);
  }

  /**
   * Assess individual feed health
   */
  private assessFeedHealth(feed: MarketDataFeed): FeedHealthStatus {
    const now = Date.now();
    const lastUpdateTime = feed.lastUpdate?.getTime() || 0;
    const dataAge = now - lastUpdateTime;

    // Determine status based on data age and error count
    let status: 'active' | 'stale' | 'error' = 'active';
    
    if (feed.consecutiveErrors > 5 || feed.healthMetrics.reliability < 50) {
      status = 'error';
    } else if (dataAge > this.HEALTH_THRESHOLDS.dataFreshness.critical) {
      status = 'stale';
    } else if (dataAge > this.HEALTH_THRESHOLDS.dataFreshness.warning) {
      status = 'stale';
    }

    return {
      symbol: feed.symbol,
      status,
      lastUpdate: feed.lastUpdate,
      dataAge,
      updateFrequency: feed.healthMetrics.updateFrequency,
      spreadWidth: feed.healthMetrics.spreadWidth,
      reliability: feed.healthMetrics.reliability,
      consecutiveErrors: feed.consecutiveErrors,
      healthScore: this.calculateFeedHealthScore(feed),
      issues: this.identifyFeedIssues(feed),
    };
  }

  /**
   * Calculate feed health score (0-100)
   */
  private calculateFeedHealthScore(feed: MarketDataFeed): number {
    let score = 100;

    // Data freshness penalty
    const dataAge = feed.healthMetrics.dataFreshness;
    if (dataAge > this.HEALTH_THRESHOLDS.dataFreshness.critical) {
      score -= 40;
    } else if (dataAge > this.HEALTH_THRESHOLDS.dataFreshness.warning) {
      score -= 20;
    } else if (dataAge > this.HEALTH_THRESHOLDS.dataFreshness.good) {
      score -= 10;
    }

    // Update frequency penalty
    const updateFreq = feed.healthMetrics.updateFrequency;
    if (updateFreq < this.HEALTH_THRESHOLDS.updateFrequency.critical) {
      score -= 30;
    } else if (updateFreq < this.HEALTH_THRESHOLDS.updateFrequency.warning) {
      score -= 15;
    } else if (updateFreq < this.HEALTH_THRESHOLDS.updateFrequency.good) {
      score -= 5;
    }

    // Reliability penalty
    score *= (feed.healthMetrics.reliability / 100);

    // Consecutive errors penalty
    score -= Math.min(feed.consecutiveErrors * 5, 25);

    return Math.max(0, Math.round(score));
  }

  /**
   * Identify feed issues
   */
  private identifyFeedIssues(feed: MarketDataFeed): string[] {
    const issues: string[] = [];

    // Data freshness issues
    if (feed.healthMetrics.dataFreshness > this.HEALTH_THRESHOLDS.dataFreshness.critical) {
      issues.push('Data extremely stale');
    } else if (feed.healthMetrics.dataFreshness > this.HEALTH_THRESHOLDS.dataFreshness.warning) {
      issues.push('Data moderately stale');
    }

    // Update frequency issues
    if (feed.healthMetrics.updateFrequency < this.HEALTH_THRESHOLDS.updateFrequency.critical) {
      issues.push('Very low update frequency');
    } else if (feed.healthMetrics.updateFrequency < this.HEALTH_THRESHOLDS.updateFrequency.warning) {
      issues.push('Low update frequency');
    }

    // Spread issues
    if (feed.healthMetrics.spreadWidth > feed.expectedSpread * 3) {
      issues.push('Unusually wide spread');
    }

    // Price gap issues
    if (feed.healthMetrics.priceGaps > this.HEALTH_THRESHOLDS.priceGaps.critical) {
      issues.push('Large price gaps detected');
    }

    // Reliability issues
    if (feed.healthMetrics.reliability < 80) {
      issues.push('Low reliability');
    }

    // Consecutive errors
    if (feed.consecutiveErrors > 3) {
      issues.push('Multiple consecutive errors');
    }

    return issues;
  }

  /**
   * Generate feed-specific alerts
   */
  private generateFeedAlerts(symbol: string, feed: MarketDataFeed, feedHealth: FeedHealthStatus): any[] {
    const alerts: any[] = [];

    // Stale data alert
    if (feedHealth.dataAge > this.HEALTH_THRESHOLDS.dataFreshness.critical) {
      alerts.push({
        id: crypto.randomUUID(),
        type: 'market_data_stale',
        severity: 'high',
        message: `Market data for ${symbol} is stale (${Math.round(feedHealth.dataAge / 1000)}s old)`,
        timestamp: new Date(),
        metadata: {
          symbol,
          dataAge: feedHealth.dataAge,
          threshold: this.HEALTH_THRESHOLDS.dataFreshness.critical,
        },
      });
    }

    // Low update frequency alert
    if (feedHealth.updateFrequency < this.HEALTH_THRESHOLDS.updateFrequency.critical) {
      alerts.push({
        id: crypto.randomUUID(),
        type: 'market_data_low_frequency',
        severity: 'medium',
        message: `Low update frequency for ${symbol}: ${feedHealth.updateFrequency} updates/min`,
        timestamp: new Date(),
        metadata: {
          symbol,
          updateFrequency: feedHealth.updateFrequency,
          threshold: this.HEALTH_THRESHOLDS.updateFrequency.critical,
        },
      });
    }

    // Wide spread alert
    if (feedHealth.spreadWidth > feed.expectedSpread * 3) {
      alerts.push({
        id: crypto.randomUUID(),
        type: 'market_data_wide_spread',
        severity: 'medium',
        message: `Unusually wide spread for ${symbol}: ${(feedHealth.spreadWidth * 10000).toFixed(1)} pips`,
        timestamp: new Date(),
        metadata: {
          symbol,
          currentSpread: feedHealth.spreadWidth,
          expectedSpread: feed.expectedSpread,
        },
      });
    }

    // Feed error alert
    if (feedHealth.consecutiveErrors > 5) {
      alerts.push({
        id: crypto.randomUUID(),
        type: 'market_data_feed_error',
        severity: 'critical',
        message: `Market data feed error for ${symbol}: ${feedHealth.consecutiveErrors} consecutive failures`,
        timestamp: new Date(),
        metadata: {
          symbol,
          consecutiveErrors: feedHealth.consecutiveErrors,
          lastError: feed.lastError,
        },
      });
    }

    return alerts;
  }

  /**
   * Determine overall data status
   */
  private determineOverallDataStatus(summary: MarketDataHealthSummary): 'healthy' | 'warning' | 'critical' {
    const coveragePercentage = summary.symbolCoverage;
    const errorPercentage = (summary.errorFeeds / summary.totalFeeds) * 100;

    if (coveragePercentage < 70 || errorPercentage > 30) {
      return 'critical';
    }

    if (coveragePercentage < 90 || errorPercentage > 10 || summary.staleFeeds > summary.totalFeeds * 0.3) {
      return 'warning';
    }

    return 'healthy';
  }

  /**
   * Store health snapshot in database
   */
  private async storeHealthSnapshot(snapshot: MarketDataHealthSnapshot): Promise<void> {
    try {
      await this.prisma.marketDataHealthSnapshot.create({
        data: {
          id: snapshot.id,
          timestamp: snapshot.timestamp,
          overallStatus: snapshot.overallStatus,
          totalFeeds: snapshot.summary.totalFeeds,
          activeFeeds: snapshot.summary.activeFeeds,
          staleFeeds: snapshot.summary.staleFeeds,
          errorFeeds: snapshot.summary.errorFeeds,
          averageDataFreshness: snapshot.summary.averageDataFreshness,
          averageUpdateFrequency: snapshot.summary.averageUpdateFrequency,
          averageSpreadWidth: snapshot.summary.averageSpreadWidth,
          symbolCoverage: snapshot.summary.symbolCoverage,
          feedStatuses: Object.fromEntries(snapshot.feedStatuses),
        },
      });
    } catch (error) {
      console.error('Failed to store market data health snapshot:', error);
    }
  }

  /**
   * Get current market data health status
   */
  getCurrentHealthStatus(): MarketDataHealthStatus {
    const latestSnapshot = this.healthHistory[this.healthHistory.length - 1];
    
    return {
      isMonitoring: this.isMonitoring,
      lastCheck: latestSnapshot?.timestamp || null,
      overallStatus: latestSnapshot?.overallStatus || 'unknown',
      summary: latestSnapshot?.summary || {
        totalFeeds: this.dataFeeds.size,
        activeFeeds: 0,
        staleFeeds: 0,
        errorFeeds: 0,
        averageDataFreshness: 0,
        averageUpdateFrequency: 0,
        averageSpreadWidth: 0,
        symbolCoverage: 0,
      },
      feeds: Array.from(this.dataFeeds.values()).map(feed => ({
        symbol: feed.symbol,
        type: feed.type,
        status: feed.status,
        lastUpdate: feed.lastUpdate,
        updateCount: feed.updateCount,
        errorCount: feed.errorCount,
        healthMetrics: feed.healthMetrics,
      })),
    };
  }

  /**
   * Get health history
   */
  getHealthHistory(timeRange: string = '1h'): MarketDataHealthSnapshot[] {
    const timeRangeMs = this.getTimeRangeMs(timeRange);
    const cutoffTime = new Date(Date.now() - timeRangeMs);

    return this.healthHistory.filter(snapshot => 
      snapshot.timestamp >= cutoffTime
    );
  }

  /**
   * Convert time range to milliseconds
   */
  private getTimeRangeMs(timeRange: string): number {
    switch (timeRange) {
      case '5m': return 5 * 60 * 1000;
      case '15m': return 15 * 60 * 1000;
      case '1h': return 60 * 60 * 1000;
      case '4h': return 4 * 60 * 60 * 1000;
      case '24h': return 24 * 60 * 60 * 1000;
      case '7d': return 7 * 24 * 60 * 60 * 1000;
      default: return 60 * 60 * 1000;
    }
  }
}

// Type definitions
interface MarketDataMonitoringConfig {
  intervalMs?: number;
  monitoredSymbols?: string[];
  maxDataAge?: number;
}

interface MarketDataFeed {
  symbol: string;
  type: 'major' | 'minor' | 'exotic' | 'metal' | 'crypto';
  status: 'active' | 'stale' | 'error' | 'unknown';
  lastUpdate: Date | null;
  updateCount: number;
  errorCount: number;
  currentQuote: MarketQuote | null;
  priceHistory: PricePoint[];
  healthMetrics: {
    dataFreshness: number;
    updateFrequency: number;
    spreadWidth: number;
    priceGaps: number;
    volatility: number;
    reliability: number;
  };
  expectedSpread: number;
  tickSize: number;
  consecutiveErrors: number;
  lastError: string | null;
  subscription: {
    isSubscribed: boolean;
    subscribedAt: Date | null;
    reconnectCount: number;
  };
}

interface MarketQuote {
  symbol: string;
  bid: number;
  ask: number;
  timestamp: Date;
  volume: number;
  spread: number;
  source: string;
}

interface PricePoint {
  timestamp: Date;
  bid: number;
  ask: number;
  spread: number;
}

interface SymbolHealthMetric {
  symbol: string;
  healthScore: number;
  issues: string[];
  lastUpdate: Date;
}

interface MarketDataHealthSnapshot {
  id: string;
  timestamp: Date;
  overallStatus: 'healthy' | 'warning' | 'critical' | 'unknown';
  feedStatuses: Map<string, FeedHealthStatus>;
  summary: MarketDataHealthSummary;
  alerts: any[];
}

interface FeedHealthStatus {
  symbol: string;
  status: 'active' | 'stale' | 'error';
  lastUpdate: Date | null;
  dataAge: number;
  updateFrequency: number;
  spreadWidth: number;
  reliability: number;
  consecutiveErrors: number;
  healthScore: number;
  issues: string[];
}

interface MarketDataHealthSummary {
  totalFeeds: number;
  activeFeeds: number;
  staleFeeds: number;
  errorFeeds: number;
  averageDataFreshness: number;
  averageUpdateFrequency: number;
  averageSpreadWidth: number;
  symbolCoverage: number;
}

interface MarketDataHealthStatus {
  isMonitoring: boolean;
  lastCheck: Date | null;
  overallStatus: 'healthy' | 'warning' | 'critical' | 'unknown';
  summary: MarketDataHealthSummary;
  feeds: Array<{
    symbol: string;
    type: string;
    status: string;
    lastUpdate: Date | null;
    updateCount: number;
    errorCount: number;
    healthMetrics: any;
  }>;
}

export type {
  MarketDataMonitoringConfig,
  MarketDataFeed,
  MarketQuote,
  MarketDataHealthSnapshot,
  MarketDataHealthSummary,
  MarketDataHealthStatus,
};