/**
 * Real-Time Data Processing Pipeline
 * 
 * Handles data compression, normalization, and transformation for real-time
 * market data from multiple sources with TimescaleDB optimization.
 */

import { EventEmitter } from 'events';
import Decimal from 'decimal.js';

// Data source types
export enum DataSource {
  MT5 = 'mt5',
  ALPHA_VANTAGE = 'alpha_vantage',
  YAHOO_FINANCE = 'yahoo_finance',
}

// Market data timeframes
export enum TimeFrame {
  M1 = '1m',
  M5 = '5m',
  M15 = '15m',
  M30 = '30m',
  H1 = '1h',
  H4 = '4h',
  D1 = '1d',
  W1 = '1w',
  MN1 = '1M',
}

// Raw market data interface (before normalization)
export interface RawMarketData {
  source: DataSource;
  symbol: string;
  timestamp: Date;
  data: any; // Source-specific format
  metadata?: Record<string, any>;
}

// Normalized market data interface (after processing)
export interface NormalizedMarketData {
  id: string;
  source: DataSource;
  instrument: string;
  timeframe: TimeFrame;
  timestamp: Date;
  open: Decimal.Instance;
  high: Decimal.Instance;
  low: Decimal.Instance;
  close: Decimal.Instance;
  volume: Decimal.Instance;
  spread?: Decimal.Instance;
  bid?: Decimal.Instance;
  ask?: Decimal.Instance;
  precision: number;
  timezone: string;
  isCompressed: boolean;
  compressionRatio?: number;
  qualityScore: number; // 0-100
  originalPayloadSize?: number;
  processedPayloadSize?: number;
  processingTimeMs: number;
}

// Technical indicators interface
export interface TechnicalIndicators {
  instrument: string;
  timeframe: TimeFrame;
  timestamp: Date;
  sma20?: Decimal.Instance;
  sma50?: Decimal.Instance;
  rsi14?: Decimal.Instance;
  macdLine?: Decimal.Instance;
  macdSignal?: Decimal.Instance;
  macdHistogram?: Decimal.Instance;
  bollingerUpper?: Decimal.Instance;
  bollingerLower?: Decimal.Instance;
  bollingerMiddle?: Decimal.Instance;
}

// Data processing configuration
export interface ProcessingConfig {
  enableCompression: boolean;
  compressionAlgorithm: 'gzip' | 'lzma' | 'brotli';
  maxCompressionRatio: number;
  enableTechnicalIndicators: boolean;
  indicatorConfigs: Record<string, any>;
  timestampPrecision: 'second' | 'millisecond' | 'microsecond';
  decimalPrecision: number;
  maxProcessingTimeMs: number;
  enableQualityValidation: boolean;
  minQualityScore: number;
}

// Data processing statistics
export interface ProcessingStats {
  totalProcessed: number;
  totalCompressed: number;
  averageCompressionRatio: number;
  averageProcessingTime: number;
  averageQualityScore: number;
  errorCount: number;
  lastProcessedAt: Date;
  throughputPerSecond: number;
  memoryUsageMb: number;
}

/**
 * Real-Time Data Processor for market data normalization and compression
 */
export class RealTimeDataProcessor extends EventEmitter {
  private config: ProcessingConfig;
  private stats: ProcessingStats;
  private compressionBuffer: Map<string, Buffer> = new Map();
  private processingQueue: RawMarketData[] = [];
  private isProcessing: boolean = false;

  constructor(config: ProcessingConfig) {
    super();
    this.config = config;
    this.stats = this.initializeStats();
  }

  /**
   * Initialize processing statistics
   */
  private initializeStats(): ProcessingStats {
    return {
      totalProcessed: 0,
      totalCompressed: 0,
      averageCompressionRatio: 0,
      averageProcessingTime: 0,
      averageQualityScore: 0,
      errorCount: 0,
      lastProcessedAt: new Date(),
      throughputPerSecond: 0,
      memoryUsageMb: 0,
    };
  }

  /**
   * Process raw market data from various sources
   */
  public async processMarketData(rawData: RawMarketData): Promise<NormalizedMarketData> {
    const startTime = Date.now();

    try {
      // Add to processing queue
      this.processingQueue.push(rawData);
      
      // Process data based on source
      const normalizedData = await this.normalizeBySource(rawData);
      
      // Apply compression if enabled
      if (this.config.enableCompression) {
        await this.compressData(normalizedData);
      }

      // Calculate technical indicators if enabled
      if (this.config.enableTechnicalIndicators) {
        await this.calculateTechnicalIndicators(normalizedData);
      }

      // Validate data quality
      if (this.config.enableQualityValidation) {
        this.validateDataQuality(normalizedData);
      }

      // Update processing statistics
      this.updateStats(normalizedData, startTime);

      this.emit('data_processed', normalizedData);
      return normalizedData;

    } catch (error) {
      this.handleProcessingError(rawData, error as Error);
      throw error;
    }
  }

  /**
   * Normalize data based on source format
   */
  private async normalizeBySource(rawData: RawMarketData): Promise<NormalizedMarketData> {
    switch (rawData.source) {
      case DataSource.MT5:
        return this.normalizeMT5Data(rawData);
      case DataSource.ALPHA_VANTAGE:
        return this.normalizeAlphaVantageData(rawData);
      case DataSource.YAHOO_FINANCE:
        return this.normalizeYahooFinanceData(rawData);
      default:
        throw new Error(`Unsupported data source: ${rawData.source}`);
    }
  }

  /**
   * Normalize MT5 market data format
   */
  private normalizeMT5Data(rawData: RawMarketData): NormalizedMarketData {
    const mt5Data = rawData.data;
    
    return {
      id: this.generateDataId(rawData),
      source: DataSource.MT5,
      instrument: this.normalizeInstrument(mt5Data.symbol),
      timeframe: this.normalizeTimeframe(mt5Data.period || '1m'),
      timestamp: this.normalizeTimestamp(mt5Data.time || rawData.timestamp),
      open: mt5Data.open !== undefined ? new Decimal(mt5Data.open).toDecimalPlaces(this.config.decimalPrecision) : new Decimal(0),
      high: mt5Data.high !== undefined ? new Decimal(mt5Data.high).toDecimalPlaces(this.config.decimalPrecision) : new Decimal(0),
      low: mt5Data.low !== undefined ? new Decimal(mt5Data.low).toDecimalPlaces(this.config.decimalPrecision) : new Decimal(0),
      close: mt5Data.close !== undefined ? new Decimal(mt5Data.close).toDecimalPlaces(this.config.decimalPrecision) : new Decimal(0),
      volume: new Decimal(mt5Data.volume || 0).toDecimalPlaces(this.config.decimalPrecision),
      bid: mt5Data.bid ? new Decimal(mt5Data.bid).toDecimalPlaces(this.config.decimalPrecision) : undefined,
      ask: mt5Data.ask ? new Decimal(mt5Data.ask).toDecimalPlaces(this.config.decimalPrecision) : undefined,
      spread: this.calculateSpread(mt5Data.bid, mt5Data.ask),
      precision: this.config.decimalPrecision,
      timezone: 'UTC',
      isCompressed: false,
      qualityScore: 100, // MT5 is primary source
      originalPayloadSize: JSON.stringify(mt5Data).length,
      processedPayloadSize: 0, // Will be calculated after compression
      processingTimeMs: 0, // Will be set by caller
    };
  }

  /**
   * Normalize Alpha Vantage data format
   */
  private normalizeAlphaVantageData(rawData: RawMarketData): NormalizedMarketData {
    const avData = rawData.data;
    
    return {
      id: this.generateDataId(rawData),
      source: DataSource.ALPHA_VANTAGE,
      instrument: this.normalizeInstrument(rawData.symbol),
      timeframe: this.normalizeTimeframe(avData.interval || '1min'),
      timestamp: this.normalizeTimestamp(avData.timestamp || rawData.timestamp),
      open: new Decimal(avData['1. open']).toDecimalPlaces(this.config.decimalPrecision),
      high: new Decimal(avData['2. high']).toDecimalPlaces(this.config.decimalPrecision),
      low: new Decimal(avData['3. low']).toDecimalPlaces(this.config.decimalPrecision),
      close: new Decimal(avData['4. close']).toDecimalPlaces(this.config.decimalPrecision),
      volume: new Decimal(avData['5. volume'] || 0).toDecimalPlaces(this.config.decimalPrecision),
      precision: this.config.decimalPrecision,
      timezone: 'UTC',
      isCompressed: false,
      qualityScore: 85, // Secondary source
      originalPayloadSize: JSON.stringify(avData).length,
      processedPayloadSize: 0,
      processingTimeMs: 0,
    };
  }

  /**
   * Normalize Yahoo Finance data format
   */
  private normalizeYahooFinanceData(rawData: RawMarketData): NormalizedMarketData {
    const yhData = rawData.data;
    
    return {
      id: this.generateDataId(rawData),
      source: DataSource.YAHOO_FINANCE,
      instrument: this.normalizeInstrument(rawData.symbol),
      timeframe: this.normalizeTimeframe(yhData.granularity || '1m'),
      timestamp: this.normalizeTimestamp(yhData.timestamp || rawData.timestamp),
      open: new Decimal(yhData.open).toDecimalPlaces(this.config.decimalPrecision),
      high: new Decimal(yhData.high).toDecimalPlaces(this.config.decimalPrecision),
      low: new Decimal(yhData.low).toDecimalPlaces(this.config.decimalPrecision),
      close: new Decimal(yhData.close).toDecimalPlaces(this.config.decimalPrecision),
      volume: new Decimal(yhData.volume || 0).toDecimalPlaces(this.config.decimalPrecision),
      precision: this.config.decimalPrecision,
      timezone: 'UTC',
      isCompressed: false,
      qualityScore: 80, // Secondary source
      originalPayloadSize: JSON.stringify(yhData).length,
      processedPayloadSize: 0,
      processingTimeMs: 0,
    };
  }

  /**
   * Compress market data for efficient storage
   */
  private async compressData(data: NormalizedMarketData): Promise<void> {
    try {
      const { gzip } = await import('zlib');
      const { promisify } = await import('util');
      const gzipAsync = promisify(gzip);

      const originalData = JSON.stringify({
        ohlcv: [data.open, data.high, data.low, data.close, data.volume],
        meta: { 
          instrument: data.instrument, 
          timeframe: data.timeframe,
          timestamp: data.timestamp.getTime(),
        }
      });

      const compressed = await gzipAsync(Buffer.from(originalData));
      const compressionRatio = compressed.length / originalData.length;

      if (compressionRatio <= this.config.maxCompressionRatio) {
        data.isCompressed = true;
        data.compressionRatio = compressionRatio;
        data.processedPayloadSize = compressed.length;
        
        // Store compressed data for potential retrieval
        this.compressionBuffer.set(data.id, compressed);
      } else {
        data.processedPayloadSize = originalData.length;
      }
    } catch (error) {
      this.emit('compression_error', { data, error });
      data.processedPayloadSize = data.originalPayloadSize || 0;
    }
  }

  /**
   * Calculate technical indicators for market data
   */
  private async calculateTechnicalIndicators(data: NormalizedMarketData): Promise<TechnicalIndicators | null> {
    try {
      // This would integrate with existing technical analysis libraries
      // For now, return placeholder structure
      const indicators: TechnicalIndicators = {
        instrument: data.instrument,
        timeframe: data.timeframe,
        timestamp: data.timestamp,
        // Technical indicators would be calculated here based on historical data
      };

      this.emit('indicators_calculated', { data, indicators });
      return indicators;
    } catch (error) {
      this.emit('indicators_error', { data, error });
      return null;
    }
  }

  /**
   * Validate data quality and assign quality score
   */
  private validateDataQuality(data: NormalizedMarketData): void {
    let qualityScore = data.qualityScore;

    // Check for missing OHLC data (only if they are zero, not just falsy)
    if (data.open.isZero() || data.high.isZero() || data.low.isZero() || data.close.isZero()) {
      qualityScore -= 30;
    }

    // Check for logical consistency (high >= low, etc.)
    if (data.high.lt(data.low) || 
        data.high.lt(data.open) || 
        data.high.lt(data.close) ||
        data.low.gt(data.open) || 
        data.low.gt(data.close)) {
      qualityScore -= 40;
    }

    // Check timestamp freshness (only for old data, not future data)
    const timestampAge = Date.now() - data.timestamp.getTime();
    if (timestampAge > 300000 && timestampAge > 0) { // 5 minutes and actually old
      qualityScore -= 20;
    }

    // Check spread reasonableness (if available)
    if (data.spread && data.spread.gt(data.close.mul(0.01))) { // > 1% spread
      qualityScore -= 10;
    }

    data.qualityScore = Math.max(qualityScore, 0);

    if (data.qualityScore < this.config.minQualityScore) {
      this.emit('quality_warning', { 
        data, 
        message: `Quality score ${data.qualityScore} below threshold ${this.config.minQualityScore}` 
      });
    }
  }

  /**
   * Update processing statistics
   */
  private updateStats(data: NormalizedMarketData, startTime: number): void {
    const processingTime = Date.now() - startTime;
    data.processingTimeMs = processingTime;

    this.stats.totalProcessed++;
    if (data.isCompressed) {
      this.stats.totalCompressed++;
    }

    // Update rolling averages
    const totalWeight = this.stats.totalProcessed;
    this.stats.averageProcessingTime = 
      ((this.stats.averageProcessingTime * (totalWeight - 1)) + processingTime) / totalWeight;
    
    this.stats.averageQualityScore = 
      ((this.stats.averageQualityScore * (totalWeight - 1)) + data.qualityScore) / totalWeight;

    if (data.compressionRatio) {
      const compressedWeight = this.stats.totalCompressed;
      this.stats.averageCompressionRatio = 
        ((this.stats.averageCompressionRatio * (compressedWeight - 1)) + data.compressionRatio) / compressedWeight;
    }

    this.stats.lastProcessedAt = new Date();
    this.stats.memoryUsageMb = process.memoryUsage().heapUsed / 1024 / 1024;

    // Calculate throughput (rough estimate)
    this.stats.throughputPerSecond = this.stats.totalProcessed / 
      ((Date.now() - this.stats.lastProcessedAt.getTime()) / 1000 || 1);

    // Emit stats update
    this.emit('stats_updated', this.stats);
  }

  /**
   * Handle processing errors
   */
  private handleProcessingError(rawData: RawMarketData, error: Error): void {
    this.stats.errorCount++;
    
    this.emit('processing_error', {
      rawData,
      error: {
        message: error.message,
        stack: error.stack,
        timestamp: new Date(),
      }
    });
  }

  /**
   * Generate unique ID for market data
   */
  private generateDataId(rawData: RawMarketData): string {
    const timestamp = rawData.timestamp.getTime();
    return `${rawData.source}_${rawData.symbol}_${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Normalize instrument symbol across sources
   */
  private normalizeInstrument(symbol: string): string {
    // Standard forex pair normalization
    return symbol.toUpperCase()
      .replace(/[^A-Z0-9]/g, '')
      .replace(/^EUR/, 'EUR')
      .replace(/^USD/, 'USD')
      .replace(/^GBP/, 'GBP')
      .replace(/^JPY/, 'JPY');
  }

  /**
   * Normalize timeframe across sources
   */
  private normalizeTimeframe(timeframe: string): TimeFrame {
    // Handle special cases first before lowercasing
    if (timeframe === '1M' || timeframe === '1month' || timeframe.toLowerCase() === 'monthly') {
      return TimeFrame.MN1;
    }

    const normalized = timeframe.toLowerCase()
      .replace(/min/, 'm')
      .replace(/hour/, 'h')
      .replace(/day/, 'd')
      .replace(/week/, 'w');

    // Map common variations
    switch (normalized) {
      case '1min': case '1m': return TimeFrame.M1;
      case '5min': case '5m': return TimeFrame.M5;
      case '15min': case '15m': return TimeFrame.M15;
      case '30min': case '30m': return TimeFrame.M30;
      case '1hour': case '1h': case '60m': return TimeFrame.H1;
      case '4hour': case '4h': case '240m': return TimeFrame.H4;
      case '1day': case '1d': case 'daily': return TimeFrame.D1;
      case '1week': case '1w': case 'weekly': return TimeFrame.W1;
      default: return TimeFrame.M1;
    }
  }

  /**
   * Normalize timestamp with timezone handling
   */
  private normalizeTimestamp(timestamp: Date | string | number): Date {
    let date: Date;

    if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'string') {
      date = new Date(timestamp);
    } else if (typeof timestamp === 'number') {
      // Handle both seconds and milliseconds timestamps
      date = new Date(timestamp > 1000000000000 ? timestamp : timestamp * 1000);
    } else {
      date = new Date();
    }

    // Ensure timestamp is valid
    if (isNaN(date.getTime())) {
      date = new Date();
    }

    // Convert to UTC if needed
    return new Date(date.getTime() - (date.getTimezoneOffset() * 60000));
  }

  /**
   * Calculate spread from bid/ask prices
   */
  private calculateSpread(bid?: number, ask?: number): Decimal | undefined {
    if (bid && ask) {
      return new Decimal(ask - bid).toDecimalPlaces(this.config.decimalPrecision);
    }
    return undefined;
  }

  /**
   * Get current processing statistics
   */
  public getStats(): ProcessingStats {
    return { ...this.stats };
  }

  /**
   * Update processing configuration
   */
  public updateConfig(newConfig: Partial<ProcessingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config_updated', this.config);
  }

  /**
   * Get compressed data by ID
   */
  public getCompressedData(dataId: string): Buffer | null {
    return this.compressionBuffer.get(dataId) || null;
  }

  /**
   * Clear compression buffer to free memory
   */
  public clearCompressionBuffer(): void {
    this.compressionBuffer.clear();
    this.emit('compression_buffer_cleared');
  }

  /**
   * Shutdown processor and cleanup resources
   */
  public shutdown(): void {
    this.clearCompressionBuffer();
    this.processingQueue = [];
    this.isProcessing = false;
    this.removeAllListeners();
  }
}