/**
 * Trade Status Tracker Tests
 * 
 * Comprehensive unit tests for real-time trade status tracking,
 * WebSocket communication, and trade lifecycle management.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { WebSocket } from 'ws';
import { PrismaClient } from '@prisma/client';
import Decimal from 'decimal.js';
import { TradeStatusTracker, TradeLifecycleState } from '../TradeStatusTracker.js';
import type {
  ExecutionStatus,
  ExecutionErrorCategory,
  TradeStatusSubscription,
  PositionUpdateData,
  ExecutionErrorData
} from '@golddaddy/types';

// Mock Prisma Client
const mockPrisma = {
  tradeExecution: {
    findUnique: vi.fn()
  },
  tradeLifecycleRecord: {
    findMany: vi.fn(),
    create: vi.fn()
  }
} as unknown as PrismaClient;

// Mock WebSocket Server with all required methods
const mockWebSocketServerInstance = {
  on: vi.fn(),
  close: vi.fn(),
  clients: new Set(),
  address: vi.fn().mockReturnValue({ port: 3999 }),
  handleUpgrade: vi.fn(),
  shouldHandle: vi.fn()
};

// Mock the WebSocket module
vi.mock('ws', () => ({
  WebSocket: vi.fn().mockImplementation(() => ({
    on: vi.fn(),
    send: vi.fn(),
    close: vi.fn(),
    readyState: 1, // OPEN
    ping: vi.fn(),
    pong: vi.fn()
  })),
  WebSocketServer: vi.fn(() => mockWebSocketServerInstance)
}));

describe('TradeStatusTracker', () => {
  let statusTracker: TradeStatusTracker;
  let mockWebSocketServer: any;

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // Initialize status tracker with test configuration
    statusTracker = new TradeStatusTracker(mockPrisma, {
      websocketPort: 3999,
      jwtSecret: 'test-secret',
      maxClients: 100,
      heartbeatInterval: 5000,
      clientTimeout: 10000,
      messageQueueSize: 50,
      notificationRetention: 1,
      enableAudioAlerts: false,
      enableDesktopNotifications: false
    });

    // Setup default mocks
    setupDefaultMocks();

    await statusTracker.initialize();
  });

  afterEach(async () => {
    await statusTracker.shutdown();
    vi.restoreAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize WebSocket server successfully', async () => {
      expect(statusTracker['wss']).toBeDefined();
      expect(statusTracker['clients']).toBeDefined();
      expect(statusTracker['tradeStates']).toBeDefined();
    });

    it('should load active trade states on initialization', async () => {
      const mockActiveRecords = [
        {
          tradeId: 'trade_123',
          executionId: 'exec_123',
          currentState: 'EXECUTING',
          previousState: 'PENDING_EXECUTION',
          stateChangeTime: new Date(),
          stateChangeReason: 'Execution started',
          metadata: {},
          auditTrailId: 'audit_123'
        }
      ];

      mockPrisma.tradeLifecycleRecord.findMany.mockResolvedValue(mockActiveRecords);

      const newTracker = new TradeStatusTracker(mockPrisma);
      await newTracker.initialize();

      const activeStates = newTracker.getActiveTradeStates();
      expect(activeStates.size).toBe(1);
      expect(activeStates.get('trade_123')?.currentState).toBe(TradeLifecycleState.EXECUTING);

      await newTracker.shutdown();
    });
  });

  describe('Trade Status Tracking', () => {
    it('should track trade status updates correctly', async () => {
      const mockExecution = {
        id: 'exec_123',
        tradeId: 'trade_123',
        brokerId: 'broker_1',
        brokerOrderId: 'order_123',
        executedPrice: new Decimal('1.0850'),
        slippage: new Decimal('0.0005'),
        latency: 150,
        quality: {
          score: 85,
          slippageScore: 90,
          latencyScore: 95,
          fillRateScore: 100
        },
        trade: {
          userId: 'user_123',
          user: { id: 'user_123' }
        }
      };

      mockPrisma.tradeExecution.findUnique.mockResolvedValue(mockExecution);
      mockPrisma.tradeLifecycleRecord.create.mockResolvedValue({});

      const broadcastSpy = vi.spyOn(statusTracker as any, 'broadcastToSubscribers').mockResolvedValue(undefined);

      await statusTracker.trackTradeStatusUpdate(
        'trade_123',
        'exec_123',
        'FILLED' as ExecutionStatus,
        {
          statusChangeReason: 'Trade execution completed',
          previousStatus: 'EXECUTING' as ExecutionStatus
        }
      );

      expect(broadcastSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'trade_status_update',
          tradeId: 'trade_123',
          userId: 'user_123',
          priority: 'high'
        })
      );

      // Verify trade state was updated
      const tradeStates = statusTracker.getActiveTradeStates();
      const tradeState = tradeStates.get('trade_123');
      expect(tradeState?.currentState).toBe(TradeLifecycleState.FILLED);
    });

    it('should handle position updates with proper priority', async () => {
      const positionData: PositionUpdateData = {
        positionId: 'pos_123',
        instrument: 'EURUSD',
        size: new Decimal('10000'),
        averageEntryPrice: new Decimal('1.0800'),
        currentPrice: new Decimal('1.0750'), // 50 pips loss
        unrealizedPnl: new Decimal('-500'),
        pnlPercentage: new Decimal('-4.6'),
        riskMetrics: {
          exposure: new Decimal('10800'),
          riskPercentage: new Decimal('2.5'),
          currentDrawdown: new Decimal('0.046') // 4.6% drawdown
        }
      };

      const broadcastSpy = vi.spyOn(statusTracker as any, 'broadcastToSubscribers').mockResolvedValue(undefined);

      await statusTracker.trackPositionUpdate(positionData, 'user_123');

      expect(broadcastSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'position_update',
          userId: 'user_123',
          priority: 'normal', // Should be normal since drawdown < 5%
          data: positionData
        })
      );
    });

    it('should assign high priority for significant position drawdown', async () => {
      const positionData: PositionUpdateData = {
        positionId: 'pos_123',
        instrument: 'EURUSD',
        size: new Decimal('10000'),
        averageEntryPrice: new Decimal('1.0800'),
        currentPrice: new Decimal('1.0260'), // Significant loss
        unrealizedPnl: new Decimal('-5400'),
        pnlPercentage: new Decimal('-5.0'),
        riskMetrics: {
          exposure: new Decimal('10800'),
          riskPercentage: new Decimal('5.0'),
          currentDrawdown: new Decimal('0.06') // 6% drawdown - should trigger high priority
        }
      };

      const broadcastSpy = vi.spyOn(statusTracker as any, 'broadcastToSubscribers').mockResolvedValue(undefined);

      await statusTracker.trackPositionUpdate(positionData, 'user_123');

      expect(broadcastSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'position_update',
          userId: 'user_123',
          priority: 'high' // Should be high due to significant drawdown
        })
      );
    });

    it('should track execution errors with recovery actions', async () => {
      const executionError = {
        code: 'NETWORK_TIMEOUT_001',
        message: 'Connection timeout during execution',
        category: 'NETWORK_ERROR' as ExecutionErrorCategory,
        retryable: true,
        brokerId: 'broker_1',
        timestamp: new Date(),
        details: {}
      };

      mockPrisma.tradeLifecycleRecord.create.mockResolvedValue({});
      const broadcastSpy = vi.spyOn(statusTracker as any, 'broadcastToSubscribers').mockResolvedValue(undefined);
      const storeNotificationSpy = vi.spyOn(statusTracker as any, 'storeNotification').mockResolvedValue(undefined);

      await statusTracker.trackExecutionError('trade_123', 'exec_123', executionError, 'user_123');

      expect(broadcastSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'execution_error',
          tradeId: 'trade_123',
          userId: 'user_123',
          priority: 'critical',
          data: expect.objectContaining({
            errorCode: 'NETWORK_TIMEOUT_001',
            errorMessage: 'Connection timeout during execution',
            errorCategory: 'NETWORK_ERROR',
            retryable: true,
            recoveryActions: expect.arrayContaining(['Check network connectivity'])
          })
        })
      );

      expect(storeNotificationSpy).toHaveBeenCalled();
    });
  });

  describe('Trade Lifecycle Management', () => {
    it('should map execution status to lifecycle state correctly', () => {
      const mapper = statusTracker['mapExecutionStatusToLifecycleState'];

      expect(mapper('PENDING' as ExecutionStatus)).toBe(TradeLifecycleState.PENDING_EXECUTION);
      expect(mapper('EXECUTING' as ExecutionStatus)).toBe(TradeLifecycleState.EXECUTING);
      expect(mapper('FILLED' as ExecutionStatus)).toBe(TradeLifecycleState.FILLED);
      expect(mapper('PARTIALLY_FILLED' as ExecutionStatus)).toBe(TradeLifecycleState.PARTIALLY_FILLED);
      expect(mapper('CANCELLED' as ExecutionStatus)).toBe(TradeLifecycleState.CANCELLED);
      expect(mapper('REJECTED' as ExecutionStatus)).toBe(TradeLifecycleState.REJECTED);
      expect(mapper('FAILED' as ExecutionStatus)).toBe(TradeLifecycleState.FAILED);
    });

    it('should update trade lifecycle state correctly', async () => {
      mockPrisma.tradeLifecycleRecord.create.mockResolvedValue({});

      await statusTracker['updateTradeLifecycleState'](
        'trade_123',
        'exec_123',
        TradeLifecycleState.EXECUTING,
        'Execution started'
      );

      const tradeStates = statusTracker.getActiveTradeStates();
      const tradeState = tradeStates.get('trade_123');

      expect(tradeState).toBeDefined();
      expect(tradeState?.currentState).toBe(TradeLifecycleState.EXECUTING);
      expect(tradeState?.stateChangeReason).toBe('Execution started');
      expect(mockPrisma.tradeLifecycleRecord.create).toHaveBeenCalled();
    });

    it('should retrieve trade lifecycle history', async () => {
      const mockHistoryRecords = [
        {
          tradeId: 'trade_123',
          executionId: 'exec_123',
          currentState: 'PENDING_EXECUTION',
          previousState: null,
          stateChangeTime: new Date('2024-01-01T10:00:00Z'),
          stateChangeReason: 'Trade created',
          metadata: {},
          auditTrailId: null
        },
        {
          tradeId: 'trade_123',
          executionId: 'exec_123',
          currentState: 'EXECUTING',
          previousState: 'PENDING_EXECUTION',
          stateChangeTime: new Date('2024-01-01T10:00:30Z'),
          stateChangeReason: 'Execution started',
          metadata: {},
          auditTrailId: 'audit_123'
        }
      ];

      mockPrisma.tradeLifecycleRecord.findMany.mockResolvedValue(mockHistoryRecords);

      const history = await statusTracker.getTradeLifecycleHistory('trade_123');

      expect(history).toHaveLength(2);
      expect(history[0].currentState).toBe(TradeLifecycleState.PENDING_EXECUTION);
      expect(history[1].currentState).toBe(TradeLifecycleState.EXECUTING);
      expect(history[1].previousState).toBe(TradeLifecycleState.PENDING_EXECUTION);
    });
  });

  describe('Priority System', () => {
    it('should determine correct priority for different execution statuses', () => {
      const determinePriority = statusTracker['determinePriority'];

      expect(determinePriority('PENDING' as ExecutionStatus)).toBe('normal');
      expect(determinePriority('EXECUTING' as ExecutionStatus)).toBe('normal');
      expect(determinePriority('FILLED' as ExecutionStatus)).toBe('high');
      expect(determinePriority('PARTIALLY_FILLED' as ExecutionStatus)).toBe('normal');
      expect(determinePriority('CANCELLED' as ExecutionStatus)).toBe('normal');
      expect(determinePriority('REJECTED' as ExecutionStatus)).toBe('critical');
      expect(determinePriority('FAILED' as ExecutionStatus)).toBe('critical');
    });

    it('should generate appropriate recovery actions for different error categories', () => {
      const generateRecoveryActions = statusTracker['generateRecoveryActions'];

      const networkError = {
        code: 'NET_001',
        message: 'Network timeout',
        category: 'NETWORK_ERROR' as ExecutionErrorCategory,
        retryable: true,
        brokerId: 'broker_1',
        timestamp: new Date(),
        details: {}
      };

      const marginError = {
        code: 'MARGIN_001',
        message: 'Insufficient margin',
        category: 'INSUFFICIENT_MARGIN' as ExecutionErrorCategory,
        retryable: false,
        brokerId: 'broker_1',
        timestamp: new Date(),
        details: {}
      };

      const networkActions = generateRecoveryActions(networkError);
      const marginActions = generateRecoveryActions(marginError);

      expect(networkActions).toContain('Check network connectivity');
      expect(networkActions).toContain('Switch to backup broker');

      expect(marginActions).toContain('Add funds to account');
      expect(marginActions).toContain('Reduce position size');
    });

    it('should handle priority queue correctly', async () => {
      // This would test the priority queue system for busy clients
      // Implementation depends on the specific queue management logic
      const getPriorityWeight = statusTracker['getPriorityWeight'];

      expect(getPriorityWeight('critical')).toBeGreaterThan(getPriorityWeight('high'));
      expect(getPriorityWeight('high')).toBeGreaterThan(getPriorityWeight('normal'));
      expect(getPriorityWeight('normal')).toBeGreaterThan(getPriorityWeight('low'));
    });
  });

  describe('Notification Management', () => {
    it('should send notifications with correct priority', async () => {
      const broadcastSpy = vi.spyOn(statusTracker as any, 'broadcastToSubscribers').mockResolvedValue(undefined);
      const storeNotificationSpy = vi.spyOn(statusTracker as any, 'storeNotification').mockResolvedValue(undefined);

      const notification = {
        title: 'Trade Executed',
        message: 'Your EURUSD trade has been executed successfully',
        category: 'execution' as const,
        actionRequired: false
      };

      await statusTracker.sendNotification('user_123', notification, 'high');

      expect(broadcastSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'notification',
          userId: 'user_123',
          priority: 'high',
          data: notification
        })
      );

      expect(storeNotificationSpy).toHaveBeenCalled();
    });

    it('should store critical notifications for later retrieval', async () => {
      const storeNotificationSpy = vi.spyOn(statusTracker as any, 'storeNotification');
      vi.spyOn(statusTracker as any, 'broadcastToSubscribers').mockResolvedValue(undefined);

      const criticalNotification = {
        title: 'Trade Failed',
        message: 'Trade execution failed due to broker error',
        category: 'execution' as const,
        actionRequired: true,
        actions: [
          { label: 'Retry', action: 'retry_trade', data: { tradeId: 'trade_123' } }
        ]
      };

      await statusTracker.sendNotification('user_123', criticalNotification, 'critical');

      expect(storeNotificationSpy).toHaveBeenCalledWith(
        'user_123',
        expect.objectContaining({
          priority: 'critical',
          data: criticalNotification
        })
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      mockPrisma.tradeExecution.findUnique.mockResolvedValue(null); // Return null instead of throwing

      // The method should handle the error gracefully and not throw
      await expect(async () => {
        await statusTracker.trackTradeStatusUpdate('trade_123', 'exec_123', 'FILLED' as ExecutionStatus);
      }).not.toThrow();
    });

    it('should handle WebSocket client errors', () => {
      const clientErrorSpy = vi.spyOn(statusTracker, 'emit');
      const testError = new Error('WebSocket connection lost');

      statusTracker['handleClientError']('client_123', testError);

      expect(clientErrorSpy).toHaveBeenCalledWith('clientError', {
        clientId: 'client_123',
        error: testError
      });
    });
  });

  describe('Client Management', () => {
    it('should handle client subscriptions correctly', () => {
      const mockClient = {
        ws: {
          close: vi.fn(),
          readyState: 1, // OPEN
          send: vi.fn(),
          on: vi.fn(),
          ping: vi.fn(),
          pong: vi.fn()
        } as any,
        clientId: 'client_123',
        userId: 'user_123',
        subscriptions: [],
        permissions: ['trade'],
        lastHeartbeat: new Date(),
        connectionTime: new Date(),
        messagesSent: 0,
        messagesReceived: 0,
        priorityQueue: []
      };

      statusTracker['clients'].set('client_123', mockClient);

      const subscription: TradeStatusSubscription = {
        type: 'specific_trades',
        filters: {
          tradeIds: ['trade_123', 'trade_456'],
          statuses: ['EXECUTING' as ExecutionStatus, 'FILLED' as ExecutionStatus]
        },
        features: {
          realTimeUpdates: true,
          positionTracking: true,
          errorReporting: true,
          notifications: true,
          performance: false
        }
      };

      statusTracker['handleSubscription']('client_123', subscription);

      expect(mockClient.subscriptions).toContain(subscription);
    });

    it('should filter messages based on client subscriptions', () => {
      const mockClient = {
        ws: {
          close: vi.fn(),
          readyState: 1, // OPEN
          send: vi.fn(),
          on: vi.fn(),
          ping: vi.fn(),
          pong: vi.fn()
        } as any,
        clientId: 'client_123',
        userId: 'user_123',
        subscriptions: [{
          type: 'specific_trades' as const,
          filters: {
            tradeIds: ['trade_123']
          },
          features: {
            realTimeUpdates: true,
            positionTracking: true,
            errorReporting: true,
            notifications: true,
            performance: false
          }
        }],
        permissions: ['trade'],
        lastHeartbeat: new Date(),
        connectionTime: new Date(),
        messagesSent: 0,
        messagesReceived: 0,
        priorityQueue: []
      };

      const relevantMessage = {
        type: 'trade_status_update' as const,
        tradeId: 'trade_123',
        userId: 'user_123',
        data: {} as any,
        timestamp: new Date(),
        priority: 'normal' as const
      };

      const irrelevantMessage = {
        type: 'trade_status_update' as const,
        tradeId: 'trade_456', // Not in subscription
        userId: 'user_123',
        data: {} as any,
        timestamp: new Date(),
        priority: 'normal' as const
      };

      expect(statusTracker['isClientSubscribedToMessage'](mockClient, relevantMessage)).toBe(true);
      expect(statusTracker['isClientSubscribedToMessage'](mockClient, irrelevantMessage)).toBe(false);
    });
  });

  // Helper function to setup default mocks
  function setupDefaultMocks(): void {
    mockPrisma.tradeLifecycleRecord.findMany.mockResolvedValue([]);
    mockPrisma.tradeLifecycleRecord.create.mockResolvedValue({});
    mockPrisma.tradeExecution.findUnique.mockResolvedValue(null);
  }
});