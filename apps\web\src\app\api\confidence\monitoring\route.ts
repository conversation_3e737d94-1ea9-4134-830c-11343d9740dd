import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@golddaddy/types';
import {
  UserMonitoringProfile,
  PerformanceMetrics,
  MonitoringAlert,
  evaluateMonitoringRules,
  calculateConfidenceScore,
  calculateRiskScore,
  initializeMonitoringProfile
} from '@/lib/ongoing-monitoring';
import { LiveTradingLevel } from '@/lib/graduated-live-access';

// Mock monitoring profiles (in production, this would be in database)
const mockMonitoringProfiles: Record<string, UserMonitoringProfile> = {
  'demo-user': {
    userId: 'demo-user',
    tradingLevel: 'developing',
    riskProfile: 'moderate',
    monitoringIntensity: 'comprehensive',
    customRules: [],
    alertPreferences: {
      email: true,
      sms: false,
      inApp: true,
      push: true
    },
    emergencyContacts: [
      {
        name: '<PERSON>',
        phone: '******-0123',
        email: '<EMAIL>',
        relationship: 'Spouse',
        notifyOnCritical: true
      }
    ],
    lastMonitored: new Date(),
    metricsHistory: [],
    activeAlerts: []
  }
};

// Mock current performance metrics
const mockCurrentMetrics: Record<string, PerformanceMetrics> = {
  'demo-user': {
    timestamp: new Date(),
    accountBalance: 12450.75,
    dailyPnL: -185.25,
    weeklyPnL: 320.80,
    monthlyPnL: 1205.50,
    drawdownCurrent: 1.8,
    drawdownMax: 4.2,
    winRate: 64.5,
    profitFactor: 1.35,
    sharpeRatio: 1.18,
    tradesCount: 3,
    avgTradeTime: 145,
    emotionalState: 'calm',
    riskScore: 35,
    confidenceScore: 78
  }
};

// Mock performance history (last 30 days)
const mockMetricsHistory: Record<string, PerformanceMetrics[]> = {
  'demo-user': generateMockHistory('demo-user')
};

function generateMockHistory(userId: string): PerformanceMetrics[] {
  const history: PerformanceMetrics[] = [];
  const baseBalance = 11000;
  
  for (let i = 30; i >= 1; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    
    // Generate realistic trading metrics with some randomness
    const dailyReturn = (Math.random() - 0.4) * 0.03; // Slightly positive bias
    const balance = baseBalance * (1 + (30 - i) * 0.01 + dailyReturn);
    
    history.push({
      timestamp: date,
      accountBalance: balance,
      dailyPnL: balance * dailyReturn,
      weeklyPnL: balance * (Math.random() - 0.3) * 0.05,
      monthlyPnL: balance * 0.08,
      drawdownCurrent: Math.max(0, Math.random() * 0.06),
      drawdownMax: Math.max(0, Math.random() * 0.08),
      winRate: 55 + Math.random() * 20,
      profitFactor: 1.1 + Math.random() * 0.5,
      sharpeRatio: 0.8 + Math.random() * 0.8,
      tradesCount: Math.floor(Math.random() * 8),
      avgTradeTime: 60 + Math.random() * 200,
      emotionalState: ['calm', 'confident', 'excited', 'anxious'][Math.floor(Math.random() * 4)] as any,
      riskScore: 20 + Math.random() * 40,
      confidenceScore: 60 + Math.random() * 30
    });
  }
  
  return history;
}

/**
 * GET /api/confidence/monitoring
 * Get monitoring status and alerts
 */
export async function GET(
  request: NextRequest
): Promise<NextResponse<ApiResponse<any>>> {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    const action = searchParams.get('action') || 'status';

    if (action === 'status') {
      const profile = mockMonitoringProfiles[userId];
      const currentMetrics = mockCurrentMetrics[userId];
      const history = mockMetricsHistory[userId] || [];

      if (!profile) {
        return NextResponse.json({
          success: false,
          error: 'Monitoring not initialized',
          message: 'User monitoring profile not found'
        }, { status: 404 });
      }

      // Update metrics history
      profile.metricsHistory = history;
      profile.lastMonitored = new Date();

      // Calculate updated scores
      currentMetrics.confidenceScore = calculateConfidenceScore(currentMetrics, history);
      currentMetrics.riskScore = calculateRiskScore(currentMetrics, history);

      // Evaluate monitoring rules and generate alerts
      const newAlerts = evaluateMonitoringRules(profile, currentMetrics);
      profile.activeAlerts.push(...newAlerts);

      // Filter out auto-resolved alerts older than 24 hours
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      profile.activeAlerts = profile.activeAlerts.filter(alert => 
        !alert.autoResolve || 
        alert.timestamp > oneDayAgo || 
        alert.severity === 'critical'
      );

      return NextResponse.json({
        success: true,
        data: {
          profile,
          currentMetrics,
          newAlertsCount: newAlerts.length,
          monitoringStatus: 'active'
        },
        message: `Monitoring status updated for ${userId}`
      });
    }

    if (action === 'alerts') {
      const profile = mockMonitoringProfiles[userId];
      if (!profile) {
        return NextResponse.json({
          success: false,
          error: 'Profile not found'
        }, { status: 404 });
      }

      const severity = searchParams.get('severity') as 'low' | 'medium' | 'high' | 'critical' | null;
      const unacknowledgedOnly = searchParams.get('unacknowledged') === 'true';

      let alerts = profile.activeAlerts;

      if (severity) {
        alerts = alerts.filter(alert => alert.severity === severity);
      }

      if (unacknowledgedOnly) {
        alerts = alerts.filter(alert => !alert.acknowledged);
      }

      return NextResponse.json({
        success: true,
        data: {
          alerts,
          totalCount: profile.activeAlerts.length,
          unacknowledgedCount: profile.activeAlerts.filter(a => !a.acknowledged).length
        },
        message: `Retrieved ${alerts.length} alerts`
      });
    }

    if (action === 'metrics_history') {
      const history = mockMetricsHistory[userId] || [];
      const days = parseInt(searchParams.get('days') || '7');
      const fromDate = new Date();
      fromDate.setDate(fromDate.getDate() - days);

      const filteredHistory = history.filter(metric => metric.timestamp >= fromDate);

      return NextResponse.json({
        success: true,
        data: {
          metrics: filteredHistory,
          period: `${days} days`,
          dataPoints: filteredHistory.length
        },
        message: `Retrieved ${filteredHistory.length} metrics data points`
      });
    }

    return NextResponse.json({
      success: false,
      error: 'Invalid action',
      message: 'Supported actions: status, alerts, metrics_history'
    }, { status: 400 });

  } catch (error) {
    console.error('Error in monitoring GET:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Monitoring failed',
        message: 'Failed to retrieve monitoring data'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/confidence/monitoring
 * Update monitoring data or acknowledge alerts
 */
export async function POST(
  request: NextRequest
): Promise<NextResponse<ApiResponse<any>>> {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    const action = body.action;

    if (action === 'update_metrics') {
      const metricsUpdate = body.metrics as Partial<PerformanceMetrics>;
      const currentMetrics = mockCurrentMetrics[userId];
      const profile = mockMonitoringProfiles[userId];

      if (!currentMetrics || !profile) {
        return NextResponse.json({
          success: false,
          error: 'Profile not found'
        }, { status: 404 });
      }

      // Update current metrics
      Object.assign(currentMetrics, {
        ...metricsUpdate,
        timestamp: new Date()
      });

      // Add to history
      const history = mockMetricsHistory[userId] || [];
      history.push({ ...currentMetrics });
      mockMetricsHistory[userId] = history.slice(-100); // Keep last 100 entries

      // Recalculate scores
      currentMetrics.confidenceScore = calculateConfidenceScore(currentMetrics, history);
      currentMetrics.riskScore = calculateRiskScore(currentMetrics, history);

      // Evaluate rules for new alerts
      const newAlerts = evaluateMonitoringRules(profile, currentMetrics);
      profile.activeAlerts.push(...newAlerts);

      return NextResponse.json({
        success: true,
        data: {
          updatedMetrics: currentMetrics,
          newAlerts: newAlerts.length,
          riskScore: currentMetrics.riskScore,
          confidenceScore: currentMetrics.confidenceScore
        },
        message: 'Metrics updated successfully'
      });
    }

    if (action === 'acknowledge_alerts') {
      const alertIds = body.alertIds as string[];
      const profile = mockMonitoringProfiles[userId];

      if (!profile) {
        return NextResponse.json({
          success: false,
          error: 'Profile not found'
        }, { status: 404 });
      }

      let acknowledgedCount = 0;
      profile.activeAlerts.forEach(alert => {
        if (alertIds.includes(alert.id) && !alert.acknowledged) {
          alert.acknowledged = true;
          acknowledgedCount++;
        }
      });

      return NextResponse.json({
        success: true,
        data: { acknowledgedCount },
        message: `Acknowledged ${acknowledgedCount} alerts`
      });
    }

    if (action === 'initialize') {
      const { tradingLevel, riskProfile } = body;
      
      if (!tradingLevel) {
        return NextResponse.json({
          success: false,
          error: 'Missing required data',
          message: 'Trading level is required for initialization'
        }, { status: 400 });
      }

      const profile = initializeMonitoringProfile(userId, tradingLevel, riskProfile);
      mockMonitoringProfiles[userId] = profile;

      // Initialize with current metrics
      const initialMetrics: PerformanceMetrics = {
        timestamp: new Date(),
        accountBalance: 10000,
        dailyPnL: 0,
        weeklyPnL: 0,
        monthlyPnL: 0,
        drawdownCurrent: 0,
        drawdownMax: 0,
        winRate: 50,
        profitFactor: 1,
        sharpeRatio: 0,
        tradesCount: 0,
        avgTradeTime: 0,
        emotionalState: 'calm',
        riskScore: 25,
        confidenceScore: 75
      };

      mockCurrentMetrics[userId] = initialMetrics;
      mockMetricsHistory[userId] = [initialMetrics];

      return NextResponse.json({
        success: true,
        data: { profile, initialMetrics },
        message: 'Monitoring initialized successfully'
      });
    }

    if (action === 'emergency_alert') {
      const { alertType, description } = body;
      const profile = mockMonitoringProfiles[userId];

      if (!profile) {
        return NextResponse.json({
          success: false,
          error: 'Profile not found'
        }, { status: 404 });
      }

      const emergencyAlert: MonitoringAlert = {
        id: `emergency_${Date.now()}`,
        userId,
        type: alertType || 'system_alert',
        severity: 'critical',
        title: 'Emergency Alert',
        description: description || 'Emergency situation detected',
        timestamp: new Date(),
        acknowledged: false,
        actionRequired: true,
        recommendedActions: [
          'Stop all trading activity immediately',
          'Contact support team',
          'Review account status'
        ],
        autoResolve: false,
        metadata: { emergency: true }
      };

      profile.activeAlerts.push(emergencyAlert);

      // In production, this would trigger emergency notifications
      console.log(`EMERGENCY ALERT for ${userId}:`, emergencyAlert);

      return NextResponse.json({
        success: true,
        data: { emergencyAlert },
        message: 'Emergency alert created and notifications sent'
      });
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Invalid action',
        message: 'Supported actions: update_metrics, acknowledge_alerts, initialize, emergency_alert'
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in monitoring POST:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Operation failed',
        message: 'Failed to process monitoring request'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/confidence/monitoring
 * Update monitoring configuration
 */
export async function PUT(
  request: NextRequest
): Promise<NextResponse<ApiResponse<any>>> {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    const action = body.action;

    if (action === 'update_preferences') {
      const { alertPreferences, emergencyContacts, monitoringIntensity } = body;
      const profile = mockMonitoringProfiles[userId];

      if (!profile) {
        return NextResponse.json({
          success: false,
          error: 'Profile not found'
        }, { status: 404 });
      }

      if (alertPreferences) {
        Object.assign(profile.alertPreferences, alertPreferences);
      }

      if (emergencyContacts) {
        profile.emergencyContacts = emergencyContacts;
      }

      if (monitoringIntensity) {
        profile.monitoringIntensity = monitoringIntensity;
      }

      return NextResponse.json({
        success: true,
        data: { profile },
        message: 'Monitoring preferences updated'
      });
    }

    if (action === 'update_rules') {
      const { customRules } = body;
      const profile = mockMonitoringProfiles[userId];

      if (!profile) {
        return NextResponse.json({
          success: false,
          error: 'Profile not found'
        }, { status: 404 });
      }

      profile.customRules = customRules || [];

      return NextResponse.json({
        success: true,
        data: { rulesCount: profile.customRules.length },
        message: 'Custom monitoring rules updated'
      });
    }

    if (action === 'resolve_alerts') {
      const { alertIds, resolution } = body;
      const profile = mockMonitoringProfiles[userId];

      if (!profile) {
        return NextResponse.json({
          success: false,
          error: 'Profile not found'
        }, { status: 404 });
      }

      let resolvedCount = 0;
      const now = new Date();

      profile.activeAlerts.forEach(alert => {
        if (alertIds.includes(alert.id)) {
          alert.acknowledged = true;
          alert.resolvedAt = now;
          resolvedCount++;
        }
      });

      // Remove resolved non-critical alerts
      profile.activeAlerts = profile.activeAlerts.filter(alert =>
        !alertIds.includes(alert.id) || alert.severity === 'critical'
      );

      return NextResponse.json({
        success: true,
        data: { resolvedCount },
        message: `Resolved ${resolvedCount} alerts`
      });
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Invalid action',
        message: 'Supported actions: update_preferences, update_rules, resolve_alerts'
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in monitoring PUT:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Update failed',
        message: 'Failed to update monitoring configuration'
      },
      { status: 500 }
    );
  }
}