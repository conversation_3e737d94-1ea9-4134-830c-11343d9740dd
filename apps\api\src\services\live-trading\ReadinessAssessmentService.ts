import type { 
  ReadinessAssessment, 
  ScenarioTest,
  ReadinessAssessmentResult,
  ReadinessAssessmentSession,
  AssessmentSection,
  AssessmentQuestion,
  ReadinessScore,
  ReadinessValidation,
  ReadinessRetryPolicy
} from '@golddaddy/types';
import { ChecklistItemType as QuestionType, AlertSeverity as QuestionSeverity, PsychologicalFactor, ReadinessAssessmentType, AssessmentStatus as ReadinessAssessmentStatus, ReadinessLevel, ScenarioTestStatus } from '@golddaddy/types';

export interface ReadinessAssessmentServiceDependencies {
  loggerService: any;
  auditService?: any;
  notificationService?: any;
  userService?: any;
}

/**
 * ReadinessAssessmentService
 * 
 * Handles psychological readiness evaluation and scenario-based testing
 * for users transitioning to live trading. Provides comprehensive assessment
 * including stress testing, risk tolerance, and emotional stability evaluation.
 */
export class ReadinessAssessmentService {
  private readonly logger: any;
  private readonly auditService?: any;
  private readonly notificationService?: any;
  private readonly userService?: any;

  // Assessment configuration
  private readonly ASSESSMENT_CONFIG = {
    minPassingScore: 85,
    maxRetryAttempts: 3,
    retryDelayHours: 24,
    sessionTimeoutMinutes: 45,
    scenarioTestThreshold: 80,
    psychologicalFactorWeights: {
      [PsychologicalFactor.STRESS_TOLERANCE]: 0.25,
      [PsychologicalFactor.RISK_TOLERANCE]: 0.20,
      [PsychologicalFactor.EMOTIONAL_STABILITY]: 0.20,
      [PsychologicalFactor.DECISION_MAKING]: 0.15,
      [PsychologicalFactor.IMPULSE_CONTROL]: 0.10,
      [PsychologicalFactor.LOSS_ACCEPTANCE]: 0.10
    }
  };

  // Predefined assessment sections
  private readonly ASSESSMENT_SECTIONS: AssessmentSection[] = [
    {
      id: 'stress_management',
      title: 'Stress Management & Emotional Control',
      description: 'Evaluates ability to handle trading stress and maintain emotional stability',
      psychologicalFactor: PsychologicalFactor.STRESS_TOLERANCE,
      weight: 0.25,
      requiredScore: 80,
      questions: [
        {
          id: 'stress_1',
          text: 'You see your position moving against you with a 15% loss. Your immediate reaction is:',
          type: QuestionType.MULTIPLE_CHOICE,
          severity: QuestionSeverity.HIGH,
          options: [
            { id: 'a', text: 'Panic and close the position immediately', score: 0 },
            { id: 'b', text: 'Review my stop loss and stick to my plan', score: 100 },
            { id: 'c', text: 'Add to the position to average down', score: 20 },
            { id: 'd', text: 'Close half the position to reduce stress', score: 60 }
          ],
          explanation: 'Sticking to your predetermined plan shows emotional discipline and stress management.'
        },
        {
          id: 'stress_2',
          text: 'During a volatile market day with multiple losses, how do you manage your emotions?',
          type: QuestionType.MULTIPLE_CHOICE,
          severity: QuestionSeverity.HIGH,
          options: [
            { id: 'a', text: 'Take a break and step away from trading', score: 100 },
            { id: 'b', text: 'Try to win back losses with larger positions', score: 0 },
            { id: 'c', text: 'Continue trading normally', score: 40 },
            { id: 'd', text: 'Reduce position sizes for the day', score: 80 }
          ],
          explanation: 'Taking breaks and reducing risk during stressful periods demonstrates emotional intelligence.'
        },
        {
          id: 'stress_3',
          text: 'Rate your ability to remain calm during significant market movements (1-10):',
          type: QuestionType.SCALE,
          severity: QuestionSeverity.MEDIUM,
          scaleMin: 1,
          scaleMax: 10,
          scaleMinLabel: 'Very Poor',
          scaleMaxLabel: 'Excellent',
          explanation: 'Self-awareness of emotional control is crucial for live trading success.'
        }
      ]
    },
    {
      id: 'risk_management',
      title: 'Risk Management Understanding',
      description: 'Assesses understanding and application of risk management principles',
      psychologicalFactor: PsychologicalFactor.RISK_TOLERANCE,
      weight: 0.20,
      requiredScore: 85,
      questions: [
        {
          id: 'risk_1',
          text: 'What is the maximum percentage of your account you should risk on a single trade?',
          type: QuestionType.MULTIPLE_CHOICE,
          severity: QuestionSeverity.CRITICAL,
          options: [
            { id: 'a', text: '1-2%', score: 100 },
            { id: 'b', text: '5%', score: 60 },
            { id: 'c', text: '10%', score: 20 },
            { id: 'd', text: 'It depends on confidence level', score: 0 }
          ],
          explanation: 'Professional traders typically risk 1-2% per trade to ensure long-term survival.'
        },
        {
          id: 'risk_2',
          text: 'Your trading plan shows a 60% win rate with 1:2 risk-reward. After 5 consecutive losses, you:',
          type: QuestionType.MULTIPLE_CHOICE,
          severity: QuestionSeverity.HIGH,
          options: [
            { id: 'a', text: 'Continue following the plan', score: 100 },
            { id: 'b', text: 'Increase position size to recover faster', score: 0 },
            { id: 'c', text: 'Take a break and review the strategy', score: 80 },
            { id: 'd', text: 'Switch to a different strategy', score: 20 }
          ],
          explanation: 'Consecutive losses are normal in profitable systems. Discipline is key.'
        }
      ]
    },
    {
      id: 'decision_making',
      title: 'Decision Making Under Pressure',
      description: 'Evaluates ability to make rational decisions during high-pressure situations',
      psychologicalFactor: PsychologicalFactor.DECISION_MAKING,
      weight: 0.15,
      requiredScore: 80,
      questions: [
        {
          id: 'decision_1',
          text: 'Major news breaks during your trade. The market gaps against you. You:',
          type: QuestionType.MULTIPLE_CHOICE,
          severity: QuestionSeverity.HIGH,
          options: [
            { id: 'a', text: 'Wait for the market to settle before acting', score: 80 },
            { id: 'b', text: 'Close immediately regardless of loss', score: 60 },
            { id: 'c', text: 'Add to position believing it will reverse', score: 0 },
            { id: 'd', text: 'Follow predetermined news trading rules', score: 100 }
          ],
          explanation: 'Having and following predetermined rules for news events shows disciplined decision-making.'
        }
      ]
    },
    {
      id: 'loss_psychology',
      title: 'Loss Psychology & Recovery',
      description: 'Assesses emotional resilience and ability to recover from trading losses',
      psychologicalFactor: PsychologicalFactor.LOSS_ACCEPTANCE,
      weight: 0.15,
      requiredScore: 80,
      questions: [
        {
          id: 'loss_1',
          text: 'You experience your largest single-day loss (within risk limits). Your mindset is:',
          type: QuestionType.MULTIPLE_CHOICE,
          severity: QuestionSeverity.HIGH,
          options: [
            { id: 'a', text: 'Losses are part of trading, review and learn', score: 100 },
            { id: 'b', text: 'Feel frustrated but continue normally', score: 70 },
            { id: 'c', text: 'Question my trading ability', score: 30 },
            { id: 'd', text: 'Want to quit trading', score: 0 }
          ],
          explanation: 'Accepting losses as normal business expenses is essential for psychological health.'
        }
      ]
    },
    {
      id: 'impulse_control',
      title: 'Impulse Control & Discipline',
      description: 'Tests ability to follow trading plans and resist impulsive decisions',
      psychologicalFactor: PsychologicalFactor.IMPULSE_CONTROL,
      weight: 0.15,
      requiredScore: 85,
      questions: [
        {
          id: 'impulse_1',
          text: 'You see a "perfect setup" outside your trading hours. You:',
          type: QuestionType.MULTIPLE_CHOICE,
          severity: QuestionSeverity.MEDIUM,
          options: [
            { id: 'a', text: 'Stick to my trading schedule', score: 100 },
            { id: 'b', text: 'Take the trade with reduced size', score: 60 },
            { id: 'c', text: 'Take the trade with full size', score: 20 },
            { id: 'd', text: 'Depends on how confident I feel', score: 0 }
          ],
          explanation: 'Discipline in following trading schedules prevents overtrading and burnout.'
        }
      ]
    },
    {
      id: 'emotional_stability',
      title: 'Emotional Stability Assessment',
      description: 'Evaluates overall emotional resilience and stability for live trading',
      psychologicalFactor: PsychologicalFactor.EMOTIONAL_STABILITY,
      weight: 0.10,
      requiredScore: 80,
      questions: [
        {
          id: 'emotion_1',
          text: 'How would you describe your typical mood after a losing day?',
          type: QuestionType.MULTIPLE_CHOICE,
          severity: QuestionSeverity.MEDIUM,
          options: [
            { id: 'a', text: 'Neutral, losses are expected', score: 100 },
            { id: 'b', text: 'Slightly disappointed but motivated', score: 80 },
            { id: 'c', text: 'Frustrated and stressed', score: 40 },
            { id: 'd', text: 'Angry or depressed', score: 0 }
          ],
          explanation: 'Emotional stability after losses is crucial for consistent performance.'
        }
      ]
    }
  ];

  // Predefined scenario tests
  private readonly SCENARIO_TESTS: ScenarioTest[] = [
    {
      id: 'flash_crash',
      title: 'Flash Crash Response',
      description: 'Market drops 5% in 10 minutes while you have open positions',
      category: 'market_stress',
      difficulty: 'high',
      timeLimit: 300, // 5 minutes
      scenario: {
        initialState: {
          accountBalance: 10000,
          openPositions: [
            { symbol: 'EURUSD', size: 0.1, direction: 'long', unrealizedPnL: -150 },
            { symbol: 'GBPUSD', size: 0.05, direction: 'long', unrealizedPnL: -75 }
          ],
          marketCondition: 'extreme_volatility'
        },
        event: 'sudden_market_drop',
        availableActions: [
          'close_all_positions',
          'close_partial_positions', 
          'hold_and_wait',
          'add_to_positions',
          'contact_broker',
          'check_news'
        ]
      },
      passingCriteria: {
        requiredScore: 80,
        maxTimeSeconds: 180,
        correctActions: ['check_news', 'close_partial_positions'],
        forbiddenActions: ['add_to_positions']
      }
    },
    {
      id: 'broker_outage',
      title: 'Broker Platform Outage',
      description: 'Trading platform becomes unavailable during active trades',
      category: 'technical_failure',
      difficulty: 'high',
      timeLimit: 600, // 10 minutes
      scenario: {
        initialState: {
          accountBalance: 10000,
          openPositions: [
            { symbol: 'XAUUSD', size: 0.01, direction: 'short', unrealizedPnL: 50 }
          ],
          platformStatus: 'offline'
        },
        event: 'platform_outage',
        availableActions: [
          'try_mobile_app',
          'call_broker_emergency',
          'use_backup_broker',
          'wait_and_hope',
          'panic_sell_everything'
        ]
      },
      passingCriteria: {
        requiredScore: 85,
        maxTimeSeconds: 300,
        correctActions: ['call_broker_emergency', 'try_mobile_app'],
        forbiddenActions: ['panic_sell_everything', 'wait_and_hope']
      }
    },
    {
      id: 'margin_call',
      title: 'Margin Call Management',
      description: 'Account receives margin call with multiple open positions',
      category: 'risk_management',
      difficulty: 'medium',
      timeLimit: 900, // 15 minutes
      scenario: {
        initialState: {
          accountBalance: 1000,
          usedMargin: 800,
          freeMargin: 200,
          marginLevel: 125, // Below 150% warning level
          openPositions: [
            { symbol: 'EURUSD', size: 0.2, direction: 'long', unrealizedPnL: -100 },
            { symbol: 'USDJPY', size: 0.15, direction: 'short', unrealizedPnL: -50 },
            { symbol: 'GBPUSD', size: 0.1, direction: 'long', unrealizedPnL: 25 }
          ]
        },
        event: 'margin_call_warning',
        availableActions: [
          'close_losing_positions',
          'close_profitable_position',
          'deposit_more_funds',
          'reduce_all_positions',
          'ignore_warning'
        ]
      },
      passingCriteria: {
        requiredScore: 80,
        maxTimeSeconds: 600,
        correctActions: ['close_losing_positions', 'reduce_all_positions'],
        forbiddenActions: ['ignore_warning']
      }
    }
  ];

  constructor(dependencies: ReadinessAssessmentServiceDependencies) {
    this.logger = dependencies.loggerService;
    this.auditService = dependencies.auditService;
    this.notificationService = dependencies.notificationService;
    this.userService = dependencies.userService;
  }

  /**
   * Start a new readiness assessment session
   */
  async startAssessment(
    userId: string,
    assessmentType: ReadinessAssessmentType = ReadinessAssessmentType.COMPREHENSIVE
  ): Promise<ReadinessAssessmentSession> {
    try {
      this.logger.info('Starting readiness assessment', { 
        userId, 
        assessmentType 
      });

      // Check if user has active session
      const activeSession = await this.getActiveSession(userId);
      if (activeSession) {
        throw new Error('User already has an active assessment session');
      }

      // Check retry policy
      await this.validateRetryPolicy(userId);

      const session: ReadinessAssessmentSession = {
        id: `readiness_${userId}_${Date.now()}`,
        userId,
        assessmentType,
        status: ReadinessAssessmentStatus.IN_PROGRESS,
        startedAt: new Date(),
        expiresAt: new Date(Date.now() + this.ASSESSMENT_CONFIG.sessionTimeoutMinutes * 60 * 1000),
        sections: this.getAssessmentSections(assessmentType),
        currentSectionIndex: 0,
        responses: [],
        scenarioTests: this.getScenarioTests(assessmentType),
        completedScenarios: [],
        overallProgress: 0
      };

      // Store session (implementation would depend on your storage system)
      await this.storeSession(session);

      // Audit log
      if (this.auditService) {
        await this.auditService.log({
          userId,
          action: 'readiness_assessment_started',
          details: { sessionId: session.id, assessmentType },
          timestamp: new Date()
        });
      }

      return session;
    } catch (error) {
      this.logger.error('Failed to start readiness assessment', { 
        error: error.message, 
        userId 
      });
      throw error;
    }
  }

  /**
   * Submit answer to assessment question
   */
  async submitAnswer(
    userId: string,
    sessionId: string,
    questionId: string,
    answer: any
  ): Promise<{ success: boolean; nextQuestion?: AssessmentQuestion; sectionComplete?: boolean }> {
    try {
      const session = await this.getSession(sessionId);
      if (!session || session.userId !== userId) {
        throw new Error('Invalid session');
      }

      if (session.status !== ReadinessAssessmentStatus.IN_PROGRESS) {
        throw new Error('Session is not active');
      }

      if (new Date() > session.expiresAt) {
        await this.expireSession(sessionId);
        throw new Error('Session has expired');
      }

      const currentSection = session.sections[session.currentSectionIndex];
      const question = currentSection.questions.find(q => q.id === questionId);
      
      if (!question) {
        throw new Error('Question not found');
      }

      // Calculate score based on question type
      const score = this.calculateQuestionScore(question, answer);
      
      // Store response
      session.responses.push({
        questionId,
        sectionId: currentSection.id,
        answer,
        score,
        timestamp: new Date()
      });

      // Check if section is complete
      const sectionQuestions = currentSection.questions;
      const sectionResponses = session.responses.filter(r => r.sectionId === currentSection.id);
      const sectionComplete = sectionResponses.length === sectionQuestions.length;

      let nextQuestion: AssessmentQuestion | undefined;

      if (sectionComplete) {
        // Move to next section or complete assessment
        session.currentSectionIndex++;
        if (session.currentSectionIndex < session.sections.length) {
          nextQuestion = session.sections[session.currentSectionIndex].questions[0];
        } else {
          // Assessment sections complete, move to scenario tests
          if (session.scenarioTests.length > 0 && session.completedScenarios.length === 0) {
            // Start scenario testing phase
            session.status = ReadinessAssessmentStatus.SCENARIO_TESTING;
          } else {
            // Complete assessment
            await this.completeAssessment(session);
          }
        }
      } else {
        // Get next question in current section
        const nextQuestionIndex = sectionResponses.length;
        nextQuestion = sectionQuestions[nextQuestionIndex];
      }

      // Update progress
      session.overallProgress = this.calculateOverallProgress(session);

      await this.updateSession(session);

      return { success: true, nextQuestion, sectionComplete };
    } catch (error) {
      this.logger.error('Failed to submit answer', { 
        error: error.message, 
        userId, 
        sessionId, 
        questionId 
      });
      throw error;
    }
  }

  /**
   * Start scenario test
   */
  async startScenarioTest(
    userId: string,
    sessionId: string,
    scenarioId: string
  ): Promise<ScenarioTest> {
    try {
      const session = await this.getSession(sessionId);
      if (!session || session.userId !== userId) {
        throw new Error('Invalid session');
      }

      const scenario = session.scenarioTests.find(s => s.id === scenarioId);
      if (!scenario) {
        throw new Error('Scenario not found');
      }

      if (session.completedScenarios.some(cs => cs.scenarioId === scenarioId)) {
        throw new Error('Scenario already completed');
      }

      // Initialize scenario state
      const scenarioSession = {
        scenarioId,
        startedAt: new Date(),
        status: ScenarioTestStatus.IN_PROGRESS,
        actions: [],
        timeRemaining: scenario.timeLimit
      };

      session.currentScenario = scenarioSession;
      await this.updateSession(session);

      return scenario;
    } catch (error) {
      this.logger.error('Failed to start scenario test', { 
        error: error.message, 
        userId, 
        sessionId, 
        scenarioId 
      });
      throw error;
    }
  }

  /**
   * Submit scenario action
   */
  async submitScenarioAction(
    userId: string,
    sessionId: string,
    action: string,
    reasoning?: string
  ): Promise<{ success: boolean; feedback?: string; completed?: boolean }> {
    try {
      const session = await this.getSession(sessionId);
      if (!session || !session.currentScenario) {
        throw new Error('No active scenario');
      }

      const scenario = session.scenarioTests.find(s => s.id === session.currentScenario!.scenarioId);
      if (!scenario) {
        throw new Error('Scenario not found');
      }

      // Record action
      session.currentScenario.actions.push({
        action,
        reasoning,
        timestamp: new Date()
      });

      // Calculate time remaining
      const elapsed = Date.now() - session.currentScenario.startedAt.getTime();
      session.currentScenario.timeRemaining = scenario.timeLimit - Math.floor(elapsed / 1000);

      // Check if scenario is complete (time up or sufficient actions)
      const completed = session.currentScenario.timeRemaining <= 0 || 
                       this.isScenarioComplete(session.currentScenario, scenario);

      if (completed) {
        const result = await this.completeScenarioTest(session, scenario);
        return { success: true, completed: true, feedback: result.feedback };
      }

      await this.updateSession(session);
      return { success: true, completed: false };
    } catch (error) {
      this.logger.error('Failed to submit scenario action', { 
        error: error.message, 
        userId, 
        sessionId 
      });
      throw error;
    }
  }

  /**
   * Get assessment results
   */
  async getAssessmentResult(userId: string, assessmentId: string): Promise<ReadinessAssessmentResult> {
    try {
      // Implementation would retrieve from storage
      const result = await this.retrieveAssessmentResult(assessmentId);
      
      if (!result || result.userId !== userId) {
        throw new Error('Assessment result not found');
      }

      return result;
    } catch (error) {
      this.logger.error('Failed to get assessment result', { 
        error: error.message, 
        userId, 
        assessmentId 
      });
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private getAssessmentSections(type: ReadinessAssessmentType): AssessmentSection[] {
    if (type === ReadinessAssessmentType.QUICK) {
      // Return abbreviated version for quick assessment
      return this.ASSESSMENT_SECTIONS.map(section => ({
        ...section,
        questions: section.questions.slice(0, 1) // Take only first question
      }));
    }
    
    return this.ASSESSMENT_SECTIONS;
  }

  private getScenarioTests(type: ReadinessAssessmentType): ScenarioTest[] {
    if (type === ReadinessAssessmentType.QUICK) {
      return [this.SCENARIO_TESTS[0]]; // Only one scenario for quick test
    }
    
    return this.SCENARIO_TESTS;
  }

  private calculateQuestionScore(question: AssessmentQuestion, answer: any): number {
    switch (question.type) {
      case QuestionType.MULTIPLE_CHOICE:
        const option = question.options?.find(opt => opt.id === answer);
        return option?.score || 0;
        
      case QuestionType.SCALE:
        // For scale questions, normalize to 0-100
        const scaleValue = parseInt(answer);
        const scaleRange = (question.scaleMax || 10) - (question.scaleMin || 1);
        return Math.round(((scaleValue - (question.scaleMin || 1)) / scaleRange) * 100);
        
      case QuestionType.TRUE_FALSE:
        return question.correctAnswer === answer ? 100 : 0;
        
      default:
        return 0;
    }
  }

  private calculateOverallProgress(session: ReadinessAssessmentSession): number {
    const totalQuestions = session.sections.reduce((sum, section) => sum + section.questions.length, 0);
    const completedQuestions = session.responses.length;
    const scenarioProgress = (session.completedScenarios.length / session.scenarioTests.length) * 100;
    
    const questionProgress = (completedQuestions / totalQuestions) * 70; // 70% weight for questions
    const scenarioWeight = 30; // 30% weight for scenarios
    
    return Math.round(questionProgress + (scenarioProgress * scenarioWeight / 100));
  }

  private async completeAssessment(session: ReadinessAssessmentSession): Promise<void> {
    session.status = ReadinessAssessmentStatus.COMPLETED;
    session.completedAt = new Date();

    // Calculate final results
    const result = await this.calculateAssessmentResult(session);
    session.result = result;

    await this.updateSession(session);

    // Audit log
    if (this.auditService) {
      await this.auditService.log({
        userId: session.userId,
        action: 'readiness_assessment_completed',
        details: { 
          sessionId: session.id, 
          overallScore: result.overallScore,
          readinessLevel: result.readinessLevel,
          passed: result.passed
        },
        timestamp: new Date()
      });
    }
  }

  private async calculateAssessmentResult(session: ReadinessAssessmentSession): Promise<ReadinessAssessmentResult> {
    // Calculate section scores
    const sectionScores = session.sections.map(section => {
      const sectionResponses = session.responses.filter(r => r.sectionId === section.id);
      const averageScore = sectionResponses.length > 0 
        ? sectionResponses.reduce((sum, r) => sum + r.score, 0) / sectionResponses.length
        : 0;

      return {
        sectionId: section.id,
        score: Math.round(averageScore),
        passed: averageScore >= section.requiredScore
      };
    });

    // Calculate scenario scores
    const scenarioScores = session.completedScenarios.map(scenario => ({
      scenarioId: scenario.scenarioId,
      score: scenario.score,
      passed: scenario.passed
    }));

    // Calculate overall score using weighted average
    const questionScore = sectionScores.reduce((sum, s) => {
      const section = session.sections.find(sec => sec.id === s.sectionId);
      return sum + (s.score * (section?.weight || 0));
    }, 0);

    const scenarioScore = scenarioScores.length > 0 
      ? scenarioScores.reduce((sum, s) => sum + s.score, 0) / scenarioScores.length
      : 0;

    const overallScore = Math.round((questionScore * 0.7) + (scenarioScore * 0.3));

    // Determine readiness level
    let readinessLevel: ReadinessLevel;
    if (overallScore >= 90) readinessLevel = ReadinessLevel.HIGHLY_READY;
    else if (overallScore >= 80) readinessLevel = ReadinessLevel.READY;
    else if (overallScore >= 70) readinessLevel = ReadinessLevel.CONDITIONALLY_READY;
    else if (overallScore >= 60) readinessLevel = ReadinessLevel.NOT_READY;
    else readinessLevel = ReadinessLevel.STRONGLY_NOT_READY;

    const passed = overallScore >= this.ASSESSMENT_CONFIG.minPassingScore;

    // Generate recommendations
    const recommendations = this.generateRecommendations(sectionScores, scenarioScores, overallScore);

    return {
      assessmentId: session.id,
      userId: session.userId,
      overallScore,
      readinessLevel,
      passed,
      sectionScores,
      scenarioScores,
      recommendations,
      completedAt: session.completedAt!,
      validUntil: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
      retryAllowed: !passed,
      nextRetryAt: !passed ? new Date(Date.now() + this.ASSESSMENT_CONFIG.retryDelayHours * 60 * 60 * 1000) : undefined
    };
  }

  private generateRecommendations(
    sectionScores: any[], 
    scenarioScores: any[], 
    overallScore: number
  ): string[] {
    const recommendations: string[] = [];

    // Section-specific recommendations
    sectionScores.forEach(sectionScore => {
      if (!sectionScore.passed) {
        const section = this.ASSESSMENT_SECTIONS.find(s => s.id === sectionScore.sectionId);
        if (section) {
          recommendations.push(
            `Improve your ${section.title.toLowerCase()}: Score ${sectionScore.score}% (required: ${section.requiredScore}%)`
          );
        }
      }
    });

    // Scenario-specific recommendations
    scenarioScores.forEach(scenarioScore => {
      if (!scenarioScore.passed) {
        const scenario = this.SCENARIO_TESTS.find(s => s.id === scenarioScore.scenarioId);
        if (scenario) {
          recommendations.push(`Practice ${scenario.category} scenarios: Focus on ${scenario.title}`);
        }
      }
    });

    // Overall recommendations
    if (overallScore < 70) {
      recommendations.push('Consider additional paper trading practice before attempting live trading');
      recommendations.push('Review fundamental risk management principles');
    } else if (overallScore < 80) {
      recommendations.push('Complete additional scenario-based training');
      recommendations.push('Consider starting with smaller position sizes');
    } else if (overallScore < 90) {
      recommendations.push('Review areas of weakness before live trading');
    }

    return recommendations;
  }

  private isScenarioComplete(scenarioSession: any, scenario: ScenarioTest): boolean {
    // Check if user has taken sufficient actions or reached logical conclusion
    return scenarioSession.actions.length >= 3 ||
           scenarioSession.actions.some((action: any) => 
             scenario.passingCriteria.correctActions.includes(action.action)
           );
  }

  private async completeScenarioTest(session: ReadinessAssessmentSession, scenario: ScenarioTest): Promise<any> {
    const scenarioSession = session.currentScenario!;
    const actions = scenarioSession.actions.map((a: any) => a.action);
    
    // Calculate score based on actions taken
    let score = 0;
    let feedback = '';

    // Check correct actions
    const correctActions = scenario.passingCriteria.correctActions.filter(action => 
      actions.includes(action)
    );
    score += (correctActions.length / scenario.passingCriteria.correctActions.length) * 70;

    // Check forbidden actions
    const forbiddenActions = scenario.passingCriteria.forbiddenActions?.filter(action => 
      actions.includes(action)
    ) || [];
    score -= forbiddenActions.length * 20;

    // Time bonus
    const timeUsed = scenarioSession.startedAt.getTime() - Date.now();
    if (timeUsed < scenario.passingCriteria.maxTimeSeconds * 1000) {
      score += 10;
    }

    score = Math.max(0, Math.min(100, Math.round(score)));
    const passed = score >= scenario.passingCriteria.requiredScore;

    feedback = this.generateScenarioFeedback(scenario, actions, score, passed);

    // Add to completed scenarios
    session.completedScenarios.push({
      scenarioId: scenario.id,
      score,
      passed,
      completedAt: new Date(),
      actions,
      feedback
    });

    session.currentScenario = undefined;

    // Check if all scenarios complete
    if (session.completedScenarios.length === session.scenarioTests.length) {
      await this.completeAssessment(session);
    }

    return { score, passed, feedback };
  }

  private generateScenarioFeedback(scenario: ScenarioTest, actions: string[], score: number, passed: boolean): string {
    let feedback = `Scenario: ${scenario.title}\n\n`;
    
    if (passed) {
      feedback += `✅ PASSED (Score: ${score}%)\n\n`;
      feedback += 'You demonstrated good decision-making under pressure. ';
    } else {
      feedback += `❌ FAILED (Score: ${score}%)\n\n`;
      feedback += 'Areas for improvement identified. ';
    }

    feedback += `Actions taken: ${actions.join(', ')}\n\n`;
    
    const correctActions = scenario.passingCriteria.correctActions.filter(action => actions.includes(action));
    const missedActions = scenario.passingCriteria.correctActions.filter(action => !actions.includes(action));
    
    if (correctActions.length > 0) {
      feedback += `Good decisions: ${correctActions.join(', ')}\n`;
    }
    
    if (missedActions.length > 0) {
      feedback += `Consider: ${missedActions.join(', ')}\n`;
    }

    const forbiddenActions = scenario.passingCriteria.forbiddenActions?.filter(action => actions.includes(action)) || [];
    if (forbiddenActions.length > 0) {
      feedback += `Avoid: ${forbiddenActions.join(', ')} - These actions increase risk\n`;
    }

    return feedback;
  }

  // Storage methods (implementation depends on your persistence layer)
  private async storeSession(session: ReadinessAssessmentSession): Promise<void> {
    // Implementation would store in database/cache
    this.logger.debug('Storing assessment session', { sessionId: session.id });
  }

  private async getSession(sessionId: string): Promise<ReadinessAssessmentSession | null> {
    // Implementation would retrieve from database/cache
    this.logger.debug('Retrieving assessment session', { sessionId });
    return null; // Placeholder
  }

  private async updateSession(session: ReadinessAssessmentSession): Promise<void> {
    // Implementation would update in database/cache
    this.logger.debug('Updating assessment session', { sessionId: session.id });
  }

  private async getActiveSession(userId: string): Promise<ReadinessAssessmentSession | null> {
    // Implementation would check for active sessions
    return null; // Placeholder
  }

  private async validateRetryPolicy(userId: string): Promise<void> {
    // Implementation would check retry attempts and timing
    this.logger.debug('Validating retry policy', { userId });
  }

  private async expireSession(sessionId: string): Promise<void> {
    // Implementation would mark session as expired
    this.logger.debug('Expiring assessment session', { sessionId });
  }

  private async retrieveAssessmentResult(assessmentId: string): Promise<ReadinessAssessmentResult | null> {
    // Implementation would retrieve result from storage
    return null; // Placeholder
  }

  /**
   * Create a new readiness assessment (alias for startAssessment)
   */
  async createReadinessAssessment(
    userId: string,
    assessmentType: string
  ): Promise<any> {
    return await this.startAssessment(userId, assessmentType);
  }

  /**
   * Get current assessment for a user
   */
  async getCurrentAssessment(userId: string): Promise<any> {
    const session = await this.getActiveSession(userId);
    if (!session) {
      return {
        psychologicalEvaluation: {
          overallPsychologicalScore: 0,
          riskTolerance: 0,
          emotionalStability: 0,
          decisionMakingUnderPressure: 0,
          stressManagementCapability: 0
        },
        scenarioTests: [],
        combinedScore: 0,
        status: 'NOT_STARTED',
        attempts: []
      };
    }

    const result = await this.getAssessmentResult(userId, session.id);
    return result || {
      psychologicalEvaluation: {
        overallPsychologicalScore: 0,
        riskTolerance: 0,
        emotionalStability: 0,
        decisionMakingUnderPressure: 0,
        stressManagementCapability: 0
      },
      scenarioTests: [],
      combinedScore: 0,
      status: 'IN_PROGRESS',
      attempts: []
    };
  }
}