"""
Database Management for Historical Market Data
Handles TimescaleDB integration, schema creation, and data operations
"""

import asyncio
import psycopg2
from psycopg2.extras import RealDictCursor, execute_batch
import asyncpg
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timed<PERSON><PERSON>
from dataclasses import dataclass
import json
from loguru import logger

from config import get_config

@dataclass
class MarketDataPoint:
    """Market data point structure"""
    timestamp: datetime
    instrument: str
    timeframe: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    source: str
    metadata: Optional[Dict[str, Any]] = None

class TimescaleDBManager:
    """
    Manages TimescaleDB operations for historical market data storage
    """
    
    def __init__(self):
        self.config = get_config()
        self.connection_pool: Optional[asyncpg.Pool] = None
        self.sync_connection: Optional[psycopg2.extensions.connection] = None
        
    async def initialize(self):
        """Initialize database connection and schema"""
        logger.info("🗄️ Initializing TimescaleDB connection...")
        
        try:
            # Create async connection pool
            self.connection_pool = await asyncpg.create_pool(
                self.config.database.url,
                min_size=2,
                max_size=self.config.database.max_connections,
                command_timeout=self.config.database.timeout
            )
            
            # Create sync connection for schema operations
            self.sync_connection = psycopg2.connect(
                self.config.database.url,
                cursor_factory=RealDictCursor
            )
            
            # Create schema if not exists
            await self.create_schema()
            
            logger.info("✅ TimescaleDB initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize TimescaleDB: {e}")
            return False
    
    async def create_schema(self):
        """Create TimescaleDB schema for market data"""
        logger.info("📋 Creating TimescaleDB schema...")
        
        schema_sql = """
        -- Enable TimescaleDB extension
        CREATE EXTENSION IF NOT EXISTS timescaledb;
        
        -- Create market_data table
        CREATE TABLE IF NOT EXISTS market_data (
            timestamp TIMESTAMPTZ NOT NULL,
            instrument VARCHAR(50) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            open DECIMAL(15,5) NOT NULL,
            high DECIMAL(15,5) NOT NULL,
            low DECIMAL(15,5) NOT NULL,
            close DECIMAL(15,5) NOT NULL,
            volume BIGINT NOT NULL DEFAULT 0,
            source VARCHAR(20) NOT NULL DEFAULT 'mt5',
            metadata JSONB,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            
            -- Primary key and constraints
            CONSTRAINT market_data_pkey PRIMARY KEY (timestamp, instrument, timeframe),
            CONSTRAINT market_data_prices_check CHECK (high >= low AND high >= open AND high >= close AND low <= open AND low <= close),
            CONSTRAINT market_data_volume_check CHECK (volume >= 0)
        );
        
        -- Create hypertable if not already exists
        SELECT create_hypertable('market_data', 'timestamp', 
                               chunk_time_interval => INTERVAL '1 day',
                               if_not_exists => TRUE);
        
        -- Create indexes for efficient queries
        CREATE INDEX IF NOT EXISTS idx_market_data_instrument_timeframe_timestamp 
            ON market_data (instrument, timeframe, timestamp DESC);
        CREATE INDEX IF NOT EXISTS idx_market_data_timestamp_instrument 
            ON market_data (timestamp, instrument);
        CREATE INDEX IF NOT EXISTS idx_market_data_source 
            ON market_data (source);
        CREATE INDEX IF NOT EXISTS idx_market_data_created_at 
            ON market_data (created_at);
        
        -- Create data quality table
        CREATE TABLE IF NOT EXISTS data_quality_checks (
            id SERIAL PRIMARY KEY,
            check_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            instrument VARCHAR(50) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            data_date DATE NOT NULL,
            check_type VARCHAR(50) NOT NULL,
            status VARCHAR(20) NOT NULL CHECK (status IN ('PASS', 'FAIL', 'WARNING')),
            details JSONB,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_data_quality_checks_instrument_date 
            ON data_quality_checks (instrument, data_date, check_type);
        
        -- Create data sync status table
        CREATE TABLE IF NOT EXISTS data_sync_status (
            id SERIAL PRIMARY KEY,
            instrument VARCHAR(50) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            last_sync_timestamp TIMESTAMPTZ,
            last_successful_sync TIMESTAMPTZ,
            sync_status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (sync_status IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'FAILED')),
            error_message TEXT,
            records_synced BIGINT DEFAULT 0,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            
            CONSTRAINT data_sync_status_unique UNIQUE (instrument, timeframe)
        );
        
        -- Create compression policy for old data (compress data older than 7 days)
        SELECT add_compression_policy('market_data', INTERVAL '7 days', if_not_exists => TRUE);
        
        -- Create retention policy (keep data for 2 years)
        SELECT add_retention_policy('market_data', INTERVAL '2 years', if_not_exists => TRUE);
        
        -- Create continuous aggregate for daily OHLCV data
        CREATE MATERIALIZED VIEW IF NOT EXISTS market_data_daily
        WITH (timescaledb.continuous) AS
        SELECT
            time_bucket('1 day', timestamp) AS day,
            instrument,
            timeframe,
            FIRST(open, timestamp) AS open,
            MAX(high) AS high,
            MIN(low) AS low,
            LAST(close, timestamp) AS close,
            SUM(volume) AS volume,
            COUNT(*) AS tick_count
        FROM market_data
        GROUP BY day, instrument, timeframe;
        
        -- Add continuous aggregate policy
        SELECT add_continuous_aggregate_policy('market_data_daily',
            start_offset => INTERVAL '30 days',
            end_offset => INTERVAL '1 hour',
            schedule_interval => INTERVAL '1 hour',
            if_not_exists => TRUE);
        
        -- Create paper trading orders table
        CREATE TABLE IF NOT EXISTS paper_trading_orders (
            id VARCHAR(50) PRIMARY KEY,
            instrument VARCHAR(50) NOT NULL,
            order_type VARCHAR(20) NOT NULL CHECK (order_type IN ('BUY', 'SELL', 'BUY_LIMIT', 'SELL_LIMIT', 'BUY_STOP', 'SELL_STOP')),
            volume DECIMAL(10,2) NOT NULL CHECK (volume > 0),
            price DECIMAL(15,5),
            stop_loss DECIMAL(15,5),
            take_profit DECIMAL(15,5),
            status VARCHAR(20) NOT NULL DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'FILLED', 'PARTIALLY_FILLED', 'CANCELLED', 'REJECTED', 'EXPIRED')),
            creation_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            fill_time TIMESTAMPTZ,
            fill_price DECIMAL(15,5),
            filled_volume DECIMAL(10,2) DEFAULT 0,
            commission DECIMAL(10,2) DEFAULT 0,
            rejection_reason TEXT,
            metadata JSONB,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_paper_trading_orders_instrument_status 
            ON paper_trading_orders (instrument, status);
        CREATE INDEX IF NOT EXISTS idx_paper_trading_orders_creation_time 
            ON paper_trading_orders (creation_time);
        CREATE INDEX IF NOT EXISTS idx_paper_trading_orders_status 
            ON paper_trading_orders (status);
        
        -- Create paper trading positions table
        CREATE TABLE IF NOT EXISTS paper_trading_positions (
            id VARCHAR(50) PRIMARY KEY,
            instrument VARCHAR(50) NOT NULL,
            volume DECIMAL(10,2) NOT NULL CHECK (volume != 0),
            entry_price DECIMAL(15,5) NOT NULL CHECK (entry_price > 0),
            current_price DECIMAL(15,5) NOT NULL DEFAULT 0,
            status VARCHAR(20) NOT NULL DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'CLOSED', 'PARTIAL')),
            open_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            close_time TIMESTAMPTZ,
            stop_loss DECIMAL(15,5),
            take_profit DECIMAL(15,5),
            unrealized_pnl DECIMAL(15,2) DEFAULT 0,
            realized_pnl DECIMAL(15,2) DEFAULT 0,
            commission DECIMAL(10,2) DEFAULT 0,
            swap DECIMAL(10,2) DEFAULT 0,
            metadata JSONB,
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_paper_trading_positions_instrument_status 
            ON paper_trading_positions (instrument, status);
        CREATE INDEX IF NOT EXISTS idx_paper_trading_positions_open_time 
            ON paper_trading_positions (open_time);
        CREATE INDEX IF NOT EXISTS idx_paper_trading_positions_status 
            ON paper_trading_positions (status);
        
        -- Create paper trading account state table
        CREATE TABLE IF NOT EXISTS paper_trading_account (
            id SERIAL PRIMARY KEY,
            balance DECIMAL(15,2) NOT NULL DEFAULT 10000,
            equity DECIMAL(15,2) NOT NULL DEFAULT 10000,
            margin DECIMAL(15,2) DEFAULT 0,
            free_margin DECIMAL(15,2) DEFAULT 10000,
            profit DECIMAL(15,2) DEFAULT 0,
            total_trades INTEGER DEFAULT 0,
            winning_trades INTEGER DEFAULT 0,
            losing_trades INTEGER DEFAULT 0,
            snapshot_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
            metadata JSONB
        );
        
        CREATE INDEX IF NOT EXISTS idx_paper_trading_account_snapshot_time 
            ON paper_trading_account (snapshot_time);
        
        -- Create data integrity checks table
        CREATE TABLE IF NOT EXISTS data_integrity_checks (
            check_id VARCHAR(100) PRIMARY KEY,
            instrument VARCHAR(50) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            timestamp TIMESTAMPTZ NOT NULL,
            status VARCHAR(20) NOT NULL CHECK (status IN ('VALID', 'WARNING', 'CORRUPT', 'MISSING')),
            message TEXT NOT NULL,
            details JSONB,
            checksum VARCHAR(64),
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_data_integrity_checks_instrument_timeframe 
            ON data_integrity_checks (instrument, timeframe);
        CREATE INDEX IF NOT EXISTS idx_data_integrity_checks_status 
            ON data_integrity_checks (status);
        CREATE INDEX IF NOT EXISTS idx_data_integrity_checks_timestamp 
            ON data_integrity_checks (timestamp);
        
        -- Create data anomalies table
        CREATE TABLE IF NOT EXISTS data_anomalies (
            anomaly_id VARCHAR(100) PRIMARY KEY,
            instrument VARCHAR(50) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            anomaly_type VARCHAR(30) NOT NULL CHECK (anomaly_type IN ('PRICE_SPIKE', 'VOLUME_ANOMALY', 'DATA_GAP', 'DUPLICATE_DATA', 'TIMESTAMP_ANOMALY', 'OUTLIER')),
            severity DECIMAL(3,2) NOT NULL CHECK (severity >= 0 AND severity <= 1),
            timestamp TIMESTAMPTZ NOT NULL,
            affected_records INTEGER DEFAULT 1,
            description TEXT NOT NULL,
            details JSONB,
            confidence DECIMAL(3,2) NOT NULL CHECK (confidence >= 0 AND confidence <= 1),
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_data_anomalies_instrument_timeframe 
            ON data_anomalies (instrument, timeframe);
        CREATE INDEX IF NOT EXISTS idx_data_anomalies_type_severity 
            ON data_anomalies (anomaly_type, severity);
        CREATE INDEX IF NOT EXISTS idx_data_anomalies_timestamp 
            ON data_anomalies (timestamp);
        CREATE INDEX IF NOT EXISTS idx_data_anomalies_confidence 
            ON data_anomalies (confidence);
        
        -- Create data checksums table
        CREATE TABLE IF NOT EXISTS data_checksums (
            id SERIAL PRIMARY KEY,
            instrument VARCHAR(50) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            checksum VARCHAR(64) NOT NULL,
            record_count INTEGER NOT NULL,
            created_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_data_checksums_instrument_timeframe 
            ON data_checksums (instrument, timeframe, created_at);
        CREATE INDEX IF NOT EXISTS idx_data_checksums_checksum 
            ON data_checksums (checksum);
        
        -- Create data quality reports table
        CREATE TABLE IF NOT EXISTS data_quality_reports (
            id SERIAL PRIMARY KEY,
            instrument VARCHAR(50) NOT NULL,
            timeframe VARCHAR(10) NOT NULL,
            period_start TIMESTAMPTZ NOT NULL,
            period_end TIMESTAMPTZ NOT NULL,
            total_records INTEGER NOT NULL,
            valid_records INTEGER NOT NULL,
            integrity_score DECIMAL(5,4) NOT NULL CHECK (integrity_score >= 0 AND integrity_score <= 1),
            anomalies_detected INTEGER DEFAULT 0,
            data_completeness DECIMAL(5,4) NOT NULL CHECK (data_completeness >= 0 AND data_completeness <= 1),
            recommendations JSONB,
            generated_at TIMESTAMPTZ DEFAULT NOW()
        );
        
        CREATE INDEX IF NOT EXISTS idx_data_quality_reports_instrument_timeframe 
            ON data_quality_reports (instrument, timeframe);
        CREATE INDEX IF NOT EXISTS idx_data_quality_reports_period 
            ON data_quality_reports (period_start, period_end);
        CREATE INDEX IF NOT EXISTS idx_data_quality_reports_scores 
            ON data_quality_reports (integrity_score, data_completeness);
        """
        
        try:
            async with self.connection_pool.acquire() as conn:
                await conn.execute(schema_sql)
            logger.info("✅ TimescaleDB schema created successfully")
        except Exception as e:
            logger.error(f"❌ Failed to create schema: {e}")
            raise
    
    async def insert_market_data(self, data_points: List[MarketDataPoint]) -> int:
        """Insert market data points into database"""
        if not data_points:
            return 0
            
        insert_sql = """
        INSERT INTO market_data 
        (timestamp, instrument, timeframe, open, high, low, close, volume, source, metadata)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
        ON CONFLICT (timestamp, instrument, timeframe) 
        DO UPDATE SET
            open = EXCLUDED.open,
            high = EXCLUDED.high,
            low = EXCLUDED.low,
            close = EXCLUDED.close,
            volume = EXCLUDED.volume,
            source = EXCLUDED.source,
            metadata = EXCLUDED.metadata
        """
        
        try:
            async with self.connection_pool.acquire() as conn:
                # Prepare data for batch insert
                records = []
                for point in data_points:
                    records.append((
                        point.timestamp,
                        point.instrument,
                        point.timeframe,
                        point.open,
                        point.high,
                        point.low,
                        point.close,
                        point.volume,
                        point.source,
                        json.dumps(point.metadata) if point.metadata else None
                    ))
                
                # Execute batch insert
                result = await conn.executemany(insert_sql, records)
                
                inserted_count = len(records)
                logger.info(f"📊 Inserted {inserted_count} market data points")
                return inserted_count
                
        except Exception as e:
            logger.error(f"❌ Failed to insert market data: {e}")
            raise
    
    async def get_market_data(self, 
                            instrument: str, 
                            timeframe: str,
                            start_time: datetime, 
                            end_time: datetime,
                            limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Retrieve market data from database"""
        
        query = """
        SELECT timestamp, instrument, timeframe, open, high, low, close, volume, source, metadata
        FROM market_data
        WHERE instrument = $1 AND timeframe = $2 
        AND timestamp >= $3 AND timestamp <= $4
        ORDER BY timestamp ASC
        """
        
        params = [instrument, timeframe, start_time, end_time]
        
        if limit:
            query += " LIMIT $5"
            params.append(limit)
        
        try:
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(query, *params)
                
                results = []
                for row in rows:
                    result = dict(row)
                    # Parse metadata JSON
                    if result['metadata']:
                        result['metadata'] = json.loads(result['metadata'])
                    results.append(result)
                
                logger.info(f"📊 Retrieved {len(results)} market data points for {instrument} {timeframe}")
                return results
                
        except Exception as e:
            logger.error(f"❌ Failed to retrieve market data: {e}")
            raise
    
    async def get_latest_timestamp(self, instrument: str, timeframe: str) -> Optional[datetime]:
        """Get the latest timestamp for an instrument/timeframe combination"""
        
        query = """
        SELECT MAX(timestamp) as latest_timestamp
        FROM market_data
        WHERE instrument = $1 AND timeframe = $2
        """
        
        try:
            async with self.connection_pool.acquire() as conn:
                row = await conn.fetchrow(query, instrument, timeframe)
                return row['latest_timestamp'] if row else None
                
        except Exception as e:
            logger.error(f"❌ Failed to get latest timestamp: {e}")
            return None
    
    async def get_data_gaps(self, 
                          instrument: str, 
                          timeframe: str,
                          start_time: datetime, 
                          end_time: datetime) -> List[Tuple[datetime, datetime]]:
        """Identify gaps in historical data"""
        
        query = """
        WITH expected_timestamps AS (
            SELECT generate_series($3::timestamp, $4::timestamp, 
                CASE $2
                    WHEN '1m' THEN INTERVAL '1 minute'
                    WHEN '5m' THEN INTERVAL '5 minutes'
                    WHEN '15m' THEN INTERVAL '15 minutes'
                    WHEN '30m' THEN INTERVAL '30 minutes'
                    WHEN '1h' THEN INTERVAL '1 hour'
                    WHEN '4h' THEN INTERVAL '4 hours'
                    WHEN '1d' THEN INTERVAL '1 day'
                    ELSE INTERVAL '1 minute'
                END
            ) AS expected_time
        ),
        existing_data AS (
            SELECT timestamp
            FROM market_data
            WHERE instrument = $1 AND timeframe = $2
            AND timestamp >= $3 AND timestamp <= $4
        ),
        gaps AS (
            SELECT expected_time
            FROM expected_timestamps e
            LEFT JOIN existing_data d ON e.expected_time = d.timestamp
            WHERE d.timestamp IS NULL
        )
        SELECT 
            MIN(expected_time) as gap_start,
            MAX(expected_time) as gap_end
        FROM (
            SELECT expected_time,
                   expected_time - ROW_NUMBER() OVER (ORDER BY expected_time) * 
                   CASE $2
                       WHEN '1m' THEN INTERVAL '1 minute'
                       WHEN '5m' THEN INTERVAL '5 minutes'
                       WHEN '15m' THEN INTERVAL '15 minutes'
                       WHEN '30m' THEN INTERVAL '30 minutes'
                       WHEN '1h' THEN INTERVAL '1 hour'
                       WHEN '4h' THEN INTERVAL '4 hours'
                       WHEN '1d' THEN INTERVAL '1 day'
                       ELSE INTERVAL '1 minute'
                   END as group_id
            FROM gaps
        ) grouped
        GROUP BY group_id
        ORDER BY gap_start
        """
        
        try:
            async with self.connection_pool.acquire() as conn:
                rows = await conn.fetch(query, instrument, timeframe, start_time, end_time)
                
                gaps = [(row['gap_start'], row['gap_end']) for row in rows]
                logger.info(f"📊 Found {len(gaps)} data gaps for {instrument} {timeframe}")
                return gaps
                
        except Exception as e:
            logger.error(f"❌ Failed to identify data gaps: {e}")
            return []
    
    async def update_sync_status(self, 
                               instrument: str, 
                               timeframe: str,
                               status: str,
                               last_sync_timestamp: Optional[datetime] = None,
                               records_synced: Optional[int] = None,
                               error_message: Optional[str] = None):
        """Update synchronization status"""
        
        query = """
        INSERT INTO data_sync_status 
        (instrument, timeframe, last_sync_timestamp, last_successful_sync, 
         sync_status, error_message, records_synced, updated_at)
        VALUES ($1, $2, $3, 
                CASE WHEN $4 = 'COMPLETED' THEN $3 ELSE 
                    (SELECT last_successful_sync FROM data_sync_status 
                     WHERE instrument = $1 AND timeframe = $2)
                END,
                $4, $5, $6, NOW())
        ON CONFLICT (instrument, timeframe)
        DO UPDATE SET
            last_sync_timestamp = EXCLUDED.last_sync_timestamp,
            last_successful_sync = CASE WHEN EXCLUDED.sync_status = 'COMPLETED' 
                                  THEN EXCLUDED.last_sync_timestamp 
                                  ELSE data_sync_status.last_successful_sync END,
            sync_status = EXCLUDED.sync_status,
            error_message = EXCLUDED.error_message,
            records_synced = COALESCE(EXCLUDED.records_synced, data_sync_status.records_synced),
            updated_at = NOW()
        """
        
        try:
            async with self.connection_pool.acquire() as conn:
                await conn.execute(query, instrument, timeframe, last_sync_timestamp, 
                                 status, error_message, records_synced)
                logger.info(f"📊 Updated sync status for {instrument} {timeframe}: {status}")
                
        except Exception as e:
            logger.error(f"❌ Failed to update sync status: {e}")
            raise
    
    async def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        
        stats_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(DISTINCT instrument) as unique_instruments,
            COUNT(DISTINCT timeframe) as unique_timeframes,
            MIN(timestamp) as earliest_data,
            MAX(timestamp) as latest_data,
            pg_size_pretty(pg_total_relation_size('market_data')) as table_size
        FROM market_data;
        """
        
        try:
            async with self.connection_pool.acquire() as conn:
                row = await conn.fetchrow(stats_query)
                return dict(row) if row else {}
                
        except Exception as e:
            logger.error(f"❌ Failed to get database stats: {e}")
            return {}
    
    async def cleanup_old_data(self, retention_days: int = 730):
        """Clean up old data beyond retention period"""
        
        cleanup_date = datetime.now() - timedelta(days=retention_days)
        
        query = """
        DELETE FROM market_data 
        WHERE timestamp < $1
        """
        
        try:
            async with self.connection_pool.acquire() as conn:
                result = await conn.execute(query, cleanup_date)
                logger.info(f"🗑️ Cleaned up old data before {cleanup_date}")
                return result
                
        except Exception as e:
            logger.error(f"❌ Failed to cleanup old data: {e}")
            raise
    
    async def close(self):
        """Close database connections"""
        if self.connection_pool:
            await self.connection_pool.close()
        if self.sync_connection:
            self.sync_connection.close()
        logger.info("🔌 Database connections closed")

# Global instance
_db_manager: Optional[TimescaleDBManager] = None

def get_db_manager() -> TimescaleDBManager:
    """Get global database manager instance"""
    global _db_manager
    if _db_manager is None:
        _db_manager = TimescaleDBManager()
    return _db_manager