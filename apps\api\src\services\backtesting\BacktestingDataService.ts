import { PrismaClient, MarketData, Timeframe, DataSource, MarketRegime } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';

/**
 * Historical market data with regime classification
 */
export interface HistoricalDataPoint {
  timestamp: Date;
  instrument: string;
  timeframe: Timeframe;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  source: DataSource;
  indicators?: {
    sma_20?: number;
    sma_50?: number;
    rsi_14?: number;
    macd?: number;
    bollinger_upper?: number;
    bollinger_lower?: number;
  };
  marketRegime?: MarketRegime;
}

/**
 * Historical data request parameters
 */
export interface HistoricalDataRequest {
  instrument: string;
  timeframe: Timeframe;
  startDate: Date;
  endDate: Date;
  includeIndicators?: boolean;
  includeRegimeData?: boolean;
}

/**
 * Market regime analysis result
 */
export interface MarketRegimeAnalysis {
  regime: MarketRegime;
  confidence: number;
  volatility: number;
  trendStrength: number;
  period: {
    start: Date;
    end: Date;
  };
}

/**
 * Service for managing historical market data for backtesting operations
 */
export class BacktestingDataService {
  private readonly prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Retrieve historical market data for backtesting
   */
  async getHistoricalData(request: HistoricalDataRequest): Promise<HistoricalDataPoint[]> {
    try {
      const marketData = await this.prisma.marketData.findMany({
        where: {
          instrument: request.instrument,
          timeframe: request.timeframe,
          timestamp: {
            gte: request.startDate,
            lte: request.endDate,
          },
        },
        orderBy: {
          timestamp: 'asc',
        },
      });

      return marketData.map(this.transformMarketData);
    } catch (error) {
      throw new Error(`Failed to retrieve historical data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get available instruments for backtesting
   */
  async getAvailableInstruments(): Promise<string[]> {
    try {
      const instruments = await this.prisma.marketData.findMany({
        select: {
          instrument: true,
        },
        distinct: ['instrument'],
        orderBy: {
          instrument: 'asc',
        },
      });

      return instruments.map(item => item.instrument);
    } catch (error) {
      throw new Error(`Failed to retrieve available instruments: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get available date range for an instrument
   */
  async getDataRange(instrument: string, timeframe: Timeframe): Promise<{ startDate: Date; endDate: Date } | null> {
    try {
      const range = await this.prisma.marketData.aggregate({
        where: {
          instrument,
          timeframe,
        },
        _min: {
          timestamp: true,
        },
        _max: {
          timestamp: true,
        },
      });

      if (!range._min.timestamp || !range._max.timestamp) {
        return null;
      }

      return {
        startDate: range._min.timestamp,
        endDate: range._max.timestamp,
      };
    } catch (error) {
      throw new Error(`Failed to retrieve data range: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Detect market regimes for historical data
   */
  async detectMarketRegimes(
    instrument: string,
    timeframe: Timeframe,
    startDate: Date,
    endDate: Date
  ): Promise<MarketRegimeAnalysis[]> {
    try {
      const data = await this.getHistoricalData({
        instrument,
        timeframe,
        startDate,
        endDate,
        includeIndicators: true,
      });

      return this.analyzeMarketRegimes(data);
    } catch (error) {
      throw new Error(`Failed to detect market regimes: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get market data with regime classification
   */
  async getHistoricalDataWithRegimes(request: HistoricalDataRequest): Promise<HistoricalDataPoint[]> {
    try {
      const data = await this.getHistoricalData(request);
      
      if (request.includeRegimeData && data.length > 0) {
        const regimes = await this.detectMarketRegimes(
          request.instrument,
          request.timeframe,
          request.startDate,
          request.endDate
        );
        
        // Assign regime to each data point
        return data.map(point => ({
          ...point,
          marketRegime: this.getRegimeForTimestamp(point.timestamp, regimes),
        }));
      }

      return data;
    } catch (error) {
      throw new Error(`Failed to retrieve historical data with regimes: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Store historical market data
   */
  async storeHistoricalData(data: HistoricalDataPoint[]): Promise<void> {
    try {
      const transformedData = data.map(point => ({
        timestamp: point.timestamp,
        instrument: point.instrument,
        timeframe: point.timeframe,
        open: new Decimal(point.open),
        high: new Decimal(point.high),
        low: new Decimal(point.low),
        close: new Decimal(point.close),
        volume: new Decimal(point.volume),
        source: point.source,
        indicators: point.indicators ? JSON.stringify(point.indicators) : null,
      }));

      // Use upsert for efficient batch operations
      await this.prisma.$transaction(async (tx) => {
        for (const point of transformedData) {
          await tx.marketData.upsert({
            where: {
              timestamp_instrument_timeframe: {
                timestamp: point.timestamp,
                instrument: point.instrument,
                timeframe: point.timeframe,
              },
            },
            create: point,
            update: {
              open: point.open,
              high: point.high,
              low: point.low,
              close: point.close,
              volume: point.volume,
              indicators: point.indicators,
            },
          });
        }
      });
    } catch (error) {
      throw new Error(`Failed to store historical data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Calculate technical indicators for historical data
   */
  async calculateIndicators(data: HistoricalDataPoint[]): Promise<HistoricalDataPoint[]> {
    if (data.length === 0) return data;

    const prices = data.map(point => point.close);
    const result = [...data];

    // Calculate Simple Moving Averages
    for (let i = 0; i < data.length; i++) {
      const indicators = result[i].indicators || {};

      // SMA 20
      if (i >= 19) {
        const sma20Sum = prices.slice(i - 19, i + 1).reduce((sum, price) => sum + price, 0);
        indicators.sma_20 = sma20Sum / 20;
      }

      // SMA 50
      if (i >= 49) {
        const sma50Sum = prices.slice(i - 49, i + 1).reduce((sum, price) => sum + price, 0);
        indicators.sma_50 = sma50Sum / 50;
      }

      // RSI 14
      if (i >= 14) {
        indicators.rsi_14 = this.calculateRSI(prices.slice(Math.max(0, i - 13), i + 1));
      }

      // Bollinger Bands
      if (i >= 19 && indicators.sma_20 !== undefined) {
        const sma20 = indicators.sma_20;
        const variance = prices.slice(i - 19, i + 1)
          .reduce((sum, price) => sum + Math.pow(price - sma20, 2), 0) / 20;
        const stdDev = Math.sqrt(variance);
        
        indicators.bollinger_upper = sma20 + (2 * stdDev);
        indicators.bollinger_lower = sma20 - (2 * stdDev);
      }

      result[i] = { ...result[i], indicators };
    }

    return result;
  }

  /**
   * Transform MarketData from Prisma to HistoricalDataPoint
   */
  private transformMarketData(data: MarketData): HistoricalDataPoint {
    return {
      timestamp: data.timestamp,
      instrument: data.instrument,
      timeframe: data.timeframe,
      open: data.open.toNumber(),
      high: data.high.toNumber(),
      low: data.low.toNumber(),
      close: data.close.toNumber(),
      volume: data.volume.toNumber(),
      source: data.source,
      indicators: data.indicators ? JSON.parse(data.indicators as string) : undefined,
    };
  }

  /**
   * Analyze market regimes based on volatility and trend
   */
  private analyzeMarketRegimes(data: HistoricalDataPoint[]): MarketRegimeAnalysis[] {
    if (data.length < 50) return [];

    const regimes: MarketRegimeAnalysis[] = [];
    const windowSize = 20; // Analysis window
    
    for (let i = windowSize; i < data.length; i += windowSize) {
      const window = data.slice(i - windowSize, i);
      const analysis = this.analyzeWindowRegime(window);
      
      regimes.push({
        regime: analysis.regime,
        confidence: analysis.confidence,
        volatility: analysis.volatility,
        trendStrength: analysis.trendStrength,
        period: {
          start: window[0].timestamp,
          end: window[window.length - 1].timestamp,
        },
      });
    }

    return regimes;
  }

  /**
   * Analyze market regime for a specific window
   */
  private analyzeWindowRegime(window: HistoricalDataPoint[]): {
    regime: MarketRegime;
    confidence: number;
    volatility: number;
    trendStrength: number;
  } {
    const prices = window.map(point => point.close);
    const returns = prices.slice(1).map((price, i) => (price - prices[i]) / prices[i]);
    
    // Calculate volatility (standard deviation of returns)
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    const volatility = Math.sqrt(variance);
    
    // Calculate trend strength (correlation with time)
    const trendStrength = this.calculateTrendStrength(prices);
    
    // Determine regime based on volatility and trend
    let regime: MarketRegime;
    let confidence: number;
    
    if (volatility > 0.02) {
      regime = MarketRegime.VOLATILE;
      confidence = Math.min(volatility * 50, 1.0);
    } else if (Math.abs(trendStrength) > 0.7) {
      regime = MarketRegime.TRENDING;
      confidence = Math.abs(trendStrength);
    } else {
      regime = MarketRegime.RANGING;
      confidence = 1.0 - Math.abs(trendStrength);
    }
    
    return { regime, confidence, volatility, trendStrength };
  }

  /**
   * Calculate trend strength using linear correlation
   */
  private calculateTrendStrength(prices: number[]): number {
    const n = prices.length;
    const x = Array.from({ length: n }, (_, i) => i);
    const y = prices;
    
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumXX = x.reduce((sum, val) => sum + val * val, 0);
    const sumYY = y.reduce((sum, val) => sum + val * val, 0);
    
    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumXX - sumX * sumX) * (n * sumYY - sumY * sumY));
    
    return denominator === 0 ? 0 : numerator / denominator;
  }

  /**
   * Calculate RSI (Relative Strength Index)
   */
  private calculateRSI(prices: number[]): number {
    if (prices.length < 2) return 50;
    
    const gains: number[] = [];
    const losses: number[] = [];
    
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? -change : 0);
    }
    
    const avgGain = gains.reduce((sum, gain) => sum + gain, 0) / gains.length;
    const avgLoss = losses.reduce((sum, loss) => sum + loss, 0) / losses.length;
    
    if (avgLoss === 0) return 100;
    
    const rs = avgGain / avgLoss;
    return 100 - (100 / (1 + rs));
  }

  /**
   * Get market regime for a specific timestamp
   */
  private getRegimeForTimestamp(timestamp: Date, regimes: MarketRegimeAnalysis[]): MarketRegime {
    const regime = regimes.find(r => 
      timestamp >= r.period.start && timestamp <= r.period.end
    );
    
    return regime?.regime || MarketRegime.ANY;
  }
}