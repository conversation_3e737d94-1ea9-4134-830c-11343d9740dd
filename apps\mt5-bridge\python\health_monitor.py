"""
Connection Health Monitoring System
Monitors MT5 connection health, implements automatic reconnection, and provides alerting
"""

import asyncio
import time
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
from loguru import logger

from config import get_config
from mt5_connection import get_mt5_connection

class HealthStatus(Enum):
    HEALTHY = "HEALTHY"
    DEGRADED = "DEGRADED"
    UNHEALTHY = "UNHEALTHY"
    CRITICAL = "CRITICAL"

class AlertLevel(Enum):
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

@dataclass
class HealthCheck:
    """Individual health check result"""
    name: str
    status: HealthStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    execution_time_ms: float = 0.0
    threshold_ms: float = 1000.0  # Default 1 second threshold

@dataclass
class Alert:
    """System alert"""
    id: str
    level: AlertLevel
    component: str
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    resolved: bool = False
    resolution_time: Optional[datetime] = None

@dataclass
class ConnectionMetrics:
    """Connection performance metrics"""
    connection_attempts: int = 0
    successful_connections: int = 0
    failed_connections: int = 0
    disconnections: int = 0
    reconnections: int = 0
    avg_response_time_ms: float = 0.0
    last_successful_connection: Optional[datetime] = None
    last_failed_connection: Optional[datetime] = None
    uptime_seconds: float = 0.0
    downtime_seconds: float = 0.0

class HealthMonitor:
    """
    Comprehensive health monitoring system for MT5 Bridge Service
    """
    
    def __init__(self):
        self.config = get_config()
        self.mt5_connection = get_mt5_connection()
        
        # Monitoring state
        self.is_running = False
        self.monitoring_interval = 30  # seconds
        self.critical_check_interval = 5  # seconds for critical components
        
        # Health checks registry
        self.health_checks: Dict[str, Callable] = {}
        self.last_check_results: Dict[str, HealthCheck] = {}
        
        # Metrics and alerts
        self.connection_metrics = ConnectionMetrics()
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.max_alert_history = 1000
        
        # Reconnection settings
        self.reconnection_config = {
            'max_attempts': 10,
            'initial_delay': 5,
            'max_delay': 300,
            'backoff_multiplier': 1.5,
            'reset_after_success_minutes': 60
        }
        
        # Alert callbacks
        self.alert_callbacks: List[Callable] = []
        
        # Service degradation tracking
        self.degraded_services = set()
        self.service_timeouts = {
            'mt5_connection': 10.0,
            'database': 5.0,
            'price_stream': 3.0,
            'historical_data': 30.0,
            'paper_trading': 2.0
        }
        
        # Register default health checks
        self._register_default_health_checks()
        
    async def start(self):
        """Start the health monitoring service"""
        if self.is_running:
            logger.warning("Health monitor already running")
            return
            
        logger.info("🚀 Starting health monitoring system...")
        self.is_running = True
        
        # Start monitoring tasks
        asyncio.create_task(self._health_check_loop())
        asyncio.create_task(self._critical_check_loop())
        asyncio.create_task(self._connection_monitor())
        asyncio.create_task(self._alert_processor())
        
        logger.info("✅ Health monitoring system started")
        
    async def stop(self):
        """Stop the health monitoring service"""
        if not self.is_running:
            return
            
        logger.info("🔌 Stopping health monitoring system...")
        self.is_running = False
        
        # Resolve all active alerts
        for alert in self.active_alerts.values():
            alert.resolved = True
            alert.resolution_time = datetime.now()
        
        logger.info("✅ Health monitoring system stopped")
        
    def register_health_check(self, name: str, check_func: Callable):
        """Register a custom health check"""
        self.health_checks[name] = check_func
        logger.info(f"📋 Registered health check: {name}")
        
    def add_alert_callback(self, callback: Callable):
        """Add callback for alert notifications"""
        self.alert_callbacks.append(callback)
        logger.info("📢 Added alert callback")
        
    async def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        
        # Run all health checks
        health_results = {}
        overall_status = HealthStatus.HEALTHY
        
        for name, check_func in self.health_checks.items():
            try:
                start_time = time.time()
                result = await check_func()
                execution_time = (time.time() - start_time) * 1000
                
                if isinstance(result, HealthCheck):
                    result.execution_time_ms = execution_time
                    health_results[name] = result
                else:
                    # Convert dict result to HealthCheck
                    health_results[name] = HealthCheck(
                        name=name,
                        status=result.get('status', HealthStatus.HEALTHY),
                        message=result.get('message', 'OK'),
                        details=result.get('details', {}),
                        execution_time_ms=execution_time
                    )
                
                # Update overall status
                if health_results[name].status.value > overall_status.value:
                    overall_status = health_results[name].status
                    
            except Exception as e:
                logger.error(f"❌ Health check failed for {name}: {e}")
                health_results[name] = HealthCheck(
                    name=name,
                    status=HealthStatus.CRITICAL,
                    message=f"Health check failed: {str(e)}",
                    details={'error': str(e)}
                )
                overall_status = HealthStatus.CRITICAL
        
        self.last_check_results = health_results
        
        return {
            'overall_status': overall_status.value,
            'timestamp': datetime.now().isoformat(),
            'checks': {
                name: {
                    'status': check.status.value,
                    'message': check.message,
                    'execution_time_ms': check.execution_time_ms,
                    'details': check.details
                }
                for name, check in health_results.items()
            },
            'metrics': self._get_metrics_summary(),
            'active_alerts': len(self.active_alerts),
            'degraded_services': list(self.degraded_services)
        }
        
    async def get_connection_metrics(self) -> Dict[str, Any]:
        """Get connection performance metrics"""
        
        success_rate = 0.0
        if self.connection_metrics.connection_attempts > 0:
            success_rate = self.connection_metrics.successful_connections / self.connection_metrics.connection_attempts
        
        return {
            'connection_attempts': self.connection_metrics.connection_attempts,
            'successful_connections': self.connection_metrics.successful_connections,
            'failed_connections': self.connection_metrics.failed_connections,
            'success_rate': success_rate,
            'disconnections': self.connection_metrics.disconnections,
            'reconnections': self.connection_metrics.reconnections,
            'avg_response_time_ms': self.connection_metrics.avg_response_time_ms,
            'uptime_seconds': self.connection_metrics.uptime_seconds,
            'downtime_seconds': self.connection_metrics.downtime_seconds,
            'last_successful_connection': self.connection_metrics.last_successful_connection.isoformat() if self.connection_metrics.last_successful_connection else None,
            'last_failed_connection': self.connection_metrics.last_failed_connection.isoformat() if self.connection_metrics.last_failed_connection else None
        }
        
    async def get_alerts(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get system alerts"""
        
        alerts_to_return = self.active_alerts.values() if active_only else self.alert_history
        
        return [
            {
                'id': alert.id,
                'level': alert.level.value,
                'component': alert.component,
                'message': alert.message,
                'details': alert.details,
                'timestamp': alert.timestamp.isoformat(),
                'resolved': alert.resolved,
                'resolution_time': alert.resolution_time.isoformat() if alert.resolution_time else None
            }
            for alert in alerts_to_return
        ]
        
    async def trigger_reconnection(self) -> Dict[str, Any]:
        """Manually trigger MT5 reconnection"""
        logger.info("🔄 Manual reconnection triggered")
        
        try:
            success = await self._attempt_reconnection()
            return {
                'success': success,
                'message': 'Reconnection successful' if success else 'Reconnection failed',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"❌ Manual reconnection failed: {e}")
            return {
                'success': False,
                'message': f'Reconnection failed: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
            
    def _register_default_health_checks(self):
        """Register default health checks"""
        
        self.register_health_check('mt5_connection', self._check_mt5_connection)
        self.register_health_check('database_connection', self._check_database_connection)
        self.register_health_check('price_streaming', self._check_price_streaming)
        self.register_health_check('system_resources', self._check_system_resources)
        self.register_health_check('service_response_times', self._check_service_response_times)
        
    async def _check_mt5_connection(self) -> HealthCheck:
        """Check MT5 connection health"""
        
        try:
            start_time = time.time()
            is_connected = await self.mt5_connection.ensure_connection()
            response_time = (time.time() - start_time) * 1000
            
            if is_connected:
                # Get additional MT5 status
                status = self.mt5_connection.get_status()
                
                # Check response time
                if response_time > 5000:  # 5 seconds
                    return HealthCheck(
                        name='mt5_connection',
                        status=HealthStatus.DEGRADED,
                        message=f'MT5 connected but slow response time: {response_time:.0f}ms',
                        details={'response_time_ms': response_time, 'status': status}
                    )
                else:
                    return HealthCheck(
                        name='mt5_connection',
                        status=HealthStatus.HEALTHY,
                        message='MT5 connection healthy',
                        details={'response_time_ms': response_time, 'status': status}
                    )
            else:
                return HealthCheck(
                    name='mt5_connection',
                    status=HealthStatus.CRITICAL,
                    message='MT5 connection failed',
                    details={'response_time_ms': response_time}
                )
                
        except Exception as e:
            return HealthCheck(
                name='mt5_connection',
                status=HealthStatus.CRITICAL,
                message=f'MT5 connection check failed: {str(e)}',
                details={'error': str(e)}
            )
            
    async def _check_database_connection(self) -> HealthCheck:
        """Check database connection health"""
        
        try:
            from database import get_db_manager
            db_manager = get_db_manager()
            
            if not db_manager.connection_pool:
                return HealthCheck(
                    name='database_connection',
                    status=HealthStatus.CRITICAL,
                    message='Database connection pool not initialized'
                )
            
            start_time = time.time()
            async with db_manager.connection_pool.acquire() as conn:
                await conn.execute('SELECT 1')
            response_time = (time.time() - start_time) * 1000
            
            if response_time > 1000:  # 1 second
                return HealthCheck(
                    name='database_connection',
                    status=HealthStatus.DEGRADED,
                    message=f'Database connected but slow: {response_time:.0f}ms',
                    details={'response_time_ms': response_time}
                )
            else:
                return HealthCheck(
                    name='database_connection',
                    status=HealthStatus.HEALTHY,
                    message='Database connection healthy',
                    details={'response_time_ms': response_time}
                )
                
        except Exception as e:
            return HealthCheck(
                name='database_connection',
                status=HealthStatus.CRITICAL,
                message=f'Database connection failed: {str(e)}',
                details={'error': str(e)}
            )
            
    async def _check_price_streaming(self) -> HealthCheck:
        """Check price streaming health"""
        
        try:
            from price_streamer import get_price_streamer
            price_streamer = get_price_streamer()
            
            if not price_streamer.is_running:
                return HealthCheck(
                    name='price_streaming',
                    status=HealthStatus.DEGRADED,
                    message='Price streaming not running'
                )
            
            # Check buffer status and recent activity
            buffer_size = len(price_streamer.price_buffer.buffer) if hasattr(price_streamer, 'price_buffer') else 0
            
            status = HealthStatus.HEALTHY
            message = 'Price streaming healthy'
            
            if buffer_size == 0:
                status = HealthStatus.DEGRADED
                message = 'Price streaming running but no recent data'
            
            return HealthCheck(
                name='price_streaming',
                status=status,
                message=message,
                details={
                    'is_running': price_streamer.is_running,
                    'buffer_size': buffer_size
                }
            )
            
        except Exception as e:
            return HealthCheck(
                name='price_streaming',
                status=HealthStatus.UNHEALTHY,
                message=f'Price streaming check failed: {str(e)}',
                details={'error': str(e)}
            )
            
    async def _check_system_resources(self) -> HealthCheck:
        """Check system resource utilization"""
        
        try:
            import psutil
            
            # Get CPU and memory usage
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Check disk space
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # Determine status based on thresholds
            status = HealthStatus.HEALTHY
            issues = []
            
            if cpu_percent > 90:
                status = HealthStatus.CRITICAL
                issues.append(f'High CPU usage: {cpu_percent:.1f}%')
            elif cpu_percent > 70:
                status = HealthStatus.DEGRADED
                issues.append(f'Elevated CPU usage: {cpu_percent:.1f}%')
            
            if memory_percent > 90:
                status = HealthStatus.CRITICAL
                issues.append(f'High memory usage: {memory_percent:.1f}%')
            elif memory_percent > 80:
                status = HealthStatus.DEGRADED
                issues.append(f'Elevated memory usage: {memory_percent:.1f}%')
            
            if disk_percent > 95:
                status = HealthStatus.CRITICAL
                issues.append(f'Disk space critical: {disk_percent:.1f}%')
            elif disk_percent > 85:
                status = HealthStatus.DEGRADED
                issues.append(f'Disk space low: {disk_percent:.1f}%')
            
            message = 'System resources healthy' if not issues else '; '.join(issues)
            
            return HealthCheck(
                name='system_resources',
                status=status,
                message=message,
                details={
                    'cpu_percent': cpu_percent,
                    'memory_percent': memory_percent,
                    'disk_percent': disk_percent,
                    'memory_available_gb': memory.available / (1024**3)
                }
            )
            
        except ImportError:
            return HealthCheck(
                name='system_resources',
                status=HealthStatus.DEGRADED,
                message='psutil not available for system monitoring',
                details={'error': 'psutil module not found'}
            )
        except Exception as e:
            return HealthCheck(
                name='system_resources',
                status=HealthStatus.UNHEALTHY,
                message=f'System resource check failed: {str(e)}',
                details={'error': str(e)}
            )
            
    async def _check_service_response_times(self) -> HealthCheck:
        """Check service response times"""
        
        response_times = {}
        slow_services = []
        
        # Check each service timeout
        for service, timeout_threshold in self.service_timeouts.items():
            if service in self.last_check_results:
                response_time = self.last_check_results[service].execution_time_ms
                response_times[service] = response_time
                
                if response_time > timeout_threshold * 1000:  # Convert to ms
                    slow_services.append(f'{service}: {response_time:.0f}ms')
        
        if slow_services:
            status = HealthStatus.DEGRADED
            message = f'Slow services detected: {", ".join(slow_services)}'
        else:
            status = HealthStatus.HEALTHY
            message = 'Service response times healthy'
        
        return HealthCheck(
            name='service_response_times',
            status=status,
            message=message,
            details={
                'response_times_ms': response_times,
                'slow_services': slow_services,
                'thresholds_ms': {k: v * 1000 for k, v in self.service_timeouts.items()}
            }
        )
        
    async def _health_check_loop(self):
        """Main health check loop"""
        
        while self.is_running:
            try:
                logger.debug("🔍 Running health checks...")
                
                # Run all health checks
                await self.get_system_health()
                
                # Process alerts based on health check results
                await self._process_health_alerts()
                
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                logger.error(f"❌ Health check loop error: {e}")
                await asyncio.sleep(5)
                
    async def _critical_check_loop(self):
        """Critical health check loop (more frequent)"""
        
        while self.is_running:
            try:
                # Only check critical components more frequently
                critical_checks = ['mt5_connection', 'database_connection']
                
                for check_name in critical_checks:
                    if check_name in self.health_checks:
                        check_func = self.health_checks[check_name]
                        result = await check_func()
                        self.last_check_results[check_name] = result
                        
                        # Trigger immediate alerts for critical issues
                        if result.status == HealthStatus.CRITICAL:
                            await self._create_alert(
                                AlertLevel.CRITICAL,
                                check_name,
                                result.message,
                                result.details
                            )
                
                await asyncio.sleep(self.critical_check_interval)
                
            except Exception as e:
                logger.error(f"❌ Critical check loop error: {e}")
                await asyncio.sleep(2)
                
    async def _connection_monitor(self):
        """Monitor connection status and trigger reconnections"""
        
        consecutive_failures = 0
        last_connection_check = datetime.now()
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # Check if MT5 connection is healthy
                if 'mt5_connection' in self.last_check_results:
                    mt5_check = self.last_check_results['mt5_connection']
                    
                    if mt5_check.status == HealthStatus.CRITICAL:
                        consecutive_failures += 1
                        self.connection_metrics.failed_connections += 1
                        self.connection_metrics.last_failed_connection = current_time
                        
                        # Trigger automatic reconnection
                        if consecutive_failures <= self.reconnection_config['max_attempts']:
                            logger.warning(f"🔄 Attempting automatic reconnection (attempt {consecutive_failures})")
                            
                            success = await self._attempt_reconnection()
                            if success:
                                consecutive_failures = 0
                                self.connection_metrics.successful_connections += 1
                                self.connection_metrics.last_successful_connection = current_time
                                self.connection_metrics.reconnections += 1
                                
                                await self._create_alert(
                                    AlertLevel.INFO,
                                    'connection_monitor',
                                    'MT5 connection restored',
                                    {'reconnection_attempt': consecutive_failures}
                                )
                            else:
                                # Wait with exponential backoff
                                delay = min(
                                    self.reconnection_config['initial_delay'] * 
                                    (self.reconnection_config['backoff_multiplier'] ** (consecutive_failures - 1)),
                                    self.reconnection_config['max_delay']
                                )
                                logger.warning(f"⏱️ Reconnection failed, waiting {delay:.1f}s before next attempt")
                                await asyncio.sleep(delay)
                        else:
                            await self._create_alert(
                                AlertLevel.CRITICAL,
                                'connection_monitor',
                                f'Max reconnection attempts ({self.reconnection_config["max_attempts"]}) exceeded',
                                {'consecutive_failures': consecutive_failures}
                            )
                    else:
                        if consecutive_failures > 0:
                            consecutive_failures = 0
                            self.connection_metrics.successful_connections += 1
                            self.connection_metrics.last_successful_connection = current_time
                
                # Update uptime/downtime metrics
                time_since_last_check = (current_time - last_connection_check).total_seconds()
                
                if self.mt5_connection.is_connected():
                    self.connection_metrics.uptime_seconds += time_since_last_check
                else:
                    self.connection_metrics.downtime_seconds += time_since_last_check
                
                last_connection_check = current_time
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                logger.error(f"❌ Connection monitor error: {e}")
                await asyncio.sleep(5)
                
    async def _attempt_reconnection(self) -> bool:
        """Attempt to reconnect to MT5"""
        
        try:
            self.connection_metrics.connection_attempts += 1
            
            # Shutdown existing connection
            self.mt5_connection.shutdown()
            await asyncio.sleep(2)
            
            # Attempt reconnection
            success = self.mt5_connection.initialize()
            
            if success:
                logger.info("✅ MT5 reconnection successful")
                return True
            else:
                logger.warning("❌ MT5 reconnection failed")
                return False
                
        except Exception as e:
            logger.error(f"❌ Reconnection attempt error: {e}")
            return False
            
    async def _alert_processor(self):
        """Process and manage alerts"""
        
        while self.is_running:
            try:
                # Auto-resolve old alerts
                current_time = datetime.now()
                alerts_to_resolve = []
                
                for alert_id, alert in self.active_alerts.items():
                    # Auto-resolve INFO alerts after 5 minutes
                    if (alert.level == AlertLevel.INFO and 
                        (current_time - alert.timestamp).total_seconds() > 300):
                        alerts_to_resolve.append(alert_id)
                    
                    # Auto-resolve WARNING alerts after 30 minutes if condition cleared
                    elif (alert.level == AlertLevel.WARNING and 
                          (current_time - alert.timestamp).total_seconds() > 1800):
                        # Check if the condition has been cleared
                        if await self._is_alert_condition_cleared(alert):
                            alerts_to_resolve.append(alert_id)
                
                # Resolve alerts
                for alert_id in alerts_to_resolve:
                    await self._resolve_alert(alert_id)
                
                await asyncio.sleep(60)  # Process every minute
                
            except Exception as e:
                logger.error(f"❌ Alert processor error: {e}")
                await asyncio.sleep(10)
                
    async def _process_health_alerts(self):
        """Process alerts based on health check results"""
        
        for check_name, check_result in self.last_check_results.items():
            alert_level = None
            
            if check_result.status == HealthStatus.CRITICAL:
                alert_level = AlertLevel.CRITICAL
            elif check_result.status == HealthStatus.UNHEALTHY:
                alert_level = AlertLevel.ERROR
            elif check_result.status == HealthStatus.DEGRADED:
                alert_level = AlertLevel.WARNING
            
            if alert_level:
                await self._create_alert(
                    alert_level,
                    check_name,
                    check_result.message,
                    check_result.details
                )
                
    async def _create_alert(self, level: AlertLevel, component: str, message: str, details: Dict[str, Any] = None):
        """Create a new alert"""
        
        import uuid
        alert_id = f"{component}_{level.value}_{int(time.time())}"
        
        # Check if similar alert already exists
        existing_alert = None
        for alert in self.active_alerts.values():
            if (alert.component == component and 
                alert.level == level and 
                not alert.resolved):
                existing_alert = alert
                break
        
        if existing_alert:
            # Update existing alert
            existing_alert.message = message
            existing_alert.details = details or {}
            existing_alert.timestamp = datetime.now()
        else:
            # Create new alert
            alert = Alert(
                id=alert_id,
                level=level,
                component=component,
                message=message,
                details=details or {}
            )
            
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            # Limit alert history size
            if len(self.alert_history) > self.max_alert_history:
                self.alert_history = self.alert_history[-self.max_alert_history:]
            
            logger.warning(f"🚨 Alert created: [{level.value}] {component}: {message}")
            
            # Notify alert callbacks
            for callback in self.alert_callbacks:
                try:
                    await callback(alert)
                except Exception as e:
                    logger.error(f"❌ Alert callback error: {e}")
                    
    async def _resolve_alert(self, alert_id: str):
        """Resolve an active alert"""
        
        if alert_id in self.active_alerts:
            alert = self.active_alerts[alert_id]
            alert.resolved = True
            alert.resolution_time = datetime.now()
            
            del self.active_alerts[alert_id]
            
            logger.info(f"✅ Alert resolved: {alert.component}: {alert.message}")
            
    async def _is_alert_condition_cleared(self, alert: Alert) -> bool:
        """Check if alert condition has been cleared"""
        
        # Check if the component that triggered the alert is now healthy
        if alert.component in self.last_check_results:
            check_result = self.last_check_results[alert.component]
            return check_result.status == HealthStatus.HEALTHY
        
        return False
        
    def _get_metrics_summary(self) -> Dict[str, Any]:
        """Get summary of key metrics"""
        
        return {
            'uptime_minutes': self.connection_metrics.uptime_seconds / 60,
            'success_rate': (
                self.connection_metrics.successful_connections / 
                max(self.connection_metrics.connection_attempts, 1)
            ),
            'avg_response_time_ms': self.connection_metrics.avg_response_time_ms,
            'total_reconnections': self.connection_metrics.reconnections,
            'active_alerts': len(self.active_alerts),
            'degraded_services': len(self.degraded_services)
        }

# Global instance
_health_monitor: Optional[HealthMonitor] = None

def get_health_monitor() -> HealthMonitor:
    """Get global health monitor instance"""
    global _health_monitor
    if _health_monitor is None:
        _health_monitor = HealthMonitor()
    return _health_monitor