import { EventEmitter } from 'events';
import type { MarketData } from '@golddaddy/types';

export interface Order {
  ticket: number;
  symbol: string;
  type: OrderType;
  state: OrderState;
  magic: number;
  volumeInitial: number;
  volumeCurrent: number;
  priceOpen: number;
  priceStopLoss: number;
  priceTakeProfit: number;
  priceCurrent: number;
  commission: number;
  swap: number;
  profit: number;
  comment: string;
  timeSetup: Date;
  timeExpiration?: Date;
  timeFill?: Date;
  timeClose?: Date;
  fillPolicy: FillPolicy;
  reason: OrderReason;
  positionId?: number;
  expirationType: ExpirationType;
}

export enum OrderType {
  BUY = 0,
  SELL = 1,
  BUY_LIMIT = 2,
  SELL_LIMIT = 3,
  BUY_STOP = 4,
  SELL_STOP = 5,
  BUY_STOP_LIMIT = 6,
  SELL_STOP_LIMIT = 7,
  CLOSE_BY = 8
}

export enum OrderState {
  STARTED = 0,
  PLACED = 1,
  CANCELED = 2,
  PARTIAL = 3,
  FILLED = 4,
  REJECTED = 5,
  EXPIRED = 6,
  REQUEST_ADD = 7,
  REQUEST_MODIFY = 8,
  REQUEST_CANCEL = 9
}

export enum FillPolicy {
  FOK = 0, // Fill or Kill
  IOC = 1, // Immediate or Cancel
  RETURN = 2 // Return remaining
}

export enum OrderReason {
  CLIENT = 0,
  MOBILE = 1,
  WEB = 2,
  EXPERT = 3,
  SL = 4,
  TP = 5,
  SO = 6
}

export enum ExpirationType {
  GTC = 0, // Good Till Cancel
  DAY = 1, // Good Till Day
  SPECIFIED = 2, // Good Till Specified
  SPECIFIED_DAY = 3 // Good Till Specified Day
}

export interface OrderRequest {
  action: number;
  magic: number;
  order?: number;
  symbol: string;
  volume: number;
  price: number;
  stoplimit?: number;
  sl: number;
  tp: number;
  deviation: number;
  type: OrderType;
  typeFilling: FillPolicy;
  typeTime: ExpirationType;
  expiration?: Date;
  comment: string;
  position?: number;
  positionBy?: number;
}

export interface OrderResult {
  retcode: number;
  deal: number;
  order: number;
  volume: number;
  price: number;
  bid: number;
  ask: number;
  comment: string;
  requestId: number;
  retcodeExternal: number;
}

export interface OrderModification {
  ticket: number;
  price?: number;
  sl?: number;
  tp?: number;
  expiration?: Date;
  comment?: string;
}

export interface PartialFill {
  orderId: number;
  volume: number;
  price: number;
  timestamp: Date;
  remaining: number;
}

export interface OrderManagerConfig {
  enablePartialFills: boolean;
  partialFillProbability: number;
  minPartialFillSize: number;
  enableOrderExpiration: boolean;
  enableSlippage: boolean;
  enableOrderQueue: boolean;
  queueProcessingInterval: number;
  maxOrdersPerSymbol: number;
  enableOrderModification: boolean;
  enableAdvancedOrderTypes: boolean;
}

/**
 * Advanced Order Management Simulator
 * Handles complex order lifecycle including pending orders, partial fills,
 * modifications, expirations, and advanced order types
 */
export class OrderManagementSimulator extends EventEmitter {
  private config: OrderManagerConfig;
  private orders: Map<number, Order> = new Map();
  private pendingOrders: Map<number, Order> = new Map();
  private orderHistory: Order[] = [];
  private marketData: Map<string, MarketData> = new Map();
  private nextTicket: number = 1000000;
  private nextDeal: number = 2000000;
  private processingQueue: boolean = false;
  private expirationTimer: NodeJS.Timeout | null = null;

  constructor(config: Partial<OrderManagerConfig> = {}) {
    super();
    
    this.config = {
      enablePartialFills: true,
      partialFillProbability: 0.15,
      minPartialFillSize: 0.01,
      enableOrderExpiration: true,
      enableSlippage: true,
      enableOrderQueue: true,
      queueProcessingInterval: 100, // 100ms
      maxOrdersPerSymbol: 100,
      enableOrderModification: true,
      enableAdvancedOrderTypes: true,
      ...config
    };

    this.startOrderProcessing();
    this.startExpirationMonitoring();
  }

  /**
   * Start order processing queue
   */
  private startOrderProcessing(): void {
    if (!this.config.enableOrderQueue) return;

    setInterval(() => {
      this.processOrders();
    }, this.config.queueProcessingInterval);
  }

  /**
   * Start expiration monitoring
   */
  private startExpirationMonitoring(): void {
    if (!this.config.enableOrderExpiration) return;

    this.expirationTimer = setInterval(() => {
      this.checkOrderExpirations();
    }, 1000); // Check every second
  }

  /**
   * Update market data for order processing
   */
  updateMarketData(marketData: MarketData): void {
    this.marketData.set(marketData.symbol, marketData);
    this.processPendingOrders(marketData.symbol);
  }

  /**
   * Send an order
   */
  async sendOrder(request: OrderRequest): Promise<OrderResult> {
    try {
      // Validate order request
      const validation = this.validateOrderRequest(request);
      if (validation.retcode !== 10009) {
        return validation;
      }

      // Create order
      const order = this.createOrder(request);
      
      // Process based on order type
      if (this.isMarketOrder(request.type)) {
        return await this.processMarketOrder(order, request);
      } else {
        return await this.processPendingOrder(order, request);
      }
    } catch (error) {
      console.error('Order send error:', error);
      return this.createErrorResult(10010, 'Order processing failed'); // TRADE_RETCODE_ERROR
    }
  }

  /**
   * Modify an existing order
   */
  async modifyOrder(modification: OrderModification): Promise<OrderResult> {
    if (!this.config.enableOrderModification) {
      return this.createErrorResult(10013, 'Order modification not supported');
    }

    const order = this.orders.get(modification.ticket) || this.pendingOrders.get(modification.ticket);
    if (!order) {
      return this.createErrorResult(10013, 'Order not found');
    }

    // Can't modify filled orders
    if (order.state === OrderState.FILLED || order.state === OrderState.CANCELED) {
      return this.createErrorResult(10013, 'Cannot modify filled or canceled order');
    }

    // Apply modifications
    if (modification.price !== undefined) {
      order.priceOpen = modification.price;
    }
    if (modification.sl !== undefined) {
      order.priceStopLoss = modification.sl;
    }
    if (modification.tp !== undefined) {
      order.priceTakeProfit = modification.tp;
    }
    if (modification.expiration !== undefined) {
      order.timeExpiration = modification.expiration;
    }
    if (modification.comment !== undefined) {
      order.comment = modification.comment;
    }

    order.state = OrderState.REQUEST_MODIFY;
    
    this.emit('orderModified', order);

    return {
      retcode: 10009, // TRADE_RETCODE_DONE
      deal: 0,
      order: order.ticket,
      volume: order.volumeCurrent,
      price: order.priceOpen,
      bid: 0,
      ask: 0,
      comment: 'Order modified',
      requestId: 0,
      retcodeExternal: 0
    };
  }

  /**
   * Cancel an order
   */
  async cancelOrder(ticket: number): Promise<OrderResult> {
    const order = this.orders.get(ticket) || this.pendingOrders.get(ticket);
    if (!order) {
      return this.createErrorResult(10013, 'Order not found');
    }

    // Can't cancel filled orders
    if (order.state === OrderState.FILLED) {
      return this.createErrorResult(10013, 'Cannot cancel filled order');
    }

    order.state = OrderState.CANCELED;
    order.timeClose = new Date();

    // Remove from active orders
    this.orders.delete(ticket);
    this.pendingOrders.delete(ticket);
    
    // Add to history
    this.orderHistory.push(order);

    this.emit('orderCanceled', order);

    return {
      retcode: 10009,
      deal: 0,
      order: ticket,
      volume: 0,
      price: 0,
      bid: 0,
      ask: 0,
      comment: 'Order canceled',
      requestId: 0,
      retcodeExternal: 0
    };
  }

  /**
   * Get all orders
   */
  getOrders(): Order[] {
    return Array.from(this.orders.values());
  }

  /**
   * Get pending orders
   */
  getPendingOrders(): Order[] {
    return Array.from(this.pendingOrders.values());
  }

  /**
   * Get order history
   */
  getOrderHistory(): Order[] {
    return [...this.orderHistory];
  }

  /**
   * Get specific order
   */
  getOrder(ticket: number): Order | null {
    return this.orders.get(ticket) || this.pendingOrders.get(ticket) || null;
  }

  /**
   * Process orders in the queue
   */
  private processOrders(): void {
    if (this.processingQueue) return;
    
    this.processingQueue = true;
    
    try {
      // Process all symbols with market data
      for (const symbol of this.marketData.keys()) {
        this.processPendingOrders(symbol);
      }
    } finally {
      this.processingQueue = false;
    }
  }

  /**
   * Process pending orders for a symbol
   */
  private processPendingOrders(symbol: string): void {
    const marketData = this.marketData.get(symbol);
    if (!marketData) return;

    const pendingOrders = Array.from(this.pendingOrders.values())
      .filter(order => order.symbol === symbol);

    for (const order of pendingOrders) {
      this.checkOrderActivation(order, marketData);
    }
  }

  /**
   * Check if a pending order should be activated
   */
  private checkOrderActivation(order: Order, marketData: MarketData): void {
    let shouldActivate = false;
    let executionPrice = 0;

    switch (order.type) {
      case OrderType.BUY_LIMIT:
        shouldActivate = marketData.ask <= order.priceOpen;
        executionPrice = order.priceOpen;
        break;
        
      case OrderType.SELL_LIMIT:
        shouldActivate = marketData.bid >= order.priceOpen;
        executionPrice = order.priceOpen;
        break;
        
      case OrderType.BUY_STOP:
        shouldActivate = marketData.ask >= order.priceOpen;
        executionPrice = marketData.ask;
        break;
        
      case OrderType.SELL_STOP:
        shouldActivate = marketData.bid <= order.priceOpen;
        executionPrice = marketData.bid;
        break;
        
      case OrderType.BUY_STOP_LIMIT:
        shouldActivate = marketData.ask >= order.priceOpen;
        executionPrice = Math.min(marketData.ask, order.priceOpen);
        break;
        
      case OrderType.SELL_STOP_LIMIT:
        shouldActivate = marketData.bid <= order.priceOpen;
        executionPrice = Math.max(marketData.bid, order.priceOpen);
        break;
    }

    if (shouldActivate) {
      this.activatePendingOrder(order, executionPrice, marketData);
    }
  }

  /**
   * Activate a pending order
   */
  private activatePendingOrder(order: Order, executionPrice: number, marketData: MarketData): void {
    // Remove from pending orders
    this.pendingOrders.delete(order.ticket);

    // Apply slippage if enabled
    if (this.config.enableSlippage) {
      const slippage = this.calculateSlippage(order, marketData);
      executionPrice += slippage;
    }

    // Handle partial fills
    if (this.config.enablePartialFills && this.shouldPartiallyFill(order)) {
      this.processPartialFill(order, executionPrice, marketData);
    } else {
      this.processFullFill(order, executionPrice, marketData);
    }
  }

  /**
   * Determine if order should be partially filled
   */
  private shouldPartiallyFill(order: Order): boolean {
    if (order.volumeCurrent <= this.config.minPartialFillSize) return false;
    return Math.random() < this.config.partialFillProbability;
  }

  /**
   * Process partial fill
   */
  private processPartialFill(order: Order, executionPrice: number, marketData: MarketData): void {
    // Determine fill size (30-80% of remaining volume)
    const fillRatio = 0.3 + Math.random() * 0.5;
    const fillVolume = Math.max(
      this.config.minPartialFillSize,
      Math.min(order.volumeCurrent, order.volumeCurrent * fillRatio)
    );

    // Create partial fill
    const partialFill: PartialFill = {
      orderId: order.ticket,
      volume: fillVolume,
      price: executionPrice,
      timestamp: new Date(),
      remaining: order.volumeCurrent - fillVolume
    };

    // Update order
    order.volumeCurrent -= fillVolume;
    order.priceCurrent = executionPrice;
    order.state = OrderState.PARTIAL;
    order.timeFill = new Date();

    // Calculate profit/loss for the partial fill
    const profit = this.calculateProfit(order, executionPrice, fillVolume);
    order.profit += profit;

    // If volume remaining, keep as pending
    if (order.volumeCurrent > 0) {
      this.pendingOrders.set(order.ticket, order);
    } else {
      // Fully filled
      order.state = OrderState.FILLED;
      order.timeClose = new Date();
      this.orders.set(order.ticket, order);
    }

    this.emit('partialFill', partialFill);
    this.emit('orderUpdate', order);
  }

  /**
   * Process full fill
   */
  private processFullFill(order: Order, executionPrice: number, marketData: MarketData): void {
    order.state = OrderState.FILLED;
    order.priceCurrent = executionPrice;
    order.timeFill = new Date();
    order.timeClose = new Date();
    order.volumeCurrent = 0;

    // Calculate final profit/loss
    order.profit = this.calculateProfit(order, executionPrice, order.volumeInitial);

    // Move to active orders
    this.orders.set(order.ticket, order);

    this.emit('orderFilled', order);
  }

  /**
   * Force check for order expirations (for testing)
   */
  checkExpirations(): void {
    this.checkOrderExpirations();
  }

  /**
   * Check for order expirations
   */
  private checkOrderExpirations(): void {
    const now = new Date();
    const expiredOrders: Order[] = [];

    for (const order of this.pendingOrders.values()) {
      if (order.timeExpiration && now > order.timeExpiration) {
        expiredOrders.push(order);
      }
    }

    for (const order of expiredOrders) {
      order.state = OrderState.EXPIRED;
      order.timeClose = new Date();
      
      this.pendingOrders.delete(order.ticket);
      this.orderHistory.push(order);
      
      this.emit('orderExpired', order);
    }
  }

  /**
   * Validate order request
   */
  private validateOrderRequest(request: OrderRequest): OrderResult {
    // Check symbol
    if (!this.marketData.has(request.symbol)) {
      return this.createErrorResult(10014, 'Invalid symbol');
    }

    // Check volume
    if (request.volume <= 0 || request.volume > 100) {
      return this.createErrorResult(10015, 'Invalid volume');
    }

    // Check order type support
    if (!this.config.enableAdvancedOrderTypes && request.type > OrderType.SELL_STOP) {
      return this.createErrorResult(10016, 'Order type not supported');
    }

    // Check maximum orders per symbol
    const symbolOrders = Array.from(this.orders.values())
      .concat(Array.from(this.pendingOrders.values()))
      .filter(order => order.symbol === request.symbol);
    
    if (symbolOrders.length >= this.config.maxOrdersPerSymbol) {
      return this.createErrorResult(10020, 'Too many orders for symbol');
    }

    return this.createSuccessResult();
  }

  /**
   * Create order from request
   */
  private createOrder(request: OrderRequest): Order {
    const now = new Date();
    
    return {
      ticket: this.nextTicket++,
      symbol: request.symbol,
      type: request.type,
      state: OrderState.STARTED,
      magic: request.magic,
      volumeInitial: request.volume,
      volumeCurrent: request.volume,
      priceOpen: request.price,
      priceStopLoss: request.sl,
      priceTakeProfit: request.tp,
      priceCurrent: request.price,
      commission: 0,
      swap: 0,
      profit: 0,
      comment: request.comment,
      timeSetup: now,
      timeExpiration: request.expiration,
      fillPolicy: request.typeFilling,
      reason: OrderReason.CLIENT,
      expirationType: request.typeTime
    };
  }

  /**
   * Check if order type is market order
   */
  private isMarketOrder(orderType: OrderType): boolean {
    return orderType === OrderType.BUY || orderType === OrderType.SELL;
  }

  /**
   * Process market order
   */
  private async processMarketOrder(order: Order, request: OrderRequest): Promise<OrderResult> {
    const marketData = this.marketData.get(request.symbol);
    if (!marketData) {
      return this.createErrorResult(10018, 'Market closed');
    }

    // Get execution price
    const executionPrice = order.type === OrderType.BUY ? marketData.ask : marketData.bid;
    
    // Apply slippage
    let finalPrice = executionPrice;
    if (this.config.enableSlippage) {
      const slippage = this.calculateSlippage(order, marketData);
      finalPrice += slippage;
    }

    // Check for partial fills on large orders
    if (this.config.enablePartialFills && this.shouldPartiallyFill(order)) {
      // Process as partial fill
      order.state = OrderState.PARTIAL;
      order.priceCurrent = finalPrice;
      order.timeFill = new Date();
      
      this.processPartialFill(order, finalPrice, marketData);
      
      // Store order (still active with remaining volume)
      this.orders.set(order.ticket, order);
      
      return {
        retcode: 10009,
        deal: this.nextDeal++,
        order: order.ticket,
        volume: order.volumeInitial - order.volumeCurrent, // Filled volume
        price: finalPrice,
        bid: marketData.bid,
        ask: marketData.ask,
        comment: 'Order partially executed',
        requestId: 0,
        retcodeExternal: 0
      };
    }

    // Execute immediately (full fill)
    order.state = OrderState.FILLED;
    order.priceCurrent = finalPrice;
    order.timeFill = new Date();
    order.timeClose = new Date();
    order.volumeCurrent = 0;
    order.profit = this.calculateProfit(order, finalPrice, order.volumeInitial);

    // Store order
    this.orders.set(order.ticket, order);

    this.emit('orderFilled', order);

    return {
      retcode: 10009,
      deal: this.nextDeal++,
      order: order.ticket,
      volume: order.volumeInitial,
      price: finalPrice,
      bid: marketData.bid,
      ask: marketData.ask,
      comment: 'Order executed',
      requestId: 0,
      retcodeExternal: 0
    };
  }

  /**
   * Process pending order
   */
  private async processPendingOrder(order: Order, request: OrderRequest): Promise<OrderResult> {
    order.state = OrderState.PLACED;
    
    // Store as pending order
    this.pendingOrders.set(order.ticket, order);

    this.emit('orderPlaced', order);

    return {
      retcode: 10009,
      deal: 0,
      order: order.ticket,
      volume: order.volumeInitial,
      price: order.priceOpen,
      bid: 0,
      ask: 0,
      comment: 'Order placed',
      requestId: 0,
      retcodeExternal: 0
    };
  }

  /**
   * Calculate slippage for order
   */
  private calculateSlippage(order: Order, marketData: MarketData): number {
    const baseSlippage = 0.0001; // 1 pip
    const volumeMultiplier = Math.sqrt(order.volumeInitial);
    const spreadMultiplier = marketData.spread / 0.0001;
    
    const slippage = baseSlippage * volumeMultiplier * spreadMultiplier * (Math.random() * 0.5 + 0.5);
    
    // Apply direction
    const isBuy = order.type === OrderType.BUY || 
                  order.type === OrderType.BUY_LIMIT || 
                  order.type === OrderType.BUY_STOP;
    
    return isBuy ? slippage : -slippage;
  }

  /**
   * Calculate profit for order
   */
  private calculateProfit(order: Order, executionPrice: number, volume: number): number {
    const contractSize = 100000; // Standard lot
    const isBuy = order.type === OrderType.BUY || 
                  order.type === OrderType.BUY_LIMIT || 
                  order.type === OrderType.BUY_STOP;
    
    const priceDiff = isBuy ? 
      executionPrice - order.priceOpen : 
      order.priceOpen - executionPrice;
    
    return priceDiff * volume * contractSize;
  }

  /**
   * Create error result
   */
  private createErrorResult(retcode: number, comment: string): OrderResult {
    return {
      retcode,
      deal: 0,
      order: 0,
      volume: 0,
      price: 0,
      bid: 0,
      ask: 0,
      comment,
      requestId: 0,
      retcodeExternal: 0
    };
  }

  /**
   * Create success result
   */
  private createSuccessResult(): OrderResult {
    return {
      retcode: 10009,
      deal: 0,
      order: 0,
      volume: 0,
      price: 0,
      bid: 0,
      ask: 0,
      comment: 'Request validated',
      requestId: 0,
      retcodeExternal: 0
    };
  }

  /**
   * Configure order manager
   */
  configure(newConfig: Partial<OrderManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Get order statistics
   */
  getStatistics(): {
    totalOrders: number;
    activeOrders: number;
    pendingOrders: number;
    filledOrders: number;
    canceledOrders: number;
    expiredOrders: number;
    partialFills: number;
    avgFillTime: number;
  } {
    const allOrders = [...this.orders.values(), ...this.pendingOrders.values(), ...this.orderHistory];
    
    return {
      totalOrders: allOrders.length,
      activeOrders: this.orders.size,
      pendingOrders: this.pendingOrders.size,
      filledOrders: allOrders.filter(o => o.state === OrderState.FILLED).length,
      canceledOrders: allOrders.filter(o => o.state === OrderState.CANCELED).length,
      expiredOrders: allOrders.filter(o => o.state === OrderState.EXPIRED).length,
      partialFills: allOrders.filter(o => o.state === OrderState.PARTIAL).length,
      avgFillTime: this.calculateAverageFillTime(allOrders)
    };
  }

  /**
   * Calculate average fill time
   */
  private calculateAverageFillTime(orders: Order[]): number {
    const filledOrders = orders.filter(o => o.timeFill);
    if (filledOrders.length === 0) return 0;
    
    const totalTime = filledOrders.reduce((sum, order) => {
      if (order.timeFill && order.timeSetup) {
        return sum + (order.timeFill.getTime() - order.timeSetup.getTime());
      }
      return sum;
    }, 0);
    
    return totalTime / filledOrders.length;
  }

  /**
   * Reset order manager
   */
  reset(): void {
    this.orders.clear();
    this.pendingOrders.clear();
    this.orderHistory = [];
    this.nextTicket = 1000000;
    this.nextDeal = 2000000;
  }

  /**
   * Cleanup and stop timers
   */
  dispose(): void {
    if (this.expirationTimer) {
      clearInterval(this.expirationTimer);
      this.expirationTimer = null;
    }
  }
}