import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { performance } from 'perf_hooks';

/**
 * Database Monitoring Service for tracking database performance and health
 * Monitors query performance, connection health, and database-specific metrics
 */
export class DatabaseMonitoringService extends EventEmitter {
  private prisma: PrismaClient;
  private queryMetrics: Map<string, QueryMetrics> = new Map();
  private connectionMetrics: ConnectionMetrics[] = [];
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private slowQueryThreshold = 1000; // 1 second
  private readonly METRICS_RETENTION_LIMIT = 500;

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
    this.setupQueryInterception();
  }

  /**
   * Start database monitoring
   */
  async startMonitoring(config: DatabaseMonitoringConfig = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('Database monitoring already active');
      return;
    }

    console.log('🗄️ Starting database monitoring...');
    this.isMonitoring = true;

    // Apply configuration
    this.slowQueryThreshold = config.slowQueryThreshold || this.slowQueryThreshold;

    // Start periodic database health checks
    this.monitoringInterval = setInterval(async () => {
      await this.performDatabaseHealthCheck();
    }, config.intervalMs || 60000); // Default 1 minute

    console.log('✅ Database monitoring started');
    this.emit('monitoringStarted', { config });
  }

  /**
   * Stop database monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ Stopping database monitoring...');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('✅ Database monitoring stopped');
    this.emit('monitoringStopped');
  }

  /**
   * Setup query interception for performance monitoring
   */
  private setupQueryInterception(): void {
    // This is a simplified approach - in production you'd use Prisma middleware
    // or database-specific query logging
    
    const originalQuery = this.prisma.$queryRaw;
    this.prisma.$queryRaw = new Proxy(originalQuery, {
      apply: async (target, thisArg, args) => {
        const startTime = performance.now();
        const queryId = crypto.randomUUID();
        
        try {
          const result = await target.apply(thisArg, args);
          const duration = performance.now() - startTime;
          
          await this.recordQueryExecution({
            queryId,
            query: args[0]?.toString() || 'Unknown query',
            duration,
            success: true,
            timestamp: new Date(),
          });
          
          return result;
        } catch (error) {
          const duration = performance.now() - startTime;
          
          await this.recordQueryExecution({
            queryId,
            query: args[0]?.toString() || 'Unknown query',
            duration,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date(),
          });
          
          throw error;
        }
      },
    });
  }

  /**
   * Record query execution metrics
   */
  private async recordQueryExecution(execution: QueryExecution): Promise<void> {
    const queryType = this.extractQueryType(execution.query);
    
    // Update or create query metrics
    let metrics = this.queryMetrics.get(queryType);
    if (!metrics) {
      metrics = {
        queryType,
        totalExecutions: 0,
        successfulExecutions: 0,
        totalDuration: 0,
        averageDuration: 0,
        minDuration: Infinity,
        maxDuration: 0,
        slowQueries: 0,
        errorCount: 0,
        lastExecution: new Date(),
        recentExecutions: [],
      };
      this.queryMetrics.set(queryType, metrics);
    }

    // Update metrics
    metrics.totalExecutions++;
    metrics.totalDuration += execution.duration;
    metrics.averageDuration = metrics.totalDuration / metrics.totalExecutions;
    metrics.minDuration = Math.min(metrics.minDuration, execution.duration);
    metrics.maxDuration = Math.max(metrics.maxDuration, execution.duration);
    metrics.lastExecution = execution.timestamp;

    if (execution.success) {
      metrics.successfulExecutions++;
    } else {
      metrics.errorCount++;
    }

    if (execution.duration > this.slowQueryThreshold) {
      metrics.slowQueries++;
      
      // Emit slow query alert
      this.emit('slowQuery', {
        queryId: execution.queryId,
        queryType,
        duration: execution.duration,
        threshold: this.slowQueryThreshold,
        query: execution.query,
      });
    }

    // Add to recent executions
    metrics.recentExecutions.push(execution);
    if (metrics.recentExecutions.length > 50) { // Keep last 50 executions per query type
      metrics.recentExecutions = metrics.recentExecutions.slice(-50);
    }

    // Log performance issues
    if (execution.duration > this.slowQueryThreshold * 2) { // Very slow queries
      console.warn(`🐌 Very slow query detected: ${queryType} took ${execution.duration.toFixed(2)}ms`);
    }

    if (!execution.success) {
      console.error(`❌ Query failed: ${queryType} - ${execution.error}`);
    }
  }

  /**
   * Extract query type from SQL query
   */
  private extractQueryType(query: string): string {
    const cleanQuery = query.trim().toLowerCase();
    
    if (cleanQuery.startsWith('select')) return 'SELECT';
    if (cleanQuery.startsWith('insert')) return 'INSERT';
    if (cleanQuery.startsWith('update')) return 'UPDATE';
    if (cleanQuery.startsWith('delete')) return 'DELETE';
    if (cleanQuery.startsWith('create')) return 'CREATE';
    if (cleanQuery.startsWith('alter')) return 'ALTER';
    if (cleanQuery.startsWith('drop')) return 'DROP';
    if (cleanQuery.startsWith('begin') || cleanQuery.startsWith('commit') || cleanQuery.startsWith('rollback')) {
      return 'TRANSACTION';
    }
    
    return 'OTHER';
  }

  /**
   * Perform comprehensive database health check
   */
  private async performDatabaseHealthCheck(): Promise<void> {
    const startTime = performance.now();
    
    try {
      const healthCheck: DatabaseHealthCheck = {
        timestamp: new Date(),
        connectionTest: await this.testConnection(),
        queryPerformance: await this.testQueryPerformance(),
        connectionStats: await this.getConnectionStats(),
        databaseStats: await this.getDatabaseStats(),
        tableStats: await this.getTableStats(),
        indexUsage: await this.getIndexUsage(),
        lockInfo: await this.getLockInfo(),
        overallHealth: 'unknown',
      };

      // Determine overall health
      healthCheck.overallHealth = this.determineOverallHealth(healthCheck);

      // Store connection metrics
      this.connectionMetrics.push({
        timestamp: healthCheck.timestamp,
        isHealthy: healthCheck.overallHealth !== 'critical',
        connectionTime: healthCheck.connectionTest.duration,
        activeConnections: healthCheck.connectionStats.active,
        idleConnections: healthCheck.connectionStats.idle,
        totalConnections: healthCheck.connectionStats.total,
        databaseSize: healthCheck.databaseStats.size,
        checkDuration: performance.now() - startTime,
      });

      // Keep only recent connection metrics
      if (this.connectionMetrics.length > this.METRICS_RETENTION_LIMIT) {
        this.connectionMetrics = this.connectionMetrics.slice(-this.METRICS_RETENTION_LIMIT);
      }

      // Emit health check event
      this.emit('healthCheck', healthCheck);

      // Generate alerts if needed
      await this.checkHealthAlerts(healthCheck);

    } catch (error) {
      console.error('Database health check failed:', error);
      
      // Record failed health check
      this.connectionMetrics.push({
        timestamp: new Date(),
        isHealthy: false,
        connectionTime: -1,
        activeConnections: 0,
        idleConnections: 0,
        totalConnections: 0,
        databaseSize: 0,
        checkDuration: performance.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      });

      this.emit('error', error);
    }
  }

  /**
   * Test database connection
   */
  private async testConnection(): Promise<ConnectionTestResult> {
    const startTime = performance.now();
    
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      const duration = performance.now() - startTime;
      
      return {
        success: true,
        duration,
        timestamp: new Date(),
      };
    } catch (error) {
      const duration = performance.now() - startTime;
      
      return {
        success: false,
        duration,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Connection failed',
      };
    }
  }

  /**
   * Test query performance with a standardized query
   */
  private async testQueryPerformance(): Promise<QueryPerformanceTest> {
    const startTime = performance.now();
    
    try {
      // Execute a simple performance test query
      await this.prisma.$queryRaw`
        SELECT 
          schemaname,
          tablename,
          attname,
          n_distinct,
          correlation
        FROM pg_stats 
        LIMIT 10
      `;
      
      const duration = performance.now() - startTime;
      
      return {
        testType: 'standard_select',
        duration,
        success: true,
        timestamp: new Date(),
      };
    } catch (error) {
      const duration = performance.now() - startTime;
      
      return {
        testType: 'standard_select',
        duration,
        success: false,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Query test failed',
      };
    }
  }

  /**
   * Get connection statistics
   */
  private async getConnectionStats(): Promise<ConnectionStats> {
    try {
      const result = await this.prisma.$queryRaw<[{
        total: number;
        active: number;
        idle: number;
        waiting: number;
      }]>`
        SELECT 
          count(*) as total,
          count(*) FILTER (WHERE state = 'active') as active,
          count(*) FILTER (WHERE state = 'idle') as idle,
          count(*) FILTER (WHERE wait_event_type IS NOT NULL) as waiting
        FROM pg_stat_activity
        WHERE datname = current_database()
      `;

      const stats = result[0];
      
      return {
        total: Number(stats.total) || 0,
        active: Number(stats.active) || 0,
        idle: Number(stats.idle) || 0,
        waiting: Number(stats.waiting) || 0,
        maxConnections: await this.getMaxConnections(),
        timestamp: new Date(),
      };
    } catch (error) {
      console.warn('Failed to get connection stats:', error);
      return {
        total: 0,
        active: 0,
        idle: 0,
        waiting: 0,
        maxConnections: 0,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get maximum connections setting
   */
  private async getMaxConnections(): Promise<number> {
    try {
      const result = await this.prisma.$queryRaw<[{setting: string}]>`
        SELECT setting FROM pg_settings WHERE name = 'max_connections'
      `;
      
      return parseInt(result[0]?.setting || '0', 10);
    } catch (error) {
      return 0;
    }
  }

  /**
   * Get database statistics
   */
  private async getDatabaseStats(): Promise<DatabaseStats> {
    try {
      const sizeResult = await this.prisma.$queryRaw<[{size_bytes: bigint}]>`
        SELECT pg_database_size(current_database()) as size_bytes
      `;

      const activityResult = await this.prisma.$queryRaw<[{
        commits: bigint;
        rollbacks: bigint;
        blocks_read: bigint;
        blocks_hit: bigint;
      }]>`
        SELECT 
          xact_commit as commits,
          xact_rollback as rollbacks,
          blks_read as blocks_read,
          blks_hit as blocks_hit
        FROM pg_stat_database 
        WHERE datname = current_database()
      `;

      const size = Number(sizeResult[0]?.size_bytes || 0);
      const activity = activityResult[0];
      
      const cacheHitRatio = Number(activity?.blocks_hit || 0) + Number(activity?.blocks_read || 0) > 0
        ? (Number(activity.blocks_hit) / (Number(activity.blocks_hit) + Number(activity.blocks_read))) * 100
        : 0;

      return {
        size,
        commits: Number(activity?.commits || 0),
        rollbacks: Number(activity?.rollbacks || 0),
        cacheHitRatio,
        timestamp: new Date(),
      };
    } catch (error) {
      console.warn('Failed to get database stats:', error);
      return {
        size: 0,
        commits: 0,
        rollbacks: 0,
        cacheHitRatio: 0,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get table statistics
   */
  private async getTableStats(): Promise<TableStats[]> {
    try {
      const result = await this.prisma.$queryRaw<Array<{
        table_name: string;
        row_count: bigint;
        table_size: bigint;
        index_size: bigint;
        seq_scan: bigint;
        seq_tup_read: bigint;
        idx_scan: bigint;
        idx_tup_fetch: bigint;
      }>>`
        SELECT 
          schemaname || '.' || relname as table_name,
          n_tup_ins + n_tup_upd + n_tup_del as row_count,
          pg_relation_size(schemaname||'.'||relname) as table_size,
          pg_indexes_size(schemaname||'.'||relname) as index_size,
          seq_scan,
          seq_tup_read,
          idx_scan,
          idx_tup_fetch
        FROM pg_stat_user_tables 
        ORDER BY pg_relation_size(schemaname||'.'||relname) DESC 
        LIMIT 10
      `;

      return result.map(row => ({
        tableName: row.table_name,
        rowCount: Number(row.row_count),
        tableSize: Number(row.table_size),
        indexSize: Number(row.index_size),
        sequentialScans: Number(row.seq_scan),
        indexScans: Number(row.idx_scan),
        indexEfficiency: Number(row.idx_scan) > 0 
          ? (Number(row.idx_scan) / (Number(row.idx_scan) + Number(row.seq_scan))) * 100 
          : 0,
      }));
    } catch (error) {
      console.warn('Failed to get table stats:', error);
      return [];
    }
  }

  /**
   * Get index usage statistics
   */
  private async getIndexUsage(): Promise<IndexUsageStats[]> {
    try {
      const result = await this.prisma.$queryRaw<Array<{
        index_name: string;
        table_name: string;
        index_scans: bigint;
        tuples_read: bigint;
        tuples_fetched: bigint;
        index_size: bigint;
      }>>`
        SELECT 
          indexrelname as index_name,
          relname as table_name,
          idx_scan as index_scans,
          idx_tup_read as tuples_read,
          idx_tup_fetch as tuples_fetched,
          pg_relation_size(indexrelid) as index_size
        FROM pg_stat_user_indexes 
        WHERE idx_scan > 0
        ORDER BY idx_scan DESC 
        LIMIT 20
      `;

      return result.map(row => ({
        indexName: row.index_name,
        tableName: row.table_name,
        scans: Number(row.index_scans),
        tuplesRead: Number(row.tuples_read),
        tuplesFetched: Number(row.tuples_fetched),
        size: Number(row.index_size),
        efficiency: Number(row.tuples_read) > 0 
          ? (Number(row.tuples_fetched) / Number(row.tuples_read)) * 100 
          : 0,
      }));
    } catch (error) {
      console.warn('Failed to get index usage stats:', error);
      return [];
    }
  }

  /**
   * Get lock information
   */
  private async getLockInfo(): Promise<LockInfo> {
    try {
      const result = await this.prisma.$queryRaw<[{
        total_locks: bigint;
        blocked_queries: bigint;
        deadlocks: bigint;
      }]>`
        SELECT 
          count(*) as total_locks,
          count(*) FILTER (WHERE NOT granted) as blocked_queries,
          0 as deadlocks
        FROM pg_locks 
        WHERE database = (SELECT oid FROM pg_database WHERE datname = current_database())
      `;

      const locks = result[0];

      return {
        totalLocks: Number(locks.total_locks),
        blockedQueries: Number(locks.blocked_queries),
        deadlocks: Number(locks.deadlocks),
        hasLockContentions: Number(locks.blocked_queries) > 0,
        timestamp: new Date(),
      };
    } catch (error) {
      console.warn('Failed to get lock info:', error);
      return {
        totalLocks: 0,
        blockedQueries: 0,
        deadlocks: 0,
        hasLockContentions: false,
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Determine overall database health
   */
  private determineOverallHealth(healthCheck: DatabaseHealthCheck): 'healthy' | 'warning' | 'critical' {
    // Critical conditions
    if (!healthCheck.connectionTest.success) {
      return 'critical';
    }

    if (healthCheck.connectionTest.duration > 5000) { // 5 seconds
      return 'critical';
    }

    if (healthCheck.lockInfo.blockedQueries > 10) {
      return 'critical';
    }

    // Warning conditions
    if (healthCheck.connectionTest.duration > 1000) { // 1 second
      return 'warning';
    }

    if (healthCheck.connectionStats.active / healthCheck.connectionStats.total > 0.8) {
      return 'warning';
    }

    if (healthCheck.databaseStats.cacheHitRatio < 90) {
      return 'warning';
    }

    if (healthCheck.lockInfo.blockedQueries > 0) {
      return 'warning';
    }

    return 'healthy';
  }

  /**
   * Check for health-related alerts
   */
  private async checkHealthAlerts(healthCheck: DatabaseHealthCheck): Promise<void> {
    const alerts: DatabaseAlert[] = [];

    // Connection alerts
    if (!healthCheck.connectionTest.success) {
      alerts.push({
        type: 'connection_failure',
        severity: 'critical',
        message: 'Database connection failed',
        timestamp: new Date(),
        metadata: { error: healthCheck.connectionTest.error },
      });
    } else if (healthCheck.connectionTest.duration > 3000) {
      alerts.push({
        type: 'slow_connection',
        severity: 'warning',
        message: `Slow database connection: ${healthCheck.connectionTest.duration.toFixed(0)}ms`,
        timestamp: new Date(),
        metadata: { duration: healthCheck.connectionTest.duration },
      });
    }

    // Connection pool alerts
    const connectionRatio = healthCheck.connectionStats.active / healthCheck.connectionStats.maxConnections;
    if (connectionRatio > 0.9) {
      alerts.push({
        type: 'connection_pool_exhaustion',
        severity: 'critical',
        message: `Connection pool near exhaustion: ${healthCheck.connectionStats.active}/${healthCheck.connectionStats.maxConnections}`,
        timestamp: new Date(),
        metadata: { 
          active: healthCheck.connectionStats.active, 
          max: healthCheck.connectionStats.maxConnections 
        },
      });
    }

    // Lock alerts
    if (healthCheck.lockInfo.blockedQueries > 5) {
      alerts.push({
        type: 'lock_contention',
        severity: 'warning',
        message: `High lock contention: ${healthCheck.lockInfo.blockedQueries} blocked queries`,
        timestamp: new Date(),
        metadata: { blockedQueries: healthCheck.lockInfo.blockedQueries },
      });
    }

    // Cache hit ratio alerts
    if (healthCheck.databaseStats.cacheHitRatio < 85) {
      alerts.push({
        type: 'low_cache_hit_ratio',
        severity: 'warning',
        message: `Low cache hit ratio: ${healthCheck.databaseStats.cacheHitRatio.toFixed(1)}%`,
        timestamp: new Date(),
        metadata: { cacheHitRatio: healthCheck.databaseStats.cacheHitRatio },
      });
    }

    // Emit alerts
    for (const alert of alerts) {
      this.emit('alert', alert);
    }
  }

  /**
   * Get current database performance summary
   */
  getDatabaseSummary(): DatabaseSummary {
    const queryTypes = Array.from(this.queryMetrics.values());
    const recentConnections = this.connectionMetrics.slice(-10);
    
    return {
      queryPerformance: {
        totalQueries: queryTypes.reduce((sum, q) => sum + q.totalExecutions, 0),
        slowQueries: queryTypes.reduce((sum, q) => sum + q.slowQueries, 0),
        averageDuration: queryTypes.length > 0 
          ? queryTypes.reduce((sum, q) => sum + q.averageDuration, 0) / queryTypes.length 
          : 0,
        errorRate: this.calculateQueryErrorRate(queryTypes),
        queryTypeBreakdown: queryTypes.map(q => ({
          type: q.queryType,
          count: q.totalExecutions,
          avgDuration: q.averageDuration,
          errorCount: q.errorCount,
        })),
      },
      connectionHealth: {
        isHealthy: recentConnections.length > 0 ? recentConnections[recentConnections.length - 1].isHealthy : false,
        averageConnectionTime: recentConnections.length > 0 
          ? recentConnections.reduce((sum, c) => sum + c.connectionTime, 0) / recentConnections.length 
          : 0,
        currentConnections: recentConnections.length > 0 
          ? recentConnections[recentConnections.length - 1].activeConnections 
          : 0,
        connectionTrend: this.calculateConnectionTrend(recentConnections),
      },
      overallHealth: this.calculateOverallHealth(),
      lastCheck: recentConnections.length > 0 
        ? recentConnections[recentConnections.length - 1].timestamp 
        : new Date(),
      isMonitoring: this.isMonitoring,
    };
  }

  /**
   * Calculate query error rate
   */
  private calculateQueryErrorRate(queryTypes: QueryMetrics[]): number {
    const totalExecutions = queryTypes.reduce((sum, q) => sum + q.totalExecutions, 0);
    const totalErrors = queryTypes.reduce((sum, q) => sum + q.errorCount, 0);
    
    return totalExecutions > 0 ? (totalErrors / totalExecutions) * 100 : 0;
  }

  /**
   * Calculate connection trend
   */
  private calculateConnectionTrend(connections: ConnectionMetrics[]): 'improving' | 'stable' | 'degrading' {
    if (connections.length < 5) return 'stable';
    
    const recent = connections.slice(-3);
    const earlier = connections.slice(-6, -3);
    
    const recentAvg = recent.reduce((sum, c) => sum + c.connectionTime, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, c) => sum + c.connectionTime, 0) / earlier.length;
    
    if (recentAvg < earlierAvg * 0.9) return 'improving';
    if (recentAvg > earlierAvg * 1.1) return 'degrading';
    return 'stable';
  }

  /**
   * Calculate overall database health
   */
  private calculateOverallHealth(): 'healthy' | 'warning' | 'critical' {
    const recent = this.connectionMetrics.slice(-3);
    if (recent.length === 0) return 'warning';
    
    const unhealthyCount = recent.filter(c => !c.isHealthy).length;
    
    if (unhealthyCount >= 2) return 'critical';
    if (unhealthyCount >= 1) return 'warning';
    return 'healthy';
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus(): DatabaseMonitoringStatus {
    return {
      isActive: this.isMonitoring,
      queryTypesTracked: this.queryMetrics.size,
      connectionMetricsCount: this.connectionMetrics.length,
      lastHealthCheck: this.connectionMetrics.length > 0 
        ? this.connectionMetrics[this.connectionMetrics.length - 1].timestamp 
        : null,
    };
  }

  /**
   * Clear metrics (useful for testing)
   */
  clearMetrics(): void {
    this.queryMetrics.clear();
    this.connectionMetrics = [];
    console.log('🧹 Database metrics cleared');
  }
}

// Type definitions
interface DatabaseMonitoringConfig {
  intervalMs?: number;
  slowQueryThreshold?: number;
}

interface QueryExecution {
  queryId: string;
  query: string;
  duration: number;
  success: boolean;
  error?: string;
  timestamp: Date;
}

interface QueryMetrics {
  queryType: string;
  totalExecutions: number;
  successfulExecutions: number;
  totalDuration: number;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  slowQueries: number;
  errorCount: number;
  lastExecution: Date;
  recentExecutions: QueryExecution[];
}

interface ConnectionMetrics {
  timestamp: Date;
  isHealthy: boolean;
  connectionTime: number;
  activeConnections: number;
  idleConnections: number;
  totalConnections: number;
  databaseSize: number;
  checkDuration: number;
  error?: string;
}

interface DatabaseHealthCheck {
  timestamp: Date;
  connectionTest: ConnectionTestResult;
  queryPerformance: QueryPerformanceTest;
  connectionStats: ConnectionStats;
  databaseStats: DatabaseStats;
  tableStats: TableStats[];
  indexUsage: IndexUsageStats[];
  lockInfo: LockInfo;
  overallHealth: 'healthy' | 'warning' | 'critical' | 'unknown';
}

interface ConnectionTestResult {
  success: boolean;
  duration: number;
  timestamp: Date;
  error?: string;
}

interface QueryPerformanceTest {
  testType: string;
  duration: number;
  success: boolean;
  timestamp: Date;
  error?: string;
}

interface ConnectionStats {
  total: number;
  active: number;
  idle: number;
  waiting: number;
  maxConnections: number;
  timestamp: Date;
  error?: string;
}

interface DatabaseStats {
  size: number;
  commits: number;
  rollbacks: number;
  cacheHitRatio: number;
  timestamp: Date;
  error?: string;
}

interface TableStats {
  tableName: string;
  rowCount: number;
  tableSize: number;
  indexSize: number;
  sequentialScans: number;
  indexScans: number;
  indexEfficiency: number;
}

interface IndexUsageStats {
  indexName: string;
  tableName: string;
  scans: number;
  tuplesRead: number;
  tuplesFetched: number;
  size: number;
  efficiency: number;
}

interface LockInfo {
  totalLocks: number;
  blockedQueries: number;
  deadlocks: number;
  hasLockContentions: boolean;
  timestamp: Date;
  error?: string;
}

interface DatabaseAlert {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

interface DatabaseSummary {
  queryPerformance: {
    totalQueries: number;
    slowQueries: number;
    averageDuration: number;
    errorRate: number;
    queryTypeBreakdown: Array<{
      type: string;
      count: number;
      avgDuration: number;
      errorCount: number;
    }>;
  };
  connectionHealth: {
    isHealthy: boolean;
    averageConnectionTime: number;
    currentConnections: number;
    connectionTrend: 'improving' | 'stable' | 'degrading';
  };
  overallHealth: 'healthy' | 'warning' | 'critical';
  lastCheck: Date;
  isMonitoring: boolean;
}

interface DatabaseMonitoringStatus {
  isActive: boolean;
  queryTypesTracked: number;
  connectionMetricsCount: number;
  lastHealthCheck: Date | null;
}

export type {
  DatabaseMonitoringConfig,
  QueryExecution,
  QueryMetrics,
  ConnectionMetrics,
  DatabaseHealthCheck,
  DatabaseAlert,
  DatabaseSummary,
  DatabaseMonitoringStatus,
};