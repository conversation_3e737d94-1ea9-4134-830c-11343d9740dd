/**
 * Unit tests for WebSocket Security Middleware
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as jwt from 'jsonwebtoken';
import {
  WebSocketSecurityMiddleware,
  SecurityConfig,
  SecurityContext,
} from './SecurityMiddleware';

// Mock external dependencies
vi.mock('jsonwebtoken', () => ({
  verify: vi.fn(),
  TokenExpiredError: class extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'TokenExpiredError';
    }
  },
  JsonWebTokenError: class extends Error {
    constructor(message: string) {
      super(message);
      this.name = 'JsonWebTokenError';
    }
  },
}));

vi.mock('../../lib/audit', () => ({
  auditLogger: {
    logAuditEvent: vi.fn().mockResolvedValue(undefined),
    logSecurityEvent: vi.fn().mockResolvedValue(undefined),
  },
  AUDIT_ACTIONS: {
    UNAUTHORIZED_ACCESS: 'unauthorized_access',
    RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
  },
}));

describe('WebSocketSecurityMiddleware', () => {
  let middleware: WebSocketSecurityMiddleware;
  let config: SecurityConfig;
  let mockContext: SecurityContext;

  beforeEach(() => {
    config = {
      jwtSecret: 'test-secret-key',
      rateLimiting: {
        tokensPerInterval: 10,
        interval: 1000, // 1 second
        burstCapacity: 15,
      },
      connectionLimits: {
        maxConnectionsPerUser: 5,
        maxConnectionsPerIP: 10,
        maxGlobalConnections: 1000,
      },
      validation: {
        enableStrictValidation: true,
        maxMessageSize: 10240, // 10KB
        allowedOrigins: ['https://app.golddaddy.com'],
      },
      monitoring: {
        enableAuditLogging: true,
        enableSecurityAlerts: true,
        suspiciousActivityThreshold: 20,
      },
    };

    middleware = new WebSocketSecurityMiddleware(config);

    mockContext = {
      userId: 'test-user-123',
      permissions: ['market_data_access'],
      region: 'US',
      userTier: 'basic',
      ipAddress: '***********00',
      userAgent: 'Mozilla/5.0 Test Browser',
      connectionTime: new Date(),
      lastActivity: new Date(),
    };
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Token Verification', () => {
    it('should verify valid JWT token', () => {
      const mockDecoded = {
        sub: 'test-user-123',
        permissions: ['market_data_access'],
        region: 'US',
        tier: 'basic',
      };

      (jwt.verify as any).mockReturnValue(mockDecoded);

      const result = middleware.verifyToken('valid.jwt.token');

      expect(result.valid).toBe(true);
      if (result.valid) {
        expect(result.context.userId).toBe('test-user-123');
        expect(result.context.permissions).toEqual(['market_data_access']);
        expect(result.context.region).toBe('US');
        expect(result.context.userTier).toBe('basic');
      }
    });

    it('should reject expired JWT token', () => {
      (jwt.verify as any).mockImplementation(() => {
        throw new jwt.TokenExpiredError('Token expired');
      });

      const result = middleware.verifyToken('expired.jwt.token');

      expect(result.valid).toBe(false);
      if (!result.valid) {
        expect(result.error).toBe('Token expired');
      }
    });

    it('should reject malformed JWT token', () => {
      (jwt.verify as any).mockImplementation(() => {
        throw new jwt.JsonWebTokenError('Invalid token');
      });

      const result = middleware.verifyToken('invalid.token');

      expect(result.valid).toBe(false);
      if (!result.valid) {
        expect(result.error).toBe('Invalid token format');
      }
    });

    it('should reject token without subject', () => {
      (jwt.verify as any).mockReturnValue({ permissions: [] });

      const result = middleware.verifyToken('token.without.subject');

      expect(result.valid).toBe(false);
      if (!result.valid) {
        expect(result.error).toBe('Invalid token: missing subject');
      }
    });
  });

  describe('Connection Registration', () => {
    it('should successfully register valid connection', async () => {
      const connectionId = 'conn-123';
      
      const result = await middleware.registerConnection(connectionId, mockContext);

      expect(result.success).toBe(true);
    });

    it('should reject blocked user', async () => {
      const connectionId = 'conn-blocked-user';
      
      // Block the user first
      middleware.blockUser(mockContext.userId, 'security violation');
      
      const result = await middleware.registerConnection(connectionId, mockContext);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBe('User account is blocked');
      }
    });

    it('should reject blocked IP address', async () => {
      const connectionId = 'conn-blocked-ip';
      
      // Block the IP first
      middleware.blockIP(mockContext.ipAddress, 'suspicious activity');
      
      const result = await middleware.registerConnection(connectionId, mockContext);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBe('IP address is blocked');
      }
    });

    it('should enforce per-user connection limits', async () => {
      // Register maximum allowed connections
      for (let i = 0; i < config.connectionLimits.maxConnectionsPerUser; i++) {
        const result = await middleware.registerConnection(`conn-${i}`, mockContext);
        expect(result.success).toBe(true);
      }

      // Try to register one more connection
      const result = await middleware.registerConnection('conn-overflow', mockContext);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBe('User connection limit exceeded');
      }
    });

    it('should enforce per-IP connection limits', async () => {
      // Register maximum allowed connections from same IP with different users
      for (let i = 0; i < config.connectionLimits.maxConnectionsPerIP; i++) {
        const context = {
          ...mockContext,
          userId: `user-${i}`,
        };
        const result = await middleware.registerConnection(`conn-${i}`, context);
        expect(result.success).toBe(true);
      }

      // Try to register one more connection from same IP
      const overflowContext = {
        ...mockContext,
        userId: 'overflow-user',
      };
      const result = await middleware.registerConnection('conn-overflow', overflowContext);

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error).toBe('IP connection limit exceeded');
      }
    });
  });

  describe('Message Validation', () => {
    const connectionId = 'conn-validation-test';

    beforeEach(async () => {
      await middleware.registerConnection(connectionId, mockContext);
    });

    it('should validate correct subscription message', async () => {
      const validMessage = {
        type: 'subscribe',
        action: 'subscribe',
        instruments: ['EURUSD', 'GBPUSD'],
        features: {
          marketAnalysis: true,
        },
        timeframes: ['1m'],
        updateInterval: 1000,
      };

      const result = await middleware.validateMessage(connectionId, validMessage);

      expect(result.valid).toBe(true);
      if (result.valid) {
        expect(result.message.type).toBe('subscribe');
        expect(result.context.userId).toBe(mockContext.userId);
      }
    });

    it('should reject malformed message', async () => {
      const invalidMessage = {
        type: 'invalid-type',
        invalidField: 'test',
      };

      const result = await middleware.validateMessage(connectionId, invalidMessage);

      expect(result.valid).toBe(false);
      if (!result.valid) {
        expect(result.error).toBe('Message validation failed');
      }
    });

    it('should reject oversized message', async () => {
      const oversizedMessage = {
        type: 'subscribe',
        action: 'subscribe',
        instruments: ['EURUSD'],
        data: 'x'.repeat(config.validation.maxMessageSize + 1),
      };

      const result = await middleware.validateMessage(connectionId, oversizedMessage);

      expect(result.valid).toBe(false);
      if (!result.valid) {
        expect(result.error).toBe('Message too large');
      }
    });

    it('should enforce rate limiting', async () => {
      const validMessage = {
        type: 'ping',
      };

      // Send messages up to the rate limit
      for (let i = 0; i < config.rateLimiting.burstCapacity; i++) {
        const result = await middleware.validateMessage(connectionId, validMessage);
        expect(result.valid).toBe(true);
      }

      // Next message should be rate limited
      const result = await middleware.validateMessage(connectionId, validMessage);

      expect(result.valid).toBe(false);
      if (!result.valid) {
        expect(result.error).toBe('Rate limit exceeded');
      }
    });

    it('should validate subscription permissions', async () => {
      const contextWithoutPermissions = {
        ...mockContext,
        permissions: [], // No permissions
      };

      const connectionIdNoPerms = 'conn-no-perms';
      await middleware.registerConnection(connectionIdNoPerms, contextWithoutPermissions);

      const subscriptionMessage = {
        type: 'subscribe',
        action: 'subscribe',
        instruments: ['EURUSD'],
      };

      const result = await middleware.validateMessage(connectionIdNoPerms, subscriptionMessage);

      expect(result.valid).toBe(false);
      if (!result.valid) {
        expect(result.error).toBe('Insufficient permissions for market data access');
      }
    });

    it('should validate premium feature access', async () => {
      const premiumMessage = {
        type: 'subscribe',
        action: 'subscribe',
        instruments: ['EURUSD'],
        features: {
          mlPredictions: true, // Premium feature
        },
      };

      const result = await middleware.validateMessage(connectionId, premiumMessage);

      expect(result.valid).toBe(false);
      if (!result.valid) {
        expect(result.error).toContain('Premium feature');
      }
    });
  });

  describe('Security Monitoring', () => {
    it('should track security statistics', async () => {
      const connectionId = 'conn-stats';
      await middleware.registerConnection(connectionId, mockContext);

      const stats = middleware.getSecurityStats();

      expect(stats.totalConnections).toBe(1);
      expect(stats.connectionsByUser[mockContext.userId]).toBe(1);
      expect(stats.connectionsByIP[mockContext.ipAddress]).toBe(1);
    });

    it('should detect suspicious activity', async () => {
      let suspiciousActivityDetected = false;
      
      middleware.on('suspicious_activity_detected', () => {
        suspiciousActivityDetected = true;
      });

      const connectionId = 'conn-suspicious';
      await middleware.registerConnection(connectionId, mockContext);

      // Generate many invalid messages to increase suspicious activity score
      const invalidMessage = { type: 'invalid' };
      
      for (let i = 0; i < 15; i++) {
        await middleware.validateMessage(connectionId, invalidMessage);
      }

      // Manually trigger suspicious activity monitoring
      middleware.checkForSuspiciousActivity();

      expect(suspiciousActivityDetected).toBe(true);
    });

    it('should handle connection cleanup', async () => {
      const connectionId = 'conn-cleanup';
      await middleware.registerConnection(connectionId, mockContext);

      expect(middleware.getSecurityStats().totalConnections).toBe(1);

      await middleware.disconnectConnection(connectionId, 'test_cleanup');

      expect(middleware.getSecurityStats().totalConnections).toBe(0);
    });
  });

  describe('User and IP Blocking', () => {
    it('should block and unblock users', () => {
      let userBlockedEmitted = false;
      let userUnblockedEmitted = false;

      middleware.on('user_blocked', () => {
        userBlockedEmitted = true;
      });

      middleware.on('user_unblocked', () => {
        userUnblockedEmitted = true;
      });

      middleware.blockUser('test-user', 'policy violation');
      expect(userBlockedEmitted).toBe(true);

      const stats = middleware.getSecurityStats();
      expect(stats.blockedUsers).toBe(1);

      middleware.unblockUser('test-user');
      expect(userUnblockedEmitted).toBe(true);

      const statsAfterUnblock = middleware.getSecurityStats();
      expect(statsAfterUnblock.blockedUsers).toBe(0);
    });

    it('should block and unblock IP addresses', () => {
      let ipBlockedEmitted = false;
      let ipUnblockedEmitted = false;

      middleware.on('ip_blocked', () => {
        ipBlockedEmitted = true;
      });

      middleware.on('ip_unblocked', () => {
        ipUnblockedEmitted = true;
      });

      middleware.blockIP('***********', 'suspicious activity');
      expect(ipBlockedEmitted).toBe(true);

      const stats = middleware.getSecurityStats();
      expect(stats.blockedIPs).toBe(1);

      middleware.unblockIP('***********');
      expect(ipUnblockedEmitted).toBe(true);

      const statsAfterUnblock = middleware.getSecurityStats();
      expect(statsAfterUnblock.blockedIPs).toBe(0);
    });
  });

  describe('Event Emission', () => {
    it('should emit connection_registered event', async () => {
      let eventEmitted = false;
      let eventData: any;

      middleware.on('connection_registered', (data) => {
        eventEmitted = true;
        eventData = data;
      });

      const connectionId = 'conn-event-test';
      await middleware.registerConnection(connectionId, mockContext);

      expect(eventEmitted).toBe(true);
      expect(eventData.connectionId).toBe(connectionId);
      expect(eventData.context.userId).toBe(mockContext.userId);
    });

    it('should emit security_alert event', async () => {
      let alertEmitted = false;
      let alertData: any;

      middleware.on('security_alert', (data) => {
        alertEmitted = true;
        alertData = data;
      });

      const connectionId = 'conn-alert-test';
      await middleware.registerConnection(connectionId, mockContext);

      // Generate rate limit violation
      const validMessage = { type: 'ping' };
      
      // Exceed rate limit
      for (let i = 0; i < config.rateLimiting.burstCapacity + 1; i++) {
        await middleware.validateMessage(connectionId, validMessage);
      }

      expect(alertEmitted).toBe(true);
      expect(alertData.type).toBe('security_alert');
      expect(alertData.alertType).toBe('rate_limit_exceeded');
    });
  });

  describe('Edge Cases', () => {
    it('should handle validation for non-existent connection', async () => {
      const result = await middleware.validateMessage('non-existent', { type: 'ping' });

      expect(result.valid).toBe(false);
      if (!result.valid) {
        expect(result.error).toBe('Connection not found');
        expect(result.shouldDisconnect).toBe(true);
      }
    });

    it('should handle empty message validation', async () => {
      const connectionId = 'conn-empty-msg';
      await middleware.registerConnection(connectionId, mockContext);

      const result = await middleware.validateMessage(connectionId, null);

      expect(result.valid).toBe(false);
    });

    it('should handle concurrent connection registrations', async () => {
      const promises = [];
      
      for (let i = 0; i < 5; i++) {
        promises.push(
          middleware.registerConnection(`conn-concurrent-${i}`, {
            ...mockContext,
            userId: `user-${i}`,
          })
        );
      }

      const results = await Promise.all(promises);
      
      results.forEach(result => {
        expect(result.success).toBe(true);
      });

      expect(middleware.getSecurityStats().totalConnections).toBe(5);
    });
  });
});