"""
MT5 Bridge Configuration Management
Handles environment variables, connection settings, and error handling
"""

import os
from typing import Optional, Dict, Any
from dataclasses import dataclass
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

@dataclass
class MT5Config:
    """MT5 connection configuration"""
    login: Optional[int] = None
    password: Optional[str] = None
    server: Optional[str] = None
    path: Optional[str] = None
    timeout: int = 10000
    portable: bool = False

@dataclass
class APIConfig:
    """API service configuration"""
    host: str = "localhost"
    port: int = 8001
    websocket_port: int = 8002
    debug: bool = False
    log_level: str = "INFO"

@dataclass
class DatabaseConfig:
    """Database configuration"""
    url: str = ""
    max_connections: int = 10
    timeout: int = 30

@dataclass
class RedisConfig:
    """Redis cache configuration"""
    url: str = "redis://localhost:6379"
    max_connections: int = 10
    ttl: int = 300  # 5 minutes default TTL

class ConfigManager:
    """Centralized configuration management"""
    
    def __init__(self):
        self.mt5 = self._load_mt5_config()
        self.api = self._load_api_config()
        self.database = self._load_database_config()
        self.redis = self._load_redis_config()
        
    def _load_mt5_config(self) -> MT5Config:
        """Load MT5 configuration from environment"""
        return MT5Config(
            login=self._get_int_env("MT5_LOGIN"),
            password=os.getenv("MT5_PASSWORD"),
            server=os.getenv("MT5_SERVER"),
            path=os.getenv("MT5_PATH"),
            timeout=self._get_int_env("MT5_TIMEOUT", 10000),
            portable=self._get_bool_env("MT5_PORTABLE", False)
        )
    
    def _load_api_config(self) -> APIConfig:
        """Load API configuration from environment"""
        return APIConfig(
            host=os.getenv("API_HOST", "localhost"),
            port=self._get_int_env("API_PORT", 8001),
            websocket_port=self._get_int_env("WEBSOCKET_PORT", 8002),
            debug=self._get_bool_env("DEBUG", False),
            log_level=os.getenv("LOG_LEVEL", "INFO")
        )
    
    def _load_database_config(self) -> DatabaseConfig:
        """Load database configuration from environment"""
        return DatabaseConfig(
            url=os.getenv("DATABASE_URL", ""),
            max_connections=self._get_int_env("DB_MAX_CONNECTIONS", 10),
            timeout=self._get_int_env("DB_TIMEOUT", 30)
        )
    
    def _load_redis_config(self) -> RedisConfig:
        """Load Redis configuration from environment"""
        return RedisConfig(
            url=os.getenv("REDIS_URL", "redis://localhost:6379"),
            max_connections=self._get_int_env("REDIS_MAX_CONNECTIONS", 10),
            ttl=self._get_int_env("REDIS_TTL", 300)
        )
    
    def _get_int_env(self, key: str, default: Optional[int] = None) -> Optional[int]:
        """Get integer environment variable"""
        value = os.getenv(key)
        if value is None:
            return default
        try:
            return int(value)
        except ValueError:
            return default
    
    def _get_bool_env(self, key: str, default: bool = False) -> bool:
        """Get boolean environment variable"""
        value = os.getenv(key, "").lower()
        return value in ("true", "1", "yes", "on")
    
    def validate_config(self) -> Dict[str, Any]:
        """Validate configuration and return validation results"""
        errors = []
        warnings = []
        
        # Validate MT5 configuration
        if not self.mt5.login and not os.getenv("MT5_DEMO_MODE"):
            warnings.append("MT5_LOGIN not set - running in demo mode")
        
        if not self.mt5.server and not os.getenv("MT5_DEMO_MODE"):
            warnings.append("MT5_SERVER not set - using default broker settings")
        
        # Validate database configuration
        if not self.database.url:
            errors.append("DATABASE_URL is required")
        
        # Validate API configuration
        if self.api.port == self.api.websocket_port:
            errors.append("API_PORT and WEBSOCKET_PORT cannot be the same")
        
        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }
    
    def get_connection_string(self) -> Dict[str, Any]:
        """Get MT5 connection parameters"""
        params = {}
        
        if self.mt5.login:
            params["login"] = self.mt5.login
        if self.mt5.password:
            params["password"] = self.mt5.password
        if self.mt5.server:
            params["server"] = self.mt5.server
        if self.mt5.path:
            params["path"] = self.mt5.path
        
        params["timeout"] = self.mt5.timeout
        params["portable"] = self.mt5.portable
        
        return params

# Global configuration instance
config = ConfigManager()

def get_config() -> ConfigManager:
    """Get global configuration instance"""
    return config