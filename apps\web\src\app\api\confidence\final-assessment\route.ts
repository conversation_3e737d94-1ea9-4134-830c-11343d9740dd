import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@golddaddy/types';
import {
  generateFinalAssessmentQuestions,
  calculateFinalAssessmentScore,
  validateFinalAssessmentEligibility,
  FINAL_ASSESSMENT_CONFIG,
  FinalAssessmentQuestion,
  FinalAssessmentResponse,
  FinalAssessmentResult
} from '@/lib/final-assessment';

// Mock user assessment data
const mockUserAssessments: Record<string, any> = {
  'demo-user': {
    knowledgeQuiz: {
      score: 88,
      completedAt: new Date('2025-01-20'),
      attempts: 2,
      weakAreas: ['Risk Management']
    },
    behavioralAssessment: {
      riskTolerance: 85,
      decisionConsistency: 88,
      emotionalStability: 82,
      lastAssessed: new Date('2025-01-22')
    },
    performanceEvaluation: {
      paperTradingWinRate: 68,
      riskManagementScore: 85,
      strategyAdherence: 88,
      consistencyRating: 85
    },
    stressTestResults: {
      scenariosPassed: 8,
      totalScenarios: 10,
      panicResponses: 1,
      recoveryTimes: [30, 45, 25, 40, 35, 50, 42, 38]
    }
  }
};

// Mock paper trading analysis data
const mockPaperTradingAnalysis = {
  totalTrades: 25,
  timeframe: {
    tradingDays: 35
  },
  winRate: 68,
  totalPnL: 342.5
};

// Store for assessment attempts (in production, this would be in database)
const assessmentAttempts: Record<string, number> = {};
const completedAssessments: Record<string, FinalAssessmentResult[]> = {};

/**
 * GET /api/confidence/final-assessment
 * Get assessment questions or check eligibility
 */
export async function GET(
  request: NextRequest
): Promise<NextResponse<ApiResponse<{ questions: FinalAssessmentQuestion[] } | { eligibility: any }>>> {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    const action = searchParams.get('action') || 'questions';

    if (action === 'eligibility') {
      // Check if user is eligible for final assessment
      const userAssessments = mockUserAssessments[userId];
      const eligibility = validateFinalAssessmentEligibility(
        userAssessments,
        mockPaperTradingAnalysis
      );

      const attempts = assessmentAttempts[userId] || 0;
      const canRetake = attempts < FINAL_ASSESSMENT_CONFIG.maxAttempts;
      const hasCompleted = completedAssessments[userId]?.some(a => a.passed) || false;

      return NextResponse.json({
        success: true,
        data: {
          eligibility: {
            ...eligibility,
            canRetake,
            hasCompleted,
            remainingAttempts: Math.max(0, FINAL_ASSESSMENT_CONFIG.maxAttempts - attempts),
            config: FINAL_ASSESSMENT_CONFIG
          }
        },
        message: eligibility.eligible 
          ? 'User is eligible for final assessment'
          : 'User needs to complete prerequisites'
      });
    }

    // Check eligibility before providing questions
    const userAssessments = mockUserAssessments[userId];
    const eligibility = validateFinalAssessmentEligibility(
      userAssessments,
      mockPaperTradingAnalysis
    );

    if (!eligibility.eligible) {
      return NextResponse.json(
        {
          success: false,
          error: 'Not eligible',
          message: `Prerequisites not met: ${eligibility.reasons.join(', ')}`
        },
        { status: 403 }
      );
    }

    // Check attempt limit
    const attempts = assessmentAttempts[userId] || 0;
    if (attempts >= FINAL_ASSESSMENT_CONFIG.maxAttempts) {
      return NextResponse.json(
        {
          success: false,
          error: 'Attempt limit reached',
          message: `Maximum ${FINAL_ASSESSMENT_CONFIG.maxAttempts} attempts allowed`
        },
        { status: 403 }
      );
    }

    // Generate assessment questions
    const questionCount = parseInt(searchParams.get('count') || '15');
    const questions = generateFinalAssessmentQuestions(questionCount);

    return NextResponse.json({
      success: true,
      data: { questions },
      message: `Generated ${questions.length} final assessment questions`
    });

  } catch (error) {
    console.error('Error in final assessment GET:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Assessment generation failed',
        message: 'Failed to generate final assessment questions'
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/confidence/final-assessment
 * Submit assessment responses and get results
 */
export async function POST(
  request: NextRequest
): Promise<NextResponse<ApiResponse<FinalAssessmentResult>>> {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';

    if (!body.responses || !Array.isArray(body.responses)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request',
          message: 'Responses array is required'
        },
        { status: 400 }
      );
    }

    // Validate responses format
    const responses: FinalAssessmentResponse[] = body.responses;
    const invalidResponses = responses.filter(r => 
      !r.questionId || r.selectedAnswer === undefined || !r.timeSpent
    );

    if (invalidResponses.length > 0) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid responses',
          message: 'All responses must have questionId, selectedAnswer, and timeSpent'
        },
        { status: 400 }
      );
    }

    // Check attempt limit
    const attempts = assessmentAttempts[userId] || 0;
    if (attempts >= FINAL_ASSESSMENT_CONFIG.maxAttempts) {
      return NextResponse.json(
        {
          success: false,
          error: 'Attempt limit reached',
          message: `Maximum ${FINAL_ASSESSMENT_CONFIG.maxAttempts} attempts allowed`
        },
        { status: 403 }
      );
    }

    // Calculate assessment results
    const result = calculateFinalAssessmentScore(responses);
    result.userId = userId;

    // Update attempt count
    assessmentAttempts[userId] = attempts + 1;

    // Store completed assessment
    if (!completedAssessments[userId]) {
      completedAssessments[userId] = [];
    }
    completedAssessments[userId].push(result);

    // Log assessment completion
    console.log(`Final assessment completed for user ${userId}:`, {
      score: result.percentageScore,
      passed: result.passed,
      attempts: assessmentAttempts[userId],
      timeSpent: result.timeSpent
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: result.passed 
        ? `Assessment passed with ${result.percentageScore}% score!`
        : `Assessment completed with ${result.percentageScore}% score. Minimum ${result.passThreshold}% required to pass.`
    });

  } catch (error) {
    console.error('Error in final assessment POST:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Assessment submission failed',
        message: 'Failed to process assessment submission'
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/confidence/final-assessment
 * Reset assessment attempts (admin function)
 */
export async function PUT(
  request: NextRequest
): Promise<NextResponse<ApiResponse<{ reset: boolean; remainingAttempts: number }>>> {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';

    if (body.action === 'reset_attempts') {
      // In production, this would require admin authorization
      assessmentAttempts[userId] = 0;
      
      return NextResponse.json({
        success: true,
        data: {
          reset: true,
          remainingAttempts: FINAL_ASSESSMENT_CONFIG.maxAttempts
        },
        message: `Assessment attempts reset for user ${userId}`
      });
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Invalid action',
        message: 'Supported actions: reset_attempts'
      },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error in final assessment PUT:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Assessment reset failed',
        message: 'Failed to reset assessment'
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/confidence/final-assessment
 * Delete assessment history (admin function)
 */
export async function DELETE(
  request: NextRequest
): Promise<NextResponse<ApiResponse<{ deleted: boolean }>>> {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';

    // In production, this would require admin authorization
    delete assessmentAttempts[userId];
    delete completedAssessments[userId];

    return NextResponse.json({
      success: true,
      data: { deleted: true },
      message: `Assessment history deleted for user ${userId}`
    });

  } catch (error) {
    console.error('Error in final assessment DELETE:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Deletion failed',
        message: 'Failed to delete assessment history'
      },
      { status: 500 }
    );
  }
}