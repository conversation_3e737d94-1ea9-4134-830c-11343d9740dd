/**
 * Intelligent Metrics Cache Manager
 * 
 * Provides intelligent caching for trading metrics with automatic invalidation,
 * priority-based eviction, and performance analytics for optimal trading experience.
 */

import { 
  PlainEnglishMetric, 
  PerformanceMetrics, 
  StrategyHealthScore,
  UserExperienceLevel,
  MetricType 
} from '@golddaddy/types';
import { PersistenceAdapter, CacheEntry as PersistentCacheEntry } from './CachePersistenceAdapter';
import { CacheHydrationService } from './CacheHydrationService';

interface CacheEntry<T = any> {
  key: string;
  data: T;
  timestamp: number;
  expiresAt: number;
  accessCount: number;
  lastAccessed: number;
  priority: CachePriority;
  tags: string[];
  size: number; // Estimated memory size in bytes
  computationCost: number; // Relative cost to recompute (1-10)
  dependencies: string[]; // Keys this entry depends on
}

interface CacheStats {
  hits: number;
  misses: number;
  evictions: number;
  totalEntries: number;
  totalSize: number;
  hitRate: number;
  averageAccessTime: number;
}

interface CacheConfig {
  maxSize: number; // Max cache size in bytes
  maxEntries: number; // Max number of entries
  defaultTTL: number; // Default time to live in ms
  cleanupInterval: number; // Cleanup interval in ms
  enablePersistence: boolean;
  enableAnalytics: boolean;
}

type CachePriority = 'critical' | 'high' | 'medium' | 'low';
type InvalidationStrategy = 'immediate' | 'lazy' | 'scheduled';

export class MetricsCacheManager {
  private cache = new Map<string, CacheEntry>();
  private stats: CacheStats = {
    hits: 0,
    misses: 0,
    evictions: 0,
    totalEntries: 0,
    totalSize: 0,
    hitRate: 0,
    averageAccessTime: 0,
  };
  
  private config: CacheConfig;
  private cleanupTimer?: NodeJS.Timeout;
  private persistenceTimer?: NodeJS.Timeout;
  private analytics: Map<string, number[]> = new Map(); // Access times by key
  private persistenceAdapter?: PersistenceAdapter;
  private hydrationService?: CacheHydrationService;

  constructor(config: Partial<CacheConfig> = {}, persistenceAdapter?: PersistenceAdapter) {
    this.config = {
      maxSize: 100 * 1024 * 1024, // 100MB default
      maxEntries: 10000,
      defaultTTL: 5 * 60 * 1000, // 5 minutes
      cleanupInterval: 60 * 1000, // 1 minute
      enablePersistence: true,
      enableAnalytics: true,
      ...config,
    };

    this.persistenceAdapter = persistenceAdapter;
    if (this.persistenceAdapter) {
      this.hydrationService = new CacheHydrationService(this, this.persistenceAdapter);
    }

    this.startCleanupTimer();
    this.loadFromPersistence();
  }

  /**
   * Get cached metric with intelligent priority handling
   */
  public async get<T>(key: string): Promise<T | null> {
    const startTime = performance.now();
    
    const entry = this.cache.get(key);
    if (!entry) {
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Check expiration
    const now = Date.now();
    if (now > entry.expiresAt) {
      this.cache.delete(key);
      this.stats.misses++;
      this.updateHitRate();
      return null;
    }

    // Update access stats
    entry.accessCount++;
    entry.lastAccessed = now;
    this.stats.hits++;
    this.updateHitRate();

    // Track access time for analytics
    const accessTime = performance.now() - startTime;
    this.trackAccessTime(key, accessTime);

    return entry.data as T;
  }

  /**
   * Set cached metric with intelligent eviction
   */
  public async set<T>(
    key: string, 
    data: T, 
    options: {
      ttl?: number;
      priority?: CachePriority;
      tags?: string[];
      computationCost?: number;
      dependencies?: string[];
    } = {}
  ): Promise<void> {
    const now = Date.now();
    const ttl = options.ttl || this.config.defaultTTL;
    const size = this.estimateSize(data);

    const entry: CacheEntry<T> = {
      key,
      data,
      timestamp: now,
      expiresAt: now + ttl,
      accessCount: 1,
      lastAccessed: now,
      priority: options.priority || this.inferPriority(key),
      tags: options.tags || [],
      size,
      computationCost: options.computationCost || this.inferComputationCost(key),
      dependencies: options.dependencies || [],
    };

    // Check if we need to evict before adding
    await this.ensureCapacity(size);

    this.cache.set(key, entry);
    this.updateStats();
  }

  /**
   * Invalidate cache entries by key, tag, or pattern
   */
  public async invalidate(
    target: string | string[] | RegExp,
    strategy: InvalidationStrategy = 'immediate'
  ): Promise<void> {
    const keysToInvalidate = this.findKeysToInvalidate(target);
    
    switch (strategy) {
      case 'immediate':
        keysToInvalidate.forEach(key => this.cache.delete(key));
        break;
        
      case 'lazy':
        // Mark for lazy deletion on next access
        keysToInvalidate.forEach(key => {
          const entry = this.cache.get(key);
          if (entry) {
            entry.expiresAt = Date.now() - 1; // Expire immediately
          }
        });
        break;
        
      case 'scheduled':
        // Schedule invalidation for next cleanup cycle
        setTimeout(() => {
          keysToInvalidate.forEach(key => this.cache.delete(key));
        }, this.config.cleanupInterval);
        break;
    }

    this.updateStats();
  }

  /**
   * Invalidate dependent cache entries
   */
  public async invalidateDependencies(changedKey: string): Promise<void> {
    const dependentKeys: string[] = [];
    
    this.cache.forEach((entry, key) => {
      if (entry.dependencies.includes(changedKey)) {
        dependentKeys.push(key);
      }
    });

    await this.invalidate(dependentKeys);
  }

  /**
   * Cache metrics with intelligent TTL based on volatility
   */
  public async cacheMetric(
    strategyId: string,
    metricType: MetricType,
    metric: PlainEnglishMetric,
    userExperience: UserExperienceLevel
  ): Promise<void> {
    const key = this.buildMetricKey(strategyId, metricType, userExperience);
    const ttl = this.calculateMetricTTL(metricType, metric);
    
    await this.set(key, metric, {
      ttl,
      priority: this.getMetricPriority(metricType),
      tags: ['metric', strategyId, metricType, userExperience],
      computationCost: this.getMetricComputationCost(metricType),
      dependencies: [`strategy:${strategyId}`, `trades:${strategyId}`],
    });

    // Persist to storage if adapter is available
    if (this.persistenceAdapter) {
      const now = new Date();
      const persistentEntry: PersistentCacheEntry = {
        strategyId,
        metricType,
        metric,
        userExperience,
        cachedAt: now,
        expiresAt: new Date(now.getTime() + ttl),
        priority: this.getMetricPriorityNumber(metricType),
        accessCount: 1,
        lastAccessed: now,
        version: '1.0',
      };
      await this.persistenceAdapter.save(key, persistentEntry);
    }
  }

  /**
   * Get cached metric from memory or persistence
   */
  public async getMetric(
    strategyId: string,
    metricType: MetricType,
    userExperience: UserExperienceLevel
  ): Promise<PlainEnglishMetric | null> {
    const key = this.buildMetricKey(strategyId, metricType, userExperience);
    
    // Try memory cache first
    const memoryResult = await this.get<PlainEnglishMetric>(key);
    if (memoryResult) {
      return memoryResult;
    }

    // Try persistence if available
    if (this.persistenceAdapter) {
      const persistentEntry = await this.persistenceAdapter.load(key);
      if (persistentEntry && persistentEntry.expiresAt.getTime() > Date.now()) {
        // Restore to memory cache
        await this.set(key, persistentEntry.metric, {
          ttl: persistentEntry.expiresAt.getTime() - Date.now(),
          priority: this.numberToCachePriority(persistentEntry.priority),
          tags: ['metric', strategyId, metricType, userExperience],
          computationCost: this.getMetricComputationCost(metricType),
          dependencies: [`strategy:${strategyId}`, `trades:${strategyId}`],
        });
        return persistentEntry.metric;
      }
    }

    return null;
  }

  /**
   * Cache performance metrics with dependency tracking
   */
  public async cachePerformanceMetrics(
    strategyId: string,
    metrics: PerformanceMetrics
  ): Promise<void> {
    const key = `performance:${strategyId}`;
    
    await this.set(key, metrics, {
      ttl: 2 * 60 * 1000, // 2 minutes for performance data
      priority: 'high',
      tags: ['performance', strategyId],
      computationCost: 8, // High computation cost
      dependencies: [`trades:${strategyId}`],
    });
  }

  /**
   * Cache strategy health score with moderate TTL
   */
  public async cacheHealthScore(
    strategyId: string,
    healthScore: StrategyHealthScore
  ): Promise<void> {
    const key = `health:${strategyId}`;
    
    await this.set(key, healthScore, {
      ttl: 3 * 60 * 1000, // 3 minutes
      priority: 'high',
      tags: ['health', strategyId],
      computationCost: 6,
      dependencies: [`performance:${strategyId}`, `strategy:${strategyId}`],
    });
  }

  /**
   * Get cache statistics and analytics
   */
  public getStats(): CacheStats & {
    topKeys: Array<{ key: string; accessCount: number; hitRate: number }>;
    memoryUsage: { used: number; available: number; utilization: number };
    avgAccessTimes: Array<{ key: string; avgTime: number }>;
  } {
    const topKeys = Array.from(this.cache.entries())
      .sort(([, a], [, b]) => b.accessCount - a.accessCount)
      .slice(0, 10)
      .map(([key, entry]) => ({
        key,
        accessCount: entry.accessCount,
        hitRate: entry.accessCount / (this.stats.hits + this.stats.misses),
      }));

    const avgAccessTimes = Array.from(this.analytics.entries())
      .map(([key, times]) => ({
        key,
        avgTime: times.reduce((sum, time) => sum + time, 0) / times.length,
      }))
      .sort((a, b) => b.avgTime - a.avgTime)
      .slice(0, 10);

    return {
      ...this.stats,
      topKeys,
      memoryUsage: {
        used: this.stats.totalSize,
        available: this.config.maxSize - this.stats.totalSize,
        utilization: (this.stats.totalSize / this.config.maxSize) * 100,
      },
      avgAccessTimes,
    };
  }

  /**
   * Clear cache with optional filtering
   */
  public async clear(filter?: (key: string, entry: CacheEntry) => boolean): Promise<void> {
    if (filter) {
      const keysToDelete: string[] = [];
      this.cache.forEach((entry, key) => {
        if (filter(key, entry)) {
          keysToDelete.push(key);
        }
      });
      keysToDelete.forEach(key => this.cache.delete(key));
    } else {
      this.cache.clear();
    }
    
    this.updateStats();
  }

  /**
   * Preload critical metrics for strategy
   */
  public async preloadStrategy(_strategyId: string): Promise<void> {
    // This would trigger background loading of commonly accessed metrics
    // Implementation depends on your data sources
    const criticalMetrics: MetricType[] = ['win_rate', 'profit_factor', 'sharpe_ratio', 'max_drawdown'];
    
    // Mark these as high priority for future caching
    criticalMetrics.forEach(_metricType => {
      // Could trigger background computation here for metric type: metricType
      // Key would be: this.buildMetricKey(strategyId, metricType, 'intermediate')
    });
  }

  /**
   * Private helper methods
   */

  private buildMetricKey(strategyId: string, metricType: MetricType, userExperience: UserExperienceLevel): string {
    return `metric:${strategyId}:${metricType}:${userExperience}`;
  }

  private calculateMetricTTL(metricType: MetricType, _metric: PlainEnglishMetric): number {
    // High volatility metrics get shorter TTL
    const baselineMetrics = ['win_rate', 'profit_factor', 'total_return'];
    const volatileMetrics = ['sharpe_ratio', 'max_drawdown', 'volatility'];
    const stableMetrics = ['trade_count', 'calmar_ratio'];

    if (volatileMetrics.includes(metricType)) {
      return 1 * 60 * 1000; // 1 minute
    } else if (baselineMetrics.includes(metricType)) {
      return 3 * 60 * 1000; // 3 minutes
    } else if (stableMetrics.includes(metricType)) {
      return 10 * 60 * 1000; // 10 minutes
    }

    return this.config.defaultTTL;
  }

  private getMetricPriority(metricType: MetricType): CachePriority {
    const criticalMetrics = ['win_rate', 'profit_factor'];
    const highMetrics = ['sharpe_ratio', 'max_drawdown', 'total_return'];
    const mediumMetrics = ['calmar_ratio', 'sortino_ratio', 'volatility'];

    if (criticalMetrics.includes(metricType)) return 'critical';
    if (highMetrics.includes(metricType)) return 'high';
    if (mediumMetrics.includes(metricType)) return 'medium';
    return 'low';
  }

  private getMetricComputationCost(metricType: MetricType): number {
    const costs: Record<string, number> = {
      'win_rate': 2,
      'profit_factor': 3,
      'sharpe_ratio': 7,
      'max_drawdown': 5,
      'calmar_ratio': 8,
      'sortino_ratio': 8,
      'volatility': 6,
      'total_return': 2,
      'trade_count': 1,
      'avg_trade_return': 3,
    };

    return costs[metricType] || 5;
  }

  private inferPriority(key: string): CachePriority {
    if (key.includes('health') || key.includes('performance')) return 'high';
    if (key.includes('metric')) return 'medium';
    return 'low';
  }

  private inferComputationCost(key: string): number {
    if (key.includes('health')) return 6;
    if (key.includes('performance')) return 8;
    if (key.includes('metric')) return 4;
    return 3;
  }

  private estimateSize(data: any): number {
    return JSON.stringify(data).length * 2; // Rough estimate
  }

  private async ensureCapacity(newEntrySize: number): Promise<void> {
    const wouldExceedSize = this.stats.totalSize + newEntrySize > this.config.maxSize;
    const wouldExceedEntries = this.cache.size >= this.config.maxEntries;

    if (wouldExceedSize || wouldExceedEntries) {
      await this.evictEntries(newEntrySize);
    }
  }

  private async evictEntries(spaceNeeded: number): Promise<void> {
    const entries = Array.from(this.cache.entries())
      .map(([key, entry]) => ({
        key,
        entry,
        score: this.calculateEvictionScore(entry),
      }))
      .sort((a, b) => a.score - b.score); // Lower score = higher priority for eviction

    let freedSpace = 0;
    let evicted = 0;

    for (const { key, entry } of entries) {
      if (freedSpace >= spaceNeeded && evicted >= 10) break; // Evict at least some entries
      
      this.cache.delete(key);
      freedSpace += entry.size;
      evicted++;
      this.stats.evictions++;
    }

    this.updateStats();
  }

  private calculateEvictionScore(entry: CacheEntry): number {
    const now = Date.now();
    const age = now - entry.timestamp;
    const timeSinceAccess = now - entry.lastAccessed;
    
    // Lower score = higher priority for eviction
    const priorityScore = { low: 1, medium: 2, high: 3, critical: 4 }[entry.priority];
    const ageScore = Math.min(age / (60 * 60 * 1000), 1); // Normalize to hours
    const accessScore = 1 / (entry.accessCount + 1);
    const recencyScore = Math.min(timeSinceAccess / (60 * 60 * 1000), 1);
    const costScore = entry.computationCost / 10;

    return (ageScore * 0.3) + (accessScore * 0.3) + (recencyScore * 0.2) - (priorityScore * 0.1) - (costScore * 0.1);
  }

  private findKeysToInvalidate(target: string | string[] | RegExp): string[] {
    const keys = Array.from(this.cache.keys());
    
    if (typeof target === 'string') {
      return keys.filter(key => key === target || key.includes(target));
    } else if (Array.isArray(target)) {
      return keys.filter(key => target.includes(key) || target.some(t => key.includes(t)));
    } else if (target instanceof RegExp) {
      return keys.filter(key => target.test(key));
    }
    
    return [];
  }

  private trackAccessTime(key: string, time: number): void {
    if (!this.config.enableAnalytics) return;
    
    if (!this.analytics.has(key)) {
      this.analytics.set(key, []);
    }
    
    const times = this.analytics.get(key)!;
    times.push(time);
    
    // Keep only last 100 access times
    if (times.length > 100) {
      times.shift();
    }
  }

  private updateHitRate(): void {
    const total = this.stats.hits + this.stats.misses;
    this.stats.hitRate = total > 0 ? (this.stats.hits / total) * 100 : 0;
  }

  private updateStats(): void {
    this.stats.totalEntries = this.cache.size;
    this.stats.totalSize = Array.from(this.cache.values())
      .reduce((total, entry) => total + entry.size, 0);
  }

  private startCleanupTimer(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    this.cache.forEach((entry, key) => {
      if (now > entry.expiresAt) {
        expiredKeys.push(key);
      }
    });
    
    expiredKeys.forEach(key => this.cache.delete(key));
    this.updateStats();
  }

  private async loadFromPersistence(): Promise<void> {
    if (!this.config.enablePersistence || !this.hydrationService) return;
    
    try {
      console.log('Loading cache from persistence...');
      const result = await this.hydrationService.hydrateCache({
        validateEntries: true,
        skipExpired: true,
        batchSize: 50,
        maxAge: 24 * 60 * 60 * 1000, // 24 hours
      });
      
      console.log(`Cache hydration completed: ${result.hydratedEntries} entries loaded`);
    } catch (error) {
      console.error('Failed to load cache from persistence:', error);
    }
  }

  private async saveToPersistence(): Promise<void> {
    if (!this.config.enablePersistence || !this.persistenceAdapter) return;
    
    try {
      // Persistence is handled automatically during cacheMetric calls
      console.log('Cache persistence is handled automatically');
    } catch (error) {
      console.error('Failed to save cache to persistence:', error);
    }
  }

  /**
   * Get hydration service for manual cache management
   */
  public getHydrationService(): CacheHydrationService | undefined {
    return this.hydrationService;
  }

  /**
   * Get persistence adapter for direct access
   */
  public getPersistenceAdapter(): PersistenceAdapter | undefined {
    return this.persistenceAdapter;
  }

  /**
   * Helper methods for priority conversion
   */
  private getMetricPriorityNumber(metricType: MetricType): number {
    const priority = this.getMetricPriority(metricType);
    return { low: 1, medium: 2, high: 3, critical: 4 }[priority];
  }

  private numberToCachePriority(num: number): CachePriority {
    const priorities: CachePriority[] = ['low', 'medium', 'high', 'critical'];
    return priorities[Math.max(0, Math.min(3, num - 1))] || 'medium';
  }

  /**
   * Cleanup and shutdown
   */
  public shutdown(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    if (this.persistenceTimer) {
      clearInterval(this.persistenceTimer);
    }
    this.saveToPersistence();
  }
}