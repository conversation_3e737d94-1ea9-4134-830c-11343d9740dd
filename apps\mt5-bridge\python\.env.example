# MT5 Bridge Python Service Environment Configuration

# MT5 Connection Settings
# Leave empty for demo mode (no real account connection)
MT5_LOGIN=
MT5_PASSWORD=
MT5_SERVER=
MT5_PATH=
MT5_TIMEOUT=10000
MT5_PORTABLE=false
MT5_DEMO_MODE=true

# API Service Configuration
API_HOST=localhost
API_PORT=8001
WEBSOCKET_PORT=8002
DEBUG=true
LOG_LEVEL=INFO

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/golddaddyph
DB_MAX_CONNECTIONS=10
DB_TIMEOUT=30

# Redis Cache Configuration
REDIS_URL=redis://localhost:6379
REDIS_MAX_CONNECTIONS=10
REDIS_TTL=300

# TypeScript Bridge Communication
TS_BRIDGE_HOST=localhost
TS_BRIDGE_PORT=3002
TS_BRIDGE_WEBSOCKET_URL=ws://localhost:3002

# Logging Configuration
LOG_FILE_PATH=logs/mt5_bridge.log
LOG_ROTATION=daily
LOG_RETENTION=30

# Performance Settings
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30
PRICE_UPDATE_INTERVAL=100
HEARTBEAT_INTERVAL=30

# Security Settings
API_KEY=your_api_key_here
JWT_SECRET=your_jwt_secret_here
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Feature Flags
ENABLE_REAL_TRADING=false
ENABLE_HISTORICAL_DATA=true
ENABLE_PRICE_STREAMING=true
ENABLE_PAPER_TRADING=true
ENABLE_PERFORMANCE_MONITORING=true