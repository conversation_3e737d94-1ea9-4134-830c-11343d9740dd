"""
Real-time Inference Service for Transformer Models
Provides fast predictions for trading strategies and market analysis
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
import asyncio
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import redis
from loguru import logger
from pathlib import Path
import time

from .model_registry import ModelRegistry, MLModel, ModelStatus
from .transformer_models import create_model, ModelConfig
from .feature_engineering import FinancialFeatureEngineer, FeatureConfig

@dataclass
class PredictionRequest:
    """Request for model prediction"""
    model_id: str
    market_data: pd.DataFrame
    prediction_type: str = "price_change"
    confidence_threshold: float = 0.5
    return_features: bool = False

@dataclass
class PredictionResponse:
    """Response from model prediction"""
    model_id: str
    prediction: Dict[str, float]
    confidence: float
    processing_time_ms: float
    timestamp: datetime
    features_used: Optional[List[str]] = None
    model_version: Optional[str] = None

class ModelCache:
    """Cache for loaded models to avoid repeated loading"""
    
    def __init__(self, max_models: int = 5):
        self.max_models = max_models
        self.models: Dict[str, Tu<PERSON>[nn.Module, Dict]] = {}
        self.access_times: Dict[str, datetime] = {}
    
    def get_model(self, model_id: str) -> Optional[Tuple[nn.Module, Dict]]:
        """Get model from cache"""
        if model_id in self.models:
            self.access_times[model_id] = datetime.now()
            return self.models[model_id]
        return None
    
    def add_model(self, model_id: str, model: nn.Module, config: Dict):
        """Add model to cache"""
        # Remove oldest model if cache is full
        if len(self.models) >= self.max_models:
            oldest_id = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            self.remove_model(oldest_id)
        
        self.models[model_id] = (model, config)
        self.access_times[model_id] = datetime.now()
        logger.info(f"Added model {model_id} to cache")
    
    def remove_model(self, model_id: str):
        """Remove model from cache"""
        if model_id in self.models:
            del self.models[model_id]
            del self.access_times[model_id]
            logger.info(f"Removed model {model_id} from cache")

class InferenceService:
    """
    High-performance inference service for transformer models
    """
    
    def __init__(self, model_registry: ModelRegistry, redis_client: Optional[redis.Redis] = None):
        self.model_registry = model_registry
        self.redis_client = redis_client
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model_cache = ModelCache()
        self.feature_engineers: Dict[str, FinancialFeatureEngineer] = {}
        
        # Performance metrics
        self.prediction_count = 0
        self.total_inference_time = 0.0
        self.error_count = 0
        
        logger.info(f"Inference service initialized with device: {self.device}")
    
    async def predict(self, request: PredictionRequest) -> PredictionResponse:
        """
        Make prediction using specified model
        """
        start_time = time.perf_counter()
        
        try:
            # Load model if not in cache
            model, config = await self._load_model(request.model_id)
            
            # Prepare features
            features = await self._prepare_features(request.market_data, config)
            
            # Make prediction
            with torch.no_grad():
                model.eval()
                features_tensor = torch.FloatTensor(features).unsqueeze(0).to(self.device)
                
                # Forward pass
                outputs = model(features_tensor)
                
                # Extract predictions based on type
                prediction = self._extract_prediction(outputs, request.prediction_type)
                confidence = self._calculate_confidence(outputs)
            
            processing_time = (time.perf_counter() - start_time) * 1000
            
            # Update metrics
            self.prediction_count += 1
            self.total_inference_time += processing_time
            
            # Cache result if Redis is available
            if self.redis_client:
                await self._cache_prediction(request, prediction, confidence)
            
            response = PredictionResponse(
                model_id=request.model_id,
                prediction=prediction,
                confidence=confidence,
                processing_time_ms=processing_time,
                timestamp=datetime.now(),
                features_used=config.get('feature_names', []) if request.return_features else None,
                model_version=config.get('version', 'unknown')
            )
            
            logger.debug(f"Prediction completed in {processing_time:.2f}ms")
            return response
            
        except Exception as e:
            self.error_count += 1
            logger.error(f"Prediction failed for model {request.model_id}: {e}")
            raise
    
    async def batch_predict(self, requests: List[PredictionRequest]) -> List[PredictionResponse]:
        """
        Make batch predictions for multiple requests
        """
        tasks = [self.predict(request) for request in requests]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Filter out exceptions and log errors
        valid_responses = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                logger.error(f"Batch prediction failed for request {i}: {response}")
            else:
                valid_responses.append(response)
        
        return valid_responses
    
    async def predict_market_direction(self, model_id: str, market_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Predict market direction with confidence intervals
        """
        request = PredictionRequest(
            model_id=model_id,
            market_data=market_data,
            prediction_type="direction"
        )
        
        response = await self.predict(request)
        
        # Convert to human-readable format
        direction_map = {0: "down", 1: "sideways", 2: "up"}
        predicted_class = int(np.argmax(list(response.prediction.values())))
        
        return {
            "direction": direction_map.get(predicted_class, "unknown"),
            "probabilities": response.prediction,
            "confidence": response.confidence,
            "processing_time_ms": response.processing_time_ms
        }
    
    async def predict_strategy_performance(self, model_id: str, strategy_params: Dict, 
                                         market_data: pd.DataFrame) -> Dict[str, Any]:
        """
        Predict strategy performance given parameters and market conditions
        """
        # This would be implemented for strategy optimization models
        # For now, return a placeholder
        return {
            "expected_return": 0.05,
            "expected_sharpe": 1.2,
            "risk_score": 0.3,
            "confidence": 0.8
        }
    
    async def _load_model(self, model_id: str) -> Tuple[nn.Module, Dict]:
        """Load model from cache or disk"""
        # Check cache first
        cached = self.model_cache.get_model(model_id)
        if cached:
            return cached
        
        # Load from registry
        model_info = self.model_registry.get_model(model_id)
        if not model_info:
            raise ValueError(f"Model {model_id} not found in registry")
        
        if model_info.deployment.status != ModelStatus.DEPLOYED:
            raise ValueError(f"Model {model_id} is not deployed (status: {model_info.deployment.status})")
        
        # Load configuration
        with open(model_info.config_path, 'r') as f:
            config = json.load(f)
        
        # Create model
        model_config = ModelConfig(**config['model_config'])
        model = create_model(config['training_config']['model_type'], model_config)
        
        # Load weights
        state_dict = torch.load(model_info.model_path, map_location=self.device)
        model.load_state_dict(state_dict)
        model.to(self.device)
        model.eval()
        
        # Cache model
        self.model_cache.add_model(model_id, model, config)
        
        # Create feature engineer if needed
        if model_id not in self.feature_engineers:
            feature_config = FeatureConfig(**config['feature_config'])
            self.feature_engineers[model_id] = FinancialFeatureEngineer(feature_config)
        
        logger.info(f"Loaded model {model_id} for inference")
        return model, config
    
    async def _prepare_features(self, market_data: pd.DataFrame, config: Dict) -> np.ndarray:
        """Prepare features for model input"""
        model_id = config.get('model_id', 'default')
        
        # Get feature engineer
        if model_id not in self.feature_engineers:
            feature_config = FeatureConfig(**config['feature_config'])
            self.feature_engineers[model_id] = FinancialFeatureEngineer(feature_config)
        
        engineer = self.feature_engineers[model_id]
        
        # Engineer features
        features_df = engineer.extract_features(market_data)
        
        # Get feature names used during training
        feature_names = config.get('feature_names', [])
        normalized_features = [name for name in feature_names if name.endswith('_normalized')]
        
        # Extract feature matrix
        if normalized_features:
            feature_matrix = features_df[normalized_features].fillna(0).values
        else:
            # Fallback to all numeric columns
            numeric_cols = features_df.select_dtypes(include=[np.number]).columns
            feature_matrix = features_df[numeric_cols].fillna(0).values
        
        # Get the last sequence for prediction
        sequence_length = config['model_config']['sequence_length']
        if len(feature_matrix) >= sequence_length:
            return feature_matrix[-sequence_length:]
        else:
            # Pad with zeros if not enough data
            padded = np.zeros((sequence_length, feature_matrix.shape[1]))
            padded[-len(feature_matrix):] = feature_matrix
            return padded
    
    def _extract_prediction(self, outputs: Dict[str, torch.Tensor], prediction_type: str) -> Dict[str, float]:
        """Extract prediction from model outputs"""
        if prediction_type == "price_change":
            return {"price_change": float(outputs['price_change'].item())}
        elif prediction_type == "direction":
            probs = torch.softmax(outputs['direction'], dim=-1)
            return {
                "down": float(probs[0, 0].item()),
                "sideways": float(probs[0, 1].item()),
                "up": float(probs[0, 2].item())
            }
        elif prediction_type == "volatility":
            return {"volatility": float(outputs['volatility'].item())}
        elif prediction_type == "strategy_score":
            return {"strategy_score": float(outputs['strategy_score'].item())}
        else:
            # Return all predictions
            result = {}
            for key, value in outputs.items():
                if key != 'embeddings':
                    if value.dim() > 1 and value.size(1) > 1:
                        # Multi-class output
                        probs = torch.softmax(value, dim=-1)
                        for i in range(probs.size(1)):
                            result[f"{key}_{i}"] = float(probs[0, i].item())
                    else:
                        result[key] = float(value.item())
            return result
    
    def _calculate_confidence(self, outputs: Dict[str, torch.Tensor]) -> float:
        """Calculate prediction confidence"""
        # Simple confidence calculation based on output variance
        confidences = []
        
        for key, value in outputs.items():
            if key != 'embeddings':
                if value.dim() > 1 and value.size(1) > 1:
                    # For multi-class outputs, use max probability
                    probs = torch.softmax(value, dim=-1)
                    confidence = float(torch.max(probs).item())
                else:
                    # For regression outputs, use inverse of absolute value (normalized)
                    confidence = 1.0 / (1.0 + abs(float(value.item())))
                
                confidences.append(confidence)
        
        return np.mean(confidences) if confidences else 0.5
    
    async def _cache_prediction(self, request: PredictionRequest, prediction: Dict, confidence: float):
        """Cache prediction result in Redis"""
        if not self.redis_client:
            return
        
        try:
            cache_key = f"prediction:{request.model_id}:{hash(str(request.market_data.values))}"
            cache_data = {
                "prediction": prediction,
                "confidence": confidence,
                "timestamp": datetime.now().isoformat()
            }
            
            # Cache for 5 minutes
            await self.redis_client.setex(cache_key, 300, json.dumps(cache_data, default=str))
        except Exception as e:
            logger.warning(f"Failed to cache prediction: {e}")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get inference service performance metrics"""
        avg_inference_time = self.total_inference_time / max(self.prediction_count, 1)
        
        return {
            "total_predictions": self.prediction_count,
            "total_errors": self.error_count,
            "error_rate": self.error_count / max(self.prediction_count, 1),
            "average_inference_time_ms": avg_inference_time,
            "models_cached": len(self.model_cache.models),
            "cache_hit_rate": 0.0  # To be implemented
        }
    
    async def warm_up_models(self, model_ids: List[str]):
        """Pre-load models into cache for faster inference"""
        for model_id in model_ids:
            try:
                await self._load_model(model_id)
                logger.info(f"Warmed up model {model_id}")
            except Exception as e:
                logger.error(f"Failed to warm up model {model_id}: {e}")
    
    def clear_cache(self):
        """Clear model cache"""
        self.model_cache = ModelCache()
        self.feature_engineers.clear()
        logger.info("Model cache cleared")
