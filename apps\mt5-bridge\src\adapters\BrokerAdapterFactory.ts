/**
 * Broker Adapter Factory
 * 
 * Factory pattern implementation for creating broker-specific adapters:
 * - Standardized interface for different broker implementations
 * - Runtime broker switching and load distribution
 * - Broker configuration management with environment-specific settings
 * - Adapter registration and discovery mechanism
 */

import type { StandardBrokerAdapter } from './StandardBrokerAdapter';
import { MetaQuotesDemoAdapter } from './MetaQuotesDemoAdapter';
import { ProductionBrokerAdapter } from './ProductionBrokerAdapter';

// Broker adapter configuration
export interface BrokerAdapterConfig {
  id: string;
  name: string;
  type: 'demo' | 'production' | 'sandbox';
  server: string;
  apiVersion: string;
  maxOrdersPerSecond: number;
  supportedSymbols: string[];
  supportedTimeframes: string[];
  marginCalculation: 'hedged' | 'netting';
  swapCalculation: 'points' | 'percentage';
  features: BrokerFeature[];
  connectionLimits: {
    maxConnections: number;
    maxConcurrentRequests: number;
    requestTimeout: number;
  };
  authentication: {
    method: 'login_password' | 'certificate' | 'oauth';
    requiresTwoFactor: boolean;
    sessionTimeout: number;
  };
  riskManagement: {
    maxLeverage: number;
    marginRequirement: number;
    maxPositionSize: number;
    hedgingAllowed: boolean;
  };
}

// Broker feature enumeration
export type BrokerFeature = 
  | 'real_time_streaming'
  | 'historical_data'
  | 'trade_execution'
  | 'position_management'
  | 'order_history'
  | 'account_info'
  | 'market_analysis'
  | 'algorithmic_trading'
  | 'copy_trading'
  | 'social_features';

// Adapter registration information
export interface AdapterRegistration {
  type: string;
  name: string;
  version: string;
  constructor: new (config: BrokerAdapterConfig) => StandardBrokerAdapter;
  supportedFeatures: BrokerFeature[];
  defaultConfig: Partial<BrokerAdapterConfig>;
  priority: number;
}

// Broker discovery result
export interface BrokerDiscoveryResult {
  brokerId: string;
  config: BrokerAdapterConfig;
  adapter: StandardBrokerAdapter;
  capabilities: BrokerFeature[];
  healthScore: number;
  lastChecked: Date;
}

// Load balancing strategy
export type LoadBalancingStrategy = 
  | 'round_robin'
  | 'least_connections'
  | 'fastest_response'
  | 'health_based'
  | 'feature_based';

/**
 * Broker Adapter Factory
 * Creates and manages broker-specific adapters
 */
export class BrokerAdapterFactory {
  private registeredAdapters: Map<string, AdapterRegistration> = new Map();
  private activeAdapters: Map<string, StandardBrokerAdapter> = new Map();
  private brokerConfigs: Map<string, BrokerAdapterConfig> = new Map();
  private discoveryResults: Map<string, BrokerDiscoveryResult> = new Map();
  private loadBalancingStrategy: LoadBalancingStrategy = 'health_based';

  constructor() {
    this.registerDefaultAdapters();
  }

  /**
   * Register a broker adapter type
   */
  registerAdapter(registration: AdapterRegistration): void {
    if (this.registeredAdapters.has(registration.type)) {
      console.warn(`⚠️ Overwriting existing adapter registration: ${registration.type}`);
    }
    
    this.registeredAdapters.set(registration.type, registration);
    console.log(`✅ Registered broker adapter: ${registration.name} v${registration.version}`);
  }

  /**
   * Create an adapter instance for a broker
   */
  createAdapter(brokerId: string, config: BrokerAdapterConfig): StandardBrokerAdapter {
    const registration = this.findBestAdapter(config);
    if (!registration) {
      throw new Error(`No suitable adapter found for broker type: ${config.type}`);
    }

    // Merge default config with provided config
    const mergedConfig: BrokerAdapterConfig = {
      ...registration.defaultConfig,
      ...config,
      id: brokerId
    };

    // Validate configuration
    this.validateBrokerConfig(mergedConfig);
    
    // Create adapter instance
    const adapter = new registration.constructor(mergedConfig);
    
    // Store references
    this.activeAdapters.set(brokerId, adapter);
    this.brokerConfigs.set(brokerId, mergedConfig);
    
    console.log(`✅ Created ${registration.name} adapter for broker: ${brokerId}`);
    return adapter;
  }

  /**
   * Get an existing adapter instance
   */
  getAdapter(brokerId: string): StandardBrokerAdapter | null {
    return this.activeAdapters.get(brokerId) || null;
  }

  /**
   * Get the best adapter for a specific feature
   */
  getBestAdapterForFeature(
    feature: BrokerFeature, 
    excludeBrokers: string[] = []
  ): StandardBrokerAdapter | null {
    const candidates = Array.from(this.activeAdapters.entries())
      .filter(([brokerId, adapter]) => {
        if (excludeBrokers.includes(brokerId)) return false;
        
        const config = this.brokerConfigs.get(brokerId);
        return config?.features.includes(feature) || false;
      });

    if (candidates.length === 0) return null;

    // Apply load balancing strategy
    const selectedEntry = this.selectBestCandidate(candidates, feature);
    return selectedEntry ? selectedEntry[1] : null;
  }

  /**
   * Discover available brokers and their capabilities
   */
  async discoverBrokers(): Promise<BrokerDiscoveryResult[]> {
    const discoveries: BrokerDiscoveryResult[] = [];
    
    for (const [brokerId, adapter] of this.activeAdapters) {
      const config = this.brokerConfigs.get(brokerId);
      if (!config) continue;

      try {
        // Test adapter connectivity and capabilities
        const isConnected = await adapter.testConnection();
        const capabilities = await adapter.getCapabilities();
        const healthScore = await adapter.getHealthScore();
        
        const discovery: BrokerDiscoveryResult = {
          brokerId,
          config,
          adapter,
          capabilities,
          healthScore: isConnected ? healthScore : 0,
          lastChecked: new Date()
        };
        
        discoveries.push(discovery);
        this.discoveryResults.set(brokerId, discovery);
        
        console.log(`🔍 Discovered broker ${brokerId}: health=${healthScore.toFixed(2)}, features=${capabilities.length}`);
        
      } catch (error) {
        console.error(`❌ Failed to discover broker ${brokerId}:`, error);
        
        // Create discovery result with error state
        const discovery: BrokerDiscoveryResult = {
          brokerId,
          config,
          adapter,
          capabilities: [],
          healthScore: 0,
          lastChecked: new Date()
        };
        
        discoveries.push(discovery);
        this.discoveryResults.set(brokerId, discovery);
      }
    }
    
    return discoveries.sort((a, b) => b.healthScore - a.healthScore);
  }

  /**
   * Switch to a different broker for load balancing
   */
  async switchBroker(
    currentBrokerId: string, 
    requiredFeatures: BrokerFeature[] = []
  ): Promise<string | null> {
    const alternatives = Array.from(this.activeAdapters.entries())
      .filter(([brokerId, adapter]) => {
        if (brokerId === currentBrokerId) return false;
        
        const config = this.brokerConfigs.get(brokerId);
        if (!config) return false;
        
        // Check if broker supports all required features
        return requiredFeatures.every(feature => config.features.includes(feature));
      });

    if (alternatives.length === 0) {
      console.warn(`⚠️ No alternative brokers found for ${currentBrokerId}`);
      return null;
    }

    // Select best alternative using load balancing strategy
    const selected = this.selectBestCandidate(alternatives, requiredFeatures[0]);
    
    if (selected) {
      const [newBrokerId, adapter] = selected;
      console.log(`🔄 Switched from ${currentBrokerId} to ${newBrokerId}`);
      return newBrokerId;
    }

    return null;
  }

  /**
   * Get load balancing statistics
   */
  getLoadBalancingStats(): {
    totalAdapters: number;
    activeAdapters: number;
    strategyUsed: LoadBalancingStrategy;
    brokerUtilization: Map<string, {
      requestCount: number;
      responseTime: number;
      errorRate: number;
      healthScore: number;
    }>;
  } {
    const brokerUtilization = new Map();
    
    for (const [brokerId, adapter] of this.activeAdapters) {
      const stats = adapter.getConnectionStats();
      brokerUtilization.set(brokerId, {
        requestCount: stats.totalRequests,
        responseTime: stats.averageResponseTime,
        errorRate: stats.errorRate,
        healthScore: this.discoveryResults.get(brokerId)?.healthScore || 0
      });
    }

    return {
      totalAdapters: this.registeredAdapters.size,
      activeAdapters: this.activeAdapters.size,
      strategyUsed: this.loadBalancingStrategy,
      brokerUtilization
    };
  }

  /**
   * Set load balancing strategy
   */
  setLoadBalancingStrategy(strategy: LoadBalancingStrategy): void {
    this.loadBalancingStrategy = strategy;
    console.log(`📊 Load balancing strategy set to: ${strategy}`);
  }

  /**
   * Shutdown all adapters
   */
  async shutdown(): Promise<void> {
    console.log('🔌 Shutting down Broker Adapter Factory...');
    
    const shutdownPromises = Array.from(this.activeAdapters.values()).map(adapter => 
      adapter.disconnect()
    );

    await Promise.allSettled(shutdownPromises);
    
    this.activeAdapters.clear();
    this.brokerConfigs.clear();
    this.discoveryResults.clear();
    
    console.log('✅ Broker Adapter Factory shutdown complete');
  }

  /**
   * Register default adapters
   */
  private registerDefaultAdapters(): void {
    // MetaQuotes Demo Server Adapter
    this.registerAdapter({
      type: 'metaquotes_demo',
      name: 'MetaQuotes Demo Server',
      version: '1.0.0',
      constructor: MetaQuotesDemoAdapter,
      supportedFeatures: [
        'real_time_streaming',
        'historical_data',
        'trade_execution',
        'position_management',
        'account_info'
      ],
      defaultConfig: {
        type: 'demo',
        server: 'demo.metaquotes.net:443',
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'],
        supportedTimeframes: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1'],
        marginCalculation: 'hedged',
        swapCalculation: 'points',
        connectionLimits: {
          maxConnections: 5,
          maxConcurrentRequests: 10,
          requestTimeout: 30000
        },
        authentication: {
          method: 'login_password',
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 500,
          marginRequirement: 0.002,
          maxPositionSize: 10.0,
          hedgingAllowed: true
        }
      } as Partial<BrokerAdapterConfig>,
      priority: 1
    });

    // Production Broker Adapter
    this.registerAdapter({
      type: 'production_broker',
      name: 'Production Broker',
      version: '1.0.0',
      constructor: ProductionBrokerAdapter,
      supportedFeatures: [
        'real_time_streaming',
        'historical_data',
        'trade_execution',
        'position_management',
        'order_history',
        'account_info',
        'algorithmic_trading'
      ],
      defaultConfig: {
        type: 'production',
        apiVersion: '5.0.40',
        maxOrdersPerSecond: 5,
        supportedSymbols: ['EURUSD', 'GBPUSD'],
        supportedTimeframes: ['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1'],
        marginCalculation: 'netting',
        swapCalculation: 'percentage',
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 20000
        },
        authentication: {
          method: 'certificate',
          requiresTwoFactor: true,
          sessionTimeout: 7200000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 5.0,
          hedgingAllowed: false
        }
      } as Partial<BrokerAdapterConfig>,
      priority: 2
    });
  }

  /**
   * Find the best adapter for a configuration
   */
  private findBestAdapter(config: BrokerAdapterConfig): AdapterRegistration | null {
    const candidates = Array.from(this.registeredAdapters.values())
      .filter(registration => {
        // Check if adapter supports the broker type
        if (registration.defaultConfig.type !== config.type) return false;
        
        // Check if adapter supports required features
        return config.features?.every(feature => 
          registration.supportedFeatures.includes(feature)
        ) ?? true;
      })
      .sort((a, b) => b.priority - a.priority);

    return candidates.length > 0 ? candidates[0] : null;
  }

  /**
   * Validate broker configuration
   */
  private validateBrokerConfig(config: BrokerAdapterConfig): void {
    const required = ['id', 'name', 'type', 'server'];
    const missing = required.filter(field => !(field in config));
    
    if (missing.length > 0) {
      throw new Error(`Missing required broker config fields: ${missing.join(', ')}`);
    }

    if (config.connectionLimits.maxConnections <= 0) {
      throw new Error('maxConnections must be greater than 0');
    }

    if (config.connectionLimits.requestTimeout <= 0) {
      throw new Error('requestTimeout must be greater than 0');
    }

    if (config.supportedSymbols.length === 0) {
      throw new Error('At least one supported symbol must be specified');
    }
  }

  /**
   * Select the best candidate based on load balancing strategy
   */
  private selectBestCandidate(
    candidates: [string, StandardBrokerAdapter][], 
    primaryFeature?: BrokerFeature
  ): [string, StandardBrokerAdapter] | null {
    if (candidates.length === 0) return null;
    if (candidates.length === 1) return candidates[0];

    switch (this.loadBalancingStrategy) {
      case 'round_robin': {
        // Simple round-robin selection
        const timestamp = Date.now();
        const index = Math.floor(timestamp / 1000) % candidates.length;
        return candidates[index];
      }

      case 'least_connections': {
        return candidates.reduce((best, current) => {
          const [, bestAdapter] = best;
          const [, currentAdapter] = current;
          
          const bestConnections = bestAdapter.getConnectionStats().activeConnections;
          const currentConnections = currentAdapter.getConnectionStats().activeConnections;
          
          return currentConnections < bestConnections ? current : best;
        });
      }

      case 'fastest_response': {
        return candidates.reduce((best, current) => {
          const [, bestAdapter] = best;
          const [, currentAdapter] = current;
          
          const bestResponseTime = bestAdapter.getConnectionStats().averageResponseTime;
          const currentResponseTime = currentAdapter.getConnectionStats().averageResponseTime;
          
          return currentResponseTime < bestResponseTime ? current : best;
        });
      }

      case 'health_based': {
        return candidates.reduce((best, current) => {
          const [bestId, bestAdapter] = best;
          const [currentId, currentAdapter] = current;
          
          const bestHealth = this.discoveryResults.get(bestId)?.healthScore || 0;
          const currentHealth = this.discoveryResults.get(currentId)?.healthScore || 0;
          
          return currentHealth > bestHealth ? current : best;
        });
      }

      case 'feature_based': {
        if (!primaryFeature) return candidates[0];
        
        // Prefer brokers that specialize in the required feature
        const specialized = candidates.filter(([brokerId, _]) => {
          const config = this.brokerConfigs.get(brokerId);
          return config?.features.includes(primaryFeature);
        });
        
        if (specialized.length > 0) {
          return specialized[0];
        }
        
        return candidates[0];
      }

      default:
        return candidates[0];
    }
  }
}