import { 
  PaperTrade, 
  PaperTradeRequest, 
  PaperTradeResponse, 
  PaperTradeExecutionResult,
  PaperTradeRiskChecks,
  PaperTradeCoaching,
  MarketConditionsSnapshot,
  SimulationMetadata,
  PortfolioImpact,
  LearningMetadata,
  TradeType,
  TradeStatus
} from '@golddaddy/types';
import Decimal from 'decimal.js';

/**
 * Configuration for paper trading engine simulation
 */
export interface PaperTradingEngineConfig {
  // Slippage simulation
  minSlippage: number;
  maxSlippage: number;
  
  // Spread simulation 
  spreadMultiplier: number;
  
  // Latency simulation (milliseconds)
  minLatency: number;
  maxLatency: number;
  
  // Market conditions impact
  volatilityImpactFactor: number;
  liquidityImpactFactor: number;
  
  // Risk thresholds
  maxPositionSizePercent: number;
  maxDailyLimitPercent: number;
}

/**
 * Market data service interface for realistic simulation
 */
export interface MarketDataService {
  getCurrentPrice(instrument: string): Promise<{
    price: number;
    bid: number;
    ask: number;
    spread: number;
    timestamp: Date;
    conditions: MarketConditionsSnapshot;
  }>;
}

/**
 * Virtual portfolio service interface
 */
export interface VirtualPortfolioService {
  validateTrade(
    userId: string, 
    request: PaperTradeRequest, 
    simulatedExecution: PaperTradeExecutionResult
  ): Promise<{ approved: boolean; reason?: string; }>;
  
  executeTrade(
    userId: string, 
    request: PaperTradeRequest, 
    simulatedExecution: PaperTradeExecutionResult
  ): Promise<PaperTrade>;
  
  getVirtualBalance(userId: string, sessionId: string): Promise<{
    balance: number;
    usedMargin: number;
    availableMargin: number;
  }>;
}

/**
 * Paper Trading Engine - Core simulation engine for realistic paper trading
 */
export class PaperTradingEngine {
  private config: PaperTradingEngineConfig;
  
  constructor(
    private marketDataService: MarketDataService,
    private portfolioService: VirtualPortfolioService,
    config?: Partial<PaperTradingEngineConfig>
  ) {
    // Default configuration with realistic parameters
    this.config = {
      minSlippage: 0.0001, // 0.01% minimum slippage
      maxSlippage: 0.005,  // 0.5% maximum slippage
      spreadMultiplier: 1.2, // 20% higher than actual spread
      minLatency: 50,      // 50ms minimum execution delay
      maxLatency: 500,     // 500ms maximum execution delay
      volatilityImpactFactor: 2.0,
      liquidityImpactFactor: 1.5,
      maxPositionSizePercent: 10.0, // Max 10% of portfolio per position
      maxDailyLimitPercent: 25.0,   // Max 25% of portfolio daily
      ...config
    };
  }

  /**
   * Execute a paper trade with realistic simulation
   */
  async executePaperTrade(
    userId: string,
    sessionId: string,
    request: PaperTradeRequest
  ): Promise<PaperTradeResponse> {
    try {
      // 1. Get current market data
      const marketData = await this.marketDataService.getCurrentPrice(request.instrument);
      
      // 2. Simulate realistic execution
      const simulatedExecution = await this.simulateExecution(request, marketData);
      
      // 3. Calculate portfolio impact
      const portfolioBalance = await this.portfolioService.getVirtualBalance(userId, sessionId);
      const portfolioImpact = this.calculatePortfolioImpact(
        request, 
        simulatedExecution, 
        portfolioBalance
      );
      
      // 4. Validate against virtual portfolio limits
      const portfolioValidation = await this.portfolioService.validateTrade(
        userId,
        request,
        simulatedExecution
      );
      
      if (!portfolioValidation.approved) {
        throw new PaperTradingError(
          `Trade rejected: ${portfolioValidation.reason}`,
          'VALIDATION_FAILED'
        );
      }
      
      // 5. Perform risk checks
      const riskChecks = this.performRiskChecks(
        request,
        simulatedExecution,
        portfolioBalance
      );
      
      // 6. Execute trade through portfolio service
      const paperTrade = await this.portfolioService.executeTrade(
        userId,
        request,
        simulatedExecution
      );
      
      // 7. Generate coaching insights
      const coaching = this.generateCoachingInsights(
        request,
        simulatedExecution,
        marketData,
        riskChecks
      );
      
      return {
        trade: paperTrade,
        execution: simulatedExecution,
        riskChecks,
        coaching
      };
      
    } catch (error) {
      console.error('Paper trading execution failed:', error);
      throw new PaperTradingError(
        error instanceof Error ? error.message : 'Unknown error during paper trading execution',
        'EXECUTION_FAILED'
      );
    }
  }

  /**
   * Simulate realistic execution with slippage, spreads, and latency
   */
  private async simulateExecution(
    request: PaperTradeRequest,
    marketData: any
  ): Promise<PaperTradeExecutionResult> {
    // Calculate slippage based on market conditions
    const slippage = this.calculateSlippage(marketData.conditions, request.quantity);
    
    // Simulate execution latency
    const latency = this.simulateLatency(marketData.conditions);
    await new Promise(resolve => setTimeout(resolve, latency));
    
    // Calculate execution price with slippage
    const basePrice = request.type === TradeType.BUY ? marketData.ask : marketData.bid;
    const slippageAmount = request.type === TradeType.BUY ? slippage : -slippage;
    const executionPrice = new Decimal(basePrice).mul(new Decimal(1).plus(slippageAmount));
    
    // Calculate fees (simplified commission structure)
    const fees = this.calculateFees(request.quantity, executionPrice.toNumber());
    
    return {
      filledAt: new Date(),
      executionPrice: executionPrice.toNumber(),
      slippage,
      fees
    };
  }

  /**
   * Calculate realistic slippage based on market conditions and trade size
   */
  private calculateSlippage(
    conditions: MarketConditionsSnapshot, 
    quantity: number
  ): number {
    const baseSlippage = this.config.minSlippage + 
      Math.random() * (this.config.maxSlippage - this.config.minSlippage);
    
    // Adjust for volatility (higher volatility = higher slippage)
    const volatilityAdjustment = conditions.volatility * this.config.volatilityImpactFactor;
    
    // Adjust for liquidity (lower liquidity = higher slippage)
    const liquidityAdjustment = (1 / conditions.liquidity) * this.config.liquidityImpactFactor;
    
    // Adjust for trade size (larger trades = higher slippage)
    const sizeAdjustment = Math.log(quantity / 1000 + 1) * 0.001;
    
    return Math.min(
      baseSlippage + volatilityAdjustment + liquidityAdjustment + sizeAdjustment,
      this.config.maxSlippage
    );
  }

  /**
   * Simulate execution latency based on market conditions
   */
  private simulateLatency(conditions: MarketConditionsSnapshot): number {
    const baseLatency = this.config.minLatency + 
      Math.random() * (this.config.maxLatency - this.config.minLatency);
    
    // Higher volatility can cause system delays
    const volatilityDelay = conditions.volatility * 100;
    
    return Math.min(baseLatency + volatilityDelay, this.config.maxLatency);
  }

  /**
   * Calculate trading fees (commission and spread costs)
   */
  private calculateFees(quantity: number, price: number): number {
    // Simplified commission: $0.01 per share or 0.1% of trade value, whichever is higher
    const shareCommission = quantity * 0.01;
    const percentageCommission = (quantity * price) * 0.001;
    
    return Math.max(shareCommission, percentageCommission);
  }

  /**
   * Calculate portfolio impact of the trade
   */
  private calculatePortfolioImpact(
    request: PaperTradeRequest,
    execution: PaperTradeExecutionResult,
    portfolioBalance: any
  ): PortfolioImpact {
    const tradeValue = new Decimal(request.quantity).mul(execution.executionPrice);
    const totalFees = new Decimal(execution.fees);
    
    const preTradeBalance = portfolioBalance.balance;
    const postTradeBalance = request.type === TradeType.BUY 
      ? preTradeBalance - tradeValue.toNumber() - totalFees.toNumber()
      : preTradeBalance + tradeValue.toNumber() - totalFees.toNumber();
    
    const marginUsed = request.type === TradeType.BUY ? tradeValue.toNumber() : 0;
    const availableMargin = portfolioBalance.availableMargin - marginUsed;
    
    return {
      preTradeBalance,
      postTradeBalance,
      marginUsed,
      availableMargin
    };
  }

  /**
   * Perform risk checks for the paper trade
   */
  private performRiskChecks(
    request: PaperTradeRequest,
    execution: PaperTradeExecutionResult,
    portfolioBalance: any
  ): PaperTradeRiskChecks {
    const tradeValue = new Decimal(request.quantity).mul(execution.executionPrice).toNumber();
    const portfolioValue = portfolioBalance.balance;
    
    // Position size check
    const positionSizePercent = (tradeValue / portfolioValue) * 100;
    let positionSize: 'safe' | 'moderate' | 'risky';
    
    if (positionSizePercent <= 2) {
      positionSize = 'safe';
    } else if (positionSizePercent <= 5) {
      positionSize = 'moderate';
    } else {
      positionSize = 'risky';
    }
    
    const portfolioImpact = positionSizePercent;
    const dailyLimitUsed = positionSizePercent; // Simplified - would track actual daily usage
    
    return {
      positionSize,
      portfolioImpact,
      dailyLimitUsed
    };
  }

  /**
   * Generate educational coaching insights
   */
  private generateCoachingInsights(
    request: PaperTradeRequest,
    execution: PaperTradeExecutionResult,
    marketData: any,
    riskChecks: PaperTradeRiskChecks
  ): PaperTradeCoaching {
    const insights: string[] = [];
    
    // Slippage insights
    if (execution.slippage > 0.002) {
      insights.push(
        `High slippage detected (${(execution.slippage * 100).toFixed(3)}%). ` +
        `Consider using limit orders in volatile conditions.`
      );
    }
    
    // Position size insights
    if (riskChecks.positionSize === 'risky') {
      insights.push(
        `Large position size (${riskChecks.portfolioImpact.toFixed(1)}% of portfolio). ` +
        `Consider reducing position size to manage risk.`
      );
    }
    
    // Market conditions insights
    if (marketData.conditions.volatility > 0.05) {
      insights.push(
        `High market volatility detected. Consider tighter stop losses and smaller position sizes.`
      );
    }
    
    const expectedReturn = this.calculateExpectedReturn(request, marketData);
    const riskReward = this.calculateRiskReward(request);
    
    return {
      preTradeAnalysis: `Market conditions: ${marketData.conditions.trend} trend, ` +
        `volatility: ${(marketData.conditions.volatility * 100).toFixed(2)}%, ` +
        `spread: ${(marketData.spread * 10000).toFixed(1)} pips`,
      expectedOutcome: `Expected return: ${expectedReturn.toFixed(2)}%, ` +
        `Risk/Reward ratio: ${riskReward.toFixed(2)}:1`,
      learningPoints: insights
    };
  }

  /**
   * Calculate expected return based on historical patterns
   */
  private calculateExpectedReturn(request: PaperTradeRequest, marketData: any): number {
    // Simplified expected return calculation
    // In reality, this would use historical data and strategy performance
    const trendMultiplier = marketData.conditions.trend === 'up' ? 1.2 : 
                           marketData.conditions.trend === 'down' ? 0.8 : 1.0;
    const baseReturn = request.type === TradeType.BUY ? 2.0 : -2.0;
    
    return baseReturn * trendMultiplier;
  }

  /**
   * Calculate risk/reward ratio for the trade
   */
  private calculateRiskReward(request: PaperTradeRequest): number {
    if (!request.stopLoss || !request.takeProfit) {
      return 1.0; // Default ratio when SL/TP not set
    }
    
    const entryPrice = request.quantity; // Simplified - would use actual entry price
    const risk = Math.abs(entryPrice - (request.stopLoss || entryPrice));
    const reward = Math.abs((request.takeProfit || entryPrice) - entryPrice);
    
    return risk > 0 ? reward / risk : 1.0;
  }
}

/**
 * Custom error class for paper trading operations
 */
export class PaperTradingError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'PaperTradingError';
  }
}