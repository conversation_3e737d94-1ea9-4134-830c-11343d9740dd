/**
 * Integration Tests for SecurityMiddleware with MarketDataWebSocketServer
 * 
 * Tests the interaction between WebSocket security middleware and the
 * market data server to ensure proper authentication, authorization,
 * and security enforcement.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import WebSocket from 'ws';
import jwt from 'jsonwebtoken';
import { WebSocketSecurityMiddleware, SecurityConfig, SecurityContext } from './SecurityMiddleware';
import { MarketDataWebSocketServer, WebSocketServerConfig } from './MarketDataWebSocketServer';
import { NormalizedMarketData, DataSource, TimeFrame } from '../market-data/RealTimeDataProcessor';
import Decimal from 'decimal.js';

// Mock the audit logger to avoid database dependencies in integration tests
vi.mock('../../lib/audit', () => ({
  auditLogger: {
    logAuditEvent: vi.fn(),
    logSecurityEvent: vi.fn(),
  },
  AUDIT_ACTIONS: {
    WEBSOCKET_CONNECTION_ESTABLISHED: 'websocket_connection_established',
    WEBSOCKET_CONNECTION_CLOSED: 'websocket_connection_closed',
  },
}));

describe('SecurityMiddleware + MarketDataWebSocketServer Integration', () => {
  let securityMiddleware: WebSocketSecurityMiddleware;
  let wsServer: MarketDataWebSocketServer;
  let securityConfig: SecurityConfig;
  let serverConfig: WebSocketServerConfig;
  const JWT_SECRET = 'test-secret-key-for-integration-tests';
  const TEST_PORT = 8081;

  beforeEach(() => {
    // Setup security middleware configuration
    securityConfig = {
      jwtSecret: JWT_SECRET,
      rateLimiting: {
        tokensPerInterval: 10,
        interval: 1000,
        burstCapacity: 15,
      },
      connectionLimits: {
        maxConnectionsPerUser: 3,
        maxConnectionsPerIP: 15, // Allow more connections from same IP for testing
        maxGlobalConnections: 100,
      },
      validation: {
        enableStrictValidation: true,
        maxMessageSize: 1024,
        allowedOrigins: ['http://localhost:3000'],
      },
      monitoring: {
        enableAuditLogging: false, // Disable for tests
        enableSecurityAlerts: true,
        suspiciousActivityThreshold: 20,
      },
    };

    // Setup WebSocket server configuration
    serverConfig = {
      port: TEST_PORT,
      jwtSecret: JWT_SECRET,
      maxConnections: 50,
      heartbeatInterval: 5000,
      clientTimeout: 10000,
      maxInstrumentsPerClient: 10,
      defaultUpdateInterval: 1000,
      rateLimitPerSecond: 10,
      enableCompression: true,
      enableBatching: false,
    };

    securityMiddleware = new WebSocketSecurityMiddleware(securityConfig);
    wsServer = new MarketDataWebSocketServer(serverConfig);
  });

  afterEach(async () => {
    // Cleanup
    if (wsServer) {
      wsServer.shutdown();
    }
    if (securityMiddleware) {
      securityMiddleware.removeAllListeners();
    }
  });

  /**
   * Helper function to create a valid JWT token
   */
  const createValidJWT = (payload: Record<string, any> = {}): string => {
    const defaultPayload = {
      sub: 'test-user-123',
      permissions: ['market_data_access'],
      region: 'US',
      tier: 'premium',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
    };
    
    return jwt.sign({ ...defaultPayload, ...payload }, JWT_SECRET);
  };

  /**
   * Helper function to create test market data
   */
  const createTestMarketData = (): NormalizedMarketData => ({
    id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    source: DataSource.MT5,
    instrument: 'EURUSD',
    timeframe: TimeFrame.M1,
    timestamp: new Date(),
    open: new Decimal('1.08500'),
    high: new Decimal('1.08650'),
    low: new Decimal('1.08450'),
    close: new Decimal('1.08600'),
    volume: new Decimal('1000'),
    precision: 5,
    timezone: 'UTC',
    isCompressed: false,
    qualityScore: 100,
    originalPayloadSize: 500,
    processedPayloadSize: 500,
    processingTimeMs: 10,
  });

  describe('Token Validation Integration', () => {
    it('should validate JWT tokens consistently between middleware and server', async () => {
      const validToken = createValidJWT();
      
      // Test SecurityMiddleware token validation
      const middlewareResult = securityMiddleware.verifyToken(validToken);
      expect(middlewareResult.valid).toBe(true);
      
      if (middlewareResult.valid) {
        expect(middlewareResult.context.userId).toBe('test-user-123');
        expect(middlewareResult.context.permissions).toContain('market_data_access');
      }

      // Test that server would accept the same token by simulating verification
      expect(() => {
        const decoded = jwt.verify(validToken, JWT_SECRET);
        expect(decoded).toHaveProperty('sub', 'test-user-123');
      }).not.toThrow();
    });

    it('should reject expired tokens consistently', async () => {
      const expiredToken = createValidJWT({
        exp: Math.floor(Date.now() / 1000) - 1000, // Expired 1000 seconds ago
      });
      
      // Test SecurityMiddleware rejection
      const middlewareResult = securityMiddleware.verifyToken(expiredToken);
      expect(middlewareResult.valid).toBe(false);
      expect(middlewareResult.error).toContain('expired');

      // Test that server would also reject
      expect(() => {
        jwt.verify(expiredToken, JWT_SECRET);
      }).toThrow('jwt expired');
    });

    it('should reject malformed tokens consistently', async () => {
      const malformedToken = 'invalid.token.format';
      
      // Test SecurityMiddleware rejection
      const middlewareResult = securityMiddleware.verifyToken(malformedToken);
      expect(middlewareResult.valid).toBe(false);
      expect(middlewareResult.error).toContain('Invalid token format');

      // Test that server would also reject
      expect(() => {
        jwt.verify(malformedToken, JWT_SECRET);
      }).toThrow();
    });
  });

  describe('Connection Registration and Message Validation', () => {
    it('should register connection and validate messages in sequence', async () => {
      const validToken = createValidJWT();
      const connectionId = 'conn_123';
      
      // Create security context
      const securityContext: SecurityContext = {
        userId: 'test-user-123',
        permissions: ['market_data_access'],
        region: 'US',
        userTier: 'premium',
        ipAddress: '127.0.0.1',
        userAgent: 'test-agent',
        connectionTime: new Date(),
        lastActivity: new Date(),
      };

      // Register connection with SecurityMiddleware
      const registrationResult = await securityMiddleware.registerConnection(
        connectionId,
        securityContext
      );
      expect(registrationResult.success).toBe(true);

      // Validate a subscription message
      const subscribeMessage = {
        type: 'subscribe',
        action: 'subscribe',
        instruments: ['EURUSD', 'GBPUSD'],
        features: {
          marketAnalysis: true,
          mlPredictions: false,
        },
        timeframes: ['1m'],
        updateInterval: 1000,
      };

      const validationResult = await securityMiddleware.validateMessage(
        connectionId,
        subscribeMessage
      );
      
      expect(validationResult.valid).toBe(true);
      if (validationResult.valid) {
        expect(validationResult.message.type).toBe('subscribe');
        expect(validationResult.context.userId).toBe('test-user-123');
      }

      // Cleanup
      await securityMiddleware.disconnectConnection(connectionId);
    });

    it('should enforce rate limiting across message validation', async () => {
      const connectionId = 'conn_rate_limit';
      const securityContext: SecurityContext = {
        userId: 'test-rate-limit-user',
        permissions: ['market_data_access'],
        region: 'US',
        userTier: 'basic',
        ipAddress: '127.0.0.1',
        connectionTime: new Date(),
        lastActivity: new Date(),
      };

      await securityMiddleware.registerConnection(connectionId, securityContext);

      const testMessage = { type: 'ping' };
      let rateLimitHit = false;

      // Send messages rapidly to trigger rate limit
      for (let i = 0; i < 20; i++) {
        const result = await securityMiddleware.validateMessage(connectionId, testMessage);
        if (!result.valid && result.error?.includes('Rate limit exceeded')) {
          rateLimitHit = true;
          break;
        }
      }

      expect(rateLimitHit).toBe(true);

      await securityMiddleware.disconnectConnection(connectionId);
    });
  });

  describe('Security Event Handling', () => {
    it('should emit security alerts that could be monitored by server', async () => {
      const alertSpy = vi.fn();
      securityMiddleware.on('security_alert', alertSpy);

      const connectionId = 'conn_security_test';
      const securityContext: SecurityContext = {
        userId: 'test-security-user',
        permissions: ['market_data_access'],
        region: 'US',
        userTier: 'basic',
        ipAddress: '*************',
        connectionTime: new Date(),
        lastActivity: new Date(),
      };

      await securityMiddleware.registerConnection(connectionId, securityContext);

      // Send oversized message to trigger security alert
      const oversizedMessage = {
        type: 'subscribe',
        data: 'x'.repeat(2000), // Exceeds maxMessageSize of 1024
      };

      await securityMiddleware.validateMessage(connectionId, oversizedMessage);

      expect(alertSpy).toHaveBeenCalled();
      const alertCall = alertSpy.mock.calls[0][0];
      expect(alertCall.type).toBe('security_alert');
      expect(alertCall.alertType).toBe('oversized_message');

      await securityMiddleware.disconnectConnection(connectionId);
    });

    it('should detect and report suspicious activity', async () => {
      const suspiciousActivitySpy = vi.fn();
      securityMiddleware.on('suspicious_activity_detected', suspiciousActivitySpy);

      const connectionId = 'conn_suspicious';
      const securityContext: SecurityContext = {
        userId: 'test-suspicious-user',
        permissions: ['market_data_access'],
        region: 'US',
        userTier: 'basic',
        ipAddress: '********',
        connectionTime: new Date(),
        lastActivity: new Date(),
      };

      await securityMiddleware.registerConnection(connectionId, securityContext);

      // Generate suspicious activity by sending malformed messages repeatedly
      for (let i = 0; i < 15; i++) {
        await securityMiddleware.validateMessage(connectionId, { invalid: 'message' });
      }

      // Check for suspicious activity manually
      securityMiddleware.checkForSuspiciousActivity();

      expect(suspiciousActivitySpy).toHaveBeenCalled();

      await securityMiddleware.disconnectConnection(connectionId);
    });
  });

  describe('Market Data Broadcasting with Security', () => {
    it('should integrate security context with market data broadcasting', async () => {
      // This test simulates how security context could be used
      // to filter market data based on user permissions
      
      const connectionId = 'conn_market_data';
      const securityContext: SecurityContext = {
        userId: 'test-market-user',
        permissions: ['market_data_access', 'premium_features'],
        region: 'US',
        userTier: 'premium',
        ipAddress: '127.0.0.1',
        connectionTime: new Date(),
        lastActivity: new Date(),
      };

      await securityMiddleware.registerConnection(connectionId, securityContext);

      // Validate subscription message
      const subscribeMessage = {
        type: 'subscribe',
        action: 'subscribe',
        instruments: ['EURUSD'],
        features: {
          marketAnalysis: true,
          mlPredictions: true, // Premium feature
        },
        timeframes: ['1m'],
        updateInterval: 1000,
      };

      const validationResult = await securityMiddleware.validateMessage(
        connectionId,
        subscribeMessage
      );

      expect(validationResult.valid).toBe(true);

      // Verify that premium features are allowed for premium users
      if (validationResult.valid) {
        expect(validationResult.context.userTier).toBe('premium');
        expect(validationResult.context.permissions).toContain('premium_features');
      }

      await securityMiddleware.disconnectConnection(connectionId);
    });

    it('should handle connection cleanup properly', async () => {
      const connectionId = 'conn_cleanup_test';
      const securityContext: SecurityContext = {
        userId: 'test-cleanup-user',
        permissions: ['market_data_access'],
        region: 'US',
        userTier: 'basic',
        ipAddress: '127.0.0.1',
        connectionTime: new Date(),
        lastActivity: new Date(),
      };

      // Register connection
      await securityMiddleware.registerConnection(connectionId, securityContext);

      // Verify connection exists
      const statsBefore = securityMiddleware.getSecurityStats();
      expect(statsBefore.totalConnections).toBe(1);

      // Disconnect connection
      await securityMiddleware.disconnectConnection(connectionId, 'test_cleanup');

      // Verify connection was cleaned up
      const statsAfter = securityMiddleware.getSecurityStats();
      expect(statsAfter.totalConnections).toBe(0);
    });
  });

  describe('Error Scenarios and Edge Cases', () => {
    it('should handle validation of unknown connection gracefully', async () => {
      const unknownConnectionId = 'unknown_connection';
      const testMessage = { type: 'ping' };

      const validationResult = await securityMiddleware.validateMessage(
        unknownConnectionId,
        testMessage
      );

      expect(validationResult.valid).toBe(false);
      expect(validationResult.error).toBe('Connection not found');
      expect(validationResult.shouldDisconnect).toBe(true);
    });

    it('should prevent double registration of same connection', async () => {
      const connectionId = 'conn_double_register';
      const securityContext: SecurityContext = {
        userId: 'test-double-user',
        permissions: ['market_data_access'],
        region: 'US',
        userTier: 'basic',
        ipAddress: '127.0.0.1',
        connectionTime: new Date(),
        lastActivity: new Date(),
      };

      // First registration should succeed
      const firstResult = await securityMiddleware.registerConnection(
        connectionId,
        securityContext
      );
      expect(firstResult.success).toBe(true);

      // Second registration of same connection should handle gracefully
      // (In practice, this might overwrite or be prevented depending on implementation)
      const secondResult = await securityMiddleware.registerConnection(
        connectionId,
        securityContext
      );
      
      // The behavior here depends on implementation - either succeed (overwrite) or fail
      expect(typeof secondResult.success).toBe('boolean');

      await securityMiddleware.disconnectConnection(connectionId);
    });

    it('should handle user blocking and connection termination', async () => {
      const connectionId = 'conn_block_test';
      const userId = 'test-block-user';
      const securityContext: SecurityContext = {
        userId,
        permissions: ['market_data_access'],
        region: 'US',
        userTier: 'basic',
        ipAddress: '127.0.0.1',
        connectionTime: new Date(),
        lastActivity: new Date(),
      };

      const forceDisconnectSpy = vi.fn();
      securityMiddleware.on('force_disconnect', forceDisconnectSpy);

      await securityMiddleware.registerConnection(connectionId, securityContext);

      // Block the user
      securityMiddleware.blockUser(userId, 'test_security_violation');

      expect(forceDisconnectSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          connectionId,
          reason: 'user_blocked',
        })
      );

      // Try to register new connection for blocked user
      const newConnectionAttempt = await securityMiddleware.registerConnection(
        'new_conn',
        securityContext
      );
      expect(newConnectionAttempt.success).toBe(false);
      expect(newConnectionAttempt.error).toBe('User account is blocked');
    });
  });

  describe('Performance and Load Testing', () => {
    it('should handle multiple concurrent connections efficiently', async () => {
      const connectionPromises: Promise<any>[] = [];
      const connectionIds: string[] = [];

      // Create multiple connections concurrently
      for (let i = 0; i < 10; i++) {
        const connectionId = `conn_concurrent_${i}`;
        connectionIds.push(connectionId);
        
        const securityContext: SecurityContext = {
          userId: `test-concurrent-user-${i}`,
          permissions: ['market_data_access'],
          region: 'US',
          userTier: 'basic',
          ipAddress: '127.0.0.1',
          connectionTime: new Date(),
          lastActivity: new Date(),
        };

        connectionPromises.push(
          securityMiddleware.registerConnection(connectionId, securityContext)
        );
      }

      const results = await Promise.all(connectionPromises);
      
      // All connections should succeed
      results.forEach(result => {
        expect(result.success).toBe(true);
      });

      // Verify all connections are tracked
      const stats = securityMiddleware.getSecurityStats();
      expect(stats.totalConnections).toBe(10);

      // Clean up all connections
      const cleanupPromises = connectionIds.map(id =>
        securityMiddleware.disconnectConnection(id, 'test_cleanup')
      );
      await Promise.all(cleanupPromises);

      const finalStats = securityMiddleware.getSecurityStats();
      expect(finalStats.totalConnections).toBe(0);
    });
  });
});