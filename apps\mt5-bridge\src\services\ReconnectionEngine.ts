/**
 * Reconnection Engine
 * 
 * Handles automatic reconnection with advanced features:
 * - Exponential backoff algorithm for connection retries
 * - Network failure detection and classification
 * - State preservation during outages
 * - Connection priority and circuit breaker patterns
 */

import { EventEmitter } from 'events';

// Reconnection configuration
export interface ReconnectionConfig {
  maxRetryAttempts: number;
  baseRetryDelay: number;
  maxRetryDelay: number;
  retryMultiplier: number;
  jitterMax: number;
  circuitBreakerThreshold: number;
  circuitBreakerTimeout: number;
  healthCheckInterval: number;
  statePreservationTimeout: number;
}

// Connection attempt result
export interface ConnectionAttemptResult {
  connectionId: string;
  brokerId: string;
  success: boolean;
  attemptNumber: number;
  latency: number;
  error: string | null;
  timestamp: Date;
  nextRetryDelay: number | null;
}

// Network failure classification
export type FailureType = 
  | 'timeout'           // Connection timeout
  | 'refused'           // Connection refused
  | 'auth'              // Authentication failure
  | 'broker_down'       // Broker server down
  | 'network'           // Network connectivity issue
  | 'protocol'          // Protocol/API error
  | 'rate_limit'        // Rate limit exceeded
  | 'unknown';          // Unclassified error

// Failure analysis result
export interface FailureAnalysis {
  type: FailureType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  shouldRetry: boolean;
  backoffMultiplier: number;
  estimatedRecoveryTime: number;
  recommendation: string;
}

// Circuit breaker states
export type CircuitBreakerState = 'closed' | 'open' | 'half-open';

// Circuit breaker status
export interface CircuitBreakerStatus {
  state: CircuitBreakerState;
  failureCount: number;
  lastFailureTime: Date | null;
  nextAttemptTime: Date | null;
  successCount: number;
  totalAttempts: number;
}

// Connection state preservation
export interface PreservedConnectionState {
  connectionId: string;
  brokerId: string;
  subscriptions: string[];
  lastKnownState: any;
  preservedAt: Date;
  metadata: Record<string, any>;
}

// Reconnection statistics
export interface ReconnectionStats {
  totalAttempts: number;
  successfulReconnections: number;
  failedReconnections: number;
  averageReconnectionTime: number;
  currentRetryConnections: number;
  circuitBreakersOpen: number;
  statePreservationActive: number;
  failuresByType: Map<FailureType, number>;
  averageBackoffTime: number;
}

/**
 * Reconnection Engine
 * Manages automatic reconnection with sophisticated failure handling
 */
export class ReconnectionEngine extends EventEmitter {
  private reconnectionQueue: Map<string, {
    connectionId: string;
    brokerId: string;
    attemptNumber: number;
    nextAttemptTime: Date;
    lastError: string | null;
    retryTimer: NodeJS.Timeout | null;
    preservedState: PreservedConnectionState | null;
  }> = new Map();

  private circuitBreakers: Map<string, CircuitBreakerStatus> = new Map();
  private preservedStates: Map<string, PreservedConnectionState> = new Map();
  private stats: ReconnectionStats;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private stateCleanupInterval: NodeJS.Timeout | null = null;
  private isShuttingDown = false;

  constructor(private config: ReconnectionConfig = {
    maxRetryAttempts: 10,
    baseRetryDelay: 1000,
    maxRetryDelay: 60000,
    retryMultiplier: 2,
    jitterMax: 1000,
    circuitBreakerThreshold: 5,
    circuitBreakerTimeout: 300000, // 5 minutes
    healthCheckInterval: 30000,
    statePreservationTimeout: 1800000 // 30 minutes
  }) {
    super();
    
    this.stats = {
      totalAttempts: 0,
      successfulReconnections: 0,
      failedReconnections: 0,
      averageReconnectionTime: 0,
      currentRetryConnections: 0,
      circuitBreakersOpen: 0,
      statePreservationActive: 0,
      failuresByType: new Map(),
      averageBackoffTime: 0
    };

    this.startHealthChecks();
    this.startStateCleanup();
  }

  /**
   * Schedule a reconnection attempt
   */
  async scheduleReconnection(
    connectionId: string, 
    brokerId: string, 
    error: string,
    preserveState: any = null
  ): Promise<boolean> {
    // Check if circuit breaker is open
    const circuitBreaker = this.getCircuitBreaker(brokerId);
    if (circuitBreaker.state === 'open' && Date.now() < (circuitBreaker.nextAttemptTime?.getTime() || 0)) {
      console.log(`🔌 Circuit breaker open for ${brokerId}, skipping reconnection`);
      return false;
    }

    // Get or create reconnection entry
    let reconnectionEntry = this.reconnectionQueue.get(connectionId);
    if (!reconnectionEntry) {
      reconnectionEntry = {
        connectionId,
        brokerId,
        attemptNumber: 0,
        nextAttemptTime: new Date(),
        lastError: null,
        retryTimer: null,
        preservedState: null
      };
      this.reconnectionQueue.set(connectionId, reconnectionEntry);
    }

    // Update reconnection entry
    reconnectionEntry.attemptNumber++;
    reconnectionEntry.lastError = error;
    
    // Check if maximum attempts reached
    if (reconnectionEntry.attemptNumber > this.config.maxRetryAttempts) {
      console.error(`❌ Maximum reconnection attempts reached for ${connectionId}`);
      this.handleMaxRetriesReached(connectionId, brokerId);
      return false;
    }

    // Analyze the failure
    const failureAnalysis = this.analyzeFailure(error);
    this.updateFailureStats(failureAnalysis.type);

    if (!failureAnalysis.shouldRetry) {
      console.log(`🚫 Failure analysis indicates no retry for ${connectionId}: ${failureAnalysis.recommendation}`);
      this.removeFromQueue(connectionId);
      return false;
    }

    // Preserve connection state if provided
    if (preserveState) {
      const preserved: PreservedConnectionState = {
        connectionId,
        brokerId,
        subscriptions: preserveState.subscriptions || [],
        lastKnownState: preserveState,
        preservedAt: new Date(),
        metadata: preserveState.metadata || {}
      };
      
      this.preservedStates.set(connectionId, preserved);
      reconnectionEntry.preservedState = preserved;
      this.stats.statePreservationActive++;
    }

    // Calculate next retry delay with exponential backoff
    const baseDelay = this.config.baseRetryDelay * failureAnalysis.backoffMultiplier;
    const exponentialDelay = baseDelay * Math.pow(this.config.retryMultiplier, reconnectionEntry.attemptNumber - 1);
    const jitter = Math.random() * this.config.jitterMax;
    const delay = Math.min(exponentialDelay + jitter, this.config.maxRetryDelay);

    reconnectionEntry.nextAttemptTime = new Date(Date.now() + delay);
    
    // Clear existing timer if any
    if (reconnectionEntry.retryTimer) {
      clearTimeout(reconnectionEntry.retryTimer);
    }

    // Schedule the reconnection attempt
    reconnectionEntry.retryTimer = setTimeout(async () => {
      await this.executeReconnection(connectionId);
    }, delay);

    console.log(`🔄 Scheduled reconnection attempt ${reconnectionEntry.attemptNumber}/${this.config.maxRetryAttempts} for ${connectionId} in ${(delay / 1000).toFixed(1)}s`);
    this.emit('reconnectionScheduled', {
      connectionId,
      brokerId,
      attemptNumber: reconnectionEntry.attemptNumber,
      delay,
      failureAnalysis
    });

    return true;
  }

  /**
   * Cancel reconnection attempts for a connection
   */
  cancelReconnection(connectionId: string): boolean {
    const reconnectionEntry = this.reconnectionQueue.get(connectionId);
    if (!reconnectionEntry) return false;

    if (reconnectionEntry.retryTimer) {
      clearTimeout(reconnectionEntry.retryTimer);
    }

    this.reconnectionQueue.delete(connectionId);
    this.preservedStates.delete(connectionId);
    
    console.log(`🛑 Cancelled reconnection attempts for ${connectionId}`);
    this.emit('reconnectionCancelled', { connectionId });
    
    return true;
  }

  /**
   * Get preserved state for a connection
   */
  getPreservedState(connectionId: string): PreservedConnectionState | null {
    return this.preservedStates.get(connectionId) || null;
  }

  /**
   * Get circuit breaker status for a broker
   */
  getCircuitBreakerStatus(brokerId: string): CircuitBreakerStatus {
    return this.getCircuitBreaker(brokerId);
  }

  /**
   * Reset circuit breaker for a broker
   */
  resetCircuitBreaker(brokerId: string): void {
    const circuitBreaker = this.getCircuitBreaker(brokerId);
    circuitBreaker.state = 'closed';
    circuitBreaker.failureCount = 0;
    circuitBreaker.lastFailureTime = null;
    circuitBreaker.nextAttemptTime = null;
    circuitBreaker.successCount = 0;
    
    console.log(`🔄 Reset circuit breaker for ${brokerId}`);
    this.emit('circuitBreakerReset', { brokerId });
  }

  /**
   * Get reconnection statistics
   */
  getStats(): ReconnectionStats {
    this.stats.currentRetryConnections = this.reconnectionQueue.size;
    this.stats.circuitBreakersOpen = Array.from(this.circuitBreakers.values())
      .filter(cb => cb.state === 'open').length;
    this.stats.statePreservationActive = this.preservedStates.size;
    
    return { ...this.stats };
  }

  /**
   * Shutdown the reconnection engine
   */
  async shutdown(): Promise<void> {
    console.log('🔌 Shutting down Reconnection Engine...');
    this.isShuttingDown = true;

    // Clear all intervals
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
    }
    
    if (this.stateCleanupInterval) {
      clearInterval(this.stateCleanupInterval);
      this.stateCleanupInterval = null;
    }

    // Clear all retry timers
    for (const [connectionId, reconnectionEntry] of this.reconnectionQueue) {
      if (reconnectionEntry.retryTimer) {
        clearTimeout(reconnectionEntry.retryTimer);
      }
    }

    this.reconnectionQueue.clear();
    this.preservedStates.clear();
    this.circuitBreakers.clear();
    
    console.log('✅ Reconnection Engine shutdown complete');
    this.emit('shutdown');
  }

  /**
   * Execute a reconnection attempt
   */
  private async executeReconnection(connectionId: string): Promise<void> {
    const reconnectionEntry = this.reconnectionQueue.get(connectionId);
    if (!reconnectionEntry || this.isShuttingDown) return;

    const { brokerId, attemptNumber, preservedState } = reconnectionEntry;
    
    console.log(`🔄 Executing reconnection attempt ${attemptNumber} for ${connectionId}`);
    this.stats.totalAttempts++;

    const startTime = Date.now();
    
    try {
      // Emit reconnection attempt event
      this.emit('reconnectionAttempt', {
        connectionId,
        brokerId,
        attemptNumber,
        preservedState: preservedState !== null
      });

      // Mock reconnection attempt - in production would use actual MT5 connection logic
      const success = await this.attemptConnection(connectionId, brokerId, preservedState);
      
      const latency = Date.now() - startTime;
      
      if (success) {
        await this.handleSuccessfulReconnection(connectionId, brokerId, attemptNumber, latency);
      } else {
        await this.handleFailedReconnection(connectionId, brokerId, attemptNumber, latency, 'Connection failed');
      }
      
    } catch (error) {
      const latency = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      await this.handleFailedReconnection(connectionId, brokerId, attemptNumber, latency, errorMessage);
    }
  }

  /**
   * Attempt to establish connection (mock implementation)
   */
  private async attemptConnection(
    connectionId: string, 
    brokerId: string, 
    preservedState: PreservedConnectionState | null
  ): Promise<boolean> {
    // Mock connection attempt with circuit breaker consideration
    const circuitBreaker = this.getCircuitBreaker(brokerId);
    
    if (circuitBreaker.state === 'half-open') {
      // In half-open state, be more conservative
      const successProbability = 0.3;
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      return Math.random() < successProbability;
    }
    
    // Normal connection attempt
    const successProbability = 0.7; // 70% success rate for reconnections
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 2000));
    return Math.random() < successProbability;
  }

  /**
   * Handle successful reconnection
   */
  private async handleSuccessfulReconnection(
    connectionId: string, 
    brokerId: string, 
    attemptNumber: number, 
    latency: number
  ): Promise<void> {
    this.stats.successfulReconnections++;
    this.updateAverageReconnectionTime(latency);
    
    // Update circuit breaker
    const circuitBreaker = this.getCircuitBreaker(brokerId);
    circuitBreaker.successCount++;
    
    if (circuitBreaker.state === 'half-open' && circuitBreaker.successCount >= 3) {
      circuitBreaker.state = 'closed';
      circuitBreaker.failureCount = 0;
      console.log(`✅ Circuit breaker closed for ${brokerId} after successful reconnections`);
      this.emit('circuitBreakerClosed', { brokerId });
    }

    // Get preserved state for restoration
    const preservedState = this.preservedStates.get(connectionId);
    
    // Remove from queue and preserved states
    this.removeFromQueue(connectionId);
    this.preservedStates.delete(connectionId);
    
    const result: ConnectionAttemptResult = {
      connectionId,
      brokerId,
      success: true,
      attemptNumber,
      latency,
      error: null,
      timestamp: new Date(),
      nextRetryDelay: null
    };

    console.log(`✅ Reconnection successful for ${connectionId} after ${attemptNumber} attempts (${latency}ms)`);
    this.emit('reconnectionSuccess', { 
      ...result, 
      preservedState,
      totalAttempts: attemptNumber
    });
  }

  /**
   * Handle failed reconnection
   */
  private async handleFailedReconnection(
    connectionId: string, 
    brokerId: string, 
    attemptNumber: number, 
    latency: number,
    errorMessage: string
  ): Promise<void> {
    this.stats.failedReconnections++;
    
    // Update circuit breaker
    const circuitBreaker = this.getCircuitBreaker(brokerId);
    circuitBreaker.failureCount++;
    circuitBreaker.lastFailureTime = new Date();
    
    if (circuitBreaker.failureCount >= this.config.circuitBreakerThreshold) {
      circuitBreaker.state = 'open';
      circuitBreaker.nextAttemptTime = new Date(Date.now() + this.config.circuitBreakerTimeout);
      console.log(`🔌 Circuit breaker opened for ${brokerId} due to ${circuitBreaker.failureCount} failures`);
      this.emit('circuitBreakerOpened', { brokerId, failureCount: circuitBreaker.failureCount });
    }

    const reconnectionEntry = this.reconnectionQueue.get(connectionId);
    if (!reconnectionEntry) return;

    const shouldContinue = await this.scheduleReconnection(connectionId, brokerId, errorMessage);
    
    const result: ConnectionAttemptResult = {
      connectionId,
      brokerId,
      success: false,
      attemptNumber,
      latency,
      error: errorMessage,
      timestamp: new Date(),
      nextRetryDelay: shouldContinue ? this.calculateNextDelay(attemptNumber) : null
    };

    console.log(`❌ Reconnection attempt ${attemptNumber} failed for ${connectionId}: ${errorMessage}`);
    this.emit('reconnectionFailed', result);
  }

  /**
   * Analyze failure type and determine retry strategy
   */
  private analyzeFailure(error: string): FailureAnalysis {
    const errorLower = error.toLowerCase();
    
    if (errorLower.includes('timeout')) {
      return {
        type: 'timeout',
        severity: 'medium',
        shouldRetry: true,
        backoffMultiplier: 1.5,
        estimatedRecoveryTime: 30000,
        recommendation: 'Network timeout - retry with increased backoff'
      };
    }
    
    if (errorLower.includes('refused') || errorLower.includes('connection refused')) {
      return {
        type: 'refused',
        severity: 'high',
        shouldRetry: true,
        backoffMultiplier: 2.0,
        estimatedRecoveryTime: 60000,
        recommendation: 'Connection refused - broker may be down'
      };
    }
    
    if (errorLower.includes('auth') || errorLower.includes('login') || errorLower.includes('credential')) {
      return {
        type: 'auth',
        severity: 'critical',
        shouldRetry: false,
        backoffMultiplier: 1.0,
        estimatedRecoveryTime: -1,
        recommendation: 'Authentication failure - check credentials'
      };
    }
    
    if (errorLower.includes('rate limit') || errorLower.includes('too many requests')) {
      return {
        type: 'rate_limit',
        severity: 'medium',
        shouldRetry: true,
        backoffMultiplier: 3.0,
        estimatedRecoveryTime: 120000,
        recommendation: 'Rate limited - extend backoff significantly'
      };
    }
    
    if (errorLower.includes('protocol') || errorLower.includes('api')) {
      return {
        type: 'protocol',
        severity: 'high',
        shouldRetry: true,
        backoffMultiplier: 2.0,
        estimatedRecoveryTime: 90000,
        recommendation: 'Protocol error - may require service restart'
      };
    }
    
    // Default analysis for unknown errors
    return {
      type: 'unknown',
      severity: 'medium',
      shouldRetry: true,
      backoffMultiplier: 1.5,
      estimatedRecoveryTime: 45000,
      recommendation: 'Unknown error - apply standard retry logic'
    };
  }

  /**
   * Get or create circuit breaker for a broker
   */
  private getCircuitBreaker(brokerId: string): CircuitBreakerStatus {
    let circuitBreaker = this.circuitBreakers.get(brokerId);
    
    if (!circuitBreaker) {
      circuitBreaker = {
        state: 'closed',
        failureCount: 0,
        lastFailureTime: null,
        nextAttemptTime: null,
        successCount: 0,
        totalAttempts: 0
      };
      
      this.circuitBreakers.set(brokerId, circuitBreaker);
    }
    
    return circuitBreaker;
  }

  /**
   * Remove connection from reconnection queue
   */
  private removeFromQueue(connectionId: string): void {
    const reconnectionEntry = this.reconnectionQueue.get(connectionId);
    if (reconnectionEntry && reconnectionEntry.retryTimer) {
      clearTimeout(reconnectionEntry.retryTimer);
    }
    this.reconnectionQueue.delete(connectionId);
  }

  /**
   * Handle maximum retries reached
   */
  private handleMaxRetriesReached(connectionId: string, brokerId: string): void {
    this.removeFromQueue(connectionId);
    
    console.error(`💀 Maximum reconnection attempts reached for ${connectionId}`);
    this.emit('maxRetriesReached', { connectionId, brokerId });
    
    // Consider opening circuit breaker
    const circuitBreaker = this.getCircuitBreaker(brokerId);
    circuitBreaker.failureCount += 2; // Penalty for max retries reached
  }

  /**
   * Update failure statistics
   */
  private updateFailureStats(failureType: FailureType): void {
    const currentCount = this.stats.failuresByType.get(failureType) || 0;
    this.stats.failuresByType.set(failureType, currentCount + 1);
  }

  /**
   * Update average reconnection time
   */
  private updateAverageReconnectionTime(latency: number): void {
    const totalReconnections = this.stats.successfulReconnections;
    const currentAverage = this.stats.averageReconnectionTime;
    
    this.stats.averageReconnectionTime = 
      ((currentAverage * (totalReconnections - 1)) + latency) / totalReconnections;
  }

  /**
   * Calculate next retry delay
   */
  private calculateNextDelay(attemptNumber: number): number {
    const exponentialDelay = this.config.baseRetryDelay * 
      Math.pow(this.config.retryMultiplier, attemptNumber);
    return Math.min(exponentialDelay, this.config.maxRetryDelay);
  }

  /**
   * Start health checks for circuit breakers
   */
  private startHealthChecks(): void {
    this.healthCheckInterval = setInterval(() => {
      this.checkCircuitBreakers();
    }, this.config.healthCheckInterval);
  }

  /**
   * Check circuit breakers and transition to half-open if timeout expired
   */
  private checkCircuitBreakers(): void {
    const now = Date.now();
    
    for (const [brokerId, circuitBreaker] of this.circuitBreakers) {
      if (circuitBreaker.state === 'open' && 
          circuitBreaker.nextAttemptTime && 
          now >= circuitBreaker.nextAttemptTime.getTime()) {
        
        circuitBreaker.state = 'half-open';
        circuitBreaker.successCount = 0;
        
        console.log(`🔄 Circuit breaker for ${brokerId} transitioned to half-open`);
        this.emit('circuitBreakerHalfOpen', { brokerId });
      }
    }
  }

  /**
   * Start cleanup of old preserved states
   */
  private startStateCleanup(): void {
    this.stateCleanupInterval = setInterval(() => {
      this.cleanupOldStates();
    }, this.config.statePreservationTimeout / 6); // Check every 5 minutes if timeout is 30 minutes
  }

  /**
   * Clean up old preserved states
   */
  private cleanupOldStates(): void {
    const now = Date.now();
    const expiredStates: string[] = [];
    
    for (const [connectionId, preservedState] of this.preservedStates) {
      if (now - preservedState.preservedAt.getTime() > this.config.statePreservationTimeout) {
        expiredStates.push(connectionId);
      }
    }
    
    expiredStates.forEach(connectionId => {
      this.preservedStates.delete(connectionId);
      console.log(`🧹 Cleaned up expired preserved state for ${connectionId}`);
    });
    
    if (expiredStates.length > 0) {
      this.emit('stateCleanup', { expiredConnections: expiredStates });
    }
  }
}