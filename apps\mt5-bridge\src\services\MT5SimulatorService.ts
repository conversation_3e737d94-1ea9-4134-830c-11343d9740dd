import { EventEmitter } from 'events';
import type { MarketData, Trade, TradingAccount, OHLC, TradeType, TradeStatus } from '@golddaddy/types';

// MT5 Trade Return Codes
export const MT5_RETCODES = {
  DONE: 10009,
  INVALID: 10013,
  INVALID_SYMBOL: 10014,
  INVALID_VOLUME: 10015,
  INVALID_ORDER_TYPE: 10016,
  MARKET_CLOSED: 10018,
  NO_MONEY: 10019,
  TIMEOUT: 10020
} as const;

// Default configuration constants
export const DEFAULT_CONFIG = {
  INITIAL_BALANCE: 10000,
  DEFAULT_LEVERAGE: 500,
  BASE_CURRENCY: 'USD',
  DEFAULT_SERVER: 'MetaQuotes-Demo',
  DEFAULT_COMPANY: 'MetaQuotes Software Corp.',
  MIN_SLIPPAGE: 0.1,
  MAX_SLIPPAGE: 2.0,
  MIN_LATENCY: 50,
  MAX_LATENCY: 200,
  STANDARD_LOT_SIZE: 100000,
  PIP_VALUE_4DIGIT: 0.0001,
  PIP_VALUE_2DIGIT: 0.01
} as const;

export interface MT5AccountInfo {
  login: number;
  server: string;
  name: string;
  company: string;
  currency: string;
  leverage: number;
  balance: number;
  equity: number;
  margin: number;
  margin_free: number;
  margin_level: number;
  profit: number;
  credit: number;
  trade_allowed: boolean;
  trade_expert: boolean;
  limit_orders: number;
  margin_so_so: number;
  margin_so_call: number;
  margin_initial: number;
  margin_maintenance: number;
}

export interface MT5Symbol {
  symbol: string;
  description: string;
  currency_base: string;
  currency_profit: string;
  currency_margin: string;
  point: number;
  digits: number;
  spread: number;
  trade_mode: number;
  volume_min: number;
  volume_max: number;
  volume_step: number;
  volume_limit: number;
  margin_initial: number;
  margin_maintenance: number;
  session_deals: number;
  session_buy_orders: number;
  session_sell_orders: number;
  path: string;
}

export interface MT5Position {
  ticket: number;
  time: number;
  time_msc: number;
  time_update: number;
  time_update_msc: number;
  type: number;
  magic: number;
  identifier: number;
  reason: number;
  volume: number;
  price_open: number;
  sl: number;
  tp: number;
  price_current: number;
  swap: number;
  profit: number;
  symbol: string;
  comment: string;
  external_id: string;
}

export interface MT5OrderRequest {
  action: number;
  magic: number;
  order: number;
  symbol: string;
  volume: number;
  price: number;
  stoplimit: number;
  sl: number;
  tp: number;
  deviation: number;
  type: number;
  type_filling: number;
  type_time: number;
  expiration: number;
  comment: string;
  position: number;
  position_by: number;
}

export interface MT5OrderResult {
  retcode: number;
  deal: number;
  order: number;
  volume: number;
  price: number;
  bid: number;
  ask: number;
  comment: string;
  request_id: number;
  retcode_external: number;
}

export interface SimulatorConfig {
  enableSlippage: boolean;
  slippageRange: [number, number];
  latencyMs: [number, number];
  enableErrorSimulation: boolean;
  errorRate: number;
  brokerProfile: string;
  initialBalance: number;
  leverage: number;
  currency: string;
  server: string;
  company: string;
}

export interface BrokerProfile {
  name: string;
  spreadMultiplier: number;
  latencyRange: [number, number];
  slippageRange: [number, number];
  commissionRate: number;
  errorRate: number;
  maxLeverage: number;
  supportedSymbols: string[];
  tradingHours: {
    start: string;
    end: string;
    timezone: string;
  };
}

/**
 * MT5 Simulator Service
 * Provides realistic MT5 API simulation for development and testing
 */
export class MT5SimulatorService extends EventEmitter {
  private isConnected: boolean = false;
  private account: MT5AccountInfo;
  private symbols: Map<string, MT5Symbol> = new Map();
  private positions: Map<number, MT5Position> = new Map();
  private marketData: Map<string, MarketData> = new Map();
  private config: SimulatorConfig;
  private brokerProfiles: Map<string, BrokerProfile> = new Map();
  private nextTicket: number = 100000;
  private historicalData: Map<string, OHLC[]> = new Map();

  constructor(config: Partial<SimulatorConfig> = {}) {
    super();
    
    this.config = {
      enableSlippage: true,
      slippageRange: [DEFAULT_CONFIG.MIN_SLIPPAGE, DEFAULT_CONFIG.MAX_SLIPPAGE],
      latencyMs: [DEFAULT_CONFIG.MIN_LATENCY, DEFAULT_CONFIG.MAX_LATENCY],
      enableErrorSimulation: false,
      errorRate: 0.01,
      brokerProfile: 'default',
      initialBalance: DEFAULT_CONFIG.INITIAL_BALANCE,
      leverage: DEFAULT_CONFIG.DEFAULT_LEVERAGE,
      currency: DEFAULT_CONFIG.BASE_CURRENCY,
      server: DEFAULT_CONFIG.DEFAULT_SERVER,
      company: DEFAULT_CONFIG.DEFAULT_COMPANY,
      ...config
    };

    this.initializeBrokerProfiles();
    this.initializeAccount();
    this.initializeSymbols();
    this.startMarketDataSimulation();
  }

  /**
   * Initialize broker profiles with different characteristics
   */
  private initializeBrokerProfiles(): void {
    this.brokerProfiles.set('default', {
      name: 'Default Broker',
      spreadMultiplier: 1.0,
      latencyRange: [50, 150],
      slippageRange: [0.1, 1.0],
      commissionRate: 0.0,
      errorRate: 0.01,
      maxLeverage: 500,
      supportedSymbols: ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD'],
      tradingHours: {
        start: '00:00',
        end: '23:59',
        timezone: 'UTC'
      }
    });

    this.brokerProfiles.set('ecn', {
      name: 'ECN Broker',
      spreadMultiplier: 0.5,
      latencyRange: [20, 50],
      slippageRange: [0.0, 0.5],
      commissionRate: 3.5,
      errorRate: 0.005,
      maxLeverage: 200,
      supportedSymbols: ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD', 'EURGBP', 'EURJPY'],
      tradingHours: {
        start: '00:00',
        end: '23:59',
        timezone: 'UTC'
      }
    });

    this.brokerProfiles.set('retail', {
      name: 'Retail Broker',
      spreadMultiplier: 2.0,
      latencyRange: [100, 300],
      slippageRange: [0.5, 3.0],
      commissionRate: 0.0,
      errorRate: 0.02,
      maxLeverage: 1000,
      supportedSymbols: ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD'],
      tradingHours: {
        start: '00:00',
        end: '23:59',
        timezone: 'UTC'
      }
    });
  }

  /**
   * Initialize simulated account information
   */
  private initializeAccount(): void {
    this.account = {
      login: ********,
      server: this.config.server,
      name: 'Demo Account',
      company: this.config.company,
      currency: this.config.currency,
      leverage: this.config.leverage,
      balance: this.config.initialBalance,
      equity: this.config.initialBalance,
      margin: 0,
      margin_free: this.config.initialBalance,
      margin_level: 0,
      profit: 0,
      credit: 0,
      trade_allowed: true,
      trade_expert: true,
      limit_orders: 200,
      margin_so_so: 50,
      margin_so_call: 30,
      margin_initial: 0,
      margin_maintenance: 0
    };
  }

  /**
   * Initialize supported symbols with realistic parameters
   */
  private initializeSymbols(): void {
    const symbolConfigs = [
      { symbol: 'EURUSD', description: 'Euro vs US Dollar', digits: 5, point: 0.00001, spread: 1.5 },
      { symbol: 'GBPUSD', description: 'British Pound vs US Dollar', digits: 5, point: 0.00001, spread: 2.0 },
      { symbol: 'USDJPY', description: 'US Dollar vs Japanese Yen', digits: 3, point: 0.001, spread: 1.8 },
      { symbol: 'USDCHF', description: 'US Dollar vs Swiss Franc', digits: 5, point: 0.00001, spread: 2.5 },
      { symbol: 'AUDUSD', description: 'Australian Dollar vs US Dollar', digits: 5, point: 0.00001, spread: 2.2 },
      { symbol: 'USDCAD', description: 'US Dollar vs Canadian Dollar', digits: 5, point: 0.00001, spread: 2.8 },
      { symbol: 'NZDUSD', description: 'New Zealand Dollar vs US Dollar', digits: 5, point: 0.00001, spread: 3.0 }
    ];

    const currentProfile = this.brokerProfiles.get(this.config.brokerProfile) || this.brokerProfiles.get('default')!;

    symbolConfigs.forEach(config => {
      if (currentProfile.supportedSymbols.includes(config.symbol)) {
        const symbol: MT5Symbol = {
          symbol: config.symbol,
          description: config.description,
          currency_base: config.symbol.slice(0, 3),
          currency_profit: config.symbol.slice(3, 6),
          currency_margin: config.symbol.slice(0, 3),
          point: config.point,
          digits: config.digits,
          spread: Math.round(config.spread * currentProfile.spreadMultiplier),
          trade_mode: 4, // Full trading
          volume_min: 0.01,
          volume_max: 100.0,
          volume_step: 0.01,
          volume_limit: 0,
          margin_initial: 0,
          margin_maintenance: 0,
          session_deals: 0,
          session_buy_orders: 0,
          session_sell_orders: 0,
          path: `Forex\\Major\\${config.symbol}`
        };
        this.symbols.set(config.symbol, symbol);
      }
    });
  }

  /**
   * Start market data simulation with realistic price movements
   */
  private startMarketDataSimulation(): void {
    // Initialize prices for each symbol
    const initialPrices: Record<string, number> = {
      'EURUSD': 1.0850,
      'GBPUSD': 1.2650,
      'USDJPY': 149.50,
      'USDCHF': 0.8950,
      'AUDUSD': 0.6580,
      'USDCAD': 1.3650,
      'NZDUSD': 0.5980
    };

    this.symbols.forEach((symbol, symbolName) => {
      const basePrice = initialPrices[symbolName] || 1.0000;
      const spread = symbol.spread * symbol.point;
      
      this.marketData.set(symbolName, {
        symbol: symbolName,
        bid: basePrice - spread / 2,
        ask: basePrice + spread / 2,
        spread,
        timestamp: new Date()
      });
    });

    // Update market data every 100ms with random movements
    setInterval(() => {
      this.updateMarketData();
    }, 100);
  }

  /**
   * Update market data with realistic price movements
   */
  private updateMarketData(): void {
    this.marketData.forEach((data, symbolName) => {
      const symbol = this.symbols.get(symbolName);
      if (!symbol) return;

      // Random price movement (between -0.1% to +0.1%)
      const movement = (Math.random() - 0.5) * 0.002;
      const newPrice = data.bid + (data.bid * movement);
      const spread = symbol.spread * symbol.point;

      const updatedData: MarketData = {
        symbol: symbolName,
        bid: Math.round(newPrice / symbol.point) * symbol.point,
        ask: Math.round((newPrice + spread) / symbol.point) * symbol.point,
        spread,
        timestamp: new Date()
      };

      this.marketData.set(symbolName, updatedData);
      this.emit('marketData', updatedData);
    });
  }

  /**
   * Connect to MT5 (simulated)
   */
  async connect(): Promise<boolean> {
    await this.simulateLatency();
    
    if (this.shouldSimulateError()) {
      throw new Error('Connection failed: Unable to connect to trading server');
    }

    this.isConnected = true;
    this.emit('connected');
    return true;
  }

  /**
   * Disconnect from MT5 (simulated)
   */
  async disconnect(): Promise<boolean> {
    await this.simulateLatency();
    this.isConnected = false;
    this.emit('disconnected');
    return true;
  }

  /**
   * Check if connected to MT5
   */
  isTerminalConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Common pre-operation checks and setup
   */
  private async performPreOperationChecks(enableErrorSimulation: boolean = true): Promise<void> {
    await this.simulateLatency();
    
    if (!this.isConnected) {
      throw new Error('Not connected to MT5 terminal');
    }

    if (enableErrorSimulation && this.shouldSimulateError()) {
      throw new Error('Operation failed: Server error');
    }
  }

  /**
   * Get account information
   */
  async getAccountInfo(): Promise<MT5AccountInfo> {
    await this.performPreOperationChecks();

    // Update account equity based on current positions
    this.updateAccountEquity();
    
    return { ...this.account };
  }

  /**
   * Get all symbols
   */
  async getSymbols(): Promise<MT5Symbol[]> {
    await this.performPreOperationChecks();
    return Array.from(this.symbols.values());
  }

  /**
   * Get symbol information
   */
  async getSymbolInfo(symbol: string): Promise<MT5Symbol | null> {
    await this.performPreOperationChecks(false); // Don't simulate errors for info queries
    return this.symbols.get(symbol) || null;
  }

  /**
   * Get current market data for a symbol
   */
  async getSymbolTick(symbol: string): Promise<MarketData | null> {
    await this.performPreOperationChecks(false); // Don't simulate errors for tick data
    return this.marketData.get(symbol) || null;
  }

  /**
   * Get historical data
   */
  async getHistoricalData(
    symbol: string,
    timeframe: string,
    startDate: Date,
    endDate: Date
  ): Promise<OHLC[]> {
    await this.performPreOperationChecks();

    // Generate simulated historical data
    return this.generateHistoricalData(symbol, timeframe, startDate, endDate);
  }

  /**
   * Execute a trade order
   */
  async orderSend(request: MT5OrderRequest): Promise<MT5OrderResult> {
    await this.performPreOperationChecks();

    // Validate order
    const validationResult = this.validateOrder(request);
    if (validationResult.retcode !== 10009) { // TRADE_RETCODE_DONE
      return validationResult;
    }

    // Simulate execution with slippage
    const result = await this.executeOrder(request);
    
    if (result.retcode === 10009) {
      // Create position if market order
      if (request.type === 0 || request.type === 1) { // BUY or SELL
        this.createPosition(request, result);
      }
      
      this.updateAccountEquity();
      this.emit('tradeUpdate', this.convertToTrade(request, result));
    }

    return result;
  }

  /**
   * Get all open positions
   */
  async getPositions(): Promise<MT5Position[]> {
    await this.performPreOperationChecks(false); // Don't simulate errors for position queries
    return Array.from(this.positions.values());
  }

  /**
   * Close a position
   */
  async closePosition(ticket: number): Promise<MT5OrderResult> {
    await this.performPreOperationChecks();

    const position = this.positions.get(ticket);
    if (!position) {
      return this.createOrderResult(MT5_RETCODES.INVALID, 'Position not found');
    }

    // Get current market price
    const marketData = this.marketData.get(position.symbol);
    if (!marketData) {
      return this.createOrderResult(MT5_RETCODES.MARKET_CLOSED, 'Market closed');
    }

    // Close position at current market price (with slippage)
    const closePrice = position.type === 0 ? marketData.bid : marketData.ask;
    const slippage = this.calculateSlippage(closePrice);
    const finalPrice = position.type === 0 ? closePrice - slippage : closePrice + slippage;

    // Update position with close data
    position.price_current = finalPrice;
    position.profit = this.calculateProfit(position, finalPrice);
    
    // Remove from positions
    this.positions.delete(ticket);
    
    this.updateAccountEquity();

    return this.createOrderResult(
      MT5_RETCODES.DONE,
      'Position closed',
      this.nextTicket++,  // deal
      ticket,             // order
      position.volume,    // volume
      finalPrice,         // price
      marketData.bid,     // bid
      marketData.ask      // ask
    );
  }

  /**
   * Configure simulator settings
   */
  configure(newConfig: Partial<SimulatorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Reinitialize if broker profile changed
    if (newConfig.brokerProfile) {
      this.initializeSymbols();
    }
  }

  /**
   * Reset simulator to initial state
   */
  reset(): void {
    this.positions.clear();
    this.marketData.clear();
    this.historicalData.clear();
    this.nextTicket = 100000;
    this.initializeAccount();
    this.initializeSymbols();
    this.startMarketDataSimulation();
    this.emit('reset');
  }

  /**
   * Get simulator status and performance metrics
   */
  getStatus(): {
    connected: boolean;
    positionsCount: number;
    accountEquity: number;
    totalProfit: number;
    brokerProfile: string;
    config: SimulatorConfig;
  } {
    const totalProfit = Array.from(this.positions.values()).reduce(
      (sum, pos) => sum + pos.profit, 0
    );

    return {
      connected: this.isConnected,
      positionsCount: this.positions.size,
      accountEquity: this.account.equity,
      totalProfit,
      brokerProfile: this.config.brokerProfile,
      config: this.config
    };
  }

  // Private helper methods

  private async simulateLatency(): Promise<void> {
    if (this.config.latencyMs[0] > 0) {
      const delay = Math.random() * (this.config.latencyMs[1] - this.config.latencyMs[0]) + this.config.latencyMs[0];
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  private shouldSimulateError(): boolean {
    return this.config.enableErrorSimulation && Math.random() < this.config.errorRate;
  }

  private validateOrder(request: MT5OrderRequest): MT5OrderResult {
    const symbol = this.symbols.get(request.symbol);
    if (!symbol) {
      return this.createOrderResult(MT5_RETCODES.INVALID_SYMBOL, 'Invalid symbol');
    }

    // Check volume
    if (request.volume < symbol.volume_min || request.volume > symbol.volume_max) {
      return this.createOrderResult(MT5_RETCODES.INVALID_VOLUME, 'Invalid volume');
    }

    // Check margin
    const requiredMargin = this.calculateRequiredMargin(request);
    if (requiredMargin > this.account.margin_free) {
      return this.createOrderResult(MT5_RETCODES.NO_MONEY, 'Not enough money');
    }

    return this.createOrderResult(MT5_RETCODES.DONE, 'Validation passed');
  }

  /**
   * Helper method to create standardized order results
   */
  private createOrderResult(
    retcode: number, 
    comment: string, 
    deal: number = 0, 
    order: number = 0, 
    volume: number = 0, 
    price: number = 0, 
    bid: number = 0, 
    ask: number = 0
  ): MT5OrderResult {
    return {
      retcode,
      deal,
      order,
      volume,
      price,
      bid,
      ask,
      comment,
      request_id: 0,
      retcode_external: 0
    };
  }

  private async executeOrder(request: MT5OrderRequest): Promise<MT5OrderResult> {
    const marketData = this.marketData.get(request.symbol);
    if (!marketData) {
      return this.createOrderResult(MT5_RETCODES.MARKET_CLOSED, 'Market closed');
    }

    // Calculate execution price with slippage
    let executionPrice = request.type === 0 ? marketData.ask : marketData.bid; // BUY uses ask, SELL uses bid
    
    if (this.config.enableSlippage) {
      const slippage = this.calculateSlippage(executionPrice);
      executionPrice = request.type === 0 ? executionPrice + slippage : executionPrice - slippage;
    }

    return this.createOrderResult(
      MT5_RETCODES.DONE,
      'Order executed',
      this.nextTicket++,  // deal
      this.nextTicket++,  // order
      request.volume,     // volume
      executionPrice,     // price
      marketData.bid,     // bid
      marketData.ask      // ask
    );
  }

  private calculateSlippage(price: number): number {
    const range = this.config.slippageRange;
    const slippagePips = Math.random() * (range[1] - range[0]) + range[0];
    return slippagePips * DEFAULT_CONFIG.PIP_VALUE_4DIGIT; // Convert pips to price units
  }

  private calculateRequiredMargin(request: MT5OrderRequest): number {
    const marketData = this.marketData.get(request.symbol);
    if (!marketData) return 0;

    const symbol = this.symbols.get(request.symbol);
    if (!symbol) return 0;

    // Simplified margin calculation
    const contractSize = DEFAULT_CONFIG.STANDARD_LOT_SIZE;
    const price = request.type === 0 ? marketData.ask : marketData.bid;
    
    return (request.volume * contractSize * price) / this.account.leverage;
  }

  private createPosition(request: MT5OrderRequest, result: MT5OrderResult): void {
    const position: MT5Position = {
      ticket: result.order,
      time: Math.floor(Date.now() / 1000),
      time_msc: Date.now(),
      time_update: Math.floor(Date.now() / 1000),
      time_update_msc: Date.now(),
      type: request.type,
      magic: request.magic,
      identifier: result.order,
      reason: 0,
      volume: request.volume,
      price_open: result.price,
      sl: request.sl,
      tp: request.tp,
      price_current: result.price,
      swap: 0,
      profit: 0,
      symbol: request.symbol,
      comment: request.comment,
      external_id: ''
    };

    this.positions.set(result.order, position);
  }

  private updateAccountEquity(): void {
    let totalProfit = 0;
    let totalMargin = 0;

    this.positions.forEach(position => {
      const marketData = this.marketData.get(position.symbol);
      if (marketData) {
        const currentPrice = position.type === 0 ? marketData.bid : marketData.ask;
        position.price_current = currentPrice;
        position.profit = this.calculateProfit(position, currentPrice);
        totalProfit += position.profit;
        
        // Calculate margin
        const requiredMargin = this.calculatePositionMargin(position);
        totalMargin += requiredMargin;
      }
    });

    this.account.profit = totalProfit;
    this.account.equity = this.account.balance + totalProfit;
    this.account.margin = totalMargin;
    this.account.margin_free = this.account.equity - totalMargin;
    this.account.margin_level = totalMargin > 0 ? (this.account.equity / totalMargin) * 100 : 0;
  }

  private calculateProfit(position: MT5Position, currentPrice: number): number {
    const contractSize = DEFAULT_CONFIG.STANDARD_LOT_SIZE;
    const priceDiff = position.type === 0 
      ? currentPrice - position.price_open 
      : position.price_open - currentPrice;
    
    return priceDiff * position.volume * contractSize;
  }

  private calculatePositionMargin(position: MT5Position): number {
    const contractSize = DEFAULT_CONFIG.STANDARD_LOT_SIZE;
    return (position.volume * contractSize * position.price_open) / this.account.leverage;
  }

  private generateHistoricalData(
    symbol: string,
    timeframe: string,
    startDate: Date,
    endDate: Date
  ): OHLC[] {
    const data: OHLC[] = [];
    const intervalMs = this.getTimeframeInterval(timeframe);
    const currentData = this.marketData.get(symbol);
    
    if (!currentData) return data;

    let currentTime = new Date(startDate);
    let currentPrice = currentData.bid;

    while (currentTime <= endDate) {
      const open = currentPrice;
      const volatility = 0.001; // 0.1% volatility
      
      // Generate OHLC with realistic movements
      const change1 = (Math.random() - 0.5) * volatility * currentPrice;
      const change2 = (Math.random() - 0.5) * volatility * currentPrice;
      const change3 = (Math.random() - 0.5) * volatility * currentPrice;
      
      const price1 = open + change1;
      const price2 = open + change2;
      const price3 = open + change3;
      
      const high = Math.max(open, price1, price2, price3);
      const low = Math.min(open, price1, price2, price3);
      const close = price3;
      
      data.push({
        symbol,
        timeframe,
        open: Math.round(open / DEFAULT_CONFIG.PIP_VALUE_4DIGIT) * DEFAULT_CONFIG.PIP_VALUE_4DIGIT,
        high: Math.round(high / DEFAULT_CONFIG.PIP_VALUE_4DIGIT) * DEFAULT_CONFIG.PIP_VALUE_4DIGIT,
        low: Math.round(low / DEFAULT_CONFIG.PIP_VALUE_4DIGIT) * DEFAULT_CONFIG.PIP_VALUE_4DIGIT,
        close: Math.round(close / DEFAULT_CONFIG.PIP_VALUE_4DIGIT) * DEFAULT_CONFIG.PIP_VALUE_4DIGIT,
        volume: Math.floor(Math.random() * 1000) + 100,
        timestamp: new Date(currentTime)
      });

      currentPrice = close;
      currentTime = new Date(currentTime.getTime() + intervalMs);
    }

    return data;
  }

  private getTimeframeInterval(timeframe: string): number {
    const intervals: Record<string, number> = {
      'M1': 60 * 1000,
      'M5': 5 * 60 * 1000,
      'M15': 15 * 60 * 1000,
      'M30': 30 * 60 * 1000,
      'H1': 60 * 60 * 1000,
      'H4': 4 * 60 * 60 * 1000,
      'D1': 24 * 60 * 60 * 1000,
      'W1': 7 * 24 * 60 * 60 * 1000,
      'MN1': 30 * 24 * 60 * 60 * 1000
    };
    
    return intervals[timeframe] || intervals['M1'];
  }

  private convertToTrade(request: MT5OrderRequest, result: MT5OrderResult): Trade {
    return {
      id: result.order.toString(),
      accountId: this.account.login.toString(),
      symbol: request.symbol,
      type: request.type === 0 ? 'BUY' as TradeType : 'SELL' as TradeType,
      volume: request.volume,
      openPrice: result.price,
      stopLoss: request.sl || undefined,
      takeProfit: request.tp || undefined,
      commission: 0,
      swap: 0,
      openTime: new Date(),
      status: 'OPEN' as TradeStatus
    };
  }
}