#!/usr/bin/env python3
"""
MT5 Connection Test (Windows Compatible)
Simple test without Unicode emojis for Windows console
"""

import MetaTrader5 as mt5
from datetime import datetime, timedelta
import time
import sys

def test_mt5_connection():
    """Test basic MT5 connection and functionality"""
    print("Starting MT5 Connection Test")
    print("=" * 50)
    
    # Initialize MT5
    print("\nStep 1: Initializing MT5 connection...")
    if not mt5.initialize():
        error = mt5.last_error()
        print(f"ERROR: MT5 initialization failed: {error}")
        return False
    
    print("SUCCESS: MT5 initialized successfully!")
    
    # Test 1: Account Information
    print("\nStep 2: Getting account information...")
    account_info = mt5.account_info()
    
    if account_info is None:
        print("ERROR: Failed to get account information")
        return False
    
    print("SUCCESS: Account Information:")
    print(f"   Server: {account_info.server}")
    print(f"   Account: {account_info.login}")
    print(f"   Name: {account_info.name}")
    print(f"   Company: {account_info.company}")
    print(f"   Currency: {account_info.currency}")
    print(f"   Balance: {account_info.balance:.2f}")
    print(f"   Equity: {account_info.equity:.2f}")
    print(f"   Leverage: 1:{account_info.leverage}")
    
    # Check if demo account
    if account_info.trade_mode == mt5.ACCOUNT_TRADE_MODE_DEMO:
        print("SUCCESS: Demo account confirmed - safe for testing")
    else:
        print("WARNING: This is not a demo account!")
    
    # Test 2: Terminal Information
    print("\nStep 3: Getting terminal information...")
    terminal_info = mt5.terminal_info()
    
    if terminal_info is not None:
        print("SUCCESS: Terminal Information:")
        print(f"   Version: {terminal_info.build}")
        print(f"   Path: {terminal_info.path}")
        print(f"   Data Path: {terminal_info.data_path}")
        print(f"   Connected: {terminal_info.connected}")
        print(f"   Experts Enabled: {terminal_info.dlls_allowed}")
    
    # Test 3: Symbol Information
    print("\nStep 4: Testing symbol information...")
    
    # Test EURUSD
    symbol = "EURUSD"
    symbol_info = mt5.symbol_info(symbol)
    
    if symbol_info is not None:
        print(f"SUCCESS: {symbol} Information:")
        print(f"   Bid: {symbol_info.bid:.5f}")
        print(f"   Ask: {symbol_info.ask:.5f}")
        print(f"   Spread: {symbol_info.spread} points")
        print(f"   Digits: {symbol_info.digits}")
        print(f"   Point Value: {symbol_info.point}")
        print(f"   Volume Min: {symbol_info.volume_min}")
        print(f"   Volume Max: {symbol_info.volume_max}")
    else:
        print(f"ERROR: {symbol} not available")
        return False
    
    # Test 4: Current Tick
    print("\nStep 5: Getting current tick data...")
    tick = mt5.symbol_info_tick(symbol)
    
    if tick is not None:
        print(f"SUCCESS: Current {symbol} Tick:")
        print(f"   Time: {datetime.fromtimestamp(tick.time)}")
        print(f"   Bid: {tick.bid:.5f}")
        print(f"   Ask: {tick.ask:.5f}")
        print(f"   Volume: {tick.volume}")
        print(f"   Spread: {(tick.ask - tick.bid):.5f}")
    else:
        print(f"ERROR: Failed to get {symbol} tick data")
        return False
    
    # Test 5: Historical Data
    print("\nStep 6: Testing historical data download...")
    
    # Get last 10 daily bars
    rates_daily = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_D1, 0, 10)
    
    if rates_daily is not None and len(rates_daily) > 0:
        print(f"SUCCESS: Daily Data - Retrieved {len(rates_daily)} bars:")
        latest = rates_daily[-1]
        print(f"   Latest Date: {datetime.fromtimestamp(latest['time'])}")
        print(f"   OHLC: O:{latest['open']:.5f} H:{latest['high']:.5f} L:{latest['low']:.5f} C:{latest['close']:.5f}")
        print(f"   Volume: {latest['tick_volume']}")
    else:
        print("ERROR: Failed to get daily historical data")
        return False
    
    # Get last 100 hourly bars
    rates_hourly = mt5.copy_rates_from_pos(symbol, mt5.TIMEFRAME_H1, 0, 100)
    
    if rates_hourly is not None and len(rates_hourly) > 0:
        print(f"SUCCESS: Hourly Data - Retrieved {len(rates_hourly)} bars")
        latest = rates_hourly[-1]
        print(f"   Latest Time: {datetime.fromtimestamp(latest['time'])}")
        print(f"   Close: {latest['close']:.5f}")
    else:
        print("ERROR: Failed to get hourly historical data")
        return False
    
    # Test 6: Multiple Price Updates (Streaming Simulation)
    print("\nStep 7: Testing price streaming (5 updates)...")
    
    for i in range(5):
        tick = mt5.symbol_info_tick(symbol)
        if tick is not None:
            spread = tick.ask - tick.bid
            print(f"   Update {i+1}: {tick.bid:.5f}/{tick.ask:.5f} (spread: {spread:.5f})")
        else:
            print(f"   ERROR: Update {i+1}: Failed to get tick")
            
        # Small delay
        time.sleep(0.5)
    
    print("SUCCESS: Price streaming test completed")
    
    # Test 7: Available Symbols
    print("\nStep 8: Checking available symbols...")
    symbols = mt5.symbols_get()
    
    if symbols is not None:
        symbol_count = len(symbols)
        print(f"SUCCESS: Found {symbol_count} available symbols")
        
        # Show major forex pairs
        major_pairs = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']
        available_majors = []
        
        for pair in major_pairs:
            if mt5.symbol_info(pair) is not None:
                available_majors.append(pair)
        
        print(f"   Major pairs available: {', '.join(available_majors)}")
        
    else:
        print("ERROR: Failed to get symbols list")
    
    # Test 8: Date Range Historical Data
    print("\nStep 9: Testing date range historical data...")
    
    # Get last 7 days of hourly data
    end_date = datetime.now()
    start_date = end_date - timedelta(days=7)
    
    rates_range = mt5.copy_rates_range(symbol, mt5.TIMEFRAME_H1, start_date, end_date)
    
    if rates_range is not None and len(rates_range) > 0:
        print(f"SUCCESS: Date Range Data - Retrieved {len(rates_range)} hourly bars")
        print(f"   From: {datetime.fromtimestamp(rates_range[0]['time'])}")
        print(f"   To: {datetime.fromtimestamp(rates_range[-1]['time'])}")
    else:
        print("ERROR: Failed to get date range data")
        return False
    
    return True

def main():
    """Main test function"""
    try:
        success = test_mt5_connection()
        
        if success:
            print("\n" + "=" * 50)
            print("ALL TESTS PASSED!")
            print("SUCCESS: MT5 connection is working properly")
            print("SUCCESS: Account information accessible")
            print("SUCCESS: Historical data download working")
            print("SUCCESS: Real-time price data available")
            print("SUCCESS: Ready for full integration!")
            print("=" * 50)
            return True
        else:
            print("\n" + "=" * 50)
            print("SOME TESTS FAILED")
            print("Please check MT5 connection and try again")
            print("=" * 50)
            return False
            
    except Exception as e:
        print(f"\nERROR: Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # Always cleanup
        print("\nShutting down MT5 connection...")
        mt5.shutdown()
        print("SUCCESS: MT5 connection closed")

if __name__ == "__main__":
    print("Make sure MetaTrader 5 is running with your demo account!")
    print("Press Ctrl+C to cancel if needed...\n")
    
    try:
        success = main()
        if success:
            print("\n>>> MT5 Integration Test SUCCESSFUL! <<<")
        else:
            print("\n>>> MT5 Integration Test FAILED! <<<")
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTest cancelled by user")
        mt5.shutdown()
        sys.exit(1)