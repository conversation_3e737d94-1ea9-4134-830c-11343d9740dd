import { PrismaClient } from '@prisma/client';
import {
  QuizCategory,
  QuizDifficulty,
  type QuizQuestion,
  type QuizResponse,
  type LearningResource
} from '@golddaddy/types';
import { ConfidenceAssessmentService } from './ConfidenceAssessmentService.js';

interface ImmediateFeedback {
  isCorrect: boolean;
  explanation: string;
  correctAnswers: {
    id: string;
    text: string;
    reasoning: string;
  }[];
  userAnswers: {
    id: string;
    text: string;
    analysis: string;
  }[];
  learningPoints: string[];
  relatedConcepts: string[];
  additionalResources: LearningResource[];
  confidenceGuidance: {
    message: string;
    recommendedAction: 'continue' | 'review' | 'pause';
    confidenceCalibration: string;
  };
  nextQuestionHint?: string;
}

interface PersonalizedFeedback {
  userLevel: 'beginner' | 'intermediate' | 'advanced';
  feedbackStyle: 'encouraging' | 'analytical' | 'direct';
  explanation: string;
  analogies?: string[];
  practicalExamples?: string[];
  commonMistakes?: string[];
}

interface EducationalContent {
  category: QuizCategory;
  topic: string;
  difficulty: QuizDifficulty;
  conceptExplanation: string;
  realWorldApplication: string;
  keyTakeaways: string[];
  prerequisites: string[];
  relatedTopics: string[];
  practiceExercises: {
    description: string;
    difficulty: QuizDifficulty;
    estimatedTime: number;
  }[];
}

interface WeakAreaRecommendations {
  category: QuizCategory;
  specificTopics: string[];
  recommendedResources: LearningResource[];
  practiceQuestions: string[];
  studyPlan: {
    phase: string;
    duration: number; // days
    objectives: string[];
    milestones: string[];
  }[];
  progressMetrics: {
    currentLevel: number; // 0-100
    targetLevel: number;
    estimatedTimeToTarget: number; // days
  };
}

export class QuizFeedbackService extends ConfidenceAssessmentService {
  private readonly FEEDBACK_STYLES = {
    beginner: {
      tone: 'encouraging',
      explanationDepth: 'detailed',
      includeAnalogies: true,
      emphasizeBasics: true
    },
    intermediate: {
      tone: 'balanced',
      explanationDepth: 'moderate',
      includeAnalogies: false,
      emphasizeConnections: true
    },
    advanced: {
      tone: 'direct',
      explanationDepth: 'concise',
      includeAnalogies: false,
      emphasizeNuances: true
    }
  };

  private readonly EDUCATIONAL_CONTENT_LIBRARY = {
    [QuizCategory.TRADING_FUNDAMENTALS]: {
      'Position Sizing': {
        conceptExplanation: 'Position sizing determines how much capital to risk on each trade, balancing profit potential with risk management.',
        realWorldApplication: 'Professional traders typically risk 1-2% per trade. For a $10,000 account, this means risking $100-200 per trade.',
        keyTakeaways: [
          'Never risk more than you can afford to lose',
          'Consistent position sizing preserves capital during losing streaks',
          'Position size affects emotional decision-making'
        ],
        commonMistakes: [
          'Risking too much to "make back" losses quickly',
          'Using fixed dollar amounts instead of percentages',
          'Ignoring correlation between positions'
        ]
      },
      'Risk-Reward Ratios': {
        conceptExplanation: 'Risk-reward ratio compares potential loss to potential profit, helping evaluate trade attractiveness.',
        realWorldApplication: 'A 1:3 risk-reward ratio means risking $100 to potentially make $300. This allows profitability even with 30% win rate.',
        keyTakeaways: [
          'Minimum 1:2 ratio recommended for most strategies',
          'Higher ratios allow for lower win rates',
          'Must account for realistic price targets'
        ],
        commonMistakes: [
          'Setting unrealistic profit targets',
          'Moving stop losses to avoid losses',
          'Ignoring probability of reaching targets'
        ]
      }
    },
    [QuizCategory.RISK_MANAGEMENT]: {
      'Stop Loss Orders': {
        conceptExplanation: 'Stop losses automatically close positions when losses reach predetermined levels, limiting downside risk.',
        realWorldApplication: 'If you buy a stock at $100 with a 5% stop loss, the position closes automatically at $95.',
        keyTakeaways: [
          'Stop losses are risk management, not profit guarantees',
          'Placement should be based on technical analysis',
          'Never move stop losses against you'
        ],
        commonMistakes: [
          'Placing stops too tight and getting stopped out frequently',
          'Removing stops hoping for recovery',
          'Using fixed percentage stops without technical context'
        ]
      },
      'Drawdown Management': {
        conceptExplanation: 'Drawdown is the decline from peak account value to trough, requiring specific management strategies.',
        realWorldApplication: 'If your account drops from $10,000 to $8,000, you have a 20% drawdown requiring 25% gain to recover.',
        keyTakeaways: [
          'Large drawdowns require exponentially larger gains to recover',
          'Reduce position sizes during drawdown periods',
          'Track maximum drawdown as key risk metric'
        ],
        commonMistakes: [
          'Increasing risk to recover losses faster',
          'Not tracking drawdown metrics',
          'Emotional trading during drawdown periods'
        ]
      }
    },
    [QuizCategory.PLATFORM_FEATURES]: {
      'Safety Systems': {
        conceptExplanation: 'GoldDaddy includes multiple safety mechanisms to prevent catastrophic losses and protect user capital.',
        realWorldApplication: 'Daily loss limits automatically close all positions and prevent new trades when exceeded.',
        keyTakeaways: [
          'Safety systems are mandatory, not optional',
          'Understanding platform protections is crucial',
          'Emergency procedures should be memorized'
        ],
        commonMistakes: [
          'Trying to bypass safety systems',
          'Not understanding emergency procedures',
          'Relying solely on platform protection'
        ]
      }
    },
    [QuizCategory.PSYCHOLOGY_DISCIPLINE]: {
      'Emotional Control': {
        conceptExplanation: 'Trading psychology affects decision-making more than technical analysis, requiring disciplined emotional management.',
        realWorldApplication: 'Fear causes early exits from winning trades, while greed leads to holding losing positions too long.',
        keyTakeaways: [
          'Emotions are the biggest threat to consistent profitability',
          'Discipline beats intelligence in trading',
          'Systematic approaches reduce emotional interference'
        ],
        commonMistakes: [
          'Trading to recover from emotional losses',
          'Overconfidence after winning streaks',
          'Revenge trading after significant losses'
        ]
      }
    }
  };

  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  async generateImmediateFeedback(
    response: QuizResponse,
    question: QuizQuestion,
    userExperience: 'beginner' | 'intermediate' | 'advanced' = 'beginner'
  ): Promise<ImmediateFeedback> {
    const isCorrect = response.isCorrect;
    
    // Generate personalized explanation
    const personalizedExplanation = this.generatePersonalizedExplanation(
      question,
      response,
      userExperience,
      isCorrect
    );

    // Get correct and user answers with analysis
    const correctAnswers = question.options
      .filter(option => question.correctAnswerIds.includes(option.id))
      .map(option => ({
        id: option.id,
        text: option.text,
        reasoning: this.generateAnswerReasoning(option, question, true)
      }));

    const userAnswers = question.options
      .filter(option => response.selectedAnswerIds.includes(option.id))
      .map(option => ({
        id: option.id,
        text: option.text,
        analysis: this.generateAnswerReasoning(option, question, false)
      }));

    // Generate learning points and concepts
    const learningPoints = this.extractLearningPoints(question, isCorrect);
    const relatedConcepts = this.getRelatedConcepts(question.category, question.topic);

    // Get additional resources
    const additionalResources = await this.getAdditionalResources(
      question.category,
      question.topic,
      question.difficulty,
      isCorrect
    );

    // Generate confidence guidance
    const confidenceGuidance = this.generateConfidenceGuidance(
      response.confidenceLevel || 3,
      isCorrect,
      userExperience
    );

    // Generate next question hint if appropriate
    const nextQuestionHint = this.generateNextQuestionHint(
      question,
      response,
      userExperience
    );

    return {
      isCorrect,
      explanation: personalizedExplanation.explanation,
      correctAnswers,
      userAnswers,
      learningPoints,
      relatedConcepts,
      additionalResources,
      confidenceGuidance,
      nextQuestionHint
    };
  }

  async generateEducationalContent(
    category: QuizCategory,
    topic: string,
    difficulty: QuizDifficulty,
    _userContext?: {
      weakAreas: string[];
      strongAreas: string[];
      experience: string;
    }
  ): Promise<EducationalContent> {
    const baseContent = this.getBaseEducationalContent(category, topic);
    
    return {
      category,
      topic,
      difficulty,
      conceptExplanation: baseContent.conceptExplanation,
      realWorldApplication: baseContent.realWorldApplication,
      keyTakeaways: baseContent.keyTakeaways,
      prerequisites: this.getPrerequisites(category, topic, difficulty),
      relatedTopics: this.getRelatedTopics(category, topic),
      practiceExercises: this.generatePracticeExercises(category, topic, difficulty)
    };
  }

  async generateWeakAreaRecommendations(
    userId: string,
    weakAreas: QuizCategory[],
    userPerformanceData: {
      averageScore: number;
      attemptCount: number;
      consistencyRating: number;
    }
  ): Promise<WeakAreaRecommendations[]> {
    const recommendations: WeakAreaRecommendations[] = [];

    for (const category of weakAreas) {
      const specificTopics = await this.identifySpecificWeakTopics(userId, category);
      const recommendedResources = await this.getTargetedResources(category, specificTopics);
      const studyPlan = this.createStudyPlan(category, userPerformanceData);
      
      recommendations.push({
        category,
        specificTopics,
        recommendedResources,
        practiceQuestions: this.generatePracticeQuestionIds(category, specificTopics),
        studyPlan,
        progressMetrics: {
          currentLevel: this.calculateCategoryLevel(userPerformanceData, category),
          targetLevel: this.getTargetLevel(category),
          estimatedTimeToTarget: this.estimateTimeToTarget(userPerformanceData, category)
        }
      });
    }

    return recommendations;
  }

  async getInteractiveExplanation(
    questionId: string,
    userId: string,
    clarificationRequest: string
  ): Promise<{
    clarification: string;
    additionalExamples: string[];
    followUpQuestions: string[];
    suggestedActions: string[];
  }> {
    // This would integrate with AI/LLM service for dynamic explanations
    // For now, providing structured response based on common clarification patterns
    
    const question = await this.getQuestionById(questionId);
    if (!question) {
      throw new Error('Question not found');
    }

    const clarificationLower = clarificationRequest.toLowerCase();
    let clarification = '';
    let additionalExamples: string[] = [];
    let followUpQuestions: string[] = [];
    let suggestedActions: string[] = [];

    if (clarificationLower.includes('why') || clarificationLower.includes('reason')) {
      clarification = this.explainReasoning(question);
      followUpQuestions = ['What are the consequences of choosing differently?', 'How does this apply in real trading?'];
    } else if (clarificationLower.includes('example') || clarificationLower.includes('real world')) {
      clarification = this.provideRealWorldContext(question);
      additionalExamples = this.generateAdditionalExamples(question);
    } else if (clarificationLower.includes('calculate') || clarificationLower.includes('math')) {
      clarification = this.explainCalculations(question);
      suggestedActions = ['Practice similar calculations', 'Review mathematical concepts'];
    } else {
      clarification = question.explanation;
      followUpQuestions = ['Would you like a real-world example?', 'Do you need clarification on any specific part?'];
    }

    return {
      clarification,
      additionalExamples,
      followUpQuestions,
      suggestedActions
    };
  }

  private generatePersonalizedExplanation(
    question: QuizQuestion,
    response: QuizResponse,
    userExperience: string,
    isCorrect: boolean
  ): PersonalizedFeedback {
    const style = this.FEEDBACK_STYLES[userExperience as keyof typeof this.FEEDBACK_STYLES];
    let explanation = question.explanation;
    let analogies: string[] = [];
    let practicalExamples: string[] = [];
    let commonMistakes: string[] = [];

    // Adjust explanation based on user level
    if (userExperience === 'beginner') {
      explanation = this.simplifyExplanation(explanation);
      if (style.includeAnalogies) {
        analogies = this.generateAnalogies(question);
      }
      practicalExamples = this.generatePracticalExamples(question, 'beginner');
    } else if (userExperience === 'intermediate') {
      explanation = this.addConnections(explanation, question);
      practicalExamples = this.generatePracticalExamples(question, 'intermediate');
    } else { // advanced
      explanation = this.addNuances(explanation, question);
      practicalExamples = this.generatePracticalExamples(question, 'advanced');
    }

    // Add common mistakes for incorrect answers
    if (!isCorrect) {
      const baseContent = this.getBaseEducationalContent(question.category, question.topic);
      commonMistakes = baseContent.commonMistakes || [];
    }

    return {
      userLevel: userExperience as any,
      feedbackStyle: style.tone as any,
      explanation,
      analogies: analogies.length > 0 ? analogies : undefined,
      practicalExamples: practicalExamples.length > 0 ? practicalExamples : undefined,
      commonMistakes: commonMistakes.length > 0 ? commonMistakes : undefined
    };
  }

  private generateAnswerReasoning(
    option: any,
    question: QuizQuestion,
    isCorrect: boolean
  ): string {
    if (isCorrect) {
      return `This is correct because it aligns with best practices in ${question.category.replace('_', ' ')}.`;
    } else {
      return `This option is incorrect as it contradicts fundamental principles of ${question.category.replace('_', ' ')}.`;
    }
  }

  private extractLearningPoints(question: QuizQuestion, isCorrect: boolean): string[] {
    const baseContent = this.getBaseEducationalContent(question.category, question.topic);
    
    if (isCorrect) {
      return baseContent.keyTakeaways.slice(0, 2); // Show 2 key takeaways for correct answers
    } else {
      return [
        ...baseContent.keyTakeaways.slice(0, 1),
        ...(baseContent.commonMistakes?.slice(0, 1) || [])
      ];
    }
  }

  private getRelatedConcepts(category: QuizCategory, topic: string): string[] {
    const conceptMap = {
      [QuizCategory.TRADING_FUNDAMENTALS]: {
        'Position Sizing': ['Risk Management', 'Portfolio Theory', 'Capital Preservation'],
        'Risk-Reward Ratios': ['Probability Theory', 'Trade Planning', 'Expectancy']
      },
      [QuizCategory.RISK_MANAGEMENT]: {
        'Stop Loss Orders': ['Position Sizing', 'Technical Analysis', 'Order Types'],
        'Drawdown Management': ['Risk Assessment', 'Capital Recovery', 'Psychology']
      }
    };

    return conceptMap[category]?.[topic] || [];
  }

  private async getAdditionalResources(
    category: QuizCategory,
    topic: string,
    difficulty: QuizDifficulty,
    isCorrect: boolean
  ): Promise<LearningResource[]> {
    // In a real implementation, this would query a resources database
    const resourceLevel = isCorrect ? 'advanced' : 'basic';
    
    return [
      {
        type: 'article',
        title: `${topic} - Complete Guide`,
        url: `/education/${category}/${topic.toLowerCase().replace(' ', '-')}`,
        description: `Comprehensive guide to ${topic} for ${resourceLevel} learning`,
        difficulty: isCorrect ? QuizDifficulty.INTERMEDIATE : QuizDifficulty.BEGINNER,
        estimatedReadTime: 15
      },
      {
        type: 'video',
        title: `${topic} in Practice`,
        url: `/videos/${category}/${topic.toLowerCase().replace(' ', '-')}`,
        description: `Video tutorial showing ${topic} application`,
        difficulty,
        estimatedReadTime: 10
      }
    ];
  }

  private generateConfidenceGuidance(
    confidenceLevel: number,
    isCorrect: boolean,
    _userExperience: string
  ): ImmediateFeedback['confidenceGuidance'] {
    let message = '';
    let recommendedAction: 'continue' | 'review' | 'pause' = 'continue';
    let confidenceCalibration = '';

    if (isCorrect && confidenceLevel >= 4) {
      message = 'Great! Your confidence matches your performance.';
      recommendedAction = 'continue';
      confidenceCalibration = 'Well calibrated';
    } else if (isCorrect && confidenceLevel <= 2) {
      message = 'You got it right! Build confidence in your knowledge.';
      recommendedAction = 'continue';
      confidenceCalibration = 'Underconfident - trust your knowledge more';
    } else if (!isCorrect && confidenceLevel >= 4) {
      message = 'Take time to review this concept before continuing.';
      recommendedAction = 'review';
      confidenceCalibration = 'Overconfident - review fundamentals';
    } else if (!isCorrect && confidenceLevel <= 2) {
      message = 'Your caution was warranted. Study this topic more.';
      recommendedAction = 'pause';
      confidenceCalibration = 'Appropriate caution - focus on learning';
    } else {
      message = 'Keep practicing to improve both knowledge and confidence.';
      recommendedAction = 'continue';
      confidenceCalibration = 'Developing';
    }

    return { message, recommendedAction, confidenceCalibration };
  }

  private generateNextQuestionHint(
    question: QuizQuestion,
    response: QuizResponse,
    userExperience: string
  ): string | undefined {
    if (response.isCorrect && userExperience === 'beginner') {
      return `Great job! The next question will build on ${question.topic}.`;
    } else if (!response.isCorrect) {
      return `The next question will give you another chance to apply ${question.topic} concepts.`;
    }
    return undefined;
  }

  private getBaseEducationalContent(category: QuizCategory, topic: string): any {
    return this.EDUCATIONAL_CONTENT_LIBRARY[category]?.[topic] || {
      conceptExplanation: `Core concepts related to ${topic}`,
      realWorldApplication: `How ${topic} applies in practical trading`,
      keyTakeaways: [`Understanding ${topic} is crucial for trading success`],
      commonMistakes: [`Common errors when dealing with ${topic}`]
    };
  }

  private getPrerequisites(category: QuizCategory, _topic: string, _difficulty: QuizDifficulty): string[] {
    const prerequisites = {
      [QuizCategory.TRADING_FUNDAMENTALS]: ['Basic market knowledge', 'Understanding of financial instruments'],
      [QuizCategory.RISK_MANAGEMENT]: ['Trading fundamentals', 'Basic probability concepts'],
      [QuizCategory.PLATFORM_FEATURES]: ['Platform navigation', 'Basic trading operations'],
      [QuizCategory.PSYCHOLOGY_DISCIPLINE]: ['Trading experience', 'Self-awareness concepts']
    };

    return prerequisites[category] || ['Basic trading knowledge'];
  }

  private getRelatedTopics(category: QuizCategory, _topic: string): string[] {
    const relatedMap = {
      [QuizCategory.TRADING_FUNDAMENTALS]: ['Market Analysis', 'Order Types', 'Trade Planning'],
      [QuizCategory.RISK_MANAGEMENT]: ['Portfolio Management', 'Insurance Strategies', 'Stress Testing'],
      [QuizCategory.PLATFORM_FEATURES]: ['Order Management', 'Account Settings', 'Safety Systems'],
      [QuizCategory.PSYCHOLOGY_DISCIPLINE]: ['Behavioral Finance', 'Decision Making', 'Stress Management']
    };

    return relatedMap[category] || [];
  }

  private generatePracticeExercises(
    category: QuizCategory,
    topic: string,
    difficulty: QuizDifficulty
  ): EducationalContent['practiceExercises'] {
    return [
      {
        description: `Practice problems for ${topic}`,
        difficulty,
        estimatedTime: 15
      },
      {
        description: `Real-world scenarios involving ${topic}`,
        difficulty: difficulty === QuizDifficulty.BEGINNER ? QuizDifficulty.INTERMEDIATE : QuizDifficulty.ADVANCED,
        estimatedTime: 30
      }
    ];
  }

  private async identifySpecificWeakTopics(userId: string, category: QuizCategory): Promise<string[]> {
    // This would analyze user's quiz history to identify specific weak topics
    // For now, returning common weak areas by category
    const commonWeakAreas = {
      [QuizCategory.TRADING_FUNDAMENTALS]: ['Position Sizing', 'Risk-Reward Ratios'],
      [QuizCategory.RISK_MANAGEMENT]: ['Stop Loss Orders', 'Drawdown Management'],
      [QuizCategory.PLATFORM_FEATURES]: ['Safety Systems', 'Order Management'],
      [QuizCategory.PSYCHOLOGY_DISCIPLINE]: ['Emotional Control', 'Discipline']
    };

    return commonWeakAreas[category] || [];
  }

  private async getTargetedResources(category: QuizCategory, topics: string[]): Promise<LearningResource[]> {
    return topics.flatMap(topic => [
      {
        type: 'article',
        title: `Mastering ${topic}`,
        url: `/education/${category}/${topic.toLowerCase().replace(' ', '-')}`,
        description: `Targeted learning for ${topic}`,
        difficulty: QuizDifficulty.INTERMEDIATE,
        estimatedReadTime: 20
      }
    ]);
  }

  private createStudyPlan(
    category: QuizCategory,
    performance: { averageScore: number; attemptCount: number; consistencyRating: number }
  ): WeakAreaRecommendations['studyPlan'] {
    const intensity = performance.averageScore < 60 ? 'intensive' : 'moderate';
    
    return [
      {
        phase: 'Foundation Review',
        duration: intensity === 'intensive' ? 7 : 3,
        objectives: [`Review core ${category.replace('_', ' ')} concepts`, 'Complete practice exercises'],
        milestones: ['80% score on review quiz', 'Complete all reading materials']
      },
      {
        phase: 'Application Practice',
        duration: intensity === 'intensive' ? 10 : 5,
        objectives: ['Apply concepts in practice scenarios', 'Build confidence through repetition'],
        milestones: ['90% score on practice quiz', 'Consistent performance over 3 attempts']
      }
    ];
  }

  private generatePracticeQuestionIds(category: QuizCategory, topics: string[]): string[] {
    // This would return actual question IDs from the database
    return topics.map(topic => `practice_${category}_${topic.replace(' ', '_').toLowerCase()}`);
  }

  private calculateCategoryLevel(
    performance: { averageScore: number; consistencyRating: number },
    _category: QuizCategory
  ): number {
    return Math.round((performance.averageScore * 0.7 + performance.consistencyRating * 100 * 0.3));
  }

  private getTargetLevel(category: QuizCategory): number {
    const targets = {
      [QuizCategory.TRADING_FUNDAMENTALS]: 85,
      [QuizCategory.RISK_MANAGEMENT]: 90,
      [QuizCategory.PLATFORM_FEATURES]: 80,
      [QuizCategory.PSYCHOLOGY_DISCIPLINE]: 85
    };

    return targets[category] || 85;
  }

  private estimateTimeToTarget(
    performance: { averageScore: number },
    category: QuizCategory
  ): number {
    const gap = this.getTargetLevel(category) - performance.averageScore;
    return Math.max(1, Math.ceil(gap / 5)); // Roughly 1 day per 5 points improvement
  }

  // Utility methods for explanation generation
  private simplifyExplanation(explanation: string): string {
    return explanation; // Would implement NLP simplification
  }

  private addConnections(explanation: string, _question: QuizQuestion): string {
    return `${explanation} This concept connects to other aspects of ${_question.category.replace('_', ' ')}.`;
  }

  private addNuances(explanation: string, _question: QuizQuestion): string {
    return `${explanation} Note the nuances in real-world application.`;
  }

  private generateAnalogies(question: QuizQuestion): string[] {
    // Simple analogies for complex concepts
    if (question.topic.toLowerCase().includes('position sizing')) {
      return ['Like not putting all your eggs in one basket', 'Similar to insurance - pay small premiums to protect against big losses'];
    }
    return [];
  }

  private generatePracticalExamples(question: QuizQuestion, level: string): string[] {
    const examples = {
      'Position Sizing': {
        beginner: ['With a $1,000 account, risk only $10-20 per trade'],
        intermediate: ['Calculate position size based on account size and stop loss distance'],
        advanced: ['Use volatility-adjusted position sizing for different instruments']
      }
    };

    return examples[question.topic]?.[level] || [`Practical application of ${question.topic}`];
  }

  private async getQuestionById(questionId: string): Promise<QuizQuestion | null> {
    const prismaQuestion = await this.prisma.quizQuestion.findUnique({
      where: { id: questionId }
    });

    return prismaQuestion ? this.mapPrismaToQuizQuestion(prismaQuestion) : null;
  }

  private explainReasoning(question: QuizQuestion): string {
    return `The reasoning behind this ${question.topic} question relates to fundamental principles of risk management and capital preservation.`;
  }

  private provideRealWorldContext(question: QuizQuestion): string {
    return `In real trading situations, ${question.topic} is applied when making position size decisions and managing risk exposure.`;
  }

  private explainCalculations(question: QuizQuestion): string {
    return `The mathematical calculation for ${question.topic} involves determining the optimal balance between risk and reward.`;
  }

  private generateAdditionalExamples(question: QuizQuestion): string[] {
    return [
      `Example 1: ${question.topic} in a bull market scenario`,
      `Example 2: ${question.topic} during high volatility periods`
    ];
  }

  private mapPrismaToQuizQuestion(prismaQuestion: any): QuizQuestion {
    return {
      id: prismaQuestion.id,
      category: prismaQuestion.category,
      difficulty: prismaQuestion.difficulty,
      topic: prismaQuestion.topic,
      question: prismaQuestion.question,
      options: prismaQuestion.options,
      correctAnswerIds: prismaQuestion.correctAnswerIds,
      explanation: prismaQuestion.explanation,
      learningResources: prismaQuestion.learningResources || [],
      metadata: prismaQuestion.metadata,
      createdAt: prismaQuestion.createdAt,
      updatedAt: prismaQuestion.updatedAt
    };
  }
}