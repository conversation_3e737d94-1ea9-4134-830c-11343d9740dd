"""
Model Registry for GoldDaddy ML Infrastructure
Manages model versioning, deployment, and lifecycle
"""

import os
import json
import asyncio
import hashlib
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum
import joblib
import torch
from loguru import logger

class ModelStatus(Enum):
    TRAINING = "training"
    TESTING = "testing"
    DEPLOYED = "deployed"
    ARCHIVED = "archived"
    FAILED = "failed"

class ModelType(Enum):
    TRANSFORMER = "transformer"
    LSTM = "lstm"
    RANDOM_FOREST = "random_forest"
    GRADIENT_BOOST = "gradient_boost"
    ENSEMBLE = "ensemble"

@dataclass
class ModelPerformance:
    training_accuracy: float
    validation_accuracy: float
    test_accuracy: float
    precision: float
    recall: float
    f1_score: float
    backtest_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float

@dataclass
class ModelDeployment:
    status: ModelStatus
    endpoint: Optional[str] = None
    last_prediction: Optional[datetime] = None
    predictions_per_day: int = 0
    deployment_date: Optional[datetime] = None
    health_score: float = 1.0

@dataclass
class ModelTraining:
    dataset_size: int
    features_used: List[str]
    hyperparameters: Dict[str, Any]
    training_duration: float
    last_trained: datetime
    next_retraining: Optional[datetime] = None
    training_data_hash: str = ""

@dataclass
class MLModel:
    id: str
    name: str
    version: str
    model_type: ModelType
    description: str
    performance: ModelPerformance
    deployment: ModelDeployment
    training: ModelTraining
    created_at: datetime
    updated_at: datetime
    model_path: str
    config_path: str
    metadata: Dict[str, Any]

class ModelRegistry:
    """
    Centralized model registry for managing ML models lifecycle
    """
    
    def __init__(self, registry_path: str = "./models"):
        self.registry_path = Path(registry_path)
        self.registry_path.mkdir(exist_ok=True)
        self.models_db_path = self.registry_path / "models.json"
        self.models: Dict[str, MLModel] = {}
        self.load_registry()
        
    def load_registry(self):
        """Load model registry from disk"""
        if self.models_db_path.exists():
            try:
                with open(self.models_db_path, 'r') as f:
                    data = json.load(f)
                    for model_data in data.values():
                        # Convert datetime strings back to datetime objects
                        model_data['created_at'] = datetime.fromisoformat(model_data['created_at'])
                        model_data['updated_at'] = datetime.fromisoformat(model_data['updated_at'])
                        model_data['training']['last_trained'] = datetime.fromisoformat(model_data['training']['last_trained'])
                        
                        if model_data['training']['next_retraining']:
                            model_data['training']['next_retraining'] = datetime.fromisoformat(model_data['training']['next_retraining'])
                        
                        if model_data['deployment']['last_prediction']:
                            model_data['deployment']['last_prediction'] = datetime.fromisoformat(model_data['deployment']['last_prediction'])
                        
                        if model_data['deployment']['deployment_date']:
                            model_data['deployment']['deployment_date'] = datetime.fromisoformat(model_data['deployment']['deployment_date'])
                        
                        # Convert enums
                        model_data['model_type'] = ModelType(model_data['model_type'])
                        model_data['deployment']['status'] = ModelStatus(model_data['deployment']['status'])
                        
                        # Reconstruct nested objects
                        model_data['performance'] = ModelPerformance(**model_data['performance'])
                        model_data['deployment'] = ModelDeployment(**model_data['deployment'])
                        model_data['training'] = ModelTraining(**model_data['training'])
                        
                        model = MLModel(**model_data)
                        self.models[model.id] = model
                        
                logger.info(f"Loaded {len(self.models)} models from registry")
            except Exception as e:
                logger.error(f"Failed to load model registry: {e}")
                self.models = {}
    
    def save_registry(self):
        """Save model registry to disk"""
        try:
            # Convert models to serializable format
            serializable_models = {}
            for model_id, model in self.models.items():
                model_dict = asdict(model)
                
                # Convert datetime objects to ISO strings
                model_dict['created_at'] = model.created_at.isoformat()
                model_dict['updated_at'] = model.updated_at.isoformat()
                model_dict['training']['last_trained'] = model.training.last_trained.isoformat()
                
                if model.training.next_retraining:
                    model_dict['training']['next_retraining'] = model.training.next_retraining.isoformat()
                
                if model.deployment.last_prediction:
                    model_dict['deployment']['last_prediction'] = model.deployment.last_prediction.isoformat()
                
                if model.deployment.deployment_date:
                    model_dict['deployment']['deployment_date'] = model.deployment.deployment_date.isoformat()
                
                # Convert enums to strings
                model_dict['model_type'] = model.model_type.value
                model_dict['deployment']['status'] = model.deployment.status.value
                
                serializable_models[model_id] = model_dict
            
            with open(self.models_db_path, 'w') as f:
                json.dump(serializable_models, f, indent=2)
                
            logger.info(f"Saved {len(self.models)} models to registry")
        except Exception as e:
            logger.error(f"Failed to save model registry: {e}")
    
    def register_model(self, model: MLModel) -> str:
        """Register a new model"""
        model.updated_at = datetime.now()
        self.models[model.id] = model
        self.save_registry()
        logger.info(f"Registered model {model.name} v{model.version} with ID {model.id}")
        return model.id
    
    def get_model(self, model_id: str) -> Optional[MLModel]:
        """Get model by ID"""
        return self.models.get(model_id)
    
    def get_models_by_type(self, model_type: ModelType) -> List[MLModel]:
        """Get all models of a specific type"""
        return [model for model in self.models.values() if model.model_type == model_type]
    
    def get_deployed_models(self) -> List[MLModel]:
        """Get all deployed models"""
        return [model for model in self.models.values() if model.deployment.status == ModelStatus.DEPLOYED]
    
    def get_latest_model(self, name: str) -> Optional[MLModel]:
        """Get the latest version of a model by name"""
        models = [model for model in self.models.values() if model.name == name]
        if not models:
            return None
        return max(models, key=lambda m: m.created_at)
    
    def update_model_status(self, model_id: str, status: ModelStatus):
        """Update model deployment status"""
        if model_id in self.models:
            self.models[model_id].deployment.status = status
            self.models[model_id].updated_at = datetime.now()
            if status == ModelStatus.DEPLOYED:
                self.models[model_id].deployment.deployment_date = datetime.now()
            self.save_registry()
            logger.info(f"Updated model {model_id} status to {status.value}")
    
    def update_model_performance(self, model_id: str, performance: ModelPerformance):
        """Update model performance metrics"""
        if model_id in self.models:
            self.models[model_id].performance = performance
            self.models[model_id].updated_at = datetime.now()
            self.save_registry()
            logger.info(f"Updated performance metrics for model {model_id}")
    
    def schedule_retraining(self, model_id: str, retrain_date: datetime):
        """Schedule model for retraining"""
        if model_id in self.models:
            self.models[model_id].training.next_retraining = retrain_date
            self.models[model_id].updated_at = datetime.now()
            self.save_registry()
            logger.info(f"Scheduled retraining for model {model_id} at {retrain_date}")
    
    def get_models_for_retraining(self) -> List[MLModel]:
        """Get models that need retraining"""
        now = datetime.now()
        return [
            model for model in self.models.values()
            if model.training.next_retraining and model.training.next_retraining <= now
        ]
    
    def archive_model(self, model_id: str):
        """Archive a model"""
        self.update_model_status(model_id, ModelStatus.ARCHIVED)
    
    def delete_model(self, model_id: str):
        """Delete a model from registry and disk"""
        if model_id in self.models:
            model = self.models[model_id]
            
            # Delete model files
            if os.path.exists(model.model_path):
                os.remove(model.model_path)
            if os.path.exists(model.config_path):
                os.remove(model.config_path)
            
            # Remove from registry
            del self.models[model_id]
            self.save_registry()
            logger.info(f"Deleted model {model_id}")
    
    def get_model_stats(self) -> Dict[str, Any]:
        """Get registry statistics"""
        total_models = len(self.models)
        status_counts = {}
        type_counts = {}
        
        for model in self.models.values():
            status = model.deployment.status.value
            model_type = model.model_type.value
            
            status_counts[status] = status_counts.get(status, 0) + 1
            type_counts[model_type] = type_counts.get(model_type, 0) + 1
        
        return {
            "total_models": total_models,
            "status_distribution": status_counts,
            "type_distribution": type_counts,
            "models_needing_retraining": len(self.get_models_for_retraining())
        }
