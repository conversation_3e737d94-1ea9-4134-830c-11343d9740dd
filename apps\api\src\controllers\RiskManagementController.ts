/**
 * Risk Management API Controller
 * 
 * RESTful API endpoints for risk data retrieval, real-time risk metrics,
 * and risk limit configuration with proper authorization.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import { Request, Response } from 'express';
import { z } from 'zod';
import Decimal from 'decimal.js';
import { RiskCalculationEngine, RiskCalculationParams } from '../services/trading/RiskCalculationEngine';
import { PortfolioRiskAnalyzer } from '../services/trading/PortfolioRiskAnalyzer';
import { RealTimeRiskMonitor } from '../services/trading/RealTimeRiskMonitor';
import { LossLimitEnforcer } from '../services/trading/LossLimitEnforcer';
import { RiskLimitProtection } from '../services/trading/RiskLimitProtection';
import { PortfolioMetricsCalculator } from '../services/trading/PortfolioMetricsCalculator';

// Request validation schemas
const GetRiskMetricsSchema = z.object({
  userId: z.string().uuid(),
  includeHistorical: z.boolean().optional().default(false),
  timeframe: z.enum(['1h', '1d', '1w', '1m']).optional().default('1d'),
});

const CalculatePositionSizeSchema = z.object({
  accountBalance: z.string().regex(/^\d+(\.\d+)?$/),
  riskTolerance: z.enum(['conservative', 'moderate', 'aggressive']),
  marketVolatility: z.number().min(0).max(100),
  correlationRisk: z.number().min(0).max(1).optional(),
  userExperienceLevel: z.enum(['beginner', 'intermediate', 'advanced']),
  maxPositionSize: z.string().regex(/^\d+(\.\d+)?$/).optional(),
});

const UpdateRiskLimitsSchema = z.object({
  dailyLossLimit: z.number().min(0).max(10).optional(),
  weeklyLossLimit: z.number().min(0).max(20).optional(),
  maxPositionSize: z.number().min(0).max(25).optional(),
  requiresApproval: z.boolean().optional().default(true),
});

const SetRiskAlertsSchema = z.object({
  portfolioRiskThreshold: z.number().min(1).max(20),
  dailyLossThreshold: z.number().min(0.5).max(5),
  positionConcentrationThreshold: z.number().min(5).max(50),
  correlationThreshold: z.number().min(0.3).max(0.9),
});

// Response types
interface RiskMetricsResponse {
  data: {
    currentRiskExposure: string;
    portfolioVaR: {
      daily95: string;
      daily99: string;
      weekly95: string;
      weekly99: string;
    };
    riskBudgetUtilization: number;
    diversificationScore: number;
    riskGrade: 'A' | 'B' | 'C' | 'D' | 'F';
    alerts: Array<{
      level: 'info' | 'warning' | 'critical';
      message: string;
      timestamp: string;
    }>;
    correlationMatrix?: Record<string, Record<string, number>>;
    concentrationRisk: {
      score: number;
      largestPositionWeight: number;
      recommendations: string[];
    };
  };
  meta: {
    lastCalculated: string;
    calculationTime: number;
  };
}

interface PortfolioMetricsResponse {
  data: {
    performance: {
      totalReturn: number;
      annualizedReturn: number;
      sharpeRatio: number;
      sortinoRatio: number;
      maxDrawdown: number;
      winRate: number;
    };
    risk: {
      volatility: number;
      beta: number;
      valueAtRisk: string;
      riskScore: number;
    };
    explanation: {
      performance: string;
      risk: string;
      recommendations: string[];
    };
  };
}

export class RiskManagementController {
  private riskCalculationEngine: RiskCalculationEngine;
  private portfolioRiskAnalyzer: PortfolioRiskAnalyzer;
  private riskMonitor: RealTimeRiskMonitor;
  private lossLimitEnforcer: LossLimitEnforcer;
  private riskLimitProtection: RiskLimitProtection;
  private portfolioMetricsCalculator: PortfolioMetricsCalculator;

  constructor() {
    this.riskCalculationEngine = new RiskCalculationEngine();
    this.portfolioRiskAnalyzer = new PortfolioRiskAnalyzer();
    this.riskMonitor = new RealTimeRiskMonitor();
    this.lossLimitEnforcer = new LossLimitEnforcer();
    this.riskLimitProtection = new RiskLimitProtection();
    this.portfolioMetricsCalculator = new PortfolioMetricsCalculator();
  }

  /**
   * GET /api/risk/metrics
   * Retrieve current risk metrics for a user's portfolio
   */
  public async getRiskMetrics(req: Request, res: Response): Promise<void> {
    try {
      const startTime = Date.now();
      const query = GetRiskMetricsSchema.parse(req.query);
      const userId = query.userId || req.user?.id;

      // Validate user authorization
      if (req.user?.id !== userId && !req.user?.isAdmin) {
        res.status(403).json({
          error: {
            code: 'FORBIDDEN',
            message: 'Access denied to risk metrics',
            timestamp: new Date().toISOString(),
          },
        });
        return;
      }

      // Get real-time risk metrics
      const riskScore = await this.riskMonitor.calculateRiskScore(userId);
      const portfolioRisk = await this.portfolioRiskAnalyzer.analyzePortfolioRisk(userId);
      const alerts = await this.riskMonitor.getActiveAlerts(userId);

      // Calculate risk grade
      const riskGrade = this.calculateRiskGrade(riskScore.overallScore);

      const response: RiskMetricsResponse = {
        data: {
          currentRiskExposure: portfolioRisk.totalExposure.toString(),
          portfolioVaR: {
            daily95: portfolioRisk.valueAtRisk.daily.confidence95.toString(),
            daily99: portfolioRisk.valueAtRisk.daily.confidence99.toString(),
            weekly95: portfolioRisk.valueAtRisk.weekly.confidence95.toString(),
            weekly99: portfolioRisk.valueAtRisk.weekly.confidence99.toString(),
          },
          riskBudgetUtilization: riskScore.riskBudgetUtilization,
          diversificationScore: portfolioRisk.concentrationRisk.diversificationIndex,
          riskGrade,
          alerts: alerts.map(alert => ({
            level: alert.severity as 'info' | 'warning' | 'critical',
            message: alert.message,
            timestamp: alert.timestamp.toISOString(),
          })),
          correlationMatrix: query.includeHistorical ? 
            portfolioRisk.correlationRisk.correlationMatrix : undefined,
          concentrationRisk: {
            score: portfolioRisk.concentrationRisk.concentrationScore,
            largestPositionWeight: portfolioRisk.concentrationRisk.largestPosition.toNumber(),
            recommendations: this.getConcentrationRecommendations(portfolioRisk.concentrationRisk.concentrationScore),
          },
        },
        meta: {
          lastCalculated: portfolioRisk.lastCalculated.toISOString(),
          calculationTime: Date.now() - startTime,
        },
      };

      res.status(200).json(response);
    } catch (error) {
      this.handleError(res, error, 'Failed to retrieve risk metrics');
    }
  }

  /**
   * POST /api/risk/calculate-position-size
   * Calculate recommended position size based on risk parameters
   */
  public async calculatePositionSize(req: Request, res: Response): Promise<void> {
    try {
      const body = CalculatePositionSizeSchema.parse(req.body);
      
      const params: RiskCalculationParams = {
        accountBalance: new Decimal(body.accountBalance),
        riskTolerance: body.riskTolerance,
        marketVolatility: body.marketVolatility,
        correlationRisk: body.correlationRisk,
        userExperienceLevel: body.userExperienceLevel,
        maxPositionSize: body.maxPositionSize ? new Decimal(body.maxPositionSize) : undefined,
      };

      const result = await this.riskCalculationEngine.calculateOptimalPositionSize(params);

      res.status(200).json({
        data: {
          recommendedSize: result.recommendedSize.toString(),
          maxAllowedSize: result.maxAllowedSize.toString(),
          riskPercentage: result.riskPercentage,
          kellyRecommendation: result.kellyRecommendation.toString(),
          adjustments: {
            volatility: result.volatilityAdjustment,
            correlation: result.correlationAdjustment,
            experience: result.experienceAdjustment,
          },
          reasoning: result.reasoning,
        },
        status: 'success',
        message: 'Position size calculated successfully',
      });
    } catch (error) {
      this.handleError(res, error, 'Failed to calculate position size');
    }
  }

  /**
   * GET /api/risk/portfolio-metrics
   * Get comprehensive portfolio performance and risk metrics
   */
  public async getPortfolioMetrics(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          error: {
            code: 'UNAUTHORIZED',
            message: 'User authentication required',
            timestamp: new Date().toISOString(),
          },
        });
        return;
      }

      const metrics = await this.portfolioMetricsCalculator.calculatePortfolioMetrics(userId);

      const response: PortfolioMetricsResponse = {
        data: {
          performance: {
            totalReturn: metrics.totalReturn,
            annualizedReturn: metrics.annualizedReturn,
            sharpeRatio: metrics.sharpeRatio,
            sortinoRatio: metrics.sortinoRatio,
            maxDrawdown: metrics.maxDrawdown,
            winRate: metrics.winRate,
          },
          risk: {
            volatility: metrics.volatility,
            beta: metrics.beta,
            valueAtRisk: `${(metrics.volatility * 1.96).toFixed(2)}%`, // 95% confidence
            riskScore: this.calculateRiskScoreFromMetrics(metrics),
          },
          explanation: metrics.explanation,
        },
      };

      res.status(200).json(response);
    } catch (error) {
      this.handleError(res, error, 'Failed to retrieve portfolio metrics');
    }
  }

  /**
   * GET /api/risk/limits
   * Get current risk limits for a user
   */
  public async getRiskLimits(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          error: {
            code: 'UNAUTHORIZED',
            message: 'User authentication required',
            timestamp: new Date().toISOString(),
          },
        });
        return;
      }

      const limits = await this.lossLimitEnforcer.getUserLossData(userId);
      const protectedParams = await this.riskLimitProtection.getAllParameters(userId);

      res.status(200).json({
        data: {
          dailyLossLimit: limits?.dailyLimitAmount?.toString() || '0',
          weeklyLossLimit: limits?.weeklyLimitAmount?.toString() || '0',
          currentDailyLoss: limits?.currentDayLoss?.toString() || '0',
          currentWeeklyLoss: limits?.currentWeekLoss?.toString() || '0',
          isLocked: limits?.isLocked || false,
          lockoutReason: limits?.lockoutReason,
          protectedParameters: protectedParams,
          canModifyLimits: true, // Simplified for now
        },
        status: 'success',
      });
    } catch (error) {
      this.handleError(res, error, 'Failed to retrieve risk limits');
    }
  }

  /**
   * PUT /api/risk/limits
   * Update risk limits (requires approval for critical changes)
   */
  public async updateRiskLimits(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          error: {
            code: 'UNAUTHORIZED',
            message: 'User authentication required',
            timestamp: new Date().toISOString(),
          },
        });
        return;
      }

      const body = UpdateRiskLimitsSchema.parse(req.body);

      // Check if modifications are allowed - simplified for now
      const canModify = true;

      if (!canModify && !req.user?.isAdmin) {
        res.status(403).json({
          error: {
            code: 'FORBIDDEN',
            message: 'Risk limit modifications are currently protected',
            timestamp: new Date().toISOString(),
          },
        });
        return;
      }

      // For non-admin users, require approval for significant changes
      if (body.requiresApproval && !req.user?.isAdmin) {
        // Store pending changes for admin review - simplified for now
        // In real implementation would use the actual service method

        res.status(202).json({
          status: 'pending_approval',
          message: 'Risk limit changes submitted for admin review',
          data: {
            pendingChanges: body,
            estimatedReviewTime: '24-48 hours',
          },
        });
        return;
      }

      // Apply changes directly (admin or pre-approved)
      // In real implementation, would update the limits through the service
      // For now, just acknowledge the request

      res.status(200).json({
        status: 'success',
        message: 'Risk limits updated successfully',
        data: {
          appliedChanges: body,
          effectiveDate: new Date().toISOString(),
        },
      });
    } catch (error) {
      this.handleError(res, error, 'Failed to update risk limits');
    }
  }

  /**
   * POST /api/risk/alerts
   * Configure risk alert thresholds
   */
  public async setRiskAlerts(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          error: {
            code: 'UNAUTHORIZED',
            message: 'User authentication required',
            timestamp: new Date().toISOString(),
          },
        });
        return;
      }

      const body = SetRiskAlertsSchema.parse(req.body);

      // In real implementation would update alert thresholds
      // Simplified for now

      res.status(200).json({
        status: 'success',
        message: 'Risk alert thresholds updated successfully',
        data: {
          thresholds: body,
          activeAlerts: await this.riskMonitor.getActiveAlerts(userId),
        },
      });
    } catch (error) {
      this.handleError(res, error, 'Failed to update risk alerts');
    }
  }

  /**
   * GET /api/risk/stress-test
   * Run stress test scenarios on current portfolio
   */
  public async runStressTest(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          error: {
            code: 'UNAUTHORIZED',
            message: 'User authentication required',
            timestamp: new Date().toISOString(),
          },
        });
        return;
      }

      const scenarios = [
        { name: 'Market Crash (-20%)', marketMove: -0.20, volatilityIncrease: 2.0 },
        { name: 'Flash Crash (-10%)', marketMove: -0.10, volatilityIncrease: 5.0 },
        { name: 'High Volatility (+50% Vol)', marketMove: 0, volatilityIncrease: 1.5 },
        { name: 'Correlation Breakdown', marketMove: -0.05, correlationChange: 0.9 },
      ];

      const results = [];
      
      for (const scenario of scenarios) {
        const portfolioRisk = await this.portfolioRiskAnalyzer.analyzePortfolioRisk(userId);
        
        // Simulate scenario impact
        const scenarioImpact = {
          potentialLoss: portfolioRisk.totalExposure.mul(Math.abs(scenario.marketMove)),
          newVaR: portfolioRisk.valueAtRisk.daily.confidence95 * scenario.volatilityIncrease,
          riskGrade: this.calculateRiskGrade(85 + (scenario.marketMove * 100)),
        };

        results.push({
          scenario: scenario.name,
          impact: {
            potentialLoss: scenarioImpact.potentialLoss.toString(),
            newVaR: scenarioImpact.newVaR.toFixed(2),
            riskGrade: scenarioImpact.riskGrade,
          },
          recommendation: this.getStressTestRecommendation(scenarioImpact.riskGrade),
        });
      }

      res.status(200).json({
        data: {
          stressTestResults: results,
          currentPortfolioValue: (await this.portfolioRiskAnalyzer.analyzePortfolioRisk(userId)).totalExposure.toString(),
          testDate: new Date().toISOString(),
          recommendations: this.getOverallStressTestRecommendations(results),
        },
        status: 'success',
      });
    } catch (error) {
      this.handleError(res, error, 'Failed to run stress test');
    }
  }

  // Helper methods
  private calculateRiskGrade(riskScore: number): 'A' | 'B' | 'C' | 'D' | 'F' {
    if (riskScore >= 90) return 'A';
    if (riskScore >= 80) return 'B';
    if (riskScore >= 70) return 'C';
    if (riskScore >= 60) return 'D';
    return 'F';
  }

  private calculateRiskScoreFromMetrics(metrics: { sharpeRatio?: number; maxDrawdown?: number; volatility?: number }): number {
    // Simple scoring based on Sharpe ratio, drawdown, and volatility
    const sharpeScore = Math.min(100, Math.max(0, (metrics.sharpeRatio + 1) * 40));
    const drawdownScore = Math.max(0, 100 - (Math.abs(metrics.maxDrawdown) * 2));
    const volatilityScore = Math.max(0, 100 - (metrics.volatility * 3));
    
    return Math.round((sharpeScore + drawdownScore + volatilityScore) / 3);
  }

  private getConcentrationRecommendations(score: number): string[] {
    if (score > 80) {
      return [
        'Portfolio is well diversified',
        'Continue maintaining current position sizing discipline',
      ];
    } else if (score > 60) {
      return [
        'Consider reducing largest positions by 10-20%',
        'Add positions in uncorrelated instruments',
      ];
    } else {
      return [
        'High concentration risk detected',
        'Immediately reduce largest positions',
        'Diversify across more instruments and sectors',
        'Review position sizing strategy',
      ];
    }
  }

  private getStressTestRecommendation(riskGrade: 'A' | 'B' | 'C' | 'D' | 'F'): string {
    switch (riskGrade) {
      case 'A': return 'Portfolio handles stress well - maintain current strategy';
      case 'B': return 'Minor adjustments needed - consider hedging key positions';
      case 'C': return 'Moderate risk - reduce position sizes by 20%';
      case 'D': return 'High risk - significant position reduction recommended';
      case 'F': return 'Critical risk - immediate portfolio rebalancing required';
      default: return 'Unknown risk level';
    }
  }

  private getOverallStressTestRecommendations(results: Array<{ impact: { riskGrade: 'A' | 'B' | 'C' | 'D' | 'F' } }>): string[] {
    const avgRisk = results.length > 0 ? 
      results.reduce((sum, r) => sum + this.riskGradeToNumber(r.impact.riskGrade), 0) / results.length : 85;
    
    const recommendations = [];
    
    if (avgRisk < 70) {
      recommendations.push('Consider reducing overall portfolio risk');
      recommendations.push('Implement stronger hedging strategies');
    } else if (avgRisk < 80) {
      recommendations.push('Monitor portfolio closely during volatile periods');
      recommendations.push('Consider partial hedge for largest positions');
    } else {
      recommendations.push('Portfolio shows good stress resilience');
      recommendations.push('Continue current risk management approach');
    }
    
    return recommendations;
  }

  private riskGradeToNumber(grade: 'A' | 'B' | 'C' | 'D' | 'F'): number {
    switch (grade) {
      case 'A': return 95;
      case 'B': return 85;
      case 'C': return 75;
      case 'D': return 65;
      case 'F': return 50;
      default: return 50;
    }
  }

  private handleError(res: Response, error: Error | unknown, defaultMessage: string): void {
    console.error('Risk Management Controller Error:', error);
    
    const statusCode = (error && typeof error === 'object' && 'statusCode' in error) ? (error as { statusCode?: number }).statusCode || 500 : 500;
    const message = error instanceof Error ? error.message : defaultMessage;
    
    res.status(statusCode).json({
      error: {
        code: (error && typeof error === 'object' && 'code' in error) ? (error as { code?: string }).code || 'INTERNAL_ERROR' : 'INTERNAL_ERROR',
        message,
        timestamp: new Date().toISOString(),
        requestId: res.locals.requestId || 'unknown',
      },
    });
  }
}

// Export singleton instance
export default new RiskManagementController();