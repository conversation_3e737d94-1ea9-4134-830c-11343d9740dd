import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { supabase } from '@golddaddy/config/src/database';
import { userOperations } from '../../lib/database';
import { auditLogger } from '../../lib/audit';
import { validateEmail, validatePassword } from '../../lib/auth';

const router = Router();

// Registration request validation schema
const RegisterSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  profile: z.object({
    displayName: z.string().min(1, 'Display name is required'),
    riskTolerance: z.enum(['conservative', 'moderate', 'aggressive']).default('moderate'),
    experienceLevel: z.enum(['beginner', 'intermediate', 'advanced']).default('beginner'),
    coachingStyle: z.enum(['guided', 'independent', 'assisted']).default('guided'),
    tradingCapital: z.number().positive().optional(),
  }),
});

// POST /api/auth/register - User registration
router.post('/register', async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = RegisterSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid registration data',
          details: validationResult.error.errors,
          timestamp: new Date().toISOString(),
        },
      });
    }

    const { email, password, profile } = validationResult.data;

    // Additional email validation
    if (!validateEmail(email)) {
      return res.status(400).json({
        error: {
          code: 'INVALID_EMAIL',
          message: 'Invalid email format',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Password strength validation
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.valid) {
      return res.status(400).json({
        error: {
          code: 'WEAK_PASSWORD',
          message: 'Password does not meet security requirements',
          details: passwordValidation.errors,
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Register user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          display_name: profile.displayName,
          risk_tolerance: profile.riskTolerance,
          experience_level: profile.experienceLevel,
          coaching_style: profile.coachingStyle,
        },
      },
    });

    if (authError) {
      console.error('Supabase registration error:', authError);
      return res.status(400).json({
        error: {
          code: 'REGISTRATION_FAILED',
          message: authError.message,
          timestamp: new Date().toISOString(),
        },
      });
    }

    if (!authData.user) {
      return res.status(400).json({
        error: {
          code: 'REGISTRATION_FAILED',
          message: 'User registration failed',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Create user profile in our database
    try {
      const userProfile = await userOperations.create({
        id: authData.user.id,
        email: authData.user.email || '',
        displayName: profile.displayName,
        riskTolerance: profile.riskTolerance,
        experienceLevel: profile.experienceLevel,
        coachingStyle: profile.coachingStyle,
        tradingCapital: profile.tradingCapital,
      });

      // Log registration event
      await auditLogger.logUserRegistration(authData.user.id, req, {
        email: authData.user.email,
        displayName: profile.displayName,
        experienceLevel: profile.experienceLevel,
      });

      // Return success response
      res.status(201).json({
        data: {
          user: {
            id: userProfile.id,
            email: userProfile.email,
            displayName: userProfile.displayName,
            experienceLevel: userProfile.experienceLevel,
            riskTolerance: userProfile.riskTolerance,
          },
          session: authData.session,
          emailConfirmation: !authData.session ? 'Email confirmation required' : null,
        },
        status: 'success',
        message: 'User registered successfully',
        timestamp: new Date().toISOString(),
      });

    } catch (dbError) {
      console.error('Database user creation error:', dbError);
      
      // If database creation fails, we should clean up the Supabase auth user
      // For now, we'll log the error and return failure
      return res.status(500).json({
        error: {
          code: 'PROFILE_CREATION_FAILED',
          message: 'Failed to create user profile',
          timestamp: new Date().toISOString(),
        },
      });
    }

  } catch (error) {
    console.error('Registration error:', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error during registration',
        timestamp: new Date().toISOString(),
      },
    });
  }
});

export default router;