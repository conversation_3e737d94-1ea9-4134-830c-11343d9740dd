import { PrismaClient, Strategy, Timeframe, MarketRegime } from '@prisma/client';
import { BacktestingDataService, HistoricalDataRequest, HistoricalDataPoint } from './BacktestingDataService';
import { 
  BacktestingExecutionEngine, 
  BacktestTradeOrder, 
  BacktestExecutionResult,
  ExecutionConfig 
} from './BacktestingExecutionEngine';

/**
 * Backtesting request parameters
 */
export interface BacktestRequest {
  strategyId: string;
  instruments: string[];
  timeframes: Timeframe[];
  startDate: Date;
  endDate: Date;
  initialCapital: number;
  userId: string;
  
  // Configuration options
  executionConfig?: Partial<ExecutionConfig>;
  includeRegimeAnalysis?: boolean;
  includeDrawdownAnalysis?: boolean;
  maxConcurrentPositions?: number;
  
  // Enhancement options
  includeMLInsights?: boolean;
  generateCoachingAdvice?: boolean;
}

/**
 * Performance metrics for backtesting
 */
export interface PerformanceMetrics {
  totalReturn: number;
  totalReturnPercentage: number;
  annualizedReturn: number;
  sharpeRatio: number;
  maxDrawdown: number;
  maxDrawdownPercentage: number;
  winRate: number;
  profitFactor: number;
  averageWin: number;
  averageLoss: number;
  largestWin: number;
  largestLoss: number;
  totalTrades: number;
  winningTrades: number;
  losingTrades: number;
  averageTradeReturn: number;
  
  // Risk metrics
  volatility: number;
  downsideDeviation: number;
  calmarRatio: number;
  sortinoRatio: number;
  
  // Trade timing metrics
  averageHoldingPeriod: number; // in hours
  averageTimeBetweenTrades: number; // in hours
}

/**
 * Multi-timeframe performance analysis
 */
export interface TimeframePerformance {
  timeframe: Timeframe;
  metrics: PerformanceMetrics;
  trades: BacktestTrade[];
  equityCurve: EquityPoint[];
}

/**
 * Market regime performance breakdown
 */
export interface RegimePerformance {
  regime: MarketRegime;
  duration: number; // hours in this regime
  durationPercentage: number;
  metrics: PerformanceMetrics;
  trades: BacktestTrade[];
}

/**
 * Backtest trade result
 */
export interface BacktestTrade {
  id: string;
  instrument: string;
  type: 'BUY' | 'SELL';
  quantity: number;
  entryTime: Date;
  exitTime: Date;
  entryPrice: number;
  exitPrice: number;
  stopLoss?: number;
  takeProfit?: number;
  pnl: number;
  pnlPercentage: number;
  commission: number;
  slippage: number;
  holdingPeriod: number; // in hours
  marketRegime?: MarketRegime;
  executionDetails: {
    entryExecution: BacktestExecutionResult;
    exitExecution: BacktestExecutionResult;
  };
}

/**
 * Equity curve point
 */
export interface EquityPoint {
  timestamp: Date;
  equity: number;
  drawdown: number;
  drawdownPercentage: number;
  numberOfPositions: number;
}

/**
 * Drawdown analysis result
 */
export interface DrawdownAnalysis {
  maxDrawdown: number;
  maxDrawdownPercentage: number;
  maxDrawdownDuration: number; // in hours
  drawdownPeriods: Array<{
    start: Date;
    end: Date;
    peak: number;
    trough: number;
    drawdown: number;
    drawdownPercentage: number;
    duration: number; // in hours
    recovery: Date | null;
  }>;
  averageDrawdown: number;
  averageDrawdownDuration: number;
}

/**
 * Complete backtesting result
 */
export interface BacktestResult {
  id: string;
  strategyId: string;
  userId: string;
  startDate: Date;
  endDate: Date;
  initialCapital: number;
  finalCapital: number;
  
  // Overall performance
  overallMetrics: PerformanceMetrics;
  equityCurve: EquityPoint[];
  trades: BacktestTrade[];
  
  // Multi-timeframe analysis
  timeframeAnalysis: TimeframePerformance[];
  
  // Market regime analysis
  regimeAnalysis?: RegimePerformance[];
  
  // Drawdown analysis
  drawdownAnalysis: DrawdownAnalysis;
  
  // Execution statistics
  executionStats: {
    totalOrders: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageSlippage: number;
    averageSpread: number;
    totalCommission: number;
    totalExecutionCost: number;
  };
  
  // Enhanced insights
  mlInsights?: string[];
  coachingAdvice?: string[];
  
  // Metadata
  backtestDuration: number; // in milliseconds
  dataPoints: number;
  createdAt: Date;
}

/**
 * Comprehensive backtesting service with multi-timeframe and regime analysis
 */
export class ComprehensiveBacktestService {
  private readonly prisma: PrismaClient;
  private readonly dataService: BacktestingDataService;
  private readonly executionEngine: BacktestingExecutionEngine;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.dataService = new BacktestingDataService(prisma);
    this.executionEngine = new BacktestingExecutionEngine();
  }

  /**
   * Run comprehensive backtesting analysis
   */
  async runBacktest(request: BacktestRequest): Promise<BacktestResult> {
    const startTime = Date.now();
    
    try {
      // Update execution configuration if provided
      if (request.executionConfig) {
        this.executionEngine.updateConfig(request.executionConfig);
      }

      // Get strategy details
      const strategy = await this.getStrategy(request.strategyId);
      
      // Collect historical data for all instruments and timeframes
      const historicalData = await this.collectHistoricalData(request);
      
      // Run backtesting for each timeframe
      const timeframeResults: TimeframePerformance[] = [];
      
      for (const timeframe of request.timeframes) {
        const timeframeData = historicalData.filter(d => d.timeframe === timeframe);
        const performance = await this.runTimeframeBacktest(
          strategy,
          timeframeData,
          request
        );
        timeframeResults.push(performance);
      }

      // Combine results and calculate overall performance
      const overallMetrics = this.calculateOverallMetrics(timeframeResults);
      const allTrades = timeframeResults.flatMap(tf => tf.trades);
      const equityCurve = this.calculateEquityCurve(allTrades, request.initialCapital);
      
      // Analyze market regimes if requested
      let regimeAnalysis: RegimePerformance[] | undefined;
      if (request.includeRegimeAnalysis) {
        regimeAnalysis = await this.analyzeRegimePerformance(allTrades, request);
      }
      
      // Calculate drawdown analysis
      const drawdownAnalysis = this.calculateDrawdownAnalysis(equityCurve);
      
      // Calculate execution statistics
      const executionStats = this.calculateExecutionStats(allTrades);
      
      // Generate enhanced insights if requested
      const mlInsights = request.includeMLInsights 
        ? this.generateMLInsights(overallMetrics, regimeAnalysis)
        : undefined;
        
      const coachingAdvice = request.generateCoachingAdvice
        ? this.generateCoachingAdvice(overallMetrics, drawdownAnalysis)
        : undefined;

      const result: BacktestResult = {
        id: this.generateBacktestId(),
        strategyId: request.strategyId,
        userId: request.userId,
        startDate: request.startDate,
        endDate: request.endDate,
        initialCapital: request.initialCapital,
        finalCapital: request.initialCapital + overallMetrics.totalReturn,
        overallMetrics,
        equityCurve,
        trades: allTrades,
        timeframeAnalysis: timeframeResults,
        regimeAnalysis,
        drawdownAnalysis,
        executionStats,
        mlInsights,
        coachingAdvice,
        backtestDuration: Date.now() - startTime,
        dataPoints: historicalData.length,
        createdAt: new Date(),
      };

      return result;
    } catch (error) {
      throw new Error(`Backtesting failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Collect historical data for all requested instruments and timeframes
   */
  private async collectHistoricalData(request: BacktestRequest): Promise<HistoricalDataPoint[]> {
    const allData: HistoricalDataPoint[] = [];
    
    for (const instrument of request.instruments) {
      for (const timeframe of request.timeframes) {
        const dataRequest: HistoricalDataRequest = {
          instrument,
          timeframe,
          startDate: request.startDate,
          endDate: request.endDate,
          includeIndicators: true,
          includeRegimeData: request.includeRegimeAnalysis,
        };
        
        const data = await this.dataService.getHistoricalDataWithRegimes(dataRequest);
        allData.push(...data);
      }
    }
    
    return allData.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  /**
   * Run backtest for a specific timeframe
   */
  private async runTimeframeBacktest(
    strategy: Strategy,
    data: HistoricalDataPoint[],
    request: BacktestRequest
  ): Promise<TimeframePerformance> {
    if (data.length === 0) {
      throw new Error('No historical data available for backtesting');
    }

    const timeframe = data[0].timeframe;
    const trades: BacktestTrade[] = [];
    const equityPoints: EquityPoint[] = [];
    
    let currentEquity = request.initialCapital;
    const openPositions: Map<string, BacktestTrade> = new Map();
    
    // Generate trading signals and execute trades
    for (let i = 1; i < data.length; i++) {
      const currentCandle = data[i];
      const previousCandles = data.slice(Math.max(0, i - 50), i); // Last 50 candles for analysis
      
      // Generate trading signals based on strategy
      const signals = this.generateTradingSignals(strategy, previousCandles, currentCandle);
      
      // Process signals and create orders
      for (const signal of signals) {
        // Check position limits
        if (openPositions.size >= (request.maxConcurrentPositions || 5)) {
          continue;
        }
        
        const order: BacktestTradeOrder = {
          id: this.generateOrderId(),
          type: signal.type,
          instrument: currentCandle.instrument,
          quantity: signal.quantity,
          requestedPrice: currentCandle.close,
          stopLoss: signal.stopLoss,
          takeProfit: signal.takeProfit,
          timestamp: currentCandle.timestamp,
          marketConditions: {
            regime: currentCandle.marketRegime || MarketRegime.ANY,
            volatility: this.calculateVolatility(previousCandles),
            spread: 0.8, // Will be calculated by execution engine
            liquidityScore: 0.8,
          },
        };
        
        // Execute order
        const execution = await this.executionEngine.executeOrder(order, currentCandle);
        
        if (execution.success) {
          const trade: BacktestTrade = {
            id: order.id,
            instrument: order.instrument,
            type: order.type,
            quantity: order.quantity,
            entryTime: execution.executionTime,
            exitTime: new Date(), // Will be updated when position closes
            entryPrice: execution.executedPrice,
            exitPrice: 0, // Will be updated when position closes
            stopLoss: order.stopLoss,
            takeProfit: order.takeProfit,
            pnl: 0,
            pnlPercentage: 0,
            commission: execution.commission,
            slippage: execution.slippage,
            holdingPeriod: 0,
            marketRegime: currentCandle.marketRegime,
            executionDetails: {
              entryExecution: execution,
              exitExecution: execution, // Will be updated
            },
          };
          
          openPositions.set(trade.id, trade);
        }
      }
      
      // Check for position exits (stop loss, take profit, or strategy exit signals)
      await this.processPositionExits(openPositions, currentCandle, trades);
      
      // Update equity curve
      const unrealizedPnL = this.calculateUnrealizedPnL(openPositions, currentCandle);
      const realizedPnL = trades.reduce((sum, trade) => sum + trade.pnl, 0);
      currentEquity = request.initialCapital + realizedPnL + unrealizedPnL;
      
      equityPoints.push({
        timestamp: currentCandle.timestamp,
        equity: currentEquity,
        drawdown: Math.max(0, Math.max(...equityPoints.map(p => p.equity), request.initialCapital) - currentEquity),
        drawdownPercentage: 0, // Will be calculated later
        numberOfPositions: openPositions.size,
      });
    }
    
    // Close any remaining open positions
    if (openPositions.size > 0) {
      const lastCandle = data[data.length - 1];
      await this.closeAllPositions(openPositions, lastCandle, trades);
    }
    
    // Calculate drawdown percentages
    this.calculateDrawdownPercentages(equityPoints);
    
    // Calculate performance metrics
    const metrics = this.calculatePerformanceMetrics(trades, request.initialCapital, equityPoints);
    
    return {
      timeframe,
      metrics,
      trades,
      equityCurve: equityPoints,
    };
  }

  /**
   * Generate trading signals based on strategy parameters
   */
  private generateTradingSignals(
    strategy: Strategy,
    historicalData: HistoricalDataPoint[],
    currentCandle: HistoricalDataPoint
  ): Array<{
    type: 'BUY' | 'SELL';
    quantity: number;
    stopLoss?: number;
    takeProfit?: number;
  }> {
    const signals: Array<{
      type: 'BUY' | 'SELL';
      quantity: number;
      stopLoss?: number;
      takeProfit?: number;
    }> = [];
    
    if (historicalData.length < 20) return signals; // Need enough data for indicators
    
    const currentPrice = currentCandle.close;
    const sma20 = currentCandle.indicators?.sma_20;
    const sma50 = currentCandle.indicators?.sma_50;
    const rsi = currentCandle.indicators?.rsi_14;
    
    // Basic momentum strategy implementation
    if (strategy.type === 'MOMENTUM' && sma20 && sma50 && rsi) {
      // Buy signal: Price above SMA20, SMA20 above SMA50, RSI not overbought
      if (currentPrice > sma20 && sma20 > sma50 && rsi < 70) {
        signals.push({
          type: 'BUY',
          quantity: 10000, // Standard lot size
          stopLoss: currentPrice * (1 - strategy.stopLoss.toNumber() / 100),
          takeProfit: currentPrice * (1 + strategy.takeProfit.toNumber() / 100),
        });
      }
      
      // Sell signal: Price below SMA20, SMA20 below SMA50, RSI not oversold
      if (currentPrice < sma20 && sma20 < sma50 && rsi > 30) {
        signals.push({
          type: 'SELL',
          quantity: 10000,
          stopLoss: currentPrice * (1 + strategy.stopLoss.toNumber() / 100),
          takeProfit: currentPrice * (1 - strategy.takeProfit.toNumber() / 100),
        });
      }
    }
    
    // Additional strategy types can be implemented here
    
    return signals;
  }

  /**
   * Process position exits based on stop loss, take profit, or strategy signals
   */
  private async processPositionExits(
    openPositions: Map<string, BacktestTrade>,
    currentCandle: HistoricalDataPoint,
    completedTrades: BacktestTrade[]
  ): Promise<void> {
    const positionsToClose: string[] = [];
    
    for (const [positionId, position] of openPositions.entries()) {
      let shouldClose = false;
      let exitReason = '';
      
      const currentPrice = currentCandle.close;
      
      // Check stop loss
      if (position.stopLoss) {
        if (
          (position.type === 'BUY' && currentPrice <= position.stopLoss) ||
          (position.type === 'SELL' && currentPrice >= position.stopLoss)
        ) {
          shouldClose = true;
          exitReason = 'Stop Loss';
        }
      }
      
      // Check take profit
      if (!shouldClose && position.takeProfit) {
        if (
          (position.type === 'BUY' && currentPrice >= position.takeProfit) ||
          (position.type === 'SELL' && currentPrice <= position.takeProfit)
        ) {
          shouldClose = true;
          exitReason = 'Take Profit';
          console.log(`Position closed: ${exitReason}`);
        }
      }
      
      if (shouldClose) {
        // Create exit order
        const exitOrder: BacktestTradeOrder = {
          id: this.generateOrderId(),
          type: position.type === 'BUY' ? 'SELL' : 'BUY',
          instrument: position.instrument,
          quantity: position.quantity,
          requestedPrice: currentPrice,
          timestamp: currentCandle.timestamp,
        };
        
        // Execute exit
        const exitExecution = await this.executionEngine.executeOrder(exitOrder, currentCandle);
        
        if (exitExecution.success) {
          // Update position with exit details
          position.exitTime = exitExecution.executionTime;
          position.exitPrice = exitExecution.executedPrice;
          position.holdingPeriod = (position.exitTime.getTime() - position.entryTime.getTime()) / (1000 * 60 * 60); // hours
          
          // Calculate P&L
          if (position.type === 'BUY') {
            position.pnl = (position.exitPrice - position.entryPrice) * position.quantity - position.commission - exitExecution.commission;
          } else {
            position.pnl = (position.entryPrice - position.exitPrice) * position.quantity - position.commission - exitExecution.commission;
          }
          
          position.pnlPercentage = (position.pnl / (position.entryPrice * position.quantity)) * 100;
          position.executionDetails.exitExecution = exitExecution;
          
          // Move to completed trades
          completedTrades.push(position);
          positionsToClose.push(positionId);
        }
      }
    }
    
    // Remove closed positions
    positionsToClose.forEach(id => openPositions.delete(id));
  }

  /**
   * Calculate unrealized P&L for open positions
   */
  private calculateUnrealizedPnL(openPositions: Map<string, BacktestTrade>, currentCandle: HistoricalDataPoint): number {
    let unrealizedPnL = 0;
    
    for (const position of openPositions.values()) {
      if (position.type === 'BUY') {
        unrealizedPnL += (currentCandle.close - position.entryPrice) * position.quantity;
      } else {
        unrealizedPnL += (position.entryPrice - currentCandle.close) * position.quantity;
      }
    }
    
    return unrealizedPnL;
  }

  /**
   * Close all remaining open positions
   */
  private async closeAllPositions(
    openPositions: Map<string, BacktestTrade>,
    lastCandle: HistoricalDataPoint,
    completedTrades: BacktestTrade[]
  ): Promise<void> {
    for (const position of openPositions.values()) {
      const exitOrder: BacktestTradeOrder = {
        id: this.generateOrderId(),
        type: position.type === 'BUY' ? 'SELL' : 'BUY',
        instrument: position.instrument,
        quantity: position.quantity,
        requestedPrice: lastCandle.close,
        timestamp: lastCandle.timestamp,
      };
      
      const exitExecution = await this.executionEngine.executeOrder(exitOrder, lastCandle);
      
      // Update position (similar to processPositionExits)
      position.exitTime = exitExecution.executionTime;
      position.exitPrice = exitExecution.success ? exitExecution.executedPrice : lastCandle.close;
      position.holdingPeriod = (position.exitTime.getTime() - position.entryTime.getTime()) / (1000 * 60 * 60);
      
      if (position.type === 'BUY') {
        position.pnl = (position.exitPrice - position.entryPrice) * position.quantity - position.commission;
      } else {
        position.pnl = (position.entryPrice - position.exitPrice) * position.quantity - position.commission;
      }
      
      position.pnlPercentage = (position.pnl / (position.entryPrice * position.quantity)) * 100;
      position.executionDetails.exitExecution = exitExecution;
      
      completedTrades.push(position);
    }
    
    openPositions.clear();
  }

  /**
   * Calculate volatility from historical data
   */
  private calculateVolatility(data: HistoricalDataPoint[]): number {
    if (data.length < 2) return 0;
    
    const returns = data.slice(1).map((candle, i) => 
      Math.log(candle.close / data[i].close)
    );
    
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    
    return Math.sqrt(variance * 252); // Annualized volatility
  }

  /**
   * Calculate drawdown percentages for equity curve
   */
  private calculateDrawdownPercentages(equityPoints: EquityPoint[]): void {
    let runningMax = 0;
    
    for (const point of equityPoints) {
      runningMax = Math.max(runningMax, point.equity);
      point.drawdownPercentage = runningMax > 0 ? ((runningMax - point.equity) / runningMax) * 100 : 0;
    }
  }

  /**
   * Calculate performance metrics from trades and equity curve
   */
  private calculatePerformanceMetrics(
    trades: BacktestTrade[],
    initialCapital: number,
    equityCurve: EquityPoint[]
  ): PerformanceMetrics {
    if (trades.length === 0) {
      return this.getEmptyMetrics();
    }

    const totalReturn = trades.reduce((sum, trade) => sum + trade.pnl, 0);
    const totalReturnPercentage = (totalReturn / initialCapital) * 100;
    
    const winningTrades = trades.filter(trade => trade.pnl > 0);
    const losingTrades = trades.filter(trade => trade.pnl < 0);
    
    const winRate = (winningTrades.length / trades.length) * 100;
    
    const totalWins = winningTrades.reduce((sum, trade) => sum + trade.pnl, 0);
    const totalLosses = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.pnl, 0));
    
    const profitFactor = totalLosses > 0 ? totalWins / totalLosses : totalWins > 0 ? 999 : 1;
    
    const averageWin = winningTrades.length > 0 ? totalWins / winningTrades.length : 0;
    const averageLoss = losingTrades.length > 0 ? totalLosses / losingTrades.length : 0;
    
    const largestWin = winningTrades.length > 0 ? Math.max(...winningTrades.map(t => t.pnl)) : 0;
    const largestLoss = losingTrades.length > 0 ? Math.min(...losingTrades.map(t => t.pnl)) : 0;
    
    // Calculate time-based metrics
    const firstTrade = trades[0];
    const lastTrade = trades[trades.length - 1];
    const totalDays = (lastTrade.exitTime.getTime() - firstTrade.entryTime.getTime()) / (1000 * 60 * 60 * 24);
    const annualizedReturn = totalDays > 0 ? (totalReturnPercentage * 365) / totalDays : 0;
    
    // Calculate volatility from equity curve
    const returns = equityCurve.slice(1).map((point, i) => 
      (point.equity - equityCurve[i].equity) / equityCurve[i].equity
    );
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const volatility = Math.sqrt(
      returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length
    ) * Math.sqrt(252) * 100; // Annualized volatility percentage
    
    const sharpeRatio = volatility > 0 ? annualizedReturn / volatility : 0;
    
    // Drawdown metrics
    const maxDrawdown = Math.max(...equityCurve.map(p => p.drawdown));
    const maxDrawdownPercentage = Math.max(...equityCurve.map(p => p.drawdownPercentage));
    
    // Calculate additional metrics
    const averageHoldingPeriod = trades.reduce((sum, trade) => sum + trade.holdingPeriod, 0) / trades.length;
    const averageTradeReturn = totalReturn / trades.length;
    
    // Risk metrics
    const negativeReturns = returns.filter(ret => ret < 0);
    const downsideDeviation = negativeReturns.length > 0 
      ? Math.sqrt(negativeReturns.reduce((sum, ret) => sum + ret * ret, 0) / negativeReturns.length) * Math.sqrt(252) * 100
      : 0;
    
    const calmarRatio = maxDrawdownPercentage > 0 ? annualizedReturn / maxDrawdownPercentage : 0;
    const sortinoRatio = downsideDeviation > 0 ? annualizedReturn / downsideDeviation : 0;
    
    return {
      totalReturn,
      totalReturnPercentage,
      annualizedReturn,
      sharpeRatio,
      maxDrawdown,
      maxDrawdownPercentage,
      winRate,
      profitFactor,
      averageWin,
      averageLoss,
      largestWin,
      largestLoss,
      totalTrades: trades.length,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      averageTradeReturn,
      volatility,
      downsideDeviation,
      calmarRatio,
      sortinoRatio,
      averageHoldingPeriod,
      averageTimeBetweenTrades: totalDays > 0 ? (totalDays * 24) / trades.length : 0,
    };
  }

  /**
   * Get strategy from database
   */
  private async getStrategy(strategyId: string): Promise<Strategy> {
    const strategy = await this.prisma.strategy.findUnique({
      where: { id: strategyId },
    });
    
    if (!strategy) {
      throw new Error(`Strategy not found: ${strategyId}`);
    }
    
    return strategy;
  }

  /**
   * Calculate overall metrics by combining timeframe results
   */
  private calculateOverallMetrics(timeframeResults: TimeframePerformance[]): PerformanceMetrics {
    if (timeframeResults.length === 0) {
      return this.getEmptyMetrics();
    }

    // Use the primary timeframe (first one) as the base
    // In a more sophisticated implementation, you might weight by timeframe or combine differently
    return timeframeResults[0].metrics;
  }

  /**
   * Calculate equity curve from trades
   */
  private calculateEquityCurve(trades: BacktestTrade[], initialCapital: number): EquityPoint[] {
    const points: EquityPoint[] = [];
    let runningEquity = initialCapital;
    let runningMax = initialCapital;
    
    // Sort trades by exit time
    const sortedTrades = [...trades].sort((a, b) => a.exitTime.getTime() - b.exitTime.getTime());
    
    for (const trade of sortedTrades) {
      runningEquity += trade.pnl;
      runningMax = Math.max(runningMax, runningEquity);
      
      const drawdown = runningMax - runningEquity;
      const drawdownPercentage = runningMax > 0 ? (drawdown / runningMax) * 100 : 0;
      
      points.push({
        timestamp: trade.exitTime,
        equity: runningEquity,
        drawdown,
        drawdownPercentage,
        numberOfPositions: 1, // Simplified - would need position tracking for accurate count
      });
    }
    
    return points;
  }

  /**
   * Analyze performance by market regime
   */
  private async analyzeRegimePerformance(
    trades: BacktestTrade[],
    request: BacktestRequest
  ): Promise<RegimePerformance[]> {
    const regimeMap = new Map<MarketRegime, BacktestTrade[]>();
    
    // Group trades by market regime
    for (const trade of trades) {
      const regime = trade.marketRegime || MarketRegime.ANY;
      if (!regimeMap.has(regime)) {
        regimeMap.set(regime, []);
      }
      regimeMap.get(regime)!.push(trade);
    }
    
    const results: RegimePerformance[] = [];
    const totalDuration = request.endDate.getTime() - request.startDate.getTime();
    
    for (const [regime, regimeTrades] of regimeMap.entries()) {
      const regimeDuration = regimeTrades.reduce((sum, trade) => sum + trade.holdingPeriod, 0);
      const durationPercentage = totalDuration > 0 ? (regimeDuration / (totalDuration / (1000 * 60 * 60))) * 100 : 0;
      
      // Calculate metrics for this regime
      const dummyEquityCurve = this.calculateEquityCurve(regimeTrades, request.initialCapital);
      const metrics = this.calculatePerformanceMetrics(regimeTrades, request.initialCapital, dummyEquityCurve);
      
      results.push({
        regime,
        duration: regimeDuration,
        durationPercentage,
        metrics,
        trades: regimeTrades,
      });
    }
    
    return results;
  }

  /**
   * Calculate detailed drawdown analysis
   */
  private calculateDrawdownAnalysis(equityCurve: EquityPoint[]): DrawdownAnalysis {
    if (equityCurve.length === 0) {
      return {
        maxDrawdown: 0,
        maxDrawdownPercentage: 0,
        maxDrawdownDuration: 0,
        drawdownPeriods: [],
        averageDrawdown: 0,
        averageDrawdownDuration: 0,
      };
    }

    const maxDrawdown = Math.max(...equityCurve.map(p => p.drawdown));
    const maxDrawdownPercentage = Math.max(...equityCurve.map(p => p.drawdownPercentage));
    
    // Find drawdown periods
    const drawdownPeriods: DrawdownAnalysis['drawdownPeriods'] = [];
    let currentDrawdown: any = null;
    let peak = equityCurve[0].equity;
    
    for (let i = 0; i < equityCurve.length; i++) {
      const point = equityCurve[i];
      
      if (point.equity > peak) {
        // New peak reached
        if (currentDrawdown) {
          // End current drawdown period
          currentDrawdown.end = equityCurve[i - 1].timestamp;
          currentDrawdown.recovery = point.timestamp;
          currentDrawdown.duration = (currentDrawdown.end.getTime() - currentDrawdown.start.getTime()) / (1000 * 60 * 60);
          drawdownPeriods.push(currentDrawdown);
          currentDrawdown = null;
        }
        peak = point.equity;
      } else if (point.equity < peak) {
        // In drawdown
        if (!currentDrawdown) {
          // Start new drawdown period
          currentDrawdown = {
            start: point.timestamp,
            end: point.timestamp,
            peak,
            trough: point.equity,
            drawdown: peak - point.equity,
            drawdownPercentage: ((peak - point.equity) / peak) * 100,
            duration: 0,
            recovery: null,
          };
        } else {
          // Update existing drawdown
          if (point.equity < currentDrawdown.trough) {
            currentDrawdown.trough = point.equity;
            currentDrawdown.drawdown = peak - point.equity;
            currentDrawdown.drawdownPercentage = ((peak - point.equity) / peak) * 100;
          }
        }
      }
    }
    
    // Handle case where backtest ends in drawdown
    if (currentDrawdown) {
      currentDrawdown.end = equityCurve[equityCurve.length - 1].timestamp;
      currentDrawdown.duration = (currentDrawdown.end.getTime() - currentDrawdown.start.getTime()) / (1000 * 60 * 60);
      drawdownPeriods.push(currentDrawdown);
    }
    
    const averageDrawdown = drawdownPeriods.length > 0 
      ? drawdownPeriods.reduce((sum, dd) => sum + dd.drawdown, 0) / drawdownPeriods.length 
      : 0;
      
    const averageDrawdownDuration = drawdownPeriods.length > 0
      ? drawdownPeriods.reduce((sum, dd) => sum + dd.duration, 0) / drawdownPeriods.length
      : 0;
      
    const maxDrawdownDuration = drawdownPeriods.length > 0
      ? Math.max(...drawdownPeriods.map(dd => dd.duration))
      : 0;

    return {
      maxDrawdown,
      maxDrawdownPercentage,
      maxDrawdownDuration,
      drawdownPeriods,
      averageDrawdown,
      averageDrawdownDuration,
    };
  }

  /**
   * Calculate execution statistics
   */
  private calculateExecutionStats(trades: BacktestTrade[]) {
    const totalOrders = trades.length * 2; // Entry + exit for each trade
    const successfulExecutions = trades.filter(t => 
      t.executionDetails.entryExecution.success && t.executionDetails.exitExecution.success
    ).length * 2;
    
    const totalSlippage = trades.reduce((sum, trade) => 
      sum + Math.abs(trade.executionDetails.entryExecution.slippage) + Math.abs(trade.executionDetails.exitExecution.slippage), 0
    );
    
    const totalSpread = trades.reduce((sum, trade) => 
      sum + trade.executionDetails.entryExecution.spread + trade.executionDetails.exitExecution.spread, 0
    );
    
    const totalCommission = trades.reduce((sum, trade) => 
      sum + trade.executionDetails.entryExecution.commission + trade.executionDetails.exitExecution.commission, 0
    );
    
    const totalExecutionCost = trades.reduce((sum, trade) => 
      sum + trade.executionDetails.entryExecution.executionCost + trade.executionDetails.exitExecution.executionCost, 0
    );

    return {
      totalOrders,
      successfulExecutions,
      failedExecutions: totalOrders - successfulExecutions,
      averageSlippage: trades.length > 0 ? totalSlippage / (trades.length * 2) : 0,
      averageSpread: trades.length > 0 ? totalSpread / (trades.length * 2) : 0,
      totalCommission,
      totalExecutionCost,
    };
  }

  /**
   * Generate ML insights based on performance
   */
  private generateMLInsights(metrics: PerformanceMetrics, regimeAnalysis?: RegimePerformance[]): string[] {
    const insights: string[] = [];
    
    if (metrics.sharpeRatio > 1.5) {
      insights.push('Excellent risk-adjusted returns detected. Strategy shows strong performance consistency.');
    }
    
    if (metrics.maxDrawdownPercentage > 20) {
      insights.push('High drawdown detected. Consider implementing additional risk management measures.');
    }
    
    if (regimeAnalysis) {
      const trendingPerformance = regimeAnalysis.find(r => r.regime === MarketRegime.TRENDING);
      const rangingPerformance = regimeAnalysis.find(r => r.regime === MarketRegime.RANGING);
      
      if (trendingPerformance && rangingPerformance) {
        if (trendingPerformance.metrics.totalReturnPercentage > rangingPerformance.metrics.totalReturnPercentage * 2) {
          insights.push('Strategy performs significantly better in trending markets. Consider regime filtering.');
        }
      }
    }
    
    return insights;
  }

  /**
   * Generate coaching advice based on performance
   */
  private generateCoachingAdvice(metrics: PerformanceMetrics, drawdownAnalysis: DrawdownAnalysis): string[] {
    const advice: string[] = [];
    
    if (metrics.winRate < 40) {
      advice.push('Low win rate detected. Focus on improving entry signals or consider trend-following strategies.');
    }
    
    if (metrics.profitFactor < 1.2) {
      advice.push('Profit factor is below optimal range. Work on cutting losses faster or letting winners run longer.');
    }
    
    if (drawdownAnalysis.maxDrawdownDuration > 168) { // 1 week
      advice.push('Extended drawdown periods detected. Consider position sizing adjustments or diversification.');
    }
    
    return advice;
  }

  /**
   * Get empty metrics structure
   */
  private getEmptyMetrics(): PerformanceMetrics {
    return {
      totalReturn: 0,
      totalReturnPercentage: 0,
      annualizedReturn: 0,
      sharpeRatio: 0,
      maxDrawdown: 0,
      maxDrawdownPercentage: 0,
      winRate: 0,
      profitFactor: 1,
      averageWin: 0,
      averageLoss: 0,
      largestWin: 0,
      largestLoss: 0,
      totalTrades: 0,
      winningTrades: 0,
      losingTrades: 0,
      averageTradeReturn: 0,
      volatility: 0,
      downsideDeviation: 0,
      calmarRatio: 0,
      sortinoRatio: 0,
      averageHoldingPeriod: 0,
      averageTimeBetweenTrades: 0,
    };
  }

  /**
   * Generate unique backtest ID
   */
  private generateBacktestId(): string {
    return `bt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Generate unique order ID
   */
  private generateOrderId(): string {
    return `ord_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}