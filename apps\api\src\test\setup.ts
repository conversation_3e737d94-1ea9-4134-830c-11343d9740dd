import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';

// Test environment setup for GoldDaddy API

// Setup before all tests
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.DATABASE_URL = 'postgresql://postgres:postgres@localhost:5432/golddaddy_test';
  process.env.SUPABASE_URL = 'https://test-project.supabase.co';
  process.env.SUPABASE_ANON_KEY = 'test-anon-key';
  process.env.ENCRYPTION_KEY = 'test-encryption-key-32-chars-long-1234567890abcdef';
  process.env.ANONYMIZATION_SECRET = 'test-anonymization-secret';
  
  console.log('Test environment initialized');
});

// Cleanup after all tests
afterAll(async () => {
  console.log('Test environment cleanup completed');
});

// Setup before each test
beforeEach(async () => {
  // Reset any global state if needed
});

// Cleanup after each test
afterEach(async () => {
  // Clean up any test data if needed
});

// Mock console methods in test environment to reduce noise
if (process.env.NODE_ENV === 'test') {
  global.console = {
    ...console,
    log: () => {}, // Suppress console.log in tests
    info: () => {}, // Suppress console.info in tests
    // Keep error and warn for debugging
    error: console.error,
    warn: console.warn,
  };
}