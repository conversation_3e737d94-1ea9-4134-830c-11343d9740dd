import { EventEmitter } from 'events'
import { createClient } from '@supabase/supabase-js'
import { AuditLoggingService, AuditEventType } from './AuditLoggingService'
import { v4 as uuidv4 } from 'uuid'

export interface RetentionPolicy {
  id: string
  name: string
  description: string
  dataType: DataType
  retentionPeriod: number // in milliseconds
  deleteAfter: number // in milliseconds
  isActive: boolean
  legalBasis: string[]
  exceptions: RetentionException[]
  createdAt: Date
  updatedAt: Date
}

export enum DataType {
  PERSONAL_DATA = 'personal_data',
  TRADING_DATA = 'trading_data',
  FINANCIAL_DATA = 'financial_data',
  COMMUNICATION_DATA = 'communication_data',
  ANALYTICS_DATA = 'analytics_data',
  AUDIT_LOGS = 'audit_logs',
  SYSTEM_LOGS = 'system_logs',
  BACKUP_DATA = 'backup_data',
  KYC_DOCUMENTS = 'kyc_documents',
  SUPPORT_TICKETS = 'support_tickets'
}

export interface RetentionException {
  condition: string
  extendedPeriod?: number
  reason: string
  regulatoryBasis: string
}

export interface DataDeletionRequest {
  id: string
  userId?: string
  requestType: 'user_request' | 'automated' | 'legal_obligation' | 'business_purpose'
  dataTypes: DataType[]
  reason: string
  status: 'pending' | 'approved' | 'rejected' | 'completed' | 'failed'
  requestedBy: string
  requestedAt: Date
  approvedBy?: string
  approvedAt?: Date
  completedAt?: Date
  deletedRecords: Record<string, number>
  errors: string[]
  verificationRequired: boolean
}

export interface GDPRRequest {
  id: string
  userId: string
  requestType: 'access' | 'rectification' | 'erasure' | 'portability' | 'restriction' | 'objection'
  status: 'received' | 'verified' | 'processing' | 'completed' | 'rejected'
  submittedAt: Date
  completedAt?: Date
  reason?: string
  details: Record<string, any>
  responseData?: any
  verificationToken: string
}

interface RetentionJob {
  id: string
  name: string
  schedule: string // cron expression
  policies: string[] // policy IDs
  lastRun?: Date
  nextRun: Date
  isActive: boolean
  results: RetentionJobResult[]
}

interface RetentionJobResult {
  jobId: string
  runAt: Date
  status: 'success' | 'failure' | 'partial'
  recordsProcessed: number
  recordsDeleted: number
  errors: string[]
  duration: number
}

export class DataRetentionService extends EventEmitter {
  private supabase = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  )
  
  private auditService: AuditLoggingService

  private defaultPolicies: Omit<RetentionPolicy, 'id' | 'createdAt' | 'updatedAt'>[] = [
    {
      name: 'Personal Data - GDPR Compliance',
      description: 'Standard retention for personal data under GDPR',
      dataType: DataType.PERSONAL_DATA,
      retentionPeriod: 2 * 365 * 24 * 60 * 60 * 1000, // 2 years
      deleteAfter: 30 * 24 * 60 * 60 * 1000, // 30 days after retention period
      isActive: true,
      legalBasis: ['GDPR Article 5(1)(e)', 'GDPR Article 17'],
      exceptions: [
        {
          condition: 'active_legal_proceedings',
          reason: 'Legal hold for ongoing litigation',
          regulatoryBasis: 'Legal obligation'
        },
        {
          condition: 'regulatory_investigation',
          extendedPeriod: 365 * 24 * 60 * 60 * 1000, // 1 year extension
          reason: 'Regulatory investigation in progress',
          regulatoryBasis: 'Legal obligation'
        }
      ]
    },
    {
      name: 'Trading Records - Financial Regulations',
      description: 'Mandatory retention for trading data under MiFID II',
      dataType: DataType.TRADING_DATA,
      retentionPeriod: 7 * 365 * 24 * 60 * 60 * 1000, // 7 years
      deleteAfter: 0, // No automatic deletion due to regulatory requirements
      isActive: true,
      legalBasis: ['MiFID II Article 25', 'FINRA Rule 4511'],
      exceptions: []
    },
    {
      name: 'Financial Transaction Records',
      description: 'AML and tax compliance retention for financial data',
      dataType: DataType.FINANCIAL_DATA,
      retentionPeriod: 7 * 365 * 24 * 60 * 60 * 1000, // 7 years
      deleteAfter: 0, // No automatic deletion
      isActive: true,
      legalBasis: ['AML Directive', 'Tax Code Section 6001'],
      exceptions: []
    },
    {
      name: 'KYC Documents',
      description: 'Customer identification documents retention',
      dataType: DataType.KYC_DOCUMENTS,
      retentionPeriod: 5 * 365 * 24 * 60 * 60 * 1000, // 5 years
      deleteAfter: 180 * 24 * 60 * 60 * 1000, // 180 days after retention period
      isActive: true,
      legalBasis: ['AML/KYC Regulations', 'Customer Due Diligence'],
      exceptions: [
        {
          condition: 'suspicious_activity_report',
          extendedPeriod: 2 * 365 * 24 * 60 * 60 * 1000, // 2 years extension
          reason: 'SAR filed - extended retention required',
          regulatoryBasis: 'AML compliance'
        }
      ]
    },
    {
      name: 'Communication and Support Data',
      description: 'Customer communication and support ticket retention',
      dataType: DataType.COMMUNICATION_DATA,
      retentionPeriod: 3 * 365 * 24 * 60 * 60 * 1000, // 3 years
      deleteAfter: 90 * 24 * 60 * 60 * 1000, // 90 days after retention period
      isActive: true,
      legalBasis: ['Customer service obligations', 'Dispute resolution'],
      exceptions: []
    },
    {
      name: 'Analytics and Usage Data',
      description: 'Platform analytics and user behavior data',
      dataType: DataType.ANALYTICS_DATA,
      retentionPeriod: 2 * 365 * 24 * 60 * 60 * 1000, // 2 years
      deleteAfter: 30 * 24 * 60 * 60 * 1000, // 30 days after retention period
      isActive: true,
      legalBasis: ['Legitimate interest', 'Service improvement'],
      exceptions: []
    },
    {
      name: 'System and Audit Logs',
      description: 'System logs for security and compliance monitoring',
      dataType: DataType.SYSTEM_LOGS,
      retentionPeriod: 1 * 365 * 24 * 60 * 60 * 1000, // 1 year
      deleteAfter: 90 * 24 * 60 * 60 * 1000, // 90 days after retention period
      isActive: true,
      legalBasis: ['Security monitoring', 'Compliance requirements'],
      exceptions: [
        {
          condition: 'security_incident',
          extendedPeriod: 2 * 365 * 24 * 60 * 60 * 1000, // 2 years extension
          reason: 'Security incident investigation',
          regulatoryBasis: 'Security obligation'
        }
      ]
    }
  ]

  constructor() {
    super()
    this.auditService = new AuditLoggingService()
    this.initializePolicies()
  }

  private async initializePolicies(): Promise<void> {
    // Check if policies already exist
    const { data: existingPolicies } = await this.supabase
      .from('data_retention_policies')
      .select('name')

    if (!existingPolicies || existingPolicies.length === 0) {
      // Initialize with default policies
      for (const policy of this.defaultPolicies) {
        await this.createPolicy(policy)
      }
    }
  }

  async createPolicy(
    policyData: Omit<RetentionPolicy, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<RetentionPolicy> {
    const policy: RetentionPolicy = {
      ...policyData,
      id: uuidv4(),
      createdAt: new Date(),
      updatedAt: new Date()
    }

    const { error } = await this.supabase
      .from('data_retention_policies')
      .insert(policy)

    if (error) {
      throw new Error(`Failed to create retention policy: ${error.message}`)
    }

    await this.auditService.logEvent(AuditEventType.CONFIG_CHANGED, {
      action: 'Created data retention policy',
      resource: 'retention_policy',
      resourceId: policy.id,
      details: {
        policyName: policy.name,
        dataType: policy.dataType,
        retentionPeriod: policy.retentionPeriod
      },
      ipAddress: '127.0.0.1',
      userAgent: 'system'
    })

    return policy
  }

  async getPolicies(): Promise<RetentionPolicy[]> {
    const { data, error } = await this.supabase
      .from('data_retention_policies')
      .select('*')
      .eq('isActive', true)
      .order('createdAt', { ascending: false })

    if (error) {
      throw new Error(`Failed to get retention policies: ${error.message}`)
    }

    return data as RetentionPolicy[]
  }

  async updatePolicy(
    policyId: string, 
    updates: Partial<RetentionPolicy>
  ): Promise<RetentionPolicy> {
    const updatedPolicy = {
      ...updates,
      id: policyId,
      updatedAt: new Date()
    }

    const { data, error } = await this.supabase
      .from('data_retention_policies')
      .update(updatedPolicy)
      .eq('id', policyId)
      .select('*')
      .single()

    if (error) {
      throw new Error(`Failed to update retention policy: ${error.message}`)
    }

    await this.auditService.logEvent(AuditEventType.CONFIG_CHANGED, {
      action: 'Updated data retention policy',
      resource: 'retention_policy',
      resourceId: policyId,
      details: { updates },
      ipAddress: '127.0.0.1',
      userAgent: 'system'
    })

    return data as RetentionPolicy
  }

  async applyRetentionPolicies(): Promise<RetentionJobResult> {
    const jobResult: RetentionJobResult = {
      jobId: uuidv4(),
      runAt: new Date(),
      status: 'success',
      recordsProcessed: 0,
      recordsDeleted: 0,
      errors: [],
      duration: Date.now()
    }

    try {
      const policies = await this.getPolicies()
      
      for (const policy of policies) {
        try {
          const result = await this.applyPolicyToData(policy)
          jobResult.recordsProcessed += result.processed
          jobResult.recordsDeleted += result.deleted
        } catch (error) {
          jobResult.errors.push(`Policy ${policy.name}: ${error}`)
          jobResult.status = 'partial'
        }
      }

      jobResult.duration = Date.now() - jobResult.duration

      // Log retention job completion
      await this.auditService.logEvent(AuditEventType.DATA_RETENTION_APPLIED, {
        action: 'Applied data retention policies',
        resource: 'retention_job',
        resourceId: jobResult.jobId,
        details: {
          recordsProcessed: jobResult.recordsProcessed,
          recordsDeleted: jobResult.recordsDeleted,
          errors: jobResult.errors.length,
          duration: jobResult.duration
        },
        ipAddress: '127.0.0.1',
        userAgent: 'retention_job',
        outcome: jobResult.errors.length > 0 ? 'warning' : 'success'
      })

    } catch (error) {
      jobResult.status = 'failure'
      jobResult.errors.push(`Job failed: ${error}`)
    }

    return jobResult
  }

  private async applyPolicyToData(policy: RetentionPolicy): Promise<{ processed: number; deleted: number }> {
    const cutoffDate = new Date(Date.now() - policy.retentionPeriod)
    const deleteDate = new Date(Date.now() - policy.retentionPeriod - policy.deleteAfter)
    
    let processed = 0
    let deleted = 0

    switch (policy.dataType) {
      case DataType.PERSONAL_DATA:
        const personalDataResult = await this.processPersonalData(deleteDate)
        processed += personalDataResult.processed
        deleted += personalDataResult.deleted
        break

      case DataType.ANALYTICS_DATA:
        const analyticsResult = await this.processAnalyticsData(deleteDate)
        processed += analyticsResult.processed
        deleted += analyticsResult.deleted
        break

      case DataType.COMMUNICATION_DATA:
        const commResult = await this.processCommunicationData(deleteDate)
        processed += commResult.processed
        deleted += commResult.deleted
        break

      case DataType.SYSTEM_LOGS:
        const logsResult = await this.processSystemLogs(deleteDate)
        processed += logsResult.processed
        deleted += logsResult.deleted
        break

      default:
        // Skip data types that require manual review
        console.log(`Skipping automatic deletion for ${policy.dataType}`)
    }

    return { processed, deleted }
  }

  private async processPersonalData(deleteDate: Date): Promise<{ processed: number; deleted: number }> {
    // Process user profiles marked for deletion
    const { data: expiredProfiles, error: selectError } = await this.supabase
      .from('users')
      .select('id, email')
      .eq('account_status', 'deleted')
      .lt('deleted_at', deleteDate.toISOString())

    if (selectError || !expiredProfiles) {
      return { processed: 0, deleted: 0 }
    }

    let deleted = 0
    for (const profile of expiredProfiles) {
      try {
        // Anonymize user data instead of hard delete to maintain referential integrity
        await this.anonymizeUserData(profile.id)
        deleted++
      } catch (error) {
        console.error(`Failed to anonymize user ${profile.id}:`, error)
      }
    }

    return { processed: expiredProfiles.length, deleted }
  }

  private async processAnalyticsData(deleteDate: Date): Promise<{ processed: number; deleted: number }> {
    const { count: processed, error: processError } = await this.supabase
      .from('user_analytics')
      .select('*', { count: 'exact', head: true })
      .lt('created_at', deleteDate.toISOString())

    if (processError) {
      return { processed: 0, deleted: 0 }
    }

    const { count: deleted, error: deleteError } = await this.supabase
      .from('user_analytics')
      .delete({ count: 'exact' })
      .lt('created_at', deleteDate.toISOString())

    if (deleteError) {
      return { processed: processed || 0, deleted: 0 }
    }

    return { processed: processed || 0, deleted: deleted || 0 }
  }

  private async processCommunicationData(deleteDate: Date): Promise<{ processed: number; deleted: number }> {
    // Delete resolved support tickets older than retention period
    const { count: deleted, error } = await this.supabase
      .from('support_tickets')
      .delete({ count: 'exact' })
      .eq('status', 'closed')
      .lt('created_at', deleteDate.toISOString())

    if (error) {
      return { processed: 0, deleted: 0 }
    }

    return { processed: deleted || 0, deleted: deleted || 0 }
  }

  private async processSystemLogs(deleteDate: Date): Promise<{ processed: number; deleted: number }> {
    // Delete old system logs (not audit logs which have their own retention)
    const { count: deleted, error } = await this.supabase
      .from('system_logs')
      .delete({ count: 'exact' })
      .lt('timestamp', deleteDate.toISOString())

    if (error) {
      return { processed: 0, deleted: 0 }
    }

    return { processed: deleted || 0, deleted: deleted || 0 }
  }

  private async anonymizeUserData(userId: string): Promise<void> {
    const anonymizedData = {
      email: `deleted_${userId}@anonymized.local`,
      first_name: '[DELETED]',
      last_name: '[DELETED]',
      phone: null,
      address: null,
      date_of_birth: null,
      account_status: 'anonymized',
      anonymized_at: new Date().toISOString()
    }

    const { error } = await this.supabase
      .from('users')
      .update(anonymizedData)
      .eq('id', userId)

    if (error) {
      throw new Error(`Failed to anonymize user data: ${error.message}`)
    }

    await this.auditService.logEvent(AuditEventType.DATA_DELETED, {
      userId,
      action: 'Anonymized user personal data',
      resource: 'user_profile',
      resourceId: userId,
      details: { anonymizedFields: Object.keys(anonymizedData) },
      ipAddress: '127.0.0.1',
      userAgent: 'retention_service'
    })
  }

  async requestDataDeletion(request: Omit<DataDeletionRequest, 'id' | 'status' | 'deletedRecords' | 'errors'>): Promise<DataDeletionRequest> {
    const deletionRequest: DataDeletionRequest = {
      ...request,
      id: uuidv4(),
      status: 'pending',
      deletedRecords: {},
      errors: []
    }

    const { error } = await this.supabase
      .from('data_deletion_requests')
      .insert(deletionRequest)

    if (error) {
      throw new Error(`Failed to create deletion request: ${error.message}`)
    }

    await this.auditService.logEvent(AuditEventType.GDPR_REQUEST, {
      userId: request.userId,
      action: 'Data deletion requested',
      resource: 'deletion_request',
      resourceId: deletionRequest.id,
      details: {
        requestType: request.requestType,
        dataTypes: request.dataTypes,
        reason: request.reason
      },
      ipAddress: '127.0.0.1',
      userAgent: 'retention_service'
    })

    this.emit('deletion_request_created', deletionRequest)

    return deletionRequest
  }

  async processGDPRRequest(request: Omit<GDPRRequest, 'id' | 'status' | 'submittedAt' | 'verificationToken'>): Promise<GDPRRequest> {
    const gdprRequest: GDPRRequest = {
      ...request,
      id: uuidv4(),
      status: 'received',
      submittedAt: new Date(),
      verificationToken: this.generateVerificationToken()
    }

    const { error } = await this.supabase
      .from('gdpr_requests')
      .insert(gdprRequest)

    if (error) {
      throw new Error(`Failed to create GDPR request: ${error.message}`)
    }

    await this.auditService.logEvent(AuditEventType.GDPR_REQUEST, {
      userId: request.userId,
      action: `GDPR ${request.requestType} request submitted`,
      resource: 'gdpr_request',
      resourceId: gdprRequest.id,
      details: {
        requestType: request.requestType,
        details: request.details
      },
      ipAddress: '127.0.0.1',
      userAgent: 'gdpr_service'
    })

    // Auto-process certain request types
    if (request.requestType === 'access') {
      await this.processDataAccessRequest(gdprRequest.id)
    }

    return gdprRequest
  }

  private async processDataAccessRequest(requestId: string): Promise<void> {
    const { data: request, error } = await this.supabase
      .from('gdpr_requests')
      .select('*')
      .eq('id', requestId)
      .single()

    if (error || !request) {
      return
    }

    try {
      const userData = await this.collectUserData(request.user_id)
      
      await this.supabase
        .from('gdpr_requests')
        .update({
          status: 'completed',
          completed_at: new Date().toISOString(),
          response_data: userData
        })
        .eq('id', requestId)

      await this.auditService.logEvent(AuditEventType.DATA_EXPORTED, {
        userId: request.user_id,
        action: 'GDPR data access request completed',
        resource: 'user_data_export',
        resourceId: requestId,
        details: {
          dataTypes: Object.keys(userData),
          recordCount: Object.values(userData).reduce((sum, records) => 
            sum + (Array.isArray(records) ? records.length : 1), 0
          )
        },
        ipAddress: '127.0.0.1',
        userAgent: 'gdpr_service'
      })

    } catch (error) {
      await this.supabase
        .from('gdpr_requests')
        .update({
          status: 'rejected',
          reason: `Failed to process request: ${error}`
        })
        .eq('id', requestId)
    }
  }

  private async collectUserData(userId: string): Promise<Record<string, any>> {
    const userData: Record<string, any> = {}

    // Collect data from various tables
    const tables = [
      'users',
      'user_profiles', 
      'trading_accounts',
      'trades',
      'transactions',
      'support_tickets',
      'user_preferences'
    ]

    for (const table of tables) {
      try {
        const { data, error } = await this.supabase
          .from(table)
          .select('*')
          .eq('user_id', userId)

        if (!error && data) {
          userData[table] = data
        }
      } catch (error) {
        console.error(`Failed to collect data from ${table}:`, error)
      }
    }

    return userData
  }

  private generateVerificationToken(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15)
  }

  async getRetentionReport(): Promise<{
    policies: RetentionPolicy[]
    upcomingDeletions: Array<{
      dataType: DataType
      recordCount: number
      deletionDate: Date
    }>
    recentDeletions: RetentionJobResult[]
    complianceStatus: Record<string, boolean>
  }> {
    const policies = await this.getPolicies()
    
    // Calculate upcoming deletions
    const upcomingDeletions = await Promise.all(
      policies.map(async (policy) => {
        const cutoffDate = new Date(Date.now() - policy.retentionPeriod - policy.deleteAfter)
        
        // This would need to be implemented based on actual table structures
        return {
          dataType: policy.dataType,
          recordCount: 0, // Calculate actual count
          deletionDate: cutoffDate
        }
      })
    )

    // Get recent deletion job results
    const { data: recentDeletions } = await this.supabase
      .from('retention_job_results')
      .select('*')
      .order('runAt', { ascending: false })
      .limit(10)

    const complianceStatus = {
      gdpr: true,
      ccpa: true,
      sox: true,
      mifid: true,
      finra: true
    }

    return {
      policies,
      upcomingDeletions,
      recentDeletions: recentDeletions as RetentionJobResult[] || [],
      complianceStatus
    }
  }
}