import { Router, Request, Response, NextFunction } from 'express';
import { rateLimit } from 'express-rate-limit';
import { PaperTradingEngine } from '../../services/trading/PaperTradingEngine';
import { VirtualPortfolioService } from '../../services/trading/VirtualPortfolioService';
import { PaperTradingAnalyticsService } from '../../services/trading/PaperTradingAnalyticsService';
import { PaperTradingRequirementsService } from '../../services/trading/PaperTradingRequirementsService';
import { AuditService } from '../../services/compliance/AuditTrailService';
// import { validateRequest } from '../../utils/validation.utils'; // TODO: Implement validation utilities
import { z } from 'zod';

// Rate limiting configurations
const paperTradeExecutionLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // Limit each user to 30 paper trades per minute
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    error: 'Too many paper trades. Please wait before submitting more.',
    code: 'RATE_LIMIT_EXCEEDED',
    retryAfter: 60
  }
});

const paperTradeGeneralLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // General API limit
  standardHeaders: true,
  legacyHeaders: false,
});

// Validation schemas
const PaperTradeRequestSchema = z.object({
  sessionId: z.string().uuid('Invalid session ID'),
  symbol: z.string().min(3, 'Symbol must be at least 3 characters'),
  side: z.enum(['buy', 'sell']),
  quantity: z.number().positive().max(10, 'Maximum position size is 10 lots'),
  orderType: z.enum(['market', 'limit', 'stop']),
  price: z.number().positive().optional(),
  stopLoss: z.number().positive().optional(),
  takeProfit: z.number().positive().optional(),
  strategyId: z.string().uuid().optional(),
  goalId: z.string().uuid().optional(),
});

const SessionCreateSchema = z.object({
  name: z.string().min(1, 'Session name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  initialBalance: z.number().positive().max(1000000, 'Initial balance too high'),
  strategyId: z.string().uuid().optional(),
  goalId: z.string().uuid().optional(),
});

const PositionModifySchema = z.object({
  sessionId: z.string().uuid('Invalid session ID'),
  stopLoss: z.number().positive().optional(),
  takeProfit: z.number().positive().optional(),
});

// Controller functions
interface PaperTradingControllers {
  executeTradeController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getPortfolioController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getSessionController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  createSessionController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getPerformanceController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getRequirementsController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  closePositionController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  modifyPositionController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getPendingOrdersController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  cancelOrderController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
}

function createPaperTradingControllers(
  paperTradingEngine: PaperTradingEngine,
  portfolioService: VirtualPortfolioService,
  analyticsService: PaperTradingAnalyticsService,
  requirementsService: PaperTradingRequirementsService,
  auditService: AuditService
): PaperTradingControllers {

  const executeTradeController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      const validatedData = PaperTradeRequestSchema.parse(req.body);

      // Additional validation for limit orders
      if (validatedData.orderType === 'limit' && !validatedData.price) {
        res.status(400).json({
          success: false,
          error: 'Price is required for limit orders',
          code: 'VALIDATION_ERROR'
        });
        return;
      }

      // Execute paper trade
      const result = await paperTradingEngine.executePaperTrade(
        userId,
        validatedData.sessionId,
        {
          symbol: validatedData.symbol,
          side: validatedData.side,
          quantity: validatedData.quantity,
          orderType: validatedData.orderType,
          price: validatedData.price,
          stopLoss: validatedData.stopLoss,
          takeProfit: validatedData.takeProfit,
          strategyId: validatedData.strategyId,
          goalId: validatedData.goalId,
        }
      );

      // Audit the trade execution
      await auditService.logActivity({
        userId,
        action: 'PAPER_TRADE_EXECUTED',
        details: {
          sessionId: validatedData.sessionId,
          tradeId: result.trade.id,
          symbol: validatedData.symbol,
          side: validatedData.side,
          quantity: validatedData.quantity,
          executionPrice: result.trade.executionPrice,
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.status(200).json({
        success: true,
        data: {
          trade: result.trade,
          portfolio: result.portfolio,
          executionQuality: result.executionQuality,
          learningInsights: result.learningInsights,
        },
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      if (error.name === 'ZodError') {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors
        });
        return;
      }
      next(error);
    }
  };

  const getPortfolioController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const portfolio = await portfolioService.getVirtualPortfolio(userId, sessionId);

      if (!portfolio) {
        res.status(404).json({
          success: false,
          error: 'Portfolio not found',
          code: 'PORTFOLIO_NOT_FOUND'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: portfolio,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const getSessionController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const session = await paperTradingEngine.getSession(userId, sessionId);

      if (!session) {
        res.status(404).json({
          success: false,
          error: 'Session not found',
          code: 'SESSION_NOT_FOUND'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: session,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const createSessionController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      const validatedData = SessionCreateSchema.parse(req.body);

      const session = await paperTradingEngine.createSession(userId, validatedData);

      // Audit session creation
      await auditService.logActivity({
        userId,
        action: 'PAPER_TRADING_SESSION_CREATED',
        details: {
          sessionId: session.id,
          name: validatedData.name,
          initialBalance: validatedData.initialBalance,
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.status(201).json({
        success: true,
        data: session,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      if (error.name === 'ZodError') {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors
        });
        return;
      }
      next(error);
    }
  };

  const getPerformanceController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const analytics = await analyticsService.generateAnalytics(userId, sessionId);

      res.status(200).json({
        success: true,
        data: analytics,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const getRequirementsController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const requirements = await requirementsService.evaluateGraduationCriteria(userId, sessionId);

      res.status(200).json({
        success: true,
        data: requirements,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const closePositionController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { positionId } = req.params;
      const { sessionId } = req.body;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const result = await paperTradingEngine.closePosition(userId, sessionId, positionId);

      // Audit position closure
      await auditService.logActivity({
        userId,
        action: 'PAPER_POSITION_CLOSED',
        details: {
          sessionId,
          positionId,
          realizedPnL: result.realizedPnL,
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const modifyPositionController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { positionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      const validatedData = PositionModifySchema.parse(req.body);

      const result = await paperTradingEngine.modifyPosition(
        userId,
        validatedData.sessionId,
        positionId,
        {
          stopLoss: validatedData.stopLoss,
          takeProfit: validatedData.takeProfit,
        }
      );

      // Audit position modification
      await auditService.logActivity({
        userId,
        action: 'PAPER_POSITION_MODIFIED',
        details: {
          sessionId: validatedData.sessionId,
          positionId,
          modifications: {
            stopLoss: validatedData.stopLoss,
            takeProfit: validatedData.takeProfit,
          },
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      if (error.name === 'ZodError') {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors
        });
        return;
      }
      next(error);
    }
  };

  const getPendingOrdersController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.query;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || typeof sessionId !== 'string' || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const orders = await paperTradingEngine.getPendingOrders(userId, sessionId);

      res.status(200).json({
        success: true,
        data: {
          pendingOrders: orders,
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const cancelOrderController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { orderId } = req.params;
      const { sessionId } = req.body;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const result = await paperTradingEngine.cancelOrder(userId, sessionId, orderId);

      // Audit order cancellation
      await auditService.logActivity({
        userId,
        action: 'PAPER_ORDER_CANCELLED',
        details: {
          sessionId,
          orderId,
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  return {
    executeTradeController,
    getPortfolioController,
    getSessionController,
    createSessionController,
    getPerformanceController,
    getRequirementsController,
    closePositionController,
    modifyPositionController,
    getPendingOrdersController,
    cancelOrderController,
  };
}

// Export the router factory function
export const createPaperTradingRoutes = (services: {
  paperTradingEngine: PaperTradingEngine;
  portfolioService: VirtualPortfolioService;
  analyticsService: PaperTradingAnalyticsService;
  requirementsService: PaperTradingRequirementsService;
  auditService: AuditService;
}) => {
  const router = Router();

  // Apply rate limiting
  router.use(paperTradeGeneralLimit);

  // Create controllers
  const controllers = createPaperTradingControllers(
    services.paperTradingEngine,
    services.portfolioService,
    services.analyticsService,
    services.requirementsService,
    services.auditService
  );

  // === PAPER TRADE EXECUTION ROUTES ===

  /**
   * @route POST /api/trades/paper
   * @desc Execute a paper trade
   * @access Private
   * @rateLimit 30 requests per minute
   */
  router.post(
    '/',
    paperTradeExecutionLimit,
    controllers.executeTradeController
  );

  /**
   * @route GET /api/trades/paper
   * @desc Get recent paper trades for a session
   * @access Private
   */
  router.get(
    '/',
    async (req: Request, res: Response, next: NextFunction) => {
      try {
        const userId = req.user?.id;
        const { sessionId, limit = '10', offset = '0' } = req.query;

        if (!userId) {
          res.status(401).json({
            success: false,
            error: 'Authentication required',
            code: 'UNAUTHORIZED'
          });
          return;
        }

        if (!sessionId || typeof sessionId !== 'string') {
          res.status(400).json({
            success: false,
            error: 'Session ID required',
            code: 'INVALID_REQUEST'
          });
          return;
        }

        const trades = await services.paperTradingEngine.getRecentTrades(
          userId,
          sessionId,
          parseInt(limit as string, 10),
          parseInt(offset as string, 10)
        );

        res.status(200).json({
          success: true,
          data: {
            trades,
            pagination: {
              limit: parseInt(limit as string, 10),
              offset: parseInt(offset as string, 10),
            }
          },
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        next(error);
      }
    }
  );

  // === PAPER TRADING SESSION ROUTES ===

  /**
   * @route POST /api/paper-trading/session
   * @desc Create a new paper trading session
   * @access Private
   */
  router.post('/session', controllers.createSessionController);

  /**
   * @route GET /api/paper-trading/session/:sessionId
   * @desc Get paper trading session details
   * @access Private
   */
  router.get('/session/:sessionId', controllers.getSessionController);

  // === VIRTUAL PORTFOLIO ROUTES ===

  /**
   * @route GET /api/portfolio/virtual/:sessionId
   * @desc Get virtual portfolio status
   * @access Private
   */
  router.get('/portfolio/virtual/:sessionId', controllers.getPortfolioController);

  // === PERFORMANCE ANALYSIS ROUTES ===

  /**
   * @route GET /api/paper-trading/performance/:sessionId
   * @desc Get paper trading performance analytics
   * @access Private
   */
  router.get('/performance/:sessionId', controllers.getPerformanceController);

  // === GRADUATION REQUIREMENTS ROUTES ===

  /**
   * @route GET /api/paper-trading/requirements/:sessionId
   * @desc Get graduation requirements status
   * @access Private
   */
  router.get('/requirements/:sessionId', controllers.getRequirementsController);

  // === POSITION MANAGEMENT ROUTES ===

  /**
   * @route POST /api/trades/paper/:positionId/close
   * @desc Close a paper trading position
   * @access Private
   */
  router.post('/:positionId/close', controllers.closePositionController);

  /**
   * @route PUT /api/trades/paper/:positionId/modify
   * @desc Modify a paper trading position
   * @access Private
   */
  router.put('/:positionId/modify', controllers.modifyPositionController);

  // === ORDER MANAGEMENT ROUTES ===

  /**
   * @route GET /api/trades/paper/orders
   * @desc Get pending paper trading orders
   * @access Private
   */
  router.get('/orders', controllers.getPendingOrdersController);

  /**
   * @route DELETE /api/trades/paper/orders/:orderId
   * @desc Cancel a pending paper trading order
   * @access Private
   */
  router.delete('/orders/:orderId', controllers.cancelOrderController);

  // === ERROR HANDLING ===

  // Handle 404 for unmatched paper trading routes
  router.use((req: Request, res: Response) => {
    res.status(404).json({
      success: false,
      error: 'Paper trading endpoint not found',
      code: 'ENDPOINT_NOT_FOUND',
      availableEndpoints: [
        'POST /api/trades/paper',
        'GET /api/trades/paper',
        'POST /api/paper-trading/session',
        'GET /api/paper-trading/session/:sessionId',
        'GET /api/portfolio/virtual/:sessionId',
        'GET /api/paper-trading/performance/:sessionId',
        'GET /api/paper-trading/requirements/:sessionId',
        'POST /api/trades/paper/:positionId/close',
        'PUT /api/trades/paper/:positionId/modify',
        'GET /api/trades/paper/orders',
        'DELETE /api/trades/paper/orders/:orderId'
      ]
    });
  });

  // Global error handler for paper trading routes
  router.use((error: any, req: Request, res: Response, _next: NextFunction) => {
    console.error('Paper trading route error:', {
      error: error.message,
      stack: error.stack,
      path: req.path,
      method: req.method,
      user: req.user?.id,
      timestamp: new Date().toISOString()
    });

    // Handle specific paper trading errors
    if (error.code === 'INSUFFICIENT_VIRTUAL_BALANCE') {
      res.status(400).json({
        success: false,
        error: 'Insufficient virtual balance for this trade',
        code: 'INSUFFICIENT_VIRTUAL_BALANCE',
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (error.code === 'INVALID_PAPER_TRADING_SESSION') {
      res.status(400).json({
        success: false,
        error: 'Invalid or expired paper trading session',
        code: 'INVALID_PAPER_TRADING_SESSION',
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (error.code === 'POSITION_NOT_FOUND') {
      res.status(404).json({
        success: false,
        error: 'Position not found or not accessible',
        code: 'POSITION_NOT_FOUND',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Generic error response
    const isDevelopment = process.env.NODE_ENV === 'development';
    res.status(error.status || 500).json({
      success: false,
      error: isDevelopment ? error.message : 'Internal server error',
      code: error.code || 'INTERNAL_ERROR',
      ...(isDevelopment && { stack: error.stack }),
      timestamp: new Date().toISOString()
    });
  });

  return router;
};