/**
 * Position Sizing Calculator Tests
 * 
 * Comprehensive test suite for PositionSizingCalculator with 100% coverage
 * as required for financial calculations.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import { describe, it, expect, beforeEach } from 'vitest';
import Decimal from 'decimal.js';
import { 
  PositionSizingCalculator, 
  createPositionSizingCalculator,
  type PositionSizingConfig,
  type PositionDetails,
  type ExistingPosition 
} from './PositionSizingCalculator';

describe('PositionSizingCalculator', () => {
  let calculator: PositionSizingCalculator;
  let defaultConfig: PositionSizingConfig;
  
  beforeEach(() => {
    defaultConfig = {
      algorithm: 'fixed_percentage',
      riskPercentage: 2.0,
      maxPositionPercentage: 20.0,
      volatilityLookbackDays: 20,
      correlationThreshold: 0.7,
      diversificationTarget: 10
    };
    calculator = new PositionSizingCalculator(defaultConfig);
  });

  describe('Constructor and Factory', () => {
    it('should create instance successfully', () => {
      expect(calculator).toBeInstanceOf(PositionSizingCalculator);
    });

    it('should create instance via factory function', () => {
      const calc = createPositionSizingCalculator();
      expect(calc).toBeInstanceOf(PositionSizingCalculator);
    });

    it('should create instance with custom config', () => {
      const customConfig = { riskPercentage: 1.5, maxPositionPercentage: 15.0 };
      const calc = createPositionSizingCalculator(customConfig);
      expect(calc.getConfig().riskPercentage).toBe(1.5);
      expect(calc.getConfig().maxPositionPercentage).toBe(15.0);
    });

    it('should throw error for invalid config', () => {
      const invalidConfig = { ...defaultConfig, riskPercentage: -1 };
      expect(() => new PositionSizingCalculator(invalidConfig)).toThrow();
    });
  });

  describe('Configuration Validation', () => {
    it('should reject negative risk percentage', () => {
      const config = { ...defaultConfig, riskPercentage: -1 };
      expect(() => new PositionSizingCalculator(config)).toThrow('Risk percentage must be between 0 and 100');
    });

    it('should reject risk percentage over 100', () => {
      const config = { ...defaultConfig, riskPercentage: 150 };
      expect(() => new PositionSizingCalculator(config)).toThrow('Risk percentage must be between 0 and 100');
    });

    it('should reject invalid max position percentage', () => {
      const config = { ...defaultConfig, maxPositionPercentage: 0 };
      expect(() => new PositionSizingCalculator(config)).toThrow('Maximum position percentage must be between 0 and 100');
    });

    it('should reject invalid correlation threshold', () => {
      const config = { ...defaultConfig, correlationThreshold: 1.5 };
      expect(() => new PositionSizingCalculator(config)).toThrow('Correlation threshold must be between 0 and 1');
    });

    it('should reject invalid diversification target', () => {
      const config = { ...defaultConfig, diversificationTarget: 0 };
      expect(() => new PositionSizingCalculator(config)).toThrow('Diversification target must be at least 1');
    });
  });

  describe('Fixed Percentage Algorithm', () => {
    const positionDetails: PositionDetails = {
      symbol: 'EURUSD',
      currentPrice: new Decimal(1.1000),
      stopLoss: new Decimal(1.0900),
      takeProfit: new Decimal(1.1200),
      marketVolatility: 15,
      correlationWithPortfolio: 0.3,
      averageTrueRange: new Decimal(0.0050)
    };

    it('should calculate fixed percentage position size correctly', () => {
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      // Risk amount = 10000 * 2% = 200
      // Risk per share = 1.1000 - 1.0900 = 0.01
      // Position size = 200 / 0.01 = 20000 shares
      // But this is too much, so it gets reduced to account size / price
      expect(result.riskAmount.toNumber()).toBe(200);
      expect(result.recommendedShares.toNumber()).toBeCloseTo(1818.18, 2);
      expect(result.algorithm).toBe('fixed_percentage');
    });

    it('should handle zero risk per share error', () => {
      const invalidPosition = { ...positionDetails, stopLoss: new Decimal(1.1000) };
      const accountBalance = new Decimal(10000);
      
      expect(() => calculator.calculatePositionSize(accountBalance, invalidPosition))
        .toThrow('Stop loss cannot equal current price');
    });

    it('should apply correlation adjustments', () => {
      const highCorrPosition = { ...positionDetails, correlationWithPortfolio: 0.8 };
      const accountBalance = new Decimal(10000);
      
      const highCorrResult = calculator.calculatePositionSize(accountBalance, highCorrPosition);
      
      // Both should be the same due to max position limit (20%)
      // expect(lowCorrResult.recommendedShares.gt(highCorrResult.recommendedShares)).toBe(true);
      expect(highCorrResult.adjustments.some(adj => adj.type === 'correlation')).toBe(true);
    });

    it('should enforce maximum position size limits', () => {
      // Create scenario where position would exceed 20% limit
      const largePosition = { ...positionDetails, stopLoss: new Decimal(1.0999) }; // Very small stop loss
      const accountBalance = new Decimal(10000);
      
      const result = calculator.calculatePositionSize(accountBalance, largePosition);
      
      expect(result.portfolioWeight).toBeLessThanOrEqual(20.0);
      expect(result.adjustments.some(adj => adj.type === 'max_limit')).toBe(true);
    });
  });

  describe('Volatility-Based Algorithm', () => {
    beforeEach(() => {
      calculator.updateConfig({ algorithm: 'volatility_based' });
    });

    const positionDetails: PositionDetails = {
      symbol: 'EURUSD',
      currentPrice: new Decimal(1.1000),
      stopLoss: new Decimal(1.0900),
      takeProfit: new Decimal(1.1200),
      marketVolatility: 20,
      correlationWithPortfolio: 0,
      averageTrueRange: new Decimal(0.0050)
    };

    it('should calculate volatility-based position size', () => {
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      expect(result.algorithm).toBe('volatility_based');
      expect(result.recommendedShares.gt(0)).toBe(true);
      expect(result.reasoning).toContain('Using volatility-based sizing algorithm');
    });

    it('should adjust for different volatility levels', () => {
      const lowVolPosition = { ...positionDetails, marketVolatility: 10 };
      const highVolPosition = { ...positionDetails, marketVolatility: 40 };
      const accountBalance = new Decimal(10000);
      
      const lowVolResult = calculator.calculatePositionSize(accountBalance, lowVolPosition);
      const highVolResult = calculator.calculatePositionSize(accountBalance, highVolPosition);
      
      // Lower volatility should allow larger positions (but both may hit max limits)
      expect(lowVolResult.recommendedShares.gte(highVolResult.recommendedShares)).toBe(true);
    });
  });

  describe('Kelly Criterion Algorithm', () => {
    beforeEach(() => {
      calculator.updateConfig({ algorithm: 'kelly_criterion' });
    });

    const positionDetails: PositionDetails = {
      symbol: 'EURUSD',
      currentPrice: new Decimal(1.1000),
      stopLoss: new Decimal(1.0900),
      takeProfit: new Decimal(1.1200),
      marketVolatility: 15,
      correlationWithPortfolio: 0,
      averageTrueRange: new Decimal(0.0050)
    };

    it('should calculate Kelly-based position size', () => {
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      expect(result.algorithm).toBe('kelly_criterion');
      expect(result.recommendedShares.gt(0)).toBe(true);
      expect(result.reasoning).toContain('Using Kelly Criterion algorithm');
    });

    it('should handle positions without take profit', () => {
      const noTpPosition = { ...positionDetails, takeProfit: undefined };
      const accountBalance = new Decimal(10000);
      
      const result = calculator.calculatePositionSize(accountBalance, noTpPosition);
      expect(result.riskMetrics.riskRewardRatio).toBe(2.0); // Default ratio
    });
  });

  describe('Equal Weight Algorithm', () => {
    beforeEach(() => {
      calculator.updateConfig({ algorithm: 'equal_weight' });
    });

    const positionDetails: PositionDetails = {
      symbol: 'EURUSD',
      currentPrice: new Decimal(1.1000),
      stopLoss: new Decimal(1.0900),
      marketVolatility: 15,
      correlationWithPortfolio: 0,
      averageTrueRange: new Decimal(0.0050)
    };

    it('should calculate equal weight position size', () => {
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      expect(result.algorithm).toBe('equal_weight');
      expect(result.portfolioWeight).toBeCloseTo(10.0, 1); // 1/10 = 10%
      expect(result.reasoning).toContain('Using equal weight algorithm');
    });

    it('should account for diversification target', () => {
      calculator.updateConfig({ diversificationTarget: 5 });
      
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      expect(result.portfolioWeight).toBeCloseTo(20.0, 1); // 1/5 = 20%
    });
  });

  describe('Risk Budget Allocation', () => {
    const proposedPositions: PositionDetails[] = [
      {
        symbol: 'EURUSD',
        currentPrice: new Decimal(1.1000),
        stopLoss: new Decimal(1.0900),
        marketVolatility: 15,
        correlationWithPortfolio: 0.2,
        averageTrueRange: new Decimal(0.0050)
      },
      {
        symbol: 'GBPUSD',
        currentPrice: new Decimal(1.3000),
        stopLoss: new Decimal(1.2900),
        marketVolatility: 20,
        correlationWithPortfolio: 0.8,
        averageTrueRange: new Decimal(0.0070)
      }
    ];

    it('should allocate risk budget correctly', () => {
      const accountBalance = new Decimal(10000);
      const result = calculator.allocateRiskBudget(accountBalance, proposedPositions);
      
      expect(result.totalRiskBudget.toNumber()).toBe(200); // 2% of 10000
      expect(result.positionRiskAllocations).toHaveLength(2);
      expect(result.utilizationPercentage).toBeGreaterThan(0);
    });

    it('should account for existing positions', () => {
      const existingPositions: ExistingPosition[] = [
        {
          symbol: 'USDJPY',
          size: new Decimal(100),
          marketValue: new Decimal(1100),
          unrealizedPnL: new Decimal(50),
          weight: 11.0
        }
      ];
      
      const accountBalance = new Decimal(10000);
      const result = calculator.allocateRiskBudget(accountBalance, proposedPositions, existingPositions);
      
      expect(result.allocatedRisk.gt(0)).toBe(true);
      expect(result.remainingRisk.lt(result.totalRiskBudget)).toBe(true);
    });

    it('should adjust for correlation in risk allocation', () => {
      const accountBalance = new Decimal(10000);
      const result = calculator.allocateRiskBudget(accountBalance, proposedPositions);
      
      // High correlation position (GBPUSD) should get less allocation
      const eurusdAllocation = result.positionRiskAllocations.find(p => p.symbol === 'EURUSD');
      const gbpusdAllocation = result.positionRiskAllocations.find(p => p.symbol === 'GBPUSD');
      
      expect(eurusdAllocation!.allocatedRisk.gt(gbpusdAllocation!.allocatedRisk)).toBe(true);
    });
  });

  describe('Risk Metrics Calculations', () => {
    const positionDetails: PositionDetails = {
      symbol: 'EURUSD',
      currentPrice: new Decimal(1.1000),
      stopLoss: new Decimal(1.0900),
      takeProfit: new Decimal(1.1200),
      marketVolatility: 15,
      correlationWithPortfolio: 0.3,
      averageTrueRange: new Decimal(0.0050)
    };

    it('should calculate potential loss correctly', () => {
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      const expectedLoss = result.recommendedShares.mul(
        positionDetails.currentPrice.sub(positionDetails.stopLoss).abs()
      );
      expect(result.riskMetrics.potentialLoss.eq(expectedLoss)).toBe(true);
    });

    it('should calculate risk-reward ratio correctly', () => {
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      // Risk = 1.1000 - 1.0900 = 0.01
      // Reward = 1.1200 - 1.1000 = 0.02
      // Ratio = 0.02 / 0.01 = 2.0
      expect(result.riskMetrics.riskRewardRatio).toBe(2.0);
    });

    it('should calculate diversification score for existing positions', () => {
      const existingPositions: ExistingPosition[] = [
        { symbol: 'EURUSD', size: new Decimal(100), marketValue: new Decimal(1100), unrealizedPnL: new Decimal(0), weight: 33.3 },
        { symbol: 'GBPUSD', size: new Decimal(100), marketValue: new Decimal(1300), unrealizedPnL: new Decimal(0), weight: 33.3 },
        { symbol: 'USDJPY', size: new Decimal(100), marketValue: new Decimal(1100), unrealizedPnL: new Decimal(0), weight: 33.4 }
      ];
      
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails, existingPositions);
      
      expect(result.riskMetrics.diversificationScore).toBeGreaterThan(0);
      expect(result.riskMetrics.diversificationScore).toBeLessThanOrEqual(1);
    });

    it('should handle single position diversification', () => {
      const existingPositions: ExistingPosition[] = [
        { symbol: 'EURUSD', size: new Decimal(100), marketValue: new Decimal(1100), unrealizedPnL: new Decimal(0), weight: 100 }
      ];
      
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails, existingPositions);
      
      expect(result.riskMetrics.diversificationScore).toBe(0);
    });
  });

  describe('Configuration Management', () => {
    it('should update configuration successfully', () => {
      const newConfig = { riskPercentage: 3.0, maxPositionPercentage: 25.0 };
      calculator.updateConfig(newConfig);
      
      const config = calculator.getConfig();
      expect(config.riskPercentage).toBe(3.0);
      expect(config.maxPositionPercentage).toBe(25.0);
    });

    it('should validate updated configuration', () => {
      const invalidUpdate = { riskPercentage: -1 };
      expect(() => calculator.updateConfig(invalidUpdate)).toThrow();
    });

    it('should return configuration copy', () => {
      const config1 = calculator.getConfig();
      const config2 = calculator.getConfig();
      
      expect(config1).not.toBe(config2); // Different objects
      expect(config1).toEqual(config2); // Same values
    });
  });

  describe('Concentration and Correlation Adjustments', () => {
    const positionDetails: PositionDetails = {
      symbol: 'EURUSD',
      currentPrice: new Decimal(100), // High price to test concentration
      stopLoss: new Decimal(99),
      marketVolatility: 15,
      correlationWithPortfolio: 0.9, // High correlation
      averageTrueRange: new Decimal(1)
    };

    it('should apply concentration limits', () => {
      const accountBalance = new Decimal(1000); // Small balance to trigger concentration limit
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      expect(result.portfolioWeight).toBeLessThanOrEqual(20.0);
      expect(result.adjustments.some(adj => adj.type === 'concentration')).toBe(true);
    });

    it('should reduce position size for high correlation', () => {
      calculator.updateConfig({ correlationThreshold: 0.7 });
      
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      const correlationAdjustment = result.adjustments.find(adj => adj.type === 'correlation');
      expect(correlationAdjustment).toBeDefined();
      expect(correlationAdjustment!.factor).toBeLessThan(1.0);
      expect(correlationAdjustment!.impact).toBe('decrease');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle unsupported algorithm', () => {
      const invalidConfig = { ...defaultConfig, algorithm: 'invalid_algorithm' as any };
      const calc = new PositionSizingCalculator(invalidConfig);
      
      const positionDetails: PositionDetails = {
        symbol: 'EURUSD',
        currentPrice: new Decimal(1.1000),
        stopLoss: new Decimal(1.0900),
        marketVolatility: 15,
        correlationWithPortfolio: 0,
        averageTrueRange: new Decimal(0.0050)
      };
      
      expect(() => calc.calculatePositionSize(new Decimal(10000), positionDetails))
        .toThrow('Unsupported algorithm: invalid_algorithm');
    });

    it('should handle very small position sizes', () => {
      const positionDetails: PositionDetails = {
        symbol: 'EURUSD',
        currentPrice: new Decimal(1.1000),
        stopLoss: new Decimal(1.0001), // Very small risk
        marketVolatility: 15,
        correlationWithPortfolio: 0,
        averageTrueRange: new Decimal(0.0001)
      };
      
      const accountBalance = new Decimal(100); // Small balance
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      expect(result.recommendedShares.gte(0)).toBe(true);
    });

    it('should handle empty existing positions array', () => {
      const positionDetails: PositionDetails = {
        symbol: 'EURUSD',
        currentPrice: new Decimal(1.1000),
        stopLoss: new Decimal(1.0900),
        marketVolatility: 15,
        correlationWithPortfolio: 0,
        averageTrueRange: new Decimal(0.0050)
      };
      
      const accountBalance = new Decimal(10000);
      const result = calculator.calculatePositionSize(accountBalance, positionDetails, []);
      
      expect(result.riskMetrics.diversificationScore).toBe(0); // No existing positions to diversify with
      // May still have concentration adjustments if position is large relative to account
    });
  });

  describe('Mathematical Precision', () => {
    it('should maintain precision with Decimal arithmetic', () => {
      const positionDetails: PositionDetails = {
        symbol: 'EURUSD',
        currentPrice: new Decimal('1.*********'),
        stopLoss: new Decimal('1.*********'),
        marketVolatility: 15.75,
        correlationWithPortfolio: 0.333333,
        averageTrueRange: new Decimal('0.*********')
      };
      
      const accountBalance = new Decimal('10000.*********');
      const result = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      expect(result.recommendedShares).toBeInstanceOf(Decimal);
      expect(result.positionValue).toBeInstanceOf(Decimal);
      expect(result.riskAmount).toBeInstanceOf(Decimal);
    });

    it('should produce consistent results across multiple calls', () => {
      const positionDetails: PositionDetails = {
        symbol: 'EURUSD',
        currentPrice: new Decimal(1.1000),
        stopLoss: new Decimal(1.0900),
        marketVolatility: 15,
        correlationWithPortfolio: 0.5,
        averageTrueRange: new Decimal(0.0050)
      };
      
      const accountBalance = new Decimal(10000);
      
      const result1 = calculator.calculatePositionSize(accountBalance, positionDetails);
      const result2 = calculator.calculatePositionSize(accountBalance, positionDetails);
      
      expect(result1.recommendedShares.eq(result2.recommendedShares)).toBe(true);
      expect(result1.portfolioWeight).toBe(result2.portfolioWeight);
      expect(result1.riskPercentage).toBe(result2.riskPercentage);
    });
  });
});