/**
 * Market Regime Detector Service
 * 
 * Main service for market regime detection with real-time processing,
 * historical analysis, and integration with existing market data infrastructure.
 */

import { EventEmitter } from 'events';
import Decimal from 'decimal.js';
import {
  MarketRegime,
  RegimeDetectionResult,
  RegimeDetectionConfig,
  HistoricalRegimeData,
  RegimeTransition,
  DataSource,
  TimeFrame,
  MarketDataWithRegime,
} from '@golddaddy/types';
import { 
  RegimeClassificationEngine, 
  TechnicalIndicatorInput 
} from './RegimeClassificationEngine';
import { NormalizedMarketData } from './RealTimeDataProcessor';

// Default configuration for regime detection
const DEFAULT_CONFIG: RegimeDetectionConfig = {
  trendDetectionWindow: 20,
  volatilityWindow: 14,
  confidenceThreshold: 0.5,
  trendStrengthThreshold: 0.6,
  volatilityThreshold: 0.02,
  momentumThreshold: 0.01,
  regimeChangeThreshold: 0.15,
  minimumRegimeDuration: 30, // 30 minutes
  maxProcessingTime: 1000, // 1 second
  enableAccuracyTracking: true,
};

// Cache entry for regime detection results
interface RegimeCacheEntry {
  result: RegimeDetectionResult;
  timestamp: Date;
  ttl: number; // Time to live in milliseconds
}

// Historical data point for regime analysis
interface HistoricalDataPoint {
  timestamp: Date;
  prices: {
    open: Decimal.Instance;
    high: Decimal.Instance;
    low: Decimal.Instance;
    close: Decimal.Instance;
    volume: Decimal.Instance;
  };
  indicators?: {
    sma20?: Decimal.Instance;
    sma50?: Decimal.Instance;
    rsi14?: Decimal.Instance;
    atr14?: Decimal.Instance;
    macdLine?: Decimal.Instance;
    macdSignal?: Decimal.Instance;
    bollingerUpper?: Decimal.Instance;
    bollingerLower?: Decimal.Instance;
    bollingerMiddle?: Decimal.Instance;
  };
}

/**
 * Market Regime Detector Service
 * Orchestrates regime detection using classification engine and manages caching
 */
export class MarketRegimeDetector extends EventEmitter {
  private classificationEngine: RegimeClassificationEngine;
  private config: RegimeDetectionConfig;
  private regimeCache = new Map<string, RegimeCacheEntry>();
  private historicalData = new Map<string, HistoricalDataPoint[]>();
  private lastRegimes = new Map<string, MarketRegime>();
  
  // Performance tracking
  private stats = {
    totalDetections: 0,
    cacheHits: 0,
    averageProcessingTime: 0,
    regimeChanges: 0,
    lastDetectionAt: new Date(),
  };

  constructor(config?: Partial<RegimeDetectionConfig>) {
    super();
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.classificationEngine = new RegimeClassificationEngine(this.config);
    
    // Set up classification engine event handlers
    this.classificationEngine.on('regime_classified', (result) => {
      this.emit('regime_detected', result);
    });
    
    this.classificationEngine.on('classification_error', (error) => {
      this.emit('detection_error', error);
    });

    // Clean cache periodically
    setInterval(() => this.cleanCache(), 60000); // Every minute
  }

  /**
   * Main method to detect market regime from normalized market data
   */
  public async detectRegime(marketData: NormalizedMarketData): Promise<RegimeDetectionResult> {
    const startTime = Date.now();
    const cacheKey = this.generateCacheKey(marketData);

    try {
      // Check cache first
      const cached = this.getCachedResult(cacheKey);
      if (cached) {
        this.stats.cacheHits++;
        this.emit('cache_hit', { instrument: marketData.instrument, timeframe: marketData.timeframe });
        return cached;
      }

      // Get historical data for the instrument
      const historicalKey = `${marketData.instrument}_${marketData.timeframe}`;
      const historicalData = this.getHistoricalData(historicalKey);
      
      // Add current data point to historical data
      this.updateHistoricalData(historicalKey, marketData);
      
      // Prepare input for classification engine
      const input = this.prepareClassificationInput(marketData, historicalData);
      
      // Get previous regime for change detection
      const previousRegime = this.getLastRegime(historicalKey);
      
      // Perform regime classification
      const result = await this.classificationEngine.classifyMarketRegime(input, previousRegime);
      
      // Cache the result
      this.cacheResult(cacheKey, result);
      
      // Update last regime
      this.updateLastRegime(historicalKey, result.regime);
      
      // Handle regime changes
      if (result.regimeChangeDetected) {
        await this.handleRegimeChange(result);
      }
      
      // Update statistics
      this.updateStats(startTime, result);
      
      this.emit('regime_detection_complete', result);
      return result;

    } catch (error) {
      this.emit('detection_error', {
        instrument: marketData.instrument,
        timeframe: marketData.timeframe,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime,
      });
      
      // Return error result
      return this.createErrorResult(marketData, error as Error);
    }
  }

  /**
   * Detect regime for multiple instruments and timeframes
   */
  public async detectRegimesBatch(
    marketDataList: NormalizedMarketData[]
  ): Promise<RegimeDetectionResult[]> {
    const results: RegimeDetectionResult[] = [];
    
    // Process in parallel with concurrency limit
    const batchSize = 5;
    for (let i = 0; i < marketDataList.length; i += batchSize) {
      const batch = marketDataList.slice(i, i + batchSize);
      const batchResults = await Promise.all(
        batch.map(data => this.detectRegime(data))
      );
      results.push(...batchResults);
    }
    
    this.emit('batch_detection_complete', {
      totalProcessed: marketDataList.length,
      successCount: results.filter(r => r.regime !== MarketRegime.UNKNOWN).length,
      errorCount: results.filter(r => r.regime === MarketRegime.UNKNOWN).length,
    });
    
    return results;
  }

  /**
   * Get historical regime data for analysis
   */
  public getHistoricalRegimeData(
    instrument: string,
    timeframe: TimeFrame,
    startDate: Date,
    endDate: Date
  ): HistoricalRegimeData[] {
    // This would typically query the database
    // For now, return from cache/memory
    const key = `${instrument}_${timeframe}`;
    const cachedResults = Array.from(this.regimeCache.values())
      .filter(entry => 
        entry.result.instrument === instrument &&
        entry.result.timeframe === timeframe &&
        entry.result.timestamp >= startDate &&
        entry.result.timestamp <= endDate
      )
      .map(entry => ({
        id: entry.result.id,
        instrument: entry.result.instrument,
        timeframe: entry.result.timeframe,
        timestamp: entry.result.timestamp,
        regime: entry.result.regime,
        confidence: entry.result.confidence,
        duration: 0, // Would be calculated from database
        createdAt: entry.timestamp,
      }));

    return cachedResults;
  }

  /**
   * Force regime detection without cache
   */
  public async forceDetectRegime(marketData: NormalizedMarketData): Promise<RegimeDetectionResult> {
    const cacheKey = this.generateCacheKey(marketData);
    this.regimeCache.delete(cacheKey); // Remove from cache
    return this.detectRegime(marketData);
  }

  /**
   * Get regime detection statistics
   */
  public getStats() {
    return {
      ...this.stats,
      cacheSize: this.regimeCache.size,
      historicalDataSets: this.historicalData.size,
      cacheHitRate: this.stats.totalDetections > 0 ? 
        (this.stats.cacheHits / this.stats.totalDetections) * 100 : 0,
    };
  }

  /**
   * Clear all caches and reset statistics
   */
  public clearCache(): void {
    this.regimeCache.clear();
    this.historicalData.clear();
    this.lastRegimes.clear();
    
    this.stats = {
      totalDetections: 0,
      cacheHits: 0,
      averageProcessingTime: 0,
      regimeChanges: 0,
      lastDetectionAt: new Date(),
    };
    
    this.emit('cache_cleared');
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<RegimeDetectionConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.classificationEngine.updateConfig(this.config);
    this.emit('config_updated', this.config);
  }

  /**
   * Get current configuration
   */
  public getConfig(): RegimeDetectionConfig {
    return { ...this.config };
  }

  // ===== Private Helper Methods =====

  private generateCacheKey(marketData: NormalizedMarketData): string {
    return `${marketData.instrument}_${marketData.timeframe}_${marketData.timestamp.getTime()}`;
  }

  private getCachedResult(cacheKey: string): RegimeDetectionResult | null {
    const cached = this.regimeCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp.getTime()) < cached.ttl) {
      return cached.result;
    }
    
    if (cached) {
      this.regimeCache.delete(cacheKey); // Remove expired entry
    }
    
    return null;
  }

  private cacheResult(cacheKey: string, result: RegimeDetectionResult): void {
    const ttl = 300000; // 5 minutes
    this.regimeCache.set(cacheKey, {
      result,
      timestamp: new Date(),
      ttl,
    });
  }

  private getHistoricalData(key: string): HistoricalDataPoint[] {
    return this.historicalData.get(key) || [];
  }

  private updateHistoricalData(key: string, marketData: NormalizedMarketData): void {
    let data = this.historicalData.get(key) || [];
    
    // Add new data point
    data.push({
      timestamp: marketData.timestamp,
      prices: {
        open: marketData.open,
        high: marketData.high,
        low: marketData.low,
        close: marketData.close,
        volume: marketData.volume,
      },
      // Technical indicators would be calculated or provided
    });
    
    // Keep only recent data (last 100 periods)
    if (data.length > 100) {
      data = data.slice(-100);
    }
    
    this.historicalData.set(key, data);
  }

  private prepareClassificationInput(
    marketData: NormalizedMarketData,
    historicalData: HistoricalDataPoint[]
  ): TechnicalIndicatorInput {
    // Combine current data with historical data
    const allData = [...historicalData, {
      timestamp: marketData.timestamp,
      prices: {
        open: marketData.open,
        high: marketData.high,
        low: marketData.low,
        close: marketData.close,
        volume: marketData.volume,
      },
    }];

    // Calculate basic technical indicators if not provided
    const prices = allData.map(d => d.prices);
    
    return {
      instrument: marketData.instrument,
      timeframe: marketData.timeframe,
      timestamp: marketData.timestamp,
      prices,
      source: marketData.source,
      
      // Technical indicators would be calculated here
      // For now, using placeholder calculations
      sma20: this.calculateSMA(prices, 20),
      sma50: this.calculateSMA(prices, 50),
      rsi14: this.calculateRSI(prices, 14),
      atr14: this.calculateATR(allData, 14),
    };
  }

  private getLastRegime(key: string): MarketRegime | undefined {
    return this.lastRegimes.get(key);
  }

  private updateLastRegime(key: string, regime: MarketRegime): void {
    this.lastRegimes.set(key, regime);
  }

  private async handleRegimeChange(result: RegimeDetectionResult): Promise<void> {
    this.stats.regimeChanges++;
    
    // Create regime transition record
    const transition: RegimeTransition = {
      id: `transition_${result.id}`,
      instrument: result.instrument,
      timeframe: result.timeframe,
      timestamp: result.timestamp,
      fromRegime: result.previousRegime!,
      toRegime: result.regime,
      confidence: result.confidence,
      transitionMagnitude: result.regimeChangeMagnitude || 0,
      duration: 0, // Would be calculated from last regime change
      priceMovement: 0, // Would be calculated from price data
      volumeSpike: false, // Would be determined from volume analysis
      notificationSent: false,
      createdAt: new Date(),
    };
    
    this.emit('regime_change_detected', transition);
  }

  private updateStats(startTime: number, result: RegimeDetectionResult): void {
    const processingTime = Date.now() - startTime;
    this.stats.totalDetections++;
    this.stats.averageProcessingTime = 
      ((this.stats.averageProcessingTime * (this.stats.totalDetections - 1)) + processingTime) / 
      this.stats.totalDetections;
    this.stats.lastDetectionAt = new Date();
  }

  private createErrorResult(marketData: NormalizedMarketData, error: Error): RegimeDetectionResult {
    return {
      id: `error_${marketData.instrument}_${Date.now()}`,
      instrument: marketData.instrument,
      timeframe: marketData.timeframe,
      timestamp: marketData.timestamp,
      regime: MarketRegime.UNKNOWN,
      confidence: 0,
      confidenceLevel: 'very_low',
      
      trendStrength: 0,
      volatilityLevel: 0,
      momentumScore: 0,
      
      supportingMetrics: {
        smaSlope20: 0,
        smaSlope50: 0,
        atr14: 0,
        rsi14: 50,
        bollingerBandwidth: 0,
        pricePosition: 0.5,
      },
      
      regimeChangeDetected: false,
      
      processingTimeMs: 0,
      source: marketData.source,
      algorithmVersion: '1.0.0',
    };
  }

  private cleanCache(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];
    
    for (const [key, entry] of this.regimeCache.entries()) {
      if (now - entry.timestamp.getTime() > entry.ttl) {
        expiredKeys.push(key);
      }
    }
    
    expiredKeys.forEach(key => this.regimeCache.delete(key));
    
    if (expiredKeys.length > 0) {
      this.emit('cache_cleaned', { expiredEntries: expiredKeys.length });
    }
  }

  // ===== Technical Indicator Calculations =====
  // These are simplified implementations for demonstration

  private calculateSMA(prices: any[], periods: number): Decimal | undefined {
    if (prices.length < periods) return undefined;
    
    const recentPrices = prices.slice(-periods);
    const sum = recentPrices.reduce((acc, p) => acc.plus(p.close), new Decimal(0));
    return sum.div(periods);
  }

  private calculateRSI(prices: any[], periods: number): Decimal | undefined {
    if (prices.length < periods + 1) return undefined;
    
    const gains: Decimal[] = [];
    const losses: Decimal[] = [];
    
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i].close.minus(prices[i - 1].close);
      if (change.gt(0)) {
        gains.push(change);
        losses.push(new Decimal(0));
      } else {
        gains.push(new Decimal(0));
        losses.push(change.abs());
      }
    }
    
    if (gains.length < periods) return undefined;
    
    const avgGain = gains.slice(-periods).reduce((acc, g) => acc.plus(g), new Decimal(0)).div(periods);
    const avgLoss = losses.slice(-periods).reduce((acc, l) => acc.plus(l), new Decimal(0)).div(periods);
    
    if (avgLoss.eq(0)) return new Decimal(100);
    
    const rs = avgGain.div(avgLoss);
    return new Decimal(100).minus(new Decimal(100).div(rs.plus(1)));
  }

  private calculateATR(data: HistoricalDataPoint[], periods: number): Decimal | undefined {
    if (data.length < periods + 1) return undefined;
    
    const trueRanges: Decimal[] = [];
    
    for (let i = 1; i < data.length; i++) {
      const high = data[i].prices.high;
      const low = data[i].prices.low;
      const prevClose = data[i - 1].prices.close;
      
      const tr1 = high.minus(low);
      const tr2 = high.minus(prevClose).abs();
      const tr3 = low.minus(prevClose).abs();
      
      const trueRange = Decimal.max(tr1, tr2, tr3);
      trueRanges.push(trueRange);
    }
    
    if (trueRanges.length < periods) return undefined;
    
    const recentTRs = trueRanges.slice(-periods);
    return recentTRs.reduce((acc, tr) => acc.plus(tr), new Decimal(0)).div(periods);
  }

  /**
   * Shutdown the detector and cleanup resources
   */
  public shutdown(): void {
    this.clearCache();
    this.removeAllListeners();
    this.classificationEngine.removeAllListeners();
  }
}