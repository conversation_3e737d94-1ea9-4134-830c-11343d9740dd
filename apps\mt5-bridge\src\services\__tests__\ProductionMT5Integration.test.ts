/**
 * Production MT5 Integration Test Suite
 * 
 * Comprehensive tests for all production MT5 integration components
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ProductionMT5ConnectionManager } from '../ProductionMT5ConnectionManager';
import { ConnectionPoolManager } from '../ConnectionPoolManager';
import { ReconnectionEngine } from '../ReconnectionEngine';
import { BrokerAdapterFactory } from '../../adapters/BrokerAdapterFactory';
import { MetaQuotesDemoAdapter } from '../../adapters/MetaQuotesDemoAdapter';
import { ProductionTradeExecutor } from '../ProductionTradeExecutor';
import { AccountSynchronizationService } from '../AccountSynchronizationService';
import { MT5HealthMonitor } from '../../monitoring/MT5HealthMonitor';

describe('Production MT5 Integration', () => {
  let connectionManager: ProductionMT5ConnectionManager;
  let poolManager: ConnectionPoolManager;
  let reconnectionEngine: ReconnectionEngine;
  let brokerFactory: BrokerAdapterFactory;
  let tradeExecutor: ProductionTradeExecutor;
  let accountService: AccountSynchronizationService;
  let healthMonitor: MT5HealthMonitor;

  beforeEach(async () => {
    // Initialize all components
    connectionManager = new ProductionMT5ConnectionManager({
      healthCheckInterval: 1000,
      connectionTimeout: 5000,
      maxRetryAttempts: 3,
      loadBalancingStrategy: 'health-based'
    });

    poolManager = new ConnectionPoolManager({
      enableAutoOptimization: false,
      metricsUpdateInterval: 1000
    });

    reconnectionEngine = new ReconnectionEngine({
      maxRetryAttempts: 3,
      baseRetryDelay: 100,
      maxRetryDelay: 1000,
      retryMultiplier: 2,
      circuitBreakerThreshold: 3,
      circuitBreakerTimeout: 5000
    });

    brokerFactory = new BrokerAdapterFactory();
    
    tradeExecutor = new ProductionTradeExecutor(brokerFactory, {
      maxRetryAttempts: 2,
      retryDelay: 100,
      executionTimeout: 2000,
      enableRiskValidation: false // Disabled for testing
    });

    accountService = new AccountSynchronizationService({
      syncInterval: 1000,
      enableRealTime: true,
      persistToDB: false
    });

    healthMonitor = new MT5HealthMonitor({
      checkInterval: 1000,
      uptimeThreshold: 0.95,
      latencyThreshold: 500
    });
  });

  afterEach(async () => {
    // Cleanup all components
    await connectionManager?.shutdown();
    await poolManager?.shutdown();
    await reconnectionEngine?.shutdown();
    await brokerFactory?.shutdown();
    await accountService?.shutdown();
    await healthMonitor?.shutdown();
  });

  describe('Connection Management', () => {
    it('should initialize connection manager with broker configurations', async () => {
      const brokerConfigs = [
        {
          id: 'demo1',
          name: 'MetaQuotes Demo 1',
          server: 'demo.metaquotes.net:443',
          login: 'testuser1',
          password: 'testpass1',
          priority: 1,
          maxConnections: 3,
          features: ['streaming', 'trading'] as any
        }
      ];

      const initialized = await connectionManager.initialize(brokerConfigs);
      expect(initialized).toBe(true);
      
      const stats = connectionManager.getPoolStats();
      expect(stats.totalConnections).toBeGreaterThan(0);
    });

    it('should perform health checks on connections', async () => {
      const brokerConfigs = [
        {
          id: 'demo1',
          name: 'MetaQuotes Demo 1',
          server: 'demo.metaquotes.net:443',
          login: 'testuser1',
          password: 'testpass1',
          priority: 1,
          maxConnections: 2,
          features: ['streaming', 'trading'] as any
        }
      ];

      await connectionManager.initialize(brokerConfigs);
      
      // Wait for health checks to run
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      const stats = connectionManager.getPoolStats();
      expect(stats.totalConnections).toBeGreaterThan(0);
    });

    it('should get available connections for trading', async () => {
      const brokerConfigs = [
        {
          id: 'demo1',
          name: 'MetaQuotes Demo 1',
          server: 'demo.metaquotes.net:443',
          login: 'testuser1',
          password: 'testpass1',
          priority: 1,
          maxConnections: 2,
          features: ['streaming', 'trading'] as any
        }
      ];

      await connectionManager.initialize(brokerConfigs);
      
      const connectionId = await connectionManager.getConnection('demo1', 'trading');
      expect(connectionId).toBeTypeOf('string');
    });
  });

  describe('Connection Pool Management', () => {
    it('should create and manage connection pools', async () => {
      const brokerConfig = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        server: 'demo.metaquotes.net:443',
        login: 'testuser',
        password: 'testpass',
        priority: 1,
        maxConnections: 3,
        features: ['streaming', 'trading'],
        type: 'demo' as any,
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD'],
        supportedTimeframes: ['M1', 'H1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      poolManager.createPool('demo1', brokerConfig);
      
      const connection = await poolManager.getConnection('demo1', 'trading');
      expect(connection).toBeTypeOf('string');
      
      if (connection) {
        poolManager.releaseConnection('demo1', connection);
      }
      
      const stats = poolManager.getPoolStats('demo1');
      expect(stats).toBeDefined();
    });

    it('should optimize pool size based on load', async () => {
      const brokerConfig = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        server: 'demo.metaquotes.net:443',
        login: 'testuser',
        password: 'testpass',
        priority: 1,
        maxConnections: 5,
        features: ['streaming', 'trading'],
        type: 'demo' as any,
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD'],
        supportedTimeframes: ['M1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        connectionLimits: {
          maxConnections: 5,
          maxConcurrentRequests: 10,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      poolManager.createPool('demo1', brokerConfig);
      
      const recommendation = await poolManager.optimizePool('demo1');
      expect(recommendation.action).toMatch(/increase|decrease|maintain/);
      expect(recommendation.confidence).toBeGreaterThanOrEqual(0);
      expect(recommendation.confidence).toBeLessThanOrEqual(1);
    });
  });

  describe('Reconnection Engine', () => {
    it('should schedule reconnection attempts', async () => {
      const scheduled = await reconnectionEngine.scheduleReconnection(
        'test-connection-1',
        'demo1',
        'Connection timeout'
      );

      expect(scheduled).toBe(true);
      
      const stats = reconnectionEngine.getStats();
      expect(stats.currentRetryConnections).toBe(1);
    });

    it('should handle circuit breaker logic', async () => {
      // Trigger multiple failures to open circuit breaker
      for (let i = 0; i < 6; i++) {
        await reconnectionEngine.scheduleReconnection(
          `test-connection-${i}`,
          'demo1',
          'Connection refused'
        );
      }

      const circuitBreakerStatus = reconnectionEngine.getCircuitBreakerStatus('demo1');
      expect(circuitBreakerStatus.failureCount).toBeGreaterThan(0);
    });

    it('should preserve connection state during outages', async () => {
      const preserveState = {
        subscriptions: ['EURUSD', 'GBPUSD'],
        metadata: { userId: 'test-user' }
      };

      await reconnectionEngine.scheduleReconnection(
        'test-connection-1',
        'demo1',
        'Network error',
        preserveState
      );

      const preserved = reconnectionEngine.getPreservedState('test-connection-1');
      expect(preserved).toBeDefined();
      expect(preserved?.subscriptions).toEqual(['EURUSD', 'GBPUSD']);
    });
  });

  describe('Broker Adapter Factory', () => {
    it('should create broker adapters', () => {
      const config = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        type: 'demo' as any,
        server: 'demo.metaquotes.net:443',
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD', 'GBPUSD'],
        supportedTimeframes: ['M1', 'H1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        features: ['real_time_streaming', 'trade_execution'],
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      const adapter = brokerFactory.createAdapter('demo1', config);
      expect(adapter).toBeInstanceOf(MetaQuotesDemoAdapter);
      
      const retrievedAdapter = brokerFactory.getAdapter('demo1');
      expect(retrievedAdapter).toBe(adapter);
    });

    it('should discover broker capabilities', async () => {
      const config = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        type: 'demo' as any,
        server: 'demo.metaquotes.net:443',
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD'],
        supportedTimeframes: ['M1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        features: ['real_time_streaming', 'trade_execution'],
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      brokerFactory.createAdapter('demo1', config);
      
      const discoveries = await brokerFactory.discoverBrokers();
      expect(discoveries).toHaveLength(1);
      expect(discoveries[0].brokerId).toBe('demo1');
    });
  });

  describe('Trade Execution', () => {
    it('should execute trades successfully', async () => {
      // Create a demo adapter
      const config = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        type: 'demo' as any,
        server: 'demo.metaquotes.net:443',
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD'],
        supportedTimeframes: ['M1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        features: ['real_time_streaming', 'trade_execution'],
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      const adapter = brokerFactory.createAdapter('demo1', config);
      await adapter.connect();

      const tradeOrder = {
        id: 'test-order-1',
        symbol: 'EURUSD',
        type: 'buy' as any,
        volume: 0.1,
        price: 1.1850
      };

      const result = await tradeExecutor.executeTrade(tradeOrder, 'demo1');
      expect(result.orderId).toBe('test-order-1');
      expect(typeof result.executed).toBe('boolean');
    });

    it('should track execution metrics', async () => {
      const config = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        type: 'demo' as any,
        server: 'demo.metaquotes.net:443',
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD'],
        supportedTimeframes: ['M1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        features: ['real_time_streaming', 'trade_execution'],
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      const adapter = brokerFactory.createAdapter('demo1', config);
      await adapter.connect();

      // Execute a few trades
      for (let i = 0; i < 3; i++) {
        const tradeOrder = {
          id: `test-order-${i}`,
          symbol: 'EURUSD',
          type: 'buy' as any,
          volume: 0.1,
          price: 1.1850
        };
        
        await tradeExecutor.executeTrade(tradeOrder, 'demo1');
      }

      const metrics = tradeExecutor.getExecutionMetrics();
      expect(metrics.totalOrders).toBe(3);
      expect(metrics.brokerMetrics.has('demo1')).toBe(true);
    });
  });

  describe('Health Monitoring', () => {
    it('should monitor broker health', async () => {
      const config = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        type: 'demo' as any,
        server: 'demo.metaquotes.net:443',
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD'],
        supportedTimeframes: ['M1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        features: ['real_time_streaming', 'trade_execution'],
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      const adapter = brokerFactory.createAdapter('demo1', config);
      await adapter.connect();

      const healthMetrics = await healthMonitor.checkHealth(adapter);
      expect(healthMetrics.lastCheck).toBeDefined();
      expect(healthMetrics.connectionHealth).toBeGreaterThanOrEqual(0);
      expect(healthMetrics.connectionHealth).toBeLessThanOrEqual(1);
    });

    it('should generate alerts for poor health', async () => {
      const config = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        type: 'demo' as any,
        server: 'demo.metaquotes.net:443',
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD'],
        supportedTimeframes: ['M1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        features: ['real_time_streaming', 'trade_execution'],
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      const adapter = brokerFactory.createAdapter('demo1', config);
      // Don't connect to simulate poor health
      
      let alertFired = false;
      healthMonitor.on('alert', () => {
        alertFired = true;
      });

      await healthMonitor.checkHealth(adapter);
      
      // Give some time for alert processing
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(alertFired).toBe(true);
    });
  });

  describe('Account Synchronization', () => {
    it('should synchronize account information', async () => {
      const config = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        type: 'demo' as any,
        server: 'demo.metaquotes.net:443',
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD'],
        supportedTimeframes: ['M1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        features: ['real_time_streaming', 'trade_execution'],
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      const adapter = brokerFactory.createAdapter('demo1', config);
      await adapter.connect();

      const accountInfo = await accountService.syncAccount(adapter);
      expect(accountInfo.accountNumber).toBeDefined();
      expect(accountInfo.balance).toBeGreaterThanOrEqual(0);
      
      const retrievedAccount = accountService.getAccountInfo('demo1');
      expect(retrievedAccount?.accountNumber).toBe(accountInfo.accountNumber);
    });

    it('should synchronize positions', async () => {
      const config = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        type: 'demo' as any,
        server: 'demo.metaquotes.net:443',
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD'],
        supportedTimeframes: ['M1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        features: ['real_time_streaming', 'trade_execution'],
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      const adapter = brokerFactory.createAdapter('demo1', config);
      await adapter.connect();

      const positions = await accountService.syncPositions(adapter);
      expect(Array.isArray(positions)).toBe(true);
      
      const retrievedPositions = accountService.getPositions('demo1');
      expect(retrievedPositions).toEqual(positions);
    });
  });

  describe('Integration Test', () => {
    it('should handle complete trading workflow', async () => {
      // Initialize complete system
      const brokerConfig = {
        id: 'demo1',
        name: 'MetaQuotes Demo',
        type: 'demo' as any,
        server: 'demo.metaquotes.net:443',
        apiVersion: '5.0.37',
        maxOrdersPerSecond: 10,
        supportedSymbols: ['EURUSD'],
        supportedTimeframes: ['M1'],
        marginCalculation: 'hedged' as any,
        swapCalculation: 'points' as any,
        features: ['real_time_streaming', 'trade_execution'],
        connectionLimits: {
          maxConnections: 3,
          maxConcurrentRequests: 5,
          requestTimeout: 10000
        },
        authentication: {
          method: 'login_password' as any,
          requiresTwoFactor: false,
          sessionTimeout: 3600000
        },
        riskManagement: {
          maxLeverage: 100,
          marginRequirement: 0.01,
          maxPositionSize: 10,
          hedgingAllowed: true
        }
      };

      // Create and connect adapter
      const adapter = brokerFactory.createAdapter('demo1', brokerConfig);
      await adapter.connect();

      // Execute trade
      const tradeOrder = {
        id: 'integration-test-1',
        symbol: 'EURUSD',
        type: 'buy' as any,
        volume: 0.1,
        price: 1.1850
      };

      const tradeResult = await tradeExecutor.executeTrade(tradeOrder, 'demo1');
      expect(tradeResult.orderId).toBe('integration-test-1');

      // Sync account and positions
      const accountInfo = await accountService.syncAccount(adapter);
      const positions = await accountService.syncPositions(adapter);

      expect(accountInfo.accountNumber).toBeDefined();
      expect(Array.isArray(positions)).toBe(true);

      // Check health
      const healthMetrics = await healthMonitor.checkHealth(adapter);
      expect(healthMetrics.lastCheck).toBeDefined();

      // Verify metrics
      const executionMetrics = tradeExecutor.getExecutionMetrics();
      expect(executionMetrics.totalOrders).toBeGreaterThan(0);

      console.log('✅ Complete integration test passed');
    }, 15000); // 15 second timeout for integration test
  });
});