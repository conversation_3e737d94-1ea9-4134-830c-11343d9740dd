import { EventEmitter } from 'events';
import Decimal from 'decimal.js';
import {
  BrokerPerformanceMetrics,
  ExecutionQuality,
  BrokerBenchmark,
  QualityAlert,
  ExecutionEfficiencyMetrics
} from '@golddaddy/types';

interface BrokerStats {
  brokerId: string;
  totalExecutions: number;
  averageLatency: Decimal.Instance;
  averageSlippage: Decimal.Instance;
  successRate: Decimal.Instance;
  qualityScore: Decimal.Instance;
  volumeWeightedPrice: Decimal.Instance;
  lastUpdated: Date;
}

interface QualityDegradationThresholds {
  latencyIncreasePercent: Decimal.Instance;
  slippageIncreasePercent: Decimal.Instance;
  successRateDecreasePercent: Decimal.Instance;
  qualityScoreDecreasePercent: Decimal.Instance;
  minimumSampleSize: number;
}

export class ExecutionQualityAnalyzer extends EventEmitter {
  private brokerStats: Map<string, BrokerStats> = new Map();
  private executionHistory: Map<string, ExecutionQuality[]> = new Map();
  private degradationThresholds: QualityDegradationThresholds;
  private isMonitoring = false;
  private monitoringInterval?: NodeJS.Timeout;

  constructor() {
    super();
    this.degradationThresholds = {
      latencyIncreasePercent: new Decimal(25), // 25% increase triggers alert
      slippageIncreasePercent: new Decimal(50), // 50% increase triggers alert
      successRateDecreasePercent: new Decimal(10), // 10% decrease triggers alert
      qualityScoreDecreasePercent: new Decimal(15), // 15% decrease triggers alert
      minimumSampleSize: 10
    };
  }

  async analyzeExecutionQuality(
    brokerId: string,
    execution: ExecutionQuality
  ): Promise<BrokerPerformanceMetrics> {
    // Add execution to history
    if (!this.executionHistory.has(brokerId)) {
      this.executionHistory.set(brokerId, []);
    }
    
    const history = this.executionHistory.get(brokerId)!;
    history.push(execution);
    
    // Keep only last 1000 executions per broker
    if (history.length > 1000) {
      history.shift();
    }

    // Update broker statistics
    await this.updateBrokerStats(brokerId, execution);

    // Check for quality degradation
    await this.checkQualityDegradation(brokerId);

    // Calculate comprehensive performance metrics
    return this.calculatePerformanceMetrics(brokerId);
  }

  async benchmarkBrokers(
    brokerIds: string[],
    timeWindow: { start: Date; end: Date }
  ): Promise<BrokerBenchmark[]> {
    const benchmarks: BrokerBenchmark[] = [];

    for (const brokerId of brokerIds) {
      const history = this.executionHistory.get(brokerId) || [];
      const filteredHistory = history.filter(
        exec => exec.timestamp >= timeWindow.start && exec.timestamp <= timeWindow.end
      );

      if (filteredHistory.length === 0) {
        continue;
      }

      const benchmark = await this.createBrokerBenchmark(brokerId, filteredHistory);
      benchmarks.push(benchmark);
    }

    // Rank brokers by composite score
    benchmarks.sort((a, b) => b.compositeScore.minus(a.compositeScore).toNumber());
    
    // Assign rankings
    benchmarks.forEach((benchmark, index) => {
      benchmark.ranking = index + 1;
    });

    this.emit('brokerBenchmarkCompleted', {
      timeWindow,
      benchmarks,
      totalBrokers: benchmarks.length
    });

    return benchmarks;
  }

  async getExecutionEfficiencyMetrics(brokerId: string): Promise<ExecutionEfficiencyMetrics> {
    const history = this.executionHistory.get(brokerId) || [];
    
    if (history.length === 0) {
      throw new Error(`No execution history found for broker ${brokerId}`);
    }

    const metrics = {
      brokerId,
      totalExecutions: history.length,
      
      // Latency metrics
      averageLatency: this.calculateAverage(history.map(h => new Decimal(h.latency))),
      medianLatency: this.calculateMedian(history.map(h => new Decimal(h.latency))),
      p95Latency: this.calculatePercentile(history.map(h => new Decimal(h.latency)), 95),
      p99Latency: this.calculatePercentile(history.map(h => new Decimal(h.latency)), 99),
      
      // Slippage metrics
      averageSlippage: this.calculateAverage(history.map(h => h.slippage)),
      medianSlippage: this.calculateMedian(history.map(h => h.slippage)),
      positiveSlippageRate: this.calculatePositiveSlippageRate(history),
      
      // Success metrics
      successRate: this.calculateSuccessRate(history),
      fillRate: this.calculateFillRate(history),
      
      // Quality scoring
      averageQualityScore: this.calculateAverage(history.map(h => h.overallScore)),
      qualityConsistency: this.calculateQualityConsistency(history),
      
      // Market condition performance
      performanceByVolatility: this.groupPerformanceByVolatility(history),
      performanceByTimeOfDay: this.groupPerformanceByTimeOfDay(history),
      
      // Trend analysis
      latencyTrend: this.calculateTrend(history.map(h => ({ 
        timestamp: h.timestamp, 
        value: new Decimal(h.latency) 
      }))),
      slippageTrend: this.calculateTrend(history.map(h => ({ 
        timestamp: h.timestamp, 
        value: h.slippage 
      }))),
      
      analysisTimestamp: new Date()
    };

    return metrics;
  }

  async startContinuousMonitoring(intervalMs = 60000): Promise<void> {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performQualityCheck();
      } catch (error) {
        this.emit('monitoringError', { error, timestamp: new Date() });
      }
    }, intervalMs);

    this.emit('monitoringStarted', { interval: intervalMs });
  }

  async stopContinuousMonitoring(): Promise<void> {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    this.emit('monitoringStopped', { timestamp: new Date() });
  }

  async compareExecutionAcrossBrokers(
    brokerIds: string[],
    instrument: string,
    timeWindow: { start: Date; end: Date }
  ): Promise<{ [brokerId: string]: ExecutionEfficiencyMetrics }> {
    const comparison: { [brokerId: string]: ExecutionEfficiencyMetrics } = {};

    for (const brokerId of brokerIds) {
      const history = this.executionHistory.get(brokerId) || [];
      const filteredHistory = history.filter(
        exec => exec.timestamp >= timeWindow.start && 
                exec.timestamp <= timeWindow.end &&
                exec.instrument === instrument
      );

      if (filteredHistory.length > 0) {
        // Create temporary history for this comparison
        const originalHistory = this.executionHistory.get(brokerId);
        this.executionHistory.set(brokerId, filteredHistory);
        
        try {
          comparison[brokerId] = await this.getExecutionEfficiencyMetrics(brokerId);
        } finally {
          // Restore original history
          if (originalHistory) {
            this.executionHistory.set(brokerId, originalHistory);
          }
        }
      }
    }

    this.emit('brokerComparisonCompleted', {
      instrument,
      timeWindow,
      brokers: Object.keys(comparison),
      metrics: comparison
    });

    return comparison;
  }

  private async updateBrokerStats(brokerId: string, execution: ExecutionQuality): Promise<void> {
    const existing = this.brokerStats.get(brokerId);
    const history = this.executionHistory.get(brokerId) || [];

    if (!existing) {
      this.brokerStats.set(brokerId, {
        brokerId,
        totalExecutions: 1,
        averageLatency: new Decimal(execution.latency),
        averageSlippage: execution.slippage,
        successRate: execution.success ? new Decimal(100) : new Decimal(0),
        qualityScore: execution.overallScore,
        volumeWeightedPrice: execution.executedPrice || new Decimal(0),
        lastUpdated: new Date()
      });
      return;
    }

    // Recalculate running averages
    const totalExecutions = existing.totalExecutions + 1;
    const newAverageLatency = existing.averageLatency
      .times(existing.totalExecutions)
      .plus(execution.latency)
      .div(totalExecutions);
    
    const newAverageSlippage = existing.averageSlippage
      .times(existing.totalExecutions)
      .plus(execution.slippage)
      .div(totalExecutions);

    const successCount = history.filter(h => h.success).length;
    const newSuccessRate = new Decimal(successCount).div(totalExecutions).times(100);

    const newQualityScore = existing.qualityScore
      .times(existing.totalExecutions)
      .plus(execution.overallScore)
      .div(totalExecutions);

    this.brokerStats.set(brokerId, {
      ...existing,
      totalExecutions,
      averageLatency: newAverageLatency,
      averageSlippage: newAverageSlippage,
      successRate: newSuccessRate,
      qualityScore: newQualityScore,
      lastUpdated: new Date()
    });
  }

  private async checkQualityDegradation(brokerId: string): Promise<void> {
    const history = this.executionHistory.get(brokerId) || [];
    
    if (history.length < this.degradationThresholds.minimumSampleSize * 2) {
      return; // Not enough data for comparison
    }

    const recentCount = Math.floor(history.length * 0.3); // Last 30% of executions
    const baselineCount = Math.floor(history.length * 0.3); // Previous 30% for comparison
    
    const recentExecutions = history.slice(-recentCount);
    const baselineExecutions = history.slice(
      -(recentCount + baselineCount), 
      -recentCount
    );

    const alerts: QualityAlert[] = [];

    // Check latency degradation
    const recentLatency = this.calculateAverage(recentExecutions.map(e => new Decimal(e.latency)));
    const baselineLatency = this.calculateAverage(baselineExecutions.map(e => new Decimal(e.latency)));
    const latencyIncrease = recentLatency.minus(baselineLatency).div(baselineLatency).times(100);
    
    if (latencyIncrease.gte(this.degradationThresholds.latencyIncreasePercent)) {
      alerts.push({
        type: 'LATENCY_DEGRADATION',
        brokerId,
        severity: this.calculateSeverity(latencyIncrease, this.degradationThresholds.latencyIncreasePercent),
        message: `Latency increased by ${latencyIncrease.toFixed(1)}%`,
        currentValue: recentLatency,
        baselineValue: baselineLatency,
        timestamp: new Date()
      });
    }

    // Check slippage degradation
    const recentSlippage = this.calculateAverage(recentExecutions.map(e => e.slippage.abs()));
    const baselineSlippage = this.calculateAverage(baselineExecutions.map(e => e.slippage.abs()));
    const slippageIncrease = recentSlippage.minus(baselineSlippage).div(baselineSlippage).times(100);
    
    if (slippageIncrease.gte(this.degradationThresholds.slippageIncreasePercent)) {
      alerts.push({
        type: 'SLIPPAGE_DEGRADATION',
        brokerId,
        severity: this.calculateSeverity(slippageIncrease, this.degradationThresholds.slippageIncreasePercent),
        message: `Slippage increased by ${slippageIncrease.toFixed(1)}%`,
        currentValue: recentSlippage,
        baselineValue: baselineSlippage,
        timestamp: new Date()
      });
    }

    // Check success rate degradation
    const recentSuccessRate = new Decimal(recentExecutions.filter(e => e.success).length)
      .div(recentExecutions.length).times(100);
    const baselineSuccessRate = new Decimal(baselineExecutions.filter(e => e.success).length)
      .div(baselineExecutions.length).times(100);
    const successRateDecrease = baselineSuccessRate.minus(recentSuccessRate);
    
    if (successRateDecrease.gte(this.degradationThresholds.successRateDecreasePercent)) {
      alerts.push({
        type: 'SUCCESS_RATE_DEGRADATION',
        brokerId,
        severity: this.calculateSeverity(successRateDecrease, this.degradationThresholds.successRateDecreasePercent),
        message: `Success rate decreased by ${successRateDecrease.toFixed(1)}%`,
        currentValue: recentSuccessRate,
        baselineValue: baselineSuccessRate,
        timestamp: new Date()
      });
    }

    // Emit alerts
    for (const alert of alerts) {
      this.emit('qualityDegradationAlert', alert);
    }
  }

  private calculatePerformanceMetrics(brokerId: string): BrokerPerformanceMetrics {
    const stats = this.brokerStats.get(brokerId);
    const history = this.executionHistory.get(brokerId) || [];

    if (!stats) {
      throw new Error(`No statistics found for broker ${brokerId}`);
    }

    return {
      brokerId,
      totalExecutions: stats.totalExecutions,
      averageLatency: stats.averageLatency,
      averageSlippage: stats.averageSlippage,
      successRate: stats.successRate,
      qualityScore: stats.qualityScore,
      volumeWeightedPrice: stats.volumeWeightedPrice,
      
      // Additional metrics from history analysis
      medianLatency: this.calculateMedian(history.map(h => new Decimal(h.latency))),
      p95Latency: this.calculatePercentile(history.map(h => new Decimal(h.latency)), 95),
      medianSlippage: this.calculateMedian(history.map(h => h.slippage)),
      positiveSlippageRate: this.calculatePositiveSlippageRate(history),
      fillRate: this.calculateFillRate(history),
      
      lastUpdated: stats.lastUpdated,
      analysisWindow: {
        start: history.length > 0 ? history[0].timestamp : new Date(),
        end: history.length > 0 ? history[history.length - 1].timestamp : new Date()
      }
    };
  }

  private async createBrokerBenchmark(
    brokerId: string, 
    history: ExecutionQuality[]
  ): Promise<BrokerBenchmark> {
    const latencies = history.map(h => new Decimal(h.latency));
    const slippages = history.map(h => h.slippage);
    const qualityScores = history.map(h => h.overallScore);

    // Composite score calculation (weighted average)
    const latencyScore = this.normalizeMetric(
      this.calculateAverage(latencies), 
      new Decimal(50), // Excellent latency target (50ms)
      new Decimal(200), // Poor latency threshold (200ms)
      true // Lower is better
    );

    const slippageScore = this.normalizeMetric(
      this.calculateAverage(slippages.map(s => s.abs())),
      new Decimal(0.1), // Excellent slippage (0.1 pips)
      new Decimal(2.0), // Poor slippage (2 pips)
      true // Lower is better
    );

    const successScore = new Decimal(history.filter(h => h.success).length)
      .div(history.length).times(100);

    const qualityScore = this.calculateAverage(qualityScores);

    // Weighted composite score
    const compositeScore = latencyScore.times(0.3)
      .plus(slippageScore.times(0.3))
      .plus(successScore.times(0.2))
      .plus(qualityScore.times(0.2));

    return {
      brokerId,
      ranking: 0, // Will be set during sorting
      compositeScore,
      
      latencyMetrics: {
        average: this.calculateAverage(latencies),
        median: this.calculateMedian(latencies),
        p95: this.calculatePercentile(latencies, 95),
        p99: this.calculatePercentile(latencies, 99)
      },
      
      slippageMetrics: {
        average: this.calculateAverage(slippages),
        median: this.calculateMedian(slippages),
        positiveSlippageRate: this.calculatePositiveSlippageRate(history)
      },
      
      reliabilityMetrics: {
        successRate: new Decimal(history.filter(h => h.success).length).div(history.length).times(100),
        fillRate: this.calculateFillRate(history),
        qualityConsistency: this.calculateQualityConsistency(history)
      },
      
      volumeWeightedMetrics: {
        averagePrice: this.calculateVolumeWeightedPrice(history),
        totalVolume: this.calculateTotalVolume(history)
      },
      
      sampleSize: history.length,
      analysisTimestamp: new Date()
    };
  }

  private async performQualityCheck(): Promise<void> {
    for (const [brokerId] of this.brokerStats) {
      await this.checkQualityDegradation(brokerId);
    }
  }

  private calculateAverage(values: Decimal[]): Decimal {
    if (values.length === 0) return new Decimal(0);
    return values.reduce((sum, val) => sum.plus(val), new Decimal(0)).div(values.length);
  }

  private calculateMedian(values: Decimal[]): Decimal {
    if (values.length === 0) return new Decimal(0);
    const sorted = [...values].sort((a, b) => a.minus(b).toNumber());
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 
      ? sorted[mid - 1].plus(sorted[mid]).div(2)
      : sorted[mid];
  }

  private calculatePercentile(values: Decimal[], percentile: number): Decimal {
    if (values.length === 0) return new Decimal(0);
    const sorted = [...values].sort((a, b) => a.minus(b).toNumber());
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[Math.max(0, index)];
  }

  private calculatePositiveSlippageRate(history: ExecutionQuality[]): Decimal {
    const positiveSlippage = history.filter(h => h.slippage.gt(0));
    return new Decimal(positiveSlippage.length).div(history.length).times(100);
  }

  private calculateSuccessRate(history: ExecutionQuality[]): Decimal {
    const successCount = history.filter(h => h.success).length;
    return new Decimal(successCount).div(history.length).times(100);
  }

  private calculateFillRate(history: ExecutionQuality[]): Decimal {
    const fillCount = history.filter(h => h.executedPrice && h.executedPrice.gt(0)).length;
    return new Decimal(fillCount).div(history.length).times(100);
  }

  private calculateQualityConsistency(history: ExecutionQuality[]): Decimal {
    const scores = history.map(h => h.overallScore);
    const average = this.calculateAverage(scores);
    const variance = scores.reduce((sum, score) => 
      sum.plus(score.minus(average).pow(2)), new Decimal(0)
    ).div(scores.length);
    const standardDeviation = variance.sqrt();
    
    // Consistency as inverse of coefficient of variation (lower CV = higher consistency)
    const coefficientOfVariation = standardDeviation.div(average);
    const result = new Decimal(100).minus(coefficientOfVariation.times(100));
    return Decimal.max(result, 0);
  }

  private calculateVolumeWeightedPrice(history: ExecutionQuality[]): Decimal {
    let totalValue = new Decimal(0);
    let totalVolume = new Decimal(0);

    for (const execution of history) {
      if (execution.executedPrice && execution.volume) {
        const value = execution.executedPrice.times(execution.volume);
        totalValue = totalValue.plus(value);
        totalVolume = totalVolume.plus(execution.volume);
      }
    }

    return totalVolume.gt(0) ? totalValue.div(totalVolume) : new Decimal(0);
  }

  private calculateTotalVolume(history: ExecutionQuality[]): Decimal {
    return history.reduce((sum, exec) => 
      sum.plus(exec.volume || new Decimal(0)), new Decimal(0)
    );
  }

  private groupPerformanceByVolatility(history: ExecutionQuality[]): { [key: string]: any } {
    const groups = {
      low: history.filter(h => h.marketVolatility && h.marketVolatility.lte(0.5)),
      medium: history.filter(h => h.marketVolatility && h.marketVolatility.gt(0.5) && h.marketVolatility.lte(1.5)),
      high: history.filter(h => h.marketVolatility && h.marketVolatility.gt(1.5))
    };

    return Object.entries(groups).reduce((result, [key, executions]) => {
      result[key] = executions.length > 0 ? {
        count: executions.length,
        averageLatency: this.calculateAverage(executions.map(e => new Decimal(e.latency))),
        averageSlippage: this.calculateAverage(executions.map(e => e.slippage)),
        successRate: this.calculateSuccessRate(executions)
      } : null;
      return result;
    }, {} as any);
  }

  private groupPerformanceByTimeOfDay(history: ExecutionQuality[]): { [key: string]: any } {
    const groups = {
      morning: history.filter(h => {
        const hour = h.timestamp.getHours();
        return hour >= 6 && hour < 12;
      }),
      afternoon: history.filter(h => {
        const hour = h.timestamp.getHours();
        return hour >= 12 && hour < 18;
      }),
      evening: history.filter(h => {
        const hour = h.timestamp.getHours();
        return hour >= 18 || hour < 6;
      })
    };

    return Object.entries(groups).reduce((result, [key, executions]) => {
      result[key] = executions.length > 0 ? {
        count: executions.length,
        averageLatency: this.calculateAverage(executions.map(e => new Decimal(e.latency))),
        averageSlippage: this.calculateAverage(executions.map(e => e.slippage)),
        successRate: this.calculateSuccessRate(executions)
      } : null;
      return result;
    }, {} as any);
  }

  private calculateTrend(data: { timestamp: Date; value: Decimal }[]): 'IMPROVING' | 'STABLE' | 'DEGRADING' {
    if (data.length < 10) return 'STABLE';

    const recent = data.slice(-Math.floor(data.length * 0.3));
    const baseline = data.slice(0, Math.floor(data.length * 0.3));

    const recentAvg = this.calculateAverage(recent.map(d => d.value));
    const baselineAvg = this.calculateAverage(baseline.map(d => d.value));

    const changePercent = recentAvg.minus(baselineAvg).div(baselineAvg).times(100).abs();

    if (changePercent.lt(5)) return 'STABLE';
    return recentAvg.lt(baselineAvg) ? 'IMPROVING' : 'DEGRADING';
  }

  private normalizeMetric(
    value: Decimal, 
    excellentThreshold: Decimal, 
    poorThreshold: Decimal, 
    lowerIsBetter: boolean
  ): Decimal {
    if (lowerIsBetter) {
      if (value.lte(excellentThreshold)) return new Decimal(100);
      if (value.gte(poorThreshold)) return new Decimal(0);
      return new Decimal(100).minus(
        value.minus(excellentThreshold)
          .div(poorThreshold.minus(excellentThreshold))
          .times(100)
      );
    } else {
      if (value.gte(excellentThreshold)) return new Decimal(100);
      if (value.lte(poorThreshold)) return new Decimal(0);
      return value.minus(poorThreshold)
        .div(excellentThreshold.minus(poorThreshold))
        .times(100);
    }
  }

  private calculateSeverity(current: Decimal, threshold: Decimal.Instance): 'LOW' | 'MEDIUM' | 'HIGH' {
    const ratio = current.div(threshold);
    if (ratio.gte(3)) return 'HIGH';
    if (ratio.gte(2)) return 'MEDIUM';
    return 'LOW';
  }
}