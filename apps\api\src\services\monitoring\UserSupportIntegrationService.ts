import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';

/**
 * User Support Integration Service
 * Provides comprehensive support tooling with monitoring data integration,
 * automated troubleshooting assistance, and user context analysis
 */
export class UserSupportIntegrationService extends EventEmitter {
  private prisma: PrismaClient;
  private ticketProcessingInterval: NodeJS.Timeout | null = null;
  private readonly PROCESSING_INTERVAL_MS = 30000; // 30 seconds

  // Support ticket priority levels
  private readonly PRIORITY_LEVELS = {
    low: { weight: 1, responseTarget: 24 * 60, escalationTime: 48 * 60 }, // hours -> minutes
    medium: { weight: 2, responseTarget: 8 * 60, escalationTime: 24 * 60 },
    high: { weight: 3, responseTarget: 4 * 60, escalationTime: 8 * 60 },
    critical: { weight: 4, responseTarget: 1 * 60, escalationTime: 2 * 60 },
    emergency: { weight: 5, responseTarget: 15, escalationTime: 30 }, // minutes
  };

  // Integration points with monitoring services
  private readonly MONITORING_INTEGRATIONS = {
    system_monitoring: {
      service: 'SystemMonitoringService',
      relevantMetrics: ['cpu', 'memory', 'latency', 'errors'],
    },
    user_activity: {
      service: 'UserActivityMonitoringService',
      relevantMetrics: ['login_attempts', 'suspicious_activity', 'usage_patterns'],
    },
    mt5_integration: {
      service: 'MT5HealthService',
      relevantMetrics: ['connection_health', 'execution_quality', 'market_data'],
    },
    risk_management: {
      service: 'RiskManagementMonitoringService',
      relevantMetrics: ['risk_scores', 'position_health', 'alerts'],
    },
  };

  // Support ticket state
  private supportTickets: Map<string, SupportTicket> = new Map();
  private troubleshootingSessions: Map<string, TroubleshootingSession> = new Map();
  private userContextCache: Map<string, UserContext> = new Map();
  
  constructor() {
    super();
    this.prisma = new PrismaClient();
  }

  /**
   * Start user support integration service
   */
  async startService(): Promise<void> {
    if (this.ticketProcessingInterval) {
      console.log('User support integration service is already running');
      return;
    }

    console.log('Starting user support integration service...');
    
    // Start ticket processing loop
    this.ticketProcessingInterval = setInterval(async () => {
      try {
        await this.processTickets();
        await this.updateTroubleshootingSessions();
        await this.refreshUserContexts();
      } catch (error) {
        console.error('Error during support ticket processing:', error);
        this.emit('processing_error', error);
      }
    }, this.PROCESSING_INTERVAL_MS);

    // Load existing tickets
    await this.loadActiveTickets();

    this.emit('service_started', {
      service: 'user_support_integration',
      interval: this.PROCESSING_INTERVAL_MS,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Stop user support integration service
   */
  stopService(): void {
    if (this.ticketProcessingInterval) {
      clearInterval(this.ticketProcessingInterval);
      this.ticketProcessingInterval = null;
      console.log('User support integration service stopped');
      
      this.emit('service_stopped', {
        service: 'user_support_integration',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Create a new support ticket with automated context gathering
   */
  async createSupportTicket(ticketData: CreateTicketRequest): Promise<SupportTicket> {
    const ticket: SupportTicket = {
      id: crypto.randomUUID(),
      userId: ticketData.userId,
      userEmail: ticketData.userEmail,
      subject: ticketData.subject,
      description: ticketData.description,
      priority: await this.calculateTicketPriority(ticketData),
      status: 'open',
      category: ticketData.category || 'general',
      tags: ticketData.tags || [],
      assignedTo: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      responseTarget: this.calculateResponseTarget(await this.calculateTicketPriority(ticketData)),
      escalationTarget: this.calculateEscalationTarget(await this.calculateTicketPriority(ticketData)),
      metadata: ticketData.metadata || {},
      attachments: ticketData.attachments || [],
      conversationHistory: [],
      monitoringContext: {},
      troubleshootingData: {},
      resolutionNotes: null,
      resolvedAt: null,
      resolvedBy: null,
      customerSatisfaction: null,
    };

    // Gather monitoring context
    ticket.monitoringContext = await this.gatherMonitoringContext(ticket);
    
    // Perform automated analysis
    ticket.troubleshootingData = await this.performAutomatedAnalysis(ticket);
    
    // Generate suggested actions
    ticket.suggestedActions = await this.generateSuggestedActions(ticket);

    // Store ticket
    this.supportTickets.set(ticket.id, ticket);
    await this.storeTicket(ticket);

    // Auto-assign if possible
    await this.attemptAutoAssignment(ticket);

    // Emit ticket creation event
    this.emit('ticket_created', ticket);

    // Send notifications
    await this.sendTicketNotifications(ticket, 'created');

    return ticket;
  }

  /**
   * Gather comprehensive monitoring context for a support ticket
   */
  async gatherMonitoringContext(ticket: SupportTicket): Promise<MonitoringContext> {
    const userId = ticket.userId;
    const timeRange = '24h'; // Look back 24 hours for context
    
    const context: MonitoringContext = {
      userId,
      timeRange,
      gatheredAt: new Date().toISOString(),
      systemHealth: {},
      userActivity: {},
      mt5Health: {},
      riskMetrics: {},
      alerts: [],
      correlatedIssues: [],
    };

    try {
      // Gather system monitoring data
      context.systemHealth = await this.getSystemHealthForUser(userId, timeRange);
      
      // Gather user activity data
      context.userActivity = await this.getUserActivityContext(userId, timeRange);
      
      // Gather MT5 integration data
      context.mt5Health = await this.getMT5HealthForUser(userId, timeRange);
      
      // Gather risk management data
      context.riskMetrics = await this.getRiskMetricsForUser(userId, timeRange);
      
      // Gather relevant alerts
      context.alerts = await this.getAlertsForUser(userId, timeRange);
      
      // Find correlated issues
      context.correlatedIssues = await this.findCorrelatedIssues(ticket);
      
    } catch (error) {
      console.error('Error gathering monitoring context:', error);
      context.errors = [error.message];
    }

    return context;
  }

  /**
   * Perform automated analysis and diagnostics
   */
  async performAutomatedAnalysis(ticket: SupportTicket): Promise<TroubleshootingData> {
    const analysis: TroubleshootingData = {
      analysisId: crypto.randomUUID(),
      performedAt: new Date().toISOString(),
      diagnostics: [],
      rootCauseAnalysis: [],
      impactAssessment: {},
      recommendedActions: [],
      automatedChecks: [],
      confidence: 0,
    };

    // Analyze ticket content for keywords and patterns
    const contentAnalysis = this.analyzeTicketContent(ticket);
    analysis.diagnostics.push(...contentAnalysis.diagnostics);

    // Perform system health checks
    const healthChecks = await this.performSystemHealthChecks(ticket);
    analysis.automatedChecks.push(...healthChecks);

    // Analyze user activity patterns
    const activityAnalysis = await this.analyzeUserActivityPatterns(ticket);
    analysis.diagnostics.push(...activityAnalysis);

    // Check for known issues
    const knownIssues = await this.checkForKnownIssues(ticket);
    analysis.rootCauseAnalysis.push(...knownIssues);

    // Assess impact
    analysis.impactAssessment = await this.assessTicketImpact(ticket);

    // Calculate overall confidence
    analysis.confidence = this.calculateAnalysisConfidence(analysis);

    return analysis;
  }

  /**
   * Generate suggested actions based on analysis
   */
  async generateSuggestedActions(ticket: SupportTicket): Promise<SuggestedAction[]> {
    const actions: SuggestedAction[] = [];
    const analysis = ticket.troubleshootingData;
    const context = ticket.monitoringContext;

    // Actions based on category
    switch (ticket.category) {
      case 'login_issues':
        actions.push(...this.generateLoginTroubleshootingActions(context));
        break;
      case 'trading_issues':
        actions.push(...this.generateTradingTroubleshootingActions(context));
        break;
      case 'account_issues':
        actions.push(...this.generateAccountTroubleshootingActions(context));
        break;
      case 'technical_issues':
        actions.push(...this.generateTechnicalTroubleshootingActions(context));
        break;
    }

    // Actions based on monitoring data
    if (context.systemHealth?.issues?.length > 0) {
      actions.push({
        type: 'diagnostic',
        title: 'System Health Check',
        description: 'Review system health metrics for potential issues',
        priority: 'high',
        automatable: false,
        estimatedTime: 15,
        category: 'investigation',
        instructions: 'Check system monitoring dashboard for recent performance issues',
        relatedData: context.systemHealth.issues,
      });
    }

    if (context.alerts?.length > 0) {
      actions.push({
        type: 'alert_review',
        title: 'Review Related Alerts',
        description: 'Analyze alerts that occurred around the time of the issue',
        priority: 'medium',
        automatable: false,
        estimatedTime: 10,
        category: 'investigation',
        instructions: 'Review alerts in monitoring system for correlation with user issue',
        relatedData: context.alerts,
      });
    }

    return actions.sort((a, b) => {
      const priorityWeight = { high: 3, medium: 2, low: 1 };
      return priorityWeight[b.priority] - priorityWeight[a.priority];
    });
  }

  /**
   * Start an interactive troubleshooting session
   */
  async startTroubleshootingSession(ticketId: string, initiatedBy: string): Promise<TroubleshootingSession> {
    const ticket = this.supportTickets.get(ticketId);
    if (!ticket) {
      throw new Error(`Ticket ${ticketId} not found`);
    }

    const session: TroubleshootingSession = {
      id: crypto.randomUUID(),
      ticketId,
      initiatedBy,
      startedAt: new Date().toISOString(),
      status: 'active',
      steps: [],
      currentStep: 0,
      findings: {},
      recommendations: [],
      completedActions: [],
      nextActions: [],
      sessionNotes: '',
      endedAt: null,
    };

    // Initialize with suggested actions from ticket
    if (ticket.suggestedActions) {
      session.nextActions = ticket.suggestedActions.map(action => ({
        actionId: crypto.randomUUID(),
        type: action.type,
        title: action.title,
        description: action.description,
        priority: action.priority,
        status: 'pending',
        assignedTo: initiatedBy,
        createdAt: new Date().toISOString(),
      }));
    }

    this.troubleshootingSessions.set(session.id, session);
    
    this.emit('troubleshooting_session_started', {
      sessionId: session.id,
      ticketId,
      initiatedBy,
    });

    return session;
  }

  /**
   * Update troubleshooting session with new findings
   */
  async updateTroubleshootingSession(
    sessionId: string,
    update: TroubleshootingSessionUpdate
  ): Promise<TroubleshootingSession> {
    const session = this.troubleshootingSessions.get(sessionId);
    if (!session) {
      throw new Error(`Troubleshooting session ${sessionId} not found`);
    }

    if (update.stepCompleted) {
      session.steps.push(update.stepCompleted);
      session.currentStep += 1;
    }

    if (update.findings) {
      session.findings = { ...session.findings, ...update.findings };
    }

    if (update.actionCompleted) {
      const actionIndex = session.nextActions.findIndex(a => a.actionId === update.actionCompleted.actionId);
      if (actionIndex >= 0) {
        session.nextActions[actionIndex].status = 'completed';
        session.nextActions[actionIndex].completedAt = new Date().toISOString();
        session.nextActions[actionIndex].result = update.actionCompleted.result;
        session.completedActions.push(session.nextActions[actionIndex]);
      }
    }

    if (update.newAction) {
      session.nextActions.push({
        actionId: crypto.randomUUID(),
        ...update.newAction,
        status: 'pending',
        createdAt: new Date().toISOString(),
      });
    }

    if (update.recommendations) {
      session.recommendations.push(...update.recommendations);
    }

    if (update.sessionNotes) {
      session.sessionNotes += `\n[${new Date().toISOString()}] ${update.sessionNotes}`;
    }

    this.emit('troubleshooting_session_updated', {
      sessionId: session.id,
      update,
    });

    return session;
  }

  /**
   * Get comprehensive user context for support
   */
  async getUserSupportContext(userId: string): Promise<UserContext> {
    let context = this.userContextCache.get(userId);
    
    if (!context || this.isContextStale(context)) {
      context = await this.buildUserContext(userId);
      this.userContextCache.set(userId, context);
    }
    
    return context;
  }

  /**
   * Search for similar tickets and known solutions
   */
  async searchSimilarTickets(ticket: SupportTicket): Promise<SimilarTicket[]> {
    // Mock implementation - would use actual search/ML in production
    return [
      {
        ticketId: 'ticket_456',
        similarity: 0.85,
        subject: 'Similar login issue',
        resolution: 'Reset user session and clear cache',
        resolvedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        category: ticket.category,
        tags: ['login', 'session', 'cache'],
      },
      {
        ticketId: 'ticket_789',
        similarity: 0.72,
        subject: 'Related authentication problem',
        resolution: 'Updated user permissions',
        resolvedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        category: ticket.category,
        tags: ['auth', 'permissions'],
      },
    ];
  }

  /**
   * Generate automated support summary
   */
  generateSupportSummary(): SupportSummary {
    const tickets = Array.from(this.supportTickets.values());
    const now = Date.now();
    const last24h = now - (24 * 60 * 60 * 1000);
    const last7d = now - (7 * 24 * 60 * 60 * 1000);

    return {
      totalTickets: tickets.length,
      openTickets: tickets.filter(t => t.status === 'open').length,
      inProgressTickets: tickets.filter(t => t.status === 'in_progress').length,
      resolvedTickets: tickets.filter(t => t.status === 'resolved').length,
      last24h: tickets.filter(t => new Date(t.createdAt).getTime() > last24h).length,
      last7d: tickets.filter(t => new Date(t.createdAt).getTime() > last7d).length,
      byPriority: this.groupTicketsByPriority(tickets),
      byCategory: this.groupTicketsByCategory(tickets),
      avgResolutionTime: this.calculateAvgResolutionTime(tickets),
      escalatedTickets: tickets.filter(t => this.isTicketEscalated(t)).length,
      activeSessions: this.troubleshootingSessions.size,
      automationRate: this.calculateAutomationRate(tickets),
      customerSatisfaction: this.calculateAvgSatisfaction(tickets),
    };
  }

  // Private helper methods
  private async calculateTicketPriority(ticketData: CreateTicketRequest): Promise<'low' | 'medium' | 'high' | 'critical' | 'emergency'> {
    // Analyze ticket content and user context to determine priority
    let priority: 'low' | 'medium' | 'high' | 'critical' | 'emergency' = ticketData.priority || 'medium';
    
    // Check for critical keywords
    const criticalKeywords = ['trading', 'money', 'loss', 'urgent', 'critical', 'emergency'];
    const hasUrgentKeywords = criticalKeywords.some(keyword => 
      ticketData.subject.toLowerCase().includes(keyword) || 
      ticketData.description.toLowerCase().includes(keyword)
    );
    
    if (hasUrgentKeywords && priority !== 'emergency') {
      priority = priority === 'low' ? 'medium' : priority === 'medium' ? 'high' : 'critical';
    }
    
    return priority;
  }

  private calculateResponseTarget(priority: 'low' | 'medium' | 'high' | 'critical' | 'emergency'): string {
    const targetMinutes = this.PRIORITY_LEVELS[priority].responseTarget;
    return new Date(Date.now() + targetMinutes * 60 * 1000).toISOString();
  }

  private calculateEscalationTarget(priority: 'low' | 'medium' | 'high' | 'critical' | 'emergency'): string {
    const targetMinutes = this.PRIORITY_LEVELS[priority].escalationTime;
    return new Date(Date.now() + targetMinutes * 60 * 1000).toISOString();
  }

  private analyzeTicketContent(ticket: SupportTicket): { diagnostics: Diagnostic[] } {
    const diagnostics: Diagnostic[] = [];
    
    // Simple keyword-based analysis (would use NLP in production)
    if (ticket.description.toLowerCase().includes('login')) {
      diagnostics.push({
        type: 'content_analysis',
        finding: 'Login-related issue detected',
        confidence: 0.8,
        category: 'authentication',
        recommendation: 'Check user authentication status and session validity',
      });
    }
    
    return { diagnostics };
  }

  private async performSystemHealthChecks(ticket: SupportTicket): Promise<AutomatedCheck[]> {
    // Mock system health checks
    return [
      {
        checkType: 'system_status',
        status: 'passed',
        result: 'All systems operational',
        timestamp: new Date().toISOString(),
        details: { cpu: 45, memory: 67, latency: 23 },
      },
      {
        checkType: 'user_account',
        status: 'warning',
        result: 'User has recent failed login attempts',
        timestamp: new Date().toISOString(),
        details: { failedLogins: 3, lastSuccess: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() },
      },
    ];
  }

  // Mock data gathering methods (would connect to actual services in production)
  private async getSystemHealthForUser(userId: string, timeRange: string): Promise<any> {
    return {
      overallHealth: 85,
      issues: ['High latency detected during user session'],
      metrics: { cpu: 45, memory: 67, latency: 125 },
    };
  }

  private async getUserActivityContext(userId: string, timeRange: string): Promise<any> {
    return {
      loginAttempts: 5,
      lastLogin: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
      suspiciousActivity: false,
      tradingActivity: 3,
    };
  }

  private async getMT5HealthForUser(userId: string, timeRange: string): Promise<any> {
    return {
      connectionHealth: 92,
      executionQuality: 88,
      lastTrade: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
    };
  }

  private async getRiskMetricsForUser(userId: string, timeRange: string): Promise<any> {
    return {
      riskScore: 75,
      alerts: 2,
      positionHealth: 'good',
    };
  }

  private async getAlertsForUser(userId: string, timeRange: string): Promise<any[]> {
    return [
      {
        type: 'risk_alert',
        severity: 'medium',
        message: 'Position size approaching limit',
        timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
      },
    ];
  }

  private async findCorrelatedIssues(ticket: SupportTicket): Promise<any[]> {
    return [
      {
        type: 'system_issue',
        description: 'Increased latency during the time of user report',
        correlation: 0.7,
        timeframe: '15 minutes around incident',
      },
    ];
  }

  // More helper methods...
  private generateLoginTroubleshootingActions(context: MonitoringContext): SuggestedAction[] {
    return [
      {
        type: 'verification',
        title: 'Verify User Credentials',
        description: 'Check if user credentials are valid and not expired',
        priority: 'high',
        automatable: true,
        estimatedTime: 5,
        category: 'authentication',
        instructions: 'Run user credential validation check',
      },
    ];
  }

  private generateTradingTroubleshootingActions(context: MonitoringContext): SuggestedAction[] {
    return [
      {
        type: 'diagnostic',
        title: 'Check MT5 Connection',
        description: 'Verify MT5 bridge connectivity and health',
        priority: 'high',
        automatable: true,
        estimatedTime: 10,
        category: 'trading',
        instructions: 'Check MT5 bridge status and connection quality',
      },
    ];
  }

  private generateAccountTroubleshootingActions(context: MonitoringContext): SuggestedAction[] {
    return [
      {
        type: 'verification',
        title: 'Verify Account Status',
        description: 'Check account status and permissions',
        priority: 'medium',
        automatable: true,
        estimatedTime: 5,
        category: 'account',
        instructions: 'Run account status verification',
      },
    ];
  }

  private generateTechnicalTroubleshootingActions(context: MonitoringContext): SuggestedAction[] {
    return [
      {
        type: 'diagnostic',
        title: 'System Health Check',
        description: 'Review system performance metrics',
        priority: 'medium',
        automatable: true,
        estimatedTime: 15,
        category: 'technical',
        instructions: 'Check system monitoring dashboard',
      },
    ];
  }

  // Additional helper methods for processing and analysis...
  private async processTickets(): Promise<void> {
    // Process ticket updates, escalations, etc.
  }

  private async updateTroubleshootingSessions(): Promise<void> {
    // Update active troubleshooting sessions
  }

  private async refreshUserContexts(): Promise<void> {
    // Refresh stale user contexts
  }

  private async loadActiveTickets(): Promise<void> {
    // Load existing tickets from storage
  }

  private async buildUserContext(userId: string): Promise<UserContext> {
    // Build comprehensive user context
    return {
      userId,
      lastUpdated: new Date().toISOString(),
      accountInfo: {},
      activitySummary: {},
      systemInteractions: {},
      supportHistory: {},
    };
  }

  private isContextStale(context: UserContext): boolean {
    const staleTime = 5 * 60 * 1000; // 5 minutes
    return Date.now() - new Date(context.lastUpdated).getTime() > staleTime;
  }

  // More implementation methods...
  private async analyzeUserActivityPatterns(ticket: SupportTicket): Promise<Diagnostic[]> { return []; }
  private async checkForKnownIssues(ticket: SupportTicket): Promise<RootCause[]> { return []; }
  private async assessTicketImpact(ticket: SupportTicket): Promise<ImpactAssessment> { return {} as ImpactAssessment; }
  private calculateAnalysisConfidence(analysis: TroubleshootingData): number { return 0.75; }
  private async attemptAutoAssignment(ticket: SupportTicket): Promise<void> {}
  private async sendTicketNotifications(ticket: SupportTicket, event: string): Promise<void> {}
  private async storeTicket(ticket: SupportTicket): Promise<void> {}
  private groupTicketsByPriority(tickets: SupportTicket[]): Record<string, number> { return {}; }
  private groupTicketsByCategory(tickets: SupportTicket[]): Record<string, number> { return {}; }
  private calculateAvgResolutionTime(tickets: SupportTicket[]): number { return 3.5; }
  private isTicketEscalated(ticket: SupportTicket): boolean { return false; }
  private calculateAutomationRate(tickets: SupportTicket[]): number { return 0.35; }
  private calculateAvgSatisfaction(tickets: SupportTicket[]): number { return 4.2; }
}

// Type definitions
interface CreateTicketRequest {
  userId: string;
  userEmail: string;
  subject: string;
  description: string;
  priority?: 'low' | 'medium' | 'high' | 'critical' | 'emergency';
  category?: string;
  tags?: string[];
  metadata?: Record<string, any>;
  attachments?: string[];
}

interface SupportTicket {
  id: string;
  userId: string;
  userEmail: string;
  subject: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'critical' | 'emergency';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  category: string;
  tags: string[];
  assignedTo: string | null;
  createdAt: string;
  updatedAt: string;
  lastActivity: string;
  responseTarget: string;
  escalationTarget: string;
  metadata: Record<string, any>;
  attachments: string[];
  conversationHistory: ConversationEntry[];
  monitoringContext: MonitoringContext;
  troubleshootingData: TroubleshootingData;
  suggestedActions?: SuggestedAction[];
  resolutionNotes: string | null;
  resolvedAt: string | null;
  resolvedBy: string | null;
  customerSatisfaction: number | null;
}

interface MonitoringContext {
  userId: string;
  timeRange: string;
  gatheredAt: string;
  systemHealth: any;
  userActivity: any;
  mt5Health: any;
  riskMetrics: any;
  alerts: any[];
  correlatedIssues: any[];
  errors?: string[];
}

interface TroubleshootingData {
  analysisId: string;
  performedAt: string;
  diagnostics: Diagnostic[];
  rootCauseAnalysis: RootCause[];
  impactAssessment: ImpactAssessment;
  recommendedActions: RecommendedAction[];
  automatedChecks: AutomatedCheck[];
  confidence: number;
}

interface SuggestedAction {
  type: string;
  title: string;
  description: string;
  priority: 'high' | 'medium' | 'low';
  automatable: boolean;
  estimatedTime: number;
  category: string;
  instructions: string;
  relatedData?: any;
}

interface TroubleshootingSession {
  id: string;
  ticketId: string;
  initiatedBy: string;
  startedAt: string;
  status: 'active' | 'paused' | 'completed';
  steps: TroubleshootingStep[];
  currentStep: number;
  findings: Record<string, any>;
  recommendations: string[];
  completedActions: TroubleshootingAction[];
  nextActions: TroubleshootingAction[];
  sessionNotes: string;
  endedAt: string | null;
}

interface TroubleshootingSessionUpdate {
  stepCompleted?: TroubleshootingStep;
  findings?: Record<string, any>;
  actionCompleted?: { actionId: string; result: any };
  newAction?: Partial<TroubleshootingAction>;
  recommendations?: string[];
  sessionNotes?: string;
}

interface UserContext {
  userId: string;
  lastUpdated: string;
  accountInfo: any;
  activitySummary: any;
  systemInteractions: any;
  supportHistory: any;
}

interface SimilarTicket {
  ticketId: string;
  similarity: number;
  subject: string;
  resolution: string;
  resolvedAt: string;
  category: string;
  tags: string[];
}

interface SupportSummary {
  totalTickets: number;
  openTickets: number;
  inProgressTickets: number;
  resolvedTickets: number;
  last24h: number;
  last7d: number;
  byPriority: Record<string, number>;
  byCategory: Record<string, number>;
  avgResolutionTime: number;
  escalatedTickets: number;
  activeSessions: number;
  automationRate: number;
  customerSatisfaction: number;
}

// Additional type definitions
interface ConversationEntry { id: string; timestamp: string; from: string; message: string; }
interface Diagnostic { type: string; finding: string; confidence: number; category: string; recommendation: string; }
interface RootCause { cause: string; confidence: number; evidence: string[]; }
interface ImpactAssessment { severity: string; affectedUsers: number; businessImpact: string; }
interface RecommendedAction { action: string; priority: string; effort: string; }
interface AutomatedCheck { checkType: string; status: string; result: string; timestamp: string; details: any; }
interface TroubleshootingStep { stepId: string; description: string; completed: boolean; result?: any; }
interface TroubleshootingAction { actionId: string; type: string; title: string; description: string; priority: string; status: string; assignedTo: string; createdAt: string; completedAt?: string; result?: any; }