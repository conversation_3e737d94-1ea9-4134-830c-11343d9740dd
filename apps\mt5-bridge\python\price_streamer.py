"""
MT5 Price Streaming Service
Handles real-time price data streaming with rate limiting and reconnection logic
"""

import asyncio
import time
from typing import Dict, Set, Optional, Callable, List, Any
from datetime import datetime, timedelta
from dataclasses import dataclass
from collections import defaultdict, deque
import MetaTrader5 as mt5
from loguru import logger

from mt5_connection import get_mt5_connection
from data_transformer import DataTransformer
from performance_monitor import get_performance_profiler

@dataclass
class PriceUpdate:
    """Price update data structure"""
    symbol: str
    bid: float
    ask: float
    timestamp: datetime
    volume: int = 0
    spread: float = 0.0
    source: str = "mt5"

@dataclass
class StreamingConfig:
    """Configuration for price streaming"""
    update_interval: float = 0.1  # 100ms
    rate_limit_per_second: int = 100
    max_symbols: int = 50
    buffer_size: int = 1000
    reconnect_delay: float = 5.0
    max_reconnect_attempts: int = 10

class RateLimiter:
    """Rate limiter for price updates"""
    
    def __init__(self, max_updates_per_second: int, window_size: int = 1):
        self.max_updates = max_updates_per_second
        self.window_size = window_size
        self.updates = deque()
        
    def can_update(self) -> bool:
        """Check if update is allowed within rate limit"""
        now = time.time()
        
        # Remove old entries outside the window
        while self.updates and self.updates[0] < now - self.window_size:
            self.updates.popleft()
            
        # Check if we're within the limit
        if len(self.updates) < self.max_updates:
            self.updates.append(now)
            return True
            
        return False
    
    def get_remaining_capacity(self) -> int:
        """Get remaining update capacity in current window"""
        now = time.time()
        
        # Remove old entries
        while self.updates and self.updates[0] < now - self.window_size:
            self.updates.popleft()
            
        return max(0, self.max_updates - len(self.updates))

class PriceBuffer:
    """Buffer for managing price updates with deduplication"""
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.buffer: Dict[str, PriceUpdate] = {}
        self.last_updates: Dict[str, datetime] = {}
        
    def add_update(self, update: PriceUpdate, min_interval: float = 0.05) -> bool:
        """
        Add price update to buffer with deduplication
        Returns True if update was added (not duplicate)
        """
        symbol = update.symbol
        now = update.timestamp
        
        # Check if we have a recent update for this symbol
        if symbol in self.last_updates:
            time_diff = (now - self.last_updates[symbol]).total_seconds()
            if time_diff < min_interval:
                return False  # Skip duplicate/too frequent update
        
        # Add update to buffer
        self.buffer[symbol] = update
        self.last_updates[symbol] = now
        
        # Maintain buffer size
        if len(self.buffer) > self.max_size:
            # Remove oldest update
            oldest_symbol = min(self.last_updates.keys(), 
                              key=lambda k: self.last_updates[k])
            del self.buffer[oldest_symbol]
            del self.last_updates[oldest_symbol]
            
        return True
    
    def get_updates(self) -> List[PriceUpdate]:
        """Get all updates and clear buffer"""
        updates = list(self.buffer.values())
        self.buffer.clear()
        return updates

class PriceStreamer:
    """
    Real-time price streaming service with rate limiting and reconnection logic
    """
    
    def __init__(self, config: StreamingConfig = None):
        self.config = config or StreamingConfig()
        self.mt5_connection = get_mt5_connection()
        self.data_transformer = DataTransformer()
        self.performance_profiler = get_performance_profiler()
        
        # Streaming state
        self.is_running = False
        self.subscribed_symbols: Set[str] = set()
        self.rate_limiter = RateLimiter(self.config.rate_limit_per_second)
        self.price_buffer = PriceBuffer(self.config.buffer_size)
        
        # Callback for price updates
        self.callback: Optional[Callable[[Dict[str, Any]], None]] = None
        
        # Streaming task
        self.streaming_task: Optional[asyncio.Task] = None
        
        # Statistics
        self.stats = {
            'total_updates': 0,
            'rate_limited': 0,
            'duplicates_filtered': 0,
            'errors': 0,
            'last_update': None,
            'symbols_active': 0
        }
        
    def set_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """Set callback function for price updates"""
        self.callback = callback
        
    async def start(self):
        """Start price streaming"""
        if self.is_running:
            logger.warning("Price streamer already running")
            return
            
        logger.info("🚀 Starting price streaming service...")
        self.is_running = True
        
        # Start streaming task
        self.streaming_task = asyncio.create_task(self._streaming_loop())
        
        logger.info("✅ Price streaming service started")
        
    async def stop(self):
        """Stop price streaming"""
        if not self.is_running:
            return
            
        logger.info("🔌 Stopping price streaming service...")
        self.is_running = False
        
        # Cancel streaming task
        if self.streaming_task:
            self.streaming_task.cancel()
            try:
                await self.streaming_task
            except asyncio.CancelledError:
                pass
                
        logger.info("✅ Price streaming service stopped")
        
    async def subscribe_symbol(self, symbol: str):
        """Subscribe to price updates for a symbol"""
        if len(self.subscribed_symbols) >= self.config.max_symbols:
            logger.warning(f"Maximum symbols limit reached ({self.config.max_symbols})")
            return False
            
        self.subscribed_symbols.add(symbol)
        logger.info(f"📊 Subscribed to {symbol} (total: {len(self.subscribed_symbols)})")
        return True
        
    async def unsubscribe_symbol(self, symbol: str):
        """Unsubscribe from price updates for a symbol"""
        if symbol in self.subscribed_symbols:
            self.subscribed_symbols.remove(symbol)
            logger.info(f"📊 Unsubscribed from {symbol} (total: {len(self.subscribed_symbols)})")
            return True
        return False
        
    async def _streaming_loop(self):
        """Main streaming loop"""
        logger.info("🔄 Starting price streaming loop...")
        
        reconnect_count = 0
        
        while self.is_running:
            try:
                # Check MT5 connection
                if not await self.mt5_connection.ensure_connection():
                    logger.warning("MT5 connection lost, attempting reconnection...")
                    reconnect_count += 1
                    
                    if reconnect_count > self.config.max_reconnect_attempts:
                        logger.error("Max reconnection attempts reached, stopping streaming")
                        break
                        
                    await asyncio.sleep(self.config.reconnect_delay)
                    continue
                
                # Reset reconnect count on successful connection
                reconnect_count = 0
                
                # Get price updates for subscribed symbols
                if self.subscribed_symbols:
                    await self._fetch_price_updates()
                
                # Process buffered updates
                await self._process_buffered_updates()
                
                # Update statistics
                self.stats['symbols_active'] = len(self.subscribed_symbols)
                self.stats['last_update'] = datetime.now()
                
                # Wait for next update interval
                await asyncio.sleep(self.config.update_interval)
                
            except asyncio.CancelledError:
                logger.info("Price streaming loop cancelled")
                break
            except Exception as e:
                logger.error(f"Error in streaming loop: {e}")
                self.stats['errors'] += 1
                await asyncio.sleep(1)  # Brief pause before retry
                
        logger.info("🔄 Price streaming loop ended")
        
    async def _fetch_price_updates(self):
        """Fetch price updates for subscribed symbols"""
        try:
            with self.performance_profiler.measure_latency('mt5_data_fetch'):
                for symbol in list(self.subscribed_symbols):  # Copy to avoid modification during iteration
                    try:
                        # Get current tick data
                        tick = mt5.symbol_info_tick(symbol)
                        if tick is None:
                            continue
                            
                        # Create price update
                        price_update = PriceUpdate(
                            symbol=symbol,
                            bid=float(tick.bid),
                            ask=float(tick.ask),
                            timestamp=datetime.fromtimestamp(tick.time),
                            volume=int(tick.volume),
                            spread=float(tick.ask - tick.bid),
                            source="mt5"
                        )
                        
                        # Add to buffer (with deduplication)
                        if self.price_buffer.add_update(price_update):
                            self.stats['total_updates'] += 1
                        else:
                            self.stats['duplicates_filtered'] += 1
                            
                    except Exception as e:
                        logger.warning(f"Failed to get tick for {symbol}: {e}")
                    
        except Exception as e:
            logger.error(f"Error fetching price updates: {e}")
            self.stats['errors'] += 1
            
    async def _process_buffered_updates(self):
        """Process buffered price updates"""
        if not self.callback:
            return
            
        updates = self.price_buffer.get_updates()
        
        for update in updates:
            try:
                # Check rate limit
                if not self.rate_limiter.can_update():
                    self.stats['rate_limited'] += 1
                    continue
                    
                # Transform to platform format
                with self.performance_profiler.measure_latency('price_processing'):
                    transformed_data = self.data_transformer.transform_price_update({
                        'symbol': update.symbol,
                        'bid': update.bid,
                        'ask': update.ask,
                        'timestamp': update.timestamp,
                        'volume': update.volume,
                        'spread': update.spread,
                        'source': update.source
                    })
                
                # Send update via callback
                with self.performance_profiler.measure_latency('websocket_broadcast'):
                    await self.callback(transformed_data)
                
            except Exception as e:
                logger.error(f"Error processing price update for {update.symbol}: {e}")
                self.stats['errors'] += 1
                
    def get_stats(self) -> Dict[str, Any]:
        """Get streaming statistics"""
        return {
            **self.stats,
            'is_running': self.is_running,
            'subscribed_symbols': list(self.subscribed_symbols),
            'rate_limiter_capacity': self.rate_limiter.get_remaining_capacity(),
            'buffer_size': len(self.price_buffer.buffer)
        }
        
    def get_subscribed_symbols(self) -> List[str]:
        """Get list of subscribed symbols"""
        return list(self.subscribed_symbols)

# Global instance
_price_streamer: Optional[PriceStreamer] = None

def get_price_streamer() -> PriceStreamer:
    """Get global price streamer instance"""
    global _price_streamer
    if _price_streamer is None:
        _price_streamer = PriceStreamer()
    return _price_streamer