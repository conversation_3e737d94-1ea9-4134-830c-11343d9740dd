/**
 * Request Optimization Service
 * 
 * Optimizes API response payloads, implements request batching, debouncing,
 * and caching strategies based on client hardware capabilities.
 */

interface ClientCapabilities {
  hardwareTier: 'low' | 'medium' | 'high';
  connectionType: 'slow-2g' | '2g' | '3g' | '4g' | 'wifi' | 'unknown';
  memoryLimit: number;
  compressionSupport: string[];
  preferredFormat: 'minimal' | 'standard' | 'detailed';
}

interface OptimizationConfig {
  enableBatching: boolean;
  batchSize: number;
  debounceMs: number;
  compressionLevel: number;
  cacheStrategy: 'aggressive' | 'moderate' | 'minimal';
  payloadOptimization: 'minimal' | 'standard' | 'full';
  enablePrefetching: boolean;
  maxConcurrentRequests: number;
}

interface RequestBatch {
  id: string;
  requests: PendingRequest[];
  createdAt: number;
  clientId: string;
  capabilities: ClientCapabilities;
}

interface PendingRequest {
  id: string;
  endpoint: string;
  params: Record<string, any>;
  priority: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
  resolve: (data: any) => void;
  reject: (error: any) => void;
  timeout?: number;
}

interface CachedResponse {
  data: any;
  timestamp: number;
  ttl: number;
  clientCapabilities: ClientCapabilities;
  compression: string;
  size: number;
}

interface RequestMetrics {
  totalRequests: number;
  batchedRequests: number;
  cacheHits: number;
  compressionRatio: number;
  averageResponseTime: number;
  bandwidthSaved: number;
}

export class RequestOptimizationService {
  private pendingBatches = new Map<string, RequestBatch>();
  private batchTimers = new Map<string, NodeJS.Timeout>();
  private responseCache = new Map<string, CachedResponse>();
  private clientConfigs = new Map<string, OptimizationConfig>();
  private metrics: RequestMetrics;
  private debounceTimers = new Map<string, NodeJS.Timeout>();
  private concurrentRequests = new Map<string, number>();
  private priorityQueues = new Map<string, PendingRequest[]>();

  constructor() {
    this.metrics = {
      totalRequests: 0,
      batchedRequests: 0,
      cacheHits: 0,
      compressionRatio: 0,
      averageResponseTime: 0,
      bandwidthSaved: 0
    };

    // Start cleanup intervals
    this.startCleanupProcesses();
  }

  /**
   * Optimize request based on client capabilities
   */
  async optimizeRequest(
    clientId: string,
    endpoint: string,
    params: Record<string, any>,
    capabilities: ClientCapabilities,
    options: {
      priority?: 'low' | 'medium' | 'high' | 'critical';
      timeout?: number;
      skipCache?: boolean;
      skipBatching?: boolean;
    } = {}
  ): Promise<any> {
    const startTime = Date.now();
    this.metrics.totalRequests++;

    try {
      // Update client configuration based on capabilities
      this.updateClientConfig(clientId, capabilities);
      const config = this.getClientConfig(clientId);

      // Check cache first (unless skipped)
      if (!options.skipCache) {
        const cachedResponse = await this.getCachedResponse(endpoint, params, capabilities);
        if (cachedResponse) {
          this.metrics.cacheHits++;
          return this.decompressResponse(cachedResponse.data, cachedResponse.compression);
        }
      }

      // Check if request should be batched
      if (!options.skipBatching && config.enableBatching && options.priority !== 'critical') {
        return this.addToBatch(clientId, endpoint, params, capabilities, options);
      }

      // Check concurrent request limits
      await this.enforceRequestLimits(clientId, config);

      // Execute immediate request
      const response = await this.executeRequest(clientId, endpoint, params, capabilities, config);
      
      // Cache response if appropriate
      await this.cacheResponse(endpoint, params, response, capabilities, config);

      // Update metrics
      this.updateMetrics(startTime, response);

      return response;
    } catch (error) {
      console.error('Request optimization failed:', error);
      throw error;
    } finally {
      this.decrementConcurrentRequests(clientId);
    }
  }

  /**
   * Execute a batch of requests
   */
  async executeBatch(batchId: string): Promise<void> {
    const batch = this.pendingBatches.get(batchId);
    if (!batch) return;

    try {
      const config = this.getClientConfig(batch.clientId);
      
      // Group requests by endpoint for optimization
      const requestGroups = this.groupRequestsByEndpoint(batch.requests);
      
      // Execute requests in groups
      const results = await Promise.allSettled(
        requestGroups.map(group => this.executeRequestGroup(group, batch.capabilities, config))
      );

      // Process results and resolve individual requests
      results.forEach((result, index) => {
        const group = requestGroups[index];
        
        if (result.status === 'fulfilled') {
          // Distribute responses to individual requests
          this.distributeGroupResponses(group, result.value, batch.capabilities, config);
        } else {
          // Reject all requests in failed group
          group.forEach(req => req.reject(result.reason));
        }
      });

      this.metrics.batchedRequests += batch.requests.length;
    } catch (error) {
      // Reject all pending requests in batch
      batch.requests.forEach(req => req.reject(error));
    } finally {
      // Cleanup batch
      this.pendingBatches.delete(batchId);
      const timer = this.batchTimers.get(batchId);
      if (timer) {
        clearTimeout(timer);
        this.batchTimers.delete(batchId);
      }
    }
  }

  /**
   * Get optimization metrics
   */
  getMetrics(): RequestMetrics & {
    activeBatches: number;
    cacheSize: number;
    cacheHitRate: number;
  } {
    return {
      ...this.metrics,
      activeBatches: this.pendingBatches.size,
      cacheSize: this.responseCache.size,
      cacheHitRate: this.metrics.totalRequests > 0 
        ? (this.metrics.cacheHits / this.metrics.totalRequests) * 100 
        : 0
    };
  }

  /**
   * Clear cache for specific patterns
   */
  invalidateCache(patterns: string[] = []): void {
    if (patterns.length === 0) {
      this.responseCache.clear();
      return;
    }

    for (const [key, cached] of this.responseCache.entries()) {
      const shouldInvalidate = patterns.some(pattern => {
        const regex = new RegExp(pattern.replace('*', '.*'));
        return regex.test(key);
      });

      if (shouldInvalidate) {
        this.responseCache.delete(key);
      }
    }
  }

  /**
   * Update client capabilities and reconfigure optimization
   */
  updateClientCapabilities(clientId: string, capabilities: ClientCapabilities): void {
    this.updateClientConfig(clientId, capabilities);
  }

  /**
   * Get current configuration for a client
   */
  getClientOptimization(clientId: string): OptimizationConfig | null {
    return this.clientConfigs.get(clientId) || null;
  }

  /**
   * Force flush pending batches for a client
   */
  async flushPendingBatches(clientId: string): Promise<void> {
    const batchesToFlush: string[] = [];
    
    for (const [batchId, batch] of this.pendingBatches.entries()) {
      if (batch.clientId === clientId) {
        batchesToFlush.push(batchId);
      }
    }

    await Promise.all(batchesToFlush.map(batchId => this.executeBatch(batchId)));
  }

  private updateClientConfig(clientId: string, capabilities: ClientCapabilities): void {
    const config: OptimizationConfig = {
      enableBatching: capabilities.hardwareTier !== 'high',
      batchSize: this.getBatchSize(capabilities),
      debounceMs: this.getDebounceMs(capabilities),
      compressionLevel: this.getCompressionLevel(capabilities),
      cacheStrategy: this.getCacheStrategy(capabilities),
      payloadOptimization: capabilities.preferredFormat || 'standard',
      enablePrefetching: capabilities.hardwareTier === 'high' && 
                        ['wifi', '4g'].includes(capabilities.connectionType),
      maxConcurrentRequests: this.getMaxConcurrentRequests(capabilities)
    };

    this.clientConfigs.set(clientId, config);
  }

  private getClientConfig(clientId: string): OptimizationConfig {
    return this.clientConfigs.get(clientId) || this.getDefaultConfig();
  }

  private getDefaultConfig(): OptimizationConfig {
    return {
      enableBatching: true,
      batchSize: 5,
      debounceMs: 100,
      compressionLevel: 6,
      cacheStrategy: 'moderate',
      payloadOptimization: 'standard',
      enablePrefetching: false,
      maxConcurrentRequests: 3
    };
  }

  private getBatchSize(capabilities: ClientCapabilities): number {
    switch (capabilities.hardwareTier) {
      case 'low': return 10;
      case 'medium': return 5;
      case 'high': return 3;
      default: return 5;
    }
  }

  private getDebounceMs(capabilities: ClientCapabilities): number {
    const baseDebounce = {
      'slow-2g': 500,
      '2g': 300,
      '3g': 150,
      '4g': 100,
      'wifi': 50,
      'unknown': 200
    }[capabilities.connectionType] || 200;

    // Adjust for hardware tier
    const multiplier = {
      'low': 1.5,
      'medium': 1.0,
      'high': 0.5
    }[capabilities.hardwareTier];

    return Math.round(baseDebounce * multiplier);
  }

  private getCompressionLevel(capabilities: ClientCapabilities): number {
    if (capabilities.hardwareTier === 'low') return 9; // Max compression
    if (capabilities.hardwareTier === 'medium') return 6; // Balanced
    return 3; // Fast compression for high-end devices
  }

  private getCacheStrategy(capabilities: ClientCapabilities): 'aggressive' | 'moderate' | 'minimal' {
    if (capabilities.memoryLimit < 512) return 'minimal';
    if (capabilities.memoryLimit < 2048) return 'moderate';
    return 'aggressive';
  }

  private getMaxConcurrentRequests(capabilities: ClientCapabilities): number {
    const base = {
      'low': 2,
      'medium': 4,
      'high': 8
    }[capabilities.hardwareTier];

    // Adjust for connection type
    const connectionMultiplier = {
      'slow-2g': 0.5,
      '2g': 0.75,
      '3g': 1.0,
      '4g': 1.25,
      'wifi': 1.5,
      'unknown': 1.0
    }[capabilities.connectionType] || 1.0;

    return Math.max(1, Math.round(base * connectionMultiplier));
  }

  private async getCachedResponse(
    endpoint: string, 
    params: Record<string, any>, 
    capabilities: ClientCapabilities
  ): Promise<CachedResponse | null> {
    const cacheKey = this.generateCacheKey(endpoint, params, capabilities);
    const cached = this.responseCache.get(cacheKey);

    if (!cached) return null;

    // Check TTL
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.responseCache.delete(cacheKey);
      return null;
    }

    // Check if capabilities match (for format compatibility)
    if (!this.capabilitiesMatch(cached.clientCapabilities, capabilities)) {
      return null;
    }

    return cached;
  }

  private async addToBatch(
    clientId: string,
    endpoint: string,
    params: Record<string, any>,
    capabilities: ClientCapabilities,
    options: { priority?: string; timeout?: number }
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const request: PendingRequest = {
        id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        endpoint,
        params,
        priority: (options.priority as any) || 'medium',
        timestamp: Date.now(),
        resolve,
        reject,
        timeout: options.timeout
      };

      // Find or create batch for this client
      let batch = Array.from(this.pendingBatches.values())
        .find(b => b.clientId === clientId && b.requests.length < this.getClientConfig(clientId).batchSize);

      if (!batch) {
        const batchId = `batch_${Date.now()}_${clientId}`;
        batch = {
          id: batchId,
          requests: [],
          createdAt: Date.now(),
          clientId,
          capabilities
        };
        this.pendingBatches.set(batchId, batch);
      }

      batch.requests.push(request);

      // Set up batch execution timer
      const config = this.getClientConfig(clientId);
      const existingTimer = this.batchTimers.get(batch.id);
      
      if (existingTimer) {
        clearTimeout(existingTimer);
      }

      const timer = setTimeout(() => {
        this.executeBatch(batch!.id);
      }, config.debounceMs);

      this.batchTimers.set(batch.id, timer);

      // Execute batch immediately if it's full or has critical requests
      if (batch.requests.length >= config.batchSize || 
          batch.requests.some(r => r.priority === 'critical')) {
        clearTimeout(timer);
        this.batchTimers.delete(batch.id);
        this.executeBatch(batch.id);
      }
    });
  }

  private async enforceRequestLimits(clientId: string, config: OptimizationConfig): Promise<void> {
    const current = this.concurrentRequests.get(clientId) || 0;
    
    if (current >= config.maxConcurrentRequests) {
      // Wait for a slot to become available
      await new Promise<void>(resolve => {
        const checkSlot = () => {
          const newCurrent = this.concurrentRequests.get(clientId) || 0;
          if (newCurrent < config.maxConcurrentRequests) {
            resolve();
          } else {
            setTimeout(checkSlot, 10);
          }
        };
        checkSlot();
      });
    }

    this.concurrentRequests.set(clientId, (this.concurrentRequests.get(clientId) || 0) + 1);
  }

  private decrementConcurrentRequests(clientId: string): void {
    const current = this.concurrentRequests.get(clientId) || 0;
    if (current > 0) {
      this.concurrentRequests.set(clientId, current - 1);
    }
  }

  private async executeRequest(
    clientId: string,
    endpoint: string,
    params: Record<string, any>,
    capabilities: ClientCapabilities,
    config: OptimizationConfig
  ): Promise<any> {
    // Optimize payload based on client capabilities
    const optimizedParams = this.optimizePayload(params, capabilities, config);
    
    // Simulate actual API request execution
    // In real implementation, this would call the actual service/controller
    const mockResponse = {
      data: `Response for ${endpoint}`,
      optimized: true,
      clientTier: capabilities.hardwareTier,
      timestamp: Date.now()
    };

    // Apply compression if supported
    return this.compressResponse(mockResponse, capabilities, config);
  }

  private groupRequestsByEndpoint(requests: PendingRequest[]): PendingRequest[][] {
    const groups = new Map<string, PendingRequest[]>();
    
    for (const request of requests) {
      const key = request.endpoint;
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(request);
    }

    return Array.from(groups.values());
  }

  private async executeRequestGroup(
    requests: PendingRequest[],
    capabilities: ClientCapabilities,
    config: OptimizationConfig
  ): Promise<any[]> {
    // Batch execute requests for the same endpoint
    const endpoint = requests[0].endpoint;
    const batchParams = requests.map(r => r.params);
    
    // Simulate batch execution
    const batchResponse = batchParams.map((params, index) => ({
      id: requests[index].id,
      data: `Batch response for ${endpoint}`,
      params,
      optimized: true,
      timestamp: Date.now()
    }));

    return batchResponse;
  }

  private distributeGroupResponses(
    requests: PendingRequest[],
    responses: any[],
    capabilities: ClientCapabilities,
    config: OptimizationConfig
  ): void {
    responses.forEach((response, index) => {
      const request = requests[index];
      if (request) {
        request.resolve(response);
      }
    });
  }

  private optimizePayload(
    params: Record<string, any>,
    capabilities: ClientCapabilities,
    config: OptimizationConfig
  ): Record<string, any> {
    const optimized = { ...params };

    switch (config.payloadOptimization) {
      case 'minimal':
        // Remove non-essential fields for low-spec devices
        delete optimized.metadata;
        delete optimized.debug;
        if (optimized.fields && Array.isArray(optimized.fields)) {
          optimized.fields = optimized.fields.slice(0, 5); // Limit fields
        }
        break;
      
      case 'standard':
        // Moderate optimization
        if (optimized.fields && Array.isArray(optimized.fields)) {
          optimized.fields = optimized.fields.slice(0, 20);
        }
        break;
      
      case 'full':
        // No optimization for high-spec devices
        break;
    }

    return optimized;
  }

  private compressResponse(
    response: any,
    capabilities: ClientCapabilities,
    config: OptimizationConfig
  ): any {
    // In real implementation, this would use actual compression libraries
    const compressed = {
      ...response,
      compressed: capabilities.compressionSupport.length > 0,
      compressionLevel: config.compressionLevel
    };

    return compressed;
  }

  private decompressResponse(data: any, compression: string): any {
    // In real implementation, this would decompress the data
    return data;
  }

  private async cacheResponse(
    endpoint: string,
    params: Record<string, any>,
    response: any,
    capabilities: ClientCapabilities,
    config: OptimizationConfig
  ): Promise<void> {
    const cacheKey = this.generateCacheKey(endpoint, params, capabilities);
    
    const ttl = this.getCacheTTL(endpoint, config.cacheStrategy);
    
    const cached: CachedResponse = {
      data: response,
      timestamp: Date.now(),
      ttl,
      clientCapabilities: capabilities,
      compression: 'none', // Would be actual compression type
      size: JSON.stringify(response).length
    };

    this.responseCache.set(cacheKey, cached);

    // Cleanup old cache entries if needed
    this.cleanupCache(config.cacheStrategy);
  }

  private generateCacheKey(
    endpoint: string,
    params: Record<string, any>,
    capabilities: ClientCapabilities
  ): string {
    const paramsStr = JSON.stringify(params, Object.keys(params).sort());
    const capabilitiesStr = `${capabilities.hardwareTier}_${capabilities.preferredFormat}`;
    return `${endpoint}_${paramsStr}_${capabilitiesStr}`;
  }

  private capabilitiesMatch(cached: ClientCapabilities, current: ClientCapabilities): boolean {
    return cached.hardwareTier === current.hardwareTier &&
           cached.preferredFormat === current.preferredFormat;
  }

  private getCacheTTL(endpoint: string, strategy: 'aggressive' | 'moderate' | 'minimal'): number {
    const baseTTL = {
      'aggressive': 300000, // 5 minutes
      'moderate': 120000,   // 2 minutes
      'minimal': 60000      // 1 minute
    }[strategy];

    // Adjust TTL based on endpoint type
    if (endpoint.includes('realtime') || endpoint.includes('live')) {
      return baseTTL * 0.1; // Very short for real-time data
    }
    
    if (endpoint.includes('historical') || endpoint.includes('static')) {
      return baseTTL * 5; // Longer for historical data
    }

    return baseTTL;
  }

  private cleanupCache(strategy: 'aggressive' | 'moderate' | 'minimal'): void {
    const maxSize = {
      'aggressive': 1000,
      'moderate': 500,
      'minimal': 100
    }[strategy];

    if (this.responseCache.size > maxSize) {
      // Remove oldest entries
      const entries = Array.from(this.responseCache.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, this.responseCache.size - maxSize);
      toRemove.forEach(([key]) => this.responseCache.delete(key));
    }
  }

  private updateMetrics(startTime: number, response: any): void {
    const responseTime = Date.now() - startTime;
    
    // Update average response time
    this.metrics.averageResponseTime = 
      (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime) / 
      this.metrics.totalRequests;

    // Update compression ratio and bandwidth savings (simplified calculation)
    if (response.compressed) {
      this.metrics.compressionRatio = 
        (this.metrics.compressionRatio * (this.metrics.totalRequests - 1) + 0.7) / 
        this.metrics.totalRequests;
      this.metrics.bandwidthSaved += JSON.stringify(response).length * 0.3;
    }
  }

  private startCleanupProcesses(): void {
    // Cleanup expired cache entries every 5 minutes
    setInterval(() => {
      const now = Date.now();
      for (const [key, cached] of this.responseCache.entries()) {
        if (now - cached.timestamp > cached.ttl) {
          this.responseCache.delete(key);
        }
      }
    }, 300000);

    // Cleanup stale batches every minute
    setInterval(() => {
      const now = Date.now();
      const staleBatches = Array.from(this.pendingBatches.entries())
        .filter(([_, batch]) => now - batch.createdAt > 60000); // 1 minute timeout

      staleBatches.forEach(([batchId]) => {
        this.executeBatch(batchId);
      });
    }, 60000);
  }
}