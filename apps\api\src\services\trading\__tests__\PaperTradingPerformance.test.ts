import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { performance } from 'perf_hooks';
import { PrismaClient } from '@prisma/client';
import { PaperTradingEngine } from '../PaperTradingEngine';
import { VirtualPortfolioService } from '../VirtualPortfolioService';
import { PaperTradingAnalyticsService } from '../PaperTradingAnalyticsService';
import { RedisService } from '../../cache/RedisService';
import { TradeRequest, TradeType, TradeSide } from '@golddaddy/types';
import Decimal from 'decimal.js';

describe('Paper Trading Performance Tests', () => {
  let prisma: PrismaClient;
  let redisService: RedisService;
  let paperTradingEngine: PaperTradingEngine;
  let portfolioService: VirtualPortfolioService;
  let analyticsService: PaperTradingAnalyticsService;
  let testUserIds: string[] = [];
  let testSessionIds: string[] = [];

  beforeAll(async () => {
    // Initialize test database with performance optimizations
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_TEST_URL || 'postgresql://postgres:postgres@localhost:5432/golddaddy_perf_test'
        }
      },
      log: ['error'] // Reduce logging for performance tests
    });

    redisService = new RedisService({
      url: process.env.REDIS_TEST_URL || 'redis://localhost:6379/2',
      maxRetriesPerRequest: 1,
      connectTimeout: 1000
    });

    portfolioService = new VirtualPortfolioService(prisma, redisService);
    analyticsService = new PaperTradingAnalyticsService(prisma, redisService);
    paperTradingEngine = new PaperTradingEngine(prisma, redisService, portfolioService);

    // Create test users in bulk
    const userCreationPromises = Array.from({ length: 100 }, (_, i) => 
      prisma.user.create({
        data: {
          email: `perftest${i}@example.com`,
          hashedPassword: 'test-hash',
          profile: {
            create: {
              firstName: 'Perf',
              lastName: `User${i}`,
              timezone: 'UTC',
              experienceLevel: 'INTERMEDIATE',
              riskTolerance: 'MODERATE'
            }
          }
        }
      })
    );

    const users = await Promise.all(userCreationPromises);
    testUserIds = users.map(user => user.id);
  });

  afterAll(async () => {
    // Cleanup test data
    await prisma.paperTrade.deleteMany({
      where: { sessionId: { in: testSessionIds } }
    });
    await prisma.paperTradingSession.deleteMany({
      where: { id: { in: testSessionIds } }
    });
    await prisma.user.deleteMany({
      where: { id: { in: testUserIds } }
    });
    
    await prisma.$disconnect();
    await redisService.disconnect();
  });

  beforeEach(async () => {
    // Clear performance-related cache
    await redisService.flushAll();
    
    // Force garbage collection if available
    if (global.gc) {
      global.gc();
    }
  });

  afterEach(async () => {
    // Cleanup session data after each test
    if (testSessionIds.length > 0) {
      await prisma.paperTrade.deleteMany({
        where: { sessionId: { in: testSessionIds } }
      });
      await prisma.paperTradingSession.deleteMany({
        where: { id: { in: testSessionIds } }
      });
      testSessionIds = [];
    }
  });

  describe('Concurrent Session Management', () => {
    it('should handle 50 concurrent session creations within acceptable time', async () => {
      const startTime = performance.now();
      const sessionPromises = testUserIds.slice(0, 50).map(userId => 
        paperTradingEngine.createSession(userId, {
          name: `Perf Test Session`,
          initialBalance: new Decimal(100000),
          settings: {
            enableSlippage: true,
            enableSpread: true,
            riskManagement: {
              maxPositionSize: 0.1,
              maxDrawdown: 0.15,
              requireStopLoss: false
            }
          }
        })
      );

      const sessions = await Promise.all(sessionPromises);
      const duration = performance.now() - startTime;

      testSessionIds = sessions.map(s => s.id);

      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
      expect(sessions).toHaveLength(50);
      sessions.forEach(session => {
        expect(session.status).toBe('ACTIVE');
        expect(session.balance).toEqual(new Decimal(100000));
      });

      console.log(`✓ Session creation performance: ${duration.toFixed(2)}ms for 50 sessions (${(duration/50).toFixed(2)}ms avg)`);
    });

    it('should maintain performance with 100 active sessions', async () => {
      // Create sessions first
      const sessionPromises = testUserIds.map(userId => 
        paperTradingEngine.createSession(userId, {
          name: `Load Test Session`,
          initialBalance: new Decimal(50000),
          settings: {
            enableSlippage: false,
            enableSpread: false,
            riskManagement: {
              maxPositionSize: 0.2,
              maxDrawdown: 0.2,
              requireStopLoss: false
            }
          }
        })
      );

      const sessions = await Promise.all(sessionPromises);
      testSessionIds = sessions.map(s => s.id);

      // Test concurrent read operations
      const startTime = performance.now();
      const readPromises = testSessionIds.map(sessionId => 
        portfolioService.getPortfolio(sessionId)
      );

      const portfolios = await Promise.all(readPromises);
      const duration = performance.now() - startTime;

      expect(duration).toBeLessThan(3000); // Should complete within 3 seconds
      expect(portfolios).toHaveLength(100);
      
      const averageResponseTime = duration / 100;
      expect(averageResponseTime).toBeLessThan(30); // Average < 30ms per portfolio

      console.log(`✓ Portfolio read performance: ${duration.toFixed(2)}ms for 100 portfolios (${averageResponseTime.toFixed(2)}ms avg)`);
    });
  });

  describe('Concurrent Trade Execution', () => {
    it('should handle high-frequency trade execution without deadlocks', async () => {
      // Create a single session for rapid trading
      const session = await paperTradingEngine.createSession(testUserIds[0], {
        name: 'High Frequency Test',
        initialBalance: new Decimal(1000000),
        settings: {
          enableSlippage: false,
          enableSpread: false,
          riskManagement: {
            maxPositionSize: 1.0, // Allow large positions
            maxDrawdown: 0.5,
            requireStopLoss: false
          }
        }
      });

      testSessionIds.push(session.id);

      const tradeRequest: TradeRequest = {
        symbol: 'EURUSD',
        type: TradeType.MARKET,
        side: TradeSide.BUY,
        quantity: new Decimal(1000),
        price: undefined,
        stopLoss: undefined,
        takeProfit: undefined,
        metadata: {}
      };

      // Execute 100 concurrent trades
      const startTime = performance.now();
      const tradePromises = Array.from({ length: 100 }, (_, i) => 
        paperTradingEngine.executePaperTrade(
          testUserIds[0],
          session.id,
          {
            ...tradeRequest,
            side: i % 2 === 0 ? TradeSide.BUY : TradeSide.SELL
          }
        )
      );

      const trades = await Promise.all(tradePromises);
      const duration = performance.now() - startTime;

      expect(trades).toHaveLength(100);
      expect(duration).toBeLessThan(10000); // Should complete within 10 seconds

      const successfulTrades = trades.filter(t => t.status === 'FILLED');
      const errorRate = (100 - successfulTrades.length) / 100;
      
      expect(errorRate).toBeLessThan(0.05); // Less than 5% error rate
      expect(successfulTrades.length).toBeGreaterThan(95);

      console.log(`✓ Trade execution performance: ${duration.toFixed(2)}ms for 100 trades (${(duration/100).toFixed(2)}ms avg, ${(errorRate*100).toFixed(1)}% error rate)`);
    });

    it('should maintain consistency under concurrent position management', async () => {
      // Create session with positions
      const session = await paperTradingEngine.createSession(testUserIds[0], {
        name: 'Position Management Test',
        initialBalance: new Decimal(500000),
        settings: {
          enableSlippage: false,
          enableSpread: false,
          riskManagement: {
            maxPositionSize: 1.0,
            maxDrawdown: 0.5,
            requireStopLoss: false
          }
        }
      });

      testSessionIds.push(session.id);

      // Create initial positions
      const initialTrades = await Promise.all(
        ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD'].map(symbol => 
          paperTradingEngine.executePaperTrade(
            testUserIds[0],
            session.id,
            {
              symbol,
              type: TradeType.MARKET,
              side: TradeSide.BUY,
              quantity: new Decimal(10000),
              price: undefined,
              stopLoss: undefined,
              takeProfit: undefined,
              metadata: {}
            }
          )
        )
      );

      // Concurrent position operations
      const startTime = performance.now();
      const operationPromises = [
        // Portfolio reads
        ...Array.from({ length: 20 }, () => 
          portfolioService.getPortfolio(session.id)
        ),
        // Position closes
        ...initialTrades.slice(0, 2).map(trade => 
          paperTradingEngine.closePosition(testUserIds[0], session.id, trade.id!)
        ),
        // New trades
        ...Array.from({ length: 10 }, (_, i) => 
          paperTradingEngine.executePaperTrade(
            testUserIds[0],
            session.id,
            {
              symbol: 'EURUSD',
              type: TradeType.MARKET,
              side: i % 2 === 0 ? TradeSide.BUY : TradeSide.SELL,
              quantity: new Decimal(1000),
              price: undefined,
              stopLoss: undefined,
              takeProfit: undefined,
              metadata: {}
            }
          )
        )
      ];

      const results = await Promise.all(operationPromises);
      const duration = performance.now() - startTime;

      expect(duration).toBeLessThan(8000); // Should complete within 8 seconds

      // Verify final portfolio consistency
      const finalPortfolio = await portfolioService.getPortfolio(session.id);
      expect(finalPortfolio.totalValue).toBeGreaterThan(0);
      expect(finalPortfolio.positions.length).toBeGreaterThanOrEqual(2);

      console.log(`✓ Concurrent operations performance: ${duration.toFixed(2)}ms for ${operationPromises.length} operations`);
    });
  });

  describe('Analytics and Reporting Performance', () => {
    it('should generate analytics for large datasets efficiently', async () => {
      // Create session with many trades
      const session = await paperTradingEngine.createSession(testUserIds[0], {
        name: 'Analytics Performance Test',
        initialBalance: new Decimal(1000000),
        settings: {
          enableSlippage: false,
          enableSpread: false,
          riskManagement: {
            maxPositionSize: 1.0,
            maxDrawdown: 0.8,
            requireStopLoss: false
          }
        }
      });

      testSessionIds.push(session.id);

      // Execute many trades to create substantial history
      const tradePromises = Array.from({ length: 200 }, (_, i) => 
        paperTradingEngine.executePaperTrade(
          testUserIds[0],
          session.id,
          {
            symbol: ['EURUSD', 'GBPUSD', 'USDJPY'][i % 3],
            type: TradeType.MARKET,
            side: i % 2 === 0 ? TradeSide.BUY : TradeSide.SELL,
            quantity: new Decimal(1000 + (i % 5000)),
            price: undefined,
            stopLoss: undefined,
            takeProfit: undefined,
            metadata: {}
          }
        )
      );

      await Promise.all(tradePromises);

      // Close some positions to create P&L history
      const trades = await prisma.paperTrade.findMany({
        where: { sessionId: session.id },
        take: 100
      });

      await Promise.all(
        trades.slice(0, 50).map(trade => 
          paperTradingEngine.closePosition(testUserIds[0], session.id, trade.id)
        )
      );

      // Test analytics generation performance
      const startTime = performance.now();
      const analytics = await analyticsService.generateAnalytics(testUserIds[0], session.id);
      const duration = performance.now() - startTime;

      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
      expect(analytics.totalTrades).toBeGreaterThanOrEqual(200);
      expect(analytics.riskMetrics).toBeDefined();
      expect(analytics.performanceMetrics).toBeDefined();

      console.log(`✓ Analytics generation performance: ${duration.toFixed(2)}ms for ${analytics.totalTrades} trades`);
    });

    it('should handle concurrent analytics requests efficiently', async () => {
      // Create multiple sessions with trade history
      const sessionPromises = testUserIds.slice(0, 10).map(async userId => {
        const session = await paperTradingEngine.createSession(userId, {
          name: 'Concurrent Analytics Test',
          initialBalance: new Decimal(100000),
          settings: {
            enableSlippage: false,
            enableSpread: false,
            riskManagement: {
              maxPositionSize: 0.5,
              maxDrawdown: 0.3,
              requireStopLoss: false
            }
          }
        });

        // Execute some trades
        await Promise.all(
          Array.from({ length: 20 }, (_, i) => 
            paperTradingEngine.executePaperTrade(
              userId,
              session.id,
              {
                symbol: 'EURUSD',
                type: TradeType.MARKET,
                side: i % 2 === 0 ? TradeSide.BUY : TradeSide.SELL,
                quantity: new Decimal(1000),
                price: undefined,
                stopLoss: undefined,
                takeProfit: undefined,
                metadata: {}
              }
            )
          )
        );

        return session;
      });

      const sessions = await Promise.all(sessionPromises);
      testSessionIds.push(...sessions.map(s => s.id));

      // Generate analytics concurrently
      const startTime = performance.now();
      const analyticsPromises = sessions.map(session => 
        analyticsService.generateAnalytics(session.userId, session.id)
      );

      const analyticsResults = await Promise.all(analyticsPromises);
      const duration = performance.now() - startTime;

      expect(duration).toBeLessThan(8000); // Should complete within 8 seconds
      expect(analyticsResults).toHaveLength(10);
      analyticsResults.forEach(analytics => {
        expect(analytics.totalTrades).toBeGreaterThanOrEqual(20);
        expect(analytics.riskMetrics).toBeDefined();
      });

      const averageTime = duration / 10;
      console.log(`✓ Concurrent analytics performance: ${duration.toFixed(2)}ms for 10 sessions (${averageTime.toFixed(2)}ms avg)`);
    });
  });

  describe('Memory and Resource Management', () => {
    it('should maintain reasonable memory usage under load', async () => {
      const initialMemory = process.memoryUsage();

      // Create sessions and execute trades to simulate load
      const sessions = await Promise.all(
        testUserIds.slice(0, 20).map(userId => 
          paperTradingEngine.createSession(userId, {
            name: 'Memory Test Session',
            initialBalance: new Decimal(100000),
            settings: {
              enableSlippage: false,
              enableSpread: false,
              riskManagement: {
                maxPositionSize: 0.5,
                maxDrawdown: 0.3,
                requireStopLoss: false
              }
            }
          })
        )
      );

      testSessionIds.push(...sessions.map(s => s.id));

      // Execute trades in batches
      for (const session of sessions) {
        await Promise.all(
          Array.from({ length: 50 }, (_, i) => 
            paperTradingEngine.executePaperTrade(
              session.userId,
              session.id,
              {
                symbol: 'EURUSD',
                type: TradeType.MARKET,
                side: i % 2 === 0 ? TradeSide.BUY : TradeSide.SELL,
                quantity: new Decimal(1000),
                price: undefined,
                stopLoss: undefined,
                takeProfit: undefined,
                metadata: {}
              }
            )
          )
        );
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreaseMB = memoryIncrease / 1024 / 1024;

      expect(memoryIncreaseMB).toBeLessThan(200); // Less than 200MB increase
      console.log(`✓ Memory usage increase: ${memoryIncreaseMB.toFixed(2)}MB for 20 sessions with 50 trades each`);
    });

    it('should handle resource cleanup properly', async () => {
      const sessions = await Promise.all(
        testUserIds.slice(0, 10).map(userId => 
          paperTradingEngine.createSession(userId, {
            name: 'Cleanup Test Session',
            initialBalance: new Decimal(50000),
            settings: {
              enableSlippage: false,
              enableSpread: false,
              riskManagement: {
                maxPositionSize: 0.3,
                maxDrawdown: 0.2,
                requireStopLoss: false
              }
            }
          })
        )
      );

      testSessionIds.push(...sessions.map(s => s.id));

      // Execute trades
      await Promise.all(
        sessions.flatMap(session => 
          Array.from({ length: 20 }, (_, i) => 
            paperTradingEngine.executePaperTrade(
              session.userId,
              session.id,
              {
                symbol: 'EURUSD',
                type: TradeType.MARKET,
                side: i % 2 === 0 ? TradeSide.BUY : TradeSide.SELL,
                quantity: new Decimal(1000),
                price: undefined,
                stopLoss: undefined,
                takeProfit: undefined,
                metadata: {}
              }
            )
          )
        )
      );

      // Test cleanup by completing sessions
      const startTime = performance.now();
      
      await Promise.all(
        sessions.map(session => 
          prisma.paperTradingSession.update({
            where: { id: session.id },
            data: { status: 'COMPLETED' }
          })
        )
      );

      const duration = performance.now() - startTime;
      expect(duration).toBeLessThan(2000); // Cleanup should be fast

      // Verify sessions are properly marked as completed
      const completedSessions = await prisma.paperTradingSession.findMany({
        where: { 
          id: { in: testSessionIds },
          status: 'COMPLETED'
        }
      });

      expect(completedSessions).toHaveLength(10);
      console.log(`✓ Resource cleanup performance: ${duration.toFixed(2)}ms for 10 sessions`);
    });
  });
});