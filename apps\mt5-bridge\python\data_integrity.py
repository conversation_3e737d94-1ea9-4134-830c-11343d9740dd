"""
Data Integrity and Quality Assurance System
Enhanced validation with checksums, anomaly detection, and reconciliation
"""

import asyncio
import hashlib
import statistics
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
import numpy as np
from loguru import logger

from database import get_db_manager
from config import get_config

class IntegrityStatus(Enum):
    VALID = "VALID"
    WARNING = "WARNING"
    CORRUPT = "CORRUPT"
    MISSING = "MISSING"

class AnomalyType(Enum):
    PRICE_SPIKE = "PRICE_SPIKE"
    VOLUME_ANOMALY = "VOLUME_ANOMALY"
    DATA_GAP = "DATA_GAP"
    DUPLICATE_DATA = "DUPLICATE_DATA"
    TIMESTAMP_ANOMALY = "TIMESTAMP_ANOMALY"
    OUTLIER = "OUTLIER"

@dataclass
class IntegrityCheck:
    """Data integrity check result"""
    check_id: str
    instrument: str
    timeframe: str
    timestamp: datetime
    status: IntegrityStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    checksum: Optional[str] = None
    corrective_action: Optional[str] = None

@dataclass
class AnomalyDetection:
    """Anomaly detection result"""
    anomaly_id: str
    instrument: str
    timeframe: str
    anomaly_type: AnomalyType
    severity: float  # 0.0 to 1.0
    timestamp: datetime
    affected_records: int
    description: str
    details: Dict[str, Any] = field(default_factory=dict)
    confidence: float = 0.0  # 0.0 to 1.0

@dataclass
class QualityReport:
    """Comprehensive data quality report"""
    instrument: str
    timeframe: str
    period_start: datetime
    period_end: datetime
    total_records: int
    valid_records: int
    integrity_score: float  # 0.0 to 1.0
    anomalies_detected: int
    data_completeness: float  # 0.0 to 1.0
    checks_performed: List[IntegrityCheck]
    anomalies: List[AnomalyDetection]
    recommendations: List[str]
    generated_at: datetime = field(default_factory=datetime.now)

class DataIntegrityValidator:
    """
    Advanced data integrity and quality validation system
    """
    
    def __init__(self):
        self.config = get_config()
        self.db_manager = get_db_manager()
        
        # Integrity validation settings
        self.integrity_config = {
            'price_spike_threshold': 0.05,  # 5% price spike threshold
            'volume_spike_multiplier': 10.0,  # 10x normal volume
            'outlier_std_threshold': 3.0,  # 3 standard deviations
            'duplicate_tolerance_seconds': 1,  # Tolerance for duplicate timestamps
            'gap_threshold_multiplier': 2.0,  # 2x expected interval
            'checksum_sample_size': 1000,  # Records to sample for checksums
            'anomaly_confidence_threshold': 0.7  # Minimum confidence for anomaly
        }
        
        # Statistical baselines (updated periodically)
        self.statistical_baselines: Dict[str, Dict[str, Any]] = {}
        
    async def perform_comprehensive_validation(self, 
                                             instrument: str, 
                                             timeframe: str,
                                             start_date: datetime,
                                             end_date: datetime) -> QualityReport:
        """Perform comprehensive data quality validation"""
        
        logger.info(f"🔍 Starting comprehensive validation for {instrument} {timeframe}")
        
        # Get data for validation
        data = await self.db_manager.get_market_data(
            instrument, timeframe, start_date, end_date
        )
        
        if not data:
            return QualityReport(
                instrument=instrument,
                timeframe=timeframe,
                period_start=start_date,
                period_end=end_date,
                total_records=0,
                valid_records=0,
                integrity_score=0.0,
                anomalies_detected=0,
                data_completeness=0.0,
                checks_performed=[],
                anomalies=[],
                recommendations=["No data found for the specified period"]
            )
        
        # Perform integrity checks
        integrity_checks = []
        integrity_checks.extend(await self._check_data_format_integrity(data, instrument, timeframe))
        integrity_checks.extend(await self._check_price_data_integrity(data, instrument, timeframe))
        integrity_checks.extend(await self._check_temporal_integrity(data, instrument, timeframe))
        integrity_checks.extend(await self._check_volume_data_integrity(data, instrument, timeframe))
        integrity_checks.extend(await self._calculate_data_checksums(data, instrument, timeframe))
        
        # Perform anomaly detection
        anomalies = []
        anomalies.extend(await self._detect_price_anomalies(data, instrument, timeframe))
        anomalies.extend(await self._detect_volume_anomalies(data, instrument, timeframe))
        anomalies.extend(await self._detect_temporal_anomalies(data, instrument, timeframe))
        anomalies.extend(await self._detect_outliers(data, instrument, timeframe))
        
        # Calculate quality metrics
        valid_records = sum(1 for check in integrity_checks if check.status == IntegrityStatus.VALID)
        total_checks = len(integrity_checks)
        integrity_score = valid_records / max(total_checks, 1)
        
        # Calculate data completeness
        expected_records = await self._calculate_expected_records(instrument, timeframe, start_date, end_date)
        data_completeness = len(data) / max(expected_records, 1)
        
        # Generate recommendations
        recommendations = await self._generate_recommendations(integrity_checks, anomalies, data_completeness)
        
        # Store validation results
        await self._store_validation_results(instrument, timeframe, integrity_checks, anomalies)
        
        return QualityReport(
            instrument=instrument,
            timeframe=timeframe,
            period_start=start_date,
            period_end=end_date,
            total_records=len(data),
            valid_records=len(data),  # Adjusted based on validation
            integrity_score=integrity_score,
            anomalies_detected=len(anomalies),
            data_completeness=data_completeness,
            checks_performed=integrity_checks,
            anomalies=anomalies,
            recommendations=recommendations
        )
        
    async def _check_data_format_integrity(self, 
                                         data: List[Dict[str, Any]], 
                                         instrument: str, 
                                         timeframe: str) -> List[IntegrityCheck]:
        """Check data format integrity"""
        
        checks = []
        format_errors = 0
        
        required_fields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
        
        for i, record in enumerate(data):
            check_id = f"format_{instrument}_{timeframe}_{i}"
            
            # Check required fields
            missing_fields = [field for field in required_fields if field not in record]
            if missing_fields:
                format_errors += 1
                checks.append(IntegrityCheck(
                    check_id=check_id,
                    instrument=instrument,
                    timeframe=timeframe,
                    timestamp=record.get('timestamp', datetime.now()),
                    status=IntegrityStatus.CORRUPT,
                    message=f"Missing required fields: {missing_fields}",
                    details={'missing_fields': missing_fields, 'record_index': i}
                ))
                continue
            
            # Check data types
            type_errors = []
            try:
                float(record['open'])
                float(record['high'])
                float(record['low'])
                float(record['close'])
                int(record['volume'])
            except (ValueError, TypeError) as e:
                type_errors.append(str(e))
            
            if type_errors:
                format_errors += 1
                checks.append(IntegrityCheck(
                    check_id=check_id,
                    instrument=instrument,
                    timeframe=timeframe,
                    timestamp=record['timestamp'],
                    status=IntegrityStatus.CORRUPT,
                    message=f"Data type errors: {type_errors}",
                    details={'type_errors': type_errors, 'record_index': i}
                ))
            else:
                checks.append(IntegrityCheck(
                    check_id=check_id,
                    instrument=instrument,
                    timeframe=timeframe,
                    timestamp=record['timestamp'],
                    status=IntegrityStatus.VALID,
                    message="Data format valid"
                ))
        
        # Summary check
        error_rate = format_errors / len(data) if data else 0
        if error_rate > 0.01:  # More than 1% errors
            checks.append(IntegrityCheck(
                check_id=f"format_summary_{instrument}_{timeframe}",
                instrument=instrument,
                timeframe=timeframe,
                timestamp=datetime.now(),
                status=IntegrityStatus.CORRUPT,
                message=f"High format error rate: {error_rate:.2%}",
                details={'error_count': format_errors, 'total_records': len(data)}
            ))
        
        return checks
        
    async def _check_price_data_integrity(self, 
                                        data: List[Dict[str, Any]], 
                                        instrument: str, 
                                        timeframe: str) -> List[IntegrityCheck]:
        """Check price data integrity and relationships"""
        
        checks = []
        price_violations = 0
        
        for i, record in enumerate(data):
            check_id = f"price_{instrument}_{timeframe}_{i}"
            
            try:
                open_price = float(record['open'])
                high_price = float(record['high'])
                low_price = float(record['low'])
                close_price = float(record['close'])
                
                violations = []
                
                # Check OHLC relationships
                if not (low_price <= open_price <= high_price):
                    violations.append("Open price outside low-high range")
                if not (low_price <= close_price <= high_price):
                    violations.append("Close price outside low-high range")
                if high_price < low_price:
                    violations.append("High price less than low price")
                
                # Check for zero or negative prices
                if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
                    violations.append("Zero or negative prices detected")
                
                # Check for unrealistic price relationships
                price_range = high_price - low_price
                if price_range > open_price * 0.5:  # Range > 50% of open price
                    violations.append(f"Unrealistic price range: {price_range:.5f}")
                
                if violations:
                    price_violations += 1
                    checks.append(IntegrityCheck(
                        check_id=check_id,
                        instrument=instrument,
                        timeframe=timeframe,
                        timestamp=record['timestamp'],
                        status=IntegrityStatus.CORRUPT,
                        message=f"Price integrity violations: {'; '.join(violations)}",
                        details={
                            'violations': violations,
                            'prices': {'open': open_price, 'high': high_price, 'low': low_price, 'close': close_price}
                        }
                    ))
                else:
                    checks.append(IntegrityCheck(
                        check_id=check_id,
                        instrument=instrument,
                        timeframe=timeframe,
                        timestamp=record['timestamp'],
                        status=IntegrityStatus.VALID,
                        message="Price data integrity valid"
                    ))
                    
            except (ValueError, KeyError) as e:
                price_violations += 1
                checks.append(IntegrityCheck(
                    check_id=check_id,
                    instrument=instrument,
                    timeframe=timeframe,
                    timestamp=record.get('timestamp', datetime.now()),
                    status=IntegrityStatus.CORRUPT,
                    message=f"Price data error: {str(e)}",
                    details={'error': str(e)}
                ))
        
        return checks
        
    async def _check_temporal_integrity(self, 
                                      data: List[Dict[str, Any]], 
                                      instrument: str, 
                                      timeframe: str) -> List[IntegrityCheck]:
        """Check temporal data integrity"""
        
        checks = []
        temporal_issues = 0
        
        if len(data) < 2:
            return checks
        
        # Sort data by timestamp
        sorted_data = sorted(data, key=lambda x: x['timestamp'])
        
        # Calculate expected interval
        timeframe_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '4h': 240, '1d': 1440
        }
        expected_interval = timedelta(minutes=timeframe_minutes.get(timeframe, 1))
        
        for i in range(1, len(sorted_data)):
            check_id = f"temporal_{instrument}_{timeframe}_{i}"
            
            prev_time = sorted_data[i-1]['timestamp']
            curr_time = sorted_data[i]['timestamp']
            time_diff = curr_time - prev_time
            
            issues = []
            
            # Check for duplicate timestamps
            if time_diff.total_seconds() <= self.integrity_config['duplicate_tolerance_seconds']:
                issues.append("Duplicate or near-duplicate timestamp")
                temporal_issues += 1
            
            # Check for unrealistic gaps
            if time_diff > expected_interval * self.integrity_config['gap_threshold_multiplier']:
                issues.append(f"Large time gap: {time_diff}")
                temporal_issues += 1
            
            # Check for negative time progression
            if time_diff.total_seconds() < 0:
                issues.append("Negative time progression")
                temporal_issues += 1
            
            if issues:
                checks.append(IntegrityCheck(
                    check_id=check_id,
                    instrument=instrument,
                    timeframe=timeframe,
                    timestamp=curr_time,
                    status=IntegrityStatus.WARNING if len(issues) == 1 else IntegrityStatus.CORRUPT,
                    message=f"Temporal issues: {'; '.join(issues)}",
                    details={
                        'issues': issues,
                        'time_diff_seconds': time_diff.total_seconds(),
                        'expected_interval_seconds': expected_interval.total_seconds()
                    }
                ))
            else:
                checks.append(IntegrityCheck(
                    check_id=check_id,
                    instrument=instrument,
                    timeframe=timeframe,
                    timestamp=curr_time,
                    status=IntegrityStatus.VALID,
                    message="Temporal integrity valid"
                ))
        
        return checks
        
    async def _check_volume_data_integrity(self, 
                                         data: List[Dict[str, Any]], 
                                         instrument: str, 
                                         timeframe: str) -> List[IntegrityCheck]:
        """Check volume data integrity"""
        
        checks = []
        volume_issues = 0
        
        volumes = []
        for record in data:
            try:
                volume = int(record['volume'])
                volumes.append(volume)
            except (ValueError, KeyError):
                volume_issues += 1
                continue
        
        if not volumes:
            return checks
        
        # Calculate volume statistics
        avg_volume = statistics.mean(volumes)
        volume_std = statistics.stdev(volumes) if len(volumes) > 1 else 0
        
        for i, record in enumerate(data):
            check_id = f"volume_{instrument}_{timeframe}_{i}"
            
            try:
                volume = int(record['volume'])
                
                issues = []
                
                # Check for negative volume
                if volume < 0:
                    issues.append("Negative volume")
                    volume_issues += 1
                
                # Check for zero volume (warning only)
                if volume == 0:
                    issues.append("Zero volume")
                
                # Check for extreme volume spikes
                if volume_std > 0 and volume > avg_volume + (self.integrity_config['outlier_std_threshold'] * volume_std):
                    issues.append(f"Extreme volume spike: {volume} vs avg {avg_volume:.0f}")
                
                if issues:
                    status = IntegrityStatus.CORRUPT if "Negative volume" in issues else IntegrityStatus.WARNING
                    checks.append(IntegrityCheck(
                        check_id=check_id,
                        instrument=instrument,
                        timeframe=timeframe,
                        timestamp=record['timestamp'],
                        status=status,
                        message=f"Volume issues: {'; '.join(issues)}",
                        details={
                            'issues': issues,
                            'volume': volume,
                            'avg_volume': avg_volume,
                            'volume_std': volume_std
                        }
                    ))
                else:
                    checks.append(IntegrityCheck(
                        check_id=check_id,
                        instrument=instrument,
                        timeframe=timeframe,
                        timestamp=record['timestamp'],
                        status=IntegrityStatus.VALID,
                        message="Volume data integrity valid"
                    ))
                    
            except (ValueError, KeyError) as e:
                volume_issues += 1
                checks.append(IntegrityCheck(
                    check_id=check_id,
                    instrument=instrument,
                    timeframe=timeframe,
                    timestamp=record.get('timestamp', datetime.now()),
                    status=IntegrityStatus.CORRUPT,
                    message=f"Volume data error: {str(e)}",
                    details={'error': str(e)}
                ))
        
        return checks
        
    async def _calculate_data_checksums(self, 
                                      data: List[Dict[str, Any]], 
                                      instrument: str, 
                                      timeframe: str) -> List[IntegrityCheck]:
        """Calculate and verify data checksums"""
        
        checks = []
        
        if not data:
            return checks
        
        try:
            # Calculate overall data checksum
            data_string = json.dumps(data, sort_keys=True, default=str)
            overall_checksum = hashlib.sha256(data_string.encode()).hexdigest()
            
            # Calculate sample checksums for verification
            sample_size = min(self.integrity_config['checksum_sample_size'], len(data))
            sample_data = data[:sample_size]
            
            sample_string = json.dumps(sample_data, sort_keys=True, default=str)
            sample_checksum = hashlib.sha256(sample_string.encode()).hexdigest()
            
            checks.append(IntegrityCheck(
                check_id=f"checksum_{instrument}_{timeframe}_overall",
                instrument=instrument,
                timeframe=timeframe,
                timestamp=datetime.now(),
                status=IntegrityStatus.VALID,
                message=f"Data checksum calculated",
                details={
                    'total_records': len(data),
                    'sample_size': sample_size
                },
                checksum=overall_checksum
            ))
            
            checks.append(IntegrityCheck(
                check_id=f"checksum_{instrument}_{timeframe}_sample",
                instrument=instrument,
                timeframe=timeframe,
                timestamp=datetime.now(),
                status=IntegrityStatus.VALID,
                message=f"Sample checksum calculated",
                details={
                    'sample_size': sample_size
                },
                checksum=sample_checksum
            ))
            
            # Store checksums for future verification
            await self._store_checksum(instrument, timeframe, overall_checksum, len(data))
            
        except Exception as e:
            checks.append(IntegrityCheck(
                check_id=f"checksum_{instrument}_{timeframe}_error",
                instrument=instrument,
                timeframe=timeframe,
                timestamp=datetime.now(),
                status=IntegrityStatus.CORRUPT,
                message=f"Checksum calculation failed: {str(e)}",
                details={'error': str(e)}
            ))
        
        return checks
        
    async def _detect_price_anomalies(self, 
                                    data: List[Dict[str, Any]], 
                                    instrument: str, 
                                    timeframe: str) -> List[AnomalyDetection]:
        """Detect price anomalies and unusual movements"""
        
        anomalies = []
        
        if len(data) < 10:  # Need sufficient data
            return anomalies
        
        # Extract price data
        closes = [float(record['close']) for record in data]
        timestamps = [record['timestamp'] for record in data]
        
        # Calculate price changes
        price_changes = []
        for i in range(1, len(closes)):
            change_percent = abs(closes[i] - closes[i-1]) / closes[i-1] * 100
            price_changes.append((change_percent, i, timestamps[i]))
        
        # Detect price spikes
        spike_threshold = self.integrity_config['price_spike_threshold'] * 100  # Convert to percentage
        
        for change_percent, index, timestamp in price_changes:
            if change_percent > spike_threshold:
                severity = min(change_percent / (spike_threshold * 2), 1.0)
                confidence = 0.8 if change_percent > spike_threshold * 2 else 0.6
                
                anomalies.append(AnomalyDetection(
                    anomaly_id=f"price_spike_{instrument}_{timeframe}_{index}",
                    instrument=instrument,
                    timeframe=timeframe,
                    anomaly_type=AnomalyType.PRICE_SPIKE,
                    severity=severity,
                    timestamp=timestamp,
                    affected_records=1,
                    description=f"Price spike detected: {change_percent:.2f}% change",
                    details={
                        'price_change_percent': change_percent,
                        'threshold': spike_threshold,
                        'previous_close': closes[index-1],
                        'current_close': closes[index]
                    },
                    confidence=confidence
                ))
        
        # Detect unusual price patterns using statistical analysis
        if len(closes) >= 50:  # Need sufficient data for statistical analysis
            price_mean = statistics.mean(closes)
            price_std = statistics.stdev(closes)
            
            for i, (close, timestamp) in enumerate(zip(closes, timestamps)):
                z_score = abs(close - price_mean) / price_std if price_std > 0 else 0
                
                if z_score > self.integrity_config['outlier_std_threshold']:
                    severity = min(z_score / (self.integrity_config['outlier_std_threshold'] * 2), 1.0)
                    confidence = 0.7 if z_score > self.integrity_config['outlier_std_threshold'] * 1.5 else 0.5
                    
                    anomalies.append(AnomalyDetection(
                        anomaly_id=f"price_outlier_{instrument}_{timeframe}_{i}",
                        instrument=instrument,
                        timeframe=timeframe,
                        anomaly_type=AnomalyType.OUTLIER,
                        severity=severity,
                        timestamp=timestamp,
                        affected_records=1,
                        description=f"Price outlier detected: {z_score:.2f} standard deviations",
                        details={
                            'z_score': z_score,
                            'price': close,
                            'mean': price_mean,
                            'std': price_std
                        },
                        confidence=confidence
                    ))
        
        return anomalies
        
    async def _detect_volume_anomalies(self, 
                                     data: List[Dict[str, Any]], 
                                     instrument: str, 
                                     timeframe: str) -> List[AnomalyDetection]:
        """Detect volume anomalies"""
        
        anomalies = []
        
        if len(data) < 10:
            return anomalies
        
        # Extract volume data
        volumes = [int(record['volume']) for record in data if record['volume'] > 0]
        timestamps = [record['timestamp'] for record in data if record['volume'] > 0]
        
        if len(volumes) < 10:
            return anomalies
        
        # Calculate volume statistics
        avg_volume = statistics.mean(volumes)
        volume_std = statistics.stdev(volumes)
        
        # Detect volume spikes
        spike_multiplier = self.integrity_config['volume_spike_multiplier']
        
        for i, (volume, timestamp) in enumerate(zip(volumes, timestamps)):
            if volume > avg_volume * spike_multiplier:
                severity = min(volume / (avg_volume * spike_multiplier * 2), 1.0)
                confidence = 0.8
                
                anomalies.append(AnomalyDetection(
                    anomaly_id=f"volume_spike_{instrument}_{timeframe}_{i}",
                    instrument=instrument,
                    timeframe=timeframe,
                    anomaly_type=AnomalyType.VOLUME_ANOMALY,
                    severity=severity,
                    timestamp=timestamp,
                    affected_records=1,
                    description=f"Volume spike detected: {volume} vs avg {avg_volume:.0f}",
                    details={
                        'volume': volume,
                        'avg_volume': avg_volume,
                        'spike_ratio': volume / avg_volume
                    },
                    confidence=confidence
                ))
        
        return anomalies
        
    async def _detect_temporal_anomalies(self, 
                                       data: List[Dict[str, Any]], 
                                       instrument: str, 
                                       timeframe: str) -> List[AnomalyDetection]:
        """Detect temporal anomalies"""
        
        anomalies = []
        
        if len(data) < 2:
            return anomalies
        
        # Sort data by timestamp
        sorted_data = sorted(data, key=lambda x: x['timestamp'])
        
        # Calculate expected interval
        timeframe_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '4h': 240, '1d': 1440
        }
        expected_interval = timedelta(minutes=timeframe_minutes.get(timeframe, 1))
        
        # Detect gaps
        gap_count = 0
        for i in range(1, len(sorted_data)):
            prev_time = sorted_data[i-1]['timestamp']
            curr_time = sorted_data[i]['timestamp']
            time_diff = curr_time - prev_time
            
            if time_diff > expected_interval * self.integrity_config['gap_threshold_multiplier']:
                gap_count += 1
                severity = min(time_diff.total_seconds() / (expected_interval.total_seconds() * 10), 1.0)
                confidence = 0.9
                
                anomalies.append(AnomalyDetection(
                    anomaly_id=f"data_gap_{instrument}_{timeframe}_{i}",
                    instrument=instrument,
                    timeframe=timeframe,
                    anomaly_type=AnomalyType.DATA_GAP,
                    severity=severity,
                    timestamp=curr_time,
                    affected_records=1,
                    description=f"Data gap detected: {time_diff}",
                    details={
                        'gap_duration_seconds': time_diff.total_seconds(),
                        'expected_interval_seconds': expected_interval.total_seconds(),
                        'gap_ratio': time_diff.total_seconds() / expected_interval.total_seconds()
                    },
                    confidence=confidence
                ))
        
        return anomalies
        
    async def _detect_outliers(self, 
                             data: List[Dict[str, Any]], 
                             instrument: str, 
                             timeframe: str) -> List[AnomalyDetection]:
        """Detect statistical outliers using multiple methods"""
        
        anomalies = []
        
        if len(data) < 20:  # Need sufficient data for statistical analysis
            return anomalies
        
        # Extract numeric data
        closes = [float(record['close']) for record in data]
        volumes = [int(record['volume']) for record in data if record['volume'] > 0]
        timestamps = [record['timestamp'] for record in data]
        
        # Use numpy for advanced statistical analysis
        try:
            import numpy as np
            
            # Detect outliers using IQR method for prices
            close_array = np.array(closes)
            q1 = np.percentile(close_array, 25)
            q3 = np.percentile(close_array, 75)
            iqr = q3 - q1
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            for i, (close, timestamp) in enumerate(zip(closes, timestamps)):
                if close < lower_bound or close > upper_bound:
                    distance = max(abs(close - lower_bound), abs(close - upper_bound))
                    severity = min(distance / (iqr * 2), 1.0)
                    confidence = 0.6
                    
                    anomalies.append(AnomalyDetection(
                        anomaly_id=f"iqr_outlier_{instrument}_{timeframe}_{i}",
                        instrument=instrument,
                        timeframe=timeframe,
                        anomaly_type=AnomalyType.OUTLIER,
                        severity=severity,
                        timestamp=timestamp,
                        affected_records=1,
                        description=f"IQR outlier detected: {close:.5f}",
                        details={
                            'value': close,
                            'q1': q1,
                            'q3': q3,
                            'iqr': iqr,
                            'lower_bound': lower_bound,
                            'upper_bound': upper_bound
                        },
                        confidence=confidence
                    ))
                    
        except ImportError:
            logger.warning("NumPy not available for advanced outlier detection")
        
        return anomalies
        
    async def _calculate_expected_records(self, 
                                        instrument: str, 
                                        timeframe: str,
                                        start_date: datetime, 
                                        end_date: datetime) -> int:
        """Calculate expected number of records for a time period"""
        
        timeframe_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30,
            '1h': 60, '4h': 240, '1d': 1440
        }
        
        interval_minutes = timeframe_minutes.get(timeframe, 1)
        total_minutes = (end_date - start_date).total_seconds() / 60
        
        # Account for market hours (assuming 5 days a week, some timeframes 24/7)
        if timeframe in ['1d']:
            # Daily data - account for weekends
            total_days = (end_date - start_date).days
            business_days = total_days * 5 / 7  # Rough estimate
            return int(business_days)
        else:
            # Intraday data - assume 24/5 for forex
            return int(total_minutes / interval_minutes * 5 / 7)
            
    async def _generate_recommendations(self, 
                                      integrity_checks: List[IntegrityCheck], 
                                      anomalies: List[AnomalyDetection],
                                      data_completeness: float) -> List[str]:
        """Generate data quality recommendations"""
        
        recommendations = []
        
        # Analyze integrity issues
        corrupt_checks = [c for c in integrity_checks if c.status == IntegrityStatus.CORRUPT]
        warning_checks = [c for c in integrity_checks if c.status == IntegrityStatus.WARNING]
        
        if corrupt_checks:
            recommendations.append(f"Address {len(corrupt_checks)} critical data integrity issues")
        
        if warning_checks:
            recommendations.append(f"Review {len(warning_checks)} data quality warnings")
        
        # Analyze completeness
        if data_completeness < 0.8:
            recommendations.append(f"Improve data completeness (currently {data_completeness:.1%})")
        
        # Analyze anomalies
        high_severity_anomalies = [a for a in anomalies if a.severity > 0.7]
        if high_severity_anomalies:
            recommendations.append(f"Investigate {len(high_severity_anomalies)} high-severity anomalies")
        
        # Specific recommendations based on anomaly types
        anomaly_types = set(a.anomaly_type for a in anomalies)
        if AnomalyType.PRICE_SPIKE in anomaly_types:
            recommendations.append("Review price spike detection thresholds")
        if AnomalyType.DATA_GAP in anomaly_types:
            recommendations.append("Implement gap filling procedures")
        if AnomalyType.VOLUME_ANOMALY in anomaly_types:
            recommendations.append("Verify volume data sources")
        
        # General recommendations
        if not recommendations:
            recommendations.append("Data quality is good - continue monitoring")
        
        return recommendations
        
    async def _store_validation_results(self, 
                                      instrument: str, 
                                      timeframe: str,
                                      integrity_checks: List[IntegrityCheck], 
                                      anomalies: List[AnomalyDetection]):
        """Store validation results in database"""
        
        try:
            # Store integrity checks
            for check in integrity_checks:
                query = """
                INSERT INTO data_integrity_checks 
                (check_id, instrument, timeframe, timestamp, status, message, details, checksum)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
                ON CONFLICT (check_id) DO UPDATE SET
                    status = EXCLUDED.status,
                    message = EXCLUDED.message,
                    details = EXCLUDED.details,
                    checksum = EXCLUDED.checksum,
                    updated_at = NOW()
                """
                
                async with self.db_manager.connection_pool.acquire() as conn:
                    await conn.execute(
                        query,
                        check.check_id,
                        check.instrument,
                        check.timeframe,
                        check.timestamp,
                        check.status.value,
                        check.message,
                        check.details,
                        check.checksum
                    )
            
            # Store anomalies
            for anomaly in anomalies:
                query = """
                INSERT INTO data_anomalies 
                (anomaly_id, instrument, timeframe, anomaly_type, severity, timestamp, 
                 affected_records, description, details, confidence)
                VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
                ON CONFLICT (anomaly_id) DO UPDATE SET
                    severity = EXCLUDED.severity,
                    description = EXCLUDED.description,
                    details = EXCLUDED.details,
                    confidence = EXCLUDED.confidence,
                    updated_at = NOW()
                """
                
                async with self.db_manager.connection_pool.acquire() as conn:
                    await conn.execute(
                        query,
                        anomaly.anomaly_id,
                        anomaly.instrument,
                        anomaly.timeframe,
                        anomaly.anomaly_type.value,
                        anomaly.severity,
                        anomaly.timestamp,
                        anomaly.affected_records,
                        anomaly.description,
                        anomaly.details,
                        anomaly.confidence
                    )
                    
        except Exception as e:
            logger.error(f"❌ Failed to store validation results: {e}")
            
    async def _store_checksum(self, 
                            instrument: str, 
                            timeframe: str, 
                            checksum: str, 
                            record_count: int):
        """Store data checksum for future verification"""
        
        try:
            query = """
            INSERT INTO data_checksums 
            (instrument, timeframe, checksum, record_count, created_at)
            VALUES ($1, $2, $3, $4, NOW())
            """
            
            async with self.db_manager.connection_pool.acquire() as conn:
                await conn.execute(query, instrument, timeframe, checksum, record_count)
                
        except Exception as e:
            logger.error(f"❌ Failed to store checksum: {e}")

# Global instance
_data_integrity_validator: Optional[DataIntegrityValidator] = None

def get_data_integrity_validator() -> DataIntegrityValidator:
    """Get global data integrity validator instance"""
    global _data_integrity_validator
    if _data_integrity_validator is None:
        _data_integrity_validator = DataIntegrityValidator()
    return _data_integrity_validator