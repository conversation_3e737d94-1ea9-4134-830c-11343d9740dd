import { Router } from 'express';
import { CacheAnalyticsController } from '../../controllers/admin/CacheAnalyticsController';
import { MetricsCacheManager } from '../../services/cache/MetricsCacheManager';
import { CacheInvalidationService } from '../../services/cache/CacheInvalidationService';

export function createCacheAnalyticsRoutes(
  cacheManager: MetricsCacheManager,
  invalidationService: CacheInvalidationService
): Router {
  const router = Router();
  const controller = new CacheAnalyticsController(cacheManager, invalidationService);

  /**
   * @route GET /api/admin/cache/analytics
   * @desc Get comprehensive cache analytics data
   * @access Admin
   */
  router.get('/analytics', controller.getAnalytics.bind(controller));

  /**
   * @route GET /api/admin/cache/health
   * @desc Get cache health status and recommendations
   * @access Admin
   */
  router.get('/health', controller.getHealthStatus.bind(controller));

  /**
   * @route GET /api/admin/cache/config
   * @desc Get cache configuration
   * @access Admin
   */
  router.get('/config', controller.getConfiguration.bind(controller));

  /**
   * @route POST /api/admin/cache/operations
   * @desc Trigger manual cache operations
   * @access Admin
   * @body { operation: string, parameters?: object }
   */
  router.post('/operations', controller.triggerOperation.bind(controller));

  return router;
}

// Middleware for admin authentication (implement as needed)
export function requireAdminAuth(req: any, res: any, next: any) {
  // Implement your admin authentication logic here
  // For now, we'll allow all requests (NOT SECURE - implement proper auth)
  
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Authentication required' });
  }

  // In a real implementation, validate the admin token
  // const token = authHeader.substring(7);
  // if (!isValidAdminToken(token)) {
  //   return res.status(403).json({ error: 'Admin access required' });
  // }

  next();
}