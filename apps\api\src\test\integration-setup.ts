/**
 * Integration Test Setup
 * 
 * Global setup for integration tests including database setup,
 * environment configuration, and test utilities.
 */

import { beforeAll, afterAll, beforeEach, afterEach } from 'vitest';
import { vi } from 'vitest';

// Mock environment variables for testing
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/golddaddy_test';
process.env.REDIS_URL = 'redis://localhost:6379';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_REFRESH_SECRET = 'test-jwt-refresh-secret';

// Global test setup
beforeAll(async () => {
  console.log('Setting up integration tests...');
  
  // Initialize test database if needed
  // await initializeTestDatabase();
  
  // Set up global mocks
  setupGlobalMocks();
});

afterAll(async () => {
  console.log('Cleaning up integration tests...');
  
  // Clean up test database
  // await cleanupTestDatabase();
  
  // Clean up global mocks
  vi.clearAllMocks();
  vi.resetAllMocks();
});

beforeEach(() => {
  // Reset mocks before each test
  vi.clearAllMocks();
});

afterEach(() => {
  // Clean up after each test
  vi.clearAllTimers();
});

function setupGlobalMocks() {
  // Mock console methods to reduce test output noise
  vi.spyOn(console, 'log').mockImplementation(() => {});
  vi.spyOn(console, 'warn').mockImplementation(() => {});
  vi.spyOn(console, 'error').mockImplementation(() => {});
  
  // Mock timers
  vi.useFakeTimers();
}

// Export test utilities
export const testUtils = {
  // Test user for authentication
  mockUser: {
    id: 'test-user-id',
    email: '<EMAIL>',
    name: 'Test User'
  },
  
  // Mock request object
  createMockRequest: (overrides: any = {}) => ({
    user: testUtils.mockUser,
    params: {},
    query: {},
    body: {},
    headers: {},
    ...overrides
  }),
  
  // Mock response object
  createMockResponse: () => {
    const res: any = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
      send: vi.fn().mockReturnThis(),
      writeHead: vi.fn(),
      write: vi.fn(),
      end: vi.fn(),
      set: vi.fn()
    };
    return res;
  },
  
  // Mock next function
  createMockNext: () => vi.fn(),
  
  // Wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Generate test trade ID
  generateTestTradeId: () => `test-trade-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
  
  // Generate test modification ID
  generateTestModificationId: () => `test-mod-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
};

// Make test utils available globally
declare global {
  // eslint-disable-next-line no-var
  var testUtils: typeof testUtils;
}

(global as any).testUtils = testUtils;