/**
 * Validation Utilities
 * 
 * Reusable validation functions extracted from various services
 * to promote code reuse and maintainability across the application.
 */

import { z } from 'zod';
import Decimal from 'decimal.js';

// ===== String Validation Utilities =====

/**
 * Sanitize string inputs to prevent XSS and injection attacks
 */
export const sanitizeString = (str: string): string => {
  return str
    .replace(/[<>]/g, '') // Remove HTML brackets
    .replace(/['"]/g, '') // Remove quotes
    .replace(/[&]/g, '') // Remove ampersand
    .trim()
    .slice(0, 100); // Limit length
};

/**
 * Create sanitized string schema
 */
export const sanitizedStringSchema = z.string().transform(sanitizeString);

/**
 * Validate financial instrument format (alphanumeric + common symbols only)
 */
export const validateInstrument = (instrument: string): boolean => {
  const pattern = /^[A-Z0-9/_-]+$/;
  return pattern.test(instrument) && instrument.length >= 3 && instrument.length <= 20;
};

/**
 * Financial instrument schema
 */
export const instrumentSchema = z.string()
  .regex(/^[A-Z0-9/_-]+$/, 'Invalid instrument format')
  .min(3, 'Instrument must be at least 3 characters')
  .max(20, 'Instrument must not exceed 20 characters')
  .transform(str => str.toUpperCase());

// ===== JWT and Security Validation =====

/**
 * Validate JWT token structure without verification
 */
export const isValidJWTStructure = (token: string): boolean => {
  if (!token || typeof token !== 'string') return false;
  
  const parts = token.split('.');
  if (parts.length !== 3) return false;
  
  // Check if each part is valid base64url
  return parts.every(part => {
    try {
      // Basic base64url check (allow URL-safe characters)
      return /^[A-Za-z0-9_-]+$/.test(part);
    } catch {
      return false;
    }
  });
};

/**
 * Validate email format
 */
export const emailSchema = z.string()
  .email('Invalid email format')
  .max(254, 'Email too long')
  .transform(str => str.toLowerCase().trim());

/**
 * Validate password strength
 */
export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password too long')
  .regex(/[A-Z]/, 'Password must contain uppercase letter')
  .regex(/[a-z]/, 'Password must contain lowercase letter')
  .regex(/[0-9]/, 'Password must contain number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain special character');

// ===== Financial Data Validation =====

/**
 * Validate OHLC price data consistency
 */
export const validateOHLCConsistency = (
  open: Decimal,
  high: Decimal,
  low: Decimal,
  close: Decimal
): { isValid: boolean; issues: string[] } => {
  const issues: string[] = [];

  // High should be >= all other prices
  if (high.lt(open)) issues.push('High price is less than open price');
  if (high.lt(close)) issues.push('High price is less than close price');
  if (high.lt(low)) issues.push('High price is less than low price');

  // Low should be <= all other prices
  if (low.gt(open)) issues.push('Low price is greater than open price');
  if (low.gt(close)) issues.push('Low price is greater than close price');
  if (low.gt(high)) issues.push('Low price is greater than high price');

  return {
    isValid: issues.length === 0,
    issues
  };
};

/**
 * Validate price precision for financial instruments
 */
export const validatePricePrecision = (price: Decimal, expectedPrecision: number): boolean => {
  const priceStr = price.toFixed();
  const decimalPart = priceStr.split('.')[1];
  
  if (!decimalPart) return expectedPrecision === 0;
  return decimalPart.length <= expectedPrecision;
};

/**
 * Validate spread reasonableness
 */
export const validateSpread = (
  bid: Decimal,
  ask: Decimal,
  maxSpreadPercent: number = 1
): { isValid: boolean; spreadPercent: number } => {
  if (bid.gte(ask)) {
    return { isValid: false, spreadPercent: 0 };
  }

  const spread = ask.minus(bid);
  const midPrice = bid.plus(ask).dividedBy(2);
  const spreadPercent = spread.dividedBy(midPrice).times(100).toNumber();

  return {
    isValid: spreadPercent <= maxSpreadPercent,
    spreadPercent
  };
};

// ===== Market Data Validation =====

/**
 * Validate volume data
 */
export const validateVolume = (volume: Decimal.Instance): { isValid: boolean; reason?: string } => {
  if (volume.isNegative()) {
    return { isValid: false, reason: 'Volume cannot be negative' };
  }

  if (volume.isZero()) {
    return { isValid: false, reason: 'Volume should be greater than zero' };
  }

  // Check for unreasonably high volumes (could indicate data corruption)
  if (volume.gt(new Decimal('1e15'))) {
    return { isValid: false, reason: 'Volume suspiciously high' };
  }

  return { isValid: true };
};

/**
 * Validate timestamp for market data
 */
export const validateMarketTimestamp = (
  timestamp: Date,
  maxStaleMs: number = 300000 // 5 minutes default
): { isValid: boolean; age: number; isStale: boolean } => {
  const now = Date.now();
  const age = now - timestamp.getTime();

  return {
    isValid: age >= 0, // Not in future
    age,
    isStale: age > maxStaleMs
  };
};

// ===== Rate Limiting Validation =====

/**
 * Token bucket rate limit configuration validation
 */
export const rateLimitConfigSchema = z.object({
  tokensPerInterval: z.number().int().positive('Tokens per interval must be positive'),
  interval: z.number().int().positive('Interval must be positive milliseconds'),
  burstCapacity: z.number().int().positive('Burst capacity must be positive'),
});

// ===== Connection and Network Validation =====

/**
 * Validate IP address format (IPv4)
 */
export const validateIPv4 = (ip: string): boolean => {
  const ipv4Regex = /^(\d{1,3}\.){3}\d{1,3}$/;
  if (!ipv4Regex.test(ip)) return false;
  
  const parts = ip.split('.');
  return parts.every(part => {
    const num = parseInt(part, 10);
    return num >= 0 && num <= 255;
  });
};

/**
 * Validate user agent string (basic check)
 */
export const validateUserAgent = (userAgent: string): boolean => {
  if (!userAgent || userAgent.length < 5 || userAgent.length > 512) {
    return false;
  }
  
  // Basic check for common user agent patterns
  return /Mozilla|Chrome|Safari|Firefox|Edge|Bot|Crawler/i.test(userAgent);
};

// ===== WebSocket Message Validation =====

/**
 * Validate message size limits
 */
export const validateMessageSize = (
  message: unknown,
  maxSize: number = 1024
): { isValid: boolean; size: number } => {
  const messageStr = JSON.stringify(message);
  const size = messageStr.length;
  
  return {
    isValid: size <= maxSize,
    size
  };
};

/**
 * Validate connection limits
 */
export const validateConnectionLimits = (
  currentConnections: number,
  maxConnections: number
): { isValid: boolean; utilizationPercent: number } => {
  const utilizationPercent = (currentConnections / maxConnections) * 100;
  
  return {
    isValid: currentConnections < maxConnections,
    utilizationPercent
  };
};

// ===== Data Quality Validation =====

/**
 * Detect potential price anomalies
 */
export const detectPriceAnomaly = (
  currentPrice: Decimal,
  historicalPrices: Decimal[],
  maxDeviationPercent: number = 5
): { isAnomaly: boolean; deviationPercent: number; averagePrice: Decimal } => {
  if (historicalPrices.length === 0) {
    return {
      isAnomaly: false,
      deviationPercent: 0,
      averagePrice: currentPrice
    };
  }

  const averagePrice = historicalPrices
    .reduce((sum, price) => sum.plus(price), new Decimal(0))
    .dividedBy(historicalPrices.length);

  const deviation = currentPrice.minus(averagePrice).abs();
  const deviationPercent = deviation.dividedBy(averagePrice).times(100).toNumber();

  return {
    isAnomaly: deviationPercent > maxDeviationPercent,
    deviationPercent,
    averagePrice
  };
};

/**
 * Detect volume anomalies
 */
export const detectVolumeAnomaly = (
  currentVolume: Decimal,
  historicalVolumes: Decimal[],
  maxMultiplier: number = 10
): { isAnomaly: boolean; multiplier: number; averageVolume: Decimal } => {
  if (historicalVolumes.length === 0) {
    return {
      isAnomaly: false,
      multiplier: 1,
      averageVolume: currentVolume
    };
  }

  const averageVolume = historicalVolumes
    .reduce((sum, volume) => sum.plus(volume), new Decimal(0))
    .dividedBy(historicalVolumes.length);

  const multiplier = currentVolume.dividedBy(averageVolume).toNumber();

  return {
    isAnomaly: multiplier > maxMultiplier || multiplier < (1 / maxMultiplier),
    multiplier,
    averageVolume
  };
};

// ===== Environment and Configuration Validation =====

/**
 * Validate environment variable presence and format
 */
export const validateEnvVar = (
  name: string,
  value: string | undefined,
  options: {
    required?: boolean;
    format?: 'url' | 'number' | 'boolean' | 'json';
    minLength?: number;
    maxLength?: number;
  } = {}
): { isValid: boolean; reason?: string } => {
  if (options.required && !value) {
    return { isValid: false, reason: `Environment variable ${name} is required` };
  }

  if (!value) return { isValid: true }; // Optional and not provided

  if (options.minLength && value.length < options.minLength) {
    return { isValid: false, reason: `${name} is too short` };
  }

  if (options.maxLength && value.length > options.maxLength) {
    return { isValid: false, reason: `${name} is too long` };
  }

  switch (options.format) {
    case 'url':
      try {
        new URL(value);
        return { isValid: true };
      } catch {
        return { isValid: false, reason: `${name} must be a valid URL` };
      }

    case 'number':
      if (isNaN(Number(value))) {
        return { isValid: false, reason: `${name} must be a valid number` };
      }
      return { isValid: true };

    case 'boolean':
      if (!['true', 'false', '1', '0'].includes(value.toLowerCase())) {
        return { isValid: false, reason: `${name} must be a boolean value` };
      }
      return { isValid: true };

    case 'json':
      try {
        JSON.parse(value);
        return { isValid: true };
      } catch {
        return { isValid: false, reason: `${name} must be valid JSON` };
      }

    default:
      return { isValid: true };
  }
};

// ===== Broker Configuration Validation =====

/**
 * Validate broker configuration request
 */
export const validateBrokerConfig = async (
  config: any
): Promise<{ isValid: boolean; errors?: Record<string, string> }> => {
  const errors: Record<string, string> = {};

  // Validate broker name
  if (!config.brokerName || typeof config.brokerName !== 'string') {
    errors.brokerName = 'Broker name is required';
  } else if (config.brokerName.length < 2 || config.brokerName.length > 50) {
    errors.brokerName = 'Broker name must be between 2 and 50 characters';
  }

  // Validate priority
  if (typeof config.priority !== 'number' || config.priority < 1) {
    errors.priority = 'Priority must be a positive integer';
  }

  // Validate connection details
  if (!config.connectionDetails) {
    errors.connectionDetails = 'Connection details are required';
  } else {
    const { server, login, password, timeout } = config.connectionDetails;

    if (!server || typeof server !== 'string') {
      errors.server = 'Server is required';
    } else if (server.length < 5 || server.length > 100) {
      errors.server = 'Server must be between 5 and 100 characters';
    }

    if (!login || typeof login !== 'string') {
      errors.login = 'Login is required';
    } else if (login.length < 1 || login.length > 50) {
      errors.login = 'Login must be between 1 and 50 characters';
    }

    if (!password || typeof password !== 'string') {
      errors.password = 'Password is required';
    } else if (password.length < 1 || password.length > 100) {
      errors.password = 'Password must be between 1 and 100 characters';
    }

    if (typeof timeout !== 'number' || timeout < 1000 || timeout > 60000) {
      errors.timeout = 'Timeout must be between 1000 and 60000 milliseconds';
    }
  }

  // Validate features
  if (!config.features || !Array.isArray(config.features)) {
    errors.features = 'Features array is required';
  } else {
    const validFeatures = ['streaming', 'trading', 'history', 'monitoring'];
    const invalidFeatures = config.features.filter((f: any) => !validFeatures.includes(f));
    if (invalidFeatures.length > 0) {
      errors.features = `Invalid features: ${invalidFeatures.join(', ')}`;
    }
    if (config.features.length === 0) {
      errors.features = 'At least one feature must be specified';
    }
  }

  // Validate health check configuration (optional)
  if (config.healthCheck) {
    const { interval, timeout, retryCount } = config.healthCheck;

    if (interval !== undefined) {
      if (typeof interval !== 'number' || interval < 5000 || interval > 300000) {
        errors.healthCheckInterval = 'Health check interval must be between 5000 and 300000 milliseconds';
      }
    }

    if (timeout !== undefined) {
      if (typeof timeout !== 'number' || timeout < 1000 || timeout > 30000) {
        errors.healthCheckTimeout = 'Health check timeout must be between 1000 and 30000 milliseconds';
      }
    }

    if (retryCount !== undefined) {
      if (typeof retryCount !== 'number' || retryCount < 1 || retryCount > 10) {
        errors.retryCount = 'Retry count must be between 1 and 10';
      }
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors: Object.keys(errors).length > 0 ? errors : undefined
  };
};

// ===== Utility Export Object =====

/**
 * Consolidated validation utilities for easy import
 */
export const ValidationUtils = {
  // String utilities
  sanitizeString,
  sanitizedStringSchema,
  validateInstrument,
  instrumentSchema,
  emailSchema,
  passwordSchema,

  // JWT and security
  isValidJWTStructure,

  // Financial data
  validateOHLCConsistency,
  validatePricePrecision,
  validateSpread,
  validateVolume,
  validateMarketTimestamp,

  // Rate limiting
  rateLimitConfigSchema,

  // Network
  validateIPv4,
  validateUserAgent,

  // WebSocket messages
  validateMessageSize,
  validateConnectionLimits,

  // Data quality
  detectPriceAnomaly,
  detectVolumeAnomaly,

  // Environment
  validateEnvVar,

  // Broker configuration
  validateBrokerConfig,
};