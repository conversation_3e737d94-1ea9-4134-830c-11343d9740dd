/**
 * Performance Monitor Service
 * 
 * Real-time performance tracking for market data processing pipeline
 * with benchmark validation, memory optimization, and comprehensive metrics
 */

import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';

// Performance interfaces
export interface ProcessingMetrics {
  operationId: string;
  operationType: 'data_processing' | 'websocket_broadcast' | 'cache_operation' | 'database_query' | 'api_request';
  startTime: number;
  endTime: number;
  duration: number;
  success: boolean;
  memoryUsageBefore: NodeJS.MemoryUsage;
  memoryUsageAfter: NodeJS.MemoryUsage;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface PerformanceThresholds {
  dataProcessing: number; // ms
  websocketBroadcast: number; // ms
  cacheOperation: number; // ms
  databaseQuery: number; // ms
  apiRequest: number; // ms
  memoryThreshold: number; // MB
  gcThreshold: number; // MB
}

export interface SystemMetrics {
  cpuUsage: number;
  memoryUsage: NodeJS.MemoryUsage;
  eventLoopLag: number;
  uptime: number;
  processId: number;
  timestamp: Date;
}

export interface PerformanceStats {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  averageProcessingTime: number;
  medianProcessingTime: number;
  p95ProcessingTime: number;
  p99ProcessingTime: number;
  operationsByType: Record<string, {
    count: number;
    averageTime: number;
    successRate: number;
  }>;
  benchmarkViolations: number;
  memoryPressureEvents: number;
  gcEvents: number;
  lastGcTime?: Date;
  startTime: Date;
  lastUpdateTime: Date;
}

export interface PerformanceAlert {
  type: 'benchmark_violation' | 'memory_pressure' | 'high_error_rate' | 'system_degradation';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  operationType?: string;
  duration?: number;
  threshold?: number;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface PerformanceConfig {
  thresholds: PerformanceThresholds;
  monitoringInterval: number; // ms
  metricsRetentionPeriod: number; // ms
  alertingEnabled: boolean;
  benchmarkValidation: boolean;
  memoryMonitoring: boolean;
  gcMonitoring: boolean;
  maxMetricsHistory: number;
}

/**
 * Performance Monitor for real-time tracking of market data processing performance
 */
export class PerformanceMonitor extends EventEmitter {
  private config: PerformanceConfig;
  private metrics: ProcessingMetrics[] = [];
  private systemMetrics: SystemMetrics[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private startTime: Date;
  private operationTrackers: Map<string, {
    startTime: number;
    memoryBefore: NodeJS.MemoryUsage;
    operationType: ProcessingMetrics['operationType'];
    metadata?: Record<string, any>;
  }> = new Map();

  constructor(config?: Partial<PerformanceConfig>) {
    super();
    
    this.config = {
      thresholds: {
        dataProcessing: 500, // 500ms benchmark requirement
        websocketBroadcast: 100,
        cacheOperation: 50,
        databaseQuery: 200,
        apiRequest: 1000,
        memoryThreshold: 512, // MB
        gcThreshold: 256, // MB
      },
      monitoringInterval: 5000, // 5 seconds
      metricsRetentionPeriod: 3600000, // 1 hour
      alertingEnabled: true,
      benchmarkValidation: true,
      memoryMonitoring: true,
      gcMonitoring: true,
      maxMetricsHistory: 10000,
      ...config,
    };

    this.startTime = new Date();
    this.initializeMonitoring();
  }

  /**
   * Initialize performance monitoring
   */
  private initializeMonitoring(): void {
    // Start system metrics collection
    this.monitoringInterval = setInterval(() => {
      this.collectSystemMetrics();
      this.cleanupOldMetrics();
      this.checkPerformanceHealth();
    }, this.config.monitoringInterval);

    // Monitor garbage collection if enabled
    if (this.config.gcMonitoring) {
      this.setupGcMonitoring();
    }

    console.log('Performance monitoring initialized');
  }

  /**
   * Start tracking a performance operation
   */
  public startOperation(
    operationId: string,
    operationType: ProcessingMetrics['operationType'],
    metadata?: Record<string, any>
  ): void {
    const startTime = performance.now();
    const memoryBefore = process.memoryUsage();

    this.operationTrackers.set(operationId, {
      startTime,
      memoryBefore,
      operationType,
      metadata,
    });
  }

  /**
   * End tracking a performance operation
   */
  public endOperation(operationId: string, success: boolean = true): void {
    const endTime = performance.now();
    const memoryAfter = process.memoryUsage();
    
    const tracker = this.operationTrackers.get(operationId);
    if (!tracker) {
      console.warn(`No tracker found for operation: ${operationId}`);
      return;
    }

    const duration = endTime - tracker.startTime;
    
    const metrics: ProcessingMetrics = {
      operationId,
      operationType: tracker.operationType,
      startTime: tracker.startTime,
      endTime,
      duration,
      success,
      memoryUsageBefore: tracker.memoryBefore,
      memoryUsageAfter: memoryAfter,
      timestamp: new Date(),
      metadata: tracker.metadata,
    };

    this.metrics.push(metrics);
    this.operationTrackers.delete(operationId);

    // Validate against benchmarks
    if (this.config.benchmarkValidation) {
      this.validateBenchmark(metrics);
    }

    // Emit performance event
    this.emit('operation_completed', metrics);

    // Check for immediate alerts
    this.checkOperationAlerts(metrics);
  }

  /**
   * Validate operation against performance benchmarks
   */
  private validateBenchmark(metrics: ProcessingMetrics): void {
    const threshold = this.config.thresholds[metrics.operationType];
    
    if (metrics.duration > threshold) {
      const alert: PerformanceAlert = {
        type: 'benchmark_violation',
        severity: metrics.duration > threshold * 2 ? 'high' : 'medium',
        message: `${metrics.operationType} operation exceeded benchmark: ${metrics.duration.toFixed(2)}ms > ${threshold}ms`,
        operationType: metrics.operationType,
        duration: metrics.duration,
        threshold,
        timestamp: new Date(),
        metadata: metrics.metadata,
      };

      this.emitAlert(alert);
    }
  }

  /**
   * Check for operation-specific alerts
   */
  private checkOperationAlerts(metrics: ProcessingMetrics): void {
    // Memory pressure check
    const memoryDiff = metrics.memoryUsageAfter.heapUsed - metrics.memoryUsageBefore.heapUsed;
    const memoryDiffMB = memoryDiff / (1024 * 1024);
    
    if (memoryDiffMB > this.config.thresholds.memoryThreshold / 10) { // 10% of threshold
      const alert: PerformanceAlert = {
        type: 'memory_pressure',
        severity: memoryDiffMB > this.config.thresholds.memoryThreshold / 5 ? 'high' : 'medium',
        message: `High memory allocation during ${metrics.operationType}: ${memoryDiffMB.toFixed(2)}MB`,
        operationType: metrics.operationType,
        timestamp: new Date(),
        metadata: {
          memoryBefore: metrics.memoryUsageBefore.heapUsed,
          memoryAfter: metrics.memoryUsageAfter.heapUsed,
          memoryDiff,
        },
      };

      this.emitAlert(alert);
    }
  }

  /**
   * Collect system-level performance metrics
   */
  private collectSystemMetrics(): void {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const eventLoopLag = this.measureEventLoopLag();

    const systemMetrics: SystemMetrics = {
      cpuUsage: (cpuUsage.user + cpuUsage.system) / 1000, // Convert to ms
      memoryUsage,
      eventLoopLag,
      uptime: process.uptime(),
      processId: process.pid,
      timestamp: new Date(),
    };

    this.systemMetrics.push(systemMetrics);
    this.emit('system_metrics', systemMetrics);
  }

  /**
   * Measure event loop lag
   */
  private measureEventLoopLag(): number {
    const start = performance.now();
    
    return new Promise<number>((resolve) => {
      setImmediate(() => {
        const lag = performance.now() - start;
        resolve(lag);
      });
    }) as any; // Simplified for sync operation
    
    // Simplified synchronous approximation
    return 0;
  }

  /**
   * Setup garbage collection monitoring
   */
  private setupGcMonitoring(): void {
    // Monitor GC events using performance observers if available
    try {
      const { PerformanceObserver } = require('perf_hooks');
      
      const gcObserver = new PerformanceObserver((list: any) => {
        const entries = list.getEntries();
        for (const entry of entries) {
          if (entry.entryType === 'gc') {
            this.emit('gc_event', {
              kind: entry.kind,
              duration: entry.duration,
              timestamp: new Date(),
            });
          }
        }
      });

      gcObserver.observe({ entryTypes: ['gc'] });
    } catch (error) {
      console.warn('GC monitoring not available in this Node.js version');
    }
  }

  /**
   * Check overall performance health
   */
  private checkPerformanceHealth(): void {
    const stats = this.getPerformanceStats();
    const recentSystemMetrics = this.systemMetrics.slice(-10);
    
    // Check error rate
    const errorRate = 1 - (stats.successfulOperations / stats.totalOperations);
    if (errorRate > 0.1) { // 10% error rate
      this.emitAlert({
        type: 'high_error_rate',
        severity: errorRate > 0.2 ? 'critical' : 'high',
        message: `High error rate detected: ${(errorRate * 100).toFixed(1)}%`,
        timestamp: new Date(),
        metadata: { errorRate, totalOperations: stats.totalOperations },
      });
    }

    // Check system degradation
    if (recentSystemMetrics.length >= 5) {
      const avgMemory = recentSystemMetrics.reduce((sum, m) => sum + m.memoryUsage.heapUsed, 0) / recentSystemMetrics.length;
      const memoryMB = avgMemory / (1024 * 1024);
      
      if (memoryMB > this.config.thresholds.memoryThreshold) {
        this.emitAlert({
          type: 'system_degradation',
          severity: memoryMB > this.config.thresholds.memoryThreshold * 1.5 ? 'critical' : 'high',
          message: `High memory usage: ${memoryMB.toFixed(2)}MB`,
          timestamp: new Date(),
          metadata: { memoryUsage: memoryMB, threshold: this.config.thresholds.memoryThreshold },
        });
      }
    }
  }

  /**
   * Emit performance alert
   */
  private emitAlert(alert: PerformanceAlert): void {
    if (!this.config.alertingEnabled) return;

    this.emit('performance_alert', alert);
    
    // Log critical alerts
    if (alert.severity === 'critical') {
      console.error('CRITICAL Performance Alert:', alert.message);
    } else if (alert.severity === 'high') {
      console.warn('HIGH Performance Alert:', alert.message);
    }
  }

  /**
   * Get comprehensive performance statistics
   */
  public getPerformanceStats(): PerformanceStats {
    const now = new Date();
    const successfulOps = this.metrics.filter(m => m.success).length;
    const failedOps = this.metrics.length - successfulOps;
    
    const durations = this.metrics.map(m => m.duration).sort((a, b) => a - b);
    const averageTime = durations.length > 0 ? durations.reduce((sum, d) => sum + d, 0) / durations.length : 0;
    const medianTime = durations.length > 0 ? durations[Math.floor(durations.length / 2)] : 0;
    const p95Time = durations.length > 0 ? durations[Math.floor(durations.length * 0.95)] : 0;
    const p99Time = durations.length > 0 ? durations[Math.floor(durations.length * 0.99)] : 0;

    // Group by operation type
    const operationsByType: Record<string, { count: number; averageTime: number; successRate: number; }> = {};
    
    for (const metric of this.metrics) {
      if (!operationsByType[metric.operationType]) {
        operationsByType[metric.operationType] = { count: 0, averageTime: 0, successRate: 0 };
      }
      operationsByType[metric.operationType].count++;
    }

    // Calculate averages for each type
    Object.keys(operationsByType).forEach(type => {
      const typeMetrics = this.metrics.filter(m => m.operationType === type);
      const successfulTypeOps = typeMetrics.filter(m => m.success).length;
      
      operationsByType[type].averageTime = typeMetrics.reduce((sum, m) => sum + m.duration, 0) / typeMetrics.length;
      operationsByType[type].successRate = successfulTypeOps / typeMetrics.length;
    });

    // Count benchmark violations
    const benchmarkViolations = this.metrics.filter(m => {
      const threshold = this.config.thresholds[m.operationType];
      return m.duration > threshold;
    }).length;

    return {
      totalOperations: this.metrics.length,
      successfulOperations: successfulOps,
      failedOperations: failedOps,
      averageProcessingTime: averageTime,
      medianProcessingTime: medianTime,
      p95ProcessingTime: p95Time,
      p99ProcessingTime: p99Time,
      operationsByType,
      benchmarkViolations,
      memoryPressureEvents: 0, // Would be tracked separately
      gcEvents: 0, // Would be tracked separately
      startTime: this.startTime,
      lastUpdateTime: now,
    };
  }

  /**
   * Get recent system metrics
   */
  public getSystemMetrics(limit: number = 100): SystemMetrics[] {
    return this.systemMetrics.slice(-limit);
  }

  /**
   * Get performance health status
   */
  public getHealthStatus(): {
    isHealthy: boolean;
    issues: string[];
    metrics: PerformanceStats;
    systemHealth: {
      memoryUsage: number;
      cpuUsage: number;
      uptime: number;
    };
  } {
    const stats = this.getPerformanceStats();
    const recentSystem = this.systemMetrics.slice(-1)[0];
    const issues: string[] = [];

    // Check benchmark compliance
    if (stats.benchmarkViolations > stats.totalOperations * 0.05) { // 5% threshold
      issues.push(`High benchmark violation rate: ${stats.benchmarkViolations}/${stats.totalOperations} operations`);
    }

    // Check error rate
    const errorRate = stats.failedOperations / stats.totalOperations;
    if (errorRate > 0.1) {
      issues.push(`High error rate: ${(errorRate * 100).toFixed(1)}%`);
    }

    // Check system resources
    if (recentSystem) {
      const memoryMB = recentSystem.memoryUsage.heapUsed / (1024 * 1024);
      if (memoryMB > this.config.thresholds.memoryThreshold) {
        issues.push(`High memory usage: ${memoryMB.toFixed(2)}MB`);
      }
    }

    return {
      isHealthy: issues.length === 0,
      issues,
      metrics: stats,
      systemHealth: {
        memoryUsage: recentSystem ? recentSystem.memoryUsage.heapUsed / (1024 * 1024) : 0,
        cpuUsage: recentSystem ? recentSystem.cpuUsage : 0,
        uptime: process.uptime(),
      },
    };
  }

  /**
   * Clean up old metrics to prevent memory leaks
   */
  private cleanupOldMetrics(): void {
    const cutoffTime = Date.now() - this.config.metricsRetentionPeriod;
    
    // Clean old processing metrics
    this.metrics = this.metrics.filter(m => m.timestamp.getTime() > cutoffTime);
    
    // Clean old system metrics
    this.systemMetrics = this.systemMetrics.filter(m => m.timestamp.getTime() > cutoffTime);

    // Enforce max history limit
    if (this.metrics.length > this.config.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.config.maxMetricsHistory);
    }

    if (this.systemMetrics.length > this.config.maxMetricsHistory) {
      this.systemMetrics = this.systemMetrics.slice(-this.config.maxMetricsHistory);
    }
  }

  /**
   * Reset performance metrics
   */
  public resetMetrics(): void {
    this.metrics = [];
    this.systemMetrics = [];
    this.operationTrackers.clear();
    this.startTime = new Date();
    
    this.emit('metrics_reset', { timestamp: new Date() });
    console.log('Performance metrics reset');
  }

  /**
   * Update performance thresholds
   */
  public updateThresholds(thresholds: Partial<PerformanceThresholds>): void {
    this.config.thresholds = { ...this.config.thresholds, ...thresholds };
    
    this.emit('thresholds_updated', { 
      thresholds: this.config.thresholds,
      timestamp: new Date() 
    });
    
    console.log('Performance thresholds updated:', thresholds);
  }

  /**
   * Force garbage collection if available
   */
  public forceGarbageCollection(): boolean {
    if (global.gc) {
      global.gc();
      this.emit('gc_forced', { timestamp: new Date() });
      return true;
    }
    return false;
  }

  /**
   * Shutdown performance monitoring
   */
  public shutdown(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    // Complete any pending operations
    this.operationTrackers.clear();
    
    this.emit('monitor_shutdown', {
      finalStats: this.getPerformanceStats(),
      timestamp: new Date(),
    });

    this.removeAllListeners();
    console.log('Performance monitoring shut down');
  }
}