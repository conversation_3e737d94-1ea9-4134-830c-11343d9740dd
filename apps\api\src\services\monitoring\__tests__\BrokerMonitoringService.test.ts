/**
 * Broker Monitoring Service Tests
 * 
 * Test suite for the BrokerMonitoringService
 * Part of Task 4: Real-time Monitoring and Alerting
 */

import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { EventEmitter } from 'events';
import { BrokerMonitoringService } from '../BrokerMonitoringService.js';
import { BrokerHealthMonitor } from '../../trading/BrokerHealthMonitor.js';
import { ErrorClassificationService } from '../../trading/ErrorClassificationService.js';
import type { MonitoringAlert, MonitoringMetrics } from '@golddaddy/types';

// Mock WebSocket Server
vi.mock('ws', () => ({
  Server: vi.fn().mockImplementation(() => ({
    on: vi.fn(),
    close: vi.fn()
  }))
}));

// Mock PrismaClient
const mockPrisma = {
  systemError: {
    create: vi.fn(),
    update: vi.fn(),
  },
  brokerConfiguration: {
    findMany: vi.fn(),
  },
} as any;

// Mock BrokerHealthMonitor
class MockBrokerHealthMonitor extends EventEmitter {
  getHealthSummary = vi.fn();
}

// Mock ErrorClassificationService  
class MockErrorClassificationService extends EventEmitter {
  // Mock methods as needed
}

describe('BrokerMonitoringService', () => {
  let service: BrokerMonitoringService;
  let healthMonitor: MockBrokerHealthMonitor;
  let errorClassifier: MockErrorClassificationService;
  const mockUserId = 'user_123';

  beforeEach(() => {
    healthMonitor = new MockBrokerHealthMonitor();
    errorClassifier = new MockErrorClassificationService();
    
    service = new BrokerMonitoringService(
      mockPrisma,
      healthMonitor as any,
      errorClassifier as any
    );

    vi.clearAllMocks();
  });

  describe('Alert Management', () => {
    it('should create and store alerts', async () => {
      // Mock database create
      mockPrisma.systemError.create.mockResolvedValue({
        id: 'alert_123',
        message: 'Test alert'
      });

      const alertPromise = new Promise<MonitoringAlert>((resolve) => {
        service.on('alert', (alert: MonitoringAlert) => {
          resolve(alert);
        });
      });

      // Trigger alert creation by emitting error classification event
      errorClassifier.emit('errorClassified', {
        category: 'VALIDATION',
        message: 'Test error message',
        brokerId: 'broker_123',
        timestamp: new Date()
      });

      const alert = await alertPromise;
      expect(alert.type).toBe('system_error');
      expect(alert.severity).toBe('medium');
      expect(alert.message).toBe('Test error message');
      expect(alert.acknowledged).toBe(false);
      expect(mockPrisma.systemError.create).toHaveBeenCalled();
    });

    it('should track alert history', async () => {
      let alertCount = 0;

      const alertPromise = new Promise<void>((resolve) => {
        service.on('alert', () => {
          alertCount++;
          if (alertCount === 2) {
            resolve();
          }
        });
      });

      // Create two alerts
      errorClassifier.emit('errorClassified', {
        category: 'VALIDATION',
        message: 'First alert',
        brokerId: 'broker_123',
        timestamp: new Date()
      });

      setTimeout(() => {
        errorClassifier.emit('errorClassified', {
          category: 'VALIDATION', 
          message: 'Second alert',
          brokerId: 'broker_123',
          timestamp: new Date()
        });
      }, 10);

      await alertPromise;
      
      const history = service.getAlertHistory({ limit: 10 });
      expect(history).toHaveLength(2);
      expect(history[0].message).toBe('Second alert');
      expect(history[1].message).toBe('First alert');
    });

    it('should filter alerts by criteria', async () => {
      let alertCount = 0;

      const alertPromise = new Promise<void>((resolve) => {
        service.on('alert', () => {
          alertCount++;
          if (alertCount === 2) {
            resolve();
          }
        });
      });

      // Create alerts with different severities
      errorClassifier.emit('errorClassified', {
        category: 'CRITICAL',
        message: 'Critical error',
        brokerId: 'broker_123',
        timestamp: new Date()
      });

      errorClassifier.emit('errorClassified', {
        category: 'VALIDATION',
        message: 'Validation error',
        brokerId: 'broker_123', 
        timestamp: new Date()
      });

      await alertPromise;

      const criticalAlerts = service.getAlertHistory({ severity: 'critical' });
      const brokerAlerts = service.getAlertHistory({ brokerId: 'broker_123' });
      
      expect(criticalAlerts).toHaveLength(1);
      expect(brokerAlerts).toHaveLength(2);
    });
  });

  describe('Metrics Tracking', () => {
    it('should provide current metrics', () => {
      const metrics = service.getCurrentMetrics();
      
      expect(metrics).toHaveProperty('totalBrokers');
      expect(metrics).toHaveProperty('healthyBrokers');
      expect(metrics).toHaveProperty('unhealthyBrokers');
      expect(metrics).toHaveProperty('averageLatency');
      expect(metrics).toHaveProperty('errorRate');
      expect(metrics).toHaveProperty('alertCount');
      expect(metrics).toHaveProperty('lastUpdateTime');
    });

    it('should update metrics on health check results', () => {
      const initialMetrics = service.getCurrentMetrics();
      const initialLatency = initialMetrics.averageLatency;

      // Simulate health check result
      healthMonitor.emit('healthCheck', {
        brokerId: 'broker_123',
        healthy: true,
        latency: 1000,
        timestamp: new Date()
      });

      const updatedMetrics = service.getCurrentMetrics();
      expect(updatedMetrics.averageLatency).not.toBe(initialLatency);
      expect(updatedMetrics.lastUpdateTime).not.toBe(initialMetrics.lastUpdateTime);
    });
  });

  describe('Dashboard Data', () => {
    it('should get comprehensive dashboard data', async () => {
      // Mock health summary
      healthMonitor.getHealthSummary.mockResolvedValue({
        totalBrokers: 3,
        healthyBrokers: 2,
        unhealthyBrokers: 1,
        averageLatency: 150,
        lastCheckTime: new Date()
      });

      // Mock broker configurations
      mockPrisma.brokerConfiguration.findMany.mockResolvedValue([
        {
          id: 'broker_1',
          brokerName: 'Broker 1',
          priority: 1,
          status: 'ACTIVE',
          isHealthy: true,
          lastHealthCheck: new Date(),
          lastError: null,
          failureCount: 0,
          createdAt: new Date()
        },
        {
          id: 'broker_2', 
          brokerName: 'Broker 2',
          priority: 2,
          status: 'FAILED',
          isHealthy: false,
          lastHealthCheck: new Date(),
          lastError: 'Connection timeout',
          failureCount: 3,
          createdAt: new Date()
        }
      ]);

      const dashboardData = await service.getDashboardData(mockUserId);

      expect(dashboardData.metrics.totalBrokers).toBe(3);
      expect(dashboardData.metrics.healthyBrokers).toBe(2);
      expect(dashboardData.metrics.unhealthyBrokers).toBe(1);
      expect(dashboardData.brokerStatuses).toHaveLength(2);
      expect(dashboardData.brokerStatuses[0].brokerName).toBe('Broker 1');
      expect(dashboardData.brokerStatuses[1].isHealthy).toBe(false);
    });

    it('should handle dashboard data errors gracefully', async () => {
      healthMonitor.getHealthSummary.mockRejectedValue(new Error('Database error'));

      await expect(service.getDashboardData(mockUserId)).rejects.toThrow('Database error');
    });
  });

  describe('High Latency Detection', () => {
    it('should create alerts for high latency', async () => {
      const alertPromise = new Promise<MonitoringAlert>((resolve) => {
        service.on('alert', (alert: MonitoringAlert) => {
          if (alert.type === 'high_latency') {
            resolve(alert);
          }
        });
      });

      // Simulate high latency health check
      healthMonitor.emit('healthCheck', {
        brokerId: 'broker_123',
        healthy: true,
        latency: 8000, // Higher than threshold
        timestamp: new Date()
      });

      const alert = await alertPromise;
      expect(alert.severity).toBe('high');
      expect(alert.message).toContain('High latency detected');
      expect(alert.metadata?.latency).toBe(8000);
    });

    it('should create critical alerts for very high latency', async () => {
      const alertPromise = new Promise<MonitoringAlert>((resolve) => {
        service.on('alert', (alert: MonitoringAlert) => {
          if (alert.type === 'high_latency') {
            resolve(alert);
          }
        });
      });

      // Simulate very high latency
      healthMonitor.emit('healthCheck', {
        brokerId: 'broker_123',
        healthy: true,
        latency: 15000, // Higher than critical threshold
        timestamp: new Date()
      });

      const alert = await alertPromise;
      expect(alert.severity).toBe('critical');
      expect(alert.metadata?.latency).toBe(15000);
    });
  });

  describe('Broker Health Events', () => {
    it('should create alerts for unhealthy brokers', async () => {
      const alertPromise = new Promise<MonitoringAlert>((resolve) => {
        service.on('alert', (alert: MonitoringAlert) => {
          if (alert.type === 'broker_unhealthy') {
            resolve(alert);
          }
        });
      });

      healthMonitor.emit('brokerUnhealthy', {
        brokerId: 'broker_123',
        brokerName: 'Test Broker',
        error: 'Connection failed',
        timestamp: new Date()
      });

      const alert = await alertPromise;
      expect(alert.severity).toBe('high');
      expect(alert.message).toContain('Broker Test Broker is unhealthy');
      expect(alert.brokerId).toBe('broker_123');
    });

    it('should create info alerts for healthy brokers', async () => {
      const alertPromise = new Promise<MonitoringAlert>((resolve) => {
        service.on('alert', (alert: MonitoringAlert) => {
          if (alert.type === 'broker_healthy') {
            resolve(alert);
          }
        });
      });

      healthMonitor.emit('brokerHealthy', {
        brokerId: 'broker_123',
        brokerName: 'Test Broker',
        latency: 100,
        timestamp: new Date()
      });

      const alert = await alertPromise;
      expect(alert.severity).toBe('info');
      expect(alert.message).toContain('Broker Test Broker is now healthy');
    });
  });

  describe('Circuit Breaker Events', () => {
    it('should create critical alerts for circuit breaker opens', async () => {
      const alertPromise = new Promise<MonitoringAlert>((resolve) => {
        service.on('alert', (alert: MonitoringAlert) => {
          if (alert.type === 'circuit_breaker_opened') {
            resolve(alert);
          }
        });
      });

      errorClassifier.emit('circuitBreakerOpened', {
        service: 'trading',
        reason: 'Too many failures',
        failureCount: 5,
        brokerId: 'broker_123',
        timestamp: new Date()
      });

      const alert = await alertPromise;
      expect(alert.severity).toBe('critical');
      expect(alert.message).toContain('Circuit breaker opened');
      expect(alert.metadata?.service).toBe('trading');
    });
  });

  describe('WebSocket Integration', () => {
    it('should initialize WebSocket server', () => {
      expect(() => {
        service.initializeWebSocket(8080);
      }).not.toThrow();
    });
  });

  describe('Service Shutdown', () => {
    it('should shutdown gracefully', () => {
      expect(() => {
        service.shutdown();
      }).not.toThrow();
    });
  });
});