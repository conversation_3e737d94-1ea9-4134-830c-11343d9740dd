/**
 * Python Bridge Communication
 * Handles communication between TypeScript bridge and Python MT5 service
 */

import axios, { AxiosInstance } from 'axios';
import WebSocket from 'ws';
import { EventEmitter } from 'events';
import type { MarketData, Trade } from '@golddaddy/types';

interface PythonServiceConfig {
  apiUrl: string;
  websocketUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
}

interface ConnectionStatus {
  connected: boolean;
  lastPing: Date | null;
  error: string | null;
  retryCount: number;
}

export class PythonBridge extends EventEmitter {
  private config: PythonServiceConfig;
  private httpClient: AxiosInstance;
  private websocket: WebSocket | null = null;
  private status: ConnectionStatus;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private pingTimer: NodeJS.Timeout | null = null;
  private connectionId: string;

  constructor(config: Partial<PythonServiceConfig> = {}) {
    super();
    
    this.connectionId = `bridge_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    this.config = {
      apiUrl: process.env.PYTHON_API_URL || 'http://localhost:8001',
      websocketUrl: process.env.PYTHON_WS_URL || 'ws://localhost:8001',
      timeout: parseInt(process.env.PYTHON_TIMEOUT || '30000'),
      retryAttempts: parseInt(process.env.PYTHON_RETRY_ATTEMPTS || '5'),
      retryDelay: parseInt(process.env.PYTHON_RETRY_DELAY || '5000'),
      ...config
    };

    this.httpClient = axios.create({
      baseURL: this.config.apiUrl,
      timeout: this.config.timeout,
      headers: {
        'Content-Type': 'application/json',
      }
    });

    this.status = {
      connected: false,
      lastPing: null,
      error: null,
      retryCount: 0
    };

    this.setupHttpInterceptors();
  }

  private setupHttpInterceptors(): void {
    this.httpClient.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error('Python API error:', error.message);
        this.status.error = error.message;
        this.emit('apiError', error);
        return Promise.reject(error);
      }
    );
  }

  async initialize(): Promise<boolean> {
    try {
      console.log('🔗 Initializing Python bridge...');
      
      // Test HTTP connection
      const healthCheck = await this.healthCheck();
      if (!healthCheck.success) {
        throw new Error(`Python service health check failed: ${healthCheck.error}`);
      }

      // Initialize WebSocket connection
      await this.connectWebSocket();

      console.log('✅ Python bridge initialized successfully');
      return true;
    } catch (error) {
      console.error('❌ Python bridge initialization failed:', error);
      this.status.error = error instanceof Error ? error.message : 'Unknown error';
      return false;
    }
  }

  async healthCheck(): Promise<{ success: boolean; error?: string; data?: any }> {
    try {
      const response = await this.httpClient.get('/health');
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Health check failed'
      };
    }
  }

  async getMT5Status(): Promise<any> {
    try {
      const response = await this.httpClient.get('/mt5/status');
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get MT5 status: ${error}`);
    }
  }

  async initializeMT5(config?: any): Promise<boolean> {
    try {
      const response = await this.httpClient.post('/mt5/initialize', config || {});
      return response.data.success;
    } catch (error) {
      throw new Error(`Failed to initialize MT5: ${error}`);
    }
  }

  async getMarketData(symbol: string, timeframe: string, count: number = 100): Promise<MarketData[]> {
    try {
      const response = await this.httpClient.get('/mt5/market-data', {
        params: { symbol, timeframe, count }
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get market data: ${error}`);
    }
  }

  async executeTrade(tradeRequest: any): Promise<Trade> {
    try {
      const response = await this.httpClient.post('/mt5/trade', tradeRequest);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to execute trade: ${error}`);
    }
  }

  async getSymbols(): Promise<string[]> {
    try {
      const response = await this.httpClient.get('/mt5/symbols');
      return response.data;
    } catch (error) {
      throw new Error(`Failed to get symbols: ${error}`);
    }
  }

  private async connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = `${this.config.websocketUrl}/ws/${this.connectionId}`;
        console.log(`🔗 Connecting to WebSocket: ${wsUrl}`);
        this.websocket = new WebSocket(wsUrl);

        this.websocket.on('open', () => {
          console.log('🔗 WebSocket connected to Python service');
          this.status.connected = true;
          this.status.error = null;
          this.status.retryCount = 0;
          this.startPingTimer();
          this.emit('connected');
          resolve();
        });

        this.websocket.on('message', (data: Buffer) => {
          try {
            const message = JSON.parse(data.toString());
            this.handleWebSocketMessage(message);
          } catch (error) {
            console.error('Failed to parse WebSocket message:', error);
          }
        });

        this.websocket.on('close', () => {
          console.log('🔌 WebSocket disconnected from Python service');
          this.status.connected = false;
          this.stopPingTimer();
          this.emit('disconnected');
          this.scheduleReconnect();
        });

        this.websocket.on('error', (error) => {
          console.error('WebSocket error:', error);
          this.status.error = error.message;
          this.emit('error', error);
          reject(error);
        });

        // Connection timeout
        setTimeout(() => {
          if (!this.status.connected) {
            reject(new Error('WebSocket connection timeout'));
          }
        }, this.config.timeout);

      } catch (error) {
        reject(error);
      }
    });
  }

  private handleWebSocketMessage(message: any): void {
    switch (message.type) {
      case 'price_update':
        this.emit('marketData', message.data);
        break;
      case 'trade_update':
        this.emit('tradeUpdate', message.data);
        break;
      case 'status_response':
        this.emit('mt5Status', message.data);
        break;
      case 'subscribed':
        console.log(`✅ Subscribed to ${message.symbol}`);
        this.emit('subscribed', message.symbol);
        break;
      case 'unsubscribed':
        console.log(`🔌 Unsubscribed from ${message.symbol}`);
        this.emit('unsubscribed', message.symbol);
        break;
      case 'pong':
        this.status.lastPing = new Date();
        break;
      case 'error':
        console.error('Python service error:', message.message || message.error);
        this.emit('pythonError', message.message || message.error);
        break;
      default:
        console.warn('Unknown WebSocket message type:', message.type);
    }
  }

  private startPingTimer(): void {
    this.pingTimer = setInterval(() => {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.websocket.send(JSON.stringify({ type: 'ping', timestamp: new Date().toISOString() }));
      }
    }, 30000); // Ping every 30 seconds
  }

  private stopPingTimer(): void {
    if (this.pingTimer) {
      clearInterval(this.pingTimer);
      this.pingTimer = null;
    }
  }

  private scheduleReconnect(): void {
    if (this.status.retryCount >= this.config.retryAttempts) {
      console.error('❌ Maximum reconnection attempts reached');
      this.emit('maxRetriesReached');
      return;
    }

    this.status.retryCount++;
    console.log(`🔄 Scheduling reconnection attempt ${this.status.retryCount}/${this.config.retryAttempts}`);

    this.reconnectTimer = setTimeout(async () => {
      try {
        await this.connectWebSocket();
      } catch (error) {
        console.error('Reconnection failed:', error);
      }
    }, this.config.retryDelay);
  }

  sendMessage(message: any): boolean {
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      console.error('WebSocket not connected');
      return false;
    }

    try {
      this.websocket.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error('Failed to send WebSocket message:', error);
      return false;
    }
  }

  subscribeToSymbol(symbol: string): boolean {
    return this.sendMessage({
      type: 'subscribe',
      symbol,
      timestamp: new Date().toISOString()
    });
  }

  unsubscribeFromSymbol(symbol: string): boolean {
    return this.sendMessage({
      type: 'unsubscribe',
      symbol,
      timestamp: new Date().toISOString()
    });
  }

  getStatus(): ConnectionStatus {
    return { ...this.status };
  }

  isConnected(): boolean {
    return this.status.connected && this.websocket?.readyState === WebSocket.OPEN;
  }

  async disconnect(): Promise<void> {
    console.log('🔌 Disconnecting Python bridge...');

    // Clear timers
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    this.stopPingTimer();

    // Close WebSocket
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }

    this.status.connected = false;
    this.emit('disconnected');
    console.log('✅ Python bridge disconnected');
  }
}

// Export singleton instance
let pythonBridge: PythonBridge | null = null;

export function getPythonBridge(): PythonBridge {
  if (!pythonBridge) {
    pythonBridge = new PythonBridge();
  }
  return pythonBridge;
}