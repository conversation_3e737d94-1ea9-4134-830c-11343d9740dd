"""
Latency Measurement and Optimization System
Advanced latency tracking, analysis, and optimization for critical operations
"""

import asyncio
import time
import statistics
from typing import Dict, List, Any, Optional, Callable, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict, deque
import threading
import json
import numpy as np
from loguru import logger

from performance_monitor import PerformanceProfiler, LatencyMeasurement

@dataclass
class LatencyBenchmark:
    """Latency benchmark configuration"""
    operation: str
    target_ms: float
    warning_ms: float
    critical_ms: float
    description: str
    optimization_hints: List[str] = field(default_factory=list)

@dataclass
class LatencyOptimization:
    """Latency optimization recommendation"""
    operation: str
    current_latency_ms: float
    target_latency_ms: float
    optimization_type: str
    description: str
    implementation_priority: int  # 1-5, 1 being highest
    estimated_improvement_ms: float
    implementation_effort: str  # 'low', 'medium', 'high'

class LatencyAnalyzer:
    """Advanced latency analysis and optimization system"""
    
    def __init__(self):
        self.profiler = PerformanceProfiler()
        
        # Define latency benchmarks for different operations
        self.benchmarks = {
            'api_response': LatencyBenchmark(
                operation='api_response',
                target_ms=50,
                warning_ms=100,
                critical_ms=200,
                description='API endpoint response time',
                optimization_hints=[
                    'Use async/await for I/O operations',
                    'Implement response caching',
                    'Optimize database queries',
                    'Use connection pooling'
                ]
            ),
            'database_query': LatencyBenchmark(
                operation='database_query',
                target_ms=10,
                warning_ms=25,
                critical_ms=50,
                description='Database query execution time',
                optimization_hints=[
                    'Add database indexes',
                    'Optimize query structure',
                    'Use prepared statements',
                    'Implement query caching'
                ]
            ),
            'price_processing': LatencyBenchmark(
                operation='price_processing',
                target_ms=2,
                warning_ms=5,
                critical_ms=10,
                description='Price data processing time',
                optimization_hints=[
                    'Vectorize calculations',
                    'Use compiled extensions',
                    'Batch process data',
                    'Optimize data structures'
                ]
            ),
            'websocket_broadcast': LatencyBenchmark(
                operation='websocket_broadcast',
                target_ms=5,
                warning_ms=10,
                critical_ms=20,
                description='WebSocket message broadcast time',
                optimization_hints=[
                    'Use message queuing',
                    'Implement connection pooling',
                    'Optimize serialization',
                    'Use async broadcasting'
                ]
            ),
            'mt5_data_fetch': LatencyBenchmark(
                operation='mt5_data_fetch',
                target_ms=20,
                warning_ms=50,
                critical_ms=100,
                description='MT5 data retrieval time',
                optimization_hints=[
                    'Implement data caching',
                    'Use bulk data requests',
                    'Optimize MT5 connection',
                    'Implement request batching'
                ]
            )
        }
        
        # Optimization tracking
        self.optimization_history: List[LatencyOptimization] = []
        self.baseline_measurements: Dict[str, List[float]] = defaultdict(list)
        
    async def start_latency_monitoring(self):
        """Start continuous latency monitoring and analysis"""
        
        logger.info("📊 Starting advanced latency monitoring...")
        
        # Start the base profiler
        self.profiler.start_monitoring()
        
        # Start latency analysis loop
        asyncio.create_task(self._latency_analysis_loop())
    
    async def _latency_analysis_loop(self):
        """Continuous latency analysis and optimization detection"""
        
        while self.profiler.monitoring_active:
            try:
                await asyncio.sleep(30)  # Analyze every 30 seconds
                
                # Analyze recent latency patterns
                analysis_results = self.analyze_latency_patterns()
                
                # Generate optimization recommendations
                optimizations = self.generate_optimization_recommendations(analysis_results)
                
                # Log critical latency issues
                for optimization in optimizations:
                    if optimization.implementation_priority <= 2:
                        logger.warning(
                            f"🔧 High-priority optimization needed: {optimization.operation} "
                            f"(current: {optimization.current_latency_ms:.2f}ms, "
                            f"target: {optimization.target_latency_ms:.2f}ms)"
                        )
                
            except Exception as e:
                logger.error(f"Latency analysis loop error: {e}")
    
    def measure_operation_latency(self, operation: str, target_ms: Optional[float] = None):
        """Enhanced latency measurement with automatic optimization detection"""
        
        class EnhancedLatencyContext:
            def __init__(self, analyzer, op_name, target):
                self.analyzer = analyzer
                self.operation = op_name
                self.target_ms = target
                self.start_time = None
                
            def __enter__(self):
                self.start_time = time.perf_counter()
                return self
                
            def __exit__(self, exc_type, exc_val, exc_tb):
                end_time = time.perf_counter()
                latency_ms = (end_time - self.start_time) * 1000
                
                # Record measurement
                measurement = LatencyMeasurement(
                    operation=self.operation,
                    latency_ms=latency_ms,
                    timestamp=datetime.now(),
                    success=exc_type is None,
                    error_message=str(exc_val) if exc_val else None
                )
                
                self.analyzer.profiler.record_latency(measurement)
                
                # Check against benchmarks
                self.analyzer._check_latency_benchmark(measurement)
        
        return EnhancedLatencyContext(self, operation, target_ms)
    
    def _check_latency_benchmark(self, measurement: LatencyMeasurement):
        """Check latency measurement against benchmarks"""
        
        benchmark = self.benchmarks.get(measurement.operation)
        if not benchmark:
            return
        
        if measurement.latency_ms > benchmark.critical_ms:
            logger.critical(
                f"🔴 CRITICAL LATENCY: {measurement.operation} took {measurement.latency_ms:.2f}ms "
                f"(critical threshold: {benchmark.critical_ms}ms)"
            )
        elif measurement.latency_ms > benchmark.warning_ms:
            logger.warning(
                f"🟡 HIGH LATENCY: {measurement.operation} took {measurement.latency_ms:.2f}ms "
                f"(warning threshold: {benchmark.warning_ms}ms)"
            )
    
    def analyze_latency_patterns(self, window_minutes: int = 10) -> Dict[str, Any]:
        """Analyze latency patterns over a time window"""
        
        cutoff_time = datetime.now() - timedelta(minutes=window_minutes)
        analysis = {
            'window_minutes': window_minutes,
            'operations': {},
            'trends': {},
            'anomalies': []
        }
        
        for key, measurements in self.profiler.measurements.items():
            if not key.startswith('latency_'):
                continue
                
            operation = key[8:]  # Remove 'latency_' prefix
            recent_measurements = [
                m for m in measurements 
                if m.timestamp > cutoff_time and m.success
            ]
            
            if len(recent_measurements) < 5:
                continue
            
            latencies = [m.latency_ms for m in recent_measurements]
            
            # Calculate statistics
            mean_latency = statistics.mean(latencies)
            median_latency = statistics.median(latencies)
            std_latency = statistics.stdev(latencies) if len(latencies) > 1 else 0
            
            # Calculate percentiles
            p95_latency = np.percentile(latencies, 95) if len(latencies) >= 20 else max(latencies)
            p99_latency = np.percentile(latencies, 99) if len(latencies) >= 100 else max(latencies)
            
            analysis['operations'][operation] = {
                'count': len(latencies),
                'mean_ms': mean_latency,
                'median_ms': median_latency,
                'std_ms': std_latency,
                'min_ms': min(latencies),
                'max_ms': max(latencies),
                'p95_ms': p95_latency,
                'p99_ms': p99_latency,
                'coefficient_of_variation': std_latency / mean_latency if mean_latency > 0 else 0
            }
            
            # Detect trends
            if len(latencies) >= 10:
                # Split into first and second half to detect trends
                mid_point = len(latencies) // 2
                first_half_mean = statistics.mean(latencies[:mid_point])
                second_half_mean = statistics.mean(latencies[mid_point:])
                
                trend_change = ((second_half_mean - first_half_mean) / first_half_mean * 100) if first_half_mean > 0 else 0
                
                analysis['trends'][operation] = {
                    'trend_percentage': trend_change,
                    'direction': 'increasing' if trend_change > 5 else 'decreasing' if trend_change < -5 else 'stable'
                }
            
            # Detect anomalies (values > 2 standard deviations from mean)
            if std_latency > 0:
                threshold = mean_latency + (2 * std_latency)
                anomalous_measurements = [
                    m for m in recent_measurements 
                    if m.latency_ms > threshold
                ]
                
                if anomalous_measurements:
                    analysis['anomalies'].extend([
                        {
                            'operation': operation,
                            'latency_ms': m.latency_ms,
                            'timestamp': m.timestamp.isoformat(),
                            'deviation_factor': (m.latency_ms - mean_latency) / std_latency
                        }
                        for m in anomalous_measurements
                    ])
        
        return analysis
    
    def generate_optimization_recommendations(self, analysis: Dict[str, Any]) -> List[LatencyOptimization]:
        """Generate latency optimization recommendations based on analysis"""
        
        recommendations = []
        
        for operation, stats in analysis['operations'].items():
            benchmark = self.benchmarks.get(operation)
            if not benchmark:
                continue
            
            current_latency = stats['mean_ms']
            
            # Skip if already within target
            if current_latency <= benchmark.target_ms:
                continue
            
            # Determine optimization priority
            if current_latency > benchmark.critical_ms:
                priority = 1  # Critical
                effort = 'high'
            elif current_latency > benchmark.warning_ms:
                priority = 2  # High
                effort = 'medium'
            elif current_latency > benchmark.target_ms * 1.5:
                priority = 3  # Medium
                effort = 'low'
            else:
                priority = 4  # Low
                effort = 'low'
            
            # Estimate potential improvement
            if stats['coefficient_of_variation'] > 0.5:
                # High variability suggests optimization potential
                estimated_improvement = (current_latency - benchmark.target_ms) * 0.7
                optimization_type = 'variability_reduction'
                description = f"High latency variability detected (CV: {stats['coefficient_of_variation']:.2f}). Focus on consistency optimization."
            elif stats['p95_ms'] > current_latency * 2:
                # High tail latencies
                estimated_improvement = (stats['p95_ms'] - benchmark.target_ms) * 0.5
                optimization_type = 'tail_latency_optimization'
                description = f"High tail latencies detected (P95: {stats['p95_ms']:.2f}ms). Focus on worst-case optimization."
            else:
                # General optimization
                estimated_improvement = (current_latency - benchmark.target_ms) * 0.6
                optimization_type = 'general_optimization'
                description = f"General latency optimization needed. Current: {current_latency:.2f}ms, Target: {benchmark.target_ms:.2f}ms."
            
            recommendation = LatencyOptimization(
                operation=operation,
                current_latency_ms=current_latency,
                target_latency_ms=benchmark.target_ms,
                optimization_type=optimization_type,
                description=description,
                implementation_priority=priority,
                estimated_improvement_ms=estimated_improvement,
                implementation_effort=effort
            )
            
            recommendations.append(recommendation)
        
        # Sort by priority and potential impact
        recommendations.sort(
            key=lambda x: (x.implementation_priority, -x.estimated_improvement_ms)
        )
        
        return recommendations
    
    async def benchmark_operation(self, operation: str, func: Callable, iterations: int = 100, 
                          warmup_iterations: int = 10) -> Dict[str, Any]:
        """Benchmark a specific operation with detailed analysis"""
        
        logger.info(f"🏃 Benchmarking operation: {operation} ({iterations} iterations)")
        
        # Warmup
        for _ in range(warmup_iterations):
            try:
                if asyncio.iscoroutinefunction(func):
                    await func()
                else:
                    func()
            except Exception:
                pass  # Ignore warmup errors
        
        # Actual benchmarking
        latencies = []
        errors = 0
        
        for i in range(iterations):
            start_time = time.perf_counter()
            try:
                if asyncio.iscoroutinefunction(func):
                    await func()
                else:
                    func()
                success = True
            except Exception as e:
                success = False
                errors += 1
                logger.debug(f"Benchmark error in iteration {i}: {e}")
            
            end_time = time.perf_counter()
            latency_ms = (end_time - start_time) * 1000
            
            if success:
                latencies.append(latency_ms)
        
        if not latencies:
            return {
                'operation': operation,
                'error': 'No successful iterations',
                'total_errors': errors
            }
        
        # Calculate comprehensive statistics
        results = {
            'operation': operation,
            'iterations': len(latencies),
            'errors': errors,
            'success_rate': len(latencies) / iterations,
            'statistics': {
                'mean_ms': statistics.mean(latencies),
                'median_ms': statistics.median(latencies),
                'std_ms': statistics.stdev(latencies) if len(latencies) > 1 else 0,
                'min_ms': min(latencies),
                'max_ms': max(latencies),
                'p90_ms': np.percentile(latencies, 90),
                'p95_ms': np.percentile(latencies, 95),
                'p99_ms': np.percentile(latencies, 99),
                'coefficient_of_variation': statistics.stdev(latencies) / statistics.mean(latencies) if len(latencies) > 1 and statistics.mean(latencies) > 0 else 0
            },
            'benchmark_time': datetime.now().isoformat()
        }
        
        # Compare against benchmark if available
        benchmark = self.benchmarks.get(operation)
        if benchmark:
            results['benchmark_comparison'] = {
                'target_ms': benchmark.target_ms,
                'warning_ms': benchmark.warning_ms,
                'critical_ms': benchmark.critical_ms,
                'performance_ratio': results['statistics']['mean_ms'] / benchmark.target_ms,
                'meets_target': results['statistics']['mean_ms'] <= benchmark.target_ms,
                'meets_warning': results['statistics']['mean_ms'] <= benchmark.warning_ms
            }
        
        return results
    
    async def run_comprehensive_latency_tests(self) -> Dict[str, Any]:
        """Run comprehensive latency testing suite"""
        
        logger.info("🧪 Running comprehensive latency tests...")
        
        results = {
            'test_suite': 'Comprehensive Latency Tests',
            'started_at': datetime.now().isoformat(),
            'tests': {}
        }
        
        # Test data transformation latency
        from data_transformer import DataTransformer
        transformer = DataTransformer()
        
        sample_data = {
            'symbol': 'EURUSD',
            'timeframe': '1m',
            'timestamp': datetime.now(),
            'open': 1.1000,
            'high': 1.1005,
            'low': 1.0995,
            'close': 1.1002,
            'volume': 1000,
            'source': 'mt5'
        }
        
        def transform_data():
            return transformer.transform_market_data(sample_data)
        
        results['tests']['data_transformation'] = self.benchmark_operation(
            'price_processing', transform_data, iterations=1000
        )
        
        # Test database operations if available
        from database import get_db_manager
        db_manager = get_db_manager()
        
        if hasattr(db_manager, 'connection_pool') and db_manager.connection_pool:
            async def test_db_query():
                async with db_manager.connection_pool.acquire() as conn:
                    await conn.fetchval("SELECT 1")
            
            results['tests']['database_query'] = await self.benchmark_operation(
                'database_query', test_db_query, iterations=100
            )
        
        # Test JSON serialization/deserialization
        test_object = {
            'data': [sample_data] * 100,
            'metadata': {'test': True, 'timestamp': datetime.now().isoformat()},
            'large_field': 'x' * 1000
        }
        
        def test_json_ops():
            serialized = json.dumps(test_object, default=str)
            return json.loads(serialized)
        
        results['tests']['json_serialization'] = self.benchmark_operation(
            'api_response', test_json_ops, iterations=500
        )
        
        results['completed_at'] = datetime.now().isoformat()
        
        # Generate optimization recommendations
        analysis = self.analyze_latency_patterns()
        optimizations = self.generate_optimization_recommendations(analysis)
        
        results['optimization_recommendations'] = [
            {
                'operation': opt.operation,
                'current_latency_ms': opt.current_latency_ms,
                'target_latency_ms': opt.target_latency_ms,
                'optimization_type': opt.optimization_type,
                'description': opt.description,
                'priority': opt.implementation_priority,
                'estimated_improvement_ms': opt.estimated_improvement_ms,
                'effort': opt.implementation_effort
            }
            for opt in optimizations[:5]  # Top 5 recommendations
        ]
        
        return results
    
    def export_latency_report(self, format_type: str = 'json') -> str:
        """Export comprehensive latency analysis report"""
        
        analysis = self.analyze_latency_patterns(window_minutes=60)
        optimizations = self.generate_optimization_recommendations(analysis)
        
        report = {
            'report_type': 'Latency Analysis Report',
            'generated_at': datetime.now().isoformat(),
            'analysis_window_minutes': 60,
            'summary': {
                'total_operations_analyzed': len(analysis['operations']),
                'optimization_recommendations': len(optimizations),
                'critical_issues': len([opt for opt in optimizations if opt.implementation_priority == 1]),
                'anomalies_detected': len(analysis['anomalies'])
            },
            'operation_analysis': analysis['operations'],
            'trends': analysis['trends'],
            'anomalies': analysis['anomalies'],
            'optimization_recommendations': [
                {
                    'operation': opt.operation,
                    'current_latency_ms': opt.current_latency_ms,
                    'target_latency_ms': opt.target_latency_ms,
                    'optimization_type': opt.optimization_type,
                    'description': opt.description,
                    'priority': opt.implementation_priority,
                    'estimated_improvement_ms': opt.estimated_improvement_ms,
                    'effort': opt.implementation_effort
                }
                for opt in optimizations
            ],
            'benchmarks': {
                name: {
                    'target_ms': benchmark.target_ms,
                    'warning_ms': benchmark.warning_ms,
                    'critical_ms': benchmark.critical_ms,
                    'description': benchmark.description,
                    'optimization_hints': benchmark.optimization_hints
                }
                for name, benchmark in self.benchmarks.items()
            }
        }
        
        if format_type == 'json':
            return json.dumps(report, indent=2, default=str)
        else:
            raise ValueError(f"Unsupported format: {format_type}")

# Global instance
_latency_analyzer: Optional[LatencyAnalyzer] = None

def get_latency_analyzer() -> LatencyAnalyzer:
    """Get global latency analyzer instance"""
    global _latency_analyzer
    if _latency_analyzer is None:
        _latency_analyzer = LatencyAnalyzer()
    return _latency_analyzer