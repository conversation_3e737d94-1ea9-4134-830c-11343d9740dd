/**
 * Trade Status Tracker with Real-time WebSocket Integration
 * 
 * Provides real-time trade execution status updates, error reporting,
 * and comprehensive trade lifecycle state management via WebSocket.
 * 
 * Implements Task 2 from Story 4.4: Trade Execution and Monitoring
 */

import { EventEmitter } from 'events';
import { WebSocket, WebSocketServer } from 'ws';
import { IncomingMessage } from 'http';
import { PrismaClient } from '@prisma/client';
import Decimal from 'decimal.js';
import * as jwt from 'jsonwebtoken';
import {
  ExecutionStatus,
  ExecutionErrorCategory
} from '@golddaddy/types';
import type {
  TradeExecution,
  TradeExecutionEvent,
  Position,
  ExecutionError
} from '@golddaddy/types';

// WebSocket Message Types for Trade Status
export interface TradeStatusMessage {
  type: 'trade_status_update' | 'position_update' | 'execution_error' | 'notification';
  tradeId: string;
  userId: string;
  data: TradeStatusUpdateData | PositionUpdateData | ExecutionErrorData | NotificationData;
  timestamp: Date;
  priority: 'low' | 'normal' | 'high' | 'critical';
}

export interface TradeStatusUpdateData {
  status: ExecutionStatus;
  executionId: string;
  brokerId: string;
  brokerOrderId?: string;
  executedPrice?: Decimal.Instance;
  slippage?: Decimal.Instance;
  latency?: number;
  quality?: {
    score: number;
    slippageScore: number;
    latencyScore: number;
    fillRateScore: number;
  };
  previousStatus?: ExecutionStatus;
  statusChangeReason?: string;
}

export interface PositionUpdateData {
  positionId: string;
  instrument: string;
  size: Decimal.Instance;
  averageEntryPrice: Decimal.Instance;
  currentPrice: Decimal.Instance;
  unrealizedPnl: Decimal.Instance;
  pnlPercentage: Decimal.Instance;
  riskMetrics: {
    exposure: Decimal.Instance;
    riskPercentage: Decimal.Instance;
    currentDrawdown: Decimal.Instance;
  };
}

export interface ExecutionErrorData {
  errorCode: string;
  errorMessage: string;
  errorCategory: ExecutionErrorCategory;
  brokerId: string;
  retryable: boolean;
  retryCount: number;
  maxRetries: number;
  nextRetryAt?: Date;
  recoveryActions?: string[];
}

export interface NotificationData {
  title: string;
  message: string;
  category: 'execution' | 'risk' | 'system' | 'performance';
  actionRequired: boolean;
  actions?: Array<{
    label: string;
    action: string;
    data: Record<string, unknown>;
  }>;
}

// Client Subscription Management
export interface TradeStatusSubscription {
  type: 'all_trades' | 'specific_trades' | 'strategy_trades' | 'goal_trades';
  filters: {
    tradeIds?: string[];
    strategyIds?: string[];
    goalIds?: string[];
    instruments?: string[];
    statuses?: ExecutionStatus[];
  };
  features: {
    realTimeUpdates: boolean;
    positionTracking: boolean;
    errorReporting: boolean;
    notifications: boolean;
    performance: boolean;
  };
}

export interface TradeStatusClient {
  ws: WebSocket;
  clientId: string;
  userId: string;
  subscriptions: TradeStatusSubscription[];
  permissions: string[];
  lastHeartbeat: Date;
  connectionTime: Date;
  messagesSent: number;
  messagesReceived: number;
  priorityQueue: TradeStatusMessage[];
}

// Trade Lifecycle State Management
export enum TradeLifecycleState {
  CREATED = 'CREATED',
  VALIDATED = 'VALIDATED',
  QUEUED = 'QUEUED',
  PENDING_EXECUTION = 'PENDING_EXECUTION',
  EXECUTING = 'EXECUTING',
  PARTIALLY_FILLED = 'PARTIALLY_FILLED',
  FILLED = 'FILLED',
  CANCELLED = 'CANCELLED',
  REJECTED = 'REJECTED',
  FAILED = 'FAILED',
  CLOSED = 'CLOSED',
  ERROR = 'ERROR'
}

export interface TradeLifecycleRecord {
  tradeId: string;
  executionId: string;
  currentState: TradeLifecycleState;
  previousState?: TradeLifecycleState;
  stateChangeTime: Date;
  stateChangeReason?: string;
  metadata: Record<string, unknown>;
  auditTrailId?: string;
}

export interface TradeStatusTrackerConfig {
  websocketPort: number;
  jwtSecret: string;
  maxClients: number;
  heartbeatInterval: number;
  clientTimeout: number;
  messageQueueSize: number;
  notificationRetention: number; // Hours
  enableAudioAlerts: boolean;
  enableDesktopNotifications: boolean;
}

/**
 * Trade Status Tracker with Real-time WebSocket Communication
 */
export class TradeStatusTracker extends EventEmitter {
  private wss: WebSocketServer | null = null;
  private clients: Map<string, TradeStatusClient> = new Map();
  private tradeStates: Map<string, TradeLifecycleRecord> = new Map();
  private notificationQueue: Map<string, TradeStatusMessage[]> = new Map();
  private config: TradeStatusTrackerConfig;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isShutdown = false;

  constructor(
    private prisma: PrismaClient,
    config: Partial<TradeStatusTrackerConfig> = {}
  ) {
    super();
    
    this.config = {
      websocketPort: 3003,
      jwtSecret: process.env.JWT_SECRET || 'default-secret',
      maxClients: 1000,
      heartbeatInterval: 30000, // 30 seconds
      clientTimeout: 60000, // 60 seconds
      messageQueueSize: 100,
      notificationRetention: 24, // 24 hours
      enableAudioAlerts: true,
      enableDesktopNotifications: true,
      ...config
    };
  }

  /**
   * Initialize the trade status tracker
   */
  async initialize(): Promise<void> {
    if (this.wss) {
      return;
    }

    console.log('🚀 Initializing Trade Status Tracker...');

    // Initialize WebSocket server
    this.wss = new WebSocketServer({
      port: this.config.websocketPort,
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.wss.on('error', this.handleServerError.bind(this));

    // Start heartbeat
    this.startHeartbeat();

    // Load active trade states from database
    await this.loadActiveTradeStates();

    console.log(`✅ Trade Status Tracker initialized on port ${this.config.websocketPort}`);
    this.emit('initialized');
  }

  /**
   * Track trade status update
   */
  async trackTradeStatusUpdate(
    tradeId: string,
    executionId: string,
    status: ExecutionStatus,
    data: Partial<TradeStatusUpdateData> = {}
  ): Promise<void> {
    try {
      // Update trade lifecycle state
      const lifecycleState = this.mapExecutionStatusToLifecycleState(status);
      await this.updateTradeLifecycleState(tradeId, executionId, lifecycleState, data.statusChangeReason);

      // Get trade execution details
      const execution = await this.prisma.tradeExecution.findUnique({
        where: { id: executionId },
        include: {
          trade: {
            include: {
              user: true
            }
          }
        }
      });

      if (!execution || !execution.trade) {
        console.error(`Trade execution not found: ${executionId}`);
        return;
      }

      const userId = execution.trade.userId;

      // Create status update message
      const statusUpdate: TradeStatusMessage = {
        type: 'trade_status_update',
        tradeId,
        userId,
        data: {
          status,
          executionId,
          brokerId: execution.brokerId,
          brokerOrderId: execution.brokerOrderId,
          executedPrice: execution.executedPrice,
          slippage: execution.slippage,
          latency: execution.latency,
          quality: execution.quality,
          previousStatus: data.previousStatus,
          statusChangeReason: data.statusChangeReason,
          ...data
        } as TradeStatusUpdateData,
        timestamp: new Date(),
        priority: this.determinePriority(status)
      };

      // Broadcast to subscribed clients
      await this.broadcastToSubscribers(statusUpdate);

      // Store notification if critical
      if (statusUpdate.priority === 'critical') {
        await this.storeNotification(userId, statusUpdate);
      }

      console.log(`📊 Trade status tracked: ${tradeId} -> ${status}`);
      this.emit('tradeStatusTracked', statusUpdate);

    } catch (error) {
      console.error('Failed to track trade status update:', error);
      this.emit('error', error);
    }
  }

  /**
   * Track position update
   */
  async trackPositionUpdate(positionData: PositionUpdateData, userId: string): Promise<void> {
    try {
      const positionUpdate: TradeStatusMessage = {
        type: 'position_update',
        tradeId: '', // Position updates don't necessarily have a specific trade ID
        userId,
        data: positionData,
        timestamp: new Date(),
        priority: this.determinePositionUpdatePriority(positionData)
      };

      await this.broadcastToSubscribers(positionUpdate);
      this.emit('positionTracked', positionUpdate);

    } catch (error) {
      console.error('Failed to track position update:', error);
      this.emit('error', error);
    }
  }

  /**
   * Track execution error
   */
  async trackExecutionError(
    tradeId: string,
    executionId: string,
    error: ExecutionError,
    userId: string
  ): Promise<void> {
    try {
      // Update trade lifecycle to error state
      await this.updateTradeLifecycleState(
        tradeId,
        executionId,
        TradeLifecycleState.ERROR,
        `Execution error: ${error.message}`
      );

      const errorMessage: TradeStatusMessage = {
        type: 'execution_error',
        tradeId,
        userId,
        data: {
          errorCode: error.code,
          errorMessage: error.message,
          errorCategory: error.category,
          brokerId: error.brokerId,
          retryable: error.retryable,
          retryCount: 0, // Would be tracked separately
          maxRetries: 3, // From configuration
          recoveryActions: this.generateRecoveryActions(error)
        } as ExecutionErrorData,
        timestamp: new Date(),
        priority: 'critical'
      };

      await this.broadcastToSubscribers(errorMessage);
      await this.storeNotification(userId, errorMessage);

      console.log(`❌ Execution error tracked: ${tradeId} - ${error.code}`);
      this.emit('executionErrorTracked', errorMessage);

    } catch (err) {
      console.error('Failed to track execution error:', err);
      this.emit('error', err);
    }
  }

  /**
   * Send notification to user
   */
  async sendNotification(
    userId: string,
    notification: NotificationData,
    priority: TradeStatusMessage['priority'] = 'normal'
  ): Promise<void> {
    try {
      const notificationMessage: TradeStatusMessage = {
        type: 'notification',
        tradeId: '',
        userId,
        data: notification,
        timestamp: new Date(),
        priority
      };

      await this.broadcastToSubscribers(notificationMessage);
      
      if (priority === 'critical' || priority === 'high') {
        await this.storeNotification(userId, notificationMessage);
      }

      this.emit('notificationSent', notificationMessage);

    } catch (error) {
      console.error('Failed to send notification:', error);
      this.emit('error', error);
    }
  }

  /**
   * Get trade lifecycle history
   */
  async getTradeLifecycleHistory(tradeId: string): Promise<TradeLifecycleRecord[]> {
    try {
      const records = await this.prisma.tradeLifecycleRecord.findMany({
        where: { tradeId },
        orderBy: { stateChangeTime: 'asc' }
      });

      return records.map(record => ({
        tradeId: record.tradeId,
        executionId: record.executionId,
        currentState: record.currentState as TradeLifecycleState,
        previousState: record.previousState as TradeLifecycleState,
        stateChangeTime: record.stateChangeTime,
        stateChangeReason: record.stateChangeReason,
        metadata: record.metadata as Record<string, unknown>,
        auditTrailId: record.auditTrailId
      }));

    } catch (error) {
      console.error('Failed to get trade lifecycle history:', error);
      return [];
    }
  }

  /**
   * Get active trade states
   */
  getActiveTradeStates(): Map<string, TradeLifecycleRecord> {
    return new Map(this.tradeStates);
  }

  /**
   * Shutdown the tracker
   */
  async shutdown(): Promise<void> {
    if (this.isShutdown) {
      return;
    }

    console.log('⏹️ Shutting down Trade Status Tracker...');
    this.isShutdown = true;

    // Stop heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    // Close all client connections
    this.clients.forEach(client => {
      if (client.ws.readyState === WebSocket.OPEN) {
        client.ws.close(1001, 'Server shutting down');
      }
    });
    this.clients.clear();

    // Close WebSocket server
    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }

    console.log('✅ Trade Status Tracker shutdown complete');
    this.emit('shutdown');
  }

  // === Private Helper Methods ===

  private verifyClient(info: { req: IncomingMessage }): boolean {
    try {
      // Check client limit
      if (this.clients.size >= this.config.maxClients) {
        return false;
      }

      // Extract JWT token
      const url = new URL(info.req.url || '', 'ws://localhost');
      const token = url.searchParams.get('token') || 
                   info.req.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        return false;
      }

      // Verify JWT token
      const decoded = jwt.verify(token, this.config.jwtSecret) as any;
      if (!decoded.sub) {
        return false;
      }

      // Store decoded token for connection handler
      (info.req as any).decodedToken = decoded;
      return true;

    } catch (error) {
      console.error('Trade status WebSocket verification failed:', error);
      return false;
    }
  }

  private handleConnection(ws: WebSocket, req: IncomingMessage): void {
    const decodedToken = (req as any).decodedToken;
    const userId = decodedToken.sub;
    const permissions = decodedToken.permissions || [];
    const clientId = this.generateClientId(userId);

    const client: TradeStatusClient = {
      ws,
      clientId,
      userId,
      subscriptions: [],
      permissions,
      lastHeartbeat: new Date(),
      connectionTime: new Date(),
      messagesSent: 0,
      messagesReceived: 0,
      priorityQueue: []
    };

    this.clients.set(clientId, client);

    // Send connection acknowledgment
    this.sendToClient(clientId, {
      type: 'notification',
      tradeId: '',
      userId,
      data: {
        title: 'Connection Established',
        message: 'Trade status tracking connected',
        category: 'system',
        actionRequired: false
      } as NotificationData,
      timestamp: new Date(),
      priority: 'low'
    });

    // Handle messages
    ws.on('message', (data: Buffer) => this.handleClientMessage(clientId, data));
    ws.on('close', () => this.handleClientDisconnect(clientId));
    ws.on('error', (error) => this.handleClientError(clientId, error));

    console.log(`📡 Trade status client connected: ${clientId} (${userId})`);
    this.emit('clientConnected', { clientId, userId });
  }

  private handleClientMessage(clientId: string, data: Buffer): void {
    try {
      const client = this.clients.get(clientId);
      if (!client) return;

      const message = JSON.parse(data.toString());
      client.messagesReceived++;

      switch (message.type) {
        case 'subscribe':
          this.handleSubscription(clientId, message.subscription);
          break;
        case 'unsubscribe':
          this.handleUnsubscription(clientId, message.subscription);
          break;
        case 'heartbeat':
          this.handleHeartbeat(clientId);
          break;
        case 'action':
          this.handleClientAction(clientId, message.action, message.data);
          break;
        default:
          this.sendError(clientId, 'UNKNOWN_MESSAGE_TYPE', `Unknown message type: ${message.type}`);
      }

    } catch (error) {
      this.sendError(clientId, 'INVALID_MESSAGE_FORMAT', 'Invalid JSON message format');
    }
  }

  private handleSubscription(clientId: string, subscription: TradeStatusSubscription): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    client.subscriptions.push(subscription);
    
    this.sendToClient(clientId, {
      type: 'notification',
      tradeId: '',
      userId: client.userId,
      data: {
        title: 'Subscription Confirmed',
        message: `Subscribed to ${subscription.type} trade updates`,
        category: 'system',
        actionRequired: false
      } as NotificationData,
      timestamp: new Date(),
      priority: 'low'
    });

    console.log(`📝 Client subscribed: ${clientId} -> ${subscription.type}`);
  }

  private handleUnsubscription(clientId: string, subscription: TradeStatusSubscription): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    client.subscriptions = client.subscriptions.filter(s => 
      JSON.stringify(s) !== JSON.stringify(subscription)
    );

    console.log(`📝 Client unsubscribed: ${clientId} -> ${subscription.type}`);
  }

  private handleHeartbeat(clientId: string): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    client.lastHeartbeat = new Date();
    this.sendToClient(clientId, {
      type: 'notification',
      tradeId: '',
      userId: client.userId,
      data: {
        title: 'Heartbeat',
        message: 'Connection healthy',
        category: 'system',
        actionRequired: false
      } as NotificationData,
      timestamp: new Date(),
      priority: 'low'
    });
  }

  private handleClientAction(clientId: string, action: string, data: any): void {
    // Handle client actions like retry failed execution, cancel trade, etc.
    console.log(`🎯 Client action received: ${clientId} -> ${action}`, data);
    this.emit('clientAction', { clientId, action, data });
  }

  private handleClientDisconnect(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      this.clients.delete(clientId);
      console.log(`📡 Trade status client disconnected: ${clientId}`);
      this.emit('clientDisconnected', { clientId, userId: client.userId });
    }
  }

  private handleClientError(clientId: string, error: Error): void {
    console.error(`Trade status client error: ${clientId}`, error);
    this.emit('clientError', { clientId, error });
  }

  private handleServerError(error: Error): void {
    console.error('Trade status WebSocket server error:', error);
    this.emit('serverError', error);
  }

  private async broadcastToSubscribers(message: TradeStatusMessage): Promise<void> {
    const relevantClients = Array.from(this.clients.values())
      .filter(client => this.isClientSubscribedToMessage(client, message));

    for (const client of relevantClients) {
      // Add to priority queue if client is busy
      if (client.priorityQueue.length > 0) {
        client.priorityQueue.push(message);
        client.priorityQueue.sort((a, b) => this.getPriorityWeight(b.priority) - this.getPriorityWeight(a.priority));
        
        // Limit queue size
        if (client.priorityQueue.length > this.config.messageQueueSize) {
          client.priorityQueue = client.priorityQueue.slice(0, this.config.messageQueueSize);
        }
      } else {
        this.sendToClient(client.clientId, message);
      }
    }
  }

  private isClientSubscribedToMessage(client: TradeStatusClient, message: TradeStatusMessage): boolean {
    // Check if message is for this user
    if (message.userId !== client.userId) {
      return false;
    }

    // Check if client has relevant subscriptions
    return client.subscriptions.some(sub => {
      switch (sub.type) {
        case 'all_trades':
          return true;
        case 'specific_trades':
          return sub.filters.tradeIds?.includes(message.tradeId) || false;
        default:
          return true; // For now, allow all message types
      }
    });
  }

  private sendToClient(clientId: string, message: TradeStatusMessage): void {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      client.ws.send(JSON.stringify(message));
      client.messagesSent++;
    } catch (error) {
      this.handleClientError(clientId, error as Error);
    }
  }

  private sendError(clientId: string, code: string, message: string): void {
    this.sendToClient(clientId, {
      type: 'notification',
      tradeId: '',
      userId: '',
      data: {
        title: 'Error',
        message: `${code}: ${message}`,
        category: 'system',
        actionRequired: false
      } as NotificationData,
      timestamp: new Date(),
      priority: 'high'
    });
  }

  private async loadActiveTradeStates(): Promise<void> {
    try {
      const activeRecords = await this.prisma.tradeLifecycleRecord.findMany({
        where: {
          currentState: {
            in: ['PENDING_EXECUTION', 'EXECUTING', 'PARTIALLY_FILLED']
          }
        }
      });

      activeRecords.forEach(record => {
        this.tradeStates.set(record.tradeId, {
          tradeId: record.tradeId,
          executionId: record.executionId,
          currentState: record.currentState as TradeLifecycleState,
          previousState: record.previousState as TradeLifecycleState,
          stateChangeTime: record.stateChangeTime,
          stateChangeReason: record.stateChangeReason,
          metadata: record.metadata as Record<string, unknown>,
          auditTrailId: record.auditTrailId
        });
      });

      console.log(`📊 Loaded ${activeRecords.length} active trade states`);
    } catch (error) {
      console.error('Failed to load active trade states:', error);
    }
  }

  private async updateTradeLifecycleState(
    tradeId: string,
    executionId: string,
    newState: TradeLifecycleState,
    reason?: string
  ): Promise<void> {
    try {
      const currentRecord = this.tradeStates.get(tradeId);
      const previousState = currentRecord?.currentState;

      const lifecycleRecord: TradeLifecycleRecord = {
        tradeId,
        executionId,
        currentState: newState,
        previousState,
        stateChangeTime: new Date(),
        stateChangeReason: reason,
        metadata: currentRecord?.metadata || {}
      };

      // Update in-memory state
      this.tradeStates.set(tradeId, lifecycleRecord);

      // Persist to database
      await this.prisma.tradeLifecycleRecord.create({
        data: {
          tradeId: lifecycleRecord.tradeId,
          executionId: lifecycleRecord.executionId,
          currentState: lifecycleRecord.currentState,
          previousState: lifecycleRecord.previousState,
          stateChangeTime: lifecycleRecord.stateChangeTime,
          stateChangeReason: lifecycleRecord.stateChangeReason,
          metadata: lifecycleRecord.metadata,
          auditTrailId: lifecycleRecord.auditTrailId
        }
      });

    } catch (error) {
      console.error('Failed to update trade lifecycle state:', error);
    }
  }

  private async storeNotification(userId: string, message: TradeStatusMessage): Promise<void> {
    try {
      let notifications = this.notificationQueue.get(userId) || [];
      notifications.push(message);

      // Keep only recent notifications (based on retention policy)
      const retentionTime = Date.now() - (this.config.notificationRetention * 60 * 60 * 1000);
      notifications = notifications.filter(n => n.timestamp.getTime() > retentionTime);

      this.notificationQueue.set(userId, notifications);
    } catch (error) {
      console.error('Failed to store notification:', error);
    }
  }

  private mapExecutionStatusToLifecycleState(status: ExecutionStatus): TradeLifecycleState {
    const mapping: Record<ExecutionStatus, TradeLifecycleState> = {
      [ExecutionStatus.PENDING]: TradeLifecycleState.PENDING_EXECUTION,
      [ExecutionStatus.EXECUTING]: TradeLifecycleState.EXECUTING,
      [ExecutionStatus.FILLED]: TradeLifecycleState.FILLED,
      [ExecutionStatus.PARTIALLY_FILLED]: TradeLifecycleState.PARTIALLY_FILLED,
      [ExecutionStatus.CANCELLED]: TradeLifecycleState.CANCELLED,
      [ExecutionStatus.REJECTED]: TradeLifecycleState.REJECTED,
      [ExecutionStatus.FAILED]: TradeLifecycleState.FAILED
    };

    return mapping[status] || TradeLifecycleState.ERROR;
  }

  private determinePriority(status: ExecutionStatus): TradeStatusMessage['priority'] {
    const priorityMap: Record<ExecutionStatus, TradeStatusMessage['priority']> = {
      [ExecutionStatus.PENDING]: 'normal',
      [ExecutionStatus.EXECUTING]: 'normal',
      [ExecutionStatus.FILLED]: 'high',
      [ExecutionStatus.PARTIALLY_FILLED]: 'normal',
      [ExecutionStatus.CANCELLED]: 'normal',
      [ExecutionStatus.REJECTED]: 'critical',
      [ExecutionStatus.FAILED]: 'critical'
    };

    return priorityMap[status] || 'normal';
  }

  private determinePositionUpdatePriority(data: PositionUpdateData): TradeStatusMessage['priority'] {
    // High priority if significant drawdown
    if (data.riskMetrics.currentDrawdown.gte(0.05)) { // 5% drawdown
      return 'high';
    }
    
    // High priority if large unrealized loss
    if (data.unrealizedPnl.lt(0) && data.unrealizedPnl.abs().gte(1000)) {
      return 'high';
    }

    return 'normal';
  }

  private generateRecoveryActions(error: ExecutionError): string[] {
    const actions: string[] = [];

    switch (error.category) {
      case ExecutionErrorCategory.NETWORK_ERROR:
        actions.push('Check network connectivity', 'Retry execution', 'Switch to backup broker');
        break;
      case ExecutionErrorCategory.BROKER_ERROR:
        actions.push('Contact broker support', 'Switch to alternative broker', 'Check account status');
        break;
      case ExecutionErrorCategory.INSUFFICIENT_MARGIN:
        actions.push('Add funds to account', 'Reduce position size', 'Close existing positions');
        break;
      case ExecutionErrorCategory.MARKET_CLOSED:
        actions.push('Wait for market open', 'Check trading hours', 'Consider alternative markets');
        break;
      default:
        actions.push('Contact support', 'Review error details', 'Check system status');
    }

    return actions;
  }

  private getPriorityWeight(priority: TradeStatusMessage['priority']): number {
    const weights = { low: 1, normal: 2, high: 3, critical: 4 };
    return weights[priority] || 2;
  }

  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      if (this.isShutdown) return;

      const now = new Date();
      const staleClients: string[] = [];

      this.clients.forEach((client, clientId) => {
        const timeSinceLastHeartbeat = now.getTime() - client.lastHeartbeat.getTime();
        
        if (timeSinceLastHeartbeat > this.config.clientTimeout) {
          staleClients.push(clientId);
        }
      });

      // Remove stale clients
      staleClients.forEach(clientId => {
        const client = this.clients.get(clientId);
        if (client) {
          client.ws.terminate();
          this.handleClientDisconnect(clientId);
        }
      });

    }, this.config.heartbeatInterval);
  }

  private generateClientId(userId: string): string {
    return `trade_status_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}