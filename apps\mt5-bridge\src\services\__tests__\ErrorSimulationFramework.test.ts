import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  ErrorSimulationFramework, 
  type ErrorSimulationConfig, 
  type ErrorScenario,
  ErrorType 
} from '../ErrorSimulationFramework';

describe('ErrorSimulationFramework', () => {
  let framework: ErrorSimulationFramework;
  let mockConfig: Partial<ErrorSimulationConfig>;

  beforeEach(() => {
    mockConfig = {
      enabled: true,
      globalErrorRate: 0.1, // Higher for testing
      networkErrorRate: 0.05,
      brokerErrorRate: 0.03,
      systemErrorRate: 0.02,
      timeoutRate: 0.04,
      errorBurstEnabled: false, // Disabled for predictable testing
      burstIntensity: 0.3,
      burstDuration: 1000,
      errorRecoveryTime: 100,
      customErrorScenarios: []
    };

    framework = new ErrorSimulationFramework(mockConfig);
    
    // Add error event handler to prevent unhandled errors in tests
    framework.on('error', () => {
      // Expected error events during testing
    });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should initialize with default configuration', () => {
      const defaultFramework = new ErrorSimulationFramework();
      expect(defaultFramework).toBeDefined();
    });

    it('should apply custom configuration', () => {
      const config = framework.getConfig();
      expect(config.enabled).toBe(true);
      expect(config.globalErrorRate).toBe(0.1);
      expect(config.errorBurstEnabled).toBe(false);
    });

    it('should initialize with default error scenarios', () => {
      const stats = framework.getStatistics();
      expect(stats).toBeDefined();
      expect(stats.totalErrors).toBe(0);
    });
  });

  describe('Error Simulation', () => {
    it('should not simulate errors when disabled', () => {
      framework.setEnabled(false);
      
      // Try many operations - none should generate errors
      for (let i = 0; i < 100; i++) {
        const error = framework.shouldSimulateError('test_operation', {});
        expect(error).toBeNull();
      }
    });

    it('should simulate errors when enabled', () => {
      // Configure high error rate for testing
      framework.configure({ globalErrorRate: 1.0 }); // 100% error rate
      
      const error = framework.shouldSimulateError('test_operation', {});
      expect(error).not.toBeNull();
      expect(error!.type).toBeDefined();
      expect(error!.code).toBeGreaterThan(0);
      expect(error!.message).toBeDefined();
    });

    it('should generate different error types', () => {
      framework.configure({ 
        globalErrorRate: 1.0,
        networkErrorRate: 0.4,
        brokerErrorRate: 0.3,
        systemErrorRate: 0.2,
        timeoutRate: 0.1
      });

      const errors = [];
      for (let i = 0; i < 20; i++) {
        const error = framework.shouldSimulateError('test_operation', {});
        if (error) {
          errors.push(error.type);
        }
      }

      // Should have multiple error types
      const uniqueTypes = new Set(errors);
      expect(uniqueTypes.size).toBeGreaterThan(1);
    });

    it('should include context in error objects', () => {
      framework.configure({ globalErrorRate: 1.0 });
      
      const context = {
        symbol: 'EURUSD',
        volume: 1.0,
        operation: 'order_send'
      };

      const error = framework.shouldSimulateError('order_send', context);
      expect(error).not.toBeNull();
      expect(error!.context).toEqual(expect.objectContaining(context));
    });
  });

  describe('Custom Error Scenarios', () => {
    it('should add custom error scenarios', () => {
      const customScenario: ErrorScenario = {
        id: 'test_scenario',
        name: 'Test Scenario',
        description: 'A test scenario for unit testing',
        probability: 1.0, // Always trigger for testing
        duration: 1000,
        errorType: ErrorType.NETWORK,
        errorCode: 9999,
        errorMessage: 'Test error message'
      };

      framework.addErrorScenario(customScenario);
      
      const error = framework.shouldSimulateError('test_operation', {});
      expect(error).not.toBeNull();
      expect(error!.scenarioId).toBe('test_scenario');
      expect(error!.code).toBe(9999);
    });

    it('should remove custom error scenarios', () => {
      const customScenario: ErrorScenario = {
        id: 'removable_scenario',
        name: 'Removable Scenario',
        description: 'A scenario that will be removed',
        probability: 1.0,
        duration: 1000,
        errorType: ErrorType.BROKER,
        errorCode: 8888,
        errorMessage: 'Removable error'
      };

      framework.addErrorScenario(customScenario);
      expect(framework.removeErrorScenario('removable_scenario')).toBe(true);
      expect(framework.removeErrorScenario('non_existent')).toBe(false);
    });

    it('should handle scenario conditions correctly', () => {
      const timeBasedScenario: ErrorScenario = {
        id: 'time_based',
        name: 'Time Based Error',
        description: 'Error that occurs during specific times',
        probability: 1.0,
        duration: 1000,
        errorType: ErrorType.MARKET_CLOSED,
        errorCode: 10018,
        errorMessage: 'Market is closed',
        conditions: [
          {
            type: 'time',
            operator: 'between',
            value: '22:00',
            value2: '06:00'
          }
        ]
      };

      framework.addErrorScenario(timeBasedScenario);

      // Use fake timers to mock current time to be during market closed hours (23:00)
      vi.useFakeTimers();
      vi.setSystemTime(new Date('2023-01-01T23:00:00Z'));

      const error = framework.shouldSimulateError('test_operation', {});
      
      // Restore real timers
      vi.useRealTimers();

      // Should trigger market closed error during specified hours
      expect(error?.errorType).toBe(ErrorType.MARKET_CLOSED);
    });

    it('should handle volume-based conditions', () => {
      const volumeBasedScenario: ErrorScenario = {
        id: 'large_volume',
        name: 'Large Volume Error',
        description: 'Error for large volume orders',
        probability: 1.0,
        duration: 0,
        errorType: ErrorType.INSUFFICIENT_FUNDS,
        errorCode: 10019,
        errorMessage: 'Insufficient funds for large order',
        conditions: [
          {
            type: 'volume',
            operator: 'greater',
            value: 10.0
          }
        ]
      };

      framework.addErrorScenario(volumeBasedScenario);

      // Test with large volume
      const largeVolumeError = framework.shouldSimulateError('order_send', { volume: 15.0 });
      expect(largeVolumeError).not.toBeNull();
      expect(largeVolumeError!.errorType).toBe(ErrorType.INSUFFICIENT_FUNDS);

      // Test with small volume
      const smallVolumeError = framework.shouldSimulateError('order_send', { volume: 1.0 });
      // Should not trigger the large volume scenario
      if (smallVolumeError) {
        expect(smallVolumeError.errorType).not.toBe(ErrorType.INSUFFICIENT_FUNDS);
      }
    });
  });

  describe('Error Recovery', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should handle recovery strategies', async () => {
      const recoveryScenario: ErrorScenario = {
        id: 'recovery_test',
        name: 'Recovery Test',
        description: 'Scenario with recovery',
        probability: 1.0,
        duration: 1000,
        errorType: ErrorType.CONNECTION_LOST,
        errorCode: 4006,
        errorMessage: 'Connection lost',
        recovery: {
          type: 'immediate',
          successRate: 1.0,
          retryDelay: 100,
          maxRetries: 1
        }
      };

      framework.addErrorScenario(recoveryScenario);

      let recoveryEventReceived = false;
      framework.on('recovery', (event) => {
        recoveryEventReceived = true;
        expect(event.successful).toBe(true);
      });

      const error = framework.shouldSimulateError('test_operation', {});
      expect(error).not.toBeNull();

      // Fast-forward time to trigger recovery
      vi.advanceTimersByTime(200);

      // Run all pending timers and promises
      await vi.runAllTimersAsync();

      expect(recoveryEventReceived).toBe(true);
    });

    it('should handle failed recovery attempts', async () => {
      const failingRecoveryScenario: ErrorScenario = {
        id: 'failing_recovery',
        name: 'Failing Recovery',
        description: 'Scenario with failing recovery',
        probability: 1.0,
        duration: 1000,
        errorType: ErrorType.SYSTEM,
        errorCode: 5001,
        errorMessage: 'System error',
        recovery: {
          type: 'gradual',
          successRate: 0.0, // Always fail
          retryDelay: 50,
          maxRetries: 2
        }
      };

      framework.addErrorScenario(failingRecoveryScenario);

      let failedRecoveryReceived = false;
      framework.on('recovery', (event) => {
        if (!event.successful) {
          failedRecoveryReceived = true;
        }
      });

      const error = framework.shouldSimulateError('test_operation', {});
      expect(error).not.toBeNull();

      // Fast-forward through multiple retry attempts
      vi.advanceTimersByTime(300);

      // Run all pending timers and promises
      await vi.runAllTimersAsync();

      expect(failedRecoveryReceived).toBe(true);
    });
  });

  describe('Error Bursts', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should handle error bursts', () => {
      framework.configure({ 
        errorBurstEnabled: true,
        burstIntensity: 0.8,
        burstDuration: 1000
      });

      let burstStartReceived = false;
      let burstEndReceived = false;

      framework.on('burstStart', () => {
        burstStartReceived = true;
      });

      framework.on('burstEnd', () => {
        burstEndReceived = true;
      });

      // Manually trigger burst for testing
      (framework as any).triggerErrorBurst();

      expect(burstStartReceived).toBe(true);

      // Fast-forward past burst duration
      vi.advanceTimersByTime(1500);

      expect(burstEndReceived).toBe(true);
    });
  });

  describe('Statistics', () => {
    it('should track error statistics correctly', () => {
      framework.configure({ globalErrorRate: 1.0 });

      // Generate some errors
      for (let i = 0; i < 10; i++) {
        framework.shouldSimulateError('test_operation', {});
      }

      const stats = framework.getStatistics();
      
      expect(stats.totalErrors).toBe(10);
      expect(stats.errorRate).toBeGreaterThan(0);
      expect(stats.errorsByType).toBeDefined();
      expect(stats.errorsByCode).toBeDefined();
    });

    it('should categorize errors by type', () => {
      framework.configure({ 
        globalErrorRate: 1.0,
        networkErrorRate: 1.0,
        brokerErrorRate: 0,
        systemErrorRate: 0,
        timeoutRate: 0
      });

      // Generate network errors
      for (let i = 0; i < 5; i++) {
        const error = framework.shouldSimulateError('test_operation', {});
        expect(error?.type).toBe(ErrorType.NETWORK);
      }

      const stats = framework.getStatistics();
      expect(stats.errorsByType[ErrorType.NETWORK]).toBe(5);
    });

    it('should track error codes', () => {
      framework.configure({ globalErrorRate: 1.0 });

      const errors = [];
      for (let i = 0; i < 10; i++) {
        const error = framework.shouldSimulateError('test_operation', {});
        if (error) {
          errors.push(error.code);
        }
      }

      const stats = framework.getStatistics();
      
      // Check that error codes are tracked
      errors.forEach(code => {
        expect(stats.errorsByCode[code]).toBeGreaterThan(0);
      });
    });
  });

  describe('Events', () => {
    it('should emit error events', () => {
      framework.configure({ globalErrorRate: 1.0 });

      let errorEventReceived = false;
      framework.on('error', (errorEvent) => {
        errorEventReceived = true;
        expect(errorEvent.error).toBeDefined();
        expect(errorEvent.operation).toBe('test_operation');
      });

      framework.shouldSimulateError('test_operation', { symbol: 'EURUSD' });

      expect(errorEventReceived).toBe(true);
    });

    it('should emit scenario events', () => {
      const testScenario: ErrorScenario = {
        id: 'event_test',
        name: 'Event Test Scenario',
        description: 'Scenario for testing events',
        probability: 1.0,
        duration: 100,
        errorType: ErrorType.TIMEOUT,
        errorCode: 4014,
        errorMessage: 'Timeout occurred'
      };

      framework.addErrorScenario(testScenario);

      let scenarioStartReceived = false;
      framework.on('scenarioStart', (event) => {
        scenarioStartReceived = true;
        expect(event.scenarioId).toBe('event_test');
      });

      framework.shouldSimulateError('test_operation', {});

      expect(scenarioStartReceived).toBe(true);
    });
  });

  describe('Configuration Management', () => {
    it('should update configuration at runtime', () => {
      const newConfig = {
        globalErrorRate: 0.5,
        networkErrorRate: 0.2,
        errorBurstEnabled: true
      };

      framework.configure(newConfig);
      
      const config = framework.getConfig();
      expect(config.globalErrorRate).toBe(0.5);
      expect(config.networkErrorRate).toBe(0.2);
      expect(config.errorBurstEnabled).toBe(true);
    });

    it('should enable and disable framework', () => {
      framework.setEnabled(false);
      expect(framework.getConfig().enabled).toBe(false);

      framework.setEnabled(true);
      expect(framework.getConfig().enabled).toBe(true);
    });

    it('should reset framework state', () => {
      // Generate some errors first
      framework.configure({ globalErrorRate: 1.0 });
      for (let i = 0; i < 5; i++) {
        framework.shouldSimulateError('test_operation', {});
      }

      const initialStats = framework.getStatistics();
      expect(initialStats.totalErrors).toBe(5);

      // Reset and verify
      framework.reset();
      
      const resetStats = framework.getStatistics();
      expect(resetStats.totalErrors).toBe(0);
    });
  });

  describe('Edge Cases', () => {
    it('should handle empty context gracefully', () => {
      framework.configure({ globalErrorRate: 1.0 });
      
      const error = framework.shouldSimulateError('test_operation');
      expect(error).not.toBeNull();
      expect(error!.context).toBeDefined();
    });

    it('should handle invalid scenario conditions', () => {
      const invalidScenario: ErrorScenario = {
        id: 'invalid_condition',
        name: 'Invalid Condition Scenario',
        description: 'Scenario with invalid conditions',
        probability: 1.0,
        duration: 1000,
        errorType: ErrorType.VALIDATION,
        errorCode: 10013,
        errorMessage: 'Validation error',
        conditions: [
          {
            type: 'invalid_type' as any,
            operator: 'equals',
            value: 'test'
          }
        ]
      };

      framework.addErrorScenario(invalidScenario);

      // Should not crash with invalid condition
      expect(() => {
        framework.shouldSimulateError('test_operation', {});
      }).not.toThrow();
    });

    it('should handle scenario duration expiration', () => {
      vi.useFakeTimers();

      const timedScenario: ErrorScenario = {
        id: 'timed_scenario',
        name: 'Timed Scenario',
        description: 'Scenario that expires',
        probability: 1.0,
        duration: 1000,
        errorType: ErrorType.BROKER,
        errorCode: 4051,
        errorMessage: 'Broker error'
      };

      framework.addErrorScenario(timedScenario);

      // Trigger the scenario
      const error1 = framework.shouldSimulateError('test_operation', {});
      expect(error1?.scenarioId).toBe('timed_scenario');

      // Fast-forward past expiration
      vi.advanceTimersByTime(1500);

      // Should no longer trigger this specific scenario
      framework.configure({ globalErrorRate: 0 }); // Disable random errors
      const error2 = framework.shouldSimulateError('test_operation', {});
      expect(error2).toBeNull();

      vi.useRealTimers();
    });
  });
});