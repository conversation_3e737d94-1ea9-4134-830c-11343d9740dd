/**
 * Data Quality Monitor Service
 * 
 * Monitors data quality for market data streams, detects gaps, anomalies,
 * and provides automated repair mechanisms for minor data inconsistencies.
 */

import { EventEmitter } from 'events';
import Decimal from 'decimal.js';
import { NormalizedMarketData, DataSource, TimeFrame } from './RealTimeDataProcessor';

// Data quality issue types
export enum IssueType {
  MISSING_DATA = 'missing_data',
  PRICE_SPIKE = 'price_spike',
  VOLUME_ANOMALY = 'volume_anomaly',
  TIMESTAMP_GAP = 'timestamp_gap',
  OHLC_INCONSISTENCY = 'ohlc_inconsistency',
  DUPLICATE_DATA = 'duplicate_data',
  STALE_DATA = 'stale_data',
  SPREAD_ANOMALY = 'spread_anomaly',
}

// Severity levels for data quality issues
export enum IssueSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Data quality issue interface
export interface DataQualityIssue {
  id: string;
  type: IssueType;
  severity: IssueSeverity;
  timestamp: Date;
  instrument: string;
  source: DataSource;
  timeframe: TimeFrame;
  description: string;
  affectedData?: NormalizedMarketData;
  suggestedRepair?: RepairAction;
  metadata?: Record<string, any>;
}

// Repair action types
export enum RepairActionType {
  INTERPOLATE = 'interpolate',
  USE_FALLBACK_SOURCE = 'use_fallback_source',
  IGNORE = 'ignore',
  MANUAL_REVIEW = 'manual_review',
  AUTO_CORRECT = 'auto_correct',
}

// Repair action interface
export interface RepairAction {
  type: RepairActionType;
  description: string;
  confidence: number; // 0-100
  estimatedImpact: 'low' | 'medium' | 'high';
  automaticRepair: boolean;
  repairData?: NormalizedMarketData;
}

// Quality monitoring configuration
export interface QualityMonitorConfig {
  enableAutoRepair: boolean;
  autoRepairConfidenceThreshold: number; // 0-100
  maxPriceDeviationPercent: number; // For spike detection
  maxVolumeDeviationMultiplier: number; // For volume anomaly detection
  maxTimestampGapMs: number; // Maximum allowed gap between data points
  staleDataThresholdMs: number; // When to consider data stale
  maxSpreadPercent: number; // Maximum allowed spread as % of price
  enableGapDetection: boolean;
  enableAnomalyDetection: boolean;
  enableDuplicateDetection: boolean;
  fallbackSources: DataSource[]; // Ordered list of fallback sources
}

// Quality statistics
export interface QualityStats {
  totalDataPointsProcessed: number;
  totalIssuesDetected: number;
  issuesByType: Record<IssueType, number>;
  issuesBySeverity: Record<IssueSeverity, number>;
  autoRepairsPerformed: number;
  manualReviewRequired: number;
  overallQualityScore: number; // 0-100
  lastProcessedAt: Date;
  averageProcessingTimeMs: number;
}

// Historical data point for gap detection
interface HistoricalDataPoint {
  timestamp: Date;
  instrument: string;
  source: DataSource;
  timeframe: TimeFrame;
  close: Decimal.Instance;
  volume: Decimal.Instance;
}

/**
 * Data Quality Monitor for real-time market data validation and repair
 */
export class DataQualityMonitor extends EventEmitter {
  private config: QualityMonitorConfig;
  private stats: QualityStats;
  private historicalData: Map<string, HistoricalDataPoint[]> = new Map();
  private detectedIssues: DataQualityIssue[] = [];
  private repairHistory: Map<string, RepairAction[]> = new Map();

  constructor(config: QualityMonitorConfig) {
    super();
    this.config = config;
    this.stats = this.initializeStats();
  }

  /**
   * Initialize quality monitoring statistics
   */
  private initializeStats(): QualityStats {
    return {
      totalDataPointsProcessed: 0,
      totalIssuesDetected: 0,
      issuesByType: Object.values(IssueType).reduce((acc, type) => {
        acc[type] = 0;
        return acc;
      }, {} as Record<IssueType, number>),
      issuesBySeverity: Object.values(IssueSeverity).reduce((acc, severity) => {
        acc[severity] = 0;
        return acc;
      }, {} as Record<IssueSeverity, number>),
      autoRepairsPerformed: 0,
      manualReviewRequired: 0,
      overallQualityScore: 100,
      lastProcessedAt: new Date(),
      averageProcessingTimeMs: 0,
    };
  }

  /**
   * Monitor data quality for incoming market data
   */
  public async monitorDataQuality(data: NormalizedMarketData): Promise<DataQualityIssue[]> {
    const startTime = Date.now();
    const issues: DataQualityIssue[] = [];

    try {
      this.stats.totalDataPointsProcessed++;

      // Run checks that compare against historical data BEFORE storing current data point
      if (this.config.enableGapDetection) {
        const gapIssues = await this.detectTimestampGaps(data);
        issues.push(...gapIssues);
      }

      if (this.config.enableAnomalyDetection) {
        const anomalyIssues = await this.detectAnomalies(data);
        issues.push(...anomalyIssues);
      }

      if (this.config.enableDuplicateDetection) {
        const duplicateIssues = await this.detectDuplicates(data);
        issues.push(...duplicateIssues);
      }

      // Store historical data AFTER checks that need to compare against history
      this.storeHistoricalData(data);

      // Check for basic data consistency (doesn't require historical data)
      const consistencyIssues = this.checkDataConsistency(data);
      issues.push(...consistencyIssues);

      // Check for stale data (doesn't require historical data)
      const staleIssues = this.checkStaleData(data);
      issues.push(...staleIssues);

      // Process detected issues
      for (const issue of issues) {
        await this.processIssue(issue);
      }

      // Update statistics
      this.updateStats(issues, startTime);

      this.emit('quality_check_completed', {
        data,
        issues,
        processingTimeMs: Date.now() - startTime
      });

      return issues;

    } catch (error) {
      this.emit('monitoring_error', { data, error });
      throw error;
    }
  }

  /**
   * Store historical data for trend analysis and gap detection
   */
  private storeHistoricalData(data: NormalizedMarketData): void {
    const key = `${data.instrument}_${data.source}_${data.timeframe}`;
    
    if (!this.historicalData.has(key)) {
      this.historicalData.set(key, []);
    }

    const history = this.historicalData.get(key)!;
    history.push({
      timestamp: data.timestamp,
      instrument: data.instrument,
      source: data.source,
      timeframe: data.timeframe,
      close: data.close,
      volume: data.volume,
    });

    // Keep only recent history (last 100 data points)
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }
  }

  /**
   * Detect timestamp gaps in market data
   */
  private async detectTimestampGaps(data: NormalizedMarketData): Promise<DataQualityIssue[]> {
    const issues: DataQualityIssue[] = [];
    const key = `${data.instrument}_${data.source}_${data.timeframe}`;
    const history = this.historicalData.get(key);

    if (!history || history.length < 1) {
      return issues;
    }

    // Compare current data point against the last historical point
    const previousPoint = history[history.length - 1];
    const timeDiff = data.timestamp.getTime() - previousPoint.timestamp.getTime();

    if (timeDiff > this.config.maxTimestampGapMs) {
      const issue: DataQualityIssue = {
        id: `gap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: IssueType.TIMESTAMP_GAP,
        severity: timeDiff > this.config.maxTimestampGapMs * 5 ? IssueSeverity.HIGH : IssueSeverity.MEDIUM,
        timestamp: new Date(),
        instrument: data.instrument,
        source: data.source,
        timeframe: data.timeframe,
        description: `Timestamp gap of ${timeDiff}ms detected between data points`,
        affectedData: data,
        suggestedRepair: {
          type: RepairActionType.INTERPOLATE,
          description: 'Interpolate missing data points using linear interpolation',
          confidence: 75,
          estimatedImpact: 'medium',
          automaticRepair: true,
        },
        metadata: {
          gapDurationMs: timeDiff,
          previousTimestamp: previousPoint.timestamp,
          currentTimestamp: data.timestamp,
        }
      };

      issues.push(issue);
    }

    return issues;
  }

  /**
   * Detect price and volume anomalies
   */
  private async detectAnomalies(data: NormalizedMarketData): Promise<DataQualityIssue[]> {
    const issues: DataQualityIssue[] = [];
    const key = `${data.instrument}_${data.source}_${data.timeframe}`;
    const history = this.historicalData.get(key);

    if (!history || history.length < 10) {
      return issues;
    }

    // Calculate historical averages for comparison
    const recentHistory = history.slice(-10);
    const avgPrice = recentHistory.reduce((sum, point) => sum.plus(point.close), new Decimal(0))
                                  .dividedBy(recentHistory.length);
    const avgVolume = recentHistory.reduce((sum, point) => sum.plus(point.volume), new Decimal(0))
                                   .dividedBy(recentHistory.length);

    // Check for price spikes
    const priceDeviation = data.close.minus(avgPrice).dividedBy(avgPrice).abs();
    if (priceDeviation.toNumber() > this.config.maxPriceDeviationPercent / 100) {
      issues.push({
        id: `spike_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: IssueType.PRICE_SPIKE,
        severity: priceDeviation.toNumber() > 0.1 ? IssueSeverity.HIGH : IssueSeverity.MEDIUM,
        timestamp: new Date(),
        instrument: data.instrument,
        source: data.source,
        timeframe: data.timeframe,
        description: `Price spike detected: ${(priceDeviation.toNumber() * 100).toFixed(2)}% deviation from recent average`,
        affectedData: data,
        suggestedRepair: {
          type: RepairActionType.USE_FALLBACK_SOURCE,
          description: 'Verify against alternative data source',
          confidence: 60,
          estimatedImpact: 'high',
          automaticRepair: false,
        },
        metadata: {
          deviationPercent: priceDeviation.toNumber(),
          currentPrice: data.close.toNumber(),
          averagePrice: avgPrice.toNumber(),
        }
      });
    }

    // Check for volume anomalies
    const volumeRatio = data.volume.dividedBy(avgVolume);
    if (volumeRatio.toNumber() > this.config.maxVolumeDeviationMultiplier || 
        volumeRatio.toNumber() < (1 / this.config.maxVolumeDeviationMultiplier)) {
      issues.push({
        id: `volume_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: IssueType.VOLUME_ANOMALY,
        severity: IssueSeverity.MEDIUM,
        timestamp: new Date(),
        instrument: data.instrument,
        source: data.source,
        timeframe: data.timeframe,
        description: `Volume anomaly detected: ${volumeRatio.toFixed(2)}x average volume`,
        affectedData: data,
        suggestedRepair: {
          type: RepairActionType.MANUAL_REVIEW,
          description: 'Review volume data for accuracy',
          confidence: 50,
          estimatedImpact: 'low',
          automaticRepair: false,
        },
        metadata: {
          volumeRatio: volumeRatio.toNumber(),
          currentVolume: data.volume.toNumber(),
          averageVolume: avgVolume.toNumber(),
        }
      });
    }

    return issues;
  }

  /**
   * Detect duplicate data entries
   */
  private async detectDuplicates(data: NormalizedMarketData): Promise<DataQualityIssue[]> {
    const issues: DataQualityIssue[] = [];
    const key = `${data.instrument}_${data.source}_${data.timeframe}`;
    const history = this.historicalData.get(key);

    if (!history || history.length < 1) {
      return issues;
    }

    const previousPoint = history[history.length - 1];
    
    // Check if current data is identical to previous data
    if (data.timestamp.getTime() === previousPoint.timestamp.getTime() &&
        data.close.equals(previousPoint.close) &&
        data.volume.equals(previousPoint.volume)) {
      
      issues.push({
        id: `duplicate_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: IssueType.DUPLICATE_DATA,
        severity: IssueSeverity.MEDIUM,
        timestamp: new Date(),
        instrument: data.instrument,
        source: data.source,
        timeframe: data.timeframe,
        description: 'Duplicate data point detected',
        affectedData: data,
        suggestedRepair: {
          type: RepairActionType.IGNORE,
          description: 'Ignore duplicate data point',
          confidence: 95,
          estimatedImpact: 'low',
          automaticRepair: true,
        },
      });
    }

    return issues;
  }

  /**
   * Check basic data consistency (OHLC relationships, etc.)
   */
  private checkDataConsistency(data: NormalizedMarketData): DataQualityIssue[] {
    const issues: DataQualityIssue[] = [];

    // Check OHLC consistency
    if (data.high.lt(data.low) || 
        data.high.lt(data.open) || 
        data.high.lt(data.close) ||
        data.low.gt(data.open) || 
        data.low.gt(data.close)) {
      
      issues.push({
        id: `consistency_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: IssueType.OHLC_INCONSISTENCY,
        severity: IssueSeverity.HIGH,
        timestamp: new Date(),
        instrument: data.instrument,
        source: data.source,
        timeframe: data.timeframe,
        description: 'OHLC data inconsistency detected (high < low or price outside high/low range)',
        affectedData: data,
        suggestedRepair: {
          type: RepairActionType.AUTO_CORRECT,
          description: 'Auto-correct OHLC relationships',
          confidence: 80,
          estimatedImpact: 'medium',
          automaticRepair: true,
          repairData: this.repairOHLCInconsistency(data),
        },
      });
    }

    // Check spread reasonableness
    if (data.spread && data.close.gt(0)) {
      const spreadPercent = data.spread.dividedBy(data.close);
      if (spreadPercent.toNumber() > this.config.maxSpreadPercent / 100) {
        issues.push({
          id: `spread_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: IssueType.SPREAD_ANOMALY,
          severity: IssueSeverity.MEDIUM,
          timestamp: new Date(),
          instrument: data.instrument,
          source: data.source,
          timeframe: data.timeframe,
          description: `Abnormally wide spread detected: ${(spreadPercent.toNumber() * 100).toFixed(2)}%`,
          affectedData: data,
          suggestedRepair: {
            type: RepairActionType.USE_FALLBACK_SOURCE,
            description: 'Verify spread against alternative data source',
            confidence: 70,
            estimatedImpact: 'medium',
            automaticRepair: false,
          },
        });
      }
    }

    return issues;
  }

  /**
   * Check for stale data based on timestamp
   */
  private checkStaleData(data: NormalizedMarketData): DataQualityIssue[] {
    const issues: DataQualityIssue[] = [];
    const age = Date.now() - data.timestamp.getTime();

    if (age > this.config.staleDataThresholdMs) {
      issues.push({
        id: `stale_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        type: IssueType.STALE_DATA,
        severity: age > this.config.staleDataThresholdMs * 5 ? IssueSeverity.HIGH : IssueSeverity.MEDIUM,
        timestamp: new Date(),
        instrument: data.instrument,
        source: data.source,
        timeframe: data.timeframe,
        description: `Stale data detected: ${Math.round(age / 1000)}s old`,
        affectedData: data,
        suggestedRepair: {
          type: RepairActionType.USE_FALLBACK_SOURCE,
          description: 'Request fresh data from alternative source',
          confidence: 85,
          estimatedImpact: 'medium',
          automaticRepair: this.config.fallbackSources.length > 0,
        },
        metadata: {
          ageMs: age,
          dataTimestamp: data.timestamp,
          currentTime: new Date(),
        }
      });
    }

    return issues;
  }

  /**
   * Repair OHLC inconsistency by adjusting high/low values
   */
  private repairOHLCInconsistency(data: NormalizedMarketData): NormalizedMarketData {
    const repaired = { ...data };

    // Ensure high is the highest value
    const maxPrice = Decimal.max(data.open, data.close);
    if (data.high.lt(maxPrice)) {
      repaired.high = maxPrice;
    }

    // Ensure low is the lowest value
    const minPrice = Decimal.min(data.open, data.close);
    if (data.low.gt(minPrice)) {
      repaired.low = minPrice;
    }

    // Additional check: ensure high >= low
    if (repaired.high.lt(repaired.low)) {
      const avg = repaired.high.plus(repaired.low).dividedBy(2);
      repaired.high = avg;
      repaired.low = avg;
    }

    return repaired;
  }

  /**
   * Process detected quality issue
   */
  private async processIssue(issue: DataQualityIssue): Promise<void> {
    this.detectedIssues.push(issue);
    this.stats.totalIssuesDetected++;
    this.stats.issuesByType[issue.type]++;
    this.stats.issuesBySeverity[issue.severity]++;

    // Perform automatic repair if enabled and confidence is high enough
    if (this.config.enableAutoRepair && 
        issue.suggestedRepair?.automaticRepair &&
        issue.suggestedRepair.confidence >= this.config.autoRepairConfidenceThreshold) {
      
      await this.performAutomaticRepair(issue);
    } else {
      this.stats.manualReviewRequired++;
      this.emit('manual_review_required', issue);
    }

    this.emit('quality_issue_detected', issue);
  }

  /**
   * Perform automatic repair for qualifying issues
   */
  private async performAutomaticRepair(issue: DataQualityIssue): Promise<void> {
    try {
      if (!issue.suggestedRepair) return;

      const repairKey = `${issue.instrument}_${issue.source}_${issue.timeframe}`;
      if (!this.repairHistory.has(repairKey)) {
        this.repairHistory.set(repairKey, []);
      }

      this.repairHistory.get(repairKey)!.push(issue.suggestedRepair);
      this.stats.autoRepairsPerformed++;

      this.emit('automatic_repair_performed', {
        issue,
        repairAction: issue.suggestedRepair,
        timestamp: new Date(),
      });

    } catch (error) {
      this.emit('repair_error', { issue, error });
    }
  }

  /**
   * Update monitoring statistics
   */
  private updateStats(issues: DataQualityIssue[], startTime: number): void {
    const processingTime = Math.max(Date.now() - startTime, 0.01); // Ensure minimum 0.01ms
    
    // Update rolling average processing time
    const totalProcessed = this.stats.totalDataPointsProcessed;
    this.stats.averageProcessingTimeMs = 
      ((this.stats.averageProcessingTimeMs * (totalProcessed - 1)) + processingTime) / totalProcessed;

    // Update overall quality score (100 - percentage of issues)
    const issueRate = this.stats.totalIssuesDetected / this.stats.totalDataPointsProcessed;
    this.stats.overallQualityScore = Math.max(0, 100 - (issueRate * 100));

    this.stats.lastProcessedAt = new Date();
  }

  /**
   * Get current quality monitoring statistics
   */
  public getStats(): QualityStats {
    return { ...this.stats };
  }

  /**
   * Get detected issues with optional filtering
   */
  public getIssues(filter?: {
    type?: IssueType;
    severity?: IssueSeverity;
    instrument?: string;
    source?: DataSource;
    since?: Date;
  }): DataQualityIssue[] {
    let filteredIssues = [...this.detectedIssues];

    if (filter) {
      if (filter.type) {
        filteredIssues = filteredIssues.filter(issue => issue.type === filter.type);
      }
      if (filter.severity) {
        filteredIssues = filteredIssues.filter(issue => issue.severity === filter.severity);
      }
      if (filter.instrument) {
        filteredIssues = filteredIssues.filter(issue => issue.instrument === filter.instrument);
      }
      if (filter.source) {
        filteredIssues = filteredIssues.filter(issue => issue.source === filter.source);
      }
      if (filter.since) {
        filteredIssues = filteredIssues.filter(issue => issue.timestamp >= filter.since!);
      }
    }

    return filteredIssues;
  }

  /**
   * Clear historical data and issue history
   */
  public clearHistory(): void {
    this.historicalData.clear();
    this.detectedIssues = [];
    this.repairHistory.clear();
    this.emit('history_cleared');
  }

  /**
   * Reset all monitoring statistics
   */
  public resetStats(): void {
    this.stats = this.initializeStats();
    this.emit('stats_reset');
  }

  /**
   * Update monitoring configuration
   */
  public updateConfig(newConfig: Partial<QualityMonitorConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config_updated', this.config);
  }

  /**
   * Health check for the quality monitor itself
   */
  public healthCheck(): {
    isHealthy: boolean;
    issues: string[];
    stats: QualityStats;
  } {
    const issues: string[] = [];
    const stats = this.getStats();

    // Check if quality score is too low
    if (stats.overallQualityScore < 70) {
      issues.push(`Low overall quality score: ${stats.overallQualityScore.toFixed(2)}%`);
    }

    // Check if too many manual reviews are required
    const manualReviewRate = stats.totalDataPointsProcessed > 0 
      ? stats.manualReviewRequired / stats.totalDataPointsProcessed 
      : 0;
    if (manualReviewRate > 0.1) {
      issues.push(`High manual review rate: ${(manualReviewRate * 100).toFixed(2)}%`);
    }

    // Check processing performance
    if (stats.averageProcessingTimeMs > 100) {
      issues.push(`Slow processing time: ${stats.averageProcessingTimeMs.toFixed(2)}ms`);
    }

    return {
      isHealthy: issues.length === 0,
      issues,
      stats,
    };
  }

  /**
   * Shutdown quality monitor and cleanup resources
   */
  public shutdown(): void {
    this.clearHistory();
    this.removeAllListeners();
    this.emit('shutdown');
  }
}