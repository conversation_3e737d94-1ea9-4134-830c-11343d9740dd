/**
 * Broker Failover Engine
 * 
 * Handles automated broker failover with seamless trade execution continuity
 * Implements Task 2 from Story 4.3: Automated Failover Engine
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import type { 
  FailoverEvent, 
  FailoverTrigger
} from '@golddaddy/types';
import { BrokerConfigurationService } from './BrokerConfigurationService.js';
// Note: ProductionMT5ConnectionManager will be imported at runtime to avoid circular dependency

export interface FailoverDecision {
  shouldFailover: boolean;
  targetBroker?: string;
  reason: string;
  trigger: FailoverTrigger;
  impactedTrades: string[];
}

export interface FailoverConfiguration {
  // Failover trigger thresholds
  connectionTimeoutMs: number;
  maxFailureCount: number;
  healthCheckFailureThreshold: number;
  
  // Failover behavior
  maxFailoverTimeMs: number;
  enableAutomaticFailover: boolean;
  requireManualConfirmation: boolean;
  
  // Position sync settings
  enablePositionSync: boolean;
  syncTimeoutMs: number;
  allowPartialSync: boolean;
}

export class BrokerFailoverEngine extends EventEmitter {
  private activeFailovers: Map<string, FailoverEvent> = new Map();
  private failoverConfiguration: FailoverConfiguration;
  private connectionManager?: any; // ProductionMT5ConnectionManager - imported dynamically to avoid circular deps
  private brokerConfigService: BrokerConfigurationService;
  private isShuttingDown = false;

  constructor(
    private prisma: PrismaClient,
    config: Partial<FailoverConfiguration> = {}
  ) {
    super();
    
    this.brokerConfigService = new BrokerConfigurationService(prisma);
    
    // Default failover configuration
    this.failoverConfiguration = {
      connectionTimeoutMs: 10000,
      maxFailureCount: 3,
      healthCheckFailureThreshold: 5,
      maxFailoverTimeMs: 5000,
      enableAutomaticFailover: true,
      requireManualConfirmation: false,
      enablePositionSync: true,
      syncTimeoutMs: 30000,
      allowPartialSync: true,
      ...config
    };
  }

  /**
   * Initialize the failover engine with connection manager
   */
  async initialize(connectionManager: any): Promise<void> {
    this.connectionManager = connectionManager;
    
    // Listen for connection events
    connectionManager.on('connectionFailed', this.handleConnectionFailure.bind(this));
    connectionManager.on('connectionTimeout', this.handleConnectionTimeout.bind(this));
    connectionManager.on('healthCheckComplete', this.handleHealthCheckUpdate.bind(this));
    
    console.log('✅ Broker Failover Engine initialized');
    this.emit('initialized');
  }

  /**
   * Handle connection failure event
   */
  private async handleConnectionFailure(event: { connectionId: string; brokerId: string; error: string }): Promise<void> {
    console.log(`🚨 Connection failure detected: ${event.brokerId} - ${event.error}`);
    
    const decision = await this.evaluateFailoverDecision(
      event.brokerId,
      'CONNECTION_TIMEOUT',
      event.error
    );
    
    if (decision.shouldFailover) {
      await this.executeFailover(event.brokerId, decision);
    }
  }

  /**
   * Handle connection timeout event
   */
  private async handleConnectionTimeout(event: { connectionId: string; brokerId: string }): Promise<void> {
    console.log(`⏰ Connection timeout detected: ${event.brokerId}`);
    
    const decision = await this.evaluateFailoverDecision(
      event.brokerId,
      'CONNECTION_TIMEOUT',
      'Connection timeout exceeded threshold'
    );
    
    if (decision.shouldFailover) {
      await this.executeFailover(event.brokerId, decision);
    }
  }

  /**
   * Handle health check updates
   */
  private async handleHealthCheckUpdate(event: { 
    totalConnections: number; 
    healthyConnections: number; 
    averageLatency: number; 
  }): Promise<void> {
    // Check if overall system health is degraded
    const healthRatio = event.healthyConnections / event.totalConnections;
    
    if (healthRatio < 0.5) { // Less than 50% healthy connections
      console.log(`⚠️ System health degraded: ${Math.round(healthRatio * 100)}% healthy connections`);
      this.emit('systemHealthDegraded', {
        healthRatio,
        totalConnections: event.totalConnections,
        healthyConnections: event.healthyConnections,
        timestamp: new Date()
      });
    }
  }

  /**
   * Evaluate whether failover should occur
   */
  async evaluateFailoverDecision(
    failingBrokerId: string,
    trigger: FailoverTrigger,
    reason: string
  ): Promise<FailoverDecision> {
    try {
      console.log(`🤔 Evaluating failover decision for broker: ${failingBrokerId}`);

      if (!this.failoverConfiguration.enableAutomaticFailover) {
        return {
          shouldFailover: false,
          reason: 'Automatic failover is disabled',
          trigger,
          impactedTrades: []
        };
      }

      // Check if failover is already in progress for this broker
      if (this.activeFailovers.has(failingBrokerId)) {
        return {
          shouldFailover: false,
          reason: 'Failover already in progress',
          trigger,
          impactedTrades: []
        };
      }

      // Get broker configuration to check failure count
      const brokerConfigResult = await this.brokerConfigService.getBrokerConfiguration('system', failingBrokerId);
      if (!brokerConfigResult.success || !brokerConfigResult.data) {
        return {
          shouldFailover: false,
          reason: 'Broker configuration not found',
          trigger,
          impactedTrades: []
        };
      }

      const failingBroker = brokerConfigResult.data;
      
      // Check if failure threshold is exceeded
      if (failingBroker.failureCount < this.failoverConfiguration.maxFailureCount) {
        return {
          shouldFailover: false,
          reason: `Failure count (${failingBroker.failureCount}) below threshold (${this.failoverConfiguration.maxFailureCount})`,
          trigger,
          impactedTrades: []
        };
      }

      // Find available target broker for failover
      const availableBrokers = await this.brokerConfigService.getBrokersForFailover(
        failingBroker.userId,
        'trading' // Require trading feature for failover
      );

      // Filter out the failing broker and unhealthy brokers
      const viableBrokers = availableBrokers.filter(broker => 
        broker.id !== failingBrokerId && 
        broker.isHealthy && 
        broker.status === 'ACTIVE'
      );

      if (viableBrokers.length === 0) {
        console.error('❌ No viable brokers available for failover');
        return {
          shouldFailover: false,
          reason: 'No healthy backup brokers available',
          trigger,
          impactedTrades: []
        };
      }

      // Select best target broker (lowest priority number = highest priority)
      const targetBroker = viableBrokers.reduce((best, current) => 
        current.priority < best.priority ? current : best
      );

      // Get impacted trades (mock implementation)
      const impactedTrades = await this.getImpactedTrades(failingBrokerId);

      return {
        shouldFailover: true,
        targetBroker: targetBroker.id,
        reason: `Automatic failover triggered: ${reason}`,
        trigger,
        impactedTrades
      };

    } catch (error) {
      console.error('Failed to evaluate failover decision:', error);
      return {
        shouldFailover: false,
        reason: 'Failed to evaluate failover decision',
        trigger,
        impactedTrades: []
      };
    }
  }

  /**
   * Execute broker failover
   */
  async executeFailover(
    fromBrokerId: string,
    decision: FailoverDecision
  ): Promise<FailoverEvent | null> {
    if (!decision.targetBroker) {
      console.error('❌ Cannot execute failover: no target broker specified');
      return null;
    }

    const failoverStartTime = Date.now();
    console.log(`🔄 Starting failover from ${fromBrokerId} to ${decision.targetBroker}`);

    try {
      // Create failover event record
      const failoverEvent: Omit<FailoverEvent, 'id' | 'createdAt'> = {
        userId: 'system', // Will be updated with actual user ID
        fromBroker: fromBrokerId,
        toBroker: decision.targetBroker,
        trigger: decision.trigger,
        failoverTime: new Date(),
        duration: undefined,
        impactedTrades: decision.impactedTrades,
        positionsSynced: false,
        dataLoss: false,
        errorCode: undefined,
        errorMessage: undefined,
        errorContext: undefined,
        recoveryActions: [],
        manualIntervention: false,
        auditTrailId: undefined,
        recoveryTime: undefined
      };

      // Add to active failovers
      this.activeFailovers.set(fromBrokerId, failoverEvent as FailoverEvent);

      this.emit('failoverStarted', {
        fromBroker: fromBrokerId,
        toBroker: decision.targetBroker,
        reason: decision.reason,
        timestamp: new Date()
      });

      // Step 1: Prepare target broker connection
      await this.prepareTargetBroker(decision.targetBroker);
      failoverEvent.recoveryActions.push('Target broker connection prepared');

      // Step 2: Synchronize positions if enabled
      if (this.failoverConfiguration.enablePositionSync) {
        const syncResult = await this.synchronizePositions(
          fromBrokerId,
          decision.targetBroker,
          decision.impactedTrades
        );
        
        failoverEvent.positionsSynced = syncResult.success;
        failoverEvent.dataLoss = !syncResult.success && !this.failoverConfiguration.allowPartialSync;
        
        if (syncResult.success) {
          failoverEvent.recoveryActions.push('Positions synchronized successfully');
        } else {
          failoverEvent.recoveryActions.push(`Position sync failed: ${syncResult.error}`);
          
          if (!this.failoverConfiguration.allowPartialSync) {
            throw new Error(`Position synchronization failed: ${syncResult.error}`);
          }
        }
      }

      // Step 3: Update connection routing
      await this.updateConnectionRouting(fromBrokerId, decision.targetBroker);
      failoverEvent.recoveryActions.push('Connection routing updated');

      // Step 4: Validate failover success
      const validationResult = await this.validateFailoverSuccess(decision.targetBroker);
      if (!validationResult.success) {
        throw new Error(`Failover validation failed: ${validationResult.error}`);
      }
      failoverEvent.recoveryActions.push('Failover validation completed');

      // Mark failover as complete
      const failoverDuration = Date.now() - failoverStartTime;
      failoverEvent.duration = failoverDuration;
      failoverEvent.recoveryTime = new Date();

      // Save failover event to database
      const savedEvent = await this.prisma.failoverEvent.create({
        data: {
          ...failoverEvent,
          userId: await this.getBrokerUserId(fromBrokerId) || 'system'
        }
      });

      // Remove from active failovers
      this.activeFailovers.delete(fromBrokerId);

      console.log(`✅ Failover completed successfully in ${failoverDuration}ms`);
      
      this.emit('failoverCompleted', {
        event: savedEvent,
        duration: failoverDuration,
        success: true
      });

      // Update broker statuses
      await this.brokerConfigService.updateBrokerHealth(fromBrokerId, false, 0, 'Failed over to backup broker');
      await this.brokerConfigService.updateBrokerHealth(decision.targetBroker, true, failoverDuration);

      return savedEvent as FailoverEvent;

    } catch (error) {
      const failoverDuration = Date.now() - failoverStartTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown failover error';

      console.error(`❌ Failover failed after ${failoverDuration}ms:`, error);

      // Update failover event with error info
      const failedEvent = this.activeFailovers.get(fromBrokerId);
      if (failedEvent) {
        failedEvent.duration = failoverDuration;
        failedEvent.errorMessage = errorMessage;
        failedEvent.errorCode = 'FAILOVER_FAILED';
        failedEvent.recoveryActions.push(`Failover failed: ${errorMessage}`);

        // Save failed event
        try {
          await this.prisma.failoverEvent.create({
            data: {
              ...failedEvent,
              userId: await this.getBrokerUserId(fromBrokerId) || 'system'
            }
          });
        } catch (saveError) {
          console.error('Failed to save failed failover event:', saveError);
        }
      }

      // Remove from active failovers
      this.activeFailovers.delete(fromBrokerId);

      this.emit('failoverFailed', {
        fromBroker: fromBrokerId,
        targetBroker: decision.targetBroker,
        error: errorMessage,
        duration: failoverDuration
      });

      return null;
    }
  }

  /**
   * Trigger manual failover
   */
  async triggerManualFailover(
    userId: string,
    fromBrokerId: string,
    toBrokerId: string,
    reason: string
  ): Promise<FailoverEvent | null> {
    console.log(`👤 Manual failover triggered by user: ${userId}`);

    const decision: FailoverDecision = {
      shouldFailover: true,
      targetBroker: toBrokerId,
      reason: `Manual override: ${reason}`,
      trigger: 'MANUAL_OVERRIDE',
      impactedTrades: await this.getImpactedTrades(fromBrokerId)
    };

    return await this.executeFailover(fromBrokerId, decision);
  }

  /**
   * Get currently active failover events
   */
  getActiveFailovers(): FailoverEvent[] {
    return Array.from(this.activeFailovers.values());
  }

  /**
   * Cancel active failover (if possible)
   */
  async cancelFailover(fromBrokerId: string, reason: string): Promise<boolean> {
    const activeFailover = this.activeFailovers.get(fromBrokerId);
    if (!activeFailover) {
      return false;
    }

    console.log(`⏹️ Cancelling failover for broker: ${fromBrokerId} - ${reason}`);
    
    // Mark as cancelled and remove from active failovers
    activeFailover.errorMessage = `Failover cancelled: ${reason}`;
    activeFailover.recoveryActions.push(`Cancelled by system: ${reason}`);
    activeFailover.manualIntervention = true;

    this.activeFailovers.delete(fromBrokerId);
    
    this.emit('failoverCancelled', {
      fromBroker: fromBrokerId,
      reason,
      timestamp: new Date()
    });

    return true;
  }

  /**
   * Shutdown the failover engine
   */
  async shutdown(): Promise<void> {
    console.log('⏹️ Shutting down Broker Failover Engine...');
    this.isShuttingDown = true;

    // Cancel any active failovers
    for (const [brokerId] of this.activeFailovers) {
      await this.cancelFailover(brokerId, 'System shutdown');
    }

    console.log('✅ Broker Failover Engine shutdown complete');
    this.emit('shutdown');
  }

  // === Private Helper Methods ===

  private async prepareTargetBroker(targetBrokerId: string): Promise<void> {
    console.log(`🔧 Preparing target broker: ${targetBrokerId}`);
    
    // Mock preparation - in production would:
    // 1. Validate broker connection
    // 2. Check account balance and margin
    // 3. Verify trading permissions
    // 4. Test basic trading operations
    
    await new Promise(resolve => setTimeout(resolve, 500)); // Simulate preparation time
    
    console.log(`✅ Target broker prepared: ${targetBrokerId}`);
  }

  private async synchronizePositions(
    fromBrokerId: string,
    toBrokerId: string,
    impactedTrades: string[]
  ): Promise<{ success: boolean; error?: string }> {
    console.log(`🔄 Synchronizing positions from ${fromBrokerId} to ${toBrokerId}`);
    
    try {
      // Mock position sync - in production would:
      // 1. Get current positions from failing broker
      // 2. Calculate net positions by instrument
      // 3. Open corresponding positions on target broker
      // 4. Verify position accuracy
      // 5. Close positions on failing broker if possible
      
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate sync time
      
      // Simulate 95% success rate for testing
      if (Math.random() < 0.95) {
        console.log(`✅ Positions synchronized successfully (${impactedTrades.length} trades)`);
        return { success: true };
      } else {
        return { 
          success: false, 
          error: 'Position synchronization timeout' 
        };
      }
      
    } catch (error) {
      console.error('Position synchronization failed:', error);
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown sync error' 
      };
    }
  }

  private async updateConnectionRouting(fromBrokerId: string, toBrokerId: string): Promise<void> {
    console.log(`🔄 Updating connection routing from ${fromBrokerId} to ${toBrokerId}`);
    
    if (this.connectionManager) {
      // Update connection manager to route requests to new broker
      // This would involve updating the internal routing logic
    }
    
    console.log(`✅ Connection routing updated to ${toBrokerId}`);
  }

  private async validateFailoverSuccess(targetBrokerId: string): Promise<{ success: boolean; error?: string }> {
    console.log(`🔍 Validating failover success for broker: ${targetBrokerId}`);
    
    try {
      // Mock validation - in production would:
      // 1. Test connection to target broker
      // 2. Verify positions are correct
      // 3. Test a small trade execution
      // 4. Check data feed continuity
      
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate validation time
      
      // Simulate 98% success rate
      if (Math.random() < 0.98) {
        console.log(`✅ Failover validation successful for ${targetBrokerId}`);
        return { success: true };
      } else {
        return { 
          success: false, 
          error: 'Target broker connection validation failed' 
        };
      }
      
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown validation error' 
      };
    }
  }

  private async getImpactedTrades(brokerId: string): Promise<string[]> {
    try {
      // Mock implementation - in production would query actual active trades
      const activeTradesCount = Math.floor(Math.random() * 10) + 1; // 1-10 trades
      const impactedTrades: string[] = [];
      
      for (let i = 0; i < activeTradesCount; i++) {
        impactedTrades.push(`trade_${brokerId}_${i}`);
      }
      
      return impactedTrades;
      
    } catch (error) {
      console.error('Failed to get impacted trades:', error);
      return [];
    }
  }

  private async getBrokerUserId(brokerId: string): Promise<string | null> {
    try {
      const brokerConfig = await this.prisma.brokerConfiguration.findUnique({
        where: { id: brokerId }
      });
      
      return brokerConfig?.userId || null;
      
    } catch (error) {
      console.error('Failed to get broker user ID:', error);
      return null;
    }
  }
}