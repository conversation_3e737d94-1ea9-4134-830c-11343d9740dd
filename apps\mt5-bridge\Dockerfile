# MT5 Bridge Service - Production Dockerfile
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    POETRY_VERSION=1.6.1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    curl \
    wget \
    unzip \
    libpq-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

# Install Poetry
RUN pip install poetry==$POETRY_VERSION

# Create application directory
WORKDIR /app

# Copy dependency files
COPY pyproject.toml poetry.lock* ./

# Configure poetry
RUN poetry config virtualenvs.create false

# Install dependencies
RUN poetry install --only=main --no-dev

# Create non-root user
RUN groupadd -r mt5user && useradd -r -g mt5user -d /app -s /bin/bash mt5user

# Copy application code
COPY --chown=mt5user:mt5user apps/mt5-bridge/python/ ./

# Create necessary directories
RUN mkdir -p logs data && \
    chown -R mt5user:mt5user logs data

# Set user
USER mt5user

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import requests; requests.get('http://localhost:8000/health', timeout=5)"

# Expose port
EXPOSE 8000

# Default command
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]

# =============================================================================
# Development Stage
# =============================================================================
FROM base as development

USER root

# Install development dependencies
RUN poetry install --with=dev

# Install development tools
RUN pip install ipython jupyter debugpy

USER mt5user

# Development command
CMD ["python", "-m", "uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

# =============================================================================
# Testing Stage  
# =============================================================================
FROM base as testing

USER root

# Install test dependencies
RUN poetry install --with=dev,test

USER mt5user

# Test command
CMD ["python", "-m", "pytest", "-v", "--cov=.", "--cov-report=html"]

# =============================================================================
# Production Stage
# =============================================================================
FROM base as production

# Copy optimized Python bytecode
RUN python -m compileall .

# Production settings
ENV ENVIRONMENT=production \
    LOG_LEVEL=INFO \
    WORKERS=4

# Use gunicorn for production
RUN pip install gunicorn[gevent]

# Production command with gunicorn
CMD ["gunicorn", "main:app", "-w", "4", "-k", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000", "--access-logfile", "-", "--error-logfile", "-"]