import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { QuizFeedbackService } from '../../services/confidence/QuizFeedbackService.js';
import { QuizCategory, QuizDifficulty } from '@golddaddy/types';

const router = Router();
const prisma = new PrismaClient();

// Initialize services
const feedbackService = new QuizFeedbackService(prisma);

// Validation schemas
const educationalContentSchema = z.object({
  category: z.nativeEnum(QuizCategory),
  topic: z.string().min(1),
  difficulty: z.nativeEnum(QuizDifficulty),
  userId: z.string().min(1).optional()
});

const weakAreaRecommendationsSchema = z.object({
  userId: z.string().min(1),
  categories: z.array(z.nativeEnum(QuizCategory)),
  performance: z.object({
    averageScore: z.number().min(0).max(100),
    attemptCount: z.number().min(1),
    consistencyRating: z.number().min(0).max(1)
  }).optional()
});

const interactiveExplanationSchema = z.object({
  questionId: z.string().min(1),
  userId: z.string().min(1),
  clarificationRequest: z.string().min(1).max(500)
});

const learningResourcesSchema = z.object({
  userId: z.string().min(1),
  categories: z.array(z.nativeEnum(QuizCategory)).optional(),
  difficulty: z.nativeEnum(QuizDifficulty).optional(),
  topics: z.array(z.string()).optional(),
  limit: z.number().min(1).max(50).optional(),
  offset: z.number().min(0).optional()
});

// Error handling middleware
const handleAsync = (fn: (...args: any[]) => any) => (req: any, res: any, next: any) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * POST /api/confidence/content/educational
 * Generate educational content for a specific topic and category
 */
router.post('/educational', handleAsync(async (req: any, res: any) => {
  try {
    const { category, topic, difficulty, userId } = educationalContentSchema.parse(req.body);

    const content = await feedbackService.generateEducationalContent(
      category,
      topic,
      difficulty
    );

    // If userId provided, track content access for personalization
    if (userId) {
      await prisma.userActivity.create({
        data: {
          userId,
          activityType: 'EDUCATIONAL_CONTENT_ACCESS',
          metadata: {
            category,
            topic,
            difficulty,
            contentId: `${category}_${topic}_${difficulty}`,
            accessedAt: new Date()
          }
        }
      }).catch(err => {
        // Log but don't fail the request if activity tracking fails
        console.warn('Failed to track educational content access:', err);
      });
    }

    res.json({
      success: true,
      content: {
        category,
        topic,
        difficulty,
        conceptExplanation: content.conceptExplanation,
        realWorldApplication: content.realWorldApplication,
        keyTakeaways: content.keyTakeaways,
        prerequisites: content.prerequisites,
        relatedTopics: content.relatedTopics,
        practiceExercises: content.practiceExercises,
        estimatedReadTime: Math.ceil(content.conceptExplanation.split(' ').length / 200), // ~200 WPM
        lastUpdated: new Date()
      }
    });
  } catch (error) {
    console.error('Error generating educational content:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to generate educational content'
    });
  }
}));

/**
 * POST /api/confidence/content/recommendations
 * Get personalized learning recommendations based on weak areas
 */
router.post('/recommendations', handleAsync(async (req: any, res: any) => {
  try {
    const { userId, categories, performance } = weakAreaRecommendationsSchema.parse(req.body);

    // If no performance data provided, calculate from user's quiz history
    let userPerformance = performance;
    if (!userPerformance) {
      const recentAttempts = await prisma.quizAttempt.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: 10
      });

      if (recentAttempts.length === 0) {
        return res.status(404).json({
          error: 'No quiz history found for recommendations'
        });
      }

      userPerformance = {
        averageScore: Math.round(
          recentAttempts.reduce((sum, attempt) => sum + attempt.overallScore, 0) / recentAttempts.length
        ),
        attemptCount: recentAttempts.length,
        consistencyRating: calculateConsistencyRating(recentAttempts.map(a => a.overallScore))
      };
    }

    const recommendations = await feedbackService.generateWeakAreaRecommendations(
      userId,
      categories,
      userPerformance
    );

    res.json({
      success: true,
      userPerformance,
      recommendations: recommendations.map(rec => ({
        category: rec.category,
        priority: rec.priority,
        specificTopics: rec.specificTopics,
        recommendedResources: rec.recommendedResources,
        studyPlan: rec.studyPlan,
        progressMetrics: {
          currentLevel: rec.progressMetrics.currentLevel,
          targetLevel: rec.progressMetrics.targetLevel,
          estimatedTimeToTarget: rec.progressMetrics.estimatedTimeToTarget
        },
        customTips: rec.customTips
      })),
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Error generating recommendations:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to generate recommendations'
    });
  }
}));

/**
 * POST /api/confidence/content/explanation
 * Get interactive explanation for a specific question
 */
router.post('/explanation', handleAsync(async (req: any, res: any) => {
  try {
    const { questionId, userId, clarificationRequest } = interactiveExplanationSchema.parse(req.body);

    const explanation = await feedbackService.getInteractiveExplanation(
      questionId,
      userId,
      clarificationRequest
    );

    // Track interaction for learning analytics
    await prisma.userActivity.create({
      data: {
        userId,
        activityType: 'INTERACTIVE_EXPLANATION_REQUEST',
        metadata: {
          questionId,
          clarificationRequest: clarificationRequest.substring(0, 100), // Truncate for privacy
          requestType: categorizeRequest(clarificationRequest),
          timestamp: new Date()
        }
      }
    }).catch(err => {
      console.warn('Failed to track explanation request:', err);
    });

    res.json({
      success: true,
      explanation: {
        clarification: explanation.clarification,
        followUpQuestions: explanation.followUpQuestions,
        additionalExamples: explanation.additionalExamples,
        suggestedActions: explanation.suggestedActions,
        relatedConcepts: explanation.relatedConcepts
      }
    });
  } catch (error) {
    console.error('Error generating interactive explanation:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    if (error.message === 'Question not found') {
      return res.status(404).json({
        error: 'Question not found'
      });
    }

    res.status(500).json({
      error: 'Failed to generate explanation'
    });
  }
}));

/**
 * GET /api/confidence/content/resources
 * Get learning resources filtered by user preferences
 */
router.get('/resources', handleAsync(async (req: any, res: any) => {
  try {
    const { userId, categories, difficulty, topics, limit = 20, offset = 0 } = 
      learningResourcesSchema.parse(req.query);

    // Build filter criteria
    const whereClause: any = {};
    if (categories?.length) {
      whereClause.category = { in: categories };
    }
    if (difficulty) {
      whereClause.difficulty = difficulty;
    }
    if (topics?.length) {
      whereClause.topic = { in: topics };
    }

    // Get learning resources
    const resources = await prisma.learningResource.findMany({
      where: whereClause,
      orderBy: [
        { priority: 'desc' },
        { createdAt: 'desc' }
      ],
      take: limit,
      skip: offset
    });

    const totalCount = await prisma.learningResource.count({
      where: whereClause
    });

    // Get user's progress on these resources if available
    const userProgress = await prisma.userProgress.findMany({
      where: {
        userId,
        resourceId: { in: resources.map(r => r.id) }
      }
    });

    const progressMap = userProgress.reduce((map, progress) => {
      map[progress.resourceId] = {
        completed: progress.completed,
        progress: progress.progress,
        lastAccessed: progress.lastAccessed
      };
      return map;
    }, {} as Record<string, any>);

    // Get personalized recommendations based on user's quiz history
    const recentWeakAreas = await prisma.quizAttempt.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 5,
      select: {
        weakAreas: true,
        categoryScores: true
      }
    });

    const personalizedScore = (resource: any) => {
      let score = resource.priority || 0;
      
      // Boost score for resources in user's weak areas
      if (recentWeakAreas.some(attempt => 
        attempt.weakAreas.includes(resource.category))) {
        score += 20;
      }
      
      // Boost score for appropriate difficulty level
      const userLevel = getUserDifficultyLevel(recentWeakAreas);
      if (resource.difficulty === userLevel) {
        score += 10;
      }
      
      return score;
    };

    res.json({
      success: true,
      resources: resources
        .map(resource => ({
          id: resource.id,
          title: resource.title,
          description: resource.description,
          category: resource.category,
          difficulty: resource.difficulty,
          topic: resource.topic,
          type: resource.type, // 'article', 'video', 'interactive', 'quiz'
          url: resource.url,
          duration: resource.estimatedDuration, // in minutes
          tags: resource.tags,
          personalizedScore: personalizedScore(resource),
          userProgress: progressMap[resource.id] || {
            completed: false,
            progress: 0,
            lastAccessed: null
          }
        }))
        .sort((a, b) => b.personalizedScore - a.personalizedScore),
      pagination: {
        total: totalCount,
        limit,
        offset,
        hasMore: offset + limit < totalCount
      },
      filters: {
        categories: Object.values(QuizCategory),
        difficulties: Object.values(QuizDifficulty),
        availableTopics: await getAvailableTopics(categories)
      }
    });
  } catch (error) {
    console.error('Error getting learning resources:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to get learning resources'
    });
  }
}));

/**
 * POST /api/confidence/content/progress
 * Track user progress on learning resources
 */
router.post('/progress', handleAsync(async (req: any, res: any) => {
  try {
    const { userId, resourceId, progress, completed, timeSpent } = z.object({
      userId: z.string().min(1),
      resourceId: z.string().min(1),
      progress: z.number().min(0).max(100),
      completed: z.boolean().optional(),
      timeSpent: z.number().min(0).optional()
    }).parse(req.body);

    const userProgress = await prisma.userProgress.upsert({
      where: {
        userId_resourceId: {
          userId,
          resourceId
        }
      },
      update: {
        progress,
        completed: completed ?? (progress >= 100),
        lastAccessed: new Date(),
        timeSpent: timeSpent ? { increment: timeSpent } : undefined,
        updatedAt: new Date()
      },
      create: {
        userId,
        resourceId,
        progress,
        completed: completed ?? (progress >= 100),
        lastAccessed: new Date(),
        timeSpent: timeSpent || 0
      }
    });

    res.json({
      success: true,
      progress: {
        userId: userProgress.userId,
        resourceId: userProgress.resourceId,
        progress: userProgress.progress,
        completed: userProgress.completed,
        lastAccessed: userProgress.lastAccessed,
        totalTimeSpent: userProgress.timeSpent
      }
    });
  } catch (error) {
    console.error('Error tracking resource progress:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to track progress'
    });
  }
}));

// Helper functions
function calculateConsistencyRating(scores: number[]): number {
  if (scores.length < 2) return 1;
  
  const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
  const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
  const standardDeviation = Math.sqrt(variance);
  
  // Convert to 0-1 scale where lower standard deviation = higher consistency
  return Math.max(0, 1 - (standardDeviation / 50));
}

function categorizeRequest(request: string): string {
  const lowerRequest = request.toLowerCase();
  
  if (lowerRequest.includes('why') || lowerRequest.includes('reason')) return 'reasoning';
  if (lowerRequest.includes('example') || lowerRequest.includes('instance')) return 'example';
  if (lowerRequest.includes('calculate') || lowerRequest.includes('math')) return 'calculation';
  if (lowerRequest.includes('how') || lowerRequest.includes('process')) return 'process';
  
  return 'general';
}

function getUserDifficultyLevel(recentAttempts: any[]): QuizDifficulty {
  if (recentAttempts.length === 0) return QuizDifficulty.BEGINNER;
  
  const avgScore = recentAttempts.reduce((sum, attempt) => {
    const scores = Object.values(attempt.categoryScores || {}) as number[];
    const attemptAvg = scores.length > 0 ? scores.reduce((s, score) => s + score, 0) / scores.length : 0;
    return sum + attemptAvg;
  }, 0) / recentAttempts.length;
  
  if (avgScore >= 85) return QuizDifficulty.ADVANCED;
  if (avgScore >= 70) return QuizDifficulty.INTERMEDIATE;
  return QuizDifficulty.BEGINNER;
}

async function getAvailableTopics(categories?: QuizCategory[]): Promise<string[]> {
  const whereClause = categories?.length ? { category: { in: categories } } : {};
  
  const questions = await prisma.quizQuestion.findMany({
    where: whereClause,
    select: { topic: true },
    distinct: ['topic']
  });
  
  return questions.map(q => q.topic).sort();
}

export default router;