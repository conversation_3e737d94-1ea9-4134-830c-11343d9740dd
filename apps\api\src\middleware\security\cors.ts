/**
 * CORS Configuration Middleware for GoldDaddy Trading Platform
 * Production-optimized Cross-Origin Resource Sharing policies
 */

import { Request, Response, NextFunction } from 'express';

// Production CORS origins
const PRODUCTION_ORIGINS = [
  'https://golddaddy.app',
  'https://www.golddaddy.app',
  'https://api.golddaddy.app',
  'https://app.golddaddy.app'
];

// Staging CORS origins
const STAGING_ORIGINS = [
  'https://staging.golddaddy.app',
  'https://staging-api.golddaddy.app',
  'http://localhost:3000',
  'http://localhost:3001'
];

// Development CORS origins
const DEVELOPMENT_ORIGINS = [
  'http://localhost:3000',
  'http://localhost:3001',
  'http://localhost:3002',
  'http://127.0.0.1:3000',
  'http://127.0.0.1:3001',
  'http://127.0.0.1:3002'
];

/**
 * Get allowed origins based on environment
 */
function getAllowedOrigins(): string[] {
  const environment = process.env.NODE_ENV || 'development';
  
  switch (environment) {
    case 'production':
      return PRODUCTION_ORIGINS;
    case 'staging':
      return [...STAGING_ORIGINS, ...PRODUCTION_ORIGINS];
    case 'test':
      return ['http://localhost:3000', 'http://localhost:3001'];
    default:
      return [...DEVELOPMENT_ORIGINS, ...STAGING_ORIGINS];
  }
}

/**
 * Check if origin is allowed
 */
function isOriginAllowed(origin: string | undefined): boolean {
  if (!origin) {
    // Allow requests with no origin (mobile apps, curl, etc.) in development
    return process.env.NODE_ENV !== 'production';
  }
  
  const allowedOrigins = getAllowedOrigins();
  return allowedOrigins.includes(origin);
}

/**
 * CORS middleware with security-first approach
 */
export const corsMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const origin = req.headers.origin;
  const requestMethod = req.method;
  
  // Check if origin is allowed
  if (isOriginAllowed(origin)) {
    res.setHeader('Access-Control-Allow-Origin', origin || '*');
  } else {
    // Log unauthorized origin attempts in production
    if (process.env.NODE_ENV === 'production' && origin) {
      console.warn(`CORS: Unauthorized origin attempted: ${origin} from IP: ${req.ip}`);
    }
    
    // In production, reject unauthorized origins
    if (process.env.NODE_ENV === 'production') {
      res.status(403).json({
        error: 'Forbidden',
        message: 'Origin not allowed by CORS policy'
      });
      return;
    }
  }
  
  // Handle preflight requests
  if (requestMethod === 'OPTIONS') {
    // Allowed methods for different endpoints
    const allowedMethods = getAllowedMethodsForPath(req.path);
    res.setHeader('Access-Control-Allow-Methods', allowedMethods.join(', '));
    
    // Allowed headers
    const allowedHeaders = [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-API-Key',
      'X-Client-Version',
      'X-Request-ID'
    ];
    res.setHeader('Access-Control-Allow-Headers', allowedHeaders.join(', '));
    
    // Preflight cache duration (24 hours in production, 1 hour in development)
    const maxAge = process.env.NODE_ENV === 'production' ? 86400 : 3600;
    res.setHeader('Access-Control-Max-Age', maxAge.toString());
    
    // Allow credentials for authenticated requests
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    res.status(204).end();
    return;
  }
  
  // Set CORS headers for actual requests
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Expose-Headers', [
    'X-Total-Count',
    'X-Rate-Limit-Limit',
    'X-Rate-Limit-Remaining',
    'X-Rate-Limit-Reset',
    'X-Request-ID'
  ].join(', '));
  
  next();
};

/**
 * Get allowed HTTP methods based on API path
 */
function getAllowedMethodsForPath(path: string): string[] {
  // Default allowed methods
  let allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'];
  
  // Restrict methods for sensitive endpoints
  if (path.startsWith('/api/auth/')) {
    allowedMethods = ['GET', 'POST', 'OPTIONS'];
  } else if (path.startsWith('/api/admin/')) {
    allowedMethods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'];
  } else if (path.startsWith('/api/monitoring/')) {
    allowedMethods = ['GET', 'OPTIONS'];
  } else if (path.startsWith('/api/webhooks/')) {
    allowedMethods = ['POST', 'OPTIONS'];
  }
  
  return allowedMethods;
}

/**
 * Strict CORS middleware for sensitive endpoints
 * Only allows specific origins and methods
 */
export const strictCorsMiddleware = (allowedOrigins: string[] = PRODUCTION_ORIGINS) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const origin = req.headers.origin;
    
    if (!origin || !allowedOrigins.includes(origin)) {
      res.status(403).json({
        error: 'Forbidden',
        message: 'Origin not allowed for this endpoint'
      });
      return;
    }
    
    res.setHeader('Access-Control-Allow-Origin', origin);
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept, Authorization');
    res.setHeader('Access-Control-Max-Age', '3600');
    
    if (req.method === 'OPTIONS') {
      res.status(204).end();
      return;
    }
    
    next();
  };
};

/**
 * WebSocket CORS validation
 */
export const validateWebSocketOrigin = (origin: string): boolean => {
  return isOriginAllowed(origin);
};

/**
 * CORS configuration for different environments
 */
export const getCorsConfig = () => {
  const environment = process.env.NODE_ENV || 'development';
  
  return {
    origin: (origin: string | undefined, callback: (err: Error | null, allowed?: boolean) => void) => {
      if (isOriginAllowed(origin)) {
        callback(null, true);
      } else {
        const error = new Error('Not allowed by CORS policy');
        callback(environment === 'production' ? error : null, environment !== 'production');
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-API-Key',
      'X-Client-Version',
      'X-Request-ID'
    ],
    exposedHeaders: [
      'X-Total-Count',
      'X-Rate-Limit-Limit',
      'X-Rate-Limit-Remaining',
      'X-Rate-Limit-Reset',
      'X-Request-ID'
    ],
    maxAge: environment === 'production' ? 86400 : 3600,
    preflightContinue: false,
    optionsSuccessStatus: 204
  };
};

/**
 * Development CORS middleware with logging
 */
export const developmentCorsMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const origin = req.headers.origin;
  
  if (process.env.NODE_ENV === 'development' && origin) {
    console.log(`CORS: Request from origin: ${origin}`);
  }
  
  // Allow all origins in development
  res.setHeader('Access-Control-Allow-Origin', origin || '*');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, PATCH, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', '*');
  res.setHeader('Access-Control-Expose-Headers', '*');
  
  if (req.method === 'OPTIONS') {
    res.status(204).end();
    return;
  }
  
  next();
};