/**
 * Unit tests for DataQualityMonitor
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import Decimal from 'decimal.js';
import {
  DataQualityMonitor,
  QualityMonitorConfig,
  IssueType,
  IssueSeverity,
  RepairActionType,
} from './DataQualityMonitor';
import {
  NormalizedMarketData,
  DataSource,
  TimeFrame,
} from './RealTimeDataProcessor';

describe('DataQualityMonitor', () => {
  let monitor: DataQualityMonitor;
  let config: QualityMonitorConfig;

  beforeEach(() => {
    config = {
      enableAutoRepair: true,
      autoRepairConfidenceThreshold: 80,
      maxPriceDeviationPercent: 5,
      maxVolumeDeviationMultiplier: 10,
      maxTimestampGapMs: 60000, // 1 minute
      staleDataThresholdMs: 300000, // 5 minutes
      maxSpreadPercent: 1,
      enableGapDetection: true,
      enableAnomalyDetection: true,
      enableDuplicateDetection: true,
      fallbackSources: [DataSource.ALPHA_VANTAGE, DataSource.YAHOO_FINANCE],
    };

    monitor = new DataQualityMonitor(config);
  });

  afterEach(() => {
    monitor.shutdown();
  });

  // Helper function to create test market data
  const createTestMarketData = (overrides: Partial<NormalizedMarketData> = {}): NormalizedMarketData => ({
    id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    source: DataSource.MT5,
    instrument: 'EURUSD',
    timeframe: TimeFrame.M1,
    timestamp: new Date(),
    open: new Decimal('1.08500'),
    high: new Decimal('1.08650'),
    low: new Decimal('1.08450'),
    close: new Decimal('1.08600'),
    volume: new Decimal('1000'),
    precision: 5,
    timezone: 'UTC',
    isCompressed: false,
    qualityScore: 100,
    originalPayloadSize: 500,
    processedPayloadSize: 500,
    processingTimeMs: 10,
    ...overrides,
  });

  describe('Initialization', () => {
    it('should initialize with default stats', () => {
      const stats = monitor.getStats();
      
      expect(stats.totalDataPointsProcessed).toBe(0);
      expect(stats.totalIssuesDetected).toBe(0);
      expect(stats.overallQualityScore).toBe(100);
      expect(stats.autoRepairsPerformed).toBe(0);
      expect(stats.manualReviewRequired).toBe(0);
    });

    it('should initialize with all issue types tracked', () => {
      const stats = monitor.getStats();
      
      expect(stats.issuesByType[IssueType.MISSING_DATA]).toBe(0);
      expect(stats.issuesByType[IssueType.PRICE_SPIKE]).toBe(0);
      expect(stats.issuesByType[IssueType.VOLUME_ANOMALY]).toBe(0);
      expect(stats.issuesByType[IssueType.TIMESTAMP_GAP]).toBe(0);
      expect(stats.issuesByType[IssueType.OHLC_INCONSISTENCY]).toBe(0);
      expect(stats.issuesByType[IssueType.DUPLICATE_DATA]).toBe(0);
      expect(stats.issuesByType[IssueType.STALE_DATA]).toBe(0);
      expect(stats.issuesByType[IssueType.SPREAD_ANOMALY]).toBe(0);
    });
  });

  describe('Data Consistency Checks', () => {
    it('should detect OHLC inconsistency', async () => {
      const testData = createTestMarketData({
        open: new Decimal('1.08500'),
        high: new Decimal('1.08400'), // High < Open (invalid)
        low: new Decimal('1.08600'),  // Low > Open (invalid)
        close: new Decimal('1.08550'),
      });

      const issues = await monitor.monitorDataQuality(testData);
      
      expect(issues).toHaveLength(1);
      expect(issues[0].type).toBe(IssueType.OHLC_INCONSISTENCY);
      expect(issues[0].severity).toBe(IssueSeverity.HIGH);
      expect(issues[0].suggestedRepair?.type).toBe(RepairActionType.AUTO_CORRECT);
      expect(issues[0].suggestedRepair?.automaticRepair).toBe(true);
    });

    it('should detect spread anomalies', async () => {
      const testData = createTestMarketData({
        close: new Decimal('1.08500'),
        spread: new Decimal('0.02000'), // 1.8% spread (above 1% threshold)
        bid: new Decimal('1.07500'),
        ask: new Decimal('1.09500'),
      });

      const issues = await monitor.monitorDataQuality(testData);
      
      expect(issues.some(issue => issue.type === IssueType.SPREAD_ANOMALY)).toBe(true);
      const spreadIssue = issues.find(issue => issue.type === IssueType.SPREAD_ANOMALY);
      expect(spreadIssue?.severity).toBe(IssueSeverity.MEDIUM);
      expect(spreadIssue?.suggestedRepair?.type).toBe(RepairActionType.USE_FALLBACK_SOURCE);
    });

    it('should not detect issues with valid OHLC data', async () => {
      const testData = createTestMarketData({
        open: new Decimal('1.08500'),
        high: new Decimal('1.08650'),
        low: new Decimal('1.08450'),
        close: new Decimal('1.08600'),
      });

      const issues = await monitor.monitorDataQuality(testData);
      
      const ohlcIssues = issues.filter(issue => issue.type === IssueType.OHLC_INCONSISTENCY);
      expect(ohlcIssues).toHaveLength(0);
    });
  });

  describe('Stale Data Detection', () => {
    it('should detect stale data', async () => {
      const staleTimestamp = new Date(Date.now() - 600000); // 10 minutes ago
      const testData = createTestMarketData({
        timestamp: staleTimestamp,
      });

      const issues = await monitor.monitorDataQuality(testData);
      
      const staleIssues = issues.filter(issue => issue.type === IssueType.STALE_DATA);
      expect(staleIssues).toHaveLength(1);
      expect(staleIssues[0].severity).toBe(IssueSeverity.MEDIUM); // 600s is 2x threshold (300s)
      expect(staleIssues[0].suggestedRepair?.type).toBe(RepairActionType.USE_FALLBACK_SOURCE);
    });

    it('should not detect stale data for recent timestamps', async () => {
      const recentTimestamp = new Date(Date.now() - 30000); // 30 seconds ago
      const testData = createTestMarketData({
        timestamp: recentTimestamp,
      });

      const issues = await monitor.monitorDataQuality(testData);
      
      const staleIssues = issues.filter(issue => issue.type === IssueType.STALE_DATA);
      expect(staleIssues).toHaveLength(0);
    });
  });

  describe('Gap Detection', () => {
    it('should detect timestamp gaps', async () => {
      const now = Date.now();
      
      // First data point
      const firstData = createTestMarketData({
        timestamp: new Date(now - 240000), // 240s = 4 minutes ago (4x threshold < 5x threshold)
      });
      await monitor.monitorDataQuality(firstData);

      // Second data point with large gap
      const secondData = createTestMarketData({
        timestamp: new Date(now), // Now (240 second gap > 60 second threshold, but < 300s)
      });
      const issues = await monitor.monitorDataQuality(secondData);

      const gapIssues = issues.filter(issue => issue.type === IssueType.TIMESTAMP_GAP);
      expect(gapIssues).toHaveLength(1);
      expect(gapIssues[0].severity).toBe(IssueSeverity.MEDIUM); // 240s is 4x threshold (60s), less than 5x
      expect(gapIssues[0].suggestedRepair?.type).toBe(RepairActionType.INTERPOLATE);
    });

    it('should not detect gaps for normal data frequency', async () => {
      // First data point
      const firstData = createTestMarketData({
        timestamp: new Date(Date.now() - 30000), // 30 seconds ago
      });
      await monitor.monitorDataQuality(firstData);

      // Second data point with normal gap
      const secondData = createTestMarketData({
        timestamp: new Date(), // Now (30 second gap < 60 second threshold)
      });
      const issues = await monitor.monitorDataQuality(secondData);

      const gapIssues = issues.filter(issue => issue.type === IssueType.TIMESTAMP_GAP);
      expect(gapIssues).toHaveLength(0);
    });
  });

  describe('Anomaly Detection', () => {
    it('should detect price spikes', async () => {
      // Build historical data with consistent prices around 1.085
      for (let i = 0; i < 10; i++) {
        const historicalData = createTestMarketData({
          close: new Decimal('1.08500'),
          timestamp: new Date(Date.now() - (10 - i) * 10000), // 10-100 seconds ago (not stale)
        });
        await monitor.monitorDataQuality(historicalData);
      }

      // Add data with price spike (20% higher)
      const spikeData = createTestMarketData({
        open: new Decimal('1.30000'),
        high: new Decimal('1.30500'),
        low: new Decimal('1.29500'),
        close: new Decimal('1.30200'), // 20% spike with valid OHLC
        timestamp: new Date(),
      });
      const issues = await monitor.monitorDataQuality(spikeData);

      const spikeIssues = issues.filter(issue => issue.type === IssueType.PRICE_SPIKE);
      expect(spikeIssues).toHaveLength(1);
      expect(spikeIssues[0].severity).toBe(IssueSeverity.HIGH); // > 10% deviation
      expect(spikeIssues[0].suggestedRepair?.type).toBe(RepairActionType.USE_FALLBACK_SOURCE);
    });

    it('should detect volume anomalies', async () => {
      // Build historical data with consistent volume around 1000
      for (let i = 0; i < 10; i++) {
        const historicalData = createTestMarketData({
          volume: new Decimal('1000'),
          timestamp: new Date(Date.now() - (10 - i) * 10000), // 10-100 seconds ago (not stale)
        });
        await monitor.monitorDataQuality(historicalData);
      }

      // Add data with volume anomaly (50x higher)
      const anomalyData = createTestMarketData({
        volume: new Decimal('50000'), // 50x volume (well above 10x threshold)
        timestamp: new Date(),
      });
      const issues = await monitor.monitorDataQuality(anomalyData);

      const volumeIssues = issues.filter(issue => issue.type === IssueType.VOLUME_ANOMALY);
      expect(volumeIssues).toHaveLength(1);
      expect(volumeIssues[0].severity).toBe(IssueSeverity.MEDIUM);
      expect(volumeIssues[0].suggestedRepair?.type).toBe(RepairActionType.MANUAL_REVIEW);
    });

    it('should not detect anomalies with insufficient historical data', async () => {
      // Only one previous data point (need 10+ for anomaly detection)
      const firstData = createTestMarketData({
        close: new Decimal('1.08500'),
        timestamp: new Date(Date.now() - 60000),
      });
      await monitor.monitorDataQuality(firstData);

      // Potential spike data
      const spikeData = createTestMarketData({
        close: new Decimal('1.30200'), // Would be 20% spike
        timestamp: new Date(),
      });
      const issues = await monitor.monitorDataQuality(spikeData);

      const anomalyIssues = issues.filter(issue => 
        issue.type === IssueType.PRICE_SPIKE || issue.type === IssueType.VOLUME_ANOMALY
      );
      expect(anomalyIssues).toHaveLength(0);
    });
  });

  describe('Duplicate Detection', () => {
    it('should detect duplicate data', async () => {
      const timestamp = new Date();
      const firstData = createTestMarketData({
        timestamp,
        close: new Decimal('1.08500'),
        volume: new Decimal('1000'),
      });
      await monitor.monitorDataQuality(firstData);

      // Exact duplicate
      const duplicateData = createTestMarketData({
        timestamp,
        close: new Decimal('1.08500'),
        volume: new Decimal('1000'),
      });
      const issues = await monitor.monitorDataQuality(duplicateData);

      const duplicateIssues = issues.filter(issue => issue.type === IssueType.DUPLICATE_DATA);
      expect(duplicateIssues).toHaveLength(1);
      expect(duplicateIssues[0].severity).toBe(IssueSeverity.MEDIUM);
      expect(duplicateIssues[0].suggestedRepair?.type).toBe(RepairActionType.IGNORE);
      expect(duplicateIssues[0].suggestedRepair?.automaticRepair).toBe(true);
    });

    it('should not detect duplicates for similar but different data', async () => {
      const timestamp = new Date();
      const firstData = createTestMarketData({
        timestamp,
        close: new Decimal('1.08500'),
        volume: new Decimal('1000'),
      });
      await monitor.monitorDataQuality(firstData);

      // Similar but different data
      const similarData = createTestMarketData({
        timestamp: new Date(timestamp.getTime() + 1), // Different timestamp
        close: new Decimal('1.08500'),
        volume: new Decimal('1000'),
      });
      const issues = await monitor.monitorDataQuality(similarData);

      const duplicateIssues = issues.filter(issue => issue.type === IssueType.DUPLICATE_DATA);
      expect(duplicateIssues).toHaveLength(0);
    });
  });

  describe('Automatic Repair', () => {
    it('should perform automatic repair when enabled and confidence is high', async () => {
      const repairSpy = vi.fn();
      monitor.on('automatic_repair_performed', repairSpy);

      const testData = createTestMarketData({
        open: new Decimal('1.08500'),
        high: new Decimal('1.08400'), // High < Open (will trigger auto-repair)
        low: new Decimal('1.08450'),
        close: new Decimal('1.08550'),
      });

      await monitor.monitorDataQuality(testData);

      expect(repairSpy).toHaveBeenCalled();
      const stats = monitor.getStats();
      expect(stats.autoRepairsPerformed).toBe(1);
    });

    it('should not perform automatic repair when confidence is below threshold', async () => {
      const repairSpy = vi.fn();
      const manualReviewSpy = vi.fn();
      monitor.on('automatic_repair_performed', repairSpy);
      monitor.on('manual_review_required', manualReviewSpy);

      // Build historical data for anomaly detection
      for (let i = 0; i < 10; i++) {
        const historicalData = createTestMarketData({
          close: new Decimal('1.08500'),
          timestamp: new Date(Date.now() - (10 - i) * 10000), // 10-100 seconds ago (not stale)
        });
        await monitor.monitorDataQuality(historicalData);
      }

      // Price spike with low confidence repair (60% < 80% threshold)
      const spikeData = createTestMarketData({
        open: new Decimal('1.30000'),
        high: new Decimal('1.30500'),
        low: new Decimal('1.29500'),
        close: new Decimal('1.30200'), // Valid OHLC to avoid automatic repair
        timestamp: new Date(),
      });
      await monitor.monitorDataQuality(spikeData);

      expect(repairSpy).not.toHaveBeenCalled();
      expect(manualReviewSpy).toHaveBeenCalled();
      
      const stats = monitor.getStats();
      expect(stats.autoRepairsPerformed).toBe(0);
      expect(stats.manualReviewRequired).toBeGreaterThan(0);
    });

    it('should not perform automatic repair when disabled', async () => {
      monitor.updateConfig({ enableAutoRepair: false });
      
      const repairSpy = vi.fn();
      const manualReviewSpy = vi.fn();
      monitor.on('automatic_repair_performed', repairSpy);
      monitor.on('manual_review_required', manualReviewSpy);

      const testData = createTestMarketData({
        open: new Decimal('1.08500'),
        high: new Decimal('1.08400'), // High < Open
        low: new Decimal('1.08450'),
        close: new Decimal('1.08550'),
      });

      await monitor.monitorDataQuality(testData);

      expect(repairSpy).not.toHaveBeenCalled();
      expect(manualReviewSpy).toHaveBeenCalled();
    });
  });

  describe('Statistics Tracking', () => {
    it('should track processing statistics', async () => {
      const testData1 = createTestMarketData();
      const testData2 = createTestMarketData({ 
        high: new Decimal('1.08400'), // Will cause OHLC issue
        low: new Decimal('1.08600'),
      });

      await monitor.monitorDataQuality(testData1);
      await monitor.monitorDataQuality(testData2);

      const stats = monitor.getStats();
      expect(stats.totalDataPointsProcessed).toBe(2);
      expect(stats.totalIssuesDetected).toBeGreaterThanOrEqual(1);
      expect(stats.issuesByType[IssueType.OHLC_INCONSISTENCY]).toBeGreaterThanOrEqual(1);
      expect(stats.issuesBySeverity[IssueSeverity.HIGH]).toBeGreaterThanOrEqual(1);
      expect(stats.averageProcessingTimeMs).toBeGreaterThan(0);
      expect(stats.overallQualityScore).toBeLessThan(100);
    });

    it('should update quality score based on issue rate', async () => {
      // Process some clean data
      for (let i = 0; i < 8; i++) {
        const cleanData = createTestMarketData();
        await monitor.monitorDataQuality(cleanData);
      }

      // Process some problematic data
      for (let i = 0; i < 2; i++) {
        const problemData = createTestMarketData({
          high: new Decimal('1.08400'), // Will cause OHLC issue
          low: new Decimal('1.08600'),
        });
        await monitor.monitorDataQuality(problemData);
      }

      const stats = monitor.getStats();
      expect(stats.totalDataPointsProcessed).toBe(10);
      expect(stats.totalIssuesDetected).toBeGreaterThanOrEqual(2);
      expect(stats.overallQualityScore).toBeLessThan(100); // Should be reduced due to issues
    });
  });

  describe('Issue Filtering', () => {
    beforeEach(async () => {
      // Add various types of issues
      const ohlcIssue = createTestMarketData({
        instrument: 'EURUSD',
        source: DataSource.MT5,
        high: new Decimal('1.08400'),
        low: new Decimal('1.08600'),
      });
      await monitor.monitorDataQuality(ohlcIssue);

      const staleIssue = createTestMarketData({
        instrument: 'GBPUSD',
        source: DataSource.ALPHA_VANTAGE,
        timestamp: new Date(Date.now() - 600000),
      });
      await monitor.monitorDataQuality(staleIssue);
    });

    it('should filter issues by type', () => {
      const ohlcIssues = monitor.getIssues({ type: IssueType.OHLC_INCONSISTENCY });
      expect(ohlcIssues).toHaveLength(1);
      expect(ohlcIssues[0].type).toBe(IssueType.OHLC_INCONSISTENCY);

      const staleIssues = monitor.getIssues({ type: IssueType.STALE_DATA });
      expect(staleIssues).toHaveLength(1);
      expect(staleIssues[0].type).toBe(IssueType.STALE_DATA);
    });

    it('should filter issues by severity', () => {
      const highSeverityIssues = monitor.getIssues({ severity: IssueSeverity.HIGH });
      expect(highSeverityIssues).toHaveLength(1); // Only OHLC issue is high severity
      expect(highSeverityIssues.every(issue => issue.severity === IssueSeverity.HIGH)).toBe(true);
      
      const mediumSeverityIssues = monitor.getIssues({ severity: IssueSeverity.MEDIUM });
      expect(mediumSeverityIssues).toHaveLength(1); // Stale data is medium severity
      expect(mediumSeverityIssues.every(issue => issue.severity === IssueSeverity.MEDIUM)).toBe(true);
    });

    it('should filter issues by instrument', () => {
      const eurusdIssues = monitor.getIssues({ instrument: 'EURUSD' });
      expect(eurusdIssues).toHaveLength(1);
      expect(eurusdIssues[0].instrument).toBe('EURUSD');

      const gbpusdIssues = monitor.getIssues({ instrument: 'GBPUSD' });
      expect(gbpusdIssues).toHaveLength(1);
      expect(gbpusdIssues[0].instrument).toBe('GBPUSD');
    });

    it('should filter issues by source', () => {
      const mt5Issues = monitor.getIssues({ source: DataSource.MT5 });
      expect(mt5Issues).toHaveLength(1);
      expect(mt5Issues[0].source).toBe(DataSource.MT5);

      const avIssues = monitor.getIssues({ source: DataSource.ALPHA_VANTAGE });
      expect(avIssues).toHaveLength(1);
      expect(avIssues[0].source).toBe(DataSource.ALPHA_VANTAGE);
    });

    it('should filter issues by timestamp', () => {
      const recentIssues = monitor.getIssues({ 
        since: new Date(Date.now() - 60000) // Last minute
      });
      expect(recentIssues).toHaveLength(2); // Both issues should be recent

      const veryRecentIssues = monitor.getIssues({ 
        since: new Date() // Right now
      });
      expect(veryRecentIssues).toHaveLength(2); // Both should still be included
    });
  });

  describe('Configuration Management', () => {
    it('should update configuration', () => {
      const configSpy = vi.fn();
      monitor.on('config_updated', configSpy);

      const newConfig = {
        maxPriceDeviationPercent: 10,
        enableAutoRepair: false,
      };

      monitor.updateConfig(newConfig);

      expect(configSpy).toHaveBeenCalledWith(
        expect.objectContaining(newConfig)
      );
    });
  });

  describe('Health Check', () => {
    it('should report healthy status when quality is good', async () => {
      // Clear any previous state first
      monitor.clearHistory();
      monitor.resetStats();
      
      // Process some clean data with proper timestamps to avoid issues
      for (let i = 0; i < 10; i++) {
        const cleanData = createTestMarketData({
          timestamp: new Date(Date.now() - (9 - i) * 5000), // 5-45 seconds ago, well spaced
          open: new Decimal('1.08500'),
          high: new Decimal('1.08650'), // Valid: high > open, close, low
          low: new Decimal('1.08450'),  // Valid: low < open, close, high
          close: new Decimal('1.08600'), // Valid: open < close < high, close > low
        });
        await monitor.monitorDataQuality(cleanData);
      }

      const health = monitor.healthCheck();
      const stats = monitor.getStats();

      // Debug output to understand what's happening
      console.log('Health check result:', {
        isHealthy: health.isHealthy,
        issues: health.issues,
        qualityScore: stats.overallQualityScore,
        totalIssues: stats.totalIssuesDetected,
        totalProcessed: stats.totalDataPointsProcessed,
        manualReviewRate: stats.totalDataPointsProcessed > 0 ? stats.manualReviewRequired / stats.totalDataPointsProcessed : 0,
        issuesByType: stats.issuesByType,
        averageProcessingTime: stats.averageProcessingTimeMs
      });

      expect(health.isHealthy).toBe(true);
      expect(health.issues).toHaveLength(0);
      expect(health.stats.overallQualityScore).toBe(100);
    });

    it('should report unhealthy status when quality is poor', async () => {
      // Clear any previous state first
      monitor.clearHistory();
      monitor.resetStats();
      
      // Disable auto repair to ensure issues are counted properly
      monitor.updateConfig({ enableAutoRepair: false });
      
      // Process mostly problematic data
      for (let i = 0; i < 10; i++) {
        const problemData = createTestMarketData({
          timestamp: new Date(Date.now() - (9 - i) * 5000), // Well spaced timestamps
          open: new Decimal('1.08500'),
          high: new Decimal('1.08400'), // OHLC issue: high < open
          low: new Decimal('1.08600'),  // OHLC issue: low > open
          close: new Decimal('1.08550'),
        });
        
        const issues = await monitor.monitorDataQuality(problemData);
        console.log(`Data point ${i + 1} detected ${issues.length} issues:`, issues.map(i => i.type));
      }

      const health = monitor.healthCheck();
      const stats = monitor.getStats();

      // Debug output
      console.log('Poor quality health check:', {
        isHealthy: health.isHealthy,
        issues: health.issues,
        qualityScore: stats.overallQualityScore,
        totalIssues: stats.totalIssuesDetected,
        totalProcessed: stats.totalDataPointsProcessed,
        issuesByType: stats.issuesByType,
        autoRepairsPerformed: stats.autoRepairsPerformed,
        manualReviewRequired: stats.manualReviewRequired
      });

      expect(health.isHealthy).toBe(false);
      expect(health.stats.totalIssuesDetected).toBeGreaterThan(0);
      expect(health.stats.overallQualityScore).toBeLessThan(70);
      // Check the exact string that was generated
      const qualityIssue = health.issues.find(issue => issue.includes('quality score'));
      expect(qualityIssue).toBeDefined();
      expect(qualityIssue).toContain('Low overall quality score');
    });

    it('should report issues when too many manual reviews required', async () => {
      // Clear any previous state first
      monitor.clearHistory();
      monitor.resetStats();
      
      // Disable auto repair to force manual reviews
      monitor.updateConfig({ enableAutoRepair: false });

      // Process data that creates issues requiring manual review
      for (let i = 0; i < 10; i++) {
        const problemData = createTestMarketData({
          timestamp: new Date(Date.now() - (9 - i) * 5000), // Well spaced timestamps
          open: new Decimal('1.08500'),
          high: new Decimal('1.08400'), // OHLC issue: high < open
          low: new Decimal('1.08600'),  // OHLC issue: low > open
          close: new Decimal('1.08550'),
        });
        await monitor.monitorDataQuality(problemData);
      }

      const health = monitor.healthCheck();
      const stats = monitor.getStats();

      // Debug output
      console.log('Manual review health check:', {
        isHealthy: health.isHealthy,
        issues: health.issues,
        qualityScore: stats.overallQualityScore,
        totalIssues: stats.totalIssuesDetected,
        totalProcessed: stats.totalDataPointsProcessed,
        manualReviewRequired: stats.manualReviewRequired,
        manualReviewRate: stats.totalDataPointsProcessed > 0 ? stats.manualReviewRequired / stats.totalDataPointsProcessed : 0,
        autoRepairsPerformed: stats.autoRepairsPerformed
      });

      expect(health.isHealthy).toBe(false);
      // Check the exact string that was generated
      const manualReviewIssue = health.issues.find(issue => issue.includes('manual review'));
      expect(manualReviewIssue).toBeDefined();
      expect(manualReviewIssue).toContain('High manual review rate');
    });
  });

  describe('Event Handling', () => {
    it('should emit quality_issue_detected events', async () => {
      const issueSpy = vi.fn();
      monitor.on('quality_issue_detected', issueSpy);

      const testData = createTestMarketData({
        high: new Decimal('1.08400'),
        low: new Decimal('1.08600'),
      });

      await monitor.monitorDataQuality(testData);

      expect(issueSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: IssueType.OHLC_INCONSISTENCY,
          severity: IssueSeverity.HIGH,
        })
      );
    });

    it('should emit quality_check_completed events', async () => {
      const completedSpy = vi.fn();
      monitor.on('quality_check_completed', completedSpy);

      const testData = createTestMarketData();
      await monitor.monitorDataQuality(testData);

      expect(completedSpy).toHaveBeenCalledWith({
        data: testData,
        issues: expect.any(Array),
        processingTimeMs: expect.any(Number),
      });
    });
  });

  describe('Memory Management', () => {
    it('should limit historical data storage', async () => {
      // Add more than 100 data points
      for (let i = 0; i < 120; i++) {
        const testData = createTestMarketData({
          timestamp: new Date(Date.now() - i * 60000),
        });
        await monitor.monitorDataQuality(testData);
      }

      // Historical data should be limited (we can't directly access it, but we test indirectly)
      // The fact that the monitor still functions normally indicates memory management is working
      const stats = monitor.getStats();
      expect(stats.totalDataPointsProcessed).toBe(120);
    });

    it('should clear history when requested', async () => {
      const historyClearedSpy = vi.fn();
      monitor.on('history_cleared', historyClearedSpy);

      // Add some data and issues
      const testData = createTestMarketData({
        high: new Decimal('1.08400'),
        low: new Decimal('1.08600'),
      });
      await monitor.monitorDataQuality(testData);

      expect(monitor.getIssues()).toHaveLength(1);

      monitor.clearHistory();

      expect(monitor.getIssues()).toHaveLength(0);
      expect(historyClearedSpy).toHaveBeenCalled();
    });

    it('should cleanup resources on shutdown', () => {
      expect(() => monitor.shutdown()).not.toThrow();
      
      // Should not have any listeners after shutdown
      expect(monitor.listenerCount('quality_issue_detected')).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle errors gracefully during monitoring', async () => {
      const errorSpy = vi.fn();
      monitor.on('monitoring_error', errorSpy);

      // Create invalid test data that might cause errors
      const invalidData = {
        ...createTestMarketData(),
        close: null as any, // Invalid data
      };

      await expect(monitor.monitorDataQuality(invalidData)).rejects.toThrow();
      expect(errorSpy).toHaveBeenCalled();
    });

    it('should handle repair errors gracefully', async () => {
      const errorSpy = vi.fn();
      monitor.on('repair_error', errorSpy);

      // This test is more for coverage - repair errors are handled internally
      expect(() => {
        const testData = createTestMarketData({
          high: new Decimal('1.08400'),
          low: new Decimal('1.08600'),
        });
        monitor.monitorDataQuality(testData);
      }).not.toThrow();
    });
  });
});