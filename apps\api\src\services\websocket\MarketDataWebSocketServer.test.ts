/**
 * Unit tests for MarketDataWebSocketServer
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import Decimal from 'decimal.js';
import {
  MarketDataWebSocketServer,
  WebSocketServerConfig,
  PriceStreamMessage,
  SubscribeMessage,
} from './MarketDataWebSocketServer';
import {
  NormalizedMarketData,
  DataSource,
  TimeFrame,
} from '../market-data/RealTimeDataProcessor';

describe('MarketDataWebSocketServer', () => {
  let server: MarketDataWebSocketServer;
  let config: WebSocketServerConfig;

  beforeEach(() => {
    config = {
      port: 8080,
      jwtSecret: 'test-secret-key',
      maxConnections: 100,
      heartbeatInterval: 30000,
      clientTimeout: 60000,
      maxInstrumentsPerClient: 50,
      defaultUpdateInterval: 1000,
      rateLimitPerSecond: 10,
      enableCompression: false,
      enableBatching: false,
      allowedOrigins: ['http://localhost:3000'],
    };

    server = new MarketDataWebSocketServer(config);
  });

  afterEach(() => {
    server.shutdown();
  });

  // Helper function to create test market data
  const createTestMarketData = (overrides: Partial<NormalizedMarketData> = {}): NormalizedMarketData => ({
    id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    source: DataSource.MT5,
    instrument: 'EURUSD',
    timeframe: TimeFrame.M1,
    timestamp: new Date(),
    open: new Decimal('1.08500'),
    high: new Decimal('1.08650'),
    low: new Decimal('1.08450'),
    close: new Decimal('1.08600'),
    volume: new Decimal('1000'),
    bid: new Decimal('1.08598'),
    ask: new Decimal('1.08602'),
    spread: new Decimal('0.00004'),
    precision: 5,
    timezone: 'UTC',
    isCompressed: false,
    qualityScore: 100,
    originalPayloadSize: 500,
    processedPayloadSize: 500,
    processingTimeMs: 10,
    ...overrides,
  });

  describe('Initialization', () => {
    it('should create server with correct configuration', () => {
      expect(server).toBeDefined();
      expect(server).toBeInstanceOf(MarketDataWebSocketServer);
    });

    it('should have initial performance metrics', () => {
      const stats = server.getConnectionStats();
      
      expect(stats.totalConnections).toBe(0);
      expect(stats.activeConnections).toBe(0);
      expect(stats.connectedInstruments).toHaveLength(0);
      expect(stats.clientDetails).toHaveLength(0);
      expect(stats.performanceMetrics.totalConnections).toBe(0);
      expect(stats.performanceMetrics.activeConnections).toBe(0);
      expect(stats.performanceMetrics.totalMessagesReceived).toBe(0);
      expect(stats.performanceMetrics.totalMessagesSent).toBe(0);
      expect(stats.performanceMetrics.errorCount).toBe(0);
      expect(stats.performanceMetrics.startTime).toBeInstanceOf(Date);
    });
  });

  describe('Configuration Management', () => {
    it('should use provided configuration values', () => {
      const customConfig = {
        port: 9090,
        jwtSecret: 'custom-secret',
        maxConnections: 50,
        heartbeatInterval: 15000,
        clientTimeout: 30000,
        maxInstrumentsPerClient: 25,
        defaultUpdateInterval: 500,
        rateLimitPerSecond: 20,
        enableCompression: true,
        enableBatching: true,
      };

      const customServer = new MarketDataWebSocketServer(customConfig);
      expect(customServer).toBeDefined();
      customServer.shutdown();
    });

    it('should handle allowed origins configuration', () => {
      const configWithOrigins = {
        ...config,
        allowedOrigins: ['https://app.golddaddy.com', 'https://dashboard.golddaddy.com'],
      };

      const serverWithOrigins = new MarketDataWebSocketServer(configWithOrigins);
      expect(serverWithOrigins).toBeDefined();
      serverWithOrigins.shutdown();
    });
  });

  describe('Health Check', () => {
    it('should report healthy status when no issues or handle startup warning', () => {
      const health = server.healthCheck();
      
      expect(health.stats).toBeDefined();
      expect(health.stats.totalConnections).toBe(0);
      expect(health.stats.activeConnections).toBe(0);
      
      // Server might report unhealthy due to "recently started" warning
      if (!health.isHealthy) {
        expect(health.issues.some(issue => issue.includes('recently started'))).toBe(true);
      } else {
        expect(health.issues).toHaveLength(0);
      }
    });

    it('should include performance metrics in health check', () => {
      const health = server.healthCheck();
      
      expect(health.stats.performanceMetrics).toBeDefined();
      expect(health.stats.performanceMetrics.errorCount).toBe(0);
      expect(health.stats.performanceMetrics.startTime).toBeInstanceOf(Date);
    });

    it('should detect potential issues based on metrics', () => {
      const health = server.healthCheck();
      
      // With a fresh server, uptime might trigger a warning
      const uptimeWarning = health.issues.some(issue => issue.includes('recently started'));
      if (uptimeWarning) {
        expect(health.isHealthy).toBe(false);
      }
    });
  });

  describe('Market Data Processing', () => {
    it('should accept market data for broadcasting', () => {
      const testData = createTestMarketData();
      
      // Should not throw when broadcasting data (even with no clients)
      expect(() => server.broadcastMarketData(testData)).not.toThrow();
    });

    it('should handle different data sources', () => {
      const mt5Data = createTestMarketData({ source: DataSource.MT5 });
      const avData = createTestMarketData({ source: DataSource.ALPHA_VANTAGE });
      const yahooData = createTestMarketData({ source: DataSource.YAHOO_FINANCE });
      
      expect(() => server.broadcastMarketData(mt5Data)).not.toThrow();
      expect(() => server.broadcastMarketData(avData)).not.toThrow();
      expect(() => server.broadcastMarketData(yahooData)).not.toThrow();
    });

    it('should handle different timeframes', () => {
      const m1Data = createTestMarketData({ timeframe: TimeFrame.M1 });
      const m5Data = createTestMarketData({ timeframe: TimeFrame.M5 });
      const h1Data = createTestMarketData({ timeframe: TimeFrame.H1 });
      const d1Data = createTestMarketData({ timeframe: TimeFrame.D1 });
      
      expect(() => server.broadcastMarketData(m1Data)).not.toThrow();
      expect(() => server.broadcastMarketData(m5Data)).not.toThrow();
      expect(() => server.broadcastMarketData(h1Data)).not.toThrow();
      expect(() => server.broadcastMarketData(d1Data)).not.toThrow();
    });

    it('should handle market data with missing optional fields', () => {
      const minimalData = createTestMarketData({
        bid: undefined,
        ask: undefined,
        spread: undefined,
      });
      
      expect(() => server.broadcastMarketData(minimalData)).not.toThrow();
    });
  });

  describe('Message Interfaces', () => {
    it('should create valid PriceStreamMessage', () => {
      const testData = createTestMarketData();
      
      const priceMessage: PriceStreamMessage = {
        type: 'price_update',
        instrument: testData.instrument,
        price: {
          bid: testData.bid?.toNumber(),
          ask: testData.ask?.toNumber(),
          close: testData.close.toNumber(),
          open: testData.open.toNumber(),
          high: testData.high.toNumber(),
          low: testData.low.toNumber(),
          spread: testData.spread?.toNumber(),
        },
        timestamp: testData.timestamp,
        source: testData.source,
        timeframe: testData.timeframe,
        volume: testData.volume.toNumber(),
      };

      expect(priceMessage.type).toBe('price_update');
      expect(priceMessage.instrument).toBe('EURUSD');
      expect(priceMessage.price.close).toBe(1.08600);
      expect(priceMessage.source).toBe(DataSource.MT5);
      expect(priceMessage.timestamp).toBeInstanceOf(Date);
    });

    it('should create valid SubscribeMessage', () => {
      const subscribeMessage: SubscribeMessage = {
        type: 'subscribe',
        action: 'subscribe',
        instruments: ['EURUSD', 'GBPUSD', 'USDJPY'],
        features: {
          marketAnalysis: true,
          mlPredictions: false,
          volatilityIndicators: true,
          technicalIndicators: false,
        },
        timeframes: [TimeFrame.M1, TimeFrame.M5, TimeFrame.H1],
        updateInterval: 1000,
      };

      expect(subscribeMessage.type).toBe('subscribe');
      expect(subscribeMessage.action).toBe('subscribe');
      expect(subscribeMessage.instruments).toHaveLength(3);
      expect(subscribeMessage.instruments).toContain('EURUSD');
      expect(subscribeMessage.features?.marketAnalysis).toBe(true);
      expect(subscribeMessage.timeframes).toContain(TimeFrame.M1);
      expect(subscribeMessage.updateInterval).toBe(1000);
    });

    it('should create valid UnsubscribeMessage', () => {
      const unsubscribeMessage: SubscribeMessage = {
        type: 'unsubscribe',
        action: 'unsubscribe',
        instruments: ['EURUSD'],
      };

      expect(unsubscribeMessage.type).toBe('unsubscribe');
      expect(unsubscribeMessage.action).toBe('unsubscribe');
      expect(unsubscribeMessage.instruments).toHaveLength(1);
    });
  });

  describe('Performance Metrics', () => {
    it('should track connection statistics', () => {
      const stats = server.getConnectionStats();
      
      expect(stats).toHaveProperty('totalConnections');
      expect(stats).toHaveProperty('activeConnections');
      expect(stats).toHaveProperty('connectedInstruments');
      expect(stats).toHaveProperty('performanceMetrics');
      expect(stats).toHaveProperty('clientDetails');
      
      expect(stats.connectedInstruments).toBeInstanceOf(Array);
      expect(stats.clientDetails).toBeInstanceOf(Array);
    });

    it('should track performance metrics over time', () => {
      const initialStats = server.getConnectionStats();
      
      // Simulate some activity by broadcasting data
      const testData = createTestMarketData();
      server.broadcastMarketData(testData);
      
      const laterStats = server.getConnectionStats();
      
      // Performance metrics structure should remain consistent
      expect(laterStats.performanceMetrics.startTime).toEqual(initialStats.performanceMetrics.startTime);
    });
  });

  describe('Event Handling', () => {
    it('should be an EventEmitter', () => {
      expect(server.on).toBeTypeOf('function');
      expect(server.emit).toBeTypeOf('function');
      expect(server.removeAllListeners).toBeTypeOf('function');
    });

    it('should support event registration', () => {
      const testHandler = () => {};
      
      // Should not throw when adding event listeners
      expect(() => server.on('server_started', testHandler)).not.toThrow();
      expect(() => server.on('client_connected', testHandler)).not.toThrow();
      expect(() => server.on('client_disconnected', testHandler)).not.toThrow();
      expect(() => server.on('data_broadcast', testHandler)).not.toThrow();
    });
  });

  describe('Resource Management', () => {
    it('should shutdown gracefully', () => {
      expect(() => server.shutdown()).not.toThrow();
    });

    it('should clear event listeners on shutdown', () => {
      const testHandler = () => {};
      server.on('test_event', testHandler);
      
      server.shutdown();
      
      // Should have no listeners after shutdown
      expect(server.listenerCount('test_event')).toBe(0);
    });

    it('should handle multiple shutdowns gracefully', () => {
      server.shutdown();
      
      // Second shutdown should not throw
      expect(() => server.shutdown()).not.toThrow();
    });
  });

  describe('Data Validation', () => {
    it('should handle invalid market data gracefully', () => {
      // Should handle data with extreme values
      const extremeData = createTestMarketData({
        open: new Decimal('0.00001'),
        high: new Decimal('999999'),
        low: new Decimal('0'),
        close: new Decimal('1'),
      });
      
      expect(() => server.broadcastMarketData(extremeData)).not.toThrow();
    });

    it('should handle data with zero values', () => {
      const zeroData = createTestMarketData({
        volume: new Decimal('0'),
        spread: new Decimal('0'),
      });
      
      expect(() => server.broadcastMarketData(zeroData)).not.toThrow();
    });
  });

  describe('Configuration Edge Cases', () => {
    it('should handle minimum configuration values', () => {
      const minConfig = {
        ...config,
        maxConnections: 1,
        maxInstrumentsPerClient: 1,
        heartbeatInterval: 1000,
        clientTimeout: 2000,
        defaultUpdateInterval: 100,
        rateLimitPerSecond: 1,
      };
      
      const minServer = new MarketDataWebSocketServer(minConfig);
      expect(minServer).toBeDefined();
      minServer.shutdown();
    });

    it('should handle maximum configuration values', () => {
      const maxConfig = {
        ...config,
        maxConnections: 10000,
        maxInstrumentsPerClient: 1000,
        heartbeatInterval: 300000,
        clientTimeout: 600000,
        defaultUpdateInterval: 10000,
        rateLimitPerSecond: 1000,
      };
      
      const maxServer = new MarketDataWebSocketServer(maxConfig);
      expect(maxServer).toBeDefined();
      maxServer.shutdown();
    });
  });
});