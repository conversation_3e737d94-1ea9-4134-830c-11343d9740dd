import { EventEmitter } from 'events'
import { createClient } from '@supabase/supabase-js'
import { v4 as uuidv4 } from 'uuid'

export interface AuditLogEntry {
  id: string
  timestamp: Date
  userId?: string
  sessionId?: string
  eventType: AuditEventType
  category: AuditCategory
  action: string
  resource: string
  resourceId?: string
  details: Record<string, any>
  ipAddress: string
  userAgent: string
  outcome: 'success' | 'failure' | 'warning'
  sensitiveData: boolean
  complianceFlags: string[]
  retention: AuditRetention
  metadata: {
    requestId?: string
    correlationId?: string
    source: string
    version: string
  }
}

export enum AuditEventType {
  // Authentication events
  LOGIN_ATTEMPT = 'login_attempt',
  LOGIN_SUCCESS = 'login_success',
  LOGIN_FAILURE = 'login_failure',
  LOGOUT = 'logout',
  PASSWORD_CHANGE = 'password_change',
  PASSWORD_RESET = 'password_reset',
  MFA_ENABLED = 'mfa_enabled',
  MFA_DISABLED = 'mfa_disabled',
  ACCOUNT_LOCKED = 'account_locked',
  ACCOUNT_UNLOCKED = 'account_unlocked',

  // Account management
  ACCOUNT_CREATED = 'account_created',
  ACCOUNT_UPDATED = 'account_updated',
  ACCOUNT_DELETED = 'account_deleted',
  PROFILE_UPDATED = 'profile_updated',
  KYC_SUBMITTED = 'kyc_submitted',
  KYC_APPROVED = 'kyc_approved',
  KYC_REJECTED = 'kyc_rejected',

  // Trading activities
  ORDER_PLACED = 'order_placed',
  ORDER_MODIFIED = 'order_modified',
  ORDER_CANCELLED = 'order_cancelled',
  ORDER_EXECUTED = 'order_executed',
  POSITION_OPENED = 'position_opened',
  POSITION_CLOSED = 'position_closed',
  STOP_LOSS_TRIGGERED = 'stop_loss_triggered',
  TAKE_PROFIT_TRIGGERED = 'take_profit_triggered',

  // Financial transactions
  DEPOSIT_INITIATED = 'deposit_initiated',
  DEPOSIT_COMPLETED = 'deposit_completed',
  DEPOSIT_FAILED = 'deposit_failed',
  WITHDRAWAL_REQUESTED = 'withdrawal_requested',
  WITHDRAWAL_APPROVED = 'withdrawal_approved',
  WITHDRAWAL_REJECTED = 'withdrawal_rejected',
  WITHDRAWAL_COMPLETED = 'withdrawal_completed',

  // Data access and export
  DATA_EXPORTED = 'data_exported',
  DATA_IMPORTED = 'data_imported',
  DATA_VIEWED = 'data_viewed',
  DATA_UPDATED = 'data_updated',
  DATA_DELETED = 'data_deleted',
  GDPR_REQUEST = 'gdpr_request',
  DATA_RETENTION_APPLIED = 'data_retention_applied',

  // Administrative actions
  USER_CREATED = 'user_created',
  USER_UPDATED = 'user_updated',
  USER_DELETED = 'user_deleted',
  ROLE_ASSIGNED = 'role_assigned',
  ROLE_REMOVED = 'role_removed',
  PERMISSION_GRANTED = 'permission_granted',
  PERMISSION_REVOKED = 'permission_revoked',
  CONFIG_CHANGED = 'config_changed',

  // Security events
  SUSPICIOUS_ACTIVITY = 'suspicious_activity',
  SECURITY_ALERT = 'security_alert',
  IP_BLOCKED = 'ip_blocked',
  RATE_LIMIT_EXCEEDED = 'rate_limit_exceeded',
  UNAUTHORIZED_ACCESS = 'unauthorized_access',
  ENCRYPTION_KEY_ROTATED = 'encryption_key_rotated',

  // System events
  SYSTEM_START = 'system_start',
  SYSTEM_SHUTDOWN = 'system_shutdown',
  BACKUP_CREATED = 'backup_created',
  BACKUP_RESTORED = 'backup_restored',
  MAINTENANCE_START = 'maintenance_start',
  MAINTENANCE_END = 'maintenance_end'
}

export enum AuditCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  DATA_ACCESS = 'data_access',
  TRADING = 'trading',
  FINANCIAL = 'financial',
  COMPLIANCE = 'compliance',
  SECURITY = 'security',
  ADMINISTRATION = 'administration',
  SYSTEM = 'system',
  PRIVACY = 'privacy'
}

export enum AuditRetention {
  SHORT_TERM = 'short_term',    // 1 year
  MEDIUM_TERM = 'medium_term',  // 3 years
  LONG_TERM = 'long_term',      // 7 years
  PERMANENT = 'permanent'       // Never delete
}

interface AuditSearchFilters {
  userId?: string
  eventType?: AuditEventType[]
  category?: AuditCategory[]
  dateRange?: {
    start: Date
    end: Date
  }
  ipAddress?: string
  outcome?: ('success' | 'failure' | 'warning')[]
  sensitiveData?: boolean
  complianceFlags?: string[]
  searchText?: string
}

interface AuditStatistics {
  totalEvents: number
  eventsByType: Record<string, number>
  eventsByCategory: Record<string, number>
  eventsByOutcome: Record<string, number>
  uniqueUsers: number
  suspiciousActivityCount: number
  complianceViolations: number
  recentActivity: AuditLogEntry[]
}

export class AuditLoggingService extends EventEmitter {
  private supabase = createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  )

  private retentionPolicies: Record<AuditRetention, number> = {
    [AuditRetention.SHORT_TERM]: 365 * 24 * 60 * 60 * 1000, // 1 year
    [AuditRetention.MEDIUM_TERM]: 3 * 365 * 24 * 60 * 60 * 1000, // 3 years
    [AuditRetention.LONG_TERM]: 7 * 365 * 24 * 60 * 60 * 1000, // 7 years
    [AuditRetention.PERMANENT]: Infinity
  }

  private complianceRules = new Map<AuditEventType, {
    retention: AuditRetention
    sensitiveData: boolean
    complianceFlags: string[]
  }>([
    // Financial regulations - 7 years retention
    [AuditEventType.ORDER_PLACED, { 
      retention: AuditRetention.LONG_TERM, 
      sensitiveData: true, 
      complianceFlags: ['MIFID_II', 'SOX', 'FINRA']
    }],
    [AuditEventType.ORDER_EXECUTED, { 
      retention: AuditRetention.LONG_TERM, 
      sensitiveData: true, 
      complianceFlags: ['MIFID_II', 'SOX', 'FINRA']
    }],
    [AuditEventType.DEPOSIT_COMPLETED, { 
      retention: AuditRetention.LONG_TERM, 
      sensitiveData: true, 
      complianceFlags: ['AML', 'KYC', 'FINRA']
    }],
    [AuditEventType.WITHDRAWAL_COMPLETED, { 
      retention: AuditRetention.LONG_TERM, 
      sensitiveData: true, 
      complianceFlags: ['AML', 'KYC', 'FINRA']
    }],

    // KYC/AML - 5-7 years retention
    [AuditEventType.KYC_SUBMITTED, { 
      retention: AuditRetention.LONG_TERM, 
      sensitiveData: true, 
      complianceFlags: ['AML', 'KYC', 'GDPR']
    }],
    [AuditEventType.KYC_APPROVED, { 
      retention: AuditRetention.LONG_TERM, 
      sensitiveData: true, 
      complianceFlags: ['AML', 'KYC']
    }],

    // Security events - Medium term retention
    [AuditEventType.LOGIN_SUCCESS, { 
      retention: AuditRetention.MEDIUM_TERM, 
      sensitiveData: false, 
      complianceFlags: ['SOX', 'PCI_DSS']
    }],
    [AuditEventType.LOGIN_FAILURE, { 
      retention: AuditRetention.MEDIUM_TERM, 
      sensitiveData: false, 
      complianceFlags: ['SOX', 'PCI_DSS']
    }],
    [AuditEventType.SUSPICIOUS_ACTIVITY, { 
      retention: AuditRetention.PERMANENT, 
      sensitiveData: true, 
      complianceFlags: ['AML', 'FINRA', 'SOX']
    }],

    // Data privacy - GDPR compliance
    [AuditEventType.GDPR_REQUEST, { 
      retention: AuditRetention.LONG_TERM, 
      sensitiveData: true, 
      complianceFlags: ['GDPR', 'CCPA']
    }],
    [AuditEventType.DATA_EXPORTED, { 
      retention: AuditRetention.LONG_TERM, 
      sensitiveData: true, 
      complianceFlags: ['GDPR', 'CCPA', 'SOX']
    }],
    [AuditEventType.DATA_DELETED, { 
      retention: AuditRetention.PERMANENT, 
      sensitiveData: true, 
      complianceFlags: ['GDPR', 'CCPA']
    }]
  ])

  async logEvent(
    eventType: AuditEventType,
    details: {
      userId?: string
      sessionId?: string
      action: string
      resource: string
      resourceId?: string
      details: Record<string, any>
      ipAddress: string
      userAgent: string
      outcome?: 'success' | 'failure' | 'warning'
      requestId?: string
      correlationId?: string
      source?: string
    }
  ): Promise<AuditLogEntry> {
    const category = this.getEventCategory(eventType)
    const complianceInfo = this.complianceRules.get(eventType) || {
      retention: AuditRetention.SHORT_TERM,
      sensitiveData: false,
      complianceFlags: []
    }

    const auditEntry: AuditLogEntry = {
      id: uuidv4(),
      timestamp: new Date(),
      userId: details.userId,
      sessionId: details.sessionId,
      eventType,
      category,
      action: details.action,
      resource: details.resource,
      resourceId: details.resourceId,
      details: this.sanitizeDetails(details.details, complianceInfo.sensitiveData),
      ipAddress: this.anonymizeIpAddress(details.ipAddress),
      userAgent: details.userAgent,
      outcome: details.outcome || 'success',
      sensitiveData: complianceInfo.sensitiveData,
      complianceFlags: complianceInfo.complianceFlags,
      retention: complianceInfo.retention,
      metadata: {
        requestId: details.requestId,
        correlationId: details.correlationId,
        source: details.source || 'api',
        version: '1.0'
      }
    }

    // Store in database
    const { error } = await this.supabase
      .from('audit_logs')
      .insert(auditEntry)

    if (error) {
      console.error('Failed to store audit log:', error)
      // Emit error event for monitoring
      this.emit('audit_log_error', { entry: auditEntry, error })
      throw new Error(`Audit log storage failed: ${error.message}`)
    }

    // Emit event for real-time monitoring
    this.emit('audit_log_created', auditEntry)

    // Check for suspicious patterns
    await this.checkSuspiciousActivity(auditEntry)

    return auditEntry
  }

  private getEventCategory(eventType: AuditEventType): AuditCategory {
    const categoryMap: Record<string, AuditCategory> = {
      'login_': AuditCategory.AUTHENTICATION,
      'logout': AuditCategory.AUTHENTICATION,
      'password_': AuditCategory.AUTHENTICATION,
      'mfa_': AuditCategory.AUTHENTICATION,
      'account_': AuditCategory.AUTHENTICATION,
      'kyc_': AuditCategory.COMPLIANCE,
      'order_': AuditCategory.TRADING,
      'position_': AuditCategory.TRADING,
      'stop_': AuditCategory.TRADING,
      'take_': AuditCategory.TRADING,
      'deposit_': AuditCategory.FINANCIAL,
      'withdrawal_': AuditCategory.FINANCIAL,
      'data_': AuditCategory.DATA_ACCESS,
      'gdpr_': AuditCategory.PRIVACY,
      'user_': AuditCategory.ADMINISTRATION,
      'role_': AuditCategory.AUTHORIZATION,
      'permission_': AuditCategory.AUTHORIZATION,
      'suspicious_': AuditCategory.SECURITY,
      'security_': AuditCategory.SECURITY,
      'ip_': AuditCategory.SECURITY,
      'rate_': AuditCategory.SECURITY,
      'system_': AuditCategory.SYSTEM,
      'backup_': AuditCategory.SYSTEM,
      'maintenance_': AuditCategory.SYSTEM
    }

    for (const [prefix, category] of Object.entries(categoryMap)) {
      if (eventType.startsWith(prefix)) {
        return category
      }
    }

    return AuditCategory.SYSTEM
  }

  private sanitizeDetails(details: Record<string, any>, isSensitive: boolean): Record<string, any> {
    if (!isSensitive) {
      return details
    }

    const sanitized = { ...details }
    const sensitiveFields = ['password', 'ssn', 'creditCard', 'bankAccount', 'apiKey', 'token']
    
    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '[REDACTED]'
      }
    }

    // Hash PII fields for compliance while maintaining searchability
    const piiFields = ['email', 'phone', 'address']
    for (const field of piiFields) {
      if (field in sanitized && typeof sanitized[field] === 'string') {
        sanitized[`${field}_hash`] = this.hashPii(sanitized[field])
        sanitized[field] = '[HASHED]'
      }
    }

    return sanitized
  }

  private hashPii(value: string): string {
    // Use a consistent hashing algorithm for PII
    // In production, use proper cryptographic hashing
    const crypto = require('crypto')
    return crypto.createHash('sha256')
      .update(value + process.env.PII_HASH_SALT)
      .digest('hex')
  }

  private anonymizeIpAddress(ipAddress: string): string {
    // Anonymize IP address for GDPR compliance
    const parts = ipAddress.split('.')
    if (parts.length === 4) {
      // IPv4: Replace last octet with 0
      return `${parts[0]}.${parts[1]}.${parts[2]}.0`
    }
    
    // IPv6: Replace last 64 bits with zeros
    if (ipAddress.includes(':')) {
      const segments = ipAddress.split(':')
      return `${segments.slice(0, 4).join(':')  }::0`
    }
    
    return ipAddress
  }

  private async checkSuspiciousActivity(entry: AuditLogEntry): Promise<void> {
    // Check for suspicious patterns based on the audit entry
    const suspiciousPatterns = [
      // Multiple failed logins
      {
        condition: entry.eventType === AuditEventType.LOGIN_FAILURE,
        check: () => this.checkRepeatedFailedLogins(entry)
      },
      // Unusual trading patterns
      {
        condition: entry.category === AuditCategory.TRADING,
        check: () => this.checkUnusualTradingActivity(entry)
      },
      // Data access from unusual location
      {
        condition: entry.category === AuditCategory.DATA_ACCESS,
        check: () => this.checkUnusualDataAccess(entry)
      },
      // High-value transactions
      {
        condition: entry.category === AuditCategory.FINANCIAL,
        check: () => this.checkHighValueTransactions(entry)
      }
    ]

    for (const pattern of suspiciousPatterns) {
      if (pattern.condition) {
        await pattern.check()
      }
    }
  }

  private async checkRepeatedFailedLogins(entry: AuditLogEntry): Promise<void> {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
    
    const { data: recentFailures } = await this.supabase
      .from('audit_logs')
      .select('id')
      .eq('event_type', AuditEventType.LOGIN_FAILURE)
      .eq('ip_address', entry.ipAddress)
      .gte('timestamp', fiveMinutesAgo.toISOString())

    if (recentFailures && recentFailures.length >= 5) {
      await this.logEvent(AuditEventType.SUSPICIOUS_ACTIVITY, {
        action: 'Multiple failed login attempts detected',
        resource: 'authentication',
        details: {
          pattern: 'repeated_login_failures',
          count: recentFailures.length,
          timeWindow: '5_minutes',
          ipAddress: entry.ipAddress
        },
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent,
        outcome: 'warning',
        correlationId: entry.metadata.correlationId
      })
    }
  }

  private async checkUnusualTradingActivity(entry: AuditLogEntry): Promise<void> {
    if (!entry.userId) return

    // Check for unusual trading volume in short time
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
    
    const { data: recentTrades } = await this.supabase
      .from('audit_logs')
      .select('details')
      .eq('user_id', entry.userId)
      .eq('event_type', AuditEventType.ORDER_PLACED)
      .gte('timestamp', oneHourAgo.toISOString())

    if (recentTrades && recentTrades.length >= 50) {
      await this.logEvent(AuditEventType.SUSPICIOUS_ACTIVITY, {
        userId: entry.userId,
        action: 'High frequency trading detected',
        resource: 'trading',
        details: {
          pattern: 'high_frequency_trading',
          count: recentTrades.length,
          timeWindow: '1_hour'
        },
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent,
        outcome: 'warning',
        correlationId: entry.metadata.correlationId
      })
    }
  }

  private async checkUnusualDataAccess(entry: AuditLogEntry): Promise<void> {
    // Implementation for checking unusual data access patterns
    // This would typically check against user's historical access patterns
    console.log('Checking unusual data access for entry:', entry.id)
  }

  private async checkHighValueTransactions(entry: AuditLogEntry): Promise<void> {
    const amount = entry.details.amount
    if (typeof amount === 'number' && amount >= 10000) {
      await this.logEvent(AuditEventType.SUSPICIOUS_ACTIVITY, {
        userId: entry.userId,
        action: 'High value transaction detected',
        resource: 'financial',
        resourceId: entry.resourceId,
        details: {
          pattern: 'high_value_transaction',
          amount,
          threshold: 10000,
          originalEvent: entry.eventType
        },
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent,
        outcome: 'warning',
        correlationId: entry.metadata.correlationId
      })
    }
  }

  async searchAuditLogs(
    filters: AuditSearchFilters,
    page = 1,
    pageSize = 50
  ): Promise<{ logs: AuditLogEntry[], total: number }> {
    let query = this.supabase
      .from('audit_logs')
      .select('*', { count: 'exact' })

    // Apply filters
    if (filters.userId) {
      query = query.eq('user_id', filters.userId)
    }

    if (filters.eventType?.length) {
      query = query.in('event_type', filters.eventType)
    }

    if (filters.category?.length) {
      query = query.in('category', filters.category)
    }

    if (filters.dateRange) {
      query = query
        .gte('timestamp', filters.dateRange.start.toISOString())
        .lte('timestamp', filters.dateRange.end.toISOString())
    }

    if (filters.ipAddress) {
      query = query.eq('ip_address', filters.ipAddress)
    }

    if (filters.outcome?.length) {
      query = query.in('outcome', filters.outcome)
    }

    if (filters.sensitiveData !== undefined) {
      query = query.eq('sensitive_data', filters.sensitiveData)
    }

    if (filters.complianceFlags?.length) {
      query = query.overlaps('compliance_flags', filters.complianceFlags)
    }

    if (filters.searchText) {
      query = query.or(`action.ilike.%${filters.searchText}%,resource.ilike.%${filters.searchText}%`)
    }

    // Apply pagination
    const offset = (page - 1) * pageSize
    query = query
      .range(offset, offset + pageSize - 1)
      .order('timestamp', { ascending: false })

    const { data, error, count } = await query

    if (error) {
      throw new Error(`Failed to search audit logs: ${error.message}`)
    }

    return {
      logs: data as AuditLogEntry[],
      total: count || 0
    }
  }

  async getAuditStatistics(dateRange?: { start: Date; end: Date }): Promise<AuditStatistics> {
    let query = this.supabase.from('audit_logs').select('*')

    if (dateRange) {
      query = query
        .gte('timestamp', dateRange.start.toISOString())
        .lte('timestamp', dateRange.end.toISOString())
    }

    const { data: logs, error } = await query

    if (error) {
      throw new Error(`Failed to get audit statistics: ${error.message}`)
    }

    if (!logs) {
      return this.getEmptyStatistics()
    }

    const stats: AuditStatistics = {
      totalEvents: logs.length,
      eventsByType: {},
      eventsByCategory: {},
      eventsByOutcome: {},
      uniqueUsers: new Set(logs.map(log => log.user_id).filter(Boolean)).size,
      suspiciousActivityCount: logs.filter(log => log.event_type === AuditEventType.SUSPICIOUS_ACTIVITY).length,
      complianceViolations: logs.filter(log => log.outcome === 'failure' && log.compliance_flags?.length > 0).length,
      recentActivity: logs
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 10) as AuditLogEntry[]
    }

    // Aggregate by event type
    logs.forEach(log => {
      stats.eventsByType[log.event_type] = (stats.eventsByType[log.event_type] || 0) + 1
      stats.eventsByCategory[log.category] = (stats.eventsByCategory[log.category] || 0) + 1
      stats.eventsByOutcome[log.outcome] = (stats.eventsByOutcome[log.outcome] || 0) + 1
    })

    return stats
  }

  private getEmptyStatistics(): AuditStatistics {
    return {
      totalEvents: 0,
      eventsByType: {},
      eventsByCategory: {},
      eventsByOutcome: {},
      uniqueUsers: 0,
      suspiciousActivityCount: 0,
      complianceViolations: 0,
      recentActivity: []
    }
  }

  async applyRetentionPolicies(): Promise<{ deleted: number; errors: string[] }> {
    const now = new Date()
    const errors: string[] = []
    let totalDeleted = 0

    for (const [retention, maxAge] of Object.entries(this.retentionPolicies)) {
      if (maxAge === Infinity) continue // Skip permanent retention

      const cutoffDate = new Date(now.getTime() - maxAge)
      
      try {
        const { count, error } = await this.supabase
          .from('audit_logs')
          .delete({ count: 'exact' })
          .eq('retention', retention)
          .lt('timestamp', cutoffDate.toISOString())

        if (error) {
          errors.push(`Failed to delete ${retention} logs: ${error.message}`)
        } else {
          totalDeleted += count || 0
          console.log(`Deleted ${count} audit logs with ${retention} retention`)
        }
      } catch (error) {
        errors.push(`Error processing ${retention} retention: ${error}`)
      }
    }

    // Log the retention policy application
    await this.logEvent(AuditEventType.DATA_RETENTION_APPLIED, {
      action: 'Applied audit log retention policies',
      resource: 'audit_logs',
      details: {
        deletedCount: totalDeleted,
        errors: errors.length,
        timestamp: now.toISOString()
      },
      ipAddress: '127.0.0.1',
      userAgent: 'system',
      outcome: errors.length > 0 ? 'warning' : 'success',
      source: 'retention_job'
    })

    return { deleted: totalDeleted, errors }
  }

  // Method for regulatory compliance reporting
  async generateComplianceReport(
    regulation: string,
    dateRange: { start: Date; end: Date }
  ): Promise<{
    regulation: string
    period: { start: Date; end: Date }
    totalEvents: number
    criticalEvents: number
    violations: number
    events: AuditLogEntry[]
  }> {
    const { logs } = await this.searchAuditLogs({
      complianceFlags: [regulation],
      dateRange
    }, 1, 10000) // Get all matching logs

    const criticalEvents = logs.filter(log => 
      log.sensitiveData || log.outcome === 'failure'
    ).length

    const violations = logs.filter(log => 
      log.outcome === 'failure' && log.category === AuditCategory.COMPLIANCE
    ).length

    return {
      regulation,
      period: dateRange,
      totalEvents: logs.length,
      criticalEvents,
      violations,
      events: logs
    }
  }
}