/**
 * MetaQuotes Demo Server Adapter
 * 
 * Specific implementation for MetaQuotes demo server:
 * - Demo trading environment with simulated market conditions
 * - Standard MT5 API integration patterns
 * - Development and testing optimized features
 */

import { StandardBrokerAdapter, type TradeOrder, type TradeExecutionResult, type AccountInfo, type PositionInfo, type SymbolInfo, BrokerAdapterError } from './StandardBrokerAdapter';
import type { MarketData } from '@golddaddy/types';
import type { BrokerAdapterConfig } from './BrokerAdapterFactory';

/**
 * MetaQuotes Demo Server Adapter
 * Handles connections to MetaQuotes demo trading servers
 */
export class MetaQuotesDemoAdapter extends StandardBrokerAdapter {
  private mockAccountInfo: AccountInfo;
  private mockPositions: PositionInfo[] = [];
  private mockSymbols: SymbolInfo[] = [];
  private ticketCounter = 100000;
  private orderCounter = 1;

  constructor(config: BrokerAdapterConfig) {
    super(config);
    
    this.mockAccountInfo = {
      accountNumber: '********', // MetaQuotes demo account
      balance: 100000.00,
      equity: 100000.00,
      margin: 0.00,
      freeMargin: 100000.00,
      marginLevel: 0.00,
      currency: 'USD',
      leverage: 100,
      server: config.server,
      company: 'MetaQuotes Software Corp.',
      isConnected: false,
      lastUpdate: new Date()
    };

    this.initializeMockSymbols();
  }

  /**
   * Connect to MetaQuotes demo server
   */
  async connect(): Promise<boolean> {
    try {
      console.log(`🔗 Connecting to MetaQuotes Demo: ${this.config.server}`);
      
      // Simulate connection time
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
      
      // Simulate authentication
      const authSuccess = await this.authenticate();
      if (!authSuccess) {
        throw new Error('Authentication failed');
      }
      
      this.isConnected = true;
      this.mockAccountInfo.isConnected = true;
      this.connectionStats.activeConnections = 1;
      
      console.log(`✅ Connected to MetaQuotes Demo server`);
      this.emit('connected', { brokerId: this.config.id });
      
      return true;
    } catch (error) {
      console.error(`❌ Failed to connect to MetaQuotes Demo:`, error);
      this.isConnected = false;
      this.mockAccountInfo.isConnected = false;
      
      throw this.handleBrokerError(error, 'connection');
    }
  }

  /**
   * Disconnect from demo server
   */
  async disconnect(): Promise<boolean> {
    try {
      console.log(`🔌 Disconnecting from MetaQuotes Demo...`);
      
      // Clear all subscriptions
      this.subscriptions.clear();
      
      this.isConnected = false;
      this.mockAccountInfo.isConnected = false;
      this.connectionStats.activeConnections = 0;
      
      console.log(`✅ Disconnected from MetaQuotes Demo`);
      this.emit('disconnected', { brokerId: this.config.id });
      
      return true;
    } catch (error) {
      console.error(`❌ Error during MetaQuotes Demo disconnect:`, error);
      return false;
    }
  }

  /**
   * Test connection health
   */
  async testConnection(): Promise<boolean> {
    if (!this.isConnected) return false;
    
    try {
      const startTime = Date.now();
      
      // Simulate ping to server
      await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
      
      // Simulate occasional failures
      if (Math.random() < 0.05) { // 5% failure rate
        throw new Error('Connection test failed');
      }
      
      const responseTime = Date.now() - startTime;
      this.recordRequest(responseTime, true);
      
      return true;
    } catch (error) {
      this.recordRequest(500, false);
      return false;
    }
  }

  /**
   * Get demo account information
   */
  async getAccountInfo(): Promise<AccountInfo> {
    if (!this.isConnected) {
      throw new BrokerAdapterError('Not connected to demo server', 'NOT_CONNECTED');
    }

    try {
      const startTime = Date.now();
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 100 + Math.random() * 200));
      
      // Update equity based on open positions
      let floatingPL = 0;
      for (const position of this.mockPositions) {
        floatingPL += position.profit;
      }
      
      this.mockAccountInfo.equity = this.mockAccountInfo.balance + floatingPL;
      this.mockAccountInfo.freeMargin = this.mockAccountInfo.equity - this.mockAccountInfo.margin;
      this.mockAccountInfo.marginLevel = this.mockAccountInfo.margin > 0 
        ? (this.mockAccountInfo.equity / this.mockAccountInfo.margin) * 100 
        : 0;
      this.mockAccountInfo.lastUpdate = new Date();
      
      const responseTime = Date.now() - startTime;
      this.recordRequest(responseTime, true);
      
      return { ...this.mockAccountInfo };
    } catch (error) {
      this.recordRequest(200, false);
      throw this.handleBrokerError(error, 'getAccountInfo');
    }
  }

  /**
   * Get available symbols
   */
  async getSymbols(): Promise<SymbolInfo[]> {
    if (!this.isConnected) {
      throw new BrokerAdapterError('Not connected to demo server', 'NOT_CONNECTED');
    }

    try {
      const startTime = Date.now();
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 150 + Math.random() * 300));
      
      const responseTime = Date.now() - startTime;
      this.recordRequest(responseTime, true);
      
      return [...this.mockSymbols];
    } catch (error) {
      this.recordRequest(300, false);
      throw this.handleBrokerError(error, 'getSymbols');
    }
  }

  /**
   * Get specific symbol information
   */
  async getSymbolInfo(symbol: string): Promise<SymbolInfo> {
    if (!this.isConnected) {
      throw new BrokerAdapterError('Not connected to demo server', 'NOT_CONNECTED');
    }

    const symbolInfo = this.mockSymbols.find(s => s.name === symbol);
    if (!symbolInfo) {
      throw new BrokerAdapterError(`Symbol not found: ${symbol}`, 'SYMBOL_NOT_FOUND');
    }

    try {
      const startTime = Date.now();
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 80 + Math.random() * 120));
      
      // Update spread randomly
      const updatedSymbol = { 
        ...symbolInfo, 
        spread: symbolInfo.spread + (Math.random() - 0.5) * 0.5 
      };
      
      const responseTime = Date.now() - startTime;
      this.recordRequest(responseTime, true);
      
      return updatedSymbol;
    } catch (error) {
      this.recordRequest(150, false);
      throw this.handleBrokerError(error, 'getSymbolInfo');
    }
  }

  /**
   * Subscribe to market data
   */
  async subscribeToMarketData(symbol: string, timeframe: string): Promise<string> {
    if (!this.isConnected) {
      throw new BrokerAdapterError('Not connected to demo server', 'NOT_CONNECTED');
    }

    if (!this.config.supportedSymbols.includes(symbol)) {
      throw new BrokerAdapterError(`Unsupported symbol: ${symbol}`, 'UNSUPPORTED_SYMBOL');
    }

    try {
      const subscriptionId = `${symbol}_${timeframe}_${Date.now()}`;
      
      const subscription = {
        symbol,
        timeframe,
        subscriptionId,
        subscribedAt: new Date(),
        lastUpdate: null,
        active: true
      };
      
      this.subscriptions.set(subscriptionId, subscription);
      
      // Start mock data streaming
      this.startMockDataStream(subscriptionId, symbol);
      
      console.log(`✅ Subscribed to ${symbol} ${timeframe} market data`);
      this.emit('subscribed', { symbol, timeframe, subscriptionId });
      
      return subscriptionId;
    } catch (error) {
      throw this.handleBrokerError(error, 'subscribeToMarketData');
    }
  }

  /**
   * Unsubscribe from market data
   */
  async unsubscribeFromMarketData(subscriptionId: string): Promise<boolean> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (!subscription) return false;

    try {
      subscription.active = false;
      this.subscriptions.delete(subscriptionId);
      
      console.log(`✅ Unsubscribed from ${subscription.symbol} market data`);
      this.emit('unsubscribed', { subscriptionId, symbol: subscription.symbol });
      
      return true;
    } catch (error) {
      console.error('Failed to unsubscribe:', error);
      return false;
    }
  }

  /**
   * Get historical market data
   */
  async getHistoricalData(symbol: string, timeframe: string, from: Date, to: Date): Promise<MarketData[]> {
    if (!this.isConnected) {
      throw new BrokerAdapterError('Not connected to demo server', 'NOT_CONNECTED');
    }

    try {
      const startTime = Date.now();
      
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 500));
      
      // Generate mock historical data
      const data: MarketData[] = [];
      const timeframeMins = this.parseTimeframe(timeframe);
      const startTime_data = from.getTime();
      const endTime = to.getTime();
      
      let basePrice = this.getBasePrice(symbol);
      
      for (let timestamp = startTime_data; timestamp <= endTime; timestamp += timeframeMins * 60 * 1000) {
        const variation = (Math.random() - 0.5) * basePrice * 0.001; // 0.1% max variation
        const open = basePrice;
        const close = basePrice + variation;
        const high = Math.max(open, close) + Math.random() * basePrice * 0.0005;
        const low = Math.min(open, close) - Math.random() * basePrice * 0.0005;
        
        data.push({
          symbol,
          timestamp: new Date(timestamp),
          bid: close - 0.00001,
          ask: close + 0.00001,
          last: close,
          volume: Math.floor(Math.random() * 1000) + 100,
          high,
          low,
          open,
          close
        });
        
        basePrice = close;
      }
      
      const responseTime = Date.now() - startTime;
      this.recordRequest(responseTime, true);
      
      return data;
    } catch (error) {
      this.recordRequest(400, false);
      throw this.handleBrokerError(error, 'getHistoricalData');
    }
  }

  /**
   * Execute a trade order
   */
  async executeTrade(order: TradeOrder): Promise<TradeExecutionResult> {
    if (!this.isConnected) {
      throw new BrokerAdapterError('Not connected to demo server', 'NOT_CONNECTED');
    }

    try {
      this.validateTradeOrder(order);
      
      const startTime = Date.now();
      
      // Simulate order processing delay
      await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 500));
      
      // Simulate occasional order failures
      if (Math.random() < 0.02) { // 2% failure rate
        throw new Error('Order execution failed - market closed');
      }
      
      const ticket = this.ticketCounter++;
      const executionPrice = order.price + (Math.random() - 0.5) * 0.0001; // Small slippage
      const executionTime = new Date();
      
      // Create position
      const position: PositionInfo = {
        ticket,
        symbol: order.symbol,
        type: order.type,
        volume: order.volume,
        openPrice: executionPrice,
        currentPrice: executionPrice,
        stopLoss: order.stopLoss || 0,
        takeProfit: order.takeProfit || 0,
        profit: 0,
        commission: -Math.abs(order.volume * 7), // $7 per lot commission
        swap: 0,
        openTime: executionTime,
        comment: order.comment || '',
        magicNumber: order.magicNumber || 0
      };
      
      this.mockPositions.push(position);
      
      // Update account balance for commission
      this.mockAccountInfo.balance += position.commission;
      
      const result: TradeExecutionResult = {
        orderId: order.id,
        ticket,
        executed: true,
        executionPrice,
        executionTime,
        volume: order.volume,
        commission: position.commission,
        swap: 0,
        profit: 0,
        error: null,
        retryCount: 0
      };
      
      const responseTime = Date.now() - startTime;
      this.recordRequest(responseTime, true);
      
      console.log(`✅ Demo trade executed: ${order.type} ${order.volume} ${order.symbol} at ${executionPrice}`);
      this.emit('tradeExecuted', result);
      
      return result;
    } catch (error) {
      this.recordRequest(500, false);
      
      const result: TradeExecutionResult = {
        orderId: order.id,
        ticket: 0,
        executed: false,
        executionPrice: order.price,
        executionTime: new Date(),
        volume: order.volume,
        commission: 0,
        swap: 0,
        profit: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
        retryCount: 0
      };
      
      return result;
    }
  }

  /**
   * Get current positions
   */
  async getPositions(): Promise<PositionInfo[]> {
    if (!this.isConnected) {
      throw new BrokerAdapterError('Not connected to demo server', 'NOT_CONNECTED');
    }

    try {
      const startTime = Date.now();
      
      // Update position P&L with current prices
      for (const position of this.mockPositions) {
        const currentPrice = this.getCurrentPrice(position.symbol);
        position.currentPrice = currentPrice;
        
        const priceChange = position.type === 'buy' 
          ? currentPrice - position.openPrice
          : position.openPrice - currentPrice;
        
        position.profit = priceChange * position.volume * 100000; // 100,000 units per lot
      }
      
      const responseTime = Date.now() - startTime;
      this.recordRequest(responseTime, true);
      
      return [...this.mockPositions];
    } catch (error) {
      this.recordRequest(150, false);
      throw this.handleBrokerError(error, 'getPositions');
    }
  }

  /**
   * Close a position
   */
  async closePosition(ticket: number): Promise<TradeExecutionResult> {
    if (!this.isConnected) {
      throw new BrokerAdapterError('Not connected to demo server', 'NOT_CONNECTED');
    }

    const positionIndex = this.mockPositions.findIndex(p => p.ticket === ticket);
    if (positionIndex === -1) {
      throw new BrokerAdapterError(`Position not found: ${ticket}`, 'POSITION_NOT_FOUND');
    }

    try {
      const position = this.mockPositions[positionIndex];
      const startTime = Date.now();
      
      // Simulate close order processing
      await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));
      
      const closePrice = this.getCurrentPrice(position.symbol);
      const closedPosition = { ...position, currentPrice: closePrice };
      
      // Calculate final profit
      const priceChange = position.type === 'buy' 
        ? closePrice - position.openPrice
        : position.openPrice - closePrice;
      
      const finalProfit = priceChange * position.volume * 100000;
      
      // Remove position from active positions
      this.mockPositions.splice(positionIndex, 1);
      
      // Update account balance with profit/loss
      this.mockAccountInfo.balance += finalProfit;
      
      const result: TradeExecutionResult = {
        orderId: `close_${ticket}`,
        ticket,
        executed: true,
        executionPrice: closePrice,
        executionTime: new Date(),
        volume: position.volume,
        commission: position.commission,
        swap: position.swap,
        profit: finalProfit,
        error: null,
        retryCount: 0
      };
      
      const responseTime = Date.now() - startTime;
      this.recordRequest(responseTime, true);
      
      console.log(`✅ Demo position closed: ${ticket} with profit ${finalProfit.toFixed(2)}`);
      this.emit('positionClosed', result);
      
      return result;
    } catch (error) {
      this.recordRequest(300, false);
      throw this.handleBrokerError(error, 'closePosition');
    }
  }

  /**
   * Modify a position
   */
  async modifyPosition(ticket: number, stopLoss?: number, takeProfit?: number): Promise<boolean> {
    if (!this.isConnected) {
      throw new BrokerAdapterError('Not connected to demo server', 'NOT_CONNECTED');
    }

    const position = this.mockPositions.find(p => p.ticket === ticket);
    if (!position) {
      throw new BrokerAdapterError(`Position not found: ${ticket}`, 'POSITION_NOT_FOUND');
    }

    try {
      const startTime = Date.now();
      
      // Simulate modification processing
      await new Promise(resolve => setTimeout(resolve, 150 + Math.random() * 200));
      
      if (stopLoss !== undefined) position.stopLoss = stopLoss;
      if (takeProfit !== undefined) position.takeProfit = takeProfit;
      
      const responseTime = Date.now() - startTime;
      this.recordRequest(responseTime, true);
      
      console.log(`✅ Demo position modified: ${ticket}`);
      this.emit('positionModified', { ticket, stopLoss, takeProfit });
      
      return true;
    } catch (error) {
      this.recordRequest(200, false);
      throw this.handleBrokerError(error, 'modifyPosition');
    }
  }

  // Protected methods

  /**
   * Perform MetaQuotes demo authentication
   */
  protected async performAuthentication(): Promise<boolean> {
    try {
      // Simulate authentication process
      await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));
      
      // Demo server always accepts authentication
      // In production, this would use actual credentials
      return true;
    } catch (error) {
      return false;
    }
  }

  // Private methods

  /**
   * Initialize mock symbol data
   */
  private initializeMockSymbols(): void {
    this.mockSymbols = [
      {
        name: 'EURUSD',
        description: 'Euro vs US Dollar',
        type: 'forex',
        digits: 5,
        spread: 1.2,
        point: 0.00001,
        minVolume: 0.01,
        maxVolume: 500.0,
        volumeStep: 0.01,
        contractSize: 100000,
        marginRequired: 0.002,
        isTradeAllowed: true,
        sessionTimes: { open: '00:00', close: '23:59', timezone: 'GMT' }
      },
      {
        name: 'GBPUSD',
        description: 'British Pound vs US Dollar',
        type: 'forex',
        digits: 5,
        spread: 1.8,
        point: 0.00001,
        minVolume: 0.01,
        maxVolume: 500.0,
        volumeStep: 0.01,
        contractSize: 100000,
        marginRequired: 0.002,
        isTradeAllowed: true,
        sessionTimes: { open: '00:00', close: '23:59', timezone: 'GMT' }
      },
      {
        name: 'USDJPY',
        description: 'US Dollar vs Japanese Yen',
        type: 'forex',
        digits: 3,
        spread: 1.0,
        point: 0.001,
        minVolume: 0.01,
        maxVolume: 500.0,
        volumeStep: 0.01,
        contractSize: 100000,
        marginRequired: 0.002,
        isTradeAllowed: true,
        sessionTimes: { open: '00:00', close: '23:59', timezone: 'GMT' }
      },
      {
        name: 'XAUUSD',
        description: 'Gold vs US Dollar',
        type: 'metal',
        digits: 2,
        spread: 3.5,
        point: 0.01,
        minVolume: 0.01,
        maxVolume: 100.0,
        volumeStep: 0.01,
        contractSize: 100,
        marginRequired: 0.01,
        isTradeAllowed: true,
        sessionTimes: { open: '00:00', close: '23:59', timezone: 'GMT' }
      }
    ];
  }

  /**
   * Start mock data streaming
   */
  private startMockDataStream(subscriptionId: string, symbol: string): void {
    const interval = setInterval(() => {
      const subscription = this.subscriptions.get(subscriptionId);
      if (!subscription || !subscription.active) {
        clearInterval(interval);
        return;
      }

      const marketData = this.generateMockMarketData(symbol);
      subscription.lastUpdate = new Date();
      
      this.emit('marketData', { subscriptionId, data: marketData });
    }, 1000 + Math.random() * 4000); // 1-5 second intervals
  }

  /**
   * Generate mock market data
   */
  private generateMockMarketData(symbol: string): MarketData {
    const basePrice = this.getBasePrice(symbol);
    const spread = 0.00002; // 2 pips
    const variation = (Math.random() - 0.5) * basePrice * 0.0001;
    
    const bid = basePrice + variation;
    const ask = bid + spread;
    
    return {
      symbol,
      timestamp: new Date(),
      bid,
      ask,
      last: bid,
      volume: Math.floor(Math.random() * 100) + 10,
      high: bid + Math.random() * 0.0001,
      low: bid - Math.random() * 0.0001,
      open: bid,
      close: bid
    };
  }

  /**
   * Get base price for a symbol
   */
  private getBasePrice(symbol: string): number {
    const prices: Record<string, number> = {
      'EURUSD': 1.1850,
      'GBPUSD': 1.3650,
      'USDJPY': 148.50,
      'XAUUSD': 2650.00
    };
    
    return prices[symbol] || 1.0;
  }

  /**
   * Get current price for a symbol
   */
  private getCurrentPrice(symbol: string): number {
    const basePrice = this.getBasePrice(symbol);
    const variation = (Math.random() - 0.5) * basePrice * 0.0002;
    return basePrice + variation;
  }

  /**
   * Parse timeframe string to minutes
   */
  private parseTimeframe(timeframe: string): number {
    const timeframes: Record<string, number> = {
      'M1': 1,
      'M5': 5,
      'M15': 15,
      'M30': 30,
      'H1': 60,
      'H4': 240,
      'D1': 1440
    };
    
    return timeframes[timeframe] || 1;
  }
}