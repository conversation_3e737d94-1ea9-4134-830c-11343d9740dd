/**
 * Regime Accuracy Tracker Service
 * 
 * Tracks historical regime predictions vs actual market movements
 * to measure accuracy and performance of regime detection algorithms.
 */

import { EventEmitter } from 'events';
import Decimal from 'decimal.js';
import {
  MarketRegime,
  RegimeDetectionResult,
  RegimeAccuracyMetrics,
  TimeFrame,
} from '@golddaddy/types';

// Performance tracking data point
interface AccuracyDataPoint {
  id: string;
  instrument: string;
  timeframe: TimeFrame;
  timestamp: Date;
  predictedRegime: MarketRegime;
  actualRegime?: MarketRegime;
  confidence: number;
  isCorrect?: boolean;
  priceMovement: number; // Actual price movement percentage
  predictionTimeframe: number; // Hours ahead prediction was for
  verificationTimestamp?: Date;
  
  // Additional context
  volatilityDuringPeriod: number;
  trendStrengthDuringPeriod: number;
  marketConditions?: 'normal' | 'high_volatility' | 'low_liquidity' | 'news_driven';
}

// Accuracy calculation result
interface AccuracyCalculationResult {
  instrument: string;
  timeframe: TimeFrame;
  accuracy: number;
  precision: number;
  recall: number;
  f1Score: number;
  totalPredictions: number;
  correctPredictions: number;
  confidenceCorrelation: number;
}

// Weekly/monthly accuracy trends
interface AccuracyTrend {
  period: string; // Week/Month identifier (e.g., "2025-W03", "2025-01")
  accuracy: number;
  totalPredictions: number;
  averageConfidence: number;
  regimeDistribution: Record<MarketRegime, number>;
}

/**
 * Regime Accuracy Tracker Service
 * Monitors and analyzes prediction accuracy over time
 */
export class RegimeAccuracyTracker extends EventEmitter {
  private accuracyData = new Map<string, AccuracyDataPoint[]>();
  private verificationQueue = new Map<string, AccuracyDataPoint>();
  private accuracyMetrics = new Map<string, RegimeAccuracyMetrics>();
  
  // Configuration
  private config = {
    verificationDelayHours: 4, // Hours to wait before verifying prediction
    maxDataPointsPerInstrument: 1000,
    minimumDataPointsForAccuracy: 50,
    accuracyCalculationInterval: 3600000, // 1 hour
    confidenceThresholds: [0.5, 0.7, 0.85] as const,
  };

  // Statistics
  private stats = {
    totalPredictions: 0,
    totalVerifications: 0,
    overallAccuracy: 0,
    lastCalculationAt: new Date(),
    instrumentsTracked: 0,
  };

  constructor() {
    super();
    
    // Start periodic accuracy calculation
    setInterval(() => {
      this.calculateAccuracyMetrics().catch(error => {
        this.emit('calculation_error', error);
      });
    }, this.config.accuracyCalculationInterval);

    // Start verification queue processing
    setInterval(() => {
      this.processVerificationQueue().catch(error => {
        this.emit('verification_error', error);
      });
    }, 300000); // Every 5 minutes
  }

  /**
   * Track a new regime prediction
   */
  public async trackPrediction(result: RegimeDetectionResult, priceData?: {
    currentPrice: Decimal.Instance;
    volume: Decimal.Instance;
  }): Promise<void> {
    const key = `${result.instrument}_${result.timeframe}`;
    
    // Create accuracy data point
    const dataPoint: AccuracyDataPoint = {
      id: result.id,
      instrument: result.instrument,
      timeframe: result.timeframe,
      timestamp: result.timestamp,
      predictedRegime: result.regime,
      confidence: result.confidence,
      priceMovement: 0, // Will be calculated later
      predictionTimeframe: this.getPredictionTimeframeHours(result.timeframe),
      volatilityDuringPeriod: result.volatilityLevel,
      trendStrengthDuringPeriod: result.trendStrength,
      marketConditions: this.inferMarketConditions(result),
    };

    // Add to tracking data
    if (!this.accuracyData.has(key)) {
      this.accuracyData.set(key, []);
    }
    
    const data = this.accuracyData.get(key)!;
    data.push(dataPoint);
    
    // Maintain size limit
    if (data.length > this.config.maxDataPointsPerInstrument) {
      data.splice(0, data.length - this.config.maxDataPointsPerInstrument);
    }

    // Add to verification queue for later accuracy check
    const verificationKey = `${dataPoint.id}_${Date.now()}`;
    this.verificationQueue.set(verificationKey, dataPoint);

    this.stats.totalPredictions++;
    this.stats.instrumentsTracked = this.accuracyData.size;

    this.emit('prediction_tracked', {
      instrument: result.instrument,
      timeframe: result.timeframe,
      regime: result.regime,
      confidence: result.confidence,
    });
  }

  /**
   * Manually verify a prediction with actual outcome
   */
  public async verifyPrediction(
    predictionId: string,
    actualOutcome: {
      actualRegime: MarketRegime;
      priceMovement: number;
      marketConditions?: 'normal' | 'high_volatility' | 'low_liquidity' | 'news_driven';
    }
  ): Promise<void> {
    // Find the prediction in our data
    let dataPoint: AccuracyDataPoint | undefined;
    let dataKey: string | undefined;

    for (const [key, data] of this.accuracyData.entries()) {
      const found = data.find(d => d.id === predictionId);
      if (found) {
        dataPoint = found;
        dataKey = key;
        break;
      }
    }

    if (!dataPoint || !dataKey) {
      this.emit('verification_error', {
        predictionId,
        error: 'Prediction not found for verification',
      });
      return;
    }

    // Update the data point with verification results
    dataPoint.actualRegime = actualOutcome.actualRegime;
    dataPoint.priceMovement = actualOutcome.priceMovement;
    dataPoint.isCorrect = this.isPredictionCorrect(
      dataPoint.predictedRegime,
      actualOutcome.actualRegime,
      actualOutcome.priceMovement
    );
    dataPoint.verificationTimestamp = new Date();
    
    if (actualOutcome.marketConditions) {
      dataPoint.marketConditions = actualOutcome.marketConditions;
    }

    this.stats.totalVerifications++;

    this.emit('prediction_verified', {
      predictionId,
      instrument: dataPoint.instrument,
      timeframe: dataPoint.timeframe,
      predicted: dataPoint.predictedRegime,
      actual: actualOutcome.actualRegime,
      isCorrect: dataPoint.isCorrect,
      confidence: dataPoint.confidence,
    });

    // Recalculate accuracy metrics for this instrument
    await this.calculateAccuracyForInstrument(dataKey);
  }

  /**
   * Get accuracy metrics for a specific instrument and timeframe
   */
  public getAccuracyMetrics(instrument: string, timeframe: TimeFrame): RegimeAccuracyMetrics | null {
    const key = `${instrument}_${timeframe}`;
    return this.accuracyMetrics.get(key) || null;
  }

  /**
   * Get overall accuracy statistics
   */
  public getOverallStats() {
    return {
      ...this.stats,
      accuracyByInstrument: this.getAccuracyByInstrument(),
      recentAccuracyTrend: this.getRecentAccuracyTrend(),
      confidenceDistribution: this.getConfidenceDistribution(),
    };
  }

  /**
   * Get accuracy breakdown by confidence levels
   */
  public getAccuracyByConfidenceLevel(instrument: string, timeframe: TimeFrame) {
    const key = `${instrument}_${timeframe}`;
    const data = this.accuracyData.get(key) || [];
    const verifiedData = data.filter(d => d.isCorrect !== undefined);

    const results: Record<string, AccuracyCalculationResult> = {};

    for (const threshold of this.config.confidenceThresholds) {
      const filteredData = verifiedData.filter(d => d.confidence >= threshold);
      
      if (filteredData.length === 0) continue;

      const correctPredictions = filteredData.filter(d => d.isCorrect).length;
      const accuracy = correctPredictions / filteredData.length;

      results[`${threshold}+`] = {
        instrument,
        timeframe,
        accuracy,
        precision: accuracy, // Simplified for this implementation
        recall: accuracy,
        f1Score: accuracy,
        totalPredictions: filteredData.length,
        correctPredictions,
        confidenceCorrelation: this.calculateConfidenceCorrelation(filteredData),
      };
    }

    return results;
  }

  /**
   * Get recent accuracy trends (weekly/monthly)
   */
  public getAccuracyTrends(
    instrument: string,
    timeframe: TimeFrame,
    periodType: 'weekly' | 'monthly' = 'weekly'
  ): AccuracyTrend[] {
    const key = `${instrument}_${timeframe}`;
    const data = this.accuracyData.get(key) || [];
    const verifiedData = data.filter(d => d.isCorrect !== undefined);

    const trends = new Map<string, AccuracyTrend>();

    for (const dataPoint of verifiedData) {
      const period = this.getPeriodKey(dataPoint.timestamp, periodType);
      
      if (!trends.has(period)) {
        trends.set(period, {
          period,
          accuracy: 0,
          totalPredictions: 0,
          averageConfidence: 0,
          regimeDistribution: {
            [MarketRegime.TRENDING_UP]: 0,
            [MarketRegime.TRENDING_DOWN]: 0,
            [MarketRegime.SIDEWAYS]: 0,
            [MarketRegime.VOLATILE]: 0,
            [MarketRegime.LOW_VOLATILITY]: 0,
            [MarketRegime.UNKNOWN]: 0,
          },
        });
      }

      const trend = trends.get(period)!;
      trend.totalPredictions++;
      trend.regimeDistribution[dataPoint.predictedRegime]++;
      
      if (dataPoint.isCorrect) {
        trend.accuracy = ((trend.accuracy * (trend.totalPredictions - 1)) + 1) / trend.totalPredictions;
      } else {
        trend.accuracy = (trend.accuracy * (trend.totalPredictions - 1)) / trend.totalPredictions;
      }
      
      trend.averageConfidence = ((trend.averageConfidence * (trend.totalPredictions - 1)) + dataPoint.confidence) / trend.totalPredictions;
    }

    return Array.from(trends.values()).sort((a, b) => a.period.localeCompare(b.period));
  }

  // ===== Private Methods =====

  private async processVerificationQueue(): Promise<void> {
    const now = Date.now();
    const verificationEntries: [string, AccuracyDataPoint][] = [];

    // Find predictions ready for verification
    for (const [key, dataPoint] of this.verificationQueue.entries()) {
      const ageHours = (now - dataPoint.timestamp.getTime()) / (1000 * 60 * 60);
      if (ageHours >= this.config.verificationDelayHours) {
        verificationEntries.push([key, dataPoint]);
      }
    }

    // Process verification entries
    for (const [key, dataPoint] of verificationEntries) {
      try {
        // Auto-verify based on historical price movement
        // In a real implementation, this would fetch actual market data
        await this.autoVerifyPrediction(dataPoint);
        this.verificationQueue.delete(key);
      } catch (error) {
        this.emit('auto_verification_error', {
          predictionId: dataPoint.id,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }
  }

  private async autoVerifyPrediction(dataPoint: AccuracyDataPoint): Promise<void> {
    // This is a simplified auto-verification
    // In reality, you'd fetch actual market data and calculate the actual regime
    
    // For demonstration, we'll simulate verification based on some logic
    const actualRegime = this.simulateActualRegime(dataPoint);
    const priceMovement = this.simulatePriceMovement(dataPoint);

    await this.verifyPrediction(dataPoint.id, {
      actualRegime,
      priceMovement,
      marketConditions: dataPoint.marketConditions,
    });
  }

  private simulateActualRegime(dataPoint: AccuracyDataPoint): MarketRegime {
    // Simplified simulation - in reality, this would be based on actual market data
    const randomFactor = Math.random();
    
    // Higher confidence predictions are more likely to be correct
    const accuracyBonus = dataPoint.confidence * 0.3;
    
    if (randomFactor + accuracyBonus > 0.7) {
      return dataPoint.predictedRegime; // Correct prediction
    }
    
    // Random incorrect regime
    const regimes = Object.values(MarketRegime).filter(r => r !== dataPoint.predictedRegime);
    return regimes[Math.floor(Math.random() * regimes.length)];
  }

  private simulatePriceMovement(dataPoint: AccuracyDataPoint): number {
    // Simulate price movement based on predicted regime
    switch (dataPoint.predictedRegime) {
      case MarketRegime.TRENDING_UP:
        return (Math.random() * 0.04) + 0.01; // 1-5% up
      case MarketRegime.TRENDING_DOWN:
        return -(Math.random() * 0.04) - 0.01; // 1-5% down
      case MarketRegime.VOLATILE:
        return (Math.random() - 0.5) * 0.08; // -4% to +4%
      case MarketRegime.SIDEWAYS:
      case MarketRegime.LOW_VOLATILITY:
        return (Math.random() - 0.5) * 0.02; // -1% to +1%
      default:
        return (Math.random() - 0.5) * 0.04; // -2% to +2%
    }
  }

  private async calculateAccuracyMetrics(): Promise<void> {
    for (const [key] of this.accuracyData.entries()) {
      await this.calculateAccuracyForInstrument(key);
    }

    this.stats.lastCalculationAt = new Date();
    this.emit('accuracy_metrics_updated', this.stats);
  }

  private async calculateAccuracyForInstrument(key: string): Promise<void> {
    const data = this.accuracyData.get(key) || [];
    const verifiedData = data.filter(d => d.isCorrect !== undefined);

    if (verifiedData.length < this.config.minimumDataPointsForAccuracy) {
      return; // Not enough data for reliable accuracy calculation
    }

    const [instrument, timeframe] = key.split('_');
    const correctPredictions = verifiedData.filter(d => d.isCorrect).length;
    const overallAccuracy = correctPredictions / verifiedData.length;

    // Calculate per-regime accuracy
    const regimeAccuracy: Record<MarketRegime, any> = {} as any;
    
    for (const regime of Object.values(MarketRegime)) {
      const regimeData = verifiedData.filter(d => d.predictedRegime === regime);
      if (regimeData.length === 0) continue;

      const regimeCorrect = regimeData.filter(d => d.isCorrect).length;
      regimeAccuracy[regime] = {
        accuracy: regimeCorrect / regimeData.length,
        precision: regimeCorrect / regimeData.length, // Simplified
        recall: regimeCorrect / regimeData.length, // Simplified
        f1Score: regimeCorrect / regimeData.length, // Simplified
        totalPredictions: regimeData.length,
        correctPredictions: regimeCorrect,
      };
    }

    // Calculate weekly and monthly trends
    const weeklyAccuracy = this.calculateWeeklyAccuracy(verifiedData);
    const monthlyAccuracy = this.calculateMonthlyAccuracy(verifiedData);
    const confidenceCorrelation = this.calculateConfidenceCorrelation(verifiedData);

    const metrics: RegimeAccuracyMetrics = {
      instrument,
      timeframe: timeframe as TimeFrame,
      overallAccuracy,
      totalPredictions: verifiedData.length,
      correctPredictions,
      regimeAccuracy,
      weeklyAccuracy,
      monthlyAccuracy,
      confidenceAccuracyCorrelation: confidenceCorrelation,
      lastUpdated: new Date(),
      dataPointsAnalyzed: verifiedData.length,
    };

    this.accuracyMetrics.set(key, metrics);
    
    this.emit('accuracy_updated', {
      instrument,
      timeframe,
      accuracy: overallAccuracy,
      totalPredictions: verifiedData.length,
    });
  }

  private calculateWeeklyAccuracy(data: AccuracyDataPoint[]): number[] {
    const weeklyData = new Map<string, { correct: number; total: number }>();
    
    for (const point of data) {
      const week = this.getPeriodKey(point.timestamp, 'weekly');
      if (!weeklyData.has(week)) {
        weeklyData.set(week, { correct: 0, total: 0 });
      }
      
      const weekData = weeklyData.get(week)!;
      weekData.total++;
      if (point.isCorrect) weekData.correct++;
    }
    
    return Array.from(weeklyData.values())
      .map(w => w.correct / w.total)
      .slice(-12); // Last 12 weeks
  }

  private calculateMonthlyAccuracy(data: AccuracyDataPoint[]): number[] {
    const monthlyData = new Map<string, { correct: number; total: number }>();
    
    for (const point of data) {
      const month = this.getPeriodKey(point.timestamp, 'monthly');
      if (!monthlyData.has(month)) {
        monthlyData.set(month, { correct: 0, total: 0 });
      }
      
      const monthData = monthlyData.get(month)!;
      monthData.total++;
      if (point.isCorrect) monthData.correct++;
    }
    
    return Array.from(monthlyData.values())
      .map(m => m.correct / m.total)
      .slice(-6); // Last 6 months
  }

  private calculateConfidenceCorrelation(data: AccuracyDataPoint[]): number {
    if (data.length < 2) return 0;

    const confidences = data.map(d => d.confidence);
    const accuracies = data.map(d => d.isCorrect ? 1 : 0);
    
    return this.pearsonCorrelation(confidences, accuracies);
  }

  private pearsonCorrelation(x: number[], y: number[]): number {
    const n = x.length;
    if (n !== y.length || n === 0) return 0;

    const sumX = x.reduce((a, b) => a + b, 0);
    const sumY = y.reduce((a, b) => a + b, 0);
    const sumXY = x.map((xi, i) => xi * y[i]).reduce((a, b) => a + b, 0);
    const sumX2 = x.map(xi => xi * xi).reduce((a, b) => a + b, 0);
    const sumY2 = y.map(yi => yi * yi).reduce((a, b) => a + b, 0);

    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));

    return denominator === 0 ? 0 : numerator / denominator;
  }

  private isPredictionCorrect(
    predicted: MarketRegime,
    actual: MarketRegime,
    priceMovement: number
  ): boolean {
    // Direct regime match
    if (predicted === actual) return true;

    // Check if prediction aligns with price movement
    if (predicted === MarketRegime.TRENDING_UP && priceMovement > 0.02) return true;
    if (predicted === MarketRegime.TRENDING_DOWN && priceMovement < -0.02) return true;
    if (predicted === MarketRegime.SIDEWAYS && Math.abs(priceMovement) < 0.01) return true;

    return false;
  }

  private getPredictionTimeframeHours(timeframe: TimeFrame): number {
    switch (timeframe) {
      case TimeFrame.M1: return 0.25;
      case TimeFrame.M5: return 1;
      case TimeFrame.M15: return 2;
      case TimeFrame.M30: return 4;
      case TimeFrame.H1: return 4;
      case TimeFrame.H4: return 8;
      case TimeFrame.D1: return 24;
      default: return 4;
    }
  }

  private inferMarketConditions(result: RegimeDetectionResult): 'normal' | 'high_volatility' | 'low_liquidity' | 'news_driven' {
    if (result.volatilityLevel > 0.8) return 'high_volatility';
    if (result.confidence < 0.4) return 'low_liquidity';
    if (result.regimeChangeDetected && result.regimeChangeMagnitude && result.regimeChangeMagnitude > 0.8) return 'news_driven';
    return 'normal';
  }

  private getPeriodKey(timestamp: Date, periodType: 'weekly' | 'monthly'): string {
    if (periodType === 'weekly') {
      const year = timestamp.getFullYear();
      const week = this.getWeekNumber(timestamp);
      return `${year}-W${week.toString().padStart(2, '0')}`;
    } else {
      const year = timestamp.getFullYear();
      const month = timestamp.getMonth() + 1;
      return `${year}-${month.toString().padStart(2, '0')}`;
    }
  }

  private getWeekNumber(date: Date): number {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d.getTime() - yearStart.getTime()) / 86400000) + 1) / 7);
  }

  private getAccuracyByInstrument() {
    const result: Record<string, { accuracy: number; totalPredictions: number }> = {};
    
    for (const [key, metrics] of this.accuracyMetrics.entries()) {
      result[key] = {
        accuracy: metrics.overallAccuracy,
        totalPredictions: metrics.totalPredictions,
      };
    }
    
    return result;
  }

  private getRecentAccuracyTrend(): { date: string; accuracy: number }[] {
    // Aggregate recent accuracy across all instruments
    const dailyAccuracy = new Map<string, { correct: number; total: number }>();
    
    for (const [, data] of this.accuracyData.entries()) {
      const verifiedData = data.filter(d => d.isCorrect !== undefined);
      
      for (const point of verifiedData) {
        const date = point.timestamp.toISOString().split('T')[0];
        if (!dailyAccuracy.has(date)) {
          dailyAccuracy.set(date, { correct: 0, total: 0 });
        }
        
        const dayData = dailyAccuracy.get(date)!;
        dayData.total++;
        if (point.isCorrect) dayData.correct++;
      }
    }
    
    return Array.from(dailyAccuracy.entries())
      .map(([date, data]) => ({
        date,
        accuracy: data.correct / data.total,
      }))
      .sort((a, b) => a.date.localeCompare(b.date))
      .slice(-30); // Last 30 days
  }

  private getConfidenceDistribution() {
    const distribution = {
      'low': { count: 0, accuracy: 0 },
      'medium': { count: 0, accuracy: 0 },
      'high': { count: 0, accuracy: 0 },
    };

    for (const [, data] of this.accuracyData.entries()) {
      const verifiedData = data.filter(d => d.isCorrect !== undefined);
      
      for (const point of verifiedData) {
        let bucket: 'low' | 'medium' | 'high';
        if (point.confidence < 0.5) bucket = 'low';
        else if (point.confidence < 0.75) bucket = 'medium';
        else bucket = 'high';
        
        distribution[bucket].count++;
        if (point.isCorrect) {
          distribution[bucket].accuracy++;
        }
      }
    }

    // Calculate accuracy percentages
    for (const bucket of Object.keys(distribution) as Array<keyof typeof distribution>) {
      const data = distribution[bucket];
      data.accuracy = data.count > 0 ? data.accuracy / data.count : 0;
    }

    return distribution;
  }

  /**
   * Export accuracy data for external analysis
   */
  public exportAccuracyData(instrument?: string, timeframe?: TimeFrame) {
    const result: { [key: string]: AccuracyDataPoint[] } = {};
    
    for (const [key, data] of this.accuracyData.entries()) {
      if (instrument && timeframe) {
        const [inst, tf] = key.split('_');
        if (inst !== instrument || tf !== timeframe) continue;
      }
      
      result[key] = data.filter(d => d.isCorrect !== undefined);
    }
    
    return result;
  }

  /**
   * Clear accuracy data (for maintenance or reset)
   */
  public clearAccuracyData(instrument?: string, timeframe?: TimeFrame): void {
    if (instrument && timeframe) {
      const key = `${instrument}_${timeframe}`;
      this.accuracyData.delete(key);
      this.accuracyMetrics.delete(key);
    } else {
      this.accuracyData.clear();
      this.accuracyMetrics.clear();
      this.verificationQueue.clear();
    }
    
    this.emit('accuracy_data_cleared', { instrument, timeframe });
  }
}