/**
 * Broker Configuration API Routes
 * 
 * API endpoints for managing multi-broker configurations
 * Part of Task 1: Multi-Broker Configuration System
 */

import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { BrokerConfigurationService } from '../services/trading/BrokerConfigurationService.js';
import { authMiddleware } from '../middleware/auth.js';
import { rateLimiter } from '../middleware/rate-limit.js';
import type { 
  CreateBrokerConfigurationRequest, 
  UpdateBrokerConfigurationRequest 
} from '@golddaddy/types';

const router = Router();
const prisma = new PrismaClient();
const brokerConfigService = new BrokerConfigurationService(prisma);

// Apply authentication to all routes
router.use(authMiddleware);

// Rate limiting for broker configuration operations
const brokerConfigRateLimit = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many broker configuration requests'
});

router.use(brokerConfigRateLimit);

/**
 * GET /api/broker-configurations
 * Get all broker configurations for the authenticated user
 */
router.get('/', async (req, res) => {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    const result = await brokerConfigService.getBrokerConfigurations(userId);
    
    if (result.success) {
      res.status(200).json(result);
    } else {
      const statusCode = result.error?.code === 'NOT_FOUND' ? 404 : 500;
      res.status(statusCode).json(result);
    }
    
  } catch (error) {
    console.error('Error getting broker configurations:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve broker configurations'
      }
    });
  }
});

/**
 * GET /api/broker-configurations/:id
 * Get a specific broker configuration by ID
 */
router.get('/:id', async (req, res) => {
  try {
    const userId = req.user?.id;
    const brokerId = req.params.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    if (!brokerId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Broker ID is required'
        }
      });
    }

    const result = await brokerConfigService.getBrokerConfiguration(userId, brokerId);
    
    if (result.success) {
      res.status(200).json(result);
    } else {
      const statusCode = result.error?.code === 'NOT_FOUND' ? 404 : 500;
      res.status(statusCode).json(result);
    }
    
  } catch (error) {
    console.error('Error getting broker configuration:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve broker configuration'
      }
    });
  }
});

/**
 * POST /api/broker-configurations
 * Create a new broker configuration
 */
router.post('/', async (req, res) => {
  try {
    const userId = req.user?.id;
    const request: CreateBrokerConfigurationRequest = req.body;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    // Basic request validation
    if (!request.brokerName || !request.connectionDetails || !request.features) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Missing required fields: brokerName, connectionDetails, and features'
        }
      });
    }

    const result = await brokerConfigService.createBrokerConfiguration(userId, request);
    
    if (result.success) {
      res.status(201).json(result);
    } else {
      const statusCode = result.error?.code === 'VALIDATION_ERROR' ? 400 : 
                        result.error?.code === 'BROKER_EXISTS' ? 409 : 500;
      res.status(statusCode).json(result);
    }
    
  } catch (error) {
    console.error('Error creating broker configuration:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to create broker configuration'
      }
    });
  }
});

/**
 * PUT /api/broker-configurations/:id
 * Update an existing broker configuration
 */
router.put('/:id', async (req, res) => {
  try {
    const userId = req.user?.id;
    const brokerId = req.params.id;
    const request: UpdateBrokerConfigurationRequest = req.body;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    if (!brokerId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Broker ID is required'
        }
      });
    }

    const result = await brokerConfigService.updateBrokerConfiguration(userId, brokerId, request);
    
    if (result.success) {
      res.status(200).json(result);
    } else {
      const statusCode = result.error?.code === 'NOT_FOUND' ? 404 : 
                        result.error?.code === 'VALIDATION_ERROR' ? 400 : 500;
      res.status(statusCode).json(result);
    }
    
  } catch (error) {
    console.error('Error updating broker configuration:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update broker configuration'
      }
    });
  }
});

/**
 * DELETE /api/broker-configurations/:id
 * Delete a broker configuration (soft delete)
 */
router.delete('/:id', async (req, res) => {
  try {
    const userId = req.user?.id;
    const brokerId = req.params.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    if (!brokerId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Broker ID is required'
        }
      });
    }

    const result = await brokerConfigService.deleteBrokerConfiguration(userId, brokerId);
    
    if (result.success) {
      res.status(204).send(); // No content on successful deletion
    } else {
      const statusCode = result.error?.code === 'NOT_FOUND' ? 404 : 500;
      res.status(statusCode).json(result);
    }
    
  } catch (error) {
    console.error('Error deleting broker configuration:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to delete broker configuration'
      }
    });
  }
});

/**
 * POST /api/broker-configurations/:id/test-connection
 * Test connection to a specific broker
 */
router.post('/:id/test-connection', async (req, res) => {
  try {
    const userId = req.user?.id;
    const brokerId = req.params.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'User not authenticated'
        }
      });
    }

    if (!brokerId) {
      return res.status(400).json({
        success: false,
        error: {
          code: 'INVALID_REQUEST',
          message: 'Broker ID is required'
        }
      });
    }

    // Get broker configuration
    const brokerResult = await brokerConfigService.getBrokerConfiguration(userId, brokerId);
    
    if (!brokerResult.success || !brokerResult.data) {
      return res.status(404).json({
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: 'Broker configuration not found'
        }
      });
    }

    // Mock connection test - in production this would actually test the MT5 connection
    const testStartTime = Date.now();
    
    // Simulate connection test delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    const testDuration = Date.now() - testStartTime;
    const testSuccess = Math.random() > 0.2; // 80% success rate for testing
    
    if (testSuccess) {
      // Update broker health status
      await brokerConfigService.updateBrokerHealth(brokerId, true, testDuration);
      
      res.status(200).json({
        success: true,
        data: {
          connectionTest: 'passed',
          latency: testDuration,
          brokerName: brokerResult.data.brokerName,
          timestamp: new Date().toISOString()
        }
      });
    } else {
      // Update broker health status with error
      const errorMessage = 'Connection test failed - broker unreachable';
      await brokerConfigService.updateBrokerHealth(brokerId, false, testDuration, errorMessage);
      
      res.status(200).json({
        success: true,
        data: {
          connectionTest: 'failed',
          latency: testDuration,
          error: errorMessage,
          brokerName: brokerResult.data.brokerName,
          timestamp: new Date().toISOString()
        }
      });
    }
    
  } catch (error) {
    console.error('Error testing broker connection:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to test broker connection'
      }
    });
  }
});

export default router;