# Task Completion Checklist

## CRITICAL: Post-Development Validation Steps

### 1. Code Quality Checks (MANDATORY)
```bash
npm run lint                        # Must pass - fix all issues
npm run format:check               # Must pass - run format if needed
npm run type-check                 # Must pass - fix all TypeScript errors
```

### 2. Testing Validation (MANDATORY)
```bash
npm run test                       # All tests must pass
npm run test:unit                  # Unit tests must pass
npm run test:integration          # Integration tests must pass
npm run test:e2e                  # E2E tests must pass (when applicable)
```

### 3. Build Validation
```bash
npm run build                      # Must build without errors
```

### 4. Pre-commit Hook Validation
- Husky + lint-staged will run automatically on commit
- ESLint auto-fixing
- Prettier formatting  
- TypeScript type checking

### 5. Branch Protection Requirements
- All code must pass linting and type checking
- Tests must pass before merging
- Commits follow conventional commit format

## Story Development Completion Criteria
- All Tasks and Subtasks marked [x] and have tests
- Validations and full regression passes
- File List is complete in story
- Code matches requirements
- Follows established patterns and standards
- Status set to 'Ready for Review'

## Never Skip These Steps
- Running lint and type-check is MANDATORY
- All tests must pass before task completion
- Build must succeed without errors
- Regression testing required for changes