{"permissions": {"allow": ["<PERSON><PERSON>(dir:*)", "Bash(npm install:*)", "Bash(npx create-mcp-server:*)", "WebSearch", "<PERSON><PERSON>(claude mcp:*)", "mcp__serena__find_file", "mcp__serena__activate_project", "WebFetch(domain:github.com)", "mcp__serena__list_dir", "Bash(md-tree:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(npm run build:*)", "<PERSON><PERSON>(docker-compose up:*)", "<PERSON><PERSON>(docker compose:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(npx husky:*)", "Bash(git init:*)", "Bash(npm run lint)", "Bash(npm run format:check:*)", "Bash(npm run format:*)", "Bash(git add:*)", "Bash(npm run type-check:*)", "Bash(npm run lint:*)", "Bash(rm:*)", "<PERSON><PERSON>(npx playwright:*)", "<PERSON><PERSON>(docker-compose:*)", "Bash(npm run test:*)", "Bash(npx prisma generate:*)", "<PERSON><PERSON>(docker:*)", "mcp__supabase__get_project_url", "mcp__supabase__list_tables", "mcp__supabase__list_migrations", "mcp__supabase__apply_migration", "mcp__supabase__get_anon_key", "mcp__supabase__deploy_edge_function", "mcp__supabase__list_edge_functions", "mcp__serena__check_onboarding_performed", "Bash(npm view:*)", "Bash(npm test:*)", "Bash(npx prisma migrate dev:*)", "Bash(cp:*)", "mcp__supabase__execute_sql", "Bash(npm run dev:*)", "Bash(node:*)", "mcp__serena__replace_symbol_body", "mcp__serena__find_symbol", "<PERSON><PERSON>(timeout 5 npm start)", "Bash(npx tsc:*)", "<PERSON><PERSON>(git clone:*)", "Bash(pip install:*)", "<PERSON><PERSON>(python:*)", "mcp__serena__think_about_whether_you_are_done", "mcp__serena__get_symbols_overview", "mcp__serena__read_memory", "Bash(venvScriptspython.exe -m pip install --upgrade pip)", "Bash(venv\\Scripts\\python.exe -m pip install:*)", "<PERSON><PERSON>(curl:*)", "mcp__serena__think_about_collected_information", "<PERSON><PERSON>(timeout 10 npm run dev)", "Bash(npx eslint:*)", "<PERSON><PERSON>(touch:*)", "mcp__serena__search_for_pattern", "Bash(npx vitest run:*)", "Bash(npm run docker:test:up:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(taskkill:*)", "Bash(PORT=3002 npm run dev)", "Bash(grep:*)", "Bash(npx tsx:*)", "Bash(find:*)", "<PERSON><PERSON>(sed:*)", "mcp__sequential-thinking__sequentialthinking", "Bash(DATABASE_URL=\"file:./dev.db\" npx prisma migrate dev --name init)", "Bash(DATABASE_URL=\"file:./dev.db\" npm test -- BrokerFailoverIntegration.test.ts)", "Bash(PORT=3002 timeout 3 npx tsx src/index.ts)", "Bash(for file in financial/PrecisionCalculations.test.ts risk-integration/*.test.ts stress/*.test.ts)", "Bash(do if [ -f \"$file\" ])", "Bash(then mv \"$file\" \"$file.skip\")", "<PERSON><PERSON>(echo:*)", "Bash(fi)", "Bash(done)", "mcp__serena__onboarding", "mcp__serena__write_memory", "mcp__serena__insert_after_symbol", "mcp__serena__insert_before_symbol"], "deny": [], "ask": [], "defaultMode": "acceptEdits", "additionalDirectories": ["C:\\c\\Users\\sherwingorechomante\\golddaddyph\\apps", "C:\\c\\Users\\sherwingorechomante\\golddaddyph\\packages", "C:\\c\\Users\\sherwingorechomante"]}, "enabledMcpjsonServers": ["supabase", "basic-memory"]}