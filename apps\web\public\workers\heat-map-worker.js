/**
 * Web Worker for heavy heat map data processing
 * Handles sorting, filtering, and calculations off the main thread
 */

// Worker message types
const MessageTypes = {
  SORT_DATA: 'SORT_DATA',
  FILTER_DATA: 'FILTER_DATA',
  CALCULATE_PERFORMANCE: 'CALCULATE_PERFORMANCE',
  BATCH_UPDATE: 'BATCH_UPDATE',
  GENERATE_EXPORT_DATA: 'GENERATE_EXPORT_DATA'
};

// Utility functions for data processing
function sortData(data, sortColumn, sortDirection, selectedTimeframe, viewMode) {
  return [...data].sort((a, b) => {
    let aValue, bValue;

    switch (sortColumn) {
      case 'name':
        aValue = a.name;
        bValue = b.name;
        break;
      case 'performance':
        aValue = a.performance[selectedTimeframe];
        bValue = b.performance[selectedTimeframe];
        break;
      case 'metadata':
        // Sort by volume for instruments/sectors, by trades for strategies
        if (viewMode === 'strategies') {
          aValue = a.metadata?.trades || 0;
          bValue = b.metadata?.trades || 0;
        } else {
          aValue = a.metadata?.volume || 0;
          bValue = b.metadata?.volume || 0;
        }
        break;
      default:
        return 0;
    }

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc' 
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    const numA = Number(aValue);
    const numB = Number(bValue);
    return sortDirection === 'asc' ? numA - numB : numB - numA;
  });
}

function filterData(data, filters) {
  return data.filter(item => {
    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      if (!item.name.toLowerCase().includes(searchLower)) {
        return false;
      }
    }

    // Apply view mode filter
    if (filters.viewMode && filters.viewMode !== 'all') {
      if (item.type !== filters.viewMode.slice(0, -1)) { // Remove 's' from end
        return false;
      }
    }

    // Apply performance range filter
    if (filters.performanceRange && filters.selectedTimeframe) {
      const performance = item.performance[filters.selectedTimeframe];
      const { min, max } = filters.performanceRange;
      if (performance < min || performance > max) {
        return false;
      }
    }

    // Apply metadata filter
    if (filters.minVolume && item.metadata?.volume) {
      if (item.metadata.volume < filters.minVolume) {
        return false;
      }
    }

    return true;
  });
}

function calculatePerformanceMetrics(data, timeframe) {
  if (data.length === 0) return null;

  const performances = data.map(item => item.performance[timeframe]).filter(p => typeof p === 'number');
  
  if (performances.length === 0) return null;

  const sorted = [...performances].sort((a, b) => a - b);
  const sum = performances.reduce((acc, val) => acc + val, 0);
  
  return {
    average: sum / performances.length,
    median: sorted.length % 2 === 0 
      ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
      : sorted[Math.floor(sorted.length / 2)],
    min: Math.min(...performances),
    max: Math.max(...performances),
    count: performances.length,
    positive: performances.filter(p => p > 0).length,
    negative: performances.filter(p => p < 0).length,
    neutral: performances.filter(p => p === 0).length
  };
}

function batchUpdateData(originalData, updates) {
  const dataMap = new Map(originalData.map(item => [item.id, { ...item }]));
  
  updates.forEach(update => {
    if (dataMap.has(update.id)) {
      const item = dataMap.get(update.id);
      
      // Update performance data
      if (update.performance) {
        Object.assign(item.performance, update.performance);
      }
      
      // Update metadata
      if (update.metadata) {
        item.metadata = { ...item.metadata, ...update.metadata };
      }
      
      // Update other fields
      if (update.name) item.name = update.name;
      if (update.type) item.type = update.type;
    }
  });
  
  return Array.from(dataMap.values());
}

function generateExportData(data, format, options = {}) {
  const { includeMetadata = true, selectedTimeframe = '1d', viewMode = 'all' } = options;
  
  if (format === 'csv') {
    // Generate CSV headers
    const headers = ['Name', 'Type', `Performance (${selectedTimeframe})`];
    
    if (includeMetadata) {
      headers.push('Volume', 'Market Cap', 'Trades', 'Win Rate', 'Sharpe Ratio');
    }
    
    // Generate CSV rows
    const rows = data.map(item => {
      const row = [
        item.name,
        item.type,
        item.performance[selectedTimeframe].toFixed(2)
      ];
      
      if (includeMetadata) {
        row.push(
          item.metadata?.volume?.toFixed(0) || '0',
          item.metadata?.marketCap?.toFixed(0) || '0',
          item.metadata?.trades?.toString() || '0',
          item.metadata?.winRate?.toFixed(2) || '0',
          item.metadata?.sharpeRatio?.toFixed(2) || '0'
        );
      }
      
      return row.join(',');
    });
    
    return [headers.join(','), ...rows].join('\n');
  }
  
  if (format === 'json') {
    return JSON.stringify(data, null, 2);
  }
  
  throw new Error(`Unsupported export format: ${format}`);
}

// Message handler
self.onmessage = function(e) {
  const { type, data, id } = e.data;
  
  try {
    let result;
    
    switch (type) {
      case MessageTypes.SORT_DATA:
        result = sortData(
          data.items,
          data.sortColumn,
          data.sortDirection,
          data.selectedTimeframe,
          data.viewMode
        );
        break;
        
      case MessageTypes.FILTER_DATA:
        result = filterData(data.items, data.filters);
        break;
        
      case MessageTypes.CALCULATE_PERFORMANCE:
        result = calculatePerformanceMetrics(data.items, data.timeframe);
        break;
        
      case MessageTypes.BATCH_UPDATE:
        result = batchUpdateData(data.originalData, data.updates);
        break;
        
      case MessageTypes.GENERATE_EXPORT_DATA:
        result = generateExportData(data.items, data.format, data.options);
        break;
        
      default:
        throw new Error(`Unknown message type: ${type}`);
    }
    
    // Send success response
    self.postMessage({
      id,
      type: `${type}_SUCCESS`,
      result,
      timestamp: Date.now()
    });
    
  } catch (error) {
    // Send error response
    self.postMessage({
      id,
      type: `${type}_ERROR`,
      error: {
        message: error.message,
        stack: error.stack
      },
      timestamp: Date.now()
    });
  }
};

// Send ready message
self.postMessage({
  type: 'WORKER_READY',
  timestamp: Date.now()
});