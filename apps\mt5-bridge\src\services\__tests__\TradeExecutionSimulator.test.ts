import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TradeExecutionSimulator, type ExecutionConfig, type ExecutionRequest } from '../TradeExecutionSimulator';
import type { MarketData } from '@golddaddy/types';

describe('TradeExecutionSimulator', () => {
  let simulator: TradeExecutionSimulator;
  let mockConfig: Partial<ExecutionConfig>;

  beforeEach(() => {
    mockConfig = {
      enableSlippage: true,
      slippageModel: 'variable',
      baseSlippagePips: 1.0,
      volatilityMultiplier: 1.5,
      latencyRange: [10, 50], // Reduced for testing
      enablePartialFills: true,
      partialFillThreshold: 1.0,
      enableRejects: false, // Disabled for most tests
      rejectRate: 0,
      marketImpactEnabled: true,
      liquidityLevels: {
        'EURUSD': 10000,
        'GBPUSD': 8000,
        'USDJPY': 7000
      }
    };

    simulator = new TradeExecutionSimulator(mockConfig);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Configuration', () => {
    it('should initialize with default configuration', () => {
      const defaultSimulator = new TradeExecutionSimulator();
      expect(defaultSimulator).toBeDefined();
    });

    it('should apply custom configuration', () => {
      const customConfig = {
        enableSlippage: false,
        baseSlippagePips: 2.0,
        latencyRange: [0, 0] as [number, number]
      };
      
      const customSimulator = new TradeExecutionSimulator(customConfig);
      expect(customSimulator).toBeDefined();
    });
  });

  describe('Market Data Updates', () => {
    it('should accept market data updates', () => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };

      expect(() => simulator.updateMarketData(marketData)).not.toThrow();
    });

    it('should process multiple market data updates', () => {
      const symbols = ['EURUSD', 'GBPUSD', 'USDJPY'];
      
      symbols.forEach(symbol => {
        const marketData: MarketData = {
          symbol,
          bid: 1.0850,
          ask: 1.0852,
          spread: 0.0002,
          timestamp: new Date()
        };
        
        expect(() => simulator.updateMarketData(marketData)).not.toThrow();
      });
    });
  });

  describe('Order Execution', () => {
    beforeEach(() => {
      // Setup market data
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should execute market buy order', async () => {
      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 1.0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Test order',
        requestId: 'test-001'
      };

      const result = await simulator.submitExecution(request);
      
      expect(result.requestId).toBe('test-001');
      expect(result.status).toBe('filled');
      expect(result.executedVolume).toBe(1.0);
      expect(result.remainingVolume).toBe(0);
      expect(result.executionPrice).toBeGreaterThan(0);
      expect(result.fills).toHaveLength(1);
    });

    it('should execute market sell order', async () => {
      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'sell',
        volume: 0.5,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Test sell order',
        requestId: 'test-002'
      };

      const result = await simulator.submitExecution(request);
      
      expect(result.requestId).toBe('test-002');
      expect(result.status).toBe('filled');
      expect(result.executedVolume).toBe(0.5);
      expect(result.executionPrice).toBeGreaterThan(0);
    });

    it('should apply slippage when enabled', async () => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      
      const expectedPrice = marketData.ask;
      
      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 1.0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Slippage test',
        requestId: 'test-003'
      };

      const result = await simulator.submitExecution(request);
      
      expect(result.slippage).toBeGreaterThan(0);
      expect(Math.abs(result.executionPrice - expectedPrice)).toBeGreaterThan(0);
    });

    it('should handle partial fills for large orders', async () => {
      simulator.configure({ 
        enablePartialFills: true, 
        partialFillThreshold: 0.5 
      });

      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 2.0, // Large order to trigger partial fill
        fillPolicy: 'return',
        magic: 12345,
        comment: 'Partial fill test',
        requestId: 'test-004'
      };

      const result = await simulator.submitExecution(request);
      
      expect(result.requestId).toBe('test-004');
      expect(result.fills.length).toBeGreaterThan(1);
      
      const totalFilled = result.fills.reduce((sum, fill) => sum + fill.volume, 0);
      expect(totalFilled).toBe(result.executedVolume);
    });

    it('should reject orders when rejection is enabled', async () => {
      simulator.configure({ 
        enableRejects: true, 
        rejectRate: 1.0 // Force rejection
      });

      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 1.0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Rejection test',
        requestId: 'test-005'
      };

      const result = await simulator.submitExecution(request);
      
      expect(result.status).toBe('rejected');
      expect(result.executedVolume).toBe(0);
      expect(result.rejectReason).toBeDefined();
    });

    it('should calculate commission correctly', async () => {
      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 1.0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Commission test',
        requestId: 'test-006'
      };

      const result = await simulator.submitExecution(request);
      
      expect(result.commission).toBeGreaterThanOrEqual(0);
      expect(result.commission).toBe(result.executedVolume * 3.5); // Default commission rate
    });

    it('should respect latency configuration', async () => {
      simulator.configure({ latencyRange: [100, 200] });

      const startTime = Date.now();
      
      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 1.0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Latency test',
        requestId: 'test-007'
      };

      const result = await simulator.submitExecution(request);
      const endTime = Date.now();
      
      const actualLatency = endTime - startTime;
      expect(actualLatency).toBeGreaterThanOrEqual(100);
      expect(result.latency).toBeGreaterThanOrEqual(100);
      expect(result.latency).toBeLessThanOrEqual(200);
    });
  });

  describe('Market Impact Modeling', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should apply market impact for large orders', async () => {
      const smallOrderRequest: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 0.1,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Small order',
        requestId: 'small-001'
      };

      const largeOrderRequest: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 10.0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Large order',
        requestId: 'large-001'
      };

      const smallResult = await simulator.submitExecution(smallOrderRequest);
      const largeResult = await simulator.submitExecution(largeOrderRequest);

      // Large orders should have higher effective prices due to market impact
      expect(largeResult.executionPrice).toBeGreaterThan(smallResult.executionPrice);
    });

    it('should disable market impact when configured', async () => {
      simulator.configure({ marketImpactEnabled: false });

      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 10.0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'No impact test',
        requestId: 'no-impact-001'
      };

      const result = await simulator.submitExecution(request);
      expect(result.status).toBe('filled');
    });
  });

  describe('Slippage Models', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should use fixed slippage model', async () => {
      simulator.configure({ 
        slippageModel: 'fixed',
        baseSlippagePips: 1.0
      });

      const requests = Array.from({ length: 5 }, (_, i) => ({
        symbol: 'EURUSD',
        type: 'market' as const,
        side: 'buy' as const,
        volume: 1.0,
        fillPolicy: 'fok' as const,
        magic: 12345,
        comment: `Fixed slippage test ${i}`,
        requestId: `fixed-${i}`
      }));

      const results = await Promise.all(
        requests.map(req => simulator.submitExecution(req))
      );

      // With fixed slippage, all slippage values should be the same
      const slippages = results.map(r => r.slippage);
      const firstSlippage = slippages[0];
      
      slippages.forEach(slippage => {
        expect(Math.abs(slippage - firstSlippage)).toBeLessThan(0.0001);
      });
    });

    it('should use variable slippage model', async () => {
      simulator.configure({ 
        slippageModel: 'variable',
        baseSlippagePips: 1.0,
        volatilityMultiplier: 2.0
      });

      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 1.0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Variable slippage test',
        requestId: 'variable-001'
      };

      const result = await simulator.submitExecution(request);
      expect(result.slippage).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing market data gracefully', async () => {
      const request: ExecutionRequest = {
        symbol: 'INVALID',
        type: 'market',
        side: 'buy',
        volume: 1.0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Invalid symbol test',
        requestId: 'invalid-001'
      };

      await expect(simulator.submitExecution(request)).rejects.toThrow();
    });

    it('should handle zero volume orders', async () => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);

      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Zero volume test',
        requestId: 'zero-001'
      };

      const result = await simulator.submitExecution(request);
      expect(result.status).toBe('rejected');
    });
  });

  describe('Statistics and Monitoring', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should track execution statistics', async () => {
      const requests = Array.from({ length: 10 }, (_, i) => ({
        symbol: 'EURUSD',
        type: 'market' as const,
        side: 'buy' as const,
        volume: 1.0,
        fillPolicy: 'fok' as const,
        magic: 12345,
        comment: `Stats test ${i}`,
        requestId: `stats-${i}`
      }));

      await Promise.all(requests.map(req => simulator.submitExecution(req)));

      const stats = simulator.getExecutionStats();
      
      expect(stats.totalExecutions).toBe(10);
      expect(stats.fillRate).toBeGreaterThan(0);
      expect(stats.avgSlippage).toBeGreaterThan(0);
      expect(stats.avgLatency).toBeGreaterThan(0);
    });

    it('should provide meaningful statistics for mixed results', async () => {
      // Configure for some rejections
      simulator.configure({ enableRejects: true, rejectRate: 0.3 });

      const requests = Array.from({ length: 20 }, (_, i) => ({
        symbol: 'EURUSD',
        type: 'market' as const,
        side: 'buy' as const,
        volume: 1.0,
        fillPolicy: 'fok' as const,
        magic: 12345,
        comment: `Mixed stats test ${i}`,
        requestId: `mixed-${i}`
      }));

      await Promise.all(requests.map(req => simulator.submitExecution(req)));

      const stats = simulator.getExecutionStats();
      
      expect(stats.totalExecutions).toBe(20);
      expect(stats.fillRate).toBeLessThan(1.0); // Some should be rejected
      expect(stats.rejectRate).toBeGreaterThan(0);
    });
  });

  describe('Events', () => {
    beforeEach(() => {
      const marketData: MarketData = {
        symbol: 'EURUSD',
        bid: 1.0850,
        ask: 1.0852,
        spread: 0.0002,
        timestamp: new Date()
      };
      simulator.updateMarketData(marketData);
    });

    it('should emit execution events', async () => {
      let executionEventReceived = false;
      
      simulator.on('execution', (result) => {
        executionEventReceived = true;
        expect(result.requestId).toBe('event-001');
      });

      const request: ExecutionRequest = {
        symbol: 'EURUSD',
        type: 'market',
        side: 'buy',
        volume: 1.0,
        fillPolicy: 'fok',
        magic: 12345,
        comment: 'Event test',
        requestId: 'event-001'
      };

      await simulator.submitExecution(request);
      
      // Give event time to propagate
      await new Promise(resolve => setTimeout(resolve, 100));
      
      expect(executionEventReceived).toBe(true);
    });
  });

  describe('Configuration Updates', () => {
    it('should allow runtime configuration changes', () => {
      const newConfig = {
        enableSlippage: false,
        baseSlippagePips: 2.0,
        latencyRange: [0, 0] as [number, number]
      };

      expect(() => simulator.configure(newConfig)).not.toThrow();
    });

    it('should reset state correctly', () => {
      simulator.reset();
      
      const stats = simulator.getExecutionStats();
      expect(stats.totalExecutions).toBe(0);
    });
  });
});