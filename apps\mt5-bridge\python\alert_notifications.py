"""
Alert Notification System
Handles alert notifications through various channels including WebSocket broadcasts
"""

import asyncio
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from loguru import logger

from health_monitor import Alert, AlertLevel

class AlertNotificationManager:
    """
    Manages alert notifications and integrates with various notification channels
    """
    
    def __init__(self):
        self.notification_channels: List[Callable] = []
        self.alert_history: List[Dict[str, Any]] = []
        self.max_history_size = 500
        
        # Notification settings
        self.notification_config = {
            'rate_limit_seconds': 60,  # Minimum time between same alert types
            'batch_size': 10,  # Maximum alerts to send in one batch
            'priority_levels': {
                AlertLevel.CRITICAL: 1,
                AlertLevel.ERROR: 2,
                AlertLevel.WARNING: 3,
                AlertLevel.INFO: 4
            }
        }
        
        # Rate limiting tracking
        self.last_notification_time: Dict[str, datetime] = {}
        
    def add_notification_channel(self, channel: Callable):
        """Add a notification channel (callback function)"""
        self.notification_channels.append(channel)
        logger.info(f"📢 Added notification channel: {channel.__name__}")
        
    async def process_alert(self, alert: Alert):
        """Process and send notifications for an alert"""
        
        try:
            # Check rate limiting
            alert_key = f"{alert.component}_{alert.level.value}"
            current_time = datetime.now()
            
            if alert_key in self.last_notification_time:
                time_since_last = (current_time - self.last_notification_time[alert_key]).total_seconds()
                if time_since_last < self.notification_config['rate_limit_seconds']:
                    logger.debug(f"Rate limiting alert: {alert_key}")
                    return
            
            self.last_notification_time[alert_key] = current_time
            
            # Prepare alert data for notification
            alert_data = {
                'id': alert.id,
                'level': alert.level.value,
                'component': alert.component,
                'message': alert.message,
                'details': alert.details,
                'timestamp': alert.timestamp.isoformat(),
                'priority': self.notification_config['priority_levels'].get(alert.level, 4)
            }
            
            # Add to history
            self.alert_history.append(alert_data)
            if len(self.alert_history) > self.max_history_size:
                self.alert_history = self.alert_history[-self.max_history_size:]
            
            # Send to all notification channels
            for channel in self.notification_channels:
                try:
                    if asyncio.iscoroutinefunction(channel):
                        await channel(alert_data)
                    else:
                        channel(alert_data)
                except Exception as e:
                    logger.error(f"❌ Notification channel error: {e}")
            
            logger.info(f"📢 Alert notification sent: [{alert.level.value}] {alert.component}")
            
        except Exception as e:
            logger.error(f"❌ Failed to process alert notification: {e}")
    
    async def get_recent_alerts(self, limit: int = 50, level_filter: Optional[str] = None) -> List[Dict[str, Any]]:
        """Get recent alerts with optional filtering"""
        
        alerts = self.alert_history
        
        # Filter by level if specified
        if level_filter:
            alerts = [a for a in alerts if a['level'] == level_filter.upper()]
        
        # Sort by timestamp (most recent first) and limit
        alerts.sort(key=lambda x: x['timestamp'], reverse=True)
        return alerts[:limit]
    
    def get_alert_statistics(self) -> Dict[str, Any]:
        """Get alert statistics"""
        
        if not self.alert_history:
            return {
                'total_alerts': 0,
                'by_level': {},
                'by_component': {},
                'recent_24h': 0
            }
        
        # Count by level
        by_level = {}
        for alert in self.alert_history:
            level = alert['level']
            by_level[level] = by_level.get(level, 0) + 1
        
        # Count by component
        by_component = {}
        for alert in self.alert_history:
            component = alert['component']
            by_component[component] = by_component.get(component, 0) + 1
        
        # Count recent alerts (24 hours)
        current_time = datetime.now()
        recent_24h = 0
        for alert in self.alert_history:
            alert_time = datetime.fromisoformat(alert['timestamp'])
            if (current_time - alert_time).total_seconds() < 86400:  # 24 hours
                recent_24h += 1
        
        return {
            'total_alerts': len(self.alert_history),
            'by_level': by_level,
            'by_component': by_component,
            'recent_24h': recent_24h,
            'oldest_alert': self.alert_history[0]['timestamp'] if self.alert_history else None,
            'newest_alert': self.alert_history[-1]['timestamp'] if self.alert_history else None
        }

# Global instance
_alert_notification_manager: Optional[AlertNotificationManager] = None

def get_alert_notification_manager() -> AlertNotificationManager:
    """Get global alert notification manager instance"""
    global _alert_notification_manager
    if _alert_notification_manager is None:
        _alert_notification_manager = AlertNotificationManager()
    return _alert_notification_manager

# WebSocket notification callback
async def websocket_alert_callback(alert_data: Dict[str, Any]):
    """Send alert via WebSocket to connected clients"""
    
    try:
        # Import here to avoid circular imports
        from main import manager
        
        # Prepare WebSocket message
        websocket_message = {
            'type': 'health_alert',
            'data': alert_data,
            'timestamp': datetime.now().isoformat()
        }
        
        # Broadcast to all connected clients
        await manager.broadcast(websocket_message)
        
        logger.debug(f"📡 Alert broadcast via WebSocket: {alert_data['level']}")
        
    except Exception as e:
        logger.error(f"❌ WebSocket alert callback error: {e}")

# Console logging callback
def console_alert_callback(alert_data: Dict[str, Any]):
    """Log alert to console with appropriate level"""
    
    level = alert_data['level']
    component = alert_data['component']
    message = alert_data['message']
    
    if level == 'CRITICAL':
        logger.critical(f"🔴 CRITICAL ALERT - {component}: {message}")
    elif level == 'ERROR':
        logger.error(f"🟠 ERROR ALERT - {component}: {message}")
    elif level == 'WARNING':
        logger.warning(f"🟡 WARNING ALERT - {component}: {message}")
    else:
        logger.info(f"🔵 INFO ALERT - {component}: {message}")

# Email notification callback (placeholder)
async def email_alert_callback(alert_data: Dict[str, Any]):
    """Send alert via email (placeholder implementation)"""
    
    try:
        # This would integrate with an email service
        level = alert_data['level']
        component = alert_data['component']
        message = alert_data['message']
        
        # Only send emails for critical and error alerts
        if level in ['CRITICAL', 'ERROR']:
            logger.info(f"📧 Email alert sent: [{level}] {component}: {message}")
            # TODO: Implement actual email sending
        
    except Exception as e:
        logger.error(f"❌ Email alert callback error: {e}")

# Slack notification callback (placeholder)
async def slack_alert_callback(alert_data: Dict[str, Any]):
    """Send alert via Slack (placeholder implementation)"""
    
    try:
        level = alert_data['level']
        component = alert_data['component']
        message = alert_data['message']
        
        # Only send Slack notifications for critical alerts
        if level == 'CRITICAL':
            logger.info(f"💬 Slack alert sent: [{level}] {component}: {message}")
            # TODO: Implement actual Slack webhook integration
        
    except Exception as e:
        logger.error(f"❌ Slack alert callback error: {e}")

def setup_default_notification_channels():
    """Setup default notification channels"""
    
    manager = get_alert_notification_manager()
    
    # Add default channels
    manager.add_notification_channel(websocket_alert_callback)
    manager.add_notification_channel(console_alert_callback)
    manager.add_notification_channel(email_alert_callback)
    manager.add_notification_channel(slack_alert_callback)
    
    logger.info("✅ Default notification channels configured")