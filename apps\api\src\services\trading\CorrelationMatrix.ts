/**
 * Correlation Matrix Utility
 * 
 * Implements real-time correlation calculation between trading instruments,
 * dynamic correlation updates based on rolling historical data, and
 * correlation-adjusted position sizing recommendations.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';

// Correlation Matrix Types
export interface CorrelationData {
  symbol1: string;
  symbol2: string;
  correlation: number;
  confidence: number; // 0-1, based on data quality and sample size
  lastUpdated: Date;
  sampleSize: number;
}

export interface PriceData {
  timestamp: Date;
  symbol: string;
  price: Decimal.Instance;
  volume?: Decimal.Instance;
}

export interface CorrelationMatrixConfig {
  rollingWindow: number; // Number of periods for rolling correlation
  minSampleSize: number; // Minimum data points required
  updateFrequency: number; // Update frequency in milliseconds
  significanceThreshold: number; // Minimum correlation considered significant
}

export interface CorrelationAdjustment {
  originalSize: Decimal.Instance;
  adjustedSize: Decimal.Instance;
  adjustmentFactor: number;
  reasoning: string;
  correlationRisk: number;
}

/**
 * Correlation Matrix Utility
 * Manages real-time correlation calculations and updates
 */
export class CorrelationMatrix {
  private correlationData: Map<string, CorrelationData> = new Map();
  private priceHistory: Map<string, PriceData[]> = new Map();
  private readonly config: CorrelationMatrixConfig;
  private updateTimer: NodeJS.Timeout | null = null;

  constructor(config?: Partial<CorrelationMatrixConfig>) {
    this.config = {
      rollingWindow: 60, // 60 periods
      minSampleSize: 30, // Minimum 30 data points
      updateFrequency: 300000, // 5 minutes
      significanceThreshold: 0.1, // 10% correlation threshold
      ...config
    };

    this.startPeriodicUpdates();
  }

  /**
   * Add price data for correlation calculation
   */
  public addPriceData(data: PriceData): void {
    const symbol = data.symbol;
    
    if (!this.priceHistory.has(symbol)) {
      this.priceHistory.set(symbol, []);
    }

    const history = this.priceHistory.get(symbol)!;
    history.push(data);

    // Maintain rolling window
    if (history.length > this.config.rollingWindow) {
      history.splice(0, history.length - this.config.rollingWindow);
    }

    // Update correlations with other symbols
    this.updateCorrelationsForSymbol(symbol);
  }

  /**
   * Calculate correlation between two symbols
   */
  public calculateCorrelation(symbol1: string, symbol2: string): CorrelationData | null {
    const key = this.getCorrelationKey(symbol1, symbol2);
    const cached = this.correlationData.get(key);

    // Return cached data if recent
    if (cached && this.isCacheValid(cached)) {
      return cached;
    }

    // Calculate fresh correlation
    const correlation = this.computeCorrelation(symbol1, symbol2);
    if (correlation) {
      this.correlationData.set(key, correlation);
    }

    return correlation;
  }

  /**
   * Get correlation matrix for multiple symbols
   */
  public getCorrelationMatrix(symbols: string[]): number[][] {
    const matrix: number[][] = [];

    for (let i = 0; i < symbols.length; i++) {
      matrix[i] = [];
      for (let j = 0; j < symbols.length; j++) {
        if (i === j) {
          matrix[i][j] = 1.0;
        } else {
          const correlation = this.calculateCorrelation(symbols[i], symbols[j]);
          matrix[i][j] = correlation?.correlation || 0;
        }
      }
    }

    return matrix;
  }

  /**
   * Get all correlations for a specific symbol
   */
  public getSymbolCorrelations(symbol: string): CorrelationData[] {
    const correlations: CorrelationData[] = [];

    for (const [key, data] of this.correlationData) {
      if (data.symbol1 === symbol || data.symbol2 === symbol) {
        correlations.push(data);
      }
    }

    return correlations.sort((a, b) => Math.abs(b.correlation) - Math.abs(a.correlation));
  }

  /**
   * Find highly correlated symbols
   */
  public findHighlyCorrelated(symbol: string, threshold?: number): CorrelationData[] {
    const correlationThreshold = threshold || 0.7;
    const symbolCorrelations = this.getSymbolCorrelations(symbol);

    return symbolCorrelations.filter(data => 
      Math.abs(data.correlation) >= correlationThreshold &&
      data.confidence >= 0.8
    );
  }

  /**
   * Adjust position size based on correlation risk
   */
  public adjustPositionSizeForCorrelation(
    symbol: string,
    proposedSize: Decimal,
    existingPositions: { symbol: string; size: Decimal }[]
  ): CorrelationAdjustment {
    let maxCorrelation = 0;
    let correlatedSymbols: string[] = [];

    // Check correlations with existing positions
    for (const position of existingPositions) {
      if (position.symbol === symbol) continue;

      const correlation = this.calculateCorrelation(symbol, position.symbol);
      if (correlation && Math.abs(correlation.correlation) > Math.abs(maxCorrelation)) {
        maxCorrelation = correlation.correlation;
        correlatedSymbols = [position.symbol];
      } else if (correlation && Math.abs(correlation.correlation) === Math.abs(maxCorrelation)) {
        correlatedSymbols.push(position.symbol);
      }
    }

    // Calculate adjustment factor based on correlation
    const correlationRisk = Math.abs(maxCorrelation);
    let adjustmentFactor = 1.0;
    let reasoning = 'No significant correlations detected';

    if (correlationRisk > 0.8) {
      adjustmentFactor = 0.5; // Reduce by 50% for very high correlation
      reasoning = `Very high correlation (${(correlationRisk * 100).toFixed(1)}%) with ${correlatedSymbols.join(', ')}`;
    } else if (correlationRisk > 0.6) {
      adjustmentFactor = 0.7; // Reduce by 30% for high correlation
      reasoning = `High correlation (${(correlationRisk * 100).toFixed(1)}%) with ${correlatedSymbols.join(', ')}`;
    } else if (correlationRisk > 0.4) {
      adjustmentFactor = 0.85; // Reduce by 15% for moderate correlation
      reasoning = `Moderate correlation (${(correlationRisk * 100).toFixed(1)}%) with ${correlatedSymbols.join(', ')}`;
    }

    const adjustedSize = proposedSize.mul(adjustmentFactor);

    return {
      originalSize: proposedSize,
      adjustedSize,
      adjustmentFactor,
      reasoning,
      correlationRisk
    };
  }

  /**
   * Get correlation statistics
   */
  public getCorrelationStatistics(): {
    totalPairs: number;
    highCorrelations: number;
    averageCorrelation: number;
    mostCorrelated: CorrelationData | null;
    leastCorrelated: CorrelationData | null;
  } {
    const correlations = Array.from(this.correlationData.values());
    
    if (correlations.length === 0) {
      return {
        totalPairs: 0,
        highCorrelations: 0,
        averageCorrelation: 0,
        mostCorrelated: null,
        leastCorrelated: null
      };
    }

    const highCorrelations = correlations.filter(c => Math.abs(c.correlation) > 0.7).length;
    const averageCorrelation = correlations.reduce((sum, c) => sum + Math.abs(c.correlation), 0) / correlations.length;
    
    const sortedByCorrelation = correlations.sort((a, b) => Math.abs(b.correlation) - Math.abs(a.correlation));
    const mostCorrelated = sortedByCorrelation[0] || null;
    const leastCorrelated = sortedByCorrelation[sortedByCorrelation.length - 1] || null;

    return {
      totalPairs: correlations.length,
      highCorrelations,
      averageCorrelation,
      mostCorrelated,
      leastCorrelated
    };
  }

  /**
   * Clear correlation data for a symbol
   */
  public clearSymbolData(symbol: string): void {
    // Remove price history
    this.priceHistory.delete(symbol);

    // Remove correlations involving this symbol
    const keysToDelete: string[] = [];
    for (const [key, data] of this.correlationData) {
      if (data.symbol1 === symbol || data.symbol2 === symbol) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.correlationData.delete(key));
  }

  /**
   * Export correlation matrix as CSV
   */
  public exportToCSV(symbols: string[]): string {
    const matrix = this.getCorrelationMatrix(symbols);
    let csv = `,${  symbols.join(',')  }\n`;

    for (let i = 0; i < symbols.length; i++) {
      csv += `${symbols[i]  },${  matrix[i].map(val => val.toFixed(4)).join(',')  }\n`;
    }

    return csv;
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }
    this.correlationData.clear();
    this.priceHistory.clear();
  }

  // Private helper methods

  private startPeriodicUpdates(): void {
    this.updateTimer = setInterval(() => {
      this.updateAllCorrelations();
    }, this.config.updateFrequency);
  }

  private updateAllCorrelations(): void {
    const symbols = Array.from(this.priceHistory.keys());
    
    for (let i = 0; i < symbols.length; i++) {
      for (let j = i + 1; j < symbols.length; j++) {
        this.computeCorrelation(symbols[i], symbols[j]);
      }
    }
  }

  private updateCorrelationsForSymbol(symbol: string): void {
    const allSymbols = Array.from(this.priceHistory.keys());
    
    for (const otherSymbol of allSymbols) {
      if (otherSymbol !== symbol) {
        this.computeCorrelation(symbol, otherSymbol);
      }
    }
  }

  private computeCorrelation(symbol1: string, symbol2: string): CorrelationData | null {
    const data1 = this.priceHistory.get(symbol1);
    const data2 = this.priceHistory.get(symbol2);

    if (!data1 || !data2 || data1.length < this.config.minSampleSize || data2.length < this.config.minSampleSize) {
      return null;
    }

    // Calculate returns
    const returns1 = this.calculateReturns(data1);
    const returns2 = this.calculateReturns(data2);

    if (returns1.length < 2 || returns2.length < 2) {
      return null;
    }

    // Align data by timestamp and calculate correlation
    const alignedData = this.alignReturns(returns1, returns2);
    if (alignedData.length < this.config.minSampleSize) {
      return null;
    }

    const correlation = this.calculatePearsonCorrelation(
      alignedData.map(d => d.return1),
      alignedData.map(d => d.return2)
    );

    // Calculate confidence based on sample size and data quality
    const confidence = Math.min(1.0, alignedData.length / (this.config.minSampleSize * 2));

    return {
      symbol1,
      symbol2,
      correlation,
      confidence,
      lastUpdated: new Date(),
      sampleSize: alignedData.length
    };
  }

  private calculateReturns(priceData: PriceData[]): { timestamp: Date; return: number }[] {
    const returns: { timestamp: Date; return: number }[] = [];

    for (let i = 1; i < priceData.length; i++) {
      const currentPrice = priceData[i].price;
      const previousPrice = priceData[i - 1].price;
      
      if (!previousPrice.isZero()) {
        const returnValue = currentPrice.sub(previousPrice).div(previousPrice).toNumber();
        returns.push({
          timestamp: priceData[i].timestamp,
          return: returnValue
        });
      }
    }

    return returns;
  }

  private alignReturns(
    returns1: { timestamp: Date; return: number }[],
    returns2: { timestamp: Date; return: number }[]
  ): { timestamp: Date; return1: number; return2: number }[] {
    const aligned: { timestamp: Date; return1: number; return2: number }[] = [];

    // Create maps for faster lookup
    const returns2Map = new Map<string, number>();
    returns2.forEach(r => returns2Map.set(r.timestamp.toISOString(), r.return));

    for (const r1 of returns1) {
      const timestampKey = r1.timestamp.toISOString();
      const r2 = returns2Map.get(timestampKey);
      
      if (r2 !== undefined) {
        aligned.push({
          timestamp: r1.timestamp,
          return1: r1.return,
          return2: r2
        });
      }
    }

    return aligned;
  }

  private calculatePearsonCorrelation(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length);
    if (n < 2) return 0;

    const meanX = x.slice(0, n).reduce((sum, val) => sum + val, 0) / n;
    const meanY = y.slice(0, n).reduce((sum, val) => sum + val, 0) / n;

    let numerator = 0;
    let sumSquareX = 0;
    let sumSquareY = 0;

    for (let i = 0; i < n; i++) {
      const deltaX = x[i] - meanX;
      const deltaY = y[i] - meanY;
      numerator += deltaX * deltaY;
      sumSquareX += deltaX * deltaX;
      sumSquareY += deltaY * deltaY;
    }

    const denominator = Math.sqrt(sumSquareX * sumSquareY);
    
    if (denominator === 0) {
      return 0;
    }

    const correlation = numerator / denominator;
    return Math.max(-1, Math.min(1, correlation)); // Clamp to [-1, 1]
  }

  private getCorrelationKey(symbol1: string, symbol2: string): string {
    // Ensure consistent key ordering
    return symbol1 < symbol2 ? `${symbol1}:${symbol2}` : `${symbol2}:${symbol1}`;
  }

  private isCacheValid(data: CorrelationData): boolean {
    const maxAge = this.config.updateFrequency * 2; // Cache valid for 2x update frequency
    const age = Date.now() - data.lastUpdated.getTime();
    return age < maxAge && data.confidence >= 0.5;
  }
}