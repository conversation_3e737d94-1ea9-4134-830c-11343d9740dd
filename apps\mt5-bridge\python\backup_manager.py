"""
Automated TimescaleDB Backup Manager
Comprehensive backup strategy with automated scheduling, compression, and cloud storage
"""

import asyncio
import os
import time
import subprocess
import gzip
import shutil
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import json
import hashlib
from loguru import logger
import asyncpg
from croniter import croniter

try:
    import boto3
    from botocore.exceptions import ClientError
    AWS_S3_AVAILABLE = True
except ImportError:
    AWS_S3_AVAILABLE = False
    logger.warning("AWS S3 not available, using local storage only")

try:
    from azure.storage.blob import BlobServiceClient
    AZURE_AVAILABLE = True
except ImportError:
    AZURE_AVAILABLE = False
    logger.warning("Azure Blob Storage not available")

try:
    from google.cloud import storage as gcp_storage
    GCP_AVAILABLE = True
except ImportError:
    GCP_AVAILABLE = False
    logger.warning("Google Cloud Storage not available")

@dataclass
class BackupConfig:
    """Backup configuration"""
    # Database connection
    database_url: str
    database_name: str
    
    # Local backup settings
    backup_directory: str = "/backups"
    compress_backups: bool = True
    
    # Retention settings
    local_retention_days: int = 7
    remote_retention_days: int = 30
    
    # Scheduling
    full_backup_schedule: str = "0 2 * * *"      # Daily at 2 AM
    incremental_schedule: str = "0 */6 * * *"    # Every 6 hours
    
    # Cloud storage settings
    enable_s3: bool = False
    s3_bucket: Optional[str] = None
    s3_region: Optional[str] = None
    s3_access_key: Optional[str] = None
    s3_secret_key: Optional[str] = None
    
    enable_azure: bool = False
    azure_account_name: Optional[str] = None
    azure_account_key: Optional[str] = None
    azure_container: Optional[str] = None
    
    enable_gcp: bool = False
    gcp_project: Optional[str] = None
    gcp_bucket: Optional[str] = None
    gcp_credentials_path: Optional[str] = None
    
    # Verification settings
    verify_backups: bool = True
    test_restore: bool = False  # Test restore on separate database

@dataclass
class BackupMetadata:
    """Backup metadata information"""
    backup_id: str
    backup_type: str  # "full" or "incremental"
    timestamp: datetime
    database_name: str
    file_path: str
    file_size: int
    compressed: bool
    compression_ratio: Optional[float]
    checksum: str
    duration_seconds: float
    status: str  # "completed", "failed", "in_progress"
    error_message: Optional[str] = None
    
    # For incremental backups
    base_backup_id: Optional[str] = None
    lsn_start: Optional[str] = None
    lsn_end: Optional[str] = None

@dataclass
class RestorePoint:
    """Database restore point"""
    timestamp: datetime
    backup_id: str
    backup_type: str
    file_path: str
    database_size: int
    description: str

class S3Storage:
    """AWS S3 storage backend"""
    
    def __init__(self, bucket: str, region: str, access_key: str, secret_key: str):
        if not AWS_S3_AVAILABLE:
            raise ImportError("AWS S3 dependencies not available")
        
        self.bucket = bucket
        self.region = region
        self.client = boto3.client(
            's3',
            region_name=region,
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key
        )
    
    async def upload_backup(self, local_path: str, remote_key: str) -> bool:
        """Upload backup to S3"""
        try:
            self.client.upload_file(local_path, self.bucket, remote_key)
            logger.info(f"Backup uploaded to S3: s3://{self.bucket}/{remote_key}")
            return True
        except ClientError as e:
            logger.error(f"Failed to upload to S3: {e}")
            return False
    
    async def download_backup(self, remote_key: str, local_path: str) -> bool:
        """Download backup from S3"""
        try:
            self.client.download_file(self.bucket, remote_key, local_path)
            logger.info(f"Backup downloaded from S3: {remote_key}")
            return True
        except ClientError as e:
            logger.error(f"Failed to download from S3: {e}")
            return False
    
    async def list_backups(self, prefix: str = "") -> List[str]:
        """List backups in S3"""
        try:
            response = self.client.list_objects_v2(Bucket=self.bucket, Prefix=prefix)
            return [obj['Key'] for obj in response.get('Contents', [])]
        except ClientError as e:
            logger.error(f"Failed to list S3 objects: {e}")
            return []
    
    async def delete_backup(self, remote_key: str) -> bool:
        """Delete backup from S3"""
        try:
            self.client.delete_object(Bucket=self.bucket, Key=remote_key)
            logger.info(f"Backup deleted from S3: {remote_key}")
            return True
        except ClientError as e:
            logger.error(f"Failed to delete from S3: {e}")
            return False

class BackupManager:
    """Automated TimescaleDB backup management"""
    
    def __init__(self, config: BackupConfig):
        self.config = config
        
        # Backup state
        self.backup_history: List[BackupMetadata] = []
        self.is_running = False
        self.current_backup: Optional[BackupMetadata] = None
        
        # Storage backends
        self.s3_storage = None
        if config.enable_s3 and config.s3_bucket:
            try:
                self.s3_storage = S3Storage(
                    config.s3_bucket, 
                    config.s3_region,
                    config.s3_access_key,
                    config.s3_secret_key
                )
            except Exception as e:
                logger.error(f"Failed to initialize S3 storage: {e}")
        
        # Ensure backup directory exists
        os.makedirs(config.backup_directory, exist_ok=True)
        
        # Load backup history
        self._load_backup_history()
        
        # Scheduler
        self.scheduler_task: Optional[asyncio.Task] = None
    
    def _load_backup_history(self):
        """Load backup history from metadata file"""
        history_file = os.path.join(self.config.backup_directory, "backup_history.json")
        
        if os.path.exists(history_file):
            try:
                with open(history_file, 'r') as f:
                    history_data = json.load(f)
                
                for item in history_data:
                    # Convert timestamp back to datetime
                    item['timestamp'] = datetime.fromisoformat(item['timestamp'])
                    self.backup_history.append(BackupMetadata(**item))
                
                logger.info(f"Loaded {len(self.backup_history)} backup records")
            except Exception as e:
                logger.error(f"Failed to load backup history: {e}")
    
    def _save_backup_history(self):
        """Save backup history to metadata file"""
        history_file = os.path.join(self.config.backup_directory, "backup_history.json")
        
        try:
            # Convert to JSON-serializable format
            history_data = []
            for backup in self.backup_history:
                data = asdict(backup)
                data['timestamp'] = backup.timestamp.isoformat()
                history_data.append(data)
            
            with open(history_file, 'w') as f:
                json.dump(history_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to save backup history: {e}")
    
    async def start_scheduler(self):
        """Start backup scheduler"""
        if self.scheduler_task:
            logger.warning("Backup scheduler already running")
            return
        
        logger.info("🔄 Starting backup scheduler...")
        self.scheduler_task = asyncio.create_task(self._scheduler_loop())
        logger.info("✅ Backup scheduler started")
    
    async def stop_scheduler(self):
        """Stop backup scheduler"""
        if not self.scheduler_task:
            return
        
        logger.info("🔌 Stopping backup scheduler...")
        self.scheduler_task.cancel()
        
        try:
            await self.scheduler_task
        except asyncio.CancelledError:
            pass
        
        self.scheduler_task = None
        logger.info("✅ Backup scheduler stopped")
    
    async def _scheduler_loop(self):
        """Main scheduler loop"""
        full_backup_cron = croniter(self.config.full_backup_schedule, datetime.now())
        incremental_cron = croniter(self.config.incremental_schedule, datetime.now())
        
        while True:
            try:
                now = datetime.now()
                
                # Check if it's time for a full backup
                next_full = full_backup_cron.get_next(datetime)
                if now >= next_full:
                    logger.info("⏰ Scheduled full backup triggered")
                    await self.create_full_backup(automated=True)
                    full_backup_cron = croniter(self.config.full_backup_schedule, datetime.now())
                
                # Check if it's time for an incremental backup
                next_incremental = incremental_cron.get_next(datetime)
                if now >= next_incremental:
                    logger.info("⏰ Scheduled incremental backup triggered")
                    await self.create_incremental_backup(automated=True)
                    incremental_cron = croniter(self.config.incremental_schedule, datetime.now())
                
                # Clean up old backups
                await self._cleanup_old_backups()
                
                # Sleep for 60 seconds before next check
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"Error in backup scheduler: {e}")
                await asyncio.sleep(300)  # Wait 5 minutes before retrying
    
    async def create_full_backup(self, automated: bool = False) -> BackupMetadata:
        """Create full database backup"""
        if self.is_running:
            raise RuntimeError("Backup already in progress")
        
        backup_id = f"full_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        timestamp = datetime.now()
        
        logger.info(f"🔄 Starting full backup: {backup_id}")
        self.is_running = True
        
        try:
            # Prepare backup metadata
            backup_metadata = BackupMetadata(
                backup_id=backup_id,
                backup_type="full",
                timestamp=timestamp,
                database_name=self.config.database_name,
                file_path="",  # Will be set after backup
                file_size=0,
                compressed=self.config.compress_backups,
                compression_ratio=None,
                checksum="",
                duration_seconds=0,
                status="in_progress"
            )
            
            self.current_backup = backup_metadata
            start_time = time.time()
            
            # Create backup file path
            backup_filename = f"{backup_id}.sql"
            if self.config.compress_backups:
                backup_filename += ".gz"
            
            backup_path = os.path.join(self.config.backup_directory, backup_filename)
            
            # Execute pg_dump
            dump_command = [
                "pg_dump",
                self.config.database_url,
                "--verbose",
                "--no-password",
                "--format=custom",
                "--no-privileges",
                "--no-owner"
            ]
            
            logger.info(f"Executing pg_dump for database: {self.config.database_name}")
            
            if self.config.compress_backups:
                # Pipe through gzip
                dump_process = subprocess.Popen(
                    dump_command,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                
                with gzip.open(backup_path, 'wb') as gz_file:
                    while True:
                        chunk = dump_process.stdout.read(8192)
                        if not chunk:
                            break
                        gz_file.write(chunk)
                
                dump_process.wait()
            else:
                # Direct output to file
                with open(backup_path, 'wb') as backup_file:
                    result = subprocess.run(
                        dump_command,
                        stdout=backup_file,
                        stderr=subprocess.PIPE,
                        check=True
                    )
            
            # Calculate backup statistics
            file_size = os.path.getsize(backup_path)
            duration = time.time() - start_time
            
            # Calculate checksum
            checksum = self._calculate_file_checksum(backup_path)
            
            # Calculate compression ratio if compressed
            compression_ratio = None
            if self.config.compress_backups:
                # Estimate original size (rough approximation)
                compression_ratio = 0.3  # Typical compression ratio for SQL dumps
            
            # Update metadata
            backup_metadata.file_path = backup_path
            backup_metadata.file_size = file_size
            backup_metadata.checksum = checksum
            backup_metadata.duration_seconds = duration
            backup_metadata.compression_ratio = compression_ratio
            backup_metadata.status = "completed"
            
            # Add to history
            self.backup_history.append(backup_metadata)
            self._save_backup_history()
            
            logger.info(f"✅ Full backup completed: {backup_id} ({file_size / 1024 / 1024:.1f} MB)")
            
            # Upload to cloud storage
            if self.s3_storage:
                await self._upload_to_cloud(backup_metadata)
            
            # Verify backup if configured
            if self.config.verify_backups:
                await self._verify_backup(backup_metadata)
            
            return backup_metadata
            
        except Exception as e:
            backup_metadata.status = "failed"
            backup_metadata.error_message = str(e)
            backup_metadata.duration_seconds = time.time() - start_time
            
            self.backup_history.append(backup_metadata)
            self._save_backup_history()
            
            logger.error(f"❌ Full backup failed: {backup_id} - {e}")
            raise
            
        finally:
            self.is_running = False
            self.current_backup = None
    
    async def create_incremental_backup(self, automated: bool = False) -> Optional[BackupMetadata]:
        """Create incremental backup using WAL archiving"""
        # Find the last full backup
        last_full = None
        for backup in reversed(self.backup_history):
            if backup.backup_type == "full" and backup.status == "completed":
                last_full = backup
                break
        
        if not last_full:
            logger.warning("No full backup found for incremental backup, creating full backup instead")
            return await self.create_full_backup(automated=automated)
        
        backup_id = f"incr_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        timestamp = datetime.now()
        
        logger.info(f"🔄 Starting incremental backup: {backup_id}")
        self.is_running = True
        
        try:
            # For now, we'll create a full logical backup
            # In production, you'd implement WAL-based incremental backups
            logger.info("Creating logical backup (incremental WAL backup not implemented)")
            return await self.create_full_backup(automated=automated)
            
        finally:
            self.is_running = False
    
    def _calculate_file_checksum(self, file_path: str) -> str:
        """Calculate SHA256 checksum of file"""
        sha256_hash = hashlib.sha256()
        
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        
        return sha256_hash.hexdigest()
    
    async def _upload_to_cloud(self, backup_metadata: BackupMetadata):
        """Upload backup to configured cloud storage"""
        if not self.s3_storage:
            return
        
        remote_key = f"backups/{backup_metadata.database_name}/{backup_metadata.backup_id}/{os.path.basename(backup_metadata.file_path)}"
        
        success = await self.s3_storage.upload_backup(backup_metadata.file_path, remote_key)
        if success:
            logger.info(f"✅ Backup uploaded to cloud: {remote_key}")
        else:
            logger.error(f"❌ Failed to upload backup to cloud: {backup_metadata.backup_id}")
    
    async def _verify_backup(self, backup_metadata: BackupMetadata):
        """Verify backup integrity"""
        logger.info(f"🔍 Verifying backup: {backup_metadata.backup_id}")
        
        # Check file exists and size matches
        if not os.path.exists(backup_metadata.file_path):
            logger.error(f"Backup file not found: {backup_metadata.file_path}")
            return False
        
        current_size = os.path.getsize(backup_metadata.file_path)
        if current_size != backup_metadata.file_size:
            logger.error(f"Backup file size mismatch: expected {backup_metadata.file_size}, got {current_size}")
            return False
        
        # Verify checksum
        current_checksum = self._calculate_file_checksum(backup_metadata.file_path)
        if current_checksum != backup_metadata.checksum:
            logger.error(f"Backup checksum mismatch: expected {backup_metadata.checksum}, got {current_checksum}")
            return False
        
        logger.info(f"✅ Backup verification successful: {backup_metadata.backup_id}")
        return True
    
    async def _cleanup_old_backups(self):
        """Clean up old backups based on retention policy"""
        cutoff_date = datetime.now() - timedelta(days=self.config.local_retention_days)
        
        backups_to_remove = []
        for backup in self.backup_history:
            if backup.timestamp < cutoff_date:
                backups_to_remove.append(backup)
        
        for backup in backups_to_remove:
            try:
                # Remove local file
                if os.path.exists(backup.file_path):
                    os.remove(backup.file_path)
                    logger.info(f"🗑️ Removed old backup file: {backup.file_path}")
                
                # Remove from history
                self.backup_history.remove(backup)
                
            except Exception as e:
                logger.error(f"Failed to remove old backup {backup.backup_id}: {e}")
        
        if backups_to_remove:
            self._save_backup_history()
            logger.info(f"Cleaned up {len(backups_to_remove)} old backups")
    
    async def restore_database(self, backup_id: str, target_database: str) -> bool:
        """Restore database from backup"""
        # Find backup
        backup = None
        for b in self.backup_history:
            if b.backup_id == backup_id:
                backup = b
                break
        
        if not backup:
            logger.error(f"Backup not found: {backup_id}")
            return False
        
        if backup.status != "completed":
            logger.error(f"Cannot restore from incomplete backup: {backup_id}")
            return False
        
        logger.info(f"🔄 Starting database restore: {backup_id} -> {target_database}")
        
        try:
            # Download from cloud if needed
            if not os.path.exists(backup.file_path) and self.s3_storage:
                remote_key = f"backups/{backup.database_name}/{backup.backup_id}/{os.path.basename(backup.file_path)}"
                success = await self.s3_storage.download_backup(remote_key, backup.file_path)
                if not success:
                    logger.error(f"Failed to download backup from cloud: {backup_id}")
                    return False
            
            # Verify backup before restore
            if not await self._verify_backup(backup):
                logger.error(f"Backup verification failed before restore: {backup_id}")
                return False
            
            # Execute pg_restore
            restore_command = [
                "pg_restore",
                "--verbose",
                "--clean",
                "--no-owner",
                "--no-privileges",
                f"--dbname={target_database}",
                backup.file_path
            ]
            
            if backup.compressed:
                # Handle compressed backup
                with gzip.open(backup.file_path, 'rb') as gz_file:
                    restore_process = subprocess.Popen(
                        restore_command[:-1],  # Remove file path
                        stdin=subprocess.PIPE,
                        stderr=subprocess.PIPE
                    )
                    
                    stdout, stderr = restore_process.communicate(gz_file.read())
            else:
                result = subprocess.run(
                    restore_command,
                    capture_output=True,
                    text=True
                )
                stderr = result.stderr
            
            if stderr and "error" in stderr.lower():
                logger.error(f"Restore completed with errors: {stderr}")
                return False
            
            logger.info(f"✅ Database restore completed: {backup_id} -> {target_database}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Database restore failed: {backup_id} - {e}")
            return False
    
    def get_backup_status(self) -> Dict[str, Any]:
        """Get backup system status"""
        recent_backups = sorted(self.backup_history, key=lambda b: b.timestamp, reverse=True)[:10]
        
        # Calculate backup statistics
        total_size = sum(b.file_size for b in self.backup_history if b.status == "completed")
        completed_backups = len([b for b in self.backup_history if b.status == "completed"])
        failed_backups = len([b for b in self.backup_history if b.status == "failed"])
        
        return {
            "is_running": self.is_running,
            "current_backup": asdict(self.current_backup) if self.current_backup else None,
            "total_backups": len(self.backup_history),
            "completed_backups": completed_backups,
            "failed_backups": failed_backups,
            "total_backup_size_mb": round(total_size / 1024 / 1024, 2),
            "recent_backups": [asdict(b) for b in recent_backups],
            "cloud_storage_enabled": self.s3_storage is not None,
            "retention_days": self.config.local_retention_days
        }
    
    def get_restore_points(self) -> List[RestorePoint]:
        """Get available restore points"""
        restore_points = []
        
        for backup in self.backup_history:
            if backup.status == "completed":
                restore_point = RestorePoint(
                    timestamp=backup.timestamp,
                    backup_id=backup.backup_id,
                    backup_type=backup.backup_type,
                    file_path=backup.file_path,
                    database_size=backup.file_size,
                    description=f"{backup.backup_type.title()} backup from {backup.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"
                )
                restore_points.append(restore_point)
        
        return sorted(restore_points, key=lambda rp: rp.timestamp, reverse=True)

# Global backup manager instance
_backup_manager: Optional[BackupManager] = None

def get_backup_manager(config: BackupConfig) -> BackupManager:
    """Get global backup manager instance"""
    global _backup_manager
    if _backup_manager is None:
        _backup_manager = BackupManager(config)
    return _backup_manager