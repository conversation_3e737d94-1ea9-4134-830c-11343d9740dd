/**
 * Position Manager with Automatic Updates and Synchronization
 * 
 * Handles position synchronization across broker connections, real-time P&L calculation,
 * position reconciliation for failover scenarios, and risk monitoring.
 * 
 * Implements Task 3 from Story 4.4: Trade Execution and Monitoring
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import Decimal from 'decimal.js';
import type {
  Position,
  PositionRiskMetrics,
  SynchronizationStatus,
  TradeExecutionEvent,
  PositionUpdateData
} from '@golddaddy/types';
import { PositionStatus } from '@golddaddy/types';
import { BrokerFailoverEngine } from './BrokerFailoverEngine.js';
import { TradeStatusTracker } from './TradeStatusTracker.js';

// Position Synchronization Types
export interface PositionSyncRecord {
  positionId: string;
  brokerId: string;
  brokerPositionId: string;
  lastSyncTime: Date;
  syncStatus: 'synchronized' | 'pending' | 'failed' | 'conflict';
  conflictReason?: string;
  retryCount: number;
  maxRetries: number;
}

export interface PositionReconciliation {
  positionId: string;
  discrepancies: PositionDiscrepancy[];
  resolutionStrategy: 'use_broker_data' | 'use_local_data' | 'manual_review';
  resolvedAt?: Date;
  resolvedBy?: string;
}

export interface PositionDiscrepancy {
  field: string;
  localValue: string;
  brokerValue: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  impact: string;
}

// Broker Position Data Interface
export interface BrokerPositionData {
  brokerId: string;
  brokerPositionId: string;
  instrument: string;
  side: 'LONG' | 'SHORT';
  size: Decimal.Instance;
  averagePrice: Decimal.Instance;
  currentPrice: Decimal.Instance;
  unrealizedPnl: Decimal.Instance;
  realizedPnl: Decimal.Instance;
  margin: Decimal.Instance;
  timestamp: Date;
}

// Position Update Event
export interface PositionUpdateEvent {
  type: 'position_opened' | 'position_updated' | 'position_closed' | 'position_sync_failed';
  position: Position;
  previousState?: Partial<Position>;
  syncStatus: SynchronizationStatus;
  timestamp: Date;
}

export interface PositionManagerConfig {
  syncIntervalMs: number;
  reconciliationIntervalMs: number;
  maxSyncRetries: number;
  positionUpdateBatchSize: number;
  realTimePnlEnabled: boolean;
  riskMonitoringEnabled: boolean;
  autoReconciliationEnabled: boolean;
  syncTimeoutMs: number;
}

/**
 * Position Manager for automated position tracking and synchronization
 */
export class PositionManager extends EventEmitter {
  private positions: Map<string, Position> = new Map();
  private syncRecords: Map<string, PositionSyncRecord> = new Map();
  private reconciliations: Map<string, PositionReconciliation> = new Map();
  private config: PositionManagerConfig;
  private syncInterval: NodeJS.Timeout | null = null;
  private reconciliationInterval: NodeJS.Timeout | null = null;
  private brokerFailover: BrokerFailoverEngine;
  private statusTracker: TradeStatusTracker;
  private isInitialized = false;

  constructor(
    private prisma: PrismaClient,
    brokerFailover: BrokerFailoverEngine,
    statusTracker: TradeStatusTracker,
    config: Partial<PositionManagerConfig> = {}
  ) {
    super();
    
    this.brokerFailover = brokerFailover;
    this.statusTracker = statusTracker;
    
    this.config = {
      syncIntervalMs: 5000, // 5 seconds
      reconciliationIntervalMs: 60000, // 1 minute
      maxSyncRetries: 3,
      positionUpdateBatchSize: 50,
      realTimePnlEnabled: true,
      riskMonitoringEnabled: true,
      autoReconciliationEnabled: true,
      syncTimeoutMs: 10000, // 10 seconds
      ...config
    };

    // Listen for broker failover events
    this.brokerFailover.on('failoverCompleted', this.handleBrokerFailover.bind(this));
    this.brokerFailover.on('failoverStarted', this.handleFailoverStarted.bind(this));
  }

  /**
   * Initialize position manager
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    console.log('🚀 Initializing Position Manager...');

    // Load existing positions from database
    await this.loadExistingPositions();

    // Start synchronization intervals
    this.startSynchronizationIntervals();

    // Initialize risk monitoring if enabled
    if (this.config.riskMonitoringEnabled) {
      await this.initializeRiskMonitoring();
    }

    this.isInitialized = true;
    console.log('✅ Position Manager initialized');
    this.emit('initialized');
  }

  /**
   * Create new position
   */
  async createPosition(positionData: Omit<Position, 'id' | 'createdAt' | 'updatedAt'>): Promise<Position> {
    try {
      const positionId = `pos_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const position: Position = {
        ...positionData,
        id: positionId,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Store in database
      await this.prisma.position.create({
        data: {
          id: position.id,
          userId: position.userId,
          accountId: position.accountId,
          strategyId: position.strategyId,
          goalId: position.goalId,
          instrument: position.instrument,
          side: position.side,
          size: position.size,
          averageEntryPrice: position.averageEntryPrice,
          currentPrice: position.currentPrice,
          unrealizedPnl: position.unrealizedPnl,
          realizedPnl: position.realizedPnl,
          totalPnl: position.totalPnl,
          pnlPercentage: position.pnlPercentage,
          stopLoss: position.stopLoss,
          takeProfit: position.takeProfit,
          status: position.status,
          riskMetrics: position.riskMetrics as any,
          synchronizationStatus: position.synchronizationStatus as any,
          createdAt: position.createdAt,
          updatedAt: position.updatedAt
        }
      });

      // Store in memory
      this.positions.set(positionId, position);

      // Create sync record
      await this.createSyncRecord(position);

      // Emit position creation event
      const event: PositionUpdateEvent = {
        type: 'position_opened',
        position,
        syncStatus: position.synchronizationStatus,
        timestamp: new Date()
      };

      this.emit('positionCreated', event);
      
      // Notify status tracker
      await this.notifyStatusTracker(position, 'position_opened');

      console.log(`📊 Position created: ${positionId} (${position.instrument})`);
      return position;

    } catch (error) {
      console.error('Failed to create position:', error);
      throw error;
    }
  }

  /**
   * Update position with broker data
   */
  async updatePosition(
    positionId: string,
    brokerData: Partial<BrokerPositionData>,
    forceUpdate = false
  ): Promise<Position | null> {
    try {
      const existingPosition = this.positions.get(positionId);
      if (!existingPosition) {
        console.error(`Position not found: ${positionId}`);
        return null;
      }

      const previousState = { ...existingPosition };

      // Calculate updated position data
      const updatedPosition = await this.calculatePositionUpdate(existingPosition, brokerData);

      // Update in database
      await this.prisma.position.update({
        where: { id: positionId },
        data: {
          currentPrice: updatedPosition.currentPrice,
          unrealizedPnl: updatedPosition.unrealizedPnl,
          totalPnl: updatedPosition.totalPnl,
          pnlPercentage: updatedPosition.pnlPercentage,
          riskMetrics: updatedPosition.riskMetrics as any,
          synchronizationStatus: updatedPosition.synchronizationStatus as any,
          updatedAt: new Date()
        }
      });

      // Update in memory
      this.positions.set(positionId, updatedPosition);

      // Update sync record
      await this.updateSyncRecord(positionId, 'synchronized');

      // Emit position update event
      const event: PositionUpdateEvent = {
        type: 'position_updated',
        position: updatedPosition,
        previousState,
        syncStatus: updatedPosition.synchronizationStatus,
        timestamp: new Date()
      };

      this.emit('positionUpdated', event);

      // Notify status tracker
      await this.notifyStatusTracker(updatedPosition, 'position_updated');

      // Check for risk alerts
      if (this.config.riskMonitoringEnabled) {
        await this.checkRiskAlerts(updatedPosition, previousState);
      }

      return updatedPosition;

    } catch (error) {
      console.error(`Failed to update position ${positionId}:`, error);
      await this.updateSyncRecord(positionId, 'failed', error.message);
      return null;
    }
  }

  /**
   * Close position
   */
  async closePosition(positionId: string, closePrice: Decimal, closeReason: string): Promise<Position | null> {
    try {
      const position = this.positions.get(positionId);
      if (!position) {
        console.error(`Position not found for closing: ${positionId}`);
        return null;
      }

      // Calculate final P&L
      const pnlDiff = position.side === 'LONG' 
        ? closePrice.minus(position.averageEntryPrice)
        : position.averageEntryPrice.minus(closePrice);
      
      const finalRealizedPnl = pnlDiff.mul(position.size);
      const finalTotalPnl = position.realizedPnl.add(finalRealizedPnl);

      const closedPosition: Position = {
        ...position,
        currentPrice: closePrice,
        unrealizedPnl: new Decimal(0),
        realizedPnl: finalRealizedPnl,
        totalPnl: finalTotalPnl,
        pnlPercentage: finalTotalPnl.div(position.averageEntryPrice.mul(position.size)).mul(100),
        status: PositionStatus.CLOSED,
        synchronizationStatus: {
          ...position.synchronizationStatus,
          lastSyncTime: new Date(),
          isSynchronized: true
        },
        updatedAt: new Date()
      };

      // Update in database
      await this.prisma.position.update({
        where: { id: positionId },
        data: {
          currentPrice: closedPosition.currentPrice,
          unrealizedPnl: closedPosition.unrealizedPnl,
          realizedPnl: closedPosition.realizedPnl,
          totalPnl: closedPosition.totalPnl,
          pnlPercentage: closedPosition.pnlPercentage,
          status: closedPosition.status,
          synchronizationStatus: closedPosition.synchronizationStatus as any,
          updatedAt: closedPosition.updatedAt
        }
      });

      // Update in memory
      this.positions.set(positionId, closedPosition);

      // Remove from sync records (position is closed)
      this.syncRecords.delete(positionId);

      // Emit position closed event
      const event: PositionUpdateEvent = {
        type: 'position_closed',
        position: closedPosition,
        previousState: position,
        syncStatus: closedPosition.synchronizationStatus,
        timestamp: new Date()
      };

      this.emit('positionClosed', event);

      // Notify status tracker
      await this.notifyStatusTracker(closedPosition, 'position_closed');

      console.log(`✅ Position closed: ${positionId} - P&L: ${finalTotalPnl.toFixed(2)} (${closeReason})`);
      return closedPosition;

    } catch (error) {
      console.error(`Failed to close position ${positionId}:`, error);
      throw error;
    }
  }

  /**
   * Synchronize positions across all brokers
   */
  async synchronizeAllPositions(): Promise<{
    synchronized: number;
    failed: number;
    conflicts: number;
  }> {
    try {
      console.log('🔄 Starting position synchronization across all brokers...');

      const results = {
        synchronized: 0,
        failed: 0,
        conflicts: 0
      };

      const activePositions = Array.from(this.positions.values())
        .filter(pos => pos.status === PositionStatus.OPEN);

      // Process positions in batches
      const batches = this.createBatches(activePositions, this.config.positionUpdateBatchSize);

      for (const batch of batches) {
        const batchPromises = batch.map(async (position) => {
          try {
            const brokerData = await this.fetchBrokerPositionData(position);
            if (brokerData) {
              const discrepancies = await this.detectDiscrepancies(position, brokerData);
              
              if (discrepancies.length > 0) {
                await this.handlePositionConflict(position.id, discrepancies);
                results.conflicts++;
              } else {
                await this.updatePosition(position.id, brokerData);
                results.synchronized++;
              }
            }
          } catch (error) {
            console.error(`Failed to sync position ${position.id}:`, error);
            await this.updateSyncRecord(position.id, 'failed', error.message);
            results.failed++;
          }
        });

        await Promise.all(batchPromises);
      }

      console.log(`✅ Position synchronization completed: ${results.synchronized} synced, ${results.failed} failed, ${results.conflicts} conflicts`);
      
      this.emit('synchronizationCompleted', results);
      return results;

    } catch (error) {
      console.error('Position synchronization failed:', error);
      throw error;
    }
  }

  /**
   * Reconcile position conflicts
   */
  async reconcilePositionConflicts(positionId: string, strategy: PositionReconciliation['resolutionStrategy']): Promise<boolean> {
    try {
      const reconciliation = this.reconciliations.get(positionId);
      if (!reconciliation) {
        console.error(`No reconciliation record found for position: ${positionId}`);
        return false;
      }

      const position = this.positions.get(positionId);
      if (!position) {
        console.error(`Position not found for reconciliation: ${positionId}`);
        return false;
      }

      let resolved = false;

      switch (strategy) {
        case 'use_broker_data':
          resolved = await this.resolveToBrokerData(positionId);
          break;
        case 'use_local_data':
          resolved = await this.resolveToLocalData(positionId);
          break;
        case 'manual_review':
          // Manual review required - just update the strategy
          reconciliation.resolutionStrategy = strategy;
          resolved = false;
          break;
        default:
          console.error(`Unknown reconciliation strategy: ${strategy}`);
          return false;
      }

      // Update reconciliation record
      reconciliation.resolvedAt = new Date();
      reconciliation.resolutionStrategy = strategy;
      
      if (resolved) {
        this.reconciliations.delete(positionId);
        console.log(`✅ Position reconciliation completed: ${positionId} using ${strategy}`);
      }

      return resolved;

    } catch (error) {
      console.error(`Failed to reconcile position ${positionId}:`, error);
      return false;
    }
  }

  /**
   * Get position by ID
   */
  getPosition(positionId: string): Position | null {
    return this.positions.get(positionId) || null;
  }

  /**
   * Get all positions for a user
   */
  getUserPositions(userId: string, includeClosedPositions = false): Position[] {
    return Array.from(this.positions.values())
      .filter(pos => 
        pos.userId === userId && 
        (includeClosedPositions || pos.status !== PositionStatus.CLOSED)
      );
  }

  /**
   * Get synchronization status for a position
   */
  getSynchronizationStatus(positionId: string): SynchronizationStatus | null {
    const position = this.positions.get(positionId);
    return position?.synchronizationStatus || null;
  }

  /**
   * Shutdown position manager
   */
  async shutdown(): Promise<void> {
    console.log('⏹️ Shutting down Position Manager...');

    // Stop intervals
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }

    if (this.reconciliationInterval) {
      clearInterval(this.reconciliationInterval);
      this.reconciliationInterval = null;
    }

    // Clear memory
    this.positions.clear();
    this.syncRecords.clear();
    this.reconciliations.clear();

    console.log('✅ Position Manager shutdown complete');
    this.emit('shutdown');
  }

  // === Private Helper Methods ===

  private async loadExistingPositions(): Promise<void> {
    try {
      const positions = await this.prisma.position.findMany({
        where: {
          status: {
            in: ['OPEN', 'CLOSING']
          }
        }
      });

      positions.forEach(pos => {
        const position: Position = {
          id: pos.id,
          userId: pos.userId,
          accountId: pos.accountId,
          strategyId: pos.strategyId,
          goalId: pos.goalId,
          instrument: pos.instrument,
          side: pos.side as 'LONG' | 'SHORT',
          size: pos.size,
          averageEntryPrice: pos.averageEntryPrice,
          currentPrice: pos.currentPrice,
          unrealizedPnl: pos.unrealizedPnl,
          realizedPnl: pos.realizedPnl,
          totalPnl: pos.totalPnl,
          pnlPercentage: pos.pnlPercentage,
          stopLoss: pos.stopLoss,
          takeProfit: pos.takeProfit,
          status: pos.status as PositionStatus,
          riskMetrics: pos.riskMetrics as PositionRiskMetrics,
          synchronizationStatus: pos.synchronizationStatus as SynchronizationStatus,
          createdAt: pos.createdAt,
          updatedAt: pos.updatedAt
        };

        this.positions.set(pos.id, position);
      });

      console.log(`📊 Loaded ${positions.length} existing positions`);

    } catch (error) {
      console.error('Failed to load existing positions:', error);
    }
  }

  private startSynchronizationIntervals(): void {
    // Position synchronization interval
    this.syncInterval = setInterval(async () => {
      try {
        await this.synchronizeAllPositions();
      } catch (error) {
        console.error('Scheduled position synchronization failed:', error);
      }
    }, this.config.syncIntervalMs);

    // Reconciliation interval
    if (this.config.autoReconciliationEnabled) {
      this.reconciliationInterval = setInterval(async () => {
        try {
          await this.processAutoReconciliation();
        } catch (error) {
          console.error('Automated reconciliation failed:', error);
        }
      }, this.config.reconciliationIntervalMs);
    }
  }

  private async initializeRiskMonitoring(): Promise<void> {
    // Initialize risk monitoring for all positions
    console.log('🛡️ Initializing position risk monitoring...');
    
    for (const position of this.positions.values()) {
      await this.updatePositionRiskMetrics(position);
    }
  }

  private async createSyncRecord(position: Position): Promise<void> {
    const syncRecord: PositionSyncRecord = {
      positionId: position.id,
      brokerId: position.accountId, // Assuming accountId maps to brokerId
      brokerPositionId: `broker_${position.id}`,
      lastSyncTime: new Date(),
      syncStatus: 'synchronized',
      retryCount: 0,
      maxRetries: this.config.maxSyncRetries
    };

    this.syncRecords.set(position.id, syncRecord);
  }

  private async updateSyncRecord(
    positionId: string,
    status: PositionSyncRecord['syncStatus'],
    conflictReason?: string
  ): Promise<void> {
    const record = this.syncRecords.get(positionId);
    if (record) {
      record.syncStatus = status;
      record.lastSyncTime = new Date();
      record.conflictReason = conflictReason;
      
      if (status === 'failed') {
        record.retryCount++;
      } else if (status === 'synchronized') {
        record.retryCount = 0;
      }
    }
  }

  private async calculatePositionUpdate(
    position: Position,
    brokerData: Partial<BrokerPositionData>
  ): Promise<Position> {
    const currentPrice = brokerData.currentPrice || position.currentPrice;
    const size = brokerData.size || position.size;
    
    // Calculate unrealized P&L
    const priceDiff = position.side === 'LONG' 
      ? currentPrice.minus(position.averageEntryPrice)
      : position.averageEntryPrice.minus(currentPrice);
    
    const unrealizedPnl = priceDiff.mul(size);
    const totalPnl = position.realizedPnl.add(unrealizedPnl);
    const pnlPercentage = totalPnl.div(position.averageEntryPrice.mul(size)).mul(100);

    // Update risk metrics
    const riskMetrics = await this.calculateRiskMetrics(position, currentPrice, unrealizedPnl);

    // Update synchronization status
    const synchronizationStatus: SynchronizationStatus = {
      ...position.synchronizationStatus,
      isSynchronized: true,
      lastSyncTime: new Date(),
      pendingSyncOperations: 0,
      syncErrors: []
    };

    return {
      ...position,
      currentPrice,
      size,
      unrealizedPnl,
      totalPnl,
      pnlPercentage,
      riskMetrics,
      synchronizationStatus,
      updatedAt: new Date()
    };
  }

  private async calculateRiskMetrics(
    position: Position,
    currentPrice: Decimal,
    unrealizedPnl: Decimal
  ): Promise<PositionRiskMetrics> {
    const exposure = position.averageEntryPrice.mul(position.size);
    const riskPercentage = exposure.div(100000).mul(100); // Assuming $100k account
    
    // Calculate drawdown
    const peakValue = position.totalPnl.greaterThan(0) ? position.totalPnl : new Decimal(0);
    const currentDrawdown = peakValue.greaterThan(0) 
      ? peakValue.minus(position.totalPnl).div(peakValue)
      : new Decimal(0);

    // Get historical max drawdown (simplified)
    const maxDrawdown = currentDrawdown.greaterThan(position.riskMetrics.maxDrawdown)
      ? currentDrawdown
      : position.riskMetrics.maxDrawdown;

    // Calculate volatility (simplified - would use historical price data)
    const priceChangePercent = currentPrice.minus(position.averageEntryPrice)
      .div(position.averageEntryPrice).abs().mul(100);
    const volatility = priceChangePercent; // Simplified volatility measure

    return {
      exposure,
      riskPercentage,
      maxDrawdown,
      currentDrawdown,
      volatility,
      beta: position.riskMetrics.beta // Keep existing beta if available
    };
  }

  private async fetchBrokerPositionData(position: Position): Promise<BrokerPositionData | null> {
    try {
      // Mock implementation - in production would call actual broker API
      await new Promise(resolve => setTimeout(resolve, 50)); // Simulate API call
      
      // Simulate 95% success rate
      if (Math.random() < 0.95) {
        const mockPrice = position.currentPrice.mul(new Decimal(0.99 + Math.random() * 0.02)); // +/-1% price movement
        const priceDiff = position.side === 'LONG'
          ? mockPrice.minus(position.averageEntryPrice)
          : position.averageEntryPrice.minus(mockPrice);
        const unrealizedPnl = priceDiff.mul(position.size);

        return {
          brokerId: position.accountId,
          brokerPositionId: `broker_${position.id}`,
          instrument: position.instrument,
          side: position.side,
          size: position.size,
          averagePrice: position.averageEntryPrice,
          currentPrice: mockPrice,
          unrealizedPnl,
          realizedPnl: position.realizedPnl,
          margin: position.averageEntryPrice.mul(position.size).div(100), // 1% margin
          timestamp: new Date()
        };
      } else {
        throw new Error('Broker API timeout');
      }

    } catch (error) {
      console.error(`Failed to fetch broker data for position ${position.id}:`, error);
      return null;
    }
  }

  private async detectDiscrepancies(
    position: Position,
    brokerData: BrokerPositionData
  ): Promise<PositionDiscrepancy[]> {
    const discrepancies: PositionDiscrepancy[] = [];

    // Check size discrepancy
    if (!position.size.equals(brokerData.size)) {
      discrepancies.push({
        field: 'size',
        localValue: position.size.toString(),
        brokerValue: brokerData.size.toString(),
        severity: 'high',
        impact: 'Position size mismatch affects P&L calculations'
      });
    }

    // Check price discrepancy (allow small tolerance)
    const priceTolerance = new Decimal('0.0001'); // 1 pip tolerance
    const priceDiff = position.currentPrice.minus(brokerData.currentPrice).abs();
    if (priceDiff.greaterThan(priceTolerance)) {
      discrepancies.push({
        field: 'currentPrice',
        localValue: position.currentPrice.toString(),
        brokerValue: brokerData.currentPrice.toString(),
        severity: 'medium',
        impact: 'Price discrepancy affects unrealized P&L'
      });
    }

    // Check P&L discrepancy (allow small tolerance)
    const pnlTolerance = new Decimal('1.00'); // $1 tolerance
    const pnlDiff = position.unrealizedPnl.minus(brokerData.unrealizedPnl).abs();
    if (pnlDiff.greaterThan(pnlTolerance)) {
      discrepancies.push({
        field: 'unrealizedPnl',
        localValue: position.unrealizedPnl.toString(),
        brokerValue: brokerData.unrealizedPnl.toString(),
        severity: 'high',
        impact: 'P&L mismatch affects risk calculations'
      });
    }

    return discrepancies;
  }

  private async handlePositionConflict(
    positionId: string,
    discrepancies: PositionDiscrepancy[]
  ): Promise<void> {
    const reconciliation: PositionReconciliation = {
      positionId,
      discrepancies,
      resolutionStrategy: 'use_broker_data' // Default strategy
    };

    this.reconciliations.set(positionId, reconciliation);

    this.emit('positionConflict', {
      positionId,
      discrepancies,
      timestamp: new Date()
    });

    console.warn(`⚠️ Position conflict detected: ${positionId} - ${discrepancies.length} discrepancies`);
  }

  private async resolveToBrokerData(positionId: string): Promise<boolean> {
    try {
      const position = this.positions.get(positionId);
      if (!position) return false;

      const brokerData = await this.fetchBrokerPositionData(position);
      if (!brokerData) return false;

      await this.updatePosition(positionId, brokerData, true);
      return true;

    } catch (error) {
      console.error(`Failed to resolve to broker data for position ${positionId}:`, error);
      return false;
    }
  }

  private async resolveToLocalData(positionId: string): Promise<boolean> {
    try {
      // Update sync record to indicate local data is authoritative
      await this.updateSyncRecord(positionId, 'synchronized');
      return true;

    } catch (error) {
      console.error(`Failed to resolve to local data for position ${positionId}:`, error);
      return false;
    }
  }

  private async processAutoReconciliation(): Promise<void> {
    for (const [positionId, reconciliation] of this.reconciliations.entries()) {
      // Only auto-reconcile low severity discrepancies
      const hasHighSeverityDiscrepancies = reconciliation.discrepancies.some(d => 
        d.severity === 'high' || d.severity === 'critical'
      );

      if (!hasHighSeverityDiscrepancies) {
        await this.reconcilePositionConflicts(positionId, 'use_broker_data');
      }
    }
  }

  private async updatePositionRiskMetrics(position: Position): Promise<void> {
    const updatedRiskMetrics = await this.calculateRiskMetrics(
      position,
      position.currentPrice,
      position.unrealizedPnl
    );

    position.riskMetrics = updatedRiskMetrics;
  }

  private async checkRiskAlerts(position: Position, previousState: Partial<Position>): Promise<void> {
    const riskThresholds = {
      maxDrawdown: new Decimal('0.1'), // 10%
      exposureLimit: new Decimal('50000'), // $50k
      pnlAlert: new Decimal('-5000') // -$5k
    };

    // Check for drawdown alert
    if (position.riskMetrics.currentDrawdown.greaterThan(riskThresholds.maxDrawdown)) {
      this.emit('riskAlert', {
        type: 'drawdown_exceeded',
        positionId: position.id,
        currentValue: position.riskMetrics.currentDrawdown,
        threshold: riskThresholds.maxDrawdown,
        position,
        timestamp: new Date()
      });
    }

    // Check for P&L alert
    if (position.totalPnl.lessThan(riskThresholds.pnlAlert)) {
      this.emit('riskAlert', {
        type: 'loss_limit_approaching',
        positionId: position.id,
        currentValue: position.totalPnl,
        threshold: riskThresholds.pnlAlert,
        position,
        timestamp: new Date()
      });
    }
  }

  private async notifyStatusTracker(position: Position, eventType: string): Promise<void> {
    try {
      const positionUpdateData: PositionUpdateData = {
        positionId: position.id,
        instrument: position.instrument,
        size: position.size,
        averageEntryPrice: position.averageEntryPrice,
        currentPrice: position.currentPrice,
        unrealizedPnl: position.unrealizedPnl,
        pnlPercentage: position.pnlPercentage,
        riskMetrics: {
          exposure: position.riskMetrics.exposure,
          riskPercentage: position.riskMetrics.riskPercentage,
          currentDrawdown: position.riskMetrics.currentDrawdown
        }
      };

      await this.statusTracker.trackPositionUpdate(positionUpdateData, position.userId);

    } catch (error) {
      console.error('Failed to notify status tracker:', error);
    }
  }

  private handleBrokerFailover(event: any): void {
    console.log(`🔄 Handling broker failover for position synchronization: ${event.fromBroker} → ${event.toBroker}`);
    
    // Re-sync all positions affected by the failover
    const affectedPositions = Array.from(this.positions.values())
      .filter(pos => pos.accountId === event.fromBroker);

    affectedPositions.forEach(position => {
      // Update account ID to new broker
      position.accountId = event.toBroker;
      position.synchronizationStatus.isSynchronized = false;
      position.synchronizationStatus.pendingSyncOperations = 1;
      
      // Update sync record
      const syncRecord = this.syncRecords.get(position.id);
      if (syncRecord) {
        syncRecord.brokerId = event.toBroker;
        syncRecord.syncStatus = 'pending';
        syncRecord.lastSyncTime = new Date();
      }
    });

    console.log(`📊 Updated ${affectedPositions.length} positions for broker failover`);
  }

  private handleFailoverStarted(event: any): void {
    console.log(`⏸️ Pausing position updates during failover: ${event.fromBroker}`);
    
    // Mark positions as pending sync during failover
    const affectedPositions = Array.from(this.positions.values())
      .filter(pos => pos.accountId === event.fromBroker);

    affectedPositions.forEach(position => {
      position.synchronizationStatus.isSynchronized = false;
      position.synchronizationStatus.pendingSyncOperations++;
    });
  }

  private createBatches<T>(items: T[], batchSize: number): T[][] {
    const batches: T[][] = [];
    for (let i = 0; i < items.length; i += batchSize) {
      batches.push(items.slice(i, i + batchSize));
    }
    return batches;
  }
}