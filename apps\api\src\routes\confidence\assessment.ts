import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { ProgressTrackingService } from '../../services/confidence/ProgressTrackingService.js';
import { ConfidenceAssessmentService } from '../../services/confidence/ConfidenceAssessmentService.js';
import { ConfidenceStage } from '@golddaddy/types';

const router = Router();
const prisma = new PrismaClient();

// Initialize services
const progressService = new ProgressTrackingService(prisma);
const assessmentService = new ConfidenceAssessmentService(prisma);

// Validation schemas
const progressSummarySchema = z.object({
  userId: z.string().min(1)
});

const advancementAttemptSchema = z.object({
  userId: z.string().min(1)
});

const stageReadinessSchema = z.object({
  userId: z.string().min(1),
  targetStage: z.nativeEnum(ConfidenceStage)
});

const activityTrackingSchema = z.object({
  userId: z.string().min(1),
  activityType: z.enum([
    'QUIZ_STARTED',
    'QUIZ_COMPLETED',
    'EDUCATIONAL_CONTENT_ACCESSED',
    'FEEDBACK_REVIEWED',
    'STAGE_ADVANCED',
    'RECOMMENDATION_FOLLOWED'
  ]),
  metadata: z.record(z.any()).optional()
});

// const _detailedReportSchema = z.object({
//   userId: z.string().min(1),
//   includeRecommendations: z.boolean().optional()
// });

// Error handling middleware
const handleAsync = (fn: (...args: any[]) => any) => (req: any, res: any, next: any) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * GET /api/confidence/assessment/progress/:userId
 * Get user's overall confidence assessment progress
 */
router.get('/progress/:userId', handleAsync(async (req: any, res: any) => {
  try {
    const { userId } = progressSummarySchema.parse({ userId: req.params.userId });

    const progressSummary = await progressService.getProgressSummary(userId);

    // Get recent activity for additional context
    const recentActivity = await prisma.userActivity.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        activityType: true,
        metadata: true,
        createdAt: true
      }
    });

    // Get user's confidence assessment
    const assessment = await assessmentService.getCurrentConfidenceAssessment(userId);

    res.json({
      success: true,
      progress: {
        currentStage: progressSummary.currentStage,
        timeInCurrentStage: progressSummary.timeInCurrentStage,
        overallProgress: progressSummary.overallProgress,
        readyForAdvancement: progressSummary.readyForAdvancement,
        nextStage: progressSummary.nextStage,
        estimatedTimeToAdvancement: progressSummary.estimatedTimeToAdvancement,
        criteriaStatus: progressSummary.criteriaStatus,
        milestones: progressSummary.milestones
      },
      assessment: {
        overallConfidenceScore: assessment.overallConfidenceScore,
        assessmentScores: assessment.assessmentScores,
        lastUpdated: assessment.updatedAt
      },
      recommendedActions: progressSummary.recommendedActions,
      recentActivity: recentActivity.map(activity => ({
        type: activity.activityType,
        timestamp: activity.createdAt,
        summary: generateActivitySummary(activity.activityType, activity.metadata)
      })),
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Error getting progress summary:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to get progress summary'
    });
  }
}));

/**
 * POST /api/confidence/assessment/advance
 * Attempt to advance user to next stage
 */
router.post('/advance', handleAsync(async (req: any, res: any) => {
  try {
    const { userId } = advancementAttemptSchema.parse(req.body);

    const result = await progressService.attemptStageAdvancement(userId);

    // Log advancement attempt
    await prisma.userActivity.create({
      data: {
        userId,
        activityType: result.success ? 'STAGE_ADVANCED' : 'ADVANCEMENT_ATTEMPTED',
        metadata: {
          success: result.success,
          newStage: result.newStage,
          message: result.message,
          validationResults: result.validationResults,
          attemptedAt: new Date()
        }
      }
    }).catch(err => {
      console.warn('Failed to log advancement attempt:', err);
    });

    if (result.success) {
      res.json({
        success: true,
        advancement: {
          advanced: true,
          newStage: result.newStage,
          message: result.message,
          validationResults: result.validationResults
        },
        celebration: {
          title: `Congratulations! You've advanced to ${result.newStage ? getStageLabel(result.newStage) : 'Next Stage'}`,
          message: result.newStage ? generateCelebrationMessage(result.newStage) : 'Great progress!',  
          nextSteps: result.newStage ? generateNextSteps(result.newStage) : []
        }
      });
    } else {
      res.json({
        success: true,
        advancement: {
          advanced: false,
          message: result.message,
          validationResults: result.validationResults
        },
        guidance: {
          title: 'Not quite ready yet',
          message: result.message,
          nextSteps: result.validationResults?.recommendations || []
        }
      });
    }
  } catch (error) {
    console.error('Error attempting stage advancement:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to attempt stage advancement'
    });
  }
}));

/**
 * GET /api/confidence/assessment/readiness/:userId/:targetStage
 * Check readiness for a specific stage
 */
router.get('/readiness/:userId/:targetStage', handleAsync(async (req: any, res: any) => {
  try {
    const { userId, targetStage } = stageReadinessSchema.parse({
      userId: req.params.userId,
      targetStage: req.params.targetStage
    });

    const readiness = await progressService.validateStageReadiness(userId, targetStage);

    res.json({
      success: true,
      readiness: {
        stage: targetStage,
        ready: readiness.ready,
        missingRequirements: readiness.missingRequirements,
        completedRequirements: readiness.completedRequirements,
        estimatedCompletionTime: readiness.estimatedCompletionTime,
        confidence: readiness.confidence
      },
      guidance: {
        currentFocus: readiness.ready 
          ? `You're ready for ${getStageLabel(targetStage)}!`
          : `Focus on: ${readiness.missingRequirements.slice(0, 3).join(', ')}`,
        actionPlan: generateActionPlan(readiness.missingRequirements, targetStage),
        estimatedTimeframe: formatTimeEstimate(readiness.estimatedCompletionTime)
      }
    });
  } catch (error) {
    console.error('Error checking stage readiness:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to check stage readiness'
    });
  }
}));

/**
 * POST /api/confidence/assessment/activity
 * Track user activity for progress analytics
 */
router.post('/activity', handleAsync(async (req: any, res: any) => {
  try {
    const { userId, activityType, metadata } = activityTrackingSchema.parse(req.body);

    const activity = await prisma.userActivity.create({
      data: {
        userId,
        activityType,
        metadata: metadata || {},
        createdAt: new Date()
      }
    });

    // Update user's confidence assessment based on activity
    if (activityType === 'QUIZ_COMPLETED') {
      await assessmentService.updateConfidenceFromActivity(userId, {
        type: 'QUIZ_COMPLETION',
        score: metadata?.score || 0,
        timeSpent: metadata?.timeSpent || 0,
        category: metadata?.category
      });
    }

    res.json({
      success: true,
      activity: {
        id: activity.id,
        type: activity.activityType,
        timestamp: activity.createdAt,
        tracked: true
      }
    });
  } catch (error) {
    console.error('Error tracking activity:', error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.errors
      });
    }

    res.status(500).json({
      error: 'Failed to track activity'
    });
  }
}));

/**
 * GET /api/confidence/assessment/report/:userId
 * Get detailed progress report with analytics
 */
router.get('/report/:userId', handleAsync(async (req: any, res: any) => {
  try {
    const { userId } = req.params;
    const { includeRecommendations = true } = req.query;

    const report = await progressService.getDetailedProgressReport(userId);

    // Get additional analytics
    const analytics = await generateProgressAnalytics(userId);

    res.json({
      success: true,
      report: {
        overview: report.overview,
        stageHistory: report.stageHistory.map(stage => ({
          stage: stage.stage,
          completedAt: stage.completedAt,
          metadata: stage.metadata,
          duration: calculateStageDuration(stage)
        })),
        performanceMetrics: {
          quiz: {
            averageScore: report.performanceMetrics.quizPerformance.averageScore,
            improvement: report.performanceMetrics.quizPerformance.improvement,
            consistency: report.performanceMetrics.quizPerformance.consistency,
            strongCategories: report.performanceMetrics.quizPerformance.strongCategories,
            weakCategories: report.performanceMetrics.quizPerformance.weakCategories
          },
          time: {
            totalTimeSpent: report.performanceMetrics.timeMetrics.totalTimeSpent,
            averageSessionTime: report.performanceMetrics.timeMetrics.averageSessionTime,
            efficiency: calculateEfficiencyScore(report.performanceMetrics)
          },
          engagement: analytics.engagement
        },
        recommendations: includeRecommendations ? report.recommendations : undefined
      },
      analytics: {
        progressVelocity: analytics.progressVelocity,
        learningPattern: analytics.learningPattern,
        confidenceCalibration: analytics.confidenceCalibration,
        riskIndicators: analytics.riskIndicators
      },
      generatedAt: new Date()
    });
  } catch (error) {
    console.error('Error generating progress report:', error);
    
    res.status(500).json({
      error: 'Failed to generate progress report'
    });
  }
}));

// Helper functions
function generateActivitySummary(activityType: string, metadata: any): string {
  switch (activityType) {
    case 'QUIZ_COMPLETED':
      return `Completed quiz with ${metadata?.score || 'N/A'}% score`;
    case 'STAGE_ADVANCED':
      return `Advanced to ${getStageLabel(metadata?.newStage)}`;
    case 'EDUCATIONAL_CONTENT_ACCESSED':
      return `Studied ${metadata?.topic || 'educational content'}`;
    default:
      return activityType.replace(/_/g, ' ').toLowerCase();
  }
}

function getStageLabel(stage: ConfidenceStage): string {
  switch (stage) {
    case ConfidenceStage.GOAL_SETTING:
      return 'Goal Setting';
    case ConfidenceStage.STRATEGY_LEARNING:
      return 'Strategy Learning';
    case ConfidenceStage.BACKTESTING_REVIEW:
      return 'Backtesting Review';
    case ConfidenceStage.PAPER_TRADING:
      return 'Paper Trading';
    case ConfidenceStage.LIVE_READY:
      return 'Live Trading Ready';
    default:
      return 'Assessment';
  }
}

function generateCelebrationMessage(stage: ConfidenceStage): string {
  switch (stage) {
    case ConfidenceStage.STRATEGY_LEARNING:
      return 'You\'ve mastered the fundamentals! Ready to dive deeper into trading strategies.';
    case ConfidenceStage.BACKTESTING_REVIEW:
      return 'Great progress! Time to analyze and understand trading strategies through backtesting.';
    case ConfidenceStage.PAPER_TRADING:
      return 'Excellent knowledge base! Ready to practice with paper trading.';
    case ConfidenceStage.LIVE_READY:
      return 'Outstanding achievement! You\'re ready for live trading with proper risk management.';
    default:
      return 'Congratulations on your progress!';
  }
}

function generateNextSteps(stage: ConfidenceStage): string[] {
  switch (stage) {
    case ConfidenceStage.STRATEGY_LEARNING:
      return [
        'Study different trading strategies',
        'Learn about technical and fundamental analysis',
        'Practice identifying trading opportunities'
      ];
    case ConfidenceStage.BACKTESTING_REVIEW:
      return [
        'Analyze historical strategy performance',
        'Understand risk-reward ratios',
        'Learn to interpret backtesting results'
      ];
    case ConfidenceStage.PAPER_TRADING:
      return [
        'Start paper trading with virtual funds',
        'Practice risk management techniques',
        'Track and analyze your trading decisions'
      ];
    case ConfidenceStage.LIVE_READY:
      return [
        'Set up your live trading account',
        'Start with small position sizes',
        'Continue monitoring and learning'
      ];
    default:
      return ['Continue your learning journey'];
  }
}

function generateActionPlan(missingRequirements: string[], _targetStage: ConfidenceStage): string[] {
  const plan: string[] = [];
  
  missingRequirements.forEach(requirement => {
    if (requirement.includes('quiz')) {
      plan.push('Complete additional quiz assessments');
    } else if (requirement.includes('confidence')) {
      plan.push('Focus on building knowledge in weak areas');
    } else if (requirement.includes('time')) {
      plan.push('Continue practicing and learning in current stage');
    } else if (requirement.includes('behavioral')) {
      plan.push('Work on emotional discipline and decision consistency');
    } else {
      plan.push(`Address requirement: ${requirement}`);
    }
  });

  return plan.length > 0 ? plan : ['Continue current learning path'];
}

function formatTimeEstimate(days: number): string {
  if (days < 1) return 'Less than 1 day';
  if (days === 1) return '1 day';
  if (days < 7) return `${Math.ceil(days)} days`;
  if (days < 14) return `${Math.ceil(days / 7)} week${Math.ceil(days / 7) > 1 ? 's' : ''}`;
  return `${Math.ceil(days / 7)} weeks`;
}

async function generateProgressAnalytics(userId: string) {
  const recentActivity = await prisma.userActivity.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
    take: 50
  });

  const quizAttempts = await prisma.quizAttempt.findMany({
    where: { userId },
    orderBy: { createdAt: 'desc' },
    take: 20
  });

  return {
    progressVelocity: calculateProgressVelocity(quizAttempts),
    learningPattern: analyzeLearningPattern(recentActivity),
    confidenceCalibration: calculateConfidenceCalibration(quizAttempts),
    engagement: calculateEngagementMetrics(recentActivity),
    riskIndicators: identifyRiskIndicators(quizAttempts, recentActivity)
  };
}

function calculateProgressVelocity(attempts: any[]): any {
  if (attempts.length < 2) return { trend: 'insufficient_data', velocity: 0 };
  
  const recentScores = attempts.slice(0, 5).map(a => a.overallScore);
  const earlierScores = attempts.slice(5, 10).map(a => a.overallScore);
  
  if (earlierScores.length === 0) return { trend: 'insufficient_data', velocity: 0 };
  
  const recentAvg = recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length;
  const earlierAvg = earlierScores.reduce((sum, score) => sum + score, 0) / earlierScores.length;
  
  const velocity = recentAvg - earlierAvg;
  
  return {
    trend: velocity > 5 ? 'improving' : velocity < -5 ? 'declining' : 'stable',
    velocity: Math.round(velocity * 10) / 10,
    recentAverage: Math.round(recentAvg),
    comparison: Math.round(earlierAvg)
  };
}

function analyzeLearningPattern(activities: any[]): any {
  const dailyActivity = activities.reduce((counts, activity) => {
    const date = new Date(activity.createdAt).toDateString();
    counts[date] = (counts[date] || 0) + 1;
    return counts;
  }, {});

  const days = Object.keys(dailyActivity).length;
  const totalActivities = activities.length;
  
  return {
    consistency: days / 30, // How many days active in last 30 days
    intensity: totalActivities / Math.max(days, 1), // Activities per active day
    pattern: days > 15 ? 'consistent' : days > 7 ? 'moderate' : 'sporadic'
  };
}

function calculateConfidenceCalibration(_attempts: any[]): any {
  // This would analyze how well user's confidence matches their actual performance
  return {
    overconfidenceRate: 0.15,
    underconfidenceRate: 0.10,
    calibrationScore: 0.75
  };
}

function calculateEngagementMetrics(activities: any[]): any {
  const last7Days = activities.filter(a => 
    new Date(a.createdAt) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  );

  return {
    weeklyEngagement: last7Days.length,
    engagementTrend: last7Days.length > 10 ? 'high' : last7Days.length > 5 ? 'moderate' : 'low',
    diversityScore: new Set(activities.slice(0, 20).map(a => a.activityType)).size / 6
  };
}

function identifyRiskIndicators(attempts: any[], activities: any[]): string[] {
  const indicators: string[] = [];
  
  if (attempts.length > 0) {
    const recentScores = attempts.slice(0, 5).map(a => a.overallScore);
    const avgScore = recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length;
    
    if (avgScore < 60) indicators.push('low_performance');
    if (recentScores.length >= 3 && recentScores.every((score, i) => i === 0 || score < recentScores[i-1])) {
      indicators.push('declining_performance');
    }
  }

  const recentActivities = activities.filter(a => 
    new Date(a.createdAt) >= new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  );
  
  if (recentActivities.length < 3) indicators.push('low_engagement');
  
  return indicators;
}

function calculateStageDuration(_stage: any): number {
  // Calculate duration in days if we have completion metadata
  return 0; // Placeholder
}

function calculateEfficiencyScore(_metrics: any): number {
  // Calculate learning efficiency based on time spent vs improvement
  return Math.round(Math.random() * 100); // Placeholder
}

export default router;