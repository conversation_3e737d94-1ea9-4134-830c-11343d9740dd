# Essential Development Commands

## Setup and Development
```bash
npm install                          # Install all dependencies
docker compose up -d postgres redis  # Start databases
npm run dev                         # Start all development servers

# Individual workspace commands  
npm run dev --workspace=@golddaddy/web        # Frontend only
npm run dev --workspace=@golddaddy/api        # API only  
npm run dev --workspace=@golddaddy/mt5-bridge # MT5 Bridge only
```

## Code Quality (CRITICAL - Run after development)
```bash
npm run lint                        # Lint all packages
npm run lint:fix                    # Auto-fix lint issues
npm run format                      # Format with Prettier  
npm run format:check               # Check formatting
npm run type-check                 # TypeScript type checking
```

## Testing (CRITICAL - Run before completion)
```bash
npm run test                       # Run all tests
npm run test:unit                  # Unit tests only
npm run test:integration          # Integration tests only
npm run test:e2e                  # E2E tests with Playwright
npm run test:coverage             # Test coverage report
```

## Build and Production
```bash
npm run build                       # Build all packages
npm run start:prod                  # Start production servers
```

## Database Commands
```bash
npm run db:migrate                 # Run database migrations
npm run test:seed                  # Seed test data
```

## Docker Development
```bash
docker compose up -d               # Start all services
npm run docker:test:up            # Start test containers
npm run docker:test:down          # Stop test containers
```

## Windows System Commands
- `dir` instead of `ls`
- `type` instead of `cat`  
- `findstr` instead of `grep`
- `del` instead of `rm`