#!/usr/bin/env python3
"""
Paper Trading Test Script
Tests paper trading functionality including order placement, position management, and account operations
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List
from loguru import logger
import sys

# Configure logging
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{message}</cyan>",
    level="INFO"
)

class PaperTradingTester:
    """Test client for paper trading functionality"""
    
    def __init__(self, api_url: str = "http://localhost:8001"):
        self.api_url = api_url
        self.test_results = {}
        self.created_orders = []
        self.created_positions = []
        
    async def test_paper_trading_startup(self) -> bool:
        """Test starting the paper trading engine"""
        logger.info("🚀 Testing paper trading engine startup...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{self.api_url}/paper-trading/start") as response:
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"✅ Paper trading engine started: {data.get('message')}")
                        self.test_results['startup'] = True
                        return True
                    else:
                        logger.error(f"❌ Failed to start paper trading engine: {response.status}")
                        self.test_results['startup'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Paper trading startup error: {e}")
            self.test_results['startup'] = False
            return False
    
    async def test_account_info(self) -> bool:
        """Test retrieving account information"""
        logger.info("💰 Testing account information retrieval...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/paper-trading/account") as response:
                    if response.status == 200:
                        data = await response.json()
                        account = data.get('account', {})
                        
                        logger.info(f"✅ Account info retrieved:")
                        logger.info(f"  💵 Balance: ${account.get('balance', 0):.2f}")
                        logger.info(f"  📊 Equity: ${account.get('equity', 0):.2f}")
                        logger.info(f"  📈 Total Trades: {account.get('total_trades', 0)}")
                        logger.info(f"  🎯 Win Rate: {account.get('win_rate', 0):.2%}")
                        
                        self.test_results['account_info'] = True
                        return True
                    else:
                        logger.error(f"❌ Failed to get account info: {response.status}")
                        self.test_results['account_info'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Account info error: {e}")
            self.test_results['account_info'] = False
            return False
    
    async def test_market_buy_order(self, instrument: str = "EURUSD", volume: float = 0.1) -> bool:
        """Test placing a market buy order"""
        logger.info(f"📈 Testing market buy order: {volume} {instrument}...")
        
        try:
            async with aiohttp.ClientSession() as session:
                order_data = {
                    "instrument": instrument,
                    "order_type": "BUY",
                    "volume": volume,
                    "comment": "Test market buy order"
                }
                
                async with session.post(
                    f"{self.api_url}/paper-trading/orders",
                    json=order_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        order_id = data.get('order_id')
                        status = data.get('status')
                        
                        logger.info(f"✅ Market buy order placed: {order_id} (Status: {status})")
                        
                        if order_id:
                            self.created_orders.append(order_id)
                        
                        self.test_results['market_buy_order'] = True
                        return True
                    else:
                        response_text = await response.text()
                        logger.error(f"❌ Failed to place market buy order: {response.status} - {response_text}")
                        self.test_results['market_buy_order'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Market buy order error: {e}")
            self.test_results['market_buy_order'] = False
            return False
    
    async def test_limit_sell_order(self, instrument: str = "EURUSD", volume: float = 0.1) -> bool:
        """Test placing a limit sell order"""
        logger.info(f"📉 Testing limit sell order: {volume} {instrument}...")
        
        try:
            async with aiohttp.ClientSession() as session:
                # Set limit price higher than market (unlikely to fill immediately)
                order_data = {
                    "instrument": instrument,
                    "order_type": "SELL_LIMIT",
                    "volume": volume,
                    "price": 1.15000,  # High price for EUR/USD
                    "stop_loss": 1.16000,
                    "take_profit": 1.14000,
                    "comment": "Test limit sell order"
                }
                
                async with session.post(
                    f"{self.api_url}/paper-trading/orders",
                    json=order_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        order_id = data.get('order_id')
                        status = data.get('status')
                        
                        logger.info(f"✅ Limit sell order placed: {order_id} (Status: {status})")
                        
                        if order_id:
                            self.created_orders.append(order_id)
                        
                        self.test_results['limit_sell_order'] = True
                        return True
                    else:
                        response_text = await response.text()
                        logger.error(f"❌ Failed to place limit sell order: {response.status} - {response_text}")
                        self.test_results['limit_sell_order'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Limit sell order error: {e}")
            self.test_results['limit_sell_order'] = False
            return False
    
    async def test_order_validation(self) -> bool:
        """Test order validation (should fail)"""
        logger.info("🔍 Testing order validation with invalid parameters...")
        
        test_cases = [
            {"instrument": "", "order_type": "BUY", "volume": 0.1, "expected_error": "instrument"},
            {"instrument": "EURUSD", "order_type": "INVALID", "volume": 0.1, "expected_error": "order type"},
            {"instrument": "EURUSD", "order_type": "BUY", "volume": 0, "expected_error": "volume"},
            {"instrument": "EURUSD", "order_type": "BUY", "volume": 10.0, "expected_error": "volume"}  # Too large
        ]
        
        failed_as_expected = 0
        
        try:
            async with aiohttp.ClientSession() as session:
                for i, test_case in enumerate(test_cases):
                    expected_error = test_case.pop("expected_error")
                    
                    async with session.post(
                        f"{self.api_url}/paper-trading/orders",
                        json=test_case
                    ) as response:
                        if response.status != 200:
                            failed_as_expected += 1
                            response_text = await response.text()
                            logger.info(f"  ✅ Test case {i+1} failed as expected: {expected_error} - {response.status}")
                        else:
                            logger.warning(f"  ⚠️ Test case {i+1} should have failed but succeeded")
            
            success_rate = failed_as_expected / len(test_cases)
            logger.info(f"✅ Order validation test: {failed_as_expected}/{len(test_cases)} failed as expected ({success_rate:.0%})")
            
            self.test_results['order_validation'] = success_rate >= 0.75  # At least 75% should fail
            return self.test_results['order_validation']
            
        except Exception as e:
            logger.error(f"❌ Order validation error: {e}")
            self.test_results['order_validation'] = False
            return False
    
    async def test_order_listing(self) -> bool:
        """Test listing all orders"""
        logger.info("📋 Testing order listing...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/paper-trading/orders") as response:
                    if response.status == 200:
                        data = await response.json()
                        orders = data.get('orders', [])
                        
                        logger.info(f"✅ Orders retrieved: {len(orders)} orders")
                        
                        for i, order in enumerate(orders[:3]):  # Show first 3 orders
                            logger.info(f"  📊 Order {i+1}: {order['order_type']} {order['volume']} {order['instrument']} - {order['status']}")
                        
                        self.test_results['order_listing'] = True
                        return True
                    else:
                        logger.error(f"❌ Failed to list orders: {response.status}")
                        self.test_results['order_listing'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Order listing error: {e}")
            self.test_results['order_listing'] = False
            return False
    
    async def test_position_listing(self) -> bool:
        """Test listing all positions"""
        logger.info("📊 Testing position listing...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/paper-trading/positions") as response:
                    if response.status == 200:
                        data = await response.json()
                        positions = data.get('positions', [])
                        
                        logger.info(f"✅ Positions retrieved: {len(positions)} positions")
                        
                        for i, position in enumerate(positions[:3]):  # Show first 3 positions
                            pnl = position.get('unrealized_pnl', 0) + position.get('realized_pnl', 0)
                            logger.info(f"  📈 Position {i+1}: {position['volume']} {position['instrument']} @ {position['entry_price']} - P&L: ${pnl:.2f}")
                            
                            # Store position ID for later tests
                            if position['status'] == 'OPEN' and position['id'] not in self.created_positions:
                                self.created_positions.append(position['id'])
                        
                        self.test_results['position_listing'] = True
                        return True
                    else:
                        logger.error(f"❌ Failed to list positions: {response.status}")
                        self.test_results['position_listing'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Position listing error: {e}")
            self.test_results['position_listing'] = False
            return False
    
    async def test_position_closure(self) -> bool:
        """Test closing a position"""
        logger.info("🔒 Testing position closure...")
        
        if not self.created_positions:
            logger.warning("⚠️ No open positions to close, skipping test")
            self.test_results['position_closure'] = True
            return True
        
        try:
            position_id = self.created_positions[0]
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.api_url}/paper-trading/positions/{position_id}/close"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        closed_volume = data.get('closed_volume', 0)
                        close_price = data.get('close_price', 0)
                        pnl = data.get('pnl', 0)
                        
                        logger.info(f"✅ Position closed: {closed_volume} @ {close_price:.5f} - P&L: ${pnl:.2f}")
                        
                        self.test_results['position_closure'] = True
                        return True
                    else:
                        response_text = await response.text()
                        logger.error(f"❌ Failed to close position: {response.status} - {response_text}")
                        self.test_results['position_closure'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Position closure error: {e}")
            self.test_results['position_closure'] = False
            return False
    
    async def test_order_cancellation(self) -> bool:
        """Test cancelling a pending order"""
        logger.info("❌ Testing order cancellation...")
        
        # First, get pending orders
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/paper-trading/orders") as response:
                    if response.status == 200:
                        data = await response.json()
                        orders = data.get('orders', [])
                        
                        # Find a pending order
                        pending_order = None
                        for order in orders:
                            if order['status'] == 'PENDING':
                                pending_order = order
                                break
                        
                        if not pending_order:
                            logger.warning("⚠️ No pending orders to cancel, skipping test")
                            self.test_results['order_cancellation'] = True
                            return True
                        
                        # Cancel the order
                        order_id = pending_order['id']
                        async with session.delete(
                            f"{self.api_url}/paper-trading/orders/{order_id}"
                        ) as cancel_response:
                            if cancel_response.status == 200:
                                cancel_data = await cancel_response.json()
                                status = cancel_data.get('status')
                                
                                logger.info(f"✅ Order cancelled: {order_id} (Status: {status})")
                                
                                self.test_results['order_cancellation'] = True
                                return True
                            else:
                                response_text = await cancel_response.text()
                                logger.error(f"❌ Failed to cancel order: {cancel_response.status} - {response_text}")
                                self.test_results['order_cancellation'] = False
                                return False
                    else:
                        logger.error(f"❌ Failed to get orders for cancellation test: {response.status}")
                        self.test_results['order_cancellation'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Order cancellation error: {e}")
            self.test_results['order_cancellation'] = False
            return False
    
    async def test_trading_statistics(self) -> bool:
        """Test retrieving trading statistics"""
        logger.info("📊 Testing trading statistics...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/paper-trading/statistics") as response:
                    if response.status == 200:
                        data = await response.json()
                        stats = data.get('statistics', {})
                        
                        logger.info("✅ Trading statistics retrieved:")
                        logger.info(f"  📊 Orders Processed: {stats.get('orders_processed', 0)}")
                        logger.info(f"  ✅ Orders Filled: {stats.get('orders_filled', 0)}")
                        logger.info(f"  📈 Positions Opened: {stats.get('positions_opened', 0)}")
                        logger.info(f"  📉 Positions Closed: {stats.get('positions_closed', 0)}")
                        logger.info(f"  💱 Total Volume Traded: {stats.get('total_volume_traded', 0):.2f}")
                        logger.info(f"  🎯 Fill Rate: {stats.get('fill_rate', 0):.2%}")
                        logger.info(f"  ⏰ Uptime: {stats.get('uptime_seconds', 0):.0f} seconds")
                        
                        self.test_results['trading_statistics'] = True
                        return True
                    else:
                        logger.error(f"❌ Failed to get trading statistics: {response.status}")
                        self.test_results['trading_statistics'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Trading statistics error: {e}")
            self.test_results['trading_statistics'] = False
            return False
    
    async def test_multiple_orders_execution(self) -> bool:
        """Test placing multiple orders in sequence"""
        logger.info("🔄 Testing multiple orders execution...")
        
        test_orders = [
            {"instrument": "GBPUSD", "order_type": "BUY", "volume": 0.05, "comment": "Test order 1"},
            {"instrument": "USDJPY", "order_type": "SELL", "volume": 0.03, "comment": "Test order 2"},
            {"instrument": "EURUSD", "order_type": "BUY_LIMIT", "volume": 0.02, "price": 1.05000, "comment": "Test order 3"}
        ]
        
        successful_orders = 0
        
        try:
            async with aiohttp.ClientSession() as session:
                for i, order_data in enumerate(test_orders):
                    async with session.post(
                        f"{self.api_url}/paper-trading/orders",
                        json=order_data
                    ) as response:
                        if response.status == 200:
                            data = await response.json()
                            order_id = data.get('order_id')
                            successful_orders += 1
                            logger.info(f"  ✅ Order {i+1}: {order_id} placed successfully")
                        else:
                            response_text = await response.text()
                            logger.warning(f"  ⚠️ Order {i+1} failed: {response.status} - {response_text}")
                    
                    # Small delay between orders
                    await asyncio.sleep(0.5)
            
            success_rate = successful_orders / len(test_orders)
            logger.info(f"✅ Multiple orders execution: {successful_orders}/{len(test_orders)} successful ({success_rate:.0%})")
            
            self.test_results['multiple_orders_execution'] = success_rate >= 0.8  # At least 80% should succeed
            return self.test_results['multiple_orders_execution']
            
        except Exception as e:
            logger.error(f"❌ Multiple orders execution error: {e}")
            self.test_results['multiple_orders_execution'] = False
            return False
    
    async def test_account_state_consistency(self) -> bool:
        """Test account state consistency after operations"""
        logger.info("🔍 Testing account state consistency...")
        
        try:
            # Get initial account state
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/paper-trading/account") as response:
                    if response.status == 200:
                        data = await response.json()
                        account = data.get('account', {})
                        
                        balance = account.get('balance', 0)
                        equity = account.get('equity', 0)
                        total_trades = account.get('total_trades', 0)
                        open_positions = account.get('open_positions', 0)
                        pending_orders = account.get('pending_orders', 0)
                        
                        # Basic consistency checks
                        checks_passed = 0
                        total_checks = 4
                        
                        # Check 1: Balance should be positive
                        if balance > 0:
                            checks_passed += 1
                            logger.info(f"  ✅ Balance is positive: ${balance:.2f}")
                        else:
                            logger.warning(f"  ⚠️ Balance is not positive: ${balance:.2f}")
                        
                        # Check 2: Equity should be close to balance (within reason)
                        if abs(equity - balance) <= balance * 0.5:  # Within 50% is reasonable for testing
                            checks_passed += 1
                            logger.info(f"  ✅ Equity is reasonable: ${equity:.2f} vs ${balance:.2f}")
                        else:
                            logger.warning(f"  ⚠️ Equity seems inconsistent: ${equity:.2f} vs ${balance:.2f}")
                        
                        # Check 3: Total trades should be reasonable
                        if total_trades >= 0:
                            checks_passed += 1
                            logger.info(f"  ✅ Total trades count is valid: {total_trades}")
                        else:
                            logger.warning(f"  ⚠️ Total trades count is invalid: {total_trades}")
                        
                        # Check 4: Open positions and pending orders should be non-negative
                        if open_positions >= 0 and pending_orders >= 0:
                            checks_passed += 1
                            logger.info(f"  ✅ Open positions and pending orders are valid: {open_positions}, {pending_orders}")
                        else:
                            logger.warning(f"  ⚠️ Open positions or pending orders are invalid: {open_positions}, {pending_orders}")
                        
                        consistency_score = checks_passed / total_checks
                        logger.info(f"✅ Account state consistency: {checks_passed}/{total_checks} checks passed ({consistency_score:.0%})")
                        
                        self.test_results['account_state_consistency'] = consistency_score >= 0.8
                        return self.test_results['account_state_consistency']
                    else:
                        logger.error(f"❌ Failed to get account info for consistency test: {response.status}")
                        self.test_results['account_state_consistency'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Account state consistency error: {e}")
            self.test_results['account_state_consistency'] = False
            return False
    
    def print_summary(self):
        """Print test results summary"""
        logger.info("=" * 60)
        logger.info("📋 PAPER TRADING TEST SUMMARY")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{test_name:35} {status}")
        
        logger.info("-" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        logger.info("=" * 60)

async def main():
    """Main test function"""
    logger.info("🚀 Starting Paper Trading Tests")
    logger.info("=" * 60)
    
    tester = PaperTradingTester()
    
    # Run tests in sequence
    tests = [
        ("Paper Trading Startup", tester.test_paper_trading_startup()),
        ("Account Information", tester.test_account_info()),
        ("Market Buy Order", tester.test_market_buy_order("EURUSD", 0.1)),
        ("Limit Sell Order", tester.test_limit_sell_order("EURUSD", 0.05)),
        ("Order Validation", tester.test_order_validation()),
        ("Order Listing", tester.test_order_listing()),
        ("Position Listing", tester.test_position_listing()),
        ("Order Cancellation", tester.test_order_cancellation()),
        ("Position Closure", tester.test_position_closure()),
        ("Multiple Orders Execution", tester.test_multiple_orders_execution()),
        ("Trading Statistics", tester.test_trading_statistics()),
        ("Account State Consistency", tester.test_account_state_consistency())
    ]
    
    for test_name, test_coro in tests:
        logger.info(f"🧪 Running: {test_name}")
        try:
            result = await test_coro
            logger.info(f"{'✅' if result else '❌'} {test_name}: {'PASS' if result else 'FAIL'}")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            tester.test_results[test_name.lower().replace(' ', '_')] = False
        
        # Brief pause between tests
        await asyncio.sleep(2)
    
    # Print summary
    tester.print_summary()
    
    # Return overall success
    all_passed = all(tester.test_results.values())
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)