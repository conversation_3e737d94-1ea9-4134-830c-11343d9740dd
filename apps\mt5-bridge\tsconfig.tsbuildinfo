{"fileNames": ["../../node_modules/typescript/lib/lib.es5.d.ts", "../../node_modules/typescript/lib/lib.es2015.d.ts", "../../node_modules/typescript/lib/lib.es2016.d.ts", "../../node_modules/typescript/lib/lib.es2017.d.ts", "../../node_modules/typescript/lib/lib.es2018.d.ts", "../../node_modules/typescript/lib/lib.es2019.d.ts", "../../node_modules/typescript/lib/lib.es2020.d.ts", "../../node_modules/typescript/lib/lib.es2021.d.ts", "../../node_modules/typescript/lib/lib.es2022.d.ts", "../../node_modules/typescript/lib/lib.es2015.core.d.ts", "../../node_modules/typescript/lib/lib.es2015.collection.d.ts", "../../node_modules/typescript/lib/lib.es2015.generator.d.ts", "../../node_modules/typescript/lib/lib.es2015.iterable.d.ts", "../../node_modules/typescript/lib/lib.es2015.promise.d.ts", "../../node_modules/typescript/lib/lib.es2015.proxy.d.ts", "../../node_modules/typescript/lib/lib.es2015.reflect.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2016.array.include.d.ts", "../../node_modules/typescript/lib/lib.es2016.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.arraybuffer.d.ts", "../../node_modules/typescript/lib/lib.es2017.date.d.ts", "../../node_modules/typescript/lib/lib.es2017.object.d.ts", "../../node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2017.string.d.ts", "../../node_modules/typescript/lib/lib.es2017.intl.d.ts", "../../node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "../../node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "../../node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "../../node_modules/typescript/lib/lib.es2018.intl.d.ts", "../../node_modules/typescript/lib/lib.es2018.promise.d.ts", "../../node_modules/typescript/lib/lib.es2018.regexp.d.ts", "../../node_modules/typescript/lib/lib.es2019.array.d.ts", "../../node_modules/typescript/lib/lib.es2019.object.d.ts", "../../node_modules/typescript/lib/lib.es2019.string.d.ts", "../../node_modules/typescript/lib/lib.es2019.symbol.d.ts", "../../node_modules/typescript/lib/lib.es2019.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.bigint.d.ts", "../../node_modules/typescript/lib/lib.es2020.date.d.ts", "../../node_modules/typescript/lib/lib.es2020.promise.d.ts", "../../node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "../../node_modules/typescript/lib/lib.es2020.string.d.ts", "../../node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "../../node_modules/typescript/lib/lib.es2020.intl.d.ts", "../../node_modules/typescript/lib/lib.es2020.number.d.ts", "../../node_modules/typescript/lib/lib.es2021.promise.d.ts", "../../node_modules/typescript/lib/lib.es2021.string.d.ts", "../../node_modules/typescript/lib/lib.es2021.weakref.d.ts", "../../node_modules/typescript/lib/lib.es2021.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.array.d.ts", "../../node_modules/typescript/lib/lib.es2022.error.d.ts", "../../node_modules/typescript/lib/lib.es2022.intl.d.ts", "../../node_modules/typescript/lib/lib.es2022.object.d.ts", "../../node_modules/typescript/lib/lib.es2022.string.d.ts", "../../node_modules/typescript/lib/lib.es2022.regexp.d.ts", "../../node_modules/typescript/lib/lib.decorators.d.ts", "../../node_modules/typescript/lib/lib.decorators.legacy.d.ts", "../../node_modules/@types/node/compatibility/disposable.d.ts", "../../node_modules/@types/node/compatibility/indexable.d.ts", "../../node_modules/@types/node/compatibility/iterators.d.ts", "../../node_modules/@types/node/compatibility/index.d.ts", "../../node_modules/@types/node/globals.typedarray.d.ts", "../../node_modules/@types/node/buffer.buffer.d.ts", "../../node_modules/buffer/index.d.ts", "../../node_modules/undici-types/header.d.ts", "../../node_modules/undici-types/readable.d.ts", "../../node_modules/undici-types/file.d.ts", "../../node_modules/undici-types/fetch.d.ts", "../../node_modules/undici-types/formdata.d.ts", "../../node_modules/undici-types/connector.d.ts", "../../node_modules/undici-types/client.d.ts", "../../node_modules/undici-types/errors.d.ts", "../../node_modules/undici-types/dispatcher.d.ts", "../../node_modules/undici-types/global-dispatcher.d.ts", "../../node_modules/undici-types/global-origin.d.ts", "../../node_modules/undici-types/pool-stats.d.ts", "../../node_modules/undici-types/pool.d.ts", "../../node_modules/undici-types/handlers.d.ts", "../../node_modules/undici-types/balanced-pool.d.ts", "../../node_modules/undici-types/agent.d.ts", "../../node_modules/undici-types/mock-interceptor.d.ts", "../../node_modules/undici-types/mock-agent.d.ts", "../../node_modules/undici-types/mock-client.d.ts", "../../node_modules/undici-types/mock-pool.d.ts", "../../node_modules/undici-types/mock-errors.d.ts", "../../node_modules/undici-types/proxy-agent.d.ts", "../../node_modules/undici-types/env-http-proxy-agent.d.ts", "../../node_modules/undici-types/retry-handler.d.ts", "../../node_modules/undici-types/retry-agent.d.ts", "../../node_modules/undici-types/api.d.ts", "../../node_modules/undici-types/interceptors.d.ts", "../../node_modules/undici-types/util.d.ts", "../../node_modules/undici-types/cookies.d.ts", "../../node_modules/undici-types/patch.d.ts", "../../node_modules/undici-types/websocket.d.ts", "../../node_modules/undici-types/eventsource.d.ts", "../../node_modules/undici-types/filereader.d.ts", "../../node_modules/undici-types/diagnostics-channel.d.ts", "../../node_modules/undici-types/content-type.d.ts", "../../node_modules/undici-types/cache.d.ts", "../../node_modules/undici-types/index.d.ts", "../../node_modules/@types/node/globals.d.ts", "../../node_modules/@types/node/assert.d.ts", "../../node_modules/@types/node/assert/strict.d.ts", "../../node_modules/@types/node/async_hooks.d.ts", "../../node_modules/@types/node/buffer.d.ts", "../../node_modules/@types/node/child_process.d.ts", "../../node_modules/@types/node/cluster.d.ts", "../../node_modules/@types/node/console.d.ts", "../../node_modules/@types/node/constants.d.ts", "../../node_modules/@types/node/crypto.d.ts", "../../node_modules/@types/node/dgram.d.ts", "../../node_modules/@types/node/diagnostics_channel.d.ts", "../../node_modules/@types/node/dns.d.ts", "../../node_modules/@types/node/dns/promises.d.ts", "../../node_modules/@types/node/domain.d.ts", "../../node_modules/@types/node/dom-events.d.ts", "../../node_modules/@types/node/events.d.ts", "../../node_modules/@types/node/fs.d.ts", "../../node_modules/@types/node/fs/promises.d.ts", "../../node_modules/@types/node/http.d.ts", "../../node_modules/@types/node/http2.d.ts", "../../node_modules/@types/node/https.d.ts", "../../node_modules/@types/node/inspector.d.ts", "../../node_modules/@types/node/module.d.ts", "../../node_modules/@types/node/net.d.ts", "../../node_modules/@types/node/os.d.ts", "../../node_modules/@types/node/path.d.ts", "../../node_modules/@types/node/perf_hooks.d.ts", "../../node_modules/@types/node/process.d.ts", "../../node_modules/@types/node/punycode.d.ts", "../../node_modules/@types/node/querystring.d.ts", "../../node_modules/@types/node/readline.d.ts", "../../node_modules/@types/node/readline/promises.d.ts", "../../node_modules/@types/node/repl.d.ts", "../../node_modules/@types/node/sea.d.ts", "../../node_modules/@types/node/stream.d.ts", "../../node_modules/@types/node/stream/promises.d.ts", "../../node_modules/@types/node/stream/consumers.d.ts", "../../node_modules/@types/node/stream/web.d.ts", "../../node_modules/@types/node/string_decoder.d.ts", "../../node_modules/@types/node/test.d.ts", "../../node_modules/@types/node/timers.d.ts", "../../node_modules/@types/node/timers/promises.d.ts", "../../node_modules/@types/node/tls.d.ts", "../../node_modules/@types/node/trace_events.d.ts", "../../node_modules/@types/node/tty.d.ts", "../../node_modules/@types/node/url.d.ts", "../../node_modules/@types/node/util.d.ts", "../../node_modules/@types/node/v8.d.ts", "../../node_modules/@types/node/vm.d.ts", "../../node_modules/@types/node/wasi.d.ts", "../../node_modules/@types/node/worker_threads.d.ts", "../../node_modules/@types/node/zlib.d.ts", "../../node_modules/@types/node/index.d.ts", "../../node_modules/@types/ws/index.d.ts", "../../packages/config/dist/index.d.ts", "../../packages/types/dist/validation.d.ts", "../../packages/types/dist/optimization.d.ts", "../../packages/types/dist/metrics.d.ts", "../../node_modules/decimal.js/decimal.d.ts", "../../packages/types/dist/market-data.d.ts", "../../packages/types/dist/broker-failover.d.ts", "../../packages/types/dist/index.d.ts", "../../node_modules/axios/index.d.ts", "./src/python-bridge.ts", "./src/index.ts", "./node_modules/@vitest/utils/dist/types.d.ts", "./node_modules/@vitest/utils/dist/helpers.d.ts", "../../node_modules/@sinclair/typebox/typebox.d.ts", "../../node_modules/@jest/schemas/build/index.d.ts", "./node_modules/pretty-format/build/index.d.ts", "./node_modules/@vitest/utils/dist/index.d.ts", "./node_modules/@vitest/runner/dist/tasks-k5xerdtv.d.ts", "./node_modules/@vitest/utils/dist/types-9l4nily8.d.ts", "./node_modules/@vitest/utils/dist/diff.d.ts", "./node_modules/@vitest/utils/diff.d.ts", "./node_modules/@vitest/runner/dist/types.d.ts", "./node_modules/@vitest/utils/dist/error.d.ts", "./node_modules/@vitest/utils/error.d.ts", "./node_modules/@vitest/runner/dist/index.d.ts", "./node_modules/@vitest/runner/dist/utils.d.ts", "./node_modules/@vitest/runner/utils.d.ts", "../../node_modules/@types/estree/index.d.ts", "../../node_modules/rollup/dist/rollup.d.ts", "./node_modules/vitest/node_modules/vite/types/hmrpayload.d.ts", "./node_modules/vitest/node_modules/vite/types/customevent.d.ts", "./node_modules/vitest/node_modules/vite/types/hot.d.ts", "./node_modules/vitest/node_modules/vite/dist/node/types.d-agj9qkwt.d.ts", "./node_modules/esbuild/lib/main.d.ts", "../../node_modules/source-map-js/source-map.d.ts", "./node_modules/postcss/lib/previous-map.d.ts", "./node_modules/postcss/lib/input.d.ts", "./node_modules/postcss/lib/css-syntax-error.d.ts", "./node_modules/postcss/lib/declaration.d.ts", "./node_modules/postcss/lib/root.d.ts", "./node_modules/postcss/lib/warning.d.ts", "./node_modules/postcss/lib/lazy-result.d.ts", "./node_modules/postcss/lib/no-work-result.d.ts", "./node_modules/postcss/lib/processor.d.ts", "./node_modules/postcss/lib/result.d.ts", "./node_modules/postcss/lib/document.d.ts", "./node_modules/postcss/lib/rule.d.ts", "./node_modules/postcss/lib/node.d.ts", "./node_modules/postcss/lib/comment.d.ts", "./node_modules/postcss/lib/container.d.ts", "./node_modules/postcss/lib/at-rule.d.ts", "./node_modules/postcss/lib/list.d.ts", "./node_modules/postcss/lib/postcss.d.ts", "./node_modules/vitest/node_modules/vite/dist/node/runtime.d.ts", "./node_modules/vitest/node_modules/vite/types/importglob.d.ts", "./node_modules/vitest/node_modules/vite/types/metadata.d.ts", "./node_modules/vitest/node_modules/vite/dist/node/index.d.ts", "./node_modules/vite-node/dist/trace-mapping.d-xyifztpm.d.ts", "./node_modules/vite-node/dist/index-o2irwhkf.d.ts", "./node_modules/vite-node/dist/index.d.ts", "./node_modules/@vitest/snapshot/dist/environment-cmigivxz.d.ts", "./node_modules/@vitest/snapshot/dist/index-s94asl6q.d.ts", "./node_modules/@vitest/snapshot/dist/index.d.ts", "./node_modules/@vitest/expect/dist/chai.d.cts", "./node_modules/@vitest/expect/dist/index.d.ts", "./node_modules/@vitest/expect/index.d.ts", "../../node_modules/tinybench/dist/index.d.cts", "./node_modules/vite-node/dist/client.d.ts", "./node_modules/@vitest/snapshot/dist/manager.d.ts", "./node_modules/@vitest/snapshot/manager.d.ts", "./node_modules/vite-node/node_modules/vite/dist/node/index.d.ts", "./node_modules/vite-node/dist/server.d.ts", "../../node_modules/@types/deep-eql/index.d.ts", "../../node_modules/@types/chai/index.d.ts", "./node_modules/vitest/dist/reporters-w_64as5f.d.ts", "./node_modules/vitest/dist/suite-dwqifb_-.d.ts", "./node_modules/@vitest/spy/dist/index.d.ts", "./node_modules/@vitest/snapshot/dist/environment.d.ts", "./node_modules/@vitest/snapshot/environment.d.ts", "./node_modules/vitest/dist/index.d.ts", "./src/__tests__/mt5-bridge.test.ts", "./src/adapters/standardbrokeradapter.ts", "./src/adapters/metaquotesdemoadapter.ts", "./src/adapters/productionbrokeradapter.ts", "./src/adapters/brokeradapterfactory.ts", "./src/monitoring/mt5healthmonitor.ts", "./src/services/accountsynchronizationservice.ts", "./src/services/productionmt5connectionmanager.ts", "./src/services/connectionpoolmanager.ts", "./src/services/errorsimulationframework.ts", "./src/services/historicaldataservice.ts", "./src/services/mt5simulatorservice.ts", "./src/services/ordermanagementsimulator.ts", "./src/services/productiontradeexecutor.ts", "./src/services/reconnectionengine.ts", "./src/services/tradeexecutionsimulator.ts", "./src/services/__tests__/errorsimulationframework.test.ts", "./src/services/__tests__/historicaldataservice.test.ts", "./src/services/__tests__/mt5simulatorservice.test.ts", "./src/services/__tests__/ordermanagementsimulator.test.ts", "./src/services/__tests__/productionmt5integration.test.ts", "./src/services/__tests__/tradeexecutionsimulator.test.ts", "./src/test/setup.ts", "../../node_modules/@types/aria-query/index.d.ts", "../../node_modules/@types/bcryptjs/index.d.ts", "../../node_modules/@types/better-sqlite3/index.d.ts", "../../node_modules/@types/connect/index.d.ts", "../../node_modules/@types/body-parser/index.d.ts", "../../node_modules/@types/cookie/index.d.ts", "../../node_modules/@types/cookiejar/index.d.ts", "../../node_modules/@types/cors/index.d.ts", "../../node_modules/@types/decimal.js/index.d.ts", "../../node_modules/@types/mime/index.d.ts", "../../node_modules/@types/send/index.d.ts", "../../node_modules/@types/qs/index.d.ts", "../../node_modules/@types/range-parser/index.d.ts", "../../node_modules/@types/express-serve-static-core/index.d.ts", "../../node_modules/@types/http-errors/index.d.ts", "../../node_modules/@types/serve-static/index.d.ts", "../../node_modules/@types/express/index.d.ts", "../../node_modules/@types/istanbul-lib-coverage/index.d.ts", "../../node_modules/@types/json-schema/index.d.ts", "../../node_modules/@types/json5/index.d.ts", "../../node_modules/@types/ms/index.d.ts", "../../node_modules/@types/jsonwebtoken/index.d.ts", "../../node_modules/@types/methods/index.d.ts", "../../node_modules/@types/phoenix/index.d.ts", "../../node_modules/@types/prop-types/index.d.ts", "../../node_modules/@types/react/global.d.ts", "../../node_modules/csstype/index.d.ts", "../../node_modules/@types/react/index.d.ts", "../../node_modules/@types/react-dom/index.d.ts", "../../node_modules/@types/semver/classes/semver.d.ts", "../../node_modules/@types/semver/functions/parse.d.ts", "../../node_modules/@types/semver/functions/valid.d.ts", "../../node_modules/@types/semver/functions/clean.d.ts", "../../node_modules/@types/semver/functions/inc.d.ts", "../../node_modules/@types/semver/functions/diff.d.ts", "../../node_modules/@types/semver/functions/major.d.ts", "../../node_modules/@types/semver/functions/minor.d.ts", "../../node_modules/@types/semver/functions/patch.d.ts", "../../node_modules/@types/semver/functions/prerelease.d.ts", "../../node_modules/@types/semver/functions/compare.d.ts", "../../node_modules/@types/semver/functions/rcompare.d.ts", "../../node_modules/@types/semver/functions/compare-loose.d.ts", "../../node_modules/@types/semver/functions/compare-build.d.ts", "../../node_modules/@types/semver/functions/sort.d.ts", "../../node_modules/@types/semver/functions/rsort.d.ts", "../../node_modules/@types/semver/functions/gt.d.ts", "../../node_modules/@types/semver/functions/lt.d.ts", "../../node_modules/@types/semver/functions/eq.d.ts", "../../node_modules/@types/semver/functions/neq.d.ts", "../../node_modules/@types/semver/functions/gte.d.ts", "../../node_modules/@types/semver/functions/lte.d.ts", "../../node_modules/@types/semver/functions/cmp.d.ts", "../../node_modules/@types/semver/functions/coerce.d.ts", "../../node_modules/@types/semver/classes/comparator.d.ts", "../../node_modules/@types/semver/classes/range.d.ts", "../../node_modules/@types/semver/functions/satisfies.d.ts", "../../node_modules/@types/semver/ranges/max-satisfying.d.ts", "../../node_modules/@types/semver/ranges/min-satisfying.d.ts", "../../node_modules/@types/semver/ranges/to-comparators.d.ts", "../../node_modules/@types/semver/ranges/min-version.d.ts", "../../node_modules/@types/semver/ranges/valid.d.ts", "../../node_modules/@types/semver/ranges/outside.d.ts", "../../node_modules/@types/semver/ranges/gtr.d.ts", "../../node_modules/@types/semver/ranges/ltr.d.ts", "../../node_modules/@types/semver/ranges/intersects.d.ts", "../../node_modules/@types/semver/ranges/simplify.d.ts", "../../node_modules/@types/semver/ranges/subset.d.ts", "../../node_modules/@types/semver/internals/identifiers.d.ts", "../../node_modules/@types/semver/index.d.ts", "../../node_modules/@types/statuses/index.d.ts", "../../node_modules/@types/superagent/lib/agent-base.d.ts", "../../node_modules/@types/superagent/lib/node/response.d.ts", "../../node_modules/@types/superagent/types.d.ts", "../../node_modules/@types/superagent/lib/node/agent.d.ts", "../../node_modules/@types/superagent/lib/request-base.d.ts", "../../node_modules/form-data/index.d.ts", "../../node_modules/@types/superagent/lib/node/http2wrapper.d.ts", "../../node_modules/@types/superagent/lib/node/index.d.ts", "../../node_modules/@types/superagent/index.d.ts", "../../node_modules/@types/supertest/types.d.ts", "../../node_modules/@types/supertest/lib/agent.d.ts", "../../node_modules/@types/supertest/lib/test.d.ts", "../../node_modules/@types/supertest/index.d.ts", "../../node_modules/@types/tough-cookie/index.d.ts", "../../node_modules/@types/uuid/index.d.ts"], "fileIdsList": [[63, 106], [63, 106, 173, 177], [63, 106, 221], [63, 106, 173, 174, 177, 178, 180], [63, 106, 173], [63, 106, 173, 174, 177], [63, 106, 173, 174], [63, 106, 182], [63, 106, 217], [63, 106, 172, 217], [63, 106, 172, 217, 218], [63, 106, 234], [63, 106, 225], [63, 106, 176], [63, 106, 172, 175], [63, 106, 168], [63, 106, 168, 169, 172], [63, 106, 172], [63, 106, 179], [63, 106, 206], [63, 106, 204, 206], [63, 106, 195, 203, 204, 205, 207, 209], [63, 106, 193], [63, 106, 196, 201, 206, 209], [63, 106, 192, 209], [63, 106, 196, 197, 200, 201, 202, 209], [63, 106, 196, 197, 198, 200, 201, 209], [63, 106, 193, 194, 195, 196, 197, 201, 202, 203, 205, 206, 207, 209], [63, 106, 191, 193, 194, 195, 196, 197, 198, 200, 201, 202, 203, 204, 205, 206, 207, 208], [63, 106, 191, 209], [63, 106, 196, 198, 199, 201, 202, 209], [63, 106, 200, 209], [63, 106, 201, 202, 206, 209], [63, 106, 194, 204], [63, 106, 171], [63, 106, 214, 215], [63, 106, 214], [63, 106, 213, 214, 215, 231], [63, 106, 118, 119, 121, 122, 123, 126, 137, 145, 148, 154, 185, 186, 187, 188, 189, 190, 209, 210, 211, 212], [63, 106, 119, 137, 153, 173, 177, 181, 183, 213, 216, 219, 220, 222, 223, 224, 226, 228, 231, 232, 233, 235], [63, 106, 119, 137, 153, 173, 181, 183, 213, 216, 219, 220, 222, 223, 224, 226, 228, 231], [63, 106, 181, 183, 223, 231], [63, 106, 118, 119, 121, 122, 123, 126, 137, 145, 148, 154, 155, 185, 186, 187, 188, 189, 190, 209, 210, 211, 212], [63, 106, 186, 187, 188, 189], [63, 106, 186, 187, 188], [63, 106, 186], [63, 106, 187], [63, 106, 185], [63, 106, 156, 166, 167, 236], [63, 106, 238, 239, 240], [63, 106, 164, 238, 241], [63, 106, 118, 164, 241], [63, 106, 156, 157, 164, 166], [63, 106, 118, 238], [63, 106, 118, 156, 164, 165], [63, 106, 236, 246], [63, 106, 164, 236, 247], [63, 106, 164, 236, 248], [63, 106, 164, 236, 249], [63, 106, 236, 239, 241, 242, 243, 244, 245, 250, 251], [63, 106, 164, 236, 252], [63, 106, 118, 244], [63, 106, 118], [63, 106, 164], [63, 106, 118, 164], [63, 106, 118, 238, 241], [63, 106, 236], [63, 106, 170], [63, 106, 155], [63, 106, 121, 155, 263], [63, 106, 229], [63, 106, 121, 155], [63, 106, 118, 121, 155, 270, 271, 272], [63, 106, 264, 271, 273, 275], [63, 106, 111, 155, 280], [63, 103, 106], [63, 105, 106], [106], [63, 106, 111, 140], [63, 106, 107, 112, 118, 126, 137, 148], [63, 106, 107, 108, 118, 126], [58, 59, 60, 63, 106], [63, 106, 109, 149], [63, 106, 110, 111, 119, 127], [63, 106, 111, 137, 145], [63, 106, 112, 114, 118, 126], [63, 105, 106, 113], [63, 106, 114, 115], [63, 106, 116, 118], [63, 105, 106, 118], [63, 106, 118, 119, 120, 137, 148], [63, 106, 118, 119, 120, 133, 137, 140], [63, 101, 106], [63, 106, 114, 118, 121, 126, 137, 148], [63, 106, 118, 119, 121, 122, 126, 137, 145, 148], [63, 106, 121, 123, 137, 145, 148], [61, 62, 63, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154], [63, 106, 118, 124], [63, 106, 125, 148, 153], [63, 106, 114, 118, 126, 137], [63, 106, 127], [63, 106, 128], [63, 105, 106, 129], [63, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154], [63, 106, 131], [63, 106, 132], [63, 106, 118, 133, 134], [63, 106, 133, 135, 149, 151], [63, 106, 118, 137, 138, 140], [63, 106, 139, 140], [63, 106, 137, 138], [63, 106, 140], [63, 106, 141], [63, 103, 106, 137, 142], [63, 106, 118, 143, 144], [63, 106, 143, 144], [63, 106, 111, 126, 137, 145], [63, 106, 146], [63, 106, 126, 147], [63, 106, 121, 132, 148], [63, 106, 111, 149], [63, 106, 137, 150], [63, 106, 125, 151], [63, 106, 152], [63, 106, 118, 120, 129, 137, 140, 148, 151, 153], [63, 106, 137, 154], [63, 106, 287], [63, 106, 284, 285, 286], [63, 106, 289, 328], [63, 106, 289, 313, 328], [63, 106, 328], [63, 106, 289], [63, 106, 289, 314, 328], [63, 106, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327], [63, 106, 314, 328], [63, 106, 119, 137, 155, 269], [63, 106, 121, 155, 270, 274], [63, 106, 337], [63, 106, 266, 282, 330, 332, 338], [63, 106, 122, 126, 137, 145, 155], [63, 106, 119, 121, 122, 123, 126, 137, 282, 331, 332, 333, 334, 335, 336], [63, 106, 121, 137, 337], [63, 106, 119, 331, 332], [63, 106, 148, 331], [63, 106, 338, 339, 340, 341], [63, 106, 338, 339, 342], [63, 106, 338, 339], [63, 106, 121, 122, 126, 282, 338], [63, 106, 118, 121, 123, 126, 137, 145, 148, 154, 155], [63, 106, 121, 137, 155], [63, 106, 184, 185], [63, 73, 77, 106, 148], [63, 73, 106, 137, 148], [63, 68, 106], [63, 70, 73, 106, 145, 148], [63, 106, 126, 145], [63, 68, 106, 155], [63, 70, 73, 106, 126, 148], [63, 65, 66, 69, 72, 106, 118, 137, 148], [63, 73, 80, 106], [63, 65, 71, 106], [63, 73, 94, 95, 106], [63, 69, 73, 106, 140, 148, 155], [63, 94, 106, 155], [63, 67, 68, 106, 155], [63, 73, 106], [63, 67, 68, 69, 70, 71, 72, 73, 74, 75, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 95, 96, 97, 98, 99, 100, 106], [63, 73, 88, 106], [63, 73, 80, 81, 106], [63, 71, 73, 81, 82, 106], [63, 72, 106], [63, 65, 68, 73, 106], [63, 73, 77, 81, 82, 106], [63, 77, 106], [63, 71, 73, 76, 106, 148], [63, 65, 70, 73, 80, 106], [63, 106, 137], [63, 68, 73, 94, 106, 153, 155], [63, 106, 158, 159, 160, 162, 163], [63, 106, 268]], "fileInfos": [{"version": "c430d44666289dae81f30fa7b2edebf186ecc91a2d4c71266ea6ae76388792e1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "45b7ab580deca34ae9729e97c13cfd999df04416a79116c3bfb483804f85ded4", "impliedFormat": 1}, {"version": "3facaf05f0c5fc569c5649dd359892c98a85557e3e0c847964caeb67076f4d75", "impliedFormat": 1}, {"version": "e44bb8bbac7f10ecc786703fe0a6a4b952189f908707980ba8f3c8975a760962", "impliedFormat": 1}, {"version": "5e1c4c362065a6b95ff952c0eab010f04dcd2c3494e813b493ecfd4fcb9fc0d8", "impliedFormat": 1}, {"version": "68d73b4a11549f9c0b7d352d10e91e5dca8faa3322bfb77b661839c42b1ddec7", "impliedFormat": 1}, {"version": "5efce4fc3c29ea84e8928f97adec086e3dc876365e0982cc8479a07954a3efd4", "impliedFormat": 1}, {"version": "feecb1be483ed332fad555aff858affd90a48ab19ba7272ee084704eb7167569", "impliedFormat": 1}, {"version": "ee7bad0c15b58988daa84371e0b89d313b762ab83cb5b31b8a2d1162e8eb41c2", "impliedFormat": 1}, {"version": "c57796738e7f83dbc4b8e65132f11a377649c00dd3eee333f672b8f0a6bea671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "dc2df20b1bcdc8c2d34af4926e2c3ab15ffe1160a63e58b7e09833f616efff44", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "515d0b7b9bea2e31ea4ec968e9edd2c39d3eebf4a2d5cbd04e88639819ae3b71", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0559b1f683ac7505ae451f9a96ce4c3c92bdc71411651ca6ddb0e88baaaad6a3", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0dc1e7ceda9b8b9b455c3a2d67b0412feab00bd2f66656cd8850e8831b08b537", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ce691fb9e5c64efb9547083e4a34091bcbe5bdb41027e310ebba8f7d96a98671", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d697a2a929a5fcb38b7a65594020fcef05ec1630804a33748829c5ff53640d0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4ff2a353abf8a80ee399af572debb8faab2d33ad38c4b4474cff7f26e7653b8d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "fb0f136d372979348d59b3f5020b4cdb81b5504192b1cacff5d1fbba29378aa1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d15bea3d62cbbdb9797079416b8ac375ae99162a7fba5de2c6c505446486ac0a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "68d18b664c9d32a7336a70235958b8997ebc1c3b8505f4f1ae2b7e7753b87618", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb3d66c8327153d8fa7dd03f9c58d351107fe824c79e9b56b462935176cdf12a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "38f0219c9e23c915ef9790ab1d680440d95419ad264816fa15009a8851e79119", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "69ab18c3b76cd9b1be3d188eaf8bba06112ebbe2f47f6c322b5105a6fbc45a2e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "a680117f487a4d2f30ea46f1b4b7f58bef1480456e18ba53ee85c2746eeca012", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "2f11ff796926e0832f9ae148008138ad583bd181899ab7dd768a2666700b1893", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4de680d5bb41c17f7f68e0419412ca23c98d5749dcaaea1896172f06435891fc", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "954296b30da6d508a104a3a0b5d96b76495c709785c1d11610908e63481ee667", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ac9538681b19688c8eae65811b329d3744af679e0bdfa5d842d0e32524c73e1c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a969edff4bd52585473d24995c5ef223f6652d6ef46193309b3921d65dd4376", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e9fbd7030c440b33d021da145d3232984c8bb7916f277e8ffd3dc2e3eae2bdb", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "811ec78f7fefcabbda4bfa93b3eb67d9ae166ef95f9bff989d964061cbf81a0c", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "717937616a17072082152a2ef351cb51f98802fb4b2fdabd32399843875974ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d7e7d9b7b50e5f22c915b525acc5a49a7a6584cf8f62d0569e557c5cfc4b2ac2", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "71c37f4c9543f31dfced6c7840e068c5a5aacb7b89111a4364b1d5276b852557", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "576711e016cf4f1804676043e6a0a5414252560eb57de9faceee34d79798c850", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "89c1b1281ba7b8a96efc676b11b264de7a8374c5ea1e6617f11880a13fc56dc6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "74f7fa2d027d5b33eb0471c8e82a6c87216223181ec31247c357a3e8e2fddc5b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d6d7ae4d1f1f3772e2a3cde568ed08991a8ae34a080ff1151af28b7f798e22ca", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "063600664504610fe3e99b717a1223f8b1900087fab0b4cad1496a114744f8df", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "934019d7e3c81950f9a8426d093458b65d5aff2c7c1511233c0fd5b941e608ab", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "52ada8e0b6e0482b728070b7639ee42e83a9b1c22d205992756fe020fd9f4a47", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3bdefe1bfd4d6dee0e26f928f93ccc128f1b64d5d501ff4a8cf3c6371200e5e6", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "59fb2c069260b4ba00b5643b907ef5d5341b167e7d1dbf58dfd895658bda2867", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "639e512c0dfc3fad96a84caad71b8834d66329a1f28dc95e3946c9b58176c73a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "368af93f74c9c932edd84c58883e736c9e3d53cec1fe24c0b0ff451f529ceab1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "af3dd424cf267428f30ccfc376f47a2c0114546b55c44d8c0f1d57d841e28d74", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "995c005ab91a498455ea8dfb63aa9f83fa2ea793c3d8aa344be4a1678d06d399", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "959d36cddf5e7d572a65045b876f2956c973a586da58e5d26cde519184fd9b8a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "965f36eae237dd74e6cca203a43e9ca801ce38824ead814728a2807b1910117d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3925a6c820dcb1a06506c90b1577db1fdbf7705d65b62b99dce4be75c637e26b", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0a3d63ef2b853447ec4f749d3f368ce642264246e02911fcb1590d8c161b8005", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8cdf8847677ac7d20486e54dd3fcf09eda95812ac8ace44b4418da1bbbab6eb8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8444af78980e3b20b49324f4a16ba35024fef3ee069a0eb67616ea6ca821c47a", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "3287d9d085fbd618c3971944b65b4be57859f5415f495b33a6adc994edd2f004", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b4b67b1a91182421f5df999988c690f14d813b9850b40acd06ed44691f6727ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e7f8264d0fb4c5339605a15daadb037bf238c10b654bb3eee14208f860a32ea", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "782dec38049b92d4e85c1585fbea5474a219c6984a35b004963b00beb1aab538", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "70521b6ab0dcba37539e5303104f29b721bfb2940b2776da4cc818c07e1fefc1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "ab41ef1f2cdafb8df48be20cd969d875602483859dc194e9c97c8a576892c052", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d153a11543fd884b596587ccd97aebbeed950b26933ee000f94009f1ab142848", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "21d819c173c0cf7cc3ce57c3276e77fd9a8a01d35a06ad87158781515c9a438a", "impliedFormat": 1}, {"version": "a79e62f1e20467e11a904399b8b18b18c0c6eea6b50c1168bf215356d5bebfaf", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "49a5a44f2e68241a1d2bd9ec894535797998841c09729e506a7cbfcaa40f2180", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8e9c23ba78aabc2e0a27033f18737a6df754067731e69dc5f52823957d60a4b6", "impliedFormat": 1}, {"version": "5929864ce17fba74232584d90cb721a89b7ad277220627cc97054ba15a98ea8f", "impliedFormat": 1}, {"version": "763fe0f42b3d79b440a9b6e51e9ba3f3f91352469c1e4b3b67bfa4ff6352f3f4", "impliedFormat": 1}, {"version": "25c8056edf4314820382a5fdb4bb7816999acdcb929c8f75e3f39473b87e85bc", "impliedFormat": 1}, {"version": "c464d66b20788266e5353b48dc4aa6bc0dc4a707276df1e7152ab0c9ae21fad8", "impliedFormat": 1}, {"version": "78d0d27c130d35c60b5e5566c9f1e5be77caf39804636bc1a40133919a949f21", "impliedFormat": 1}, {"version": "c6fd2c5a395f2432786c9cb8deb870b9b0e8ff7e22c029954fabdd692bff6195", "impliedFormat": 1}, {"version": "1d6e127068ea8e104a912e42fc0a110e2aa5a66a356a917a163e8cf9a65e4a75", "impliedFormat": 1}, {"version": "5ded6427296cdf3b9542de4471d2aa8d3983671d4cac0f4bf9c637208d1ced43", "impliedFormat": 1}, {"version": "7f182617db458e98fc18dfb272d40aa2fff3a353c44a89b2c0ccb3937709bfb5", "impliedFormat": 1}, {"version": "cadc8aced301244057c4e7e73fbcae534b0f5b12a37b150d80e5a45aa4bebcbd", "impliedFormat": 1}, {"version": "385aab901643aa54e1c36f5ef3107913b10d1b5bb8cbcd933d4263b80a0d7f20", "impliedFormat": 1}, {"version": "9670d44354bab9d9982eca21945686b5c24a3f893db73c0dae0fd74217a4c219", "impliedFormat": 1}, {"version": "0b8a9268adaf4da35e7fa830c8981cfa22adbbe5b3f6f5ab91f6658899e657a7", "impliedFormat": 1}, {"version": "11396ed8a44c02ab9798b7dca436009f866e8dae3c9c25e8c1fbc396880bf1bb", "impliedFormat": 1}, {"version": "ba7bc87d01492633cb5a0e5da8a4a42a1c86270e7b3d2dea5d156828a84e4882", "impliedFormat": 1}, {"version": "4893a895ea92c85345017a04ed427cbd6a1710453338df26881a6019432febdd", "impliedFormat": 1}, {"version": "c21dc52e277bcfc75fac0436ccb75c204f9e1b3fa5e12729670910639f27343e", "impliedFormat": 1}, {"version": "13f6f39e12b1518c6650bbb220c8985999020fe0f21d818e28f512b7771d00f9", "impliedFormat": 1}, {"version": "9b5369969f6e7175740bf51223112ff209f94ba43ecd3bb09eefff9fd675624a", "impliedFormat": 1}, {"version": "4fe9e626e7164748e8769bbf74b538e09607f07ed17c2f20af8d680ee49fc1da", "impliedFormat": 1}, {"version": "24515859bc0b836719105bb6cc3d68255042a9f02a6022b3187948b204946bd2", "impliedFormat": 1}, {"version": "ea0148f897b45a76544ae179784c95af1bd6721b8610af9ffa467a518a086a43", "impliedFormat": 1}, {"version": "24c6a117721e606c9984335f71711877293a9651e44f59f3d21c1ea0856f9cc9", "impliedFormat": 1}, {"version": "dd3273ead9fbde62a72949c97dbec2247ea08e0c6952e701a483d74ef92d6a17", "impliedFormat": 1}, {"version": "405822be75ad3e4d162e07439bac80c6bcc6dbae1929e179cf467ec0b9ee4e2e", "impliedFormat": 1}, {"version": "0db18c6e78ea846316c012478888f33c11ffadab9efd1cc8bcc12daded7a60b6", "impliedFormat": 1}, {"version": "e61be3f894b41b7baa1fbd6a66893f2579bfad01d208b4ff61daef21493ef0a8", "impliedFormat": 1}, {"version": "bd0532fd6556073727d28da0edfd1736417a3f9f394877b6d5ef6ad88fba1d1a", "impliedFormat": 1}, {"version": "89167d696a849fce5ca508032aabfe901c0868f833a8625d5a9c6e861ef935d2", "impliedFormat": 1}, {"version": "615ba88d0128ed16bf83ef8ccbb6aff05c3ee2db1cc0f89ab50a4939bfc1943f", "impliedFormat": 1}, {"version": "a4d551dbf8746780194d550c88f26cf937caf8d56f102969a110cfaed4b06656", "impliedFormat": 1}, {"version": "8bd86b8e8f6a6aa6c49b71e14c4ffe1211a0e97c80f08d2c8cc98838006e4b88", "impliedFormat": 1}, {"version": "317e63deeb21ac07f3992f5b50cdca8338f10acd4fbb7257ebf56735bf52ab00", "impliedFormat": 1}, {"version": "4732aec92b20fb28c5fe9ad99521fb59974289ed1e45aecb282616202184064f", "impliedFormat": 1}, {"version": "2e85db9e6fd73cfa3d7f28e0ab6b55417ea18931423bd47b409a96e4a169e8e6", "impliedFormat": 1}, {"version": "c46e079fe54c76f95c67fb89081b3e399da2c7d109e7dca8e4b58d83e332e605", "impliedFormat": 1}, {"version": "bf67d53d168abc1298888693338cb82854bdb2e69ef83f8a0092093c2d562107", "impliedFormat": 1}, {"version": "1ca84b44ad1d8e4576f24904d8b95dd23b94ea67e1575f89614ac90062fc67f4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "6d586db0a09a9495ebb5dece28f54df9684bfbd6e1f568426ca153126dac4a40", "impliedFormat": 1}, {"version": "7394959e5a741b185456e1ef5d64599c36c60a323207450991e7a42e08911419", "impliedFormat": 1}, {"version": "8c0bcd6c6b67b4b503c11e91a1fb91522ed585900eab2ab1f61bba7d7caa9d6f", "impliedFormat": 1}, {"version": "567b7f607f400873151d7bc63a049514b53c3c00f5f56e9e95695d93b66a138e", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "823f9c08700a30e2920a063891df4e357c64333fdba6889522acc5b7ae13fc08", "impliedFormat": 1}, {"version": "84c1930e33d1bb12ad01bcbe11d656f9646bd21b2fb2afd96e8e10615a021aef", "impliedFormat": 1}, {"version": "35ec8b6760fd7138bbf5809b84551e31028fb2ba7b6dc91d95d098bf212ca8b4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5524481e56c48ff486f42926778c0a3cce1cc85dc46683b92b1271865bcf015a", "impliedFormat": 1}, {"version": "4b87f767c7bc841511113c876a6b8bf1fd0cb0b718c888ad84478b372ec486b1", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8d04e3640dd9eb67f7f1e5bd3d0bf96c784666f7aefc8ac1537af6f2d38d4c29", "impliedFormat": 1}, {"version": "9d19808c8c291a9010a6c788e8532a2da70f811adb431c97520803e0ec649991", "impliedFormat": 1}, {"version": "2bf469abae4cc9c0f340d4e05d9d26e37f936f9c8ca8f007a6534f109dcc77e4", "impliedFormat": 1}, {"version": "4aacb0dd020eeaef65426153686cc639a78ec2885dc72ad220be1d25f1a439df", "impliedFormat": 1}, {"version": "f0bd7e6d931657b59605c44112eaf8b980ba7f957a5051ed21cb93d978cf2f45", "impliedFormat": 1}, {"version": "71450bbc2d82821d24ca05699a533e72758964e9852062c53b30f31c36978ab8", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "0ada07543808f3b967624645a8e1ccd446f8b01ade47842acf1328aec899fed0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "4c21aaa8257d7950a5b75a251d9075b6a371208fc948c9c8402f6690ef3b5b55", "impliedFormat": 1}, {"version": "b5895e6353a5d708f55d8685c38a235c3a6d8138e374dee8ceb8ffde5aa8002a", "impliedFormat": 1}, {"version": "54c4f21f578864961efc94e8f42bc893a53509e886370ec7dd602e0151b9266c", "impliedFormat": 1}, {"version": "de735eca2c51dd8b860254e9fdb6d9ec19fe402dfe597c23090841ce3937cfc5", "impliedFormat": 1}, {"version": "4ff41188773cbf465807dd2f7059c7494cbee5115608efc297383832a1150c43", "impliedFormat": 1}, {"version": "5650cf3dace09e7c25d384e3e6b818b938f68f4e8de96f52d9c5a1b3db068e86", "impliedFormat": 1}, {"version": "1354ca5c38bd3fd3836a68e0f7c9f91f172582ba30ab15bb8c075891b91502b7", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "5155da3047ef977944d791a2188ff6e6c225f6975cc1910ab7bb6838ab84cede", "impliedFormat": 1}, {"version": "93f437e1398a4f06a984f441f7fa7a9f0535c04399619b5c22e0b87bdee182cb", "impliedFormat": 1}, {"version": "afbe24ab0d74694372baa632ecb28bb375be53f3be53f9b07ecd7fc994907de5", "impliedFormat": 1}, {"version": "e16d218a30f6a6810b57f7e968124eaa08c7bb366133ea34bbf01e7cd6b8c0ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "eb8692dea24c27821f77e397272d9ed2eda0b95e4a75beb0fdda31081d15a8ae", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "9e043a1bc8fbf2a255bccf9bf27e0f1caf916c3b0518ea34aa72357c0afd42ec", "impliedFormat": 1}, {"version": "b4f70ec656a11d570e1a9edce07d118cd58d9760239e2ece99306ee9dfe61d02", "impliedFormat": 1}, {"version": "3bc2f1e2c95c04048212c569ed38e338873f6a8593930cf5a7ef24ffb38fc3b6", "impliedFormat": 1}, {"version": "8145e07aad6da5f23f2fcd8c8e4c5c13fb26ee986a79d03b0829b8fce152d8b2", "impliedFormat": 1}, {"version": "f9d9d753d430ed050dc1bf2667a1bab711ccbb1c1507183d794cc195a5b085cc", "impliedFormat": 1}, {"version": "9eece5e586312581ccd106d4853e861aaaa1a39f8e3ea672b8c3847eedd12f6e", "impliedFormat": 1}, {"version": "5b6844ad931dcc1d3aca53268f4bd671428421464b1286746027aede398094f2", "impliedFormat": 1}, {"version": "37ba7b45141a45ce6e80e66f2a96c8a5ab1bcef0fc2d0f56bb58df96ec67e972", "impliedFormat": 1}, {"version": "125d792ec6c0c0f657d758055c494301cc5fdb327d9d9d5960b3f129aff76093", "impliedFormat": 1}, {"version": "0225ecb9ed86bdb7a2c7fd01f1556906902929377b44483dc4b83e03b3ef227d", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "1851a3b4db78664f83901bb9cac9e45e03a37bb5933cc5bf37e10bb7e91ab4eb", "impliedFormat": 1}, {"version": "461e54289e6287e8494a0178ba18182acce51a02bca8dea219149bf2cf96f105", "impliedFormat": 1}, {"version": "12ed4559eba17cd977aa0db658d25c4047067444b51acfdcbf38470630642b23", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "f3ffabc95802521e1e4bcba4c88d8615176dc6e09111d920c7a213bdda6e1d65", "impliedFormat": 1}, {"version": "e31e51c55800014d926e3f74208af49cb7352803619855c89296074d1ecbb524", "impliedFormat": 1}, {"version": "ae56f65caf3be91108707bd8dfbccc2a57a91feb5daabf7165a06a945545ed26", "impliedFormat": 1}, {"version": "a136d5de521da20f31631a0a96bf712370779d1c05b7015d7019a9b2a0446ca9", "impliedFormat": 1}, {"version": "dfb96ba5177b68003deec9e773c47257da5c4c8a74053d8956389d832df72002", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "92d3070580cf72b4bb80959b7f16ede9a3f39e6f4ef2ac87cfa4561844fdc69f", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d3dffd70e6375b872f0b4e152de4ae682d762c61a24881ecc5eb9f04c5caf76f", "impliedFormat": 1}, {"version": "613deebaec53731ff6b74fe1a89f094b708033db6396b601df3e6d5ab0ec0a47", "impliedFormat": 1}, {"version": "d91a7d8b5655c42986f1bdfe2105c4408f472831c8f20cf11a8c3345b6b56c8c", "impliedFormat": 1}, {"version": "e56eb632f0281c9f8210eb8c86cc4839a427a4ffffcfd2a5e40b956050b3e042", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "e8a979b8af001c9fc2e774e7809d233c8ca955a28756f52ee5dee88ccb0611d2", "impliedFormat": 1}, {"version": "cac793cc47c29e26e4ac3601dcb00b4435ebed26203485790e44f2ad8b6ad847", "impliedFormat": 1}, {"version": "1ba59c8bbeed2cb75b239bb12041582fa3e8ef32f8d0bd0ec802e38442d3f317", "impliedFormat": 1}, "848fc665c43e067612227cfb87ffed876ae96d85ee87639977be280b2a4482f5", "bb095d8e262ba8561390d15c41428467d198b7429d5d474978b42e3d5cdfd312", "3225a95d3eb37bd0d34433a3b571ecec09816ebf000cb15b1bbff6f880f88e0d", "372360ef1cbeb0fdae07b7f8d3acadaaa9aa57f57c093c5b3d3eaee8c2774ff4", {"version": "e6cfcf171b5f7ec0cb620eee4669739ad2711597d0ff7fdb79298dfc1118e66a", "impliedFormat": 1}, "bb34e958cfdac3926006188413eb165024f608d267301dbbec9e6f52287fe1da", "8823bfd30fa3d708fdb6d41c9bf32ddd40c80baae9e3e962dcb2e320606b65c8", "693f2978a742c270d805091d5cea94953e132c184887b2d7f18a057b2d1962ca", {"version": "1d7ee0c4eb734d59b6d962bc9151f6330895067cd3058ce6a3cd95347ef5c6e8", "impliedFormat": 99}, {"version": "d0b06287940d6c4fb039c27849e9eaf9e21f359ae3b1a5ff254bcb9b48570110", "signature": "e32b8a43e092e33341ba494b2eaf18991492ccc19519ff78d830d8f8204ad79a"}, "0a16b8fc675b26abca80de8089bad17ce37ce3d4db51d88b1bfd1eeacbfacb47", {"version": "3deed5e2a5f1e7590d44e65a5b61900158a3c38bac9048462d38b1bc8098bb2e", "impliedFormat": 99}, {"version": "d435a43f89ed8794744c59d72ce71e43c1953338303f6be9ef99086faa8591d7", "impliedFormat": 99}, {"version": "c085e9aa62d1ae1375794c1fb927a445fa105fed891a7e24edbb1c3300f7384a", "impliedFormat": 1}, {"version": "f315e1e65a1f80992f0509e84e4ae2df15ecd9ef73df975f7c98813b71e4c8da", "impliedFormat": 1}, {"version": "5b9586e9b0b6322e5bfbd2c29bd3b8e21ab9d871f82346cb71020e3d84bae73e", "impliedFormat": 1}, {"version": "a4f64e674903a21e1594a24c3fc8583f3a587336d17d41ade46aa177a8ab889b", "impliedFormat": 99}, {"version": "b6f69984ffcd00a7cbcef9c931b815e8872c792ed85d9213cb2e2c14c50ca63a", "impliedFormat": 99}, {"version": "2bbc5abe5030aa07a97aabd6d3932ed2e8b7a241cf3923f9f9bf91a0addbe41f", "impliedFormat": 99}, {"version": "1e5e5592594e16bcf9544c065656293374120eb8e78780fb6c582cc710f6db11", "impliedFormat": 99}, {"version": "05c7aef6a4e496b93c2e682cced8903c0dfe6340d04f3fe616176e2782193435", "impliedFormat": 99}, {"version": "4abf1e884eecb0bf742510d69d064e33d53ac507991d6c573958356f920c3de4", "impliedFormat": 99}, {"version": "44f1d2dd522c849ca98c4f95b8b2bc84b64408d654f75eb17ec78b8ceb84da11", "impliedFormat": 99}, {"version": "500a67e158e4025f27570ab6a99831680852bb45a44d4c3647ab7567feb1fb4c", "impliedFormat": 99}, {"version": "89edc5e1739692904fdf69edcff9e1023d2213e90372ec425b2f17e3aecbaa4a", "impliedFormat": 99}, {"version": "e7d5bcffc98eded65d620bc0b6707c307b79c21d97a5fb8601e8bdf2296026b6", "impliedFormat": 99}, {"version": "e666e31d323fef5642f87db0da48a83e58f0aaf9e3823e87eabd8ec7e0441a36", "impliedFormat": 99}, {"version": "151ff381ef9ff8da2da9b9663ebf657eac35c4c9a19183420c05728f31a6761d", "impliedFormat": 1}, {"version": "ffb518fc55181aefd066c690dbc0f8fa6a1533c8ddac595469c8c5f7fda2d756", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "282f98006ed7fa9bb2cd9bdbe2524595cfc4bcd58a0bb3232e4519f2138df811", "impliedFormat": 1}, {"version": "6222e987b58abfe92597e1273ad7233626285bc2d78409d4a7b113d81a83496b", "impliedFormat": 1}, {"version": "cbe726263ae9a7bf32352380f7e8ab66ee25b3457137e316929269c19e18a2be", "impliedFormat": 1}, {"version": "8b96046bf5fb0a815cba6b0880d9f97b7f3a93cf187e8dcfe8e2792e97f38f87", "impliedFormat": 99}, {"version": "bacf2c84cf448b2cd02c717ad46c3d7fd530e0c91282888c923ad64810a4d511", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "402e5c534fb2b85fa771170595db3ac0dd532112c8fa44fc23f233bc6967488b", "impliedFormat": 1}, {"version": "8885cf05f3e2abf117590bbb951dcf6359e3e5ac462af1c901cfd24c6a6472e2", "impliedFormat": 1}, {"version": "333caa2bfff7f06017f114de738050dd99a765c7eb16571c6d25a38c0d5365dc", "impliedFormat": 1}, {"version": "e61df3640a38d535fd4bc9f4a53aef17c296b58dc4b6394fd576b808dd2fe5e6", "impliedFormat": 1}, {"version": "459920181700cec8cbdf2a5faca127f3f17fd8dd9d9e577ed3f5f3af5d12a2e4", "impliedFormat": 1}, {"version": "4719c209b9c00b579553859407a7e5dcfaa1c472994bd62aa5dd3cc0757eb077", "impliedFormat": 1}, {"version": "7ec359bbc29b69d4063fe7dad0baaf35f1856f914db16b3f4f6e3e1bca4099fa", "impliedFormat": 1}, {"version": "70790a7f0040993ca66ab8a07a059a0f8256e7bb57d968ae945f696cbff4ac7a", "impliedFormat": 1}, {"version": "d1b9a81e99a0050ca7f2d98d7eedc6cda768f0eb9fa90b602e7107433e64c04c", "impliedFormat": 1}, {"version": "a022503e75d6953d0e82c2c564508a5c7f8556fad5d7f971372d2d40479e4034", "impliedFormat": 1}, {"version": "b215c4f0096f108020f666ffcc1f072c81e9f2f95464e894a5d5f34c5ea2a8b1", "impliedFormat": 1}, {"version": "644491cde678bd462bb922c1d0cfab8f17d626b195ccb7f008612dc31f445d2d", "impliedFormat": 1}, {"version": "dfe54dab1fa4961a6bcfba68c4ca955f8b5bbeb5f2ab3c915aa7adaa2eabc03a", "impliedFormat": 1}, {"version": "1251d53755b03cde02466064260bb88fd83c30006a46395b7d9167340bc59b73", "impliedFormat": 1}, {"version": "47865c5e695a382a916b1eedda1b6523145426e48a2eae4647e96b3b5e52024f", "impliedFormat": 1}, {"version": "4cdf27e29feae6c7826cdd5c91751cc35559125e8304f9e7aed8faef97dcf572", "impliedFormat": 1}, {"version": "331b8f71bfae1df25d564f5ea9ee65a0d847c4a94baa45925b6f38c55c7039bf", "impliedFormat": 1}, {"version": "2a771d907aebf9391ac1f50e4ad37952943515eeea0dcc7e78aa08f508294668", "impliedFormat": 1}, {"version": "0146fd6262c3fd3da51cb0254bb6b9a4e42931eb2f56329edd4c199cb9aaf804", "impliedFormat": 1}, {"version": "82e687ebd99518bc63ea04b0c3810fb6e50aa6942decd0ca6f7a56d9b9a212a6", "impliedFormat": 99}, {"version": "7f698624bbbb060ece7c0e51b7236520ebada74b747d7523c7df376453ed6fea", "impliedFormat": 1}, {"version": "8f07f2b6514744ac96e51d7cb8518c0f4de319471237ea10cf688b8d0e9d0225", "impliedFormat": 1}, {"version": "257b83faa134d971c738a6b9e4c47e59bb7b23274719d92197580dd662bfafc3", "impliedFormat": 99}, {"version": "4a27c79c57a6692abb196711f82b8b07a27908c94652148d5469887836390116", "impliedFormat": 99}, {"version": "f42400484f181c2c2d7557c0ed3b8baaace644a9e943511f3d35ac6be6eb5257", "impliedFormat": 99}, {"version": "54b381d36b35df872159a8d3b52e8d852659ee805695a867a388c8ccbf57521b", "impliedFormat": 99}, {"version": "c67b4c864ec9dcde25f7ad51b90ae9fe1f6af214dbd063d15db81194fe652223", "impliedFormat": 99}, {"version": "7a4aa00aaf2160278aeae3cf0d2fc6820cf22b86374efa7a00780fbb965923ff", "impliedFormat": 99}, {"version": "66e3ee0a655ff3698be0aef05f7b76ac34c349873e073cde46d43db795b79f04", "impliedFormat": 99}, {"version": "48c411efce1848d1ed55de41d7deb93cbf7c04080912fd87aa517ed25ef42639", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "28e065b6fb60a04a538b5fbf8c003d7dac3ae9a49eddc357c2a14f2ffe9b3185", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "fe2d63fcfdde197391b6b70daf7be8c02a60afa90754a5f4a04bdc367f62793d", "impliedFormat": 99}, {"version": "69bf2422313487956e4dacf049f30cb91b34968912058d244cb19e4baa24da97", "impliedFormat": 1}, {"version": "0d87708dafcde5468a130dfe64fac05ecad8328c298a4f0f2bd86603e5fd002e", "impliedFormat": 99}, {"version": "a3f2554ba6726d0da0ffdc15b675b8b3de4aea543deebbbead845680b740a7fd", "impliedFormat": 99}, {"version": "b7e28e06011460436d5c2ec2996846ac0c451e135357fc5a7269e5665a32fbd7", "impliedFormat": 99}, {"version": "257b83faa134d971c738a6b9e4c47e59bb7b23274719d92197580dd662bfafc3", "impliedFormat": 99}, {"version": "93dda0982b139b27b85dd2924d23e07ee8b4ca36a10be7bdf361163e4ffcc033", "impliedFormat": 99}, {"version": "427fe2004642504828c1476d0af4270e6ad4db6de78c0b5da3e4c5ca95052a99", "impliedFormat": 1}, {"version": "c8905dbea83f3220676a669366cd8c1acef56af4d9d72a8b2241b1d044bb4302", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "d7b652822e2a387fd2bcf0b78bcf2b7a9a9e73c4a71c12c5d0bbbb367aea6a87", "affectsGlobalScope": true, "impliedFormat": 99}, {"version": "cb80558784fc93165b64809b3ba66266d10585d838709ebf5e4576f63f9f2929", "impliedFormat": 99}, {"version": "dfa6bb848807bc5e01e84214d4ec13ee8ffe5e1142546dcbb32065783a5db468", "impliedFormat": 99}, {"version": "2f1ffc29f9ba7b005c0c48e6389536a245837264c99041669e0b768cfab6711d", "impliedFormat": 99}, {"version": "f2d1a59a658165341b0e2b7879aa2e19ea6a709146b2d3f70ee8a07159d3d08e", "impliedFormat": 99}, {"version": "b4270f889835e50044bf80e479fef2482edd69daf4b168f9e3ee34cf817ae41a", "impliedFormat": 99}, "55468ca85078e9d64778045e137cb92b75cc38315bb81f487aa973d22a2afe26", "df0e29b24966f4dc4ca04d70fbfa50ba30dfbc1189a47950781ec77d850118e8", "61dc526f82695fd3bd892505afa64e9f09b6c89a87b9ecdf6e878423b34dbf2b", {"version": "d543ef9e02017edb0232f1b104d1054771b343294bdd181d3be1421bb8ba280d", "signature": "514780f66af865d746db8e15391fccd445d84fd1a9847b3fdfff405df0db941a"}, "411e916a06a79c3f57653f8770f22f21b3a755c5a5bbd77743baacdd6070b5dc", "4ebaa3af6522043f3b439cfe47a07adf4335e655c0e7e83e618102c049f9dc1c", "49c564561747fc554ab20a36c1b5ccbf13af5e70794c59f2a24f4ef565a1928c", "68194991e89ee729e760a3c70748188e8fd5f45676427cdf0e5ec15977b76591", "395543a40f737d118782f0cc7e5cbcef2176b6e2175390c73886d9c0ee7bf246", {"version": "6892779bee9944f7d8a53117f9b49c9cc436a83ccdc3828e3e82f66d79cc59c7", "signature": "b3a112ec3b3b6e26c48113a4e4b8c94fb0216ece02a5cfa99d21ef798cb88035"}, "7d9e13e04e0fe0619e6c23d23b1d1c7871d0429c41337031468e609c737e08f2", {"version": "c32aba412471ea1947593a265702d2719953ff0bd13cba273fc7f6e5ed0c49f5", "signature": "922f8ee882083ecf505b30fc6607b5cd7206bf3b9834b78307821171fa83b775"}, "ba179a06d6bbf72158e04e94d5a6d307bf77ac8e3b29baf48941e3ad4e1bc7ad", "341f925433670e2fc5a68497eb1582bf12a17f3dc1b0079f1a0055890a97438f", {"version": "d7fd84297cde63a613fd82ce37d2ae4619fc99e17be91380f461f2e253a8f9e7", "signature": "1da2fdbbf734c48352a41e4a85bd305a6845598a2c1d346f662902d63267ae93"}, "4a268dfe684764a93b8f95592012c6a387f68b3e19e0d008f6bd3917004c8b45", {"version": "0719a2a62cece8bb3016e5c4286a128882ffcfa66e9acaa60855565e6a79c5b5", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, "8111d9b8e1d21cdc5c32eacabe9864a46dab7d96b1f33a759804f08751aff749", "066088b54db6343605d56cb3cb517ba33968ab377d9746a3de45a3bdf9aab9de", "4aebf262b601768169e1c2aff4fa7ea816d30bba43f90fbaca8b8ca0d6079f2e", "979d7b392d6f77ed2c749d5ff53ba27427f5319ac47bf2d9924cb9d2be56a9bd", "4d102c64a5240c17792c18e89677a012add1fc5b3ee024f84501a5f9b02bda95", {"version": "278a529b2123d385537a35fbebca728e4c746b38eae6ed5b0f7f9fb6c024763f", "signature": "4bd4552f61a5b761f2b36fd277b78484f764c0765582c76735daa82faae206d3"}, {"version": "ae77d81a5541a8abb938a0efedf9ac4bea36fb3a24cc28cfa11c598863aba571", "impliedFormat": 1}, {"version": "a3d3f704c5339a36da3ca8c62b29072f87e86c783b8452d235992142ec71aa2d", "impliedFormat": 1}, {"version": "c2a6a737189ced24ffe0634e9239b087e4c26378d0490f95141b9b9b042b746c", "impliedFormat": 1}, {"version": "104c67f0da1bdf0d94865419247e20eded83ce7f9911a1aa75fc675c077ca66e", "impliedFormat": 1}, {"version": "cc0d0b339f31ce0ab3b7a5b714d8e578ce698f1e13d7f8c60bfb766baeb1d35c", "impliedFormat": 1}, {"version": "1748c03e7a7d118f7f6648c709507971eb0d416f489958492c5ae625de445184", "impliedFormat": 1}, {"version": "0dc6940ff35d845686a118ee7384713a84024d60ef26f25a2f87992ec7ddbd64", "impliedFormat": 1}, {"version": "25be1eb939c9c63242c7a45446edb20c40541da967f43f1aa6a00ed53c0552db", "impliedFormat": 1}, {"version": "64ef412b8346db43b82d6ad96aa5edf342c635346e41185cd3796937df2b11ad", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "d3f2d715f57df3f04bf7b16dde01dec10366f64fce44503c92b8f78f614c1769", "impliedFormat": 1}, {"version": "b78cd10245a90e27e62d0558564f5d9a16576294eee724a59ae21b91f9269e4a", "impliedFormat": 1}, {"version": "baac9896d29bcc55391d769e408ff400d61273d832dd500f21de766205255acb", "impliedFormat": 1}, {"version": "2f5747b1508ccf83fad0c251ba1e5da2f5a30b78b09ffa1cfaf633045160afed", "impliedFormat": 1}, {"version": "a45c25e77c911c1f2a04cade78f6f42b4d7d896a3882d4e226efd3a3fcd5f2c4", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "b71c603a539078a5e3a039b20f2b0a0d1708967530cf97dec8850a9ca45baa2b", "impliedFormat": 1}, {"version": "0e13570a7e86c6d83dd92e81758a930f63747483e2cd34ef36fcdb47d1f9726a", "impliedFormat": 1}, {"version": "5c45abf1e13e4463eacfd5dedda06855da8748a6a6cb3334f582b52e219acc04", "impliedFormat": 1}, {"version": "035a5df183489c2e22f3cf59fc1ed2b043d27f357eecc0eb8d8e840059d44245", "impliedFormat": 1}, {"version": "f3d8c757e148ad968f0d98697987db363070abada5f503da3c06aefd9d4248c1", "impliedFormat": 1}, {"version": "96d14f21b7652903852eef49379d04dbda28c16ed36468f8c9fa08f7c14c9538", "impliedFormat": 1}, {"version": "fb893a0dfc3c9fb0f9ca93d0648694dd95f33cbad2c0f2c629f842981dfd4e2e", "impliedFormat": 1}, {"version": "95da3c365e3d45709ad6e0b4daa5cdaf05e9076ba3c201e8f8081dd282c02f57", "impliedFormat": 1}, {"version": "b0f9ef6423d6b29dde29fd60d83d215796b2c1b76bfca28ac374ae18702cfb8e", "impliedFormat": 1}, {"version": "f4ae5546352701fd6932fdd86419438bb51253e4627a44808489742035bac644", "impliedFormat": 1}, {"version": "87d9d29dbc745f182683f63187bf3d53fd8673e5fca38ad5eaab69798ed29fbc", "impliedFormat": 1}, {"version": "eb5b19b86227ace1d29ea4cf81387279d04bb34051e944bc53df69f58914b788", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "8a8eb4ebffd85e589a1cc7c178e291626c359543403d58c9cd22b81fab5b1fb9", "impliedFormat": 1}, {"version": "472f5aab7edc498a0a761096e8e254c5bc3323d07a1e7f5f8b8ec0d6395b60a0", "affectsGlobalScope": true, "impliedFormat": 1}, {"version": "17ed71200119e86ccef2d96b73b02ce8854b76ad6bd21b5021d4269bec527b5f", "impliedFormat": 1}, {"version": "cf3d384d082b933d987c4e2fe7bfb8710adfd9dc8155190056ed6695a25a559e", "impliedFormat": 1}, {"version": "9871b7ee672bc16c78833bdab3052615834b08375cb144e4d2cba74473f4a589", "impliedFormat": 1}, {"version": "c863198dae89420f3c552b5a03da6ed6d0acfa3807a64772b895db624b0de707", "impliedFormat": 1}, {"version": "8b03a5e327d7db67112ebbc93b4f744133eda2c1743dbb0a990c61a8007823ef", "impliedFormat": 1}, {"version": "86c73f2ee1752bac8eeeece234fd05dfcf0637a4fbd8032e4f5f43102faa8eec", "impliedFormat": 1}, {"version": "42fad1f540271e35ca37cecda12c4ce2eef27f0f5cf0f8dd761d723c744d3159", "impliedFormat": 1}, {"version": "ff3743a5de32bee10906aff63d1de726f6a7fd6ee2da4b8229054dfa69de2c34", "impliedFormat": 1}, {"version": "83acd370f7f84f203e71ebba33ba61b7f1291ca027d7f9a662c6307d74e4ac22", "impliedFormat": 1}, {"version": "1445cec898f90bdd18b2949b9590b3c012f5b7e1804e6e329fb0fe053946d5ec", "impliedFormat": 1}, {"version": "0e5318ec2275d8da858b541920d9306650ae6ac8012f0e872fe66eb50321a669", "impliedFormat": 1}, {"version": "cf530297c3fb3a92ec9591dd4fa229d58b5981e45fe6702a0bd2bea53a5e59be", "impliedFormat": 1}, {"version": "c1f6f7d08d42148ddfe164d36d7aba91f467dbcb3caa715966ff95f55048b3a4", "impliedFormat": 1}, {"version": "f4e9bf9103191ef3b3612d3ec0044ca4044ca5be27711fe648ada06fad4bcc85", "impliedFormat": 1}, {"version": "0c1ee27b8f6a00097c2d6d91a21ee4d096ab52c1e28350f6362542b55380059a", "impliedFormat": 1}, {"version": "7677d5b0db9e020d3017720f853ba18f415219fb3a9597343b1b1012cfd699f7", "impliedFormat": 1}, {"version": "bc1c6bc119c1784b1a2be6d9c47addec0d83ef0d52c8fbe1f14a51b4dfffc675", "impliedFormat": 1}, {"version": "52cf2ce99c2a23de70225e252e9822a22b4e0adb82643ab0b710858810e00bf1", "impliedFormat": 1}, {"version": "770625067bb27a20b9826255a8d47b6b5b0a2d3dfcbd21f89904c731f671ba77", "impliedFormat": 1}, {"version": "d1ed6765f4d7906a05968fb5cd6d1db8afa14dbe512a4884e8ea5c0f5e142c80", "impliedFormat": 1}, {"version": "799c0f1b07c092626cf1efd71d459997635911bb5f7fc1196efe449bba87e965", "impliedFormat": 1}, {"version": "2a184e4462b9914a30b1b5c41cf80c6d3428f17b20d3afb711fff3f0644001fd", "impliedFormat": 1}, {"version": "9eabde32a3aa5d80de34af2c2206cdc3ee094c6504a8d0c2d6d20c7c179503cc", "impliedFormat": 1}, {"version": "397c8051b6cfcb48aa22656f0faca2553c5f56187262135162ee79d2b2f6c966", "impliedFormat": 1}, {"version": "a8ead142e0c87dcd5dc130eba1f8eeed506b08952d905c47621dc2f583b1bff9", "impliedFormat": 1}, {"version": "a02f10ea5f73130efca046429254a4e3c06b5475baecc8f7b99a0014731be8b3", "impliedFormat": 1}, {"version": "c2576a4083232b0e2d9bd06875dd43d371dee2e090325a9eac0133fd5650c1cb", "impliedFormat": 1}, {"version": "4c9a0564bb317349de6a24eb4efea8bb79898fa72ad63a1809165f5bd42970dd", "impliedFormat": 1}, {"version": "f40ac11d8859092d20f953aae14ba967282c3bb056431a37fced1866ec7a2681", "impliedFormat": 1}, {"version": "cc11e9e79d4746cc59e0e17473a59d6f104692fd0eeea1bdb2e206eabed83b03", "impliedFormat": 1}, {"version": "b444a410d34fb5e98aa5ee2b381362044f4884652e8bc8a11c8fe14bbd85518e", "impliedFormat": 1}, {"version": "c35808c1f5e16d2c571aa65067e3cb95afeff843b259ecfa2fc107a9519b5392", "impliedFormat": 1}, {"version": "14d5dc055143e941c8743c6a21fa459f961cbc3deedf1bfe47b11587ca4b3ef5", "impliedFormat": 1}, {"version": "a3ad4e1fc542751005267d50a6298e6765928c0c3a8dce1572f2ba6ca518661c", "impliedFormat": 1}, {"version": "f237e7c97a3a89f4591afd49ecb3bd8d14f51a1c4adc8fcae3430febedff5eb6", "impliedFormat": 1}, {"version": "3ffdfbec93b7aed71082af62b8c3e0cc71261cc68d796665faa1e91604fbae8f", "impliedFormat": 1}, {"version": "662201f943ed45b1ad600d03a90dffe20841e725203ced8b708c91fcd7f9379a", "impliedFormat": 1}, {"version": "c9ef74c64ed051ea5b958621e7fb853fe3b56e8787c1587aefc6ea988b3c7e79", "impliedFormat": 1}, {"version": "2462ccfac5f3375794b861abaa81da380f1bbd9401de59ffa43119a0b644253d", "impliedFormat": 1}, {"version": "34baf65cfee92f110d6653322e2120c2d368ee64b3c7981dff08ed105c4f19b0", "impliedFormat": 1}, {"version": "844ab83672160ca57a2a2ea46da4c64200d8c18d4ebb2087819649cad099ff0e", "impliedFormat": 1}, {"version": "1f4ae755492a669b317903a6b1664cb7af3fe0c3d1eec6447f4e95a80616d15a", "impliedFormat": 1}, {"version": "e7bb49fac2aa46a13011b5eb5e4a8648f70a28aea1853fab2444dd4fcb4d4ec7", "impliedFormat": 1}, {"version": "464e45d1a56dae066d7e1a2f32e55b8de4bfb072610c3483a4091d73c9924908", "impliedFormat": 1}, {"version": "da318e126ac39362c899829547cc8ee24fa3e8328b52cdd27e34173cf19c7941", "impliedFormat": 1}, {"version": "24bd01a91f187b22456c7171c07dbf44f3ad57ebd50735aab5c13fa23d7114b4", "impliedFormat": 1}, {"version": "4738eefeaaba4d4288a08c1c226a76086095a4d5bcc7826d2564e7c29da47671", "impliedFormat": 1}, {"version": "736097ddbb2903bef918bb3b5811ef1c9c5656f2a73bd39b22a91b9cc2525e50", "impliedFormat": 1}, {"version": "dbec715e9e82df297e49e3ed0029f6151aa40517ebfd6fcdba277a8a2e1d3a1b", "impliedFormat": 1}, {"version": "097f1f8ca02e8940cfdcca553279e281f726485fa6fb214b3c9f7084476f6bcc", "impliedFormat": 1}, {"version": "8f75e211a2e83ff216eb66330790fb6412dcda2feb60c4f165c903cf375633ee", "impliedFormat": 1}, {"version": "c3fb0d969970b37d91f0dbf493c014497fe457a2280ac42ae24567015963dbf7", "impliedFormat": 1}, {"version": "a9155c6deffc2f6a69e69dc12f0950ba1b4db03b3d26ab7a523efc89149ce979", "impliedFormat": 1}, {"version": "c99faf0d7cb755b0424a743ea0cbf195606bf6cd023b5d10082dba8d3714673c", "impliedFormat": 1}, {"version": "21942c5a654cc18ffc2e1e063c8328aca3b127bbf259c4e97906d4696e3fa915", "impliedFormat": 1}, {"version": "03c258e060b7da220973f84b89615e4e9850e9b5d30b3a8e4840b3e3268ae8eb", "impliedFormat": 1}, {"version": "f874ea4d0091b0a44362a5f74d26caab2e66dec306c2bf7e8965f5106e784c3b", "impliedFormat": 1}], "root": [166, 167, [237, 259]], "options": {"allowJs": true, "declaration": true, "esModuleInterop": true, "jsx": 1, "module": 1, "outDir": "./dist", "rootDir": "./src", "skipLibCheck": true, "sourceMap": true, "strict": true, "target": 9}, "referencedMap": [[220, 1], [221, 2], [222, 3], [181, 4], [174, 5], [178, 6], [182, 7], [183, 8], [217, 1], [234, 9], [218, 10], [219, 11], [225, 11], [235, 12], [226, 13], [233, 1], [177, 14], [176, 15], [179, 15], [169, 16], [173, 17], [175, 18], [168, 1], [180, 19], [190, 1], [207, 20], [205, 21], [206, 22], [194, 23], [195, 21], [202, 24], [193, 25], [198, 26], [208, 1], [199, 27], [204, 28], [209, 29], [192, 30], [200, 31], [201, 32], [196, 33], [203, 20], [197, 34], [172, 35], [224, 36], [215, 37], [216, 36], [228, 38], [214, 1], [227, 39], [236, 40], [231, 41], [232, 42], [213, 43], [210, 44], [189, 45], [187, 46], [186, 1], [188, 47], [211, 1], [212, 48], [237, 49], [241, 50], [239, 51], [240, 51], [238, 52], [167, 53], [242, 54], [166, 55], [253, 56], [254, 57], [255, 58], [256, 59], [257, 60], [258, 61], [243, 54], [245, 62], [246, 63], [247, 64], [248, 65], [249, 65], [244, 65], [250, 66], [251, 63], [252, 65], [259, 67], [171, 68], [170, 1], [260, 1], [261, 1], [262, 69], [264, 70], [230, 71], [263, 72], [265, 1], [266, 1], [267, 72], [268, 1], [229, 1], [184, 1], [273, 73], [276, 74], [274, 1], [277, 1], [278, 1], [279, 1], [281, 75], [282, 1], [269, 1], [280, 1], [103, 76], [104, 76], [105, 77], [63, 78], [106, 79], [107, 80], [108, 81], [58, 1], [61, 82], [59, 1], [60, 1], [109, 83], [110, 84], [111, 85], [112, 86], [113, 87], [114, 88], [115, 88], [117, 1], [116, 89], [118, 90], [119, 91], [120, 92], [102, 93], [62, 1], [121, 94], [122, 95], [123, 96], [155, 97], [124, 98], [125, 99], [126, 100], [127, 101], [128, 102], [129, 103], [130, 104], [131, 105], [132, 106], [133, 107], [134, 107], [135, 108], [136, 1], [137, 109], [139, 110], [138, 111], [140, 112], [141, 113], [142, 114], [143, 115], [144, 116], [145, 117], [146, 118], [147, 119], [148, 120], [149, 121], [150, 122], [151, 123], [152, 124], [153, 125], [154, 126], [283, 1], [284, 1], [271, 1], [272, 1], [288, 127], [285, 1], [287, 128], [313, 129], [314, 130], [289, 131], [292, 131], [311, 129], [312, 129], [302, 129], [301, 132], [299, 129], [294, 129], [307, 129], [305, 129], [309, 129], [293, 129], [306, 129], [310, 129], [295, 129], [296, 129], [308, 129], [290, 129], [297, 129], [298, 129], [300, 129], [304, 129], [315, 133], [303, 129], [291, 129], [328, 134], [327, 1], [322, 133], [324, 135], [323, 133], [316, 133], [317, 133], [319, 133], [321, 133], [325, 135], [326, 135], [318, 135], [320, 135], [270, 136], [275, 137], [329, 1], [338, 138], [330, 1], [333, 139], [336, 140], [337, 141], [331, 142], [334, 143], [332, 144], [342, 145], [340, 146], [341, 147], [339, 148], [343, 1], [344, 1], [156, 149], [165, 1], [64, 1], [286, 1], [161, 1], [335, 150], [185, 151], [191, 1], [223, 1], [56, 1], [57, 1], [11, 1], [10, 1], [2, 1], [12, 1], [13, 1], [14, 1], [15, 1], [16, 1], [17, 1], [18, 1], [19, 1], [3, 1], [20, 1], [21, 1], [4, 1], [22, 1], [26, 1], [23, 1], [24, 1], [25, 1], [27, 1], [28, 1], [29, 1], [5, 1], [30, 1], [31, 1], [32, 1], [33, 1], [6, 1], [37, 1], [34, 1], [35, 1], [36, 1], [38, 1], [7, 1], [39, 1], [44, 1], [45, 1], [40, 1], [41, 1], [42, 1], [43, 1], [8, 1], [49, 1], [46, 1], [47, 1], [48, 1], [50, 1], [9, 1], [51, 1], [52, 1], [53, 1], [55, 1], [54, 1], [1, 1], [80, 152], [90, 153], [79, 152], [100, 154], [71, 155], [70, 156], [99, 69], [93, 157], [98, 158], [73, 159], [87, 160], [72, 161], [96, 162], [68, 163], [67, 69], [97, 164], [69, 165], [74, 166], [75, 1], [78, 166], [65, 1], [101, 167], [91, 168], [82, 169], [83, 170], [85, 171], [81, 172], [84, 173], [94, 69], [76, 174], [77, 175], [86, 176], [66, 177], [89, 168], [88, 166], [92, 1], [95, 178], [157, 1], [163, 1], [164, 179], [162, 180], [160, 1], [159, 1], [158, 64]], "semanticDiagnosticsPerFile": [[237, [{"start": 2957, "length": 4, "messageText": "Parameter 'call' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3204, "length": 4, "messageText": "Parameter 'call' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3591, "length": 4, "messageText": "Parameter 'call' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3984, "length": 4, "messageText": "Parameter 'call' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4491, "length": 9, "messageText": "'WebSocket' cannot be used as a value because it was imported using 'import type'.", "category": 1, "code": 1361, "relatedInformation": [{"start": 176, "length": 14, "messageText": "'WebSocket' was imported here.", "category": 3, "code": 1376}]}, {"start": 4560, "length": 9, "messageText": "'WebSocket' cannot be used as a value because it was imported using 'import type'.", "category": 1, "code": 1361, "relatedInformation": [{"start": 176, "length": 14, "messageText": "'WebSocket' was imported here.", "category": 3, "code": 1376}]}, {"start": 4629, "length": 9, "messageText": "'WebSocket' cannot be used as a value because it was imported using 'import type'.", "category": 1, "code": 1361, "relatedInformation": [{"start": 176, "length": 14, "messageText": "'WebSocket' was imported here.", "category": 3, "code": 1376}]}]], [238, [{"start": 11704, "length": 4, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'last' does not exist in type 'MarketData'."}]], [239, [{"start": 9759, "length": 4, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'last' does not exist in type 'MarketData'."}, {"start": 20381, "length": 4, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'last' does not exist in type 'MarketData'."}]], [240, [{"start": 2289, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'metadata' does not exist on type 'BrokerAdapterConfig'."}]], [248, [{"start": 3562, "length": 7, "messageText": "Property 'account' has no initializer and is not definitely assigned in the constructor.", "category": 1, "code": 2564}]], [253, [{"start": 5885, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'errorType' does not exist on type 'SimulatedError'."}, {"start": 6754, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'errorType' does not exist on type 'SimulatedError'."}, {"start": 7043, "length": 9, "code": 2339, "category": 1, "messageText": "Property 'errorType' does not exist on type 'SimulatedError'."}]], [255, [{"start": 5601, "length": 4, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'TaskContext<Test<{}>> & TestContext' has no call signatures.", "category": 1, "code": 2757}]}}, {"start": 5706, "length": 4, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'TaskContext<Test<{}>> & TestContext' has no call signatures.", "category": 1, "code": 2757}]}}, {"start": 11489, "length": 4, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'TaskContext<Test<{}>> & TestContext' has no call signatures.", "category": 1, "code": 2757}]}}]], [256, [{"start": 17749, "length": 20, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'partialFillThreshold' does not exist in type 'Partial<OrderManagerConfig>'."}, {"start": 19011, "length": 20, "code": 2353, "category": 1, "messageText": "Object literal may only specify known properties, and 'partialFillThreshold' does not exist in type 'Partial<OrderManagerConfig>'."}, {"start": 19279, "length": 4, "code": 2349, "category": 1, "messageText": {"messageText": "This expression is not callable.", "category": 1, "code": 2349, "next": [{"messageText": "Type 'TaskContext<Test<{}>> & TestContext' has no call signatures.", "category": 1, "code": 2757}]}}]], [257, [{"start": 1597, "length": 182, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ maxRetryAttempts: number; baseRetryDelay: number; maxRetryDelay: number; retryMultiplier: number; circuitBreakerThreshold: number; circuitBreakerTimeout: number; }' is not assignable to parameter of type 'ReconnectionConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Type '{ maxRetryAttempts: number; baseRetryDelay: number; maxRetryDelay: number; retryMultiplier: number; circuitBreakerThreshold: number; circuitBreakerTimeout: number; }' is missing the following properties from type 'ReconnectionConfig': jitterMax, healthCheckInterval, statePreservationTimeout", "category": 1, "code": 2739}]}}, {"start": 2103, "length": 86, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ syncInterval: number; enableRealTime: true; persistToDB: false; }' is not assignable to parameter of type 'SyncConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Property 'riskMonitoring' is missing in type '{ syncInterval: number; enableRealTime: true; persistToDB: false; }' but required in type 'SyncConfig'.", "category": 1, "code": 2741}]}, "relatedInformation": [{"file": "./src/services/accountsynchronizationservice.ts", "start": 544, "length": 14, "messageText": "'riskMonitoring' is declared here.", "category": 3, "code": 2728}]}, {"start": 5785, "length": 12, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; server: string; login: string; password: string; priority: number; maxConnections: number; features: string[]; type: any; apiVersion: string; maxOrdersPerSecond: number; ... 6 more ...; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerConnectionConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type '(\"streaming\" | \"trading\" | \"history\" | \"monitoring\")[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type '\"streaming\" | \"trading\" | \"history\" | \"monitoring\"'.", "category": 1, "code": 2322}]}]}]}}, {"start": 7224, "length": 12, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; server: string; login: string; password: string; priority: number; maxConnections: number; features: string[]; type: any; apiVersion: string; maxOrdersPerSecond: number; ... 6 more ...; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerConnectionConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type '(\"streaming\" | \"trading\" | \"history\" | \"monitoring\")[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type '\"streaming\" | \"trading\" | \"history\" | \"monitoring\"'.", "category": 1, "code": 2322}]}]}]}}, {"start": 10053, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; type: any; server: string; apiVersion: string; maxOrdersPerSecond: number; supportedSymbols: string[]; supportedTimeframes: string[]; marginCalculation: any; swapCalculation: any; features: string[]; connectionLimits: { ...; }; authentication: { ...; }; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerAdapterConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type 'BrokerFeature[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'BrokerFeature'.", "category": 1, "code": 2322}]}]}]}}, {"start": 11245, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; type: any; server: string; apiVersion: string; maxOrdersPerSecond: number; supportedSymbols: string[]; supportedTimeframes: string[]; marginCalculation: any; swapCalculation: any; features: string[]; connectionLimits: { ...; }; authentication: { ...; }; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerAdapterConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type 'BrokerFeature[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'BrokerFeature'.", "category": 1, "code": 2322}]}]}]}}, {"start": 12515, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; type: any; server: string; apiVersion: string; maxOrdersPerSecond: number; supportedSymbols: string[]; supportedTimeframes: string[]; marginCalculation: any; swapCalculation: any; features: string[]; connectionLimits: { ...; }; authentication: { ...; }; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerAdapterConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type 'BrokerFeature[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'BrokerFeature'.", "category": 1, "code": 2322}]}]}]}}, {"start": 13913, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; type: any; server: string; apiVersion: string; maxOrdersPerSecond: number; supportedSymbols: string[]; supportedTimeframes: string[]; marginCalculation: any; swapCalculation: any; features: string[]; connectionLimits: { ...; }; authentication: { ...; }; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerAdapterConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type 'BrokerFeature[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'BrokerFeature'.", "category": 1, "code": 2322}]}]}]}}, {"start": 15500, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; type: any; server: string; apiVersion: string; maxOrdersPerSecond: number; supportedSymbols: string[]; supportedTimeframes: string[]; marginCalculation: any; swapCalculation: any; features: string[]; connectionLimits: { ...; }; authentication: { ...; }; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerAdapterConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type 'BrokerFeature[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'BrokerFeature'.", "category": 1, "code": 2322}]}]}]}}, {"start": 16827, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; type: any; server: string; apiVersion: string; maxOrdersPerSecond: number; supportedSymbols: string[]; supportedTimeframes: string[]; marginCalculation: any; swapCalculation: any; features: string[]; connectionLimits: { ...; }; authentication: { ...; }; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerAdapterConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type 'BrokerFeature[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'BrokerFeature'.", "category": 1, "code": 2322}]}]}]}}, {"start": 18278, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; type: any; server: string; apiVersion: string; maxOrdersPerSecond: number; supportedSymbols: string[]; supportedTimeframes: string[]; marginCalculation: any; swapCalculation: any; features: string[]; connectionLimits: { ...; }; authentication: { ...; }; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerAdapterConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type 'BrokerFeature[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'BrokerFeature'.", "category": 1, "code": 2322}]}]}]}}, {"start": 19673, "length": 6, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; type: any; server: string; apiVersion: string; maxOrdersPerSecond: number; supportedSymbols: string[]; supportedTimeframes: string[]; marginCalculation: any; swapCalculation: any; features: string[]; connectionLimits: { ...; }; authentication: { ...; }; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerAdapterConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type 'BrokerFeature[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'BrokerFeature'.", "category": 1, "code": 2322}]}]}]}}, {"start": 21111, "length": 12, "code": 2345, "category": 1, "messageText": {"messageText": "Argument of type '{ id: string; name: string; type: any; server: string; apiVersion: string; maxOrdersPerSecond: number; supportedSymbols: string[]; supportedTimeframes: string[]; marginCalculation: any; swapCalculation: any; features: string[]; connectionLimits: { ...; }; authentication: { ...; }; riskManagement: { ...; }; }' is not assignable to parameter of type 'BrokerAdapterConfig'.", "category": 1, "code": 2345, "next": [{"messageText": "Types of property 'features' are incompatible.", "category": 1, "code": 2326, "next": [{"messageText": "Type 'string[]' is not assignable to type 'BrokerFeature[]'.", "category": 1, "code": 2322, "next": [{"messageText": "Type 'string' is not assignable to type 'BrokerFeature'.", "category": 1, "code": 2322}]}]}]}}]], [259, [{"start": 875, "length": 9, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 927, "length": 19, "messageText": "Element implicitly has an 'any' type because type 'typeof globalThis' has no index signature.", "category": 1, "code": 7017}, {"start": 1137, "length": 9, "messageText": "Cannot find name 'after<PERSON><PERSON>'.", "category": 1, "code": 2304}]]], "affectedFilesPendingEmit": [237, 241, 239, 240, 238, [167], 242, 166, 253, 254, 255, 256, 257, 258, 243, 245, 246, 247, 248, 249, 244, 250, 251, 252], "version": "5.9.2"}