"""
Advanced Monitoring and Metrics Collection
Provides comprehensive system monitoring, metrics collection, and alerting capabilities
"""

import asyncio
import time
import psutil
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
import json
from loguru import logger

from performance_monitor import get_performance_profiler
from health_monitor import get_health_monitor
from config import get_config

@dataclass
class SystemMetrics:
    """System-level metrics"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    network_bytes_sent: int
    network_bytes_recv: int
    load_average: Optional[Tuple[float, float, float]]
    process_count: int
    thread_count: int

@dataclass 
class ServiceMetrics:
    """Service-level metrics"""
    timestamp: datetime
    active_connections: int
    websocket_connections: int
    requests_per_second: float
    response_time_ms: float
    error_rate: float
    cache_hit_rate: float
    database_connections: int
    mt5_connection_status: str
    price_updates_per_second: float

@dataclass
class AlertCondition:
    """Alert condition configuration"""
    name: str
    metric_path: str  # e.g., "system.cpu_percent" or "service.error_rate"
    threshold: float
    comparison: str  # "gt", "lt", "eq", "gte", "lte"
    duration_seconds: int  # How long condition must persist
    severity: str  # "info", "warning", "error", "critical"
    enabled: bool = True

class MetricsCollector:
    """Advanced metrics collection and monitoring system"""
    
    def __init__(self):
        self.config = get_config()
        self.performance_profiler = get_performance_profiler()
        self.health_monitor = get_health_monitor()
        
        # Metrics storage
        self.system_metrics_history: deque = deque(maxlen=1000)
        self.service_metrics_history: deque = deque(maxlen=1000)
        self.alert_conditions: Dict[str, AlertCondition] = {}
        self.active_alerts: Dict[str, Dict[str, Any]] = {}
        
        # Monitoring state
        self.is_collecting = False
        self.collection_interval = 30  # seconds
        self.collection_task: Optional[asyncio.Task] = None
        
        # Performance tracking
        self.request_times: deque = deque(maxlen=1000)
        self.error_counts: defaultdict = defaultdict(int)
        self.connection_counts = {
            'http': 0,
            'websocket': 0,
            'database': 0
        }
        
        # Initialize default alert conditions
        self._setup_default_alerts()
        
    def _setup_default_alerts(self):
        """Setup default system alert conditions"""
        default_alerts = [
            AlertCondition(
                name="high_cpu_usage",
                metric_path="system.cpu_percent",
                threshold=80.0,
                comparison="gt",
                duration_seconds=300,
                severity="warning"
            ),
            AlertCondition(
                name="critical_cpu_usage",
                metric_path="system.cpu_percent", 
                threshold=95.0,
                comparison="gt",
                duration_seconds=60,
                severity="critical"
            ),
            AlertCondition(
                name="high_memory_usage",
                metric_path="system.memory_percent",
                threshold=85.0,
                comparison="gt",
                duration_seconds=300,
                severity="warning"
            ),
            AlertCondition(
                name="critical_memory_usage",
                metric_path="system.memory_percent",
                threshold=95.0,
                comparison="gt",
                duration_seconds=60,
                severity="critical"
            ),
            AlertCondition(
                name="high_error_rate",
                metric_path="service.error_rate",
                threshold=5.0,
                comparison="gt",
                duration_seconds=120,
                severity="error"
            ),
            AlertCondition(
                name="slow_response_time",
                metric_path="service.response_time_ms",
                threshold=1000.0,
                comparison="gt",
                duration_seconds=180,
                severity="warning"
            ),
            AlertCondition(
                name="mt5_connection_lost",
                metric_path="service.mt5_connection_status",
                threshold="disconnected",
                comparison="eq",
                duration_seconds=30,
                severity="critical"
            )
        ]
        
        for alert in default_alerts:
            self.alert_conditions[alert.name] = alert
    
    async def start_collection(self):
        """Start metrics collection"""
        if self.is_collecting:
            logger.warning("Metrics collection already running")
            return
        
        logger.info("🔄 Starting metrics collection...")
        self.is_collecting = True
        self.collection_task = asyncio.create_task(self._collection_loop())
        logger.info("✅ Metrics collection started")
    
    async def stop_collection(self):
        """Stop metrics collection"""
        if not self.is_collecting:
            return
        
        logger.info("🔌 Stopping metrics collection...")
        self.is_collecting = False
        
        if self.collection_task:
            self.collection_task.cancel()
            try:
                await self.collection_task
            except asyncio.CancelledError:
                pass
        
        logger.info("✅ Metrics collection stopped")
    
    async def _collection_loop(self):
        """Main metrics collection loop"""
        while self.is_collecting:
            try:
                # Collect system metrics
                system_metrics = await self._collect_system_metrics()
                self.system_metrics_history.append(system_metrics)
                
                # Collect service metrics
                service_metrics = await self._collect_service_metrics()
                self.service_metrics_history.append(service_metrics)
                
                # Check alert conditions
                await self._check_alerts(system_metrics, service_metrics)
                
                # Log metrics summary
                self._log_metrics_summary(system_metrics, service_metrics)
                
            except Exception as e:
                logger.error(f"Error in metrics collection: {e}")
            
            await asyncio.sleep(self.collection_interval)
    
    async def _collect_system_metrics(self) -> SystemMetrics:
        """Collect system-level metrics"""
        
        # CPU and Memory
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        # Disk usage
        disk_usage = psutil.disk_usage('/')
        disk_free_gb = disk_usage.free / (1024**3)
        disk_usage_percent = (disk_usage.used / disk_usage.total) * 100
        
        # Network I/O
        network = psutil.net_io_counters()
        
        # Load average (Unix-like systems)
        try:
            load_avg = psutil.getloadavg()
        except (AttributeError, OSError):
            load_avg = None
        
        # Process information
        process_count = len(psutil.pids())
        current_process = psutil.Process()
        thread_count = current_process.num_threads()
        
        return SystemMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_used_mb=memory.used / (1024**2),
            memory_available_mb=memory.available / (1024**2),
            disk_usage_percent=disk_usage_percent,
            disk_free_gb=disk_free_gb,
            network_bytes_sent=network.bytes_sent,
            network_bytes_recv=network.bytes_recv,
            load_average=load_avg,
            process_count=process_count,
            thread_count=thread_count
        )
    
    async def _collect_service_metrics(self) -> ServiceMetrics:
        """Collect service-level metrics"""
        
        # Calculate requests per second (last 60 seconds)
        now = time.time()
        recent_requests = [t for t in self.request_times if now - t <= 60]
        rps = len(recent_requests) / 60.0
        
        # Calculate average response time (last 100 requests)
        perf_summary = self.performance_profiler.get_performance_summary(hours_back=1)
        avg_response_time = 0.0
        
        if 'latency_stats' in perf_summary and 'api_response' in perf_summary['latency_stats']:
            avg_response_time = perf_summary['latency_stats']['api_response'].get('mean', 0.0)
        
        # Calculate error rate (last hour)
        total_errors = sum(self.error_counts.values())
        total_requests = len(self.request_times)
        error_rate = (total_errors / total_requests * 100) if total_requests > 0 else 0.0
        
        # Get MT5 connection status
        health_status = await self.health_monitor.check_health()
        mt5_status = "unknown"
        for component in health_status.components:
            if component.name == "mt5_connection":
                mt5_status = component.status.value
                break
        
        return ServiceMetrics(
            timestamp=datetime.now(),
            active_connections=self.connection_counts['http'],
            websocket_connections=self.connection_counts['websocket'],
            requests_per_second=rps,
            response_time_ms=avg_response_time,
            error_rate=error_rate,
            cache_hit_rate=0.0,  # TODO: Implement cache metrics
            database_connections=self.connection_counts['database'],
            mt5_connection_status=mt5_status,
            price_updates_per_second=0.0  # TODO: Get from price streamer
        )
    
    async def _check_alerts(self, system_metrics: SystemMetrics, service_metrics: ServiceMetrics):
        """Check alert conditions and trigger alerts"""
        
        current_time = datetime.now()
        
        for alert_name, condition in self.alert_conditions.items():
            if not condition.enabled:
                continue
            
            # Get metric value
            metric_value = self._get_metric_value(condition.metric_path, system_metrics, service_metrics)
            if metric_value is None:
                continue
            
            # Check condition
            condition_met = self._evaluate_condition(metric_value, condition)
            
            # Handle alert state
            if condition_met:
                if alert_name not in self.active_alerts:
                    # New alert condition
                    self.active_alerts[alert_name] = {
                        'condition': condition,
                        'started_at': current_time,
                        'metric_value': metric_value,
                        'triggered': False
                    }
                else:
                    # Update existing alert
                    self.active_alerts[alert_name]['metric_value'] = metric_value
                    
                    # Check if duration threshold met
                    alert_info = self.active_alerts[alert_name]
                    duration = (current_time - alert_info['started_at']).total_seconds()
                    
                    if duration >= condition.duration_seconds and not alert_info['triggered']:
                        # Trigger alert
                        await self._trigger_alert(alert_name, alert_info, metric_value)
                        alert_info['triggered'] = True
                        
            else:
                # Condition no longer met
                if alert_name in self.active_alerts:
                    alert_info = self.active_alerts[alert_name]
                    if alert_info['triggered']:
                        # Alert resolved
                        await self._resolve_alert(alert_name, alert_info, metric_value)
                    
                    del self.active_alerts[alert_name]
    
    def _get_metric_value(self, metric_path: str, system_metrics: SystemMetrics, service_metrics: ServiceMetrics) -> Any:
        """Extract metric value from metrics objects"""
        parts = metric_path.split('.')
        if len(parts) != 2:
            return None
        
        metric_type, metric_name = parts
        
        if metric_type == "system":
            return getattr(system_metrics, metric_name, None)
        elif metric_type == "service":
            return getattr(service_metrics, metric_name, None)
        
        return None
    
    def _evaluate_condition(self, value: Any, condition: AlertCondition) -> bool:
        """Evaluate alert condition"""
        try:
            threshold = condition.threshold
            comparison = condition.comparison
            
            if comparison == "gt":
                return float(value) > float(threshold)
            elif comparison == "lt":
                return float(value) < float(threshold)
            elif comparison == "gte":
                return float(value) >= float(threshold)
            elif comparison == "lte":
                return float(value) <= float(threshold)
            elif comparison == "eq":
                return str(value) == str(threshold)
            
            return False
        except (ValueError, TypeError):
            return False
    
    async def _trigger_alert(self, alert_name: str, alert_info: Dict[str, Any], current_value: Any):
        """Trigger an alert"""
        condition = alert_info['condition']
        
        alert_data = {
            'alert_name': alert_name,
            'severity': condition.severity,
            'condition': condition.name,
            'metric_path': condition.metric_path,
            'threshold': condition.threshold,
            'current_value': current_value,
            'started_at': alert_info['started_at'].isoformat(),
            'triggered_at': datetime.now().isoformat(),
            'message': f"Alert {alert_name}: {condition.metric_path} = {current_value} {condition.comparison} {condition.threshold}"
        }
        
        logger.warning(f"🚨 ALERT TRIGGERED: {alert_data['message']}")
        
        # TODO: Send alert notifications (email, webhook, etc.)
        
    async def _resolve_alert(self, alert_name: str, alert_info: Dict[str, Any], current_value: Any):
        """Resolve an alert"""
        condition = alert_info['condition']
        
        logger.info(f"✅ ALERT RESOLVED: {alert_name} - {condition.metric_path} = {current_value}")
        
        # TODO: Send resolution notifications
    
    def _log_metrics_summary(self, system_metrics: SystemMetrics, service_metrics: ServiceMetrics):
        """Log metrics summary"""
        logger.debug(
            f"📊 Metrics - CPU: {system_metrics.cpu_percent:.1f}% | "
            f"Memory: {system_metrics.memory_percent:.1f}% | "
            f"RPS: {service_metrics.requests_per_second:.1f} | "
            f"Response: {service_metrics.response_time_ms:.1f}ms | "
            f"Errors: {service_metrics.error_rate:.1f}% | "
            f"MT5: {service_metrics.mt5_connection_status}"
        )
    
    def record_request(self):
        """Record an HTTP request"""
        self.request_times.append(time.time())
        self.connection_counts['http'] += 1
    
    def record_error(self, error_type: str = "generic"):
        """Record an error"""
        self.error_counts[error_type] += 1
    
    def update_connection_count(self, connection_type: str, count: int):
        """Update connection count"""
        if connection_type in self.connection_counts:
            self.connection_counts[connection_type] = count
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current metrics snapshot"""
        latest_system = self.system_metrics_history[-1] if self.system_metrics_history else None
        latest_service = self.service_metrics_history[-1] if self.service_metrics_history else None
        
        return {
            'system': asdict(latest_system) if latest_system else None,
            'service': asdict(latest_service) if latest_service else None,
            'active_alerts': len(self.active_alerts),
            'collection_status': self.is_collecting,
            'last_collection': latest_system.timestamp.isoformat() if latest_system else None
        }
    
    def get_metrics_history(self, hours_back: int = 1) -> Dict[str, List[Dict]]:
        """Get metrics history"""
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        system_history = [
            asdict(m) for m in self.system_metrics_history 
            if m.timestamp >= cutoff_time
        ]
        
        service_history = [
            asdict(m) for m in self.service_metrics_history 
            if m.timestamp >= cutoff_time
        ]
        
        return {
            'system': system_history,
            'service': service_history,
            'collection_interval': self.collection_interval,
            'total_points': len(system_history)
        }
    
    def get_alert_status(self) -> Dict[str, Any]:
        """Get alert status"""
        return {
            'active_alerts': {
                name: {
                    'condition': asdict(info['condition']),
                    'started_at': info['started_at'].isoformat(),
                    'current_value': info['metric_value'],
                    'triggered': info['triggered']
                }
                for name, info in self.active_alerts.items()
            },
            'total_conditions': len(self.alert_conditions),
            'enabled_conditions': sum(1 for c in self.alert_conditions.values() if c.enabled)
        }
    
    def add_alert_condition(self, condition: AlertCondition):
        """Add custom alert condition"""
        self.alert_conditions[condition.name] = condition
        logger.info(f"Added alert condition: {condition.name}")
    
    def remove_alert_condition(self, name: str):
        """Remove alert condition"""
        if name in self.alert_conditions:
            del self.alert_conditions[name]
            if name in self.active_alerts:
                del self.active_alerts[name]
            logger.info(f"Removed alert condition: {name}")
    
    def export_metrics(self, format: str = "json") -> str:
        """Export metrics in specified format"""
        data = {
            'export_timestamp': datetime.now().isoformat(),
            'system_metrics': [asdict(m) for m in self.system_metrics_history],
            'service_metrics': [asdict(m) for m in self.service_metrics_history],
            'alert_conditions': {name: asdict(condition) for name, condition in self.alert_conditions.items()},
            'active_alerts': self.get_alert_status()['active_alerts']
        }
        
        if format.lower() == "json":
            return json.dumps(data, indent=2, default=str)
        else:
            raise ValueError(f"Unsupported export format: {format}")

# Global instance
_metrics_collector: Optional[MetricsCollector] = None

def get_metrics_collector() -> MetricsCollector:
    """Get global metrics collector instance"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector