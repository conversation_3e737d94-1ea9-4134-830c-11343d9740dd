/**
 * Broker Configuration Service
 * 
 * Manages multi-broker configurations with encrypted credentials and health monitoring
 * Implements Task 1 from Story 4.3: Multi-Broker Configuration System
 */

import { PrismaClient } from '@prisma/client';
import type { 
  BrokerConfiguration, 
  CreateBrokerConfigurationRequest, 
  UpdateBrokerConfigurationRequest,
  BrokerConfigurationResponse,
  BrokerConfigurationListResponse,
  BrokerStatus,
  BrokerFeature
} from '@golddaddy/types';
import { encrypt, decrypt } from '../../lib/encryption.js';
import { validateBrokerConfig } from '../../utils/validation.utils.js';

export class BrokerConfigurationService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Create a new broker configuration with encrypted credentials
   */
  async createBrokerConfiguration(
    userId: string, 
    request: CreateBrokerConfigurationRequest
  ): Promise<BrokerConfigurationResponse> {
    try {
      // Validate request
      const validation = await validateBrokerConfig(request);
      if (!validation.isValid) {
        return {
          success: false,
          error: {
            code: 'VALIDATION_ERROR',
            message: 'Invalid broker configuration',
            details: validation.errors
          }
        };
      }

      // Check if broker name already exists for user
      const existingBroker = await this.prisma.brokerConfiguration.findUnique({
        where: {
          userId_brokerName: {
            userId,
            brokerName: request.brokerName
          }
        }
      });

      if (existingBroker) {
        return {
          success: false,
          error: {
            code: 'BROKER_EXISTS',
            message: `Broker configuration '${request.brokerName}' already exists`,
          }
        };
      }

      // Encrypt sensitive credentials
      const encryptedPassword = await encrypt(request.connectionDetails.password);

      // Set default health check values
      const healthCheckConfig = {
        interval: request.healthCheck?.interval ?? 30000,
        timeout: request.healthCheck?.timeout ?? 5000,
        retryCount: request.healthCheck?.retryCount ?? 5
      };

      // Create broker configuration
      const brokerConfig = await this.prisma.brokerConfiguration.create({
        data: {
          userId,
          brokerName: request.brokerName,
          priority: request.priority,
          server: request.connectionDetails.server,
          login: request.connectionDetails.login,
          password: encryptedPassword,
          timeout: request.connectionDetails.timeout,
          healthCheckInterval: healthCheckConfig.interval,
          healthCheckTimeout: healthCheckConfig.timeout,
          retryCount: healthCheckConfig.retryCount,
          status: 'INACTIVE',
          features: request.features,
          isHealthy: false,
          failureCount: 0
        }
      });

      const response: BrokerConfiguration = {
        id: brokerConfig.id,
        userId: brokerConfig.userId,
        brokerName: brokerConfig.brokerName,
        priority: brokerConfig.priority,
        connectionDetails: {
          server: brokerConfig.server,
          login: brokerConfig.login,
          password: '[ENCRYPTED]', // Never expose actual password
          timeout: brokerConfig.timeout
        },
        healthCheck: {
          interval: brokerConfig.healthCheckInterval,
          timeout: brokerConfig.healthCheckTimeout,
          retryCount: brokerConfig.retryCount
        },
        status: brokerConfig.status as BrokerStatus,
        lastHealthCheck: brokerConfig.lastHealthCheck || undefined,
        lastError: brokerConfig.lastError || undefined,
        isHealthy: brokerConfig.isHealthy,
        failureCount: brokerConfig.failureCount,
        features: brokerConfig.features as BrokerFeature[],
        createdAt: brokerConfig.createdAt,
        updatedAt: brokerConfig.updatedAt,
        deletedAt: brokerConfig.deletedAt || undefined
      };

      return {
        success: true,
        data: response
      };
      
    } catch (error) {
      console.error('Failed to create broker configuration:', error);
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to create broker configuration',
          details: error instanceof Error ? { message: error.message } : {}
        }
      };
    }
  }

  /**
   * Get all broker configurations for a user
   */
  async getBrokerConfigurations(userId: string): Promise<BrokerConfigurationListResponse> {
    try {
      const brokerConfigs = await this.prisma.brokerConfiguration.findMany({
        where: {
          userId,
          deletedAt: null
        },
        orderBy: [
          { priority: 'asc' },
          { createdAt: 'desc' }
        ]
      });

      const response: BrokerConfiguration[] = brokerConfigs.map(config => ({
        id: config.id,
        userId: config.userId,
        brokerName: config.brokerName,
        priority: config.priority,
        connectionDetails: {
          server: config.server,
          login: config.login,
          password: '[ENCRYPTED]', // Never expose actual password
          timeout: config.timeout
        },
        healthCheck: {
          interval: config.healthCheckInterval,
          timeout: config.healthCheckTimeout,
          retryCount: config.retryCount
        },
        status: config.status as BrokerStatus,
        lastHealthCheck: config.lastHealthCheck || undefined,
        lastError: config.lastError || undefined,
        isHealthy: config.isHealthy,
        failureCount: config.failureCount,
        features: config.features as BrokerFeature[],
        createdAt: config.createdAt,
        updatedAt: config.updatedAt,
        deletedAt: config.deletedAt || undefined
      }));

      return {
        success: true,
        data: response
      };
      
    } catch (error) {
      console.error('Failed to get broker configurations:', error);
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve broker configurations',
          details: error instanceof Error ? { message: error.message } : {}
        }
      };
    }
  }

  /**
   * Get a specific broker configuration by ID
   */
  async getBrokerConfiguration(
    userId: string, 
    brokerId: string
  ): Promise<BrokerConfigurationResponse> {
    try {
      const brokerConfig = await this.prisma.brokerConfiguration.findFirst({
        where: {
          id: brokerId,
          userId,
          deletedAt: null
        }
      });

      if (!brokerConfig) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Broker configuration not found'
          }
        };
      }

      const response: BrokerConfiguration = {
        id: brokerConfig.id,
        userId: brokerConfig.userId,
        brokerName: brokerConfig.brokerName,
        priority: brokerConfig.priority,
        connectionDetails: {
          server: brokerConfig.server,
          login: brokerConfig.login,
          password: '[ENCRYPTED]', // Never expose actual password
          timeout: brokerConfig.timeout
        },
        healthCheck: {
          interval: brokerConfig.healthCheckInterval,
          timeout: brokerConfig.healthCheckTimeout,
          retryCount: brokerConfig.retryCount
        },
        status: brokerConfig.status as BrokerStatus,
        lastHealthCheck: brokerConfig.lastHealthCheck || undefined,
        lastError: brokerConfig.lastError || undefined,
        isHealthy: brokerConfig.isHealthy,
        failureCount: brokerConfig.failureCount,
        features: brokerConfig.features as BrokerFeature[],
        createdAt: brokerConfig.createdAt,
        updatedAt: brokerConfig.updatedAt,
        deletedAt: brokerConfig.deletedAt || undefined
      };

      return {
        success: true,
        data: response
      };
      
    } catch (error) {
      console.error('Failed to get broker configuration:', error);
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to retrieve broker configuration',
          details: error instanceof Error ? { message: error.message } : {}
        }
      };
    }
  }

  /**
   * Update an existing broker configuration
   */
  async updateBrokerConfiguration(
    userId: string, 
    brokerId: string, 
    request: UpdateBrokerConfigurationRequest
  ): Promise<BrokerConfigurationResponse> {
    try {
      // Check if broker exists and belongs to user
      const existingBroker = await this.prisma.brokerConfiguration.findFirst({
        where: {
          id: brokerId,
          userId,
          deletedAt: null
        }
      });

      if (!existingBroker) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Broker configuration not found'
          }
        };
      }

      // Prepare update data
      const updateData: any = {};

      if (request.brokerName) updateData.brokerName = request.brokerName;
      if (request.priority !== undefined) updateData.priority = request.priority;
      if (request.status) updateData.status = request.status;
      if (request.features) updateData.features = request.features;

      // Handle connection details update
      if (request.connectionDetails) {
        if (request.connectionDetails.server) updateData.server = request.connectionDetails.server;
        if (request.connectionDetails.login) updateData.login = request.connectionDetails.login;
        if (request.connectionDetails.password) {
          updateData.password = await encrypt(request.connectionDetails.password);
        }
        if (request.connectionDetails.timeout) updateData.timeout = request.connectionDetails.timeout;
      }

      // Handle health check config update
      if (request.healthCheck) {
        if (request.healthCheck.interval) updateData.healthCheckInterval = request.healthCheck.interval;
        if (request.healthCheck.timeout) updateData.healthCheckTimeout = request.healthCheck.timeout;
        if (request.healthCheck.retryCount) updateData.retryCount = request.healthCheck.retryCount;
      }

      // Update broker configuration
      const updatedBroker = await this.prisma.brokerConfiguration.update({
        where: { id: brokerId },
        data: updateData
      });

      const response: BrokerConfiguration = {
        id: updatedBroker.id,
        userId: updatedBroker.userId,
        brokerName: updatedBroker.brokerName,
        priority: updatedBroker.priority,
        connectionDetails: {
          server: updatedBroker.server,
          login: updatedBroker.login,
          password: '[ENCRYPTED]', // Never expose actual password
          timeout: updatedBroker.timeout
        },
        healthCheck: {
          interval: updatedBroker.healthCheckInterval,
          timeout: updatedBroker.healthCheckTimeout,
          retryCount: updatedBroker.retryCount
        },
        status: updatedBroker.status as BrokerStatus,
        lastHealthCheck: updatedBroker.lastHealthCheck || undefined,
        lastError: updatedBroker.lastError || undefined,
        isHealthy: updatedBroker.isHealthy,
        failureCount: updatedBroker.failureCount,
        features: updatedBroker.features as BrokerFeature[],
        createdAt: updatedBroker.createdAt,
        updatedAt: updatedBroker.updatedAt,
        deletedAt: updatedBroker.deletedAt || undefined
      };

      return {
        success: true,
        data: response
      };
      
    } catch (error) {
      console.error('Failed to update broker configuration:', error);
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to update broker configuration',
          details: error instanceof Error ? { message: error.message } : {}
        }
      };
    }
  }

  /**
   * Delete a broker configuration (soft delete)
   */
  async deleteBrokerConfiguration(
    userId: string, 
    brokerId: string
  ): Promise<BrokerConfigurationResponse> {
    try {
      // Check if broker exists and belongs to user
      const existingBroker = await this.prisma.brokerConfiguration.findFirst({
        where: {
          id: brokerId,
          userId,
          deletedAt: null
        }
      });

      if (!existingBroker) {
        return {
          success: false,
          error: {
            code: 'NOT_FOUND',
            message: 'Broker configuration not found'
          }
        };
      }

      // Soft delete broker configuration
      await this.prisma.brokerConfiguration.update({
        where: { id: brokerId },
        data: {
          deletedAt: new Date(),
          status: 'INACTIVE'
        }
      });

      return {
        success: true,
        data: undefined
      };
      
    } catch (error) {
      console.error('Failed to delete broker configuration:', error);
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Failed to delete broker configuration',
          details: error instanceof Error ? { message: error.message } : {}
        }
      };
    }
  }

  /**
   * Get decrypted password for internal system use only
   * WARNING: This method should only be used by internal services
   */
  async getDecryptedCredentials(
    userId: string, 
    brokerId: string
  ): Promise<{ login: string; password: string; server: string } | null> {
    try {
      const brokerConfig = await this.prisma.brokerConfiguration.findFirst({
        where: {
          id: brokerId,
          userId,
          deletedAt: null
        }
      });

      if (!brokerConfig) {
        return null;
      }

      const decryptedPassword = await decrypt(brokerConfig.password);

      return {
        login: brokerConfig.login,
        password: decryptedPassword,
        server: brokerConfig.server
      };
      
    } catch (error) {
      console.error('Failed to decrypt credentials:', error);
      return null;
    }
  }

  /**
   * Update broker health status
   */
  async updateBrokerHealth(
    brokerId: string,
    isHealthy: boolean,
    latency?: number,
    errorMessage?: string
  ): Promise<void> {
    try {
      const updateData: any = {
        isHealthy,
        lastHealthCheck: new Date(),
      };

      if (errorMessage) {
        updateData.lastError = errorMessage;
        updateData.failureCount = {
          increment: 1
        };
        updateData.status = isHealthy ? 'ACTIVE' : 'FAILED';
      } else if (isHealthy) {
        updateData.lastError = null;
        updateData.status = 'ACTIVE';
        // Reset failure count on successful health check
        updateData.failureCount = 0;
      }

      await this.prisma.brokerConfiguration.update({
        where: { id: brokerId },
        data: updateData
      });
      
    } catch (error) {
      console.error('Failed to update broker health:', error);
      throw error;
    }
  }

  /**
   * Get brokers ordered by priority for failover
   */
  async getBrokersForFailover(
    userId: string, 
    requiredFeature?: BrokerFeature
  ): Promise<BrokerConfiguration[]> {
    try {
      const where: any = {
        userId,
        deletedAt: null,
        status: {
          in: ['ACTIVE', 'INACTIVE'] // Exclude FAILED and MAINTENANCE
        }
      };

      const brokerConfigs = await this.prisma.brokerConfiguration.findMany({
        where,
        orderBy: [
          { priority: 'asc' },
          { failureCount: 'asc' },
          { createdAt: 'desc' }
        ]
      });

      const brokers: BrokerConfiguration[] = brokerConfigs
        .filter(config => {
          // If feature is specified, check if broker supports it
          if (requiredFeature && !config.features.includes(requiredFeature)) {
            return false;
          }
          return true;
        })
        .map(config => ({
          id: config.id,
          userId: config.userId,
          brokerName: config.brokerName,
          priority: config.priority,
          connectionDetails: {
            server: config.server,
            login: config.login,
            password: '[ENCRYPTED]',
            timeout: config.timeout
          },
          healthCheck: {
            interval: config.healthCheckInterval,
            timeout: config.healthCheckTimeout,
            retryCount: config.retryCount
          },
          status: config.status as BrokerStatus,
          lastHealthCheck: config.lastHealthCheck || undefined,
          lastError: config.lastError || undefined,
          isHealthy: config.isHealthy,
          failureCount: config.failureCount,
          features: config.features as BrokerFeature[],
          createdAt: config.createdAt,
          updatedAt: config.updatedAt,
          deletedAt: config.deletedAt || undefined
        }));

      return brokers;
      
    } catch (error) {
      console.error('Failed to get brokers for failover:', error);
      return [];
    }
  }
}