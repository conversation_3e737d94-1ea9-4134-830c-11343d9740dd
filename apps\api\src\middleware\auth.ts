/**
 * Authentication Middleware
 * 
 * JWT-based authentication middleware for API routes
 */

import type { Request, Response, NextFunction } from 'express';

// Extended request type with user information
export interface AuthenticatedRequest extends Request {
  userId?: string;
  user?: {
    id: string;
    email: string;
    role?: string;
  };
}

/**
 * Authentication middleware - validates JWT tokens
 */
export async function authMiddleware(
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
): Promise<void> {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Missing or invalid authorization header'
        }
      });
      return;
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix
    
    if (!token) {
      res.status(401).json({
        success: false,
        error: {
          code: 'UNAUTHORIZED',
          message: 'Missing access token'
        }
      });
      return;
    }

    // TODO: Implement JWT verification with your preferred JWT library
    // For now, we'll use a mock implementation
    
    // In production, you would:
    // 1. Verify JWT signature
    // 2. Check expiration
    // 3. Extract user information
    // 4. Optionally validate against database
    
    // Mock implementation - extract user ID from token
    try {
      // This is a placeholder - replace with actual JWT verification
      const payload = mockJwtVerify(token);
      
      req.userId = payload.sub;
      req.user = {
        id: payload.sub,
        email: payload.email,
        role: payload.role
      };
      
      next();
    } catch (jwtError) {
      res.status(401).json({
        success: false,
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired access token'
        }
      });
    }

  } catch (error) {
    console.error('Auth middleware error:', error);
    res.status(500).json({
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Authentication error'
      }
    });
  }
}

/**
 * Mock JWT verification - replace with real implementation
 * Using jsonwebtoken library or similar
 */
function mockJwtVerify(token: string): { sub: string; email: string; role?: string } {
  // This is a mock implementation for development
  // In production, use a proper JWT library like jsonwebtoken
  
  if (token === 'mock-token') {
    return {
      sub: 'user_123',
      email: '<EMAIL>',
      role: 'user'
    };
  }
  
  if (token === 'admin-token') {
    return {
      sub: 'admin_456',
      email: '<EMAIL>',
      role: 'admin'
    };
  }
  
  throw new Error('Invalid token');
}

/**
 * Admin-only middleware
 */
export async function adminMiddleware(
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
): Promise<void> {
  if (!req.user || req.user.role !== 'admin') {
    res.status(403).json({
      success: false,
      error: {
        code: 'FORBIDDEN',
        message: 'Admin access required'
      }
    });
    return;
  }
  
  next();
}

/**
 * Optional authentication - sets user if token is present but doesn't require it
 */
export async function optionalAuthMiddleware(
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
): Promise<void> {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    const token = authHeader.substring(7);
    
    try {
      const payload = mockJwtVerify(token);
      req.userId = payload.sub;
      req.user = {
        id: payload.sub,
        email: payload.email,
        role: payload.role
      };
    } catch (error) {
      // Token invalid but we continue without authentication
      console.warn('Invalid token in optional auth:', error);
    }
  }
  
  next();
}