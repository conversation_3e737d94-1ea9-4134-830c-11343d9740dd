/**
 * Real-time Metrics WebSocket Service
 * 
 * Provides real-time updates for trading metrics with automatic
 * plain English translation and user personalization.
 */

import { WebSocket, WebSocketServer } from 'ws';
import { IncomingMessage } from 'http';
import { 
  PlainEnglishMetric, 
  StrategyHealthScore,
  UserExperienceLevel,
  RiskTolerance,
  MetricType 
} from '@golddaddy/types';
import { MetricsTranslationService } from '../metrics/MetricsTranslationService.js';

interface WebSocketClient {
  ws: WebSocket;
  userId: string;
  strategyId: string;
  userExperience: UserExperienceLevel;
  userRiskTolerance: RiskTolerance;
  subscribedMetrics: MetricType[];
  lastPing: Date;
}

interface MetricUpdate {
  type: 'metric_update';
  strategyId: string;
  metric: PlainEnglishMetric;
  timestamp: Date;
}

interface HealthScoreUpdate {
  type: 'health_score_update';
  strategyId: string;
  healthScore: StrategyHealthScore;
  timestamp: Date;
}

interface ConnectionAck {
  type: 'connection_ack';
  clientId: string;
  timestamp: Date;
}

type WebSocketMessage = MetricUpdate | HealthScoreUpdate | ConnectionAck;

export class MetricsWebSocketService {
  private wss: WebSocketServer | null = null;
  private clients: Map<string, WebSocketClient> = new Map();
  private metricsService: MetricsTranslationService;
  private pingInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.metricsService = new MetricsTranslationService();
  }

  /**
   * Initialize WebSocket server
   */
  public initialize(port: number = 8080): void {
    this.wss = new WebSocketServer({ 
      port,
      verifyClient: this.verifyClient.bind(this)
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.startPingInterval();

    console.log(`Metrics WebSocket server started on port ${port}`);
  }

  /**
   * Verify WebSocket client connection
   */
  private verifyClient(info: { req: IncomingMessage }): boolean {
    // In production, verify JWT token from query params or headers
    const url = new URL(info.req.url || '', 'ws://localhost');
    const token = url.searchParams.get('token');
    
    // Mock verification - in production, validate JWT
    return !!token;
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(ws: WebSocket, req: IncomingMessage): void {
    const url = new URL(req.url || '', 'ws://localhost');
    const userId = url.searchParams.get('userId') || 'anonymous';
    const strategyId = url.searchParams.get('strategyId') || '';
    const userExperience = (url.searchParams.get('userExperience') as UserExperienceLevel) || 'intermediate';
    const userRiskTolerance = (url.searchParams.get('userRiskTolerance') as RiskTolerance) || 'moderate';
    const subscribedMetrics = url.searchParams.get('metrics')?.split(',') as MetricType[] || [];

    const clientId = `${userId}_${strategyId}_${Date.now()}`;
    
    const client: WebSocketClient = {
      ws,
      userId,
      strategyId,
      userExperience,
      userRiskTolerance,
      subscribedMetrics,
      lastPing: new Date(),
    };

    this.clients.set(clientId, client);

    // Send connection acknowledgment
    this.sendToClient(clientId, {
      type: 'connection_ack',
      clientId,
      timestamp: new Date(),
    });

    // Handle incoming messages
    ws.on('message', (data: Buffer) => {
      this.handleClientMessage(clientId, data);
    });

    // Handle client disconnect
    ws.on('close', () => {
      this.clients.delete(clientId);
      console.log(`Client ${clientId} disconnected`);
    });

    console.log(`Client ${clientId} connected for strategy ${strategyId}`);
  }

  /**
   * Handle incoming client messages
   */
  private handleClientMessage(clientId: string, data: Buffer): void {
    try {
      const message = JSON.parse(data.toString());
      const client = this.clients.get(clientId);
      
      if (!client) return;

      switch (message.type) {
        case 'ping':
          client.lastPing = new Date();
          client.ws.send(JSON.stringify({ type: 'pong', timestamp: new Date() }));
          break;
          
        case 'subscribe_metrics':
          client.subscribedMetrics = message.metrics || [];
          break;
          
        case 'update_preferences':
          client.userExperience = message.userExperience || client.userExperience;
          client.userRiskTolerance = message.userRiskTolerance || client.userRiskTolerance;
          break;
      }
    } catch (error) {
      console.error('Error handling client message:', error);
    }
  }

  /**
   * Broadcast metric update to relevant clients
   */
  public async broadcastMetricUpdate(
    strategyId: string, 
    metricType: MetricType, 
    value: number
  ): Promise<void> {
    const relevantClients = Array.from(this.clients.values())
      .filter(client => 
        client.strategyId === strategyId && 
        (client.subscribedMetrics.length === 0 || client.subscribedMetrics.includes(metricType))
      );

    for (const client of relevantClients) {
      try {
        // Translate metric for this specific user
        const translatedMetric = await this.translateMetricForUser(
          metricType, 
          value, 
          strategyId, 
          client.userId,
          client.userExperience,
          client.userRiskTolerance
        );

        const update: MetricUpdate = {
          type: 'metric_update',
          strategyId,
          metric: translatedMetric,
          timestamp: new Date(),
        };

        this.sendToClient(this.getClientId(client), update);
      } catch (error) {
        console.error(`Error sending metric update to client ${client.userId}:`, error);
      }
    }
  }

  /**
   * Broadcast health score update to relevant clients
   */
  public async broadcastHealthScoreUpdate(
    strategyId: string,
    healthScore: StrategyHealthScore
  ): Promise<void> {
    const relevantClients = Array.from(this.clients.values())
      .filter(client => client.strategyId === strategyId);

    for (const client of relevantClients) {
      try {
        const update: HealthScoreUpdate = {
          type: 'health_score_update',
          strategyId,
          healthScore,
          timestamp: new Date(),
        };

        this.sendToClient(this.getClientId(client), update);
      } catch (error) {
        console.error(`Error sending health score update to client ${client.userId}:`, error);
      }
    }
  }

  /**
   * Send message to specific client
   */
  private sendToClient(clientId: string, message: WebSocketMessage): void {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      client.ws.send(JSON.stringify(message));
    }
  }

  /**
   * Translate metric for specific user context
   */
  private async translateMetricForUser(
    metricType: MetricType,
    value: number,
    strategyId: string,
    userId: string,
    userExperience: UserExperienceLevel,
    userRiskTolerance: RiskTolerance
  ): Promise<PlainEnglishMetric> {
    const context = {
      userId,
      strategyId,
      strategyName: `Strategy ${strategyId}`, // TODO: Fetch from strategy service
      userExperience,
      userRiskTolerance,
    };

    switch (metricType) {
      case 'win_rate':
        return this.metricsService['plainEnglishService'].translateWinRate(value, context);
      case 'sharpe_ratio':
        return this.metricsService['plainEnglishService'].translateSharpeRatio(value, context);
      case 'profit_factor':
        return this.metricsService['plainEnglishService'].translateProfitFactor(value, context);
      default:
        throw new Error(`Unsupported metric type: ${metricType}`);
    }
  }

  /**
   * Get client ID for WebSocketClient
   */
  private getClientId(client: WebSocketClient): string {
    return Array.from(this.clients.entries())
      .find(([_, c]) => c === client)?.[0] || '';
  }

  /**
   * Start ping interval to keep connections alive
   */
  private startPingInterval(): void {
    this.pingInterval = setInterval(() => {
      const now = new Date();
      const staleClients: string[] = [];

      this.clients.forEach((client, clientId) => {
        const timeSinceLastPing = now.getTime() - client.lastPing.getTime();
        
        if (timeSinceLastPing > 60000) { // 1 minute timeout
          staleClients.push(clientId);
        } else if (client.ws.readyState === WebSocket.OPEN) {
          client.ws.ping();
        }
      });

      // Remove stale clients
      staleClients.forEach(clientId => {
        const client = this.clients.get(clientId);
        if (client) {
          client.ws.terminate();
          this.clients.delete(clientId);
        }
      });
    }, 30000); // Check every 30 seconds
  }

  /**
   * Get connection statistics
   */
  public getConnectionStats(): {
    totalConnections: number;
    activeConnections: number;
    strategiesCovered: string[];
  } {
    const activeConnections = Array.from(this.clients.values())
      .filter(client => client.ws.readyState === WebSocket.OPEN);

    const strategiesCovered = [...new Set(activeConnections.map(client => client.strategyId))];

    return {
      totalConnections: this.clients.size,
      activeConnections: activeConnections.length,
      strategiesCovered,
    };
  }

  /**
   * Shutdown WebSocket server
   */
  public shutdown(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }

    this.clients.forEach(client => {
      client.ws.terminate();
    });
    this.clients.clear();

    if (this.wss) {
      this.wss.close();
    }
  }
}