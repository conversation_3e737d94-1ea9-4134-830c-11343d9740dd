/**
 * LossLimitEnforcer Service Tests
 * 
 * Comprehensive test suite for loss limit tracking and enforcement functionality.
 * Tests daily/weekly limits, lockout mechanisms, and audit logging.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { LossLimitEnforcer, type LossLimitConfig, type LossTrackingData, type LossLimitViolation } from './LossLimitEnforcer';

describe('LossLimitEnforcer', () => {
  let enforcer: LossLimitEnforcer;
  const testUserId = 'test-user-123';
  const testAccountBalance = new Decimal(10000);

  const defaultConfig: LossLimitConfig = {
    dailyLimitPercentage: 2.0,
    weeklyLimitPercentage: 5.0,
    accountBalance: testAccountBalance,
    enableHardStops: true,
    enableAuditLogging: true
  };

  beforeEach(() => {
    enforcer = new LossLimitEnforcer();
    vi.clearAllMocks();
  });

  afterEach(() => {
    enforcer.removeAllListeners();
  });

  describe('User Initialization', () => {
    it('should initialize user with correct loss tracking data', () => {
      enforcer.initializeUser(testUserId, defaultConfig);
      
      const trackingData = enforcer.getUserLossData(testUserId);
      expect(trackingData).toBeDefined();
      expect(trackingData!.userId).toBe(testUserId);
      expect(trackingData!.currentDayLoss.toString()).toBe('0');
      expect(trackingData!.currentWeekLoss.toString()).toBe('0');
      expect(trackingData!.dailyLimitAmount.toString()).toBe('200');
      expect(trackingData!.weeklyLimitAmount.toString()).toBe('500');
      expect(trackingData!.isLocked).toBe(false);
    });

    it('should calculate daily limit amount correctly', () => {
      enforcer.initializeUser(testUserId, defaultConfig);
      const trackingData = enforcer.getUserLossData(testUserId);
      
      const expectedDailyLimit = testAccountBalance.mul(2.0).div(100);
      expect(trackingData!.dailyLimitAmount.equals(expectedDailyLimit)).toBe(true);
    });

    it('should calculate weekly limit amount correctly', () => {
      enforcer.initializeUser(testUserId, defaultConfig);
      const trackingData = enforcer.getUserLossData(testUserId);
      
      const expectedWeeklyLimit = testAccountBalance.mul(5.0).div(100);
      expect(trackingData!.weeklyLimitAmount.equals(expectedWeeklyLimit)).toBe(true);
    });

    it('should not reinitialize existing user', () => {
      enforcer.initializeUser(testUserId, defaultConfig);
      const originalData = enforcer.getUserLossData(testUserId);
      
      // Try to initialize again
      enforcer.initializeUser(testUserId, { ...defaultConfig, dailyLimitPercentage: 1.0 });
      const afterData = enforcer.getUserLossData(testUserId);
      
      expect(afterData!.dailyLimitAmount.equals(originalData!.dailyLimitAmount)).toBe(true);
    });
  });

  describe('Loss Recording', () => {
    beforeEach(() => {
      enforcer.initializeUser(testUserId, defaultConfig);
    });

    it('should record loss and update tracking data', () => {
      const lossAmount = new Decimal(50);
      
      const violation = enforcer.recordLoss(testUserId, lossAmount);
      const trackingData = enforcer.getUserLossData(testUserId);
      
      expect(violation).toBeNull(); // No violation for small loss
      expect(trackingData!.currentDayLoss.equals(lossAmount)).toBe(true);
      expect(trackingData!.currentWeekLoss.equals(lossAmount)).toBe(true);
      expect(trackingData!.dailyLimitRemaining.toString()).toBe('150'); // 200 - 50
      expect(trackingData!.weeklyLimitRemaining.toString()).toBe('450'); // 500 - 50
    });

    it('should accumulate multiple losses', () => {
      const loss1 = new Decimal(30);
      const loss2 = new Decimal(40);
      
      enforcer.recordLoss(testUserId, loss1);
      enforcer.recordLoss(testUserId, loss2);
      
      const trackingData = enforcer.getUserLossData(testUserId);
      const expectedTotal = loss1.add(loss2);
      
      expect(trackingData!.currentDayLoss.equals(expectedTotal)).toBe(true);
      expect(trackingData!.currentWeekLoss.equals(expectedTotal)).toBe(true);
    });

    it('should detect daily limit violation', () => {
      const lossAmount = new Decimal(250); // Exceeds 200 daily limit
      
      const violation = enforcer.recordLoss(testUserId, lossAmount);
      
      expect(violation).toBeDefined();
      expect(violation!.violationType).toBe('daily_limit');
      expect(violation!.triggerLockout).toBe(true);
      expect(violation!.percentageUsed).toBeGreaterThan(100);
    });

    it('should detect weekly limit violation', () => {
      // Build up to weekly limit violation across multiple days
      
      // Day 1: Loss under daily limit
      enforcer.recordLoss(testUserId, new Decimal(180));
      
      // Day 2: Reset daily, add more loss  
      const trackingData1 = enforcer.getUserLossData(testUserId);
      if (trackingData1) {
        trackingData1.lastResetDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // Yesterday
        trackingData1.currentDayLoss = new Decimal(0);
        trackingData1.dailyLimitRemaining = trackingData1.dailyLimitAmount;
        enforcer['lossTrackingData'].set(testUserId, trackingData1);
      }
      
      enforcer.recordLoss(testUserId, new Decimal(190));
      
      // Day 3: Reset daily, add loss that pushes over weekly limit
      const trackingData2 = enforcer.getUserLossData(testUserId);
      if (trackingData2) {
        trackingData2.lastResetDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // Yesterday
        trackingData2.currentDayLoss = new Decimal(0);
        trackingData2.dailyLimitRemaining = trackingData2.dailyLimitAmount;
        enforcer['lossTrackingData'].set(testUserId, trackingData2);
      }
      
      // This should violate weekly limit (180 + 190 + 140 = 510, exceeding 500)
      const violation = enforcer.recordLoss(testUserId, new Decimal(140));
      
      expect(violation).toBeDefined();
      expect(violation!.violationType).toBe('weekly_limit');
      expect(violation!.triggerLockout).toBe(true);
    });

    it('should emit limitViolation event', () => {
      const eventSpy = vi.fn();
      enforcer.on('limitViolation', eventSpy);
      
      const lossAmount = new Decimal(250);
      enforcer.recordLoss(testUserId, lossAmount);
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          violationType: 'daily_limit',
          triggerLockout: true
        })
      );
    });

    it('should throw error for uninitialized user', () => {
      const uninitializedUser = 'uninitialized-user';
      const lossAmount = new Decimal(50);
      
      expect(() => {
        enforcer.recordLoss(uninitializedUser, lossAmount);
      }).toThrow(`User ${uninitializedUser} not initialized for loss tracking`);
    });
  });

  describe('Trade Impact Analysis', () => {
    beforeEach(() => {
      enforcer.initializeUser(testUserId, defaultConfig);
    });

    it('should allow trade within limits', () => {
      const potentialLoss = new Decimal(50);
      const tradeId = 'trade-123';
      
      const impact = enforcer.checkTradeImpact(testUserId, potentialLoss, tradeId);
      
      expect(impact.allowTrade).toBe(true);
      expect(impact.wouldViolateDaily).toBe(false);
      expect(impact.wouldViolateWeekly).toBe(false);
      expect(impact.warningMessage).toBeUndefined();
    });

    it('should block trade that would exceed daily limit', () => {
      // First add some existing losses
      enforcer.recordLoss(testUserId, new Decimal(150));
      
      const potentialLoss = new Decimal(100); // Would total 250, exceeding daily limit of 200
      const tradeId = 'trade-456';
      
      const impact = enforcer.checkTradeImpact(testUserId, potentialLoss, tradeId);
      
      expect(impact.allowTrade).toBe(false);
      expect(impact.wouldViolateDaily).toBe(true);
      expect(impact.wouldViolateWeekly).toBe(false);
      expect(impact.warningMessage).toContain('daily loss limit');
    });

    it('should block trade that would exceed weekly limit', () => {
      // Add existing losses approaching weekly limit
      enforcer.recordLoss(testUserId, new Decimal(350));
      
      // Reset daily limit to test weekly separately - manually reset daily tracking
      const trackingData = enforcer.getUserLossData(testUserId);
      if (trackingData) {
        trackingData.lastResetDate = new Date(Date.now() - 25 * 60 * 60 * 1000);
        trackingData.currentDayLoss = new Decimal(0); // Reset daily loss
        trackingData.dailyLimitRemaining = trackingData.dailyLimitAmount;
        enforcer['lossTrackingData'].set(testUserId, trackingData);
      }
      
      const potentialLoss = new Decimal(180); // Would total 530, exceeding weekly limit of 500
      const tradeId = 'trade-789';
      
      const impact = enforcer.checkTradeImpact(testUserId, potentialLoss, tradeId);
      
      expect(impact.allowTrade).toBe(false);
      expect(impact.wouldViolateWeekly).toBe(true);
      expect(impact.warningMessage).toContain('weekly loss limit');
    });

    it('should block trade when account is locked', () => {
      // Trigger a lockout
      enforcer.recordLoss(testUserId, new Decimal(250));
      
      const potentialLoss = new Decimal(10);
      const tradeId = 'trade-locked';
      
      const impact = enforcer.checkTradeImpact(testUserId, potentialLoss, tradeId);
      
      expect(impact.allowTrade).toBe(false);
      // The trade might be blocked for daily limit violation first, but account should be locked
      expect(impact.warningMessage).toMatch(/daily loss limit|Account is locked/);
    });
  });

  describe('Account Balance Updates', () => {
    beforeEach(() => {
      enforcer.initializeUser(testUserId, defaultConfig);
    });

    it('should update limits when account balance changes', () => {
      const newBalance = new Decimal(20000);
      
      enforcer.updateAccountBalance(testUserId, newBalance);
      const trackingData = enforcer.getUserLossData(testUserId);
      
      expect(trackingData!.dailyLimitAmount.toString()).toBe('400'); // 2% of 20000
      expect(trackingData!.weeklyLimitAmount.toString()).toBe('1000'); // 5% of 20000
    });

    it('should recalculate remaining limits', () => {
      // Add some losses
      enforcer.recordLoss(testUserId, new Decimal(50));
      
      const newBalance = new Decimal(20000);
      enforcer.updateAccountBalance(testUserId, newBalance);
      
      const trackingData = enforcer.getUserLossData(testUserId);
      expect(trackingData!.dailyLimitRemaining.toString()).toBe('350'); // 400 - 50
      expect(trackingData!.weeklyLimitRemaining.toString()).toBe('950'); // 1000 - 50
    });
  });

  describe('Time-based Reset Logic', () => {
    beforeEach(() => {
      enforcer.initializeUser(testUserId, defaultConfig);
    });

    it('should reset daily limits at start of new day', () => {
      // Add losses
      enforcer.recordLoss(testUserId, new Decimal(100));
      
      // Simulate next day by manually setting reset date to yesterday
      const trackingData = enforcer.getUserLossData(testUserId);
      if (trackingData) {
        trackingData.lastResetDate = new Date(Date.now() - 25 * 60 * 60 * 1000);
        enforcer['lossTrackingData'].set(testUserId, trackingData);
      }
      
      // Check data - should trigger reset
      const resetData = enforcer.getUserLossData(testUserId);
      
      expect(resetData!.currentDayLoss.toString()).toBe('0');
      expect(resetData!.dailyLimitRemaining.equals(resetData!.dailyLimitAmount)).toBe(true);
      expect(resetData!.currentWeekLoss.toString()).toBe('100'); // Weekly not reset
    });

    it('should reset weekly limits at start of new week', () => {
      // Add losses
      enforcer.recordLoss(testUserId, new Decimal(100));
      
      // Simulate next week
      const trackingData = enforcer.getUserLossData(testUserId);
      if (trackingData) {
        trackingData.lastWeekResetDate = new Date(Date.now() - 8 * 24 * 60 * 60 * 1000);
        enforcer['lossTrackingData'].set(testUserId, trackingData);
      }
      
      const resetData = enforcer.getUserLossData(testUserId);
      
      expect(resetData!.currentWeekLoss.toString()).toBe('0');
      expect(resetData!.weeklyLimitRemaining.equals(resetData!.weeklyLimitAmount)).toBe(true);
    });

    it('should remove daily lockout on daily reset', () => {
      // Trigger daily lockout
      enforcer.recordLoss(testUserId, new Decimal(250));
      
      let trackingData = enforcer.getUserLossData(testUserId);
      expect(trackingData!.isLocked).toBe(true);
      expect(trackingData!.lockoutReason).toBe('daily_limit');
      
      // Simulate next day
      if (trackingData) {
        trackingData.lastResetDate = new Date(Date.now() - 25 * 60 * 60 * 1000);
        enforcer['lossTrackingData'].set(testUserId, trackingData);
      }
      
      trackingData = enforcer.getUserLossData(testUserId);
      expect(trackingData!.isLocked).toBe(false);
      expect(trackingData!.lockoutReason).toBeUndefined();
    });
  });

  describe('Force Reset Functionality', () => {
    beforeEach(() => {
      enforcer.initializeUser(testUserId, defaultConfig);
    });

    it('should force reset all limits', () => {
      // Add losses and trigger lockout
      enforcer.recordLoss(testUserId, new Decimal(250));
      
      enforcer.forceResetLimits(testUserId);
      const trackingData = enforcer.getUserLossData(testUserId);
      
      expect(trackingData!.currentDayLoss.toString()).toBe('0');
      expect(trackingData!.currentWeekLoss.toString()).toBe('0');
      expect(trackingData!.isLocked).toBe(false);
      expect(trackingData!.lockoutReason).toBeUndefined();
      expect(trackingData!.lockoutUntil).toBeUndefined();
    });

    it('should emit limitsReset event on force reset', () => {
      const eventSpy = vi.fn();
      enforcer.on('limitsReset', eventSpy);
      
      enforcer.forceResetLimits(testUserId);
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          type: 'forced'
        })
      );
    });
  });

  describe('Audit Logging', () => {
    beforeEach(() => {
      enforcer.initializeUser(testUserId, defaultConfig);
    });

    it('should log loss recording events', () => {
      const lossAmount = new Decimal(50);
      
      enforcer.recordLoss(testUserId, lossAmount);
      const auditLog = enforcer.getAuditLog(testUserId);
      
      const lossEntry = auditLog.find(entry => entry.action === 'loss_recorded');
      expect(lossEntry).toBeDefined();
      expect(lossEntry!.userId).toBe(testUserId);
      expect(lossEntry!.details.lossAmount).toBe(lossAmount.toString());
    });

    it('should log limit violations', () => {
      const lossAmount = new Decimal(250);
      
      enforcer.recordLoss(testUserId, lossAmount);
      const auditLog = enforcer.getAuditLog(testUserId);
      
      const violationEntry = auditLog.find(entry => 
        entry.action === 'loss_recorded' && 
        entry.details.violation === 'daily_limit'
      );
      expect(violationEntry).toBeDefined();
    });

    it('should log account lockouts', () => {
      const lossAmount = new Decimal(250);
      
      enforcer.recordLoss(testUserId, lossAmount);
      const auditLog = enforcer.getAuditLog(testUserId);
      
      const lockoutEntry = auditLog.find(entry => entry.action === 'account_locked');
      expect(lockoutEntry).toBeDefined();
      expect(lockoutEntry!.details.reason).toBe('daily_limit');
    });

    it('should filter audit log by date range', () => {
      const lossAmount = new Decimal(50);
      
      // Record loss
      enforcer.recordLoss(testUserId, lossAmount);
      
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const tomorrow = new Date(Date.now() + 24 * 60 * 60 * 1000);
      
      // Should include entries from today
      const todayLog = enforcer.getAuditLog(testUserId, yesterday, tomorrow);
      expect(todayLog.length).toBeGreaterThan(0);
      
      // Should exclude entries from today
      const pastLog = enforcer.getAuditLog(testUserId, new Date(Date.now() - 48 * 60 * 60 * 1000), yesterday);
      const relevantEntries = pastLog.filter(entry => entry.action === 'loss_recorded');
      expect(relevantEntries.length).toBe(0);
    });
  });

  describe('Warning Thresholds', () => {
    beforeEach(() => {
      enforcer.initializeUser(testUserId, defaultConfig);
    });

    it('should detect approaching daily limit', () => {
      const lossAmount = new Decimal(185); // 92.5% of daily limit (200)
      
      const violation = enforcer.recordLoss(testUserId, lossAmount);
      
      expect(violation).toBeDefined();
      expect(violation!.violationType).toBe('approaching_daily');
      expect(violation!.triggerLockout).toBe(false);
      expect(violation!.percentageUsed).toBeGreaterThan(90);
    });

    it('should detect approaching weekly limit', () => {
      // Build up weekly loss gradually over multiple days without hitting daily limit
      
      // Day 1: Loss under daily limit
      enforcer.recordLoss(testUserId, new Decimal(150));
      
      // Day 2: Reset daily, add more loss
      const trackingData1 = enforcer.getUserLossData(testUserId);
      if (trackingData1) {
        trackingData1.lastResetDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // Yesterday
        trackingData1.currentDayLoss = new Decimal(0);
        trackingData1.dailyLimitRemaining = trackingData1.dailyLimitAmount;
        enforcer['lossTrackingData'].set(testUserId, trackingData1);
      }
      
      enforcer.recordLoss(testUserId, new Decimal(180));
      
      // Day 3: Reset daily, add more loss to approach weekly limit
      const trackingData2 = enforcer.getUserLossData(testUserId);
      if (trackingData2) {
        trackingData2.lastResetDate = new Date(Date.now() - 25 * 60 * 60 * 1000); // Yesterday
        trackingData2.currentDayLoss = new Decimal(0);
        trackingData2.dailyLimitRemaining = trackingData2.dailyLimitAmount;
        enforcer['lossTrackingData'].set(testUserId, trackingData2);
      }
      
      // This should approach weekly limit (150 + 180 + 120 = 450, which is 90% of 500)
      const violation = enforcer.recordLoss(testUserId, new Decimal(120));
      
      expect(violation).toBeDefined();
      expect(violation!.violationType).toBe('approaching_weekly');
      expect(violation!.triggerLockout).toBe(false);
    });
  });

  describe('Configuration Validation', () => {
    it('should validate valid configuration', () => {
      const validConfig: LossLimitConfig = {
        dailyLimitPercentage: 2.0,
        weeklyLimitPercentage: 5.0,
        accountBalance: new Decimal(10000),
        enableHardStops: true,
        enableAuditLogging: true
      };
      
      const result = LossLimitEnforcer.validateConfig(validConfig);
      expect(result.isValid).toBe(true);
      expect(result.errors.length).toBe(0);
    });

    it('should reject negative daily limit', () => {
      const invalidConfig: LossLimitConfig = {
        ...defaultConfig,
        dailyLimitPercentage: -1.0
      };
      
      const result = LossLimitEnforcer.validateConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Daily limit percentage must be between 0.1% and 50%');
    });

    it('should reject excessive limits', () => {
      const invalidConfig: LossLimitConfig = {
        ...defaultConfig,
        dailyLimitPercentage: 60.0,
        weeklyLimitPercentage: 150.0
      };
      
      const result = LossLimitEnforcer.validateConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.some(e => e.includes('Daily limit percentage must be between 0.1% and 50%'))).toBe(true);
      expect(result.errors.some(e => e.includes('Weekly limit percentage must be between 0.1% and 100%'))).toBe(true);
    });

    it('should reject daily limit >= weekly limit', () => {
      const invalidConfig: LossLimitConfig = {
        ...defaultConfig,
        dailyLimitPercentage: 5.0,
        weeklyLimitPercentage: 3.0
      };
      
      const result = LossLimitEnforcer.validateConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Daily limit must be less than weekly limit');
    });

    it('should reject zero or negative account balance', () => {
      const invalidConfig: LossLimitConfig = {
        ...defaultConfig,
        accountBalance: new Decimal(0)
      };
      
      const result = LossLimitEnforcer.validateConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Account balance must be positive');
    });
  });

  describe('Event Emission', () => {
    beforeEach(() => {
      enforcer.initializeUser(testUserId, defaultConfig);
    });

    it('should emit accountLocked event on lockout', () => {
      const eventSpy = vi.fn();
      enforcer.on('accountLocked', eventSpy);
      
      enforcer.recordLoss(testUserId, new Decimal(250));
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          reason: 'daily_limit'
        })
      );
    });

    it('should emit auditLog event for all entries', () => {
      const eventSpy = vi.fn();
      enforcer.on('auditLog', eventSpy);
      
      enforcer.recordLoss(testUserId, new Decimal(50));
      
      expect(eventSpy).toHaveBeenCalled();
      expect(eventSpy.mock.calls[0][0]).toMatchObject({
        userId: testUserId,
        action: 'loss_recorded'
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle operations on uninitialized user gracefully', () => {
      const uninitializedUser = 'uninitialized-user';
      
      expect(() => enforcer.getUserLossData(uninitializedUser)).not.toThrow();
      expect(enforcer.getUserLossData(uninitializedUser)).toBeNull();
      
      expect(() => enforcer.updateAccountBalance(uninitializedUser, new Decimal(1000))).toThrow();
      expect(() => enforcer.forceResetLimits(uninitializedUser)).toThrow();
    });

    it('should handle invalid loss amounts gracefully', () => {
      enforcer.initializeUser(testUserId, defaultConfig);
      
      // Negative loss should still be processed (as it affects the calculation)
      const result = enforcer.recordLoss(testUserId, new Decimal(-50));
      expect(result).toBeNull(); // No violation for negative loss
    });
  });
});