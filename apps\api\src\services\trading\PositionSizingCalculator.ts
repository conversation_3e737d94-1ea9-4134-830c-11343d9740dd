/**
 * Position Sizing Calculator Utility
 * 
 * Provides multiple position sizing algorithms (fixed percentage, volatility-based, Kelly)
 * with account balance and correlation risk considerations.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';

// Position Sizing Types
export interface PositionSizingConfig {
  algorithm: 'fixed_percentage' | 'volatility_based' | 'kelly_criterion' | 'equal_weight';
  riskPercentage: number; // Base risk percentage per position
  maxPositionPercentage: number; // Maximum position size as percentage of account
  volatilityLookbackDays: number; // Days for volatility calculation
  correlationThreshold: number; // Threshold for correlation-based adjustments
  diversificationTarget: number; // Target number of positions for diversification
}

export interface PositionDetails {
  symbol: string;
  currentPrice: Decimal.Instance;
  stopLoss: Decimal.Instance;
  takeProfit?: Decimal.Instance;
  marketVolatility: number; // Annualized volatility percentage
  correlationWithPortfolio: number; // -1 to 1
  averageTrueRange: Decimal.Instance; // ATR for volatility-based sizing
}

export interface ExistingPosition {
  symbol: string;
  size: Decimal.Instance;
  marketValue: Decimal.Instance;
  unrealizedPnL: Decimal.Instance;
  weight: number; // Portfolio weight percentage
}

export interface PositionSizeRecommendation {
  recommendedShares: Decimal.Instance;
  positionValue: Decimal.Instance;
  riskAmount: Decimal.Instance;
  portfolioWeight: number;
  algorithm: string;
  adjustments: PositionSizeAdjustment[];
  riskMetrics: {
    potentialLoss: Decimal.Instance;
    riskRewardRatio: number;
    correlationImpact: number;
    diversificationScore: number;
  };
  reasoning: string[];
}

export interface PositionSizeAdjustment {
  type: 'volatility' | 'correlation' | 'concentration' | 'max_limit' | 'min_size';
  factor: number;
  reason: string;
  impact: 'increase' | 'decrease' | 'neutral';
}

export interface RiskBudgetAllocation {
  totalRiskBudget: Decimal.Instance;
  allocatedRisk: Decimal.Instance;
  remainingRisk: Decimal.Instance;
  utilizationPercentage: number;
  positionRiskAllocations: Array<{
    symbol: string;
    allocatedRisk: Decimal.Instance;
    percentOfBudget: number;
  }>;
}

/**
 * PositionSizingCalculator - Implements multiple position sizing strategies
 */
export class PositionSizingCalculator {
  private config: PositionSizingConfig;

  constructor(config: PositionSizingConfig) {
    this.config = config;
    this.validateConfig();
  }

  /**
   * Calculate optimal position size for a new position
   */
  public calculatePositionSize(
    accountBalance: Decimal,
    positionDetails: PositionDetails,
    existingPositions: ExistingPosition[] = []
  ): PositionSizeRecommendation {
    const reasoning: string[] = [];
    const adjustments: PositionSizeAdjustment[] = [];

    // Calculate base position size using selected algorithm
    let basePositionSize: Decimal.Instance;
    let baseRiskAmount: Decimal.Instance;
    
    switch (this.config.algorithm) {
      case 'fixed_percentage':
        ({ positionSize: basePositionSize, riskAmount: baseRiskAmount } = 
          this.calculateFixedPercentageSize(accountBalance, positionDetails));
        reasoning.push(`Using fixed percentage algorithm (${this.config.riskPercentage}%)`);
        break;
        
      case 'volatility_based':
        ({ positionSize: basePositionSize, riskAmount: baseRiskAmount } = 
          this.calculateVolatilityBasedSize(accountBalance, positionDetails));
        reasoning.push('Using volatility-based sizing algorithm');
        break;
        
      case 'kelly_criterion':
        ({ positionSize: basePositionSize, riskAmount: baseRiskAmount } = 
          this.calculateKellySize(accountBalance, positionDetails));
        reasoning.push('Using Kelly Criterion algorithm');
        break;
        
      case 'equal_weight':
        ({ positionSize: basePositionSize, riskAmount: baseRiskAmount } = 
          this.calculateEqualWeightSize(accountBalance, positionDetails, existingPositions));
        reasoning.push('Using equal weight algorithm');
        break;
        
      default:
        throw new Error(`Unsupported algorithm: ${this.config.algorithm}`);
    }

    // Apply correlation adjustments
    const correlationAdjustment = this.calculateCorrelationAdjustment(
      positionDetails.correlationWithPortfolio, 
      existingPositions
    );
    
    if (correlationAdjustment.factor !== 1.0) {
      adjustments.push(correlationAdjustment);
      basePositionSize = basePositionSize.mul(correlationAdjustment.factor);
      reasoning.push(`Applied correlation adjustment: ${correlationAdjustment.factor}x`);
    }

    // Apply concentration limits
    const concentrationAdjustment = this.calculateConcentrationAdjustment(
      basePositionSize, 
      positionDetails.currentPrice, 
      accountBalance,
      existingPositions
    );
    
    if (concentrationAdjustment.factor !== 1.0) {
      adjustments.push(concentrationAdjustment);
      basePositionSize = basePositionSize.mul(concentrationAdjustment.factor);
      reasoning.push(`Applied concentration limit: ${concentrationAdjustment.factor}x`);
    }

    // Apply maximum position size limit
    const maxPositionValue = accountBalance.mul(this.config.maxPositionPercentage).div(100);
    const maxShares = maxPositionValue.div(positionDetails.currentPrice);
    
    if (basePositionSize.gt(maxShares)) {
      adjustments.push({
        type: 'max_limit',
        factor: maxShares.div(basePositionSize).toNumber(),
        reason: `Position exceeds maximum limit of ${this.config.maxPositionPercentage}%`,
        impact: 'decrease'
      });
      basePositionSize = maxShares;
      reasoning.push(`Applied maximum position limit: ${this.config.maxPositionPercentage}%`);
    }

    const finalPositionValue = basePositionSize.mul(positionDetails.currentPrice);
    const portfolioWeight = finalPositionValue.div(accountBalance).mul(100).toNumber();

    // Calculate risk metrics
    const potentialLoss = this.calculatePotentialLoss(basePositionSize, positionDetails);
    const riskRewardRatio = this.calculateRiskRewardRatio(positionDetails);
    const diversificationScore = this.calculateDiversificationScore(existingPositions);

    return {
      recommendedShares: basePositionSize,
      positionValue: finalPositionValue,
      riskAmount: baseRiskAmount,
      portfolioWeight,
      algorithm: this.config.algorithm,
      adjustments,
      riskMetrics: {
        potentialLoss,
        riskRewardRatio,
        correlationImpact: positionDetails.correlationWithPortfolio,
        diversificationScore
      },
      reasoning
    };
  }

  /**
   * Allocate risk budget across multiple positions
   */
  public allocateRiskBudget(
    accountBalance: Decimal,
    proposedPositions: PositionDetails[],
    existingPositions: ExistingPosition[] = []
  ): RiskBudgetAllocation {
    const totalRiskBudget = accountBalance.mul(this.config.riskPercentage).div(100);
    
    // Calculate current risk allocation from existing positions
    const existingRisk = existingPositions.reduce((sum, pos) => {
      // Estimate risk as 2% of position value (simplified)
      const estimatedRisk = pos.marketValue.mul(0.02);
      return sum.add(estimatedRisk);
    }, new Decimal(0));

    const remainingBudget = totalRiskBudget.sub(existingRisk);
    
    // Allocate remaining budget among proposed positions
    const positionRiskAllocations = proposedPositions.map((position, _index) => {
      // Equal allocation with correlation adjustments
      const baseAllocation = remainingBudget.div(proposedPositions.length);
      
      // Adjust for correlation - reduce allocation for highly correlated positions
      const correlationFactor = 1 - Math.abs(position.correlationWithPortfolio) * 0.3;
      const adjustedAllocation = baseAllocation.mul(correlationFactor);

      return {
        symbol: position.symbol,
        allocatedRisk: adjustedAllocation,
        percentOfBudget: adjustedAllocation.div(totalRiskBudget).mul(100).toNumber()
      };
    });

    const totalAllocated = existingRisk.add(
      positionRiskAllocations.reduce((sum, alloc) => sum.add(alloc.allocatedRisk), new Decimal(0))
    );

    return {
      totalRiskBudget,
      allocatedRisk: totalAllocated,
      remainingRisk: totalRiskBudget.sub(totalAllocated),
      utilizationPercentage: totalAllocated.div(totalRiskBudget).mul(100).toNumber(),
      positionRiskAllocations
    };
  }

  /**
   * Fixed percentage position sizing
   */
  private calculateFixedPercentageSize(
    accountBalance: Decimal,
    positionDetails: PositionDetails
  ): { positionSize: Decimal.Instance; riskAmount: Decimal } {
    const riskAmount = accountBalance.mul(this.config.riskPercentage).div(100);
    const riskPerShare = positionDetails.currentPrice.sub(positionDetails.stopLoss).abs();
    
    if (riskPerShare.eq(0)) {
      throw new Error('Stop loss cannot equal current price');
    }
    
    const positionSize = riskAmount.div(riskPerShare);
    
    return { positionSize, riskAmount };
  }

  /**
   * Volatility-based position sizing using ATR
   */
  private calculateVolatilityBasedSize(
    accountBalance: Decimal,
    positionDetails: PositionDetails
  ): { positionSize: Decimal.Instance; riskAmount: Decimal } {
    // Use ATR-based position sizing
    const atrMultiplier = 2.0; // Standard 2x ATR for stop loss
    const riskPerShare = positionDetails.averageTrueRange.mul(atrMultiplier);
    
    // Adjust risk amount based on volatility
    const volatilityAdjustment = Math.max(0.5, Math.min(2.0, 20 / positionDetails.marketVolatility));
    const adjustedRiskPercentage = this.config.riskPercentage * volatilityAdjustment;
    
    const riskAmount = accountBalance.mul(adjustedRiskPercentage).div(100);
    const positionSize = riskAmount.div(riskPerShare);
    
    return { positionSize, riskAmount };
  }

  /**
   * Kelly Criterion position sizing (simplified)
   */
  private calculateKellySize(
    accountBalance: Decimal,
    positionDetails: PositionDetails
  ): { positionSize: Decimal.Instance; riskAmount: Decimal } {
    // Simplified Kelly calculation based on risk-reward ratio
    const riskRewardRatio = this.calculateRiskRewardRatio(positionDetails);
    const estimatedWinProbability = 0.55; // Assume 55% win rate (should be historical)
    
    // Kelly fraction = (bp - q) / b, where b = odds, p = win prob, q = loss prob
    const kellyFraction = ((riskRewardRatio * estimatedWinProbability) - (1 - estimatedWinProbability)) / riskRewardRatio;
    
    // Apply maximum Kelly percentage limit (usually 25%)
    const maxKellyPercentage = 0.25;
    const kellyPercentage = Math.max(0, Math.min(kellyFraction, maxKellyPercentage));
    
    const positionValue = accountBalance.mul(kellyPercentage);
    const positionSize = positionValue.div(positionDetails.currentPrice);
    const riskAmount = positionValue.mul(0.02); // Estimate 2% risk
    
    return { positionSize, riskAmount };
  }

  /**
   * Equal weight position sizing
   */
  private calculateEqualWeightSize(
    accountBalance: Decimal,
    positionDetails: PositionDetails,
    _existingPositions: ExistingPosition[]
  ): { positionSize: Decimal.Instance; riskAmount: Decimal } {
    const targetPositions = this.config.diversificationTarget;
    const targetWeight = new Decimal(1).div(targetPositions);
    
    const positionValue = accountBalance.mul(targetWeight);
    const positionSize = positionValue.div(positionDetails.currentPrice);
    const riskAmount = positionValue.mul(0.02); // Estimate 2% risk
    
    return { positionSize, riskAmount };
  }

  /**
   * Calculate correlation-based position size adjustment
   */
  private calculateCorrelationAdjustment(
    correlation: number,
    _existingPositions: ExistingPosition[]
  ): PositionSizeAdjustment {
    const absCorrelation = Math.abs(correlation);
    
    if (absCorrelation > this.config.correlationThreshold) {
      // Reduce position size for highly correlated assets
      const reductionFactor = 1 - (absCorrelation - this.config.correlationThreshold) * 0.5;
      
      return {
        type: 'correlation',
        factor: Math.max(0.5, reductionFactor), // Minimum 50% of original size
        reason: `High correlation (${(absCorrelation * 100).toFixed(1)}%) with existing positions`,
        impact: 'decrease'
      };
    }
    
    return {
      type: 'correlation',
      factor: 1.0,
      reason: 'Low correlation with existing positions',
      impact: 'neutral'
    };
  }

  /**
   * Calculate concentration-based adjustment
   */
  private calculateConcentrationAdjustment(
    proposedShares: Decimal,
    currentPrice: Decimal,
    accountBalance: Decimal,
    _existingPositions: ExistingPosition[]
  ): PositionSizeAdjustment {
    const proposedValue = proposedShares.mul(currentPrice);
    const proposedWeight = proposedValue.div(accountBalance).toNumber();
    
    // Check if position would create too much concentration
    const maxSinglePositionWeight = 0.20; // 20% maximum per position
    
    if (proposedWeight > maxSinglePositionWeight) {
      const adjustmentFactor = maxSinglePositionWeight / proposedWeight;
      
      return {
        type: 'concentration',
        factor: adjustmentFactor,
        reason: `Position would exceed maximum concentration limit of ${maxSinglePositionWeight * 100}%`,
        impact: 'decrease'
      };
    }
    
    return {
      type: 'concentration',
      factor: 1.0,
      reason: 'Within concentration limits',
      impact: 'neutral'
    };
  }

  /**
   * Calculate potential loss for the position
   */
  private calculatePotentialLoss(shares: Decimal, positionDetails: PositionDetails): Decimal {
    const lossPerShare = positionDetails.currentPrice.sub(positionDetails.stopLoss).abs();
    return shares.mul(lossPerShare);
  }

  /**
   * Calculate risk-reward ratio
   */
  private calculateRiskRewardRatio(positionDetails: PositionDetails): number {
    if (!positionDetails.takeProfit) {
      return 2.0; // Default 2:1 ratio
    }
    
    const riskPerShare = positionDetails.currentPrice.sub(positionDetails.stopLoss).abs();
    const rewardPerShare = positionDetails.takeProfit.sub(positionDetails.currentPrice).abs();
    
    if (riskPerShare.eq(0)) return 0;
    
    return rewardPerShare.div(riskPerShare).toNumber();
  }

  /**
   * Calculate portfolio diversification score
   */
  private calculateDiversificationScore(existingPositions: ExistingPosition[]): number {
    if (existingPositions.length <= 1) return 0;
    
    // Calculate Herfindahl-Hirschman Index
    const hhi = existingPositions.reduce((sum, pos) => {
      return sum + (pos.weight * pos.weight / 10000); // Convert percentage to decimal
    }, 0);
    
    // Convert to diversification score (0-1, higher is more diversified)
    const maxHHI = 1.0; // Fully concentrated
    const minHHI = 1 / existingPositions.length; // Perfectly diversified
    
    return Math.max(0, (maxHHI - hhi) / (maxHHI - minHHI));
  }

  /**
   * Validate configuration parameters
   */
  private validateConfig(): void {
    if (this.config.riskPercentage <= 0 || this.config.riskPercentage > 100) {
      throw new Error('Risk percentage must be between 0 and 100');
    }
    
    if (this.config.maxPositionPercentage <= 0 || this.config.maxPositionPercentage > 100) {
      throw new Error('Maximum position percentage must be between 0 and 100');
    }
    
    if (this.config.correlationThreshold < 0 || this.config.correlationThreshold > 1) {
      throw new Error('Correlation threshold must be between 0 and 1');
    }
    
    if (this.config.diversificationTarget < 1) {
      throw new Error('Diversification target must be at least 1');
    }
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<PositionSizingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.validateConfig();
  }

  /**
   * Get current configuration
   */
  public getConfig(): PositionSizingConfig {
    return { ...this.config };
  }
}

/**
 * Factory function to create PositionSizingCalculator with default config
 */
export function createPositionSizingCalculator(
  customConfig?: Partial<PositionSizingConfig>
): PositionSizingCalculator {
  const defaultConfig: PositionSizingConfig = {
    algorithm: 'fixed_percentage',
    riskPercentage: 2.0,
    maxPositionPercentage: 20.0,
    volatilityLookbackDays: 20,
    correlationThreshold: 0.7,
    diversificationTarget: 10
  };
  
  const config = { ...defaultConfig, ...customConfig };
  return new PositionSizingCalculator(config);
}

/**
 * Default export
 */
export default PositionSizingCalculator;