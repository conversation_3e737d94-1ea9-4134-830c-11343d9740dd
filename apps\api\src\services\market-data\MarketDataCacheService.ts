/**
 * Market Data Cache Service with SQLite
 * 
 * Provides local caching for offline capability, performance optimization,
 * and bidirectional synchronization with TimescaleDB.
 */

import Database from 'better-sqlite3';
import { EventEmitter } from 'events';
import Decimal from 'decimal.js';
import { NormalizedMarketData, DataSource, TimeFrame } from './RealTimeDataProcessor';

export interface CacheConfig {
  databasePath: string;
  maxCacheSize: number; // MB
  defaultTTL: number; // seconds
  syncInterval: number; // seconds
  maxRetries: number;
  enableCompression: boolean;
  enableMetrics: boolean;
  vacuumInterval: number; // seconds
}

export interface CacheEntry {
  id: string;
  key: string;
  data: NormalizedMarketData;
  createdAt: Date;
  expiresAt: Date;
  accessCount: number;
  lastAccessedAt: Date;
  isCompressed: boolean;
  compressedSize?: number;
  originalSize: number;
}

export interface CacheStats {
  totalEntries: number;
  totalSize: number; // bytes
  hitCount: number;
  missCount: number;
  evictionCount: number;
  hitRatio: number;
  memoryUsage: number;
  lastSync: Date;
  syncErrors: number;
  avgQueryTime: number;
}

export interface SyncStats {
  lastSyncAt: Date;
  recordsSynced: number;
  syncDuration: number;
  conflictsResolved: number;
  errorsEncountered: number;
}

/**
 * SQLite-based market data cache with TimescaleDB synchronization
 */
export class MarketDataCacheService extends EventEmitter {
  private db: Database | null = null;
  private config: CacheConfig;
  private stats: CacheStats;
  private syncStats: SyncStats;
  private syncInterval: NodeJS.Timeout | null = null;
  private vacuumInterval: NodeJS.Timeout | null = null;
  private isInitialized: boolean = false;

  constructor(config: CacheConfig) {
    super();
    this.config = config;
    this.stats = this.initializeStats();
    this.syncStats = this.initializeSyncStats();
  }

  /**
   * Initialize cache statistics
   */
  private initializeStats(): CacheStats {
    return {
      totalEntries: 0,
      totalSize: 0,
      hitCount: 0,
      missCount: 0,
      evictionCount: 0,
      hitRatio: 0,
      memoryUsage: 0,
      lastSync: new Date(),
      syncErrors: 0,
      avgQueryTime: 0,
    };
  }

  /**
   * Initialize sync statistics
   */
  private initializeSyncStats(): SyncStats {
    return {
      lastSyncAt: new Date(),
      recordsSynced: 0,
      syncDuration: 0,
      conflictsResolved: 0,
      errorsEncountered: 0,
    };
  }

  /**
   * Initialize SQLite database and start background processes
   */
  public async initialize(): Promise<void> {
    try {
      this.db = new Database(this.config.databasePath);
      
      // Create database schema
      this.createSchema();
      
      // Start background processes
      this.startSyncProcess();
      this.startVacuumProcess();
      
      this.isInitialized = true;
      this.emit('initialized');
      
    } catch (error) {
      this.emit('error', { operation: 'initialize', error });
      throw error;
    }
  }

  /**
   * Create SQLite database schema
   */
  private createSchema(): void {
    const queries = [
      `CREATE TABLE IF NOT EXISTS market_data_cache (
        id TEXT PRIMARY KEY,
        cache_key TEXT NOT NULL UNIQUE,
        source TEXT NOT NULL,
        instrument TEXT NOT NULL,
        timeframe TEXT NOT NULL,
        timestamp INTEGER NOT NULL,
        open_price TEXT NOT NULL,
        high_price TEXT NOT NULL,
        low_price TEXT NOT NULL,
        close_price TEXT NOT NULL,
        volume TEXT NOT NULL,
        spread TEXT,
        bid_price TEXT,
        ask_price TEXT,
        quality_score INTEGER NOT NULL,
        is_compressed INTEGER DEFAULT 0,
        compressed_data BLOB,
        original_size INTEGER NOT NULL,
        compressed_size INTEGER,
        created_at INTEGER NOT NULL,
        expires_at INTEGER NOT NULL,
        access_count INTEGER DEFAULT 0,
        last_accessed_at INTEGER NOT NULL,
        sync_status TEXT DEFAULT 'pending',
        sync_timestamp INTEGER
      )`,
      
      `CREATE INDEX IF NOT EXISTS idx_cache_key ON market_data_cache(cache_key)`,
      `CREATE INDEX IF NOT EXISTS idx_instrument_timeframe ON market_data_cache(instrument, timeframe)`,
      `CREATE INDEX IF NOT EXISTS idx_timestamp ON market_data_cache(timestamp)`,
      `CREATE INDEX IF NOT EXISTS idx_expires_at ON market_data_cache(expires_at)`,
      `CREATE INDEX IF NOT EXISTS idx_sync_status ON market_data_cache(sync_status)`,
      
      `CREATE TABLE IF NOT EXISTS cache_metadata (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updated_at INTEGER NOT NULL
      )`,
      
      `CREATE TABLE IF NOT EXISTS sync_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        operation TEXT NOT NULL,
        record_id TEXT,
        sync_direction TEXT NOT NULL,
        status TEXT NOT NULL,
        error_message TEXT,
        timestamp INTEGER NOT NULL
      )`
    ];

    for (const query of queries) {
      this.db!.exec(query);
    }

    // Initialize metadata
    this.setMetadata('schema_version', '1.0');
    this.setMetadata('created_at', Date.now().toString());
  }

  /**
   * Execute SQLite query
   */
  private runQuery(sql: string, params: any[] = []): { lastInsertRowid: number | bigint; changes: number } {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    const stmt = this.db.prepare(sql);
    return stmt.run(params);
  }

  /**
   * Execute SQLite query and return all results
   */
  private allQuery(sql: string, params: any[] = []): any[] {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    const stmt = this.db.prepare(sql);
    return stmt.all(params);
  }

  /**
   * Execute SQLite query and return first result
   */
  private getQuery(sql: string, params: any[] = []): any {
    if (!this.db) {
      throw new Error('Database not initialized');
    }

    const stmt = this.db.prepare(sql);
    return stmt.get(params);
  }

  /**
   * Cache market data entry
   */
  public async set(
    key: string, 
    data: NormalizedMarketData, 
    ttl?: number
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      const expiresAt = new Date(Date.now() + (ttl || this.config.defaultTTL) * 1000);
      const now = Date.now();
      
      // Prepare data for storage
      const cacheEntry = {
        id: data.id,
        cache_key: key,
        source: data.source,
        instrument: data.instrument,
        timeframe: data.timeframe,
        timestamp: data.timestamp.getTime(),
        open_price: data.open.toString(),
        high_price: data.high.toString(),
        low_price: data.low.toString(),
        close_price: data.close.toString(),
        volume: data.volume.toString(),
        spread: data.spread?.toString() || null,
        bid_price: data.bid?.toString() || null,
        ask_price: data.ask?.toString() || null,
        quality_score: data.qualityScore,
        is_compressed: data.isCompressed ? 1 : 0,
        compressed_data: null, // TODO: Implement compression
        original_size: data.originalPayloadSize || JSON.stringify(data).length,
        compressed_size: data.processedPayloadSize || null,
        created_at: now,
        expires_at: expiresAt.getTime(),
        access_count: 0,
        last_accessed_at: now,
        sync_status: 'pending'
      };

      // Insert or replace cache entry
      const sql = `INSERT OR REPLACE INTO market_data_cache (
        id, cache_key, source, instrument, timeframe, timestamp,
        open_price, high_price, low_price, close_price, volume,
        spread, bid_price, ask_price, quality_score, is_compressed,
        compressed_data, original_size, compressed_size, created_at,
        expires_at, access_count, last_accessed_at, sync_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`;

      this.runQuery(sql, Object.values(cacheEntry));
      
      // Update statistics
      this.updateStats(Date.now() - startTime, 'set');
      this.emit('cache_set', { key, dataId: data.id });
      
    } catch (error) {
      this.emit('cache_error', { operation: 'set', key, error });
      throw error;
    }
  }

  /**
   * Retrieve cached market data
   */
  public async get(key: string): Promise<NormalizedMarketData | null> {
    const startTime = Date.now();
    
    try {
      // Check expiration and get entry
      const sql = `
        SELECT * FROM market_data_cache 
        WHERE cache_key = ? AND expires_at > ?
      `;
      
      const row = this.getQuery(sql, [key, Date.now()]);
      
      if (!row) {
        this.stats.missCount++;
        this.updateStats(Date.now() - startTime, 'miss');
        return null;
      }

      // Update access statistics
      this.runQuery(
        `UPDATE market_data_cache 
         SET access_count = access_count + 1, last_accessed_at = ?
         WHERE cache_key = ?`,
        [Date.now(), key]
      );

      // Convert row to NormalizedMarketData
      const data = this.rowToMarketData(row);
      
      this.stats.hitCount++;
      this.updateStats(Date.now() - startTime, 'hit');
      this.emit('cache_hit', { key, dataId: data.id });
      
      return data;
      
    } catch (error) {
      this.emit('cache_error', { operation: 'get', key, error });
      throw error;
    }
  }

  /**
   * Convert database row to NormalizedMarketData
   */
  private rowToMarketData(row: any): NormalizedMarketData {
    return {
      id: row.id,
      source: row.source as DataSource,
      instrument: row.instrument,
      timeframe: row.timeframe as TimeFrame,
      timestamp: new Date(row.timestamp),
      open: new Decimal(row.open_price),
      high: new Decimal(row.high_price),
      low: new Decimal(row.low_price),
      close: new Decimal(row.close_price),
      volume: new Decimal(row.volume),
      spread: row.spread ? new Decimal(row.spread) : undefined,
      bid: row.bid_price ? new Decimal(row.bid_price) : undefined,
      ask: row.ask_price ? new Decimal(row.ask_price) : undefined,
      precision: 5, // Default precision
      timezone: 'UTC',
      isCompressed: row.is_compressed === 1,
      compressionRatio: row.compressed_size ? row.compressed_size / row.original_size : undefined,
      qualityScore: row.quality_score,
      originalPayloadSize: row.original_size,
      processedPayloadSize: row.compressed_size || row.original_size,
      processingTimeMs: 0, // Not stored in cache
    };
  }

  /**
   * Delete cached entry
   */
  public async delete(key: string): Promise<boolean> {
    try {
      const result = this.runQuery(
        'DELETE FROM market_data_cache WHERE cache_key = ?',
        [key]
      );
      
      const deleted = result.changes > 0;
      if (deleted) {
        this.emit('cache_delete', { key });
      }
      
      return deleted;
      
    } catch (error) {
      this.emit('cache_error', { operation: 'delete', key, error });
      throw error;
    }
  }

  /**
   * Clear expired entries
   */
  public async clearExpired(): Promise<number> {
    try {
      const now = Date.now();
      
      // Check what entries should be expired (for debugging)
      this.allQuery(
        'SELECT cache_key, expires_at FROM market_data_cache WHERE expires_at <= ?',
        [now]
      ); // Query executed for debugging purposes
      
      const result = this.runQuery(
        'DELETE FROM market_data_cache WHERE expires_at <= ?',
        [now]
      );
      
      const evicted = result.changes;
      this.stats.evictionCount += evicted;
      
      if (evicted > 0) {
        this.emit('cache_evicted', { count: evicted });
      }
      
      return evicted;
      
    } catch (error) {
      this.emit('cache_error', { operation: 'clearExpired', error });
      throw error;
    }
  }

  /**
   * Get cache statistics
   */
  public async getStats(): Promise<CacheStats> {
    try {
      // Update entry count and size
      const countResult = this.getQuery(
        'SELECT COUNT(*) as count, SUM(original_size) as total_size FROM market_data_cache'
      );
      
      this.stats.totalEntries = countResult?.count || 0;
      this.stats.totalSize = countResult?.total_size || 0;
      const totalRequests = this.stats.hitCount + this.stats.missCount;
      this.stats.hitRatio = totalRequests > 0 ? this.stats.hitCount / totalRequests : 1;
      this.stats.memoryUsage = process.memoryUsage().heapUsed;
      
      return { ...this.stats };
      
    } catch (error) {
      this.emit('cache_error', { operation: 'getStats', error });
      throw error;
    }
  }

  /**
   * Search cache by criteria
   */
  public async search(criteria: {
    instrument?: string;
    timeframe?: TimeFrame;
    source?: DataSource;
    fromTimestamp?: Date;
    toTimestamp?: Date;
    limit?: number;
  }): Promise<NormalizedMarketData[]> {
    try {
      let sql = 'SELECT * FROM market_data_cache WHERE expires_at > ?';
      const params: any[] = [Date.now()];
      
      if (criteria.instrument) {
        sql += ' AND instrument = ?';
        params.push(criteria.instrument);
      }
      
      if (criteria.timeframe) {
        sql += ' AND timeframe = ?';
        params.push(criteria.timeframe);
      }
      
      if (criteria.source) {
        sql += ' AND source = ?';
        params.push(criteria.source);
      }
      
      if (criteria.fromTimestamp) {
        sql += ' AND timestamp >= ?';
        params.push(criteria.fromTimestamp.getTime());
      }
      
      if (criteria.toTimestamp) {
        sql += ' AND timestamp <= ?';
        params.push(criteria.toTimestamp.getTime());
      }
      
      sql += ' ORDER BY timestamp DESC';
      
      if (criteria.limit) {
        sql += ' LIMIT ?';
        params.push(criteria.limit);
      }
      
      const rows = this.allQuery(sql, params);
      
      return rows.map(row => this.rowToMarketData(row));
      
    } catch (error) {
      this.emit('cache_error', { operation: 'search', criteria, error });
      throw error;
    }
  }

  /**
   * Synchronize with TimescaleDB (placeholder for integration)
   */
  public async syncWithTimescaleDB(): Promise<SyncStats> {
    const startTime = Date.now();
    let recordsSynced = 0;
    const conflictsResolved = 0;
    let errorsEncountered = 0;

    try {
      // Get pending sync records
      const pendingRecords = this.allQuery(
        `SELECT * FROM market_data_cache 
         WHERE sync_status = 'pending' 
         ORDER BY timestamp DESC 
         LIMIT 1000`
      );

      for (const record of pendingRecords) {
        try {
          // TODO: Implement actual TimescaleDB sync
          // For now, mark as synced
          this.runQuery(
            `UPDATE market_data_cache 
             SET sync_status = 'synced', sync_timestamp = ?
             WHERE id = ?`,
            [Date.now(), record.id]
          );
          
          recordsSynced++;
        } catch (error) {
          errorsEncountered++;
          this.emit('sync_error', { recordId: record.id, error });
        }
      }

      // Update sync statistics
      this.syncStats = {
        lastSyncAt: new Date(),
        recordsSynced,
        syncDuration: Date.now() - startTime,
        conflictsResolved,
        errorsEncountered,
      };

      this.stats.lastSync = new Date();
      this.stats.syncErrors += errorsEncountered;

      this.emit('sync_completed', this.syncStats);
      return { ...this.syncStats };

    } catch (error) {
      this.emit('sync_error', { operation: 'syncWithTimescaleDB', error });
      throw error;
    }
  }

  /**
   * Set metadata value
   */
  private setMetadata(key: string, value: string): void {
    this.runQuery(
      `INSERT OR REPLACE INTO cache_metadata (key, value, updated_at) VALUES (?, ?, ?)`,
      [key, value, Date.now()]
    );
  }

  /**
   * Get metadata value
   */
  private getMetadata(key: string): string | null {
    const row = this.getQuery(
      'SELECT value FROM cache_metadata WHERE key = ?',
      [key]
    );
    return row?.value || null;
  }

  /**
   * Update cache statistics
   */
  private updateStats(queryTime: number, _operation: string): void {
    const totalQueries = this.stats.hitCount + this.stats.missCount;
    this.stats.avgQueryTime = totalQueries > 0 
      ? (this.stats.avgQueryTime * (totalQueries - 1) + queryTime) / totalQueries 
      : queryTime;
  }

  /**
   * Start background sync process
   */
  private startSyncProcess(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(async () => {
      try {
        await this.syncWithTimescaleDB();
      } catch (error) {
        this.emit('sync_error', { operation: 'background_sync', error });
      }
    }, this.config.syncInterval * 1000);
  }

  /**
   * Start database vacuum process
   */
  private startVacuumProcess(): void {
    if (this.vacuumInterval) {
      clearInterval(this.vacuumInterval);
    }

    this.vacuumInterval = setInterval(async () => {
      try {
        await this.clearExpired();
        this.db!.exec('VACUUM');
        this.emit('vacuum_completed');
      } catch (error) {
        this.emit('vacuum_error', { error });
      }
    }, this.config.vacuumInterval * 1000);
  }

  /**
   * Check if cache is healthy
   */
  public async healthCheck(): Promise<{
    isHealthy: boolean;
    issues: string[];
    stats: CacheStats;
  }> {
    const issues: string[] = [];
    const stats = await this.getStats();

    try {
      // Check database connectivity
      if (!this.db) {
        issues.push('Database not initialized');
      }

      // Check cache size limits
      if (stats.totalSize > this.config.maxCacheSize * 1024 * 1024) {
        issues.push(`Cache size exceeds limit: ${stats.totalSize} bytes`);
      }

      // Check hit ratio
      if (stats.hitRatio < 0.3) {
        issues.push(`Low cache hit ratio: ${(stats.hitRatio * 100).toFixed(2)}%`);
      }

      // Check recent sync errors
      if (stats.syncErrors > 10) {
        issues.push(`High sync error count: ${stats.syncErrors}`);
      }

      return {
        isHealthy: issues.length === 0,
        issues,
        stats,
      };

    } catch (error) {
      issues.push(`Health check failed: ${error}`);
      return {
        isHealthy: false,
        issues,
        stats,
      };
    }
  }

  /**
   * Shutdown cache service
   */
  public async shutdown(): Promise<void> {
    try {
      // Clear intervals
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
        this.syncInterval = null;
      }

      if (this.vacuumInterval) {
        clearInterval(this.vacuumInterval);
        this.vacuumInterval = null;
      }

      // Final sync
      if (this.isInitialized) {
        await this.syncWithTimescaleDB();
      }

      // Close database
      if (this.db) {
        this.db.close();
        this.db = null;
      }

      this.isInitialized = false;
      this.emit('shutdown');
      // Don't remove listeners to allow error events to be caught during testing
      // this.removeAllListeners();

    } catch (error) {
      this.emit('error', { operation: 'shutdown', error });
      throw error;
    }
  }
}