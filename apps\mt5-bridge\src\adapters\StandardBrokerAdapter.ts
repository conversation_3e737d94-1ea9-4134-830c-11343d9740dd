/**
 * Standard Broker Adapter
 * 
 * Base class for all broker adapters providing:
 * - Common broker adapter interface with standardized methods
 * - Error handling and response normalization across different brokers
 * - Authentication and session management abstraction
 * - Integration with connection pooling and monitoring systems
 */

import { EventEmitter } from 'events';
import type { MarketData, Trade } from '@golddaddy/types';
import type { BrokerAdapterConfig, BrokerFeature } from './BrokerAdapterFactory';

// Market data subscription
export interface MarketDataSubscription {
  symbol: string;
  timeframe: string;
  subscriptionId: string;
  subscribedAt: Date;
  lastUpdate: Date | null;
  active: boolean;
}

// Trade order structure
export interface TradeOrder {
  id: string;
  symbol: string;
  type: 'buy' | 'sell';
  volume: number;
  price: number;
  stopLoss?: number;
  takeProfit?: number;
  comment?: string;
  magicNumber?: number;
  slippage?: number;
  expiration?: Date;
}

// Trade execution result
export interface TradeExecutionResult {
  orderId: string;
  ticket: number;
  executed: boolean;
  executionPrice: number;
  executionTime: Date;
  volume: number;
  commission: number;
  swap: number;
  profit: number;
  error: string | null;
  retryCount: number;
}

// Account information
export interface AccountInfo {
  accountNumber: string;
  balance: number;
  equity: number;
  margin: number;
  freeMargin: number;
  marginLevel: number;
  currency: string;
  leverage: number;
  server: string;
  company: string;
  isConnected: boolean;
  lastUpdate: Date;
}

// Position information
export interface PositionInfo {
  ticket: number;
  symbol: string;
  type: 'buy' | 'sell';
  volume: number;
  openPrice: number;
  currentPrice: number;
  stopLoss: number;
  takeProfit: number;
  profit: number;
  commission: number;
  swap: number;
  openTime: Date;
  comment: string;
  magicNumber: number;
}

// Connection statistics
export interface ConnectionStats {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  activeConnections: number;
  errorRate: number;
  uptime: number;
  lastRequestTime: Date | null;
  connectionStartTime: Date;
}

// Symbol information
export interface SymbolInfo {
  name: string;
  description: string;
  type: 'forex' | 'metal' | 'crypto' | 'stock' | 'index' | 'commodity';
  digits: number;
  spread: number;
  point: number;
  minVolume: number;
  maxVolume: number;
  volumeStep: number;
  contractSize: number;
  marginRequired: number;
  isTradeAllowed: boolean;
  sessionTimes: {
    open: string;
    close: string;
    timezone: string;
  };
}

// Broker adapter error types
export class BrokerAdapterError extends Error {
  constructor(
    message: string,
    public code: string,
    public brokerCode?: string,
    public retryable: boolean = false
  ) {
    super(message);
    this.name = 'BrokerAdapterError';
  }
}

/**
 * Standard Broker Adapter
 * Abstract base class for all broker-specific adapters
 */
export abstract class StandardBrokerAdapter extends EventEmitter {
  protected config: BrokerAdapterConfig;
  protected isConnected = false;
  protected connectionStats: ConnectionStats;
  protected subscriptions: Map<string, MarketDataSubscription> = new Map();
  protected sessionInfo: {
    sessionId?: string;
    loginTime?: Date;
    lastActivity?: Date;
    expiresAt?: Date;
  } = {};

  constructor(config: BrokerAdapterConfig) {
    super();
    this.config = config;
    this.connectionStats = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0,
      activeConnections: 0,
      errorRate: 0,
      uptime: 0,
      lastRequestTime: null,
      connectionStartTime: new Date()
    };
  }

  /**
   * Connect to the broker
   */
  abstract connect(): Promise<boolean>;

  /**
   * Disconnect from the broker
   */
  abstract disconnect(): Promise<boolean>;

  /**
   * Test connection health
   */
  abstract testConnection(): Promise<boolean>;

  /**
   * Get current account information
   */
  abstract getAccountInfo(): Promise<AccountInfo>;

  /**
   * Get available symbols
   */
  abstract getSymbols(): Promise<SymbolInfo[]>;

  /**
   * Get symbol information
   */
  abstract getSymbolInfo(symbol: string): Promise<SymbolInfo>;

  /**
   * Subscribe to market data
   */
  abstract subscribeToMarketData(symbol: string, timeframe: string): Promise<string>;

  /**
   * Unsubscribe from market data
   */
  abstract unsubscribeFromMarketData(subscriptionId: string): Promise<boolean>;

  /**
   * Get historical market data
   */
  abstract getHistoricalData(
    symbol: string, 
    timeframe: string, 
    from: Date, 
    to: Date
  ): Promise<MarketData[]>;

  /**
   * Execute a trade order
   */
  abstract executeTrade(order: TradeOrder): Promise<TradeExecutionResult>;

  /**
   * Get current positions
   */
  abstract getPositions(): Promise<PositionInfo[]>;

  /**
   * Close a position
   */
  abstract closePosition(ticket: number): Promise<TradeExecutionResult>;

  /**
   * Modify a position (stop loss, take profit)
   */
  abstract modifyPosition(
    ticket: number, 
    stopLoss?: number, 
    takeProfit?: number
  ): Promise<boolean>;

  // Common implementation methods

  /**
   * Get adapter configuration
   */
  getConfig(): BrokerAdapterConfig {
    return { ...this.config };
  }

  /**
   * Get connection status
   */
  isAdapterConnected(): boolean {
    return this.isConnected;
  }

  /**
   * Get connection statistics
   */
  getConnectionStats(): ConnectionStats {
    this.connectionStats.uptime = Date.now() - this.connectionStats.connectionStartTime.getTime();
    this.connectionStats.errorRate = this.connectionStats.totalRequests > 0 
      ? this.connectionStats.failedRequests / this.connectionStats.totalRequests 
      : 0;
    
    return { ...this.connectionStats };
  }

  /**
   * Get supported capabilities
   */
  async getCapabilities(): Promise<BrokerFeature[]> {
    return [...this.config.features];
  }

  /**
   * Get health score (0-1)
   */
  async getHealthScore(): Promise<number> {
    if (!this.isConnected) return 0;

    try {
      // Test basic connectivity
      const canConnect = await this.testConnection();
      if (!canConnect) return 0.1;

      // Calculate health based on performance metrics
      const stats = this.getConnectionStats();
      const errorRate = stats.errorRate;
      const responseTime = stats.averageResponseTime;
      
      // Health components
      const connectivityScore = canConnect ? 1.0 : 0.0;
      const errorScore = Math.max(0, 1.0 - errorRate * 2); // Penalize errors heavily
      const performanceScore = Math.max(0, 1.0 - Math.min(1.0, responseTime / 5000)); // 5s max acceptable
      const sessionScore = this.isSessionValid() ? 1.0 : 0.5;
      
      // Weighted average
      const healthScore = (
        connectivityScore * 0.3 +
        errorScore * 0.3 +
        performanceScore * 0.2 +
        sessionScore * 0.2
      );

      return Math.min(1.0, Math.max(0.0, healthScore));
    } catch (error) {
      console.error(`Failed to calculate health score for ${this.config.id}:`, error);
      return 0.0;
    }
  }

  /**
   * Get active subscriptions
   */
  getActiveSubscriptions(): MarketDataSubscription[] {
    return Array.from(this.subscriptions.values()).filter(sub => sub.active);
  }

  /**
   * Authenticate with the broker
   */
  protected async authenticate(): Promise<boolean> {
    try {
      console.log(`🔐 Authenticating with ${this.config.name}...`);
      
      // This would be implemented by specific adapters
      const success = await this.performAuthentication();
      
      if (success) {
        this.sessionInfo.loginTime = new Date();
        this.sessionInfo.lastActivity = new Date();
        this.sessionInfo.expiresAt = new Date(
          Date.now() + this.config.authentication.sessionTimeout
        );
        console.log(`✅ Authentication successful for ${this.config.name}`);
      } else {
        console.error(`❌ Authentication failed for ${this.config.name}`);
      }
      
      return success;
    } catch (error) {
      console.error(`❌ Authentication error for ${this.config.name}:`, error);
      throw new BrokerAdapterError(
        `Authentication failed: ${error}`,
        'AUTH_FAILED',
        undefined,
        false
      );
    }
  }

  /**
   * Check if session is still valid
   */
  protected isSessionValid(): boolean {
    if (!this.sessionInfo.expiresAt) return false;
    return Date.now() < this.sessionInfo.expiresAt.getTime();
  }

  /**
   * Refresh authentication session
   */
  protected async refreshSession(): Promise<boolean> {
    if (this.isSessionValid()) return true;
    
    console.log(`🔄 Refreshing session for ${this.config.name}...`);
    return this.authenticate();
  }

  /**
   * Record a request for statistics
   */
  protected recordRequest(responseTime: number, success: boolean): void {
    this.connectionStats.totalRequests++;
    this.connectionStats.lastRequestTime = new Date();
    
    if (success) {
      this.connectionStats.successfulRequests++;
    } else {
      this.connectionStats.failedRequests++;
    }
    
    // Update average response time
    const totalResponseTime = this.connectionStats.averageResponseTime * 
      (this.connectionStats.totalRequests - 1);
    this.connectionStats.averageResponseTime = 
      (totalResponseTime + responseTime) / this.connectionStats.totalRequests;
  }

  /**
   * Handle broker-specific errors
   */
  protected handleBrokerError(error: any, operation: string): BrokerAdapterError {
    let errorCode = 'UNKNOWN_ERROR';
    let retryable = false;
    let message = `${operation} failed: ${error}`;

    // Common error patterns
    if (error.message?.includes('timeout')) {
      errorCode = 'TIMEOUT';
      retryable = true;
      message = `${operation} timed out`;
    } else if (error.message?.includes('network') || error.message?.includes('connection')) {
      errorCode = 'NETWORK_ERROR';
      retryable = true;
      message = `Network error during ${operation}`;
    } else if (error.message?.includes('auth') || error.message?.includes('login')) {
      errorCode = 'AUTH_ERROR';
      retryable = false;
      message = `Authentication error during ${operation}`;
    } else if (error.message?.includes('rate limit') || error.message?.includes('too many')) {
      errorCode = 'RATE_LIMIT';
      retryable = true;
      message = `Rate limit exceeded during ${operation}`;
    }

    return new BrokerAdapterError(message, errorCode, error.code, retryable);
  }

  /**
   * Validate trade order
   */
  protected validateTradeOrder(order: TradeOrder): void {
    if (!order.symbol || !this.config.supportedSymbols.includes(order.symbol)) {
      throw new BrokerAdapterError(
        `Unsupported symbol: ${order.symbol}`,
        'INVALID_SYMBOL'
      );
    }

    if (order.volume <= 0) {
      throw new BrokerAdapterError(
        'Volume must be greater than 0',
        'INVALID_VOLUME'
      );
    }

    if (order.price <= 0) {
      throw new BrokerAdapterError(
        'Price must be greater than 0',
        'INVALID_PRICE'
      );
    }

    // Additional validation would be implemented by specific adapters
  }

  /**
   * Normalize market data from broker-specific format
   */
  protected normalizeMarketData(rawData: any, symbol: string): MarketData {
    // This would be implemented by specific adapters
    return {
      symbol,
      timestamp: new Date(),
      bid: rawData.bid || 0,
      ask: rawData.ask || 0,
      last: rawData.last || rawData.bid || 0,
      volume: rawData.volume || 0,
      high: rawData.high || 0,
      low: rawData.low || 0,
      open: rawData.open || 0,
      close: rawData.close || rawData.last || 0
    };
  }

  /**
   * Update session activity
   */
  protected updateSessionActivity(): void {
    this.sessionInfo.lastActivity = new Date();
  }

  /**
   * Perform broker-specific authentication
   * To be implemented by specific adapters
   */
  protected abstract performAuthentication(): Promise<boolean>;

  /**
   * Start periodic session refresh if needed
   */
  protected startSessionRefresh(): void {
    const refreshInterval = this.config.authentication.sessionTimeout * 0.8; // Refresh at 80% of timeout
    
    setInterval(async () => {
      if (this.isConnected && !this.isSessionValid()) {
        try {
          await this.refreshSession();
        } catch (error) {
          console.error(`Failed to refresh session for ${this.config.name}:`, error);
          this.emit('sessionExpired', { brokerId: this.config.id, error });
        }
      }
    }, refreshInterval);
  }

  /**
   * Get request rate limit status
   */
  protected getRateLimitStatus(): {
    currentRate: number;
    maxRate: number;
    resetTime: Date;
    canMakeRequest: boolean;
  } {
    // Simple rate limiting implementation
    const now = Date.now();
    const window = 1000; // 1 second window
    const recentRequests = this.connectionStats.totalRequests; // Simplified
    
    return {
      currentRate: recentRequests,
      maxRate: this.config.maxOrdersPerSecond,
      resetTime: new Date(now + window),
      canMakeRequest: recentRequests < this.config.maxOrdersPerSecond
    };
  }

  /**
   * Wait for rate limit if needed
   */
  protected async waitForRateLimit(): Promise<void> {
    const rateLimit = this.getRateLimitStatus();
    
    if (!rateLimit.canMakeRequest) {
      const waitTime = rateLimit.resetTime.getTime() - Date.now();
      if (waitTime > 0) {
        console.log(`⏳ Rate limit reached, waiting ${waitTime}ms`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }
}