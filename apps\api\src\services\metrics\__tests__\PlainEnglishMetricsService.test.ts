/**
 * Tests for PlainEnglishMetricsService
 */

import { PlainEnglishMetricsService } from '../PlainEnglishMetricsService';
import {
  MetricTranslationContext,
  UserExperienceLevel,
  RiskTolerance,
  MetricType,
} from '@golddaddy/types';

describe('PlainEnglishMetricsService', () => {
  let service: PlainEnglishMetricsService;
  
  const context: MetricTranslationContext = {
    strategyId: 'test-strategy',
    userId: 'test-user',
    userExperience: 'intermediate',
    userRiskTolerance: 'moderate',
    strategyName: 'Test Strategy',
  };

  beforeEach(() => {
    service = new PlainEnglishMetricsService();
  });

  describe('translateWinRate', () => {

    it('should translate high win rate correctly', () => {
      const result = service.translateWinRate(0.75, context);
      
      expect(result.translation.primary).toContain('8 out of 10');
      expect(result.visualMetadata.color).toBe('green');
      expect(result.visualMetadata.badgeType).toBe('success');
    });

    it('should translate low win rate correctly', () => {
      const result = service.translateWinRate(0.35, context);
      
      expect(result.translation.primary).toContain('4 out of 10');
      expect(result.visualMetadata.color).toBe('red');
      expect(result.visualMetadata.badgeType).toBe('error');
    });

    it('should adapt explanation for beginner users', () => {
      const beginnerContext = { ...context, userExperience: 'beginner' as UserExperienceLevel };
      const result = service.translateWinRate(0.60, beginnerContext);
      
      expect(result.translation.secondary).toContain('This is very good');
      expect(result.contextualAdvice.interpretation).toBeDefined();
    });

    it('should provide detailed context for advanced users', () => {
      const advancedContext = { ...context, userExperience: 'advanced' as UserExperienceLevel };
      const result = service.translateWinRate(0.60, advancedContext);
      
      expect(result.translation.secondary).toContain('Solid win rate');
      expect(result.userPersonalization.experienceLevel).toBe('advanced');
    });

    it('should include warnings for poor performance', () => {
      const result = service.translateWinRate(0.25, context);
      
      expect(result.contextualAdvice.warnings).toBeDefined();
      expect(result.contextualAdvice.warnings!.length).toBeGreaterThan(0);
    });
  });

  describe('translateSharpeRatio', () => {
    const context: MetricTranslationContext = {
      strategyId: 'test-strategy',
      userId: 'test-user',
      userExperience: 'intermediate',
      userRiskTolerance: 'moderate',
      strategyName: 'Test Strategy',
    };

    it('should translate excellent Sharpe ratio', () => {
      const result = service.translateSharpeRatio(2.5, context);
      
      expect(result.translation.primary).toContain('excellent');
      expect(result.visualMetadata.color).toBe('green');
    });

    it('should translate poor Sharpe ratio', () => {
      const result = service.translateSharpeRatio(-0.5, context);
      
      expect(result.translation.primary).toContain('poor risk-adjusted');
      expect(result.visualMetadata.color).toBe('red');
    });

    it('should provide context-appropriate explanations', () => {
      const result = service.translateSharpeRatio(1.2, context);
      
      expect(result.translation.comparison).toContain('market average');
      expect(result.contextualAdvice.interpretation).toBeDefined();
    });
  });

  describe('translateProfitFactor', () => {
    const context: MetricTranslationContext = {
      strategyId: 'test-strategy',
      userId: 'test-user',
      userExperience: 'intermediate',
      userRiskTolerance: 'moderate',
      strategyName: 'Test Strategy',
    };

    it('should translate high profit factor correctly', () => {
      const result = service.translateProfitFactor(2.5, context);
      
      expect(result.translation.primary).toContain('2.50');
      expect(result.visualMetadata.color).toBe('green');
    });

    it('should warn about unprofitable strategies', () => {
      const result = service.translateProfitFactor(0.8, context);
      
      expect(result.translation.primary).toContain('unprofitable');
      expect(result.contextualAdvice.warnings).toBeDefined();
      expect(result.visualMetadata.color).toBe('red');
    });

    it('should celebrate exceptional performance', () => {
      const result = service.translateProfitFactor(5.0, context);
      
      expect(result.contextualAdvice.celebration).toBeDefined();
      expect(result.visualMetadata.badgeType).toBe('success');
    });
  });

  describe('calculateHealthScore', () => {
    it('should calculate health score correctly', () => {
      const metrics = {
        winRate: 0.65,
        profitFactor: 1.8,
        sharpeRatio: 1.2,
        maxDrawdown: 0.12,
        totalReturn: 0.15,
        calmarRatio: 1.0,
        sortinoRatio: 1.5,
        volatility: 0.08,
        tradeCount: 100,
        avgTradeReturn: 0.002,
      };

      const result = service.calculateHealthScore(metrics, context);
      
      expect(result.overall).toBeGreaterThan(0);
      expect(result.overall).toBeLessThanOrEqual(100);
      expect(result.components.profitability).toBeDefined();
      expect(result.components.consistency).toBeDefined();
      expect(result.components.riskManagement).toBeDefined();
    });

    it('should handle missing metrics gracefully', () => {
      const metrics = {
        winRate: 0.55,
        profitFactor: 1.2,
        sharpeRatio: 0.8,
        maxDrawdown: 0.15,
        totalReturn: 0.10,
        calmarRatio: 0.5,
        sortinoRatio: 0.9,
        volatility: 0.12,
        tradeCount: 50,
        avgTradeReturn: 0.001,
      };

      const result = service.calculateHealthScore(metrics, context);
      
      expect(result.overall).toBeGreaterThan(0);
      expect(result.components.profitability).toBeDefined();
    });
  });

  describe('generateHealthExplanation', () => {
    it('should generate comprehensive explanation', () => {
      const healthScore = {
        overall: 75,
        components: {
          profitability: 80,
          consistency: 70,
          riskManagement: 75,
        },
        performance: 'good' as const,
        trend: 'improving',
        lastCalculated: new Date(),
      };

      const context: MetricTranslationContext = {
        strategyId: 'test-strategy',
        userId: 'test-user',
        userExperience: 'intermediate',
        userRiskTolerance: 'moderate',
        strategyName: 'Test Strategy',
      };

      const result = service.generateHealthScoreExplanation(healthScore, context);
      
      expect(result.summary).toBeDefined();
      expect(result.strengths).toEqual(expect.any(Array));
      expect(result.weaknesses).toEqual(expect.any(Array));
      expect(result.recommendations).toEqual(expect.any(Array));
    });

    it('should adapt explanations for different experience levels', () => {
      const healthScore = {
        overall: 85,
        components: {
          profitability: 90,
          consistency: 80,
          riskManagement: 85,
        },
        performance: 'excellent' as const,
        trend: 'stable',
        lastCalculated: new Date(),
      };

      const beginnerContext: MetricTranslationContext = {
        strategyId: 'test-strategy',
        userId: 'test-user',
        userExperience: 'beginner',
        userRiskTolerance: 'conservative',
        strategyName: 'Test Strategy',
      };

      const advancedContext: MetricTranslationContext = {
        ...beginnerContext,
        userExperience: 'advanced',
        userRiskTolerance: 'aggressive',
      };

      const beginnerResult = service.generateHealthScoreExplanation(healthScore, beginnerContext);
      const advancedResult = service.generateHealthScoreExplanation(healthScore, advancedContext);
      
      expect(beginnerResult.summary).not.toBe(advancedResult.summary);
      expect(beginnerResult.recommendations.length).toBeGreaterThan(0);
      expect(advancedResult.recommendations.length).toBeGreaterThan(0);
    });
  });

  describe('edge cases', () => {
    const context: MetricTranslationContext = {
      strategyId: 'test-strategy',
      userId: 'test-user',
      userExperience: 'intermediate',
      userRiskTolerance: 'moderate',
      strategyName: 'Test Strategy',
    };

    it('should handle zero win rate', () => {
      const result = service.translateWinRate(0, context);
      
      expect(result.translation.primary).toContain('0 out of 10');
      expect(result.contextualAdvice.warnings).toBeDefined();
    });

    it('should handle perfect win rate', () => {
      const result = service.translateWinRate(1.0, context);
      
      expect(result.translation.primary).toContain('10 out of 10');
      expect(result.contextualAdvice.celebration).toBeDefined();
    });

    it('should handle negative Sharpe ratio', () => {
      const result = service.translateSharpeRatio(-1.5, context);
      
      expect(result.translation.primary).toContain('poor risk-adjusted');
      expect(result.visualMetadata.color).toBe('red');
    });

    it('should handle very low profit factor', () => {
      const result = service.translateProfitFactor(0.1, context);
      
      expect(result.contextualAdvice.warnings).toBeDefined();
      expect(result.contextualAdvice.warnings!.length).toBeGreaterThan(0);
    });
  });
});