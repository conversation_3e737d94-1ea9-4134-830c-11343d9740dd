import { 
  GraduatedAccess, 
  AccessLevel,
  AccessLevelConfiguration,
  TradingLimit,
  InstrumentRestriction,
  ProgressionCriteria,
  ProgressionStatus,
  AccessLevelProgression,
  DailyTradingLimit,
  AccessLevelMetrics,
  AccessLevelValidation,
  InstrumentCategory,
  RiskCategory
} from '@golddaddy/types';

export interface GraduatedAccessServiceDependencies {
  loggerService: any;
  auditService?: any;
  notificationService?: any;
  tradingService?: any;
  metricsService?: any;
}

/**
 * GraduatedAccessService
 * 
 * Manages tiered access levels for new live traders with progressive
 * restrictions and automatic advancement based on performance criteria.
 * Implements safety mechanisms to gradually introduce users to live trading.
 */
export class GraduatedAccessService {
  private readonly logger: any;
  private readonly auditService?: any;
  private readonly notificationService?: any;
  private readonly tradingService?: any;
  private readonly metricsService?: any;

  // Access level configurations
  private readonly ACCESS_LEVEL_CONFIGS: Record<AccessLevel, AccessLevelConfiguration> = {
    [AccessLevel.RESTRICTED]: {
      level: AccessLevel.RESTRICTED,
      displayName: 'Restricted Access',
      description: 'Limited access with strict controls for new traders',
      maxPositionSize: 0.01, // 0.01 lot maximum
      maxDailyVolume: 0.05, // 0.05 lots per day
      maxOpenPositions: 1,
      maxDailyTrades: 3,
      allowedInstruments: ['EURUSD', 'GBPUSD'], // Major pairs only
      forbiddenInstruments: ['XAUUSD', 'BTCUSD'], // No gold or crypto
      maxLeverage: 10,
      requiredMarginLevel: 200, // 200% minimum
      emergencyStopLoss: true,
      requiredConfirmations: 2,
      tradingHoursRestriction: true,
      allowedHours: { start: 8, end: 18 }, // 8 AM to 6 PM
      supervisorNotification: true,
      progressionCriteria: {
        minimumTradingDays: 14,
        minimumTrades: 20,
        maximumDrawdown: 5, // 5% max drawdown
        minimumWinRate: 45,
        minimumProfitFactor: 1.0,
        riskScoreThreshold: 70
      }
    },
    [AccessLevel.LIMITED]: {
      level: AccessLevel.LIMITED,
      displayName: 'Limited Access',
      description: 'Graduated access with moderate restrictions',
      maxPositionSize: 0.05, // 0.05 lot maximum
      maxDailyVolume: 0.25, // 0.25 lots per day
      maxOpenPositions: 3,
      maxDailyTrades: 8,
      allowedInstruments: ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF'], // Major pairs
      forbiddenInstruments: ['XAUUSD', 'BTCUSD'], // Still no gold or crypto
      maxLeverage: 20,
      requiredMarginLevel: 150, // 150% minimum
      emergencyStopLoss: true,
      requiredConfirmations: 1,
      tradingHoursRestriction: true,
      allowedHours: { start: 6, end: 20 }, // 6 AM to 8 PM
      supervisorNotification: false,
      progressionCriteria: {
        minimumTradingDays: 30,
        minimumTrades: 50,
        maximumDrawdown: 8,
        minimumWinRate: 40,
        minimumProfitFactor: 1.1,
        riskScoreThreshold: 75
      }
    },
    [AccessLevel.STANDARD]: {
      level: AccessLevel.STANDARD,
      displayName: 'Standard Access',
      description: 'Normal access with standard risk controls',
      maxPositionSize: 0.1, // 0.1 lot maximum
      maxDailyVolume: 1.0, // 1 lot per day
      maxOpenPositions: 5,
      maxDailyTrades: 15,
      allowedInstruments: [], // All major and minor pairs allowed
      forbiddenInstruments: ['BTCUSD', 'ETHUSD'], // Still no crypto
      maxLeverage: 50,
      requiredMarginLevel: 100, // 100% minimum
      emergencyStopLoss: false,
      requiredConfirmations: 0,
      tradingHoursRestriction: false,
      allowedHours: { start: 0, end: 24 }, // 24/7
      supervisorNotification: false,
      progressionCriteria: {
        minimumTradingDays: 60,
        minimumTrades: 100,
        maximumDrawdown: 12,
        minimumWinRate: 35,
        minimumProfitFactor: 1.15,
        riskScoreThreshold: 80
      }
    },
    [AccessLevel.ADVANCED]: {
      level: AccessLevel.ADVANCED,
      displayName: 'Advanced Access',
      description: 'Full access for experienced traders',
      maxPositionSize: 0.5, // 0.5 lot maximum
      maxDailyVolume: 5.0, // 5 lots per day
      maxOpenPositions: 10,
      maxDailyTrades: 30,
      allowedInstruments: [], // All instruments allowed
      forbiddenInstruments: [], // No restrictions
      maxLeverage: 100,
      requiredMarginLevel: 50, // 50% minimum
      emergencyStopLoss: false,
      requiredConfirmations: 0,
      tradingHoursRestriction: false,
      allowedHours: { start: 0, end: 24 }, // 24/7
      supervisorNotification: false,
      progressionCriteria: undefined // No further progression
    },
    [AccessLevel.PROFESSIONAL]: {
      level: AccessLevel.PROFESSIONAL,
      displayName: 'Professional Access',
      description: 'Unrestricted access for professional traders',
      maxPositionSize: 2.0, // 2 lot maximum
      maxDailyVolume: 20.0, // 20 lots per day
      maxOpenPositions: 20,
      maxDailyTrades: 100,
      allowedInstruments: [], // All instruments allowed
      forbiddenInstruments: [], // No restrictions
      maxLeverage: 500,
      requiredMarginLevel: 20, // 20% minimum
      emergencyStopLoss: false,
      requiredConfirmations: 0,
      tradingHoursRestriction: false,
      allowedHours: { start: 0, end: 24 }, // 24/7
      supervisorNotification: false,
      progressionCriteria: undefined // No further progression
    }
  };

  // Instrument categorization for risk assessment
  private readonly INSTRUMENT_CATEGORIES: Record<string, InstrumentCategory> = {
    // Major pairs (lowest risk)
    'EURUSD': InstrumentCategory.MAJOR_PAIR,
    'GBPUSD': InstrumentCategory.MAJOR_PAIR,
    'USDJPY': InstrumentCategory.MAJOR_PAIR,
    'USDCHF': InstrumentCategory.MAJOR_PAIR,
    'AUDUSD': InstrumentCategory.MAJOR_PAIR,
    'USDCAD': InstrumentCategory.MAJOR_PAIR,
    'NZDUSD': InstrumentCategory.MAJOR_PAIR,
    
    // Minor pairs (moderate risk)
    'EURGBP': InstrumentCategory.MINOR_PAIR,
    'EURJPY': InstrumentCategory.MINOR_PAIR,
    'GBPJPY': InstrumentCategory.MINOR_PAIR,
    'EURCHF': InstrumentCategory.MINOR_PAIR,
    'EURAUD': InstrumentCategory.MINOR_PAIR,
    'EURCAD': InstrumentCategory.MINOR_PAIR,
    
    // Commodities (higher risk)
    'XAUUSD': InstrumentCategory.COMMODITY,
    'XAGUSD': InstrumentCategory.COMMODITY,
    'USOIL': InstrumentCategory.COMMODITY,
    'UKOIL': InstrumentCategory.COMMODITY,
    
    // Indices (moderate-high risk)
    'US30': InstrumentCategory.INDEX,
    'US500': InstrumentCategory.INDEX,
    'UK100': InstrumentCategory.INDEX,
    'DE40': InstrumentCategory.INDEX,
    
    // Cryptocurrencies (highest risk)
    'BTCUSD': InstrumentCategory.CRYPTOCURRENCY,
    'ETHUSD': InstrumentCategory.CRYPTOCURRENCY,
    'ADAUSD': InstrumentCategory.CRYPTOCURRENCY,
    'DOGEUSD': InstrumentCategory.CRYPTOCURRENCY
  };

  constructor(dependencies: GraduatedAccessServiceDependencies) {
    this.logger = dependencies.loggerService;
    this.auditService = dependencies.auditService;
    this.notificationService = dependencies.notificationService;
    this.tradingService = dependencies.tradingService;
    this.metricsService = dependencies.metricsService;
  }

  /**
   * Initialize graduated access for a user
   */
  async initializeGraduatedAccess(
    userId: string, 
    initialLevel: AccessLevel = AccessLevel.RESTRICTED
  ): Promise<GraduatedAccess> {
    try {
      this.logger.info('Initializing graduated access', { userId, initialLevel });

      const graduatedAccess: GraduatedAccess = {
        id: `access_${userId}_${Date.now()}`,
        userId,
        currentLevel: initialLevel,
        configuration: this.ACCESS_LEVEL_CONFIGS[initialLevel],
        startedAt: new Date(),
        lastUpdated: new Date(),
        progressionHistory: [],
        currentMetrics: {
          totalTrades: 0,
          tradingDays: 0,
          winRate: 0,
          profitFactor: 0,
          maxDrawdown: 0,
          averageRiskScore: 0,
          lastCalculated: new Date()
        },
        dailyLimits: {
          tradesRemaining: this.ACCESS_LEVEL_CONFIGS[initialLevel].maxDailyTrades,
          volumeRemaining: this.ACCESS_LEVEL_CONFIGS[initialLevel].maxDailyVolume,
          lastReset: new Date()
        },
        restrictions: {
          blockedInstruments: this.ACCESS_LEVEL_CONFIGS[initialLevel].forbiddenInstruments || [],
          maxPositionSize: this.ACCESS_LEVEL_CONFIGS[initialLevel].maxPositionSize,
          maxOpenPositions: this.ACCESS_LEVEL_CONFIGS[initialLevel].maxOpenPositions,
          tradingHoursRestricted: this.ACCESS_LEVEL_CONFIGS[initialLevel].tradingHoursRestriction || false
        },
        status: ProgressionStatus.ACTIVE
      };

      await this.storeGraduatedAccess(graduatedAccess);

      // Audit log
      if (this.auditService) {
        await this.auditService.log({
          userId,
          action: 'graduated_access_initialized',
          details: { 
            accessId: graduatedAccess.id,
            initialLevel,
            configuration: this.ACCESS_LEVEL_CONFIGS[initialLevel]
          },
          timestamp: new Date()
        });
      }

      return graduatedAccess;
    } catch (error) {
      this.logger.error('Failed to initialize graduated access', { 
        error: error.message, 
        userId 
      });
      throw error;
    }
  }

  /**
   * Validate trade request against access level restrictions
   */
  async validateTradeRequest(
    userId: string, 
    instrument: string, 
    volume: number, 
    direction: 'BUY' | 'SELL'
  ): Promise<{ allowed: boolean; reason?: string; restrictions?: any }> {
    try {
      const access = await this.getGraduatedAccess(userId);
      if (!access) {
        return { allowed: false, reason: 'No graduated access configuration found' };
      }

      const config = access.configuration;
      const validation: AccessLevelValidation = {
        checks: [],
        passed: true,
        restrictions: []
      };

      // Check if instrument is allowed
      if (config.forbiddenInstruments && config.forbiddenInstruments.includes(instrument)) {
        validation.checks.push({
          check: 'instrument_allowed',
          passed: false,
          message: `Instrument ${instrument} is not allowed at ${config.displayName}`
        });
        validation.passed = false;
      }

      // Check if only specific instruments are allowed
      if (config.allowedInstruments && config.allowedInstruments.length > 0 && 
          !config.allowedInstruments.includes(instrument)) {
        validation.checks.push({
          check: 'instrument_whitelist',
          passed: false,
          message: `Only these instruments allowed: ${config.allowedInstruments.join(', ')}`
        });
        validation.passed = false;
      }

      // Check position size limits
      if (volume > config.maxPositionSize) {
        validation.checks.push({
          check: 'position_size',
          passed: false,
          message: `Position size ${volume} exceeds maximum ${config.maxPositionSize}`
        });
        validation.passed = false;
      }

      // Check daily volume limits
      if (access.dailyLimits.volumeRemaining < volume) {
        validation.checks.push({
          check: 'daily_volume',
          passed: false,
          message: `Daily volume limit exceeded. Remaining: ${access.dailyLimits.volumeRemaining}`
        });
        validation.passed = false;
      }

      // Check daily trade count limits
      if (access.dailyLimits.tradesRemaining <= 0) {
        validation.checks.push({
          check: 'daily_trades',
          passed: false,
          message: 'Daily trade limit exceeded'
        });
        validation.passed = false;
      }

      // Check open positions limit
      const openPositions = await this.getOpenPositionsCount(userId);
      if (openPositions >= config.maxOpenPositions) {
        validation.checks.push({
          check: 'max_open_positions',
          passed: false,
          message: `Maximum open positions (${config.maxOpenPositions}) reached`
        });
        validation.passed = false;
      }

      // Check trading hours restriction
      if (config.tradingHoursRestriction && config.allowedHours) {
        const currentHour = new Date().getHours();
        if (currentHour < config.allowedHours.start || currentHour >= config.allowedHours.end) {
          validation.checks.push({
            check: 'trading_hours',
            passed: false,
            message: `Trading only allowed between ${config.allowedHours.start}:00 and ${config.allowedHours.end}:00`
          });
          validation.passed = false;
        }
      }

      // Reset daily limits if new day
      await this.resetDailyLimitsIfNeeded(access);

      return {
        allowed: validation.passed,
        reason: validation.checks.filter(c => !c.passed).map(c => c.message).join('; '),
        restrictions: {
          maxPositionSize: config.maxPositionSize,
          maxDailyVolume: config.maxDailyVolume,
          maxDailyTrades: config.maxDailyTrades,
          allowedInstruments: config.allowedInstruments,
          forbiddenInstruments: config.forbiddenInstruments,
          tradingHours: config.allowedHours
        }
      };
    } catch (error) {
      this.logger.error('Failed to validate trade request', { 
        error: error.message, 
        userId, 
        instrument 
      });
      return { allowed: false, reason: 'Validation error occurred' };
    }
  }

  /**
   * Update daily limits after a trade
   */
  async updateDailyLimits(userId: string, volume: number): Promise<void> {
    try {
      const access = await this.getGraduatedAccess(userId);
      if (!access) return;

      access.dailyLimits.tradesRemaining = Math.max(0, access.dailyLimits.tradesRemaining - 1);
      access.dailyLimits.volumeRemaining = Math.max(0, access.dailyLimits.volumeRemaining - volume);
      access.lastUpdated = new Date();

      await this.updateGraduatedAccess(access);

      this.logger.debug('Updated daily limits', {
        userId,
        tradesRemaining: access.dailyLimits.tradesRemaining,
        volumeRemaining: access.dailyLimits.volumeRemaining
      });
    } catch (error) {
      this.logger.error('Failed to update daily limits', { 
        error: error.message, 
        userId 
      });
    }
  }

  /**
   * Check and potentially advance access level
   */
  async evaluateProgression(userId: string): Promise<AccessLevelProgression | null> {
    try {
      const access = await this.getGraduatedAccess(userId);
      if (!access || access.status !== ProgressionStatus.ACTIVE) {
        return null;
      }

      const config = access.configuration;
      if (!config.progressionCriteria) {
        // Already at highest level
        return null;
      }

      // Calculate current metrics
      const metrics = await this.calculateUserMetrics(userId);
      access.currentMetrics = metrics;

      // Check progression criteria
      const criteria = config.progressionCriteria;
      const meetsRequirements = this.checkProgressionCriteria(metrics, criteria);

      if (meetsRequirements.allMet) {
        // Advance to next level
        const nextLevel = this.getNextAccessLevel(access.currentLevel);
        if (nextLevel) {
          const progression = await this.advanceAccessLevel(access, nextLevel);
          return progression;
        }
      }

      await this.updateGraduatedAccess(access);

      return {
        currentLevel: access.currentLevel,
        nextLevel: this.getNextAccessLevel(access.currentLevel),
        meetsRequirements: meetsRequirements.allMet,
        criteriaStatus: meetsRequirements.details,
        estimatedTimeToAdvancement: this.estimateTimeToAdvancement(metrics, criteria),
        recommendations: this.generateProgressionRecommendations(meetsRequirements.details)
      };
    } catch (error) {
      this.logger.error('Failed to evaluate progression', { 
        error: error.message, 
        userId 
      });
      throw error;
    }
  }

  /**
   * Manually override access level (admin function)
   */
  async overrideAccessLevel(
    userId: string, 
    newLevel: AccessLevel, 
    reason: string, 
    overriddenBy: string
  ): Promise<GraduatedAccess> {
    try {
      this.logger.info('Overriding access level', { 
        userId, 
        newLevel, 
        reason, 
        overriddenBy 
      });

      const access = await this.getGraduatedAccess(userId);
      if (!access) {
        throw new Error('Graduated access not found');
      }

      const previousLevel = access.currentLevel;
      access.currentLevel = newLevel;
      access.configuration = this.ACCESS_LEVEL_CONFIGS[newLevel];
      access.lastUpdated = new Date();

      // Add to progression history
      access.progressionHistory.push({
        fromLevel: previousLevel,
        toLevel: newLevel,
        progressedAt: new Date(),
        reason: `Manual override: ${reason}`,
        metrics: access.currentMetrics,
        automatic: false,
        overriddenBy
      });

      // Reset daily limits for new level
      access.dailyLimits = {
        tradesRemaining: access.configuration.maxDailyTrades,
        volumeRemaining: access.configuration.maxDailyVolume,
        lastReset: new Date()
      };

      // Update restrictions
      access.restrictions = {
        blockedInstruments: access.configuration.forbiddenInstruments || [],
        maxPositionSize: access.configuration.maxPositionSize,
        maxOpenPositions: access.configuration.maxOpenPositions,
        tradingHoursRestricted: access.configuration.tradingHoursRestriction || false
      };

      await this.updateGraduatedAccess(access);

      // Audit log
      if (this.auditService) {
        await this.auditService.log({
          userId,
          action: 'access_level_override',
          details: { 
            fromLevel: previousLevel,
            toLevel: newLevel,
            reason,
            overriddenBy
          },
          timestamp: new Date()
        });
      }

      // Notify user
      if (this.notificationService) {
        await this.notificationService.send({
          userId,
          type: 'access_level_change',
          title: 'Trading Access Level Updated',
          message: `Your trading access level has been updated to ${access.configuration.displayName}`,
          data: { newLevel, reason }
        });
      }

      return access;
    } catch (error) {
      this.logger.error('Failed to override access level', { 
        error: error.message, 
        userId 
      });
      throw error;
    }
  }

  /**
   * Get current access level information
   */
  async getAccessLevelInfo(userId: string): Promise<{
    access: GraduatedAccess;
    dailyStatus: DailyTradingLimit;
    progressionStatus?: AccessLevelProgression;
  } | null> {
    try {
      const access = await this.getGraduatedAccess(userId);
      if (!access) return null;

      await this.resetDailyLimitsIfNeeded(access);

      const dailyStatus: DailyTradingLimit = {
        maxTrades: access.configuration.maxDailyTrades,
        tradesRemaining: access.dailyLimits.tradesRemaining,
        maxVolume: access.configuration.maxDailyVolume,
        volumeRemaining: access.dailyLimits.volumeRemaining,
        resetTime: this.getNextResetTime()
      };

      const progressionStatus = await this.evaluateProgression(userId);

      return {
        access,
        dailyStatus,
        progressionStatus: progressionStatus || undefined
      };
    } catch (error) {
      this.logger.error('Failed to get access level info', { 
        error: error.message, 
        userId 
      });
      throw error;
    }
  }

  /**
   * Private helper methods
   */
  private async advanceAccessLevel(access: GraduatedAccess, nextLevel: AccessLevel): Promise<AccessLevelProgression> {
    const previousLevel = access.currentLevel;
    access.currentLevel = nextLevel;
    access.configuration = this.ACCESS_LEVEL_CONFIGS[nextLevel];
    access.lastUpdated = new Date();

    // Add to progression history
    access.progressionHistory.push({
      fromLevel: previousLevel,
      toLevel: nextLevel,
      progressedAt: new Date(),
      reason: 'Automatic progression - criteria met',
      metrics: access.currentMetrics,
      automatic: true
    });

    // Reset daily limits for new level
    access.dailyLimits = {
      tradesRemaining: access.configuration.maxDailyTrades,
      volumeRemaining: access.configuration.maxDailyVolume,
      lastReset: new Date()
    };

    // Update restrictions
    access.restrictions = {
      blockedInstruments: access.configuration.forbiddenInstruments || [],
      maxPositionSize: access.configuration.maxPositionSize,
      maxOpenPositions: access.configuration.maxOpenPositions,
      tradingHoursRestricted: access.configuration.tradingHoursRestriction || false
    };

    await this.updateGraduatedAccess(access);

    // Audit log
    if (this.auditService) {
      await this.auditService.log({
        userId: access.userId,
        action: 'access_level_advanced',
        details: { 
          fromLevel: previousLevel,
          toLevel: nextLevel,
          metrics: access.currentMetrics
        },
        timestamp: new Date()
      });
    }

    // Notify user
    if (this.notificationService) {
      await this.notificationService.send({
        userId: access.userId,
        type: 'access_level_advancement',
        title: 'Congratulations! Access Level Advanced',
        message: `You have been promoted to ${access.configuration.displayName}!`,
        data: { 
          newLevel: nextLevel, 
          previousLevel,
          newLimits: {
            maxPositionSize: access.configuration.maxPositionSize,
            maxDailyTrades: access.configuration.maxDailyTrades
          }
        }
      });
    }

    this.logger.info('Access level advanced', {
      userId: access.userId,
      fromLevel: previousLevel,
      toLevel: nextLevel
    });

    return {
      currentLevel: nextLevel,
      nextLevel: this.getNextAccessLevel(nextLevel),
      meetsRequirements: true,
      criteriaStatus: [],
      recommendations: []
    };
  }

  private getNextAccessLevel(currentLevel: AccessLevel): AccessLevel | null {
    const levels = [
      AccessLevel.RESTRICTED,
      AccessLevel.LIMITED,
      AccessLevel.STANDARD,
      AccessLevel.ADVANCED,
      AccessLevel.PROFESSIONAL
    ];

    const currentIndex = levels.indexOf(currentLevel);
    return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : null;
  }

  private async calculateUserMetrics(userId: string): Promise<AccessLevelMetrics> {
    try {
      // This would integrate with your trading data service
      if (this.metricsService) {
        return await this.metricsService.calculateAccessMetrics(userId);
      }

      // Fallback calculation (you'd implement this based on your data structure)
      return {
        totalTrades: 0,
        tradingDays: 0,
        winRate: 0,
        profitFactor: 0,
        maxDrawdown: 0,
        averageRiskScore: 0,
        lastCalculated: new Date()
      };
    } catch (error) {
      this.logger.error('Failed to calculate user metrics', { error: error.message, userId });
      throw error;
    }
  }

  private checkProgressionCriteria(
    metrics: AccessLevelMetrics, 
    criteria: ProgressionCriteria
  ): { allMet: boolean; details: Array<{ criterion: string; required: any; actual: any; met: boolean }> } {
    const checks = [
      {
        criterion: 'minimumTradingDays',
        required: criteria.minimumTradingDays,
        actual: metrics.tradingDays,
        met: metrics.tradingDays >= criteria.minimumTradingDays
      },
      {
        criterion: 'minimumTrades',
        required: criteria.minimumTrades,
        actual: metrics.totalTrades,
        met: metrics.totalTrades >= criteria.minimumTrades
      },
      {
        criterion: 'maximumDrawdown',
        required: `<${criteria.maximumDrawdown}%`,
        actual: `${metrics.maxDrawdown}%`,
        met: metrics.maxDrawdown <= criteria.maximumDrawdown
      },
      {
        criterion: 'minimumWinRate',
        required: `>${criteria.minimumWinRate}%`,
        actual: `${metrics.winRate}%`,
        met: metrics.winRate >= criteria.minimumWinRate
      },
      {
        criterion: 'minimumProfitFactor',
        required: `>${criteria.minimumProfitFactor}`,
        actual: metrics.profitFactor,
        met: metrics.profitFactor >= criteria.minimumProfitFactor
      },
      {
        criterion: 'riskScoreThreshold',
        required: `>${criteria.riskScoreThreshold}`,
        actual: metrics.averageRiskScore,
        met: metrics.averageRiskScore >= criteria.riskScoreThreshold
      }
    ];

    return {
      allMet: checks.every(check => check.met),
      details: checks
    };
  }

  private estimateTimeToAdvancement(
    metrics: AccessLevelMetrics, 
    criteria: ProgressionCriteria
  ): string {
    const daysNeeded = Math.max(0, criteria.minimumTradingDays - metrics.tradingDays);
    const tradesNeeded = Math.max(0, criteria.minimumTrades - metrics.totalTrades);

    if (daysNeeded === 0 && tradesNeeded === 0) {
      return 'Ready for advancement';
    }

    const estimates = [];
    if (daysNeeded > 0) estimates.push(`${daysNeeded} more trading days`);
    if (tradesNeeded > 0) estimates.push(`${tradesNeeded} more trades`);

    return estimates.join(', ');
  }

  private generateProgressionRecommendations(criteriaStatus: any[]): string[] {
    const recommendations: string[] = [];

    criteriaStatus.forEach(status => {
      if (!status.met) {
        switch (status.criterion) {
          case 'minimumWinRate':
            recommendations.push('Focus on improving trade selection and entry timing');
            break;
          case 'maximumDrawdown':
            recommendations.push('Implement stricter risk management and position sizing');
            break;
          case 'minimumProfitFactor':
            recommendations.push('Work on risk-reward ratios and trade management');
            break;
          case 'riskScoreThreshold':
            recommendations.push('Review and improve risk assessment practices');
            break;
          case 'minimumTrades':
            recommendations.push('Continue trading consistently to build experience');
            break;
          case 'minimumTradingDays':
            recommendations.push('Maintain regular trading activity');
            break;
        }
      }
    });

    return recommendations;
  }

  private async resetDailyLimitsIfNeeded(access: GraduatedAccess): Promise<void> {
    const now = new Date();
    const lastReset = access.dailyLimits.lastReset;
    
    // Check if it's a new day
    if (now.getDate() !== lastReset.getDate() || 
        now.getMonth() !== lastReset.getMonth() || 
        now.getFullYear() !== lastReset.getFullYear()) {
      
      access.dailyLimits = {
        tradesRemaining: access.configuration.maxDailyTrades,
        volumeRemaining: access.configuration.maxDailyVolume,
        lastReset: now
      };

      await this.updateGraduatedAccess(access);
      
      this.logger.debug('Daily limits reset', { 
        userId: access.userId,
        newLimits: access.dailyLimits
      });
    }
  }

  private getNextResetTime(): Date {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);
    return tomorrow;
  }

  private async getOpenPositionsCount(userId: string): Promise<number> {
    try {
      if (this.tradingService) {
        return await this.tradingService.getOpenPositionsCount(userId);
      }
      return 0; // Fallback
    } catch (error) {
      this.logger.error('Failed to get open positions count', { error: error.message, userId });
      return 0;
    }
  }

  // Storage methods (implement based on your persistence layer)
  private async storeGraduatedAccess(access: GraduatedAccess): Promise<void> {
    this.logger.debug('Storing graduated access', { accessId: access.id });
    // Implementation depends on your storage system
  }

  private async getGraduatedAccess(userId: string): Promise<GraduatedAccess | null> {
    this.logger.debug('Retrieving graduated access', { userId });
    // Implementation depends on your storage system
    return null;
  }

  private async updateGraduatedAccess(access: GraduatedAccess): Promise<void> {
    this.logger.debug('Updating graduated access', { accessId: access.id });
    // Implementation depends on your storage system
  }

  /**
   * Get current access information for a user
   */
  async getCurrentAccess(userId: string): Promise<any> {
    const access = await this.getGraduatedAccess(userId);
    if (!access) {
      return {
        accessLevel: 'NONE',
        restrictions: {},
        dailyLimits: {}
      };
    }
    return access;
  }

  /**
   * Update user access level
   */
  async updateAccessLevel(
    userId: string, 
    newLevel: string, 
    metadata: any
  ): Promise<any> {
    const currentAccess = await this.getCurrentAccess(userId);
    
    // Update the access level
    const updatedAccess = {
      ...currentAccess,
      accessLevel: newLevel,
      lastUpdated: new Date(),
      updateMetadata: metadata
    };

    await this.updateGraduatedAccess(userId, updatedAccess);
    
    return {
      updated: true,
      newAccessLevel: newLevel,
      previousLevel: currentAccess.accessLevel,
      effectiveAt: new Date()
    };
  }

  /**
   * Suspend user access
   */
  async suspendAccess(userId: string, reason: string): Promise<void> {
    const currentAccess = await this.getCurrentAccess(userId);
    
    const suspendedAccess = {
      ...currentAccess,
      accessLevel: 'SUSPENDED',
      suspensionReason: reason,
      suspendedAt: new Date()
    };

    await this.updateGraduatedAccess(userId, suspendedAccess);
    
    await this.auditService.logEvent({
      userId,
      action: 'access_suspended',
      details: {
        reason,
        previousLevel: currentAccess.accessLevel,
        suspendedAt: new Date().toISOString()
      },
      category: 'access_control',
      severity: 'high'
    });
  }
}