/**
 * Rate Limiting and DDoS Protection Middleware
 * Production-grade protection for GoldDaddy Trading Platform
 */

import { Request, Response, NextFunction } from 'express';
import { createClient } from 'redis';

// Redis client for distributed rate limiting
let redisClient: ReturnType<typeof createClient> | null = null;

// Initialize Redis client
async function initializeRedis() {
  if (!redisClient) {
    redisClient = createClient({
      url: process.env.REDIS_URL || 'redis://localhost:6379'
    });
    
    redisClient.on('error', (err) => {
      console.error('Redis Client Error for Rate Limiting:', err);
    });
    
    await redisClient.connect();
  }
}

// In-memory fallback for rate limiting
const memoryStore = new Map<string, { count: number; resetTime: number }>();

/**
 * Rate limiting configuration for different endpoint types
 */
export const RATE_LIMITS = {
  // General API endpoints
  general: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 1000,
    message: 'Too many requests from this IP, please try again later'
  },
  
  // Authentication endpoints (more restrictive)
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 20,
    message: 'Too many authentication attempts, please try again later'
  },
  
  // Trading endpoints (moderate restrictions)
  trading: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100,
    message: 'Trading API rate limit exceeded, please slow down'
  },
  
  // Market data endpoints (higher limits)
  marketData: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 500,
    message: 'Market data API rate limit exceeded'
  },
  
  // WebSocket connections
  websocket: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10,
    message: 'Too many WebSocket connection attempts'
  },
  
  // Password reset (very restrictive)
  passwordReset: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    message: 'Too many password reset attempts, please wait an hour'
  },
  
  // File uploads
  upload: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 50,
    message: 'Upload rate limit exceeded'
  }
};

/**
 * Get client identifier for rate limiting
 */
function getClientId(req: Request): string {
  // Use user ID if authenticated, otherwise use IP
  const userId = req.user?.id;
  if (userId) {
    return `user:${userId}`;
  }
  
  // Get real IP address (considering proxies)
  const forwarded = req.headers['x-forwarded-for'];
  const realIp = Array.isArray(forwarded) ? forwarded[0] : forwarded;
  const ip = realIp || req.connection.remoteAddress || req.ip;
  
  return `ip:${ip}`;
}

/**
 * Redis-based distributed rate limiting
 */
async function checkRateLimit(key: string, limit: number, windowMs: number): Promise<{
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalHits: number;
}> {
  try {
    if (!redisClient) {
      await initializeRedis();
    }
    
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Use Redis sorted set for sliding window rate limiting
    const pipeline = redisClient!.multi();
    
    // Remove old entries
    pipeline.zRemRangeByScore(key, 0, windowStart);
    
    // Add current request
    pipeline.zAdd(key, { score: now, value: now.toString() });
    
    // Count current requests in window
    pipeline.zCard(key);
    
    // Set expiry on the key
    pipeline.expire(key, Math.ceil(windowMs / 1000));
    
    const results = await pipeline.exec();
    const totalHits = results ? results[2]?.result as number : 0;
    
    const allowed = totalHits <= limit;
    const remaining = Math.max(0, limit - totalHits);
    const resetTime = now + windowMs;
    
    return {
      allowed,
      remaining,
      resetTime,
      totalHits
    };
    
  } catch (error) {
    console.error('Redis rate limiting error:', error);
    // Fallback to in-memory rate limiting
    return checkMemoryRateLimit(key, limit, windowMs);
  }
}

/**
 * In-memory rate limiting fallback
 */
function checkMemoryRateLimit(key: string, limit: number, windowMs: number): {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalHits: number;
} {
  const now = Date.now();
  const record = memoryStore.get(key);
  
  if (!record || now > record.resetTime) {
    const newRecord = { count: 1, resetTime: now + windowMs };
    memoryStore.set(key, newRecord);
    
    return {
      allowed: true,
      remaining: limit - 1,
      resetTime: newRecord.resetTime,
      totalHits: 1
    };
  }
  
  record.count++;
  memoryStore.set(key, record);
  
  const allowed = record.count <= limit;
  const remaining = Math.max(0, limit - record.count);
  
  return {
    allowed,
    remaining,
    resetTime: record.resetTime,
    totalHits: record.count
  };
}

/**
 * Create rate limiting middleware for specific limits
 */
export function createRateLimiter(config: {
  windowMs: number;
  maxRequests: number;
  message: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: Request) => string;
}) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const clientId = config.keyGenerator ? config.keyGenerator(req) : getClientId(req);
      const key = `rate_limit:${clientId}:${req.route?.path || req.path}`;
      
      const result = await checkRateLimit(key, config.maxRequests, config.windowMs);
      
      // Set rate limit headers
      res.setHeader('X-Rate-Limit-Limit', config.maxRequests.toString());
      res.setHeader('X-Rate-Limit-Remaining', result.remaining.toString());
      res.setHeader('X-Rate-Limit-Reset', new Date(result.resetTime).toISOString());
      
      if (!result.allowed) {
        // Log rate limit violations
        console.warn(`Rate limit exceeded for ${clientId} on ${req.path}`, {
          ip: req.ip,
          userAgent: req.headers['user-agent'],
          totalHits: result.totalHits,
          limit: config.maxRequests
        });
        
        res.status(429).json({
          error: 'Too Many Requests',
          message: config.message,
          retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000)
        });
        return;
      }
      
      next();
    } catch (error) {
      console.error('Rate limiting middleware error:', error);
      // Allow request to proceed on error
      next();
    }
  };
}

/**
 * DDoS protection middleware
 * Monitors for suspicious patterns and implements progressive delays
 */
export const ddosProtection = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const clientId = getClientId(req);
    const windowMs = 60 * 1000; // 1 minute window
    
    // Check for excessive requests from single client
    const excessiveKey = `ddos:excessive:${clientId}`;
    const excessiveResult = await checkRateLimit(excessiveKey, 200, windowMs);
    
    if (!excessiveResult.allowed) {
      // Implement progressive delay
      const delayMs = Math.min(5000, (excessiveResult.totalHits - 200) * 100);
      
      console.warn(`DDoS protection triggered for ${clientId}`, {
        totalHits: excessiveResult.totalHits,
        delayMs,
        ip: req.ip,
        userAgent: req.headers['user-agent']
      });
      
      // Add delay before responding
      await new Promise(resolve => setTimeout(resolve, delayMs));
      
      res.status(429).json({
        error: 'Rate Limit Exceeded',
        message: 'Request rate too high, implement backoff strategy',
        retryAfter: Math.ceil(delayMs / 1000)
      });
      return;
    }
    
    // Check for suspicious patterns (same user agent, rapid requests)
    const suspiciousKey = `ddos:suspicious:${clientId}:${req.headers['user-agent']}`;
    const suspiciousResult = await checkRateLimit(suspiciousKey, 50, 60 * 1000);
    
    if (!suspiciousResult.allowed) {
      console.warn(`Suspicious pattern detected for ${clientId}`, {
        userAgent: req.headers['user-agent'],
        path: req.path,
        totalHits: suspiciousResult.totalHits
      });
      
      // Add small delay for suspicious patterns
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    next();
  } catch (error) {
    console.error('DDoS protection error:', error);
    next();
  }
};

/**
 * Predefined rate limiters for common endpoints
 */
export const rateLimiters = {
  general: createRateLimiter(RATE_LIMITS.general),
  auth: createRateLimiter(RATE_LIMITS.auth),
  trading: createRateLimiter(RATE_LIMITS.trading),
  marketData: createRateLimiter(RATE_LIMITS.marketData),
  websocket: createRateLimiter(RATE_LIMITS.websocket),
  passwordReset: createRateLimiter(RATE_LIMITS.passwordReset),
  upload: createRateLimiter(RATE_LIMITS.upload)
};

/**
 * Adaptive rate limiting based on server load
 */
export const adaptiveRateLimit = (baseConfig: typeof RATE_LIMITS.general) => {
  return createRateLimiter({
    ...baseConfig,
    maxRequests: Math.floor(baseConfig.maxRequests * getServerLoadFactor())
  });
};

/**
 * Get server load factor (0.5 to 1.0)
 * Reduces rate limits when server is under high load
 */
function getServerLoadFactor(): number {
  // Simple CPU and memory usage check (in production, use more sophisticated metrics)
  const memUsage = process.memoryUsage();
  
  // Calculate load factor based on memory usage
  const memoryLoadFactor = 1 - (memUsage.heapUsed / memUsage.heapTotal);
  
  // Return factor between 0.5 and 1.0
  return Math.max(0.5, memoryLoadFactor);
}

/**
 * Rate limiting for WebSocket connections
 */
export const websocketRateLimit = async (clientId: string): Promise<boolean> => {
  try {
    const key = `ws_rate_limit:${clientId}`;
    const result = await checkRateLimit(key, RATE_LIMITS.websocket.maxRequests, RATE_LIMITS.websocket.windowMs);
    
    return result.allowed;
  } catch (error) {
    console.error('WebSocket rate limiting error:', error);
    return true; // Allow connection on error
  }
};

/**
 * Clean up expired entries periodically
 */
export function startRateLimitCleanup() {
  setInterval(() => {
    const now = Date.now();
    for (const [key, record] of memoryStore.entries()) {
      if (now > record.resetTime) {
        memoryStore.delete(key);
      }
    }
  }, 5 * 60 * 1000); // Clean up every 5 minutes
}