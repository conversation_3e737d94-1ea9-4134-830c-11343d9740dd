import { Request, Response, NextFunction } from 'express';
import { query, validationResult } from 'express-validator';
import { TradeHistoryService } from '../../services/trading/TradeHistoryService';
import { ExecutionPerformanceReporter } from '../../services/trading/ExecutionPerformanceReporter';

interface GetHistoryQuery {
  instrument?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  category?: string;
  limit?: string;
  offset?: string;
  sortBy?: 'timestamp' | 'pnl' | 'instrument';
  sortOrder?: 'asc' | 'desc';
}

interface GetPerformanceQuery {
  strategyId?: string;
  instrument?: string;
  startDate?: string;
  endDate?: string;
  groupBy?: 'broker' | 'instrument' | 'hour' | 'day';
}

// Validation middleware
export const validateHistoryRequest = [
  query('instrument')
    .optional()
    .matches(/^[A-Z]{6}$/)
    .withMessage('Invalid instrument format'),
  
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid start date format (use ISO 8601)'),
  
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid end date format (use ISO 8601)'),
  
  query('limit')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Limit must be between 1 and 1000'),
  
  query('offset')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Offset must be non-negative'),
  
  query('sortBy')
    .optional()
    .isIn(['timestamp', 'pnl', 'instrument'])
    .withMessage('Invalid sortBy value'),
  
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Invalid sortOrder value')
];

export const validatePerformanceRequest = [
  query('strategyId')
    .optional()
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Invalid strategy ID format'),
  
  query('instrument')
    .optional()
    .matches(/^[A-Z]{6}$/)
    .withMessage('Invalid instrument format'),
  
  query('startDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid start date format'),
  
  query('endDate')
    .optional()
    .isISO8601()
    .withMessage('Invalid end date format'),
  
  query('groupBy')
    .optional()
    .isIn(['broker', 'instrument', 'hour', 'day'])
    .withMessage('Invalid groupBy value')
];

export class TradeHistoryController {
  constructor(
    private tradeHistoryService: TradeHistoryService,
    private performanceReporter: ExecutionPerformanceReporter
  ) {}

  async getTradeHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
        return;
      }

      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const query = req.query as GetHistoryQuery;
      
      // Build filters
      const filters: any = { userId };
      
      if (query.instrument) filters.instrument = query.instrument;
      if (query.status) filters.status = query.status;
      if (query.category) filters.category = query.category;
      if (query.startDate) filters.startDate = new Date(query.startDate);
      if (query.endDate) filters.endDate = new Date(query.endDate);

      const limit = query.limit ? parseInt(query.limit, 10) : 50;
      const offset = query.offset ? parseInt(query.offset, 10) : 0;
      const sortBy = query.sortBy || 'timestamp';
      const sortOrder = query.sortOrder || 'desc';

      console.log(`Getting trade history for user ${userId}:`, {
        filters,
        limit,
        offset,
        sortBy,
        sortOrder
      });

      // Get trade history
      const { records, total, summary } = await this.tradeHistoryService.getTradeHistory(
        filters,
        { limit, offset, sortBy, sortOrder }
      );

      // Convert Decimal fields to strings for JSON response
      const responseData = {
        success: true,
        data: {
          trades: records.map(record => ({
            ...record,
            // Convert performance attribution decimals
            performanceAttribution: {
              ...record.performanceAttribution,
              strategyContribution: record.performanceAttribution.strategyContribution.toString(),
              timingContribution: record.performanceAttribution.timingContribution.toString(),
              executionContribution: record.performanceAttribution.executionContribution.toString(),
              slippageImpact: record.performanceAttribution.slippageImpact.toString(),
              feeImpact: record.performanceAttribution.feeImpact.toString(),
              totalAttribution: record.performanceAttribution.totalAttribution.toString()
            }
          })),
          pagination: {
            total,
            limit,
            offset,
            hasMore: offset + limit < total
          },
          summary: {
            ...summary,
            totalPnL: summary.totalPnL.toString(),
            totalFees: summary.totalFees.toString(),
            averageTrade: summary.averageTrade.toString(),
            bestTrade: summary.bestTrade.toString(),
            worstTrade: summary.worstTrade.toString(),
            winRate: summary.winRate.toString(),
            profitFactor: summary.profitFactor.toString(),
            sharpeRatio: summary.sharpeRatio.toString()
          }
        }
      };

      res.status(200).json(responseData);

    } catch (error) {
      console.error('Get trade history error:', error);
      next(error);
    }
  }

  async getTradeDetails(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const { tradeId } = req.params;

      // Get detailed trade information
      const tradeDetails = await this.tradeHistoryService.getTradeDetails(tradeId, userId);

      if (!tradeDetails) {
        res.status(404).json({
          success: false,
          error: 'Trade not found'
        });
        return;
      }

      // Convert Decimal fields for JSON response
      const responseData = {
        success: true,
        data: {
          ...tradeDetails,
          // Convert any Decimal fields in the trade details
          performanceAttribution: {
            ...tradeDetails.performanceAttribution,
            strategyContribution: tradeDetails.performanceAttribution.strategyContribution.toString(),
            timingContribution: tradeDetails.performanceAttribution.timingContribution.toString(),
            executionContribution: tradeDetails.performanceAttribution.executionContribution.toString(),
            slippageImpact: tradeDetails.performanceAttribution.slippageImpact.toString(),
            feeImpact: tradeDetails.performanceAttribution.feeImpact.toString(),
            totalAttribution: tradeDetails.performanceAttribution.totalAttribution.toString()
          }
        }
      };

      res.status(200).json(responseData);

    } catch (error) {
      console.error('Get trade details error:', error);
      next(error);
    }
  }

  async getPerformanceMetrics(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
        return;
      }

      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const query = req.query as GetPerformanceQuery;
      
      const timeframe = {
        start: query.startDate ? new Date(query.startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // Default: 30 days ago
        end: query.endDate ? new Date(query.endDate) : new Date()
      };

      console.log(`Getting performance metrics for user ${userId}:`, {
        timeframe,
        strategyId: query.strategyId,
        instrument: query.instrument,
        groupBy: query.groupBy
      });

      // Get execution quality metrics
      const executionMetrics = await this.performanceReporter.generateExecutionCostAnalysis(
        timeframe,
        userId // portfolioId
      );

      // Get slippage analysis if instrument is specified
      let slippageAnalysis = null;
      if (query.instrument) {
        try {
          slippageAnalysis = await this.performanceReporter.generateSlippageAnalysis(
            query.instrument,
            timeframe
          );
        } catch (error) {
          console.warn('Failed to generate slippage analysis:', error);
        }
      }

      // Get trend analysis for key metrics
      const [latencyTrend, slippageTrend, costTrend] = await Promise.allSettled([
        this.performanceReporter.generateTrendAnalysis('latency', timeframe),
        this.performanceReporter.generateTrendAnalysis('slippage', timeframe),
        this.performanceReporter.generateTrendAnalysis('cost', timeframe)
      ]);

      // Convert Decimal fields to strings for JSON response
      const responseData = {
        success: true,
        data: {
          timeframe,
          executionMetrics: {
            ...executionMetrics,
            totalSlippageCost: executionMetrics.totalSlippageCost.toString(),
            totalBrokerFees: executionMetrics.totalBrokerFees.toString(),
            totalSpreadCost: executionMetrics.totalSpreadCost.toString(),
            opportunityCost: executionMetrics.opportunityCost.toString(),
            latencyCost: executionMetrics.latencyCost.toString(),
            failedExecutionCost: executionMetrics.failedExecutionCost.toString(),
            costAsPercentageOfVolume: executionMetrics.costAsPercentageOfVolume.toString(),
            costAsPercentageOfPnL: executionMetrics.costAsPercentageOfPnL.toString(),
            returnAfterCosts: executionMetrics.returnAfterCosts.toString(),
            identifiedSavings: executionMetrics.identifiedSavings.toString()
          },
          slippageAnalysis: slippageAnalysis ? {
            ...slippageAnalysis,
            averageSlippage: slippageAnalysis.averageSlippage.toString(),
            medianSlippage: slippageAnalysis.medianSlippage.toString(),
            slippageStdDev: slippageAnalysis.slippageStdDev.toString(),
            positiveSlippageRate: slippageAnalysis.positiveSlippageRate.toString(),
            negativeSlippageRate: slippageAnalysis.negativeSlippageRate.toString(),
            totalSlippageCost: slippageAnalysis.totalSlippageCost.toString(),
            averageCostPerTrade: slippageAnalysis.averageCostPerTrade.toString(),
            costAsPercentageOfPnL: slippageAnalysis.costAsPercentageOfPnL.toString(),
            recommendedSizeLimits: slippageAnalysis.recommendedSizeLimits.toString(),
            estimatedSavings: slippageAnalysis.estimatedSavings.toString()
          } : null,
          trends: {
            latency: latencyTrend.status === 'fulfilled' ? latencyTrend.value : null,
            slippage: slippageTrend.status === 'fulfilled' ? slippageTrend.value : null,
            cost: costTrend.status === 'fulfilled' ? costTrend.value : null
          }
        }
      };

      res.status(200).json(responseData);

    } catch (error) {
      console.error('Get performance metrics error:', error);
      next(error);
    }
  }

  async getPerformanceComparison(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const { strategyId } = req.query;
      
      if (!strategyId) {
        res.status(400).json({
          success: false,
          error: 'Strategy ID is required for performance comparison'
        });
        return;
      }

      const timeframe = {
        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        end: new Date()
      };

      console.log(`Getting performance comparison for strategy ${strategyId}, user ${userId}`);

      // Generate performance comparison
      const comparison = await this.performanceReporter.generatePerformanceComparison(
        strategyId as string,
        timeframe
      );

      // Convert Decimal fields for JSON response
      const responseData = {
        success: true,
        data: {
          ...comparison,
          winRateComparison: {
            ...comparison.winRateComparison,
            backtest: comparison.winRateComparison.backtest.toString(),
            live: comparison.winRateComparison.live.toString(),
            difference: comparison.winRateComparison.difference.toString()
          },
          returnComparison: {
            ...comparison.returnComparison,
            backtest: comparison.returnComparison.backtest.toString(),
            live: comparison.returnComparison.live.toString(),
            difference: comparison.returnComparison.difference.toString(),
            relativeDifference: comparison.returnComparison.relativeDifference.toString()
          },
          drawdownComparison: {
            ...comparison.drawdownComparison,
            backtest: comparison.drawdownComparison.backtest.toString(),
            live: comparison.drawdownComparison.live.toString(),
            difference: comparison.drawdownComparison.difference.toString()
          },
          slippageImpact: {
            ...comparison.slippageImpact,
            expected: comparison.slippageImpact.expected.toString(),
            actual: comparison.slippageImpact.actual.toString(),
            impact: comparison.slippageImpact.impact.toString(),
            costBasisPoints: comparison.slippageImpact.costBasisPoints.toString()
          },
          correlation: comparison.correlation.toString(),
          rSquared: comparison.rSquared.toString(),
          confidenceInterval: {
            ...comparison.confidenceInterval,
            lower: comparison.confidenceInterval.lower.toString(),
            upper: comparison.confidenceInterval.upper.toString()
          },
          recommendations: comparison.recommendations.map(rec => ({
            ...rec,
            expectedImprovement: rec.expectedImprovement.toString()
          }))
        }
      };

      res.status(200).json(responseData);

    } catch (error) {
      console.error('Get performance comparison error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('Insufficient data')) {
          res.status(404).json({
            success: false,
            error: error.message,
            code: 'INSUFFICIENT_DATA'
          });
          return;
        }
      }

      next(error);
    }
  }
}

// Factory function to create controller with dependencies
export const createTradeHistoryController = (
  tradeHistoryService: TradeHistoryService,
  performanceReporter: ExecutionPerformanceReporter
) => {
  return new TradeHistoryController(
    tradeHistoryService,
    performanceReporter
  );
};