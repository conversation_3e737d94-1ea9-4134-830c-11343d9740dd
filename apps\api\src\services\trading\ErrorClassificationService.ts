/**
 * Error Classification and Recovery Service
 * 
 * Comprehensive error taxonomy and automated recovery logic
 * Implements Task 3 from Story 4.3: Error Classification and Recovery System
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import type { 
  SystemError, 
  ErrorType, 
  ErrorCategory,
  CircuitState,
  CircuitBreakerConfig,
  CircuitBreakerState 
} from '@golddaddy/types';

// Standard error codes for the trading system
export const ERROR_CODES = {
  // Connection Errors
  MT5_CONNECTION_FAILED: 'MT5_CONNECTION_FAILED',
  BROKER_TIMEOUT: 'BROKER_TIMEOUT',
  NETWORK_ERROR: 'NETWORK_ERROR',
  CONNECTION_REFUSED: 'CONNECTION_REFUSED',
  
  // Execution Errors
  TRADE_EXECUTION_FAILED: 'TRADE_EXECUTION_FAILED',
  INSUFFICIENT_MARGIN: 'INSUFFICIENT_MARGIN',
  MARKET_CLOSED: 'MARKET_CLOSED',
  INVALID_VOLUME: 'INVALID_VOLUME',
  PRICE_CHANGED: 'PRICE_CHANGED',
  
  // System Errors
  BROKER_SERVICE_UNAVAILABLE: 'BROKER_SERVICE_UNAVAILABLE',
  FAILOVER_IN_PROGRESS: 'FAILOVER_IN_PROGRESS',
  ALL_BROKERS_DOWN: 'ALL_BROKERS_DOWN',
  DATABASE_ERROR: 'DATABASE_ERROR',
  
  // Authentication Errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  ACCOUNT_SUSPENDED: 'ACCOUNT_SUSPENDED',
  AUTHENTICATION_EXPIRED: 'AUTHENTICATION_EXPIRED',
  
  // Configuration Errors
  INVALID_BROKER_CONFIG: 'INVALID_BROKER_CONFIG',
  MISSING_CONFIGURATION: 'MISSING_CONFIGURATION',
  CONFIGURATION_VALIDATION_FAILED: 'CONFIGURATION_VALIDATION_FAILED',
} as const;

export type ErrorCode = keyof typeof ERROR_CODES;

export interface RetryConfiguration {
  maxAttempts: number;
  baseDelayMs: number;
  maxDelayMs: number;
  exponentialBase: number;
  jitterMs: number;
}

export interface RecoveryAction {
  name: string;
  description: string;
  execute: () => Promise<boolean>;
  prerequisites: string[];
  maxRetries: number;
}

export class ErrorClassificationService extends EventEmitter {
  private circuitBreakers: Map<string, CircuitBreakerState> = new Map();
  private errorCounts: Map<string, number> = new Map();
  private retryAttempts: Map<string, number> = new Map();
  
  private readonly defaultRetryConfig: RetryConfiguration = {
    maxAttempts: 3,
    baseDelayMs: 1000,
    maxDelayMs: 30000,
    exponentialBase: 2,
    jitterMs: 100
  };

  private readonly circuitBreakerConfig: CircuitBreakerConfig = {
    failureThreshold: 5,
    timeout: 60000, // 1 minute
    monitoringPeriod: 300000, // 5 minutes
    halfOpenMaxCalls: 3
  };

  constructor(private prisma: PrismaClient) {
    super();
  }

  /**
   * Classify an error and determine appropriate recovery actions
   */
  async classifyError(error: {
    component: string;
    errorCode?: string;
    message: string;
    stackTrace?: string;
    context?: Record<string, any>;
    userId?: string;
    sessionId?: string;
    requestId?: string;
  }): Promise<{
    classification: SystemError;
    recoveryActions: RecoveryAction[];
    shouldRetry: boolean;
    circuitBreakerTriggered: boolean;
  }> {
    console.log(`🔍 Classifying error from ${error.component}: ${error.message}`);

    try {
      // Create error classification
      const classification = await this.createErrorClassification(error);
      
      // Determine recovery actions
      const recoveryActions = this.getRecoveryActions(classification);
      
      // Check retry eligibility
      const shouldRetry = this.shouldRetryError(classification);
      
      // Check circuit breaker status
      const circuitBreakerTriggered = await this.checkCircuitBreaker(error.component, classification);
      
      // Save error to database
      await this.saveErrorRecord(classification);
      
      // Emit classification event
      this.emit('errorClassified', {
        classification,
        recoveryActions: recoveryActions.map(action => ({
          name: action.name,
          description: action.description
        })),
        shouldRetry,
        circuitBreakerTriggered
      });

      return {
        classification,
        recoveryActions,
        shouldRetry,
        circuitBreakerTriggered
      };

    } catch (classificationError) {
      console.error('Failed to classify error:', classificationError);
      throw classificationError;
    }
  }

  /**
   * Execute recovery actions for a classified error
   */
  async executeRecoveryActions(
    errorId: string,
    actions: RecoveryAction[]
  ): Promise<{ success: boolean; executedActions: string[]; failedActions: string[] }> {
    console.log(`🔧 Executing ${actions.length} recovery actions for error: ${errorId}`);

    const executedActions: string[] = [];
    const failedActions: string[] = [];

    for (const action of actions) {
      try {
        console.log(`🔄 Executing recovery action: ${action.name}`);
        
        const success = await this.executeRecoveryAction(action);
        
        if (success) {
          executedActions.push(action.name);
          console.log(`✅ Recovery action completed: ${action.name}`);
        } else {
          failedActions.push(action.name);
          console.error(`❌ Recovery action failed: ${action.name}`);
        }
        
      } catch (error) {
        console.error(`❌ Recovery action threw error: ${action.name}`, error);
        failedActions.push(action.name);
      }
    }

    // Update error record with recovery results
    await this.updateErrorRecovery(errorId, executedActions, failedActions);

    const success = failedActions.length === 0;
    
    this.emit('recoveryCompleted', {
      errorId,
      success,
      executedActions,
      failedActions
    });

    return { success, executedActions, failedActions };
  }

  /**
   * Retry an operation with exponential backoff
   */
  async retryWithBackoff<T>(
    operation: () => Promise<T>,
    operationId: string,
    config?: Partial<RetryConfiguration>
  ): Promise<T> {
    const retryConfig = { ...this.defaultRetryConfig, ...config };
    const currentAttempt = (this.retryAttempts.get(operationId) || 0) + 1;
    
    this.retryAttempts.set(operationId, currentAttempt);

    try {
      console.log(`🔄 Retry attempt ${currentAttempt}/${retryConfig.maxAttempts} for operation: ${operationId}`);
      
      const result = await operation();
      
      // Success - reset retry count
      this.retryAttempts.delete(operationId);
      console.log(`✅ Operation succeeded on attempt ${currentAttempt}: ${operationId}`);
      
      return result;
      
    } catch (error) {
      console.error(`❌ Operation failed on attempt ${currentAttempt}: ${operationId}`, error);
      
      if (currentAttempt >= retryConfig.maxAttempts) {
        // Max retries exceeded
        this.retryAttempts.delete(operationId);
        console.error(`🚨 Max retries exceeded for operation: ${operationId}`);
        throw error;
      }
      
      // Calculate delay with exponential backoff and jitter
      const exponentialDelay = retryConfig.baseDelayMs * Math.pow(retryConfig.exponentialBase, currentAttempt - 1);
      const jitter = Math.random() * retryConfig.jitterMs;
      const delay = Math.min(exponentialDelay + jitter, retryConfig.maxDelayMs);
      
      console.log(`⏳ Waiting ${delay}ms before retry ${currentAttempt + 1} for operation: ${operationId}`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      
      // Recursive retry
      return this.retryWithBackoff(operation, operationId, config);
    }
  }

  /**
   * Get circuit breaker state for a component
   */
  getCircuitBreakerState(component: string): CircuitState {
    const state = this.circuitBreakers.get(component);
    return state?.state || 'CLOSED';
  }

  /**
   * Manually open a circuit breaker
   */
  openCircuitBreaker(component: string, reason: string): void {
    console.log(`🔴 Manually opening circuit breaker for ${component}: ${reason}`);
    
    this.circuitBreakers.set(component, {
      state: 'OPEN',
      failureCount: this.circuitBreakerConfig.failureThreshold,
      nextAttempt: new Date(Date.now() + this.circuitBreakerConfig.timeout),
      lastFailure: new Date()
    });

    this.emit('circuitBreakerOpened', {
      component,
      reason,
      manual: true,
      timestamp: new Date()
    });
  }

  /**
   * Manually close a circuit breaker
   */
  closeCircuitBreaker(component: string, reason: string): void {
    console.log(`🟢 Manually closing circuit breaker for ${component}: ${reason}`);
    
    this.circuitBreakers.set(component, {
      state: 'CLOSED',
      failureCount: 0,
      nextAttempt: new Date(),
      lastFailure: undefined
    });

    this.emit('circuitBreakerClosed', {
      component,
      reason,
      manual: true,
      timestamp: new Date()
    });
  }

  /**
   * Get error statistics for a component
   */
  async getErrorStatistics(component: string, timeRangeMs: number = 3600000): Promise<{
    totalErrors: number;
    errorsByType: Record<ErrorType, number>;
    errorsByCategory: Record<ErrorCategory, number>;
    circuitBreakerState: CircuitState;
    averageRecoveryTime: number;
  }> {
    const since = new Date(Date.now() - timeRangeMs);

    const errors = await this.prisma.systemError.findMany({
      where: {
        component,
        createdAt: { gte: since }
      }
    });

    const errorsByType: Record<ErrorType, number> = {
      CONNECTION_ERROR: 0,
      EXECUTION_ERROR: 0,
      VALIDATION_ERROR: 0,
      SYSTEM_ERROR: 0,
      NETWORK_ERROR: 0,
      TIMEOUT_ERROR: 0
    };

    const errorsByCategory: Record<ErrorCategory, number> = {
      BROKER_ERROR: 0,
      TRADING_ERROR: 0,
      DATA_ERROR: 0,
      AUTHENTICATION_ERROR: 0,
      CONFIGURATION_ERROR: 0,
      INFRASTRUCTURE_ERROR: 0
    };

    let totalRecoveryTime = 0;
    let recoveredErrorsCount = 0;

    errors.forEach(error => {
      errorsByType[error.errorType as ErrorType]++;
      errorsByCategory[error.errorCategory as ErrorCategory]++;
      
      if (error.resolved && error.resolvedAt) {
        totalRecoveryTime += error.resolvedAt.getTime() - error.createdAt.getTime();
        recoveredErrorsCount++;
      }
    });

    const averageRecoveryTime = recoveredErrorsCount > 0 
      ? totalRecoveryTime / recoveredErrorsCount 
      : 0;

    return {
      totalErrors: errors.length,
      errorsByType,
      errorsByCategory,
      circuitBreakerState: this.getCircuitBreakerState(component),
      averageRecoveryTime
    };
  }

  // === Private Helper Methods ===

  private async createErrorClassification(error: {
    component: string;
    errorCode?: string;
    message: string;
    stackTrace?: string;
    context?: Record<string, any>;
    userId?: string;
    sessionId?: string;
    requestId?: string;
  }): Promise<SystemError> {
    const errorCode = error.errorCode || this.inferErrorCode(error.message, error.component);
    const { errorType, errorCategory } = this.classifyErrorCode(errorCode);

    return {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      errorCode,
      errorType,
      errorCategory,
      message: error.message,
      stackTrace: error.stackTrace,
      context: error.context,
      component: error.component,
      version: process.env.APP_VERSION,
      environment: process.env.NODE_ENV || 'development',
      userId: error.userId,
      sessionId: error.sessionId,
      requestId: error.requestId,
      handled: false,
      retryCount: 0,
      resolved: false,
      resolution: undefined,
      circuitState: this.getCircuitBreakerState(error.component),
      createdAt: new Date(),
      updatedAt: new Date(),
      resolvedAt: undefined
    };
  }

  private inferErrorCode(message: string, component: string): string {
    const messageUpper = message.toUpperCase();
    
    // Connection-related errors
    if (messageUpper.includes('CONNECTION') || messageUpper.includes('CONNECT')) {
      if (messageUpper.includes('TIMEOUT')) return ERROR_CODES.BROKER_TIMEOUT;
      if (messageUpper.includes('REFUSED')) return ERROR_CODES.CONNECTION_REFUSED;
      return ERROR_CODES.MT5_CONNECTION_FAILED;
    }
    
    // Network errors
    if (messageUpper.includes('NETWORK') || messageUpper.includes('UNREACHABLE')) {
      return ERROR_CODES.NETWORK_ERROR;
    }
    
    // Trading errors
    if (messageUpper.includes('MARGIN')) return ERROR_CODES.INSUFFICIENT_MARGIN;
    if (messageUpper.includes('MARKET CLOSED')) return ERROR_CODES.MARKET_CLOSED;
    if (messageUpper.includes('VOLUME')) return ERROR_CODES.INVALID_VOLUME;
    if (messageUpper.includes('EXECUTION')) return ERROR_CODES.TRADE_EXECUTION_FAILED;
    
    // Authentication errors
    if (messageUpper.includes('AUTHENTICATION') || messageUpper.includes('CREDENTIALS')) {
      return ERROR_CODES.INVALID_CREDENTIALS;
    }
    
    // Default to generic system error
    return ERROR_CODES.BROKER_SERVICE_UNAVAILABLE;
  }

  private classifyErrorCode(errorCode: string): { errorType: ErrorType; errorCategory: ErrorCategory } {
    // Connection errors
    if ([ERROR_CODES.MT5_CONNECTION_FAILED, ERROR_CODES.BROKER_TIMEOUT, ERROR_CODES.CONNECTION_REFUSED].includes(errorCode as any)) {
      return { errorType: 'CONNECTION_ERROR', errorCategory: 'BROKER_ERROR' };
    }
    
    // Network errors
    if (errorCode === ERROR_CODES.NETWORK_ERROR) {
      return { errorType: 'NETWORK_ERROR', errorCategory: 'INFRASTRUCTURE_ERROR' };
    }
    
    // Trading errors
    if ([ERROR_CODES.TRADE_EXECUTION_FAILED, ERROR_CODES.INSUFFICIENT_MARGIN, ERROR_CODES.MARKET_CLOSED, ERROR_CODES.INVALID_VOLUME, ERROR_CODES.PRICE_CHANGED].includes(errorCode as any)) {
      return { errorType: 'EXECUTION_ERROR', errorCategory: 'TRADING_ERROR' };
    }
    
    // Authentication errors
    if ([ERROR_CODES.INVALID_CREDENTIALS, ERROR_CODES.ACCOUNT_SUSPENDED, ERROR_CODES.AUTHENTICATION_EXPIRED].includes(errorCode as any)) {
      return { errorType: 'VALIDATION_ERROR', errorCategory: 'AUTHENTICATION_ERROR' };
    }
    
    // Configuration errors
    if ([ERROR_CODES.INVALID_BROKER_CONFIG, ERROR_CODES.MISSING_CONFIGURATION, ERROR_CODES.CONFIGURATION_VALIDATION_FAILED].includes(errorCode as any)) {
      return { errorType: 'VALIDATION_ERROR', errorCategory: 'CONFIGURATION_ERROR' };
    }
    
    // System errors (default)
    return { errorType: 'SYSTEM_ERROR', errorCategory: 'BROKER_ERROR' };
  }

  private getRecoveryActions(error: SystemError): RecoveryAction[] {
    const actions: RecoveryAction[] = [];

    switch (error.errorCode) {
      case ERROR_CODES.MT5_CONNECTION_FAILED:
        actions.push({
          name: 'reconnect_broker',
          description: 'Attempt to reconnect to the MT5 broker',
          execute: () => this.executeReconnection(error.component),
          prerequisites: [],
          maxRetries: 3
        });
        break;

      case ERROR_CODES.BROKER_TIMEOUT:
        actions.push({
          name: 'increase_timeout',
          description: 'Temporarily increase connection timeout',
          execute: () => this.adjustTimeout(error.component),
          prerequisites: [],
          maxRetries: 1
        });
        break;

      case ERROR_CODES.INSUFFICIENT_MARGIN:
        actions.push({
          name: 'check_margin_requirements',
          description: 'Verify account margin and adjust position size',
          execute: () => this.checkMarginRequirements(error.userId || ''),
          prerequisites: ['valid_account'],
          maxRetries: 1
        });
        break;

      case ERROR_CODES.INVALID_CREDENTIALS:
        actions.push({
          name: 'refresh_authentication',
          description: 'Refresh authentication tokens',
          execute: () => this.refreshAuthentication(error.component),
          prerequisites: [],
          maxRetries: 2
        });
        break;

      default:
        actions.push({
          name: 'generic_retry',
          description: 'Generic retry with exponential backoff',
          execute: () => Promise.resolve(true),
          prerequisites: [],
          maxRetries: 3
        });
    }

    return actions;
  }

  private shouldRetryError(error: SystemError): boolean {
    // Don't retry validation or configuration errors
    if (error.errorCategory === 'AUTHENTICATION_ERROR' || error.errorCategory === 'CONFIGURATION_ERROR') {
      return false;
    }
    
    // Don't retry if circuit breaker is open
    if (error.circuitState === 'OPEN') {
      return false;
    }
    
    // Check retry count
    return error.retryCount < this.defaultRetryConfig.maxAttempts;
  }

  private async checkCircuitBreaker(component: string, error: SystemError): Promise<boolean> {
    const currentState = this.circuitBreakers.get(component) || {
      state: 'CLOSED' as CircuitState,
      failureCount: 0,
      nextAttempt: new Date(),
      lastFailure: undefined
    };

    // Increment failure count for this error
    currentState.failureCount++;
    currentState.lastFailure = new Date();

    // Check if we should open the circuit breaker
    if (currentState.state === 'CLOSED' && currentState.failureCount >= this.circuitBreakerConfig.failureThreshold) {
      currentState.state = 'OPEN';
      currentState.nextAttempt = new Date(Date.now() + this.circuitBreakerConfig.timeout);
      
      console.log(`🔴 Circuit breaker OPENED for ${component} after ${currentState.failureCount} failures`);
      
      this.emit('circuitBreakerOpened', {
        component,
        reason: `Failure threshold exceeded (${currentState.failureCount})`,
        manual: false,
        timestamp: new Date()
      });
      
      this.circuitBreakers.set(component, currentState);
      return true;
    }

    // Check if we should transition from OPEN to HALF_OPEN
    if (currentState.state === 'OPEN' && Date.now() >= currentState.nextAttempt.getTime()) {
      currentState.state = 'HALF_OPEN';
      console.log(`🟡 Circuit breaker HALF-OPEN for ${component}`);
      
      this.emit('circuitBreakerHalfOpen', {
        component,
        timestamp: new Date()
      });
    }

    this.circuitBreakers.set(component, currentState);
    return currentState.state === 'OPEN';
  }

  private async saveErrorRecord(error: SystemError): Promise<void> {
    try {
      await this.prisma.systemError.create({
        data: {
          errorCode: error.errorCode,
          errorType: error.errorType,
          errorCategory: error.errorCategory,
          message: error.message,
          stackTrace: error.stackTrace,
          context: error.context || {},
          component: error.component,
          version: error.version,
          environment: error.environment,
          userId: error.userId,
          sessionId: error.sessionId,
          requestId: error.requestId,
          handled: error.handled,
          retryCount: error.retryCount,
          resolved: error.resolved,
          resolution: error.resolution,
          circuitState: error.circuitState
        }
      });
      
    } catch (saveError) {
      console.error('Failed to save error record:', saveError);
      // Don't throw - we don't want error classification to fail due to DB issues
    }
  }

  private async executeRecoveryAction(action: RecoveryAction): Promise<boolean> {
    try {
      return await action.execute();
    } catch (error) {
      console.error(`Recovery action ${action.name} failed:`, error);
      return false;
    }
  }

  private async updateErrorRecovery(
    errorId: string, 
    executedActions: string[], 
    failedActions: string[]
  ): Promise<void> {
    try {
      const resolved = failedActions.length === 0;
      const resolution = resolved 
        ? `Recovered via actions: ${executedActions.join(', ')}`
        : `Partial recovery. Failed actions: ${failedActions.join(', ')}`;

      await this.prisma.systemError.updateMany({
        where: { id: errorId },
        data: {
          handled: true,
          resolved,
          resolution,
          resolvedAt: resolved ? new Date() : undefined,
          updatedAt: new Date()
        }
      });
      
    } catch (error) {
      console.error('Failed to update error recovery status:', error);
    }
  }

  // Mock recovery action implementations
  private async executeReconnection(component: string): Promise<boolean> {
    console.log(`🔄 Executing reconnection for ${component}`);
    await new Promise(resolve => setTimeout(resolve, 1000));
    return Math.random() > 0.3; // 70% success rate
  }

  private async adjustTimeout(component: string): Promise<boolean> {
    console.log(`⏱️ Adjusting timeout for ${component}`);
    await new Promise(resolve => setTimeout(resolve, 200));
    return true; // Always successful
  }

  private async checkMarginRequirements(userId: string): Promise<boolean> {
    console.log(`💰 Checking margin requirements for user ${userId}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return Math.random() > 0.2; // 80% success rate
  }

  private async refreshAuthentication(component: string): Promise<boolean> {
    console.log(`🔐 Refreshing authentication for ${component}`);
    await new Promise(resolve => setTimeout(resolve, 800));
    return Math.random() > 0.1; // 90% success rate
  }
}