/**
 * Circuit Breaker Service for Broker Communication Failures
 * 
 * Implements the circuit breaker pattern to prevent cascading failures
 * and provide graceful degradation when broker communication fails.
 */

import { EventEmitter } from 'events';

export enum CircuitState {
  CLOSED = 'CLOSED',     // Normal operation
  OPEN = 'OPEN',         // Circuit is open, calls are failing fast
  HALF_OPEN = 'HALF_OPEN' // Testing if service has recovered
}

export interface CircuitBreakerConfig {
  failureThreshold: number;    // Number of failures to trigger open circuit
  recoveryTimeout: number;     // Time in ms before attempting recovery
  successThreshold: number;    // Successes needed to close circuit from half-open
  timeout: number;            // Request timeout in ms
  monitoringPeriod: number;   // Period to track failures in ms
}

export interface CircuitBreakerMetrics {
  state: CircuitState;
  failureCount: number;
  successCount: number;
  lastFailureTime?: Date;
  lastSuccessTime?: Date;
  totalRequests: number;
  rejectedRequests: number;
}

export class CircuitBreakerService extends EventEmitter {
  private circuits: Map<string, {
    state: CircuitState;
    failureCount: number;
    successCount: number;
    lastFailureTime?: Date;
    lastSuccessTime?: Date;
    nextAttemptTime?: Date;
    totalRequests: number;
    rejectedRequests: number;
    recentFailures: Date[];
  }> = new Map();

  private config: CircuitBreakerConfig;

  constructor(config: Partial<CircuitBreakerConfig> = {}) {
    super();
    
    this.config = {
      failureThreshold: 5,        // 5 failures
      recoveryTimeout: 60000,     // 1 minute
      successThreshold: 3,        // 3 successes
      timeout: 30000,            // 30 seconds
      monitoringPeriod: 300000,  // 5 minutes
      ...config
    };

    // Periodic cleanup of old failures
    setInterval(() => {
      this.cleanupOldFailures();
    }, this.config.monitoringPeriod);
  }

  /**
   * Execute a function with circuit breaker protection
   */
  async execute<T>(
    circuitId: string,
    operation: () => Promise<T>,
    fallback?: () => Promise<T>
  ): Promise<T> {
    const circuit = this.getOrCreateCircuit(circuitId);
    circuit.totalRequests++;

    // Check if circuit is open
    if (circuit.state === CircuitState.OPEN) {
      if (Date.now() < (circuit.nextAttemptTime?.getTime() || 0)) {
        circuit.rejectedRequests++;
        console.warn(`Circuit breaker OPEN for ${circuitId}, rejecting request`);
        
        if (fallback) {
          return await fallback();
        }
        throw new Error(`Circuit breaker is OPEN for service: ${circuitId}`);
      } else {
        // Move to half-open state
        circuit.state = CircuitState.HALF_OPEN;
        circuit.successCount = 0;
        console.log(`Circuit breaker moving to HALF_OPEN for ${circuitId}`);
      }
    }

    try {
      // Execute the operation with timeout
      const result = await this.executeWithTimeout(operation, this.config.timeout);
      
      // Record success
      await this.recordSuccess(circuitId);
      return result;

    } catch (error) {
      // Record failure
      await this.recordFailure(circuitId, error as Error);
      
      if (fallback) {
        return await fallback();
      }
      throw error;
    }
  }

  /**
   * Get circuit breaker metrics
   */
  getMetrics(circuitId: string): CircuitBreakerMetrics | null {
    const circuit = this.circuits.get(circuitId);
    if (!circuit) return null;

    return {
      state: circuit.state,
      failureCount: circuit.failureCount,
      successCount: circuit.successCount,
      lastFailureTime: circuit.lastFailureTime,
      lastSuccessTime: circuit.lastSuccessTime,
      totalRequests: circuit.totalRequests,
      rejectedRequests: circuit.rejectedRequests
    };
  }

  /**
   * Get all circuit breaker metrics
   */
  getAllMetrics(): Map<string, CircuitBreakerMetrics> {
    const metrics = new Map<string, CircuitBreakerMetrics>();
    
    for (const [circuitId, circuit] of this.circuits.entries()) {
      metrics.set(circuitId, {
        state: circuit.state,
        failureCount: circuit.failureCount,
        successCount: circuit.successCount,
        lastFailureTime: circuit.lastFailureTime,
        lastSuccessTime: circuit.lastSuccessTime,
        totalRequests: circuit.totalRequests,
        rejectedRequests: circuit.rejectedRequests
      });
    }

    return metrics;
  }

  /**
   * Manually reset a circuit breaker
   */
  reset(circuitId: string): void {
    const circuit = this.circuits.get(circuitId);
    if (circuit) {
      circuit.state = CircuitState.CLOSED;
      circuit.failureCount = 0;
      circuit.successCount = 0;
      circuit.recentFailures = [];
      delete circuit.nextAttemptTime;
      
      console.log(`Circuit breaker manually reset for ${circuitId}`);
      this.emit('circuitReset', { circuitId, timestamp: new Date() });
    }
  }

  /**
   * Force open a circuit breaker
   */
  forceOpen(circuitId: string): void {
    const circuit = this.getOrCreateCircuit(circuitId);
    circuit.state = CircuitState.OPEN;
    circuit.nextAttemptTime = new Date(Date.now() + this.config.recoveryTimeout);
    
    console.log(`Circuit breaker manually opened for ${circuitId}`);
    this.emit('circuitOpened', { circuitId, manual: true, timestamp: new Date() });
  }

  // === Private Methods ===

  private getOrCreateCircuit(circuitId: string) {
    if (!this.circuits.has(circuitId)) {
      this.circuits.set(circuitId, {
        state: CircuitState.CLOSED,
        failureCount: 0,
        successCount: 0,
        totalRequests: 0,
        rejectedRequests: 0,
        recentFailures: []
      });
    }
    return this.circuits.get(circuitId)!;
  }

  private async executeWithTimeout<T>(
    operation: () => Promise<T>,
    timeout: number
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Operation timed out after ${timeout}ms`));
      }, timeout);

      operation()
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  private async recordSuccess(circuitId: string): Promise<void> {
    const circuit = this.circuits.get(circuitId)!;
    circuit.successCount++;
    circuit.lastSuccessTime = new Date();

    if (circuit.state === CircuitState.HALF_OPEN) {
      if (circuit.successCount >= this.config.successThreshold) {
        // Move to closed state
        circuit.state = CircuitState.CLOSED;
        circuit.failureCount = 0;
        circuit.recentFailures = [];
        delete circuit.nextAttemptTime;
        
        console.log(`Circuit breaker CLOSED for ${circuitId} after recovery`);
        this.emit('circuitClosed', { circuitId, timestamp: new Date() });
      }
    }
  }

  private async recordFailure(circuitId: string, error: Error): Promise<void> {
    const circuit = this.circuits.get(circuitId)!;
    const now = new Date();
    
    circuit.failureCount++;
    circuit.lastFailureTime = now;
    circuit.recentFailures.push(now);

    // Clean up old failures outside monitoring period
    const cutoff = new Date(now.getTime() - this.config.monitoringPeriod);
    circuit.recentFailures = circuit.recentFailures.filter(time => time > cutoff);

    // Check if we should open the circuit
    if (circuit.state === CircuitState.CLOSED || circuit.state === CircuitState.HALF_OPEN) {
      if (circuit.recentFailures.length >= this.config.failureThreshold) {
        circuit.state = CircuitState.OPEN;
        circuit.nextAttemptTime = new Date(now.getTime() + this.config.recoveryTimeout);
        circuit.successCount = 0;
        
        console.error(`Circuit breaker OPENED for ${circuitId} after ${circuit.recentFailures.length} failures`);
        this.emit('circuitOpened', { 
          circuitId, 
          error: error.message, 
          failureCount: circuit.recentFailures.length,
          timestamp: now 
        });
      }
    }
  }

  private cleanupOldFailures(): void {
    const cutoff = new Date(Date.now() - this.config.monitoringPeriod);
    
    for (const [circuitId, circuit] of this.circuits.entries()) {
      const oldLength = circuit.recentFailures.length;
      circuit.recentFailures = circuit.recentFailures.filter(time => time > cutoff);
      
      if (oldLength > circuit.recentFailures.length) {
        console.log(`Cleaned up ${oldLength - circuit.recentFailures.length} old failures for circuit ${circuitId}`);
      }
    }
  }
}