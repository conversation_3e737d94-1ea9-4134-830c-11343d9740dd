/**
 * Unit tests for PerformanceMonitor
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  PerformanceMonitor,
  PerformanceConfig,
  ProcessingMetrics,
  PerformanceAlert,
  PerformanceThresholds,
} from './PerformanceMonitor';

// Mock performance.now()
vi.mock('perf_hooks', () => ({
  performance: {
    now: vi.fn(() => Date.now()),
  },
}));

describe('PerformanceMonitor', () => {
  let monitor: PerformanceMonitor;
  let testConfig: Partial<PerformanceConfig>;

  beforeEach(() => {
    testConfig = {
      thresholds: {
        dataProcessing: 100,
        websocketBroadcast: 50,
        cacheOperation: 25,
        databaseQuery: 75,
        apiRequest: 200,
        memoryThreshold: 256,
        gcThreshold: 128,
      },
      monitoringInterval: 1000,
      metricsRetentionPeriod: 60000, // 1 minute
      alertingEnabled: true,
      benchmarkValidation: true,
      memoryMonitoring: true,
      gcMonitoring: false, // Disable for testing
      maxMetricsHistory: 1000,
    };

    monitor = new PerformanceMonitor(testConfig);
  });

  afterEach(() => {
    monitor.shutdown();
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should create monitor with default configuration', () => {
      const defaultMonitor = new PerformanceMonitor();
      expect(defaultMonitor).toBeDefined();
      expect(defaultMonitor).toBeInstanceOf(PerformanceMonitor);
      defaultMonitor.shutdown();
    });

    it('should create monitor with custom configuration', () => {
      expect(monitor).toBeDefined();
      expect(monitor).toBeInstanceOf(PerformanceMonitor);
    });

    it('should be an EventEmitter', () => {
      expect(monitor.on).toBeTypeOf('function');
      expect(monitor.emit).toBeTypeOf('function');
      expect(monitor.removeAllListeners).toBeTypeOf('function');
    });
  });

  describe('Operation Tracking', () => {
    it('should track operation start and end', async () => {
      const operationId = 'test_operation_1';
      const operationType = 'data_processing';

      // Start tracking
      monitor.startOperation(operationId, operationType, { test: 'metadata' });

      // Simulate some processing time
      await new Promise(resolve => setTimeout(resolve, 10));

      // End tracking
      monitor.endOperation(operationId, true);

      const stats = monitor.getPerformanceStats();
      expect(stats.totalOperations).toBe(1);
      expect(stats.successfulOperations).toBe(1);
      expect(stats.operationsByType[operationType]).toBeDefined();
      expect(stats.operationsByType[operationType].count).toBe(1);
    });

    it('should handle failed operations', async () => {
      const operationId = 'failed_operation';
      const operationType = 'database_query';

      monitor.startOperation(operationId, operationType);
      await new Promise(resolve => setTimeout(resolve, 5));
      monitor.endOperation(operationId, false);

      const stats = monitor.getPerformanceStats();
      expect(stats.totalOperations).toBe(1);
      expect(stats.successfulOperations).toBe(0);
      expect(stats.failedOperations).toBe(1);
    });

    it('should handle multiple concurrent operations', async () => {
      const operations = [
        { id: 'op1', type: 'data_processing' as const },
        { id: 'op2', type: 'websocket_broadcast' as const },
        { id: 'op3', type: 'cache_operation' as const },
      ];

      // Start all operations
      operations.forEach(op => {
        monitor.startOperation(op.id, op.type);
      });

      await new Promise(resolve => setTimeout(resolve, 20));

      // End all operations
      operations.forEach(op => {
        monitor.endOperation(op.id, true);
      });

      const stats = monitor.getPerformanceStats();
      expect(stats.totalOperations).toBe(3);
      expect(stats.successfulOperations).toBe(3);
      expect(Object.keys(stats.operationsByType)).toHaveLength(3);
    });

    it('should warn for operations without trackers', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      monitor.endOperation('nonexistent_operation', true);
      
      expect(consoleSpy).toHaveBeenCalledWith('No tracker found for operation: nonexistent_operation');
      consoleSpy.mockRestore();
    });
  });

  describe('Benchmark Validation', () => {
    it('should validate benchmark configuration and thresholds', () => {
      // Test that benchmark validation is properly configured
      const stats = monitor.getPerformanceStats();
      expect(stats).toBeDefined();
      expect(stats.benchmarkViolations).toBe(0); // Should start with 0 violations
      
      // Test that thresholds are correctly set
      const health = monitor.getHealthStatus();
      expect(health).toBeDefined();
      expect(health.isHealthy).toBe(true); // Should be healthy with no operations
    });

    it('should not alert for operations within threshold', async () => {
      let alertEmitted = false;

      monitor.on('performance_alert', (alert: PerformanceAlert) => {
        if (alert.type === 'benchmark_violation') {
          alertEmitted = true;
        }
      });

      const operationId = 'fast_operation';
      monitor.startOperation(operationId, 'cache_operation');
      
      // Simulate operation within threshold (25ms)
      await new Promise(resolve => setTimeout(resolve, 10));
      
      monitor.endOperation(operationId, true);

      await new Promise(resolve => setTimeout(resolve, 10));

      expect(alertEmitted).toBe(false);
    });
  });

  describe('Performance Statistics', () => {
    it('should calculate accurate performance statistics', async () => {
      // Create multiple operations with varying durations
      const operations = [
        { id: 'op1', type: 'data_processing' as const, delay: 10 },
        { id: 'op2', type: 'data_processing' as const, delay: 20 },
        { id: 'op3', type: 'websocket_broadcast' as const, delay: 5 },
        { id: 'op4', type: 'cache_operation' as const, delay: 3 },
      ];

      for (const op of operations) {
        monitor.startOperation(op.id, op.type);
        await new Promise(resolve => setTimeout(resolve, op.delay));
        monitor.endOperation(op.id, true);
      }

      const stats = monitor.getPerformanceStats();

      expect(stats.totalOperations).toBe(4);
      expect(stats.successfulOperations).toBe(4);
      expect(stats.failedOperations).toBe(0);
      expect(stats.averageProcessingTime).toBeGreaterThan(0);
      expect(stats.medianProcessingTime).toBeGreaterThan(0);
      expect(stats.operationsByType['data_processing'].count).toBe(2);
      expect(stats.operationsByType['websocket_broadcast'].count).toBe(1);
      expect(stats.operationsByType['cache_operation'].count).toBe(1);
    });

    it('should calculate percentiles correctly', async () => {
      // Create operations with known durations for percentile testing
      const durations = [10, 20, 30, 40, 50, 60, 70, 80, 90, 100];

      for (let i = 0; i < durations.length; i++) {
        monitor.startOperation(`op_${i}`, 'data_processing');
        await new Promise(resolve => setTimeout(resolve, durations[i]));
        monitor.endOperation(`op_${i}`, true);
      }

      const stats = monitor.getPerformanceStats();

      expect(stats.totalOperations).toBe(10);
      expect(stats.p95ProcessingTime).toBeGreaterThanOrEqual(stats.medianProcessingTime);
      expect(stats.p99ProcessingTime).toBeGreaterThanOrEqual(stats.p95ProcessingTime);
    });
  });

  describe('System Metrics', () => {
    it('should collect system metrics', () => {
      const metrics = monitor.getSystemMetrics(5);
      
      // Should have collected at least some metrics during initialization
      expect(Array.isArray(metrics)).toBe(true);
      // Note: May be empty if monitoring interval hasn't fired yet
    });

    it('should limit system metrics returned', () => {
      const metrics = monitor.getSystemMetrics(3);
      expect(metrics.length).toBeLessThanOrEqual(3);
    });
  });

  describe('Health Status', () => {
    it('should report healthy status with no operations', () => {
      const health = monitor.getHealthStatus();
      
      expect(health.isHealthy).toBe(true);
      expect(health.issues).toHaveLength(0);
      expect(health.metrics).toBeDefined();
      expect(health.systemHealth).toBeDefined();
      expect(health.systemHealth.uptime).toBeGreaterThan(0);
    });

    it('should detect unhealthy status with high error rate', async () => {
      // Create mostly failed operations
      const operations = Array.from({ length: 10 }, (_, i) => ({
        id: `op_${i}`,
        success: i < 2, // Only first 2 succeed, 8 fail
      }));

      for (const op of operations) {
        monitor.startOperation(op.id, 'data_processing');
        await new Promise(resolve => setTimeout(resolve, 5));
        monitor.endOperation(op.id, op.success);
      }

      const health = monitor.getHealthStatus();
      
      expect(health.isHealthy).toBe(false);
      expect(health.issues.some(issue => issue.includes('error rate'))).toBe(true);
    });
  });

  describe('Alert System', () => {
    it('should emit operation completed events', async () => {
      let eventEmitted = false;
      let capturedMetrics: ProcessingMetrics | undefined;

      monitor.on('operation_completed', (metrics: ProcessingMetrics) => {
        eventEmitted = true;
        capturedMetrics = metrics;
      });

      const operationId = 'test_operation';
      monitor.startOperation(operationId, 'cache_operation', { test: 'data' });
      await new Promise(resolve => setTimeout(resolve, 10));
      monitor.endOperation(operationId, true);

      expect(eventEmitted).toBe(true);
      expect(capturedMetrics).toBeDefined();
      expect(capturedMetrics?.operationId).toBe(operationId);
      expect(capturedMetrics?.operationType).toBe('cache_operation');
      expect(capturedMetrics?.success).toBe(true);
      expect(capturedMetrics?.metadata?.test).toBe('data');
    });

    it('should support disabling alerting', async () => {
      const noAlertConfig = {
        ...testConfig,
        alertingEnabled: false,
      };

      const noAlertMonitor = new PerformanceMonitor(noAlertConfig);
      let alertEmitted = false;

      noAlertMonitor.on('performance_alert', () => {
        alertEmitted = true;
      });

      // Create operation that would normally trigger alert
      noAlertMonitor.startOperation('slow_op', 'data_processing');
      await new Promise(resolve => setTimeout(resolve, 200)); // Well over 100ms threshold
      noAlertMonitor.endOperation('slow_op', true);

      await new Promise(resolve => setTimeout(resolve, 10));

      expect(alertEmitted).toBe(false);
      noAlertMonitor.shutdown();
    });
  });

  describe('Memory Management', () => {
    it('should clean up old metrics', async () => {
      // Override retention period for testing
      const shortRetentionMonitor = new PerformanceMonitor({
        ...testConfig,
        metricsRetentionPeriod: 100, // 100ms retention
        monitoringInterval: 50, // 50ms monitoring
      });

      // Create some operations
      for (let i = 0; i < 5; i++) {
        shortRetentionMonitor.startOperation(`op_${i}`, 'data_processing');
        await new Promise(resolve => setTimeout(resolve, 5));
        shortRetentionMonitor.endOperation(`op_${i}`, true);
      }

      const initialStats = shortRetentionMonitor.getPerformanceStats();
      expect(initialStats.totalOperations).toBe(5);

      // Wait for metrics to expire and cleanup to run
      await new Promise(resolve => setTimeout(resolve, 200));

      const finalStats = shortRetentionMonitor.getPerformanceStats();
      // Note: Cleanup may not have run yet due to timing, so we just verify structure
      expect(finalStats).toBeDefined();
      
      shortRetentionMonitor.shutdown();
    });

    it('should reset metrics', async () => {
      // Create some operations
      monitor.startOperation('op1', 'data_processing');
      await new Promise(resolve => setTimeout(resolve, 10));
      monitor.endOperation('op1', true);

      const initialStats = monitor.getPerformanceStats();
      expect(initialStats.totalOperations).toBe(1);

      monitor.resetMetrics();

      const finalStats = monitor.getPerformanceStats();
      expect(finalStats.totalOperations).toBe(0);
      expect(finalStats.successfulOperations).toBe(0);
      expect(finalStats.failedOperations).toBe(0);
    });
  });

  describe('Configuration Management', () => {
    it('should update performance thresholds', () => {
      let thresholdsUpdated = false;

      monitor.on('thresholds_updated', () => {
        thresholdsUpdated = true;
      });

      const newThresholds = {
        dataProcessing: 200,
        websocketBroadcast: 100,
      };

      monitor.updateThresholds(newThresholds);

      expect(thresholdsUpdated).toBe(true);
    });

    it('should handle garbage collection request', () => {
      // Note: global.gc is not available in test environment
      const result = monitor.forceGarbageCollection();
      expect(typeof result).toBe('boolean');
    });
  });

  describe('Event Handling', () => {
    it('should support event registration', () => {
      const testHandler = vi.fn();

      expect(() => monitor.on('operation_completed', testHandler)).not.toThrow();
      expect(() => monitor.on('performance_alert', testHandler)).not.toThrow();
      expect(() => monitor.on('system_metrics', testHandler)).not.toThrow();
      expect(() => monitor.on('gc_event', testHandler)).not.toThrow();
    });

    it('should emit metrics reset event', () => {
      let resetEventEmitted = false;

      monitor.on('metrics_reset', () => {
        resetEventEmitted = true;
      });

      monitor.resetMetrics();

      expect(resetEventEmitted).toBe(true);
    });
  });

  describe('Resource Management', () => {
    it('should shutdown gracefully', () => {
      let shutdownEventEmitted = false;

      monitor.on('monitor_shutdown', () => {
        shutdownEventEmitted = true;
      });

      monitor.shutdown();

      expect(shutdownEventEmitted).toBe(true);
    });

    it('should handle multiple shutdowns', () => {
      monitor.shutdown();
      
      // Second shutdown should not throw
      expect(() => monitor.shutdown()).not.toThrow();
    });

    it('should clear event listeners on shutdown', () => {
      const testHandler = () => {};
      monitor.on('test_event', testHandler);

      monitor.shutdown();

      expect(monitor.listenerCount('test_event')).toBe(0);
    });
  });

  describe('Edge Cases', () => {
    it('should handle operations with zero duration', async () => {
      const operationId = 'instant_operation';
      
      monitor.startOperation(operationId, 'cache_operation');
      // End immediately without delay
      monitor.endOperation(operationId, true);

      const stats = monitor.getPerformanceStats();
      expect(stats.totalOperations).toBe(1);
      expect(stats.averageProcessingTime).toBeGreaterThanOrEqual(0);
    });

    it('should handle operations with metadata', async () => {
      const metadata = {
        instrument: 'EURUSD',
        recordCount: 100,
        source: 'MT5',
      };

      monitor.startOperation('op_with_metadata', 'data_processing', metadata);
      await new Promise(resolve => setTimeout(resolve, 10));
      monitor.endOperation('op_with_metadata', true);

      const stats = monitor.getPerformanceStats();
      expect(stats.totalOperations).toBe(1);
    });

    it('should handle different operation types', async () => {
      const operationTypes: ProcessingMetrics['operationType'][] = [
        'data_processing',
        'websocket_broadcast',
        'cache_operation',
        'database_query',
        'api_request',
      ];

      for (const type of operationTypes) {
        monitor.startOperation(`${type}_test`, type);
        await new Promise(resolve => setTimeout(resolve, 5));
        monitor.endOperation(`${type}_test`, true);
      }

      const stats = monitor.getPerformanceStats();
      expect(stats.totalOperations).toBe(operationTypes.length);
      expect(Object.keys(stats.operationsByType)).toHaveLength(operationTypes.length);

      operationTypes.forEach(type => {
        expect(stats.operationsByType[type]).toBeDefined();
        expect(stats.operationsByType[type].count).toBe(1);
      });
    });
  });
});