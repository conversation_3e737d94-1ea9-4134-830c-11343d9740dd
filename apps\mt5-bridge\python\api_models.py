"""
API Models for MT5 Bridge Service
Pydantic models for request/response validation and OpenAPI documentation
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Optional, Any, Union
from datetime import datetime
from enum import Enum

# =============================================================================
# Common Models
# =============================================================================

class ApiResponse(BaseModel):
    """Standard API response wrapper"""
    success: bool = Field(..., description="Whether the operation was successful")
    message: str = Field(..., description="Human-readable message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    timestamp: datetime = Field(default_factory=datetime.now, description="Response timestamp")

class ErrorResponse(BaseModel):
    """Standard error response"""
    success: bool = Field(False, description="Always false for errors")
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Human-readable error message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional error details")
    timestamp: datetime = Field(default_factory=datetime.now, description="Error timestamp")

# =============================================================================
# Health & Status Models
# =============================================================================

class HealthStatus(str, Enum):
    """System health status levels"""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"

class ComponentHealth(BaseModel):
    """Individual component health status"""
    name: str = Field(..., description="Component name")
    status: HealthStatus = Field(..., description="Health status")
    message: str = Field(..., description="Status message")
    last_check: datetime = Field(..., description="Last health check timestamp")
    metrics: Optional[Dict[str, Any]] = Field(None, description="Component-specific metrics")

class SystemHealthResponse(BaseModel):
    """System-wide health status response"""
    overall_status: HealthStatus = Field(..., description="Overall system health")
    components: List[ComponentHealth] = Field(..., description="Individual component statuses")
    uptime_seconds: float = Field(..., description="System uptime in seconds")
    version: str = Field(..., description="Service version")
    timestamp: datetime = Field(default_factory=datetime.now, description="Health check timestamp")

# =============================================================================
# Market Data Models
# =============================================================================

class PriceData(BaseModel):
    """Real-time price data"""
    symbol: str = Field(..., description="Trading symbol")
    bid: float = Field(..., description="Bid price")
    ask: float = Field(..., description="Ask price")
    timestamp: datetime = Field(..., description="Price timestamp")
    volume: int = Field(0, description="Tick volume")
    spread: float = Field(..., description="Bid-ask spread")
    source: str = Field("mt5", description="Data source")

class OHLCVData(BaseModel):
    """OHLCV candle data"""
    timestamp: datetime = Field(..., description="Candle timestamp")
    instrument: str = Field(..., description="Trading instrument")
    timeframe: str = Field(..., description="Timeframe (1m, 5m, 1h, etc.)")
    open: float = Field(..., description="Opening price")
    high: float = Field(..., description="Highest price")
    low: float = Field(..., description="Lowest price")
    close: float = Field(..., description="Closing price")
    volume: int = Field(..., description="Volume")
    source: str = Field("mt5", description="Data source")

class HistoricalDataRequest(BaseModel):
    """Request for historical market data"""
    symbol: str = Field(..., description="Trading symbol", example="EURUSD")
    timeframe: str = Field(..., description="Timeframe", example="1h")
    start_date: datetime = Field(..., description="Start date for data collection")
    end_date: Optional[datetime] = Field(None, description="End date (defaults to now)")
    include_current: bool = Field(True, description="Include current incomplete candle")
    
    @validator('timeframe')
    def validate_timeframe(cls, v):
        valid_timeframes = ['1m', '5m', '15m', '30m', '1h', '4h', '1d']
        if v not in valid_timeframes:
            raise ValueError(f'Invalid timeframe. Must be one of: {valid_timeframes}')
        return v

class MarketDataResponse(BaseModel):
    """Historical market data response"""
    symbol: str = Field(..., description="Trading symbol")
    timeframe: str = Field(..., description="Timeframe")
    data: List[OHLCVData] = Field(..., description="OHLCV data points")
    count: int = Field(..., description="Number of data points")
    start_time: datetime = Field(..., description="First data point timestamp")
    end_time: datetime = Field(..., description="Last data point timestamp")

# =============================================================================
# Trading Models
# =============================================================================

class OrderType(str, Enum):
    """Order types for trading"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderSide(str, Enum):
    """Order sides"""
    BUY = "buy"
    SELL = "sell"

class TradeRequest(BaseModel):
    """Trade execution request"""
    symbol: str = Field(..., description="Trading symbol", example="EURUSD")
    side: OrderSide = Field(..., description="Order side (buy/sell)")
    order_type: OrderType = Field(..., description="Order type")
    quantity: float = Field(..., gt=0, description="Trade quantity")
    price: Optional[float] = Field(None, description="Limit price (for limit orders)")
    stop_loss: Optional[float] = Field(None, description="Stop loss price")
    take_profit: Optional[float] = Field(None, description="Take profit price")
    expiry: Optional[datetime] = Field(None, description="Order expiry time")
    client_order_id: Optional[str] = Field(None, description="Client-provided order ID")

class TradeResponse(BaseModel):
    """Trade execution response"""
    order_id: str = Field(..., description="System order ID")
    client_order_id: Optional[str] = Field(None, description="Client order ID")
    symbol: str = Field(..., description="Trading symbol")
    side: OrderSide = Field(..., description="Order side")
    status: str = Field(..., description="Order status")
    filled_quantity: float = Field(..., description="Filled quantity")
    average_price: Optional[float] = Field(None, description="Average fill price")
    timestamp: datetime = Field(..., description="Execution timestamp")

class PortfolioPosition(BaseModel):
    """Portfolio position"""
    symbol: str = Field(..., description="Trading symbol")
    quantity: float = Field(..., description="Position quantity (+ for long, - for short)")
    average_price: float = Field(..., description="Average entry price")
    current_price: float = Field(..., description="Current market price")
    unrealized_pnl: float = Field(..., description="Unrealized P&L")
    realized_pnl: float = Field(..., description="Realized P&L")
    timestamp: datetime = Field(..., description="Last update timestamp")

class PortfolioSummary(BaseModel):
    """Portfolio summary"""
    total_balance: float = Field(..., description="Total account balance")
    available_balance: float = Field(..., description="Available trading balance")
    total_pnl: float = Field(..., description="Total P&L")
    positions: List[PortfolioPosition] = Field(..., description="Open positions")
    position_count: int = Field(..., description="Number of open positions")
    last_updated: datetime = Field(..., description="Last portfolio update")

# =============================================================================
# Performance Models
# =============================================================================

class LatencyStats(BaseModel):
    """Latency statistics"""
    operation: str = Field(..., description="Operation name")
    mean_ms: float = Field(..., description="Mean latency in milliseconds")
    median_ms: float = Field(..., description="Median latency in milliseconds")
    p95_ms: float = Field(..., description="95th percentile latency")
    p99_ms: float = Field(..., description="99th percentile latency")
    min_ms: float = Field(..., description="Minimum latency")
    max_ms: float = Field(..., description="Maximum latency")
    sample_count: int = Field(..., description="Number of samples")

class PerformanceMetrics(BaseModel):
    """System performance metrics"""
    timestamp: datetime = Field(..., description="Metrics timestamp")
    cpu_percent: float = Field(..., description="CPU usage percentage")
    memory_percent: float = Field(..., description="Memory usage percentage")
    network_io_mbps: float = Field(..., description="Network I/O in MB/s")
    disk_io_mbps: float = Field(..., description="Disk I/O in MB/s")
    active_connections: int = Field(..., description="Active WebSocket connections")
    requests_per_second: float = Field(..., description="Request rate")

class BenchmarkRequest(BaseModel):
    """Performance benchmark request"""
    operation: str = Field(..., description="Operation to benchmark")
    iterations: int = Field(100, ge=1, le=10000, description="Number of iterations")
    warmup_iterations: int = Field(10, ge=0, le=100, description="Warmup iterations")

class BenchmarkResponse(BaseModel):
    """Performance benchmark results"""
    operation: str = Field(..., description="Benchmarked operation")
    statistics: LatencyStats = Field(..., description="Latency statistics")
    throughput_ops_per_sec: float = Field(..., description="Operations per second")
    success_rate: float = Field(..., description="Success rate (0.0 - 1.0)")
    benchmark_duration_ms: float = Field(..., description="Total benchmark duration")

# =============================================================================
# Data Management Models
# =============================================================================

class DataValidationResult(BaseModel):
    """Data validation result"""
    passed: bool = Field(..., description="Whether validation passed")
    errors: List[str] = Field(..., description="Validation errors")
    warnings: List[str] = Field(..., description="Validation warnings")
    data_points_checked: int = Field(..., description="Number of data points validated")
    timestamp: datetime = Field(..., description="Validation timestamp")

class SyncStatusRequest(BaseModel):
    """Data synchronization status request"""
    symbol: Optional[str] = Field(None, description="Filter by symbol")
    timeframe: Optional[str] = Field(None, description="Filter by timeframe")
    limit: int = Field(10, ge=1, le=100, description="Maximum results to return")

class SyncStatus(BaseModel):
    """Data synchronization status"""
    symbol: str = Field(..., description="Trading symbol")
    timeframe: str = Field(..., description="Timeframe")
    status: str = Field(..., description="Sync status")
    last_sync_time: Optional[datetime] = Field(None, description="Last successful sync")
    records_synced: int = Field(0, description="Total records synced")
    error_message: Optional[str] = Field(None, description="Last error message")

# =============================================================================
# WebSocket Models
# =============================================================================

class WebSocketMessage(BaseModel):
    """WebSocket message format"""
    type: str = Field(..., description="Message type")
    data: Dict[str, Any] = Field(..., description="Message data")
    timestamp: datetime = Field(default_factory=datetime.now, description="Message timestamp")

class SubscriptionRequest(BaseModel):
    """WebSocket subscription request"""
    action: str = Field(..., description="Action: 'subscribe' or 'unsubscribe'")
    symbols: List[str] = Field(..., description="Symbols to subscribe/unsubscribe")
    
    @validator('action')
    def validate_action(cls, v):
        if v not in ['subscribe', 'unsubscribe']:
            raise ValueError("Action must be 'subscribe' or 'unsubscribe'")
        return v

class SubscriptionResponse(BaseModel):
    """WebSocket subscription response"""
    success: bool = Field(..., description="Whether subscription was successful")
    subscribed_symbols: List[str] = Field(..., description="Currently subscribed symbols")
    message: str = Field(..., description="Response message")

# =============================================================================
# Configuration Models
# =============================================================================

class ServiceConfiguration(BaseModel):
    """Service configuration"""
    mt5_server: str = Field(..., description="MT5 server address")
    mt5_login: str = Field(..., description="MT5 login")
    database_url: str = Field(..., description="Database connection URL")
    redis_url: Optional[str] = Field(None, description="Redis connection URL")
    log_level: str = Field("INFO", description="Logging level")
    max_connections: int = Field(100, description="Maximum concurrent connections")
    rate_limit_per_minute: int = Field(1000, description="API rate limit per minute")

# =============================================================================
# Response Examples
# =============================================================================

EXAMPLE_RESPONSES = {
    "health_check": {
        "description": "Successful health check",
        "content": {
            "application/json": {
                "example": {
                    "overall_status": "healthy",
                    "components": [
                        {
                            "name": "mt5_connection",
                            "status": "healthy",
                            "message": "Connected to MT5 server",
                            "last_check": "2023-10-01T12:00:00Z",
                            "metrics": {"latency_ms": 15.2}
                        }
                    ],
                    "uptime_seconds": 3600,
                    "version": "1.0.0",
                    "timestamp": "2023-10-01T12:00:00Z"
                }
            }
        }
    },
    "price_data": {
        "description": "Current price data",
        "content": {
            "application/json": {
                "example": {
                    "symbol": "EURUSD",
                    "bid": 1.0950,
                    "ask": 1.0952,
                    "timestamp": "2023-10-01T12:00:00Z",
                    "volume": 1000,
                    "spread": 0.0002,
                    "source": "mt5"
                }
            }
        }
    },
    "error_response": {
        "description": "Error response",
        "content": {
            "application/json": {
                "example": {
                    "success": False,
                    "error": "ValidationError",
                    "message": "Invalid symbol format",
                    "details": {
                        "field": "symbol",
                        "provided_value": "INVALID_SYMBOL"
                    },
                    "timestamp": "2023-10-01T12:00:00Z"
                }
            }
        }
    }
}