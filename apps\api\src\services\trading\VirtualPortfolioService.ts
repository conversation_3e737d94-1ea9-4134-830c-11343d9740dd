import {
  VirtualPortfolio,
  VirtualPosition,
  PaperTrade,
  PaperTradeRequest,
  PaperTradeExecutionResult,
  PortfolioRiskMetrics,
  TradeType,
  TradeStatus
} from '@golddaddy/types';
import Decimal from 'decimal.js';

/**
 * Portfolio validation result
 */
export interface PortfolioValidationResult {
  approved: boolean;
  reason?: string;
  riskLevel?: 'low' | 'medium' | 'high' | 'extreme';
  recommendations?: string[];
}

/**
 * Portfolio update result
 */
export interface PortfolioUpdateResult {
  portfolio: VirtualPortfolio;
  position?: VirtualPosition;
  pnlImpact: number;
  riskImpact: number;
}

/**
 * Real-time P&L calculation result
 */
export interface RealTimePnLResult {
  unrealizedPnl: number;
  realizedPnl: number;
  totalPnl: number;
  dailyPnl: number;
  portfolioValue: number;
  marginUsed: number;
  availableMargin: number;
  marginLevel: number;
}

/**
 * Portfolio snapshot for historical tracking
 */
export interface PortfolioSnapshot {
  timestamp: Date;
  balance: number;
  totalPnl: number;
  unrealizedPnl: number;
  realizedPnl: number;
  positionCount: number;
  riskMetrics: PortfolioRiskMetrics;
  marketValue: number;
}

/**
 * Virtual Portfolio Management Service
 * 
 * Manages virtual portfolios for paper trading including:
 * - Real-time P&L calculation using Decimal.js for precision
 * - Position tracking with mark-to-market valuation
 * - Portfolio risk metrics calculation
 * - Virtual account balance management
 * - Portfolio history tracking with snapshots
 */
export class VirtualPortfolioService {
  constructor(
    private priceService: MarketDataPriceService
  ) {}

  /**
   * Create a new virtual portfolio for a paper trading session
   */
  async createVirtualPortfolio(
    userId: string,
    sessionId: string,
    initialBalance: number
  ): Promise<VirtualPortfolio> {
    const portfolio: VirtualPortfolio = {
      id: this.generateId(),
      userId,
      sessionId,
      initialBalance,
      currentBalance: initialBalance,
      availableMargin: initialBalance,
      usedMargin: 0,
      unrealizedPnl: 0,
      realizedPnl: 0,
      totalPnl: 0,
      positions: [],
      dailyPnl: [],
      riskMetrics: {
        maxDrawdown: 0,
        currentDrawdown: 0,
        riskPercentage: 0,
        portfolioVar: 0
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // TODO: Save to database using Supabase MCP tool
    
    return portfolio;
  }

  /**
   * Validate if a trade can be executed within portfolio limits
   */
  async validateTrade(
    userId: string,
    request: PaperTradeRequest,
    simulatedExecution: PaperTradeExecutionResult
  ): Promise<PortfolioValidationResult> {
    try {
      // Get current portfolio state
      const portfolio = await this.getVirtualPortfolio(userId, request.sessionId || 'default');
      if (!portfolio) {
        return {
          approved: false,
          reason: 'No active virtual portfolio found'
        };
      }

      const tradeValue = new Decimal(request.quantity).mul(simulatedExecution.executionPrice);
      const totalCost = tradeValue.plus(simulatedExecution.fees);

      // 1. Check available balance for buy orders
      if (request.type === TradeType.BUY && totalCost.gt(portfolio.availableMargin)) {
        return {
          approved: false,
          reason: `Insufficient margin. Required: $${totalCost.toFixed(2)}, Available: $${portfolio.availableMargin}`,
          riskLevel: 'extreme',
          recommendations: ['Reduce position size', 'Add funds to portfolio', 'Close existing positions']
        };
      }

      // 2. Check position limits (max 10 positions)
      if (portfolio.positions.length >= 10) {
        return {
          approved: false,
          reason: 'Maximum position limit reached (10 positions)',
          riskLevel: 'high',
          recommendations: ['Close some existing positions', 'Consolidate similar positions']
        };
      }

      // 3. Check single position size limit (max 20% of portfolio)
      const positionSizePercent = tradeValue.div(portfolio.initialBalance).mul(100);
      if (positionSizePercent.gt(20)) {
        return {
          approved: false,
          reason: `Position size too large (${positionSizePercent.toFixed(1)}%). Maximum allowed: 20%`,
          riskLevel: 'extreme',
          recommendations: ['Reduce position size', 'Increase portfolio capital']
        };
      }

      // 4. Check instrument concentration (max 30% in single instrument)
      const existingExposure = this.calculateInstrumentExposure(portfolio, request.instrument);
      const newExposure = existingExposure.plus(tradeValue);
      const concentrationPercent = newExposure.div(portfolio.initialBalance).mul(100);
      
      if (concentrationPercent.gt(30)) {
        return {
          approved: false,
          reason: `Instrument concentration too high (${concentrationPercent.toFixed(1)}%). Maximum allowed: 30%`,
          riskLevel: 'high',
          recommendations: ['Diversify into other instruments', 'Reduce position size']
        };
      }

      // 5. Check margin utilization
      const marginUtilization = new Decimal(portfolio.usedMargin).div(portfolio.availableMargin + portfolio.usedMargin).mul(100);
      if (marginUtilization.gt(80)) {
        return {
          approved: false,
          reason: `Margin utilization too high (${marginUtilization.toFixed(1)}%). Maximum recommended: 80%`,
          riskLevel: 'high',
          recommendations: ['Close some positions', 'Reduce leverage']
        };
      }

      // 6. Risk assessment
      let riskLevel: 'low' | 'medium' | 'high' | 'extreme' = 'low';
      const recommendations: string[] = [];

      if (positionSizePercent.gt(10)) {
        riskLevel = 'medium';
        recommendations.push('Consider reducing position size for better risk management');
      }

      if (concentrationPercent.gt(20)) {
        riskLevel = 'medium';
        recommendations.push('Consider diversifying your portfolio');
      }

      if (marginUtilization.gt(60)) {
        riskLevel = 'medium';
        recommendations.push('Monitor margin levels carefully');
      }

      return {
        approved: true,
        riskLevel,
        recommendations: recommendations.length > 0 ? recommendations : undefined
      };

    } catch (error) {
      console.error('Trade validation failed:', error);
      return {
        approved: false,
        reason: 'Validation failed due to system error'
      };
    }
  }

  /**
   * Execute a validated trade and update portfolio
   */
  async executeTrade(
    userId: string,
    request: PaperTradeRequest,
    simulatedExecution: PaperTradeExecutionResult
  ): Promise<PaperTrade> {
    const portfolio = await this.getVirtualPortfolio(userId, request.sessionId || 'default');
    if (!portfolio) {
      throw new Error('No active virtual portfolio found');
    }

    // Create paper trade record
    const paperTrade: PaperTrade = {
      id: this.generateId(),
      userId,
      sessionId: request.sessionId || 'default',
      strategyId: request.strategyId,
      goalId: request.goalId,
      accountId: portfolio.id,
      symbol: request.instrument,
      type: request.type,
      volume: request.quantity,
      openPrice: simulatedExecution.executionPrice,
      closePrice: undefined,
      stopLoss: request.stopLoss,
      takeProfit: request.takeProfit,
      profit: undefined,
      commission: simulatedExecution.fees,
      swap: 0,
      openTime: simulatedExecution.filledAt,
      closeTime: undefined,
      status: TradeStatus.OPEN,
      simulationMetadata: {
        actualSlippage: simulatedExecution.slippage,
        simulatedSlippage: simulatedExecution.slippage,
        spreadAtExecution: 0, // Would be calculated from bid/ask
        latencySimulated: 100, // From execution simulation
        marketConditionsSnapshot: {
          volatility: 0.02, // Mock data - would come from market service
          liquidity: 0.8,
          trend: 'up'
        }
      },
      portfolioImpact: {
        preTradeBalance: portfolio.currentBalance,
        postTradeBalance: portfolio.currentBalance,
        marginUsed: 0,
        availableMargin: portfolio.availableMargin
      },
      learningMetadata: {
        strategyDeviation: 0,
        expectedVsActual: {
          expectedPnl: 0,
          actualPnl: 0,
          variance: 0
        },
        riskManagementScore: 85
      },
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Update portfolio with new position
    await this.updatePortfolioWithTrade(portfolio, paperTrade);

    // Save trade and updated portfolio
    // TODO: Save to database using Supabase MCP tool

    return paperTrade;
  }

  /**
   * Calculate real-time P&L for portfolio using current market prices
   */
  async calculateRealTimePnL(
    userId: string,
    sessionId: string
  ): Promise<RealTimePnLResult> {
    const portfolio = await this.getVirtualPortfolio(userId, sessionId);
    if (!portfolio) {
      throw new Error('Portfolio not found');
    }

    let totalUnrealizedPnl = new Decimal(0);
    let marginUsed = new Decimal(0);

    // Calculate P&L for all open positions
    for (const position of portfolio.positions) {
      const currentPrice = await this.priceService.getCurrentPrice(position.instrument);
      
      // Mark-to-market valuation
      const currentValue = new Decimal(position.quantity).mul(currentPrice.price);
      const bookValue = new Decimal(position.quantity).mul(position.avgEntryPrice);
      const unrealizedPnl = currentValue.minus(bookValue);
      
      totalUnrealizedPnl = totalUnrealizedPnl.plus(unrealizedPnl);
      marginUsed = marginUsed.plus(position.marginRequired);

      // Update position with current values
      position.currentPrice = currentPrice.price;
      position.unrealizedPnl = unrealizedPnl.toNumber();
    }

    const portfolioValue = new Decimal(portfolio.currentBalance).plus(totalUnrealizedPnl);
    const availableMargin = new Decimal(portfolio.availableMargin).minus(marginUsed);
    const marginLevel = marginUsed.gt(0) ? portfolioValue.div(marginUsed).mul(100).toNumber() : 100;

    // Calculate daily P&L
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);
    const dailyPnl = this.calculateDailyPnL(portfolio, todayStart);

    const result: RealTimePnLResult = {
      unrealizedPnl: totalUnrealizedPnl.toNumber(),
      realizedPnl: portfolio.realizedPnl,
      totalPnl: totalUnrealizedPnl.plus(portfolio.realizedPnl).toNumber(),
      dailyPnl,
      portfolioValue: portfolioValue.toNumber(),
      marginUsed: marginUsed.toNumber(),
      availableMargin: availableMargin.toNumber(),
      marginLevel
    };

    // Update portfolio with latest values
    portfolio.unrealizedPnl = result.unrealizedPnl;
    portfolio.totalPnl = result.totalPnl;
    portfolio.usedMargin = result.marginUsed;
    portfolio.availableMargin = result.availableMargin;
    portfolio.updatedAt = new Date();

    // TODO: Save updated portfolio to database

    return result;
  }

  /**
   * Calculate portfolio risk metrics
   */
  async calculateRiskMetrics(
    userId: string,
    sessionId: string
  ): Promise<PortfolioRiskMetrics> {
    const portfolio = await this.getVirtualPortfolio(userId, sessionId);
    if (!portfolio) {
      throw new Error('Portfolio not found');
    }

    // Calculate current drawdown
    const currentValue = new Decimal(portfolio.currentBalance).plus(portfolio.unrealizedPnl);
    const peakValue = this.calculatePeakValue(portfolio);
    const currentDrawdown = peakValue.gt(currentValue) 
      ? peakValue.minus(currentValue).div(peakValue).mul(100).toNumber()
      : 0;

    // Update max drawdown
    const maxDrawdown = Math.max(portfolio.riskMetrics.maxDrawdown, currentDrawdown);

    // Calculate risk percentage (used margin as % of equity)
    const equity = currentValue.toNumber();
    const riskPercentage = equity > 0 ? (portfolio.usedMargin / equity) * 100 : 0;

    // Calculate portfolio VaR (simplified 1-day 95% VaR)
    const portfolioVar = await this.calculatePortfolioVaR(portfolio);

    const riskMetrics: PortfolioRiskMetrics = {
      maxDrawdown,
      currentDrawdown,
      riskPercentage,
      portfolioVar
    };

    // Update portfolio risk metrics
    portfolio.riskMetrics = riskMetrics;
    portfolio.updatedAt = new Date();

    // TODO: Save updated portfolio to database

    return riskMetrics;
  }

  /**
   * Create portfolio snapshot for historical tracking
   */
  async createPortfolioSnapshot(
    userId: string,
    sessionId: string
  ): Promise<PortfolioSnapshot> {
    const portfolio = await this.getVirtualPortfolio(userId, sessionId);
    if (!portfolio) {
      throw new Error('Portfolio not found');
    }

    const pnlResult = await this.calculateRealTimePnL(userId, sessionId);
    const riskMetrics = await this.calculateRiskMetrics(userId, sessionId);

    const snapshot: PortfolioSnapshot = {
      timestamp: new Date(),
      balance: portfolio.currentBalance,
      totalPnl: pnlResult.totalPnl,
      unrealizedPnl: pnlResult.unrealizedPnl,
      realizedPnl: pnlResult.realizedPnl,
      positionCount: portfolio.positions.length,
      riskMetrics,
      marketValue: pnlResult.portfolioValue
    };

    // TODO: Save snapshot to database

    return snapshot;
  }

  /**
   * Get virtual portfolio balance information
   */
  async getVirtualBalance(
    userId: string,
    sessionId: string
  ): Promise<{
    balance: number;
    usedMargin: number;
    availableMargin: number;
  }> {
    const portfolio = await this.getVirtualPortfolio(userId, sessionId);
    if (!portfolio) {
      throw new Error('Portfolio not found');
    }

    return {
      balance: portfolio.currentBalance,
      usedMargin: portfolio.usedMargin,
      availableMargin: portfolio.availableMargin
    };
  }

  // Private helper methods

  private async getVirtualPortfolio(
    userId: string,
    sessionId: string
  ): Promise<VirtualPortfolio | null> {
    // TODO: Fetch from database using Supabase MCP tool
    // Mock implementation for now
    return null;
  }

  private calculateInstrumentExposure(
    portfolio: VirtualPortfolio,
    instrument: string
  ): Decimal {
    let exposure = new Decimal(0);
    
    for (const position of portfolio.positions) {
      if (position.instrument === instrument) {
        const positionValue = new Decimal(position.quantity).mul(position.avgEntryPrice);
        exposure = exposure.plus(positionValue);
      }
    }
    
    return exposure;
  }

  private async updatePortfolioWithTrade(
    portfolio: VirtualPortfolio,
    trade: PaperTrade
  ): Promise<void> {
    const tradeValue = new Decimal(trade.volume).mul(trade.openPrice);
    const totalCost = tradeValue.plus(trade.commission);

    if (trade.type === TradeType.BUY) {
      // Update balance and margin for buy order
      portfolio.currentBalance = new Decimal(portfolio.currentBalance).minus(totalCost).toNumber();
      portfolio.usedMargin = new Decimal(portfolio.usedMargin).plus(tradeValue).toNumber();
      portfolio.availableMargin = new Decimal(portfolio.availableMargin).minus(totalCost).toNumber();

      // Add or update position
      const existingPosition = portfolio.positions.find(p => p.instrument === trade.symbol);
      if (existingPosition) {
        // Update existing position
        const totalQuantity = new Decimal(existingPosition.quantity).plus(trade.volume);
        const totalValue = new Decimal(existingPosition.quantity).mul(existingPosition.avgEntryPrice)
          .plus(tradeValue);
        existingPosition.avgEntryPrice = totalValue.div(totalQuantity).toNumber();
        existingPosition.quantity = totalQuantity.toNumber();
        existingPosition.marginRequired = new Decimal(existingPosition.marginRequired).plus(tradeValue).toNumber();
      } else {
        // Create new position
        const newPosition: VirtualPosition = {
          instrument: trade.symbol,
          quantity: trade.volume,
          avgEntryPrice: trade.openPrice,
          currentPrice: trade.openPrice,
          unrealizedPnl: 0,
          marginRequired: tradeValue.toNumber()
        };
        portfolio.positions.push(newPosition);
      }
    } else {
      // Handle sell orders (short selling or closing positions)
      portfolio.currentBalance = new Decimal(portfolio.currentBalance).plus(tradeValue).minus(trade.commission).toNumber();
      
      // Update position or create short position
      const existingPosition = portfolio.positions.find(p => p.instrument === trade.symbol);
      if (existingPosition && existingPosition.quantity >= trade.volume) {
        // Closing existing long position
        const closingValue = new Decimal(trade.volume).mul(existingPosition.avgEntryPrice);
        const pnl = tradeValue.minus(closingValue);
        portfolio.realizedPnl = new Decimal(portfolio.realizedPnl).plus(pnl).toNumber();
        
        existingPosition.quantity -= trade.volume;
        existingPosition.marginRequired = new Decimal(existingPosition.marginRequired).minus(closingValue).toNumber();
        portfolio.usedMargin = new Decimal(portfolio.usedMargin).minus(closingValue).toNumber();
        
        if (existingPosition.quantity === 0) {
          portfolio.positions = portfolio.positions.filter(p => p !== existingPosition);
        }
      }
    }

    portfolio.totalPnl = new Decimal(portfolio.realizedPnl).plus(portfolio.unrealizedPnl).toNumber();
    portfolio.updatedAt = new Date();
  }

  private calculateDailyPnL(portfolio: VirtualPortfolio, dayStart: Date): number {
    // Simplified daily P&L calculation
    // In reality, this would track intraday changes
    const todayPnl = portfolio.dailyPnl.length > 0 
      ? portfolio.dailyPnl[portfolio.dailyPnl.length - 1]
      : 0;
    return todayPnl;
  }

  private calculatePeakValue(portfolio: VirtualPortfolio): Decimal {
    // Calculate historical peak portfolio value
    // Simplified - would use historical snapshots in reality
    const currentValue = new Decimal(portfolio.currentBalance).plus(portfolio.unrealizedPnl);
    const initialValue = new Decimal(portfolio.initialBalance);
    
    return Decimal.max(currentValue, initialValue);
  }

  private async calculatePortfolioVaR(portfolio: VirtualPortfolio): Promise<number> {
    // Simplified 1-day 95% Value at Risk calculation
    // In reality, this would use historical price data and correlation matrices
    let totalVar = new Decimal(0);
    
    for (const position of portfolio.positions) {
      const positionValue = new Decimal(position.quantity).mul(position.currentPrice);
      const instrumentVar = positionValue.mul(0.02); // Assume 2% daily volatility
      totalVar = totalVar.plus(instrumentVar.pow(2));
    }
    
    return totalVar.sqrt().mul(1.65).toNumber(); // 95% confidence level
  }

  private generateId(): string {
    return Math.random().toString(36).substr(2, 9);
  }
}

/**
 * Market data price service interface
 */
interface MarketDataPriceService {
  getCurrentPrice(instrument: string): Promise<{
    price: number;
    bid: number;
    ask: number;
    timestamp: Date;
  }>;
}