import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { ComprehensiveBacktestService, BacktestRequest } from '../services/backtesting/ComprehensiveBacktestService';
import { BacktestAnalyticsService } from '../services/backtesting/BacktestAnalyticsService';
import { BacktestResultService, ExportFormat, ExportRequest } from '../services/backtesting/BacktestResultService';

/**
 * Validation schemas for API requests
 */
const BacktestRequestSchema = z.object({
  strategyId: z.string().min(1, 'Strategy ID is required'),
  instruments: z.array(z.string()).min(1, 'At least one instrument is required'),
  timeframes: z.array(z.enum(['M1', 'M5', 'M15', 'H1', 'H4', 'D1'])).min(1, 'At least one timeframe is required'),
  startDate: z.string().transform((str) => new Date(str)),
  endDate: z.string().transform((str) => new Date(str)),
  initialCapital: z.number().min(100, 'Initial capital must be at least $100'),
  
  // Optional configuration
  executionConfig: z.object({
    baseSpread: z.number().optional(),
    volatilitySpreadMultiplier: z.number().optional(),
    baseSlippage: z.number().optional(),
    volumeSlippageMultiplier: z.number().optional(),
    volatilitySlippageMultiplier: z.number().optional(),
    marketImpactFactor: z.number().optional(),
    liquidityAdjustment: z.boolean().optional(),
    commissionPerLot: z.number().optional(),
    minimumCommission: z.number().optional(),
    maxExecutionDelay: z.number().optional(),
    executionFailureRate: z.number().optional(),
  }).optional(),
  
  includeRegimeAnalysis: z.boolean().optional(),
  includeDrawdownAnalysis: z.boolean().optional(),
  maxConcurrentPositions: z.number().min(1).max(20).optional(),
  includeMLInsights: z.boolean().optional(),
  generateCoachingAdvice: z.boolean().optional(),
});

const ExportRequestSchema = z.object({
  backtestId: z.string().min(1, 'Backtest ID is required'),
  format: z.enum(['json', 'csv', 'excel', 'pdf']),
  includeSections: z.object({
    summary: z.boolean().optional(),
    trades: z.boolean().optional(),
    analytics: z.boolean().optional(),
    charts: z.boolean().optional(),
  }).optional(),
});

const SearchParamsSchema = z.object({
  strategyId: z.string().optional(),
  dateFrom: z.string().transform((str) => str ? new Date(str) : undefined).optional(),
  dateTo: z.string().transform((str) => str ? new Date(str) : undefined).optional(),
  minReturn: z.string().transform((str) => str ? parseFloat(str) : undefined).pipe(z.number()).optional(),
  maxReturn: z.string().transform((str) => str ? parseFloat(str) : undefined).pipe(z.number()).optional(),
  minSharpeRatio: z.string().transform((str) => str ? parseFloat(str) : undefined).pipe(z.number()).optional(),
  instruments: z.array(z.string()).optional(),
  sortBy: z.enum(['createdAt', 'totalReturn', 'sharpeRatio', 'maxDrawdown']).optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
  limit: z.string().transform((str) => str ? parseInt(str, 10) : undefined).pipe(z.number().min(1).max(100)).optional(),
  offset: z.string().transform((str) => str ? parseInt(str, 10) : undefined).pipe(z.number().min(0)).optional(),
});

/**
 * Controller for backtest API endpoints
 */
export class BacktestController {
  private readonly prisma: PrismaClient;
  private readonly backtestService: ComprehensiveBacktestService;
  private readonly analyticsService: BacktestAnalyticsService;
  private readonly resultService: BacktestResultService;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.backtestService = new ComprehensiveBacktestService(prisma);
    this.analyticsService = new BacktestAnalyticsService();
    this.resultService = new BacktestResultService(prisma);
  }

  /**
   * POST /api/backtest
   * Run a comprehensive backtest
   */
  async runBacktest(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      // Validate request body
      const validationResult = BacktestRequestSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({ 
          error: 'Validation failed', 
          details: validationResult.error.issues 
        });
        return;
      }

      const backtestRequest: BacktestRequest = {
        ...validationResult.data,
        userId,
      };

      // Validate date range
      if (backtestRequest.endDate <= backtestRequest.startDate) {
        res.status(400).json({ error: 'End date must be after start date' });
        return;
      }

      // Check for reasonable date range (not more than 10 years)
      const maxDateRange = 10 * 365 * 24 * 60 * 60 * 1000; // 10 years in milliseconds
      if (backtestRequest.endDate.getTime() - backtestRequest.startDate.getTime() > maxDateRange) {
        res.status(400).json({ error: 'Date range cannot exceed 10 years' });
        return;
      }

      // Run the backtest
      const result = await this.backtestService.runBacktest(backtestRequest);

      // Store the result
      await this.resultService.storeResult(result);

      // Return result summary (not full result to reduce response size)
      const response = {
        backtestId: result.id,
        summary: {
          totalReturn: result.overallMetrics.totalReturn,
          totalReturnPercentage: result.overallMetrics.totalReturnPercentage,
          sharpeRatio: result.overallMetrics.sharpeRatio,
          maxDrawdown: result.overallMetrics.maxDrawdownPercentage,
          winRate: result.overallMetrics.winRate,
          totalTrades: result.overallMetrics.totalTrades,
          backtestDuration: result.backtestDuration,
        },
        timeframes: result.timeframeAnalysis.map(tf => ({
          timeframe: tf.timeframe,
          return: tf.metrics.totalReturnPercentage,
          sharpeRatio: tf.metrics.sharpeRatio,
          trades: tf.trades.length,
        })),
        regimeAnalysis: result.regimeAnalysis?.map(regime => ({
          regime: regime.regime,
          return: regime.metrics.totalReturnPercentage,
          duration: regime.durationPercentage,
        })),
        mlInsights: result.mlInsights,
        coachingAdvice: result.coachingAdvice,
      };

      res.status(201).json({
        success: true,
        data: response,
        message: 'Backtest completed successfully',
      });

    } catch (error) {
      console.error('Backtest error:', error);
      res.status(500).json({
        success: false,
        error: 'Backtesting failed',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * GET /api/backtest/results/:id
   * Get detailed backtest results
   */
  async getBacktestResult(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const { id: backtestId } = req.params;
      if (!backtestId) {
        res.status(400).json({ error: 'Backtest ID is required' });
        return;
      }

      const result = await this.resultService.getResult(backtestId, userId);
      if (!result) {
        res.status(404).json({ error: 'Backtest result not found' });
        return;
      }

      // Generate analytics if requested
      const includeAnalytics = req.query.includeAnalytics === 'true';
      let analytics;
      
      if (includeAnalytics) {
        analytics = {
          performanceAttribution: this.analyticsService.analyzePerformanceAttribution(result),
          riskAnalysis: this.analyticsService.analyzeRisk(result),
          tradeDistribution: this.analyticsService.analyzeTradeDistribution(result),
          visualization: this.analyticsService.generateVisualizationData(result),
        };

        // Store analytics for future use
        await this.resultService.storeAnalytics(
          backtestId,
          userId,
          analytics.performanceAttribution,
          analytics.riskAnalysis,
          analytics.tradeDistribution
        );
      }

      res.json({
        success: true,
        data: {
          ...result,
          analytics,
        },
      });

    } catch (error) {
      console.error('Get backtest result error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve backtest result',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * GET /api/backtest/history
   * Search backtest history
   */
  async getBacktestHistory(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const validationResult = SearchParamsSchema.safeParse(req.query);
      if (!validationResult.success) {
        res.status(400).json({ 
          error: 'Invalid search parameters', 
          details: validationResult.error.issues 
        });
        return;
      }

      const searchParams = {
        ...validationResult.data,
        userId,
      };

      const results = await this.resultService.searchResults(searchParams);

      res.json({
        success: true,
        data: results.results,
        pagination: {
          totalCount: results.totalCount,
          limit: searchParams.limit || 50,
          offset: searchParams.offset || 0,
          hasMore: (searchParams.offset || 0) + (searchParams.limit || 50) < results.totalCount,
        },
      });

    } catch (error) {
      console.error('Get backtest history error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve backtest history',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * POST /api/backtest/export
   * Export backtest results
   */
  async exportBacktestResult(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const validationResult = ExportRequestSchema.safeParse(req.body);
      if (!validationResult.success) {
        res.status(400).json({ 
          error: 'Invalid export request', 
          details: validationResult.error.issues 
        });
        return;
      }

      const exportRequest: ExportRequest = {
        ...validationResult.data,
        userId,
        format: validationResult.data.format as ExportFormat,
      };

      const exportResult = await this.resultService.exportResult(exportRequest);

      // Set appropriate headers for file download
      res.setHeader('Content-Type', exportResult.contentType);
      res.setHeader('Content-Disposition', `attachment; filename="${exportResult.filename}"`);
      res.setHeader('Content-Length', exportResult.size.toString());

      res.send(exportResult.data);

    } catch (error) {
      console.error('Export backtest error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to export backtest result',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * DELETE /api/backtest/results/:id
   * Delete backtest result
   */
  async deleteBacktestResult(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const { id: backtestId } = req.params;
      if (!backtestId) {
        res.status(400).json({ error: 'Backtest ID is required' });
        return;
      }

      const deleted = await this.resultService.deleteResult(backtestId, userId);
      if (!deleted) {
        res.status(404).json({ error: 'Backtest result not found' });
        return;
      }

      res.json({
        success: true,
        message: 'Backtest result deleted successfully',
      });

    } catch (error) {
      console.error('Delete backtest error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to delete backtest result',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * GET /api/backtest/statistics
   * Get user's backtest statistics
   */
  async getBacktestStatistics(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const statistics = await this.resultService.getStatistics(userId);

      res.json({
        success: true,
        data: statistics,
      });

    } catch (error) {
      console.error('Get statistics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to retrieve statistics',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  /**
   * POST /api/backtest/compare
   * Compare multiple backtest results
   */
  async compareBacktests(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({ error: 'Authentication required' });
        return;
      }

      const { backtestIds } = req.body;
      if (!Array.isArray(backtestIds) || backtestIds.length < 2) {
        res.status(400).json({ error: 'At least 2 backtest IDs are required for comparison' });
        return;
      }

      if (backtestIds.length > 10) {
        res.status(400).json({ error: 'Cannot compare more than 10 backtests at once' });
        return;
      }

      // Fetch all backtest results
      const results = await Promise.all(
        backtestIds.map((id: string) => this.resultService.getResult(id, userId))
      );

      // Filter out null results
      const validResults = results.filter(result => result !== null);

      if (validResults.length < 2) {
        res.status(404).json({ error: 'Not enough valid backtest results found for comparison' });
        return;
      }

      // Generate comparison data
      const comparison = {
        summary: validResults.map(result => ({
          backtestId: result.id,
          strategyId: result.strategyId,
          totalReturn: result.overallMetrics.totalReturnPercentage,
          sharpeRatio: result.overallMetrics.sharpeRatio,
          maxDrawdown: result.overallMetrics.maxDrawdownPercentage,
          winRate: result.overallMetrics.winRate,
          totalTrades: result.overallMetrics.totalTrades,
        })),
        
        bestPerforming: {
          byReturn: validResults.reduce((best, current) => 
            current.overallMetrics.totalReturnPercentage > best.overallMetrics.totalReturnPercentage ? current : best
          ).id,
          
          bySharpeRatio: validResults.reduce((best, current) => 
            current.overallMetrics.sharpeRatio > best.overallMetrics.sharpeRatio ? current : best
          ).id,
          
          byDrawdown: validResults.reduce((best, current) => 
            current.overallMetrics.maxDrawdownPercentage < best.overallMetrics.maxDrawdownPercentage ? current : best
          ).id,
        },
        
        averages: {
          totalReturn: validResults.reduce((sum, result) => sum + result.overallMetrics.totalReturnPercentage, 0) / validResults.length,
          sharpeRatio: validResults.reduce((sum, result) => sum + result.overallMetrics.sharpeRatio, 0) / validResults.length,
          maxDrawdown: validResults.reduce((sum, result) => sum + result.overallMetrics.maxDrawdownPercentage, 0) / validResults.length,
          winRate: validResults.reduce((sum, result) => sum + result.overallMetrics.winRate, 0) / validResults.length,
        },
      };

      res.json({
        success: true,
        data: comparison,
      });

    } catch (error) {
      console.error('Compare backtests error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to compare backtests',
        message: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }
}

/**
 * Create and configure backtest routes
 */
export function createBacktestRoutes(prisma: PrismaClient) {
  const controller = new BacktestController(prisma);

  return {
    runBacktest: controller.runBacktest.bind(controller),
    getBacktestResult: controller.getBacktestResult.bind(controller),
    getBacktestHistory: controller.getBacktestHistory.bind(controller),
    exportBacktestResult: controller.exportBacktestResult.bind(controller),
    deleteBacktestResult: controller.deleteBacktestResult.bind(controller),
    getBacktestStatistics: controller.getBacktestStatistics.bind(controller),
    compareBacktests: controller.compareBacktests.bind(controller),
  };
}