import { Request, Response, NextFunction } from 'express';
import { supabase } from '@golddaddy/config/src/database';
import { User } from '@supabase/supabase-js';

// Extended Request interface to include user
export interface AuthenticatedRequest extends Request {
  user?: User;
  userId?: string;
}

// JWT token validation middleware
export const authenticateToken = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).json({
        error: {
          code: 'AUTHENTICATION_REQUIRED',
          message: 'Access token is required',
          timestamp: new Date().toISOString(),
          requestId: generateRequestId(),
        },
      });
      return;
    }

    // Verify token with Supabase
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      res.status(401).json({
        error: {
          code: 'INVALID_TOKEN',
          message: 'Invalid or expired access token',
          timestamp: new Date().toISOString(),
          requestId: generateRequestId(),
        },
      });
      return;
    }

    // Attach user to request
    req.user = user;
    req.userId = user.id;

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      error: {
        code: 'AUTHENTICATION_ERROR',
        message: 'Internal authentication error',
        timestamp: new Date().toISOString(),
        requestId: generateRequestId(),
      },
    });
  }
};

// Helper function to generate request ID
const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// User registration utilities
export const registerUser = async (email: string, password: string, profile: {
  displayName: string;
  riskTolerance: string;
  experienceLevel: string;
  coachingStyle: string;
  tradingCapital?: number;
}) => {
  try {
    // Sign up with Supabase Auth
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          display_name: profile.displayName,
          risk_tolerance: profile.riskTolerance,
          experience_level: profile.experienceLevel,
          coaching_style: profile.coachingStyle,
          trading_capital: profile.tradingCapital,
        },
      },
    });

    if (error) {
      throw new Error(`Registration failed: ${error.message}`);
    }

    return {
      user: data.user,
      session: data.session,
    };
  } catch (error) {
    console.error('User registration error:', error);
    throw error;
  }
};

// User sign in utilities
export const signInUser = async (email: string, password: string) => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      throw new Error(`Sign in failed: ${error.message}`);
    }

    return {
      user: data.user,
      session: data.session,
    };
  } catch (error) {
    console.error('User sign in error:', error);
    throw error;
  }
};

// Refresh token utilities
export const refreshUserToken = async (refreshToken: string) => {
  try {
    const { data, error } = await supabase.auth.refreshSession({
      refresh_token: refreshToken,
    });

    if (error) {
      throw new Error(`Token refresh failed: ${error.message}`);
    }

    return {
      user: data.user,
      session: data.session,
    };
  } catch (error) {
    console.error('Token refresh error:', error);
    throw error;
  }
};

// Sign out utilities
export const signOutUser = async (token: string) => {
  try {
    // Set the session before signing out
    await supabase.auth.setSession({
      access_token: token,
      refresh_token: '', // Will be provided by client
    });

    const { error } = await supabase.auth.signOut();

    if (error) {
      throw new Error(`Sign out failed: ${error.message}`);
    }

    return { success: true };
  } catch (error) {
    console.error('User sign out error:', error);
    throw error;
  }
};

// Role-based access control (future implementation)
export const requireRole = (_roles: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    // For now, all authenticated users have access
    // In the future, implement role checking
    next();
  };
};

// Rate limiting helper (basic implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export const rateLimit = (windowMs: number, maxRequests: number) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    const identifier = req.userId || req.ip;
    const now = Date.now();
    
    const userLimit = rateLimitMap.get(identifier);
    
    if (!userLimit || now > userLimit.resetTime) {
      rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
      next();
      return;
    }
    
    if (userLimit.count >= maxRequests) {
      res.status(429).json({
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests, please try again later',
          timestamp: new Date().toISOString(),
          requestId: generateRequestId(),
        },
      });
      return;
    }
    
    userLimit.count++;
    next();
  };
};

// Input validation helpers
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < 8) {
    errors.push('Password must be at least 8 characters long');
  }
  
  if (!/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
};