# Test Environment Configuration
NODE_ENV=test

# Test Database - In-memory SQLite for testing
DATABASE_URL="file:./dev.db"
DIRECT_URL="file:./dev.db"

# JWT
JWT_SECRET=test-secret-key
JWT_EXPIRES_IN=1h
REFRESH_TOKEN_EXPIRES_IN=30d

# API
API_URL=http://localhost:3001
WS_URL=ws://localhost:3001

# Features
ENABLE_PAPER_TRADING=true
ENABLE_LIVE_TRADING=false
ENABLE_ANALYTICS=true
ENABLE_NOTIFICATIONS=true

# Logging
LOG_LEVEL=error

# WebSocket
WEBSOCKET_PORT=8080

# Monitoring
ALERT_RETENTION_DAYS=30
HEALTH_CHECK_ALERT_THRESHOLD=3
LATENCY_ALERT_THRESHOLD=5000
ERROR_RATE_ALERT_THRESHOLD=0.1