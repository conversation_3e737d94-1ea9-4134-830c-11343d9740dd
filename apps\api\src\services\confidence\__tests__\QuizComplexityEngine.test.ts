import { describe, it, expect } from 'vitest';
import { QuizComplexityEngine } from '../QuizComplexityEngine.js';
import {
  ConfidenceStage,
  QuizDifficulty,
  QuizCategory,
  QuizAttemptStatus,
  type QuizAttempt,
  type ConfidenceAssessment
} from '@golddaddy/types';

describe('QuizComplexityEngine', () => {
  describe('calculateComplexityRecommendation', () => {
    it('should recommend higher difficulty for high-performing users', () => {
      const engine = new QuizComplexityEngine();
      const highPerformanceProfile = {
        averageScore: 90,
        attemptCount: 3,
        consistencyRating: 0.9,
        timeEfficiency: 0.8,
        weakCategories: [],
        strongCategories: [QuizCategory.TRADING_FUNDAMENTALS],
        improvementTrend: 0.3
      };

      const mockAssessment: ConfidenceAssessment = {
        id: 'assessment1',
        userId: 'user1',
        currentStage: ConfidenceStage.STRATEGY_LEARNING,
        overallConfidenceScore: 85,
        assessmentScores: {
          knowledgeQuiz: { score: 90, completedAt: new Date(), attempts: 3, weakAreas: [] },
          behavioralAssessment: { riskTolerance: 80, decisionConsistency: 85, emotionalStability: 75, lastAssessed: new Date() },
          performanceEvaluation: { paperTradingWinRate: 0.7, riskManagementScore: 80, strategyAdherence: 85, consistencyRating: 80 }
        },
        progressHistory: [],
        graduationCriteria: { nextStage: 'backtesting_review', requirements: { minimumConfidenceScore: 80, requiredAssessments: [], minimumTimeInStage: 14 } },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const recommendation = engine.calculateComplexityRecommendation(
        ConfidenceStage.STRATEGY_LEARNING,
        highPerformanceProfile,
        mockAssessment
      );

      expect(recommendation.difficulty).toBe(QuizDifficulty.INTERMEDIATE);
      expect(recommendation.questionCount).toBeGreaterThanOrEqual(20); // Should be increased
      expect(recommendation.reasoning).toContain('strong performance');
    });

    it('should recommend easier difficulty for struggling users', () => {
      const engine = new QuizComplexityEngine();
      const lowPerformanceProfile = {
        averageScore: 55,
        attemptCount: 4,
        consistencyRating: 0.3,
        timeEfficiency: 0.4,
        weakCategories: [QuizCategory.RISK_MANAGEMENT, QuizCategory.PSYCHOLOGY_DISCIPLINE],
        strongCategories: [],
        improvementTrend: -0.2
      };

      const mockAssessment: ConfidenceAssessment = {
        id: 'assessment1',
        userId: 'user1',
        currentStage: ConfidenceStage.PAPER_TRADING,
        overallConfidenceScore: 55,
        assessmentScores: {
          knowledgeQuiz: { score: 55, completedAt: new Date(), attempts: 4, weakAreas: ['risk_management'] },
          behavioralAssessment: { riskTolerance: 60, decisionConsistency: 50, emotionalStability: 45, lastAssessed: new Date() },
          performanceEvaluation: { paperTradingWinRate: 0.4, riskManagementScore: 50, strategyAdherence: 60, consistencyRating: 40 }
        },
        progressHistory: [],
        graduationCriteria: { nextStage: 'live_ready', requirements: { minimumConfidenceScore: 90, requiredAssessments: [], minimumTimeInStage: 30 } },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const recommendation = engine.calculateComplexityRecommendation(
        ConfidenceStage.PAPER_TRADING,
        lowPerformanceProfile,
        mockAssessment
      );

      expect(recommendation.difficulty).toBe(QuizDifficulty.BEGINNER);
      expect(recommendation.questionCount).toBeLessThanOrEqual(30); // Should be reduced or same
      expect(recommendation.focusCategories).toContain(QuizCategory.RISK_MANAGEMENT);
      expect(recommendation.timeLimit).toBeGreaterThanOrEqual(1800); // More time given
    });

    it('should focus on weak categories', () => {
      const engine = new QuizComplexityEngine();
      const profileWithWeaknesses = {
        averageScore: 75,
        attemptCount: 2,
        consistencyRating: 0.7,
        timeEfficiency: 0.7,
        weakCategories: [QuizCategory.PSYCHOLOGY_DISCIPLINE, QuizCategory.SAFETY_PROCEDURES],
        strongCategories: [QuizCategory.TRADING_FUNDAMENTALS],
        improvementTrend: 0.1
      };

      const mockAssessment: ConfidenceAssessment = {
        id: 'assessment1',
        userId: 'user1',
        currentStage: ConfidenceStage.BACKTESTING_REVIEW,
        overallConfidenceScore: 75,
        assessmentScores: {
          knowledgeQuiz: { score: 75, completedAt: new Date(), attempts: 2, weakAreas: ['psychology_discipline'] },
          behavioralAssessment: { riskTolerance: 70, decisionConsistency: 75, emotionalStability: 60, lastAssessed: new Date() },
          performanceEvaluation: { paperTradingWinRate: 0.6, riskManagementScore: 75, strategyAdherence: 80, consistencyRating: 70 }
        },
        progressHistory: [],
        graduationCriteria: { nextStage: 'paper_trading', requirements: { minimumConfidenceScore: 85, requiredAssessments: [], minimumTimeInStage: 21 } },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const recommendation = engine.calculateComplexityRecommendation(
        ConfidenceStage.BACKTESTING_REVIEW,
        profileWithWeaknesses,
        mockAssessment
      );

      expect(recommendation.focusCategories).toContain(QuizCategory.PSYCHOLOGY_DISCIPLINE);
      expect(recommendation.reasoning).toContain('psychology discipline');
    });
  });

  describe('createUserPerformanceProfile', () => {
    it('should calculate accurate performance metrics from attempts', () => {
      const attempts: QuizAttempt[] = [
        {
          id: 'attempt1',
          userId: 'user1',
          sessionId: 'session1',
          attemptNumber: 1,
          score: 70,
          totalTimeSpent: 1800,
          questionsAnswered: 15,
          correctAnswers: 10,
          weakAreas: ['risk_management'],
          strongAreas: ['trading_fundamentals'],
          confidenceScore: 70,
          feedback: { overall: '', strengths: [], weaknesses: [], recommendations: [], nextSteps: [] },
          status: QuizAttemptStatus.COMPLETED,
          createdAt: new Date()
        },
        {
          id: 'attempt2',
          userId: 'user1',
          sessionId: 'session2',
          attemptNumber: 2,
          score: 80,
          totalTimeSpent: 1600,
          questionsAnswered: 15,
          correctAnswers: 12,
          weakAreas: ['risk_management'],
          strongAreas: ['trading_fundamentals'],
          confidenceScore: 78,
          feedback: { overall: '', strengths: [], weaknesses: [], recommendations: [], nextSteps: [] },
          status: QuizAttemptStatus.COMPLETED,
          createdAt: new Date()
        },
        {
          id: 'attempt3',
          userId: 'user1',
          sessionId: 'session3',
          attemptNumber: 3,
          score: 85,
          totalTimeSpent: 1500,
          questionsAnswered: 15,
          correctAnswers: 13,
          weakAreas: ['psychology_discipline'],
          strongAreas: ['trading_fundamentals', 'platform_features'],
          confidenceScore: 82,
          feedback: { overall: '', strengths: [], weaknesses: [], recommendations: [], nextSteps: [] },
          status: QuizAttemptStatus.COMPLETED,
          createdAt: new Date()
        }
      ];

      const engine = new QuizComplexityEngine();
      const profile = engine.createUserPerformanceProfile(attempts);

      expect(profile.averageScore).toBeCloseTo(78.33, 1);
      expect(profile.attemptCount).toBe(3);
      expect(profile.timeEfficiency).toBeGreaterThan(0.8); // Improving time efficiency
      expect(profile.weakCategories).toContain(QuizCategory.RISK_MANAGEMENT);
      expect(profile.strongCategories).toContain(QuizCategory.TRADING_FUNDAMENTALS);
      expect(profile.improvementTrend).toBeGreaterThan(0); // Positive improvement
    });

    it('should handle empty attempts gracefully', () => {
      const engine = new QuizComplexityEngine();
      const profile = engine.createUserPerformanceProfile([]);

      expect(profile.averageScore).toBe(0);
      expect(profile.attemptCount).toBe(0);
      expect(profile.consistencyRating).toBe(0);
      expect(profile.timeEfficiency).toBe(0.5);
      expect(profile.weakCategories).toHaveLength(0);
      expect(profile.strongCategories).toHaveLength(0);
      expect(profile.improvementTrend).toBe(0);
    });
  });

});