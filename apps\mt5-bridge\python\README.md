# MT5 Bridge Python Service

This service handles MetaTrader 5 integration for the GoldDaddy platform, providing real-time data streaming, historical data collection, and trade execution capabilities.

## Quick Start

### 1. Environment Setup

```bash
# Run the setup script
python setup.py

# Or manually:
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### 2. Configuration

```bash
# Copy environment template
cp .env.example .env

# Edit configuration (optional for demo mode)
# For demo mode, leave MT5_* settings empty
```

### 3. Test MT5 Connection

```bash
# Activate virtual environment
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Run connection test
python test_mt5_basic.py
```

### 4. Start the Service

```bash
# Development mode
python main.py

# Or with uvicorn directly
uvicorn main:app --host localhost --port 8001 --reload
```

## Configuration

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `MT5_LOGIN` | MT5 account login | - | No (demo mode) |
| `MT5_PASSWORD` | MT5 account password | - | No (demo mode) |
| `MT5_SERVER` | MT5 broker server | - | No (demo mode) |
| `MT5_PATH` | MT5 installation path | - | No |
| `MT5_DEMO_MODE` | Enable demo mode | true | No |
| `API_PORT` | API service port | 8001 | No |
| `WEBSOCKET_PORT` | WebSocket port | 8002 | No |
| `DATABASE_URL` | PostgreSQL connection | - | Yes |
| `REDIS_URL` | Redis connection | redis://localhost:6379 | No |

### Demo Mode

The service can run in demo mode without a real MT5 account connection:
- Set `MT5_DEMO_MODE=true`
- Leave MT5 credentials empty
- Uses simulated data for testing

## Features

### ✅ Implemented (Task 1)

- [x] Python virtual environment setup
- [x] MetaTrader5 library installation and configuration
- [x] Basic MT5 connection test script
- [x] Connection configuration management
- [x] Error handling for MT5 connection failures
- [x] Automated setup script
- [x] Docker containerization
- [x] Comprehensive connection testing

### 🔄 Coming Next

- [ ] Real-time price data streaming (Task 2)
- [ ] Historical data collection (Task 3)
- [ ] Paper trading execution (Task 4)
- [ ] Connection health monitoring (Task 5)
- [ ] Data quality validation (Task 6)
- [ ] Performance testing (Task 7)
- [ ] Integration documentation (Task 8)

## Testing

### Connection Test

```bash
# Basic connection test
python test_mt5_basic.py

# Expected output:
# ✅ Configuration validation passed
# ✅ MT5 connection successful
# ✅ Async connection management working
# 🎉 All MT5 connection tests passed!
```

### Unit Tests

```bash
# Run unit tests
pytest

# With coverage
pytest --cov=. --cov-report=html
```

## Architecture

```
python/
├── setup.py              # Environment setup script
├── config.py             # Configuration management
├── mt5_connection.py      # MT5 connection manager
├── test_mt5_basic.py      # Connection test script
├── requirements.txt       # Python dependencies
├── .env.example          # Environment template
├── Dockerfile            # Container configuration
└── README.md             # This file
```

## Error Handling

The service includes comprehensive error handling:

- **Connection Failures**: Automatic reconnection with exponential backoff
- **Invalid Credentials**: Clear error messages and demo mode fallback
- **Network Issues**: Retry logic with timeout management
- **MT5 API Errors**: Detailed error codes and descriptions
- **Configuration Errors**: Validation with helpful error messages

## Security

- Environment-based configuration
- No hardcoded credentials
- Secure connection parameter handling
- Input validation and sanitization
- Non-root Docker user

## Troubleshooting

### Common Issues

1. **MT5 Not Installed**
   ```
   Error: MT5 initialization failed: 5002 - Terminal not installed
   Solution: Install MetaTrader 5 or run in demo mode
   ```

2. **Connection Timeout**
   ```
   Error: Connection timeout
   Solution: Check network connectivity and MT5_TIMEOUT setting
   ```

3. **Invalid Credentials**
   ```
   Error: Authorization failed
   Solution: Verify MT5_LOGIN, MT5_PASSWORD, and MT5_SERVER
   ```

4. **Demo Mode Not Working**
   ```
   Solution: Ensure MT5_DEMO_MODE=true and MT5 credentials are empty
   ```

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
python test_mt5_basic.py
```

## Next Steps

1. Implement real-time price streaming (Task 2)
2. Add historical data collection (Task 3)
3. Build paper trading engine (Task 4)
4. Create health monitoring (Task 5)

For questions or issues, refer to the main project documentation.