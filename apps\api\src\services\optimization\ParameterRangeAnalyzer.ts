/**
 * Parameter Range Analyzer
 * 
 * @fileoverview Intelligent parameter range detection and validation
 * based on strategy type, market conditions, and historical analysis
 */

import type {
  ParameterDefinition,
  ParameterConstraint,
  StrategyType,
  MarketRegime,
  StrategyParameters
} from '@golddaddy/types/optimization';

// ===== Market Volatility Data =====

interface MarketVolatilityData {
  symbol: string;
  averageVolatility: number;
  highVolatilityThreshold: number;
  lowVolatilityThreshold: number;
  averageSpread: number;
  typicalRange: {
    daily: number;
    weekly: number;
    monthly: number;
  };
}

// ===== Parameter Range Templates =====

interface ParameterRangeTemplate {
  strategyType: StrategyType;
  marketRegime: MarketRegime;
  parameters: Record<string, ParameterConstraint>;
}

// ===== Parameter Range Analyzer Class =====

export class ParameterRangeAnalyzer {
  private volatilityData: Map<string, MarketVolatilityData> = new Map();
  private rangeTemplates: ParameterRangeTemplate[] = [];

  constructor() {
    this.initializeDefaultTemplates();
    this.initializeDefaultVolatilityData();
  }

  // ===== Main Analysis Methods =====

  /**
   * Analyze and generate parameter ranges for a strategy
   */
  public analyzeParameterRanges(
    strategyType: StrategyType,
    marketRegime: MarketRegime,
    instruments: string[],
    customConstraints?: Record<string, Partial<ParameterConstraint>>
  ): ParameterDefinition[] {
    
    // Get base template for strategy type and market regime
    const template = this.getParameterTemplate(strategyType, marketRegime);
    
    // Adjust ranges based on market volatility
    const adjustedRanges = this.adjustForMarketConditions(template, instruments);
    
    // Apply custom constraints if provided
    const finalRanges = this.applyCustomConstraints(adjustedRanges, customConstraints);
    
    // Validate parameter dependencies
    this.validateParameterDependencies(finalRanges);
    
    return this.convertToParameterDefinitions(finalRanges, strategyType);
  }

  /**
   * Validate parameter values against constraints
   */
  public validateParameters(
    parameters: StrategyParameters,
    definitions: ParameterDefinition[]
  ): {
    isValid: boolean;
    violations: string[];
    corrections: StrategyParameters;
  } {
    const violations: string[] = [];
    const corrections: StrategyParameters = { ...parameters };

    for (const definition of definitions) {
      const value = parameters[definition.name];
      const constraint = definition.constraint;

      if (typeof value === 'number') {
        // Check bounds
        if (value < constraint.min) {
          violations.push(`${definition.name}: ${value} is below minimum ${constraint.min}`);
          corrections[definition.name] = constraint.min;
        }
        
        if (value > constraint.max) {
          violations.push(`${definition.name}: ${value} is above maximum ${constraint.max}`);
          corrections[definition.name] = constraint.max;
        }

        // Check step constraints for discrete parameters
        if (definition.type === 'discrete' && constraint.step) {
          const stepViolation = (value - constraint.min) % constraint.step;
          if (Math.abs(stepViolation) > 0.001) {
            violations.push(`${definition.name}: ${value} does not align with step size ${constraint.step}`);
            const correctedValue = constraint.min + Math.round((value - constraint.min) / constraint.step) * constraint.step;
            corrections[definition.name] = Math.max(constraint.min, Math.min(constraint.max, correctedValue));
          }
        }
      }
    }

    // Check parameter dependencies
    const dependencyViolations = this.validateDependencies(parameters, definitions);
    violations.push(...dependencyViolations);

    return {
      isValid: violations.length === 0,
      violations,
      corrections
    };
  }

  /**
   * Get optimal parameter ranges based on historical analysis
   */
  public getOptimalRanges(
    strategyType: StrategyType,
    instruments: string[],
    historicalData?: {
      successfulParameters: StrategyParameters[];
      performanceMetrics: number[];
    }
  ): Record<string, { optimal: number; range: [number, number] }> {
    const optimal: Record<string, { optimal: number; range: [number, number] }> = {};

    if (historicalData && historicalData.successfulParameters.length > 0) {
      // Use historical data to find optimal ranges
      const parameterStats = this.analyzeHistoricalParameters(historicalData);
      
      for (const [paramName, stats] of Object.entries(parameterStats)) {
        optimal[paramName] = {
          optimal: stats.weightedMean,
          range: [stats.min, stats.max]
        };
      }
    } else {
      // Use default ranges based on strategy type
      const template = this.getParameterTemplate(strategyType, 'any');
      
      for (const [paramName, constraint] of Object.entries(template)) {
        const range = constraint.max - constraint.min;
        optimal[paramName] = {
          optimal: constraint.defaultValue,
          range: [constraint.min + range * 0.2, constraint.max - range * 0.2]
        };
      }
    }

    return optimal;
  }

  // ===== Template Management =====

  /**
   * Get parameter template for strategy type and market regime
   */
  private getParameterTemplate(strategyType: StrategyType, marketRegime: MarketRegime): Record<string, ParameterConstraint> {
    // Find exact match first
    let template = this.rangeTemplates.find(t => 
      t.strategyType === strategyType && t.marketRegime === marketRegime
    );

    // Fallback to strategy type with 'any' market regime
    if (!template) {
      template = this.rangeTemplates.find(t => 
        t.strategyType === strategyType && t.marketRegime === 'any'
      );
    }

    // Ultimate fallback to momentum strategy
    if (!template) {
      template = this.rangeTemplates.find(t => 
        t.strategyType === 'momentum' && t.marketRegime === 'any'
      );
    }

    return template ? { ...template.parameters } : this.getDefaultParameters();
  }

  /**
   * Adjust parameter ranges based on market volatility
   */
  private adjustForMarketConditions(
    baseRanges: Record<string, ParameterConstraint>,
    instruments: string[]
  ): Record<string, ParameterConstraint> {
    const adjusted = { ...baseRanges };
    
    // Calculate average volatility across instruments
    const avgVolatility = this.calculateAverageVolatility(instruments);
    
    // Volatility multiplier (1.0 = normal, >1.0 = high volatility, <1.0 = low volatility)
    const volatilityMultiplier = Math.max(0.5, Math.min(2.0, avgVolatility));

    // Adjust risk-related parameters based on volatility
    if (adjusted.stopLoss) {
      adjusted.stopLoss = {
        ...adjusted.stopLoss,
        min: adjusted.stopLoss.min * volatilityMultiplier,
        max: adjusted.stopLoss.max * volatilityMultiplier,
        defaultValue: adjusted.stopLoss.defaultValue * volatilityMultiplier
      };
    }

    if (adjusted.takeProfit) {
      adjusted.takeProfit = {
        ...adjusted.takeProfit,
        min: adjusted.takeProfit.min * volatilityMultiplier,
        max: adjusted.takeProfit.max * volatilityMultiplier,
        defaultValue: adjusted.takeProfit.defaultValue * volatilityMultiplier
      };
    }

    // Adjust indicator periods inversely to volatility (higher volatility = shorter periods)
    const periodMultiplier = 1 / Math.sqrt(volatilityMultiplier);
    
    for (const paramName of Object.keys(adjusted)) {
      if (paramName.includes('period') || paramName.includes('length')) {
        const param = adjusted[paramName];
        adjusted[paramName] = {
          ...param,
          min: Math.max(1, Math.round(param.min * periodMultiplier)),
          max: Math.round(param.max * periodMultiplier),
          defaultValue: Math.round(param.defaultValue * periodMultiplier)
        };
      }
    }

    return adjusted;
  }

  /**
   * Apply custom parameter constraints
   */
  private applyCustomConstraints(
    baseRanges: Record<string, ParameterConstraint>,
    customConstraints?: Record<string, Partial<ParameterConstraint>>
  ): Record<string, ParameterConstraint> {
    if (!customConstraints) return baseRanges;

    const result = { ...baseRanges };

    for (const [paramName, customConstraint] of Object.entries(customConstraints)) {
      if (result[paramName]) {
        result[paramName] = {
          ...result[paramName],
          ...customConstraint
        };

        // Ensure min <= default <= max
        const constraint = result[paramName];
        constraint.defaultValue = Math.max(constraint.min, Math.min(constraint.max, constraint.defaultValue));
      }
    }

    return result;
  }

  /**
   * Convert constraints to parameter definitions
   */
  private convertToParameterDefinitions(
    constraints: Record<string, ParameterConstraint>,
    strategyType: StrategyType
  ): ParameterDefinition[] {
    const definitions: ParameterDefinition[] = [];

    for (const [paramName, constraint] of Object.entries(constraints)) {
      const definition: ParameterDefinition = {
        name: paramName,
        type: this.getParameterType(paramName),
        constraint,
        description: this.getParameterDescription(paramName, strategyType),
        category: this.getParameterCategory(paramName),
        dependsOn: this.getParameterDependencies(paramName)
      };

      definitions.push(definition);
    }

    return definitions;
  }

  // ===== Parameter Analysis =====

  /**
   * Analyze historical parameters for optimal ranges
   */
  private analyzeHistoricalParameters(
    historicalData: {
      successfulParameters: StrategyParameters[];
      performanceMetrics: number[];
    }
  ): Record<string, {
    min: number;
    max: number;
    mean: number;
    weightedMean: number;
    stdDev: number;
  }> {
    const results: Record<string, {
      min: number;
      max: number;
      mean: number;
      weightedMean: number;
      stdDev: number;
    }> = {};

    // Get all parameter names
    const paramNames = new Set<string>();
    historicalData.successfulParameters.forEach(params => {
      Object.keys(params).forEach(name => paramNames.add(name));
    });

    // Analyze each parameter
    for (const paramName of paramNames) {
      const values: number[] = [];
      const weights: number[] = [];

      historicalData.successfulParameters.forEach((params, index) => {
        const value = params[paramName];
        if (typeof value === 'number') {
          values.push(value);
          weights.push(historicalData.performanceMetrics[index] || 1);
        }
      });

      if (values.length > 0) {
        const min = Math.min(...values);
        const max = Math.max(...values);
        const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
        
        // Calculate weighted mean (higher performing parameters get more weight)
        const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
        const weightedMean = values.reduce((sum, val, index) => 
          sum + (val * weights[index]), 0
        ) / totalWeight;

        // Calculate standard deviation
        const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
        const stdDev = Math.sqrt(variance);

        results[paramName] = {
          min,
          max,
          mean,
          weightedMean,
          stdDev
        };
      }
    }

    return results;
  }

  /**
   * Calculate average volatility across instruments
   */
  private calculateAverageVolatility(instruments: string[]): number {
    if (instruments.length === 0) return 1.0;

    let totalVolatility = 0;
    let count = 0;

    for (const instrument of instruments) {
      const volatilityData = this.volatilityData.get(instrument);
      if (volatilityData) {
        totalVolatility += volatilityData.averageVolatility;
        count++;
      }
    }

    if (count === 0) return 1.0;

    const avgVolatility = totalVolatility / count;
    
    // Normalize volatility (1.0 = baseline volatility)
    const baselineVolatility = 0.01; // 1% daily volatility as baseline
    return avgVolatility / baselineVolatility;
  }

  // ===== Parameter Validation =====

  /**
   * Validate parameter dependencies
   */
  private validateParameterDependencies(ranges: Record<string, ParameterConstraint>): void {
    // Stop loss should be less than take profit
    if (ranges.stopLoss && ranges.takeProfit) {
      if (ranges.stopLoss.min >= ranges.takeProfit.max) {
        // Adjust ranges to maintain valid relationship
        ranges.stopLoss.max = Math.min(ranges.stopLoss.max, ranges.takeProfit.min * 0.8);
        ranges.takeProfit.min = Math.max(ranges.takeProfit.min, ranges.stopLoss.max * 1.2);
      }
    }

    // Fast MA should be less than slow MA
    if (ranges.fastMAPeriod && ranges.slowMAPeriod) {
      if (ranges.fastMAPeriod.max >= ranges.slowMAPeriod.min) {
        ranges.fastMAPeriod.max = Math.min(ranges.fastMAPeriod.max, ranges.slowMAPeriod.min - 1);
        ranges.slowMAPeriod.min = Math.max(ranges.slowMAPeriod.min, ranges.fastMAPeriod.max + 1);
      }
    }
  }

  /**
   * Validate parameter value dependencies
   */
  private validateDependencies(
    parameters: StrategyParameters,
    definitions: ParameterDefinition[]
  ): string[] {
    const violations: string[] = [];

    // Check stop loss vs take profit
    const stopLoss = parameters.stopLoss as number;
    const takeProfit = parameters.takeProfit as number;
    if (stopLoss && takeProfit && stopLoss >= takeProfit) {
      violations.push('Stop loss must be less than take profit');
    }

    // Check fast vs slow MA periods
    const fastMA = parameters.fastMAPeriod as number;
    const slowMA = parameters.slowMAPeriod as number;
    if (fastMA && slowMA && fastMA >= slowMA) {
      violations.push('Fast MA period must be less than slow MA period');
    }

    // Check RSI overbought vs oversold
    const rsiOverbought = parameters.rsiOverbought as number;
    const rsiOversold = parameters.rsiOversold as number;
    if (rsiOverbought && rsiOversold && rsiOverbought <= rsiOversold) {
      violations.push('RSI overbought level must be greater than oversold level');
    }

    return violations;
  }

  // ===== Parameter Metadata =====

  /**
   * Get parameter type based on name
   */
  private getParameterType(paramName: string): 'continuous' | 'discrete' | 'categorical' {
    const discreteParams = [
      'period', 'length', 'lookback', 'days', 'bars',
      'fastMAPeriod', 'slowMAPeriod', 'rsiPeriod', 'macdFast', 'macdSlow', 'macdSignal'
    ];

    if (discreteParams.some(pattern => paramName.toLowerCase().includes(pattern))) {
      return 'discrete';
    }

    return 'continuous';
  }

  /**
   * Get parameter description
   */
  private getParameterDescription(paramName: string, strategyType: StrategyType): string {
    const descriptions: Record<string, string> = {
      stopLoss: 'Stop loss distance in pips from entry price',
      takeProfit: 'Take profit distance in pips from entry price',
      riskPerTrade: 'Maximum risk per trade as percentage of account balance',
      fastMAPeriod: 'Period for fast moving average calculation',
      slowMAPeriod: 'Period for slow moving average calculation',
      rsiPeriod: 'Period for RSI (Relative Strength Index) calculation',
      rsiOverbought: 'RSI level considered overbought (sell signal)',
      rsiOversold: 'RSI level considered oversold (buy signal)',
      macdFast: 'Fast EMA period for MACD calculation',
      macdSlow: 'Slow EMA period for MACD calculation',
      macdSignal: 'Signal line EMA period for MACD',
      bollingerPeriod: 'Period for Bollinger Bands calculation',
      bollingerDeviation: 'Standard deviation multiplier for Bollinger Bands',
      atrPeriod: 'Period for Average True Range calculation',
      atrMultiplier: 'Multiplier for ATR-based stop loss and take profit',
      lotSize: 'Position size in lots',
      maxPositions: 'Maximum number of open positions',
      tradeFrequency: 'Maximum trades per day'
    };

    return descriptions[paramName] || `${paramName} parameter for ${strategyType} strategy`;
  }

  /**
   * Get parameter category
   */
  private getParameterCategory(paramName: string): 'risk' | 'entry' | 'exit' | 'indicator' | 'custom' {
    const riskParams = ['stopLoss', 'takeProfit', 'riskPerTrade', 'lotSize', 'maxPositions'];
    const entryParams = ['entryThreshold', 'signalStrength', 'confirmationBars'];
    const exitParams = ['exitSignal', 'trailingStop', 'timeExit'];
    const indicatorParams = [
      'fastMAPeriod', 'slowMAPeriod', 'rsiPeriod', 'rsiOverbought', 'rsiOversold',
      'macdFast', 'macdSlow', 'macdSignal', 'bollingerPeriod', 'bollingerDeviation',
      'atrPeriod', 'atrMultiplier'
    ];

    if (riskParams.includes(paramName)) return 'risk';
    if (entryParams.includes(paramName)) return 'entry';
    if (exitParams.includes(paramName)) return 'exit';
    if (indicatorParams.includes(paramName)) return 'indicator';
    
    return 'custom';
  }

  /**
   * Get parameter dependencies
   */
  private getParameterDependencies(paramName: string): string[] | undefined {
    const dependencies: Record<string, string[]> = {
      takeProfit: ['stopLoss'],
      slowMAPeriod: ['fastMAPeriod'],
      rsiOverbought: ['rsiOversold'],
      macdSlow: ['macdFast'],
      macdSignal: ['macdFast', 'macdSlow']
    };

    return dependencies[paramName];
  }

  // ===== Initialization =====

  /**
   * Initialize default parameter range templates
   */
  private initializeDefaultTemplates(): void {
    // Momentum strategy templates
    this.rangeTemplates.push({
      strategyType: 'momentum',
      marketRegime: 'trending',
      parameters: {
        stopLoss: { min: 10, max: 100, defaultValue: 20 },
        takeProfit: { min: 20, max: 200, defaultValue: 60 },
        fastMAPeriod: { min: 5, max: 20, defaultValue: 10, step: 1 },
        slowMAPeriod: { min: 20, max: 100, defaultValue: 50, step: 1 },
        rsiPeriod: { min: 10, max: 30, defaultValue: 14, step: 1 },
        rsiOverbought: { min: 70, max: 90, defaultValue: 80 },
        rsiOversold: { min: 10, max: 30, defaultValue: 20 },
        riskPerTrade: { min: 0.5, max: 5, defaultValue: 2 }
      }
    });

    // Mean reversion strategy templates
    this.rangeTemplates.push({
      strategyType: 'mean_reversion',
      marketRegime: 'ranging',
      parameters: {
        stopLoss: { min: 15, max: 80, defaultValue: 30 },
        takeProfit: { min: 15, max: 80, defaultValue: 30 },
        rsiPeriod: { min: 10, max: 25, defaultValue: 14, step: 1 },
        rsiOverbought: { min: 75, max: 95, defaultValue: 85 },
        rsiOversold: { min: 5, max: 25, defaultValue: 15 },
        bollingerPeriod: { min: 15, max: 30, defaultValue: 20, step: 1 },
        bollingerDeviation: { min: 1.5, max: 3, defaultValue: 2 },
        riskPerTrade: { min: 0.5, max: 3, defaultValue: 1.5 }
      }
    });

    // Breakout strategy templates
    this.rangeTemplates.push({
      strategyType: 'breakout',
      marketRegime: 'volatile',
      parameters: {
        stopLoss: { min: 5, max: 50, defaultValue: 15 },
        takeProfit: { min: 20, max: 150, defaultValue: 45 },
        atrPeriod: { min: 10, max: 30, defaultValue: 14, step: 1 },
        atrMultiplier: { min: 1, max: 4, defaultValue: 2 },
        bollingerPeriod: { min: 15, max: 25, defaultValue: 20, step: 1 },
        bollingerDeviation: { min: 1.8, max: 2.5, defaultValue: 2 },
        riskPerTrade: { min: 1, max: 4, defaultValue: 2.5 }
      }
    });

    // Universal template (fallback)
    this.rangeTemplates.push({
      strategyType: 'custom',
      marketRegime: 'any',
      parameters: {
        stopLoss: { min: 10, max: 100, defaultValue: 30 },
        takeProfit: { min: 20, max: 200, defaultValue: 60 },
        riskPerTrade: { min: 0.5, max: 5, defaultValue: 2 }
      }
    });
  }

  /**
   * Initialize default market volatility data
   */
  private initializeDefaultVolatilityData(): void {
    // Major forex pairs
    this.volatilityData.set('EURUSD', {
      symbol: 'EURUSD',
      averageVolatility: 0.0065,
      highVolatilityThreshold: 0.01,
      lowVolatilityThreshold: 0.004,
      averageSpread: 1.2,
      typicalRange: { daily: 0.005, weekly: 0.015, monthly: 0.06 }
    });

    this.volatilityData.set('GBPUSD', {
      symbol: 'GBPUSD',
      averageVolatility: 0.0085,
      highVolatilityThreshold: 0.012,
      lowVolatilityThreshold: 0.005,
      averageSpread: 1.5,
      typicalRange: { daily: 0.007, weekly: 0.02, monthly: 0.08 }
    });

    this.volatilityData.set('USDJPY', {
      symbol: 'USDJPY',
      averageVolatility: 0.007,
      highVolatilityThreshold: 0.01,
      lowVolatilityThreshold: 0.004,
      averageSpread: 1.0,
      typicalRange: { daily: 0.006, weekly: 0.018, monthly: 0.07 }
    });

    // Add more instruments as needed...
  }

  /**
   * Get default parameters for unknown strategy types
   */
  private getDefaultParameters(): Record<string, ParameterConstraint> {
    return {
      stopLoss: { min: 10, max: 100, defaultValue: 30 },
      takeProfit: { min: 20, max: 200, defaultValue: 60 },
      riskPerTrade: { min: 0.5, max: 5, defaultValue: 2 }
    };
  }
}