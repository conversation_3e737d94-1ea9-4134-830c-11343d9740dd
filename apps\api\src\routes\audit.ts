/**
 * Audit Trail and Compliance API Routes
 * 
 * RESTful API endpoints for audit trail access and compliance reporting
 * Part of Task 5: Audit Trail and Compliance
 */

import { Router } from 'express';
import type { Request, Response } from 'express';
import { AuditTrailService } from '../services/compliance/AuditTrailService.js';
import { authMiddleware } from '../middleware/auth.js';
import { rateLimiter } from '../middleware/rate-limit.js';
import type { 
  AuditQueryFilter, 
  AuditExportOptions
} from '@golddaddy/types';

const router = Router();

// Note: AuditTrailService will be injected via dependency injection
let auditService: AuditTrailService;

// Apply authentication to all routes
router.use(authMiddleware);

// Rate limiting for audit operations
const auditRateLimit = rateLimiter({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many audit requests'
});

router.use(auditRateLimit);

// Middleware to inject audit service
router.use((req, res, next) => {
  if (!auditService) {
    return res.status(503).json({
      success: false,
      error: {
        code: 'SERVICE_UNAVAILABLE',
        message: 'Audit service not initialized'
      }
    });
  }
  
  // Inject service into request locals
  req.app.locals.auditService = auditService;
  next();
});

/**
 * Set audit service instance (called during initialization)
 */
export function setAuditService(service: AuditTrailService): void {
  auditService = service;
}

// Type for authenticated request
interface AuthenticatedRequest extends Request {
  userId?: string;
}

// GET /api/audit/logs - Get audit logs with filtering
router.get('/logs', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    // Parse query parameters
    const filters: AuditQueryFilter = {
      startDate: req.query.startDate ? new Date(req.query.startDate as string) : undefined,
      endDate: req.query.endDate ? new Date(req.query.endDate as string) : undefined,
      actionTypes: req.query.actionTypes ? (req.query.actionTypes as string).split(',') as any : undefined,
      severities: req.query.severities ? (req.query.severities as string).split(',') as any : undefined,
      userId: req.query.userId as string,
      brokerId: req.query.brokerId as string,
      success: req.query.success === 'true' ? true : req.query.success === 'false' ? false : undefined,
      complianceRelevant: req.query.complianceRelevant === 'true',
      hasTradeImpact: req.query.hasTradeImpact === 'true',
      searchTerm: req.query.searchTerm as string,
      limit: req.query.limit ? parseInt(req.query.limit as string) : 50,
      offset: req.query.offset ? parseInt(req.query.offset as string) : 0,
      sortBy: (req.query.sortBy as any) || 'timestamp',
      sortOrder: (req.query.sortOrder as 'asc' | 'desc') || 'desc'
    };

    const auditService = req.app.locals.auditService;
    const auditLogs = await auditService.queryAuditLogs(filters);
    
    res.json({
      success: true,
      data: auditLogs,
      totalCount: auditLogs.length,
      filters
    });

  } catch (error) {
    console.error('Failed to retrieve audit logs:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error',
      message: 'Failed to retrieve audit logs'
    });
  }
});

// GET /api/audit/statistics - Get audit trail statistics
router.get('/statistics', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000); // Default: 30 days ago
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : new Date();

    const auditService = req.app.locals.auditService;
    const statistics = await auditService.getAuditStatistics({
      startDate,
      endDate,
      userId: req.query.includeAllUsers === 'true' ? undefined : userId
    });

    res.json({
      success: true,
      data: statistics,
      period: { startDate, endDate }
    });

  } catch (error) {
    console.error('Failed to retrieve audit statistics:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Internal server error' 
    });
  }
});

// POST /api/audit/export - Export audit logs
router.post('/export', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    const exportOptions: AuditExportOptions = {
      format: req.body.format || 'json',
      filters: req.body.filters || {},
      includeMetadata: req.body.includeMetadata !== false,
      anonymizeData: req.body.anonymizeData === true,
      compressionEnabled: req.body.compressionEnabled === true,
      encryptExport: req.body.encryptExport === true
    };

    // Restrict export to user's own data unless admin
    if (!req.body.includeAllUsers) {
      exportOptions.filters.userId = userId;
    }

    const auditService = req.app.locals.auditService;
    const exportResult = await auditService.exportAuditLogs(exportOptions);

    // Log the export action
    await auditService.logUserAction({
      userId,
      actionType: 'DATA_EXPORT',
      message: `Audit logs exported in ${exportOptions.format} format`,
      success: true,
      details: {
        exportFormat: exportOptions.format,
        recordCount: exportResult.recordCount,
        anonymized: exportOptions.anonymizeData,
        encrypted: exportOptions.encryptExport
      },
      complianceRelevant: true
    });

    res.json({
      success: true,
      data: exportResult
    });

  } catch (error) {
    console.error('Failed to export audit logs:', error);
    
    // Log the failed export attempt
    if (req.userId) {
      const auditService = req.app.locals.auditService;
      await auditService.logUserAction({
        userId: req.userId,
        actionType: 'DATA_EXPORT',
        message: 'Failed to export audit logs',
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        complianceRelevant: true
      });
    }

    res.status(500).json({ 
      success: false, 
      error: 'Export failed' 
    });
  }
});

// GET /api/audit/integrity/verify - Verify audit trail integrity
router.get('/integrity/verify', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    const startDate = req.query.startDate ? new Date(req.query.startDate as string) : undefined;
    const endDate = req.query.endDate ? new Date(req.query.endDate as string) : undefined;
    const batchSize = req.query.batchSize ? parseInt(req.query.batchSize as string) : 1000;

    const auditService = req.app.locals.auditService;
    const integrityResults = await auditService.verifyDataIntegrity({
      startDate,
      endDate,
      batchSize
    });

    // Log the integrity check
    await auditService.logSystemAction({
      actionType: 'SYSTEM_STARTUP', // Using existing enum value, ideally we'd have INTEGRITY_CHECK
      message: `Audit trail integrity verification completed`,
      success: integrityResults.overall.isValid,
      details: {
        totalRecords: integrityResults.summary.totalRecords,
        validRecords: integrityResults.summary.validRecords,
        integrityViolations: integrityResults.summary.integrityViolations,
        hashChainValid: integrityResults.summary.hashChainValid
      },
      complianceRelevant: true
    });

    res.json({
      success: true,
      data: integrityResults
    });

  } catch (error) {
    console.error('Failed to verify audit trail integrity:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Integrity verification failed' 
    });
  }
});

// GET /api/compliance/reports - Get compliance reports
router.get('/compliance/reports', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    const reportType = req.query.reportType as string;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
    const offset = req.query.offset ? parseInt(req.query.offset as string) : 0;

    const auditService = req.app.locals.auditService;
    
    // This would typically query a compliance reports table
    // For now, we'll use the service method to get recent reports
    const reports = await auditService.getComplianceReports({
      reportType: reportType as any,
      limit,
      offset
    });

    res.json({
      success: true,
      data: reports
    });

  } catch (error) {
    console.error('Failed to retrieve compliance reports:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to retrieve compliance reports' 
    });
  }
});

// POST /api/compliance/reports - Generate new compliance report
router.post('/compliance/reports', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    const { reportType, startDate, endDate } = req.body;
    
    if (!reportType || !startDate || !endDate) {
      return res.status(400).json({
        success: false,
        error: 'Missing required fields: reportType, startDate, endDate'
      });
    }

    const reportPeriod = {
      start: new Date(startDate),
      end: new Date(endDate)
    };

    const auditService = req.app.locals.auditService;
    const complianceReport = await auditService.generateComplianceReport(reportPeriod);

    // Log the report generation
    await auditService.logUserAction({
      userId,
      actionType: 'COMPLIANCE_REPORT_GENERATED',
      message: `${reportType} compliance report generated for period ${startDate} to ${endDate}`,
      success: true,
      details: {
        reportId: complianceReport.id,
        reportType,
        period: reportPeriod,
        complianceScore: complianceReport.summary.complianceScore
      },
      complianceRelevant: true
    });

    res.json({
      success: true,
      data: complianceReport
    });

  } catch (error) {
    console.error('Failed to generate compliance report:', error);
    
    // Log the failed report generation
    if (req.userId) {
      const auditService = req.app.locals.auditService;
      await auditService.logUserAction({
        userId: req.userId,
        actionType: 'COMPLIANCE_REPORT_GENERATED',
        message: 'Failed to generate compliance report',
        success: false,
        errorMessage: error instanceof Error ? error.message : 'Unknown error',
        complianceRelevant: true
      });
    }

    res.status(500).json({ 
      success: false, 
      error: 'Failed to generate compliance report' 
    });
  }
});

// GET /api/compliance/issues - Get compliance issues
router.get('/compliance/issues', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    const category = req.query.category as string;
    const severity = req.query.severity as string;
    const status = req.query.status as string;
    const limit = req.query.limit ? parseInt(req.query.limit as string) : 50;
    const offset = req.query.offset ? parseInt(req.query.offset as string) : 0;

    const auditService = req.app.locals.auditService;
    const issues = await auditService.getComplianceIssues({
      category: category as any,
      severity: severity as any,
      status: status as any,
      limit,
      offset
    });

    res.json({
      success: true,
      data: issues
    });

  } catch (error) {
    console.error('Failed to retrieve compliance issues:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to retrieve compliance issues' 
    });
  }
});

// PUT /api/compliance/issues/:issueId - Update compliance issue
router.put('/compliance/issues/:issueId', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    const { issueId } = req.params;
    const { status, resolution, preventiveActions } = req.body;

    const auditService = req.app.locals.auditService;
    const updatedIssue = await auditService.updateComplianceIssue(issueId, {
      status,
      resolution,
      preventiveActions,
      resolvedBy: status === 'RESOLVED' ? userId : undefined,
      resolvedAt: status === 'RESOLVED' ? new Date() : undefined
    });

    // Log the issue update
    await auditService.logUserAction({
      userId,
      actionType: 'CONFIGURATION_CHANGE', // Using existing enum, ideally we'd have COMPLIANCE_ISSUE_UPDATED
      message: `Compliance issue ${issueId} updated to status: ${status}`,
      success: true,
      details: {
        issueId,
        oldStatus: 'unknown', // Would need to fetch old status
        newStatus: status,
        resolution,
        preventiveActions
      },
      complianceRelevant: true
    });

    res.json({
      success: true,
      data: updatedIssue
    });

  } catch (error) {
    console.error('Failed to update compliance issue:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to update compliance issue' 
    });
  }
});

// GET /api/compliance/dashboard - Get compliance dashboard data
router.get('/compliance/dashboard', async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        error: 'Authentication required' 
      });
    }

    const period = req.query.period as string || '30d'; // 7d, 30d, 90d, 1y
    
    // Calculate date range based on period
    let startDate: Date;
    const endDate = new Date();
    
    switch (period) {
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000);
        break;
      default: // 30d
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    }

    const auditService = req.app.locals.auditService;
    
    // Get dashboard data in parallel
    const [statistics, recentIssues, integrityStatus] = await Promise.all([
      auditService.getAuditStatistics({ startDate, endDate, userId }),
      auditService.getComplianceIssues({ status: 'OPEN', limit: 10 }),
      auditService.verifyDataIntegrity({ startDate, endDate, batchSize: 100 })
    ]);

    const dashboardData = {
      period: { startDate, endDate, label: period },
      statistics,
      recentIssues,
      integrityStatus: integrityStatus.summary,
      complianceScore: statistics.integrityStatus.integrityScore,
      trends: {
        dailyEventCount: Math.floor(statistics.averageEventsPerDay),
        successRate: statistics.successRate,
        complianceEventRate: statistics.complianceEvents / statistics.totalEvents
      }
    };

    res.json({
      success: true,
      data: dashboardData
    });

  } catch (error) {
    console.error('Failed to get compliance dashboard data:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Failed to get compliance dashboard data' 
    });
  }
});

export default router;