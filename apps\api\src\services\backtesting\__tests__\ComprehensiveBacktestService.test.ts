import { describe, it, expect, beforeEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { PrismaClient, Strategy, Timeframe, StrategyType, StrategyStatus, MarketRegime } from '@prisma/client';
import { ComprehensiveBacktestService, BacktestRequest } from '../ComprehensiveBacktestService';
import { BacktestingDataService } from '../BacktestingDataService';
import { BacktestingExecutionEngine } from '../BacktestingExecutionEngine';

// Mock the dependencies
vi.mock('../BacktestingDataService');
vi.mock('../BacktestingExecutionEngine');

const mockPrisma = {
  strategy: {
    findUnique: vi.fn(),
  },
} as unknown as PrismaClient;

const mockDataService = {
  getHistoricalDataWithRegimes: vi.fn(),
} as unknown as BacktestingDataService;

const mockExecutionEngine = {
  executeOrder: vi.fn(),
  updateConfig: vi.fn(),
} as unknown as BacktestingExecutionEngine;

describe('ComprehensiveBacktestService', () => {
  let service: ComprehensiveBacktestService;

  // Define mockStrategy at the outer scope so it's available to all tests
  const mockStrategy: Strategy = {
      id: 'strategy-1',
      name: 'Test Strategy',
      description: 'Test momentum strategy',
      type: StrategyType.MOMENTUM,
      instruments: ['EURUSD'],
      timeframe: 'H1',
      riskPerTrade: new Decimal(2),
      stopLoss: new Decimal(1),
      takeProfit: new Decimal(2),
      customParams: null,
      winRate: null,
      profitFactor: null,
      sharpeRatio: null,
      maxDrawdown: null,
      totalReturn: null,
      status: StrategyStatus.BACKTESTED,
      marketRegime: MarketRegime.ANY,
      riskLevel: 'MODERATE' as any,
      category: null,
      author: null,
      difficulty: 'INTERMEDIATE' as any,
      tags: [],
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    };

  const mockHistoricalData = Array.from({ length: 100 }, (_, i) => ({
      timestamp: new Date(2023, 0, 1, i),
      instrument: 'EURUSD',
      timeframe: Timeframe.H1,
      open: 1.0500 + (i * 0.0001),
      high: 1.0520 + (i * 0.0001),
      low: 1.0495 + (i * 0.0001),
      close: 1.0510 + (i * 0.0001),
      volume: 1000000,
      source: 'MT5' as any,
      indicators: {
        sma_20: 1.0505 + (i * 0.0001),
        sma_50: 1.0500 + (i * 0.0001),
        rsi_14: 50 + Math.sin(i * 0.1) * 20,
      },
      marketRegime: MarketRegime.TRENDING,
    }));

  const validBacktestRequest: BacktestRequest = {
      strategyId: 'strategy-1',
      instruments: ['EURUSD'],
      timeframes: [Timeframe.H1],
      startDate: new Date('2023-01-01T00:00:00Z'),
      endDate: new Date('2023-01-05T00:00:00Z'),
      initialCapital: 10000,
      userId: 'user-1',
      includeRegimeAnalysis: true,
      includeDrawdownAnalysis: true,
    };

  beforeEach(() => {
    service = new ComprehensiveBacktestService(mockPrisma);
    
    // Replace the internal services with mocks
    (service as any).dataService = mockDataService;
    (service as any).executionEngine = mockExecutionEngine;
    
    vi.clearAllMocks();
  });

  describe('runBacktest', () => {
    beforeEach(() => {
      (mockPrisma.strategy.findUnique as any).mockResolvedValue(mockStrategy);
      (mockDataService.getHistoricalDataWithRegimes as any).mockResolvedValue(mockHistoricalData);
      (mockExecutionEngine.executeOrder as any).mockResolvedValue({
        orderId: 'order-1',
        executedPrice: 1.0505,
        executedQuantity: 10000,
        executionTime: new Date(),
        slippage: 0.0001,
        spread: 0.8,
        commission: 7,
        marketImpact: 0.00005,
        executionCost: 15,
        success: true,
      });
    });

    it('should complete a basic backtest successfully', async () => {
      const result = await service.runBacktest(validBacktestRequest);

      expect(result).toBeDefined();
      expect(result.id).toBeTruthy();
      expect(result.strategyId).toBe('strategy-1');
      expect(result.userId).toBe('user-1');
      expect(result.initialCapital).toBe(10000);
      expect(result.overallMetrics).toBeDefined();
      expect(result.equityCurve).toBeDefined();
      expect(result.timeframeAnalysis).toHaveLength(1);
      expect(result.drawdownAnalysis).toBeDefined();
      expect(result.executionStats).toBeDefined();
    });

    it('should include regime analysis when requested', async () => {
      const result = await service.runBacktest(validBacktestRequest);

      expect(result.regimeAnalysis).toBeDefined();
      expect(result.regimeAnalysis?.length).toBeGreaterThan(0);
    });

    it('should handle multiple timeframes', async () => {
      const multiTimeframeRequest: BacktestRequest = {
        ...validBacktestRequest,
        timeframes: [Timeframe.H1, Timeframe.H4],
      };

      // Create H4 data for the test
      const h4Data = Array.from({ length: 25 }, (_, i) => ({
        timestamp: new Date(2023, 0, 1, i * 4), // Every 4 hours
        instrument: 'EURUSD',
        timeframe: Timeframe.H4,
        open: 1.0500 + (i * 0.0004),
        high: 1.0520 + (i * 0.0004),
        low: 1.0495 + (i * 0.0004),
        close: 1.0510 + (i * 0.0004),
        volume: 4000000, // Higher volume for H4
        source: 'MT5' as any,
        indicators: {
          sma_20: 1.0505 + (i * 0.0004),
          sma_50: 1.0500 + (i * 0.0004),
          rsi_14: 50 + Math.sin(i * 0.2) * 20,
        },
        marketRegime: MarketRegime.TRENDING,
      }));

      // Mock to return both H1 and H4 data
      (mockDataService.getHistoricalDataWithRegimes as any).mockResolvedValue([
        ...mockHistoricalData,
        ...h4Data,
      ]);

      const result = await service.runBacktest(multiTimeframeRequest);

      expect(result.timeframeAnalysis).toHaveLength(2);
      expect(result.timeframeAnalysis[0].timeframe).toBe(Timeframe.H1);
      expect(result.timeframeAnalysis[1].timeframe).toBe(Timeframe.H4);
    });

    it('should handle multiple instruments', async () => {
      const multiInstrumentRequest: BacktestRequest = {
        ...validBacktestRequest,
        instruments: ['EURUSD', 'GBPUSD'],
      };

      // Mock data for both instruments
      const gbpusdData = mockHistoricalData.map(point => ({
        ...point,
        instrument: 'GBPUSD',
        open: 1.2500 + (point.open - 1.0500),
        high: 1.2520 + (point.high - 1.0520),
        low: 1.2495 + (point.low - 1.0495),
        close: 1.2510 + (point.close - 1.0510),
      }));

      (mockDataService.getHistoricalDataWithRegimes as any).mockResolvedValue([
        ...mockHistoricalData,
        ...gbpusdData,
      ]);

      const result = await service.runBacktest(multiInstrumentRequest);

      expect(result).toBeDefined();
      expect(result.trades.length).toBeGreaterThan(0);
    });

    it('should update execution configuration when provided', async () => {
      const requestWithConfig: BacktestRequest = {
        ...validBacktestRequest,
        executionConfig: {
          baseSpread: 1.2,
          baseSlippage: 0.08,
        },
      };

      await service.runBacktest(requestWithConfig);

      expect(mockExecutionEngine.updateConfig).toHaveBeenCalledWith({
        baseSpread: 1.2,
        baseSlippage: 0.08,
      });
    });

    it('should throw error for non-existent strategy', async () => {
      (mockPrisma.strategy.findUnique as any).mockResolvedValue(null);

      const invalidRequest: BacktestRequest = {
        ...validBacktestRequest,
        strategyId: 'non-existent-strategy',
      };

      await expect(service.runBacktest(invalidRequest)).rejects.toThrow('Strategy not found: non-existent-strategy');
    });

    it('should throw error when no historical data available', async () => {
      (mockDataService.getHistoricalDataWithRegimes as any).mockResolvedValue([]);

      await expect(service.runBacktest(validBacktestRequest)).rejects.toThrow('No historical data available for backtesting');
    });

    it('should generate ML insights when requested', async () => {
      const requestWithML: BacktestRequest = {
        ...validBacktestRequest,
        includeMLInsights: true,
      };

      const result = await service.runBacktest(requestWithML);

      expect(result.mlInsights).toBeDefined();
      expect(Array.isArray(result.mlInsights)).toBe(true);
    });

    it('should generate coaching advice when requested', async () => {
      const requestWithCoaching: BacktestRequest = {
        ...validBacktestRequest,
        generateCoachingAdvice: true,
      };

      const result = await service.runBacktest(requestWithCoaching);

      expect(result.coachingAdvice).toBeDefined();
      expect(Array.isArray(result.coachingAdvice)).toBe(true);
    });
  });

  describe('Performance Metrics Calculation', () => {
    it('should calculate performance metrics correctly', () => {
      const mockTrades = [
        {
          id: 'trade-1',
          pnl: 100,
          pnlPercentage: 1,
          holdingPeriod: 2,
          entryTime: new Date('2023-01-01T10:00:00Z'),
          exitTime: new Date('2023-01-01T12:00:00Z'),
          entryPrice: 1.0500,
          quantity: 10000,
          executionDetails: { entryExecution: { commission: 7 }, exitExecution: { commission: 7 } },
        },
        {
          id: 'trade-2',
          pnl: -50,
          pnlPercentage: -0.5,
          holdingPeriod: 1,
          entryTime: new Date('2023-01-01T14:00:00Z'),
          exitTime: new Date('2023-01-01T15:00:00Z'),
          entryPrice: 1.0510,
          quantity: 10000,
          executionDetails: { entryExecution: { commission: 7 }, exitExecution: { commission: 7 } },
        },
        {
          id: 'trade-3',
          pnl: 75,
          pnlPercentage: 0.75,
          holdingPeriod: 3,
          entryTime: new Date('2023-01-01T16:00:00Z'),
          exitTime: new Date('2023-01-01T19:00:00Z'),
          entryPrice: 1.0505,
          quantity: 10000,
          executionDetails: { entryExecution: { commission: 7 }, exitExecution: { commission: 7 } },
        },
      ];

      const equityCurve = [
        { equity: 10000, drawdown: 0, drawdownPercentage: 0, timestamp: new Date(), numberOfPositions: 0 },
        { equity: 10100, drawdown: 0, drawdownPercentage: 0, timestamp: new Date(), numberOfPositions: 0 },
        { equity: 10050, drawdown: 50, drawdownPercentage: 0.495, timestamp: new Date(), numberOfPositions: 0 },
        { equity: 10125, drawdown: 0, drawdownPercentage: 0, timestamp: new Date(), numberOfPositions: 0 },
      ];

      const calculatePerformanceMetrics = (service as any).calculatePerformanceMetrics.bind(service);
      const metrics = calculatePerformanceMetrics(mockTrades, 10000, equityCurve);

      expect(metrics.totalReturn).toBe(125);
      expect(metrics.totalReturnPercentage).toBe(1.25);
      expect(metrics.totalTrades).toBe(3);
      expect(metrics.winningTrades).toBe(2);
      expect(metrics.losingTrades).toBe(1);
      expect(metrics.winRate).toBeCloseTo(66.67, 2);
      expect(metrics.profitFactor).toBeCloseTo(3.5, 1);
      expect(metrics.averageWin).toBe(87.5);
      expect(metrics.averageLoss).toBe(50);
      expect(metrics.largestWin).toBe(100);
      expect(metrics.largestLoss).toBe(-50);
    });

    it('should handle empty trades gracefully', () => {
      const calculatePerformanceMetrics = (service as any).calculatePerformanceMetrics.bind(service);
      const metrics = calculatePerformanceMetrics([], 10000, []);

      expect(metrics.totalReturn).toBe(0);
      expect(metrics.totalTrades).toBe(0);
      expect(metrics.winRate).toBe(0);
      expect(metrics.profitFactor).toBe(1);
    });
  });

  describe('Drawdown Analysis', () => {
    it('should calculate drawdown analysis correctly', () => {
      const equityCurve = [
        { equity: 10000, drawdown: 0, drawdownPercentage: 0, timestamp: new Date('2023-01-01T08:00:00Z'), numberOfPositions: 0 },
        { equity: 10100, drawdown: 0, drawdownPercentage: 0, timestamp: new Date('2023-01-01T09:00:00Z'), numberOfPositions: 0 },
        { equity: 10050, drawdown: 50, drawdownPercentage: 0.495, timestamp: new Date('2023-01-01T10:00:00Z'), numberOfPositions: 0 },
        { equity: 10025, drawdown: 75, drawdownPercentage: 0.743, timestamp: new Date('2023-01-01T11:00:00Z'), numberOfPositions: 0 },
        { equity: 10125, drawdown: 0, drawdownPercentage: 0, timestamp: new Date('2023-01-01T12:00:00Z'), numberOfPositions: 0 },
      ];

      const calculateDrawdownAnalysis = (service as any).calculateDrawdownAnalysis.bind(service);
      const analysis = calculateDrawdownAnalysis(equityCurve);

      expect(analysis.maxDrawdown).toBe(75);
      expect(analysis.maxDrawdownPercentage).toBe(0.743);
      expect(analysis.drawdownPeriods).toHaveLength(1);
      expect(analysis.drawdownPeriods[0].start).toEqual(new Date('2023-01-01T10:00:00Z'));
      expect(analysis.drawdownPeriods[0].trough).toBe(10025);
      expect(analysis.drawdownPeriods[0].recovery).toEqual(new Date('2023-01-01T12:00:00Z'));
    });

    it('should handle equity curve with no drawdowns', () => {
      const equityCurve = [
        { equity: 10000, drawdown: 0, drawdownPercentage: 0, timestamp: new Date(), numberOfPositions: 0 },
        { equity: 10100, drawdown: 0, drawdownPercentage: 0, timestamp: new Date(), numberOfPositions: 0 },
        { equity: 10200, drawdown: 0, drawdownPercentage: 0, timestamp: new Date(), numberOfPositions: 0 },
      ];

      const calculateDrawdownAnalysis = (service as any).calculateDrawdownAnalysis.bind(service);
      const analysis = calculateDrawdownAnalysis(equityCurve);

      expect(analysis.maxDrawdown).toBe(0);
      expect(analysis.maxDrawdownPercentage).toBe(0);
      expect(analysis.drawdownPeriods).toHaveLength(0);
      expect(analysis.averageDrawdown).toBe(0);
    });
  });

  describe('Trading Signal Generation', () => {
    it('should generate buy signals for momentum strategy', () => {
      const historicalData = Array.from({ length: 25 }, (_, i) => ({
        timestamp: new Date(2023, 0, 1, i),
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        open: 1.0500 + (i * 0.0001),
        high: 1.0520 + (i * 0.0001),
        low: 1.0495 + (i * 0.0001),
        close: 1.0510 + (i * 0.0001),
        volume: 1000000,
        source: 'MT5' as any,
        indicators: {
          sma_20: 1.0505,
          sma_50: 1.0500,
          rsi_14: 65, // Not overbought
        },
      }));

      const currentCandle = {
        ...historicalData[historicalData.length - 1],
        close: 1.0515, // Above SMA20
        indicators: {
          sma_20: 1.0510,
          sma_50: 1.0505, // SMA20 > SMA50
          rsi_14: 65,
        },
      };

      const generateTradingSignals = (service as any).generateTradingSignals.bind(service);
      const signals = generateTradingSignals(mockStrategy, historicalData, currentCandle);

      expect(signals).toHaveLength(1);
      expect(signals[0].type).toBe('BUY');
      expect(signals[0].quantity).toBe(10000);
      expect(signals[0].stopLoss).toBeDefined();
      expect(signals[0].takeProfit).toBeDefined();
    });

    it('should generate sell signals for momentum strategy', () => {
      const historicalData = Array.from({ length: 25 }, (_, i) => ({
        timestamp: new Date(2023, 0, 1, i),
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        open: 1.0510 - (i * 0.0001),
        high: 1.0530 - (i * 0.0001),
        low: 1.0505 - (i * 0.0001),
        close: 1.0520 - (i * 0.0001),
        volume: 1000000,
        source: 'MT5' as any,
        indicators: {
          sma_20: 1.0515,
          sma_50: 1.0520,
          rsi_14: 35, // Not oversold
        },
      }));

      const currentCandle = {
        ...historicalData[historicalData.length - 1],
        close: 1.0495, // Below SMA20
        indicators: {
          sma_20: 1.0500,
          sma_50: 1.0510, // SMA20 < SMA50
          rsi_14: 35,
        },
      };

      const generateTradingSignals = (service as any).generateTradingSignals.bind(service);
      const signals = generateTradingSignals(mockStrategy, historicalData, currentCandle);

      expect(signals).toHaveLength(1);
      expect(signals[0].type).toBe('SELL');
      expect(signals[0].quantity).toBe(10000);
      expect(signals[0].stopLoss).toBeDefined();
      expect(signals[0].takeProfit).toBeDefined();
    });

    it('should not generate signals when insufficient data', () => {
      const historicalData = Array.from({ length: 10 }, (_, i) => ({ // Less than 20 required
        timestamp: new Date(2023, 0, 1, i),
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        open: 1.0500,
        high: 1.0520,
        low: 1.0495,
        close: 1.0510,
        volume: 1000000,
        source: 'MT5' as any,
      }));

      const currentCandle = historicalData[historicalData.length - 1];

      const generateTradingSignals = (service as any).generateTradingSignals.bind(service);
      const signals = generateTradingSignals(mockStrategy, historicalData, currentCandle);

      expect(signals).toHaveLength(0);
    });
  });

  describe('ML Insights Generation', () => {
    it('should generate appropriate insights for good performance', () => {
      const goodMetrics = {
        sharpeRatio: 2.0,
        maxDrawdownPercentage: 5.0,
        winRate: 65,
        profitFactor: 2.5,
        totalReturnPercentage: 25,
      } as any;

      const generateMLInsights = (service as any).generateMLInsights.bind(service);
      const insights = generateMLInsights(goodMetrics);

      expect(insights).toContain('Excellent risk-adjusted returns detected. Strategy shows strong performance consistency.');
    });

    it('should generate warnings for poor performance', () => {
      const poorMetrics = {
        sharpeRatio: 0.5,
        maxDrawdownPercentage: 25.0,
        winRate: 35,
        profitFactor: 0.8,
        totalReturnPercentage: -10,
      } as any;

      const generateMLInsights = (service as any).generateMLInsights.bind(service);
      const insights = generateMLInsights(poorMetrics);

      expect(insights).toContain('High drawdown detected. Consider implementing additional risk management measures.');
    });
  });

  describe('Coaching Advice Generation', () => {
    it('should provide advice for low win rate', () => {
      const metrics = {
        winRate: 30,
        profitFactor: 1.5,
        maxDrawdownPercentage: 15,
      } as any;

      const drawdownAnalysis = {
        maxDrawdownDuration: 100, // Less than 168 hours
      } as any;

      const generateCoachingAdvice = (service as any).generateCoachingAdvice.bind(service);
      const advice = generateCoachingAdvice(metrics, drawdownAnalysis);

      expect(advice).toContain('Low win rate detected. Focus on improving entry signals or consider trend-following strategies.');
    });

    it('should provide advice for low profit factor', () => {
      const metrics = {
        winRate: 60,
        profitFactor: 1.1,
        maxDrawdownPercentage: 10,
      } as any;

      const drawdownAnalysis = {
        maxDrawdownDuration: 50,
      } as any;

      const generateCoachingAdvice = (service as any).generateCoachingAdvice.bind(service);
      const advice = generateCoachingAdvice(metrics, drawdownAnalysis);

      expect(advice).toContain('Profit factor is below optimal range. Work on cutting losses faster or letting winners run longer.');
    });

    it('should provide advice for extended drawdown periods', () => {
      const metrics = {
        winRate: 50,
        profitFactor: 1.8,
        maxDrawdownPercentage: 12,
      } as any;

      const drawdownAnalysis = {
        maxDrawdownDuration: 200, // More than 168 hours (1 week)
      } as any;

      const generateCoachingAdvice = (service as any).generateCoachingAdvice.bind(service);
      const advice = generateCoachingAdvice(metrics, drawdownAnalysis);

      expect(advice).toContain('Extended drawdown periods detected. Consider position sizing adjustments or diversification.');
    });
  });
});