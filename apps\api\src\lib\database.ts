import { prisma, executeTransaction } from '@golddaddy/config/src/database';
import { Prisma } from '@prisma/client';

// Database query utilities with error handling and logging

// User operations
export const userOperations = {
  async findById(id: string) {
    try {
      return await prisma.user.findUnique({
        where: { id },
        include: {
          goals: {
            where: { deletedAt: null },
            orderBy: { createdAt: 'desc' },
          },
          featureFlags: true,
          confidenceAssessment: true,
        },
      });
    } catch (error) {
      console.error('Error finding user by ID:', error);
      throw new Error('Failed to retrieve user');
    }
  },

  async findByEmail(email: string) {
    try {
      return await prisma.user.findUnique({
        where: { email },
        include: {
          featureFlags: true,
          confidenceAssessment: true,
        },
      });
    } catch (error) {
      console.error('Error finding user by email:', error);
      throw new Error('Failed to retrieve user');
    }
  },

  async create(userData: Prisma.UserCreateInput) {
    try {
      return await executeTransaction(async (tx) => {
        // Create user
        const user = await tx.user.create({
          data: userData,
        });

        // Create default feature flags
        await tx.featureFlags.create({
          data: {
            userId: user.id,
            level: 'BASIC',
          },
        });

        // Create initial confidence assessment
        await tx.confidenceAssessment.create({
          data: {
            userId: user.id,
            currentStage: 'GOAL_SETTING',
            overallConfidenceScore: 0,
          },
        });

        return user;
      });
    } catch (error) {
      console.error('Error creating user:', error);
      throw new Error('Failed to create user');
    }
  },

  async update(id: string, updateData: Prisma.UserUpdateInput) {
    try {
      return await prisma.user.update({
        where: { id },
        data: updateData,
        include: {
          goals: {
            where: { deletedAt: null },
            orderBy: { createdAt: 'desc' },
          },
          featureFlags: true,
          confidenceAssessment: true,
        },
      });
    } catch (error) {
      console.error('Error updating user:', error);
      throw new Error('Failed to update user');
    }
  },

  async softDelete(id: string) {
    try {
      return await executeTransaction(async (tx) => {
        // Soft delete user
        await tx.user.update({
          where: { id },
          data: { deletedAt: new Date() },
        });

        // Soft delete all user's trading goals
        await tx.tradingGoal.updateMany({
          where: { userId: id },
          data: { deletedAt: new Date() },
        });

        return { success: true };
      });
    } catch (error) {
      console.error('Error soft deleting user:', error);
      throw new Error('Failed to delete user');
    }
  },
};

// Trading goal operations
export const tradingGoalOperations = {
  async findByUserId(userId: string) {
    try {
      return await prisma.tradingGoal.findMany({
        where: {
          userId,
          deletedAt: null,
        },
        include: {
          trades: {
            orderBy: { executionTime: 'desc' },
            take: 10, // Last 10 trades per goal
          },
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      console.error('Error finding goals by user ID:', error);
      throw new Error('Failed to retrieve trading goals');
    }
  },

  async create(goalData: Prisma.TradingGoalCreateInput) {
    try {
      return await prisma.tradingGoal.create({
        data: goalData,
        include: {
          user: true,
        },
      });
    } catch (error) {
      console.error('Error creating trading goal:', error);
      throw new Error('Failed to create trading goal');
    }
  },

  async update(id: string, updateData: Prisma.TradingGoalUpdateInput) {
    try {
      return await prisma.tradingGoal.update({
        where: { id },
        data: updateData,
        include: {
          trades: {
            orderBy: { executionTime: 'desc' },
            take: 10,
          },
        },
      });
    } catch (error) {
      console.error('Error updating trading goal:', error);
      throw new Error('Failed to update trading goal');
    }
  },

  async softDelete(id: string) {
    try {
      return await prisma.tradingGoal.update({
        where: { id },
        data: { deletedAt: new Date() },
      });
    } catch (error) {
      console.error('Error soft deleting trading goal:', error);
      throw new Error('Failed to delete trading goal');
    }
  },
};

// Strategy operations
export const strategyOperations = {
  async findAll() {
    try {
      return await prisma.strategy.findMany({
        where: { deletedAt: null },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      console.error('Error finding strategies:', error);
      throw new Error('Failed to retrieve strategies');
    }
  },

  async findByType(type: string) {
    try {
      return await prisma.strategy.findMany({
        where: {
          type: type as string,
          deletedAt: null,
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      console.error('Error finding strategies by type:', error);
      throw new Error('Failed to retrieve strategies');
    }
  },

  async create(strategyData: Prisma.StrategyCreateInput) {
    try {
      return await prisma.strategy.create({
        data: strategyData,
      });
    } catch (error) {
      console.error('Error creating strategy:', error);
      throw new Error('Failed to create strategy');
    }
  },

  async update(id: string, updateData: Prisma.StrategyUpdateInput) {
    try {
      return await prisma.strategy.update({
        where: { id },
        data: updateData,
      });
    } catch (error) {
      console.error('Error updating strategy:', error);
      throw new Error('Failed to update strategy');
    }
  },
};

// Trade operations
export const tradeOperations = {
  async findByUserId(userId: string, limit: number = 50) {
    try {
      return await prisma.trade.findMany({
        where: { userId },
        include: {
          strategy: true,
          goal: true,
        },
        orderBy: { executionTime: 'desc' },
        take: limit,
      });
    } catch (error) {
      console.error('Error finding trades by user ID:', error);
      throw new Error('Failed to retrieve trades');
    }
  },

  async create(tradeData: Prisma.TradeCreateInput) {
    try {
      return await prisma.trade.create({
        data: tradeData,
        include: {
          strategy: true,
          goal: true,
        },
      });
    } catch (error) {
      console.error('Error creating trade:', error);
      throw new Error('Failed to create trade');
    }
  },

  async update(id: string, updateData: Prisma.TradeUpdateInput) {
    try {
      return await prisma.trade.update({
        where: { id },
        data: updateData,
        include: {
          strategy: true,
          goal: true,
        },
      });
    } catch (error) {
      console.error('Error updating trade:', error);
      throw new Error('Failed to update trade');
    }
  },

  async getTradeStatistics(userId: string) {
    try {
      const trades = await prisma.trade.findMany({
        where: {
          userId,
          status: 'CLOSED',
        },
      });

      const totalTrades = trades.length;
      const winningTrades = trades.filter(trade => trade.pnl && trade.pnl > 0).length;
      const totalPnl = trades.reduce((sum, trade) => sum + (trade.pnl || 0), 0);
      const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;

      return {
        totalTrades,
        winningTrades,
        losingTrades: totalTrades - winningTrades,
        winRate,
        totalPnl,
        averagePnl: totalTrades > 0 ? totalPnl / totalTrades : 0,
      };
    } catch (error) {
      console.error('Error calculating trade statistics:', error);
      throw new Error('Failed to calculate trade statistics');
    }
  },
};

// Market data operations
export const marketDataOperations = {
  async getLatestPrice(instrument: string, timeframe: string) {
    try {
      return await prisma.marketData.findFirst({
        where: {
          instrument,
          timeframe: timeframe as string,
        },
        orderBy: { timestamp: 'desc' },
      });
    } catch (error) {
      console.error('Error getting latest price:', error);
      throw new Error('Failed to retrieve market data');
    }
  },

  async getPriceHistory(
    instrument: string,
    timeframe: string,
    fromDate: Date,
    toDate: Date
  ) {
    try {
      return await prisma.marketData.findMany({
        where: {
          instrument,
          timeframe: timeframe as string,
          timestamp: {
            gte: fromDate,
            lte: toDate,
          },
        },
        orderBy: { timestamp: 'asc' },
      });
    } catch (error) {
      console.error('Error getting price history:', error);
      throw new Error('Failed to retrieve price history');
    }
  },

  async insertBatch(marketDataArray: Prisma.MarketDataCreateManyInput[]) {
    try {
      return await prisma.marketData.createMany({
        data: marketDataArray,
        skipDuplicates: true,
      });
    } catch (error) {
      console.error('Error inserting market data batch:', error);
      throw new Error('Failed to insert market data');
    }
  },
};

// Generic database utilities
export const databaseUtils = {
  async healthCheck() {
    try {
      await prisma.$queryRaw`SELECT 1`;
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      console.error('Database health check failed:', error);
      return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() };
    }
  },

  async getTableCounts() {
    try {
      const [
        userCount,
        goalCount,
        strategyCount,
        tradeCount,
        marketDataCount,
      ] = await Promise.all([
        prisma.user.count({ where: { deletedAt: null } }),
        prisma.tradingGoal.count({ where: { deletedAt: null } }),
        prisma.strategy.count({ where: { deletedAt: null } }),
        prisma.trade.count(),
        prisma.marketData.count(),
      ]);

      return {
        users: userCount,
        tradingGoals: goalCount,
        strategies: strategyCount,
        trades: tradeCount,
        marketData: marketDataCount,
      };
    } catch (error) {
      console.error('Error getting table counts:', error);
      throw new Error('Failed to get database statistics');
    }
  },
};