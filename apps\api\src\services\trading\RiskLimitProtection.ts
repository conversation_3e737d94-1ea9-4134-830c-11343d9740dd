/**
 * Risk Limit Protection Service
 * 
 * Implements prevention of user modification of critical safety parameters.
 * Provides administrative override capabilities with audit trail and API-level validation.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';
import { EventEmitter } from 'events';

// Risk Limit Protection Types
export interface ProtectedParameter {
  name: string;
  category: 'risk_management' | 'position_sizing' | 'stop_loss' | 'account_limits' | 'emergency';
  currentValue: any;
  defaultValue: any;
  allowedRange?: { min: any; max: any };
  requiresAdminOverride: boolean;
  protectionLevel: 'basic' | 'enhanced' | 'critical';
  lastModified?: Date;
  modifiedBy?: 'user' | 'admin' | 'system';
  reason?: string;
}

export interface ProtectionConfig {
  userId: string;
  protectedParameters: Map<string, ProtectedParameter>;
  adminOverrideEnabled: boolean;
  auditTrailEnabled: boolean;
  emergencyModeEnabled: boolean;
  lastValidationCheck?: Date;
}

export interface ModificationAttempt {
  userId: string;
  parameterName: string;
  currentValue: any;
  requestedValue: any;
  requestedBy: 'user' | 'admin' | 'system';
  timestamp: Date;
  reason?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface ModificationResult {
  success: boolean;
  parameterName: string;
  finalValue: any;
  reason: string;
  requiresApproval: boolean;
  warningMessage?: string;
  auditLogId?: string;
}

export interface AdminOverride {
  id: string;
  userId: string;
  parameterName: string;
  originalValue: any;
  overrideValue: any;
  authorizedBy: string;
  reason: string;
  timestamp: Date;
  expiresAt?: Date;
  status: 'pending' | 'approved' | 'rejected' | 'expired';
  reviewedBy?: string;
  reviewedAt?: Date;
}

export interface ValidationError {
  parameterName: string;
  errorType: 'out_of_range' | 'invalid_type' | 'protection_violation' | 'dependency_conflict';
  message: string;
  suggestedValue?: any;
}

export interface AuditEntry {
  id: string;
  userId: string;
  action: 'parameter_change' | 'modification_blocked' | 'override_requested' | 'override_approved' | 'validation_failed';
  parameterName: string;
  details: Record<string, any>;
  timestamp: Date;
  sessionId?: string;
  ipAddress?: string;
}

/**
 * RiskLimitProtection - Core service for protecting critical safety parameters
 */
export class RiskLimitProtection extends EventEmitter {
  private readonly protectionConfigs: Map<string, ProtectionConfig> = new Map();
  private readonly auditTrail: AuditEntry[] = [];
  private readonly pendingOverrides: Map<string, AdminOverride> = new Map();
  private auditCounter = 0;

  // Default protected parameters template
  private readonly DEFAULT_PROTECTED_PARAMETERS: Array<Omit<ProtectedParameter, 'currentValue'>> = [
    {
      name: 'daily_loss_limit_percentage',
      category: 'risk_management',
      defaultValue: 2.0,
      allowedRange: { min: 0.5, max: 10.0 },
      requiresAdminOverride: true,
      protectionLevel: 'critical'
    },
    {
      name: 'weekly_loss_limit_percentage',
      category: 'risk_management',
      defaultValue: 5.0,
      allowedRange: { min: 1.0, max: 25.0 },
      requiresAdminOverride: true,
      protectionLevel: 'critical'
    },
    {
      name: 'max_position_size_percentage',
      category: 'position_sizing',
      defaultValue: 10.0,
      allowedRange: { min: 1.0, max: 50.0 },
      requiresAdminOverride: true,
      protectionLevel: 'enhanced'
    },
    {
      name: 'emergency_liquidation_threshold',
      category: 'emergency',
      defaultValue: 10.0,
      allowedRange: { min: 5.0, max: 20.0 },
      requiresAdminOverride: true,
      protectionLevel: 'critical'
    },
    {
      name: 'stop_loss_mandatory',
      category: 'stop_loss',
      defaultValue: true,
      requiresAdminOverride: true,
      protectionLevel: 'critical'
    },
    {
      name: 'minimum_stop_loss_percentage',
      category: 'stop_loss',
      defaultValue: 1.0,
      allowedRange: { min: 0.5, max: 5.0 },
      requiresAdminOverride: true,
      protectionLevel: 'enhanced'
    },
    {
      name: 'correlation_risk_limit',
      category: 'risk_management',
      defaultValue: 0.7,
      allowedRange: { min: 0.3, max: 0.9 },
      requiresAdminOverride: false,
      protectionLevel: 'basic'
    },
    {
      name: 'max_daily_trades',
      category: 'account_limits',
      defaultValue: 50,
      allowedRange: { min: 1, max: 200 },
      requiresAdminOverride: false,
      protectionLevel: 'basic'
    }
  ];

  constructor() {
    super();
    this.setupEventHandlers();
  }

  /**
   * Initialize risk protection for a user
   */
  public initializeUser(userId: string, riskTolerance: 'conservative' | 'moderate' | 'aggressive'): void {
    if (this.protectionConfigs.has(userId)) {
      return; // Already initialized
    }

    const protectedParameters = new Map<string, ProtectedParameter>();
    
    // Initialize with default parameters adjusted for risk tolerance
    this.DEFAULT_PROTECTED_PARAMETERS.forEach(template => {
      const adjustedValue = this.adjustValueForRiskTolerance(template.defaultValue, template.name, riskTolerance);
      
      const parameter: ProtectedParameter = {
        ...template,
        currentValue: adjustedValue,
        lastModified: new Date(),
        modifiedBy: 'system',
        reason: `Initial setup for ${riskTolerance} risk tolerance`
      };

      protectedParameters.set(template.name, parameter);
    });

    const config: ProtectionConfig = {
      userId,
      protectedParameters,
      adminOverrideEnabled: true,
      auditTrailEnabled: true,
      emergencyModeEnabled: false,
      lastValidationCheck: new Date()
    };

    this.protectionConfigs.set(userId, config);

    this.auditAction({
      userId,
      action: 'parameter_change',
      parameterName: 'all_parameters',
      details: {
        action: 'user_initialization',
        riskTolerance,
        parametersCount: protectedParameters.size
      }
    });
  }

  /**
   * Attempt to modify a protected parameter
   */
  public attemptModification(
    userId: string, 
    parameterName: string, 
    newValue: any,
    modificationContext: {
      requestedBy: 'user' | 'admin' | 'system';
      reason?: string;
      sessionId?: string;
      ipAddress?: string;
      userAgent?: string;
    }
  ): ModificationResult {
    const config = this.protectionConfigs.get(userId);
    if (!config) {
      throw new Error(`User ${userId} not initialized for risk protection`);
    }

    const parameter = config.protectedParameters.get(parameterName);
    if (!parameter) {
      throw new Error(`Parameter ${parameterName} not found in protection configuration`);
    }

    const attempt: ModificationAttempt = {
      userId,
      parameterName,
      currentValue: parameter.currentValue,
      requestedValue: newValue,
      requestedBy: modificationContext.requestedBy,
      timestamp: new Date(),
      reason: modificationContext.reason,
      ipAddress: modificationContext.ipAddress,
      userAgent: modificationContext.userAgent
    };

    // Validate the modification
    const validationResult = this.validateModification(parameter, newValue);
    
    // Check if the only validation error is protection violation for a user request on an override-required parameter
    const onlyProtectionViolation = validationResult.errors.length === 1 && 
      validationResult.errors[0].errorType === 'protection_violation' &&
      parameter.requiresAdminOverride &&
      modificationContext.requestedBy === 'user';
    
    if (!validationResult.isValid && !onlyProtectionViolation) {
      const result: ModificationResult = {
        success: false,
        parameterName,
        finalValue: parameter.currentValue,
        reason: `Validation failed: ${validationResult.errors.map(e => e.message).join(', ')}`,
        requiresApproval: false,
        warningMessage: validationResult.errors[0]?.suggestedValue 
          ? `Suggested value: ${validationResult.errors[0].suggestedValue}`
          : undefined
      };

      this.auditAction({
        userId,
        action: 'modification_blocked',
        parameterName,
        details: {
          attempt,
          validationErrors: validationResult.errors,
          result
        },
        sessionId: modificationContext.sessionId,
        ipAddress: modificationContext.ipAddress
      });

      this.emit('modificationBlocked', { attempt, validationResult });
      return result;
    }

    // Check if admin override is required
    if (parameter.requiresAdminOverride && modificationContext.requestedBy === 'user') {
      return this.handleAdminOverrideRequest(userId, parameter, newValue, attempt);
    }

    // Apply the modification
    return this.applyModification(userId, parameter, newValue, modificationContext);
  }

  /**
   * Get protected parameter value
   */
  public getParameterValue(userId: string, parameterName: string): any {
    const config = this.protectionConfigs.get(userId);
    if (!config) {
      throw new Error(`User ${userId} not initialized for risk protection`);
    }

    const parameter = config.protectedParameters.get(parameterName);
    if (!parameter) {
      throw new Error(`Parameter ${parameterName} not found`);
    }

    return parameter.currentValue;
  }

  /**
   * Get all protected parameters for user
   */
  public getAllParameters(userId: string): Record<string, any> {
    const config = this.protectionConfigs.get(userId);
    if (!config) {
      throw new Error(`User ${userId} not initialized for risk protection`);
    }

    const result: Record<string, any> = {};
    config.protectedParameters.forEach((parameter, name) => {
      result[name] = {
        value: parameter.currentValue,
        category: parameter.category,
        protectionLevel: parameter.protectionLevel,
        requiresAdminOverride: parameter.requiresAdminOverride,
        lastModified: parameter.lastModified,
        modifiedBy: parameter.modifiedBy
      };
    });

    return result;
  }

  /**
   * Request administrative override
   */
  public requestAdminOverride(
    userId: string, 
    parameterName: string, 
    newValue: any, 
    reason: string,
    expirationHours: number = 168 // 7 days default
  ): string {
    const overrideId = `override_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const now = new Date();
    const expiresAt = new Date(now.getTime() + (expirationHours * 60 * 60 * 1000));

    const config = this.protectionConfigs.get(userId);
    const parameter = config?.protectedParameters.get(parameterName);

    if (!parameter) {
      throw new Error(`Parameter ${parameterName} not found for user ${userId}`);
    }

    const override: AdminOverride = {
      id: overrideId,
      userId,
      parameterName,
      originalValue: parameter.currentValue,
      overrideValue: newValue,
      authorizedBy: 'pending',
      reason,
      timestamp: now,
      expiresAt,
      status: 'pending'
    };

    this.pendingOverrides.set(overrideId, override);

    this.auditAction({
      userId,
      action: 'override_requested',
      parameterName,
      details: {
        overrideId,
        originalValue: parameter.currentValue,
        requestedValue: newValue,
        reason,
        expiresAt: expiresAt.toISOString()
      }
    });

    // Emit event for admin notification
    this.emit('overrideRequested', override);

    return overrideId;
  }

  /**
   * Approve administrative override
   */
  public approveAdminOverride(overrideId: string, approvedBy: string): ModificationResult {
    const override = this.pendingOverrides.get(overrideId);
    if (!override) {
      throw new Error(`Override request ${overrideId} not found`);
    }

    if (override.status !== 'pending') {
      throw new Error(`Override request ${overrideId} is not pending approval`);
    }

    // Check expiration
    if (override.expiresAt && new Date() > override.expiresAt) {
      override.status = 'expired';
      this.pendingOverrides.set(overrideId, override);
      throw new Error(`Override request ${overrideId} has expired`);
    }

    // Update override status
    override.status = 'approved';
    override.reviewedBy = approvedBy;
    override.reviewedAt = new Date();
    this.pendingOverrides.set(overrideId, override);

    // Apply the override
    const config = this.protectionConfigs.get(override.userId);
    const parameter = config?.protectedParameters.get(override.parameterName);
    
    if (!parameter) {
      throw new Error(`Parameter ${override.parameterName} not found for user ${override.userId}`);
    }
    
    const result = this.applyModification(
      override.userId, 
      parameter,
      override.overrideValue,
      {
        requestedBy: 'admin',
        reason: `Admin override approved by ${approvedBy}: ${override.reason}`,
        sessionId: `override_${overrideId}`
      }
    );

    this.auditAction({
      userId: override.userId,
      action: 'override_approved',
      parameterName: override.parameterName,
      details: {
        overrideId,
        approvedBy,
        result,
        originalRequest: override
      }
    });

    this.emit('overrideApproved', { override, approvedBy, result });

    return result;
  }

  /**
   * Get pending override requests
   */
  public getPendingOverrides(): AdminOverride[] {
    return Array.from(this.pendingOverrides.values())
      .filter(override => override.status === 'pending')
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  /**
   * Enable emergency mode (relaxes certain restrictions)
   */
  public enableEmergencyMode(userId: string, enabledBy: string, reason: string): void {
    const config = this.protectionConfigs.get(userId);
    if (!config) {
      throw new Error(`User ${userId} not initialized for risk protection`);
    }

    config.emergencyModeEnabled = true;

    this.auditAction({
      userId,
      action: 'parameter_change',
      parameterName: 'emergency_mode',
      details: {
        enabled: true,
        enabledBy,
        reason
      }
    });

    this.emit('emergencyModeEnabled', { userId, enabledBy, reason });
  }

  /**
   * Disable emergency mode
   */
  public disableEmergencyMode(userId: string, disabledBy: string): void {
    const config = this.protectionConfigs.get(userId);
    if (!config) {
      throw new Error(`User ${userId} not initialized for risk protection`);
    }

    config.emergencyModeEnabled = false;

    this.auditAction({
      userId,
      action: 'parameter_change',
      parameterName: 'emergency_mode',
      details: {
        enabled: false,
        disabledBy
      }
    });

    this.emit('emergencyModeDisabled', { userId, disabledBy });
  }

  /**
   * Get audit trail
   */
  public getAuditTrail(userId?: string, limit: number = 100): AuditEntry[] {
    let filtered = [...this.auditTrail];
    
    if (userId) {
      filtered = filtered.filter(entry => entry.userId === userId);
    }

    return filtered
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Validate modification attempt
   */
  private validateModification(parameter: ProtectedParameter, newValue: any): { 
    isValid: boolean; 
    errors: ValidationError[] 
  } {
    const errors: ValidationError[] = [];

    // Type validation
    if (typeof newValue !== typeof parameter.defaultValue) {
      errors.push({
        parameterName: parameter.name,
        errorType: 'invalid_type',
        message: `Expected type ${typeof parameter.defaultValue}, got ${typeof newValue}`,
        suggestedValue: parameter.defaultValue
      });
    }

    // Range validation
    if (parameter.allowedRange && typeof newValue === 'number') {
      if (newValue < parameter.allowedRange.min) {
        errors.push({
          parameterName: parameter.name,
          errorType: 'out_of_range',
          message: `Value ${newValue} is below minimum allowed value ${parameter.allowedRange.min}`,
          suggestedValue: parameter.allowedRange.min
        });
      }
      
      if (newValue > parameter.allowedRange.max) {
        errors.push({
          parameterName: parameter.name,
          errorType: 'out_of_range',
          message: `Value ${newValue} is above maximum allowed value ${parameter.allowedRange.max}`,
          suggestedValue: parameter.allowedRange.max
        });
      }
    }

    // Critical protection validation
    if (parameter.protectionLevel === 'critical' && parameter.requiresAdminOverride) {
      const valueChange = this.calculatePercentageChange(parameter.currentValue, newValue);
      if (Math.abs(valueChange) > 50) { // More than 50% change
        errors.push({
          parameterName: parameter.name,
          errorType: 'protection_violation',
          message: `Critical parameter change of ${valueChange.toFixed(1)}% requires admin override`,
          suggestedValue: parameter.currentValue
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Handle admin override request
   */
  private handleAdminOverrideRequest(
    userId: string, 
    parameter: ProtectedParameter, 
    newValue: any, 
    attempt: ModificationAttempt
  ): ModificationResult {
    const overrideId = this.requestAdminOverride(
      userId, 
      parameter.name, 
      newValue, 
      attempt.reason || 'User requested parameter change'
    );

    return {
      success: false,
      parameterName: parameter.name,
      finalValue: parameter.currentValue,
      reason: 'Administrative override required for this parameter change',
      requiresApproval: true,
      warningMessage: `Override request ${overrideId} submitted for admin approval`
    };
  }

  /**
   * Apply modification
   */
  private applyModification(
    userId: string,
    parameter: ProtectedParameter | { currentValue: any },
    newValue: any,
    context: { requestedBy: 'user' | 'admin' | 'system'; reason?: string; sessionId?: string }
  ): ModificationResult {
    const config = this.protectionConfigs.get(userId)!;
    const parameterName = 'name' in parameter ? parameter.name : 'unknown';
    
    if ('name' in parameter) {
      const updatedParameter = {
        ...parameter,
        currentValue: newValue,
        lastModified: new Date(),
        modifiedBy: context.requestedBy,
        reason: context.reason
      };

      config.protectedParameters.set(parameter.name, updatedParameter);
    }

    const auditId = this.auditAction({
      userId,
      action: 'parameter_change',
      parameterName,
      details: {
        oldValue: parameter.currentValue,
        newValue,
        requestedBy: context.requestedBy,
        reason: context.reason
      },
      sessionId: context.sessionId,
      ipAddress: context.ipAddress
    });

    const result: ModificationResult = {
      success: true,
      parameterName,
      finalValue: newValue,
      reason: context.reason || 'Parameter successfully updated',
      requiresApproval: false,
      auditLogId: auditId
    };

    this.emit('parameterModified', { userId, parameterName, oldValue: parameter.currentValue, newValue, context });

    return result;
  }

  /**
   * Adjust value for risk tolerance
   */
  private adjustValueForRiskTolerance(defaultValue: any, parameterName: string, riskTolerance: string): any {
    if (typeof defaultValue !== 'number') {
      return defaultValue;
    }

    const adjustmentFactors = {
      conservative: 0.7,  // 30% more restrictive
      moderate: 1.0,      // Default
      aggressive: 1.5     // 50% more permissive
    };

    const factor = adjustmentFactors[riskTolerance as keyof typeof adjustmentFactors] || 1.0;

    // Special handling for percentage-based limits
    if (parameterName.includes('percentage') || parameterName.includes('limit')) {
      return defaultValue * factor;
    }

    return defaultValue;
  }

  /**
   * Calculate percentage change between values
   */
  private calculatePercentageChange(oldValue: any, newValue: any): number {
    if (typeof oldValue !== 'number' || typeof newValue !== 'number') {
      return 0;
    }

    if (oldValue === 0) {
      return newValue === 0 ? 0 : 100;
    }

    return ((newValue - oldValue) / oldValue) * 100;
  }

  /**
   * Create audit entry
   */
  private auditAction(entry: Omit<AuditEntry, 'id' | 'timestamp'>): string {
    const auditId = `audit_${++this.auditCounter}_${Date.now()}`;
    
    const auditEntry: AuditEntry = {
      ...entry,
      id: auditId,
      timestamp: new Date()
    };

    this.auditTrail.push(auditEntry);

    // Keep only last 50,000 entries to prevent memory issues
    if (this.auditTrail.length > 50000) {
      this.auditTrail.splice(0, this.auditTrail.length - 50000);
    }

    this.emit('auditEntryCreated', auditEntry);

    return auditId;
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.on('modificationBlocked', (event) => {
      console.warn(`Risk parameter modification blocked for user ${event.attempt.userId}:`, {
        parameter: event.attempt.parameterName,
        requestedValue: event.attempt.requestedValue,
        errors: event.validationResult.errors.map(e => e.message)
      });
    });

    this.on('overrideRequested', (override: AdminOverride) => {
      console.info(`Admin override requested for user ${override.userId}:`, {
        parameter: override.parameterName,
        reason: override.reason,
        overrideId: override.id
      });
    });

    this.on('parameterModified', (event) => {
      console.info(`Risk parameter modified for user ${event.userId}:`, {
        parameter: event.parameterName,
        oldValue: event.oldValue,
        newValue: event.newValue,
        modifiedBy: event.context.requestedBy
      });
    });
  }

  /**
   * Validate configuration
   */
  public static validateConfiguration(parameters: ProtectedParameter[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    parameters.forEach(param => {
      if (!param.name || param.name.trim() === '') {
        errors.push('Parameter name cannot be empty');
      }

      if (!['risk_management', 'position_sizing', 'stop_loss', 'account_limits', 'emergency'].includes(param.category)) {
        errors.push(`Invalid category for parameter ${param.name}`);
      }

      if (!['basic', 'enhanced', 'critical'].includes(param.protectionLevel)) {
        errors.push(`Invalid protection level for parameter ${param.name}`);
      }

      if (param.allowedRange && typeof param.defaultValue === 'number') {
        if (param.defaultValue < param.allowedRange.min || param.defaultValue > param.allowedRange.max) {
          errors.push(`Default value for ${param.name} is outside allowed range`);
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

/**
 * Factory function to create RiskLimitProtection instance
 */
export function createRiskLimitProtection(): RiskLimitProtection {
  return new RiskLimitProtection();
}

/**
 * Default export for convenience
 */
export default RiskLimitProtection;