import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';
import { redisCacheService } from '@golddaddy/config/src/redis-cache';
import { getDatabaseMetrics } from '@golddaddy/config/src/database';

/**
 * Performance Monitoring Service for production deployment
 * Monitors API response times, system metrics, and performance KPIs
 */
export class PerformanceMonitoringService extends EventEmitter {
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private responseTimeMetrics: Map<string, ResponseTimeMetrics> = new Map();
  private systemMetrics: SystemMetrics[] = [];
  private performanceThresholds: PerformanceThresholds;
  private readonly METRICS_RETENTION_HOURS = 24;

  constructor(thresholds?: Partial<PerformanceThresholds>) {
    super();
    this.performanceThresholds = {
      responseTimeWarning: 200, // 200ms
      responseTimeCritical: 500, // 500ms
      throughputWarning: 50, // 50 req/sec
      throughputCritical: 20, // 20 req/sec
      errorRateWarning: 1, // 1%
      errorRateCritical: 5, // 5%
      memoryWarning: 0.8, // 80%
      memoryCritical: 0.95, // 95%
      cpuWarning: 0.7, // 70%
      cpuCritical: 0.9, // 90%
      ...thresholds,
    };
  }

  /**
   * Start performance monitoring
   */
  async startMonitoring(config: PerformanceMonitoringConfig = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('Performance monitoring already active');
      return;
    }

    console.log('📊 Starting performance monitoring...');
    this.isMonitoring = true;

    // Start periodic system metrics collection
    this.monitoringInterval = setInterval(async () => {
      await this.collectSystemMetrics();
    }, config.intervalMs || 30000); // Default 30 seconds

    // Set up response time monitoring middleware
    this.setupResponseTimeMonitoring();

    console.log('✅ Performance monitoring started');
    this.emit('monitoringStarted', { config });
  }

  /**
   * Stop performance monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) return;

    console.log('⏹️ Stopping performance monitoring...');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('✅ Performance monitoring stopped');
    this.emit('monitoringStopped');
  }

  /**
   * Set up response time monitoring
   */
  private setupResponseTimeMonitoring(): void {
    // This would typically be integrated with Express middleware
    // For now, we'll set up a method to track response times
  }

  /**
   * Track API response time
   */
  trackResponseTime(
    endpoint: string,
    method: string,
    statusCode: number,
    responseTime: number,
    userId?: string
  ): void {
    const key = `${method}:${endpoint}`;
    let metrics = this.responseTimeMetrics.get(key);

    if (!metrics) {
      metrics = {
        endpoint,
        method,
        totalRequests: 0,
        successfulRequests: 0,
        totalResponseTime: 0,
        averageResponseTime: 0,
        minResponseTime: Infinity,
        maxResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        errorCount: 0,
        statusCodes: new Map(),
        recentResponseTimes: [],
        hourlyStats: new Map(),
        userRequests: new Map(),
        lastRequest: new Date(),
      };
      this.responseTimeMetrics.set(key, metrics);
    }

    // Update metrics
    metrics.totalRequests++;
    metrics.totalResponseTime += responseTime;
    metrics.averageResponseTime = metrics.totalResponseTime / metrics.totalRequests;
    metrics.minResponseTime = Math.min(metrics.minResponseTime, responseTime);
    metrics.maxResponseTime = Math.max(metrics.maxResponseTime, responseTime);
    metrics.lastRequest = new Date();

    // Track status codes
    const statusKey = statusCode.toString();
    metrics.statusCodes.set(statusKey, (metrics.statusCodes.get(statusKey) || 0) + 1);

    if (statusCode >= 200 && statusCode < 300) {
      metrics.successfulRequests++;
    } else {
      metrics.errorCount++;
    }

    // Track user-specific metrics
    if (userId) {
      const userStats = metrics.userRequests.get(userId) || { requests: 0, totalResponseTime: 0 };
      userStats.requests++;
      userStats.totalResponseTime += responseTime;
      metrics.userRequests.set(userId, userStats);
    }

    // Update recent response times (for percentile calculations)
    metrics.recentResponseTimes.push({
      time: responseTime,
      timestamp: new Date(),
      statusCode,
    });

    // Keep only recent responses (last 1000 requests)
    if (metrics.recentResponseTimes.length > 1000) {
      metrics.recentResponseTimes = metrics.recentResponseTimes.slice(-1000);
    }

    // Calculate percentiles
    this.calculatePercentiles(metrics);

    // Update hourly statistics
    this.updateHourlyStats(metrics, responseTime, statusCode);

    // Check for performance alerts
    this.checkPerformanceAlerts(endpoint, method, responseTime, statusCode);

    // Emit event for real-time monitoring
    this.emit('responseTime', {
      endpoint,
      method,
      statusCode,
      responseTime,
      userId,
      timestamp: new Date(),
    });
  }

  /**
   * Calculate response time percentiles
   */
  private calculatePercentiles(metrics: ResponseTimeMetrics): void {
    const times = metrics.recentResponseTimes.map(r => r.time).sort((a, b) => a - b);
    const length = times.length;

    if (length === 0) return;

    const p95Index = Math.floor(length * 0.95);
    const p99Index = Math.floor(length * 0.99);

    metrics.p95ResponseTime = times[p95Index] || 0;
    metrics.p99ResponseTime = times[p99Index] || 0;
  }

  /**
   * Update hourly statistics
   */
  private updateHourlyStats(metrics: ResponseTimeMetrics, responseTime: number, statusCode: number): void {
    const hourKey = new Date().toISOString().slice(0, 13); // YYYY-MM-DDTHH
    let hourlyStats = metrics.hourlyStats.get(hourKey);

    if (!hourlyStats) {
      hourlyStats = {
        hour: hourKey,
        requests: 0,
        successfulRequests: 0,
        errors: 0,
        totalResponseTime: 0,
        averageResponseTime: 0,
        minResponseTime: Infinity,
        maxResponseTime: 0,
      };
      metrics.hourlyStats.set(hourKey, hourlyStats);
    }

    hourlyStats.requests++;
    hourlyStats.totalResponseTime += responseTime;
    hourlyStats.averageResponseTime = hourlyStats.totalResponseTime / hourlyStats.requests;
    hourlyStats.minResponseTime = Math.min(hourlyStats.minResponseTime, responseTime);
    hourlyStats.maxResponseTime = Math.max(hourlyStats.maxResponseTime, responseTime);

    if (statusCode >= 200 && statusCode < 300) {
      hourlyStats.successfulRequests++;
    } else {
      hourlyStats.errors++;
    }

    // Clean up old hourly stats (keep only last 24 hours)
    const cutoffTime = new Date();
    cutoffTime.setHours(cutoffTime.getHours() - this.METRICS_RETENTION_HOURS);
    const cutoffKey = cutoffTime.toISOString().slice(0, 13);

    for (const [hour] of metrics.hourlyStats) {
      if (hour < cutoffKey) {
        metrics.hourlyStats.delete(hour);
      }
    }
  }

  /**
   * Collect system metrics
   */
  private async collectSystemMetrics(): Promise<void> {
    try {
      const startTime = performance.now();

      // Collect various system metrics
      const [memoryUsage, cpuUsage, databaseMetrics, cacheMetrics] = await Promise.all([
        this.getMemoryUsage(),
        this.getCpuUsage(),
        this.getDatabasePerformance(),
        this.getCachePerformance(),
      ]);

      const collectionDuration = performance.now() - startTime;

      const systemMetric: SystemMetrics = {
        timestamp: new Date(),
        memory: memoryUsage,
        cpu: cpuUsage,
        database: databaseMetrics,
        cache: cacheMetrics,
        responseTimeStats: this.calculateOverallResponseStats(),
        throughput: this.calculateThroughput(),
        errorRate: this.calculateErrorRate(),
        collectionDuration,
        nodeVersion: process.version,
        uptime: process.uptime(),
      };

      this.systemMetrics.push(systemMetric);

      // Keep only recent system metrics
      const maxMetrics = Math.floor((this.METRICS_RETENTION_HOURS * 3600) / 30); // 30-second intervals
      if (this.systemMetrics.length > maxMetrics) {
        this.systemMetrics = this.systemMetrics.slice(-maxMetrics);
      }

      // Cache system metrics for API access
      await redisCacheService.setSystemMetrics(systemMetric, 300); // 5 minute TTL

      // Check system-level alerts
      await this.checkSystemAlerts(systemMetric);

      // Emit system metrics event
      this.emit('systemMetrics', systemMetric);

    } catch (error) {
      console.error('Failed to collect system metrics:', error);
      this.emit('error', { type: 'systemMetricsCollection', error });
    }
  }

  /**
   * Get memory usage statistics
   */
  private getMemoryUsage(): MemoryUsage {
    const usage = process.memoryUsage();
    const totalMemory = require('os').totalmem();
    const freeMemory = require('os').freemem();

    return {
      heapUsed: usage.heapUsed,
      heapTotal: usage.heapTotal,
      external: usage.external,
      rss: usage.rss,
      systemTotal: totalMemory,
      systemFree: freeMemory,
      systemUsed: totalMemory - freeMemory,
      systemUsagePercent: ((totalMemory - freeMemory) / totalMemory) * 100,
      heapUsagePercent: (usage.heapUsed / usage.heapTotal) * 100,
    };
  }

  /**
   * Get CPU usage statistics
   */
  private getCpuUsage(): CpuUsage {
    const cpus = require('os').cpus();
    const loadAverage = require('os').loadavg();

    let totalIdle = 0;
    let totalTick = 0;

    for (const cpu of cpus) {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    }

    return {
      cores: cpus.length,
      loadAverage,
      usage: 100 - (totalIdle / totalTick * 100),
      model: cpus[0]?.model || 'Unknown',
      speed: cpus[0]?.speed || 0,
    };
  }

  /**
   * Get database performance metrics
   */
  private async getDatabasePerformance(): Promise<DatabasePerformanceMetrics> {
    try {
      const metrics = await getDatabaseMetrics();
      return {
        healthy: metrics.healthy,
        connectionPoolSize: metrics.config?.poolSize || 0,
        activeConnections: metrics.connectionPool?.poolInfo?.size || 0,
        queryMetrics: metrics.connectionPool?.queryMetrics || {},
      };
    } catch (error) {
      return {
        healthy: false,
        connectionPoolSize: 0,
        activeConnections: 0,
        queryMetrics: {},
        error: error.message,
      };
    }
  }

  /**
   * Get cache performance metrics
   */
  private async getCachePerformance(): Promise<CachePerformanceMetrics> {
    try {
      const metrics = await redisCacheService.getMetrics();
      const healthCheck = await redisCacheService.healthCheck();

      return {
        healthy: healthCheck.healthy,
        hitRate: metrics.hitRate,
        totalKeys: metrics.totalKeys,
        memoryUsage: metrics.memoryUsage,
        connections: metrics.connections,
        latency: healthCheck.latency || 0,
      };
    } catch (error) {
      return {
        healthy: false,
        hitRate: 0,
        totalKeys: 0,
        memoryUsage: 0,
        connections: 0,
        latency: -1,
        error: error.message,
      };
    }
  }

  /**
   * Calculate overall response time statistics
   */
  private calculateOverallResponseStats(): ResponseTimeStats {
    const allMetrics = Array.from(this.responseTimeMetrics.values());
    
    if (allMetrics.length === 0) {
      return {
        averageResponseTime: 0,
        p95ResponseTime: 0,
        p99ResponseTime: 0,
        totalRequests: 0,
        successfulRequests: 0,
        errorCount: 0,
      };
    }

    const totalRequests = allMetrics.reduce((sum, m) => sum + m.totalRequests, 0);
    const totalResponseTime = allMetrics.reduce((sum, m) => sum + m.totalResponseTime, 0);
    const successfulRequests = allMetrics.reduce((sum, m) => sum + m.successfulRequests, 0);
    const errorCount = allMetrics.reduce((sum, m) => sum + m.errorCount, 0);

    // Collect all recent response times for percentile calculation
    const allResponseTimes: number[] = [];
    allMetrics.forEach(m => {
      allResponseTimes.push(...m.recentResponseTimes.map(r => r.time));
    });
    allResponseTimes.sort((a, b) => a - b);

    const p95Index = Math.floor(allResponseTimes.length * 0.95);
    const p99Index = Math.floor(allResponseTimes.length * 0.99);

    return {
      averageResponseTime: totalRequests > 0 ? totalResponseTime / totalRequests : 0,
      p95ResponseTime: allResponseTimes[p95Index] || 0,
      p99ResponseTime: allResponseTimes[p99Index] || 0,
      totalRequests,
      successfulRequests,
      errorCount,
    };
  }

  /**
   * Calculate throughput (requests per second)
   */
  private calculateThroughput(): ThroughputMetrics {
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60000);
    const fiveMinutesAgo = new Date(now.getTime() - 300000);

    let requestsLastMinute = 0;
    let requestsLast5Minutes = 0;

    for (const metrics of this.responseTimeMetrics.values()) {
      for (const response of metrics.recentResponseTimes) {
        if (response.timestamp >= oneMinuteAgo) {
          requestsLastMinute++;
        }
        if (response.timestamp >= fiveMinutesAgo) {
          requestsLast5Minutes++;
        }
      }
    }

    return {
      requestsPerSecond1m: requestsLastMinute / 60,
      requestsPerSecond5m: requestsLast5Minutes / 300,
      totalRequestsLastMinute: requestsLastMinute,
      totalRequestsLast5Minutes: requestsLast5Minutes,
    };
  }

  /**
   * Calculate error rate
   */
  private calculateErrorRate(): ErrorRateMetrics {
    const allMetrics = Array.from(this.responseTimeMetrics.values());
    const totalRequests = allMetrics.reduce((sum, m) => sum + m.totalRequests, 0);
    const totalErrors = allMetrics.reduce((sum, m) => sum + m.errorCount, 0);

    // Calculate error rate for the last hour
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 3600000);
    
    let recentRequests = 0;
    let recentErrors = 0;

    for (const metrics of this.responseTimeMetrics.values()) {
      for (const response of metrics.recentResponseTimes) {
        if (response.timestamp >= oneHourAgo) {
          recentRequests++;
          if (response.statusCode >= 400) {
            recentErrors++;
          }
        }
      }
    }

    return {
      overallErrorRate: totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0,
      lastHourErrorRate: recentRequests > 0 ? (recentErrors / recentRequests) * 100 : 0,
      totalErrors,
      recentErrors,
    };
  }

  /**
   * Check for performance alerts
   */
  private checkPerformanceAlerts(
    endpoint: string,
    method: string,
    responseTime: number,
    statusCode: number
  ): void {
    const alerts: PerformanceAlert[] = [];

    // Response time alerts
    if (responseTime > this.performanceThresholds.responseTimeCritical) {
      alerts.push({
        type: 'response_time_critical',
        severity: 'critical',
        message: `Critical response time: ${endpoint} took ${responseTime.toFixed(0)}ms`,
        endpoint,
        method,
        value: responseTime,
        threshold: this.performanceThresholds.responseTimeCritical,
        timestamp: new Date(),
      });
    } else if (responseTime > this.performanceThresholds.responseTimeWarning) {
      alerts.push({
        type: 'response_time_warning',
        severity: 'warning',
        message: `Slow response time: ${endpoint} took ${responseTime.toFixed(0)}ms`,
        endpoint,
        method,
        value: responseTime,
        threshold: this.performanceThresholds.responseTimeWarning,
        timestamp: new Date(),
      });
    }

    // Emit alerts
    alerts.forEach(alert => this.emit('performanceAlert', alert));
  }

  /**
   * Check system-level alerts
   */
  private async checkSystemAlerts(systemMetric: SystemMetrics): Promise<void> {
    const alerts: SystemAlert[] = [];

    // Memory alerts
    if (systemMetric.memory.systemUsagePercent > this.performanceThresholds.memoryCritical * 100) {
      alerts.push({
        type: 'memory_critical',
        severity: 'critical',
        message: `Critical memory usage: ${systemMetric.memory.systemUsagePercent.toFixed(1)}%`,
        value: systemMetric.memory.systemUsagePercent,
        threshold: this.performanceThresholds.memoryCritical * 100,
        timestamp: new Date(),
      });
    } else if (systemMetric.memory.systemUsagePercent > this.performanceThresholds.memoryWarning * 100) {
      alerts.push({
        type: 'memory_warning',
        severity: 'warning',
        message: `High memory usage: ${systemMetric.memory.systemUsagePercent.toFixed(1)}%`,
        value: systemMetric.memory.systemUsagePercent,
        threshold: this.performanceThresholds.memoryWarning * 100,
        timestamp: new Date(),
      });
    }

    // CPU alerts
    if (systemMetric.cpu.usage > this.performanceThresholds.cpuCritical * 100) {
      alerts.push({
        type: 'cpu_critical',
        severity: 'critical',
        message: `Critical CPU usage: ${systemMetric.cpu.usage.toFixed(1)}%`,
        value: systemMetric.cpu.usage,
        threshold: this.performanceThresholds.cpuCritical * 100,
        timestamp: new Date(),
      });
    }

    // Error rate alerts
    if (systemMetric.errorRate.lastHourErrorRate > this.performanceThresholds.errorRateCritical) {
      alerts.push({
        type: 'error_rate_critical',
        severity: 'critical',
        message: `Critical error rate: ${systemMetric.errorRate.lastHourErrorRate.toFixed(1)}%`,
        value: systemMetric.errorRate.lastHourErrorRate,
        threshold: this.performanceThresholds.errorRateCritical,
        timestamp: new Date(),
      });
    }

    // Cache performance alerts
    if (!systemMetric.cache.healthy) {
      alerts.push({
        type: 'cache_unhealthy',
        severity: 'critical',
        message: 'Cache service is unhealthy',
        timestamp: new Date(),
      });
    }

    // Database performance alerts
    if (!systemMetric.database.healthy) {
      alerts.push({
        type: 'database_unhealthy',
        severity: 'critical',
        message: 'Database service is unhealthy',
        timestamp: new Date(),
      });
    }

    // Emit system alerts
    alerts.forEach(alert => this.emit('systemAlert', alert));
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): PerformanceSummary {
    const endpointMetrics = Array.from(this.responseTimeMetrics.values());
    const recentSystemMetrics = this.systemMetrics.slice(-10);
    const latestSystemMetric = recentSystemMetrics[recentSystemMetrics.length - 1];

    return {
      overview: {
        totalEndpoints: endpointMetrics.length,
        totalRequests: endpointMetrics.reduce((sum, m) => sum + m.totalRequests, 0),
        overallResponseTime: this.calculateOverallResponseStats().averageResponseTime,
        overallErrorRate: this.calculateErrorRate().overallErrorRate,
        isMonitoring: this.isMonitoring,
      },
      topEndpoints: endpointMetrics
        .sort((a, b) => b.totalRequests - a.totalRequests)
        .slice(0, 10)
        .map(m => ({
          endpoint: m.endpoint,
          method: m.method,
          requests: m.totalRequests,
          avgResponseTime: m.averageResponseTime,
          errorRate: m.totalRequests > 0 ? (m.errorCount / m.totalRequests) * 100 : 0,
        })),
      systemHealth: latestSystemMetric ? {
        memoryUsage: latestSystemMetric.memory.systemUsagePercent,
        cpuUsage: latestSystemMetric.cpu.usage,
        databaseHealthy: latestSystemMetric.database.healthy,
        cacheHealthy: latestSystemMetric.cache.healthy,
        uptime: latestSystemMetric.uptime,
      } : null,
      trends: {
        responseTimeTrend: this.calculateResponseTimeTrend(recentSystemMetrics),
        throughputTrend: this.calculateThroughputTrend(),
        errorRateTrend: this.calculateErrorRateTrend(),
      },
      lastCheck: latestSystemMetric?.timestamp || new Date(),
    };
  }

  /**
   * Calculate response time trend
   */
  private calculateResponseTimeTrend(metrics: SystemMetrics[]): 'improving' | 'stable' | 'degrading' {
    if (metrics.length < 5) return 'stable';
    
    const recent = metrics.slice(-3);
    const earlier = metrics.slice(-6, -3);
    
    const recentAvg = recent.reduce((sum, m) => sum + m.responseTimeStats.averageResponseTime, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, m) => sum + m.responseTimeStats.averageResponseTime, 0) / earlier.length;
    
    if (recentAvg < earlierAvg * 0.9) return 'improving';
    if (recentAvg > earlierAvg * 1.1) return 'degrading';
    return 'stable';
  }

  /**
   * Calculate throughput trend
   */
  private calculateThroughputTrend(): 'improving' | 'stable' | 'degrading' {
    const recentMetrics = this.systemMetrics.slice(-10);
    if (recentMetrics.length < 5) return 'stable';
    
    const recent = recentMetrics.slice(-3);
    const earlier = recentMetrics.slice(-6, -3);
    
    const recentAvg = recent.reduce((sum, m) => sum + m.throughput.requestsPerSecond1m, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, m) => sum + m.throughput.requestsPerSecond1m, 0) / earlier.length;
    
    if (recentAvg > earlierAvg * 1.1) return 'improving';
    if (recentAvg < earlierAvg * 0.9) return 'degrading';
    return 'stable';
  }

  /**
   * Calculate error rate trend
   */
  private calculateErrorRateTrend(): 'improving' | 'stable' | 'degrading' {
    const recentMetrics = this.systemMetrics.slice(-10);
    if (recentMetrics.length < 5) return 'stable';
    
    const recent = recentMetrics.slice(-3);
    const earlier = recentMetrics.slice(-6, -3);
    
    const recentAvg = recent.reduce((sum, m) => sum + m.errorRate.lastHourErrorRate, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, m) => sum + m.errorRate.lastHourErrorRate, 0) / earlier.length;
    
    if (recentAvg < earlierAvg * 0.9) return 'improving';
    if (recentAvg > earlierAvg * 1.1) return 'degrading';
    return 'stable';
  }

  /**
   * Get endpoint metrics
   */
  getEndpointMetrics(endpoint?: string): ResponseTimeMetrics[] {
    if (endpoint) {
      const metrics = Array.from(this.responseTimeMetrics.values()).filter(m => 
        m.endpoint.includes(endpoint)
      );
      return metrics;
    }
    return Array.from(this.responseTimeMetrics.values());
  }

  /**
   * Clear metrics (useful for testing)
   */
  clearMetrics(): void {
    this.responseTimeMetrics.clear();
    this.systemMetrics = [];
    console.log('🧹 Performance metrics cleared');
  }
}

// Type definitions
interface PerformanceMonitoringConfig {
  intervalMs?: number;
}

interface PerformanceThresholds {
  responseTimeWarning: number;
  responseTimeCritical: number;
  throughputWarning: number;
  throughputCritical: number;
  errorRateWarning: number;
  errorRateCritical: number;
  memoryWarning: number;
  memoryCritical: number;
  cpuWarning: number;
  cpuCritical: number;
}

interface ResponseTimeMetrics {
  endpoint: string;
  method: string;
  totalRequests: number;
  successfulRequests: number;
  totalResponseTime: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  errorCount: number;
  statusCodes: Map<string, number>;
  recentResponseTimes: Array<{
    time: number;
    timestamp: Date;
    statusCode: number;
  }>;
  hourlyStats: Map<string, HourlyStats>;
  userRequests: Map<string, { requests: number; totalResponseTime: number }>;
  lastRequest: Date;
}

interface HourlyStats {
  hour: string;
  requests: number;
  successfulRequests: number;
  errors: number;
  totalResponseTime: number;
  averageResponseTime: number;
  minResponseTime: number;
  maxResponseTime: number;
}

interface SystemMetrics {
  timestamp: Date;
  memory: MemoryUsage;
  cpu: CpuUsage;
  database: DatabasePerformanceMetrics;
  cache: CachePerformanceMetrics;
  responseTimeStats: ResponseTimeStats;
  throughput: ThroughputMetrics;
  errorRate: ErrorRateMetrics;
  collectionDuration: number;
  nodeVersion: string;
  uptime: number;
}

interface MemoryUsage {
  heapUsed: number;
  heapTotal: number;
  external: number;
  rss: number;
  systemTotal: number;
  systemFree: number;
  systemUsed: number;
  systemUsagePercent: number;
  heapUsagePercent: number;
}

interface CpuUsage {
  cores: number;
  loadAverage: number[];
  usage: number;
  model: string;
  speed: number;
}

interface DatabasePerformanceMetrics {
  healthy: boolean;
  connectionPoolSize: number;
  activeConnections: number;
  queryMetrics: any;
  error?: string;
}

interface CachePerformanceMetrics {
  healthy: boolean;
  hitRate: number;
  totalKeys: number;
  memoryUsage: number;
  connections: number;
  latency: number;
  error?: string;
}

interface ResponseTimeStats {
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  totalRequests: number;
  successfulRequests: number;
  errorCount: number;
}

interface ThroughputMetrics {
  requestsPerSecond1m: number;
  requestsPerSecond5m: number;
  totalRequestsLastMinute: number;
  totalRequestsLast5Minutes: number;
}

interface ErrorRateMetrics {
  overallErrorRate: number;
  lastHourErrorRate: number;
  totalErrors: number;
  recentErrors: number;
}

interface PerformanceAlert {
  type: string;
  severity: 'warning' | 'critical';
  message: string;
  endpoint: string;
  method: string;
  value: number;
  threshold: number;
  timestamp: Date;
}

interface SystemAlert {
  type: string;
  severity: 'warning' | 'critical';
  message: string;
  value?: number;
  threshold?: number;
  timestamp: Date;
}

interface PerformanceSummary {
  overview: {
    totalEndpoints: number;
    totalRequests: number;
    overallResponseTime: number;
    overallErrorRate: number;
    isMonitoring: boolean;
  };
  topEndpoints: Array<{
    endpoint: string;
    method: string;
    requests: number;
    avgResponseTime: number;
    errorRate: number;
  }>;
  systemHealth: {
    memoryUsage: number;
    cpuUsage: number;
    databaseHealthy: boolean;
    cacheHealthy: boolean;
    uptime: number;
  } | null;
  trends: {
    responseTimeTrend: 'improving' | 'stable' | 'degrading';
    throughputTrend: 'improving' | 'stable' | 'degrading';
    errorRateTrend: 'improving' | 'stable' | 'degrading';
  };
  lastCheck: Date;
}

export {
  PerformanceMonitoringService,
  PerformanceMonitoringConfig,
  PerformanceThresholds,
  ResponseTimeMetrics,
  SystemMetrics,
  PerformanceAlert,
  SystemAlert,
  PerformanceSummary,
};