import { PrismaClient } from '@prisma/client';
import {
  QuizQuestion,
  QuizCategory,
  QuizDifficulty,
  ConfidenceStage
} from '@golddaddy/types';

interface QuizContentFilter {
  category?: QuizCategory;
  difficulty?: QuizDifficulty;
  stage?: ConfidenceStage;
  topics?: string[];
  limit?: number;
}

interface QuizProgressionConfig {
  stageQuestionCounts: Record<ConfidenceStage, number>;
  difficultyProgression: Record<ConfidenceStage, QuizDifficulty[]>;
  categoryWeighting: Record<QuizCategory, number>;
  minimumTopicCoverage: number;
}

export class QuizContentService {
  private prisma: PrismaClient;
  private progressionConfig: QuizProgressionConfig;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.progressionConfig = {
      stageQuestionCounts: {
        goal_setting: 15,
        strategy_learning: 20,
        backtesting_review: 25,
        paper_trading: 30,
        live_ready: 35
      },
      difficultyProgression: {
        goal_setting: [QuizDifficulty.BEGINNER],
        strategy_learning: [QuizDifficulty.BEGINNER, QuizDifficulty.INTERMEDIATE],
        backtesting_review: [QuizDifficulty.INTERMEDIATE],
        paper_trading: [QuizDifficulty.INTERMEDIATE, QuizDifficulty.ADVANCED],
        live_ready: [QuizDifficulty.ADVANCED, QuizDifficulty.EXPERT]
      },
      categoryWeighting: {
        trading_fundamentals: 0.25,
        risk_management: 0.30,
        platform_features: 0.15,
        safety_procedures: 0.20,
        regulatory_compliance: 0.05,
        market_analysis: 0.03,
        psychology_discipline: 0.02
      },
      minimumTopicCoverage: 0.8
    };
  }

  async getQuestionsByFilter(filter: QuizContentFilter): Promise<QuizQuestion[]> {
    const where: any = { isActive: true };

    if (filter.category) {
      where.category = filter.category;
    }

    if (filter.difficulty) {
      where.difficulty = filter.difficulty;
    }

    if (filter.topics && filter.topics.length > 0) {
      where.topic = { in: filter.topics };
    }

    const questions = await this.prisma.quizQuestion.findMany({
      where,
      take: filter.limit || 50,
      orderBy: [
        { category: 'asc' },
        { difficulty: 'asc' },
        { topic: 'asc' }
      ]
    });

    return questions.map(this.mapPrismaToQuizQuestion);
  }

  async generateQuizForStage(
    stage: ConfidenceStage,
    userExperience: 'beginner' | 'intermediate' | 'advanced'
  ): Promise<QuizQuestion[]> {
    const allowedDifficulties = this.progressionConfig.difficultyProgression[stage];
    const questionCount = this.progressionConfig.stageQuestionCounts[stage];

    // Calculate questions per category based on weighting
    const questionsPerCategory = this.calculateCategoryDistribution(questionCount);

    const selectedQuestions: QuizQuestion[] = [];

    for (const [category, count] of Object.entries(questionsPerCategory)) {
      if (count === 0) continue;

      const categoryQuestions = await this.getQuestionsByFilter({
        category: category as QuizCategory,
        difficulty: allowedDifficulties[0], // Start with appropriate difficulty
        limit: count * 2 // Get extra to allow for randomization
      });

      // Apply user experience adjustment
      const adjustedQuestions = this.adjustQuestionsForExperience(
        categoryQuestions,
        userExperience,
        allowedDifficulties
      );

      // Randomly select the required number
      const shuffled = this.shuffleArray(adjustedQuestions);
      selectedQuestions.push(...shuffled.slice(0, count));
    }

    return this.shuffleArray(selectedQuestions);
  }

  async getQuestionsByTopic(
    topic: string,
    difficulty?: QuizDifficulty,
    limit: number = 10
  ): Promise<QuizQuestion[]> {
    return this.getQuestionsByFilter({
      topics: [topic],
      difficulty,
      limit
    });
  }

  async validateQuizContent(questions: QuizQuestion[]): Promise<{
    isValid: boolean;
    issues: string[];
    recommendations: string[];
  }> {
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check category coverage
    const categoryCount = this.getCategoryCoverage(questions);
    const requiredCategories = Object.keys(this.progressionConfig.categoryWeighting);
    
    for (const category of requiredCategories) {
      const coverage = categoryCount[category] || 0;
      const expected = Math.ceil(questions.length * this.progressionConfig.categoryWeighting[category as QuizCategory]);
      
      if (coverage < expected * this.progressionConfig.minimumTopicCoverage) {
        issues.push(`Insufficient coverage for ${category}: ${coverage}/${expected} questions`);
        recommendations.push(`Add more ${category} questions to meet minimum coverage`);
      }
    }

    // Check difficulty progression
    const difficultyCount = this.getDifficultyDistribution(questions);
    if (difficultyCount.expert > difficultyCount.advanced) {
      issues.push('Too many expert questions relative to advanced questions');
      recommendations.push('Ensure progressive difficulty scaling');
    }

    // Validate question quality
    for (const question of questions) {
      if (question.options.length < 2) {
        issues.push(`Question ${question.id} has insufficient options`);
      }
      
      if (question.correctAnswerIds.length === 0) {
        issues.push(`Question ${question.id} has no correct answers`);
      }

      if (question.explanation.length < 50) {
        recommendations.push(`Question ${question.id} explanation could be more detailed`);
      }
    }

    return {
      isValid: issues.length === 0,
      issues,
      recommendations
    };
  }

  async createQuizQuestion(questionData: Partial<QuizQuestion>): Promise<QuizQuestion> {
    // Validate individual question structure (not coverage requirements)
    this.validateSingleQuestion(questionData as QuizQuestion);

    const created = await this.prisma.quizQuestion.create({
      data: {
        category: questionData.category!,
        difficulty: questionData.difficulty!,
        topic: questionData.topic!,
        question: questionData.question!,
        options: questionData.options,
        correctAnswerIds: questionData.correctAnswerIds!,
        explanation: questionData.explanation!,
        learningResources: questionData.learningResources,
        metadata: questionData.metadata,
        isActive: true
      }
    });

    return this.mapPrismaToQuizQuestion(created);
  }

  async updateQuizQuestion(id: string, updates: Partial<QuizQuestion>): Promise<QuizQuestion> {
    const updated = await this.prisma.quizQuestion.update({
      where: { id },
      data: {
        ...updates,
        updatedAt: new Date()
      }
    });

    return this.mapPrismaToQuizQuestion(updated);
  }

  async getQuizComplexityProgression(
    userStage: ConfidenceStage,
    userPerformance: { averageScore: number; attemptCount: number }
  ): Promise<{
    recommendedDifficulty: QuizDifficulty;
    questionCount: number;
    focusAreas: QuizCategory[];
  }> {
    const allowedDifficulties = this.progressionConfig.difficultyProgression[userStage];
    let recommendedDifficulty = allowedDifficulties[0];

    // Adjust difficulty based on performance
    if (userPerformance.averageScore > 85 && userPerformance.attemptCount > 1) {
      const maxIndex = allowedDifficulties.length - 1;
      const currentIndex = allowedDifficulties.indexOf(recommendedDifficulty);
      recommendedDifficulty = allowedDifficulties[Math.min(currentIndex + 1, maxIndex)];
    } else if (userPerformance.averageScore < 60) {
      recommendedDifficulty = QuizDifficulty.BEGINNER;
    }

    // Determine focus areas based on stage
    const focusAreas = this.getStageFocusAreas(userStage);

    return {
      recommendedDifficulty,
      questionCount: this.progressionConfig.stageQuestionCounts[userStage],
      focusAreas
    };
  }

  private validateSingleQuestion(question: QuizQuestion): void {
    const errors: string[] = [];

    if (!question.question || question.question.trim().length === 0) {
      errors.push('Question text is required');
    }

    if (!question.options || question.options.length < 2) {
      errors.push('At least 2 options are required');
    }

    if (!question.correctAnswerIds || question.correctAnswerIds.length === 0) {
      errors.push('At least one correct answer must be specified');
    }

    if (!question.explanation || question.explanation.length < 20) {
      errors.push('Explanation must be at least 20 characters long');
    }

    if (errors.length > 0) {
      throw new Error(`Invalid question: ${errors.join(', ')}`);
    }
  }

  private calculateCategoryDistribution(totalQuestions: number): Record<string, number> {
    const distribution: Record<string, number> = {};

    for (const [category, weight] of Object.entries(this.progressionConfig.categoryWeighting)) {
      distribution[category] = Math.ceil(totalQuestions * weight);
    }

    // Adjust for exact total if needed
    const currentTotal = Object.values(distribution).reduce((sum, count) => sum + count, 0);
    if (currentTotal !== totalQuestions) {
      const diff = totalQuestions - currentTotal;
      // Add/remove from the largest category
      const largestCategory = Object.entries(distribution)
        .sort((a, b) => b[1] - a[1])[0][0];
      distribution[largestCategory] += diff;
    }

    return distribution;
  }

  private adjustQuestionsForExperience(
    questions: QuizQuestion[],
    userExperience: string,
    allowedDifficulties: QuizDifficulty[]
  ): QuizQuestion[] {
    if (userExperience === 'advanced' && allowedDifficulties.includes(QuizDifficulty.ADVANCED)) {
      return questions.filter(q => 
        q.difficulty === QuizDifficulty.ADVANCED || q.difficulty === QuizDifficulty.EXPERT
      );
    }

    if (userExperience === 'beginner') {
      return questions.filter(q => 
        q.difficulty === QuizDifficulty.BEGINNER || q.difficulty === QuizDifficulty.INTERMEDIATE
      );
    }

    return questions;
  }

  private getCategoryCoverage(questions: QuizQuestion[]): Record<string, number> {
    const coverage: Record<string, number> = {};
    for (const question of questions) {
      coverage[question.category] = (coverage[question.category] || 0) + 1;
    }
    return coverage;
  }

  private getDifficultyDistribution(questions: QuizQuestion[]): Record<string, number> {
    const distribution: Record<string, number> = {};
    for (const question of questions) {
      distribution[question.difficulty] = (distribution[question.difficulty] || 0) + 1;
    }
    return distribution;
  }

  private getStageFocusAreas(stage: ConfidenceStage): QuizCategory[] {
    const focusMap: Record<ConfidenceStage, QuizCategory[]> = {
      goal_setting: [
        QuizCategory.TRADING_FUNDAMENTALS,
        QuizCategory.RISK_MANAGEMENT
      ],
      strategy_learning: [
        QuizCategory.TRADING_FUNDAMENTALS,
        QuizCategory.MARKET_ANALYSIS,
        QuizCategory.PLATFORM_FEATURES
      ],
      backtesting_review: [
        QuizCategory.MARKET_ANALYSIS,
        QuizCategory.RISK_MANAGEMENT,
        QuizCategory.PSYCHOLOGY_DISCIPLINE
      ],
      paper_trading: [
        QuizCategory.RISK_MANAGEMENT,
        QuizCategory.PSYCHOLOGY_DISCIPLINE,
        QuizCategory.SAFETY_PROCEDURES
      ],
      live_ready: [
        QuizCategory.RISK_MANAGEMENT,
        QuizCategory.SAFETY_PROCEDURES,
        QuizCategory.REGULATORY_COMPLIANCE
      ]
    };

    return focusMap[stage];
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  private mapPrismaToQuizQuestion(prismaQuestion: any): QuizQuestion {
    return {
      id: prismaQuestion.id,
      category: prismaQuestion.category,
      difficulty: prismaQuestion.difficulty,
      topic: prismaQuestion.topic,
      question: prismaQuestion.question,
      options: prismaQuestion.options,
      correctAnswerIds: prismaQuestion.correctAnswerIds,
      explanation: prismaQuestion.explanation,
      learningResources: prismaQuestion.learningResources || [],
      metadata: prismaQuestion.metadata,
      createdAt: prismaQuestion.createdAt,
      updatedAt: prismaQuestion.updatedAt
    };
  }
}