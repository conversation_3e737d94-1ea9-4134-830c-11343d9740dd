import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QuizFeedbackService } from '../QuizFeedbackService.js';
import {
  QuizCategory,
  QuizDifficulty,
  type QuizQuestion,
  type QuizResponse
} from '@golddaddy/types';

// Mock PrismaClient with extended methods
const mockPrisma = {
  quizSession: {
    create: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn()
  },
  quizResponse: {
    create: vi.fn()
  },
  quizAttempt: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findMany: vi.fn()
  },
  confidenceAssessment: {
    create: vi.fn(),
    findFirst: vi.fn(),
    update: vi.fn()
  },
  quizQuestion: {
    findUnique: vi.fn(),
    findMany: vi.fn()
  }
};

// Mock parent class dependencies
vi.mock('../QuizContentService.js', () => ({
  QuizContentService: vi.fn().mockImplementation(() => ({
    generateQuizForStage: vi.fn().mockResolvedValue([])
  }))
}));

vi.mock('../QuizComplexityEngine.js', () => ({
  QuizComplexityEngine: vi.fn().mockImplementation(() => ({
    calculateComplexityRecommendation: vi.fn(),
    createUserPerformanceProfile: vi.fn()
  }))
}));

describe('QuizFeedbackService', () => {
  let service: QuizFeedbackService;

  const mockQuestion: QuizQuestion = {
    id: 'q1',
    category: QuizCategory.TRADING_FUNDAMENTALS,
    difficulty: QuizDifficulty.BEGINNER,
    topic: 'Position Sizing',
    question: 'What percentage of capital should you risk per trade?',
    options: [
      { id: 'opt1', text: '1-2%', isCorrect: true },
      { id: 'opt2', text: '5-10%', isCorrect: false },
      { id: 'opt3', text: '15-20%', isCorrect: false },
      { id: 'opt4', text: 'As much as possible', isCorrect: false }
    ],
    correctAnswerIds: ['opt1'],
    explanation: 'Professional traders typically risk 1-2% of their capital per trade to preserve their account.',
    learningResources: [],
    metadata: {
      estimatedDuration: 30,
      tags: ['risk-management', 'position-sizing'],
      riskLevel: 'low'
    },
    createdAt: new Date(),
    updatedAt: new Date()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    service = new QuizFeedbackService(mockPrisma as any);
  });

  describe('generateImmediateFeedback', () => {
    it('should generate comprehensive feedback for correct answer', async () => {
      const correctResponse: QuizResponse = {
        id: 'resp1',
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt1'],
        isCorrect: true,
        timeSpent: 30,
        confidenceLevel: 4,
        submittedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const feedback = await service.generateImmediateFeedback(
        correctResponse,
        mockQuestion,
        'beginner'
      );

      expect(feedback.isCorrect).toBe(true);
      expect(feedback.explanation).toContain('Professional traders');
      expect(feedback.correctAnswers).toHaveLength(1);
      expect(feedback.correctAnswers[0].text).toBe('1-2%');
      expect(feedback.learningPoints).toHaveLength(2);
      expect(feedback.relatedConcepts).toContain('Risk Management');
      expect(feedback.additionalResources).toHaveLength(2);
      expect(feedback.confidenceGuidance.message).toContain('confidence matches');
      expect(feedback.confidenceGuidance.recommendedAction).toBe('continue');
    });

    it('should generate helpful feedback for incorrect answer', async () => {
      const incorrectResponse: QuizResponse = {
        id: 'resp2',
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt2'],
        isCorrect: false,
        timeSpent: 45,
        confidenceLevel: 3,
        submittedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const feedback = await service.generateImmediateFeedback(
        incorrectResponse,
        mockQuestion,
        'beginner'
      );

      expect(feedback.isCorrect).toBe(false);
      expect(feedback.userAnswers).toHaveLength(1);
      expect(feedback.userAnswers[0].text).toBe('5-10%');
      expect(feedback.learningPoints).toHaveLength(2);
      expect(feedback.additionalResources[0].difficulty).toBe(QuizDifficulty.BEGINNER);
      expect(feedback.confidenceGuidance.recommendedAction).toBe('continue');
    });

    it('should provide overconfident feedback for wrong high-confidence answer', async () => {
      const overconfidentResponse: QuizResponse = {
        id: 'resp3',
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt3'],
        isCorrect: false,
        timeSpent: 20,
        confidenceLevel: 5,
        submittedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const feedback = await service.generateImmediateFeedback(
        overconfidentResponse,
        mockQuestion,
        'intermediate'
      );

      expect(feedback.isCorrect).toBe(false);
      expect(feedback.confidenceGuidance.recommendedAction).toBe('review');
      expect(feedback.confidenceGuidance.confidenceCalibration).toContain('Overconfident');
      expect(feedback.confidenceGuidance.message).toContain('review this concept');
    });

    it('should adapt explanation for different user experience levels', async () => {
      const response: QuizResponse = {
        id: 'resp4',
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt1'],
        isCorrect: true,
        timeSpent: 30,
        confidenceLevel: 4,
        submittedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const beginnerFeedback = await service.generateImmediateFeedback(
        response,
        mockQuestion,
        'beginner'
      );

      const advancedFeedback = await service.generateImmediateFeedback(
        response,
        mockQuestion,
        'advanced'
      );

      // Beginner feedback should be more detailed and encouraging
      expect(beginnerFeedback.nextQuestionHint).toBeDefined();
      expect(beginnerFeedback.nextQuestionHint).toContain('Great job!');

      // Advanced feedback should be more concise
      expect(advancedFeedback.nextQuestionHint).toBeUndefined();
    });
  });

  describe('generateEducationalContent', () => {
    it('should create comprehensive educational content for a topic', async () => {
      const content = await service.generateEducationalContent(
        QuizCategory.TRADING_FUNDAMENTALS,
        'Position Sizing',
        QuizDifficulty.BEGINNER
      );

      expect(content.category).toBe(QuizCategory.TRADING_FUNDAMENTALS);
      expect(content.topic).toBe('Position Sizing');
      expect(content.difficulty).toBe(QuizDifficulty.BEGINNER);
      expect(content.conceptExplanation).toContain('Position sizing');
      expect(content.realWorldApplication).toContain('Professional traders');
      expect(content.keyTakeaways).toHaveLength(3);
      expect(content.prerequisites).toContain('Basic market knowledge');
      expect(content.relatedTopics).toContain('Market Analysis');
      expect(content.practiceExercises).toHaveLength(2);
    });

    it('should provide different content for different categories', async () => {
      const fundamentalsContent = await service.generateEducationalContent(
        QuizCategory.TRADING_FUNDAMENTALS,
        'Position Sizing',
        QuizDifficulty.BEGINNER
      );

      const riskContent = await service.generateEducationalContent(
        QuizCategory.RISK_MANAGEMENT,
        'Stop Loss Orders',
        QuizDifficulty.BEGINNER
      );

      expect(fundamentalsContent.relatedTopics).not.toEqual(riskContent.relatedTopics);
      expect(fundamentalsContent.prerequisites).not.toEqual(riskContent.prerequisites);
    });
  });

  describe('generateWeakAreaRecommendations', () => {
    it('should create targeted recommendations for weak areas', async () => {
      const userPerformance = {
        averageScore: 65,
        attemptCount: 3,
        consistencyRating: 0.6
      };

      const recommendations = await service.generateWeakAreaRecommendations(
        'user1',
        [QuizCategory.TRADING_FUNDAMENTALS, QuizCategory.RISK_MANAGEMENT],
        userPerformance
      );

      expect(recommendations).toHaveLength(2);
      
      const tradingRec = recommendations.find(r => r.category === QuizCategory.TRADING_FUNDAMENTALS);
      expect(tradingRec).toBeDefined();
      expect(tradingRec!.specificTopics).toContain('Position Sizing');
      expect(tradingRec!.recommendedResources).toHaveLength(2);
      expect(tradingRec!.studyPlan).toHaveLength(2);
      expect(tradingRec!.progressMetrics.currentLevel).toBeGreaterThan(0);
      expect(tradingRec!.progressMetrics.targetLevel).toBe(85);
    });

    it('should create intensive study plan for low-performing users', async () => {
      const lowPerformance = {
        averageScore: 45,
        attemptCount: 2,
        consistencyRating: 0.4
      };

      const recommendations = await service.generateWeakAreaRecommendations(
        'user1',
        [QuizCategory.RISK_MANAGEMENT],
        lowPerformance
      );

      const riskRec = recommendations[0];
      expect(riskRec.studyPlan[0].duration).toBe(7); // Intensive = 7 days for foundation
      expect(riskRec.studyPlan[1].duration).toBe(10); // Intensive = 10 days for practice
      expect(riskRec.progressMetrics.estimatedTimeToTarget).toBeGreaterThan(5);
    });

    it('should create moderate study plan for average-performing users', async () => {
      const moderatePerformance = {
        averageScore: 75,
        attemptCount: 4,
        consistencyRating: 0.7
      };

      const recommendations = await service.generateWeakAreaRecommendations(
        'user1',
        [QuizCategory.PLATFORM_FEATURES],
        moderatePerformance
      );

      const platformRec = recommendations[0];
      expect(platformRec.studyPlan[0].duration).toBe(3); // Moderate = 3 days for foundation
      expect(platformRec.studyPlan[1].duration).toBe(5); // Moderate = 5 days for practice
      expect(platformRec.progressMetrics.estimatedTimeToTarget).toBeLessThanOrEqual(3);
    });
  });

  describe('getInteractiveExplanation', () => {
    beforeEach(() => {
      mockPrisma.quizQuestion.findUnique.mockResolvedValue({
        id: 'q1',
        category: QuizCategory.TRADING_FUNDAMENTALS,
        topic: 'Position Sizing',
        explanation: 'Professional traders risk 1-2% per trade'
      });
    });

    it('should provide reasoning-focused clarification for "why" questions', async () => {
      const clarification = await service.getInteractiveExplanation(
        'q1',
        'user1',
        'Why is 1-2% the recommended amount?'
      );

      expect(clarification.clarification).toContain('reasoning');
      expect(clarification.followUpQuestions).toContain('What are the consequences of choosing differently?');
      expect(clarification.followUpQuestions).toContain('How does this apply in real trading?');
    });

    it('should provide examples for "example" requests', async () => {
      const clarification = await service.getInteractiveExplanation(
        'q1',
        'user1',
        'Can you give me a real world example?'
      );

      expect(clarification.clarification).toContain('real trading situations');
      expect(clarification.additionalExamples).toHaveLength(2);
      expect(clarification.additionalExamples[0]).toContain('Example 1');
    });

    it('should provide calculation help for math-related questions', async () => {
      const clarification = await service.getInteractiveExplanation(
        'q1',
        'user1',
        'How do I calculate the position size?'
      );

      expect(clarification.clarification).toContain('mathematical calculation');
      expect(clarification.suggestedActions).toContain('Practice similar calculations');
      expect(clarification.suggestedActions).toContain('Review mathematical concepts');
    });

    it('should provide default explanation for general questions', async () => {
      const clarification = await service.getInteractiveExplanation(
        'q1',
        'user1',
        'I need more help with this'
      );

      expect(clarification.clarification).toBe('Professional traders risk 1-2% per trade');
      expect(clarification.followUpQuestions).toContain('Would you like a real-world example?');
      expect(clarification.followUpQuestions).toContain('Do you need clarification on any specific part?');
    });

    it('should handle invalid question IDs', async () => {
      mockPrisma.quizQuestion.findUnique.mockResolvedValue(null);

      await expect(service.getInteractiveExplanation(
        'invalid-id',
        'user1',
        'Why is this correct?'
      )).rejects.toThrow('Question not found');
    });
  });

  describe('personalized feedback generation', () => {
    it('should include analogies for beginner users', async () => {
      const positionSizingQuestion = {
        ...mockQuestion,
        topic: 'Position Sizing'
      };

      const response: QuizResponse = {
        id: 'resp5',
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt1'],
        isCorrect: true,
        timeSpent: 30,
        confidenceLevel: 3,
        submittedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const feedback = await service.generateImmediateFeedback(
        response,
        positionSizingQuestion,
        'beginner'
      );

      // For position sizing questions, beginners should get analogies
      expect(feedback.explanation).toBeDefined();
    });

    it('should emphasize connections for intermediate users', async () => {
      const response: QuizResponse = {
        id: 'resp6',
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt1'],
        isCorrect: true,
        timeSpent: 25,
        confidenceLevel: 4,
        submittedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const feedback = await service.generateImmediateFeedback(
        response,
        mockQuestion,
        'intermediate'
      );

      expect(feedback.explanation).toContain('connects to other aspects');
    });

    it('should add nuances for advanced users', async () => {
      const response: QuizResponse = {
        id: 'resp7',
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt1'],
        isCorrect: true,
        timeSpent: 20,
        confidenceLevel: 5,
        submittedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const feedback = await service.generateImmediateFeedback(
        response,
        mockQuestion,
        'advanced'
      );

      expect(feedback.explanation).toContain('nuances in real-world application');
    });
  });

  describe('educational content library', () => {
    it('should provide comprehensive content for trading fundamentals', async () => {
      const content = await service.generateEducationalContent(
        QuizCategory.TRADING_FUNDAMENTALS,
        'Position Sizing',
        QuizDifficulty.INTERMEDIATE
      );

      expect(content.conceptExplanation).toContain('Position sizing');
      expect(content.realWorldApplication).toContain('$10,000 account');
      expect(content.keyTakeaways).toContain('Never risk more than you can afford to lose');
    });

    it('should provide content for risk management topics', async () => {
      const content = await service.generateEducationalContent(
        QuizCategory.RISK_MANAGEMENT,
        'Stop Loss Orders',
        QuizDifficulty.BEGINNER
      );

      expect(content.conceptExplanation).toContain('Stop losses');
      expect(content.realWorldApplication).toContain('$100 with a 5% stop loss');
      expect(content.keyTakeaways.some(takeaway => takeaway.includes('Stop losses'))).toBe(true);
    });

    it('should handle unknown topics gracefully', async () => {
      const content = await service.generateEducationalContent(
        QuizCategory.MARKET_ANALYSIS,
        'Unknown Topic',
        QuizDifficulty.BEGINNER
      );

      expect(content.conceptExplanation).toContain('Unknown Topic');
      expect(content.keyTakeaways).toHaveLength(1);
    });
  });
});