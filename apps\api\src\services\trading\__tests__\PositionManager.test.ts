/**
 * Position Manager Tests
 * 
 * Comprehensive unit tests for position management, synchronization,
 * and risk monitoring with financial precision requirements.
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PrismaClient } from '@prisma/client';
import Decimal from 'decimal.js';
import { PositionManager, type PositionDiscrepancy, type BrokerPositionData } from '../PositionManager.js';
import { BrokerFailoverEngine } from '../BrokerFailoverEngine.js';
import { TradeStatusTracker } from '../TradeStatusTracker.js';
import type { Position, PositionStatus, PositionRiskMetrics, SynchronizationStatus } from '@golddaddy/types';

// Mock Prisma Client
const mockPrisma = {
  position: {
    create: vi.fn(),
    update: vi.fn(),
    findMany: vi.fn(),
    findUnique: vi.fn()
  }
} as unknown as PrismaClient;

// Mock BrokerFailoverEngine
const mockBrokerFailover = {
  on: vi.fn()
} as unknown as BrokerFailoverEngine;

// Mock TradeStatusTracker
const mockStatusTracker = {
  trackPositionUpdate: vi.fn()
} as unknown as TradeStatusTracker;

describe('PositionManager', () => {
  let positionManager: PositionManager;
  let mockPosition: Omit<Position, 'id' | 'createdAt' | 'updatedAt'>;

  beforeEach(async () => {
    vi.clearAllMocks();
    
    // Initialize position manager with test configuration
    positionManager = new PositionManager(
      mockPrisma,
      mockBrokerFailover,
      mockStatusTracker,
      {
        syncIntervalMs: 1000, // Fast interval for testing
        reconciliationIntervalMs: 2000,
        maxSyncRetries: 2,
        positionUpdateBatchSize: 10,
        realTimePnlEnabled: true,
        riskMonitoringEnabled: true,
        autoReconciliationEnabled: false, // Disable for controlled testing
        syncTimeoutMs: 5000
      }
    );

    // Mock position data
    mockPosition = {
      userId: 'user_123',
      accountId: 'account_123',
      strategyId: 'strategy_123',
      goalId: 'goal_123',
      instrument: 'EURUSD',
      side: 'LONG',
      size: new Decimal('10000'), // 0.1 lot
      averageEntryPrice: new Decimal('1.0800'),
      currentPrice: new Decimal('1.0850'),
      unrealizedPnl: new Decimal('50.00'),
      realizedPnl: new Decimal('0.00'),
      totalPnl: new Decimal('50.00'),
      pnlPercentage: new Decimal('0.46'),
      stopLoss: new Decimal('1.0750'),
      takeProfit: new Decimal('1.0900'),
      status: 'OPEN' as PositionStatus,
      riskMetrics: {
        exposure: new Decimal('10800'),
        riskPercentage: new Decimal('10.8'),
        maxDrawdown: new Decimal('0.0'),
        currentDrawdown: new Decimal('0.0'),
        volatility: new Decimal('0.46'),
        beta: new Decimal('1.0')
      } as PositionRiskMetrics,
      synchronizationStatus: {
        isSynchronized: true,
        lastSyncTime: new Date(),
        pendingSyncOperations: 0,
        syncErrors: []
      } as SynchronizationStatus
    };

    // Setup default mocks
    setupDefaultMocks();

    await positionManager.initialize();
  });

  afterEach(async () => {
    await positionManager.shutdown();
    vi.restoreAllMocks();
  });

  describe('Position Creation', () => {
    it('should create position with correct financial precision', async () => {
      const createdPosition = await positionManager.createPosition(mockPosition);

      expect(createdPosition).toBeDefined();
      expect(createdPosition.id).toBeDefined();
      expect(createdPosition.size.toFixed()).toBe('10000'); // Precise decimal handling
      expect(createdPosition.averageEntryPrice.toFixed(4)).toBe('1.0800');
      expect(createdPosition.unrealizedPnl.toFixed(2)).toBe('50.00');
      expect(createdPosition.pnlPercentage.toFixed(2)).toBe('0.46');

      // Verify database call
      expect(mockPrisma.position.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          size: mockPosition.size,
          averageEntryPrice: mockPosition.averageEntryPrice,
          unrealizedPnl: mockPosition.unrealizedPnl
        })
      });

      // Verify status tracker notification
      expect(mockStatusTracker.trackPositionUpdate).toHaveBeenCalledWith(
        expect.objectContaining({
          positionId: createdPosition.id,
          instrument: 'EURUSD',
          size: mockPosition.size
        }),
        'user_123'
      );
    });

    it('should emit positionCreated event', async () => {
      const eventSpy = vi.spyOn(positionManager, 'emit');

      await positionManager.createPosition(mockPosition);

      expect(eventSpy).toHaveBeenCalledWith('positionCreated', expect.objectContaining({
        type: 'position_opened',
        position: expect.any(Object)
      }));
    });

    it('should handle position creation errors', async () => {
      mockPrisma.position.create.mockRejectedValue(new Error('Database connection failed'));

      await expect(positionManager.createPosition(mockPosition))
        .rejects.toThrow('Database connection failed');
    });
  });

  describe('Position Updates', () => {
    let createdPosition: Position;
    
    beforeEach(async () => {
      // Create a position first and store its actual ID
      createdPosition = await positionManager.createPosition(mockPosition);
    });

    it('should update position with broker data and maintain precision', async () => {
      const brokerData: Partial<BrokerPositionData> = {
        currentPrice: new Decimal('1.0875'), // 25 pips gain
        unrealizedPnl: new Decimal('75.00'), // Updated P&L
        timestamp: new Date()
      };

      const updatedPosition = await positionManager.updatePosition(createdPosition.id, brokerData);

      expect(updatedPosition).toBeDefined();
      expect(updatedPosition?.currentPrice.toFixed()).toBe('1.0875');
      expect(updatedPosition?.unrealizedPnl.toFixed()).toBe('75.00'); // Calculated based on new price
      expect(updatedPosition?.totalPnl.toFixed()).toBe('75.00');
    });

    it('should calculate P&L correctly for LONG positions', async () => {
      const brokerData: Partial<BrokerPositionData> = {
        currentPrice: new Decimal('1.0900'), // 100 pips gain
        timestamp: new Date()
      };

      const updatedPosition = await positionManager.updatePosition(createdPosition.id, brokerData);

      // For LONG: P&L = (currentPrice - entryPrice) * size
      // (1.0900 - 1.0800) * 10000 = 100.00
      expect(updatedPosition?.unrealizedPnl.toFixed()).toBe('100.00');
      expect(updatedPosition?.pnlPercentage.toFixed(2)).toBe('0.93'); // 100/10800 * 100
    });

    it('should calculate P&L correctly for SHORT positions', async () => {
      // Create SHORT position
      const shortPosition = { 
        ...mockPosition, 
        side: 'SHORT' as const,
        currentPrice: new Decimal('1.0800'), // Entry at same price
        unrealizedPnl: new Decimal('0.00'),
        totalPnl: new Decimal('0.00')
      };

      const createdShortPosition = await positionManager.createPosition(shortPosition);

      const brokerData: Partial<BrokerPositionData> = {
        currentPrice: new Decimal('1.0750'), // 50 pips gain for short
        timestamp: new Date()
      };

      mockPrisma.position.update.mockResolvedValue({});

      const updatedPosition = await positionManager.updatePosition(createdShortPosition.id, brokerData);

      // For SHORT: P&L = (entryPrice - currentPrice) * size
      // (1.0800 - 1.0750) * 10000 = 50.00
      expect(updatedPosition?.unrealizedPnl.toFixed()).toBe('50.00');
    });

    it('should update risk metrics during position update', async () => {
      const brokerData: Partial<BrokerPositionData> = {
        currentPrice: new Decimal('1.0700'), // 100 pips loss
        timestamp: new Date()
      };

      const updatedPosition = await positionManager.updatePosition(createdPosition.id, brokerData);

      expect(updatedPosition?.riskMetrics).toBeDefined();
      expect(updatedPosition?.riskMetrics.currentDrawdown.toNumber()).toBeGreaterThan(0);
      expect(updatedPosition?.riskMetrics.exposure.toFixed()).toBe('10800.00');
    });

    it('should emit risk alerts for significant drawdown', async () => {
      const brokerData: Partial<BrokerPositionData> = {
        currentPrice: new Decimal('1.0200'), // Massive loss to trigger alert
        timestamp: new Date()
      };

      const riskAlertSpy = vi.spyOn(positionManager, 'emit');

      await positionManager.updatePosition(createdPosition.id, brokerData);

      expect(riskAlertSpy).toHaveBeenCalledWith('riskAlert', expect.objectContaining({
        type: expect.stringContaining('loss'),
        positionId: createdPosition.id
      }));
    });

    it('should handle update failures gracefully', async () => {
      mockPrisma.position.update.mockRejectedValue(new Error('Update failed'));

      const updatedPosition = await positionManager.updatePosition(createdPosition.id, { currentPrice: new Decimal('1.0850') });

      expect(updatedPosition).toBeNull();
    });
  });

  describe('Position Closing', () => {
    let createdPosition: Position;
    
    beforeEach(async () => {
      createdPosition = await positionManager.createPosition(mockPosition);
    });

    it('should close position with accurate final P&L calculation', async () => {
      const closePrice = new Decimal('1.0900'); // 100 pips profit
      mockPrisma.position.update.mockResolvedValue({});

      const closedPosition = await positionManager.closePosition(createdPosition.id, closePrice, 'Take profit hit');

      expect(closedPosition).toBeDefined();
      expect(closedPosition?.status).toBe('CLOSED');
      expect(closedPosition?.currentPrice.toFixed(4)).toBe('1.0900');
      expect(closedPosition?.unrealizedPnl.toFixed(2)).toBe('0.00'); // Should be zero when closed
      expect(closedPosition?.realizedPnl.toFixed(2)).toBe('100.00'); // Final profit
      expect(closedPosition?.totalPnl.toFixed(2)).toBe('100.00');
    });

    it('should calculate correct P&L for SHORT position close', async () => {
      // Create and close SHORT position
      const shortPosition = { 
        ...mockPosition, 
        side: 'SHORT' as const 
      };

      const createdShortPosition = await positionManager.createPosition(shortPosition);

      const closePrice = new Decimal('1.0750'); // 50 pips profit for short
      mockPrisma.position.update.mockResolvedValue({});

      const closedPosition = await positionManager.closePosition(createdShortPosition.id, closePrice, 'Take profit');

      // For SHORT: P&L = (entryPrice - closePrice) * size = (1.0800 - 1.0750) * 10000 = 50.00
      expect(closedPosition?.realizedPnl.toFixed(2)).toBe('50.00');
    });

    it('should emit positionClosed event', async () => {
      mockPrisma.position.update.mockResolvedValue({});
      const eventSpy = vi.spyOn(positionManager, 'emit');

      await positionManager.closePosition(createdPosition.id, new Decimal('1.0900'), 'Manual close');

      expect(eventSpy).toHaveBeenCalledWith('positionClosed', expect.objectContaining({
        type: 'position_closed'
      }));
    });
  });

  describe('Position Synchronization', () => {
    let createdPositions: Position[];

    beforeEach(async () => {
      createdPositions = [];
      // Create multiple test positions
      for (let i = 0; i < 3; i++) {
        mockPrisma.position.create.mockResolvedValue({ 
          id: `pos_${i}`, 
          ...mockPosition, 
          createdAt: new Date(), 
          updatedAt: new Date() 
        });

        const position = await positionManager.createPosition({ ...mockPosition, instrument: `TEST${i}` });
        createdPositions.push(position);
      }
    });

    it('should synchronize all positions and return correct counts', async () => {
      // Mock broker data fetching
      vi.spyOn(positionManager as any, 'fetchBrokerPositionData').mockResolvedValue({
        brokerId: 'account_123',
        brokerPositionId: 'broker_pos_123',
        instrument: 'EURUSD',
        side: 'LONG',
        size: new Decimal('10000'),
        averagePrice: new Decimal('1.0800'),
        currentPrice: new Decimal('1.0850'),
        unrealizedPnl: new Decimal('50.00'),
        realizedPnl: new Decimal('0.00'),
        margin: new Decimal('108.00'),
        timestamp: new Date()
      });

      vi.spyOn(positionManager as any, 'detectDiscrepancies').mockResolvedValue([]);
      mockPrisma.position.update.mockResolvedValue({});

      const results = await positionManager.synchronizeAllPositions();

      expect(results.synchronized).toBeGreaterThan(0);
      expect(results.failed).toBe(0);
      expect(results.conflicts).toBe(0);
    });

    it('should detect and handle position discrepancies', async () => {
      const brokerData: BrokerPositionData = {
        brokerId: 'account_123',
        brokerPositionId: 'broker_pos_123',
        instrument: 'EURUSD',
        side: 'LONG',
        size: new Decimal('15000'), // Different size - should trigger discrepancy
        averagePrice: new Decimal('1.0800'),
        currentPrice: new Decimal('1.0850'),
        unrealizedPnl: new Decimal('50.00'), // Match local P&L to avoid second discrepancy
        realizedPnl: new Decimal('0.00'),
        margin: new Decimal('162.00'),
        timestamp: new Date()
      };

      vi.spyOn(positionManager as any, 'fetchBrokerPositionData').mockResolvedValue(brokerData);

      const discrepancies = await positionManager['detectDiscrepancies'](
        positionManager.getPosition(createdPositions[0].id)!,
        brokerData
      );

      expect(discrepancies).toHaveLength(1);
      expect(discrepancies[0].field).toBe('size');
      expect(discrepancies[0].severity).toBe('high');
      expect(discrepancies[0].localValue).toBe('10000');
      expect(discrepancies[0].brokerValue).toBe('15000');
    });

    it('should handle synchronization failures gracefully', async () => {
      vi.spyOn(positionManager as any, 'fetchBrokerPositionData').mockRejectedValue(new Error('Broker API failed'));

      const results = await positionManager.synchronizeAllPositions();

      expect(results.failed).toBeGreaterThan(0);
    });
  });

  describe('Position Reconciliation', () => {
    let createdPosition: Position;

    beforeEach(async () => {
      mockPrisma.position.create.mockResolvedValue({ 
        id: 'pos_123', 
        ...mockPosition, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      });

      createdPosition = await positionManager.createPosition(mockPosition);

      // Create a reconciliation record
      const discrepancies: PositionDiscrepancy[] = [{
        field: 'size',
        localValue: '10000',
        brokerValue: '15000',
        severity: 'high',
        impact: 'Size mismatch affects P&L'
      }];

      positionManager['reconciliations'].set(createdPosition.id, {
        positionId: createdPosition.id,
        discrepancies,
        resolutionStrategy: 'use_broker_data'
      });
    });

    it('should reconcile using broker data strategy', async () => {
      vi.spyOn(positionManager as any, 'resolveToBrokerData').mockResolvedValue(true);

      const result = await positionManager.reconcilePositionConflicts(createdPosition.id, 'use_broker_data');

      expect(result).toBe(true);
      expect(positionManager['reconciliations'].has(createdPosition.id)).toBe(false); // Should be removed after resolution
    });

    it('should reconcile using local data strategy', async () => {
      vi.spyOn(positionManager as any, 'resolveToLocalData').mockResolvedValue(true);

      const result = await positionManager.reconcilePositionConflicts(createdPosition.id, 'use_local_data');

      expect(result).toBe(true);
    });

    it('should handle manual review strategy', async () => {
      const result = await positionManager.reconcilePositionConflicts(createdPosition.id, 'manual_review');

      expect(result).toBe(false); // Manual review doesn't auto-resolve
      expect(positionManager['reconciliations'].get(createdPosition.id)?.resolutionStrategy).toBe('manual_review');
    });

    it('should handle reconciliation for non-existent position', async () => {
      const result = await positionManager.reconcilePositionConflicts('nonexistent', 'use_broker_data');

      expect(result).toBe(false);
    });
  });

  describe('Risk Monitoring', () => {
    let createdPosition: Position;

    beforeEach(async () => {
      mockPrisma.position.create.mockResolvedValue({ 
        id: 'pos_123', 
        ...mockPosition, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      });

      createdPosition = await positionManager.createPosition(mockPosition);
    });

    it('should calculate risk metrics accurately', async () => {
      const position = positionManager.getPosition(createdPosition.id)!;
      const currentPrice = new Decimal('1.0750'); // 50 pips loss
      const unrealizedPnl = new Decimal('-50.00');

      const riskMetrics = await positionManager['calculateRiskMetrics'](position, currentPrice, unrealizedPnl);

      expect(riskMetrics.exposure.toFixed(2)).toBe('10800.00'); // 1.0800 * 10000
      expect(riskMetrics.riskPercentage.toNumber()).toBe(10.8); // 10800/100000*100
      expect(riskMetrics.currentDrawdown.toNumber()).toBeGreaterThanOrEqual(0);
    });

    it('should emit risk alerts for excessive drawdown', async () => {
      const riskAlertSpy = vi.spyOn(positionManager, 'emit');
      
      const position = positionManager.getPosition(createdPosition.id)!;
      const previousState = { ...position };
      
      // Simulate position with high drawdown
      position.riskMetrics.currentDrawdown = new Decimal('0.15'); // 15% drawdown
      position.totalPnl = new Decimal('-10000'); // Large loss

      await positionManager['checkRiskAlerts'](position, previousState);

      expect(riskAlertSpy).toHaveBeenCalledWith('riskAlert', expect.objectContaining({
        type: 'drawdown_exceeded',
        positionId: createdPosition.id
      }));

      expect(riskAlertSpy).toHaveBeenCalledWith('riskAlert', expect.objectContaining({
        type: 'loss_limit_approaching',
        positionId: createdPosition.id
      }));
    });
  });

  describe('Broker Failover Integration', () => {
    let createdPosition: Position;

    beforeEach(async () => {
      mockPrisma.position.create.mockResolvedValue({ 
        id: 'pos_123', 
        ...mockPosition, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      });

      createdPosition = await positionManager.createPosition(mockPosition);
    });

    it('should handle broker failover events', () => {
      const failoverEvent = {
        fromBroker: 'account_123',
        toBroker: 'account_456',
        duration: 1000,
        success: true
      };

      positionManager['handleBrokerFailover'](failoverEvent);

      const position = positionManager.getPosition(createdPosition.id);
      expect(position?.accountId).toBe('account_456'); // Should be updated to new broker
      expect(position?.synchronizationStatus.isSynchronized).toBe(false);
    });

    it('should pause position updates during failover', () => {
      const failoverStartEvent = {
        fromBroker: 'account_123',
        toBroker: 'account_456'
      };

      positionManager['handleFailoverStarted'](failoverStartEvent);

      const position = positionManager.getPosition(createdPosition.id);
      expect(position?.synchronizationStatus.isSynchronized).toBe(false);
      expect(position?.synchronizationStatus.pendingSyncOperations).toBeGreaterThan(0);
    });
  });

  describe('Utility Functions', () => {
    it('should retrieve user positions correctly', async () => {
      // Create positions for different users
      for (let i = 0; i < 3; i++) {
        mockPrisma.position.create.mockResolvedValue({ 
          id: `pos_${i}`, 
          ...mockPosition, 
          userId: i === 0 ? 'user_123' : 'user_456',
          createdAt: new Date(), 
          updatedAt: new Date() 
        });

        await positionManager.createPosition({
          ...mockPosition,
          userId: i === 0 ? 'user_123' : 'user_456'
        });
      }

      const userPositions = positionManager.getUserPositions('user_123');
      expect(userPositions).toHaveLength(1);
      expect(userPositions[0].userId).toBe('user_123');
    });

    it('should get synchronization status', async () => {
      mockPrisma.position.create.mockResolvedValue({ 
        id: 'pos_123', 
        ...mockPosition, 
        createdAt: new Date(), 
        updatedAt: new Date() 
      });

      const createdPosition = await positionManager.createPosition(mockPosition);

      const syncStatus = positionManager.getSynchronizationStatus(createdPosition.id);
      expect(syncStatus).toBeDefined();
      expect(syncStatus?.isSynchronized).toBe(true);
    });

    it('should return null for non-existent position', () => {
      const position = positionManager.getPosition('nonexistent');
      expect(position).toBeNull();

      const syncStatus = positionManager.getSynchronizationStatus('nonexistent');
      expect(syncStatus).toBeNull();
    });
  });

  // Helper function to setup default mocks
  function setupDefaultMocks(): void {
    mockPrisma.position.findMany.mockResolvedValue([]);
    mockPrisma.position.create.mockImplementation((data) => {
      return Promise.resolve({
        ...data.data,
        id: data.data.id || 'mock_id',
        createdAt: new Date(),
        updatedAt: new Date()
      });
    });
    mockPrisma.position.update.mockImplementation((params) => {
      return Promise.resolve({
        id: params.where.id,
        ...params.data,
        updatedAt: new Date()
      });
    });
    mockStatusTracker.trackPositionUpdate.mockResolvedValue();
  }
});