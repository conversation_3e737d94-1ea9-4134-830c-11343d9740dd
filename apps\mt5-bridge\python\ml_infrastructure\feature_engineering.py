"""
Feature Engineering for Financial Market Data
Extracts and transforms market data into features suitable for transformer models
"""

import numpy as np
import pandas as pd
import ta
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio
from loguru import logger

@dataclass
class FeatureConfig:
    """Configuration for feature engineering"""
    lookback_periods: List[int] = None
    technical_indicators: List[str] = None
    price_features: bool = True
    volume_features: bool = True
    volatility_features: bool = True
    momentum_features: bool = True
    trend_features: bool = True
    pattern_features: bool = True
    market_microstructure: bool = True
    normalize_features: bool = True
    
    def __post_init__(self):
        if self.lookback_periods is None:
            self.lookback_periods = [5, 10, 20, 50, 100]
        if self.technical_indicators is None:
            self.technical_indicators = [
                'sma', 'ema', 'rsi', 'macd', 'bollinger', 'atr', 'stoch', 'williams_r'
            ]

class FinancialFeatureEngineer:
    """
    Comprehensive feature engineering for financial market data
    """
    
    def __init__(self, config: FeatureConfig = None):
        self.config = config or FeatureConfig()
        self.feature_names = []
        self.scaler_params = {}
        
    def extract_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        Extract comprehensive features from market data
        Args:
            data: DataFrame with OHLCV data
        Returns:
            DataFrame with engineered features
        """
        features_df = data.copy()
        
        # Basic price features
        if self.config.price_features:
            features_df = self._add_price_features(features_df)
        
        # Volume features
        if self.config.volume_features:
            features_df = self._add_volume_features(features_df)
        
        # Volatility features
        if self.config.volatility_features:
            features_df = self._add_volatility_features(features_df)
        
        # Momentum features
        if self.config.momentum_features:
            features_df = self._add_momentum_features(features_df)
        
        # Trend features
        if self.config.trend_features:
            features_df = self._add_trend_features(features_df)
        
        # Technical indicators
        features_df = self._add_technical_indicators(features_df)
        
        # Pattern features
        if self.config.pattern_features:
            features_df = self._add_pattern_features(features_df)
        
        # Market microstructure
        if self.config.market_microstructure:
            features_df = self._add_microstructure_features(features_df)
        
        # Time-based features
        features_df = self._add_time_features(features_df)
        
        # Normalize features
        if self.config.normalize_features:
            features_df = self._normalize_features(features_df)
        
        # Store feature names
        self.feature_names = [col for col in features_df.columns if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        return features_df
    
    def _add_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add price-based features"""
        # Returns
        df['returns'] = df['close'].pct_change()
        df['log_returns'] = np.log(df['close'] / df['close'].shift(1))
        
        # Price ratios
        df['hl_ratio'] = (df['high'] - df['low']) / df['close']
        df['oc_ratio'] = (df['open'] - df['close']) / df['close']
        
        # Price position within range
        df['price_position'] = (df['close'] - df['low']) / (df['high'] - df['low'])
        
        # Multi-period returns
        for period in self.config.lookback_periods:
            df[f'returns_{period}d'] = df['close'].pct_change(period)
            df[f'log_returns_{period}d'] = np.log(df['close'] / df['close'].shift(period))
        
        return df
    
    def _add_volume_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based features"""
        # Volume ratios
        df['volume_sma_ratio'] = df['volume'] / df['volume'].rolling(20).mean()
        
        # Volume-price features
        df['vwap'] = (df['volume'] * (df['high'] + df['low'] + df['close']) / 3).cumsum() / df['volume'].cumsum()
        df['volume_price_trend'] = df['volume'] * df['returns']
        
        # On-balance volume
        df['obv'] = ta.volume.on_balance_volume(df['close'], df['volume'])
        
        # Volume moving averages
        for period in [5, 10, 20]:
            df[f'volume_ma_{period}'] = df['volume'].rolling(period).mean()
            df[f'volume_ratio_{period}'] = df['volume'] / df[f'volume_ma_{period}']
        
        return df
    
    def _add_volatility_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility-based features"""
        # Historical volatility
        for period in self.config.lookback_periods:
            df[f'volatility_{period}d'] = df['returns'].rolling(period).std()
            df[f'volatility_log_{period}d'] = df['log_returns'].rolling(period).std()
        
        # Parkinson volatility (using high-low)
        df['parkinson_vol'] = np.sqrt(
            (1 / (4 * np.log(2))) * np.log(df['high'] / df['low']) ** 2
        )
        
        # Garman-Klass volatility
        df['gk_vol'] = np.sqrt(
            0.5 * np.log(df['high'] / df['low']) ** 2 - 
            (2 * np.log(2) - 1) * np.log(df['close'] / df['open']) ** 2
        )
        
        # ATR (Average True Range)
        df['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close'])
        
        return df
    
    def _add_momentum_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add momentum-based features"""
        # RSI
        df['rsi'] = ta.momentum.rsi(df['close'])
        
        # Stochastic oscillator
        df['stoch_k'] = ta.momentum.stoch(df['high'], df['low'], df['close'])
        df['stoch_d'] = ta.momentum.stoch_signal(df['high'], df['low'], df['close'])
        
        # Williams %R
        df['williams_r'] = ta.momentum.williams_r(df['high'], df['low'], df['close'])
        
        # Rate of Change
        for period in [5, 10, 20]:
            df[f'roc_{period}'] = ta.momentum.roc(df['close'], period)
        
        # Money Flow Index
        df['mfi'] = ta.volume.money_flow_index(df['high'], df['low'], df['close'], df['volume'])
        
        return df
    
    def _add_trend_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add trend-based features"""
        # Moving averages
        for period in [5, 10, 20, 50, 100, 200]:
            df[f'sma_{period}'] = ta.trend.sma_indicator(df['close'], period)
            df[f'ema_{period}'] = ta.trend.ema_indicator(df['close'], period)
            
            # Price relative to moving average
            df[f'price_sma_{period}_ratio'] = df['close'] / df[f'sma_{period}']
            df[f'price_ema_{period}_ratio'] = df['close'] / df[f'ema_{period}']
        
        # MACD
        df['macd'] = ta.trend.macd(df['close'])
        df['macd_signal'] = ta.trend.macd_signal(df['close'])
        df['macd_histogram'] = ta.trend.macd_diff(df['close'])
        
        # ADX (Average Directional Index)
        df['adx'] = ta.trend.adx(df['high'], df['low'], df['close'])
        df['adx_pos'] = ta.trend.adx_pos(df['high'], df['low'], df['close'])
        df['adx_neg'] = ta.trend.adx_neg(df['high'], df['low'], df['close'])
        
        return df
    
    def _add_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add technical indicators"""
        # Bollinger Bands
        df['bb_upper'] = ta.volatility.bollinger_hband(df['close'])
        df['bb_lower'] = ta.volatility.bollinger_lband(df['close'])
        df['bb_middle'] = ta.volatility.bollinger_mavg(df['close'])
        df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
        df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Commodity Channel Index
        df['cci'] = ta.trend.cci(df['high'], df['low'], df['close'])
        
        # Aroon
        df['aroon_up'] = ta.trend.aroon_up(df['high'], df['low'])
        df['aroon_down'] = ta.trend.aroon_down(df['high'], df['low'])
        
        return df
    
    def _add_pattern_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add pattern recognition features"""
        # Candlestick patterns (simplified)
        df['doji'] = (abs(df['open'] - df['close']) / (df['high'] - df['low']) < 0.1).astype(int)
        df['hammer'] = ((df['close'] > df['open']) & 
                       ((df['open'] - df['low']) > 2 * (df['close'] - df['open'])) &
                       ((df['high'] - df['close']) < 0.1 * (df['close'] - df['open']))).astype(int)
        
        # Support and resistance levels (simplified)
        df['local_high'] = (df['high'] > df['high'].shift(1)) & (df['high'] > df['high'].shift(-1))
        df['local_low'] = (df['low'] < df['low'].shift(1)) & (df['low'] < df['low'].shift(-1))
        
        return df
    
    def _add_microstructure_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add market microstructure features"""
        # Bid-ask spread proxy (using high-low)
        df['spread_proxy'] = (df['high'] - df['low']) / df['close']
        
        # Price impact proxy
        df['price_impact'] = abs(df['returns']) / np.log(df['volume'] + 1)
        
        # Tick direction
        df['tick_direction'] = np.sign(df['close'] - df['close'].shift(1))
        
        return df
    
    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features"""
        if 'timestamp' in df.columns:
            df['hour'] = pd.to_datetime(df['timestamp']).dt.hour
            df['day_of_week'] = pd.to_datetime(df['timestamp']).dt.dayofweek
            df['month'] = pd.to_datetime(df['timestamp']).dt.month
            
            # Market session indicators
            df['asian_session'] = ((df['hour'] >= 0) & (df['hour'] < 8)).astype(int)
            df['european_session'] = ((df['hour'] >= 8) & (df['hour'] < 16)).astype(int)
            df['american_session'] = ((df['hour'] >= 16) & (df['hour'] < 24)).astype(int)
        
        return df
    
    def _normalize_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Normalize features using rolling statistics"""
        feature_cols = [col for col in df.columns if col not in ['open', 'high', 'low', 'close', 'volume', 'timestamp']]
        
        for col in feature_cols:
            if df[col].dtype in ['float64', 'int64']:
                # Use rolling z-score normalization
                rolling_mean = df[col].rolling(window=100, min_periods=20).mean()
                rolling_std = df[col].rolling(window=100, min_periods=20).std()
                df[f'{col}_normalized'] = (df[col] - rolling_mean) / (rolling_std + 1e-8)
        
        return df
    
    def create_sequences(self, df: pd.DataFrame, sequence_length: int = 128, 
                        target_column: str = 'returns') -> Tuple[np.ndarray, np.ndarray]:
        """
        Create sequences for transformer training
        Args:
            df: DataFrame with features
            sequence_length: Length of input sequences
            target_column: Column to use as target
        Returns:
            Tuple of (features, targets)
        """
        feature_cols = [col for col in self.feature_names if col.endswith('_normalized')]
        
        # Prepare feature matrix
        feature_matrix = df[feature_cols].fillna(0).values
        targets = df[target_column].fillna(0).values
        
        # Create sequences
        X, y = [], []
        for i in range(sequence_length, len(feature_matrix)):
            X.append(feature_matrix[i-sequence_length:i])
            y.append(targets[i])
        
        return np.array(X), np.array(y)
    
    def get_feature_importance(self, model_predictions: Dict[str, float]) -> Dict[str, float]:
        """
        Calculate feature importance based on model predictions
        """
        # This would be implemented based on the specific model architecture
        # For now, return a placeholder
        return {name: np.random.random() for name in self.feature_names}

def create_market_regime_features(df: pd.DataFrame) -> pd.DataFrame:
    """
    Create features specifically for market regime detection
    """
    regime_df = df.copy()

    # Volatility regime indicators
    if 'volatility_20d' in regime_df.columns:
        regime_df['vol_regime'] = pd.qcut(regime_df['volatility_20d'], q=3, labels=['low', 'medium', 'high'], duplicates='drop')

    # Trend regime indicators
    if 'price_sma_20_ratio' in regime_df.columns:
        regime_df['trend_strength'] = abs(regime_df['price_sma_20_ratio'] - 1)
        regime_df['trend_regime'] = pd.qcut(regime_df['trend_strength'], q=3, labels=['weak', 'medium', 'strong'], duplicates='drop')

    # Volume regime indicators
    if 'volume_sma_ratio' in regime_df.columns:
        regime_df['volume_regime'] = pd.qcut(regime_df['volume_sma_ratio'], q=3, labels=['low', 'medium', 'high'], duplicates='drop')

    return regime_df

async def process_market_data_async(data: pd.DataFrame, config: FeatureConfig) -> pd.DataFrame:
    """
    Asynchronously process market data for feature engineering
    """
    engineer = FinancialFeatureEngineer(config)

    # Run feature engineering in a separate thread to avoid blocking
    loop = asyncio.get_event_loop()
    features = await loop.run_in_executor(None, engineer.extract_features, data)

    return features
