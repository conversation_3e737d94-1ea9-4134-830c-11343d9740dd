import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';

/**
 * MT5 Bridge Health Monitoring Service
 * Monitors the health and connectivity of the MT5 bridge service
 * Tracks connection status, latency, error rates, and service availability
 */
export class MT5BridgeHealthService extends EventEmitter {
  private prisma: PrismaClient;
  private bridgeConnections: Map<string, MT5BridgeConnection> = new Map();
  private healthHistory: MT5HealthMetric[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;

  private readonly MONITORING_INTERVAL = 30 * 1000; // 30 seconds
  private readonly HEALTH_HISTORY_LIMIT = 1000;
  private readonly CONNECTION_TIMEOUT = 10000; // 10 seconds
  
  // Health thresholds
  private readonly HEALTH_THRESHOLDS = {
    latency: {
      good: 50, // <50ms
      warning: 150, // 50-150ms
      critical: 500, // >500ms
    },
    errorRate: {
      good: 1, // <1%
      warning: 5, // 1-5%
      critical: 10, // >10%
    },
    uptime: {
      good: 99.5, // >99.5%
      warning: 98, // 98-99.5%
      critical: 95, // <95%
    },
  };

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  /**
   * Start MT5 bridge health monitoring
   */
  async startMonitoring(config: MT5BridgeMonitoringConfig = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('MT5 bridge monitoring already active');
      return;
    }

    console.log('📊 Starting MT5 bridge health monitoring...');
    this.isMonitoring = true;

    const intervalMs = config.intervalMs || this.MONITORING_INTERVAL;

    // Initialize bridge connections
    await this.initializeBridgeConnections();

    // Start health monitoring
    this.monitoringInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, intervalMs);

    console.log('✅ MT5 bridge health monitoring started');
    this.emit('monitoringStarted', { intervalMs });
  }

  /**
   * Stop MT5 bridge health monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ Stopping MT5 bridge health monitoring...');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('✅ MT5 bridge health monitoring stopped');
    this.emit('monitoringStopped');
  }

  /**
   * Initialize bridge connections
   */
  private async initializeBridgeConnections(): Promise<void> {
    // Default MT5 bridge connection
    const mainBridge: MT5BridgeConnection = {
      id: 'mt5-bridge-main',
      name: 'Main MT5 Bridge',
      endpoint: process.env.MT5_BRIDGE_URL || 'http://localhost:3002',
      status: 'unknown',
      lastHealthCheck: new Date(),
      latency: 0,
      errorRate: 0,
      uptime: 100,
      version: 'unknown',
      capabilities: ['trading', 'market_data', 'account_info'],
      consecutiveFailures: 0,
      totalRequests: 0,
      successfulRequests: 0,
      metadata: {
        startTime: new Date(),
        lastError: null,
      },
    };

    this.bridgeConnections.set(mainBridge.id, mainBridge);

    // Additional bridges can be configured via environment variables
    const additionalBridges = this.parseAdditionalBridges();
    additionalBridges.forEach(bridge => {
      this.bridgeConnections.set(bridge.id, bridge);
    });

    console.log(`🔧 Initialized ${this.bridgeConnections.size} MT5 bridge connections`);
  }

  /**
   * Parse additional bridge configurations from environment
   */
  private parseAdditionalBridges(): MT5BridgeConnection[] {
    const bridges: MT5BridgeConnection[] = [];
    
    // Parse MT5_BRIDGE_ADDITIONAL environment variable
    const additionalBridgesConfig = process.env.MT5_BRIDGE_ADDITIONAL;
    if (additionalBridgesConfig) {
      try {
        const configs = JSON.parse(additionalBridgesConfig);
        configs.forEach((config: any, index: number) => {
          bridges.push({
            id: config.id || `mt5-bridge-${index + 1}`,
            name: config.name || `MT5 Bridge ${index + 1}`,
            endpoint: config.endpoint,
            status: 'unknown',
            lastHealthCheck: new Date(),
            latency: 0,
            errorRate: 0,
            uptime: 100,
            version: 'unknown',
            capabilities: config.capabilities || ['trading', 'market_data'],
            consecutiveFailures: 0,
            totalRequests: 0,
            successfulRequests: 0,
            metadata: {
              startTime: new Date(),
              lastError: null,
            },
          });
        });
      } catch (error) {
        console.error('Failed to parse additional bridge configurations:', error);
      }
    }

    return bridges;
  }

  /**
   * Perform comprehensive health check on all bridges
   */
  private async performHealthCheck(): Promise<void> {
    const healthMetric: MT5HealthMetric = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      overallStatus: 'unknown',
      bridgeStatuses: new Map(),
      summary: {
        totalBridges: this.bridgeConnections.size,
        healthyBridges: 0,
        warningBridges: 0,
        criticalBridges: 0,
        downBridges: 0,
        averageLatency: 0,
        averageErrorRate: 0,
        averageUptime: 0,
      },
    };

    const bridgeChecks = Array.from(this.bridgeConnections.values()).map(
      bridge => this.checkBridgeHealth(bridge)
    );

    const results = await Promise.allSettled(bridgeChecks);
    
    let totalLatency = 0;
    let totalErrorRate = 0;
    let totalUptime = 0;

    results.forEach((result, index) => {
      const bridge = Array.from(this.bridgeConnections.values())[index];
      
      if (result.status === 'fulfilled') {
        const healthResult = result.value;
        healthMetric.bridgeStatuses.set(bridge.id, healthResult);
        
        totalLatency += healthResult.latency;
        totalErrorRate += healthResult.errorRate;
        totalUptime += healthResult.uptime;

        // Update bridge in memory
        this.bridgeConnections.set(bridge.id, {
          ...bridge,
          ...healthResult,
          lastHealthCheck: new Date(),
        });

        // Count status categories
        switch (healthResult.status) {
          case 'healthy':
            healthMetric.summary.healthyBridges++;
            break;
          case 'warning':
            healthMetric.summary.warningBridges++;
            break;
          case 'critical':
            healthMetric.summary.criticalBridges++;
            break;
          case 'down':
            healthMetric.summary.downBridges++;
            break;
        }
      } else {
        // Bridge check failed
        const failedStatus: MT5BridgeHealthResult = {
          status: 'down',
          latency: 0,
          errorRate: 100,
          uptime: 0,
          responseTime: 0,
          error: result.reason?.message || 'Health check failed',
          capabilities: bridge.capabilities,
        };
        
        healthMetric.bridgeStatuses.set(bridge.id, failedStatus);
        healthMetric.summary.downBridges++;
        
        // Update bridge consecutive failures
        bridge.consecutiveFailures++;
        bridge.metadata.lastError = result.reason?.message || 'Unknown error';
        this.bridgeConnections.set(bridge.id, bridge);
      }
    });

    // Calculate averages
    const totalBridges = healthMetric.summary.totalBridges;
    healthMetric.summary.averageLatency = totalBridges > 0 ? totalLatency / totalBridges : 0;
    healthMetric.summary.averageErrorRate = totalBridges > 0 ? totalErrorRate / totalBridges : 0;
    healthMetric.summary.averageUptime = totalBridges > 0 ? totalUptime / totalBridges : 0;

    // Determine overall status
    healthMetric.overallStatus = this.determineOverallStatus(healthMetric.summary);

    // Store health metric
    this.healthHistory.push(healthMetric);
    if (this.healthHistory.length > this.HEALTH_HISTORY_LIMIT) {
      this.healthHistory = this.healthHistory.slice(-this.HEALTH_HISTORY_LIMIT);
    }

    // Emit health metric
    this.emit('healthMetric', healthMetric);

    // Generate alerts if needed
    await this.generateHealthAlerts(healthMetric);

    // Store in database
    await this.storeHealthMetric(healthMetric);

    console.log(`📊 Health check completed: ${healthMetric.overallStatus} (${healthMetric.summary.healthyBridges}/${totalBridges} healthy)`);
  }

  /**
   * Check health of individual bridge
   */
  private async checkBridgeHealth(bridge: MT5BridgeConnection): Promise<MT5BridgeHealthResult> {
    const startTime = Date.now();
    
    try {
      // Health endpoint check
      const healthResponse = await this.makeHealthRequest(bridge.endpoint);
      const responseTime = Date.now() - startTime;

      // Connection info check
      const connectionInfo = await this.makeConnectionInfoRequest(bridge.endpoint);
      
      // Update bridge statistics
      bridge.totalRequests++;
      bridge.successfulRequests++;
      bridge.consecutiveFailures = 0;

      // Calculate metrics
      const latency = responseTime;
      const errorRate = bridge.totalRequests > 0 
        ? ((bridge.totalRequests - bridge.successfulRequests) / bridge.totalRequests) * 100 
        : 0;
      
      // Calculate uptime (simplified)
      const uptimeMs = Date.now() - bridge.metadata.startTime.getTime();
      const uptime = Math.min(100, (bridge.successfulRequests / Math.max(1, bridge.totalRequests)) * 100);

      // Determine status based on thresholds
      const status = this.determineBridgeStatus(latency, errorRate, uptime);

      return {
        status,
        latency,
        errorRate: Number(errorRate.toFixed(2)),
        uptime: Number(uptime.toFixed(2)),
        responseTime,
        version: healthResponse.version || 'unknown',
        capabilities: connectionInfo.capabilities || bridge.capabilities,
        connectionInfo: {
          isConnected: connectionInfo.connected || false,
          connectedAt: connectionInfo.connectedAt || null,
          serverInfo: connectionInfo.serverInfo || null,
          accountInfo: connectionInfo.accountInfo || null,
        },
      };
    } catch (error) {
      // Health check failed
      bridge.totalRequests++;
      bridge.consecutiveFailures++;
      bridge.metadata.lastError = error instanceof Error ? error.message : 'Unknown error';

      const errorRate = (bridge.totalRequests - bridge.successfulRequests) / bridge.totalRequests * 100;

      return {
        status: 'down',
        latency: 0,
        errorRate: Number(errorRate.toFixed(2)),
        uptime: 0,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        capabilities: bridge.capabilities,
      };
    }
  }

  /**
   * Make health request to bridge
   */
  private async makeHealthRequest(endpoint: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.CONNECTION_TIMEOUT);

    try {
      const response = await fetch(`${endpoint}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Make connection info request to bridge
   */
  private async makeConnectionInfoRequest(endpoint: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.CONNECTION_TIMEOUT);

    try {
      const response = await fetch(`${endpoint}/connection/info`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error) {
      clearTimeout(timeoutId);
      throw error;
    }
  }

  /**
   * Determine bridge status based on metrics
   */
  private determineBridgeStatus(latency: number, errorRate: number, uptime: number): MT5BridgeStatus {
    if (latency > this.HEALTH_THRESHOLDS.latency.critical || 
        errorRate > this.HEALTH_THRESHOLDS.errorRate.critical || 
        uptime < this.HEALTH_THRESHOLDS.uptime.critical) {
      return 'critical';
    }

    if (latency > this.HEALTH_THRESHOLDS.latency.warning || 
        errorRate > this.HEALTH_THRESHOLDS.errorRate.warning || 
        uptime < this.HEALTH_THRESHOLDS.uptime.warning) {
      return 'warning';
    }

    return 'healthy';
  }

  /**
   * Determine overall system status
   */
  private determineOverallStatus(summary: MT5HealthSummary): MT5BridgeStatus {
    if (summary.downBridges > 0 || summary.criticalBridges > 0) {
      return 'critical';
    }

    if (summary.warningBridges > 0) {
      return 'warning';
    }

    if (summary.healthyBridges === summary.totalBridges && summary.totalBridges > 0) {
      return 'healthy';
    }

    return 'down';
  }

  /**
   * Generate health alerts
   */
  private async generateHealthAlerts(healthMetric: MT5HealthMetric): Promise<void> {
    // Overall system alerts
    if (healthMetric.overallStatus === 'critical' || healthMetric.overallStatus === 'down') {
      this.emit('alert', {
        id: crypto.randomUUID(),
        type: 'mt5_system_critical',
        severity: 'critical',
        message: `MT5 bridge system is ${healthMetric.overallStatus}`,
        timestamp: new Date(),
        metadata: {
          overallStatus: healthMetric.overallStatus,
          downBridges: healthMetric.summary.downBridges,
          criticalBridges: healthMetric.summary.criticalBridges,
          totalBridges: healthMetric.summary.totalBridges,
        },
      });
    }

    // Individual bridge alerts
    for (const [bridgeId, bridgeStatus] of healthMetric.bridgeStatuses) {
      const bridge = this.bridgeConnections.get(bridgeId);
      if (!bridge) continue;

      // High latency alert
      if (bridgeStatus.latency > this.HEALTH_THRESHOLDS.latency.critical) {
        this.emit('alert', {
          id: crypto.randomUUID(),
          type: 'mt5_high_latency',
          severity: 'high',
          message: `High latency detected on ${bridge.name}: ${bridgeStatus.latency}ms`,
          timestamp: new Date(),
          metadata: {
            bridgeId,
            bridgeName: bridge.name,
            latency: bridgeStatus.latency,
            threshold: this.HEALTH_THRESHOLDS.latency.critical,
          },
        });
      }

      // High error rate alert
      if (bridgeStatus.errorRate > this.HEALTH_THRESHOLDS.errorRate.critical) {
        this.emit('alert', {
          id: crypto.randomUUID(),
          type: 'mt5_high_error_rate',
          severity: 'high',
          message: `High error rate detected on ${bridge.name}: ${bridgeStatus.errorRate}%`,
          timestamp: new Date(),
          metadata: {
            bridgeId,
            bridgeName: bridge.name,
            errorRate: bridgeStatus.errorRate,
            threshold: this.HEALTH_THRESHOLDS.errorRate.critical,
          },
        });
      }

      // Connection down alert
      if (bridgeStatus.status === 'down') {
        this.emit('alert', {
          id: crypto.randomUUID(),
          type: 'mt5_connection_down',
          severity: 'critical',
          message: `MT5 bridge connection down: ${bridge.name}`,
          timestamp: new Date(),
          metadata: {
            bridgeId,
            bridgeName: bridge.name,
            endpoint: bridge.endpoint,
            consecutiveFailures: bridge.consecutiveFailures,
            lastError: bridge.metadata.lastError,
          },
        });
      }
    }
  }

  /**
   * Store health metric in database
   */
  private async storeHealthMetric(healthMetric: MT5HealthMetric): Promise<void> {
    try {
      await this.prisma.mt5HealthMetric.create({
        data: {
          id: healthMetric.id,
          timestamp: healthMetric.timestamp,
          overallStatus: healthMetric.overallStatus,
          totalBridges: healthMetric.summary.totalBridges,
          healthyBridges: healthMetric.summary.healthyBridges,
          warningBridges: healthMetric.summary.warningBridges,
          criticalBridges: healthMetric.summary.criticalBridges,
          downBridges: healthMetric.summary.downBridges,
          averageLatency: healthMetric.summary.averageLatency,
          averageErrorRate: healthMetric.summary.averageErrorRate,
          averageUptime: healthMetric.summary.averageUptime,
          bridgeStatuses: Object.fromEntries(healthMetric.bridgeStatuses),
        },
      });
    } catch (error) {
      console.error('Failed to store MT5 health metric:', error);
    }
  }

  /**
   * Get current health status
   */
  getCurrentHealthStatus(): MT5BridgeHealthStatus {
    const latestMetric = this.healthHistory[this.healthHistory.length - 1];
    const bridgeConnections = Array.from(this.bridgeConnections.values());

    return {
      isMonitoring: this.isMonitoring,
      lastCheck: latestMetric?.timestamp || null,
      overallStatus: latestMetric?.overallStatus || 'unknown',
      bridges: bridgeConnections.map(bridge => ({
        id: bridge.id,
        name: bridge.name,
        endpoint: bridge.endpoint,
        status: bridge.status,
        latency: bridge.latency,
        errorRate: bridge.errorRate,
        uptime: bridge.uptime,
        lastHealthCheck: bridge.lastHealthCheck,
        consecutiveFailures: bridge.consecutiveFailures,
      })),
      summary: latestMetric?.summary || {
        totalBridges: bridgeConnections.length,
        healthyBridges: 0,
        warningBridges: 0,
        criticalBridges: 0,
        downBridges: 0,
        averageLatency: 0,
        averageErrorRate: 0,
        averageUptime: 0,
      },
    };
  }

  /**
   * Get historical health metrics
   */
  getHealthHistory(timeRange: string = '1h'): MT5HealthMetric[] {
    const timeRangeMs = this.getTimeRangeMs(timeRange);
    const cutoffTime = new Date(Date.now() - timeRangeMs);

    return this.healthHistory.filter(metric => 
      metric.timestamp >= cutoffTime
    );
  }

  /**
   * Test specific bridge connection
   */
  async testBridgeConnection(bridgeId: string): Promise<MT5BridgeHealthResult> {
    const bridge = this.bridgeConnections.get(bridgeId);
    if (!bridge) {
      throw new Error(`Bridge not found: ${bridgeId}`);
    }

    return await this.checkBridgeHealth(bridge);
  }

  /**
   * Convert time range to milliseconds
   */
  private getTimeRangeMs(timeRange: string): number {
    switch (timeRange) {
      case '5m': return 5 * 60 * 1000;
      case '15m': return 15 * 60 * 1000;
      case '1h': return 60 * 60 * 1000;
      case '4h': return 4 * 60 * 60 * 1000;
      case '24h': return 24 * 60 * 60 * 1000;
      case '7d': return 7 * 24 * 60 * 60 * 1000;
      default: return 60 * 60 * 1000;
    }
  }

  /**
   * Clear health history
   */
  clearHealthHistory(): void {
    this.healthHistory = [];
    console.log('🧹 MT5 health history cleared');
  }
}

// Type definitions
interface MT5BridgeMonitoringConfig {
  intervalMs?: number;
  connectionTimeout?: number;
  additionalBridges?: MT5BridgeConfig[];
}

interface MT5BridgeConfig {
  id: string;
  name: string;
  endpoint: string;
  capabilities?: string[];
}

interface MT5BridgeConnection {
  id: string;
  name: string;
  endpoint: string;
  status: MT5BridgeStatus;
  lastHealthCheck: Date;
  latency: number;
  errorRate: number;
  uptime: number;
  version: string;
  capabilities: string[];
  consecutiveFailures: number;
  totalRequests: number;
  successfulRequests: number;
  metadata: {
    startTime: Date;
    lastError: string | null;
  };
}

interface MT5BridgeHealthResult {
  status: MT5BridgeStatus;
  latency: number;
  errorRate: number;
  uptime: number;
  responseTime: number;
  version?: string;
  error?: string;
  capabilities: string[];
  connectionInfo?: {
    isConnected: boolean;
    connectedAt: Date | null;
    serverInfo: any;
    accountInfo: any;
  };
}

interface MT5HealthMetric {
  id: string;
  timestamp: Date;
  overallStatus: MT5BridgeStatus;
  bridgeStatuses: Map<string, MT5BridgeHealthResult>;
  summary: MT5HealthSummary;
}

interface MT5HealthSummary {
  totalBridges: number;
  healthyBridges: number;
  warningBridges: number;
  criticalBridges: number;
  downBridges: number;
  averageLatency: number;
  averageErrorRate: number;
  averageUptime: number;
}

interface MT5BridgeHealthStatus {
  isMonitoring: boolean;
  lastCheck: Date | null;
  overallStatus: MT5BridgeStatus;
  bridges: Array<{
    id: string;
    name: string;
    endpoint: string;
    status: MT5BridgeStatus;
    latency: number;
    errorRate: number;
    uptime: number;
    lastHealthCheck: Date;
    consecutiveFailures: number;
  }>;
  summary: MT5HealthSummary;
}

type MT5BridgeStatus = 'healthy' | 'warning' | 'critical' | 'down' | 'unknown';

export type {
  MT5BridgeMonitoringConfig,
  MT5BridgeConfig,
  MT5BridgeConnection,
  MT5BridgeHealthResult,
  MT5HealthMetric,
  MT5HealthSummary,
  MT5BridgeHealthStatus,
  MT5BridgeStatus,
};