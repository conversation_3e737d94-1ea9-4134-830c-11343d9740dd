import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import { PrismaClient } from '@prisma/client';
import { ConfidenceStage, QuizDifficulty, QuizCategory } from '@golddaddy/types';
import app from '../../../app.js';

const prisma = new PrismaClient();

describe('Confidence Assessment Quiz Workflow', () => {
  let testUserId: string;
  let sessionId: string;
  let questionIds: string[];

  beforeAll(async () => {
    // Setup test data
    testUserId = 'test-user-quiz-workflow';
    
    // Ensure user has confidence assessment
    await prisma.confidenceAssessment.upsert({
      where: { userId: testUserId },
      update: {},
      create: {
        userId: testUserId,
        currentStage: ConfidenceStage.STRATEGY_LEARNING,
        overallConfidenceScore: 65,
        assessmentScores: {
          knowledgeQuiz: {
            score: 65,
            completedAt: new Date(),
            attempts: 1,
            weakAreas: ['risk_management']
          },
          behavioralAssessment: {
            riskTolerance: 60,
            decisionConsistency: 65,
            emotionalStability: 70,
            lastAssessed: new Date()
          },
          performanceEvaluation: {
            paperTradingWinRate: 0,
            riskManagementScore: 0,
            strategyAdherence: 0,
            consistencyRating: 0
          }
        },
        progressHistory: [],
        graduationCriteria: {
          nextStage: 'backtesting_review',
          requirements: {
            minimumConfidenceScore: 80,
            requiredAssessments: ['strategy_understanding'],
            minimumTimeInStage: 7
          }
        }
      }
    });

    // Create test quiz questions
    const questions = await Promise.all([
      prisma.quizQuestion.create({
        data: {
          category: QuizCategory.TRADING_FUNDAMENTALS,
          difficulty: QuizDifficulty.INTERMEDIATE,
          topic: 'Position Sizing',
          question: 'What is the recommended risk per trade for beginners?',
          options: [
            { id: 'opt1', text: '1-2%', isCorrect: true },
            { id: 'opt2', text: '5-10%', isCorrect: false },
            { id: 'opt3', text: '15-20%', isCorrect: false },
            { id: 'opt4', text: 'As much as possible', isCorrect: false }
          ],
          correctAnswerIds: ['opt1'],
          explanation: 'Beginners should risk 1-2% per trade to preserve capital.',
          learningResources: [],
          metadata: {
            estimatedDuration: 60,
            tags: ['risk-management'],
            riskLevel: 'low'
          }
        }
      }),
      prisma.quizQuestion.create({
        data: {
          category: QuizCategory.RISK_MANAGEMENT,
          difficulty: QuizDifficulty.INTERMEDIATE,
          topic: 'Stop Losses',
          question: 'When should you set a stop loss?',
          options: [
            { id: 'opt1', text: 'Before entering the trade', isCorrect: true },
            { id: 'opt2', text: 'After the trade moves against you', isCorrect: false },
            { id: 'opt3', text: 'Only for large positions', isCorrect: false },
            { id: 'opt4', text: 'Never set stop losses', isCorrect: false }
          ],
          correctAnswerIds: ['opt1'],
          explanation: 'Stop losses should always be set before entering a trade.',
          learningResources: [],
          metadata: {
            estimatedDuration: 45,
            tags: ['stop-loss', 'risk-management'],
            riskLevel: 'medium'
          }
        }
      })
    ]);

    questionIds = questions.map(q => q.id);
  });

  afterAll(async () => {
    // Cleanup test data
    await prisma.quizResponse.deleteMany({ where: { userId: testUserId } });
    await prisma.quizSessionQuestion.deleteMany({
      where: { session: { userId: testUserId } }
    });
    await prisma.quizSession.deleteMany({ where: { userId: testUserId } });
    await prisma.quizAttempt.deleteMany({ where: { userId: testUserId } });
    await prisma.confidenceAssessment.deleteMany({ where: { userId: testUserId } });
    await prisma.quizQuestion.deleteMany({ where: { id: { in: questionIds } } });
    await prisma.userActivity.deleteMany({ where: { userId: testUserId } });
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Clean up any existing sessions
    await prisma.quizResponse.deleteMany({ where: { userId: testUserId } });
    await prisma.quizSessionQuestion.deleteMany({
      where: { session: { userId: testUserId } }
    });
    await prisma.quizSession.deleteMany({ where: { userId: testUserId } });
  });

  describe('Complete Quiz Workflow', () => {
    it('should start a quiz session successfully', async () => {
      const response = await request(app)
        .post('/api/confidence/quiz/start')
        .send({
          userId: testUserId,
          stage: ConfidenceStage.STRATEGY_LEARNING,
          difficulty: QuizDifficulty.INTERMEDIATE
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.session).toBeDefined();
      expect(response.body.session.userId).toBe(testUserId);
      expect(response.body.session.stage).toBe(ConfidenceStage.STRATEGY_LEARNING);
      expect(response.body.questions).toHaveLength(2);
      expect(response.body.questions[0]).not.toHaveProperty('correctAnswerIds');

      sessionId = response.body.session.id;
    });

    it('should submit answers and receive feedback', async () => {
      // Start session first
      const startResponse = await request(app)
        .post('/api/confidence/quiz/start')
        .send({
          userId: testUserId,
          stage: ConfidenceStage.STRATEGY_LEARNING
        });

      sessionId = startResponse.body.session.id;
      const question = startResponse.body.questions[0];

      // Submit correct answer
      const submitResponse = await request(app)
        .post('/api/confidence/quiz/submit')
        .send({
          sessionId,
          userId: testUserId,
          questionId: question.id,
          selectedAnswerIds: ['opt1'], // Correct answer
          confidenceLevel: 4,
          timeSpent: 30
        })
        .expect(200);

      expect(submitResponse.body.success).toBe(true);
      expect(submitResponse.body.isCorrect).toBe(true);
      expect(submitResponse.body.feedback).toBeDefined();
      expect(submitResponse.body.feedback.isCorrect).toBe(true);
      expect(submitResponse.body.feedback.explanation).toContain('preserve capital');
      expect(submitResponse.body.sessionProgress.currentQuestion).toBe(1);
    });

    it('should complete quiz and create attempt record', async () => {
      // Start session
      const startResponse = await request(app)
        .post('/api/confidence/quiz/start')
        .send({
          userId: testUserId,
          stage: ConfidenceStage.STRATEGY_LEARNING
        });

      sessionId = startResponse.body.session.id;
      const questions = startResponse.body.questions;

      // Submit answers to all questions
      for (let i = 0; i < questions.length; i++) {
        await request(app)
          .post('/api/confidence/quiz/submit')
          .send({
            sessionId,
            userId: testUserId,
            questionId: questions[i].id,
            selectedAnswerIds: ['opt1'], // Always select first option
            confidenceLevel: 3,
            timeSpent: 45
          })
          .expect(200);
      }

      // Verify quiz attempt was created
      const attempt = await prisma.quizAttempt.findFirst({
        where: { sessionId },
        include: { session: true }
      });

      expect(attempt).toBeDefined();
      expect(attempt!.userId).toBe(testUserId);
      expect(attempt!.stage).toBe(ConfidenceStage.STRATEGY_LEARNING);
      expect(attempt!.overallScore).toBeGreaterThan(0);
      expect(attempt!.totalQuestions).toBe(questions.length);
    });

    it('should track quiz status correctly', async () => {
      // Start session and submit one answer
      const startResponse = await request(app)
        .post('/api/confidence/quiz/start')
        .send({
          userId: testUserId,
          stage: ConfidenceStage.STRATEGY_LEARNING
        });

      sessionId = startResponse.body.session.id;
      const question = startResponse.body.questions[0];

      await request(app)
        .post('/api/confidence/quiz/submit')
        .send({
          sessionId,
          userId: testUserId,
          questionId: question.id,
          selectedAnswerIds: ['opt1'],
          confidenceLevel: 4,
          timeSpent: 30
        });

      // Check status
      const statusResponse = await request(app)
        .get('/api/confidence/quiz/status')
        .query({
          userId: testUserId,
          sessionId
        })
        .expect(200);

      expect(statusResponse.body.hasActiveSession).toBe(true);
      expect(statusResponse.body.session.currentQuestion).toBe(2); // Next question
      expect(statusResponse.body.session.progress).toBe(50); // 1 of 2 questions
      expect(statusResponse.body.session.currentScore).toBe(100); // 1 correct answer
    });

    it('should retrieve quiz history', async () => {
      // Complete a quiz first
      const startResponse = await request(app)
        .post('/api/confidence/quiz/start')
        .send({
          userId: testUserId,
          stage: ConfidenceStage.STRATEGY_LEARNING
        });

      sessionId = startResponse.body.session.id;
      const questions = startResponse.body.questions;

      for (const question of questions) {
        await request(app)
          .post('/api/confidence/quiz/submit')
          .send({
            sessionId,
            userId: testUserId,
            questionId: question.id,
            selectedAnswerIds: ['opt1'],
            confidenceLevel: 3,
            timeSpent: 45
          });
      }

      // Get history
      const historyResponse = await request(app)
        .get('/api/confidence/quiz/history')
        .query({
          userId: testUserId,
          stage: ConfidenceStage.STRATEGY_LEARNING,
          limit: 10
        })
        .expect(200);

      expect(historyResponse.body.success).toBe(true);
      expect(historyResponse.body.attempts).toHaveLength(1);
      expect(historyResponse.body.attempts[0].stage).toBe(ConfidenceStage.STRATEGY_LEARNING);
      expect(historyResponse.body.summary.totalAttempts).toBe(1);
      expect(historyResponse.body.summary.averageScore).toBeGreaterThan(0);
    });
  });

  describe('Error Handling', () => {
    it('should prevent starting multiple active sessions', async () => {
      // Start first session
      await request(app)
        .post('/api/confidence/quiz/start')
        .send({
          userId: testUserId,
          stage: ConfidenceStage.STRATEGY_LEARNING
        })
        .expect(201);

      // Try to start second session
      const response = await request(app)
        .post('/api/confidence/quiz/start')
        .send({
          userId: testUserId,
          stage: ConfidenceStage.STRATEGY_LEARNING
        })
        .expect(400);

      expect(response.body.error).toContain('active quiz session');
    });

    it('should validate quiz submission', async () => {
      // Try to submit without valid session
      await request(app)
        .post('/api/confidence/quiz/submit')
        .send({
          sessionId: 'invalid-session',
          userId: testUserId,
          questionId: 'invalid-question',
          selectedAnswerIds: ['opt1'],
          confidenceLevel: 3,
          timeSpent: 30
        })
        .expect(404);
    });

    it('should prevent duplicate answers', async () => {
      // Start session
      const startResponse = await request(app)
        .post('/api/confidence/quiz/start')
        .send({
          userId: testUserId,
          stage: ConfidenceStage.STRATEGY_LEARNING
        });

      sessionId = startResponse.body.session.id;
      const question = startResponse.body.questions[0];

      // Submit first answer
      await request(app)
        .post('/api/confidence/quiz/submit')
        .send({
          sessionId,
          userId: testUserId,
          questionId: question.id,
          selectedAnswerIds: ['opt1'],
          confidenceLevel: 3,
          timeSpent: 30
        })
        .expect(200);

      // Try to submit same question again
      await request(app)
        .post('/api/confidence/quiz/submit')
        .send({
          sessionId,
          userId: testUserId,
          questionId: question.id,
          selectedAnswerIds: ['opt2'],
          confidenceLevel: 2,
          timeSpent: 15
        })
        .expect(400);
    });
  });

  describe('Progress Integration', () => {
    it('should update confidence assessment after quiz completion', async () => {
      // Get initial assessment
      const initialAssessment = await prisma.confidenceAssessment.findUnique({
        where: { userId: testUserId }
      });

      // Complete quiz
      const startResponse = await request(app)
        .post('/api/confidence/quiz/start')
        .send({
          userId: testUserId,
          stage: ConfidenceStage.STRATEGY_LEARNING
        });

      const questions = startResponse.body.questions;
      sessionId = startResponse.body.session.id;

      for (const question of questions) {
        await request(app)
          .post('/api/confidence/quiz/submit')
          .send({
            sessionId,
            userId: testUserId,
            questionId: question.id,
            selectedAnswerIds: ['opt1'], // Correct answers
            confidenceLevel: 4,
            timeSpent: 30
          });
      }

      // Check updated assessment
      const updatedAssessment = await prisma.confidenceAssessment.findUnique({
        where: { userId: testUserId }
      });

      expect(updatedAssessment!.updatedAt).not.toEqual(initialAssessment!.updatedAt);
      expect(updatedAssessment!.overallConfidenceScore).toBeGreaterThanOrEqual(
        initialAssessment!.overallConfidenceScore
      );
    });
  });
});