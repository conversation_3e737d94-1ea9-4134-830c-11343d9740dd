{"name": "@golddaddy/api", "version": "1.0.0", "private": true, "description": "GoldDaddy API Services - Backend", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:integration": "vitest run --config vitest.integration.config.ts"}, "dependencies": {"@golddaddy/config": "*", "@golddaddy/types": "*", "@prisma/client": "^6.14.0", "@supabase/supabase-js": "^2.55.0", "@types/redis": "^4.0.10", "better-sqlite3": "^12.2.0", "cors": "^2.8.0", "dotenv": "^16.0.0", "express": "^4.18.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^7.0.0", "prisma": "^6.14.0", "redis": "^5.8.1", "ws": "^8.18.0"}, "devDependencies": {"@faker-js/faker": "^9.9.0", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.0", "@types/decimal.js": "^0.0.32", "@types/express": "^4.17.0", "@types/node": "^20.0.0", "@types/supertest": "^6.0.3", "@types/ws": "^8.5.13", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "bcryptjs": "^3.0.2", "eslint": "^8.0.0", "supertest": "^7.1.4", "tsx": "^4.0.0", "typescript": "^5.3.0"}}