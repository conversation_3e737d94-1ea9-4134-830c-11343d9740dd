/**
 * Market Data WebSocket Server
 * 
 * Provides real-time market data streaming with JWT authentication,
 * instrument subscriptions, and performance monitoring.
 */

import { WebSocket, WebSocketServer } from 'ws';
import { IncomingMessage } from 'http';
import { EventEmitter } from 'events';
import * as jwt from 'jsonwebtoken';
import Decimal from 'decimal.js';
import { NormalizedMarketData, DataSource, TimeFrame } from '../market-data/RealTimeDataProcessor';

// WebSocket Message Interfaces
export interface PriceStreamMessage {
  type: 'price_update';
  instrument: string;
  price: {
    bid?: number;
    ask?: number;
    close: number;
    open?: number;
    high?: number;
    low?: number;
    spread?: number;
  };
  timestamp: Date;
  source: DataSource;
  timeframe?: TimeFrame;
  volume?: number;
  marketAnalysis?: {
    trend?: 'bullish' | 'bearish' | 'neutral';
    volatility?: number;
    mood?: 'positive' | 'negative' | 'neutral';
    mlPrediction?: {
      direction: 'up' | 'down' | 'sideways';
      confidence: number;
      timeHorizon: string;
    };
  };
}

export interface SubscribeMessage {
  type: 'subscribe' | 'unsubscribe';
  action: 'subscribe' | 'unsubscribe';
  instruments: string[];
  features?: {
    marketAnalysis?: boolean;
    mlPredictions?: boolean;
    volatilityIndicators?: boolean;
    technicalIndicators?: boolean;
  };
  timeframes?: TimeFrame[];
  updateInterval?: number; // milliseconds
}

export interface ClientCapabilities {
  maxUpdatesPerSecond?: number;
  supportedFeatures?: string[];
  compressionSupport?: boolean;
  batchingSupport?: boolean;
}

export interface ConnectionAckMessage {
  type: 'connection_ack';
  clientId: string;
  serverCapabilities: {
    maxInstruments: number;
    supportedFeatures: string[];
    updateInterval: number;
  };
  timestamp: Date;
}

export interface ErrorMessage {
  type: 'error';
  code: string;
  message: string;
  timestamp: Date;
}

export interface HeartbeatMessage {
  type: 'heartbeat' | 'heartbeat_ack';
  timestamp: Date;
}

export interface SubscriptionConfirmation {
  type: 'subscription_confirmed';
  instruments: string[];
  features: Record<string, boolean>;
  timestamp: Date;
}

type WebSocketMessage = 
  | PriceStreamMessage 
  | ConnectionAckMessage 
  | ErrorMessage 
  | HeartbeatMessage 
  | SubscriptionConfirmation;

// Client interface
export interface MarketDataClient {
  ws: WebSocket;
  clientId: string;
  userId: string;
  permissions: string[];
  subscribedInstruments: Set<string>;
  enabledFeatures: Set<string>;
  timeframes: Set<TimeFrame>;
  updateInterval: number;
  lastHeartbeat: Date;
  connectionTime: Date;
  capabilities: ClientCapabilities;
  messagesSent: number;
  messagesReceived: number;
  dataTransferred: number;
}

// JWT Payload interface
interface JwtPayload {
  sub: string; // user ID
  permissions?: string[];
  iat: number;
  exp: number;
  [key: string]: any;
}

// Configuration interface
export interface WebSocketServerConfig {
  port: number;
  jwtSecret: string;
  maxConnections: number;
  heartbeatInterval: number; // milliseconds
  clientTimeout: number; // milliseconds
  maxInstrumentsPerClient: number;
  defaultUpdateInterval: number; // milliseconds
  rateLimitPerSecond: number;
  enableCompression: boolean;
  enableBatching: boolean;
  allowedOrigins?: string[];
}

/**
 * Market Data WebSocket Server with JWT authentication and subscription management
 */
export class MarketDataWebSocketServer extends EventEmitter {
  private wss: WebSocketServer | null = null;
  private clients: Map<string, MarketDataClient> = new Map();
  private config: WebSocketServerConfig;
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private performanceMetrics: {
    totalConnections: number;
    activeConnections: number;
    totalMessagesReceived: number;
    totalMessagesSent: number;
    totalDataTransferred: number;
    averageResponseTime: number;
    errorCount: number;
    lastErrorTime?: Date;
    startTime: Date;
  };

  constructor(config: WebSocketServerConfig) {
    super();
    this.config = config;
    this.performanceMetrics = {
      totalConnections: 0,
      activeConnections: 0,
      totalMessagesReceived: 0,
      totalMessagesSent: 0,
      totalDataTransferred: 0,
      averageResponseTime: 0,
      errorCount: 0,
      startTime: new Date(),
    };
  }

  /**
   * Initialize WebSocket server
   */
  public initialize(): void {
    this.wss = new WebSocketServer({
      port: this.config.port,
      verifyClient: this.verifyClient.bind(this),
      perMessageDeflate: this.config.enableCompression,
    });

    this.wss.on('connection', this.handleConnection.bind(this));
    this.wss.on('error', this.handleServerError.bind(this));
    
    this.startHeartbeat();

    this.emit('server_started', {
      port: this.config.port,
      timestamp: new Date(),
    });

    console.log(`Market Data WebSocket server started on port ${this.config.port}`);
  }

  /**
   * Verify WebSocket client connection with JWT authentication
   */
  private verifyClient(info: { req: IncomingMessage; origin?: string }): boolean {
    try {
      // Check origin if specified
      if (this.config.allowedOrigins && info.origin) {
        if (!this.config.allowedOrigins.includes(info.origin)) {
          return false;
        }
      }

      // Check connection limit
      if (this.clients.size >= this.config.maxConnections) {
        return false;
      }

      // Extract JWT token from query params or headers
      const url = new URL(info.req.url || '', 'ws://localhost');
      const token = url.searchParams.get('token') || 
                   info.req.headers.authorization?.replace('Bearer ', '');

      if (!token) {
        return false;
      }

      // Verify JWT token
      const decoded = jwt.verify(token, this.config.jwtSecret) as JwtPayload;
      
      if (!decoded.sub) {
        return false;
      }

      // Store decoded token for use in connection handler
      (info.req as any).decodedToken = decoded;
      return true;

    } catch (error) {
      console.error('WebSocket verification failed:', error);
      return false;
    }
  }

  /**
   * Handle new WebSocket connection
   */
  private handleConnection(ws: WebSocket, req: IncomingMessage): void {
    const decodedToken = (req as any).decodedToken as JwtPayload;
    const userId = decodedToken.sub;
    const permissions = decodedToken.permissions || [];
    
    const clientId = this.generateClientId(userId);
    
    // Parse client capabilities from connection params
    const url = new URL(req.url || '', 'ws://localhost');
    const capabilities: ClientCapabilities = {
      maxUpdatesPerSecond: parseInt(url.searchParams.get('maxUpdatesPerSecond') || '10'),
      supportedFeatures: url.searchParams.get('features')?.split(',') || [],
      compressionSupport: url.searchParams.get('compression') === 'true',
      batchingSupport: url.searchParams.get('batching') === 'true',
    };

    const client: MarketDataClient = {
      ws,
      clientId,
      userId,
      permissions,
      subscribedInstruments: new Set(),
      enabledFeatures: new Set(),
      timeframes: new Set([TimeFrame.M1]), // Default to 1-minute
      updateInterval: Math.max(this.config.defaultUpdateInterval, 1000 / (capabilities.maxUpdatesPerSecond || 10)),
      lastHeartbeat: new Date(),
      connectionTime: new Date(),
      capabilities,
      messagesSent: 0,
      messagesReceived: 0,
      dataTransferred: 0,
    };

    this.clients.set(clientId, client);
    this.performanceMetrics.totalConnections++;
    this.performanceMetrics.activeConnections++;

    // Send connection acknowledgment
    this.sendToClient(clientId, {
      type: 'connection_ack',
      clientId,
      serverCapabilities: {
        maxInstruments: this.config.maxInstrumentsPerClient,
        supportedFeatures: ['marketAnalysis', 'mlPredictions', 'volatilityIndicators', 'technicalIndicators'],
        updateInterval: this.config.defaultUpdateInterval,
      },
      timestamp: new Date(),
    });

    // Handle incoming messages
    ws.on('message', (data: Buffer) => {
      this.handleClientMessage(clientId, data);
    });

    // Handle client disconnect
    ws.on('close', (code: number, reason: Buffer) => {
      this.handleClientDisconnect(clientId, code, reason.toString());
    });

    // Handle WebSocket errors
    ws.on('error', (error: Error) => {
      this.handleClientError(clientId, error);
    });

    this.emit('client_connected', {
      clientId,
      userId,
      timestamp: new Date(),
    });

    console.log(`Market data client ${clientId} connected for user ${userId}`);
  }

  /**
   * Handle incoming client messages
   */
  private handleClientMessage(clientId: string, data: Buffer): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    try {
      const message = JSON.parse(data.toString());
      client.messagesReceived++;
      this.performanceMetrics.totalMessagesReceived++;

      switch (message.type) {
        case 'heartbeat':
          this.handleHeartbeat(clientId);
          break;

        case 'subscribe':
        case 'unsubscribe':
          this.handleSubscriptionMessage(clientId, message as SubscribeMessage);
          break;

        case 'ping':
          this.sendToClient(clientId, {
            type: 'heartbeat_ack',
            timestamp: new Date(),
          });
          break;

        default:
          this.sendError(clientId, 'UNKNOWN_MESSAGE_TYPE', `Unknown message type: ${message.type}`);
      }

    } catch (error) {
      this.sendError(clientId, 'INVALID_MESSAGE_FORMAT', 'Invalid JSON message format');
      this.handleClientError(clientId, error as Error);
    }
  }

  /**
   * Handle subscription/unsubscription messages
   */
  private handleSubscriptionMessage(clientId: string, message: SubscribeMessage): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    const { action, instruments, features, timeframes, updateInterval } = message;

    // Validate instrument limit
    if (action === 'subscribe' && 
        client.subscribedInstruments.size + instruments.length > this.config.maxInstrumentsPerClient) {
      this.sendError(clientId, 'INSTRUMENT_LIMIT_EXCEEDED', 
        `Cannot subscribe to more than ${this.config.maxInstrumentsPerClient} instruments`);
      return;
    }

    // Update subscriptions
    if (action === 'subscribe') {
      instruments.forEach(instrument => {
        client.subscribedInstruments.add(instrument.toUpperCase());
      });
      
      // Update features
      if (features) {
        Object.entries(features).forEach(([feature, enabled]) => {
          if (enabled) {
            client.enabledFeatures.add(feature);
          } else {
            client.enabledFeatures.delete(feature);
          }
        });
      }

      // Update timeframes
      if (timeframes) {
        client.timeframes.clear();
        timeframes.forEach(tf => client.timeframes.add(tf));
      }

      // Update interval if specified
      if (updateInterval) {
        client.updateInterval = Math.max(updateInterval, 100); // Min 100ms
      }

    } else if (action === 'unsubscribe') {
      instruments.forEach(instrument => {
        client.subscribedInstruments.delete(instrument.toUpperCase());
      });
    }

    // Send confirmation
    this.sendToClient(clientId, {
      type: 'subscription_confirmed',
      instruments: Array.from(client.subscribedInstruments),
      features: Object.fromEntries(
        Array.from(client.enabledFeatures).map(feature => [feature, true])
      ),
      timestamp: new Date(),
    });

    this.emit('subscription_changed', {
      clientId,
      userId: client.userId,
      action,
      instruments,
      totalSubscriptions: client.subscribedInstruments.size,
      timestamp: new Date(),
    });
  }

  /**
   * Handle client heartbeat
   */
  private handleHeartbeat(clientId: string): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    client.lastHeartbeat = new Date();
    this.sendToClient(clientId, {
      type: 'heartbeat_ack',
      timestamp: new Date(),
    });
  }

  /**
   * Broadcast market data to subscribed clients
   */
  public broadcastMarketData(data: NormalizedMarketData): void {
    const instrument = data.instrument.toUpperCase();
    const relevantClients = Array.from(this.clients.values())
      .filter(client => client.subscribedInstruments.has(instrument));

    if (relevantClients.length === 0) return;

    // Create price stream message
    const priceMessage: PriceStreamMessage = {
      type: 'price_update',
      instrument: data.instrument,
      price: {
        bid: data.bid?.toNumber(),
        ask: data.ask?.toNumber(),
        close: data.close.toNumber(),
        open: data.open.toNumber(),
        high: data.high.toNumber(),
        low: data.low.toNumber(),
        spread: data.spread?.toNumber(),
      },
      timestamp: data.timestamp,
      source: data.source,
      timeframe: data.timeframe,
      volume: data.volume.toNumber(),
      marketAnalysis: this.generateMarketAnalysis(data),
    };

    // Send to relevant clients with feature filtering
    for (const client of relevantClients) {
      try {
        // Filter message based on client's enabled features
        const filteredMessage = this.filterMessageForClient(priceMessage, client);
        
        // Check if client wants this timeframe
        if (!client.timeframes.has(data.timeframe)) {
          continue;
        }

        this.sendToClient(client.clientId, filteredMessage);
        
      } catch (error) {
        this.handleClientError(client.clientId, error as Error);
      }
    }

    this.emit('data_broadcast', {
      instrument,
      clientCount: relevantClients.length,
      timestamp: new Date(),
    });
  }

  /**
   * Filter message based on client capabilities and features
   */
  private filterMessageForClient(message: PriceStreamMessage, client: MarketDataClient): PriceStreamMessage {
    const filtered = { ...message };

    // Remove market analysis if not enabled
    if (!client.enabledFeatures.has('marketAnalysis')) {
      delete filtered.marketAnalysis;
    } else if (filtered.marketAnalysis) {
      // Filter specific analysis features
      if (!client.enabledFeatures.has('mlPredictions')) {
        delete filtered.marketAnalysis.mlPrediction;
      }
      if (!client.enabledFeatures.has('volatilityIndicators')) {
        delete filtered.marketAnalysis.volatility;
      }
    }

    return filtered;
  }

  /**
   * Generate market analysis based on market data (simplified implementation)
   */
  private generateMarketAnalysis(data: NormalizedMarketData): PriceStreamMessage['marketAnalysis'] | undefined {
    // This is a simplified implementation - in production, this would involve
    // more sophisticated technical analysis and ML predictions
    
    const priceChange = data.close.minus(data.open);
    const priceChangePercent = priceChange.dividedBy(data.open).times(100);
    
    const trend = priceChangePercent.greaterThan(0.1) ? 'bullish' : 
                  priceChangePercent.lessThan(-0.1) ? 'bearish' : 'neutral';
    
    const mood = priceChangePercent.greaterThan(0.5) ? 'positive' :
                 priceChangePercent.lessThan(-0.5) ? 'negative' : 'neutral';

    // Calculate simple volatility as percentage of high-low range
    const volatility = data.high.minus(data.low).dividedBy(data.close).times(100);

    return {
      trend,
      volatility: volatility.toNumber(),
      mood,
      mlPrediction: {
        direction: trend === 'bullish' ? 'up' : trend === 'bearish' ? 'down' : 'sideways',
        confidence: Math.random() * 100, // Mock confidence - would be from actual ML model
        timeHorizon: '1h',
      },
    };
  }

  /**
   * Send message to specific client
   */
  private sendToClient(clientId: string, message: WebSocketMessage): void {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      const messageStr = JSON.stringify(message);
      client.ws.send(messageStr);
      
      client.messagesSent++;
      client.dataTransferred += messageStr.length;
      this.performanceMetrics.totalMessagesSent++;
      this.performanceMetrics.totalDataTransferred += messageStr.length;

    } catch (error) {
      this.handleClientError(clientId, error as Error);
    }
  }

  /**
   * Send error message to client
   */
  private sendError(clientId: string, code: string, message: string): void {
    this.sendToClient(clientId, {
      type: 'error',
      code,
      message,
      timestamp: new Date(),
    });
  }

  /**
   * Handle client disconnect
   */
  private handleClientDisconnect(clientId: string, code: number, reason: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      this.clients.delete(clientId);
      this.performanceMetrics.activeConnections--;

      this.emit('client_disconnected', {
        clientId,
        userId: client.userId,
        code,
        reason,
        connectionDuration: Date.now() - client.connectionTime.getTime(),
        messagesExchanged: client.messagesSent + client.messagesReceived,
        timestamp: new Date(),
      });

      console.log(`Market data client ${clientId} disconnected: ${code} - ${reason}`);
    }
  }

  /**
   * Handle client error
   */
  private handleClientError(clientId: string, error: Error): void {
    this.performanceMetrics.errorCount++;
    this.performanceMetrics.lastErrorTime = new Date();

    this.emit('client_error', {
      clientId,
      error: error.message,
      timestamp: new Date(),
    });

    console.error(`Client ${clientId} error:`, error);
  }

  /**
   * Handle server error
   */
  private handleServerError(error: Error): void {
    this.emit('server_error', {
      error: error.message,
      timestamp: new Date(),
    });

    console.error('WebSocket server error:', error);
  }

  /**
   * Start heartbeat interval
   */
  private startHeartbeat(): void {
    this.heartbeatInterval = setInterval(() => {
      const now = new Date();
      const staleClients: string[] = [];

      this.clients.forEach((client, clientId) => {
        const timeSinceLastHeartbeat = now.getTime() - client.lastHeartbeat.getTime();
        
        if (timeSinceLastHeartbeat > this.config.clientTimeout) {
          staleClients.push(clientId);
        } else if (client.ws.readyState === WebSocket.OPEN) {
          // Send heartbeat
          this.sendToClient(clientId, {
            type: 'heartbeat',
            timestamp: now,
          });
        }
      });

      // Remove stale clients
      staleClients.forEach(clientId => {
        const client = this.clients.get(clientId);
        if (client) {
          client.ws.terminate();
          this.handleClientDisconnect(clientId, 1006, 'Heartbeat timeout');
        }
      });

    }, this.config.heartbeatInterval);
  }

  /**
   * Generate unique client ID
   */
  private generateClientId(userId: string): string {
    return `${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get connection statistics
   */
  public getConnectionStats(): {
    totalConnections: number;
    activeConnections: number;
    connectedInstruments: string[];
    performanceMetrics: typeof this.performanceMetrics;
    clientDetails: Array<{
      clientId: string;
      userId: string;
      subscribedInstruments: string[];
      enabledFeatures: string[];
      connectionDuration: number;
      messagesSent: number;
      messagesReceived: number;
    }>;
  } {
    const connectedInstruments = new Set<string>();
    const clientDetails = Array.from(this.clients.values()).map(client => {
      client.subscribedInstruments.forEach(instrument => {
        connectedInstruments.add(instrument);
      });

      return {
        clientId: client.clientId,
        userId: client.userId,
        subscribedInstruments: Array.from(client.subscribedInstruments),
        enabledFeatures: Array.from(client.enabledFeatures),
        connectionDuration: Date.now() - client.connectionTime.getTime(),
        messagesSent: client.messagesSent,
        messagesReceived: client.messagesReceived,
      };
    });

    return {
      totalConnections: this.performanceMetrics.totalConnections,
      activeConnections: this.performanceMetrics.activeConnections,
      connectedInstruments: Array.from(connectedInstruments),
      performanceMetrics: { ...this.performanceMetrics },
      clientDetails,
    };
  }

  /**
   * Health check for the WebSocket server
   */
  public healthCheck(): {
    isHealthy: boolean;
    issues: string[];
    stats: ReturnType<typeof this.getConnectionStats>;
  } {
    const issues: string[] = [];
    const stats = this.getConnectionStats();

    // Check for high error rate
    const errorRate = this.performanceMetrics.errorCount / Math.max(this.performanceMetrics.totalConnections, 1);
    if (errorRate > 0.1) {
      issues.push(`High error rate: ${(errorRate * 100).toFixed(2)}%`);
    }

    // Check for too many active connections
    if (this.performanceMetrics.activeConnections > this.config.maxConnections * 0.9) {
      issues.push(`Approaching connection limit: ${this.performanceMetrics.activeConnections}/${this.config.maxConnections}`);
    }

    // Check server uptime
    const uptime = Date.now() - this.performanceMetrics.startTime.getTime();
    if (uptime < 60000) { // Less than 1 minute
      issues.push('Server recently started - monitoring stability');
    }

    return {
      isHealthy: issues.length === 0,
      issues,
      stats,
    };
  }

  /**
   * Shutdown WebSocket server
   */
  public shutdown(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }

    // Close all client connections
    this.clients.forEach(client => {
      if (client.ws.readyState === WebSocket.OPEN) {
        client.ws.close(1001, 'Server shutting down');
      }
    });
    this.clients.clear();

    // Close server
    if (this.wss) {
      this.wss.close(() => {
        this.emit('server_shutdown', {
          timestamp: new Date(),
          finalStats: this.getConnectionStats(),
        });
      });
    }

    this.removeAllListeners();
    console.log('Market Data WebSocket server shut down');
  }
}