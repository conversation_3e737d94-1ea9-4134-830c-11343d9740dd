/**
 * Translated Metrics API Route
 * 
 * Provides translated metrics for strategies with user context
 */

import { Request, Response } from 'express';
import { z } from 'zod';
import {
  GetTranslatedMetricsRequest,
  GetTranslatedMetricsResponse,
  ApiResponse,
} from '@golddaddy/types';

import { MetricsTranslationService } from '../../services/metrics/MetricsTranslationService.js';

// Request validation schema
const GetTranslatedMetricsSchema = z.object({
  strategyId: z.string().min(1, 'Strategy ID is required'),
  metricTypes: z.array(z.enum([
    'win_rate',
    'profit_factor', 
    'sharpe_ratio',
    'max_drawdown',
    'total_return',
    'calmar_ratio',
    'sortino_ratio',
    'volatility',
    'trade_count',
    'avg_trade_return',
    'health_score'
  ] as const)).optional(),
  timeRange: z.object({
    start: z.string().transform(str => new Date(str)),
    end: z.string().transform(str => new Date(str)),
  }).optional(),
  includeComparison: z.boolean().optional().default(false),
  includeHistory: z.boolean().optional().default(false),
});

// User context validation schema
const UserContextSchema = z.object({
  experienceLevel: z.enum(['beginner', 'intermediate', 'advanced'] as const).default('intermediate'),
  riskTolerance: z.enum(['conservative', 'moderate', 'aggressive'] as const).default('moderate'),
});

// Initialize service
const metricsTranslationService = new MetricsTranslationService();

/**
 * GET /api/metrics/translated/:strategyId
 * Get translated metrics for a specific strategy
 */
export async function getTranslatedMetrics(req: Request, res: Response): Promise<void> {
  try {
    // Validate strategy ID from params
    const strategyId = req.params.strategyId;
    if (!strategyId) {
      res.status(400).json({
        success: false,
        error: 'Strategy ID is required',
      } as ApiResponse);
      return;
    }

    // Validate query parameters
    const queryValidation = GetTranslatedMetricsSchema.omit({ strategyId: true }).safeParse({
      metricTypes: req.query.metricTypes ? 
        (Array.isArray(req.query.metricTypes) ? req.query.metricTypes : 
         typeof req.query.metricTypes === 'string' ? req.query.metricTypes.split(',') : 
         [req.query.metricTypes]) : 
        undefined,
      timeRange: req.query.startDate && req.query.endDate ? {
        start: req.query.startDate as string,
        end: req.query.endDate as string,
      } : undefined,
      includeComparison: req.query.includeComparison === 'true',
      includeHistory: req.query.includeHistory === 'true',
    });

    if (!queryValidation.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid query parameters',
        message: queryValidation.error.errors?.map(e => e.message).join(', ') || 'Validation failed',
      } as ApiResponse);
      return;
    }

    // Get user context (in real implementation, get from JWT token)
    const userContextValidation = UserContextSchema.safeParse({
      experienceLevel: req.query.experienceLevel || req.headers['x-user-experience'],
      riskTolerance: req.query.riskTolerance || req.headers['x-user-risk-tolerance'],
    });

    if (!userContextValidation.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid user context',
        message: userContextValidation.error.errors?.map(e => e.message).join(', ') || 'Validation failed',
      } as ApiResponse);
      return;
    }

    // Build request object
    const request: GetTranslatedMetricsRequest = {
      strategyId,
      ...queryValidation.data,
    };

    // Get user ID from JWT token (mock for now)
    const userId = req.headers['x-user-id'] as string || 'mock-user-id';
    const userExperience = userContextValidation.data.experienceLevel;
    const userRiskTolerance = userContextValidation.data.riskTolerance;

    // Get translated metrics
    const result = await metricsTranslationService.getTranslatedMetrics(
      request,
      userId,
      userExperience,
      userRiskTolerance
    );

    // Return successful response
    res.status(200).json({
      success: true,
      data: result,
    } as ApiResponse<GetTranslatedMetricsResponse>);

  } catch (error) {
    console.error('Error getting translated metrics:', error);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    } as ApiResponse);
  }
}

/**
 * POST /api/metrics/translated/batch
 * Get translated metrics for multiple strategies
 */
export async function getBatchTranslatedMetrics(req: Request, res: Response): Promise<void> {
  try {
    // Validate request body
    const BatchRequestSchema = z.object({
      strategyIds: z.array(z.string()).min(1).max(10, 'Maximum 10 strategies per batch'),
      metricTypes: z.array(z.enum([
        'win_rate',
        'profit_factor',
        'sharpe_ratio', 
        'max_drawdown',
        'total_return',
        'calmar_ratio',
        'sortino_ratio',
        'volatility',
        'trade_count',
        'avg_trade_return',
        'health_score'
      ] as const)).optional(),
      timeRange: z.object({
        start: z.string().transform(str => new Date(str)),
        end: z.string().transform(str => new Date(str)),
      }).optional(),
    });

    const bodyValidation = BatchRequestSchema.safeParse(req.body);
    if (!bodyValidation.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid request body',
        message: bodyValidation.error.errors?.map(e => e.message).join(', ') || 'Validation failed',
      } as ApiResponse);
      return;
    }

    // Get user context
    const userContextValidation = UserContextSchema.safeParse({
      experienceLevel: req.headers['x-user-experience'],
      riskTolerance: req.headers['x-user-risk-tolerance'],
    });

    if (!userContextValidation.success) {
      res.status(400).json({
        success: false,
        error: 'Invalid user context',
        message: userContextValidation.error.errors?.map(e => e.message).join(', ') || 'Validation failed',
      } as ApiResponse);
      return;
    }

    const userId = req.headers['x-user-id'] as string || 'mock-user-id';
    const userExperience = userContextValidation.data.experienceLevel;
    const userRiskTolerance = userContextValidation.data.riskTolerance;

    // Process each strategy
    const results: GetTranslatedMetricsResponse[] = [];
    const errors: { strategyId: string; error: string }[] = [];

    for (const strategyId of bodyValidation.data.strategyIds) {
      try {
        const request: GetTranslatedMetricsRequest = {
          strategyId,
          metricTypes: bodyValidation.data.metricTypes,
          timeRange: bodyValidation.data.timeRange,
        };

        const result = await metricsTranslationService.getTranslatedMetrics(
          request,
          userId,
          userExperience,
          userRiskTolerance
        );

        results.push(result);
      } catch (error) {
        console.error(`Error processing strategy ${strategyId}:`, error);
        errors.push({
          strategyId,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Return results
    res.status(200).json({
      success: true,
      data: {
        results,
        errors: errors.length > 0 ? errors : undefined,
        processedCount: results.length,
        totalRequested: bodyValidation.data.strategyIds.length,
      },
    } as ApiResponse);

  } catch (error) {
    console.error('Error in batch translated metrics:', error);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    } as ApiResponse);
  }
}

/**
 * DELETE /api/metrics/translated/:strategyId/cache
 * Invalidate cache for a specific strategy
 */
export async function invalidateStrategyCache(req: Request, res: Response): Promise<void> {
  try {
    const strategyId = req.params.strategyId;
    if (!strategyId) {
      res.status(400).json({
        success: false,
        error: 'Strategy ID is required',
      } as ApiResponse);
      return;
    }

    // Invalidate cache
    metricsTranslationService.invalidateStrategyCache(strategyId);

    res.status(200).json({
      success: true,
      message: `Cache invalidated for strategy ${strategyId}`,
    } as ApiResponse);

  } catch (error) {
    console.error('Error invalidating cache:', error);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    } as ApiResponse);
  }
}

/**
 * GET /api/metrics/cache/stats
 * Get cache statistics
 */
export async function getCacheStats(req: Request, res: Response): Promise<void> {
  try {
    const stats = metricsTranslationService.getCacheStats();
    
    res.status(200).json({
      success: true,
      data: stats,
    } as ApiResponse);

  } catch (error) {
    console.error('Error getting cache stats:', error);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    } as ApiResponse);
  }
}

/**
 * POST /api/metrics/cache/clean
 * Clean expired cache entries
 */
export async function cleanExpiredCache(req: Request, res: Response): Promise<void> {
  try {
    metricsTranslationService.cleanExpiredCache();
    
    res.status(200).json({
      success: true,
      message: 'Expired cache entries cleaned',
    } as ApiResponse);

  } catch (error) {
    console.error('Error cleaning cache:', error);
    
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    } as ApiResponse);
  }
}

// Export route handlers
export const translatedMetricsRoutes = {
  getTranslatedMetrics,
  getBatchTranslatedMetrics,
  invalidateStrategyCache,
  getCacheStats,
  cleanExpiredCache,
};