/**
 * Security Headers and Content Security Policy Middleware
 * Comprehensive security hardening for GoldDaddy Trading Platform
 */

import { Request, Response, NextFunction } from 'express';

/**
 * Content Security Policy configuration
 */
export const CSP_POLICIES = {
  production: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'", // Required for Next.js
      "'unsafe-eval'",   // Required for development tools
      'https://vercel.live',
      'https://vitals.vercel-analytics.com',
      'https://www.google-analytics.com',
      'https://www.googletagmanager.com'
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'", // Required for styled-components and CSS-in-JS
      'https://fonts.googleapis.com'
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https:',
      'https://www.google-analytics.com',
      'https://avatars.githubusercontent.com'
    ],
    'font-src': [
      "'self'",
      'data:',
      'https://fonts.gstatic.com'
    ],
    'connect-src': [
      "'self'",
      'https://api.golddaddy.app',
      'wss://api.golddaddy.app',
      'https://vercel.live',
      'https://vitals.vercel-analytics.com',
      'https://www.google-analytics.com',
      'https://*.supabase.co',
      'wss://*.supabase.co'
    ],
    'media-src': [
      "'self'",
      'data:',
      'blob:'
    ],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"],
    'frame-src': ["'none'"],
    'manifest-src': ["'self'"],
    'worker-src': [
      "'self'",
      'blob:'
    ],
    'child-src': [
      "'self'",
      'blob:'
    ]
  },
  
  staging: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'",
      "'unsafe-eval'",
      'https://staging.golddaddy.app',
      'https://vercel.live'
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'",
      'https://fonts.googleapis.com'
    ],
    'img-src': [
      "'self'",
      'data:',
      'blob:',
      'https:'
    ],
    'font-src': [
      "'self'",
      'data:',
      'https://fonts.gstatic.com'
    ],
    'connect-src': [
      "'self'",
      'https://staging-api.golddaddy.app',
      'wss://staging-api.golddaddy.app',
      'https://vercel.live',
      'https://*.supabase.co',
      'wss://*.supabase.co'
    ],
    'media-src': ["'self'", 'data:', 'blob:'],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'none'"],
    'frame-src': ["'none'"],
    'manifest-src': ["'self'"],
    'worker-src': ["'self'", 'blob:'],
    'child-src': ["'self'", 'blob:']
  },
  
  development: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'",
      "'unsafe-eval'",
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002'
    ],
    'style-src': ["'self'", "'unsafe-inline'"],
    'img-src': ["'self'", 'data:', 'blob:', 'http:', 'https:'],
    'font-src': ["'self'", 'data:', 'http:', 'https:'],
    'connect-src': [
      "'self'",
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'ws://localhost:3000',
      'ws://localhost:3001',
      'ws://localhost:3002',
      'https://*.supabase.co',
      'wss://*.supabase.co'
    ],
    'media-src': ["'self'", 'data:', 'blob:'],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-ancestors': ["'self'"],
    'frame-src': ["'self'"],
    'manifest-src': ["'self'"],
    'worker-src': ["'self'", 'blob:'],
    'child-src': ["'self'", 'blob:']
  }
};

/**
 * Generate CSP header string from policy object
 */
function generateCSPString(policy: Record<string, string[]>): string {
  return Object.entries(policy)
    .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
    .join('; ');
}

/**
 * Security headers middleware
 * Applies comprehensive security headers based on environment
 */
export const securityHeadersMiddleware = (req: Request, res: Response, next: NextFunction): void => {
  const environment = process.env.NODE_ENV || 'development';
  const isProduction = environment === 'production';
  const isAPI = req.path.startsWith('/api/');
  
  // Content Security Policy
  const cspPolicy = CSP_POLICIES[environment as keyof typeof CSP_POLICIES] || CSP_POLICIES.development;
  res.setHeader('Content-Security-Policy', generateCSPString(cspPolicy));
  
  // Strict Transport Security (HTTPS only)
  if (isProduction) {
    res.setHeader(
      'Strict-Transport-Security',
      'max-age=31536000; includeSubDomains; preload'
    );
  }
  
  // X-Frame-Options - prevent clickjacking
  res.setHeader('X-Frame-Options', 'DENY');
  
  // X-Content-Type-Options - prevent MIME sniffing
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // X-XSS-Protection - enable XSS filtering
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Referrer Policy - control referrer information
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Permissions Policy - control browser features
  const permissionsPolicy = [
    'geolocation=()',
    'microphone=()',
    'camera=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'gyroscope=()',
    'accelerometer=()',
    'ambient-light-sensor=()'
  ];
  res.setHeader('Permissions-Policy', permissionsPolicy.join(', '));
  
  // Cross-Origin policies
  res.setHeader('Cross-Origin-Embedder-Policy', 'require-corp');
  res.setHeader('Cross-Origin-Opener-Policy', 'same-origin');
  res.setHeader('Cross-Origin-Resource-Policy', 'same-origin');
  
  // API-specific headers
  if (isAPI) {
    // Remove server information
    res.removeHeader('X-Powered-By');
    res.setHeader('Server', 'GoldDaddy-API');
    
    // Cache control for API responses
    res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, proxy-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
    
    // API versioning header
    res.setHeader('X-API-Version', '1.0');
    
    // Request ID for tracing
    const requestId = req.headers['x-request-id'] || generateRequestId();
    res.setHeader('X-Request-ID', requestId as string);
  }
  
  // Development-specific headers
  if (!isProduction) {
    res.setHeader('X-Environment', environment);
  }
  
  next();
};

/**
 * Strict security headers for sensitive endpoints
 */
export const strictSecurityHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // More restrictive CSP for sensitive endpoints
  const strictCSP = [
    "default-src 'self'",
    "script-src 'self'",
    "style-src 'self'",
    "img-src 'self' data:",
    "font-src 'self'",
    "connect-src 'self'",
    "media-src 'none'",
    "object-src 'none'",
    "base-uri 'self'",
    "form-action 'self'",
    "frame-ancestors 'none'",
    "frame-src 'none'"
  ].join('; ');
  
  res.setHeader('Content-Security-Policy', strictCSP);
  
  // Additional strict headers
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'no-referrer');
  
  // Disable caching completely
  res.setHeader('Cache-Control', 'no-store, no-cache, must-revalidate, private');
  res.setHeader('Pragma', 'no-cache');
  res.setHeader('Expires', '0');
  
  next();
};

/**
 * Generate unique request ID
 */
function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Security headers for static assets
 */
export const staticAssetHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // Cache control for static assets
  const isStaticAsset = /\.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$/i.test(req.path);
  
  if (isStaticAsset) {
    // Long cache for static assets with versioning
    res.setHeader('Cache-Control', 'public, max-age=31536000, immutable');
    
    // Security headers for static assets
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    
    // Cross-origin policy for assets
    res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
  }
  
  next();
};

/**
 * Remove sensitive headers from error responses
 */
export const sanitizeErrorHeaders = (req: Request, res: Response, next: NextFunction): void => {
  // Intercept error responses
  const originalSend = res.send;
  
  res.send = function (body: any) {
    // Remove potentially sensitive headers on errors
    if (res.statusCode >= 400) {
      res.removeHeader('X-Powered-By');
      res.removeHeader('Server');
      res.setHeader('Server', 'GoldDaddy');
      
      // Add security headers even on errors
      res.setHeader('X-Content-Type-Options', 'nosniff');
      res.setHeader('X-Frame-Options', 'DENY');
      res.setHeader('Cache-Control', 'no-store');
    }
    
    return originalSend.call(this, body);
  };
  
  next();
};

/**
 * Validate request headers for security
 */
export const validateRequestHeaders = (req: Request, res: Response, next: NextFunction): void => {
  const suspiciousHeaders = [
    'x-forwarded-host',
    'x-host',
    'x-cluster-client-ip',
    'x-real-ip'
  ];
  
  // Check for suspicious headers that might indicate header injection
  for (const header of suspiciousHeaders) {
    const value = req.headers[header];
    if (value && typeof value === 'string') {
      // Check for suspicious patterns
      if (/[<>"'()&]/.test(value)) {
        console.warn(`Suspicious header detected: ${header} = ${value}`, {
          ip: req.ip,
          userAgent: req.headers['user-agent']
        });
        
        res.status(400).json({
          error: 'Bad Request',
          message: 'Invalid request headers'
        });
        return;
      }
    }
  }
  
  // Validate Content-Type for POST/PUT requests
  if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
    const contentType = req.headers['content-type'];
    if (contentType && !isValidContentType(contentType)) {
      res.status(415).json({
        error: 'Unsupported Media Type',
        message: 'Content-Type not allowed'
      });
      return;
    }
  }
  
  next();
};

/**
 * Validate Content-Type header
 */
function isValidContentType(contentType: string): boolean {
  const allowedTypes = [
    'application/json',
    'application/x-www-form-urlencoded',
    'multipart/form-data',
    'text/plain',
    'application/octet-stream'
  ];
  
  return allowedTypes.some(type => contentType.toLowerCase().includes(type));
}

/**
 * Complete security middleware stack
 */
export const securityMiddlewareStack = [
  validateRequestHeaders,
  securityHeadersMiddleware,
  staticAssetHeaders,
  sanitizeErrorHeaders
];