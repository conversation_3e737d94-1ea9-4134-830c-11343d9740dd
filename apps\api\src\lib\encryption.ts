import crypto from 'crypto';

// AES-256-GCM encryption utilities for sensitive user data
// Used for encrypting trading capital, personal information, and other sensitive data

const ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16; // 128 bits
const TAG_LENGTH = 16; // 128 bits

// Get encryption key from environment variable
const getEncryptionKey = (): Buffer => {
  const key = process.env.ENCRYPTION_KEY;
  
  if (!key) {
    throw new Error('ENCRYPTION_KEY environment variable is required');
  }

  // If key is a hex string, convert to buffer
  if (key.length === 64) {
    return Buffer.from(key, 'hex');
  }

  // If key is a regular string, hash it to get consistent key
  return crypto.createHash('sha256').update(key).digest();
};

// Generate a new encryption key (for setup/rotation)
export const generateEncryptionKey = (): string => {
  return crypto.randomBytes(KEY_LENGTH).toString('hex');
};

// Encrypt sensitive data
export const encrypt = (text: string): string => {
  try {
    if (!text) {
      throw new Error('Text to encrypt cannot be empty');
    }

    const key = getEncryptionKey();
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipherGCM(ALGORITHM, key, iv);
    cipher.setAAD(Buffer.from('golddaddy-trading-platform', 'utf8')); // Additional authenticated data

    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    // Combine IV, auth tag, and encrypted data
    const combined = `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
    return combined;
  } catch (error) {
    console.error('Encryption error:', error);
    throw new Error('Failed to encrypt data');
  }
};

// Decrypt sensitive data
export const decrypt = (encryptedText: string): string => {
  try {
    if (!encryptedText) {
      throw new Error('Encrypted text cannot be empty');
    }

    const parts = encryptedText.split(':');
    if (parts.length !== 3) {
      throw new Error('Invalid encrypted data format');
    }

    const key = getEncryptionKey();
    const iv = Buffer.from(parts[0], 'hex');
    const authTag = Buffer.from(parts[1], 'hex');
    const encrypted = parts[2];

    const decipher = crypto.createDecipherGCM(ALGORITHM, key, iv);
    decipher.setAuthTag(authTag);
    decipher.setAAD(Buffer.from('golddaddy-trading-platform', 'utf8'));

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    throw new Error('Failed to decrypt data');
  }
};

// Hash sensitive data (one-way, for anonymization)
export const hashData = (data: string, salt?: string): string => {
  try {
    const actualSalt = salt || crypto.randomBytes(16).toString('hex');
    const hash = crypto.pbkdf2Sync(data, actualSalt, 100000, 64, 'sha512');
    return `${actualSalt}:${hash.toString('hex')}`;
  } catch (error) {
    console.error('Hashing error:', error);
    throw new Error('Failed to hash data');
  }
};

// Verify hashed data
export const verifyHash = (data: string, hashedData: string): boolean => {
  try {
    const parts = hashedData.split(':');
    if (parts.length !== 2) {
      return false;
    }

    const salt = parts[0];
    const hash = parts[1];
    const newHash = crypto.pbkdf2Sync(data, salt, 100000, 64, 'sha512').toString('hex');
    
    return hash === newHash;
  } catch (error) {
    console.error('Hash verification error:', error);
    return false;
  }
};

// Secure user ID anonymization for analytics
export const anonymizeUserId = (userId: string): string => {
  try {
    // Create a consistent anonymous ID based on user ID + app secret
    const secret = process.env.ANONYMIZATION_SECRET || 'default-secret';
    return crypto.createHmac('sha256', secret).update(userId).digest('hex').substring(0, 16);
  } catch (error) {
    console.error('User ID anonymization error:', error);
    throw new Error('Failed to anonymize user ID');
  }
};

// Encrypt financial amounts (trading capital, P&L, etc.)
export const encryptFinancialAmount = (amount: number): string => {
  return encrypt(amount.toString());
};

// Decrypt financial amounts
export const decryptFinancialAmount = (encryptedAmount: string): number => {
  const decrypted = decrypt(encryptedAmount);
  return parseFloat(decrypted);
};

// Utility to check if data appears to be encrypted
export const isEncrypted = (data: string): boolean => {
  // Check if data has the expected encrypted format (iv:tag:data)
  const parts = data.split(':');
  return parts.length === 3 && 
         parts[0].length === IV_LENGTH * 2 && // IV in hex
         parts[1].length === TAG_LENGTH * 2; // Auth tag in hex
};

// Data retention utilities
export const createDataRetentionHash = (data: unknown): string => {
  // Create a hash of data for compliance tracking
  const dataString = JSON.stringify(data);
  return crypto.createHash('sha256').update(dataString).digest('hex');
};

// Secure token generation for various purposes
export const generateSecureToken = (length: number = 32): string => {
  return crypto.randomBytes(length).toString('hex');
};

// Mask sensitive data for logging
export const maskSensitiveData = (data: string, visibleChars: number = 4): string => {
  if (data.length <= visibleChars * 2) {
    return '*'.repeat(data.length);
  }
  
  const start = data.substring(0, visibleChars);
  const end = data.substring(data.length - visibleChars);
  const middle = '*'.repeat(data.length - visibleChars * 2);
  
  return start + middle + end;
};

// Encryption configuration validation
export const validateEncryptionConfig = (): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  try {
    getEncryptionKey();
  } catch (error) {
    errors.push('ENCRYPTION_KEY environment variable is missing or invalid');
  }
  
  if (!process.env.ANONYMIZATION_SECRET) {
    errors.push('ANONYMIZATION_SECRET environment variable is missing');
  }
  
  return {
    valid: errors.length === 0,
    errors,
  };
};