-- CreateTable
CREATE TABLE "paper_trading_sessions" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "start_date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "end_date" TIMESTAMP(3),
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "min_required_days" INTEGER NOT NULL DEFAULT 30,
    "min_required_trades" INTEGER NOT NULL DEFAULT 50,
    "current_days" INTEGER NOT NULL DEFAULT 0,
    "current_trades" INTEGER NOT NULL DEFAULT 0,
    "total_return" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "total_return_percentage" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "win_rate" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "profit_factor" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "sharpe_ratio" DECIMAL(15,8),
    "max_drawdown" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "performance_thresholds" JSONB NOT NULL DEFAULT '{"winRate":{"current":0,"required":60,"met":false},"riskManagement":{"current":0,"required":75,"met":false},"consistency":{"current":0,"required":70,"met":false}}',
    "graduation_eligible" BOOLEAN NOT NULL DEFAULT false,
    "graduated_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "paper_trading_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "virtual_portfolios" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,
    "initial_balance" DECIMAL(15,8) NOT NULL,
    "current_balance" DECIMAL(15,8) NOT NULL,
    "available_margin" DECIMAL(15,8) NOT NULL,
    "used_margin" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "unrealized_pnl" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "realized_pnl" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "total_pnl" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "positions" JSONB NOT NULL DEFAULT '[]',
    "daily_pnl" DECIMAL(15,8)[] DEFAULT ARRAY[]::DECIMAL(15,8)[],
    "risk_metrics" JSONB NOT NULL DEFAULT '{"maxDrawdown":0,"currentDrawdown":0,"riskPercentage":0,"portfolioVar":0}',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "virtual_portfolios_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "paper_trades" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,
    "strategy_id" TEXT,
    "goal_id" TEXT,
    "account_id" TEXT NOT NULL,
    "symbol" TEXT NOT NULL,
    "type" "TradeType" NOT NULL,
    "volume" DECIMAL(15,8) NOT NULL,
    "open_price" DECIMAL(15,8) NOT NULL,
    "close_price" DECIMAL(15,8),
    "stop_loss" DECIMAL(15,8),
    "take_profit" DECIMAL(15,8),
    "profit" DECIMAL(15,8),
    "commission" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "swap" DECIMAL(15,8) NOT NULL DEFAULT 0,
    "open_time" TIMESTAMP(3) NOT NULL,
    "close_time" TIMESTAMP(3),
    "status" "TradeStatus" NOT NULL DEFAULT 'PENDING',
    "simulation_metadata" JSONB NOT NULL,
    "portfolio_impact" JSONB NOT NULL,
    "learning_metadata" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "paper_trades_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "paper_trading_analytics" (
    "id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,
    "backtesting_comparison" JSONB NOT NULL,
    "performance_insights" JSONB NOT NULL,
    "risk_analysis" JSONB NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "paper_trading_analytics_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "paper_trading_sessions_user_id_idx" ON "paper_trading_sessions"("user_id");

-- CreateIndex
CREATE INDEX "paper_trading_sessions_is_active_idx" ON "paper_trading_sessions"("is_active");

-- CreateIndex
CREATE INDEX "paper_trading_sessions_graduation_eligible_idx" ON "paper_trading_sessions"("graduation_eligible");

-- CreateIndex
CREATE INDEX "paper_trading_sessions_created_at_idx" ON "paper_trading_sessions"("created_at");

-- CreateIndex
CREATE INDEX "virtual_portfolios_user_id_idx" ON "virtual_portfolios"("user_id");

-- CreateIndex
CREATE INDEX "virtual_portfolios_session_id_idx" ON "virtual_portfolios"("session_id");

-- CreateIndex
CREATE INDEX "virtual_portfolios_created_at_idx" ON "virtual_portfolios"("created_at");

-- CreateIndex
CREATE INDEX "paper_trades_user_id_idx" ON "paper_trades"("user_id");

-- CreateIndex
CREATE INDEX "paper_trades_session_id_idx" ON "paper_trades"("session_id");

-- CreateIndex
CREATE INDEX "paper_trades_strategy_id_idx" ON "paper_trades"("strategy_id");

-- CreateIndex
CREATE INDEX "paper_trades_goal_id_idx" ON "paper_trades"("goal_id");

-- CreateIndex
CREATE INDEX "paper_trades_status_idx" ON "paper_trades"("status");

-- CreateIndex
CREATE INDEX "paper_trades_open_time_idx" ON "paper_trades"("open_time");

-- CreateIndex
CREATE INDEX "paper_trades_symbol_idx" ON "paper_trades"("symbol");

-- CreateIndex
CREATE INDEX "paper_trading_analytics_session_id_idx" ON "paper_trading_analytics"("session_id");

-- CreateIndex
CREATE INDEX "paper_trading_analytics_created_at_idx" ON "paper_trading_analytics"("created_at");

-- AddForeignKey
ALTER TABLE "paper_trading_sessions" ADD CONSTRAINT "paper_trading_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "virtual_portfolios" ADD CONSTRAINT "virtual_portfolios_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "virtual_portfolios" ADD CONSTRAINT "virtual_portfolios_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "paper_trading_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "paper_trades" ADD CONSTRAINT "paper_trades_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "paper_trades" ADD CONSTRAINT "paper_trades_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "paper_trading_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "paper_trades" ADD CONSTRAINT "paper_trades_strategy_id_fkey" FOREIGN KEY ("strategy_id") REFERENCES "strategies"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "paper_trades" ADD CONSTRAINT "paper_trades_goal_id_fkey" FOREIGN KEY ("goal_id") REFERENCES "trading_goals"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "paper_trading_analytics" ADD CONSTRAINT "paper_trading_analytics_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "paper_trading_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Create Row Level Security (RLS) policies
-- Enable RLS on paper trading tables
ALTER TABLE paper_trading_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE virtual_portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE paper_trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE paper_trading_analytics ENABLE ROW LEVEL SECURITY;

-- RLS policies for paper_trading_sessions
CREATE POLICY "Users can only access their own paper trading sessions"
ON paper_trading_sessions FOR ALL
USING (auth.uid()::text = user_id);

-- RLS policies for virtual_portfolios
CREATE POLICY "Users can only access their own virtual portfolios"
ON virtual_portfolios FOR ALL
USING (auth.uid()::text = user_id);

-- RLS policies for paper_trades
CREATE POLICY "Users can only access their own paper trades"
ON paper_trades FOR ALL
USING (auth.uid()::text = user_id);

-- RLS policies for paper_trading_analytics
CREATE POLICY "Users can only access their own paper trading analytics"
ON paper_trading_analytics FOR ALL
USING (EXISTS (
    SELECT 1 FROM paper_trading_sessions pts 
    WHERE pts.id = paper_trading_analytics.session_id 
    AND pts.user_id = auth.uid()::text
));