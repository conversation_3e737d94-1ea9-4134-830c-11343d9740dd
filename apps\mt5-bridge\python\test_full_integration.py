#!/usr/bin/env python3
"""
Full MT5 Integration Test
Test the complete MT5 Bridge Service with live connection
"""

import asyncio
import aiohttp
import json
import sys
from datetime import datetime, timedelta
import time

class MT5BridgeServiceTester:
    """Test the MT5 Bridge Service integration"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session = None
        
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_health_check(self):
        """Test basic health check"""
        print("\n=== Testing Health Check ===")
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"SUCCESS: Health check passed")
                    print(f"   Status: {data.get('status')}")
                    print(f"   MT5 Connected: {data.get('mt5_connected')}")
                    print(f"   Timestamp: {data.get('timestamp')}")
                    return True
                else:
                    print(f"ERROR: Health check failed with status {response.status}")
                    return False
        except Exception as e:
            print(f"ERROR: Health check exception: {e}")
            return False
    
    async def test_mt5_status(self):
        """Test MT5 connection status"""
        print("\n=== Testing MT5 Status ===")
        try:
            async with self.session.get(f"{self.base_url}/mt5/status") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"SUCCESS: MT5 status retrieved")
                    print(f"   Connected: {data.get('connected')}")
                    if data.get('account_info'):
                        acc = data['account_info']
                        print(f"   Server: {acc.get('server')}")
                        print(f"   Account: {acc.get('login')}")
                        print(f"   Balance: {acc.get('balance')}")
                    return True
                else:
                    print(f"ERROR: MT5 status failed with status {response.status}")
                    return False
        except Exception as e:
            print(f"ERROR: MT5 status exception: {e}")
            return False
    
    async def test_historical_data(self):
        """Test historical data collection"""
        print("\n=== Testing Historical Data Collection ===")
        try:
            # Test data collection request
            params = {
                "symbol": "EURUSD",
                "timeframe": "H1",
                "start_date": (datetime.now() - timedelta(days=7)).isoformat(),
                "end_date": datetime.now().isoformat(),
                "count": 100
            }
            
            async with self.session.post(f"{self.base_url}/historical/collect", json=params) as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"SUCCESS: Historical data collection initiated")
                    print(f"   Job ID: {data.get('job_id')}")
                    print(f"   Symbol: {data.get('symbol')}")
                    print(f"   Expected Records: {data.get('expected_records')}")
                    return True
                else:
                    text = await response.text()
                    print(f"ERROR: Historical data collection failed: {response.status}")
                    print(f"   Response: {text}")
                    return False
        except Exception as e:
            print(f"ERROR: Historical data exception: {e}")
            return False
    
    async def test_current_prices(self):
        """Test current price retrieval"""
        print("\n=== Testing Current Prices ===")
        try:
            symbols = ["EURUSD", "GBPUSD", "USDJPY"]
            
            for symbol in symbols:
                async with self.session.get(f"{self.base_url}/market-data/current/{symbol}") as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get('success'):
                            price_data = data.get('data', {})
                            print(f"SUCCESS: {symbol} current price")
                            print(f"   Bid: {price_data.get('bid')}")
                            print(f"   Ask: {price_data.get('ask')}")
                            print(f"   Spread: {price_data.get('spread')}")
                            print(f"   Time: {price_data.get('time')}")
                        else:
                            print(f"ERROR: {symbol} price request failed: {data.get('message')}")
                            return False
                    else:
                        print(f"ERROR: {symbol} price failed with status {response.status}")
                        return False
            
            return True
        except Exception as e:
            print(f"ERROR: Current prices exception: {e}")
            return False
    
    async def test_paper_trading(self):
        """Test paper trading functionality"""
        print("\n=== Testing Paper Trading ===")
        try:
            # Create a test trade
            trade_request = {
                "symbol": "EURUSD",
                "order_type": "buy",
                "volume": 0.1,
                "stop_loss": 1.1650,
                "take_profit": 1.1750,
                "comment": "Test trade from integration test"
            }
            
            async with self.session.post(f"{self.base_url}/paper-trading/trade", json=trade_request) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        trade_data = data.get('trade', {})
                        print(f"SUCCESS: Paper trade executed")
                        print(f"   Trade ID: {trade_data.get('id')}")
                        print(f"   Symbol: {trade_data.get('symbol')}")
                        print(f"   Type: {trade_data.get('order_type')}")
                        print(f"   Volume: {trade_data.get('volume')}")
                        print(f"   Entry Price: {trade_data.get('entry_price')}")
                        return True
                    else:
                        print(f"ERROR: Paper trading failed: {data.get('message')}")
                        return False
                else:
                    text = await response.text()
                    print(f"ERROR: Paper trading request failed: {response.status}")
                    print(f"   Response: {text}")
                    return False
        except Exception as e:
            print(f"ERROR: Paper trading exception: {e}")
            return False
    
    async def test_data_validation(self):
        """Test data validation functionality"""
        print("\n=== Testing Data Validation ===")
        try:
            # Test data integrity validation
            params = {
                "instrument": "EURUSD",
                "timeframe": "H1",
                "start_date": (datetime.now() - timedelta(days=1)).isoformat(),
                "end_date": datetime.now().isoformat()
            }
            
            async with self.session.post(f"{self.base_url}/data-integrity/validate", json=params) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        integrity_data = data.get('integrity_report', {})
                        print(f"SUCCESS: Data validation completed")
                        print(f"   Integrity Score: {integrity_data.get('integrity_score', 'N/A')}")
                        print(f"   Records Checked: {integrity_data.get('total_records', 'N/A')}")
                        print(f"   Issues Found: {integrity_data.get('total_issues', 'N/A')}")
                        return True
                    else:
                        print(f"ERROR: Data validation failed: {data.get('message')}")
                        return False
                else:
                    text = await response.text()
                    print(f"ERROR: Data validation request failed: {response.status}")
                    print(f"   Response: {text}")
                    return False
        except Exception as e:
            print(f"ERROR: Data validation exception: {e}")
            return False
    
    async def test_performance_monitoring(self):
        """Test performance monitoring"""
        print("\n=== Testing Performance Monitoring ===")
        try:
            async with self.session.get(f"{self.base_url}/performance/summary") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        perf_data = data.get('performance_summary', {})
                        print(f"SUCCESS: Performance monitoring active")
                        print(f"   Monitoring Active: {perf_data.get('monitoring_active')}")
                        print(f"   Total Operations: {perf_data.get('total_operations', 'N/A')}")
                        print(f"   Avg Response Time: {perf_data.get('average_response_time_ms', 'N/A')} ms")
                        return True
                    else:
                        print(f"ERROR: Performance monitoring failed: {data.get('message')}")
                        return False
                else:
                    print(f"ERROR: Performance monitoring request failed: {response.status}")
                    return False
        except Exception as e:
            print(f"ERROR: Performance monitoring exception: {e}")
            return False
    
    async def test_security_stats(self):
        """Test security statistics"""
        print("\n=== Testing Security Statistics ===")
        try:
            async with self.session.get(f"{self.base_url}/security/stats") as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get('success'):
                        security_data = data.get('security_stats', {})
                        print(f"SUCCESS: Security monitoring active")
                        print(f"   Requests Blocked: {security_data.get('requests_blocked', 0)}")
                        print(f"   Rate Limits Triggered: {security_data.get('rate_limits_triggered', 0)}")
                        print(f"   Invalid Auth Attempts: {security_data.get('invalid_auth_attempts', 0)}")
                        return True
                    else:
                        print(f"ERROR: Security stats failed: {data.get('message')}")
                        return False
                else:
                    print(f"ERROR: Security stats request failed: {response.status}")
                    return False
        except Exception as e:
            print(f"ERROR: Security stats exception: {e}")
            return False
    
    async def run_all_tests(self):
        """Run all integration tests"""
        print("Starting Full MT5 Bridge Service Integration Tests")
        print("=" * 60)
        
        tests = [
            ("Health Check", self.test_health_check),
            ("MT5 Status", self.test_mt5_status),
            ("Current Prices", self.test_current_prices),
            ("Historical Data", self.test_historical_data),
            ("Paper Trading", self.test_paper_trading),
            ("Data Validation", self.test_data_validation),
            ("Performance Monitoring", self.test_performance_monitoring),
            ("Security Statistics", self.test_security_stats)
        ]
        
        results = []
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"ERROR: {test_name} failed with exception: {e}")
                results.append((test_name, False))
        
        # Summary
        print("\n" + "=" * 60)
        print("INTEGRATION TEST SUMMARY")
        print("=" * 60)
        
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        print("\nDetailed Results:")
        for test_name, result in results:
            status = "PASS" if result else "FAIL"
            print(f"  {test_name:<25}: {status}")
        
        if passed == total:
            print("\n>>> ALL INTEGRATION TESTS PASSED! <<<")
            print(">>> MT5 Bridge Service is fully operational! <<<")
        else:
            print(f"\n>>> {total-passed} TESTS FAILED <<<")
            print(">>> Check service configuration and try again <<<")
        
        return passed == total

async def main():
    """Main test runner"""
    print("Full MT5 Bridge Service Integration Test")
    print("Make sure the MT5 Bridge Service is running on http://localhost:8000")
    print("Press Ctrl+C to cancel...\n")
    
    try:
        async with MT5BridgeServiceTester() as tester:
            success = await tester.run_all_tests()
            return success
    except KeyboardInterrupt:
        print("\nTest cancelled by user")
        return False
    except Exception as e:
        print(f"Test runner failed: {e}")
        return False

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nTest interrupted")
        sys.exit(1)