import { Router, Request, Response } from 'express';
import { z } from 'zod';
import { supabase } from '@golddaddy/config/src/database';
import { userOperations } from '../../lib/database';
import { auditLogger } from '../../lib/audit';
import { validateEmail } from '../../lib/auth';

const router = Router();

// Sign-in request validation schema
const SignInSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(1, 'Password is required'),
});

// POST /api/auth/signin - User authentication
router.post('/signin', async (req: Request, res: Response) => {
  try {
    // Validate request body
    const validationResult = SignInSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid sign-in data',
          details: validationResult.error.errors,
          timestamp: new Date().toISOString(),
        },
      });
    }

    const { email, password } = validationResult.data;

    // Additional email validation
    if (!validateEmail(email)) {
      return res.status(400).json({
        error: {
          code: 'INVALID_EMAIL',
          message: 'Invalid email format',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Authenticate with Supabase
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (authError || !authData.user || !authData.session) {
      // Log failed authentication attempt
      if (authData.user) {
        await auditLogger.logAuthentication(authData.user.id, req, false);
      }

      return res.status(401).json({
        error: {
          code: 'AUTHENTICATION_FAILED',
          message: authError?.message || 'Invalid email or password',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Get user profile from database
    try {
      const userProfile = await userOperations.findById(authData.user.id);
      
      if (!userProfile) {
        return res.status(404).json({
          error: {
            code: 'USER_PROFILE_NOT_FOUND',
            message: 'User profile not found',
            timestamp: new Date().toISOString(),
          },
        });
      }

      // Check if user account is soft deleted
      if (userProfile.deletedAt) {
        return res.status(403).json({
          error: {
            code: 'ACCOUNT_DELETED',
            message: 'This account has been deleted',
            timestamp: new Date().toISOString(),
          },
        });
      }

      // Log successful authentication
      await auditLogger.logAuthentication(authData.user.id, req, true);

      // Return success response with user profile and session
      res.status(200).json({
        data: {
          user: {
            id: userProfile.id,
            email: userProfile.email,
            displayName: userProfile.displayName,
            experienceLevel: userProfile.experienceLevel,
            riskTolerance: userProfile.riskTolerance,
            coachingStyle: userProfile.coachingStyle,
            notifications: userProfile.notifications,
            autoOptimization: userProfile.autoOptimization,
            paperTradingOnly: userProfile.paperTradingOnly,
          },
          session: {
            access_token: authData.session.access_token,
            refresh_token: authData.session.refresh_token,
            expires_at: authData.session.expires_at,
            token_type: authData.session.token_type,
          },
          featureFlags: userProfile.featureFlags,
          confidenceAssessment: userProfile.confidenceAssessment,
        },
        status: 'success',
        message: 'Authentication successful',
        timestamp: new Date().toISOString(),
      });

    } catch (dbError) {
      console.error('Database error during sign-in:', dbError);
      return res.status(500).json({
        error: {
          code: 'PROFILE_FETCH_FAILED',
          message: 'Failed to retrieve user profile',
          timestamp: new Date().toISOString(),
        },
      });
    }

  } catch (error) {
    console.error('Sign-in error:', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error during authentication',
        timestamp: new Date().toISOString(),
      },
    });
  }
});

// POST /api/auth/refresh - Refresh access token
router.post('/refresh', async (req: Request, res: Response) => {
  try {
    const { refresh_token } = req.body;

    if (!refresh_token) {
      return res.status(400).json({
        error: {
          code: 'MISSING_REFRESH_TOKEN',
          message: 'Refresh token is required',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Refresh session with Supabase
    const { data: authData, error: authError } = await supabase.auth.refreshSession({
      refresh_token,
    });

    if (authError || !authData.session) {
      return res.status(401).json({
        error: {
          code: 'TOKEN_REFRESH_FAILED',
          message: authError?.message || 'Failed to refresh token',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Log token refresh
    if (authData.user) {
      await auditLogger.logAuditEvent({
        userId: authData.user.id,
        action: 'token_refresh',
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });
    }

    res.status(200).json({
      data: {
        session: {
          access_token: authData.session.access_token,
          refresh_token: authData.session.refresh_token,
          expires_at: authData.session.expires_at,
          token_type: authData.session.token_type,
        },
      },
      status: 'success',
      message: 'Token refreshed successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Token refresh error:', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Internal server error during token refresh',
        timestamp: new Date().toISOString(),
      },
    });
  }
});

export default router;