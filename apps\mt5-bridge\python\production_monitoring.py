"""
Production Resource Usage Monitoring and Alerting
Advanced system monitoring with predictive alerting and resource optimization
"""

import asyncio
import time
import psutil
import docker
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from collections import deque, defaultdict
import json
import numpy as np
from loguru import logger
import threading
from pathlib import Path

try:
    import prometheus_client
    from prometheus_client import CollectorRegistry, Gauge, Counter, Histogram, generate_latest
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    logger.warning("Prometheus client not available, using internal metrics only")

@dataclass
class ResourceThresholds:
    """Resource monitoring thresholds"""
    cpu_warning: float = 70.0       # CPU % warning level
    cpu_critical: float = 85.0      # CPU % critical level
    memory_warning: float = 75.0    # Memory % warning level
    memory_critical: float = 90.0   # Memory % critical level
    disk_warning: float = 80.0      # Disk % warning level
    disk_critical: float = 90.0     # Disk % critical level
    network_warning: int = 100      # Network MB/s warning level
    network_critical: int = 200     # Network MB/s critical level
    load_warning: float = 2.0       # Load average warning level
    load_critical: float = 4.0      # Load average critical level
    
@dataclass
class ResourceMetrics:
    """Current resource usage metrics"""
    timestamp: datetime
    
    # CPU metrics
    cpu_percent: float
    cpu_count: int
    load_average: Tuple[float, float, float]
    cpu_freq: Optional[float]
    
    # Memory metrics
    memory_total: int
    memory_available: int
    memory_percent: float
    memory_used: int
    swap_total: int
    swap_used: int
    swap_percent: float
    
    # Disk metrics
    disk_total: int
    disk_used: int
    disk_free: int
    disk_percent: float
    disk_read_bytes: int
    disk_write_bytes: int
    disk_read_iops: int
    disk_write_iops: int
    
    # Network metrics
    network_bytes_sent: int
    network_bytes_recv: int
    network_packets_sent: int
    network_packets_recv: int
    
    # Process metrics
    process_count: int
    thread_count: int
    fd_count: Optional[int]
    
    # System metrics
    uptime: float
    boot_time: float

@dataclass
class ContainerMetrics:
    """Docker container metrics"""
    container_id: str
    container_name: str
    status: str
    cpu_percent: float
    memory_usage: int
    memory_limit: int
    memory_percent: float
    network_rx: int
    network_tx: int
    block_read: int
    block_write: int

@dataclass  
class AlertRule:
    """Resource monitoring alert rule"""
    name: str
    metric_path: str
    threshold: float
    comparison: str  # "gt", "lt", "gte", "lte"
    duration: int    # seconds condition must persist
    severity: str    # "warning", "critical"
    enabled: bool = True
    description: str = ""

class PrometheusMetrics:
    """Prometheus metrics collection"""
    
    def __init__(self, registry: Optional[CollectorRegistry] = None):
        self.registry = registry or CollectorRegistry()
        
        if not PROMETHEUS_AVAILABLE:
            logger.warning("Prometheus not available, skipping metrics setup")
            return
        
        # System metrics
        self.cpu_usage = Gauge('mt5_bridge_cpu_percent', 'CPU usage percentage', registry=self.registry)
        self.memory_usage = Gauge('mt5_bridge_memory_percent', 'Memory usage percentage', registry=self.registry)
        self.disk_usage = Gauge('mt5_bridge_disk_percent', 'Disk usage percentage', registry=self.registry)
        self.load_average = Gauge('mt5_bridge_load_average', 'System load average', ['period'], registry=self.registry)
        
        # Network metrics
        self.network_bytes_sent = Counter('mt5_bridge_network_bytes_sent_total', 'Network bytes sent', registry=self.registry)
        self.network_bytes_recv = Counter('mt5_bridge_network_bytes_recv_total', 'Network bytes received', registry=self.registry)
        
        # Process metrics
        self.process_count = Gauge('mt5_bridge_processes_total', 'Total number of processes', registry=self.registry)
        self.thread_count = Gauge('mt5_bridge_threads_total', 'Total number of threads', registry=self.registry)
        
        # Application metrics
        self.api_requests = Counter('mt5_bridge_api_requests_total', 'API requests', ['method', 'endpoint', 'status'], registry=self.registry)
        self.api_duration = Histogram('mt5_bridge_api_duration_seconds', 'API request duration', ['method', 'endpoint'], registry=self.registry)
        
        # Business metrics
        self.mt5_connections = Gauge('mt5_bridge_mt5_connections', 'MT5 connections', registry=self.registry)
        self.websocket_connections = Gauge('mt5_bridge_websocket_connections', 'WebSocket connections', registry=self.registry)
        self.trades_executed = Counter('mt5_bridge_trades_executed_total', 'Trades executed', ['type'], registry=self.registry)
    
    def update_system_metrics(self, metrics: ResourceMetrics):
        """Update Prometheus system metrics"""
        if not PROMETHEUS_AVAILABLE:
            return
            
        self.cpu_usage.set(metrics.cpu_percent)
        self.memory_usage.set(metrics.memory_percent)
        self.disk_usage.set(metrics.disk_percent)
        self.process_count.set(metrics.process_count)
        self.thread_count.set(metrics.thread_count)
        
        if metrics.load_average:
            self.load_average.labels(period='1m').set(metrics.load_average[0])
            self.load_average.labels(period='5m').set(metrics.load_average[1])
            self.load_average.labels(period='15m').set(metrics.load_average[2])
    
    def record_api_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record API request metrics"""
        if not PROMETHEUS_AVAILABLE:
            return
            
        self.api_requests.labels(method=method, endpoint=endpoint, status=str(status_code)).inc()
        self.api_duration.labels(method=method, endpoint=endpoint).observe(duration)
    
    def get_metrics(self) -> str:
        """Get Prometheus metrics in text format"""
        if not PROMETHEUS_AVAILABLE:
            return "# Prometheus not available"
        return generate_latest(self.registry).decode('utf-8')

class ProductionMonitor:
    """Production resource monitoring with predictive alerting"""
    
    def __init__(self, thresholds: Optional[ResourceThresholds] = None):
        self.thresholds = thresholds or ResourceThresholds()
        
        # Metrics storage
        self.metrics_history: deque = deque(maxlen=1440)  # 24 hours at 1-minute intervals
        self.container_metrics: Dict[str, ContainerMetrics] = {}
        
        # Alert rules
        self.alert_rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Dict[str, Any]] = {}
        
        # Monitoring state
        self.is_monitoring = False
        self.monitoring_interval = 60  # seconds
        self.monitoring_task: Optional[asyncio.Task] = None
        
        # Docker client
        self.docker_client = None
        try:
            self.docker_client = docker.from_env()
            logger.info("Docker client initialized for container monitoring")
        except Exception as e:
            logger.warning(f"Docker not available: {e}")
        
        # Prometheus metrics
        self.prometheus_metrics = PrometheusMetrics()
        
        # Trend analysis
        self.trend_window = 15  # minutes
        self.prediction_threshold = 0.8  # correlation threshold for predictions
        
        # Setup default alert rules
        self._setup_default_alert_rules()
    
    def _setup_default_alert_rules(self):
        """Setup default production alert rules"""
        rules = [
            AlertRule(
                name="high_cpu_sustained",
                metric_path="cpu_percent",
                threshold=self.thresholds.cpu_warning,
                comparison="gt",
                duration=300,  # 5 minutes
                severity="warning",
                description="CPU usage above warning threshold for sustained period"
            ),
            AlertRule(
                name="critical_cpu",
                metric_path="cpu_percent", 
                threshold=self.thresholds.cpu_critical,
                comparison="gt",
                duration=60,   # 1 minute
                severity="critical",
                description="CPU usage critically high"
            ),
            AlertRule(
                name="high_memory_sustained",
                metric_path="memory_percent",
                threshold=self.thresholds.memory_warning,
                comparison="gt",
                duration=300,
                severity="warning",
                description="Memory usage above warning threshold"
            ),
            AlertRule(
                name="critical_memory",
                metric_path="memory_percent",
                threshold=self.thresholds.memory_critical,
                comparison="gt",
                duration=120,  # 2 minutes
                severity="critical",
                description="Memory usage critically high"
            ),
            AlertRule(
                name="disk_space_warning",
                metric_path="disk_percent",
                threshold=self.thresholds.disk_warning,
                comparison="gt",
                duration=60,
                severity="warning",
                description="Disk usage above warning threshold"
            ),
            AlertRule(
                name="disk_space_critical",
                metric_path="disk_percent",
                threshold=self.thresholds.disk_critical,
                comparison="gt",
                duration=60,
                severity="critical",
                description="Disk space critically low"
            ),
            AlertRule(
                name="high_load_average",
                metric_path="load_average_1m",
                threshold=self.thresholds.load_warning,
                comparison="gt",
                duration=300,
                severity="warning",
                description="System load average high"
            ),
            AlertRule(
                name="low_memory_available",
                metric_path="memory_available_mb",
                threshold=1024,  # Less than 1GB available
                comparison="lt",
                duration=120,
                severity="warning",
                description="Low memory available"
            )
        ]
        
        for rule in rules:
            self.alert_rules[rule.name] = rule
    
    async def start_monitoring(self):
        """Start production resource monitoring"""
        if self.is_monitoring:
            logger.warning("Production monitoring already running")
            return
        
        logger.info("🔄 Starting production resource monitoring...")
        self.is_monitoring = True
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("✅ Production monitoring started")
    
    async def stop_monitoring(self):
        """Stop production resource monitoring"""
        if not self.is_monitoring:
            return
        
        logger.info("🔌 Stopping production monitoring...")
        self.is_monitoring = False
        
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        
        logger.info("✅ Production monitoring stopped")
    
    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                # Collect resource metrics
                metrics = await self._collect_resource_metrics()
                self.metrics_history.append(metrics)
                
                # Collect container metrics
                if self.docker_client:
                    await self._collect_container_metrics()
                
                # Update Prometheus metrics
                self.prometheus_metrics.update_system_metrics(metrics)
                
                # Check alert conditions
                await self._check_alert_conditions(metrics)
                
                # Perform trend analysis
                if len(self.metrics_history) > self.trend_window:
                    await self._analyze_trends()
                
                # Log monitoring summary
                self._log_monitoring_summary(metrics)
                
            except Exception as e:
                logger.error(f"Error in production monitoring loop: {e}")
            
            await asyncio.sleep(self.monitoring_interval)
    
    async def _collect_resource_metrics(self) -> ResourceMetrics:
        """Collect comprehensive resource metrics"""
        
        # CPU metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq().current if psutil.cpu_freq() else None
        
        try:
            load_avg = psutil.getloadavg()
        except (AttributeError, OSError):
            load_avg = (0.0, 0.0, 0.0)
        
        # Memory metrics
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()
        
        # Disk metrics
        disk_usage = psutil.disk_usage('/')
        disk_io = psutil.disk_io_counters()
        
        # Network metrics
        network_io = psutil.net_io_counters()
        
        # Process metrics
        process_count = len(psutil.pids())
        current_process = psutil.Process()
        thread_count = current_process.num_threads()
        
        # File descriptor count (Unix-like systems)
        fd_count = None
        try:
            fd_count = current_process.num_fds()
        except (AttributeError, psutil.NoSuchProcess):
            pass
        
        # System metrics
        boot_time = psutil.boot_time()
        uptime = time.time() - boot_time
        
        return ResourceMetrics(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            cpu_count=cpu_count,
            load_average=load_avg,
            cpu_freq=cpu_freq,
            memory_total=memory.total,
            memory_available=memory.available,
            memory_percent=memory.percent,
            memory_used=memory.used,
            swap_total=swap.total,
            swap_used=swap.used,
            swap_percent=swap.percent,
            disk_total=disk_usage.total,
            disk_used=disk_usage.used,
            disk_free=disk_usage.free,
            disk_percent=(disk_usage.used / disk_usage.total) * 100,
            disk_read_bytes=disk_io.read_bytes if disk_io else 0,
            disk_write_bytes=disk_io.write_bytes if disk_io else 0,
            disk_read_iops=disk_io.read_count if disk_io else 0,
            disk_write_iops=disk_io.write_count if disk_io else 0,
            network_bytes_sent=network_io.bytes_sent,
            network_bytes_recv=network_io.bytes_recv,
            network_packets_sent=network_io.packets_sent,
            network_packets_recv=network_io.packets_recv,
            process_count=process_count,
            thread_count=thread_count,
            fd_count=fd_count,
            uptime=uptime,
            boot_time=boot_time
        )
    
    async def _collect_container_metrics(self):
        """Collect Docker container metrics"""
        if not self.docker_client:
            return
        
        try:
            containers = self.docker_client.containers.list()
            
            for container in containers:
                try:
                    stats = container.stats(stream=False)
                    
                    # Calculate CPU percentage
                    cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                               stats['precpu_stats']['cpu_usage']['total_usage']
                    system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                                  stats['precpu_stats']['system_cpu_usage']
                    cpu_percent = (cpu_delta / system_delta) * 100.0 if system_delta > 0 else 0.0
                    
                    # Memory metrics
                    memory_usage = stats['memory_stats'].get('usage', 0)
                    memory_limit = stats['memory_stats'].get('limit', 0)
                    memory_percent = (memory_usage / memory_limit) * 100 if memory_limit > 0 else 0
                    
                    # Network metrics
                    network_rx = sum(net['rx_bytes'] for net in stats['networks'].values())
                    network_tx = sum(net['tx_bytes'] for net in stats['networks'].values())
                    
                    # Block I/O
                    block_read = sum(bio['value'] for bio in stats['blkio_stats']['io_service_bytes_recursive'] 
                                   if bio['op'] == 'Read')
                    block_write = sum(bio['value'] for bio in stats['blkio_stats']['io_service_bytes_recursive'] 
                                    if bio['op'] == 'Write')
                    
                    container_metrics = ContainerMetrics(
                        container_id=container.id[:12],
                        container_name=container.name,
                        status=container.status,
                        cpu_percent=cpu_percent,
                        memory_usage=memory_usage,
                        memory_limit=memory_limit,
                        memory_percent=memory_percent,
                        network_rx=network_rx,
                        network_tx=network_tx,
                        block_read=block_read,
                        block_write=block_write
                    )
                    
                    self.container_metrics[container.id] = container_metrics
                    
                except Exception as e:
                    logger.error(f"Failed to collect stats for container {container.name}: {e}")
                    
        except Exception as e:
            logger.error(f"Failed to collect container metrics: {e}")
    
    async def _check_alert_conditions(self, metrics: ResourceMetrics):
        """Check alert conditions and trigger alerts"""
        current_time = datetime.now()
        
        for rule_name, rule in self.alert_rules.items():
            if not rule.enabled:
                continue
            
            # Get metric value
            metric_value = self._get_metric_value(rule.metric_path, metrics)
            if metric_value is None:
                continue
            
            # Check condition
            condition_met = self._evaluate_condition(metric_value, rule)
            
            if condition_met:
                # Track alert
                if rule_name not in self.active_alerts:
                    self.active_alerts[rule_name] = {
                        'rule': rule,
                        'started_at': current_time,
                        'triggered': False,
                        'current_value': metric_value
                    }
                else:
                    self.active_alerts[rule_name]['current_value'] = metric_value
                
                # Check duration threshold
                alert_info = self.active_alerts[rule_name]
                duration = (current_time - alert_info['started_at']).total_seconds()
                
                if duration >= rule.duration and not alert_info['triggered']:
                    await self._trigger_alert(rule_name, alert_info, metric_value)
                    alert_info['triggered'] = True
            else:
                # Condition no longer met
                if rule_name in self.active_alerts:
                    alert_info = self.active_alerts[rule_name]
                    if alert_info['triggered']:
                        await self._resolve_alert(rule_name, alert_info, metric_value)
                    del self.active_alerts[rule_name]
    
    def _get_metric_value(self, metric_path: str, metrics: ResourceMetrics) -> Optional[float]:
        """Extract metric value from ResourceMetrics"""
        if metric_path == "load_average_1m":
            return metrics.load_average[0] if metrics.load_average else 0.0
        elif metric_path == "memory_available_mb":
            return metrics.memory_available / (1024 * 1024)
        else:
            return getattr(metrics, metric_path, None)
    
    def _evaluate_condition(self, value: float, rule: AlertRule) -> bool:
        """Evaluate alert condition"""
        if rule.comparison == "gt":
            return value > rule.threshold
        elif rule.comparison == "lt":
            return value < rule.threshold
        elif rule.comparison == "gte":
            return value >= rule.threshold
        elif rule.comparison == "lte":
            return value <= rule.threshold
        return False
    
    async def _trigger_alert(self, alert_name: str, alert_info: Dict[str, Any], current_value: float):
        """Trigger production alert"""
        rule = alert_info['rule']
        
        alert_data = {
            'alert_name': alert_name,
            'severity': rule.severity,
            'description': rule.description,
            'metric': rule.metric_path,
            'threshold': rule.threshold,
            'current_value': current_value,
            'started_at': alert_info['started_at'].isoformat(),
            'triggered_at': datetime.now().isoformat()
        }
        
        if rule.severity == "critical":
            logger.critical(f"🚨 PRODUCTION CRITICAL ALERT: {alert_name} - {rule.description} (Value: {current_value:.2f})")
        else:
            logger.warning(f"⚠️ PRODUCTION WARNING: {alert_name} - {rule.description} (Value: {current_value:.2f})")
    
    async def _resolve_alert(self, alert_name: str, alert_info: Dict[str, Any], current_value: float):
        """Resolve production alert"""
        rule = alert_info['rule']
        logger.info(f"✅ ALERT RESOLVED: {alert_name} - {rule.metric_path} = {current_value:.2f}")
    
    async def _analyze_trends(self):
        """Analyze resource usage trends for predictive alerts"""
        if len(self.metrics_history) < self.trend_window:
            return
        
        recent_metrics = list(self.metrics_history)[-self.trend_window:]
        
        # Analyze CPU trend
        cpu_values = [m.cpu_percent for m in recent_metrics]
        cpu_trend = self._calculate_trend(cpu_values)
        
        if cpu_trend > self.prediction_threshold and cpu_values[-1] > 60:
            logger.warning(f"🔮 PREDICTIVE ALERT: CPU usage trending upward (trend: {cpu_trend:.2f})")
        
        # Analyze memory trend
        memory_values = [m.memory_percent for m in recent_metrics]
        memory_trend = self._calculate_trend(memory_values)
        
        if memory_trend > self.prediction_threshold and memory_values[-1] > 60:
            logger.warning(f"🔮 PREDICTIVE ALERT: Memory usage trending upward (trend: {memory_trend:.2f})")
    
    def _calculate_trend(self, values: List[float]) -> float:
        """Calculate trend correlation coefficient"""
        if len(values) < 3:
            return 0.0
        
        x = np.arange(len(values))
        y = np.array(values)
        
        correlation = np.corrcoef(x, y)[0, 1]
        return correlation if not np.isnan(correlation) else 0.0
    
    def _log_monitoring_summary(self, metrics: ResourceMetrics):
        """Log production monitoring summary"""
        logger.info(
            f"📊 Production Metrics - "
            f"CPU: {metrics.cpu_percent:.1f}% | "
            f"Memory: {metrics.memory_percent:.1f}% | "
            f"Disk: {metrics.disk_percent:.1f}% | "
            f"Load: {metrics.load_average[0]:.2f} | "
            f"Processes: {metrics.process_count} | "
            f"Active Alerts: {len(self.active_alerts)}"
        )
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current resource metrics"""
        latest = self.metrics_history[-1] if self.metrics_history else None
        
        return {
            "system_metrics": asdict(latest) if latest else None,
            "container_metrics": {cid: asdict(cm) for cid, cm in self.container_metrics.items()},
            "active_alerts": len(self.active_alerts),
            "monitoring_status": self.is_monitoring,
            "last_check": latest.timestamp.isoformat() if latest else None
        }
    
    def get_metrics_history(self, hours_back: int = 1) -> List[Dict[str, Any]]:
        """Get metrics history"""
        cutoff_time = datetime.now() - timedelta(hours=hours_back)
        
        history = [
            asdict(m) for m in self.metrics_history 
            if m.timestamp >= cutoff_time
        ]
        
        return history
    
    def get_alert_status(self) -> Dict[str, Any]:
        """Get alert status"""
        return {
            "active_alerts": {
                name: {
                    "rule": asdict(info['rule']),
                    "started_at": info['started_at'].isoformat(),
                    "current_value": info['current_value'],
                    "triggered": info['triggered']
                }
                for name, info in self.active_alerts.items()
            },
            "total_rules": len(self.alert_rules),
            "enabled_rules": sum(1 for r in self.alert_rules.values() if r.enabled)
        }
    
    def get_prometheus_metrics(self) -> str:
        """Get Prometheus metrics"""
        return self.prometheus_metrics.get_metrics()

# Global production monitor instance
_production_monitor: Optional[ProductionMonitor] = None

def get_production_monitor(thresholds: Optional[ResourceThresholds] = None) -> ProductionMonitor:
    """Get global production monitor instance"""
    global _production_monitor
    if _production_monitor is None:
        _production_monitor = ProductionMonitor(thresholds)
    return _production_monitor