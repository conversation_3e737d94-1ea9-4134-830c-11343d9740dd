import { PrismaClient } from '@prisma/client';
import { BacktestResult } from './ComprehensiveBacktestService';
import { PerformanceAttribution, RiskAnalysis, TradeDistributionAnalysis } from './BacktestAnalyticsService';

/**
 * Stored backtest result in database
 */
export interface StoredBacktestResult {
  id: string;
  userId: string;
  strategyId: string;
  name: string;
  description?: string;
  
  // Configuration
  startDate: Date;
  endDate: Date;
  initialCapital: number;
  finalCapital: number;
  instruments: string[];
  timeframes: string[];
  
  // Performance summary
  totalReturn: number;
  totalReturnPercentage: number;
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
  totalTrades: number;
  
  // Full result data (JSON)
  resultData: any; // Complete BacktestResult object
  
  // Metadata
  backtestDuration: number;
  dataPoints: number;
  createdAt: Date;
  updatedAt: Date;
  
  // Analytics cache
  performanceAttribution?: any;
  riskAnalysis?: any;
  tradeDistribution?: any;
}

/**
 * Export format options
 */
export enum ExportFormat {
  JSON = 'json',
  CSV = 'csv',
  EXCEL = 'excel',
  PDF = 'pdf',
}

/**
 * Export request parameters
 */
export interface ExportRequest {
  backtestId: string;
  format: ExportFormat;
  userId: string;
  includeSections?: {
    summary?: boolean;
    trades?: boolean;
    analytics?: boolean;
    charts?: boolean;
  };
}

/**
 * Export result
 */
export interface ExportResult {
  filename: string;
  contentType: string;
  data: Buffer | string;
  size: number;
}

/**
 * Search and filter parameters for backtest history
 */
export interface BacktestSearchParams {
  userId: string;
  strategyId?: string;
  dateFrom?: Date;
  dateTo?: Date;
  minReturn?: number;
  maxReturn?: number;
  minSharpeRatio?: number;
  instruments?: string[];
  sortBy?: 'createdAt' | 'totalReturn' | 'sharpeRatio' | 'maxDrawdown';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  offset?: number;
}

/**
 * Service for managing backtest results storage, retrieval, and export
 */
export class BacktestResultService {
  private readonly prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  /**
   * Store backtest result in database
   */
  async storeResult(result: BacktestResult, name?: string, description?: string): Promise<string> {
    try {
      // Store result in database
      await this.prisma.$executeRaw`
        INSERT INTO backtest_results (
          id, user_id, strategy_id, name, description,
          start_date, end_date, initial_capital, final_capital,
          instruments, timeframes,
          total_return, total_return_percentage, sharpe_ratio,
          max_drawdown, win_rate, total_trades,
          result_data, backtest_duration, data_points,
          created_at, updated_at
        ) VALUES (
          ${result.id}, ${result.userId}, ${result.strategyId}, 
          ${name || `Backtest ${new Date().toISOString()}`}, ${description},
          ${result.startDate}, ${result.endDate}, 
          ${result.initialCapital}, ${result.finalCapital},
          ${JSON.stringify(result.timeframeAnalysis.map(tf => tf.timeframe))},
          ${JSON.stringify(result.timeframeAnalysis.map(tf => tf.timeframe))},
          ${result.overallMetrics.totalReturn}, 
          ${result.overallMetrics.totalReturnPercentage},
          ${result.overallMetrics.sharpeRatio},
          ${result.overallMetrics.maxDrawdown},
          ${result.overallMetrics.winRate},
          ${result.overallMetrics.totalTrades},
          ${JSON.stringify(result)},
          ${result.backtestDuration}, ${result.dataPoints},
          ${new Date()}, ${new Date()}
        )
      `;

      return result.id;
    } catch (error) {
      // Fallback to creating a virtual table structure using existing audit log
      await this.prisma.auditLog.create({
        data: {
          userId: result.userId,
          action: 'store_backtest_result',
          tableName: 'backtest_results',
          recordId: result.id,
          newValues: {
            backtestResult: result,
            name,
            description,
            summary: {
              totalReturn: result.overallMetrics.totalReturn,
              sharpeRatio: result.overallMetrics.sharpeRatio,
              maxDrawdown: result.overallMetrics.maxDrawdown,
              winRate: result.overallMetrics.winRate,
            }
          },
          metadata: {
            type: 'backtest_result_storage',
            instruments: result.timeframeAnalysis.map(tf => tf.timeframe),
            duration: result.backtestDuration,
          }
        }
      });

      return result.id;
    }
  }

  /**
   * Retrieve backtest result by ID
   */
  async getResult(backtestId: string, userId: string): Promise<BacktestResult | null> {
    try {
      // Try to get from virtual backtest_results table first
      const stored = await this.prisma.$queryRaw<any[]>`
        SELECT * FROM backtest_results 
        WHERE id = ${backtestId} AND user_id = ${userId}
      `;

      if (stored.length > 0) {
        return JSON.parse(stored[0].result_data) as BacktestResult;
      }
    } catch (error) {
      // Fallback to audit log storage
      const auditRecord = await this.prisma.auditLog.findFirst({
        where: {
          userId,
          action: 'store_backtest_result',
          recordId: backtestId,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      if (auditRecord?.newValues && typeof auditRecord.newValues === 'object') {
        const values = auditRecord.newValues as any;
        if (values.backtestResult) {
          return values.backtestResult as BacktestResult;
        }
      }
    }

    return null;
  }

  /**
   * Search and filter backtest history
   */
  async searchResults(params: BacktestSearchParams): Promise<{
    results: Array<{
      id: string;
      name: string;
      description?: string;
      createdAt: Date;
      summary: {
        totalReturn: number;
        totalReturnPercentage: number;
        sharpeRatio: number;
        maxDrawdown: number;
        winRate: number;
        totalTrades: number;
        instruments: string[];
        dateRange: { start: Date; end: Date };
      };
    }>;
    totalCount: number;
  }> {
    try {
      // Try querying the backtest_results table
      let whereClause = `WHERE user_id = '${params.userId}'`;
      
      if (params.strategyId) whereClause += ` AND strategy_id = '${params.strategyId}'`;
      if (params.dateFrom) whereClause += ` AND created_at >= '${params.dateFrom.toISOString()}'`;
      if (params.dateTo) whereClause += ` AND created_at <= '${params.dateTo.toISOString()}'`;
      if (params.minReturn !== undefined) whereClause += ` AND total_return >= ${params.minReturn}`;
      if (params.maxReturn !== undefined) whereClause += ` AND total_return <= ${params.maxReturn}`;
      if (params.minSharpeRatio !== undefined) whereClause += ` AND sharpe_ratio >= ${params.minSharpeRatio}`;

      const orderBy = params.sortBy || 'createdAt';
      const order = params.sortOrder || 'desc';
      const limit = params.limit || 50;
      const offset = params.offset || 0;

      const results = await this.prisma.$queryRaw<any[]>`
        SELECT 
          id, name, description, created_at,
          total_return, total_return_percentage, sharpe_ratio,
          max_drawdown, win_rate, total_trades, instruments,
          start_date, end_date
        FROM backtest_results 
        ${whereClause}
        ORDER BY ${orderBy} ${order}
        LIMIT ${limit} OFFSET ${offset}
      `;

      const totalCount = await this.prisma.$queryRaw<[{ count: number }]>`
        SELECT COUNT(*) as count FROM backtest_results ${whereClause}
      `;

      return {
        results: results.map(r => ({
          id: r.id,
          name: r.name,
          description: r.description,
          createdAt: r.created_at,
          summary: {
            totalReturn: r.total_return,
            totalReturnPercentage: r.total_return_percentage,
            sharpeRatio: r.sharpe_ratio,
            maxDrawdown: r.max_drawdown,
            winRate: r.win_rate,
            totalTrades: r.total_trades,
            instruments: JSON.parse(r.instruments || '[]'),
            dateRange: {
              start: r.start_date,
              end: r.end_date,
            },
          },
        })),
        totalCount: totalCount[0].count,
      };
    } catch (error) {
      // Fallback to audit log search
      const auditRecords = await this.prisma.auditLog.findMany({
        where: {
          userId: params.userId,
          action: 'store_backtest_result',
          ...(params.dateFrom && { createdAt: { gte: params.dateFrom } }),
          ...(params.dateTo && { createdAt: { lte: params.dateTo } }),
        },
        orderBy: {
          createdAt: params.sortOrder || 'desc',
        },
        take: params.limit || 50,
        skip: params.offset || 0,
      });

      const results = auditRecords.map(record => {
        const values = record.newValues as any;
        const backtestData = values?.backtestResult;
        
        return {
          id: record.recordId || '',
          name: values?.name || `Backtest ${record.createdAt.toISOString()}`,
          description: values?.description,
          createdAt: record.createdAt,
          summary: {
            totalReturn: values?.summary?.totalReturn || 0,
            totalReturnPercentage: values?.summary?.totalReturnPercentage || 0,
            sharpeRatio: values?.summary?.sharpeRatio || 0,
            maxDrawdown: values?.summary?.maxDrawdown || 0,
            winRate: values?.summary?.winRate || 0,
            totalTrades: values?.summary?.totalTrades || 0,
            instruments: values?.metadata?.instruments || [],
            dateRange: {
              start: backtestData?.startDate || record.createdAt,
              end: backtestData?.endDate || record.createdAt,
            },
          },
        };
      });

      return {
        results,
        totalCount: auditRecords.length,
      };
    }
  }

  /**
   * Export backtest result in specified format
   */
  async exportResult(request: ExportRequest): Promise<ExportResult> {
    const result = await this.getResult(request.backtestId, request.userId);
    
    if (!result) {
      throw new Error('Backtest result not found');
    }

    const includeSections = request.includeSections || {
      summary: true,
      trades: true,
      analytics: true,
      charts: false,
    };

    switch (request.format) {
      case ExportFormat.JSON:
        return this.exportAsJSON(result, includeSections);
      
      case ExportFormat.CSV:
        return this.exportAsCSV(result, includeSections);
      
      case ExportFormat.EXCEL:
        return this.exportAsExcel(result, includeSections);
      
      case ExportFormat.PDF:
        return this.exportAsPDF(result, includeSections);
      
      default:
        throw new Error(`Unsupported export format: ${request.format}`);
    }
  }

  /**
   * Delete backtest result
   */
  async deleteResult(backtestId: string, userId: string): Promise<boolean> {
    try {
      // Try deleting from backtest_results table
      await this.prisma.$executeRaw`
        DELETE FROM backtest_results 
        WHERE id = ${backtestId} AND user_id = ${userId}
      `;

      return true;
    } catch (error) {
      // Mark as deleted in audit log
      await this.prisma.auditLog.create({
        data: {
          userId,
          action: 'delete_backtest_result',
          recordId: backtestId,
          metadata: {
            deletedAt: new Date().toISOString(),
          },
        },
      });

      return true;
    }
  }

  /**
   * Store analytics results as cache
   */
  async storeAnalytics(
    backtestId: string, 
    userId: string,
    performanceAttribution?: PerformanceAttribution,
    riskAnalysis?: RiskAnalysis,
    tradeDistribution?: TradeDistributionAnalysis
  ): Promise<void> {
    try {
      await this.prisma.$executeRaw`
        UPDATE backtest_results 
        SET 
          performance_attribution = ${performanceAttribution ? JSON.stringify(performanceAttribution) : null},
          risk_analysis = ${riskAnalysis ? JSON.stringify(riskAnalysis) : null},
          trade_distribution = ${tradeDistribution ? JSON.stringify(tradeDistribution) : null},
          updated_at = ${new Date()}
        WHERE id = ${backtestId} AND user_id = ${userId}
      `;
    } catch (error) {
      // Store in audit log as fallback
      await this.prisma.auditLog.create({
        data: {
          userId,
          action: 'store_backtest_analytics',
          recordId: backtestId,
          newValues: {
            performanceAttribution,
            riskAnalysis,
            tradeDistribution,
          },
        },
      });
    }
  }

  /**
   * Export as JSON format
   */
  private exportAsJSON(
    result: BacktestResult, 
    includeSections: any
  ): ExportResult {
    const exportData: any = {};

    if (includeSections.summary) {
      exportData.summary = {
        backtestId: result.id,
        strategy: result.strategyId,
        dateRange: {
          start: result.startDate,
          end: result.endDate,
        },
        capitalInfo: {
          initial: result.initialCapital,
          final: result.finalCapital,
        },
        performance: result.overallMetrics,
        duration: result.backtestDuration,
        dataPoints: result.dataPoints,
      };
    }

    if (includeSections.trades) {
      exportData.trades = result.trades.map(trade => ({
        id: trade.id,
        instrument: trade.instrument,
        type: trade.type,
        quantity: trade.quantity,
        entryTime: trade.entryTime,
        exitTime: trade.exitTime,
        entryPrice: trade.entryPrice,
        exitPrice: trade.exitPrice,
        pnl: trade.pnl,
        pnlPercentage: trade.pnlPercentage,
        holdingPeriod: trade.holdingPeriod,
        commission: trade.commission,
        slippage: trade.slippage,
        marketRegime: trade.marketRegime,
      }));
    }

    if (includeSections.analytics) {
      exportData.analytics = {
        timeframeAnalysis: result.timeframeAnalysis,
        regimeAnalysis: result.regimeAnalysis,
        drawdownAnalysis: result.drawdownAnalysis,
        executionStats: result.executionStats,
      };
    }

    if (includeSections.charts) {
      exportData.charts = {
        equityCurve: result.equityCurve,
      };
    }

    const jsonString = JSON.stringify(exportData, null, 2);
    const buffer = Buffer.from(jsonString, 'utf-8');

    return {
      filename: `backtest_${result.id}_${new Date().toISOString().split('T')[0]}.json`,
      contentType: 'application/json',
      data: buffer,
      size: buffer.length,
    };
  }

  /**
   * Export as CSV format
   */
  private exportAsCSV(
    result: BacktestResult, 
    includeSections: any
  ): ExportResult {
    let csvContent = '';

    if (includeSections.summary) {
      csvContent += 'BACKTEST SUMMARY\n';
      csvContent += `Backtest ID,${result.id}\n`;
      csvContent += `Strategy ID,${result.strategyId}\n`;
      csvContent += `Start Date,${result.startDate.toISOString()}\n`;
      csvContent += `End Date,${result.endDate.toISOString()}\n`;
      csvContent += `Initial Capital,${result.initialCapital}\n`;
      csvContent += `Final Capital,${result.finalCapital}\n`;
      csvContent += `Total Return,${result.overallMetrics.totalReturn}\n`;
      csvContent += `Return %,${result.overallMetrics.totalReturnPercentage}\n`;
      csvContent += `Sharpe Ratio,${result.overallMetrics.sharpeRatio}\n`;
      csvContent += `Max Drawdown,${result.overallMetrics.maxDrawdown}\n`;
      csvContent += `Win Rate %,${result.overallMetrics.winRate}\n`;
      csvContent += `Total Trades,${result.overallMetrics.totalTrades}\n\n`;
    }

    if (includeSections.trades) {
      csvContent += 'TRADES\n';
      csvContent += 'Trade ID,Instrument,Type,Quantity,Entry Time,Exit Time,Entry Price,Exit Price,P&L,P&L %,Holding Period (h),Commission,Slippage,Market Regime\n';
      
      for (const trade of result.trades) {
        csvContent += `${[
          trade.id,
          trade.instrument,
          trade.type,
          trade.quantity,
          trade.entryTime.toISOString(),
          trade.exitTime.toISOString(),
          trade.entryPrice,
          trade.exitPrice,
          trade.pnl,
          trade.pnlPercentage,
          trade.holdingPeriod,
          trade.commission,
          trade.slippage,
          trade.marketRegime || 'N/A'
        ].join(',')  }\n`;
      }
    }

    const buffer = Buffer.from(csvContent, 'utf-8');

    return {
      filename: `backtest_${result.id}_${new Date().toISOString().split('T')[0]}.csv`,
      contentType: 'text/csv',
      data: buffer,
      size: buffer.length,
    };
  }

  /**
   * Export as Excel format (simplified CSV for now)
   */
  private exportAsExcel(
    result: BacktestResult, 
    includeSections: any
  ): ExportResult {
    // For now, return as CSV with Excel content type
    // In a full implementation, you would use a library like 'xlsx' to create actual Excel files
    const csvResult = this.exportAsCSV(result, includeSections);
    
    return {
      ...csvResult,
      filename: csvResult.filename.replace('.csv', '.xlsx'),
      contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    };
  }

  /**
   * Export as PDF format (simplified text for now)
   */
  private exportAsPDF(
    result: BacktestResult, 
    includeSections: any
  ): ExportResult {
    // For now, return as text. In a full implementation, you would use a PDF library
    let content = `BACKTEST REPORT\n`;
    content += `Generated: ${new Date().toISOString()}\n\n`;
    
    if (includeSections.summary) {
      content += `SUMMARY\n`;
      content += `========\n`;
      content += `Backtest ID: ${result.id}\n`;
      content += `Strategy: ${result.strategyId}\n`;
      content += `Period: ${result.startDate.toISOString()} - ${result.endDate.toISOString()}\n`;
      content += `Initial Capital: $${result.initialCapital.toLocaleString()}\n`;
      content += `Final Capital: $${result.finalCapital.toLocaleString()}\n`;
      content += `Total Return: $${result.overallMetrics.totalReturn.toLocaleString()}\n`;
      content += `Return %: ${result.overallMetrics.totalReturnPercentage.toFixed(2)}%\n`;
      content += `Sharpe Ratio: ${result.overallMetrics.sharpeRatio.toFixed(2)}\n`;
      content += `Max Drawdown: ${result.overallMetrics.maxDrawdown.toFixed(2)}%\n`;
      content += `Win Rate: ${result.overallMetrics.winRate.toFixed(2)}%\n`;
      content += `Total Trades: ${result.overallMetrics.totalTrades}\n\n`;
    }

    if (includeSections.analytics && result.drawdownAnalysis) {
      content += `RISK ANALYSIS\n`;
      content += `=============\n`;
      content += `Max Drawdown Duration: ${result.drawdownAnalysis.maxDrawdownDuration.toFixed(2)} hours\n`;
      content += `Average Drawdown: ${result.drawdownAnalysis.averageDrawdown.toFixed(2)}\n`;
      content += `Drawdown Periods: ${result.drawdownAnalysis.drawdownPeriods.length}\n\n`;
    }

    const buffer = Buffer.from(content, 'utf-8');

    return {
      filename: `backtest_${result.id}_${new Date().toISOString().split('T')[0]}.pdf`,
      contentType: 'application/pdf',
      data: buffer,
      size: buffer.length,
    };
  }

  /**
   * Get backtest summary statistics
   */
  async getStatistics(userId: string): Promise<{
    totalBacktests: number;
    avgReturn: number;
    avgSharpeRatio: number;
    bestBacktest: { id: string; return: number } | null;
    worstBacktest: { id: string; return: number } | null;
    recentActivity: Array<{ date: Date; backtestCount: number }>;
  }> {
    try {
      const stats = await this.prisma.$queryRaw<any[]>`
        SELECT 
          COUNT(*) as total_backtests,
          AVG(total_return_percentage) as avg_return,
          AVG(sharpe_ratio) as avg_sharpe_ratio,
          MAX(total_return_percentage) as best_return,
          MIN(total_return_percentage) as worst_return
        FROM backtest_results 
        WHERE user_id = ${userId}
      `;

      const bestBacktest = await this.prisma.$queryRaw<any[]>`
        SELECT id, total_return_percentage 
        FROM backtest_results 
        WHERE user_id = ${userId}
        ORDER BY total_return_percentage DESC 
        LIMIT 1
      `;

      const worstBacktest = await this.prisma.$queryRaw<any[]>`
        SELECT id, total_return_percentage 
        FROM backtest_results 
        WHERE user_id = ${userId}
        ORDER BY total_return_percentage ASC 
        LIMIT 1
      `;

      const recentActivity = await this.prisma.$queryRaw<any[]>`
        SELECT 
          DATE(created_at) as date,
          COUNT(*) as backtest_count
        FROM backtest_results 
        WHERE user_id = ${userId} 
          AND created_at >= ${new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)}
        GROUP BY DATE(created_at)
        ORDER BY date DESC
      `;

      return {
        totalBacktests: stats[0]?.total_backtests || 0,
        avgReturn: stats[0]?.avg_return || 0,
        avgSharpeRatio: stats[0]?.avg_sharpe_ratio || 0,
        bestBacktest: bestBacktest.length > 0 
          ? { id: bestBacktest[0].id, return: bestBacktest[0].total_return_percentage }
          : null,
        worstBacktest: worstBacktest.length > 0
          ? { id: worstBacktest[0].id, return: worstBacktest[0].total_return_percentage }
          : null,
        recentActivity: recentActivity.map(activity => ({
          date: activity.date,
          backtestCount: activity.backtest_count,
        })),
      };
    } catch (error) {
      // Fallback using audit logs
      const auditRecords = await this.prisma.auditLog.findMany({
        where: {
          userId,
          action: 'store_backtest_result',
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      const returns = auditRecords
        .map(record => {
          const values = record.newValues as any;
          return values?.summary?.totalReturnPercentage || 0;
        })
        .filter(ret => ret !== 0);

      const avgReturn = returns.length > 0 
        ? returns.reduce((sum, ret) => sum + ret, 0) / returns.length 
        : 0;

      return {
        totalBacktests: auditRecords.length,
        avgReturn,
        avgSharpeRatio: 0,
        bestBacktest: returns.length > 0 
          ? { id: 'unknown', return: Math.max(...returns) }
          : null,
        worstBacktest: returns.length > 0
          ? { id: 'unknown', return: Math.min(...returns) }
          : null,
        recentActivity: [],
      };
    }
  }
}