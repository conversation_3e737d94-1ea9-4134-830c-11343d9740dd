/**
 * Historical Regime Analyzer Service
 * 
 * Provides comprehensive analysis of historical market regime data including:
 * - Pattern recognition and regime cycle analysis
 * - Performance tracking and accuracy metrics
 * - Regime transition predictions
 * - Market condition correlation analysis
 */

import { EventEmitter } from 'events';
import {
  MarketRegime,
  RegimeTransition,
  HistoricalRegimeData,
  TimeFrame,
  RegimeAnalysisResult,
  RegimePerformanceMetrics,
} from '@golddaddy/types';

// Analysis configuration
interface AnalysisConfig {
  minPatternLength: number;
  maxPatternLength: number;
  minConfidenceThreshold: number;
  correlationThreshold: number;
  analysisWindow: number; // Days to analyze
  patternMatchTolerance: number;
  enablePatternPrediction: boolean;
  enablePerformanceTracking: boolean;
}

// Pattern detection result
export interface RegimePattern {
  id: string;
  sequence: MarketRegime[];
  frequency: number;
  averageDuration: number;
  confidenceScore: number;
  lastOccurrence: Date;
  nextPredictedRegime?: MarketRegime;
  predictionConfidence?: number;
  correlatedMarketConditions?: string[];
}

// Regime cycle analysis
export interface RegimeCycle {
  id: string;
  instrument: string;
  timeframe: TimeFrame;
  startDate: Date;
  endDate: Date;
  regimes: MarketRegime[];
  totalDuration: number; // in minutes
  averageRegimeDuration: number;
  regimeChanges: number;
  dominantRegime: MarketRegime;
  volatilityIndex: number;
  trendPersistence: number;
  cycleHealth: 'healthy' | 'unstable' | 'transitional';
}

// Performance tracking per regime
interface RegimePerformance {
  regime: MarketRegime;
  totalOccurrences: number;
  totalDuration: number;
  averageDuration: number;
  accuracyRate: number;
  falsePositiveRate: number;
  transitionAccuracy: number;
  performanceScore: number;
  marketConditions: {
    trend: 'bull' | 'bear' | 'neutral';
    volatility: 'high' | 'medium' | 'low';
    volume: 'high' | 'medium' | 'low';
  }[];
}

/**
 * Historical Regime Analyzer - Advanced analysis of regime patterns and performance
 */
export class HistoricalRegimeAnalyzer extends EventEmitter {
  private config: AnalysisConfig;
  private historicalData = new Map<string, HistoricalRegimeData[]>();
  private detectedPatterns = new Map<string, RegimePattern[]>();
  private performanceMetrics = new Map<string, RegimePerformance[]>();
  private cyclicAnalysis = new Map<string, RegimeCycle[]>();

  // Analysis statistics
  private stats = {
    totalAnalyses: 0,
    patternsDetected: 0,
    accuracyRate: 0,
    lastAnalysisDate: new Date(),
    averageAnalysisTime: 0,
  };

  constructor(config?: Partial<AnalysisConfig>) {
    super();
    
    this.config = {
      minPatternLength: 3,
      maxPatternLength: 8,
      minConfidenceThreshold: 0.6,
      correlationThreshold: 0.7,
      analysisWindow: 30, // 30 days
      patternMatchTolerance: 0.15,
      enablePatternPrediction: true,
      enablePerformanceTracking: true,
      ...config,
    };
  }

  /**
   * Analyze historical regime data for patterns and insights
   */
  public async analyzeHistoricalData(
    instrument: string,
    timeframe: TimeFrame,
    startDate: Date,
    endDate: Date
  ): Promise<RegimeAnalysisResult> {
    const startTime = Date.now();
    const analysisId = `analysis_${instrument}_${timeframe}_${Date.now()}`;

    try {
      // Load historical regime data
      const historicalData = await this.loadHistoricalData(instrument, timeframe, startDate, endDate);
      
      if (historicalData.length === 0) {
        throw new Error(`No historical data found for ${instrument} (${timeframe})`);
      }

      this.emit('analysis_started', { analysisId, instrument, timeframe, dataPoints: historicalData.length });

      // Perform various analyses
      const [
        patterns,
        cycles,
        performance,
        transitions,
        predictions
      ] = await Promise.all([
        this.detectRegimePatterns(historicalData),
        this.analyzeCycles(historicalData, instrument, timeframe),
        this.analyzePerformance(historicalData),
        this.analyzeTransitions(historicalData),
        this.config.enablePatternPrediction ? this.generatePredictions(historicalData) : null,
      ]);

      // Create comprehensive analysis result
      const result: RegimeAnalysisResult = {
        id: analysisId,
        instrument,
        timeframe,
        analysisDate: new Date(),
        dataRange: {
          startDate,
          endDate,
          totalDataPoints: historicalData.length,
        },
        patterns,
        cycles,
        performance,
        transitions,
        predictions: predictions || undefined,
        insights: this.generateInsights(patterns, cycles, performance),
        accuracyMetrics: this.calculateAccuracyMetrics(historicalData),
        processingTimeMs: Date.now() - startTime,
      };

      // Cache results
      this.cacheAnalysisResults(analysisId, result);
      
      // Update statistics
      this.updateAnalysisStats(startTime, result);
      
      this.emit('analysis_completed', result);
      return result;

    } catch (error) {
      this.emit('analysis_error', {
        analysisId,
        instrument,
        timeframe,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime,
      });
      throw error;
    }
  }

  /**
   * Detect recurring patterns in regime sequences
   */
  private async detectRegimePatterns(data: HistoricalRegimeData[]): Promise<RegimePattern[]> {
    const patterns: Map<string, RegimePattern> = new Map();
    
    // Extract regime sequences
    const regimeSequence = data.map(d => d.regime);
    
    // Look for patterns of different lengths
    for (let length = this.config.minPatternLength; length <= this.config.maxPatternLength; length++) {
      for (let i = 0; i <= regimeSequence.length - length; i++) {
        const pattern = regimeSequence.slice(i, i + length);
        const patternKey = pattern.join('->');
        
        if (!patterns.has(patternKey)) {
          // Find all occurrences of this pattern
          const occurrences = this.findPatternOccurrences(regimeSequence, pattern);
          
          if (occurrences.length >= 2) { // Pattern must occur at least twice
            const averageDuration = this.calculatePatternAverageDuration(data, occurrences, length);
            const confidenceScore = this.calculatePatternConfidence(occurrences, regimeSequence.length);
            
            if (confidenceScore >= this.config.minConfidenceThreshold) {
              patterns.set(patternKey, {
                id: `pattern_${patterns.size + 1}`,
                sequence: pattern,
                frequency: occurrences.length,
                averageDuration,
                confidenceScore,
                lastOccurrence: this.getPatternLastOccurrence(data, occurrences),
                nextPredictedRegime: this.predictNextRegime(regimeSequence, pattern),
                predictionConfidence: this.calculatePredictionConfidence(regimeSequence, pattern),
                correlatedMarketConditions: this.findCorrelatedConditions(data, occurrences),
              });
            }
          }
        }
      }
    }

    return Array.from(patterns.values())
      .sort((a, b) => b.confidenceScore - a.confidenceScore);
  }

  /**
   * Analyze regime cycles and their characteristics
   */
  private async analyzeCycles(
    data: HistoricalRegimeData[],
    instrument: string,
    timeframe: TimeFrame
  ): Promise<RegimeCycle[]> {
    const cycles: RegimeCycle[] = [];
    
    // Group data into cycles based on regime changes
    let currentCycle: HistoricalRegimeData[] = [];
    // Track cycle start position for future analysis
    
    for (let i = 0; i < data.length; i++) {
      currentCycle.push(data[i]);
      
      // Detect cycle end (return to starting regime or significant pattern completion)
      if (i > 0 && this.isCycleComplete(currentCycle)) {
        const cycle = this.createCycleAnalysis(
          currentCycle,
          instrument,
          timeframe,
          `cycle_${cycles.length + 1}`
        );
        
        cycles.push(cycle);
        
        // Start new cycle
        currentCycle = [data[i]];
        // Track cycle start (could be used for future cycle analysis)
      }
    }
    
    // Add final incomplete cycle if exists
    if (currentCycle.length > 1) {
      const cycle = this.createCycleAnalysis(
        currentCycle,
        instrument,
        timeframe,
        `cycle_${cycles.length + 1}_incomplete`
      );
      cycles.push(cycle);
    }

    return cycles;
  }

  /**
   * Analyze regime detection performance and accuracy
   */
  private async analyzePerformance(data: HistoricalRegimeData[]): Promise<RegimePerformanceMetrics> {
    const regimePerformance = new Map<MarketRegime, RegimePerformance>();
    
    // Initialize performance tracking for each regime
    Object.values(MarketRegime).forEach(regime => {
      regimePerformance.set(regime, {
        regime,
        totalOccurrences: 0,
        totalDuration: 0,
        averageDuration: 0,
        accuracyRate: 0,
        falsePositiveRate: 0,
        transitionAccuracy: 0,
        performanceScore: 0,
        marketConditions: [],
      });
    });

    // Analyze each data point
    let correctPredictions = 0;
    let totalPredictions = 0;

    for (let i = 0; i < data.length; i++) {
      const current = data[i];
      const performance = regimePerformance.get(current.regime)!;
      
      // Update occurrence and duration stats
      performance.totalOccurrences++;
      performance.totalDuration += current.duration;
      performance.averageDuration = performance.totalDuration / performance.totalOccurrences;
      
      // Analyze accuracy (if we have verification data)
      if (i < data.length - 1) {
        const next = data[i + 1];
        totalPredictions++;
        
        // Check if the regime was correctly identified
        // This would typically compare against actual market outcomes
        const wasCorrect = this.verifyRegimeAccuracy(current, next);
        if (wasCorrect) {
          correctPredictions++;
        }
      }
    }

    // Calculate overall accuracy metrics
    const overallAccuracy = totalPredictions > 0 ? correctPredictions / totalPredictions : 0;
    
    // Calculate performance scores for each regime
    for (const performance of regimePerformance.values()) {
      performance.accuracyRate = this.calculateRegimeAccuracy(data, performance.regime);
      performance.falsePositiveRate = this.calculateFalsePositiveRate(data, performance.regime);
      performance.transitionAccuracy = this.calculateTransitionAccuracy(data, performance.regime);
      performance.performanceScore = this.calculatePerformanceScore(performance);
    }

    return {
      overallAccuracy,
      totalAnalyzedPeriods: data.length,
      regimePerformances: Array.from(regimePerformance.values()),
      analysisDate: new Date(),
      timeRange: {
        start: data[0]?.timestamp || new Date(),
        end: data[data.length - 1]?.timestamp || new Date(),
      },
    };
  }

  /**
   * Analyze regime transitions and their patterns
   */
  private async analyzeTransitions(data: HistoricalRegimeData[]): Promise<RegimeTransition[]> {
    const transitions: RegimeTransition[] = [];
    
    for (let i = 1; i < data.length; i++) {
      const current = data[i];
      const previous = data[i - 1];
      
      if (current.regime !== previous.regime) {
        const transition: RegimeTransition = {
          id: `transition_${i}`,
          instrument: current.instrument,
          timeframe: current.timeframe,
          timestamp: current.timestamp,
          fromRegime: previous.regime,
          toRegime: current.regime,
          confidence: current.confidence,
          transitionMagnitude: this.calculateTransitionMagnitude(previous, current),
          duration: previous.duration,
          priceMovement: 0, // Would be calculated from price data
          volumeSpike: false, // Would be determined from volume analysis
          notificationSent: false,
          createdAt: current.createdAt,
        };
        
        transitions.push(transition);
      }
    }

    return transitions;
  }

  /**
   * Generate predictions based on historical patterns
   */
  private async generatePredictions(data: HistoricalRegimeData[]): Promise<{
    nextRegime: MarketRegime;
    confidence: number;
    timeHorizon: number; // minutes
    supportingPatterns: string[];
  } | null> {
    if (data.length < 3) return null;

    const recentRegimes = data.slice(-5).map(d => d.regime);
    const patterns = await this.detectRegimePatterns(data);
    
    // Find patterns that match recent regime sequence
    const matchingPatterns = patterns.filter(pattern => 
      this.patternMatchesRecent(pattern.sequence, recentRegimes)
    );

    if (matchingPatterns.length === 0) return null;

    // Weight predictions by pattern confidence and frequency
    const weightedPredictions = new Map<MarketRegime, number>();
    const supportingPatterns: string[] = [];

    matchingPatterns.forEach(pattern => {
      if (pattern.nextPredictedRegime) {
        const weight = pattern.confidenceScore * pattern.frequency;
        const existing = weightedPredictions.get(pattern.nextPredictedRegime) || 0;
        weightedPredictions.set(pattern.nextPredictedRegime, existing + weight);
        supportingPatterns.push(pattern.id);
      }
    });

    if (weightedPredictions.size === 0) return null;

    // Select highest weighted prediction
    const [nextRegime, weight] = Array.from(weightedPredictions.entries())
      .sort(([,a], [,b]) => b - a)[0];

    const totalWeight = Array.from(weightedPredictions.values()).reduce((sum, w) => sum + w, 0);
    const confidence = weight / totalWeight;

    // Estimate time horizon based on average pattern duration
    const avgDuration = matchingPatterns.reduce((sum, p) => sum + p.averageDuration, 0) / matchingPatterns.length;

    return {
      nextRegime,
      confidence,
      timeHorizon: Math.round(avgDuration),
      supportingPatterns,
    };
  }

  // ===== Helper Methods =====

  private async loadHistoricalData(
    instrument: string,
    timeframe: TimeFrame,
    _startDate: Date,
    _endDate: Date
  ): Promise<HistoricalRegimeData[]> {
    // This would typically query the database
    // For now, return cached data or empty array
    const key = `${instrument}_${timeframe}`;
    return this.historicalData.get(key) || [];
  }

  private findPatternOccurrences(sequence: MarketRegime[], pattern: MarketRegime[]): number[] {
    const occurrences: number[] = [];
    
    for (let i = 0; i <= sequence.length - pattern.length; i++) {
      let matches = true;
      for (let j = 0; j < pattern.length; j++) {
        if (sequence[i + j] !== pattern[j]) {
          matches = false;
          break;
        }
      }
      if (matches) {
        occurrences.push(i);
      }
    }
    
    return occurrences;
  }

  private calculatePatternAverageDuration(
    data: HistoricalRegimeData[],
    occurrences: number[],
    patternLength: number
  ): number {
    let totalDuration = 0;
    
    occurrences.forEach(startIndex => {
      for (let i = 0; i < patternLength && startIndex + i < data.length; i++) {
        totalDuration += data[startIndex + i].duration;
      }
    });
    
    return totalDuration / occurrences.length;
  }

  private calculatePatternConfidence(occurrences: number, totalLength: number): number {
    // Confidence based on frequency and statistical significance
    const frequency = occurrences / totalLength;
    const significance = Math.min(occurrences / 5, 1); // Higher confidence with more occurrences
    return frequency * significance;
  }

  private getPatternLastOccurrence(data: HistoricalRegimeData[], occurrences: number[]): Date {
    const lastIndex = Math.max(...occurrences);
    return data[lastIndex]?.timestamp || new Date();
  }

  private predictNextRegime(sequence: MarketRegime[], pattern: MarketRegime[]): MarketRegime | undefined {
    // Simple prediction: look for what typically follows this pattern
    const occurrences = this.findPatternOccurrences(sequence, pattern);
    const nextRegimes: MarketRegime[] = [];
    
    occurrences.forEach(index => {
      const nextIndex = index + pattern.length;
      if (nextIndex < sequence.length) {
        nextRegimes.push(sequence[nextIndex]);
      }
    });
    
    if (nextRegimes.length === 0) return undefined;
    
    // Return most common next regime
    const regimeCounts = nextRegimes.reduce((acc, regime) => {
      acc[regime] = (acc[regime] || 0) + 1;
      return acc;
    }, {} as Record<MarketRegime, number>);
    
    return Object.entries(regimeCounts)
      .sort(([,a], [,b]) => b - a)[0]?.[0] as MarketRegime;
  }

  private calculatePredictionConfidence(sequence: MarketRegime[], pattern: MarketRegime[]): number {
    const occurrences = this.findPatternOccurrences(sequence, pattern);
    if (occurrences.length === 0) return 0;
    
    const nextRegimes: MarketRegime[] = [];
    occurrences.forEach(index => {
      const nextIndex = index + pattern.length;
      if (nextIndex < sequence.length) {
        nextRegimes.push(sequence[nextIndex]);
      }
    });
    
    if (nextRegimes.length === 0) return 0;
    
    // Confidence based on consistency of next regime
    const regimeCounts = nextRegimes.reduce((acc, regime) => {
      acc[regime] = (acc[regime] || 0) + 1;
      return acc;
    }, {} as Record<MarketRegime, number>);
    
    const maxCount = Math.max(...Object.values(regimeCounts));
    return maxCount / nextRegimes.length;
  }

  private findCorrelatedConditions(_data: HistoricalRegimeData[], _occurrences: number[]): string[] {
    // This would analyze market conditions during pattern occurrences
    // For now, return placeholder
    return ['high_volatility', 'trend_reversal'];
  }

  private isCycleComplete(cycle: HistoricalRegimeData[]): boolean {
    if (cycle.length < 3) return false;
    
    // Simple cycle detection: return to starting regime or complete a trend reversal
    const first = cycle[0].regime;
    const last = cycle[cycle.length - 1].regime;
    
    return first === last || this.isCompleteTrendReversal(cycle);
  }

  private isCompleteTrendReversal(cycle: HistoricalRegimeData[]): boolean {
    // Detect complete trend reversals (up -> down or down -> up)
    const regimes = cycle.map(d => d.regime);
    
    return (
      regimes.includes(MarketRegime.TRENDING_UP) && 
      regimes.includes(MarketRegime.TRENDING_DOWN)
    ) || (
      regimes.includes(MarketRegime.VOLATILE) &&
      regimes.includes(MarketRegime.LOW_VOLATILITY)
    );
  }

  private createCycleAnalysis(
    cycleData: HistoricalRegimeData[],
    instrument: string,
    timeframe: TimeFrame,
    id: string
  ): RegimeCycle {
    const regimes = cycleData.map(d => d.regime);
    const totalDuration = cycleData.reduce((sum, d) => sum + d.duration, 0);
    const regimeChanges = this.countRegimeChanges(regimes);
    
    // Find dominant regime
    const regimeCounts = regimes.reduce((acc, regime) => {
      acc[regime] = (acc[regime] || 0) + 1;
      return acc;
    }, {} as Record<MarketRegime, number>);
    
    const dominantRegime = Object.entries(regimeCounts)
      .sort(([,a], [,b]) => b - a)[0][0] as MarketRegime;

    return {
      id,
      instrument,
      timeframe,
      startDate: cycleData[0].timestamp,
      endDate: cycleData[cycleData.length - 1].timestamp,
      regimes,
      totalDuration,
      averageRegimeDuration: totalDuration / cycleData.length,
      regimeChanges,
      dominantRegime,
      volatilityIndex: this.calculateVolatilityIndex(cycleData),
      trendPersistence: this.calculateTrendPersistence(cycleData),
      cycleHealth: this.assessCycleHealth(cycleData, regimeChanges),
    };
  }

  private countRegimeChanges(regimes: MarketRegime[]): number {
    let changes = 0;
    for (let i = 1; i < regimes.length; i++) {
      if (regimes[i] !== regimes[i - 1]) {
        changes++;
      }
    }
    return changes;
  }

  private calculateVolatilityIndex(cycleData: HistoricalRegimeData[]): number {
    // Calculate based on regime volatility and confidence variations
    let volatilityScore = 0;
    
    cycleData.forEach(data => {
      if (data.regime === MarketRegime.VOLATILE) {
        volatilityScore += 2;
      } else if (data.regime === MarketRegime.LOW_VOLATILITY) {
        volatilityScore += 0.5;
      } else {
        volatilityScore += 1;
      }
    });
    
    return volatilityScore / cycleData.length;
  }

  private calculateTrendPersistence(cycleData: HistoricalRegimeData[]): number {
    // Measure how long trends persist
    const trends = cycleData.filter(d => 
      d.regime === MarketRegime.TRENDING_UP || d.regime === MarketRegime.TRENDING_DOWN
    );
    
    if (trends.length === 0) return 0;
    
    const avgTrendDuration = trends.reduce((sum, t) => sum + t.duration, 0) / trends.length;
    const totalCycleDuration = cycleData.reduce((sum, d) => sum + d.duration, 0);
    
    return avgTrendDuration / totalCycleDuration;
  }

  private assessCycleHealth(cycleData: HistoricalRegimeData[], regimeChanges: number): 'healthy' | 'unstable' | 'transitional' {
    const avgConfidence = cycleData.reduce((sum, d) => sum + d.confidence, 0) / cycleData.length;
    const changeRate = regimeChanges / cycleData.length;
    
    if (avgConfidence > 75 && changeRate < 0.3) {
      return 'healthy';
    } else if (avgConfidence < 50 || changeRate > 0.6) {
      return 'unstable';
    } else {
      return 'transitional';
    }
  }

  private verifyRegimeAccuracy(current: HistoricalRegimeData, _next: HistoricalRegimeData): boolean {
    // This would implement actual accuracy verification logic
    // For now, return a placeholder based on confidence
    return current.confidence > 70;
  }

  private calculateRegimeAccuracy(data: HistoricalRegimeData[], regime: MarketRegime): number {
    const regimeData = data.filter(d => d.regime === regime);
    if (regimeData.length === 0) return 0;
    
    const correctPredictions = regimeData.filter(d => d.confidence > 70).length;
    return correctPredictions / regimeData.length;
  }

  private calculateFalsePositiveRate(data: HistoricalRegimeData[], regime: MarketRegime): number {
    // Placeholder implementation
    const regimeData = data.filter(d => d.regime === regime);
    const lowConfidenceCount = regimeData.filter(d => d.confidence < 50).length;
    return regimeData.length > 0 ? lowConfidenceCount / regimeData.length : 0;
  }

  private calculateTransitionAccuracy(data: HistoricalRegimeData[], regime: MarketRegime): number {
    // Measure accuracy of transitions to this regime
    let correctTransitions = 0;
    let totalTransitions = 0;
    
    for (let i = 1; i < data.length; i++) {
      if (data[i].regime === regime && data[i - 1].regime !== regime) {
        totalTransitions++;
        if (data[i].confidence > 70) {
          correctTransitions++;
        }
      }
    }
    
    return totalTransitions > 0 ? correctTransitions / totalTransitions : 0;
  }

  private calculatePerformanceScore(performance: RegimePerformance): number {
    // Composite score based on accuracy, false positive rate, and transition accuracy
    const accuracyWeight = 0.4;
    const falsePositiveWeight = 0.3;
    const transitionWeight = 0.3;
    
    const accuracyScore = performance.accuracyRate * accuracyWeight;
    const falsePositiveScore = (1 - performance.falsePositiveRate) * falsePositiveWeight;
    const transitionScore = performance.transitionAccuracy * transitionWeight;
    
    return accuracyScore + falsePositiveScore + transitionScore;
  }

  private calculateTransitionMagnitude(previous: HistoricalRegimeData, current: HistoricalRegimeData): number {
    // Calculate the magnitude of regime change based on confidence and regime difference
    const regimeDistanceMap = {
      [MarketRegime.TRENDING_UP]: { [MarketRegime.TRENDING_DOWN]: 1.0, [MarketRegime.SIDEWAYS]: 0.5 },
      [MarketRegime.TRENDING_DOWN]: { [MarketRegime.TRENDING_UP]: 1.0, [MarketRegime.SIDEWAYS]: 0.5 },
      [MarketRegime.SIDEWAYS]: { [MarketRegime.TRENDING_UP]: 0.5, [MarketRegime.TRENDING_DOWN]: 0.5 },
      [MarketRegime.VOLATILE]: { [MarketRegime.LOW_VOLATILITY]: 0.8 },
      [MarketRegime.LOW_VOLATILITY]: { [MarketRegime.VOLATILE]: 0.8 },
      [MarketRegime.UNKNOWN]: {},
    };
    
    const distance = regimeDistanceMap[previous.regime]?.[current.regime] || 0.3;
    const confidenceFactor = (current.confidence - previous.confidence) / 100;
    
    return distance + (confidenceFactor * 0.2);
  }

  private patternMatchesRecent(patternSequence: MarketRegime[], recentRegimes: MarketRegime[]): boolean {
    if (patternSequence.length > recentRegimes.length) return false;
    
    const recentTail = recentRegimes.slice(-patternSequence.length);
    return patternSequence.every((regime, index) => regime === recentTail[index]);
  }

  private generateInsights(
    patterns: RegimePattern[],
    cycles: RegimeCycle[],
    performance: RegimePerformanceMetrics
  ): string[] {
    const insights: string[] = [];
    
    // Pattern insights
    if (patterns.length > 0) {
      const topPattern = patterns[0];
      insights.push(
        `Most common pattern: ${topPattern.sequence.join(' → ')} (${topPattern.frequency} occurrences, ${(topPattern.confidenceScore * 100).toFixed(1)}% confidence)`
      );
    }
    
    // Cycle insights
    if (cycles.length > 0) {
      const avgCycleLength = cycles.reduce((sum, c) => sum + c.totalDuration, 0) / cycles.length;
      insights.push(`Average cycle duration: ${Math.round(avgCycleLength)} minutes`);
      
      const healthyCycles = cycles.filter(c => c.cycleHealth === 'healthy').length;
      if (healthyCycles > 0) {
        insights.push(`${healthyCycles}/${cycles.length} cycles show healthy regime transitions`);
      }
    }
    
    // Performance insights
    const bestPerforming = performance.regimePerformances
      .sort((a, b) => b.performanceScore - a.performanceScore)[0];
    
    if (bestPerforming) {
      insights.push(
        `Best performing regime: ${bestPerforming.regime} (${(bestPerforming.accuracyRate * 100).toFixed(1)}% accuracy)`
      );
    }
    
    return insights;
  }

  private calculateAccuracyMetrics(data: HistoricalRegimeData[]) {
    const totalPredictions = data.length;
    const highConfidencePredictions = data.filter(d => d.confidence > 80).length;
    const mediumConfidencePredictions = data.filter(d => d.confidence > 60 && d.confidence <= 80).length;
    
    return {
      totalPredictions,
      highConfidenceRate: totalPredictions > 0 ? highConfidencePredictions / totalPredictions : 0,
      mediumConfidenceRate: totalPredictions > 0 ? mediumConfidencePredictions / totalPredictions : 0,
      averageConfidence: totalPredictions > 0 ? data.reduce((sum, d) => sum + d.confidence, 0) / totalPredictions : 0,
    };
  }

  private cacheAnalysisResults(_analysisId: string, _result: RegimeAnalysisResult): void {
    // Cache results for future use
    // This would typically save to database or cache system
  }

  private updateAnalysisStats(startTime: number, result: RegimeAnalysisResult): void {
    const processingTime = Date.now() - startTime;
    this.stats.totalAnalyses++;
    this.stats.patternsDetected += result.patterns.length;
    this.stats.averageAnalysisTime = 
      ((this.stats.averageAnalysisTime * (this.stats.totalAnalyses - 1)) + processingTime) / 
      this.stats.totalAnalyses;
    this.stats.lastAnalysisDate = new Date();
    
    if (result.accuracyMetrics) {
      this.stats.accuracyRate = result.accuracyMetrics.averageConfidence / 100;
    }
  }

  /**
   * Get analysis statistics
   */
  public getAnalysisStats() {
    return { ...this.stats };
  }

  /**
   * Clear cached data and reset
   */
  public clearCache(): void {
    this.historicalData.clear();
    this.detectedPatterns.clear();
    this.performanceMetrics.clear();
    this.cyclicAnalysis.clear();
    
    this.emit('cache_cleared');
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<AnalysisConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config_updated', this.config);
  }

  /**
   * Get current configuration
   */
  public getConfig(): AnalysisConfig {
    return { ...this.config };
  }

  /**
   * Shutdown and cleanup
   */
  public shutdown(): void {
    this.clearCache();
    this.removeAllListeners();
  }
}