import { PrismaClient } from '@prisma/client';
import {
  QuizCategory,
  QuizDifficulty,
  type QuizOption,
  type QuizQuestionMetadata
} from '@golddaddy/types';

interface QuestionTemplate {
  category: QuizCategory;
  difficulty: QuizDifficulty;
  topic: string;
  question: string;
  options: QuizOption[];
  correctAnswerIds: string[];
  explanation: string;
  metadata: QuizQuestionMetadata;
}

export class QuizContentSeedService {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
  }

  async seedTradingFundamentalsQuestions(): Promise<void> {
    const questions: QuestionTemplate[] = [
      {
        category: QuizCategory.TRADING_FUNDAMENTALS,
        difficulty: QuizDifficulty.BEGINNER,
        topic: 'Position Sizing',
        question: 'What percentage of your trading capital should you typically risk on a single trade?',
        options: [
          { id: 'opt1', text: '1-2%', isCorrect: true },
          { id: 'opt2', text: '5-10%', isCorrect: false },
          { id: 'opt3', text: '15-20%', isCorrect: false },
          { id: 'opt4', text: 'As much as possible to maximize profits', isCorrect: false }
        ],
        correctAnswerIds: ['opt1'],
        explanation: 'Professional traders typically risk only 1-2% of their capital per trade to preserve their trading account and avoid catastrophic losses.',
        metadata: {
          estimatedDuration: 30,
          tags: ['position-sizing', 'risk-management', 'capital-preservation'],
          riskLevel: 'low'
        }
      },
      {
        category: QuizCategory.RISK_MANAGEMENT,
        difficulty: QuizDifficulty.BEGINNER,
        topic: 'Stop Loss Basics',
        question: 'What is the primary purpose of a stop loss order?',
        options: [
          { id: 'opt1', text: 'To guarantee profits', isCorrect: false },
          { id: 'opt2', text: 'To limit potential losses', isCorrect: true },
          { id: 'opt3', text: 'To maximize position size', isCorrect: false },
          { id: 'opt4', text: 'To increase trading frequency', isCorrect: false }
        ],
        correctAnswerIds: ['opt2'],
        explanation: 'A stop loss order is designed to limit losses by automatically closing a position when it reaches a predetermined loss level.',
        metadata: {
          estimatedDuration: 25,
          tags: ['stop-loss', 'risk-control', 'orders'],
          riskLevel: 'low'
        }
      },
      {
        category: QuizCategory.PLATFORM_FEATURES,
        difficulty: QuizDifficulty.BEGINNER,
        topic: 'Safety Features',
        question: 'In the GoldDaddy platform, what happens when you exceed your daily loss limit?',
        options: [
          { id: 'opt1', text: 'Trading continues normally', isCorrect: false },
          { id: 'opt2', text: 'All positions are automatically closed and new trades are blocked', isCorrect: true },
          { id: 'opt3', text: 'You receive a warning but can continue trading', isCorrect: false },
          { id: 'opt4', text: 'Only new buy orders are blocked', isCorrect: false }
        ],
        correctAnswerIds: ['opt2'],
        explanation: 'GoldDaddy\'s safety system automatically closes all positions and prevents new trades when daily loss limits are exceeded to protect your capital.',
        metadata: {
          estimatedDuration: 35,
          tags: ['platform-safety', 'loss-limits', 'protection'],
          riskLevel: 'high',
          relatedSafetyProcedures: ['daily-loss-limit', 'position-closure']
        }
      },
      {
        category: QuizCategory.PSYCHOLOGY_DISCIPLINE,
        difficulty: QuizDifficulty.INTERMEDIATE,
        topic: 'Emotional Control',
        question: 'What is the most appropriate response when you experience a series of losing trades?',
        options: [
          { id: 'opt1', text: 'Increase position size to recover losses quickly', isCorrect: false },
          { id: 'opt2', text: 'Continue trading with the same strategy and position size', isCorrect: false },
          { id: 'opt3', text: 'Stop trading and review your strategy and emotional state', isCorrect: true },
          { id: 'opt4', text: 'Switch to a completely different trading strategy', isCorrect: false }
        ],
        correctAnswerIds: ['opt3'],
        explanation: 'After consecutive losses, the best practice is to pause trading, analyze what went wrong, and ensure you\'re in the right emotional state before continuing.',
        metadata: {
          estimatedDuration: 45,
          tags: ['psychology', 'discipline', 'drawdown-management'],
          riskLevel: 'medium'
        }
      },
      {
        category: QuizCategory.REGULATORY_COMPLIANCE,
        difficulty: QuizDifficulty.INTERMEDIATE,
        topic: 'Financial Regulations',
        question: 'Which of the following activities require proper documentation for regulatory compliance?',
        options: [
          { id: 'opt1', text: 'All trading decisions and risk management actions', isCorrect: true },
          { id: 'opt2', text: 'Only profitable trades', isCorrect: false },
          { id: 'opt3', text: 'Only trades above $10,000', isCorrect: false },
          { id: 'opt4', text: 'Only manual trades, not automated ones', isCorrect: false }
        ],
        correctAnswerIds: ['opt1'],
        explanation: 'Financial regulations require comprehensive documentation of all trading activities, including decisions, risk management, and compliance procedures.',
        metadata: {
          estimatedDuration: 40,
          tags: ['compliance', 'documentation', 'regulations'],
          riskLevel: 'high'
        }
      }
    ];

    for (const questionTemplate of questions) {
      await this.createQuestionFromTemplate(questionTemplate);
    }
  }

  async seedPlatformSpecificContent(): Promise<void> {
    const platformQuestions: QuestionTemplate[] = [
      {
        category: QuizCategory.SAFETY_PROCEDURES,
        difficulty: QuizDifficulty.BEGINNER,
        topic: 'Emergency Procedures',
        question: 'In an emergency situation where you need to immediately close all positions, which GoldDaddy feature should you use?',
        options: [
          { id: 'opt1', text: 'Close each position manually one by one', isCorrect: false },
          { id: 'opt2', text: 'Use the "Emergency Close All" panic button', isCorrect: true },
          { id: 'opt3', text: 'Turn off the platform and wait', isCorrect: false },
          { id: 'opt4', text: 'Contact customer support first', isCorrect: false }
        ],
        correctAnswerIds: ['opt2'],
        explanation: 'The Emergency Close All button immediately closes all open positions at market prices to minimize losses during emergency situations.',
        metadata: {
          estimatedDuration: 30,
          tags: ['emergency', 'platform-safety', 'position-management'],
          riskLevel: 'high',
          relatedSafetyProcedures: ['emergency-closure', 'panic-button']
        }
      },
      {
        category: QuizCategory.PLATFORM_FEATURES,
        difficulty: QuizDifficulty.INTERMEDIATE,
        topic: 'Confidence Assessment',
        question: 'How does the GoldDaddy confidence assessment system determine if you\'re ready for live trading?',
        options: [
          { id: 'opt1', text: 'Based only on quiz scores', isCorrect: false },
          { id: 'opt2', text: 'Combination of quiz results, behavioral assessment, and performance evaluation', isCorrect: true },
          { id: 'opt3', text: 'Based only on paper trading profits', isCorrect: false },
          { id: 'opt4', text: 'Automatically after 30 days', isCorrect: false }
        ],
        correctAnswerIds: ['opt2'],
        explanation: 'GoldDaddy uses a comprehensive multi-method assessment including knowledge quizzes, behavioral analysis, and performance evaluation to ensure user readiness.',
        metadata: {
          estimatedDuration: 45,
          tags: ['confidence-assessment', 'platform-features', 'safety'],
          riskLevel: 'medium'
        }
      }
    ];

    for (const questionTemplate of platformQuestions) {
      await this.createQuestionFromTemplate(questionTemplate);
    }
  }

  private async createQuestionFromTemplate(template: QuestionTemplate): Promise<void> {
    await this.prisma.quizQuestion.create({
      data: {
        category: template.category,
        difficulty: template.difficulty,
        topic: template.topic,
        question: template.question,
        options: template.options,
        correctAnswerIds: template.correctAnswerIds,
        explanation: template.explanation,
        learningResources: [],
        metadata: template.metadata,
        isActive: true
      }
    });
  }
}