/**
 * Regime Analysis Orchestrator Service
 * 
 * Coordinates historical regime analysis and pattern matching to provide
 * comprehensive market regime insights and predictions.
 */

import { EventEmitter } from 'events';
import {
  MarketRegime,
  RegimeDetectionResult,
  TimeFrame,
  DataSource,
} from '@golddaddy/types';

import { HistoricalRegimeAnalyzer } from './HistoricalRegimeAnalyzer';
import { RegimePatternMatcher } from './RegimePatternMatcher';
import { MarketRegimeDetector } from './MarketRegimeDetector';

// Comprehensive analysis result
export interface ComprehensiveRegimeAnalysis {
  id: string;
  instrument: string;
  timeframe: TimeFrame;
  timestamp: Date;
  
  // Current state
  currentRegime: RegimeDetectionResult;
  
  // Historical analysis
  historicalInsights: {
    dominantRegimes: Array<{ regime: MarketRegime; percentage: number }>;
    averageRegimeDuration: number;
    regimeStability: 'stable' | 'volatile' | 'transitional';
    seasonalPatterns: Array<{ timeOfDay: number; preferredRegime: MarketRegime }>;
    marketEfficiency: number; // 0-1 scale
  };
  
  // Pattern analysis
  patternInsights: {
    strongPatterns: Array<{ pattern: string; confidence: number }>;
    currentPatternMatch: string | null;
    patternBasedPrediction: {
      nextRegime: MarketRegime;
      confidence: number;
      timeHorizon: number;
    } | null;
  };
  
  // Predictive analysis
  predictions: {
    shortTerm: { // Next 1-2 hours
      regime: MarketRegime;
      confidence: number;
      factors: string[];
    };
    mediumTerm: { // Next 4-6 hours
      regime: MarketRegime;
      confidence: number;
      factors: string[];
    };
    longTerm: { // Next 12-24 hours
      regime: MarketRegime;
      confidence: number;
      factors: string[];
    };
  };
  
  // Risk assessment
  riskFactors: {
    regimeInstability: number; // 0-1
    predictionUncertainty: number; // 0-1
    marketAnomalies: string[];
    externalRisks: string[];
  };
  
  // Performance metrics
  analysisQuality: {
    dataCompleteness: number; // 0-1
    patternReliability: number; // 0-1
    historicalAccuracy: number; // 0-1
    overallConfidence: number; // 0-1
  };
  
  processingTimeMs: number;
}

// Orchestrator configuration
interface OrchestrationConfig {
  historyWindowDays: number;
  minDataPointsRequired: number;
  patternLearningEnabled: boolean;
  predictionHorizons: {
    short: number; // minutes
    medium: number; // minutes  
    long: number; // minutes
  };
  riskThresholds: {
    instability: number;
    uncertainty: number;
    anomaly: number;
  };
  cacheTimeoutMs: number;
  parallelAnalysis: boolean;
}

/**
 * Regime Analysis Orchestrator - Comprehensive regime analysis coordinator
 */
export class RegimeAnalysisOrchestrator extends EventEmitter {
  private config: OrchestrationConfig;
  private historicalAnalyzer: HistoricalRegimeAnalyzer;
  private patternMatcher: RegimePatternMatcher;
  private regimeDetector: MarketRegimeDetector;
  
  // Analysis cache
  private analysisCache = new Map<string, {
    analysis: ComprehensiveRegimeAnalysis;
    timestamp: Date;
  }>();
  
  // Performance tracking
  private stats = {
    totalAnalyses: 0,
    cacheHits: 0,
    averageAnalysisTime: 0,
    successfulPredictions: 0,
    predictionAccuracy: 0,
  };

  constructor(
    config?: Partial<OrchestrationConfig>,
    historicalAnalyzer?: HistoricalRegimeAnalyzer,
    patternMatcher?: RegimePatternMatcher,
    regimeDetector?: MarketRegimeDetector
  ) {
    super();
    
    this.config = {
      historyWindowDays: 30,
      minDataPointsRequired: 50,
      patternLearningEnabled: true,
      predictionHorizons: {
        short: 120, // 2 hours
        medium: 360, // 6 hours
        long: 1440, // 24 hours
      },
      riskThresholds: {
        instability: 0.7,
        uncertainty: 0.6,
        anomaly: 0.8,
      },
      cacheTimeoutMs: 300000, // 5 minutes
      parallelAnalysis: true,
      ...config,
    };
    
    // Initialize services
    this.historicalAnalyzer = historicalAnalyzer || new HistoricalRegimeAnalyzer();
    this.patternMatcher = patternMatcher || new RegimePatternMatcher();
    this.regimeDetector = regimeDetector || new MarketRegimeDetector();
    
    // Set up event forwarding
    this.setupEventForwarding();
  }

  /**
   * Perform comprehensive regime analysis
   */
  public async analyzeRegime(
    instrument: string,
    timeframe: TimeFrame,
    forceRefresh = false
  ): Promise<ComprehensiveRegimeAnalysis> {
    const startTime = Date.now();
    const analysisId = `comprehensive_${instrument}_${timeframe}_${Date.now()}`;
    const cacheKey = `${instrument}_${timeframe}`;
    
    try {
      // Check cache first
      if (!forceRefresh) {
        const cached = this.getCachedAnalysis(cacheKey);
        if (cached) {
          this.stats.cacheHits++;
          this.emit('analysis_cache_hit', { instrument, timeframe });
          return cached;
        }
      }
      
      this.emit('comprehensive_analysis_started', { analysisId, instrument, timeframe });
      
      // Prepare date ranges
      const endDate = new Date();
      const startDate = new Date(endDate.getTime() - (this.config.historyWindowDays * 24 * 60 * 60 * 1000));
      
      // Get current regime state
      const currentData = await this.getCurrentMarketData(instrument, timeframe);
      const currentRegime = await this.regimeDetector.detectRegime(currentData);
      
      // Perform parallel analysis
      const analysisResults = this.config.parallelAnalysis ? 
        await this.performParallelAnalysis(instrument, timeframe, startDate, endDate) :
        await this.performSequentialAnalysis(instrument, timeframe, startDate, endDate);
      
      // Synthesize comprehensive analysis
      const comprehensiveAnalysis = await this.synthesizeAnalysis(
        analysisId,
        instrument,
        timeframe,
        currentRegime,
        analysisResults,
        startTime
      );
      
      // Cache the result
      this.cacheAnalysis(cacheKey, comprehensiveAnalysis);
      
      // Update statistics
      this.updateStats(startTime);
      
      // Learn from current pattern if enabled
      if (this.config.patternLearningEnabled) {
        await this.learnFromCurrentState(instrument, timeframe, currentRegime);
      }
      
      this.emit('comprehensive_analysis_completed', comprehensiveAnalysis);
      return comprehensiveAnalysis;

    } catch (error) {
      this.emit('comprehensive_analysis_error', {
        analysisId,
        instrument,
        timeframe,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime,
      });
      throw error;
    }
  }

  /**
   * Verify analysis predictions with actual outcomes
   */
  public async verifyPredictions(
    instrument: string,
    timeframe: TimeFrame,
    actualRegime: MarketRegime,
    timestamp: Date
  ): Promise<void> {
    // Find recent analysis for this instrument
    const cacheKey = `${instrument}_${timeframe}`;
    const cached = this.analysisCache.get(cacheKey);
    
    if (cached) {
      const analysis = cached.analysis;
      const predictionAge = timestamp.getTime() - analysis.timestamp.getTime();
      
      // Verify appropriate prediction based on age
      let verifiedPrediction: { regime: MarketRegime; confidence: number } | null = null;
      
      if (predictionAge <= this.config.predictionHorizons.short * 60 * 1000) {
        verifiedPrediction = analysis.predictions.shortTerm;
      } else if (predictionAge <= this.config.predictionHorizons.medium * 60 * 1000) {
        verifiedPrediction = analysis.predictions.mediumTerm;
      } else if (predictionAge <= this.config.predictionHorizons.long * 60 * 1000) {
        verifiedPrediction = analysis.predictions.longTerm;
      }
      
      if (verifiedPrediction) {
        const wasCorrect = verifiedPrediction.regime === actualRegime;
        
        // Update accuracy statistics
        this.stats.successfulPredictions += wasCorrect ? 1 : 0;
        
        // Update pattern matcher with verification
        await this.patternMatcher.verifyPrediction(
          analysis.id,
          actualRegime,
          instrument,
          timeframe
        );
        
        this.emit('prediction_verified', {
          analysisId: analysis.id,
          instrument,
          timeframe,
          predicted: verifiedPrediction.regime,
          actual: actualRegime,
          wasCorrect,
          confidence: verifiedPrediction.confidence,
          predictionAge: predictionAge / (60 * 1000), // in minutes
        });
      }
    }
  }

  /**
   * Get analysis performance statistics
   */
  public getPerformanceStats() {
    const patternStats = this.patternMatcher.getPatternStats();
    const historicalStats = this.historicalAnalyzer.getAnalysisStats();
    const detectorStats = this.regimeDetector.getStats();
    
    return {
      orchestrator: {
        ...this.stats,
        cacheHitRate: this.stats.totalAnalyses > 0 ? 
          (this.stats.cacheHits / this.stats.totalAnalyses) * 100 : 0,
        predictionAccuracyRate: this.stats.totalAnalyses > 0 ?
          (this.stats.successfulPredictions / this.stats.totalAnalyses) * 100 : 0,
      },
      patterns: patternStats,
      historical: historicalStats,
      detector: detectorStats,
    };
  }

  // ===== Private Methods =====

  private setupEventForwarding(): void {
    // Forward events from child services
    this.historicalAnalyzer.on('analysis_completed', (result) => {
      this.emit('historical_analysis_completed', result);
    });
    
    this.patternMatcher.on('pattern_matching_complete', (result) => {
      this.emit('pattern_matching_completed', result);
    });
    
    this.regimeDetector.on('regime_detected', (result) => {
      this.emit('regime_detected', result);
    });
  }

  private getCachedAnalysis(cacheKey: string): ComprehensiveRegimeAnalysis | null {
    const cached = this.analysisCache.get(cacheKey);
    if (cached) {
      const age = Date.now() - cached.timestamp.getTime();
      if (age < this.config.cacheTimeoutMs) {
        return cached.analysis;
      } else {
        this.analysisCache.delete(cacheKey);
      }
    }
    return null;
  }

  private cacheAnalysis(cacheKey: string, analysis: ComprehensiveRegimeAnalysis): void {
    this.analysisCache.set(cacheKey, {
      analysis,
      timestamp: new Date(),
    });
  }

  private async getCurrentMarketData(instrument: string, timeframe: TimeFrame) {
    // This would typically fetch from real-time data feed
    // For now, return mock data
    return {
      instrument,
      timeframe,
      timestamp: new Date(),
      open: new (require('decimal.js'))(1.2345),
      high: new (require('decimal.js'))(1.2367),
      low: new (require('decimal.js'))(1.2320),
      close: new (require('decimal.js'))(1.2356),
      volume: new (require('decimal.js'))(1500000),
      source: DataSource.MT5,
    };
  }

  private async performParallelAnalysis(
    instrument: string,
    timeframe: TimeFrame,
    startDate: Date,
    endDate: Date
  ) {
    // Run historical analysis and pattern matching in parallel
    const [historicalResult, patternResult] = await Promise.all([
      this.historicalAnalyzer.analyzeHistoricalData(instrument, timeframe, startDate, endDate)
        .catch(error => ({ error: error.message })),
      this.performPatternAnalysis(instrument, timeframe)
        .catch(error => ({ error: error.message })),
    ]);
    
    return { historical: historicalResult, patterns: patternResult };
  }

  private async performSequentialAnalysis(
    instrument: string,
    timeframe: TimeFrame,
    startDate: Date,
    endDate: Date
  ) {
    // Run historical analysis first, then pattern matching
    const historicalResult = await this.historicalAnalyzer
      .analyzeHistoricalData(instrument, timeframe, startDate, endDate)
      .catch(error => ({ error: error.message }));
    
    const patternResult = await this.performPatternAnalysis(instrument, timeframe)
      .catch(error => ({ error: error.message }));
    
    return { historical: historicalResult, patterns: patternResult };
  }

  private async performPatternAnalysis(instrument: string, timeframe: TimeFrame) {
    // Get recent regime sequence for pattern matching
    const recentSequence = await this.getRecentRegimeSequence(instrument, timeframe);
    
    if (recentSequence.length < 3) {
      return { error: 'Insufficient data for pattern analysis' };
    }
    
    // Get current market context
    const context = await this.getCurrentMarketContext();
    
    // Perform pattern matching
    return this.patternMatcher.findMatchingPatterns(
      recentSequence,
      instrument,
      timeframe,
      context
    );
  }

  private async getRecentRegimeSequence(instrument: string, timeframe: TimeFrame): Promise<MarketRegime[]> {
    // This would fetch recent regime data from database
    // For now, return mock sequence
    return [
      MarketRegime.TRENDING_UP,
      MarketRegime.SIDEWAYS,
      MarketRegime.VOLATILE,
      MarketRegime.TRENDING_DOWN,
      MarketRegime.SIDEWAYS,
    ];
  }

  private async getCurrentMarketContext() {
    const now = new Date();
    return {
      timeOfDay: now.getHours(),
      volatility: 0.45, // Mock volatility
      marketConditions: ['normal_trading', 'moderate_volume'],
    };
  }

  private async synthesizeAnalysis(
    analysisId: string,
    instrument: string,
    timeframe: TimeFrame,
    currentRegime: any,
    analysisResults: any,
    startTime: number
  ): Promise<ComprehensiveRegimeAnalysis> {
    
    const { historical, patterns } = analysisResults;
    
    // Extract historical insights
    const historicalInsights = this.extractHistoricalInsights(historical);
    
    // Extract pattern insights
    const patternInsights = this.extractPatternInsights(patterns);
    
    // Generate predictions
    const predictions = this.generatePredictions(historical, patterns, currentRegime);
    
    // Assess risks
    const riskFactors = this.assessRisks(historical, patterns, currentRegime);
    
    // Calculate analysis quality
    const analysisQuality = this.calculateAnalysisQuality(historical, patterns);
    
    return {
      id: analysisId,
      instrument,
      timeframe,
      timestamp: new Date(),
      currentRegime,
      historicalInsights,
      patternInsights,
      predictions,
      riskFactors,
      analysisQuality,
      processingTimeMs: Date.now() - startTime,
    };
  }

  private extractHistoricalInsights(historicalResult: any): any {
    if (historicalResult?.error) {
      return {
        dominantRegimes: [],
        averageRegimeDuration: 0,
        regimeStability: 'transitional',
        seasonalPatterns: [],
        marketEfficiency: 0.5,
      };
    }
    
    // Extract insights from historical analysis
    const performance = historicalResult.performance || {};
    const cycles = historicalResult.cycles || [];
    
    return {
      dominantRegimes: this.calculateDominantRegimes(performance),
      averageRegimeDuration: this.calculateAverageRegimeDuration(cycles),
      regimeStability: this.assessRegimeStability(cycles),
      seasonalPatterns: this.identifySeasonalPatterns(historicalResult),
      marketEfficiency: this.calculateMarketEfficiency(performance),
    };
  }

  private extractPatternInsights(patternResult: any): any {
    if (patternResult?.error) {
      return {
        strongPatterns: [],
        currentPatternMatch: null,
        patternBasedPrediction: null,
      };
    }
    
    const patterns = patternResult.patterns || [];
    const predictions = patternResult.predictions || [];
    
    return {
      strongPatterns: patterns.slice(0, 5).map((p: any) => ({
        pattern: p.pattern.sequence.join(' → '),
        confidence: Math.round(p.overallScore * 100),
      })),
      currentPatternMatch: patterns.length > 0 ? patterns[0].pattern.id : null,
      patternBasedPrediction: predictions.length > 0 ? {
        nextRegime: predictions[0].regime,
        confidence: predictions[0].confidence,
        timeHorizon: predictions[0].timeHorizon,
      } : null,
    };
  }

  private generatePredictions(historical: any, patterns: any, currentRegime: any): any {
    // Combine historical and pattern-based predictions
    const baseConfidence = currentRegime.confidence || 50;
    
    return {
      shortTerm: {
        regime: this.predictShortTerm(patterns, currentRegime),
        confidence: Math.min(baseConfidence + 10, 85),
        factors: ['current_trend', 'short_patterns'],
      },
      mediumTerm: {
        regime: this.predictMediumTerm(historical, patterns),
        confidence: Math.min(baseConfidence, 75),
        factors: ['historical_cycles', 'pattern_strength'],
      },
      longTerm: {
        regime: this.predictLongTerm(historical),
        confidence: Math.min(baseConfidence - 10, 65),
        factors: ['seasonal_patterns', 'market_cycles'],
      },
    };
  }

  private predictShortTerm(patterns: any, currentRegime: any): MarketRegime {
    if (patterns?.predictions?.length > 0) {
      return patterns.predictions[0].regime;
    }
    return currentRegime.regime || MarketRegime.SIDEWAYS;
  }

  private predictMediumTerm(historical: any, patterns: any): MarketRegime {
    // Use combination of historical and pattern data
    if (patterns?.predictions?.length > 0 && historical?.insights?.length > 0) {
      return patterns.predictions[0].regime;
    }
    return MarketRegime.SIDEWAYS;
  }

  private predictLongTerm(historical: any): MarketRegime {
    // Use primarily historical cycle analysis
    return MarketRegime.SIDEWAYS; // Default prediction
  }

  private assessRisks(historical: any, patterns: any, currentRegime: any): any {
    return {
      regimeInstability: this.calculateRegimeInstability(historical, currentRegime),
      predictionUncertainty: this.calculatePredictionUncertainty(patterns),
      marketAnomalies: this.identifyMarketAnomalies(historical, currentRegime),
      externalRisks: this.identifyExternalRisks(),
    };
  }

  private calculateAnalysisQuality(historical: any, patterns: any): any {
    const dataCompleteness = historical?.error ? 0.3 : 0.8;
    const patternReliability = patterns?.error ? 0.3 : 
      (patterns?.patterns?.length > 0 ? 0.8 : 0.5);
    const historicalAccuracy = historical?.accuracyMetrics?.averageConfidence / 100 || 0.5;
    
    return {
      dataCompleteness,
      patternReliability,
      historicalAccuracy,
      overallConfidence: (dataCompleteness + patternReliability + historicalAccuracy) / 3,
    };
  }

  // Helper methods for analysis synthesis
  private calculateDominantRegimes(performance: any): Array<{ regime: MarketRegime; percentage: number }> {
    // Mock implementation
    return [
      { regime: MarketRegime.SIDEWAYS, percentage: 40 },
      { regime: MarketRegime.TRENDING_UP, percentage: 30 },
      { regime: MarketRegime.VOLATILE, percentage: 20 },
      { regime: MarketRegime.TRENDING_DOWN, percentage: 10 },
    ];
  }

  private calculateAverageRegimeDuration(cycles: any[]): number {
    return 45; // Mock: 45 minutes average
  }

  private assessRegimeStability(cycles: any[]): 'stable' | 'volatile' | 'transitional' {
    return 'transitional'; // Mock assessment
  }

  private identifySeasonalPatterns(historical: any): Array<{ timeOfDay: number; preferredRegime: MarketRegime }> {
    return [
      { timeOfDay: 8, preferredRegime: MarketRegime.VOLATILE }, // Market open
      { timeOfDay: 14, preferredRegime: MarketRegime.TRENDING_UP }, // Afternoon trends
      { timeOfDay: 20, preferredRegime: MarketRegime.LOW_VOLATILITY }, // Evening calm
    ];
  }

  private calculateMarketEfficiency(performance: any): number {
    return 0.72; // Mock efficiency score
  }

  private calculateRegimeInstability(historical: any, currentRegime: any): number {
    return 0.35; // Mock instability score
  }

  private calculatePredictionUncertainty(patterns: any): number {
    return 0.42; // Mock uncertainty score
  }

  private identifyMarketAnomalies(historical: any, currentRegime: any): string[] {
    return ['unusual_volatility_spike', 'regime_persistence_anomaly'];
  }

  private identifyExternalRisks(): string[] {
    return ['economic_announcement_pending', 'geopolitical_tension'];
  }

  private async learnFromCurrentState(
    instrument: string,
    timeframe: TimeFrame,
    currentRegime: any
  ): Promise<void> {
    try {
      const recentSequence = await this.getRecentRegimeSequence(instrument, timeframe);
      const context = await this.getCurrentMarketContext();
      
      await this.patternMatcher.learnFromSequence(
        recentSequence,
        instrument,
        timeframe,
        currentRegime.regime,
        {
          ...context,
          confidence: currentRegime.confidence / 100,
        }
      );
    } catch (error) {
      this.emit('learning_error', { instrument, timeframe, error });
    }
  }

  private updateStats(startTime: number): void {
    const processingTime = Date.now() - startTime;
    this.stats.totalAnalyses++;
    this.stats.averageAnalysisTime = 
      ((this.stats.averageAnalysisTime * (this.stats.totalAnalyses - 1)) + processingTime) / 
      this.stats.totalAnalyses;
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<OrchestrationConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config_updated', this.config);
  }

  /**
   * Get current configuration
   */
  public getConfig(): OrchestrationConfig {
    return { ...this.config };
  }

  /**
   * Clear all caches and reset
   */
  public clearCache(): void {
    this.analysisCache.clear();
    this.historicalAnalyzer.clearCache();
    this.patternMatcher.clearPatterns();
    this.regimeDetector.clearCache();
    
    this.emit('cache_cleared');
  }

  /**
   * Shutdown all services
   */
  public shutdown(): void {
    this.clearCache();
    this.historicalAnalyzer.shutdown();
    this.patternMatcher.shutdown();
    this.regimeDetector.shutdown();
    this.removeAllListeners();
  }
}