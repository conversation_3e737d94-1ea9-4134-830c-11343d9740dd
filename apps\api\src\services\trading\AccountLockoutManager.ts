/**
 * Account Lockout Manager Service
 * 
 * Implements automatic account trading lockout when loss limits are breached.
 * Manages lockout duration, notifications, and integration with authentication service.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import { EventEmitter } from 'events';

// Account Lockout Types
export interface LockoutConfig {
  enableDailyLockouts: boolean;
  enableWeeklyLockouts: boolean;
  dailyCooldownHours: number; // Default: 24 hours
  weeklyCooldownHours: number; // Default: 168 hours (7 days)
  enableEmailNotifications: boolean;
  enablePushNotifications: boolean;
  maxConsecutiveLockouts: number; // Escalation threshold
  escalatedCooldownHours: number; // Extended lockout for repeated violations
}

export interface LockoutStatus {
  userId: string;
  isLocked: boolean;
  lockoutType: 'daily_limit' | 'weekly_limit' | 'consecutive_violation' | null;
  lockedAt?: Date;
  lockoutUntil?: Date;
  reason: string;
  remainingTime?: number; // Minutes remaining
  consecutiveLockouts: number;
  canRequestReview: boolean; // Can request manual review
  notificationsSent: NotificationType[];
}

export interface LockoutEvent {
  userId: string;
  eventType: 'lockout_initiated' | 'lockout_extended' | 'lockout_lifted' | 'notification_sent';
  lockoutType: 'daily_limit' | 'weekly_limit' | 'consecutive_violation';
  timestamp: Date;
  metadata: Record<string, any>;
}

export interface NotificationRequest {
  userId: string;
  type: NotificationType;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  channels: NotificationChannel[];
  scheduledFor?: Date;
}

export type NotificationType = 
  | 'lockout_initiated' 
  | 'lockout_warning' 
  | 'lockout_reminder' 
  | 'lockout_lifted' 
  | 'consecutive_violation';

export type NotificationChannel = 'email' | 'push' | 'in_app' | 'sms';

export interface UnlockRequest {
  userId: string;
  requestedBy: 'user' | 'admin' | 'system';
  reason: string;
  timestamp: Date;
  approvalRequired: boolean;
}

/**
 * AccountLockoutManager - Core service for account trading lockouts
 */
export class AccountLockoutManager extends EventEmitter {
  private readonly lockoutStatuses: Map<string, LockoutStatus> = new Map();
  private readonly lockoutHistory: LockoutEvent[] = [];
  private readonly pendingNotifications: Map<string, NotificationRequest[]> = new Map();
  private readonly unlockRequests: UnlockRequest[] = [];
  private lockoutCheckInterval?: NodeJS.Timeout;

  // Default configuration
  private readonly DEFAULT_CONFIG: LockoutConfig = {
    enableDailyLockouts: true,
    enableWeeklyLockouts: true,
    dailyCooldownHours: 24,
    weeklyCooldownHours: 168, // 7 days
    enableEmailNotifications: true,
    enablePushNotifications: true,
    maxConsecutiveLockouts: 3,
    escalatedCooldownHours: 336 // 14 days
  };

  private config: LockoutConfig;

  constructor(config?: Partial<LockoutConfig>) {
    super();
    this.config = { ...this.DEFAULT_CONFIG, ...config };
    this.startLockoutMonitoring();
    this.setupEventHandlers();
  }

  /**
   * Initialize user lockout status
   */
  public initializeUser(userId: string): void {
    if (this.lockoutStatuses.has(userId)) {
      return; // Already initialized
    }

    const status: LockoutStatus = {
      userId,
      isLocked: false,
      lockoutType: null,
      reason: '',
      consecutiveLockouts: 0,
      canRequestReview: false,
      notificationsSent: []
    };

    this.lockoutStatuses.set(userId, status);
  }

  /**
   * Lock account due to loss limit violation
   */
  public lockAccount(
    userId: string, 
    lockoutType: 'daily_limit' | 'weekly_limit', 
    reason: string
  ): LockoutStatus {
    this.initializeUser(userId);
    const status = this.lockoutStatuses.get(userId)!;

    const now = new Date();
    let cooldownHours: number;

    // Determine cooldown duration
    if (status.consecutiveLockouts >= this.config.maxConsecutiveLockouts) {
      cooldownHours = this.config.escalatedCooldownHours;
      lockoutType = 'consecutive_violation';
    } else if (lockoutType === 'daily_limit') {
      cooldownHours = this.config.dailyCooldownHours;
    } else {
      cooldownHours = this.config.weeklyCooldownHours;
    }

    // Calculate lockout end time - use configured cooldown duration
    const lockoutUntil = new Date(now.getTime() + (cooldownHours * 60 * 60 * 1000));

    // Update status
    const updatedStatus: LockoutStatus = {
      ...status,
      isLocked: true,
      lockoutType,
      lockedAt: now,
      lockoutUntil,
      reason,
      remainingTime: cooldownHours * 60,
      consecutiveLockouts: status.consecutiveLockouts + 1,
      canRequestReview: lockoutType === 'consecutive_violation',
      notificationsSent: []
    };

    this.lockoutStatuses.set(userId, updatedStatus);

    // Log event
    this.logLockoutEvent({
      userId,
      eventType: 'lockout_initiated',
      lockoutType,
      timestamp: now,
      metadata: {
        reason,
        cooldownHours,
        consecutiveCount: updatedStatus.consecutiveLockouts,
        lockoutUntil: lockoutUntil.toISOString()
      }
    });

    // Schedule notifications
    this.scheduleNotifications(userId, updatedStatus);

    // Emit events
    this.emit('accountLocked', updatedStatus);
    
    // Trigger authentication service integration
    this.emit('disableTradingFunctions', { userId, lockoutUntil });

    return updatedStatus;
  }

  /**
   * Manually unlock account (admin function)
   */
  public unlockAccount(userId: string, unlockedBy: 'admin' | 'system', reason: string): boolean {
    const status = this.lockoutStatuses.get(userId);
    if (!status || !status.isLocked) {
      return false; // Already unlocked or user not found
    }

    const now = new Date();
    const updatedStatus: LockoutStatus = {
      ...status,
      isLocked: false,
      lockoutType: null,
      reason: `Unlocked by ${unlockedBy}: ${reason}`,
      remainingTime: 0,
      consecutiveLockouts: (unlockedBy === 'admin' && reason === 'Reset') ? 0 : status.consecutiveLockouts
    };

    this.lockoutStatuses.set(userId, updatedStatus);

    // Log event
    this.logLockoutEvent({
      userId,
      eventType: 'lockout_lifted',
      lockoutType: status.lockoutType!,
      timestamp: now,
      metadata: {
        unlockedBy,
        reason,
        originalLockoutUntil: status.lockoutUntil?.toISOString()
      }
    });

    // Send notification
    this.sendNotification({
      userId,
      type: 'lockout_lifted',
      message: `Your trading account has been unlocked by ${unlockedBy}. Reason: ${reason}`,
      priority: 'high',
      channels: ['email', 'push', 'in_app']
    });

    // Emit events
    this.emit('accountUnlocked', { userId, unlockedBy, reason });
    this.emit('enableTradingFunctions', { userId });

    return true;
  }

  /**
   * Check if account is currently locked
   */
  public isAccountLocked(userId: string): boolean {
    this.checkAutomaticUnlock(userId);
    const status = this.lockoutStatuses.get(userId);
    return status?.isLocked || false;
  }

  /**
   * Get detailed lockout status
   */
  public getLockoutStatus(userId: string): LockoutStatus | null {
    this.checkAutomaticUnlock(userId);
    return this.lockoutStatuses.get(userId) || null;
  }

  /**
   * Get remaining lockout time in minutes
   */
  public getRemainingLockoutTime(userId: string): number {
    // Check for automatic unlock first
    this.checkAutomaticUnlock(userId);
    
    const status = this.lockoutStatuses.get(userId);
    if (!status?.isLocked || !status.lockoutUntil) {
      return 0;
    }

    const now = new Date();
    const remaining = Math.max(0, Math.floor((status.lockoutUntil.getTime() - now.getTime()) / (1000 * 60)));
    
    // Update remaining time
    status.remainingTime = remaining;
    this.lockoutStatuses.set(userId, status);

    return remaining;
  }

  /**
   * Request manual unlock review
   */
  public requestUnlockReview(userId: string, reason: string): boolean {
    const status = this.lockoutStatuses.get(userId);
    if (!status?.isLocked || !status.canRequestReview) {
      return false;
    }

    const unlockRequest: UnlockRequest = {
      userId,
      requestedBy: 'user',
      reason,
      timestamp: new Date(),
      approvalRequired: true
    };

    this.unlockRequests.push(unlockRequest);

    // Notify administrators
    this.emit('unlockRequestSubmitted', unlockRequest);

    return true;
  }

  /**
   * Get pending unlock requests (admin function)
   */
  public getPendingUnlockRequests(): UnlockRequest[] {
    return [...this.unlockRequests.filter(req => req.approvalRequired)];
  }

  /**
   * Approve unlock request (admin function)
   */
  public approveUnlockRequest(requestIndex: number, approvedBy: string): boolean {
    const request = this.unlockRequests[requestIndex];
    if (!request) {
      throw new Error(`Invalid unlock request index: ${requestIndex}`);
    }
    
    if (!request.approvalRequired) {
      throw new Error(`Unlock request ${requestIndex} is not pending approval`);
    }

    const success = this.unlockAccount(request.userId, 'admin', `Request approved by ${approvedBy}: ${request.reason}`);
    
    if (success) {
      request.approvalRequired = false;
      this.emit('unlockRequestApproved', { ...request, approvedBy });
    }

    return success;
  }

  /**
   * Get lockout history for user or all users
   */
  public getLockoutHistory(userId?: string, limit: number = 100): LockoutEvent[] {
    let filteredHistory = [...this.lockoutHistory];
    
    if (userId) {
      filteredHistory = filteredHistory.filter(event => event.userId === userId);
    }

    return filteredHistory
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Check and perform automatic unlocks
   */
  private checkAutomaticUnlock(userId: string): void {
    const status = this.lockoutStatuses.get(userId);
    if (!status?.isLocked || !status.lockoutUntil) {
      return;
    }

    const now = new Date();
    if (now >= status.lockoutUntil) {
      const updatedStatus: LockoutStatus = {
        ...status,
        isLocked: false,
        lockoutType: null,
        remainingTime: 0,
        reason: 'Automatic unlock after cooldown period'
      };

      this.lockoutStatuses.set(userId, updatedStatus);

      // Log event
      this.logLockoutEvent({
        userId,
        eventType: 'lockout_lifted',
        lockoutType: status.lockoutType!,
        timestamp: now,
        metadata: {
          automaticUnlock: true,
          cooldownCompleted: true
        }
      });

      // Send notification
      this.sendNotification({
        userId,
        type: 'lockout_lifted',
        message: 'Your trading account lockout has expired and access has been restored.',
        priority: 'medium',
        channels: ['email', 'push', 'in_app']
      });

      // Emit events
      this.emit('accountUnlocked', { userId, unlockedBy: 'system', reason: 'Automatic unlock' });
      this.emit('enableTradingFunctions', { userId });
    }
  }

  /**
   * Schedule lockout notifications
   */
  private scheduleNotifications(userId: string, status: LockoutStatus): void {
    const notifications: NotificationRequest[] = [];

    // Immediate lockout notification
    notifications.push({
      userId,
      type: 'lockout_initiated',
      message: this.generateLockoutMessage(status),
      priority: 'critical',
      channels: ['email', 'push', 'in_app']
    });

    // Reminder notifications
    if (status.lockoutUntil) {
      const lockoutDuration = status.lockoutUntil.getTime() - (status.lockedAt?.getTime() || Date.now());
      const reminderTimes = [
        lockoutDuration * 0.5,   // Halfway through lockout
        60 * 60 * 1000           // 1 hour before unlock
      ];

      reminderTimes.forEach((reminderTime, index) => {
        const scheduledFor = new Date((status.lockedAt?.getTime() || Date.now()) + reminderTime);
        
        notifications.push({
          userId,
          type: 'lockout_reminder',
          message: `Trading lockout reminder: Your account will be unlocked ${index === 0 ? 'soon' : 'in 1 hour'}.`,
          priority: 'medium',
          channels: ['push', 'in_app'],
          scheduledFor
        });
      });
    }

    // Store and send notifications
    this.pendingNotifications.set(userId, notifications);
    this.processNotifications(userId);
  }

  /**
   * Process and send notifications
   */
  private processNotifications(userId: string): void {
    const notifications = this.pendingNotifications.get(userId) || [];
    const now = new Date();

    notifications.forEach(notification => {
      if (!notification.scheduledFor || notification.scheduledFor <= now) {
        this.sendNotification(notification);
      }
    });

    // Keep only future notifications
    const futureNotifications = notifications.filter(n => n.scheduledFor && n.scheduledFor > now);
    this.pendingNotifications.set(userId, futureNotifications);
  }

  /**
   * Send notification
   */
  private sendNotification(notification: NotificationRequest): void {
    // Update user's sent notifications
    const status = this.lockoutStatuses.get(notification.userId);
    if (status) {
      status.notificationsSent.push(notification.type);
      this.lockoutStatuses.set(notification.userId, status);
    }

    // Log notification event
    this.logLockoutEvent({
      userId: notification.userId,
      eventType: 'notification_sent',
      lockoutType: status?.lockoutType || 'daily_limit',
      timestamp: new Date(),
      metadata: {
        notificationType: notification.type,
        channels: notification.channels,
        priority: notification.priority
      }
    });

    // Emit notification event for external handlers
    this.emit('notificationRequired', notification);
  }

  /**
   * Generate lockout message
   */
  private generateLockoutMessage(status: LockoutStatus): string {
    const typeText = status.lockoutType === 'daily_limit' ? 'daily' : 
                    status.lockoutType === 'weekly_limit' ? 'weekly' : 'consecutive';
    
    const until = status.lockoutUntil?.toLocaleString() || 'unknown time';
    
    let message = `Your trading account has been locked due to ${typeText} loss limit violation. `;
    message += `Trading functions will be disabled until ${until}. `;
    
    if (status.consecutiveLockouts > 1) {
      message += `This is lockout #${status.consecutiveLockouts}. `;
    }
    
    if (status.canRequestReview) {
      message += 'You may request a manual review for early unlock.';
    }

    return message;
  }

  /**
   * Start monitoring lockout statuses
   */
  private startLockoutMonitoring(): void {
    // Check every minute for automatic unlocks and pending notifications
    this.lockoutCheckInterval = setInterval(() => {
      for (const userId of this.lockoutStatuses.keys()) {
        this.checkAutomaticUnlock(userId);
        this.processNotifications(userId);
      }
    }, 60 * 1000); // Every minute
  }

  /**
   * Log lockout event
   */
  private logLockoutEvent(event: LockoutEvent): void {
    this.lockoutHistory.push(event);
    
    // Keep only last 10,000 events to prevent memory issues
    if (this.lockoutHistory.length > 10000) {
      this.lockoutHistory.splice(0, this.lockoutHistory.length - 10000);
    }

    this.emit('lockoutEventLogged', event);
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.on('accountLocked', (status: LockoutStatus) => {
      console.warn(`Account locked for user ${status.userId}:`, {
        type: status.lockoutType,
        until: status.lockoutUntil,
        consecutive: status.consecutiveLockouts
      });
    });

    this.on('accountUnlocked', (event: { userId: string; unlockedBy: string; reason: string }) => {
      console.info(`Account unlocked for user ${event.userId}:`, {
        unlockedBy: event.unlockedBy,
        reason: event.reason
      });
    });

    this.on('unlockRequestSubmitted', (request: UnlockRequest) => {
      console.info(`Unlock request submitted for user ${request.userId}:`, {
        reason: request.reason,
        timestamp: request.timestamp
      });
    });
  }

  /**
   * Cleanup and destroy
   */
  public destroy(): void {
    if (this.lockoutCheckInterval) {
      clearInterval(this.lockoutCheckInterval);
    }
    
    this.removeAllListeners();
    this.lockoutStatuses.clear();
    this.pendingNotifications.clear();
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<LockoutConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('configUpdated', this.config);
  }

  /**
   * Get current configuration
   */
  public getConfig(): LockoutConfig {
    return { ...this.config };
  }

  /**
   * Validate configuration
   */
  public static validateConfig(config: LockoutConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (config.dailyCooldownHours <= 0 || config.dailyCooldownHours > 168) {
      errors.push('Daily cooldown must be between 1 and 168 hours');
    }

    if (config.weeklyCooldownHours <= 0 || config.weeklyCooldownHours > 8760) {
      errors.push('Weekly cooldown must be between 1 and 8760 hours');
    }

    if (config.maxConsecutiveLockouts <= 0 || config.maxConsecutiveLockouts > 10) {
      errors.push('Max consecutive lockouts must be between 1 and 10');
    }

    if (config.escalatedCooldownHours <= config.weeklyCooldownHours) {
      errors.push('Escalated cooldown must be longer than weekly cooldown');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

/**
 * Factory function to create AccountLockoutManager instance
 */
export function createAccountLockoutManager(config?: Partial<LockoutConfig>): AccountLockoutManager {
  return new AccountLockoutManager(config);
}

/**
 * Default export for convenience
 */
export default AccountLockoutManager;