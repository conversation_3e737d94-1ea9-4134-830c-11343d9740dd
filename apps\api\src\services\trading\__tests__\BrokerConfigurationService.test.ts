/**
 * Broker Configuration Service Tests
 * 
 * Test suite for the BrokerConfigurationService
 * Part of Task 1: Multi-Broker Configuration System
 */

import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { BrokerConfigurationService } from '../BrokerConfigurationService.js';
import type { CreateBrokerConfigurationRequest } from '@golddaddy/types';

// Mock crypto module first
vi.mock('crypto', () => ({
  default: {
    randomBytes: vi.fn(() => Buffer.from('1234567890123456', 'hex')), // 16 bytes IV
    createHash: vi.fn(() => ({
      update: vi.fn().mockReturnThis(),
      digest: vi.fn(() => Buffer.from('test-key-32-chars-long-enough!!!', 'utf8'))
    })),
    createCipherGCM: vi.fn(() => ({
      setAAD: vi.fn(),
      update: vi.fn(() => 'encrypted'),
      final: vi.fn(() => 'data'),
      getAuthTag: vi.fn(() => Buffer.from('1234567890123456', 'hex')) // 16 bytes auth tag
    })),
    createDecipherGCM: vi.fn(() => ({
      setAuthTag: vi.fn(),
      setAAD: vi.fn(),
      update: vi.fn(() => 'decrypted'),
      final: vi.fn(() => '_password')
    }))
  }
}));

// Mock Prisma client
const mockPrisma = {
  brokerConfiguration: {
    findUnique: vi.fn(),
    findFirst: vi.fn(),
    findMany: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  },
} as unknown as PrismaClient;

// Mock encryption functions
vi.mock('../../lib/encryption.js', () => ({
  encrypt: vi.fn().mockReturnValue('encrypted_password'),
  decrypt: vi.fn().mockReturnValue('decrypted_password'),
}));

describe('BrokerConfigurationService', () => {
  let service: BrokerConfigurationService;
  const mockUserId = 'user_123';
  const mockBrokerId = 'broker_123';

  beforeEach(() => {
    // Mock environment variables
    process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters-long-enough';
    process.env.ANONYMIZATION_SECRET = 'test-anonymization-secret';
    
    service = new BrokerConfigurationService(mockPrisma);
    vi.clearAllMocks();
  });

  describe('createBrokerConfiguration', () => {
    const validRequest: CreateBrokerConfigurationRequest = {
      brokerName: 'Test Broker',
      priority: 1,
      connectionDetails: {
        server: 'demo.broker.com',
        login: 'test_login',
        password: 'test_password',
        timeout: 10000
      },
      features: ['trading', 'streaming'],
      healthCheck: {
        interval: 30000,
        timeout: 5000,
        retryCount: 5
      }
    };

    it('should create a broker configuration successfully', async () => {
      // Mock database responses
      (mockPrisma.brokerConfiguration.findUnique as Mock).mockResolvedValue(null);
      (mockPrisma.brokerConfiguration.create as Mock).mockResolvedValue({
        id: mockBrokerId,
        userId: mockUserId,
        brokerName: 'Test Broker',
        priority: 1,
        server: 'demo.broker.com',
        login: 'test_login',
        password: 'encrypted_password',
        timeout: 10000,
        healthCheckInterval: 30000,
        healthCheckTimeout: 5000,
        retryCount: 5,
        status: 'INACTIVE',
        features: ['trading', 'streaming'],
        isHealthy: false,
        failureCount: 0,
        lastHealthCheck: null,
        lastError: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null
      });

      const result = await service.createBrokerConfiguration(mockUserId, validRequest);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.brokerName).toBe('Test Broker');
      expect(result.data?.priority).toBe(1);
      expect(result.data?.connectionDetails.password).toBe('[ENCRYPTED]');
      expect(result.data?.features).toEqual(['trading', 'streaming']);
      expect(result.data?.status).toBe('INACTIVE');
    });

    it('should fail when broker name already exists', async () => {
      // Mock existing broker
      (mockPrisma.brokerConfiguration.findUnique as Mock).mockResolvedValue({
        id: 'existing_broker',
        userId: mockUserId,
        brokerName: 'Test Broker'
      });

      const result = await service.createBrokerConfiguration(mockUserId, validRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('BROKER_EXISTS');
      expect(result.error?.message).toContain('already exists');
    });

    it('should fail with validation error', async () => {
      // Mock no existing broker
      (mockPrisma.brokerConfiguration.findUnique as Mock).mockResolvedValue(null);
      
      // Create an invalid request (missing brokerName)
      const invalidRequest = {
        ...validRequest,
        brokerName: '' // Empty broker name should fail validation
      };

      const result = await service.createBrokerConfiguration(mockUserId, invalidRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('VALIDATION_ERROR');
      expect(result.error?.details).toBeDefined();
    });

    it('should handle database errors gracefully', async () => {
      (mockPrisma.brokerConfiguration.findUnique as Mock).mockResolvedValue(null);
      (mockPrisma.brokerConfiguration.create as Mock).mockRejectedValue(new Error('Database error'));

      const result = await service.createBrokerConfiguration(mockUserId, validRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('INTERNAL_ERROR');
      expect(result.error?.message).toBe('Failed to create broker configuration');
    });
  });

  describe('getBrokerConfigurations', () => {
    it('should return all broker configurations for user', async () => {
      const mockConfigs = [
        {
          id: 'broker_1',
          userId: mockUserId,
          brokerName: 'Broker 1',
          priority: 1,
          server: 'server1.com',
          login: 'login1',
          password: 'encrypted_password',
          timeout: 10000,
          healthCheckInterval: 30000,
          healthCheckTimeout: 5000,
          retryCount: 5,
          status: 'ACTIVE',
          features: ['trading'],
          isHealthy: true,
          failureCount: 0,
          lastHealthCheck: new Date(),
          lastError: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null
        },
        {
          id: 'broker_2',
          userId: mockUserId,
          brokerName: 'Broker 2',
          priority: 2,
          server: 'server2.com',
          login: 'login2',
          password: 'encrypted_password',
          timeout: 15000,
          healthCheckInterval: 30000,
          healthCheckTimeout: 5000,
          retryCount: 5,
          status: 'INACTIVE',
          features: ['streaming'],
          isHealthy: false,
          failureCount: 3,
          lastHealthCheck: null,
          lastError: 'Connection failed',
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null
        }
      ];

      (mockPrisma.brokerConfiguration.findMany as Mock).mockResolvedValue(mockConfigs);

      const result = await service.getBrokerConfigurations(mockUserId);

      expect(result.success).toBe(true);
      expect(result.data).toHaveLength(2);
      expect(result.data![0].brokerName).toBe('Broker 1');
      expect(result.data![1].brokerName).toBe('Broker 2');
      // Verify passwords are masked
      expect(result.data![0].connectionDetails.password).toBe('[ENCRYPTED]');
      expect(result.data![1].connectionDetails.password).toBe('[ENCRYPTED]');
    });

    it('should return empty array when no configurations exist', async () => {
      (mockPrisma.brokerConfiguration.findMany as Mock).mockResolvedValue([]);

      const result = await service.getBrokerConfigurations(mockUserId);

      expect(result.success).toBe(true);
      expect(result.data).toEqual([]);
    });

    it('should handle database errors', async () => {
      (mockPrisma.brokerConfiguration.findMany as Mock).mockRejectedValue(new Error('Database error'));

      const result = await service.getBrokerConfigurations(mockUserId);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('INTERNAL_ERROR');
    });
  });

  describe('getBrokerConfiguration', () => {
    it('should return specific broker configuration', async () => {
      const mockConfig = {
        id: mockBrokerId,
        userId: mockUserId,
        brokerName: 'Test Broker',
        priority: 1,
        server: 'demo.broker.com',
        login: 'test_login',
        password: 'encrypted_password',
        timeout: 10000,
        healthCheckInterval: 30000,
        healthCheckTimeout: 5000,
        retryCount: 5,
        status: 'ACTIVE',
        features: ['trading', 'streaming'],
        isHealthy: true,
        failureCount: 0,
        lastHealthCheck: new Date(),
        lastError: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null
      };

      (mockPrisma.brokerConfiguration.findFirst as Mock).mockResolvedValue(mockConfig);

      const result = await service.getBrokerConfiguration(mockUserId, mockBrokerId);

      expect(result.success).toBe(true);
      expect(result.data?.id).toBe(mockBrokerId);
      expect(result.data?.brokerName).toBe('Test Broker');
      expect(result.data?.connectionDetails.password).toBe('[ENCRYPTED]');
    });

    it('should return not found error when configuration does not exist', async () => {
      (mockPrisma.brokerConfiguration.findFirst as Mock).mockResolvedValue(null);

      const result = await service.getBrokerConfiguration(mockUserId, mockBrokerId);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('NOT_FOUND');
      expect(result.error?.message).toBe('Broker configuration not found');
    });
  });

  describe('updateBrokerConfiguration', () => {
    const updateRequest = {
      brokerName: 'Updated Broker',
      priority: 2,
      status: 'ACTIVE' as const
    };

    it('should update broker configuration successfully', async () => {
      // Mock existing broker
      (mockPrisma.brokerConfiguration.findFirst as Mock).mockResolvedValue({
        id: mockBrokerId,
        userId: mockUserId,
        brokerName: 'Test Broker'
      });

      // Mock update result
      (mockPrisma.brokerConfiguration.update as Mock).mockResolvedValue({
        id: mockBrokerId,
        userId: mockUserId,
        brokerName: 'Updated Broker',
        priority: 2,
        server: 'demo.broker.com',
        login: 'test_login',
        password: 'encrypted_password',
        timeout: 10000,
        healthCheckInterval: 30000,
        healthCheckTimeout: 5000,
        retryCount: 5,
        status: 'ACTIVE',
        features: ['trading'],
        isHealthy: true,
        failureCount: 0,
        lastHealthCheck: new Date(),
        lastError: null,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null
      });

      const result = await service.updateBrokerConfiguration(mockUserId, mockBrokerId, updateRequest);

      expect(result.success).toBe(true);
      expect(result.data?.brokerName).toBe('Updated Broker');
      expect(result.data?.priority).toBe(2);
      expect(result.data?.status).toBe('ACTIVE');
    });

    it('should return not found error for non-existent broker', async () => {
      (mockPrisma.brokerConfiguration.findFirst as Mock).mockResolvedValue(null);

      const result = await service.updateBrokerConfiguration(mockUserId, mockBrokerId, updateRequest);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('NOT_FOUND');
    });
  });

  describe('deleteBrokerConfiguration', () => {
    it('should soft delete broker configuration', async () => {
      // Mock existing broker
      (mockPrisma.brokerConfiguration.findFirst as Mock).mockResolvedValue({
        id: mockBrokerId,
        userId: mockUserId,
        brokerName: 'Test Broker'
      });

      (mockPrisma.brokerConfiguration.update as Mock).mockResolvedValue({});

      const result = await service.deleteBrokerConfiguration(mockUserId, mockBrokerId);

      expect(result.success).toBe(true);
      expect(mockPrisma.brokerConfiguration.update).toHaveBeenCalledWith({
        where: { id: mockBrokerId },
        data: {
          deletedAt: expect.any(Date),
          status: 'INACTIVE'
        }
      });
    });

    it('should return not found error for non-existent broker', async () => {
      (mockPrisma.brokerConfiguration.findFirst as Mock).mockResolvedValue(null);

      const result = await service.deleteBrokerConfiguration(mockUserId, mockBrokerId);

      expect(result.success).toBe(false);
      expect(result.error?.code).toBe('NOT_FOUND');
    });
  });

  describe('updateBrokerHealth', () => {
    it('should update broker health status to healthy', async () => {
      (mockPrisma.brokerConfiguration.update as Mock).mockResolvedValue({});

      await service.updateBrokerHealth(mockBrokerId, true, 150);

      expect(mockPrisma.brokerConfiguration.update).toHaveBeenCalledWith({
        where: { id: mockBrokerId },
        data: {
          isHealthy: true,
          lastHealthCheck: expect.any(Date),
          lastError: null,
          status: 'ACTIVE',
          failureCount: 0
        }
      });
    });

    it('should update broker health status to unhealthy', async () => {
      (mockPrisma.brokerConfiguration.update as Mock).mockResolvedValue({});

      await service.updateBrokerHealth(mockBrokerId, false, 5000, 'Connection timeout');

      expect(mockPrisma.brokerConfiguration.update).toHaveBeenCalledWith({
        where: { id: mockBrokerId },
        data: {
          isHealthy: false,
          lastHealthCheck: expect.any(Date),
          lastError: 'Connection timeout',
          failureCount: { increment: 1 },
          status: 'FAILED'
        }
      });
    });
  });

  describe('getBrokersForFailover', () => {
    it('should return brokers ordered by priority for failover', async () => {
      const mockBrokers = [
        {
          id: 'broker_1',
          userId: mockUserId,
          brokerName: 'Primary Broker',
          priority: 1,
          features: ['trading', 'streaming'],
          status: 'ACTIVE',
          failureCount: 0,
          server: 'server1.com',
          login: 'login1',
          password: 'encrypted_password',
          timeout: 10000,
          healthCheckInterval: 30000,
          healthCheckTimeout: 5000,
          retryCount: 5,
          isHealthy: true,
          lastHealthCheck: new Date(),
          lastError: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null
        },
        {
          id: 'broker_2',
          userId: mockUserId,
          brokerName: 'Secondary Broker',
          priority: 2,
          features: ['trading'],
          status: 'INACTIVE',
          failureCount: 1,
          server: 'server2.com',
          login: 'login2',
          password: 'encrypted_password',
          timeout: 10000,
          healthCheckInterval: 30000,
          healthCheckTimeout: 5000,
          retryCount: 5,
          isHealthy: false,
          lastHealthCheck: null,
          lastError: null,
          createdAt: new Date(),
          updatedAt: new Date(),
          deletedAt: null
        }
      ];

      (mockPrisma.brokerConfiguration.findMany as Mock).mockResolvedValue(mockBrokers);

      const result = await service.getBrokersForFailover(mockUserId, 'trading');

      expect(result).toHaveLength(2);
      expect(result[0].brokerName).toBe('Primary Broker');
      expect(result[1].brokerName).toBe('Secondary Broker');
      // Verify feature filtering worked
      expect(result[0].features).toContain('trading');
      expect(result[1].features).toContain('trading');
    });

    it('should filter brokers by required feature', async () => {
      const mockBrokers = [
        {
          id: 'broker_1',
          priority: 1,
          features: ['trading', 'streaming'],
          status: 'ACTIVE',
          failureCount: 0
        },
        {
          id: 'broker_2',
          priority: 2,
          features: ['monitoring'], // Does not have 'trading'
          status: 'ACTIVE',
          failureCount: 0
        }
      ];

      (mockPrisma.brokerConfiguration.findMany as Mock).mockResolvedValue(mockBrokers);

      const result = await service.getBrokersForFailover(mockUserId, 'trading');

      // Should only return broker_1 which has trading feature
      expect(result).toHaveLength(1);
      expect(result[0].id).toBe('broker_1');
    });

    it('should handle database errors gracefully', async () => {
      (mockPrisma.brokerConfiguration.findMany as Mock).mockRejectedValue(new Error('Database error'));

      const result = await service.getBrokersForFailover(mockUserId);

      expect(result).toEqual([]);
    });
  });

  describe('getDecryptedCredentials', () => {
    it('should return decrypted credentials for internal use', async () => {
      const mockConfig = {
        id: mockBrokerId,
        userId: mockUserId,
        login: 'test_login',
        password: '31323334353637383930313233343536:31323334353637383930313233343536:encrypteddata', // iv:authTag:data format
        server: 'demo.broker.com'
      };

      (mockPrisma.brokerConfiguration.findFirst as Mock).mockResolvedValue(mockConfig);

      const result = await service.getDecryptedCredentials(mockUserId, mockBrokerId);

      expect(result).toEqual({
        login: 'test_login',
        password: 'decrypted_password', // From mocked decrypt function
        server: 'demo.broker.com'
      });
    });

    it('should return null for non-existent broker', async () => {
      (mockPrisma.brokerConfiguration.findFirst as Mock).mockResolvedValue(null);

      const result = await service.getDecryptedCredentials(mockUserId, mockBrokerId);

      expect(result).toBeNull();
    });
  });
});