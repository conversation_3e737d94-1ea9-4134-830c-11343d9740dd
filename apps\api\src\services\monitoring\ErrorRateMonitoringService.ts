import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { performance } from 'perf_hooks';

/**
 * Error Rate Monitoring Service for tracking and analyzing system errors
 * Implements real-time error classification and alert generation
 */
export class ErrorRateMonitoringService extends EventEmitter {
  private prisma: PrismaClient;
  private errorMetrics: Map<string, ErrorMetrics> = new Map();
  private recentErrors: ErrorRecord[] = [];
  private isMonitoring = false;
  private monitoringInterval: NodeJS.Timeout | null = null;
  
  private readonly MONITORING_INTERVAL = 60000; // 1 minute
  private readonly ERROR_RETENTION_LIMIT = 1000; // Keep last 1000 errors
  private readonly ERROR_RATE_THRESHOLDS = {
    warning: 5, // 5% error rate
    critical: 15, // 15% error rate
  };

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  /**
   * Start error rate monitoring
   */
  async startMonitoring(config: ErrorMonitoringConfig = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('Error rate monitoring already active');
      return;
    }

    console.log('🔍 Starting error rate monitoring...');
    this.isMonitoring = true;

    // Apply configuration
    const thresholds = config.errorRateThresholds || this.ERROR_RATE_THRESHOLDS;

    // Initialize error metrics for different services
    const services = ['api', 'mt5-bridge', 'database', 'websocket'];
    for (const service of services) {
      this.errorMetrics.set(service, {
        serviceName: service,
        totalRequests: 0,
        totalErrors: 0,
        errorRate: 0,
        lastCalculation: new Date(),
        errorsByType: new Map(),
        errorsBySeverity: new Map(),
        recentErrors: [],
        alertsTriggered: 0,
        consecutiveErrors: 0,
        longestErrorStreak: 0,
      });
    }

    // Start periodic error rate calculations
    this.monitoringInterval = setInterval(async () => {
      await this.calculateErrorRates();
    }, config.intervalMs || this.MONITORING_INTERVAL);

    console.log('✅ Error rate monitoring started');
    this.emit('monitoringStarted', { config });
  }

  /**
   * Stop error rate monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ Stopping error rate monitoring...');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('✅ Error rate monitoring stopped');
    this.emit('monitoringStopped');
  }

  /**
   * Record an error occurrence
   */
  async recordError(error: ErrorOccurrence): Promise<void> {
    const timestamp = new Date();
    
    // Classify error severity
    const severity = this.classifyErrorSeverity(error);
    
    // Create error record
    const errorRecord: ErrorRecord = {
      id: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp,
      service: error.service,
      errorType: error.type,
      severity,
      message: error.message,
      stackTrace: error.stackTrace,
      requestId: error.requestId,
      userId: error.userId,
      endpoint: error.endpoint,
      httpStatus: error.httpStatus,
      metadata: error.metadata,
    };

    // Add to recent errors
    this.recentErrors.push(errorRecord);
    
    // Keep only recent errors
    if (this.recentErrors.length > this.ERROR_RETENTION_LIMIT) {
      this.recentErrors = this.recentErrors.slice(-this.ERROR_RETENTION_LIMIT);
    }

    // Update service metrics
    await this.updateServiceErrorMetrics(error.service, errorRecord);

    // Check for error rate thresholds
    await this.checkErrorRateThresholds(error.service);

    // Store error in database
    await this.storeErrorRecord(errorRecord);

    // Emit error event
    this.emit('errorRecorded', errorRecord);

    console.log(`🔴 Error recorded: ${error.service} - ${severity} - ${error.type}`);
  }

  /**
   * Record a successful request (to calculate error rates)
   */
  async recordSuccess(request: RequestRecord): Promise<void> {
    const metrics = this.errorMetrics.get(request.service);
    if (metrics) {
      metrics.totalRequests++;
      metrics.consecutiveErrors = 0; // Reset consecutive error count
      
      // Recalculate error rate
      metrics.errorRate = metrics.totalErrors > 0 
        ? (metrics.totalErrors / metrics.totalRequests) * 100 
        : 0;
    }
  }

  /**
   * Classify error severity based on error type and context
   */
  private classifyErrorSeverity(error: ErrorOccurrence): ErrorSeverity {
    // Database errors
    if (error.type.includes('database') || error.type.includes('sql')) {
      return error.message.includes('connection') ? 'critical' : 'high';
    }

    // Authentication/Authorization errors
    if (error.type.includes('auth') || error.httpStatus === 401 || error.httpStatus === 403) {
      return 'medium';
    }

    // Validation errors
    if (error.type.includes('validation') || error.httpStatus === 400) {
      return 'low';
    }

    // Trading-related errors
    if (error.type.includes('trading') || error.type.includes('mt5')) {
      return error.message.includes('connection') ? 'critical' : 'high';
    }

    // Server errors
    if (error.httpStatus && error.httpStatus >= 500) {
      return error.httpStatus === 500 ? 'high' : 'critical';
    }

    // Network/timeout errors
    if (error.type.includes('timeout') || error.type.includes('network')) {
      return 'medium';
    }

    // Rate limiting
    if (error.httpStatus === 429) {
      return 'low';
    }

    // Default classification
    return 'medium';
  }

  /**
   * Update error metrics for a specific service
   */
  private async updateServiceErrorMetrics(service: string, error: ErrorRecord): Promise<void> {
    let metrics = this.errorMetrics.get(service);
    if (!metrics) {
      // Initialize metrics if not exists
      metrics = {
        serviceName: service,
        totalRequests: 0,
        totalErrors: 0,
        errorRate: 0,
        lastCalculation: new Date(),
        errorsByType: new Map(),
        errorsBySeverity: new Map(),
        recentErrors: [],
        alertsTriggered: 0,
        consecutiveErrors: 0,
        longestErrorStreak: 0,
      };
      this.errorMetrics.set(service, metrics);
    }

    // Update counts
    metrics.totalErrors++;
    metrics.consecutiveErrors++;
    metrics.longestErrorStreak = Math.max(metrics.longestErrorStreak, metrics.consecutiveErrors);

    // Update error type counts
    const typeCount = metrics.errorsByType.get(error.errorType) || 0;
    metrics.errorsByType.set(error.errorType, typeCount + 1);

    // Update error severity counts
    const severityCount = metrics.errorsBySeverity.get(error.severity) || 0;
    metrics.errorsBySeverity.set(error.severity, severityCount + 1);

    // Add to recent errors for this service
    metrics.recentErrors.push(error);
    if (metrics.recentErrors.length > 100) { // Keep last 100 errors per service
      metrics.recentErrors = metrics.recentErrors.slice(-100);
    }

    // Recalculate error rate
    if (metrics.totalRequests > 0) {
      metrics.errorRate = (metrics.totalErrors / metrics.totalRequests) * 100;
    }

    metrics.lastCalculation = new Date();
  }

  /**
   * Check error rate thresholds and generate alerts
   */
  private async checkErrorRateThresholds(service: string): Promise<void> {
    const metrics = this.errorMetrics.get(service);
    if (!metrics || metrics.totalRequests < 10) { // Need minimum requests for meaningful rate
      return;
    }

    const errorRate = metrics.errorRate;

    // Generate alerts based on thresholds
    if (errorRate >= this.ERROR_RATE_THRESHOLDS.critical) {
      await this.generateAlert(service, 'critical', `Critical error rate: ${errorRate.toFixed(1)}%`);
    } else if (errorRate >= this.ERROR_RATE_THRESHOLDS.warning) {
      await this.generateAlert(service, 'warning', `High error rate: ${errorRate.toFixed(1)}%`);
    }

    // Check for consecutive errors
    if (metrics.consecutiveErrors >= 5) {
      await this.generateAlert(service, 'high', `${metrics.consecutiveErrors} consecutive errors detected`);
    }

    // Check for error spikes (more than 10 errors in last minute)
    const oneMinuteAgo = new Date(Date.now() - 60000);
    const recentErrors = metrics.recentErrors.filter(e => e.timestamp > oneMinuteAgo);
    if (recentErrors.length >= 10) {
      await this.generateAlert(service, 'high', `Error spike detected: ${recentErrors.length} errors in last minute`);
    }
  }

  /**
   * Generate an alert for error conditions
   */
  private async generateAlert(service: string, severity: 'low' | 'medium' | 'high' | 'critical', message: string): Promise<void> {
    const alert: ErrorRateAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      service,
      severity,
      message,
      timestamp: new Date(),
      errorRate: this.errorMetrics.get(service)?.errorRate || 0,
      consecutiveErrors: this.errorMetrics.get(service)?.consecutiveErrors || 0,
      acknowledged: false,
    };

    // Update metrics
    const metrics = this.errorMetrics.get(service);
    if (metrics) {
      metrics.alertsTriggered++;
    }

    // Emit alert
    this.emit('alert', alert);

    console.log(`🚨 Error rate alert: ${service} - ${severity} - ${message}`);
  }

  /**
   * Calculate error rates for all services
   */
  private async calculateErrorRates(): Promise<void> {
    const calculations: ErrorRateCalculation[] = [];

    for (const [service, metrics] of this.errorMetrics) {
      const calculation: ErrorRateCalculation = {
        service,
        totalRequests: metrics.totalRequests,
        totalErrors: metrics.totalErrors,
        errorRate: metrics.errorRate,
        timestamp: new Date(),
        topErrorTypes: this.getTopErrorTypes(metrics),
        errorDistribution: this.getErrorDistribution(metrics),
      };

      calculations.push(calculation);
    }

    this.emit('errorRatesCalculated', calculations);
    
    // Store calculations in database periodically
    await this.storeErrorRateCalculations(calculations);
  }

  /**
   * Get top error types for a service
   */
  private getTopErrorTypes(metrics: ErrorMetrics): { type: string; count: number }[] {
    return Array.from(metrics.errorsByType.entries())
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // Top 5 error types
  }

  /**
   * Get error distribution by severity
   */
  private getErrorDistribution(metrics: ErrorMetrics): { severity: ErrorSeverity; count: number; percentage: number }[] {
    const total = metrics.totalErrors;
    return Array.from(metrics.errorsBySeverity.entries())
      .map(([severity, count]) => ({
        severity: severity as ErrorSeverity,
        count,
        percentage: total > 0 ? (count / total) * 100 : 0,
      }))
      .sort((a, b) => b.count - a.count);
  }

  /**
   * Store error record in database
   */
  private async storeErrorRecord(error: ErrorRecord): Promise<void> {
    try {
      // This would store in a dedicated error_logs table
      console.log(`💾 Storing error record ${error.id}`);
    } catch (dbError) {
      console.error('Failed to store error record:', dbError);
      // Don't throw to avoid disrupting error monitoring
    }
  }

  /**
   * Store error rate calculations in database
   */
  private async storeErrorRateCalculations(calculations: ErrorRateCalculation[]): Promise<void> {
    try {
      for (const calc of calculations) {
        console.log(`📊 Error rates - ${calc.service}: ${calc.errorRate.toFixed(2)}% (${calc.totalErrors}/${calc.totalRequests})`);
      }
    } catch (error) {
      console.error('Failed to store error rate calculations:', error);
    }
  }

  /**
   * Get current error statistics
   */
  getErrorStatistics(): ErrorStatistics {
    const serviceStats: ServiceErrorStats[] = [];

    for (const [service, metrics] of this.errorMetrics) {
      serviceStats.push({
        service,
        totalRequests: metrics.totalRequests,
        totalErrors: metrics.totalErrors,
        errorRate: metrics.errorRate,
        consecutiveErrors: metrics.consecutiveErrors,
        longestErrorStreak: metrics.longestErrorStreak,
        alertsTriggered: metrics.alertsTriggered,
        lastCalculation: metrics.lastCalculation,
        topErrorTypes: this.getTopErrorTypes(metrics),
        errorDistribution: this.getErrorDistribution(metrics),
        recentErrorsCount: metrics.recentErrors.length,
      });
    }

    return {
      overallErrorRate: this.calculateOverallErrorRate(),
      totalErrors: Array.from(this.errorMetrics.values()).reduce((sum, m) => sum + m.totalErrors, 0),
      totalRequests: Array.from(this.errorMetrics.values()).reduce((sum, m) => sum + m.totalRequests, 0),
      serviceStats,
      recentErrorsCount: this.recentErrors.length,
      isMonitoring: this.isMonitoring,
      lastUpdate: new Date(),
    };
  }

  /**
   * Calculate overall error rate across all services
   */
  private calculateOverallErrorRate(): number {
    const totalRequests = Array.from(this.errorMetrics.values()).reduce((sum, m) => sum + m.totalRequests, 0);
    const totalErrors = Array.from(this.errorMetrics.values()).reduce((sum, m) => sum + m.totalErrors, 0);
    
    return totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;
  }

  /**
   * Get recent errors with filtering
   */
  getRecentErrors(filters: ErrorFilters = {}): ErrorRecord[] {
    let filteredErrors = [...this.recentErrors];

    if (filters.service) {
      filteredErrors = filteredErrors.filter(e => e.service === filters.service);
    }

    if (filters.severity) {
      filteredErrors = filteredErrors.filter(e => e.severity === filters.severity);
    }

    if (filters.errorType) {
      filteredErrors = filteredErrors.filter(e => e.errorType === filters.errorType);
    }

    if (filters.timeRange) {
      const cutoff = new Date(Date.now() - this.getTimeRangeMs(filters.timeRange));
      filteredErrors = filteredErrors.filter(e => e.timestamp > cutoff);
    }

    if (filters.limit) {
      filteredErrors = filteredErrors.slice(-filters.limit);
    }

    return filteredErrors.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Convert time range to milliseconds
   */
  private getTimeRangeMs(timeRange: string): number {
    switch (timeRange) {
      case '5m': return 5 * 60 * 1000;
      case '15m': return 15 * 60 * 1000;
      case '1h': return 60 * 60 * 1000;
      case '6h': return 6 * 60 * 60 * 1000;
      case '24h': return 24 * 60 * 60 * 1000;
      default: return 60 * 60 * 1000;
    }
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus(): ErrorMonitoringStatus {
    return {
      isActive: this.isMonitoring,
      servicesMonitored: this.errorMetrics.size,
      totalErrorsTracked: this.recentErrors.length,
      lastCalculation: this.errorMetrics.size > 0 
        ? Math.max(...Array.from(this.errorMetrics.values()).map(m => m.lastCalculation.getTime()))
        : null,
    };
  }

  /**
   * Reset error metrics (useful for testing)
   */
  resetMetrics(): void {
    this.errorMetrics.clear();
    this.recentErrors = [];
    console.log('🧹 Error metrics reset');
  }
}

// Type definitions
type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

interface ErrorMonitoringConfig {
  intervalMs?: number;
  errorRateThresholds?: {
    warning: number;
    critical: number;
  };
}

interface ErrorOccurrence {
  service: string;
  type: string;
  message: string;
  stackTrace?: string;
  requestId?: string;
  userId?: string;
  endpoint?: string;
  httpStatus?: number;
  metadata?: Record<string, any>;
}

interface RequestRecord {
  service: string;
  endpoint?: string;
  userId?: string;
  requestId?: string;
}

interface ErrorRecord {
  id: string;
  timestamp: Date;
  service: string;
  errorType: string;
  severity: ErrorSeverity;
  message: string;
  stackTrace?: string;
  requestId?: string;
  userId?: string;
  endpoint?: string;
  httpStatus?: number;
  metadata?: Record<string, any>;
}

interface ErrorMetrics {
  serviceName: string;
  totalRequests: number;
  totalErrors: number;
  errorRate: number;
  lastCalculation: Date;
  errorsByType: Map<string, number>;
  errorsBySeverity: Map<string, number>;
  recentErrors: ErrorRecord[];
  alertsTriggered: number;
  consecutiveErrors: number;
  longestErrorStreak: number;
}

interface ErrorRateAlert {
  id: string;
  service: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  errorRate: number;
  consecutiveErrors: number;
  acknowledged: boolean;
}

interface ErrorRateCalculation {
  service: string;
  totalRequests: number;
  totalErrors: number;
  errorRate: number;
  timestamp: Date;
  topErrorTypes: { type: string; count: number }[];
  errorDistribution: { severity: ErrorSeverity; count: number; percentage: number }[];
}

interface ServiceErrorStats {
  service: string;
  totalRequests: number;
  totalErrors: number;
  errorRate: number;
  consecutiveErrors: number;
  longestErrorStreak: number;
  alertsTriggered: number;
  lastCalculation: Date;
  topErrorTypes: { type: string; count: number }[];
  errorDistribution: { severity: ErrorSeverity; count: number; percentage: number }[];
  recentErrorsCount: number;
}

interface ErrorStatistics {
  overallErrorRate: number;
  totalErrors: number;
  totalRequests: number;
  serviceStats: ServiceErrorStats[];
  recentErrorsCount: number;
  isMonitoring: boolean;
  lastUpdate: Date;
}

interface ErrorFilters {
  service?: string;
  severity?: ErrorSeverity;
  errorType?: string;
  timeRange?: string;
  limit?: number;
}

interface ErrorMonitoringStatus {
  isActive: boolean;
  servicesMonitored: number;
  totalErrorsTracked: number;
  lastCalculation: number | null;
}

export type {
  ErrorSeverity,
  ErrorMonitoringConfig,
  ErrorOccurrence,
  RequestRecord,
  ErrorRecord,
  ErrorMetrics,
  ErrorRateAlert,
  ErrorRateCalculation,
  ServiceErrorStats,
  ErrorStatistics,
  ErrorFilters,
  ErrorMonitoringStatus,
};