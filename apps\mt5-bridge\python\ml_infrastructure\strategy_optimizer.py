"""
Strategy Optimization Engine using Genetic Algorithms and ML
Implements automated parameter optimization for trading strategies
"""

import numpy as np
import pandas as pd
import asyncio
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass
import random
from datetime import datetime, timed<PERSON>ta
from loguru import logger
import optuna
from concurrent.futures import ThreadPoolExecutor
import json

@dataclass
class ParameterRange:
    """Parameter range definition for optimization"""
    name: str
    min_value: float
    max_value: float
    step: Optional[float] = None
    parameter_type: str = "float"  # float, int, categorical
    categories: Optional[List[Any]] = None

@dataclass
class OptimizationConfig:
    """Configuration for strategy optimization"""
    population_size: int = 50
    generations: int = 100
    mutation_rate: float = 0.1
    crossover_rate: float = 0.8
    elite_size: int = 5
    tournament_size: int = 3
    convergence_threshold: float = 1e-6
    max_stagnant_generations: int = 20
    use_ml_guidance: bool = True
    parallel_evaluation: bool = True
    max_workers: int = 4

@dataclass
class StrategyParameters:
    """Strategy parameters container"""
    parameters: Dict[str, Any]
    fitness_score: float = 0.0
    backtest_results: Optional[Dict] = None
    generation: int = 0

class GeneticAlgorithmOptimizer:
    """
    Genetic Algorithm implementation for strategy optimization
    """
    
    def __init__(self, config: OptimizationConfig):
        self.config = config
        self.parameter_ranges: List[ParameterRange] = []
        self.population: List[StrategyParameters] = []
        self.best_individual: Optional[StrategyParameters] = None
        self.generation_history: List[Dict] = []
        self.stagnant_generations = 0
        
    def add_parameter(self, param_range: ParameterRange):
        """Add parameter range for optimization"""
        self.parameter_ranges.append(param_range)
        logger.info(f"Added parameter: {param_range.name} [{param_range.min_value}, {param_range.max_value}]")
    
    def initialize_population(self):
        """Initialize random population"""
        self.population = []
        
        for _ in range(self.config.population_size):
            parameters = {}
            for param_range in self.parameter_ranges:
                if param_range.parameter_type == "float":
                    value = random.uniform(param_range.min_value, param_range.max_value)
                elif param_range.parameter_type == "int":
                    value = random.randint(int(param_range.min_value), int(param_range.max_value))
                elif param_range.parameter_type == "categorical":
                    value = random.choice(param_range.categories)
                else:
                    value = param_range.min_value
                
                parameters[param_range.name] = value
            
            individual = StrategyParameters(parameters=parameters)
            self.population.append(individual)
        
        logger.info(f"Initialized population of {len(self.population)} individuals")
    
    async def optimize(self, fitness_function: Callable, progress_callback: Optional[Callable] = None) -> StrategyParameters:
        """
        Run genetic algorithm optimization
        Args:
            fitness_function: Function to evaluate strategy performance
            progress_callback: Optional callback for progress updates
        Returns:
            Best strategy parameters found
        """
        logger.info("Starting genetic algorithm optimization...")
        
        # Initialize population
        self.initialize_population()
        
        # Evaluate initial population
        await self._evaluate_population(fitness_function)
        
        for generation in range(self.config.generations):
            # Selection
            parents = self._selection()
            
            # Crossover and mutation
            offspring = await self._create_offspring(parents)
            
            # Evaluate offspring
            for individual in offspring:
                individual.generation = generation
            
            await self._evaluate_individuals(offspring, fitness_function)
            
            # Replacement
            self._replacement(offspring)
            
            # Track best individual
            current_best = max(self.population, key=lambda x: x.fitness_score)
            
            if self.best_individual is None or current_best.fitness_score > self.best_individual.fitness_score:
                self.best_individual = current_best
                self.stagnant_generations = 0
                logger.info(f"Generation {generation}: New best fitness = {current_best.fitness_score:.6f}")
            else:
                self.stagnant_generations += 1
            
            # Record generation statistics
            fitness_scores = [ind.fitness_score for ind in self.population]
            generation_stats = {
                "generation": generation,
                "best_fitness": max(fitness_scores),
                "average_fitness": np.mean(fitness_scores),
                "worst_fitness": min(fitness_scores),
                "std_fitness": np.std(fitness_scores)
            }
            self.generation_history.append(generation_stats)
            
            # Progress callback
            if progress_callback:
                await progress_callback(generation, generation_stats)
            
            # Check convergence
            if self._check_convergence():
                logger.info(f"Converged at generation {generation}")
                break
        
        logger.info(f"Optimization completed. Best fitness: {self.best_individual.fitness_score:.6f}")
        return self.best_individual
    
    async def _evaluate_population(self, fitness_function: Callable):
        """Evaluate fitness for entire population"""
        await self._evaluate_individuals(self.population, fitness_function)
    
    async def _evaluate_individuals(self, individuals: List[StrategyParameters], fitness_function: Callable):
        """Evaluate fitness for list of individuals"""
        if self.config.parallel_evaluation:
            # Parallel evaluation
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                tasks = []
                for individual in individuals:
                    task = executor.submit(fitness_function, individual.parameters)
                    tasks.append((individual, task))
                
                for individual, task in tasks:
                    try:
                        result = task.result()
                        individual.fitness_score = result.get('fitness', 0.0)
                        individual.backtest_results = result
                    except Exception as e:
                        logger.error(f"Fitness evaluation failed: {e}")
                        individual.fitness_score = -1000.0  # Penalty for failed evaluation
        else:
            # Sequential evaluation
            for individual in individuals:
                try:
                    result = fitness_function(individual.parameters)
                    individual.fitness_score = result.get('fitness', 0.0)
                    individual.backtest_results = result
                except Exception as e:
                    logger.error(f"Fitness evaluation failed: {e}")
                    individual.fitness_score = -1000.0
    
    def _selection(self) -> List[StrategyParameters]:
        """Tournament selection"""
        parents = []
        
        for _ in range(self.config.population_size):
            tournament = random.sample(self.population, self.config.tournament_size)
            winner = max(tournament, key=lambda x: x.fitness_score)
            parents.append(winner)
        
        return parents
    
    async def _create_offspring(self, parents: List[StrategyParameters]) -> List[StrategyParameters]:
        """Create offspring through crossover and mutation"""
        offspring = []
        
        for i in range(0, len(parents), 2):
            parent1 = parents[i]
            parent2 = parents[i + 1] if i + 1 < len(parents) else parents[0]
            
            # Crossover
            if random.random() < self.config.crossover_rate:
                child1, child2 = self._crossover(parent1, parent2)
            else:
                child1 = self._copy_individual(parent1)
                child2 = self._copy_individual(parent2)
            
            # Mutation
            if random.random() < self.config.mutation_rate:
                child1 = self._mutate(child1)
            if random.random() < self.config.mutation_rate:
                child2 = self._mutate(child2)
            
            offspring.extend([child1, child2])
        
        return offspring[:self.config.population_size]
    
    def _crossover(self, parent1: StrategyParameters, parent2: StrategyParameters) -> Tuple[StrategyParameters, StrategyParameters]:
        """Uniform crossover"""
        child1_params = {}
        child2_params = {}
        
        for param_range in self.parameter_ranges:
            param_name = param_range.name
            
            if random.random() < 0.5:
                child1_params[param_name] = parent1.parameters[param_name]
                child2_params[param_name] = parent2.parameters[param_name]
            else:
                child1_params[param_name] = parent2.parameters[param_name]
                child2_params[param_name] = parent1.parameters[param_name]
        
        child1 = StrategyParameters(parameters=child1_params)
        child2 = StrategyParameters(parameters=child2_params)
        
        return child1, child2
    
    def _mutate(self, individual: StrategyParameters) -> StrategyParameters:
        """Gaussian mutation"""
        mutated_params = individual.parameters.copy()
        
        for param_range in self.parameter_ranges:
            param_name = param_range.name
            
            if random.random() < 0.1:  # 10% chance to mutate each parameter
                if param_range.parameter_type == "float":
                    # Gaussian mutation
                    current_value = mutated_params[param_name]
                    mutation_strength = (param_range.max_value - param_range.min_value) * 0.1
                    new_value = current_value + random.gauss(0, mutation_strength)
                    new_value = max(param_range.min_value, min(param_range.max_value, new_value))
                    mutated_params[param_name] = new_value
                    
                elif param_range.parameter_type == "int":
                    # Random integer mutation
                    new_value = random.randint(int(param_range.min_value), int(param_range.max_value))
                    mutated_params[param_name] = new_value
                    
                elif param_range.parameter_type == "categorical":
                    # Random categorical mutation
                    new_value = random.choice(param_range.categories)
                    mutated_params[param_name] = new_value
        
        return StrategyParameters(parameters=mutated_params)
    
    def _replacement(self, offspring: List[StrategyParameters]):
        """Elitist replacement"""
        # Combine population and offspring
        combined = self.population + offspring
        
        # Sort by fitness
        combined.sort(key=lambda x: x.fitness_score, reverse=True)
        
        # Keep best individuals
        self.population = combined[:self.config.population_size]
    
    def _copy_individual(self, individual: StrategyParameters) -> StrategyParameters:
        """Create a copy of an individual"""
        return StrategyParameters(
            parameters=individual.parameters.copy(),
            fitness_score=individual.fitness_score,
            backtest_results=individual.backtest_results.copy() if individual.backtest_results else None
        )
    
    def _check_convergence(self) -> bool:
        """Check if algorithm has converged"""
        if self.stagnant_generations >= self.config.max_stagnant_generations:
            return True
        
        if len(self.generation_history) >= 10:
            recent_best = [gen['best_fitness'] for gen in self.generation_history[-10:]]
            if max(recent_best) - min(recent_best) < self.config.convergence_threshold:
                return True
        
        return False

class OptunaBayesianOptimizer:
    """
    Bayesian optimization using Optuna for strategy parameters
    """
    
    def __init__(self, n_trials: int = 100):
        self.n_trials = n_trials
        self.parameter_ranges: List[ParameterRange] = []
        self.study: Optional[optuna.Study] = None
    
    def add_parameter(self, param_range: ParameterRange):
        """Add parameter range for optimization"""
        self.parameter_ranges.append(param_range)
    
    def optimize(self, fitness_function: Callable) -> Dict[str, Any]:
        """
        Run Bayesian optimization
        Args:
            fitness_function: Function to evaluate strategy performance
        Returns:
            Best parameters found
        """
        def objective(trial):
            parameters = {}
            
            for param_range in self.parameter_ranges:
                if param_range.parameter_type == "float":
                    value = trial.suggest_float(
                        param_range.name, 
                        param_range.min_value, 
                        param_range.max_value,
                        step=param_range.step
                    )
                elif param_range.parameter_type == "int":
                    value = trial.suggest_int(
                        param_range.name,
                        int(param_range.min_value),
                        int(param_range.max_value)
                    )
                elif param_range.parameter_type == "categorical":
                    value = trial.suggest_categorical(param_range.name, param_range.categories)
                
                parameters[param_range.name] = value
            
            try:
                result = fitness_function(parameters)
                return result.get('fitness', 0.0)
            except Exception as e:
                logger.error(f"Fitness evaluation failed: {e}")
                return -1000.0
        
        # Create study
        self.study = optuna.create_study(direction='maximize')
        
        # Optimize
        self.study.optimize(objective, n_trials=self.n_trials)
        
        logger.info(f"Bayesian optimization completed. Best value: {self.study.best_value:.6f}")
        
        return {
            'parameters': self.study.best_params,
            'fitness': self.study.best_value,
            'n_trials': len(self.study.trials)
        }

class StrategyOptimizationEngine:
    """
    Main strategy optimization engine combining multiple optimization methods
    """
    
    def __init__(self):
        self.optimization_history: List[Dict] = []
    
    async def optimize_strategy(self, 
                              strategy_function: Callable,
                              parameter_ranges: List[ParameterRange],
                              optimization_method: str = "genetic",
                              config: Optional[OptimizationConfig] = None) -> Dict[str, Any]:
        """
        Optimize strategy parameters
        Args:
            strategy_function: Function that evaluates strategy performance
            parameter_ranges: List of parameter ranges to optimize
            optimization_method: "genetic" or "bayesian"
            config: Optimization configuration
        Returns:
            Optimization results
        """
        start_time = datetime.now()
        
        if optimization_method == "genetic":
            optimizer = GeneticAlgorithmOptimizer(config or OptimizationConfig())
            
            for param_range in parameter_ranges:
                optimizer.add_parameter(param_range)
            
            best_individual = await optimizer.optimize(strategy_function)
            
            result = {
                'method': 'genetic_algorithm',
                'best_parameters': best_individual.parameters,
                'best_fitness': best_individual.fitness_score,
                'backtest_results': best_individual.backtest_results,
                'generation_history': optimizer.generation_history,
                'optimization_time': (datetime.now() - start_time).total_seconds()
            }
            
        elif optimization_method == "bayesian":
            optimizer = OptunaBayesianOptimizer(n_trials=config.generations if config else 100)
            
            for param_range in parameter_ranges:
                optimizer.add_parameter(param_range)
            
            result = optimizer.optimize(strategy_function)
            result['method'] = 'bayesian_optimization'
            result['optimization_time'] = (datetime.now() - start_time).total_seconds()
        
        else:
            raise ValueError(f"Unknown optimization method: {optimization_method}")
        
        # Store optimization history
        self.optimization_history.append(result)
        
        logger.info(f"Strategy optimization completed in {result['optimization_time']:.2f} seconds")
        return result
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of all optimizations performed"""
        if not self.optimization_history:
            return {"total_optimizations": 0}
        
        return {
            "total_optimizations": len(self.optimization_history),
            "methods_used": list(set(opt['method'] for opt in self.optimization_history)),
            "best_fitness_overall": max(opt['best_fitness'] for opt in self.optimization_history),
            "average_optimization_time": np.mean([opt['optimization_time'] for opt in self.optimization_history])
        }
