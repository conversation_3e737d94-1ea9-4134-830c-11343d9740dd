import { Request, Response, NextFunction } from 'express';
import { param, query, validationResult } from 'express-validator';
import { TradeStatusTracker } from '../../services/trading/TradeStatusTracker';
import { PositionManager } from '../../services/trading/PositionManager';
import { TradeHistoryService } from '../../services/trading/TradeHistoryService';

interface TradeStatusParams {
  tradeId: string;
}

interface TradeStatusQuery {
  includeHistory?: string;
  includePosition?: string;
}

// Validation middleware
export const validateTradeStatusRequest = [
  param('tradeId')
    .isString()
    .notEmpty()
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Invalid trade ID format'),
  
  query('includeHistory')
    .optional()
    .isBoolean()
    .withMessage('includeHistory must be a boolean'),
  
  query('includePosition')
    .optional()
    .isBoolean()
    .withMessage('includePosition must be a boolean')
];

export class TradeStatusController {
  constructor(
    private tradeStatusTracker: TradeStatusTracker,
    private positionManager: PositionManager,
    private tradeHistoryService: TradeHistoryService
  ) {}

  async getTradeStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
        return;
      }

      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const { tradeId } = req.params as TradeStatusParams;
      const { includeHistory, includePosition } = req.query as TradeStatusQuery;

      // Get current trade status
      const tradeStatus = await this.tradeStatusTracker.getTradeStatus(tradeId);
      
      if (!tradeStatus) {
        res.status(404).json({
          success: false,
          error: 'Trade not found'
        });
        return;
      }

      // Verify user owns this trade
      if (tradeStatus.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      const responseData: any = {
        success: true,
        data: {
          tradeId,
          status: tradeStatus.currentStatus,
          lastUpdate: tradeStatus.lastUpdate,
          executionId: tradeStatus.executionId,
          brokerOrderId: tradeStatus.brokerOrderId,
          statusHistory: tradeStatus.statusHistory,
          notifications: tradeStatus.notifications,
          metadata: tradeStatus.metadata
        }
      };

      // Include position information if requested
      if (includePosition === 'true') {
        try {
          const position = await this.positionManager.getPositionByTradeId(tradeId);
          if (position) {
            responseData.data.position = {
              ...position,
              size: position.size.toString(),
              averageEntryPrice: position.averageEntryPrice.toString(),
              currentPrice: position.currentPrice.toString(),
              unrealizedPnl: position.unrealizedPnl.toString(),
              realizedPnl: position.realizedPnl.toString(),
              totalPnl: position.totalPnl.toString(),
              pnlPercentage: position.pnlPercentage.toString(),
              stopLoss: position.stopLoss?.toString(),
              takeProfit: position.takeProfit?.toString(),
              riskMetrics: {
                ...position.riskMetrics,
                exposure: position.riskMetrics.exposure.toString(),
                riskPercentage: position.riskMetrics.riskPercentage.toString(),
                maxDrawdown: position.riskMetrics.maxDrawdown.toString(),
                currentDrawdown: position.riskMetrics.currentDrawdown.toString(),
                volatility: position.riskMetrics.volatility.toString(),
                beta: position.riskMetrics.beta?.toString()
              }
            };
          }
        } catch (error) {
          console.warn('Failed to fetch position information:', error);
          // Continue without position data
        }
      }

      // Include trade history if requested
      if (includeHistory === 'true') {
        try {
          const { modifications } = await this.tradeHistoryService.getTradeHistory(tradeId);
          responseData.data.history = modifications.map(mod => ({
            ...mod,
            originalValue: mod.originalValue.toString(),
            newValue: mod.newValue.toString()
          }));
        } catch (error) {
          console.warn('Failed to fetch trade history:', error);
          // Continue without history data
        }
      }

      res.status(200).json(responseData);

    } catch (error) {
      console.error('Trade status error:', error);
      next(error);
    }
  }

  async subscribeToTradeUpdates(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { tradeId } = req.params as TradeStatusParams;
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      // Verify trade exists and user has access
      const tradeStatus = await this.tradeStatusTracker.getTradeStatus(tradeId);
      if (!tradeStatus || tradeStatus.userId !== userId) {
        res.status(404).json({
          success: false,
          error: 'Trade not found'
        });
        return;
      }

      // Set up SSE (Server-Sent Events) for real-time updates
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      });

      // Send initial status
      const initialData = JSON.stringify({
        type: 'trade_status',
        tradeId,
        status: tradeStatus.currentStatus,
        timestamp: new Date().toISOString()
      });
      res.write(`data: ${initialData}\n\n`);

      // Set up listener for trade status updates
      const updateListener = (update: any) => {
        if (update.tradeId === tradeId) {
          const eventData = JSON.stringify({
            type: 'trade_status_update',
            ...update,
            timestamp: new Date().toISOString()
          });
          res.write(`data: ${eventData}\n\n`);
        }
      };

      this.tradeStatusTracker.on('tradeStatusUpdate', updateListener);

      // Handle client disconnect
      req.on('close', () => {
        this.tradeStatusTracker.off('tradeStatusUpdate', updateListener);
        res.end();
      });

      // Keep alive heartbeat
      const heartbeat = setInterval(() => {
        res.write(`data: ${JSON.stringify({ type: 'heartbeat', timestamp: new Date().toISOString() })}\n\n`);
      }, 30000);

      req.on('close', () => {
        clearInterval(heartbeat);
      });

    } catch (error) {
      console.error('Trade subscription error:', error);
      next(error);
    }
  }

  async getAllUserTrades(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const {
        status,
        instrument,
        startDate,
        endDate,
        limit = '50',
        offset = '0'
      } = req.query;

      // Build filters
      const filters: any = { userId };
      
      if (status) filters.status = status;
      if (instrument) filters.instrument = instrument;
      if (startDate) filters.startDate = new Date(startDate as string);
      if (endDate) filters.endDate = new Date(endDate as string);

      const limitNum = parseInt(limit as string, 10);
      const offsetNum = parseInt(offset as string, 10);

      // Get user trades
      const userTrades = await this.tradeStatusTracker.getUserTrades(
        userId,
        filters,
        limitNum,
        offsetNum
      );

      const responseData = {
        success: true,
        data: {
          trades: userTrades.trades.map(trade => ({
            ...trade,
            // Convert Decimal fields to strings for JSON serialization
            requestedPrice: trade.requestedPrice?.toString(),
            executedPrice: trade.executedPrice?.toString(),
            slippage: trade.slippage?.toString()
          })),
          total: userTrades.total,
          limit: limitNum,
          offset: offsetNum
        }
      };

      res.status(200).json(responseData);

    } catch (error) {
      console.error('Get user trades error:', error);
      next(error);
    }
  }
}

// Factory function to create controller with dependencies
export const createTradeStatusController = (
  tradeStatusTracker: TradeStatusTracker,
  positionManager: PositionManager,
  tradeHistoryService: TradeHistoryService
) => {
  return new TradeStatusController(
    tradeStatusTracker,
    positionManager,
    tradeHistoryService
  );
};