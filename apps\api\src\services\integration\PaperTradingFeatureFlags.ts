import { PrismaClient } from '@prisma/client';

export interface FeatureFlag {
  id: string;
  name: string;
  key: string;
  enabled: boolean;
  rolloutPercentage: number;
  targetAudience: string[];
  conditions: FeatureFlagCondition[];
  metadata: any;
  createdAt: Date;
  updatedAt: Date;
}

export interface FeatureFlagCondition {
  type: 'user_segment' | 'confidence_stage' | 'user_experience' | 'custom';
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  field: string;
  value: any;
}

export interface UserFeatureContext {
  userId: string;
  userSegment?: string;
  confidenceStage?: string;
  experienceLevel?: string;
  registrationDate?: Date;
  tradeCount?: number;
  customAttributes?: Record<string, any>;
}

export class PaperTradingFeatureFlags {
  private prisma: PrismaClient;
  private cache: Map<string, FeatureFlag> = new Map();
  private cacheExpiry: Map<string, number> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  // Predefined paper trading feature flags
  private readonly PAPER_TRADING_FLAGS = {
    PAPER_TRADING_ENABLED: {
      key: 'paper_trading_enabled',
      name: 'Paper Trading Module',
      description: 'Enable access to paper trading features',
      defaultEnabled: false,
      rolloutPercentage: 100,
    },
    PAPER_TRADING_ADVANCED_FEATURES: {
      key: 'paper_trading_advanced_features',
      name: 'Advanced Paper Trading Features',
      description: 'Enable advanced features like strategy testing and analytics',
      defaultEnabled: false,
      rolloutPercentage: 50,
    },
    PAPER_TRADING_AUTO_GRADUATION: {
      key: 'paper_trading_auto_graduation',
      name: 'Automatic Graduation',
      description: 'Enable automatic graduation from paper trading to live trading',
      defaultEnabled: false,
      rolloutPercentage: 25,
    },
    PAPER_TRADING_SOCIAL_FEATURES: {
      key: 'paper_trading_social_features',
      name: 'Social Paper Trading Features',
      description: 'Enable leaderboards and social comparison features',
      defaultEnabled: false,
      rolloutPercentage: 75,
    },
    PAPER_TRADING_MOBILE_OPTIMIZED: {
      key: 'paper_trading_mobile_optimized',
      name: 'Mobile Optimized Interface',
      description: 'Enable mobile-optimized paper trading interface',
      defaultEnabled: true,
      rolloutPercentage: 100,
    },
  };

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.initializeFeatureFlags();
  }

  /**
   * Initialize default paper trading feature flags
   */
  private async initializeFeatureFlags(): Promise<void> {
    try {
      for (const [, flagConfig] of Object.entries(this.PAPER_TRADING_FLAGS)) {
        await this.createOrUpdateFeatureFlag({
          key: flagConfig.key,
          name: flagConfig.name,
          enabled: flagConfig.defaultEnabled,
          rolloutPercentage: flagConfig.rolloutPercentage,
          targetAudience: [],
          conditions: [],
          metadata: {
            description: flagConfig.description,
            category: 'paper_trading',
            system: 'trading_platform',
          },
        });
      }
    } catch (error) {
      console.error('Error initializing feature flags:', error);
    }
  }

  /**
   * Check if a feature flag is enabled for a specific user
   */
  async isFeatureEnabled(
    flagKey: string, 
    userContext: UserFeatureContext
  ): Promise<boolean> {
    try {
      const flag = await this.getFeatureFlag(flagKey);
      
      if (!flag) {
        return false;
      }

      if (!flag.enabled) {
        return false;
      }

      // Check rollout percentage
      if (!this.isUserInRollout(userContext.userId, flag.rolloutPercentage)) {
        return false;
      }

      // Check target audience
      if (flag.targetAudience.length > 0 && userContext.userSegment) {
        if (!flag.targetAudience.includes(userContext.userSegment)) {
          return false;
        }
      }

      // Check conditions
      for (const condition of flag.conditions) {
        if (!this.evaluateCondition(condition, userContext)) {
          return false;
        }
      }

      return true;

    } catch (error) {
      console.error(`Error checking feature flag ${flagKey}:`, error);
      return false; // Fail safely - disable feature on error
    }
  }

  /**
   * Get multiple feature flags for a user
   */
  async getUserFeatureFlags(userContext: UserFeatureContext): Promise<Record<string, boolean>> {
    const flags: Record<string, boolean> = {};

    for (const flagConfig of Object.values(this.PAPER_TRADING_FLAGS)) {
      flags[flagConfig.key] = await this.isFeatureEnabled(flagConfig.key, userContext);
    }

    return flags;
  }

  /**
   * Get paper trading feature access level for a user
   */
  async getPaperTradingAccessLevel(userContext: UserFeatureContext): Promise<{
    level: 'none' | 'basic' | 'advanced' | 'full';
    enabledFeatures: string[];
    disabledFeatures: string[];
    restrictions?: string[];
  }> {
    const flags = await this.getUserFeatureFlags(userContext);
    const enabledFeatures: string[] = [];
    const disabledFeatures: string[] = [];
    const restrictions: string[] = [];

    // Basic access
    if (!flags.paper_trading_enabled) {
      return {
        level: 'none',
        enabledFeatures: [],
        disabledFeatures: Object.values(this.PAPER_TRADING_FLAGS).map(f => f.key),
        restrictions: ['Paper trading module not available'],
      };
    }

    enabledFeatures.push('paper_trading_enabled');

    // Check individual features
    if (flags.paper_trading_advanced_features) {
      enabledFeatures.push('paper_trading_advanced_features');
    } else {
      disabledFeatures.push('paper_trading_advanced_features');
      restrictions.push('Advanced analytics not available');
    }

    if (flags.paper_trading_auto_graduation) {
      enabledFeatures.push('paper_trading_auto_graduation');
    } else {
      disabledFeatures.push('paper_trading_auto_graduation');
      restrictions.push('Manual graduation process required');
    }

    if (flags.paper_trading_social_features) {
      enabledFeatures.push('paper_trading_social_features');
    } else {
      disabledFeatures.push('paper_trading_social_features');
    }

    if (flags.paper_trading_mobile_optimized) {
      enabledFeatures.push('paper_trading_mobile_optimized');
    }

    // Determine access level
    let level: 'none' | 'basic' | 'advanced' | 'full';
    if (enabledFeatures.length === 1) {
      level = 'basic';
    } else if (enabledFeatures.length <= 3) {
      level = 'advanced';
    } else {
      level = 'full';
    }

    return {
      level,
      enabledFeatures,
      disabledFeatures,
      restrictions: restrictions.length > 0 ? restrictions : undefined,
    };
  }

  /**
   * Create or update a feature flag
   */
  async createOrUpdateFeatureFlag(flagData: {
    key: string;
    name: string;
    enabled: boolean;
    rolloutPercentage: number;
    targetAudience: string[];
    conditions: FeatureFlagCondition[];
    metadata?: any;
  }): Promise<FeatureFlag> {
    const existingFlag = await this.prisma.featureFlag.findUnique({
      where: { key: flagData.key },
    });

    if (existingFlag) {
      const updatedFlag = await this.prisma.featureFlag.update({
        where: { key: flagData.key },
        data: {
          name: flagData.name,
          enabled: flagData.enabled,
          rolloutPercentage: flagData.rolloutPercentage,
          targetAudience: flagData.targetAudience,
          conditions: flagData.conditions,
          metadata: flagData.metadata || {},
          updatedAt: new Date(),
        },
      });

      // Clear cache
      this.cache.delete(flagData.key);
      return updatedFlag;
    } else {
      const newFlag = await this.prisma.featureFlag.create({
        data: {
          key: flagData.key,
          name: flagData.name,
          enabled: flagData.enabled,
          rolloutPercentage: flagData.rolloutPercentage,
          targetAudience: flagData.targetAudience,
          conditions: flagData.conditions,
          metadata: flagData.metadata || {},
        },
      });

      return newFlag;
    }
  }

  /**
   * Update feature flag rollout percentage
   */
  async updateRolloutPercentage(flagKey: string, percentage: number): Promise<void> {
    if (percentage < 0 || percentage > 100) {
      throw new Error('Rollout percentage must be between 0 and 100');
    }

    await this.prisma.featureFlag.update({
      where: { key: flagKey },
      data: {
        rolloutPercentage: percentage,
        updatedAt: new Date(),
      },
    });

    // Clear cache
    this.cache.delete(flagKey);
  }

  /**
   * Enable/disable a feature flag
   */
  async toggleFeatureFlag(flagKey: string, enabled: boolean): Promise<void> {
    await this.prisma.featureFlag.update({
      where: { key: flagKey },
      data: {
        enabled,
        updatedAt: new Date(),
      },
    });

    // Clear cache
    this.cache.delete(flagKey);
  }

  /**
   * Add condition to feature flag
   */
  async addFlagCondition(
    flagKey: string, 
    condition: FeatureFlagCondition
  ): Promise<void> {
    const flag = await this.getFeatureFlag(flagKey);
    if (!flag) {
      throw new Error(`Feature flag ${flagKey} not found`);
    }

    const updatedConditions = [...flag.conditions, condition];

    await this.prisma.featureFlag.update({
      where: { key: flagKey },
      data: {
        conditions: updatedConditions,
        updatedAt: new Date(),
      },
    });

    // Clear cache
    this.cache.delete(flagKey);
  }

  /**
   * Get feature flag usage analytics
   */
  async getFeatureFlagAnalytics(_flagKey: string): Promise<{
    totalChecks: number;
    enabledChecks: number;
    disabledChecks: number;
    enablementRate: number;
    topUserSegments: Array<{ segment: string; count: number }>;
    recentActivity: Array<{ date: string; checks: number; enabled: number }>;
  }> {
    // This would integrate with analytics/logging system
    // For now, return mock data structure
    return {
      totalChecks: 0,
      enabledChecks: 0,
      disabledChecks: 0,
      enablementRate: 0,
      topUserSegments: [],
      recentActivity: [],
    };
  }

  /**
   * Get feature flag configuration for admin interface
   */
  async getAllFeatureFlags(): Promise<FeatureFlag[]> {
    return await this.prisma.featureFlag.findMany({
      where: {
        key: {
          startsWith: 'paper_trading',
        },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  private async getFeatureFlag(flagKey: string): Promise<FeatureFlag | null> {
    // Check cache first
    const cached = this.cache.get(flagKey);
    const cacheExpiry = this.cacheExpiry.get(flagKey);
    
    if (cached && cacheExpiry && Date.now() < cacheExpiry) {
      return cached;
    }

    // Fetch from database
    const flag = await this.prisma.featureFlag.findUnique({
      where: { key: flagKey },
    });

    if (flag) {
      // Cache the result
      this.cache.set(flagKey, flag);
      this.cacheExpiry.set(flagKey, Date.now() + this.CACHE_TTL);
    }

    return flag;
  }

  private isUserInRollout(userId: string, rolloutPercentage: number): boolean {
    if (rolloutPercentage >= 100) {
      return true;
    }
    
    if (rolloutPercentage <= 0) {
      return false;
    }

    // Use consistent hashing based on user ID
    const hash = this.hashString(userId);
    const userPercentage = (hash % 100) + 1;
    
    return userPercentage <= rolloutPercentage;
  }

  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  private evaluateCondition(
    condition: FeatureFlagCondition, 
    userContext: UserFeatureContext
  ): boolean {
    const fieldValue = this.getContextFieldValue(condition.field, userContext);
    
    if (fieldValue === undefined) {
      return false;
    }

    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
      
      case 'not_equals':
        return fieldValue !== condition.value;
      
      case 'contains':
        return String(fieldValue).toLowerCase().includes(String(condition.value).toLowerCase());
      
      case 'greater_than':
        return Number(fieldValue) > Number(condition.value);
      
      case 'less_than':
        return Number(fieldValue) < Number(condition.value);
      
      case 'in':
        return Array.isArray(condition.value) && condition.value.includes(fieldValue);
      
      case 'not_in':
        return Array.isArray(condition.value) && !condition.value.includes(fieldValue);
      
      default:
        return false;
    }
  }

  private getContextFieldValue(field: string, context: UserFeatureContext): any {
    switch (field) {
      case 'userId':
        return context.userId;
      case 'userSegment':
        return context.userSegment;
      case 'confidenceStage':
        return context.confidenceStage;
      case 'experienceLevel':
        return context.experienceLevel;
      case 'registrationDate':
        return context.registrationDate;
      case 'tradeCount':
        return context.tradeCount;
      default:
        return context.customAttributes?.[field];
    }
  }

  /**
   * Log feature flag usage for analytics
   */
  async logFeatureFlagUsage(
    flagKey: string,
    userId: string,
    enabled: boolean,
    context?: any
  ): Promise<void> {
    try {
      await this.prisma.featureFlagUsage.create({
        data: {
          flagKey,
          userId,
          enabled,
          context: context || {},
          checkedAt: new Date(),
        },
      });
    } catch (error) {
      // Don't throw errors for logging failures
      console.error('Error logging feature flag usage:', error);
    }
  }

  /**
   * Bulk check feature flags for performance
   */
  async checkMultipleFlags(
    flagKeys: string[],
    userContext: UserFeatureContext
  ): Promise<Record<string, boolean>> {
    const results: Record<string, boolean> = {};
    
    // Use Promise.all for concurrent checks
    const checks = flagKeys.map(async (flagKey) => {
      const enabled = await this.isFeatureEnabled(flagKey, userContext);
      return { flagKey, enabled };
    });

    const checkResults = await Promise.all(checks);
    
    checkResults.forEach(({ flagKey, enabled }) => {
      results[flagKey] = enabled;
    });

    return results;
  }
}

export default PaperTradingFeatureFlags;