import { MetricType } from '@golddaddy/types';
import { MetricsCacheManager } from './MetricsCacheManager';
import { PersistenceAdapter, CacheEntry } from './CachePersistenceAdapter';

export interface HydrationOptions {
  validateEntries?: boolean;
  skipExpired?: boolean;
  batchSize?: number;
  maxAge?: number; // Maximum age in milliseconds to consider for hydration
  priorities?: MetricType[]; // Priority order for hydration
}

export interface HydrationResult {
  totalEntries: number;
  hydratedEntries: number;
  skippedEntries: number;
  failedEntries: number;
  duration: number;
  errors: string[];
}

export interface HydrationStats {
  lastHydration: Date;
  totalHydrations: number;
  averageHydrationTime: number;
  averageEntriesHydrated: number;
  lastResult: HydrationResult | null;
}

export class CacheHydrationService {
  private cacheManager: MetricsCacheManager;
  private persistenceAdapter: PersistenceAdapter;
  private stats: HydrationStats;

  constructor(cacheManager: MetricsCacheManager, persistenceAdapter: PersistenceAdapter) {
    this.cacheManager = cacheManager;
    this.persistenceAdapter = persistenceAdapter;
    this.stats = {
      lastHydration: new Date(0),
      totalHydrations: 0,
      averageHydrationTime: 0,
      averageEntriesHydrated: 0,
      lastResult: null,
    };
  }

  async hydrateCache(options: HydrationOptions = {}): Promise<HydrationResult> {
    const startTime = Date.now();
    const {
      validateEntries = true,
      skipExpired = true,
      batchSize = 100,
      maxAge = 24 * 60 * 60 * 1000, // 24 hours default
      priorities = [],
    } = options;

    console.log('Starting cache hydration...');

    const result: HydrationResult = {
      totalEntries: 0,
      hydratedEntries: 0,
      skippedEntries: 0,
      failedEntries: 0,
      duration: 0,
      errors: [],
    };

    try {
      // Load all persisted entries
      const persistedEntries = await this.persistenceAdapter.loadAll();
      result.totalEntries = persistedEntries.size;

      if (persistedEntries.size === 0) {
        console.log('No persisted cache entries found');
        result.duration = Date.now() - startTime;
        return result;
      }

      console.log(`Found ${persistedEntries.size} persisted cache entries`);

      // Filter and sort entries
      const entries = this.filterAndSortEntries(
        Array.from(persistedEntries.entries()),
        { skipExpired, maxAge, priorities }
      );

      console.log(`Processing ${entries.length} entries after filtering`);

      // Process entries in batches
      for (let i = 0; i < entries.length; i += batchSize) {
        const batch = entries.slice(i, i + batchSize);
        await this.processBatch(batch, validateEntries, result);
        
        // Log progress
        const progress = Math.round(((i + batch.length) / entries.length) * 100);
        console.log(`Hydration progress: ${progress}% (${i + batch.length}/${entries.length})`);
      }

      result.duration = Date.now() - startTime;
      this.updateStats(result);

      console.log(`Cache hydration completed in ${result.duration}ms:`, {
        hydrated: result.hydratedEntries,
        skipped: result.skippedEntries,
        failed: result.failedEntries,
      });

      return result;
    } catch (error) {
      result.duration = Date.now() - startTime;
      result.errors.push(`Hydration failed: ${error.message}`);
      console.error('Cache hydration failed:', error);
      return result;
    }
  }

  private filterAndSortEntries(
    entries: [string, CacheEntry][],
    options: { skipExpired: boolean; maxAge: number; priorities: MetricType[] }
  ): [string, CacheEntry][] {
    const now = Date.now();
    const { skipExpired, maxAge, priorities } = options;

    const filtered = entries.filter(([_key, entry]) => {
      // Skip expired entries if requested
      if (skipExpired && entry.expiresAt.getTime() <= now) {
        return false;
      }

      // Skip entries older than maxAge
      if (maxAge > 0 && (now - entry.cachedAt.getTime()) > maxAge) {
        return false;
      }

      return true;
    });

    // Sort by priority and access patterns
    filtered.sort(([_keyA, entryA], [_keyB, entryB]) => {
      // First, sort by priority if specified
      if (priorities.length > 0) {
        const priorityA = priorities.indexOf(entryA.metricType);
        const priorityB = priorities.indexOf(entryB.metricType);
        
        if (priorityA !== -1 && priorityB !== -1) {
          return priorityA - priorityB;
        } else if (priorityA !== -1) {
          return -1;
        } else if (priorityB !== -1) {
          return 1;
        }
      }

      // Then sort by cache priority and access count
      const scoreA = entryA.priority + (entryA.accessCount * 0.1);
      const scoreB = entryB.priority + (entryB.accessCount * 0.1);
      
      return scoreB - scoreA; // Higher scores first
    });

    return filtered;
  }

  private async processBatch(
    batch: [string, CacheEntry][],
    validateEntries: boolean,
    result: HydrationResult
  ): Promise<void> {
    const promises = batch.map(async ([key, entry]) => {
      try {
        // Validate entry if requested
        if (validateEntries && !this.validateEntry(entry)) {
          result.skippedEntries++;
          result.errors.push(`Invalid entry skipped: ${key}`);
          return;
        }

        // Check if entry is already in memory cache
        const existingEntry = await this.cacheManager.getMetric(
          entry.strategyId,
          entry.metricType,
          entry.userExperience
        );

        if (existingEntry) {
          // Update access count if entry exists
          entry.accessCount++;
          entry.lastAccessed = new Date();
          await this.persistenceAdapter.save(key, entry);
          result.skippedEntries++;
          return;
        }

        // Hydrate entry into memory cache
        await this.cacheManager.cacheMetric(
          entry.strategyId,
          entry.metricType,
          entry.metric,
          entry.userExperience
        );

        result.hydratedEntries++;
      } catch (error) {
        result.failedEntries++;
        result.errors.push(`Failed to hydrate ${key}: ${error.message}`);
        console.warn(`Failed to hydrate cache entry ${key}:`, error);
      }
    });

    await Promise.all(promises);
  }

  private validateEntry(entry: CacheEntry): boolean {
    try {
      // Basic structure validation
      if (!entry.strategyId || !entry.metricType || !entry.metric) {
        return false;
      }

      // Date validation
      if (!(entry.cachedAt instanceof Date) || 
          !(entry.expiresAt instanceof Date) || 
          !(entry.lastAccessed instanceof Date)) {
        return false;
      }

      // Metric validation
      const metric = entry.metric;
      if (!metric.value || !metric.plainEnglish || !metric.confidence) {
        return false;
      }

      // Value validation
      if (typeof metric.value.current !== 'number' || 
          typeof metric.confidence.level !== 'number') {
        return false;
      }

      return true;
    } catch (error) {
      return false;
    }
  }

  private updateStats(result: HydrationResult): void {
    this.stats.lastHydration = new Date();
    this.stats.totalHydrations++;
    this.stats.lastResult = result;

    // Update running averages
    const alpha = 0.1; // Exponential smoothing factor
    this.stats.averageHydrationTime = this.stats.averageHydrationTime * (1 - alpha) + 
                                      result.duration * alpha;
    this.stats.averageEntriesHydrated = this.stats.averageEntriesHydrated * (1 - alpha) + 
                                        result.hydratedEntries * alpha;
  }

  async getStats(): Promise<HydrationStats> {
    return { ...this.stats };
  }

  async validatePersistedCache(): Promise<{
    valid: number;
    invalid: number;
    expired: number;
    details: string[];
  }> {
    const result = {
      valid: 0,
      invalid: 0,
      expired: 0,
      details: [],
    };

    try {
      const entries = await this.persistenceAdapter.loadAll();
      const now = Date.now();

      for (const [key, entry] of entries) {
        if (entry.expiresAt.getTime() <= now) {
          result.expired++;
          result.details.push(`Expired: ${key}`);
        } else if (!this.validateEntry(entry)) {
          result.invalid++;
          result.details.push(`Invalid: ${key}`);
        } else {
          result.valid++;
        }
      }
    } catch (error) {
      result.details.push(`Validation failed: ${error.message}`);
    }

    return result;
  }

  async preloadStrategies(strategyIds: string[]): Promise<{
    loaded: number;
    failed: number;
    errors: string[];
  }> {
    const result = {
      loaded: 0,
      failed: 0,
      errors: [],
    };

    for (const strategyId of strategyIds) {
      try {
        const entries = await this.persistenceAdapter.loadAll();
        const strategyEntries = Array.from(entries.entries())
          .filter(([, entry]) => entry.strategyId === strategyId);

        for (const [, entry] of strategyEntries) {
          if (entry.expiresAt.getTime() > Date.now()) {
            await this.cacheManager.cacheMetric(
              entry.strategyId,
              entry.metricType,
              entry.metric,
              entry.userExperience
            );
            result.loaded++;
          }
        }
      } catch (error) {
        result.failed++;
        result.errors.push(`Failed to preload strategy ${strategyId}: ${error.message}`);
      }
    }

    return result;
  }

  async schedulePeriodicHydration(intervalMs: number = 60 * 60 * 1000): Promise<void> {
    console.log(`Scheduling periodic cache hydration every ${intervalMs}ms`);
    
    setInterval(async () => {
      try {
        console.log('Starting scheduled cache hydration...');
        const result = await this.hydrateCache({
          validateEntries: true,
          skipExpired: true,
          maxAge: 24 * 60 * 60 * 1000, // 24 hours
        });
        
        console.log('Scheduled hydration completed:', {
          hydrated: result.hydratedEntries,
          duration: result.duration,
        });
      } catch (error) {
        console.error('Scheduled cache hydration failed:', error);
      }
    }, intervalMs);
  }
}