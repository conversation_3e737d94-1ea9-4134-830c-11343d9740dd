import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll, vi } from 'vitest';
import request from 'supertest';
import { Express } from 'express';
import { PrismaClient } from '@prisma/client';
import app from '../../../app';
import { RedisService } from '../../cache/RedisService';

describe('Paper Trading Integration Tests', () => {
  let prisma: PrismaClient;
  let redisService: RedisService;
  let authToken: string;
  let testUserId: string;
  let testSessionId: string;

  beforeAll(async () => {
    // Initialize test database and services
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_TEST_URL || 'postgresql://postgres:postgres@localhost:5432/golddaddy_test'
        }
      }
    });

    redisService = new RedisService({
      url: process.env.REDIS_TEST_URL || 'redis://localhost:6379/1'
    });

    // Using imported app directly

    // Create test user and authenticate
    const testUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        hashedPassword: 'test-hash',
        profile: {
          create: {
            firstName: 'Test',
            lastName: 'User',
            timezone: 'UTC',
            experienceLevel: 'INTERMEDIATE',
            riskTolerance: 'MODERATE'
          }
        }
      },
      include: { profile: true }
    });

    testUserId = testUser.id;

    // Mock authentication token
    authToken = 'Bearer test-token';
  });

  afterAll(async () => {
    // Cleanup test data
    await prisma.paperTradingSession.deleteMany({
      where: { userId: testUserId }
    });
    await prisma.user.delete({
      where: { id: testUserId }
    });
    await prisma.$disconnect();
    await redisService.disconnect();
  });

  beforeEach(async () => {
    // Clear redis cache
    await redisService.flushAll();
  });

  afterEach(async () => {
    // Cleanup session-specific data
    if (testSessionId) {
      await prisma.paperTrade.deleteMany({
        where: { sessionId: testSessionId }
      });
      await prisma.paperTradingSession.deleteMany({
        where: { id: testSessionId }
      });
      testSessionId = '';
    }
  });

  describe('Complete Paper Trading Workflow', () => {
    it('should create session, execute trades, manage portfolio, and generate analytics', async () => {
      // Step 1: Create paper trading session
      const sessionResponse = await request(app)
        .post('/api/paper-trading/sessions')
        .set('Authorization', authToken)
        .send({
          name: 'Integration Test Session',
          initialBalance: 100000,
          settings: {
            enableSlippage: true,
            enableSpread: true,
            riskManagement: {
              maxPositionSize: 0.1,
              maxDrawdown: 0.15,
              requireStopLoss: true
            }
          }
        });

      expect(sessionResponse.status).toBe(201);
      expect(sessionResponse.body.success).toBe(true);
      testSessionId = sessionResponse.body.data.id;

      // Step 2: Execute multiple trades
      const tradeRequests = [
        {
          symbol: 'EURUSD',
          type: 'MARKET',
          side: 'BUY',
          quantity: 10000,
          stopLoss: 1.0800,
          takeProfit: 1.0900
        },
        {
          symbol: 'GBPUSD',
          type: 'MARKET',
          side: 'SELL',
          quantity: 5000,
          stopLoss: 1.2700,
          takeProfit: 1.2600
        },
        {
          symbol: 'USDJPY',
          type: 'LIMIT',
          side: 'BUY',
          quantity: 8000,
          price: 149.00,
          stopLoss: 148.50,
          takeProfit: 149.80
        }
      ];

      const tradeResults = [];
      for (const tradeRequest of tradeRequests) {
        const tradeResponse = await request(app)
          .post('/api/paper-trading/trades')
          .set('Authorization', authToken)
          .send({
            sessionId: testSessionId,
            ...tradeRequest
          });

        expect(tradeResponse.status).toBe(201);
        expect(tradeResponse.body.success).toBe(true);
        tradeResults.push(tradeResponse.body.data);
      }

      // Step 3: Verify portfolio state
      const portfolioResponse = await request(app)
        .get(`/api/portfolio/${testSessionId}`)
        .set('Authorization', authToken);

      expect(portfolioResponse.status).toBe(200);
      expect(portfolioResponse.body.success).toBe(true);
      
      const portfolio = portfolioResponse.body.data;
      expect(portfolio.totalValue).toBeLessThanOrEqual(100000); // Due to spreads
      expect(portfolio.positions.length).toBeGreaterThanOrEqual(2); // Market orders filled
      expect(portfolio.openOrders.length).toBeGreaterThanOrEqual(0); // Pending limit orders

      // Step 4: Close a position
      const firstPosition = portfolio.positions[0];
      const closeResponse = await request(app)
        .post('/api/paper-trading/positions/close')
        .set('Authorization', authToken)
        .send({
          sessionId: testSessionId,
          positionId: firstPosition.id
        });

      expect(closeResponse.status).toBe(200);
      expect(closeResponse.body.success).toBe(true);

      // Step 5: Generate analytics
      const analyticsResponse = await request(app)
        .get(`/api/paper-trading/analytics/${testSessionId}`)
        .set('Authorization', authToken);

      expect(analyticsResponse.status).toBe(200);
      expect(analyticsResponse.body.success).toBe(true);
      
      const analytics = analyticsResponse.body.data;
      expect(analytics.totalTrades).toBeGreaterThanOrEqual(2);
      expect(analytics.winRate).toBeGreaterThanOrEqual(0);
      expect(analytics.averageReturn).toBeDefined();
      expect(analytics.riskMetrics.sharpeRatio).toBeDefined();

      // Step 6: Check graduation criteria
      const requirementsResponse = await request(app)
        .get(`/api/paper-trading/requirements/${testSessionId}`)
        .set('Authorization', authToken);

      expect(requirementsResponse.status).toBe(200);
      expect(requirementsResponse.body.success).toBe(true);
      
      const requirements = requirementsResponse.body.data;
      expect(requirements.overallProgress).toBeGreaterThan(0);
      expect(requirements.categories).toHaveProperty('profitability');
      expect(requirements.categories).toHaveProperty('riskManagement');
      expect(requirements.categories).toHaveProperty('consistency');
    });

    it('should handle concurrent trade execution without conflicts', async () => {
      // Create session
      const sessionResponse = await request(app)
        .post('/api/paper-trading/sessions')
        .set('Authorization', authToken)
        .send({
          name: 'Concurrent Test Session',
          initialBalance: 100000
        });

      testSessionId = sessionResponse.body.data.id;

      // Execute concurrent trades
      const concurrentTrades = Array.from({ length: 5 }, (_, i) => 
        request(app)
          .post('/api/paper-trading/trades')
          .set('Authorization', authToken)
          .send({
            sessionId: testSessionId,
            symbol: 'EURUSD',
            type: 'MARKET',
            side: i % 2 === 0 ? 'BUY' : 'SELL',
            quantity: 1000
          })
      );

      const results = await Promise.all(concurrentTrades);
      
      // All trades should succeed
      results.forEach(result => {
        expect(result.status).toBe(201);
        expect(result.body.success).toBe(true);
      });

      // Verify portfolio consistency
      const portfolioResponse = await request(app)
        .get(`/api/portfolio/${testSessionId}`)
        .set('Authorization', authToken);

      const portfolio = portfolioResponse.body.data;
      const totalQuantity = portfolio.positions.reduce(
        (sum: number, pos: any) => sum + Math.abs(pos.quantity), 
        0
      );
      expect(totalQuantity).toBeLessThanOrEqual(5000); // Net positions
    });

    it('should enforce risk management rules across workflow', async () => {
      // Create session with strict risk management
      const sessionResponse = await request(app)
        .post('/api/paper-trading/sessions')
        .set('Authorization', authToken)
        .send({
          name: 'Risk Management Test',
          initialBalance: 10000, // Smaller balance
          settings: {
            riskManagement: {
              maxPositionSize: 0.05, // 5% max
              maxDrawdown: 0.10, // 10% max drawdown
              requireStopLoss: true,
              maxDailyLoss: 0.05 // 5% daily loss limit
            }
          }
        });

      testSessionId = sessionResponse.body.data.id;

      // Try to execute oversized trade
      const oversizedTradeResponse = await request(app)
        .post('/api/paper-trading/trades')
        .set('Authorization', authToken)
        .send({
          sessionId: testSessionId,
          symbol: 'EURUSD',
          type: 'MARKET',
          side: 'BUY',
          quantity: 100000, // Way too large
          stopLoss: 1.0800
        });

      expect(oversizedTradeResponse.status).toBe(400);
      expect(oversizedTradeResponse.body.success).toBe(false);
      expect(oversizedTradeResponse.body.error).toContain('position size');

      // Try trade without stop loss
      const noStopLossResponse = await request(app)
        .post('/api/paper-trading/trades')
        .set('Authorization', authToken)
        .send({
          sessionId: testSessionId,
          symbol: 'EURUSD',
          type: 'MARKET',
          side: 'BUY',
          quantity: 1000
          // Missing stopLoss
        });

      expect(noStopLossResponse.status).toBe(400);
      expect(noStopLossResponse.body.success).toBe(false);
      expect(noStopLossResponse.body.error).toContain('stop loss');

      // Execute valid trade
      const validTradeResponse = await request(app)
        .post('/api/paper-trading/trades')
        .set('Authorization', authToken)
        .send({
          sessionId: testSessionId,
          symbol: 'EURUSD',
          type: 'MARKET',
          side: 'BUY',
          quantity: 1000,
          stopLoss: 1.0800
        });

      expect(validTradeResponse.status).toBe(201);
      expect(validTradeResponse.body.success).toBe(true);
    });
  });

  describe('Session Management Integration', () => {
    it('should handle session lifecycle with proper state transitions', async () => {
      // Create session
      const createResponse = await request(app)
        .post('/api/paper-trading/sessions')
        .set('Authorization', authToken)
        .send({
          name: 'Lifecycle Test Session',
          initialBalance: 50000
        });

      testSessionId = createResponse.body.data.id;
      expect(createResponse.body.data.status).toBe('ACTIVE');

      // Pause session
      const pauseResponse = await request(app)
        .patch(`/api/paper-trading/sessions/${testSessionId}`)
        .set('Authorization', authToken)
        .send({ status: 'PAUSED' });

      expect(pauseResponse.status).toBe(200);
      expect(pauseResponse.body.data.status).toBe('PAUSED');

      // Try to execute trade while paused
      const tradeWhilePausedResponse = await request(app)
        .post('/api/paper-trading/trades')
        .set('Authorization', authToken)
        .send({
          sessionId: testSessionId,
          symbol: 'EURUSD',
          type: 'MARKET',
          side: 'BUY',
          quantity: 1000
        });

      expect(tradeWhilePausedResponse.status).toBe(400);
      expect(tradeWhilePausedResponse.body.error).toContain('paused');

      // Resume session
      const resumeResponse = await request(app)
        .patch(`/api/paper-trading/sessions/${testSessionId}`)
        .set('Authorization', authToken)
        .send({ status: 'ACTIVE' });

      expect(resumeResponse.status).toBe(200);
      expect(resumeResponse.body.data.status).toBe('ACTIVE');

      // Execute trade after resume
      const tradeAfterResumeResponse = await request(app)
        .post('/api/paper-trading/trades')
        .set('Authorization', authToken)
        .send({
          sessionId: testSessionId,
          symbol: 'EURUSD',
          type: 'MARKET',
          side: 'BUY',
          quantity: 1000
        });

      expect(tradeAfterResumeResponse.status).toBe(201);

      // Complete session
      const completeResponse = await request(app)
        .patch(`/api/paper-trading/sessions/${testSessionId}`)
        .set('Authorization', authToken)
        .send({ status: 'COMPLETED' });

      expect(completeResponse.status).toBe(200);
      expect(completeResponse.body.data.status).toBe('COMPLETED');
    });
  });

  describe('Market Data Integration', () => {
    it('should integrate with market data for realistic pricing', async () => {
      // Create session
      const sessionResponse = await request(app)
        .post('/api/paper-trading/sessions')
        .set('Authorization', authToken)
        .send({
          name: 'Market Data Test',
          initialBalance: 50000,
          settings: {
            enableSlippage: true,
            enableSpread: true
          }
        });

      testSessionId = sessionResponse.body.data.id;

      // Get current market data
      const marketDataResponse = await request(app)
        .get('/api/market-data/quotes/EURUSD');

      expect(marketDataResponse.status).toBe(200);
      const marketData = marketDataResponse.body.data;

      // Execute trade and verify pricing reflects market conditions
      const tradeResponse = await request(app)
        .post('/api/paper-trading/trades')
        .set('Authorization', authToken)
        .send({
          sessionId: testSessionId,
          symbol: 'EURUSD',
          type: 'MARKET',
          side: 'BUY',
          quantity: 10000
        });

      expect(tradeResponse.status).toBe(201);
      const trade = tradeResponse.body.data;

      // Verify execution price considers spread and slippage
      expect(trade.executionPrice).toBeGreaterThan(marketData.bid);
      expect(trade.executionPrice).toBeLessThanOrEqual(marketData.ask + 0.0005); // Allow for slippage
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle service failures gracefully', async () => {
      // Create session
      const sessionResponse = await request(app)
        .post('/api/paper-trading/sessions')
        .set('Authorization', authToken)
        .send({
          name: 'Error Handling Test',
          initialBalance: 50000
        });

      testSessionId = sessionResponse.body.data.id;

      // Test with invalid symbol
      const invalidSymbolResponse = await request(app)
        .post('/api/paper-trading/trades')
        .set('Authorization', authToken)
        .send({
          sessionId: testSessionId,
          symbol: 'INVALID',
          type: 'MARKET',
          side: 'BUY',
          quantity: 1000
        });

      expect(invalidSymbolResponse.status).toBe(400);
      expect(invalidSymbolResponse.body.success).toBe(false);
      expect(invalidSymbolResponse.body.error).toContain('symbol');

      // Test with invalid session ID
      const invalidSessionResponse = await request(app)
        .post('/api/paper-trading/trades')
        .set('Authorization', authToken)
        .send({
          sessionId: 'invalid-session-id',
          symbol: 'EURUSD',
          type: 'MARKET',
          side: 'BUY',
          quantity: 1000
        });

      expect(invalidSessionResponse.status).toBe(404);
      expect(invalidSessionResponse.body.success).toBe(false);
      expect(invalidSessionResponse.body.error).toContain('session');
    });
  });
});