import {
  PaperTradingSession,
  PaperTrade,
  PaperTradingRequirements,
  VirtualPortfolio
} from '@golddaddy/types';
import { PaperTradingAnalyticsService, PerformanceMetrics } from './PaperTradingAnalyticsService';
import Decimal from 'decimal.js';

/**
 * Graduation criteria configuration
 */
export interface GraduationCriteria {
  // Time requirements
  minimumDays: number;
  minimumTrades: number;
  
  // Performance thresholds
  minimumWinRate: number;           // 0.0 - 1.0
  minimumProfitFactor: number;      // > 1.0
  minimumSharpeRatio: number;       // > 0.0
  maximumDrawdown: number;          // 0.0 - 1.0
  
  // Risk management thresholds
  minimumRiskScore: number;         // 0 - 100
  minimumConsistencyScore: number;  // 0 - 100
  minimumDisciplineScore: number;   // 0 - 100
  
  // Trading behavior requirements
  maximumConsecutiveLosses: number;
  minimumDiversification: number;   // Number of different instruments
  maximumPositionSize: number;      // As % of portfolio
}

/**
 * Requirement tracking status
 */
export interface RequirementStatus {
  requirementId: string;
  name: string;
  description: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'failed';
  progress: number; // 0-100
  currentValue: number;
  requiredValue: number;
  lastUpdated: Date;
  estimatedCompletion?: Date;
}

/**
 * Comprehensive graduation assessment
 */
export interface GraduationAssessment {
  sessionId: string;
  userId: string;
  assessmentDate: Date;
  
  // Overall status
  overallStatus: 'not_ready' | 'approaching' | 'ready' | 'graduated';
  overallScore: number; // 0-100
  graduationEligible: boolean;
  
  // Requirement tracking
  requirements: RequirementStatus[];
  completedRequirements: number;
  totalRequirements: number;
  completionPercentage: number;
  
  // Detailed assessment
  timeRequirement: {
    daysCompleted: number;
    daysRequired: number;
    tradesCompleted: number;
    tradesRequired: number;
    timeRemaining: number;
    projectedCompletion: Date;
  };
  
  performanceAssessment: {
    winRate: { current: number; required: number; met: boolean; };
    profitFactor: { current: number; required: number; met: boolean; };
    sharpeRatio: { current: number; required: number; met: boolean; };
    maxDrawdown: { current: number; required: number; met: boolean; };
    riskManagement: { current: number; required: number; met: boolean; };
    consistency: { current: number; required: number; met: boolean; };
  };
  
  // Feedback and guidance
  nextSteps: string[];
  strengths: string[];
  areasForImprovement: string[];
  recommendedActions: string[];
  estimatedTimeToGraduation: number; // days
}

/**
 * Progress tracking milestone
 */
export interface ProgressMilestone {
  id: string;
  name: string;
  description: string;
  targetDate: Date;
  completionDate?: Date;
  status: 'pending' | 'completed' | 'missed';
  importance: 'low' | 'medium' | 'high' | 'critical';
  category: 'time' | 'performance' | 'risk' | 'behavior';
}

/**
 * Paper Trading Requirements Service
 * 
 * Comprehensive tracking and validation system for paper trading graduation criteria:
 * - Time period and trade count validation
 * - Performance consistency measurement
 * - Risk management demonstration scoring
 * - Graduation criteria evaluation with confidence integration
 * - Progress milestone tracking and notifications
 */
export class PaperTradingRequirementsService {
  private defaultCriteria: GraduationCriteria = {
    // Time requirements (configurable based on user level)
    minimumDays: 30,
    minimumTrades: 50,
    
    // Performance thresholds
    minimumWinRate: 0.55,      // 55% win rate
    minimumProfitFactor: 1.2,   // Positive expectancy
    minimumSharpeRatio: 0.5,    // Basic risk-adjusted returns
    maximumDrawdown: 0.15,      // Max 15% drawdown
    
    // Risk management scores
    minimumRiskScore: 70,       // Good risk management
    minimumConsistencyScore: 65, // Consistent performance
    minimumDisciplineScore: 75,  // Following rules
    
    // Behavioral requirements
    maximumConsecutiveLosses: 7,
    minimumDiversification: 3,   // At least 3 different instruments
    maximumPositionSize: 0.10    // Max 10% per position
  };

  constructor(
    private analyticsService: PaperTradingAnalyticsService
  ) {}

  /**
   * Get comprehensive graduation assessment for a paper trading session
   */
  async assessGraduationEligibility(
    sessionId: string,
    userId: string,
    customCriteria?: Partial<GraduationCriteria>
  ): Promise<GraduationAssessment> {
    const criteria = { ...this.defaultCriteria, ...customCriteria };
    
    // Get session data
    const session = await this.getPaperTradingSession(sessionId);
    const trades = await this.getPaperTrades(sessionId);
    const portfolio = await this.getVirtualPortfolio(sessionId);
    
    if (!session || !portfolio) {
      throw new Error('Session or portfolio not found');
    }

    // Calculate performance metrics
    const metrics = await this.analyticsService.calculatePerformanceMetrics(
      sessionId,
      trades,
      portfolio
    );
    
    // Assess time requirements
    const timeRequirement = this.assessTimeRequirements(session, trades, criteria);
    
    // Assess performance requirements
    const performanceAssessment = this.assessPerformanceRequirements(metrics, criteria);
    
    // Assess behavioral requirements
    const behaviorAssessment = await this.assessBehavioralRequirements(trades, criteria);
    
    // Generate requirement status list
    const requirements = this.generateRequirementStatus(
      timeRequirement,
      performanceAssessment,
      behaviorAssessment,
      criteria
    );
    
    // Calculate overall scores
    const completedRequirements = requirements.filter(r => r.status === 'completed').length;
    const completionPercentage = (completedRequirements / requirements.length) * 100;
    const overallScore = this.calculateOverallScore(requirements, metrics);
    const graduationEligible = this.determineGraduationEligibility(requirements, overallScore);
    
    // Determine overall status
    const overallStatus = this.determineOverallStatus(completionPercentage, overallScore);
    
    // Generate guidance
    const guidance = this.generateGuidance(requirements, metrics, criteria);
    
    // Estimate time to graduation
    const estimatedTimeToGraduation = this.estimateTimeToGraduation(
      requirements,
      session,
      trades
    );
    
    return {
      sessionId,
      userId,
      assessmentDate: new Date(),
      overallStatus,
      overallScore,
      graduationEligible,
      requirements,
      completedRequirements,
      totalRequirements: requirements.length,
      completionPercentage,
      timeRequirement,
      performanceAssessment,
      nextSteps: guidance.nextSteps,
      strengths: guidance.strengths,
      areasForImprovement: guidance.areasForImprovement,
      recommendedActions: guidance.recommendedActions,
      estimatedTimeToGraduation
    };
  }

  /**
   * Track minimum time period requirement
   */
  async trackTimeRequirements(sessionId: string): Promise<{
    daysActive: number;
    daysRequired: number;
    daysRemaining: number;
    tradingDays: number;
    projectedCompletion: Date;
  }> {
    const session = await this.getPaperTradingSession(sessionId);
    if (!session) {
      throw new Error('Session not found');
    }

    const now = new Date();
    const sessionStart = new Date(session.startDate);
    const daysActive = Math.floor((now.getTime() - sessionStart.getTime()) / (1000 * 60 * 60 * 24));
    const daysRequired = session.minRequiredDays;
    const daysRemaining = Math.max(0, daysRequired - daysActive);
    
    // Count actual trading days (days with trades)
    const trades = await this.getPaperTrades(sessionId);
    const tradingDays = new Set(
      trades.map(t => t.openTime.toDateString())
    ).size;
    
    const projectedCompletion = new Date(
      sessionStart.getTime() + (daysRequired * 24 * 60 * 60 * 1000)
    );
    
    return {
      daysActive,
      daysRequired,
      daysRemaining,
      tradingDays,
      projectedCompletion
    };
  }

  /**
   * Track minimum trade count requirement
   */
  async trackTradeCountRequirement(sessionId: string): Promise<{
    tradesCompleted: number;
    tradesRequired: number;
    tradesRemaining: number;
    averageTradesPerDay: number;
    projectedCompletion: Date;
  }> {
    const session = await this.getPaperTradingSession(sessionId);
    const trades = await this.getPaperTrades(sessionId);
    
    if (!session) {
      throw new Error('Session not found');
    }

    const tradesCompleted = trades.length;
    const tradesRequired = session.minRequiredTrades;
    const tradesRemaining = Math.max(0, tradesRequired - tradesCompleted);
    
    const sessionDays = Math.max(1, Math.floor(
      (Date.now() - session.startDate.getTime()) / (1000 * 60 * 60 * 24)
    ));
    
    const averageTradesPerDay = tradesCompleted / sessionDays;
    
    const daysToCompletion = averageTradesPerDay > 0 
      ? Math.ceil(tradesRemaining / averageTradesPerDay)
      : Infinity;
    
    const projectedCompletion = new Date(Date.now() + daysToCompletion * 24 * 60 * 60 * 1000);
    
    return {
      tradesCompleted,
      tradesRequired,
      tradesRemaining,
      averageTradesPerDay,
      projectedCompletion
    };
  }

  /**
   * Measure performance consistency over time
   */
  async measurePerformanceConsistency(
    sessionId: string,
    windowDays: number = 7
  ): Promise<{
    consistencyScore: number;
    volatilityScore: number;
    trendScore: number;
    periodAnalysis: Array<{
      period: string;
      return: number;
      winRate: number;
      sharpe: number;
      drawdown: number;
    }>;
  }> {
    const trades = await this.getPaperTrades(sessionId);
    
    // Group trades into time windows
    const periods = this.groupTradesByPeriod(trades, windowDays);
    
    let consistencyScore = 0;
    let volatilityScore = 0;
    let trendScore = 0;
    const periodAnalysis: any[] = [];
    
    if (periods.length > 1) {
      const returns = periods.map(p => p.totalReturn);
      
      // Calculate consistency (lower variance = higher consistency)
      const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
      const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;
      const standardDeviation = Math.sqrt(variance);
      
      consistencyScore = Math.max(0, 100 - (standardDeviation * 100));
      
      // Calculate volatility score (penalize high volatility)
      volatilityScore = Math.max(0, 100 - (standardDeviation * 50));
      
      // Calculate trend score (reward positive trend)
      const firstHalf = returns.slice(0, Math.floor(returns.length / 2));
      const secondHalf = returns.slice(Math.floor(returns.length / 2));
      const firstAvg = firstHalf.reduce((sum, r) => sum + r, 0) / firstHalf.length;
      const secondAvg = secondHalf.reduce((sum, r) => sum + r, 0) / secondHalf.length;
      
      trendScore = secondAvg > firstAvg ? 75 + ((secondAvg - firstAvg) * 25) : 50;
      
      // Generate period analysis
      periods.forEach((period, index) => {
        periodAnalysis.push({
          period: `Period ${index + 1}`,
          return: period.totalReturn,
          winRate: period.winRate,
          sharpe: period.sharpe,
          drawdown: period.maxDrawdown
        });
      });
    }
    
    return {
      consistencyScore: Math.min(consistencyScore, 100),
      volatilityScore: Math.min(volatilityScore, 100),
      trendScore: Math.min(trendScore, 100),
      periodAnalysis
    };
  }

  /**
   * Demonstrate risk management scoring
   */
  async scoreRiskManagement(sessionId: string): Promise<{
    overallScore: number;
    components: {
      positionSizing: number;
      stopLossUsage: number;
      drawdownControl: number;
      diversification: number;
      marginManagement: number;
    };
    recommendations: string[];
  }> {
    const trades = await this.getPaperTrades(sessionId);
    const portfolio = await this.getVirtualPortfolio(sessionId);
    
    if (!portfolio) {
      throw new Error('Portfolio not found');
    }

    // Position sizing score
    const positionSizes = trades.map(t => 
      new Decimal(t.volume).mul(t.openPrice).div(portfolio.initialBalance).toNumber()
    );
    const maxPositionSize = Math.max(...positionSizes);
    const avgPositionSize = positionSizes.reduce((sum, size) => sum + size, 0) / positionSizes.length;
    const positionSizing = maxPositionSize <= 0.1 ? 100 : 
                          maxPositionSize <= 0.15 ? 80 : 
                          maxPositionSize <= 0.2 ? 60 : 40;
    
    // Stop loss usage score
    const tradesWithStopLoss = trades.filter(t => t.stopLoss !== undefined && t.stopLoss !== null);
    const stopLossUsage = (tradesWithStopLoss.length / trades.length) * 100;
    
    // Drawdown control score
    const metrics = await this.analyticsService.calculatePerformanceMetrics(sessionId, trades, portfolio);
    const drawdownControl = metrics.maxDrawdown <= 0.1 ? 100 : 
                           metrics.maxDrawdown <= 0.15 ? 80 :
                           metrics.maxDrawdown <= 0.2 ? 60 : 40;
    
    // Diversification score
    const uniqueInstruments = new Set(trades.map(t => t.symbol)).size;
    const diversification = uniqueInstruments >= 5 ? 100 :
                          uniqueInstruments >= 3 ? 80 :
                          uniqueInstruments >= 2 ? 60 : 40;
    
    // Margin management score (simplified)
    const marginManagement = portfolio.usedMargin <= portfolio.availableMargin * 0.7 ? 100 : 70;
    
    const components = {
      positionSizing,
      stopLossUsage,
      drawdownControl,
      diversification,
      marginManagement
    };
    
    const overallScore = Object.values(components).reduce((sum, score) => sum + score, 0) / Object.keys(components).length;
    
    // Generate recommendations
    const recommendations: string[] = [];
    if (positionSizing < 80) recommendations.push('Reduce position sizes to manage risk better');
    if (stopLossUsage < 80) recommendations.push('Use stop losses more consistently');
    if (drawdownControl < 80) recommendations.push('Improve drawdown control measures');
    if (diversification < 80) recommendations.push('Increase portfolio diversification');
    if (marginManagement < 80) recommendations.push('Better margin utilization management');
    
    return {
      overallScore,
      components,
      recommendations
    };
  }

  /**
   * Generate progress milestones for tracking
   */
  generateProgressMilestones(
    sessionId: string,
    criteria: GraduationCriteria
  ): ProgressMilestone[] {
    const now = new Date();
    const milestones: ProgressMilestone[] = [];
    
    // Time-based milestones
    milestones.push({
      id: 'time-25',
      name: '25% Time Requirement',
      description: `Complete ${Math.floor(criteria.minimumDays * 0.25)} days of trading`,
      targetDate: new Date(now.getTime() + (criteria.minimumDays * 0.25) * 24 * 60 * 60 * 1000),
      status: 'pending',
      importance: 'medium',
      category: 'time'
    });
    
    milestones.push({
      id: 'time-50',
      name: '50% Time Requirement',
      description: `Complete ${Math.floor(criteria.minimumDays * 0.5)} days of trading`,
      targetDate: new Date(now.getTime() + (criteria.minimumDays * 0.5) * 24 * 60 * 60 * 1000),
      status: 'pending',
      importance: 'medium',
      category: 'time'
    });
    
    // Trade count milestones
    milestones.push({
      id: 'trades-25',
      name: '25% Trade Requirement',
      description: `Complete ${Math.floor(criteria.minimumTrades * 0.25)} trades`,
      targetDate: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000),
      status: 'pending',
      importance: 'medium',
      category: 'performance'
    });
    
    // Performance milestones
    milestones.push({
      id: 'win-rate',
      name: 'Win Rate Achievement',
      description: `Achieve ${(criteria.minimumWinRate * 100).toFixed(0)}% win rate`,
      targetDate: new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000),
      status: 'pending',
      importance: 'high',
      category: 'performance'
    });
    
    milestones.push({
      id: 'risk-management',
      name: 'Risk Management Proficiency',
      description: `Achieve ${criteria.minimumRiskScore}+ risk management score`,
      targetDate: new Date(now.getTime() + 21 * 24 * 60 * 60 * 1000),
      status: 'pending',
      importance: 'critical',
      category: 'risk'
    });
    
    return milestones;
  }

  // Private helper methods

  private async getPaperTradingSession(sessionId: string): Promise<PaperTradingSession | null> {
    // TODO: Fetch from database using Supabase MCP tool
    return null;
  }

  private async getPaperTrades(sessionId: string): Promise<PaperTrade[]> {
    // TODO: Fetch from database using Supabase MCP tool
    return [];
  }

  private async getVirtualPortfolio(sessionId: string): Promise<VirtualPortfolio | null> {
    // TODO: Fetch from database using Supabase MCP tool
    return null;
  }

  private assessTimeRequirements(
    session: PaperTradingSession,
    trades: PaperTrade[],
    criteria: GraduationCriteria
  ): any {
    const now = new Date();
    const sessionStart = new Date(session.startDate);
    const daysCompleted = Math.floor((now.getTime() - sessionStart.getTime()) / (1000 * 60 * 60 * 24));
    const daysRequired = criteria.minimumDays;
    const daysRemaining = Math.max(0, daysRequired - daysCompleted);
    
    const tradesCompleted = trades.length;
    const tradesRequired = criteria.minimumTrades;
    const tradesRemaining = Math.max(0, tradesRequired - tradesCompleted);
    
    const projectedCompletion = new Date(
      sessionStart.getTime() + Math.max(daysRequired, tradesRemaining) * 24 * 60 * 60 * 1000
    );
    
    return {
      daysCompleted,
      daysRequired,
      tradesCompleted,
      tradesRequired,
      timeRemaining: daysRemaining,
      projectedCompletion
    };
  }

  private assessPerformanceRequirements(
    metrics: PerformanceMetrics,
    criteria: GraduationCriteria
  ): any {
    return {
      winRate: {
        current: metrics.winRate,
        required: criteria.minimumWinRate,
        met: metrics.winRate >= criteria.minimumWinRate
      },
      profitFactor: {
        current: metrics.profitFactor,
        required: criteria.minimumProfitFactor,
        met: metrics.profitFactor >= criteria.minimumProfitFactor
      },
      sharpeRatio: {
        current: metrics.sharpeRatio,
        required: criteria.minimumSharpeRatio,
        met: metrics.sharpeRatio >= criteria.minimumSharpeRatio
      },
      maxDrawdown: {
        current: metrics.maxDrawdown / 100,
        required: criteria.maximumDrawdown,
        met: (metrics.maxDrawdown / 100) <= criteria.maximumDrawdown
      },
      riskManagement: {
        current: 75, // Mock score - would be calculated
        required: criteria.minimumRiskScore,
        met: 75 >= criteria.minimumRiskScore
      },
      consistency: {
        current: 70, // Mock score - would be calculated
        required: criteria.minimumConsistencyScore,
        met: 70 >= criteria.minimumConsistencyScore
      }
    };
  }

  private async assessBehavioralRequirements(
    trades: PaperTrade[],
    criteria: GraduationCriteria
  ): Promise<any> {
    const consecutiveLosses = this.calculateMaxConsecutiveLosses(trades);
    const uniqueInstruments = new Set(trades.map(t => t.symbol)).size;
    
    return {
      consecutiveLosses: {
        current: consecutiveLosses,
        required: criteria.maximumConsecutiveLosses,
        met: consecutiveLosses <= criteria.maximumConsecutiveLosses
      },
      diversification: {
        current: uniqueInstruments,
        required: criteria.minimumDiversification,
        met: uniqueInstruments >= criteria.minimumDiversification
      },
      discipline: {
        current: 85, // Mock score
        required: criteria.minimumDisciplineScore,
        met: 85 >= criteria.minimumDisciplineScore
      }
    };
  }

  private generateRequirementStatus(
    timeRequirement: any,
    performanceAssessment: any,
    behaviorAssessment: any,
    criteria: GraduationCriteria
  ): RequirementStatus[] {
    const requirements: RequirementStatus[] = [];
    
    // Time requirements
    requirements.push({
      requirementId: 'min-days',
      name: 'Minimum Trading Days',
      description: `Complete at least ${criteria.minimumDays} days of trading`,
      status: timeRequirement.daysCompleted >= criteria.minimumDays ? 'completed' : 'in_progress',
      progress: Math.min((timeRequirement.daysCompleted / criteria.minimumDays) * 100, 100),
      currentValue: timeRequirement.daysCompleted,
      requiredValue: criteria.minimumDays,
      lastUpdated: new Date()
    });
    
    requirements.push({
      requirementId: 'min-trades',
      name: 'Minimum Trade Count',
      description: `Execute at least ${criteria.minimumTrades} trades`,
      status: timeRequirement.tradesCompleted >= criteria.minimumTrades ? 'completed' : 'in_progress',
      progress: Math.min((timeRequirement.tradesCompleted / criteria.minimumTrades) * 100, 100),
      currentValue: timeRequirement.tradesCompleted,
      requiredValue: criteria.minimumTrades,
      lastUpdated: new Date()
    });
    
    // Performance requirements
    Object.entries(performanceAssessment).forEach(([key, requirement]: [string, any]) => {
      requirements.push({
        requirementId: `perf-${key}`,
        name: key.charAt(0).toUpperCase() + key.slice(1),
        description: `Achieve ${key} of ${requirement.required}`,
        status: requirement.met ? 'completed' : 'in_progress',
        progress: Math.min((requirement.current / requirement.required) * 100, 100),
        currentValue: requirement.current,
        requiredValue: requirement.required,
        lastUpdated: new Date()
      });
    });
    
    return requirements;
  }

  private calculateOverallScore(requirements: RequirementStatus[], metrics: PerformanceMetrics): number {
    const completedCount = requirements.filter(r => r.status === 'completed').length;
    const baseScore = (completedCount / requirements.length) * 100;
    
    // Bonus points for exceptional performance
    let bonusScore = 0;
    if (metrics.sharpeRatio > 1.5) bonusScore += 5;
    if (metrics.winRate > 0.7) bonusScore += 5;
    if (metrics.maxDrawdown < 0.05) bonusScore += 5;
    
    return Math.min(baseScore + bonusScore, 100);
  }

  private determineGraduationEligibility(requirements: RequirementStatus[], overallScore: number): boolean {
    const criticalRequirements = requirements.filter(r => 
      r.requirementId.includes('min-days') || 
      r.requirementId.includes('min-trades') ||
      r.requirementId.includes('risk-management')
    );
    
    const criticalComplete = criticalRequirements.every(r => r.status === 'completed');
    
    return criticalComplete && overallScore >= 75;
  }

  private determineOverallStatus(completionPercentage: number, overallScore: number): any {
    if (overallScore >= 90 && completionPercentage >= 90) return 'graduated';
    if (overallScore >= 75 && completionPercentage >= 80) return 'ready';
    if (overallScore >= 50 && completionPercentage >= 50) return 'approaching';
    return 'not_ready';
  }

  private generateGuidance(
    requirements: RequirementStatus[],
    metrics: PerformanceMetrics,
    criteria: GraduationCriteria
  ): any {
    const incompleteRequirements = requirements.filter(r => r.status !== 'completed');
    
    const nextSteps = incompleteRequirements.slice(0, 3).map(r => 
      `Complete ${r.name}: ${r.progress.toFixed(1)}% done`
    );
    
    const strengths: string[] = [];
    const areasForImprovement: string[] = [];
    const recommendedActions: string[] = [];
    
    // Analyze strengths
    if (metrics.winRate > criteria.minimumWinRate) {
      strengths.push('Strong win rate achievement');
    }
    if (metrics.sharpeRatio > criteria.minimumSharpeRatio) {
      strengths.push('Good risk-adjusted returns');
    }
    
    // Analyze areas for improvement
    if (metrics.winRate < criteria.minimumWinRate) {
      areasForImprovement.push('Win rate needs improvement');
      recommendedActions.push('Focus on entry and exit timing');
    }
    if (metrics.maxDrawdown > criteria.maximumDrawdown * 100) {
      areasForImprovement.push('Drawdown control');
      recommendedActions.push('Implement stricter risk management');
    }
    
    return {
      nextSteps,
      strengths,
      areasForImprovement,
      recommendedActions
    };
  }

  private estimateTimeToGraduation(
    requirements: RequirementStatus[],
    session: PaperTradingSession,
    trades: PaperTrade[]
  ): number {
    const incompleteRequirements = requirements.filter(r => r.status !== 'completed');
    
    if (incompleteRequirements.length === 0) return 0;
    
    // Calculate based on current progress rates
    const timeRequirement = incompleteRequirements.find(r => r.requirementId === 'min-days');
    const tradeRequirement = incompleteRequirements.find(r => r.requirementId === 'min-trades');
    
    let estimatedDays = 0;
    
    if (timeRequirement) {
      estimatedDays = Math.max(estimatedDays, timeRequirement.requiredValue - timeRequirement.currentValue);
    }
    
    if (tradeRequirement) {
      const tradesRemaining = tradeRequirement.requiredValue - tradeRequirement.currentValue;
      const avgTradesPerDay = trades.length / Math.max(1, (Date.now() - session.startDate.getTime()) / (1000 * 60 * 60 * 24));
      const daysNeeded = Math.ceil(tradesRemaining / Math.max(1, avgTradesPerDay));
      estimatedDays = Math.max(estimatedDays, daysNeeded);
    }
    
    return estimatedDays;
  }

  private groupTradesByPeriod(trades: PaperTrade[], windowDays: number): any[] {
    // Simplified implementation - would group trades into time periods
    return [];
  }

  private calculateMaxConsecutiveLosses(trades: PaperTrade[]): number {
    let maxConsecutive = 0;
    let current = 0;
    
    trades.forEach(trade => {
      if ((trade.profit || 0) < 0) {
        current++;
        maxConsecutive = Math.max(maxConsecutive, current);
      } else {
        current = 0;
      }
    });
    
    return maxConsecutive;
  }
}