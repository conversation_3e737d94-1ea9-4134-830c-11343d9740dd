import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { UserActivityMonitoringService } from '../UserActivityMonitoringService';

// Mock Prisma
const mockPrisma = {
  userActivityLog: {
    create: vi.fn(),
    findMany: vi.fn(),
    updateMany: vi.fn(),
  },
  user: {
    findUnique: vi.fn(),
  },
} as unknown as PrismaClient;

describe('UserActivityMonitoringService', () => {
  let service: UserActivityMonitoringService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new UserActivityMonitoringService(mockPrisma);
  });

  afterEach(async () => {
    if (service) {
      service.stopMonitoring();
    }
  });

  describe('startMonitoring', () => {
    it('should start monitoring successfully', async () => {
      await service.startMonitoring();
      
      expect(service.getMonitoringStatus().isActive).toBe(true);
    });

    it('should emit monitoringStarted event', async () => {
      const eventSpy = vi.fn();
      service.on('monitoringStarted', eventSpy);
      
      await service.startMonitoring();
      
      expect(eventSpy).toHaveBeenCalled();
    });

    it('should not start if already active', async () => {
      await service.startMonitoring();
      
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      await service.startMonitoring();
      
      expect(consoleSpy).toHaveBeenCalledWith('User activity monitoring already active');
      consoleSpy.mockRestore();
    });
  });

  describe('stopMonitoring', () => {
    it('should stop monitoring successfully', async () => {
      await service.startMonitoring();
      service.stopMonitoring();
      
      expect(service.getMonitoringStatus().isActive).toBe(false);
    });

    it('should emit monitoringStopped event', async () => {
      const eventSpy = vi.fn();
      service.on('monitoringStopped', eventSpy);
      
      await service.startMonitoring();
      service.stopMonitoring();
      
      expect(eventSpy).toHaveBeenCalled();
    });
  });

  describe('trackActivity', () => {
    const mockActivityData = {
      userId: 'user-123',
      sessionId: 'session-456',
      activityType: 'login',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0',
      metadata: { location: 'dashboard' },
    };

    it('should track activity successfully', async () => {
      const mockCreatedActivity = {
        id: 'activity-789',
        ...mockActivityData,
        timestamp: new Date(),
        anomalyScore: 15,
        flaggedForReview: false,
      };

      vi.mocked(mockPrisma.userActivityLog.create).mockResolvedValue(mockCreatedActivity as any);

      const result = await service.trackActivity(mockActivityData);

      expect(mockPrisma.userActivityLog.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: mockActivityData.userId,
          sessionId: mockActivityData.sessionId,
          activityType: mockActivityData.activityType,
          ipAddress: mockActivityData.ipAddress,
          userAgent: mockActivityData.userAgent,
          metadata: mockActivityData.metadata,
          anomalyScore: expect.any(Number),
          flaggedForReview: expect.any(Boolean),
        }),
      });

      expect(result).toEqual({
        id: mockCreatedActivity.id,
        anomalyScore: mockCreatedActivity.anomalyScore,
        flaggedForReview: mockCreatedActivity.flaggedForReview,
        timestamp: mockCreatedActivity.timestamp.toISOString(),
      });
    });

    it('should calculate anomaly score correctly', async () => {
      const suspiciousActivity = {
        ...mockActivityData,
        activityType: 'risk_breach',
        metadata: { 
          tradeVolume: 1000000,
          riskScore: 85,
        },
      };

      const mockCreatedActivity = {
        id: 'activity-789',
        ...suspiciousActivity,
        timestamp: new Date(),
        anomalyScore: 75,
        flaggedForReview: true,
      };

      vi.mocked(mockPrisma.userActivityLog.create).mockResolvedValue(mockCreatedActivity as any);

      const result = await service.trackActivity(suspiciousActivity);

      expect(result.anomalyScore).toBeGreaterThan(50);
      expect(result.flaggedForReview).toBe(true);
    });

    it('should emit suspicious activity event for high anomaly scores', async () => {
      const eventSpy = vi.fn();
      service.on('suspiciousActivity', eventSpy);

      const mockCreatedActivity = {
        id: 'activity-789',
        ...mockActivityData,
        timestamp: new Date(),
        anomalyScore: 85,
        flaggedForReview: true,
      };

      vi.mocked(mockPrisma.userActivityLog.create).mockResolvedValue(mockCreatedActivity as any);

      await service.trackActivity({
        ...mockActivityData,
        activityType: 'withdrawal_request',
        metadata: { amount: 500000 },
      });

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: mockActivityData.userId,
          activityType: 'withdrawal_request',
          anomalyScore: 85,
        })
      );
    });

    it('should handle database errors gracefully', async () => {
      vi.mocked(mockPrisma.userActivityLog.create).mockRejectedValue(new Error('Database error'));

      await expect(service.trackActivity(mockActivityData)).rejects.toThrow('Database error');
    });
  });

  describe('getUserActivityData', () => {
    const mockOptions = {
      userId: 'user-123',
      timeRange: '24h',
      includeAnomalies: true,
      limit: 50,
    };

    it('should fetch user activity data successfully', async () => {
      const mockActivities = [
        {
          id: 'activity-1',
          userId: 'user-123',
          activityType: 'login',
          timestamp: new Date(),
          anomalyScore: 10,
          flaggedForReview: false,
          sessionId: 'session-1',
          metadata: {},
        },
        {
          id: 'activity-2',
          userId: 'user-123',
          activityType: 'trade_execution',
          timestamp: new Date(),
          anomalyScore: 75,
          flaggedForReview: true,
          sessionId: 'session-1',
          metadata: { tradeVolume: 50000 },
        },
      ];

      vi.mocked(mockPrisma.userActivityLog.findMany).mockResolvedValue(mockActivities as any);

      const result = await service.getUserActivityData(mockOptions);

      expect(mockPrisma.userActivityLog.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user-123',
          timestamp: {
            gte: expect.any(Date),
          },
        },
        orderBy: { timestamp: 'desc' },
        take: 50,
      });

      expect(result.activities).toHaveLength(2);
      expect(result.summary.totalActivities).toBe(2);
      expect(result.summary.suspiciousActivities).toBe(1);
      expect(result.summary.suspiciousActivityRate).toBe('50.0');
    });

    it('should filter by time range correctly', async () => {
      vi.mocked(mockPrisma.userActivityLog.findMany).mockResolvedValue([]);

      await service.getUserActivityData({ timeRange: '1h' });

      const expectedTime = new Date(Date.now() - 60 * 60 * 1000);
      const actualCall = vi.mocked(mockPrisma.userActivityLog.findMany).mock.calls[0][0];
      const actualTime = actualCall?.where?.timestamp?.gte as Date;

      expect(actualTime.getTime()).toBeCloseTo(expectedTime.getTime(), -2); // Within 100ms
    });

    it('should fetch all users when no userId provided', async () => {
      vi.mocked(mockPrisma.userActivityLog.findMany).mockResolvedValue([]);

      await service.getUserActivityData({ timeRange: '24h' });

      const actualCall = vi.mocked(mockPrisma.userActivityLog.findMany).mock.calls[0][0];
      expect(actualCall?.where).not.toHaveProperty('userId');
    });

    it('should generate alerts for suspicious activities', async () => {
      const suspiciousActivity = {
        id: 'activity-1',
        userId: 'user-123',
        activityType: 'risk_breach',
        timestamp: new Date(),
        anomalyScore: 95,
        flaggedForReview: true,
        sessionId: 'session-1',
        metadata: { breachType: 'position_limit' },
      };

      vi.mocked(mockPrisma.userActivityLog.findMany).mockResolvedValue([suspiciousActivity] as any);

      const result = await service.getUserActivityData(mockOptions);

      expect(result.alerts).toHaveLength(1);
      expect(result.alerts[0].severity).toBe('critical');
      expect(result.alerts[0].message).toContain('Suspicious risk_breach detected');
    });
  });

  describe('flagActivity', () => {
    const mockFlagData = {
      activityId: 'activity-123',
      flagReason: 'Suspicious behavior pattern',
      severity: 'high',
      flaggedBy: 'admin',
    };

    it('should flag activity successfully', async () => {
      const mockActivity = {
        id: 'activity-123',
        flaggedForReview: true,
        reviewStatus: 'pending',
      };

      vi.mocked(mockPrisma.userActivityLog.updateMany).mockResolvedValue({ count: 1 } as any);

      const result = await service.flagActivity(mockFlagData);

      expect(mockPrisma.userActivityLog.updateMany).toHaveBeenCalledWith({
        where: { id: mockFlagData.activityId },
        data: {
          flaggedForReview: true,
          reviewStatus: 'pending',
          flagReason: mockFlagData.flagReason,
          flaggedBy: mockFlagData.flaggedBy,
          flaggedAt: expect.any(Date),
        },
      });

      expect(result).toMatchObject({
        activityId: mockFlagData.activityId,
        flagReason: mockFlagData.flagReason,
        severity: mockFlagData.severity,
        flaggedBy: mockFlagData.flaggedBy,
        reviewStatus: 'pending',
      });
    });

    it('should emit activityFlagged event', async () => {
      const eventSpy = vi.fn();
      service.on('activityFlagged', eventSpy);

      vi.mocked(mockPrisma.userActivityLog.updateMany).mockResolvedValue({ count: 1 } as any);

      await service.flagActivity(mockFlagData);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          activityId: mockFlagData.activityId,
          flagReason: mockFlagData.flagReason,
        })
      );
    });

    it('should handle activity not found', async () => {
      vi.mocked(mockPrisma.userActivityLog.updateMany).mockResolvedValue({ count: 0 } as any);

      await expect(service.flagActivity(mockFlagData)).rejects.toThrow('Activity not found');
    });
  });

  describe('anomaly detection', () => {
    it('should detect unusual timing patterns', () => {
      const testService = service as any;
      
      // Activity at 3 AM
      const lateNightActivity = {
        activityType: 'login',
        timestamp: new Date('2023-01-01T03:00:00Z'),
        metadata: {},
      };

      const score = testService.calculateAnomalyScore(lateNightActivity);
      expect(score).toBeGreaterThan(20); // Base score + unusual time bonus
    });

    it('should detect high-risk activities', () => {
      const testService = service as any;
      
      const riskActivity = {
        activityType: 'risk_breach',
        metadata: { breachType: 'position_limit', severity: 'critical' },
      };

      const score = testService.calculateAnomalyScore(riskActivity);
      expect(score).toBeGreaterThan(40);
    });

    it('should detect unusual IP addresses', () => {
      const testService = service as any;
      
      const suspiciousIpActivity = {
        activityType: 'login',
        ipAddress: '************', // Not a typical internal IP
        metadata: {},
      };

      const score = testService.calculateAnomalyScore(suspiciousIpActivity);
      expect(score).toBeGreaterThan(10);
    });
  });

  describe('getMonitoringStatus', () => {
    it('should return correct monitoring status', () => {
      const status = service.getMonitoringStatus();
      
      expect(status).toHaveProperty('isActive');
      expect(status).toHaveProperty('startTime');
      expect(status).toHaveProperty('activitiesTracked');
      expect(status).toHaveProperty('suspiciousActivitiesDetected');
      expect(status).toHaveProperty('lastActivityTime');
    });

    it('should track activities count', async () => {
      const mockCreatedActivity = {
        id: 'activity-789',
        userId: 'user-123',
        timestamp: new Date(),
        anomalyScore: 15,
        flaggedForReview: false,
      };

      vi.mocked(mockPrisma.userActivityLog.create).mockResolvedValue(mockCreatedActivity as any);

      await service.trackActivity({
        userId: 'user-123',
        sessionId: 'session-456',
        activityType: 'login',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0',
        metadata: {},
      });

      const status = service.getMonitoringStatus();
      expect(status.activitiesTracked).toBe(1);
    });
  });

  describe('error handling', () => {
    it('should emit error event on tracking failure', async () => {
      const errorSpy = vi.fn();
      service.on('error', errorSpy);

      vi.mocked(mockPrisma.userActivityLog.create).mockRejectedValue(new Error('Database connection lost'));

      try {
        await service.trackActivity({
          userId: 'user-123',
          sessionId: 'session-456',
          activityType: 'login',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0',
          metadata: {},
        });
      } catch (error) {
        // Expected to throw
      }

      expect(errorSpy).toHaveBeenCalledWith(expect.any(Error));
    });
  });
});