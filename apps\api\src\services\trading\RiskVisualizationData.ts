/**
 * Risk Visualization Data Service
 * 
 * Prepares risk metric data for frontend visualization including risk heatmaps,
 * risk timeline data for historical tracking, and integration with plain English
 * metrics service for user-friendly translations.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';
import { Position, PortfolioRiskMetrics } from '../../types/trading';

// Visualization data types
export interface RiskHeatmapData {
  positions: Array<{
    symbol: string;
    size: string;
    riskContribution: number;
    correlation: number;
    volatility: number;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    color: string;
    coordinates: { x: number; y: number };
  }>;
  portfolioLevel: {
    overallRisk: number;
    diversificationScore: number;
    concentrationRisk: number;
  };
  colorScale: {
    low: string;
    medium: string;
    high: string;
    critical: string;
  };
}

export interface RiskTimelineData {
  timePoints: Array<{
    timestamp: string;
    portfolioValue: string;
    riskScore: number;
    volatility: number;
    drawdown: number;
    varBreach: boolean;
    events: Array<{
      type: 'position_opened' | 'position_closed' | 'limit_breach' | 'alert_triggered';
      description: string;
    }>;
  }>;
  trends: {
    riskTrend: 'increasing' | 'decreasing' | 'stable';
    volatilityTrend: 'increasing' | 'decreasing' | 'stable';
    performanceTrend: 'improving' | 'declining' | 'stable';
  };
}

export interface RiskAlertVisualization {
  activeAlerts: Array<{
    id: string;
    severity: 'info' | 'warning' | 'critical';
    title: string;
    message: string;
    actionRequired: boolean;
    suggestedActions: string[];
    icon: string;
    color: string;
    timestamp: string;
  }>;
  alertSummary: {
    total: number;
    critical: number;
    warning: number;
    info: number;
  };
}

export interface RiskDashboardData {
  overview: {
    riskScore: {
      current: number;
      grade: 'A' | 'B' | 'C' | 'D' | 'F';
      change24h: number;
      trend: 'improving' | 'declining' | 'stable';
    };
    portfolioValue: {
      total: string;
      unrealizedPnl: string;
      riskExposure: string;
      marginUsed: string;
    };
    limits: {
      dailyLoss: {
        limit: string;
        used: string;
        remaining: string;
        percentage: number;
      };
      weeklyLoss: {
        limit: string;
        used: string;
        remaining: string;
        percentage: number;
      };
    };
  };
  heatmap: RiskHeatmapData;
  timeline: RiskTimelineData;
  alerts: RiskAlertVisualization;
  metrics: {
    var: {
      daily95: string;
      daily99: string;
      weekly95: string;
      weekly99: string;
    };
    diversification: {
      score: number;
      grade: string;
      description: string;
    };
    correlation: {
      averageCorrelation: number;
      maxCorrelation: number;
      riskiest: string;
    };
  };
}

export class RiskVisualizationData {
  private colorPalette = {
    low: '#22c55e',      // Green
    medium: '#eab308',   // Yellow
    high: '#f97316',     // Orange
    critical: '#ef4444'  // Red
  };

  /**
   * Generate comprehensive risk dashboard data
   */
  public async generateDashboardData(
    userId: string,
    timeframe: '1d' | '1w' | '1m' = '1d',
    portfolioRisk: PortfolioRiskMetrics,
    positions: Position[]
  ): Promise<RiskDashboardData> {
    const heatmapData = await this.generateRiskHeatmap(positions, portfolioRisk);
    const timelineData = await this.generateRiskTimeline(userId, timeframe);
    const alertData = await this.generateAlertVisualization(userId);

    return {
      overview: {
        riskScore: {
          current: this.calculateOverallRiskScore(portfolioRisk),
          grade: this.calculateRiskGrade(portfolioRisk),
          change24h: await this.calculateRiskScoreChange(userId),
          trend: await this.calculateRiskTrend(userId),
        },
        portfolioValue: {
          total: portfolioRisk.totalValue.toString(),
          unrealizedPnl: portfolioRisk.totalUnrealizedPnl.toString(),
          riskExposure: this.calculateTotalRiskExposure(positions).toString(),
          marginUsed: this.calculateTotalMarginUsed(positions).toString(),
        },
        limits: {
          dailyLoss: await this.formatLimitData(userId, 'daily'),
          weeklyLoss: await this.formatLimitData(userId, 'weekly'),
        },
      },
      heatmap: heatmapData,
      timeline: timelineData,
      alerts: alertData,
      metrics: {
        var: {
          daily95: portfolioRisk.valueAtRisk.daily.confidence95.toString(),
          daily99: portfolioRisk.valueAtRisk.daily.confidence99.toString(),
          weekly95: portfolioRisk.valueAtRisk.weekly.confidence95.toString(),
          weekly99: portfolioRisk.valueAtRisk.weekly.confidence99.toString(),
        },
        diversification: {
          score: Math.round(portfolioRisk.concentrationRisk.diversificationIndex * 100),
          grade: this.getDiversificationGrade(portfolioRisk.concentrationRisk.diversificationIndex),
          description: this.getDiversificationDescription(portfolioRisk.concentrationRisk.diversificationIndex),
        },
        correlation: {
          averageCorrelation: portfolioRisk.correlationRisk.averageCorrelation,
          maxCorrelation: portfolioRisk.correlationRisk.maxCorrelation,
          riskiest: this.findRiskiestCorrelation(portfolioRisk.correlationRisk.correlationMatrix),
        },
      },
    };
  }

  /**
   * Generate risk heatmap data for portfolio visualization
   */
  public async generateRiskHeatmap(
    positions: Position[],
    portfolioRisk: PortfolioRiskMetrics
  ): Promise<RiskHeatmapData> {
    const totalValue = positions.reduce((sum, pos) => 
      sum.add(pos.size.mul(pos.currentPrice)), new Decimal(0)
    );

    const positionData = positions.map((position, index) => {
      const positionValue = position.size.mul(position.currentPrice);
      const weight = totalValue.gt(0) ? positionValue.div(totalValue).toNumber() : 0;
      const riskContribution = this.calculatePositionRiskContribution(position, portfolioRisk);
      const volatility = this.estimatePositionVolatility(position);
      const correlation = this.getPositionCorrelation(position.symbol, portfolioRisk);

      return {
        symbol: position.symbol,
        size: position.size.toString(),
        riskContribution,
        correlation,
        volatility,
        riskLevel: this.determineRiskLevel(riskContribution, volatility, correlation),
        color: this.getRiskColor(riskContribution, volatility),
        coordinates: this.calculateHeatmapCoordinates(weight, riskContribution, index),
      };
    });

    return {
      positions: positionData,
      portfolioLevel: {
        overallRisk: this.calculateOverallRiskScore(portfolioRisk),
        diversificationScore: Math.round(portfolioRisk.concentrationRisk.diversificationIndex * 100),
        concentrationRisk: Math.round(portfolioRisk.concentrationRisk.concentrationScore * 100),
      },
      colorScale: this.colorPalette,
    };
  }

  /**
   * Generate risk timeline data for historical tracking
   */
  public async generateRiskTimeline(
    userId: string,
    timeframe: '1d' | '1w' | '1m'
  ): Promise<RiskTimelineData> {
    // In a real implementation, this would query historical data from TimescaleDB
    const mockTimelineData = this.generateMockTimelineData(timeframe);

    return {
      timePoints: mockTimelineData,
      trends: {
        riskTrend: this.analyzeTrend(mockTimelineData.map(point => point.riskScore)),
        volatilityTrend: this.analyzeTrend(mockTimelineData.map(point => point.volatility)),
        performanceTrend: this.analyzePerformanceTrend(mockTimelineData),
      },
    };
  }

  /**
   * Generate alert visualization data
   */
  public async generateAlertVisualization(userId: string): Promise<RiskAlertVisualization> {
    // Mock implementation - in real scenario, this would query active alerts
    const mockAlerts = [
      {
        id: 'alert_1',
        severity: 'warning' as const,
        title: 'High Correlation Risk',
        message: 'EUR/USD and GBP/USD positions show 85% correlation',
        actionRequired: true,
        suggestedActions: [
          'Consider reducing one of the correlated positions',
          'Add hedge position to reduce correlation risk',
        ],
        icon: 'correlation-warning',
        color: this.colorPalette.medium,
        timestamp: new Date().toISOString(),
      },
      {
        id: 'alert_2',
        severity: 'info' as const,
        title: 'Position Size Limit',
        message: 'GOLD position approaching maximum size limit',
        actionRequired: false,
        suggestedActions: [
          'Monitor position size before adding more',
        ],
        icon: 'size-warning',
        color: this.colorPalette.low,
        timestamp: new Date(Date.now() - 3600000).toISOString(),
      },
    ];

    return {
      activeAlerts: mockAlerts,
      alertSummary: {
        total: mockAlerts.length,
        critical: mockAlerts.filter(a => a.severity === 'critical').length,
        warning: mockAlerts.filter(a => a.severity === 'warning').length,
        info: mockAlerts.filter(a => a.severity === 'info').length,
      },
    };
  }

  /**
   * Generate user-friendly risk explanations
   */
  public generateRiskExplanations(dashboardData: RiskDashboardData): Record<string, string> {
    const explanations: Record<string, string> = {};

    // Risk score explanation
    explanations.riskScore = this.explainRiskScore(
      dashboardData.overview.riskScore.current,
      dashboardData.overview.riskScore.grade
    );

    // Diversification explanation
    explanations.diversification = dashboardData.metrics.diversification.description;

    // VaR explanation
    explanations.var = this.explainVaR(
      parseFloat(dashboardData.metrics.var.daily95),
      parseFloat(dashboardData.metrics.var.daily99)
    );

    // Correlation explanation
    explanations.correlation = this.explainCorrelation(
      dashboardData.metrics.correlation.averageCorrelation,
      dashboardData.metrics.correlation.maxCorrelation
    );

    return explanations;
  }

  // Helper methods
  private calculateOverallRiskScore(portfolioRisk: PortfolioRiskMetrics): number {
    // Combine multiple risk factors into overall score
    const diversificationScore = portfolioRisk.concentrationRisk.diversificationIndex * 30;
    const leverageScore = Math.max(0, 40 - (portfolioRisk.leverageMetrics.grossLeverage * 10));
    const correlationScore = Math.max(0, 30 - (portfolioRisk.correlationRisk.averageCorrelation * 50));
    
    return Math.round(diversificationScore + leverageScore + correlationScore);
  }

  private calculateRiskGrade(portfolioRisk: PortfolioRiskMetrics): 'A' | 'B' | 'C' | 'D' | 'F' {
    const score = this.calculateOverallRiskScore(portfolioRisk);
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }

  private async calculateRiskScoreChange(userId: string): Promise<number> {
    // Mock implementation - would compare with yesterday's score
    return Math.round((Math.random() - 0.5) * 10);
  }

  private async calculateRiskTrend(userId: string): Promise<'improving' | 'declining' | 'stable'> {
    const change = await this.calculateRiskScoreChange(userId);
    if (change > 2) return 'improving';
    if (change < -2) return 'declining';
    return 'stable';
  }

  private calculateTotalRiskExposure(positions: Position[]): Decimal {
    return positions.reduce((total, pos) => {
      const exposure = pos.size.mul(pos.currentPrice).mul(pos.riskScore || 1);
      return total.add(exposure);
    }, new Decimal(0));
  }

  private calculateTotalMarginUsed(positions: Position[]): Decimal {
    return positions.reduce((total, pos) => total.add(pos.marginUsed), new Decimal(0));
  }

  private async formatLimitData(userId: string, period: 'daily' | 'weekly'): Promise<{
    limit: string;
    used: string;
    remaining: string;
    percentage: number;
  }> {
    // Mock implementation
    const limit = new Decimal(period === 'daily' ? '1000' : '5000');
    const used = new Decimal(period === 'daily' ? '150' : '800');
    const remaining = limit.sub(used);
    const percentage = used.div(limit).mul(100).toNumber();

    return {
      limit: limit.toString(),
      used: used.toString(),
      remaining: remaining.toString(),
      percentage: Math.round(percentage),
    };
  }

  private calculatePositionRiskContribution(
    position: Position,
    portfolioRisk: PortfolioRiskMetrics
  ): number {
    const positionValue = position.size.mul(position.currentPrice);
    const portfolioValue = portfolioRisk.totalValue;
    const weight = portfolioValue.gt(0) ? positionValue.div(portfolioValue).toNumber() : 0;
    
    // Risk contribution considers weight and volatility
    return Math.round(weight * 100 * (position.riskScore || 1));
  }

  private estimatePositionVolatility(position: Position): number {
    // Mock volatility based on symbol characteristics
    const baseVolatility = {
      'EUR/USD': 0.15,
      'GBP/USD': 0.18,
      'USD/JPY': 0.16,
      'GOLD': 0.25,
      'OIL': 0.35,
    };
    
    return baseVolatility[position.symbol as keyof typeof baseVolatility] || 0.20;
  }

  private getPositionCorrelation(symbol: string, portfolioRisk: PortfolioRiskMetrics): number {
    const correlationMatrix = portfolioRisk.correlationRisk.correlationMatrix;
    if (!correlationMatrix[symbol]) return 0;
    
    // Return average correlation with other positions
    const correlations = Object.values(correlationMatrix[symbol])
      .filter(corr => corr !== 1); // Exclude self-correlation
    
    return correlations.length > 0 ? 
      correlations.reduce((sum, corr) => sum + Math.abs(corr), 0) / correlations.length : 0;
  }

  private determineRiskLevel(
    riskContribution: number,
    volatility: number,
    correlation: number
  ): 'low' | 'medium' | 'high' | 'critical' {
    const riskScore = (riskContribution / 10) + (volatility * 100) + (correlation * 50);
    
    if (riskScore > 80) return 'critical';
    if (riskScore > 60) return 'high';
    if (riskScore > 30) return 'medium';
    return 'low';
  }

  private getRiskColor(riskContribution: number, volatility: number): string {
    const riskScore = (riskContribution / 10) + (volatility * 100);
    
    if (riskScore > 80) return this.colorPalette.critical;
    if (riskScore > 60) return this.colorPalette.high;
    if (riskScore > 30) return this.colorPalette.medium;
    return this.colorPalette.low;
  }

  private calculateHeatmapCoordinates(
    weight: number,
    riskContribution: number,
    index: number
  ): { x: number; y: number } {
    // Create a treemap-like layout
    const cols = Math.ceil(Math.sqrt(10)); // Assume max 10 positions for layout
    const x = (index % cols) * (100 / cols) + (weight * 20);
    const y = Math.floor(index / cols) * (100 / cols) + (riskContribution / 5);
    
    return { 
      x: Math.min(95, Math.max(5, x)), 
      y: Math.min(95, Math.max(5, y)) 
    };
  }

  private generateMockTimelineData(timeframe: string): Array<any> {
    const points = timeframe === '1d' ? 24 : timeframe === '1w' ? 7 : 30;
    const data = [];
    
    for (let i = 0; i < points; i++) {
      const timestamp = new Date(Date.now() - (i * (timeframe === '1d' ? 3600000 : 86400000))).toISOString();
      data.unshift({
        timestamp,
        portfolioValue: (10000 + Math.random() * 2000).toFixed(2),
        riskScore: Math.round(70 + Math.random() * 30),
        volatility: Math.round((0.15 + Math.random() * 0.10) * 100),
        drawdown: Math.round(Math.random() * 5),
        varBreach: Math.random() < 0.05, // 5% chance of VaR breach
        events: Math.random() < 0.2 ? [{
          type: 'position_opened' as const,
          description: 'New EUR/USD position opened',
        }] : [],
      });
    }
    
    return data;
  }

  private analyzeTrend(values: number[]): 'increasing' | 'decreasing' | 'stable' {
    if (values.length < 2) return 'stable';
    
    const recent = values.slice(-3);
    const earlier = values.slice(-6, -3);
    
    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    const earlierAvg = earlier.reduce((sum, val) => sum + val, 0) / earlier.length;
    
    const change = recentAvg - earlierAvg;
    
    if (change > 2) return 'increasing';
    if (change < -2) return 'decreasing';
    return 'stable';
  }

  private analyzePerformanceTrend(timelineData: any[]): 'improving' | 'declining' | 'stable' {
    if (timelineData.length < 2) return 'stable';
    
    const first = parseFloat(timelineData[0].portfolioValue);
    const last = parseFloat(timelineData[timelineData.length - 1].portfolioValue);
    const change = ((last - first) / first) * 100;
    
    if (change > 1) return 'improving';
    if (change < -1) return 'declining';
    return 'stable';
  }

  private getDiversificationGrade(score: number): string {
    if (score >= 0.9) return 'Excellent';
    if (score >= 0.7) return 'Good';
    if (score >= 0.5) return 'Fair';
    return 'Poor';
  }

  private getDiversificationDescription(score: number): string {
    if (score >= 0.9) {
      return 'Portfolio is well diversified across different instruments with low concentration risk';
    } else if (score >= 0.7) {
      return 'Good diversification with some room for improvement in position distribution';
    } else if (score >= 0.5) {
      return 'Moderate diversification - consider adding more uncorrelated instruments';
    } else {
      return 'Poor diversification - high concentration risk detected, immediate rebalancing recommended';
    }
  }

  private findRiskiestCorrelation(correlationMatrix: Record<string, Record<string, number>>): string {
    let maxCorr = 0;
    let riskiestPair = 'None';
    
    Object.keys(correlationMatrix).forEach(symbol1 => {
      Object.keys(correlationMatrix[symbol1]).forEach(symbol2 => {
        if (symbol1 !== symbol2) {
          const corr = Math.abs(correlationMatrix[symbol1][symbol2]);
          if (corr > maxCorr) {
            maxCorr = corr;
            riskiestPair = `${symbol1}-${symbol2}`;
          }
        }
      });
    });
    
    return riskiestPair;
  }

  private explainRiskScore(score: number, grade: string): string {
    return `Your portfolio has a risk score of ${score}/100 (Grade ${grade}). ${
      score >= 80 ? 'This indicates excellent risk management with well-balanced positions.' :
      score >= 60 ? 'This shows good risk control with some areas for improvement.' :
      'This suggests higher risk levels that may need attention.'
    }`;
  }

  private explainVaR(daily95: number, daily99: number): string {
    return `Value at Risk indicates potential losses: there's a 5% chance of losing more than $${daily95.toFixed(0)} daily, and a 1% chance of losing more than $${daily99.toFixed(0)} daily.`;
  }

  private explainCorrelation(average: number, max: number): string {
    const avgPercent = Math.round(average * 100);
    const maxPercent = Math.round(max * 100);
    
    return `Your positions have an average correlation of ${avgPercent}% with maximum correlation of ${maxPercent}%. ${
      maxPercent > 80 ? 'High correlation increases portfolio risk during market stress.' :
      maxPercent > 60 ? 'Moderate correlation provides some diversification benefits.' :
      'Low correlation indicates good diversification.'
    }`;
  }
}