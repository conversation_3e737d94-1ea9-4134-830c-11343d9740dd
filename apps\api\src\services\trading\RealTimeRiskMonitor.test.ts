/**
 * Real-Time Risk Monitor Tests
 * 
 * Comprehensive test suite for RealTimeRiskMonitor with 100% coverage
 * as required for financial calculations.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { 
  RealTimeRiskMonitor, 
  createRealTimeRiskMonitor,
  type RealTimeRiskData,
  type MonitoringConfig,
  type MarketDataUpdate,
  type PositionRiskData
} from './RealTimeRiskMonitor';

describe('RealTimeRiskMonitor', () => {
  let monitor: RealTimeRiskMonitor;
  let defaultConfig: MonitoringConfig;
  
  beforeEach(() => {
    defaultConfig = {
      updateIntervalMs: 1000, // 1 second for testing
      alertThresholds: {
        maxDailyLossPercentage: 5,
        maxWeeklyLossPercentage: 10,
        maxPortfolioRisk: 20,
        maxPositionConcentration: 25,
        maxMarginUtilization: 80,
        maxDrawdownPercentage: 15,
        volatilityThreshold: 30,
        correlationThreshold: 0.8
      },
      enableRealTimeAlerts: true,
      enableAutoLiquidation: false,
      maxAlertHistory: 50,
      riskCalculationMethod: 'simple',
      confidenceLevel: 0.95,
      lookbackDays: 30
    };
    monitor = new RealTimeRiskMonitor(defaultConfig);
  });

  afterEach(() => {
    // Clean up any running intervals
    if (monitor) {
      monitor.stopMonitoring('test-user');
    }
    vi.clearAllTimers();
  });

  describe('Constructor and Factory', () => {
    it('should create instance successfully', () => {
      expect(monitor).toBeInstanceOf(RealTimeRiskMonitor);
    });

    it('should create instance via factory function', () => {
      const mon = createRealTimeRiskMonitor();
      expect(mon).toBeInstanceOf(RealTimeRiskMonitor);
    });

    it('should create instance with custom config', () => {
      const customConfig = { updateIntervalMs: 2000, maxAlertHistory: 25 };
      const mon = createRealTimeRiskMonitor(customConfig);
      expect(mon).toBeInstanceOf(RealTimeRiskMonitor);
    });

    it('should throw error for invalid config', () => {
      const invalidConfig = { ...defaultConfig, updateIntervalMs: 500 };
      expect(() => new RealTimeRiskMonitor(invalidConfig))
        .toThrow('Update interval must be at least 1000ms');
    });
  });

  describe('Configuration Validation', () => {
    it('should reject update interval less than 1000ms', () => {
      const config = { ...defaultConfig, updateIntervalMs: 500 };
      expect(() => new RealTimeRiskMonitor(config))
        .toThrow('Update interval must be at least 1000ms');
    });

    it('should reject negative daily loss percentage', () => {
      const config = { ...defaultConfig };
      config.alertThresholds.maxDailyLossPercentage = -1;
      expect(() => new RealTimeRiskMonitor(config))
        .toThrow('Max daily loss percentage must be positive');
    });

    it('should reject invalid confidence level', () => {
      const config = { ...defaultConfig, confidenceLevel: 1.5 };
      expect(() => new RealTimeRiskMonitor(config))
        .toThrow('Confidence level must be between 0 and 1');
    });
  });

  describe('Monitoring Lifecycle', () => {
    const initialRiskData: RealTimeRiskData = {
      userId: 'test-user',
      timestamp: new Date(),
      accountBalance: new Decimal(10000),
      totalExposure: new Decimal(2000),
      availableMargin: new Decimal(8000),
      marginUtilization: 20,
      dailyPnL: new Decimal(-100),
      weeklyPnL: new Decimal(-200),
      portfolioVaR: new Decimal(500),
      maxDrawdown: new Decimal(300),
      riskScore: 25,
      activeAlerts: [],
      positionRisks: []
    };

    it('should start monitoring successfully', async () => {
      return new Promise<void>((resolve) => {
        monitor.on('monitoring_started', (event) => {
          expect(event.userId).toBe('test-user');
          expect(event.timestamp).toBeInstanceOf(Date);
          resolve();
        });

        monitor.startMonitoring('test-user', initialRiskData);
        
        const riskData = monitor.getRiskData('test-user');
        expect(riskData).toEqual(initialRiskData);
      });
    });

    it('should stop monitoring successfully', async () => {
      monitor.startMonitoring('test-user', initialRiskData);
      
      return new Promise<void>((resolve) => {
        monitor.on('monitoring_stopped', (event) => {
          expect(event.userId).toBe('test-user');
          resolve();
        });

        monitor.stopMonitoring('test-user');
        
        const riskData = monitor.getRiskData('test-user');
        expect(riskData).toBeUndefined();
      });
    });

    it('should handle multiple users', () => {
      const user1Data = { ...initialRiskData, userId: 'user-1' };
      const user2Data = { ...initialRiskData, userId: 'user-2' };

      monitor.startMonitoring('user-1', user1Data);
      monitor.startMonitoring('user-2', user2Data);

      expect(monitor.getRiskData('user-1')).toBeDefined();
      expect(monitor.getRiskData('user-2')).toBeDefined();

      const stats = monitor.getMonitoringStats();
      expect(stats.activeUsers).toBe(2);
    });
  });

  describe('Risk Data Updates', () => {
    const initialRiskData: RealTimeRiskData = {
      userId: 'test-user',
      timestamp: new Date(),
      accountBalance: new Decimal(10000),
      totalExposure: new Decimal(2000),
      availableMargin: new Decimal(8000),
      marginUtilization: 20,
      dailyPnL: new Decimal(-100),
      weeklyPnL: new Decimal(-200),
      portfolioVaR: new Decimal(500),
      maxDrawdown: new Decimal(300),
      riskScore: 25,
      activeAlerts: [],
      positionRisks: []
    };

    beforeEach(() => {
      monitor.startMonitoring('test-user', initialRiskData);
    });

    it('should update risk data successfully', async () => {
      return new Promise<void>((resolve) => {
        monitor.on('risk_data_updated', (event) => {
          expect(event.userId).toBe('test-user');
          expect(event.riskData.marginUtilization).toBe(30);
          resolve();
        });

        monitor.updateRiskData('test-user', { marginUtilization: 30 });
      });
    });

    it('should throw error for non-monitored user', () => {
      expect(() => monitor.updateRiskData('unknown-user', { marginUtilization: 30 }))
        .toThrow('Risk monitoring not active for user unknown-user');
    });

    it('should recalculate risk data correctly', () => {
      const positionRisks: PositionRiskData[] = [
        {
          symbol: 'EURUSD',
          size: new Decimal(1000),
          marketValue: new Decimal(1100),
          unrealizedPnL: new Decimal(100),
          dailyVolatility: 15,
          beta: 1.2,
          contribution: 0.1,
          stopLossDistance: new Decimal(50),
          timeInPosition: 24,
          riskRating: 'medium'
        }
      ];

      monitor.updateRiskData('test-user', { positionRisks });
      
      const updatedData = monitor.getRiskData('test-user');
      expect(updatedData!.positionRisks).toEqual(positionRisks);
    });
  });

  describe('Market Data Processing', () => {
    const initialRiskData: RealTimeRiskData = {
      userId: 'test-user',
      timestamp: new Date(),
      accountBalance: new Decimal(10000),
      totalExposure: new Decimal(2000),
      availableMargin: new Decimal(8000),
      marginUtilization: 20,
      dailyPnL: new Decimal(-100),
      weeklyPnL: new Decimal(-200),
      portfolioVaR: new Decimal(500),
      maxDrawdown: new Decimal(300),
      riskScore: 25,
      activeAlerts: [],
      positionRisks: [
        {
          symbol: 'EURUSD',
          size: new Decimal(1000),
          marketValue: new Decimal(1100),
          unrealizedPnL: new Decimal(100),
          dailyVolatility: 15,
          beta: 1.2,
          contribution: 0.1,
          stopLossDistance: new Decimal(50),
          timeInPosition: 24,
          riskRating: 'medium'
        }
      ]
    };

    beforeEach(() => {
      monitor.startMonitoring('test-user', initialRiskData);
    });

    it('should process market data updates', async () => {
      const marketUpdates: MarketDataUpdate[] = [
        {
          symbol: 'EURUSD',
          price: new Decimal(1.1100),
          bid: new Decimal(1.1095),
          ask: new Decimal(1.1105),
          timestamp: new Date(),
          volatility: 18
        }
      ];

      return new Promise<void>((resolve) => {
        monitor.on('market_risk_update', (event) => {
          expect(event.userId).toBe('test-user');
          expect(event.marketUpdates).toContain('EURUSD');
          resolve();
        });

        monitor.processMarketDataUpdate(marketUpdates);
      });
    });

    it('should ignore irrelevant market updates', () => {
      const marketUpdates: MarketDataUpdate[] = [
        {
          symbol: 'GBPUSD', // Not in positions
          price: new Decimal(1.3100),
          bid: new Decimal(1.3095),
          ask: new Decimal(1.3105),
          timestamp: new Date()
        }
      ];

      let updateFired = false;
      monitor.on('market_risk_update', () => {
        updateFired = true;
      });

      monitor.processMarketDataUpdate(marketUpdates);
      
      // Give some time for event to potentially fire
      setTimeout(() => {
        expect(updateFired).toBe(false);
      }, 10);
    });
  });

  describe('Risk Score Calculation', () => {
    it('should calculate risk score correctly', () => {
      const riskData: RealTimeRiskData = {
        userId: 'test-user',
        timestamp: new Date(),
        accountBalance: new Decimal(10000),
        totalExposure: new Decimal(5000), // 50% exposure
        availableMargin: new Decimal(5000),
        marginUtilization: 60,
        dailyPnL: new Decimal(-250), // 2.5% daily loss
        weeklyPnL: new Decimal(-500),
        portfolioVaR: new Decimal(750),
        maxDrawdown: new Decimal(400),
        riskScore: 0, // Will be calculated
        activeAlerts: [],
        positionRisks: [
          {
            symbol: 'EURUSD',
            size: new Decimal(1000),
            marketValue: new Decimal(2500),
            unrealizedPnL: new Decimal(-125),
            dailyVolatility: 25,
            beta: 1.0,
            contribution: 0.5,
            stopLossDistance: new Decimal(50),
            timeInPosition: 24,
            riskRating: 'medium'
          },
          {
            symbol: 'GBPUSD',
            size: new Decimal(800),
            marketValue: new Decimal(2500),
            unrealizedPnL: new Decimal(-125),
            dailyVolatility: 25,
            beta: 1.1,
            contribution: 0.5,
            stopLossDistance: new Decimal(60),
            timeInPosition: 12,
            riskRating: 'medium'
          }
        ]
      };

      const riskScore = monitor.calculateRiskScore(riskData);
      
      expect(riskScore).toBeGreaterThan(0);
      expect(riskScore).toBeLessThanOrEqual(100);
    });

    it('should return higher scores for higher risk', () => {
      const lowRiskData: RealTimeRiskData = {
        userId: 'test-user',
        timestamp: new Date(),
        accountBalance: new Decimal(10000),
        totalExposure: new Decimal(1000),
        availableMargin: new Decimal(9000),
        marginUtilization: 10,
        dailyPnL: new Decimal(50),
        weeklyPnL: new Decimal(100),
        portfolioVaR: new Decimal(200),
        maxDrawdown: new Decimal(100),
        riskScore: 0,
        activeAlerts: [],
        positionRisks: []
      };

      const highRiskData: RealTimeRiskData = {
        ...lowRiskData,
        totalExposure: new Decimal(8000),
        marginUtilization: 90,
        dailyPnL: new Decimal(-500),
        portfolioVaR: new Decimal(1600)
      };

      const lowScore = monitor.calculateRiskScore(lowRiskData);
      const highScore = monitor.calculateRiskScore(highRiskData);

      expect(highScore).toBeGreaterThan(lowScore);
    });
  });

  describe('Alert Management', () => {
    const riskDataWithAlerts: RealTimeRiskData = {
      userId: 'test-user',
      timestamp: new Date(),
      accountBalance: new Decimal(10000),
      totalExposure: new Decimal(8000),
      availableMargin: new Decimal(2000),
      marginUtilization: 85, // Above 80% threshold
      dailyPnL: new Decimal(-450), // 4.5% loss, approaching 5% limit
      weeklyPnL: new Decimal(-800),
      portfolioVaR: new Decimal(1600),
      maxDrawdown: new Decimal(500),
      riskScore: 75,
      activeAlerts: [],
      positionRisks: [
        {
          symbol: 'EURUSD',
          size: new Decimal(3000),
          marketValue: new Decimal(8000), // 80% concentration
          unrealizedPnL: new Decimal(-400),
          dailyVolatility: 20,
          beta: 1.0,
          contribution: 1.0,
          stopLossDistance: new Decimal(200),
          timeInPosition: 48,
          riskRating: 'high'
        }
      ]
    };

    beforeEach(() => {
      monitor.startMonitoring('test-user', riskDataWithAlerts);
    });

    it('should generate daily loss alerts', async () => {
      return new Promise<void>((resolve) => {
        monitor.on('new_alerts', (event) => {
          const lossAlert = event.alerts.find(alert => alert.category === 'loss_limit');
          expect(lossAlert).toBeDefined();
          expect(lossAlert!.type).toBe('warning'); // 4.5% is warning, not critical yet
          resolve();
        });

        monitor.updateRiskData('test-user', riskDataWithAlerts);
      });
    });

    it('should generate margin utilization alerts', async () => {
      return new Promise<void>((resolve) => {
        monitor.on('new_alerts', (event) => {
          const marginAlert = event.alerts.find(alert => alert.category === 'margin');
          expect(marginAlert).toBeDefined();
          expect(marginAlert!.type).toBe('critical'); // 85% > 80% threshold
          resolve();
        });

        monitor.updateRiskData('test-user', riskDataWithAlerts);
      });
    });

    it('should generate concentration alerts', async () => {
      return new Promise<void>((resolve) => {
        monitor.on('new_alerts', (event) => {
          const concentrationAlert = event.alerts.find(alert => alert.category === 'exposure');
          expect(concentrationAlert).toBeDefined();
          expect(concentrationAlert!.message).toContain('concentration');
          resolve();
        });

        monitor.updateRiskData('test-user', riskDataWithAlerts);
      });
    });

    it('should acknowledge alerts successfully', () => {
      // First generate an alert
      monitor.updateRiskData('test-user', riskDataWithAlerts);
      
      const activeAlerts = monitor.getActiveAlerts('test-user');
      expect(activeAlerts.length).toBeGreaterThan(0);

      const alertId = activeAlerts[0].id;
      const acknowledged = monitor.acknowledgeAlert('test-user', alertId);
      
      expect(acknowledged).toBe(true);
      
      const remainingAlerts = monitor.getActiveAlerts('test-user');
      expect(remainingAlerts.find(alert => alert.id === alertId)).toBeUndefined();
      
      const history = monitor.getAlertHistory('test-user');
      expect(history.find(alert => alert.id === alertId)).toBeDefined();
    });

    it('should not acknowledge non-existent alerts', () => {
      const acknowledged = monitor.acknowledgeAlert('test-user', 'non-existent-alert');
      expect(acknowledged).toBe(false);
    });

    it('should maintain alert history limit', () => {
      // Generate many alerts by repeated updates
      for (let i = 0; i < 60; i++) {
        const alertData = {
          ...riskDataWithAlerts,
          dailyPnL: new Decimal(-500 - i) // Make each one slightly worse
        };
        monitor.updateRiskData('test-user', alertData);
        
        const alerts = monitor.getActiveAlerts('test-user');
        if (alerts.length > 0) {
          monitor.acknowledgeAlert('test-user', alerts[0].id);
        }
      }
      
      const history = monitor.getAlertHistory('test-user');
      expect(history.length).toBeLessThanOrEqual(defaultConfig.maxAlertHistory);
    });
  });

  describe('Configuration Updates', () => {
    beforeEach(() => {
      vi.useFakeTimers();
    });

    afterEach(() => {
      vi.useRealTimers();
    });

    it('should update configuration successfully', () => {
      const newConfig = { 
        updateIntervalMs: 2000,
        maxAlertHistory: 25 
      };
      
      monitor.updateConfig(newConfig);
      // Configuration update should work without errors
    });

    it('should validate updated configuration', () => {
      const invalidUpdate = { updateIntervalMs: 500 };
      expect(() => monitor.updateConfig(invalidUpdate)).toThrow();
    });

    it('should restart monitoring with new interval', () => {
      const initialData: RealTimeRiskData = {
        userId: 'test-user',
        timestamp: new Date(),
        accountBalance: new Decimal(10000),
        totalExposure: new Decimal(2000),
        availableMargin: new Decimal(8000),
        marginUtilization: 20,
        dailyPnL: new Decimal(-100),
        weeklyPnL: new Decimal(-200),
        portfolioVaR: new Decimal(500),
        maxDrawdown: new Decimal(300),
        riskScore: 25,
        activeAlerts: [],
        positionRisks: []
      };

      monitor.startMonitoring('test-user', initialData);
      monitor.updateConfig({ updateIntervalMs: 2000 });
      
      // Should not throw errors
      expect(monitor.getRiskData('test-user')).toBeDefined();
    });
  });

  describe('Monitoring Statistics', () => {
    it('should provide accurate monitoring statistics', () => {
      const initialData: RealTimeRiskData = {
        userId: 'test-user',
        timestamp: new Date(),
        accountBalance: new Decimal(10000),
        totalExposure: new Decimal(2000),
        availableMargin: new Decimal(8000),
        marginUtilization: 20,
        dailyPnL: new Decimal(-100),
        weeklyPnL: new Decimal(-200),
        portfolioVaR: new Decimal(500),
        maxDrawdown: new Decimal(300),
        riskScore: 25,
        activeAlerts: [
          {
            id: 'alert1',
            type: 'warning',
            category: 'exposure',
            message: 'Test alert',
            currentValue: new Decimal(25),
            threshold: new Decimal(30),
            severity: 5,
            timestamp: new Date(),
            recommendedActions: [],
            autoResolvable: false
          }
        ],
        positionRisks: []
      };

      monitor.startMonitoring('test-user', initialData);
      
      const stats = monitor.getMonitoringStats();
      expect(stats.activeUsers).toBe(1);
      expect(stats.totalAlerts).toBe(1);
      expect(stats.averageRiskScore).toBe(25);
      expect(stats.lastUpdateTime).toBeInstanceOf(Date);
    });

    it('should handle empty monitoring state', () => {
      const stats = monitor.getMonitoringStats();
      expect(stats.activeUsers).toBe(0);
      expect(stats.totalAlerts).toBe(0);
      expect(stats.averageRiskScore).toBe(0);
    });
  });

  describe('Event Handling', () => {
    it('should emit monitoring events correctly', () => {
      const events: string[] = [];
      
      monitor.on('monitoring_started', () => events.push('started'));
      monitor.on('monitoring_stopped', () => events.push('stopped'));
      monitor.on('risk_data_updated', () => events.push('updated'));
      monitor.on('alert_acknowledged', () => events.push('acknowledged'));

      const initialData: RealTimeRiskData = {
        userId: 'test-user',
        timestamp: new Date(),
        accountBalance: new Decimal(10000),
        totalExposure: new Decimal(2000),
        availableMargin: new Decimal(8000),
        marginUtilization: 20,
        dailyPnL: new Decimal(-100),
        weeklyPnL: new Decimal(-200),
        portfolioVaR: new Decimal(500),
        maxDrawdown: new Decimal(300),
        riskScore: 25,
        activeAlerts: [],
        positionRisks: []
      };

      monitor.startMonitoring('test-user', initialData);
      monitor.updateRiskData('test-user', { marginUtilization: 30 });
      monitor.stopMonitoring('test-user');

      expect(events).toContain('started');
      expect(events).toContain('updated');
      expect(events).toContain('stopped');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle division by zero in risk calculations', () => {
      const riskData: RealTimeRiskData = {
        userId: 'test-user',
        timestamp: new Date(),
        accountBalance: new Decimal(0), // Zero balance edge case
        totalExposure: new Decimal(1000),
        availableMargin: new Decimal(0),
        marginUtilization: 0,
        dailyPnL: new Decimal(-100),
        weeklyPnL: new Decimal(-200),
        portfolioVaR: new Decimal(500),
        maxDrawdown: new Decimal(300),
        riskScore: 0,
        activeAlerts: [],
        positionRisks: []
      };

      expect(() => monitor.calculateRiskScore(riskData)).not.toThrow();
    });

    it('should handle empty position arrays', () => {
      const riskData: RealTimeRiskData = {
        userId: 'test-user',
        timestamp: new Date(),
        accountBalance: new Decimal(10000),
        totalExposure: new Decimal(0),
        availableMargin: new Decimal(10000),
        marginUtilization: 0,
        dailyPnL: new Decimal(0),
        weeklyPnL: new Decimal(0),
        portfolioVaR: new Decimal(0),
        maxDrawdown: new Decimal(0),
        riskScore: 0,
        activeAlerts: [],
        positionRisks: [] // Empty positions
      };

      const riskScore = monitor.calculateRiskScore(riskData);
      expect(riskScore).toBe(0);
    });

    it('should handle negative risk values gracefully', () => {
      const riskData: RealTimeRiskData = {
        userId: 'test-user',
        timestamp: new Date(),
        accountBalance: new Decimal(10000),
        totalExposure: new Decimal(1000),
        availableMargin: new Decimal(9000),
        marginUtilization: 10,
        dailyPnL: new Decimal(500), // Positive P&L
        weeklyPnL: new Decimal(1000),
        portfolioVaR: new Decimal(200),
        maxDrawdown: new Decimal(0),
        riskScore: 0,
        activeAlerts: [],
        positionRisks: []
      };

      const riskScore = monitor.calculateRiskScore(riskData);
      expect(riskScore).toBeGreaterThanOrEqual(0);
      expect(riskScore).toBeLessThanOrEqual(100);
    });
  });

  describe('Performance and Memory', () => {
    it('should handle large numbers of positions efficiently', () => {
      const manyPositions: PositionRiskData[] = [];
      for (let i = 0; i < 100; i++) {
        manyPositions.push({
          symbol: `PAIR${i}`,
          size: new Decimal(100 + i),
          marketValue: new Decimal(1100 + i * 10),
          unrealizedPnL: new Decimal(i - 50),
          dailyVolatility: 15 + (i % 20),
          beta: 0.8 + (i % 5) * 0.1,
          contribution: 0.01,
          stopLossDistance: new Decimal(50 + i),
          timeInPosition: i,
          riskRating: i % 4 === 0 ? 'high' : 'medium'
        });
      }

      const largeRiskData: RealTimeRiskData = {
        userId: 'test-user',
        timestamp: new Date(),
        accountBalance: new Decimal(100000),
        totalExposure: new Decimal(50000),
        availableMargin: new Decimal(50000),
        marginUtilization: 50,
        dailyPnL: new Decimal(-1000),
        weeklyPnL: new Decimal(-2000),
        portfolioVaR: new Decimal(2500),
        maxDrawdown: new Decimal(1500),
        riskScore: 0,
        activeAlerts: [],
        positionRisks: manyPositions
      };

      const startTime = Date.now();
      const riskScore = monitor.calculateRiskScore(largeRiskData);
      const endTime = Date.now();

      expect(riskScore).toBeGreaterThanOrEqual(0);
      expect(riskScore).toBeLessThanOrEqual(100);
      expect(endTime - startTime).toBeLessThan(100); // Should complete in <100ms
    });
  });
});