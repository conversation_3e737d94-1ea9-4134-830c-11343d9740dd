name: Comprehensive Testing Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run comprehensive tests daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # Job 1: Code Quality and Linting
  code-quality:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        npm run build --workspaces --if-present

    - name: Run ESLint
      run: npm run lint

    - name: Run Prettier check
      run: npm run format:check

    - name: TypeScript type checking
      run: npm run type-check

    - name: Upload lint results
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: lint-results
        path: |
          eslint-results.json
          prettier-results.json

  # Job 2: Unit Tests
  unit-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    strategy:
      matrix:
        workspace: ['api', 'mt5-bridge', 'web']
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run unit tests for ${{ matrix.workspace }}
      run: npm run test --workspace=apps/${{ matrix.workspace }}
      env:
        NODE_ENV: test

    - name: Upload unit test coverage
      uses: actions/upload-artifact@v4
      with:
        name: coverage-${{ matrix.workspace }}
        path: apps/${{ matrix.workspace }}/coverage/

    - name: Comment PR with coverage
      if: github.event_name == 'pull_request'
      uses: romeovs/lcov-reporter-action@v0.3.1
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        lcov-file: apps/${{ matrix.workspace }}/coverage/lcov.info
        delete-old-comments: true

  # Job 3: Integration Tests
  integration-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: golddaddy_test
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5433:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6380:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Setup Python (for MT5 bridge)
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        npm ci
        pip install -r apps/mt5-bridge/python/requirements.txt

    - name: Wait for services
      run: |
        timeout 60 bash -c 'until nc -z localhost 5433; do sleep 1; done'
        timeout 60 bash -c 'until nc -z localhost 6380; do sleep 1; done'

    - name: Run database migrations
      run: |
        export DATABASE_URL="postgresql://test_user:test_password@localhost:5433/golddaddy_test"
        npm run db:migrate --workspace=apps/api

    - name: Run integration tests
      run: npm run test:integration
      env:
        NODE_ENV: test
        DATABASE_URL: postgresql://test_user:test_password@localhost:5433/golddaddy_test
        REDIS_URL: redis://localhost:6380
        TEST_TIMEOUT: 60000

    - name: Upload integration test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: integration-test-results
        path: |
          tests/integration/results/
          playwright-results.json

  # Job 4: E2E Tests
  e2e-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 45
    
    strategy:
      matrix:
        browser: [chromium, firefox, webkit]
        shard: [1/3, 2/3, 3/3]
      fail-fast: false
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install --with-deps ${{ matrix.browser }}

    - name: Start test database
      run: docker-compose -f docker-compose.test.yml up -d

    - name: Wait for test database
      run: timeout 60 bash -c 'until docker-compose -f docker-compose.test.yml exec -T test-db pg_isready -U test_user; do sleep 1; done'

    - name: Build applications
      run: |
        npm run build --workspace=apps/api
        npm run build --workspace=apps/web

    - name: Start applications for E2E tests
      run: |
        npm run start:test --workspace=apps/api &
        npm run start:test --workspace=apps/web &
        sleep 30 # Wait for applications to start

    - name: Run E2E tests
      run: npx playwright test --project=${{ matrix.browser }} --shard=${{ matrix.shard }}
      env:
        E2E_BASE_URL: http://localhost:3000
        API_BASE_URL: http://localhost:3001
        TEST_USER_EMAIL: <EMAIL>
        TEST_USER_PASSWORD: TestPass123!

    - name: Upload E2E test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: e2e-results-${{ matrix.browser }}-${{ strategy.job-index }}
        path: |
          playwright-report/
          test-results/

    - name: Stop test database
      if: always()
      run: docker-compose -f docker-compose.test.yml down

  # Job 5: Performance Tests
  performance-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [unit-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build applications
      run: npm run build

    - name: Start applications
      run: |
        npm run start:prod --workspace=apps/api &
        npm run start:prod --workspace=apps/web &
        sleep 20

    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

    - name: Run API load tests
      run: |
        npm install -g artillery
        artillery run tests/performance/api-load-test.yml --output artillery-report.json

    - name: Upload performance results
      uses: actions/upload-artifact@v4
      with:
        name: performance-results
        path: |
          .lighthouseci/
          artillery-report.json

  # Job 6: Security Tests
  security-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run npm audit
      run: npm audit --audit-level moderate

    - name: Run Snyk security scan
      uses: snyk/actions/node@master
      continue-on-error: true
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
      with:
        args: --severity-threshold=high

    - name: Run CodeQL analysis
      uses: github/codeql-action/analyze@v2
      with:
        languages: javascript

    - name: Upload security scan results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: security-results
        path: |
          snyk-results.json
          codeql-results.sarif

  # Job 7: Build and Package
  build-package:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [code-quality, unit-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Setup Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        npm ci
        pip install -r apps/mt5-bridge/python/requirements.txt

    - name: Build all applications
      run: npm run build

    - name: Build Docker images
      run: |
        docker build -t golddaddy-api:${{ github.sha }} -f docker/Dockerfile.api .
        docker build -t golddaddy-web:${{ github.sha }} .

    - name: Test Docker images
      run: |
        docker run --rm -d --name test-api -p 3001:3001 golddaddy-api:${{ github.sha }}
        sleep 10
        curl -f http://localhost:3001/health || exit 1
        docker stop test-api

    - name: Save Docker images
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      run: |
        docker save golddaddy-api:${{ github.sha }} | gzip > api-image.tar.gz
        docker save golddaddy-web:${{ github.sha }} | gzip > web-image.tar.gz

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      with:
        name: docker-images
        path: |
          api-image.tar.gz
          web-image.tar.gz

  # Job 8: Test Report Generation
  test-report:
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [unit-tests, integration-tests, e2e-tests, performance-tests, security-tests]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Download all test artifacts
      uses: actions/download-artifact@v4
      with:
        path: test-artifacts

    - name: Generate comprehensive test report
      run: |
        npm install -g allure-commandline
        
        # Combine all test results
        mkdir -p allure-results
        
        # Copy unit test results
        find test-artifacts -name "*.xml" -exec cp {} allure-results/ \;
        
        # Copy integration test results
        find test-artifacts -name "integration-*.json" -exec cp {} allure-results/ \;
        
        # Copy E2E test results
        find test-artifacts -name "playwright-results.json" -exec cp {} allure-results/ \;
        
        # Generate report
        allure generate allure-results -o allure-report --clean

    - name: Upload comprehensive test report
      uses: actions/upload-artifact@v4
      with:
        name: comprehensive-test-report
        path: allure-report/

    - name: Deploy test report to GitHub Pages
      if: github.event_name == 'push' && github.ref == 'refs/heads/main'
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: allure-report
        destination_dir: test-reports/${{ github.run_number }}

  # Job 9: Slack Notification
  notify:
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, e2e-tests, performance-tests, security-tests, build-package]
    if: always()
    
    steps:
    - name: Determine workflow status
      id: status
      run: |
        if [[ "${{ needs.unit-tests.result }}" == "success" && 
              "${{ needs.integration-tests.result }}" == "success" && 
              "${{ needs.e2e-tests.result }}" == "success" && 
              "${{ needs.performance-tests.result }}" == "success" && 
              "${{ needs.security-tests.result }}" == "success" && 
              "${{ needs.build-package.result }}" == "success" ]]; then
          echo "status=success" >> $GITHUB_OUTPUT
          echo "color=good" >> $GITHUB_OUTPUT
        else
          echo "status=failure" >> $GITHUB_OUTPUT
          echo "color=danger" >> $GITHUB_OUTPUT
        fi

    - name: Notify Slack
      uses: 8398a7/action-slack@v3
      if: always()
      with:
        status: custom
        custom_payload: |
          {
            "text": "GoldDaddy Test Pipeline ${{ steps.status.outputs.status == 'success' && 'Passed' || 'Failed' }}",
            "color": "${{ steps.status.outputs.color }}",
            "fields": [
              {
                "title": "Repository",
                "value": "${{ github.repository }}",
                "short": true
              },
              {
                "title": "Branch",
                "value": "${{ github.ref_name }}",
                "short": true
              },
              {
                "title": "Commit",
                "value": "${{ github.sha }}",
                "short": true
              },
              {
                "title": "Author",
                "value": "${{ github.actor }}",
                "short": true
              },
              {
                "title": "Unit Tests",
                "value": "${{ needs.unit-tests.result }}",
                "short": true
              },
              {
                "title": "Integration Tests",
                "value": "${{ needs.integration-tests.result }}",
                "short": true
              },
              {
                "title": "E2E Tests",
                "value": "${{ needs.e2e-tests.result }}",
                "short": true
              },
              {
                "title": "Performance Tests",
                "value": "${{ needs.performance-tests.result }}",
                "short": true
              }
            ]
          }
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}