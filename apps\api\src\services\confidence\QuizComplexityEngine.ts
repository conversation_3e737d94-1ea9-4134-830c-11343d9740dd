import {
  ConfidenceStage,
  QuizDifficulty,
  QuizCategory,
  type QuizAttempt,
  type ConfidenceAssessment
} from '@golddaddy/types';

interface ComplexityRecommendation {
  difficulty: QuizDifficulty;
  questionCount: number;
  focusCategories: QuizCategory[];
  timeLimit: number; // seconds
  passingScore: number; // 0-100
  reasoning: string;
}

interface UserPerformanceProfile {
  averageScore: number;
  attemptCount: number;
  consistencyRating: number;
  timeEfficiency: number;
  weakCategories: QuizCategory[];
  strongCategories: QuizCategory[];
  improvementTrend: number; // -1 to 1
}

export class QuizComplexityEngine {
  private readonly STAGE_BASE_CONFIG = {
    [ConfidenceStage.GOAL_SETTING]: {
      baseQuestionCount: 15,
      baseTimeLimit: 900, // 15 minutes
      basePassingScore: 70,
      allowedDifficulties: [QuizDifficulty.BEGINNER]
    },
    [ConfidenceStage.STRATEGY_LEARNING]: {
      baseQuestionCount: 20,
      baseTimeLimit: 1200, // 20 minutes
      basePassingScore: 75,
      allowedDifficulties: [QuizDifficulty.BEGINNER, QuizDifficulty.INTERMEDIATE]
    },
    [ConfidenceStage.BACKTESTING_REVIEW]: {
      baseQuestionCount: 25,
      baseTimeLimit: 1500, // 25 minutes
      basePassingScore: 80,
      allowedDifficulties: [QuizDifficulty.INTERMEDIATE]
    },
    [ConfidenceStage.PAPER_TRADING]: {
      baseQuestionCount: 30,
      baseTimeLimit: 1800, // 30 minutes
      basePassingScore: 85,
      allowedDifficulties: [QuizDifficulty.INTERMEDIATE, QuizDifficulty.ADVANCED]
    },
    [ConfidenceStage.LIVE_READY]: {
      baseQuestionCount: 35,
      baseTimeLimit: 2100, // 35 minutes
      basePassingScore: 90,
      allowedDifficulties: [QuizDifficulty.ADVANCED, QuizDifficulty.EXPERT]
    }
  };

  private readonly CATEGORY_STAGE_WEIGHTING = {
    [ConfidenceStage.GOAL_SETTING]: {
      [QuizCategory.TRADING_FUNDAMENTALS]: 0.40,
      [QuizCategory.RISK_MANAGEMENT]: 0.35,
      [QuizCategory.PLATFORM_FEATURES]: 0.15,
      [QuizCategory.SAFETY_PROCEDURES]: 0.10,
      [QuizCategory.REGULATORY_COMPLIANCE]: 0.00,
      [QuizCategory.MARKET_ANALYSIS]: 0.00,
      [QuizCategory.PSYCHOLOGY_DISCIPLINE]: 0.00
    },
    [ConfidenceStage.STRATEGY_LEARNING]: {
      [QuizCategory.TRADING_FUNDAMENTALS]: 0.30,
      [QuizCategory.RISK_MANAGEMENT]: 0.25,
      [QuizCategory.PLATFORM_FEATURES]: 0.20,
      [QuizCategory.SAFETY_PROCEDURES]: 0.10,
      [QuizCategory.MARKET_ANALYSIS]: 0.10,
      [QuizCategory.REGULATORY_COMPLIANCE]: 0.03,
      [QuizCategory.PSYCHOLOGY_DISCIPLINE]: 0.02
    },
    [ConfidenceStage.BACKTESTING_REVIEW]: {
      [QuizCategory.MARKET_ANALYSIS]: 0.30,
      [QuizCategory.RISK_MANAGEMENT]: 0.25,
      [QuizCategory.TRADING_FUNDAMENTALS]: 0.20,
      [QuizCategory.PSYCHOLOGY_DISCIPLINE]: 0.15,
      [QuizCategory.PLATFORM_FEATURES]: 0.05,
      [QuizCategory.SAFETY_PROCEDURES]: 0.03,
      [QuizCategory.REGULATORY_COMPLIANCE]: 0.02
    },
    [ConfidenceStage.PAPER_TRADING]: {
      [QuizCategory.RISK_MANAGEMENT]: 0.35,
      [QuizCategory.PSYCHOLOGY_DISCIPLINE]: 0.25,
      [QuizCategory.SAFETY_PROCEDURES]: 0.20,
      [QuizCategory.MARKET_ANALYSIS]: 0.10,
      [QuizCategory.TRADING_FUNDAMENTALS]: 0.05,
      [QuizCategory.PLATFORM_FEATURES]: 0.03,
      [QuizCategory.REGULATORY_COMPLIANCE]: 0.02
    },
    [ConfidenceStage.LIVE_READY]: {
      [QuizCategory.RISK_MANAGEMENT]: 0.30,
      [QuizCategory.SAFETY_PROCEDURES]: 0.25,
      [QuizCategory.REGULATORY_COMPLIANCE]: 0.20,
      [QuizCategory.PSYCHOLOGY_DISCIPLINE]: 0.15,
      [QuizCategory.MARKET_ANALYSIS]: 0.05,
      [QuizCategory.TRADING_FUNDAMENTALS]: 0.03,
      [QuizCategory.PLATFORM_FEATURES]: 0.02
    }
  };

  calculateComplexityRecommendation(
    stage: ConfidenceStage,
    userProfile: UserPerformanceProfile,
    _assessment: ConfidenceAssessment
  ): ComplexityRecommendation {
    const baseConfig = this.STAGE_BASE_CONFIG[stage];
    let difficulty = baseConfig.allowedDifficulties[0];
    let questionCount = baseConfig.baseQuestionCount;
    let timeLimit = baseConfig.baseTimeLimit;
    const passingScore = baseConfig.basePassingScore;

    // Adjust difficulty based on performance
    if (userProfile.averageScore > 85 && userProfile.consistencyRating > 0.8) {
      const maxDifficultyIndex = baseConfig.allowedDifficulties.length - 1;
      const currentIndex = baseConfig.allowedDifficulties.indexOf(difficulty);
      if (currentIndex < maxDifficultyIndex) {
        difficulty = baseConfig.allowedDifficulties[currentIndex + 1];
      }
    } else if (userProfile.averageScore < 60 || userProfile.consistencyRating < 0.5) {
      difficulty = QuizDifficulty.BEGINNER;
    }

    // Adjust question count based on performance trends
    if (userProfile.improvementTrend > 0.5 && userProfile.timeEfficiency > 0.8) {
      questionCount = Math.ceil(questionCount * 1.2); // 20% more questions
    } else if (userProfile.improvementTrend < -0.3) {
      questionCount = Math.ceil(questionCount * 0.8); // 20% fewer questions
    }

    // Adjust time limit based on user efficiency
    if (userProfile.timeEfficiency < 0.6) {
      timeLimit = Math.ceil(timeLimit * 1.3); // 30% more time
    } else if (userProfile.timeEfficiency > 0.9) {
      timeLimit = Math.ceil(timeLimit * 0.9); // 10% less time
    }

    // Focus on weak categories
    const stageWeighting = this.CATEGORY_STAGE_WEIGHTING[stage];
    const focusCategories = this.calculateFocusCategories(
      stageWeighting,
      userProfile.weakCategories,
      userProfile.strongCategories
    );

    const reasoning = this.generateRecommendationReasoning(
      stage,
      userProfile,
      difficulty,
      questionCount,
      focusCategories
    );

    return {
      difficulty,
      questionCount,
      focusCategories,
      timeLimit,
      passingScore,
      reasoning
    };
  }

  private calculateFocusCategories(
    stageWeighting: Record<QuizCategory, number>,
    weakCategories: QuizCategory[],
    strongCategories: QuizCategory[]
  ): QuizCategory[] {
    // Boost weak category weights
    const adjustedWeighting = { ...stageWeighting };
    
    for (const weakCategory of weakCategories) {
      if (adjustedWeighting[weakCategory] > 0) {
        adjustedWeighting[weakCategory] *= 1.5; // 50% boost for weak areas
      }
    }

    // Slightly reduce strong category weights
    for (const strongCategory of strongCategories) {
      if (adjustedWeighting[strongCategory] > 0) {
        adjustedWeighting[strongCategory] *= 0.8; // 20% reduction for strong areas
      }
    }

    // Return categories sorted by adjusted weight, take top categories
    return Object.entries(adjustedWeighting)
      .filter(([_, weight]) => weight > 0)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 4) // Top 4 categories
      .map(([category]) => category as QuizCategory);
  }

  private generateRecommendationReasoning(
    stage: ConfidenceStage,
    userProfile: UserPerformanceProfile,
    difficulty: QuizDifficulty,
    questionCount: number,
    _focusCategories: QuizCategory[]
  ): string {
    const reasons: string[] = [];

    // Difficulty reasoning
    if (userProfile.averageScore > 85) {
      reasons.push(`Increased difficulty to ${difficulty} based on strong performance (${userProfile.averageScore}% average)`);
    } else if (userProfile.averageScore < 60) {
      reasons.push(`Using ${difficulty} difficulty to build foundational knowledge (${userProfile.averageScore}% average)`);
    }

    // Question count reasoning  
    const baseQuestionCount = this.STAGE_BASE_CONFIG[stage]?.baseQuestionCount || 20;
    if (questionCount > baseQuestionCount) {
      reasons.push(`Extended question set (${questionCount}) due to strong performance trend`);
    } else if (questionCount < baseQuestionCount) {
      reasons.push(`Focused question set (${questionCount}) to target specific improvement areas`);
    }

    // Focus area reasoning
    if (userProfile.weakCategories.length > 0) {
      const weakCategoryNames = userProfile.weakCategories.map(cat => cat.replace('_', ' '));
      reasons.push(`Emphasizing ${weakCategoryNames.join(', ')} based on previous performance gaps`);
    }

    return reasons.join('. ') || 'Standard quiz configuration for current stage.';
  }

  createUserPerformanceProfile(attempts: QuizAttempt[]): UserPerformanceProfile {
    if (attempts.length === 0) {
      return {
        averageScore: 0,
        attemptCount: 0,
        consistencyRating: 0,
        timeEfficiency: 0.5,
        weakCategories: [],
        strongCategories: [],
        improvementTrend: 0
      };
    }

    const scores = attempts.map(a => a.score);
    const averageScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    
    // Calculate consistency (1 - coefficient of variation)
    const scoreVariance = scores.reduce((sum, score) => sum + Math.pow(score - averageScore, 2), 0) / scores.length;
    const scoreStdDev = Math.sqrt(scoreVariance);
    const consistencyRating = Math.max(0, 1 - (scoreStdDev / averageScore));

    // Calculate time efficiency (compared to average completion time)
    const averageTime = attempts.reduce((sum, a) => sum + a.totalTimeSpent, 0) / attempts.length;
    const expectedTime = 1800; // 30 minutes baseline
    const timeEfficiency = Math.min(1, expectedTime / averageTime);

    // Identify weak and strong categories based on attempt data
    const weakCategoryFrequency = new Map<string, number>();
    const strongCategoryFrequency = new Map<string, number>();

    for (const attempt of attempts) {
      // Count frequency of weak areas
      for (const weakArea of attempt.weakAreas) {
        weakCategoryFrequency.set(weakArea, (weakCategoryFrequency.get(weakArea) || 0) + 1);
      }
      
      // Count frequency of strong areas
      for (const strongArea of attempt.strongAreas) {
        strongCategoryFrequency.set(strongArea, (strongCategoryFrequency.get(strongArea) || 0) + 1);
      }
    }

    // Convert to categories that appear in multiple attempts
    const weakCategories: QuizCategory[] = [];
    const strongCategories: QuizCategory[] = [];

    for (const [area, frequency] of weakCategoryFrequency) {
      if (frequency >= Math.ceil(attempts.length * 0.5) && Object.values(QuizCategory).includes(area as QuizCategory)) {
        weakCategories.push(area as QuizCategory);
      }
    }

    for (const [area, frequency] of strongCategoryFrequency) {
      if (frequency >= Math.ceil(attempts.length * 0.5) && Object.values(QuizCategory).includes(area as QuizCategory)) {
        strongCategories.push(area as QuizCategory);
      }
    }

    // Calculate improvement trend
    let improvementTrend = 0;
    if (attempts.length >= 3) {
      const recentScores = scores.slice(-Math.floor(scores.length / 2));
      const earlierScores = scores.slice(0, Math.floor(scores.length / 2));
      if (earlierScores.length > 0) {
        const recentAvg = recentScores.reduce((sum, s) => sum + s, 0) / recentScores.length;
        const earlierAvg = earlierScores.reduce((sum, s) => sum + s, 0) / earlierScores.length;
        improvementTrend = (recentAvg - earlierAvg) / 100; // Normalize to -1 to 1
      }
    }

    return {
      averageScore,
      attemptCount: attempts.length,
      consistencyRating,
      timeEfficiency,
      weakCategories,
      strongCategories,
      improvementTrend
    };
  }
}