import { defineConfig } from 'vitest/config'
import path from 'path'

export default defineConfig({
  test: {
    environment: 'node',
    setupFiles: ['./src/test/integration-setup.ts'],
    globals: true,
    testTimeout: 30000, // Longer timeout for integration tests
    include: ['src/**/*.integration.test.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'html', 'lcov'],
      include: ['src/**/*.ts'],
      exclude: [
        'src/**/*.test.ts',
        'src/**/*.spec.ts',
        'src/test/**',
        'src/**/*.d.ts',
      ],
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
      '@golddaddy/types': path.resolve(__dirname, '../../packages/types/src'),
      '@golddaddy/config': path.resolve(__dirname, '../../packages/config/src'),
    },
  },
})