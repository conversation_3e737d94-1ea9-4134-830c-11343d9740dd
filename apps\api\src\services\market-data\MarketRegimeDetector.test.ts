/**
 * Tests for MarketRegimeDetector
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { MarketRegime, DataSource, TimeFrame } from '@golddaddy/types';
import Decimal from 'decimal.js';

// Mock RegimeClassificationEngine at the module level BEFORE any imports
const mockClassificationEngine = {
  classifyMarketRegime: vi.fn(),
  updateConfig: vi.fn(),
  removeAllListeners: vi.fn(),
  on: vi.fn(),
  emit: vi.fn(),
  off: vi.fn(),
  once: vi.fn(),
  addListener: vi.fn(),
  removeListener: vi.fn(),
  setMaxListeners: vi.fn(),
  getMaxListeners: vi.fn(),
  listeners: vi.fn(),
  rawListeners: vi.fn(),
  listenerCount: vi.fn(),
  prependListener: vi.fn(),
  prependOnceListener: vi.fn(),
  eventNames: vi.fn(),
};

// Mock with explicit factory that returns the same instance
vi.mock('./RegimeClassificationEngine', () => {
  const MockedRegimeClassificationEngine = vi.fn().mockImplementation(() => mockClassificationEngine);
  return {
    RegimeClassificationEngine: MockedRegimeClassificationEngine,
  };
});

// Import after mock is set up
import { MarketRegimeDetector } from './MarketRegimeDetector';

describe('MarketRegimeDetector', () => {
  let detector: MarketRegimeDetector;

  const mockMarketData = {
    instrument: 'EUR/USD',
    timeframe: '1H' as TimeFrame,
    timestamp: new Date('2024-01-01T12:00:00Z'),
    open: new Decimal('1.1000'),
    high: new Decimal('1.1050'),
    low: new Decimal('1.0950'),
    close: new Decimal('1.1025'),
    volume: new Decimal('1000000'),
    source: DataSource.MT5,
  };

  const mockDetectionResult = {
    id: 'test_result_1',
    instrument: 'EUR/USD',
    timeframe: '1H' as TimeFrame,
    timestamp: new Date('2024-01-01T12:00:00Z'),
    regime: MarketRegime.TRENDING_UP,
    confidence: 85,
    confidenceLevel: 'high' as const,
    trendStrength: 0.75,
    volatilityLevel: 0.35,
    momentumScore: 0.45,
    supportingMetrics: {
      smaSlope20: 0.0012,
      smaSlope50: 0.0008,
      rsi14: 67.5,
      atr14: 0.0025,
      bollingerBandwidth: 0.0035,
      pricePosition: 0.78,
    },
    regimeChangeDetected: false,
    processingTimeMs: 150,
    source: DataSource.MT5,
    algorithmVersion: '1.0.0',
  };

  beforeEach(() => {
    // Clear all mocks
    vi.clearAllMocks();
    
    // Set up default mock behavior
    mockClassificationEngine.classifyMarketRegime.mockResolvedValue(mockDetectionResult);
    
    // Create detector instance
    detector = new MarketRegimeDetector();
    
    // HACK: Replace the actual classification engine with our mock
    // This is a workaround since Vitest mocking is not working properly
    (detector as any).classificationEngine = mockClassificationEngine;
  });

  afterEach(() => {
    detector.shutdown();
  });

  describe('detectRegime', () => {
    it('should detect regime successfully', async () => {
      const result = await detector.detectRegime(mockMarketData);
      
      expect(result).toEqual(mockDetectionResult);
      expect(mockClassificationEngine.classifyMarketRegime).toHaveBeenCalledWith(
        expect.objectContaining({
          instrument: 'EUR/USD',
          timeframe: '1H',
          timestamp: mockMarketData.timestamp,
        }),
        undefined // no previous regime initially
      );
    });

    it('should handle classification engine errors gracefully', async () => {
      const error = new Error('Classification failed');
      mockClassificationEngine.classifyMarketRegime.mockRejectedValue(error);

      const result = await detector.detectRegime(mockMarketData);

      expect(result.regime).toBe(MarketRegime.UNKNOWN);
      expect(result.confidence).toBe(0);
      expect(result.instrument).toBe(mockMarketData.instrument);
    });

    it('should emit regime_detection_complete event on success', async () => {
      const eventSpy = vi.fn();
      detector.on('regime_detection_complete', eventSpy);

      await detector.detectRegime(mockMarketData);

      expect(eventSpy).toHaveBeenCalledWith(mockDetectionResult);
    });

    it('should emit detection_error event on failure', async () => {
      const error = new Error('Classification failed');
      mockClassificationEngine.classifyMarketRegime.mockRejectedValue(error);
      
      const eventSpy = vi.fn();
      detector.on('detection_error', eventSpy);

      await detector.detectRegime(mockMarketData);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          instrument: mockMarketData.instrument,
          timeframe: mockMarketData.timeframe,
          error: 'Classification failed',
        })
      );
    });

    it('should cache detection results', async () => {
      // First call
      const result1 = await detector.detectRegime(mockMarketData);
      expect(mockClassificationEngine.classifyMarketRegime).toHaveBeenCalledTimes(1);

      // Second call with same data should use cache
      const result2 = await detector.detectRegime(mockMarketData);
      expect(mockClassificationEngine.classifyMarketRegime).toHaveBeenCalledTimes(1); // Still 1
      expect(result1).toEqual(result2);
    });

    it('should detect regime changes', async () => {
      // First detection
      await detector.detectRegime(mockMarketData);

      // Second detection with regime change
      const changedResult = {
        ...mockDetectionResult,
        regime: MarketRegime.SIDEWAYS,
        regimeChangeDetected: true,
        previousRegime: MarketRegime.TRENDING_UP,
      };
      
      mockClassificationEngine.classifyMarketRegime.mockResolvedValue(changedResult);
      
      const eventSpy = vi.fn();
      detector.on('regime_change_detected', eventSpy);

      const differentMarketData = {
        ...mockMarketData,
        timestamp: new Date('2024-01-01T13:00:00Z'),
      };

      await detector.detectRegime(differentMarketData);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          fromRegime: MarketRegime.TRENDING_UP,
          toRegime: MarketRegime.SIDEWAYS,
        })
      );
    });

    it('should update historical data', async () => {
      // Perform several detections
      for (let i = 0; i < 5; i++) {
        const data = {
          ...mockMarketData,
          timestamp: new Date(`2024-01-01T${12 + i}:00:00Z`),
        };
        await detector.detectRegime(data);
      }

      // Historical data should be maintained (private, but we can test through behavior)
      // The cache hit test above indirectly confirms this works
      expect(mockClassificationEngine.classifyMarketRegime).toHaveBeenCalledTimes(5);
    });
  });

  describe('detectRegimesBatch', () => {
    it('should process multiple market data items', async () => {
      const marketDataList = [
        mockMarketData,
        { ...mockMarketData, instrument: 'GBP/USD', timestamp: new Date('2024-01-01T13:00:00Z') },
        { ...mockMarketData, instrument: 'USD/JPY', timestamp: new Date('2024-01-01T14:00:00Z') },
      ];

      const results = await detector.detectRegimesBatch(marketDataList);

      expect(results).toHaveLength(3);
      expect(mockClassificationEngine.classifyMarketRegime).toHaveBeenCalledTimes(3);
    });

    it('should emit batch_detection_complete event', async () => {
      const eventSpy = vi.fn();
      detector.on('batch_detection_complete', eventSpy);

      const marketDataList = [mockMarketData];
      await detector.detectRegimesBatch(marketDataList);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          totalProcessed: 1,
          successCount: 1,
          errorCount: 0,
        })
      );
    });

    it('should handle batch processing with some failures', async () => {
      const marketDataList = [
        mockMarketData,
        { ...mockMarketData, instrument: 'GBP/USD' },
      ];

      // Make second call fail
      mockClassificationEngine.classifyMarketRegime
        .mockResolvedValueOnce(mockDetectionResult)
        .mockRejectedValueOnce(new Error('Failed'));

      const results = await detector.detectRegimesBatch(marketDataList);

      expect(results).toHaveLength(2);
      expect(results[0].regime).toBe(MarketRegime.TRENDING_UP);
      expect(results[1].regime).toBe(MarketRegime.UNKNOWN); // Error case
    });
  });

  describe('forceDetectRegime', () => {
    it('should bypass cache and force new detection', async () => {
      // First call
      await detector.detectRegime(mockMarketData);
      expect(mockClassificationEngine.classifyMarketRegime).toHaveBeenCalledTimes(1);

      // Force detection should bypass cache
      await detector.forceDetectRegime(mockMarketData);
      expect(mockClassificationEngine.classifyMarketRegime).toHaveBeenCalledTimes(2);
    });
  });

  describe('getHistoricalRegimeData', () => {
    it('should return historical data for specified time range', () => {
      const startDate = new Date('2024-01-01T00:00:00Z');
      const endDate = new Date('2024-01-01T23:59:59Z');

      const historicalData = detector.getHistoricalRegimeData(
        'EUR/USD',
        '1H' as TimeFrame,
        startDate,
        endDate
      );

      expect(Array.isArray(historicalData)).toBe(true);
      // Since we haven't cached any results that match the time range, should be empty
      expect(historicalData).toHaveLength(0);
    });
  });

  describe('getStats', () => {
    it('should return detection statistics', async () => {
      // Perform some detections to generate stats
      await detector.detectRegime(mockMarketData);
      
      const stats = detector.getStats();

      expect(stats).toHaveProperty('totalDetections');
      expect(stats).toHaveProperty('cacheHits');
      expect(stats).toHaveProperty('averageProcessingTime');
      expect(stats).toHaveProperty('regimeChanges');
      expect(stats).toHaveProperty('cacheSize');
      expect(stats).toHaveProperty('historicalDataSets');
      expect(stats).toHaveProperty('cacheHitRate');

      expect(stats.totalDetections).toBeGreaterThan(0);
    });
  });

  describe('clearCache', () => {
    it('should clear all cached data and reset statistics', async () => {
      // Generate some cached data
      await detector.detectRegime(mockMarketData);
      
      let stats = detector.getStats();
      expect(stats.totalDetections).toBeGreaterThan(0);

      // Clear cache
      const eventSpy = vi.fn();
      detector.on('cache_cleared', eventSpy);
      
      detector.clearCache();

      // Verify cache is cleared
      stats = detector.getStats();
      expect(stats.totalDetections).toBe(0);
      expect(stats.cacheSize).toBe(0);
      expect(eventSpy).toHaveBeenCalled();
    });
  });

  describe('updateConfig', () => {
    it('should update configuration and emit event', () => {
      const eventSpy = vi.fn();
      detector.on('config_updated', eventSpy);

      const newConfig = {
        confidenceThreshold: 0.8,
        trendStrengthThreshold: 0.7,
      };

      detector.updateConfig(newConfig);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining(newConfig)
      );

      const config = detector.getConfig();
      expect(config.confidenceThreshold).toBe(0.8);
      expect(config.trendStrengthThreshold).toBe(0.7);
    });
  });

  describe('technical indicator calculations', () => {
    it('should calculate SMA correctly', () => {
      const prices = [
        { close: new Decimal('1.1000') },
        { close: new Decimal('1.1010') },
        { close: new Decimal('1.1020') },
        { close: new Decimal('1.1030') },
        { close: new Decimal('1.1040') },
      ];

      // Access private method for testing
      const sma = (detector as any).calculateSMA(prices, 5);
      
      expect(sma).toBeDefined();
      expect(sma.toNumber()).toBeCloseTo(1.1020, 4);
    });

    it('should return undefined for insufficient data', () => {
      const prices = [
        { close: new Decimal('1.1000') },
        { close: new Decimal('1.1010') },
      ];

      const sma = (detector as any).calculateSMA(prices, 5);
      expect(sma).toBeUndefined();
    });

    it('should calculate RSI correctly', () => {
      const prices = [];
      // Generate test prices with clear trend
      for (let i = 0; i < 20; i++) {
        prices.push({
          close: new Decimal(1.1000 + (i * 0.0010)), // Upward trend
        });
      }

      const rsi = (detector as any).calculateRSI(prices, 14);
      
      expect(rsi).toBeDefined();
      expect(rsi.toNumber()).toBeGreaterThan(50); // Should be > 50 for upward trend
    });

    it('should calculate ATR correctly', () => {
      const data = [];
      for (let i = 0; i < 20; i++) {
        data.push({
          timestamp: new Date(`2024-01-01T${i}:00:00Z`),
          prices: {
            high: new Decimal(1.1000 + (i * 0.0005)),
            low: new Decimal(1.0950 + (i * 0.0005)),
            close: new Decimal(1.0975 + (i * 0.0005)),
          },
        });
      }

      const atr = (detector as any).calculateATR(data, 14);
      
      expect(atr).toBeDefined();
      expect(atr.toNumber()).toBeGreaterThan(0);
    });
  });

  describe('event emission', () => {
    it('should emit cache_hit event when using cached data', async () => {
      const eventSpy = vi.fn();
      detector.on('cache_hit', eventSpy);

      // First call to populate cache
      await detector.detectRegime(mockMarketData);

      // Second call should hit cache
      await detector.detectRegime(mockMarketData);

      expect(eventSpy).toHaveBeenCalledWith({
        instrument: mockMarketData.instrument,
        timeframe: mockMarketData.timeframe,
      });
    });

    it('should emit cache_cleaned event during periodic cleanup', (done) => {
      const eventSpy = vi.fn(() => {
        expect(eventSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            expiredEntries: expect.any(Number),
          })
        );
        done();
      });

      detector.on('cache_cleaned', eventSpy);

      // Manually trigger cache cleanup
      (detector as any).cleanCache();

      // If no expired entries, event won't be emitted, so we set a timeout
      setTimeout(() => {
        if (!eventSpy.mock.calls.length) {
          done(); // No expired entries is also valid
        }
      }, 100);
    });
  });

  describe('configuration management', () => {
    it('should use default configuration', () => {
      const config = detector.getConfig();
      
      expect(config).toHaveProperty('trendDetectionWindow');
      expect(config).toHaveProperty('volatilityWindow');
      expect(config).toHaveProperty('confidenceThreshold');
      expect(config).toHaveProperty('minimumRegimeDuration');
      expect(config.confidenceThreshold).toBe(0.5);
    });

    it('should accept custom configuration', () => {
      const customConfig = {
        confidenceThreshold: 0.8,
        volatilityThreshold: 0.05,
      };

      const customDetector = new MarketRegimeDetector(customConfig);
      const config = customDetector.getConfig();

      expect(config.confidenceThreshold).toBe(0.8);
      expect(config.volatilityThreshold).toBe(0.05);

      customDetector.shutdown();
    });
  });

  describe('error handling', () => {
    it('should handle malformed market data gracefully', async () => {
      const malformedData = {
        ...mockMarketData,
        // @ts-ignore - intentionally malformed for testing
        close: 'invalid',
      };

      // Mock should return error result for malformed data
      const errorResult = {
        ...mockDetectionResult,
        regime: MarketRegime.UNKNOWN,
        confidence: 0,
        confidenceLevel: 'very_low' as const,
      };
      mockClassificationEngine.classifyMarketRegime.mockResolvedValueOnce(errorResult);

      const result = await detector.detectRegime(malformedData as any);
      expect(result.regime).toBe(MarketRegime.UNKNOWN);
    });

    it('should handle missing instrument data', async () => {
      const incompleteData = {
        ...mockMarketData,
        instrument: '',
      };

      // Mock should return error result for missing instrument data
      const errorResult = {
        ...mockDetectionResult,
        regime: MarketRegime.UNKNOWN,
        confidence: 0,
        confidenceLevel: 'very_low' as const,
      };
      mockClassificationEngine.classifyMarketRegime.mockResolvedValueOnce(errorResult);

      const result = await detector.detectRegime(incompleteData);
      expect(result.regime).toBe(MarketRegime.UNKNOWN);
    });
  });
});

// Integration test with real classification engine
describe('MarketRegimeDetector Integration', () => {
  let detector: MarketRegimeDetector;

  beforeEach(() => {
    // Use real classification engine
    vi.unmock('./RegimeClassificationEngine');
    detector = new MarketRegimeDetector();
  });

  afterEach(() => {
    detector.shutdown();
  });

  it('should work end-to-end with real classification engine', async () => {
    const marketData = {
      instrument: 'EUR/USD',
      timeframe: '1H' as TimeFrame,
      timestamp: new Date(),
      open: new Decimal('1.1000'),
      high: new Decimal('1.1050'),
      low: new Decimal('1.0950'),
      close: new Decimal('1.1025'),
      volume: new Decimal('1000000'),
      source: DataSource.MT5,
    };

    const result = await detector.detectRegime(marketData);

    expect(result).toBeDefined();
    expect(result.instrument).toBe('EUR/USD');
    expect(result.timeframe).toBe('1H');
    expect(Object.values(MarketRegime)).toContain(result.regime);
    expect(result.confidence).toBeGreaterThanOrEqual(0);
    expect(result.confidence).toBeLessThanOrEqual(100);
    expect(result.processingTimeMs).toBeGreaterThanOrEqual(0);
  });
});