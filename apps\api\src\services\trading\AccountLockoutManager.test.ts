/**
 * AccountLockoutManager Service Tests
 * 
 * Comprehensive test suite for account lockout functionality.
 * Tests lockout mechanisms, notifications, and unlock procedures.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { 
  AccountLockoutManager, 
  type LockoutConfig, 
  type LockoutStatus, 
  type NotificationRequest,
  type UnlockRequest 
} from './AccountLockoutManager';

describe('AccountLockoutManager', () => {
  let lockoutManager: AccountLockoutManager;
  const testUserId = 'test-user-123';
  
  const defaultConfig: Partial<LockoutConfig> = {
    enableDailyLockouts: true,
    enableWeeklyLockouts: true,
    dailyCooldownHours: 24,
    weeklyCooldownHours: 168,
    enableEmailNotifications: true,
    enablePushNotifications: true,
    maxConsecutiveLockouts: 3,
    escalatedCooldownHours: 336
  };

  beforeEach(() => {
    lockoutManager = new AccountLockoutManager(defaultConfig);
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    lockoutManager.destroy();
    vi.useRealTimers();
  });

  describe('User Initialization', () => {
    it('should initialize user with correct default status', () => {
      lockoutManager.initializeUser(testUserId);
      
      const status = lockoutManager.getLockoutStatus(testUserId);
      expect(status).toBeDefined();
      expect(status!.userId).toBe(testUserId);
      expect(status!.isLocked).toBe(false);
      expect(status!.lockoutType).toBeNull();
      expect(status!.consecutiveLockouts).toBe(0);
      expect(status!.canRequestReview).toBe(false);
      expect(status!.notificationsSent).toEqual([]);
    });

    it('should not reinitialize existing user', () => {
      lockoutManager.initializeUser(testUserId);
      
      // Trigger a lockout
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      // Try to reinitialize
      lockoutManager.initializeUser(testUserId);
      
      const status = lockoutManager.getLockoutStatus(testUserId);
      expect(status!.isLocked).toBe(true); // Should remain locked
    });
  });

  describe('Account Locking', () => {
    beforeEach(() => {
      lockoutManager.initializeUser(testUserId);
    });

    it('should lock account for daily limit violation', () => {
      const reason = 'Daily loss limit exceeded';
      
      const status = lockoutManager.lockAccount(testUserId, 'daily_limit', reason);
      
      expect(status.isLocked).toBe(true);
      expect(status.lockoutType).toBe('daily_limit');
      expect(status.reason).toBe(reason);
      expect(status.consecutiveLockouts).toBe(1);
      expect(status.lockedAt).toBeDefined();
      expect(status.lockoutUntil).toBeDefined();
      expect(status.canRequestReview).toBe(false);
    });

    it('should lock account for weekly limit violation', () => {
      const reason = 'Weekly loss limit exceeded';
      
      const status = lockoutManager.lockAccount(testUserId, 'weekly_limit', reason);
      
      expect(status.isLocked).toBe(true);
      expect(status.lockoutType).toBe('weekly_limit');
      expect(status.reason).toBe(reason);
      expect(status.consecutiveLockouts).toBe(1);
    });

    it('should calculate correct lockout duration for daily limit', () => {
      const now = new Date();
      vi.setSystemTime(now);
      
      const status = lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test');
      
      const expectedDuration = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      const actualDuration = status.lockoutUntil!.getTime() - now.getTime();
      
      expect(actualDuration).toBe(expectedDuration);
    });

    it('should calculate correct lockout duration for weekly limit', () => {
      const now = new Date();
      vi.setSystemTime(now);
      
      const status = lockoutManager.lockAccount(testUserId, 'weekly_limit', 'Test');
      
      const expectedDuration = 168 * 60 * 60 * 1000; // 168 hours in milliseconds
      const actualDuration = status.lockoutUntil!.getTime() - now.getTime();
      
      expect(actualDuration).toBe(expectedDuration);
    });

    it('should increment consecutive lockout counter', () => {
      // First lockout
      let status = lockoutManager.lockAccount(testUserId, 'daily_limit', 'First lockout');
      expect(status.consecutiveLockouts).toBe(1);
      
      // Unlock and lock again
      lockoutManager.unlockAccount(testUserId, 'admin', 'Test unlock');
      status = lockoutManager.lockAccount(testUserId, 'daily_limit', 'Second lockout');
      expect(status.consecutiveLockouts).toBe(2);
    });

    it('should trigger consecutive violation lockout after max attempts', () => {
      // Trigger multiple lockouts
      for (let i = 0; i < 3; i++) {
        lockoutManager.lockAccount(testUserId, 'daily_limit', `Lockout ${i + 1}`);
        lockoutManager.unlockAccount(testUserId, 'admin', 'Reset for test');
      }
      
      // Fourth lockout should be consecutive violation
      const status = lockoutManager.lockAccount(testUserId, 'daily_limit', 'Final lockout');
      
      expect(status.lockoutType).toBe('consecutive_violation');
      expect(status.canRequestReview).toBe(true);
    });

    it('should use escalated cooldown for consecutive violations', () => {
      const now = new Date();
      vi.setSystemTime(now);
      
      // Trigger consecutive violation
      for (let i = 0; i < 3; i++) {
        lockoutManager.lockAccount(testUserId, 'daily_limit', `Lockout ${i + 1}`);
        lockoutManager.unlockAccount(testUserId, 'admin', 'Reset for test');
      }
      
      const status = lockoutManager.lockAccount(testUserId, 'daily_limit', 'Consecutive violation');
      
      const expectedDuration = 336 * 60 * 60 * 1000; // 336 hours in milliseconds
      const actualDuration = status.lockoutUntil!.getTime() - now.getTime();
      
      expect(actualDuration).toBe(expectedDuration);
    });

    it('should emit accountLocked event', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('accountLocked', eventSpy);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          lockoutType: 'daily_limit',
          isLocked: true
        })
      );
    });

    it('should emit disableTradingFunctions event', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('disableTradingFunctions', eventSpy);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          lockoutUntil: expect.any(Date)
        })
      );
    });
  });

  describe('Account Unlocking', () => {
    beforeEach(() => {
      lockoutManager.initializeUser(testUserId);
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
    });

    it('should unlock account manually', () => {
      const result = lockoutManager.unlockAccount(testUserId, 'admin', 'Manual unlock for testing');
      
      expect(result).toBe(true);
      
      const status = lockoutManager.getLockoutStatus(testUserId);
      expect(status!.isLocked).toBe(false);
      expect(status!.lockoutType).toBeNull();
      expect(status!.remainingTime).toBe(0);
    });

    it('should fail to unlock already unlocked account', () => {
      lockoutManager.unlockAccount(testUserId, 'admin', 'First unlock');
      
      const result = lockoutManager.unlockAccount(testUserId, 'admin', 'Second unlock');
      expect(result).toBe(false);
    });

    it('should fail to unlock non-existent user', () => {
      const result = lockoutManager.unlockAccount('non-existent-user', 'admin', 'Test');
      expect(result).toBe(false);
    });

    it('should emit accountUnlocked event on manual unlock', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('accountUnlocked', eventSpy);
      
      lockoutManager.unlockAccount(testUserId, 'admin', 'Test unlock');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          unlockedBy: 'admin',
          reason: 'Test unlock'
        })
      );
    });

    it('should emit enableTradingFunctions event', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('enableTradingFunctions', eventSpy);
      
      lockoutManager.unlockAccount(testUserId, 'admin', 'Test unlock');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId
        })
      );
    });
  });

  describe('Automatic Unlocking', () => {
    beforeEach(() => {
      lockoutManager.initializeUser(testUserId);
    });

    it('should automatically unlock after cooldown period', () => {
      const now = new Date();
      vi.setSystemTime(now);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      // Fast forward past lockout period (25 hours)
      vi.advanceTimersByTime(25 * 60 * 60 * 1000);
      
      const status = lockoutManager.getLockoutStatus(testUserId);
      expect(status!.isLocked).toBe(false);
    });

    it('should not unlock before cooldown period expires', () => {
      const now = new Date();
      vi.setSystemTime(now);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      // Fast forward 12 hours (halfway through 24-hour lockout)
      vi.advanceTimersByTime(12 * 60 * 60 * 1000);
      
      const status = lockoutManager.getLockoutStatus(testUserId);
      expect(status!.isLocked).toBe(true);
    });

    it('should emit accountUnlocked event on automatic unlock', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('accountUnlocked', eventSpy);
      
      const now = new Date();
      vi.setSystemTime(now);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      // Fast forward past lockout period
      vi.advanceTimersByTime(25 * 60 * 60 * 1000);
      
      // Trigger automatic unlock check
      lockoutManager.getLockoutStatus(testUserId);
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          unlockedBy: 'system',
          reason: 'Automatic unlock'
        })
      );
    });
  });

  describe('Remaining Time Calculation', () => {
    beforeEach(() => {
      lockoutManager.initializeUser(testUserId);
    });

    it('should return zero for unlocked account', () => {
      const remainingTime = lockoutManager.getRemainingLockoutTime(testUserId);
      expect(remainingTime).toBe(0);
    });

    it('should calculate remaining time correctly', () => {
      const now = new Date();
      vi.setSystemTime(now);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      // Advance 1 hour
      vi.advanceTimersByTime(60 * 60 * 1000);
      
      const remainingTime = lockoutManager.getRemainingLockoutTime(testUserId);
      expect(remainingTime).toBeLessThan(24 * 60); // Less than 24 hours in minutes
      expect(remainingTime).toBeGreaterThan(22 * 60); // More than 22 hours in minutes
    });

    it('should update remaining time in status', () => {
      const now = new Date();
      vi.setSystemTime(now);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      // Advance time
      vi.advanceTimersByTime(2 * 60 * 60 * 1000); // 2 hours
      
      const remainingTime = lockoutManager.getRemainingLockoutTime(testUserId);
      const status = lockoutManager.getLockoutStatus(testUserId);
      
      expect(status!.remainingTime).toBe(remainingTime);
    });
  });

  describe('Unlock Request System', () => {
    beforeEach(() => {
      lockoutManager.initializeUser(testUserId);
      
      // Create consecutive violation to enable request review
      for (let i = 0; i < 3; i++) {
        lockoutManager.lockAccount(testUserId, 'daily_limit', `Lockout ${i + 1}`);
        lockoutManager.unlockAccount(testUserId, 'admin', 'Reset for test');
      }
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Consecutive violation');
    });

    it('should allow unlock request for consecutive violations', () => {
      const result = lockoutManager.requestUnlockReview(testUserId, 'Need urgent access for important trade');
      expect(result).toBe(true);
    });

    it('should not allow unlock request for non-consecutive violations', () => {
      lockoutManager.unlockAccount(testUserId, 'admin', 'Reset');
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Single violation');
      
      const result = lockoutManager.requestUnlockReview(testUserId, 'Test request');
      expect(result).toBe(false);
    });

    it('should not allow unlock request for unlocked account', () => {
      lockoutManager.unlockAccount(testUserId, 'admin', 'Unlock first');
      
      const result = lockoutManager.requestUnlockReview(testUserId, 'Test request');
      expect(result).toBe(false);
    });

    it('should emit unlockRequestSubmitted event', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('unlockRequestSubmitted', eventSpy);
      
      lockoutManager.requestUnlockReview(testUserId, 'Need access');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          requestedBy: 'user',
          reason: 'Need access',
          approvalRequired: true
        })
      );
    });

    it('should track pending unlock requests', () => {
      lockoutManager.requestUnlockReview(testUserId, 'First request');
      lockoutManager.requestUnlockReview(testUserId, 'Second request');
      
      const pendingRequests = lockoutManager.getPendingUnlockRequests();
      expect(pendingRequests.length).toBe(2);
      expect(pendingRequests[0].reason).toBe('First request');
      expect(pendingRequests[1].reason).toBe('Second request');
    });

    it('should approve unlock request', () => {
      lockoutManager.requestUnlockReview(testUserId, 'Need access');
      
      const result = lockoutManager.approveUnlockRequest(0, 'admin-user');
      expect(result).toBe(true);
      
      const status = lockoutManager.getLockoutStatus(testUserId);
      expect(status!.isLocked).toBe(false);
    });

    it('should emit unlockRequestApproved event', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('unlockRequestApproved', eventSpy);
      
      lockoutManager.requestUnlockReview(testUserId, 'Need access');
      lockoutManager.approveUnlockRequest(0, 'admin-user');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          approvedBy: 'admin-user'
        })
      );
    });
  });

  describe('Notification System', () => {
    beforeEach(() => {
      lockoutManager.initializeUser(testUserId);
    });

    it('should emit notificationRequired event on lockout', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('notificationRequired', eventSpy);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          type: 'lockout_initiated',
          priority: 'critical',
          channels: expect.arrayContaining(['email', 'push', 'in_app'])
        })
      );
    });

    it('should schedule reminder notifications', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('notificationRequired', eventSpy);
      
      const now = new Date();
      vi.setSystemTime(now);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      // Fast forward to trigger reminder notifications
      vi.advanceTimersByTime(12 * 60 * 60 * 1000); // 12 hours (halfway through)
      vi.advanceTimersByTime(60 * 1000); // Advance by 1 minute to trigger interval check
      
      // Should have called notification for initial lockout and potentially reminders
      expect(eventSpy).toHaveBeenCalled();
    });

    it('should send unlock notification on manual unlock', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('notificationRequired', eventSpy);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      eventSpy.mockClear(); // Clear the lockout notification
      
      lockoutManager.unlockAccount(testUserId, 'admin', 'Manual unlock');
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          userId: testUserId,
          type: 'lockout_lifted',
          priority: 'high',
          message: expect.stringContaining('unlocked by admin')
        })
      );
    });
  });

  describe('Lockout History', () => {
    beforeEach(() => {
      lockoutManager.initializeUser(testUserId);
    });

    it('should record lockout events', () => {
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      lockoutManager.unlockAccount(testUserId, 'admin', 'Test unlock');
      
      const history = lockoutManager.getLockoutHistory(testUserId);
      
      const lockEvent = history.find(event => event.eventType === 'lockout_initiated');
      const unlockEvent = history.find(event => event.eventType === 'lockout_lifted');
      
      expect(lockEvent).toBeDefined();
      expect(unlockEvent).toBeDefined();
      expect(lockEvent!.lockoutType).toBe('daily_limit');
    });

    it('should filter history by user', () => {
      const otherUserId = 'other-user';
      lockoutManager.initializeUser(otherUserId);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'User 1 lockout');
      lockoutManager.lockAccount(otherUserId, 'weekly_limit', 'User 2 lockout');
      
      const user1History = lockoutManager.getLockoutHistory(testUserId);
      const user2History = lockoutManager.getLockoutHistory(otherUserId);
      
      expect(user1History.every(event => event.userId === testUserId)).toBe(true);
      expect(user2History.every(event => event.userId === otherUserId)).toBe(true);
      expect(user1History.length).toBeGreaterThan(0);
      expect(user2History.length).toBeGreaterThan(0);
    });

    it('should limit history results', () => {
      // Create multiple events
      for (let i = 0; i < 5; i++) {
        lockoutManager.lockAccount(testUserId, 'daily_limit', `Lockout ${i}`);
        lockoutManager.unlockAccount(testUserId, 'admin', `Unlock ${i}`);
      }
      
      const limitedHistory = lockoutManager.getLockoutHistory(testUserId, 3);
      expect(limitedHistory.length).toBeLessThanOrEqual(3);
    });

    it('should sort history by timestamp descending', () => {
      const now = new Date();
      vi.setSystemTime(now);
      
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'First');
      
      vi.advanceTimersByTime(60 * 60 * 1000); // 1 hour later
      lockoutManager.unlockAccount(testUserId, 'admin', 'Second');
      
      const history = lockoutManager.getLockoutHistory(testUserId);
      
      expect(history.length).toBeGreaterThanOrEqual(2);
      // Most recent events should be first in the array
      expect(history[0].timestamp.getTime()).toBeGreaterThanOrEqual(history[1].timestamp.getTime());
    });
  });

  describe('Configuration Management', () => {
    it('should use custom configuration', () => {
      const customConfig: Partial<LockoutConfig> = {
        dailyCooldownHours: 12,
        weeklyCooldownHours: 72,
        maxConsecutiveLockouts: 5
      };
      
      const customManager = new AccountLockoutManager(customConfig);
      const config = customManager.getConfig();
      
      expect(config.dailyCooldownHours).toBe(12);
      expect(config.weeklyCooldownHours).toBe(72);
      expect(config.maxConsecutiveLockouts).toBe(5);
      
      customManager.destroy();
    });

    it('should update configuration', () => {
      const newConfig: Partial<LockoutConfig> = {
        enableEmailNotifications: false,
        maxConsecutiveLockouts: 2
      };
      
      lockoutManager.updateConfig(newConfig);
      const config = lockoutManager.getConfig();
      
      expect(config.enableEmailNotifications).toBe(false);
      expect(config.maxConsecutiveLockouts).toBe(2);
    });

    it('should emit configUpdated event', () => {
      const eventSpy = vi.fn();
      lockoutManager.on('configUpdated', eventSpy);
      
      const newConfig: Partial<LockoutConfig> = {
        enableEmailNotifications: false
      };
      
      lockoutManager.updateConfig(newConfig);
      
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          enableEmailNotifications: false
        })
      );
    });

    it('should validate configuration correctly', () => {
      const validConfig: LockoutConfig = {
        enableDailyLockouts: true,
        enableWeeklyLockouts: true,
        dailyCooldownHours: 24,
        weeklyCooldownHours: 168,
        enableEmailNotifications: true,
        enablePushNotifications: true,
        maxConsecutiveLockouts: 3,
        escalatedCooldownHours: 336
      };
      
      const result = AccountLockoutManager.validateConfig(validConfig);
      expect(result.isValid).toBe(true);
      expect(result.errors.length).toBe(0);
    });

    it('should reject invalid configuration', () => {
      const invalidConfig: LockoutConfig = {
        enableDailyLockouts: true,
        enableWeeklyLockouts: true,
        dailyCooldownHours: -1, // Invalid
        weeklyCooldownHours: 10000, // Invalid
        enableEmailNotifications: true,
        enablePushNotifications: true,
        maxConsecutiveLockouts: 15, // Invalid
        escalatedCooldownHours: 100 // Invalid (less than weekly)
      };
      
      const result = AccountLockoutManager.validateConfig(invalidConfig);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Account Status Checks', () => {
    beforeEach(() => {
      lockoutManager.initializeUser(testUserId);
    });

    it('should return false for unlocked account', () => {
      const isLocked = lockoutManager.isAccountLocked(testUserId);
      expect(isLocked).toBe(false);
    });

    it('should return true for locked account', () => {
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test lockout');
      
      const isLocked = lockoutManager.isAccountLocked(testUserId);
      expect(isLocked).toBe(true);
    });

    it('should handle non-existent user gracefully', () => {
      const isLocked = lockoutManager.isAccountLocked('non-existent-user');
      expect(isLocked).toBe(false);
    });

    it('should return null status for non-existent user', () => {
      const status = lockoutManager.getLockoutStatus('non-existent-user');
      expect(status).toBeNull();
    });
  });

  describe('Cleanup and Destruction', () => {
    it('should clean up resources on destroy', () => {
      lockoutManager.initializeUser(testUserId);
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Test');
      
      // Should not throw
      expect(() => lockoutManager.destroy()).not.toThrow();
      
      // Should have cleared listeners
      expect(lockoutManager.listenerCount('accountLocked')).toBe(0);
    });

    it('should handle multiple destroy calls gracefully', () => {
      lockoutManager.destroy();
      expect(() => lockoutManager.destroy()).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid unlock request approval', () => {
      lockoutManager.initializeUser(testUserId);
      
      expect(() => lockoutManager.approveUnlockRequest(999, 'admin')).toThrow();
    });

    it('should handle approving non-pending unlock request', () => {
      lockoutManager.initializeUser(testUserId);
      
      // Create consecutive violation to enable requests
      for (let i = 0; i < 3; i++) {
        lockoutManager.lockAccount(testUserId, 'daily_limit', `Lockout ${i + 1}`);
        lockoutManager.unlockAccount(testUserId, 'admin', 'Reset for test');
      }
      lockoutManager.lockAccount(testUserId, 'daily_limit', 'Consecutive');
      
      lockoutManager.requestUnlockReview(testUserId, 'Test');
      lockoutManager.approveUnlockRequest(0, 'admin'); // First approval
      
      expect(() => lockoutManager.approveUnlockRequest(0, 'admin')).toThrow('Unlock request 0 is not pending approval'); // Second approval should fail
    });
  });
});