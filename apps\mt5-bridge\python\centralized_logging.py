"""
Centralized Logging System
Structured logging with multiple outputs and log aggregation support
"""

import json
import time
import uuid
import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass, asdict
from enum import Enum
import socket
import platform
from loguru import logger
import sys

try:
    import elasticsearch
    from elasticsearch import AsyncElasticsearch
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    logger.warning("Elasticsearch not available, using file-based logging only")

try:
    from pythonjsonlogger import jsonlogger
    JSON_LOGGER_AVAILABLE = True
except ImportError:
    JSON_LOGGER_AVAILABLE = False
    logger.warning("JSON logger not available, using standard formatter")

class LogLevel(Enum):
    """Log level enumeration"""
    TRACE = "TRACE"
    DEBUG = "DEBUG" 
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

@dataclass
class LogContext:
    """Structured log context"""
    service_name: str
    service_version: str
    environment: str
    instance_id: str
    hostname: str
    timestamp: datetime
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    correlation_id: Optional[str] = None
    component: Optional[str] = None
    
@dataclass  
class LogEntry:
    """Structured log entry"""
    level: LogLevel
    message: str
    context: LogContext
    extra_data: Dict[str, Any]
    exception_info: Optional[Dict[str, Any]] = None
    performance_data: Optional[Dict[str, Any]] = None
    security_data: Optional[Dict[str, Any]] = None

class ElasticsearchHandler:
    """Elasticsearch log handler for centralized logging"""
    
    def __init__(self, elasticsearch_url: str, index_pattern: str = "mt5-bridge-logs"):
        self.elasticsearch_url = elasticsearch_url
        self.index_pattern = index_pattern
        self.client: Optional[AsyncElasticsearch] = None
        self.buffer: List[Dict[str, Any]] = []
        self.buffer_size = 100
        self.flush_interval = 30  # seconds
        self.last_flush = time.time()
        
    async def initialize(self):
        """Initialize Elasticsearch connection"""
        if not ELASTICSEARCH_AVAILABLE:
            logger.warning("Elasticsearch not available, skipping initialization")
            return
            
        try:
            self.client = AsyncElasticsearch([self.elasticsearch_url])
            
            # Test connection
            info = await self.client.info()
            logger.info(f"Connected to Elasticsearch: {info['version']['number']}")
            
            # Create index template if it doesn't exist
            await self._create_index_template()
            
        except Exception as e:
            logger.error(f"Failed to initialize Elasticsearch: {e}")
            self.client = None
    
    async def _create_index_template(self):
        """Create Elasticsearch index template for log entries"""
        if not self.client:
            return
            
        template_name = f"{self.index_pattern}-template"
        index_pattern = f"{self.index_pattern}-*"
        
        template = {
            "index_patterns": [index_pattern],
            "template": {
                "settings": {
                    "number_of_shards": 1,
                    "number_of_replicas": 1,
                    "index": {
                        "lifecycle": {
                            "name": f"{self.index_pattern}-policy",
                            "rollover_alias": self.index_pattern
                        }
                    }
                },
                "mappings": {
                    "properties": {
                        "@timestamp": {"type": "date"},
                        "level": {"type": "keyword"},
                        "message": {"type": "text", "analyzer": "standard"},
                        "service_name": {"type": "keyword"},
                        "service_version": {"type": "keyword"},
                        "environment": {"type": "keyword"},
                        "instance_id": {"type": "keyword"},
                        "hostname": {"type": "keyword"},
                        "request_id": {"type": "keyword"},
                        "user_id": {"type": "keyword"},
                        "correlation_id": {"type": "keyword"},
                        "component": {"type": "keyword"},
                        "extra_data": {"type": "object", "dynamic": True},
                        "exception_info": {"type": "object"},
                        "performance_data": {"type": "object"},
                        "security_data": {"type": "object"}
                    }
                }
            }
        }
        
        try:
            await self.client.indices.put_index_template(
                name=template_name,
                body=template
            )
            logger.info(f"Created Elasticsearch index template: {template_name}")
        except Exception as e:
            logger.error(f"Failed to create index template: {e}")
    
    async def send_log(self, log_entry: LogEntry):
        """Send log entry to Elasticsearch"""
        if not self.client:
            return
            
        # Convert log entry to Elasticsearch document
        doc = {
            "@timestamp": log_entry.context.timestamp.isoformat(),
            "level": log_entry.level.value,
            "message": log_entry.message,
            "service_name": log_entry.context.service_name,
            "service_version": log_entry.context.service_version,
            "environment": log_entry.context.environment,
            "instance_id": log_entry.context.instance_id,
            "hostname": log_entry.context.hostname,
            "request_id": log_entry.context.request_id,
            "user_id": log_entry.context.user_id,
            "correlation_id": log_entry.context.correlation_id,
            "component": log_entry.context.component,
            "extra_data": log_entry.extra_data,
            "exception_info": log_entry.exception_info,
            "performance_data": log_entry.performance_data,
            "security_data": log_entry.security_data
        }
        
        # Add to buffer
        self.buffer.append(doc)
        
        # Flush if buffer is full or enough time has passed
        current_time = time.time()
        if (len(self.buffer) >= self.buffer_size or 
            current_time - self.last_flush > self.flush_interval):
            await self._flush_buffer()
    
    async def _flush_buffer(self):
        """Flush log buffer to Elasticsearch"""
        if not self.buffer or not self.client:
            return
            
        try:
            # Create daily index
            today = datetime.now().strftime("%Y.%m.%d")
            index_name = f"{self.index_pattern}-{today}"
            
            # Bulk insert
            actions = []
            for doc in self.buffer:
                actions.append({"index": {"_index": index_name}})
                actions.append(doc)
            
            await self.client.bulk(body=actions)
            logger.debug(f"Flushed {len(self.buffer)} log entries to Elasticsearch")
            
            self.buffer.clear()
            self.last_flush = time.time()
            
        except Exception as e:
            logger.error(f"Failed to flush logs to Elasticsearch: {e}")
    
    async def close(self):
        """Close Elasticsearch connection"""
        if self.buffer:
            await self._flush_buffer()
        
        if self.client:
            await self.client.close()

class CentralizedLogger:
    """Centralized logging system with structured logging and multiple outputs"""
    
    def __init__(self, 
                 service_name: str = "mt5-bridge",
                 service_version: str = "1.0.0",
                 environment: str = "development",
                 elasticsearch_url: Optional[str] = None):
        
        self.service_name = service_name
        self.service_version = service_version
        self.environment = environment
        self.instance_id = str(uuid.uuid4())[:8]
        self.hostname = socket.gethostname()
        
        # Initialize handlers
        self.elasticsearch_handler = None
        if elasticsearch_url:
            self.elasticsearch_handler = ElasticsearchHandler(elasticsearch_url)
        
        # Configure loguru
        self._configure_loguru()
        
    def _configure_loguru(self):
        """Configure loguru with structured logging"""
        
        # Remove default handler
        logger.remove()
        
        # Console handler with colors
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{extra[component]}</cyan> | <white>{message}</white>",
            level="INFO",
            colorize=True,
            enqueue=True,
            serialize=False
        )
        
        # File handler with JSON format
        if JSON_LOGGER_AVAILABLE:
            json_format = "{time} | {level} | {message} | {extra}"
        else:
            json_format = "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {extra[component]} | {message}"
        
        logger.add(
            "logs/mt5_bridge_{time:YYYY-MM-DD}.log",
            format=json_format,
            level="DEBUG",
            rotation="daily",
            retention="30 days",
            compression="gz",
            enqueue=True,
            serialize=JSON_LOGGER_AVAILABLE
        )
        
        # Error log file
        logger.add(
            "logs/mt5_bridge_errors_{time:YYYY-MM-DD}.log",
            format=json_format,
            level="ERROR",
            rotation="daily", 
            retention="90 days",
            compression="gz",
            enqueue=True,
            serialize=JSON_LOGGER_AVAILABLE,
            filter=lambda record: record["level"].name in ["ERROR", "CRITICAL"]
        )
        
        # Security events log
        logger.add(
            "logs/mt5_bridge_security_{time:YYYY-MM-DD}.log",
            format=json_format,
            level="WARNING",
            rotation="daily",
            retention="365 days",  # Keep security logs longer
            compression="gz",
            enqueue=True,
            serialize=JSON_LOGGER_AVAILABLE,
            filter=lambda record: record["extra"].get("log_type") == "security"
        )
        
        # Performance log
        logger.add(
            "logs/mt5_bridge_performance_{time:YYYY-MM-DD}.log",
            format=json_format,
            level="INFO",
            rotation="daily",
            retention="7 days",
            compression="gz",
            enqueue=True,
            serialize=JSON_LOGGER_AVAILABLE,
            filter=lambda record: record["extra"].get("log_type") == "performance"
        )
    
    async def initialize(self):
        """Initialize the logging system"""
        if self.elasticsearch_handler:
            await self.elasticsearch_handler.initialize()
        
        logger.info(
            "Centralized logging initialized",
            component="logging",
            service_name=self.service_name,
            service_version=self.service_version,
            environment=self.environment,
            instance_id=self.instance_id
        )
    
    def _create_log_context(self, 
                           request_id: Optional[str] = None,
                           user_id: Optional[str] = None,
                           correlation_id: Optional[str] = None,
                           component: Optional[str] = None) -> LogContext:
        """Create log context"""
        return LogContext(
            service_name=self.service_name,
            service_version=self.service_version,
            environment=self.environment,
            instance_id=self.instance_id,
            hostname=self.hostname,
            timestamp=datetime.utcnow(),
            request_id=request_id,
            user_id=user_id,
            correlation_id=correlation_id,
            component=component
        )
    
    async def log(self, 
                  level: LogLevel,
                  message: str,
                  component: str = "unknown",
                  request_id: Optional[str] = None,
                  user_id: Optional[str] = None,
                  correlation_id: Optional[str] = None,
                  extra_data: Optional[Dict[str, Any]] = None,
                  exception_info: Optional[Exception] = None,
                  performance_data: Optional[Dict[str, Any]] = None,
                  security_data: Optional[Dict[str, Any]] = None):
        """Log a structured message"""
        
        context = self._create_log_context(request_id, user_id, correlation_id, component)
        
        # Prepare exception info
        exc_info = None
        if exception_info:
            exc_info = {
                "type": type(exception_info).__name__,
                "message": str(exception_info),
                "traceback": str(exception_info.__traceback__) if exception_info.__traceback__ else None
            }
        
        # Create log entry
        log_entry = LogEntry(
            level=level,
            message=message,
            context=context,
            extra_data=extra_data or {},
            exception_info=exc_info,
            performance_data=performance_data,
            security_data=security_data
        )
        
        # Send to loguru
        extra = {
            "component": component,
            "request_id": request_id,
            "user_id": user_id,
            "correlation_id": correlation_id,
            "service_name": self.service_name,
            "instance_id": self.instance_id,
            "extra_data": extra_data,
            "log_type": "security" if security_data else ("performance" if performance_data else "application")
        }
        
        logger_method = getattr(logger, level.value.lower())
        logger_method(message, **extra)
        
        # Send to Elasticsearch
        if self.elasticsearch_handler:
            await self.elasticsearch_handler.send_log(log_entry)
    
    # Convenience methods
    async def debug(self, message: str, component: str = "debug", **kwargs):
        await self.log(LogLevel.DEBUG, message, component=component, **kwargs)
    
    async def info(self, message: str, component: str = "info", **kwargs):
        await self.log(LogLevel.INFO, message, component=component, **kwargs)
    
    async def warning(self, message: str, component: str = "warning", **kwargs):
        await self.log(LogLevel.WARNING, message, component=component, **kwargs)
    
    async def error(self, message: str, component: str = "error", **kwargs):
        await self.log(LogLevel.ERROR, message, component=component, **kwargs)
    
    async def critical(self, message: str, component: str = "critical", **kwargs):
        await self.log(LogLevel.CRITICAL, message, component=component, **kwargs)
    
    async def log_performance(self, 
                             operation: str,
                             duration_ms: float,
                             component: str,
                             success: bool = True,
                             **kwargs):
        """Log performance metrics"""
        performance_data = {
            "operation": operation,
            "duration_ms": duration_ms,
            "success": success,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.log(
            LogLevel.INFO,
            f"Performance: {operation} completed in {duration_ms:.2f}ms",
            component=component,
            performance_data=performance_data,
            **kwargs
        )
    
    async def log_security_event(self,
                                event_type: str,
                                severity: str,
                                details: Dict[str, Any],
                                client_ip: Optional[str] = None,
                                user_agent: Optional[str] = None,
                                **kwargs):
        """Log security events"""
        security_data = {
            "event_type": event_type,
            "severity": severity,
            "client_ip": client_ip,
            "user_agent": user_agent,
            "details": details,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        level = LogLevel.CRITICAL if severity == "critical" else LogLevel.WARNING
        
        await self.log(
            level,
            f"Security Event: {event_type} - {severity}",
            component="security",
            security_data=security_data,
            **kwargs
        )
    
    async def log_api_request(self,
                             method: str,
                             path: str,
                             status_code: int,
                             duration_ms: float,
                             client_ip: str,
                             user_agent: str,
                             request_size: int = 0,
                             response_size: int = 0,
                             **kwargs):
        """Log API request details"""
        extra_data = {
            "http_method": method,
            "http_path": path,
            "http_status": status_code,
            "client_ip": client_ip,
            "user_agent": user_agent,
            "request_size": request_size,
            "response_size": response_size
        }
        
        performance_data = {
            "operation": f"{method} {path}",
            "duration_ms": duration_ms,
            "success": status_code < 400
        }
        
        level = LogLevel.ERROR if status_code >= 500 else (
               LogLevel.WARNING if status_code >= 400 else LogLevel.INFO)
        
        await self.log(
            level,
            f"{method} {path} - {status_code} in {duration_ms:.2f}ms",
            component="api",
            extra_data=extra_data,
            performance_data=performance_data,
            **kwargs
        )
    
    async def close(self):
        """Close logging system"""
        if self.elasticsearch_handler:
            await self.elasticsearch_handler.close()

# Global centralized logger instance
_centralized_logger: Optional[CentralizedLogger] = None

def get_centralized_logger(
    service_name: str = "mt5-bridge",
    service_version: str = "1.0.0", 
    environment: str = "development",
    elasticsearch_url: Optional[str] = None
) -> CentralizedLogger:
    """Get global centralized logger instance"""
    global _centralized_logger
    if _centralized_logger is None:
        _centralized_logger = CentralizedLogger(
            service_name=service_name,
            service_version=service_version,
            environment=environment,
            elasticsearch_url=elasticsearch_url
        )
    return _centralized_logger