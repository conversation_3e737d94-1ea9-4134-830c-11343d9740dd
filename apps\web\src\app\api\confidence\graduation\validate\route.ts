import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@golddaddy/types';
import { ConfidenceAssessment, ConfidenceStage } from '@golddaddy/types/confidence';
import { 
  validateGraduation, 
  GraduationValidationResult,
  GraduationCriteria,
  getGraduationStatusMessage 
} from '@/lib/confidence-validation';

// Mock assessment data - In production, this would use Supabase/Prisma
const mockAssessmentData: Record<string, ConfidenceAssessment> = {
  'demo-user': {
    id: 'assessment-1',
    userId: 'demo-user',
    currentStage: ConfidenceStage.PAPER_TRADING,
    overallConfidenceScore: 82,
    assessmentScores: {
      knowledgeQuiz: {
        score: 85,
        completedAt: new Date('2025-01-20'),
        attempts: 2,
        weakAreas: ['Risk Management', 'Market Analysis']
      },
      behavioralAssessment: {
        riskTolerance: 78,
        decisionConsistency: 82,
        emotionalStability: 75,
        lastAssessed: new Date('2025-01-22')
      },
      performanceEvaluation: {
        paperTradingWinRate: 65,
        riskManagementScore: 78,
        strategyAdherence: 80,
        consistencyRating: 72
      },
      stressTestResults: {
        scenariosPassed: 7,
        totalScenarios: 10,
        panicResponses: 1,
        recoveryTimes: [45, 30, 60, 25, 40, 35, 50]
      }
    },
    progressHistory: [
      {
        stage: 'goal_setting',
        completedAt: new Date('2025-01-10'),
        confidenceScoreAtCompletion: 70,
        timeSpentInStage: 5
      },
      {
        stage: 'strategy_learning', 
        completedAt: new Date('2025-01-17'),
        confidenceScoreAtCompletion: 75,
        timeSpentInStage: 7
      },
      {
        stage: 'backtesting_review',
        completedAt: new Date('2025-01-31'),
        confidenceScoreAtCompletion: 80,
        timeSpentInStage: 14
      },
      {
        stage: 'paper_trading',
        completedAt: new Date('2025-02-01'), // Started paper trading stage
        confidenceScoreAtCompletion: 82,
        timeSpentInStage: 25 // Currently in this stage for 25 days
      }
    ],
    graduationCriteria: {
      nextStage: 'live_ready',
      requirements: {
        minimumConfidenceScore: 85,
        requiredAssessments: ['knowledge_quiz', 'behavioral_assessment', 'performance_evaluation', 'final_assessment'],
        minimumTimeInStage: 30,
        customCriteria: {
          paperTradingTrades: 20,
          winRateThreshold: 60
        }
      }
    },
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-02-25')
  }
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<ApiResponse<GraduationValidationResult>>> {
  try {
    // In production:
    // 1. Validate JWT token
    // 2. Extract user ID from token
    // 3. Query Supabase for user assessment data
    
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    
    const assessment = mockAssessmentData[userId];
    
    if (!assessment) {
      return NextResponse.json(
        {
          success: false,
          error: 'Assessment data not found',
          message: 'No confidence assessment found for user. Please complete initial assessment.',
        },
        { status: 404 }
      );
    }
    
    // Perform graduation validation
    const validationResult = validateGraduation(assessment);
    
    return NextResponse.json({
      success: true,
      data: validationResult,
      message: getGraduationStatusMessage(validationResult),
    });
  } catch (error) {
    console.error('Error validating graduation:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Graduation validation failed',
        message: 'An unexpected error occurred while validating graduation criteria.',
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest
): Promise<NextResponse<ApiResponse<{ validated: boolean; result: GraduationValidationResult }>>> {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    
    // Validate request body
    if (!body.assessmentUpdates && !body.customCriteria) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request',
          message: 'Either assessmentUpdates or customCriteria is required',
        },
        { status: 400 }
      );
    }
    
    const assessment = mockAssessmentData[userId];
    if (!assessment) {
      return NextResponse.json(
        {
          success: false,
          error: 'Assessment data not found',
        },
        { status: 404 }
      );
    }
    
    // Update assessment with any new data
    if (body.assessmentUpdates) {
      // In production: Update assessment in database
      Object.assign(assessment.assessmentScores, body.assessmentUpdates);
      assessment.updatedAt = new Date();
    }
    
    // Apply custom criteria if provided
    const customCriteria: Partial<GraduationCriteria> = body.customCriteria || {};
    
    // Perform validation with updated data
    const validationResult = validateGraduation(assessment, customCriteria);
    
    // In production: Log validation attempt
    console.log(`Graduation validation for user ${userId}:`, {
      isEligible: validationResult.isEligible,
      currentStage: validationResult.currentStage,
      overallScore: validationResult.overallScore,
      missingRequirements: validationResult.missingRequirements.length
    });
    
    return NextResponse.json({
      success: true,
      data: {
        validated: true,
        result: validationResult
      },
      message: getGraduationStatusMessage(validationResult),
    });
  } catch (error) {
    console.error('Error processing graduation validation:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Validation processing failed',
      },
      { status: 500 }
    );
  }
}

// PUT endpoint for updating graduation criteria
export async function PUT(
  request: NextRequest
): Promise<NextResponse<ApiResponse<{ updated: boolean; newCriteria: GraduationCriteria }>>> {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    
    const assessment = mockAssessmentData[userId];
    if (!assessment) {
      return NextResponse.json(
        {
          success: false,
          error: 'Assessment data not found',
        },
        { status: 404 }
      );
    }
    
    // Validate criteria update request
    if (!body.graduationCriteria) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request',
          message: 'graduationCriteria is required',
        },
        { status: 400 }
      );
    }
    
    // Update graduation criteria
    assessment.graduationCriteria = {
      ...assessment.graduationCriteria,
      ...body.graduationCriteria
    };
    assessment.updatedAt = new Date();
    
    // In production: Update in database and create audit log
    console.log(`Updated graduation criteria for user ${userId}:`, assessment.graduationCriteria);
    
    return NextResponse.json({
      success: true,
      data: {
        updated: true,
        newCriteria: assessment.graduationCriteria.requirements as GraduationCriteria
      },
      message: 'Graduation criteria updated successfully',
    });
  } catch (error) {
    console.error('Error updating graduation criteria:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Criteria update failed',
      },
      { status: 500 }
    );
  }
}