/**
 * API Rate Limiting Manager
 * 
 * Implements token bucket algorithm for rate limiting across multiple market data providers.
 * Provides intelligent throttling, request queuing, and monitoring for API usage optimization.
 */

import { EventEmitter } from 'events';
import { DataSource } from './RealTimeDataProcessor';

// Rate limiting configuration per provider
export interface ProviderLimitConfig {
  maxRequestsPerSecond: number;
  maxRequestsPerMinute: number;
  maxRequestsPerHour: number;
  maxRequestsPerDay: number;
  maxBurstRequests: number; // Token bucket size
  refillRate: number; // Tokens per second
  maxQueueSize: number;
  timeoutMs: number;
  retryAttempts: number;
  retryBackoffMs: number;
}

// Overall rate limiting configuration
export interface RateLimitConfig {
  providers: Record<DataSource, ProviderLimitConfig>;
  globalMaxConcurrentRequests: number;
  enableQueueing: boolean;
  enableRetries: boolean;
  enableThrottling: boolean;
  monitoringIntervalMs: number;
  alertThresholds: {
    utilizationPercent: number; // Alert when usage exceeds this
    queueSizeWarning: number;
    errorRatePercent: number;
  };
}

// Request priority levels
export enum RequestPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Request metadata for queuing and tracking
export interface ApiRequest {
  id: string;
  source: DataSource;
  endpoint: string;
  priority: RequestPriority;
  timestamp: Date;
  retryCount: number;
  maxRetries: number;
  timeoutMs: number;
  metadata?: Record<string, any>;
}

// Token bucket for rate limiting implementation
class TokenBucket {
  private tokens: number;
  private lastRefill: number;
  private readonly capacity: number;
  private readonly refillRate: number;

  constructor(capacity: number, refillRate: number) {
    this.capacity = capacity;
    this.refillRate = refillRate;
    this.tokens = capacity;
    this.lastRefill = Date.now();
  }

  /**
   * Attempt to consume tokens from the bucket
   */
  public consume(tokens: number = 1): boolean {
    this.refill();
    
    if (this.tokens >= tokens) {
      this.tokens -= tokens;
      return true;
    }
    
    return false;
  }

  /**
   * Check if tokens are available without consuming them
   */
  public canConsume(tokens: number = 1): boolean {
    this.refill();
    return this.tokens >= tokens;
  }

  /**
   * Get current token count
   */
  public getTokenCount(): number {
    this.refill();
    return this.tokens;
  }

  /**
   * Get time until next token is available (in ms)
   */
  public getTimeUntilToken(): number {
    this.refill();
    
    if (this.tokens >= 1) {
      return 0;
    }
    
    return Math.ceil(1000 / this.refillRate);
  }

  /**
   * Refill tokens based on elapsed time
   */
  private refill(): void {
    const now = Date.now();
    const elapsed = now - this.lastRefill;
    const tokensToAdd = Math.floor((elapsed / 1000) * this.refillRate);
    
    if (tokensToAdd > 0) {
      this.tokens = Math.min(this.capacity, this.tokens + tokensToAdd);
      this.lastRefill = now;
    }
  }
}

// Request queue with priority support
class PriorityQueue {
  private queues: Map<RequestPriority, ApiRequest[]> = new Map();
  private priorityOrder: RequestPriority[] = [
    RequestPriority.CRITICAL,
    RequestPriority.HIGH,
    RequestPriority.MEDIUM,
    RequestPriority.LOW,
  ];

  constructor() {
    // Initialize priority queues
    this.priorityOrder.forEach(priority => {
      this.queues.set(priority, []);
    });
  }

  /**
   * Add request to appropriate priority queue
   */
  public enqueue(request: ApiRequest): void {
    const queue = this.queues.get(request.priority);
    if (queue) {
      queue.push(request);
    }
  }

  /**
   * Get next request from highest priority queue
   */
  public dequeue(): ApiRequest | null {
    for (const priority of this.priorityOrder) {
      const queue = this.queues.get(priority);
      if (queue && queue.length > 0) {
        return queue.shift() || null;
      }
    }
    return null;
  }

  /**
   * Get total number of queued requests
   */
  public size(): number {
    let total = 0;
    this.queues.forEach(queue => {
      total += queue.length;
    });
    return total;
  }

  /**
   * Get queue size for specific priority
   */
  public sizeForPriority(priority: RequestPriority): number {
    return this.queues.get(priority)?.length || 0;
  }

  /**
   * Clear all queues
   */
  public clear(): void {
    this.queues.forEach(queue => {
      queue.length = 0;
    });
  }
}

// Rate limiting statistics
export interface RateLimitStats {
  provider: DataSource;
  requestsAllowed: number;
  requestsThrottled: number;
  requestsQueued: number;
  requestsFailed: number;
  averageQueueTime: number;
  currentQueueSize: number;
  tokenBucketLevel: number;
  utilizationPercent: number;
  errorRate: number;
  lastRequestAt: Date;
  totalRequests: number;
}

// Overall system statistics
export interface SystemStats {
  totalRequestsPerSecond: number;
  totalActiveRequests: number;
  totalQueuedRequests: number;
  overallUtilization: number;
  providerStats: Record<DataSource, RateLimitStats>;
  alerts: string[];
  lastUpdated: Date;
}

/**
 * API Rate Limit Manager with token bucket implementation
 */
export class ApiRateLimitManager extends EventEmitter {
  private config: RateLimitConfig;
  private tokenBuckets: Map<DataSource, TokenBucket> = new Map();
  private requestQueues: Map<DataSource, PriorityQueue> = new Map();
  private providerStats: Map<DataSource, RateLimitStats> = new Map();
  private activeRequests: Set<string> = new Set();
  private processingIntervals: Map<DataSource, NodeJS.Timeout> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor(config: RateLimitConfig) {
    super();
    this.config = config;
    this.initializeProviders();
    this.startMonitoring();
  }

  /**
   * Initialize rate limiting for all configured providers
   */
  private initializeProviders(): void {
    Object.entries(this.config.providers).forEach(([provider, config]) => {
      const source = provider as DataSource;
      
      // Initialize token bucket
      this.tokenBuckets.set(
        source,
        new TokenBucket(config.maxBurstRequests, config.refillRate)
      );
      
      // Initialize request queue
      this.requestQueues.set(source, new PriorityQueue());
      
      // Initialize statistics
      this.providerStats.set(source, this.createInitialStats(source));
      
      // Start request processing for this provider
      this.startRequestProcessing(source);
    });
  }

  /**
   * Create initial statistics for a provider
   */
  private createInitialStats(provider: DataSource): RateLimitStats {
    return {
      provider,
      requestsAllowed: 0,
      requestsThrottled: 0,
      requestsQueued: 0,
      requestsFailed: 0,
      averageQueueTime: 0,
      currentQueueSize: 0,
      tokenBucketLevel: this.tokenBuckets.get(provider)?.getTokenCount() || 0,
      utilizationPercent: 0,
      errorRate: 0,
      lastRequestAt: new Date(),
      totalRequests: 0,
    };
  }

  /**
   * Check if a request can be made immediately
   */
  public canMakeRequest(source: DataSource): boolean {
    const bucket = this.tokenBuckets.get(source);
    if (!bucket) return false;
    
    // Check global concurrent request limit
    if (this.activeRequests.size >= this.config.globalMaxConcurrentRequests) {
      return false;
    }
    
    return bucket.canConsume(1);
  }

  /**
   * Request API access with rate limiting
   */
  public async requestAccess(
    source: DataSource,
    endpoint: string,
    priority: RequestPriority = RequestPriority.MEDIUM,
    metadata?: Record<string, any>
  ): Promise<string> {
    const request: ApiRequest = {
      id: this.generateRequestId(),
      source,
      endpoint,
      priority,
      timestamp: new Date(),
      retryCount: 0,
      maxRetries: this.config.providers[source]?.retryAttempts || 3,
      timeoutMs: this.config.providers[source]?.timeoutMs || 5000,
      metadata,
    };

    return this.processRequest(request);
  }

  /**
   * Process API request with rate limiting logic
   */
  private async processRequest(request: ApiRequest): Promise<string> {
    const bucket = this.tokenBuckets.get(request.source);
    const stats = this.providerStats.get(request.source);
    
    if (!bucket || !stats) {
      throw new Error(`Provider ${request.source} not configured`);
    }

    stats.totalRequests++;

    // Try immediate processing
    if (this.canMakeRequest(request.source)) {
      if (bucket.consume(1)) {
        this.activeRequests.add(request.id);
        stats.requestsAllowed++;
        this.updateStats(request.source);
        
        this.emit('request_allowed', {
          requestId: request.id,
          source: request.source,
          endpoint: request.endpoint,
          timestamp: new Date(),
        });
        
        return request.id;
      }
    }

    // Queue request if queuing is enabled
    if (this.config.enableQueueing) {
      const queue = this.requestQueues.get(request.source);
      if (queue) {
        const maxQueueSize = this.config.providers[request.source].maxQueueSize;
        
        if (queue.size() >= maxQueueSize) {
          stats.requestsFailed++;
          this.updateStats(request.source);
          
          this.emit('request_rejected', {
            requestId: request.id,
            source: request.source,
            reason: 'Queue full',
            queueSize: queue.size(),
          });
          
          throw new Error(`Request queue full for ${request.source}`);
        }
        
        queue.enqueue(request);
        stats.requestsQueued++;
        this.updateStats(request.source);
        
        this.emit('request_queued', {
          requestId: request.id,
          source: request.source,
          queuePosition: queue.size(),
          priority: request.priority,
        });
        
        // Return promise that resolves when request is processed
        return new Promise((resolve, reject) => {
          const checkInterval = setInterval(() => {
            if (this.activeRequests.has(request.id)) {
              clearInterval(checkInterval);
              resolve(request.id);
            }
          }, 100);
          
          // Timeout handling
          setTimeout(() => {
            clearInterval(checkInterval);
            stats.requestsFailed++;
            this.updateStats(request.source);
            reject(new Error(`Request timeout: ${request.id}`));
          }, request.timeoutMs);
        });
      }
    }

    // Throttle request
    stats.requestsThrottled++;
    this.updateStats(request.source);
    
    this.emit('request_throttled', {
      requestId: request.id,
      source: request.source,
      nextAvailableIn: bucket.getTimeUntilToken(),
    });
    
    throw new Error(`Rate limit exceeded for ${request.source}`);
  }

  /**
   * Start request processing for a provider
   */
  private startRequestProcessing(source: DataSource): void {
    const interval = setInterval(() => {
      this.processQueuedRequests(source);
    }, 100);
    
    this.processingIntervals.set(source, interval);
  }

  /**
   * Process queued requests for a provider
   */
  private async processQueuedRequests(source: DataSource): Promise<void> {
    const queue = this.requestQueues.get(source);
    const bucket = this.tokenBuckets.get(source);
    const stats = this.providerStats.get(source);
    
    if (!queue || !bucket || !stats) return;
    
    // Process as many requests as tokens allow
    while (queue.size() > 0 && this.canMakeRequest(source)) {
      const request = queue.dequeue();
      if (!request) break;
      
      if (bucket.consume(1)) {
        this.activeRequests.add(request.id);
        stats.requestsAllowed++;
        
        // Calculate queue time
        const queueTime = Date.now() - request.timestamp.getTime();
        stats.averageQueueTime = 
          ((stats.averageQueueTime * (stats.requestsAllowed - 1)) + queueTime) / stats.requestsAllowed;
        
        this.emit('request_processed', {
          requestId: request.id,
          source: request.source,
          queueTime,
          priority: request.priority,
        });
      }
    }
    
    this.updateStats(source);
  }

  /**
   * Release a request (mark as completed)
   */
  public releaseRequest(requestId: string): void {
    if (this.activeRequests.has(requestId)) {
      this.activeRequests.delete(requestId);
      
      this.emit('request_completed', {
        requestId,
        timestamp: new Date(),
      });
    }
  }

  /**
   * Update statistics for a provider
   */
  private updateStats(source: DataSource): void {
    const stats = this.providerStats.get(source);
    const queue = this.requestQueues.get(source);
    const bucket = this.tokenBuckets.get(source);
    
    if (!stats || !queue || !bucket) return;
    
    stats.currentQueueSize = queue.size();
    stats.tokenBucketLevel = bucket.getTokenCount();
    stats.lastRequestAt = new Date();
    
    // Calculate utilization
    const maxTokens = this.config.providers[source].maxBurstRequests;
    stats.utilizationPercent = ((maxTokens - stats.tokenBucketLevel) / maxTokens) * 100;
    
    // Calculate error rate
    const totalProcessed = stats.requestsAllowed + stats.requestsFailed;
    stats.errorRate = totalProcessed > 0 ? (stats.requestsFailed / totalProcessed) * 100 : 0;
  }

  /**
   * Get current statistics for a provider
   */
  public getProviderStats(source: DataSource): RateLimitStats | null {
    const stats = this.providerStats.get(source);
    return stats ? { ...stats } : null;
  }

  /**
   * Get system-wide statistics
   */
  public getSystemStats(): SystemStats {
    const alerts: string[] = [];
    const totalActiveRequests = this.activeRequests.size;
    let totalQueuedRequests = 0;
    const totalRequestsPerSecond = 0;
    let overallUtilization = 0;
    
    const providerStats: Record<DataSource, RateLimitStats> = {};
    
    this.providerStats.forEach((stats, source) => {
      providerStats[source] = { ...stats };
      totalQueuedRequests += stats.currentQueueSize;
      overallUtilization += stats.utilizationPercent;
      
      // Generate alerts
      if (stats.utilizationPercent > this.config.alertThresholds.utilizationPercent) {
        alerts.push(`High utilization for ${source}: ${stats.utilizationPercent.toFixed(1)}%`);
      }
      
      if (stats.currentQueueSize > this.config.alertThresholds.queueSizeWarning) {
        alerts.push(`Large queue for ${source}: ${stats.currentQueueSize} requests`);
      }
      
      if (stats.errorRate > this.config.alertThresholds.errorRatePercent) {
        alerts.push(`High error rate for ${source}: ${stats.errorRate.toFixed(1)}%`);
      }
    });
    
    overallUtilization = overallUtilization / this.providerStats.size;
    
    return {
      totalRequestsPerSecond,
      totalActiveRequests,
      totalQueuedRequests,
      overallUtilization,
      providerStats,
      alerts,
      lastUpdated: new Date(),
    };
  }

  /**
   * Start monitoring and alerting
   */
  private startMonitoring(): void {
    this.monitoringInterval = setInterval(() => {
      const systemStats = this.getSystemStats();
      
      this.emit('monitoring_update', systemStats);
      
      if (systemStats.alerts.length > 0) {
        this.emit('rate_limit_alert', {
          alerts: systemStats.alerts,
          systemStats,
          timestamp: new Date(),
        });
      }
    }, this.config.monitoringIntervalMs);
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<RateLimitConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Update provider configurations
    if (newConfig.providers) {
      Object.entries(newConfig.providers).forEach(([provider, config]) => {
        const source = provider as DataSource;
        const bucket = this.tokenBuckets.get(source);
        
        if (bucket && config) {
          // Create new bucket with updated config
          this.tokenBuckets.set(
            source,
            new TokenBucket(config.maxBurstRequests, config.refillRate)
          );
        }
      });
    }
    
    this.emit('config_updated', this.config);
  }

  /**
   * Force clear queue for a provider (emergency use)
   */
  public clearQueue(source: DataSource): number {
    const queue = this.requestQueues.get(source);
    if (!queue) return 0;
    
    const cleared = queue.size();
    queue.clear();
    
    this.emit('queue_cleared', { source, requestsCleared: cleared });
    return cleared;
  }

  /**
   * Get queue status for a provider
   */
  public getQueueStatus(source: DataSource): {
    totalSize: number;
    priorityBreakdown: Record<RequestPriority, number>;
    averageWaitTime: number;
  } {
    const queue = this.requestQueues.get(source);
    const stats = this.providerStats.get(source);
    
    if (!queue || !stats) {
      throw new Error(`Provider ${source} not found`);
    }
    
    return {
      totalSize: queue.size(),
      priorityBreakdown: {
        [RequestPriority.CRITICAL]: queue.sizeForPriority(RequestPriority.CRITICAL),
        [RequestPriority.HIGH]: queue.sizeForPriority(RequestPriority.HIGH),
        [RequestPriority.MEDIUM]: queue.sizeForPriority(RequestPriority.MEDIUM),
        [RequestPriority.LOW]: queue.sizeForPriority(RequestPriority.LOW),
      },
      averageWaitTime: stats.averageQueueTime,
    };
  }

  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Health check for rate limiter
   */
  public healthCheck(): {
    isHealthy: boolean;
    issues: string[];
    systemStats: SystemStats;
  } {
    const issues: string[] = [];
    const systemStats = this.getSystemStats();
    
    // Check for system health issues
    if (systemStats.overallUtilization > 90) {
      issues.push('System utilization critically high');
    }
    
    if (systemStats.totalQueuedRequests > 1000) {
      issues.push('Total queued requests very high');
    }
    
    if (systemStats.alerts.length > 10) {
      issues.push('Too many active alerts');
    }
    
    // Check individual providers
    Object.entries(systemStats.providerStats).forEach(([provider, stats]) => {
      if (stats.errorRate > 50) {
        issues.push(`${provider} error rate critically high: ${stats.errorRate.toFixed(1)}%`);
      }
      
      if (stats.currentQueueSize > 500) {
        issues.push(`${provider} queue size critically high: ${stats.currentQueueSize}`);
      }
    });
    
    return {
      isHealthy: issues.length === 0,
      issues,
      systemStats,
    };
  }

  /**
   * Shutdown rate limiter and cleanup resources
   */
  public shutdown(): void {
    // Clear processing intervals
    this.processingIntervals.forEach(interval => {
      clearInterval(interval);
    });
    this.processingIntervals.clear();
    
    // Clear monitoring interval
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    // Clear queues
    this.requestQueues.forEach(queue => {
      queue.clear();
    });
    
    // Clear active requests
    this.activeRequests.clear();
    
    this.emit('shutdown');
    this.removeAllListeners();
  }
}