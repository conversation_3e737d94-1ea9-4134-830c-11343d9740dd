import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';

// Types for alerting system
interface AlertRule {
  id: string;
  name: string;
  description: string;
  metric: string;
  operator: 'gt' | 'lt' | 'eq' | 'neq';
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  cooldownPeriod: number; // minutes
  tags?: string[];
  conditions?: AlertCondition[];
}

interface AlertCondition {
  metric: string;
  operator: 'gt' | 'lt' | 'eq' | 'neq';
  value: number;
  timeWindow: number; // seconds
}

interface Alert {
  id: string;
  ruleId: string;
  instanceId: string;
  severity: string;
  message: string;
  timestamp: Date;
  resolved: boolean;
  resolvedAt?: Date;
  metadata: Record<string, any>;
  acknowledgements: AlertAcknowledgement[];
}

interface AlertAcknowledgement {
  userId: string;
  timestamp: Date;
  note?: string;
}

interface NotificationChannel {
  id: string;
  type: 'email' | 'slack' | 'webhook' | 'sms' | 'pagerduty';
  name: string;
  configuration: Record<string, any>;
  enabled: boolean;
  severityFilter?: string[];
  tags?: string[];
}

interface AlertEscalation {
  ruleId: string;
  levels: EscalationLevel[];
}

interface EscalationLevel {
  level: number;
  delayMinutes: number;
  channels: string[];
  severity?: string;
}

/**
 * Automated Alerting Service for Production Deployment
 * 
 * Manages alert rules, notifications, escalations, and acknowledgements
 * Integrates with multiple notification channels and external services
 */
export class AlertingService extends EventEmitter {
  private prisma: PrismaClient;
  private instanceId: string;
  private isProduction: boolean;
  
  private alertRules: Map<string, AlertRule> = new Map();
  private activeAlerts: Map<string, Alert> = new Map();
  private alertCooldowns: Map<string, Date> = new Map();
  private notificationChannels: Map<string, NotificationChannel> = new Map();
  private escalationRules: Map<string, AlertEscalation> = new Map();
  
  private evaluationInterval: NodeJS.Timeout | null = null;
  private escalationInterval: NodeJS.Timeout | null = null;
  
  // Configuration
  private readonly EVALUATION_INTERVAL = 30 * 1000; // 30 seconds
  private readonly ESCALATION_CHECK_INTERVAL = 60 * 1000; // 1 minute
  private readonly MAX_ALERT_RETENTION_DAYS = 30;

  constructor(prisma: PrismaClient) {
    super();
    
    this.prisma = prisma;
    this.instanceId = process.env.INSTANCE_ID || 'unknown';
    this.isProduction = process.env.NODE_ENV === 'production';
    
    this.initializeDefaultRules();
    this.initializeNotificationChannels();
    this.startAlertEvaluation();
    this.startEscalationMonitoring();
    
    this.emit('initialized', { instanceId: this.instanceId });
  }

  /**
   * Add or update an alert rule
   */
  public async addAlertRule(rule: Omit<AlertRule, 'id'>): Promise<string> {
    const ruleId = this.generateRuleId();
    
    const alertRule: AlertRule = {
      id: ruleId,
      ...rule
    };
    
    this.alertRules.set(ruleId, alertRule);
    
    // Store in database
    await this.prisma.alertRule.create({
      data: {
        id: ruleId,
        name: alertRule.name,
        description: alertRule.description,
        metric: alertRule.metric,
        operator: alertRule.operator,
        threshold: alertRule.threshold,
        severity: alertRule.severity.toUpperCase(),
        enabled: alertRule.enabled,
        cooldownPeriod: alertRule.cooldownPeriod,
        tags: alertRule.tags,
        conditions: alertRule.conditions as any,
        instanceId: this.instanceId,
        createdAt: new Date()
      }
    });
    
    this.emit('ruleAdded', alertRule);
    
    return ruleId;
  }

  /**
   * Add a notification channel
   */
  public async addNotificationChannel(channel: Omit<NotificationChannel, 'id'>): Promise<string> {
    const channelId = this.generateChannelId();
    
    const notificationChannel: NotificationChannel = {
      id: channelId,
      ...channel
    };
    
    this.notificationChannels.set(channelId, notificationChannel);
    
    // Store in database
    await this.prisma.notificationChannel.create({
      data: {
        id: channelId,
        type: notificationChannel.type,
        name: notificationChannel.name,
        configuration: notificationChannel.configuration as any,
        enabled: notificationChannel.enabled,
        severityFilter: notificationChannel.severityFilter,
        tags: notificationChannel.tags,
        instanceId: this.instanceId,
        createdAt: new Date()
      }
    });
    
    this.emit('channelAdded', notificationChannel);
    
    return channelId;
  }

  /**
   * Add escalation rule
   */
  public async addEscalationRule(escalation: AlertEscalation): Promise<void> {
    this.escalationRules.set(escalation.ruleId, escalation);
    
    // Store in database
    await this.prisma.alertEscalation.create({
      data: {
        ruleId: escalation.ruleId,
        levels: escalation.levels as any,
        instanceId: this.instanceId,
        createdAt: new Date()
      }
    });
    
    this.emit('escalationAdded', escalation);
  }

  /**
   * Trigger an alert manually
   */
  public async triggerAlert(
    ruleId: string,
    message: string,
    metadata: Record<string, any> = {}
  ): Promise<string> {
    const rule = this.alertRules.get(ruleId);
    
    if (!rule) {
      throw new Error(`Alert rule not found: ${ruleId}`);
    }
    
    if (!rule.enabled) {
      throw new Error(`Alert rule is disabled: ${ruleId}`);
    }
    
    // Check cooldown
    if (this.isInCooldown(ruleId)) {
      throw new Error(`Alert rule is in cooldown: ${ruleId}`);
    }
    
    return await this.createAlert(rule, message, metadata);
  }

  /**
   * Acknowledge an alert
   */
  public async acknowledgeAlert(
    alertId: string,
    userId: string,
    note?: string
  ): Promise<void> {
    const alert = this.activeAlerts.get(alertId);
    
    if (!alert) {
      throw new Error(`Alert not found: ${alertId}`);
    }
    
    const acknowledgement: AlertAcknowledgement = {
      userId,
      timestamp: new Date(),
      note
    };
    
    alert.acknowledgements.push(acknowledgement);
    
    // Update in database
    await this.prisma.alert.update({
      where: { id: alertId },
      data: {
        acknowledgements: alert.acknowledgements as any,
        updatedAt: new Date()
      }
    });
    
    this.emit('alertAcknowledged', { alert, acknowledgement });
  }

  /**
   * Resolve an alert
   */
  public async resolveAlert(
    alertId: string,
    userId?: string,
    note?: string
  ): Promise<void> {
    const alert = this.activeAlerts.get(alertId);
    
    if (!alert) {
      throw new Error(`Alert not found: ${alertId}`);
    }
    
    alert.resolved = true;
    alert.resolvedAt = new Date();
    
    // Update in database
    await this.prisma.alert.update({
      where: { id: alertId },
      data: {
        resolved: true,
        resolvedAt: alert.resolvedAt,
        resolverUserId: userId,
        resolutionNote: note,
        updatedAt: new Date()
      }
    });
    
    // Remove from active alerts
    this.activeAlerts.delete(alertId);
    
    this.emit('alertResolved', { alert, userId, note });
  }

  /**
   * Get alert statistics
   */
  public async getAlertStatistics(hours: number = 24): Promise<any> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hours);
    
    const [alertCounts, alertsBySeverity, alertsByRule, recentAlerts] = await Promise.all([
      // Total alert counts
      this.prisma.alert.count({
        where: {
          instanceId: this.instanceId,
          createdAt: { gte: cutoffDate }
        }
      }),
      
      // Alerts by severity
      this.prisma.alert.groupBy({
        by: ['severity'],
        where: {
          instanceId: this.instanceId,
          createdAt: { gte: cutoffDate }
        },
        _count: {
          id: true
        }
      }),
      
      // Alerts by rule
      this.prisma.alert.groupBy({
        by: ['alertRuleId'],
        where: {
          instanceId: this.instanceId,
          createdAt: { gte: cutoffDate }
        },
        _count: {
          id: true
        },
        orderBy: {
          _count: {
            id: 'desc'
          }
        },
        take: 10
      }),
      
      // Recent alerts
      this.prisma.alert.findMany({
        where: {
          instanceId: this.instanceId,
          createdAt: { gte: cutoffDate }
        },
        orderBy: { createdAt: 'desc' },
        take: 20,
        include: {
          alertRule: {
            select: {
              name: true
            }
          }
        }
      })
    ]);
    
    return {
      instanceId: this.instanceId,
      period: `${hours} hours`,
      totalAlerts: alertCounts,
      activeAlerts: this.activeAlerts.size,
      alertsBySeverity: alertsBySeverity.reduce((acc, item) => {
        acc[item.severity.toLowerCase()] = item._count.id;
        return acc;
      }, {} as Record<string, number>),
      topRules: alertsByRule.map(item => ({
        ruleId: item.alertRuleId,
        count: item._count.id
      })),
      recentAlerts: recentAlerts.map(alert => ({
        id: alert.id,
        ruleName: alert.alertRule?.name || 'Unknown',
        severity: alert.severity,
        message: alert.message,
        timestamp: alert.createdAt,
        resolved: alert.resolved
      }))
    };
  }

  /**
   * Get all alert rules
   */
  public getAlertRules(): AlertRule[] {
    return Array.from(this.alertRules.values());
  }

  /**
   * Get all notification channels
   */
  public getNotificationChannels(): NotificationChannel[] {
    return Array.from(this.notificationChannels.values());
  }

  /**
   * Get active alerts
   */
  public getActiveAlerts(): Alert[] {
    return Array.from(this.activeAlerts.values());
  }

  /**
   * Initialize default alert rules
   */
  private initializeDefaultRules(): void {
    const defaultRules: Omit<AlertRule, 'id'>[] = [
      {
        name: 'High CPU Usage',
        description: 'Alert when CPU usage exceeds 80%',
        metric: 'cpu_usage',
        operator: 'gt',
        threshold: 80,
        severity: 'high',
        enabled: true,
        cooldownPeriod: 5,
        tags: ['system', 'performance']
      },
      {
        name: 'High Memory Usage',
        description: 'Alert when memory usage exceeds 85%',
        metric: 'memory_usage',
        operator: 'gt',
        threshold: 85,
        severity: 'high',
        enabled: true,
        cooldownPeriod: 5,
        tags: ['system', 'performance']
      },
      {
        name: 'High Error Rate',
        description: 'Alert when error rate exceeds 5%',
        metric: 'error_rate',
        operator: 'gt',
        threshold: 0.05,
        severity: 'critical',
        enabled: true,
        cooldownPeriod: 2,
        tags: ['application', 'errors']
      },
      {
        name: 'Slow Response Time',
        description: 'Alert when response time exceeds 2 seconds',
        metric: 'response_time',
        operator: 'gt',
        threshold: 2000,
        severity: 'medium',
        enabled: true,
        cooldownPeriod: 3,
        tags: ['application', 'performance']
      },
      {
        name: 'High Disk Usage',
        description: 'Alert when disk usage exceeds 90%',
        metric: 'disk_usage',
        operator: 'gt',
        threshold: 90,
        severity: 'critical',
        enabled: true,
        cooldownPeriod: 15,
        tags: ['system', 'storage']
      },
      {
        name: 'Service Down',
        description: 'Alert when service is not responding',
        metric: 'service_uptime',
        operator: 'lt',
        threshold: 1,
        severity: 'critical',
        enabled: true,
        cooldownPeriod: 1,
        tags: ['system', 'availability']
      }
    ];
    
    // Add default rules
    defaultRules.forEach(rule => {
      const ruleId = this.generateRuleId();
      this.alertRules.set(ruleId, { id: ruleId, ...rule });
    });
  }

  /**
   * Initialize notification channels from environment
   */
  private initializeNotificationChannels(): void {
    // Slack channel
    if (process.env.SLACK_WEBHOOK_URL) {
      const slackChannel: NotificationChannel = {
        id: 'slack-default',
        type: 'slack',
        name: 'Default Slack Channel',
        configuration: {
          webhookUrl: process.env.SLACK_WEBHOOK_URL,
          channel: process.env.SLACK_CHANNEL || '#alerts'
        },
        enabled: true,
        severityFilter: ['high', 'critical']
      };
      
      this.notificationChannels.set(slackChannel.id, slackChannel);
    }
    
    // Email channel
    if (process.env.ALERT_EMAIL) {
      const emailChannel: NotificationChannel = {
        id: 'email-default',
        type: 'email',
        name: 'Default Email Channel',
        configuration: {
          recipients: [process.env.ALERT_EMAIL],
          smtpServer: process.env.SMTP_SERVER,
          smtpPort: process.env.SMTP_PORT || 587,
          smtpUser: process.env.SMTP_USER,
          smtpPassword: process.env.SMTP_PASSWORD
        },
        enabled: true,
        severityFilter: ['critical']
      };
      
      this.notificationChannels.set(emailChannel.id, emailChannel);
    }
    
    // Webhook channel
    if (process.env.ALERT_WEBHOOK_URL) {
      const webhookChannel: NotificationChannel = {
        id: 'webhook-default',
        type: 'webhook',
        name: 'Default Webhook Channel',
        configuration: {
          url: process.env.ALERT_WEBHOOK_URL,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${process.env.ALERT_WEBHOOK_TOKEN || ''}`
          }
        },
        enabled: true
      };
      
      this.notificationChannels.set(webhookChannel.id, webhookChannel);
    }
  }

  /**
   * Start alert rule evaluation
   */
  private startAlertEvaluation(): void {
    this.evaluationInterval = setInterval(async () => {
      await this.evaluateAlertRules();
    }, this.EVALUATION_INTERVAL);
  }

  /**
   * Start escalation monitoring
   */
  private startEscalationMonitoring(): void {
    this.escalationInterval = setInterval(async () => {
      await this.processEscalations();
    }, this.ESCALATION_CHECK_INTERVAL);
  }

  /**
   * Evaluate all alert rules
   */
  private async evaluateAlertRules(): Promise<void> {
    for (const rule of this.alertRules.values()) {
      if (!rule.enabled) continue;
      
      try {
        await this.evaluateRule(rule);
      } catch (error) {
        console.error(`[Alerting] Failed to evaluate rule ${rule.name}:`, error);
      }
    }
  }

  /**
   * Evaluate a single alert rule
   */
  private async evaluateRule(rule: AlertRule): Promise<void> {
    // Skip if in cooldown
    if (this.isInCooldown(rule.id)) {
      return;
    }
    
    // Get current metric value (this would integrate with your metrics system)
    const currentValue = await this.getCurrentMetricValue(rule.metric);
    
    if (currentValue === null) {
      return; // Metric not available
    }
    
    // Check if alert condition is met
    const shouldAlert = this.evaluateCondition(rule.operator, currentValue, rule.threshold);
    
    if (shouldAlert) {
      const message = `${rule.name}: ${rule.metric} is ${currentValue} (threshold: ${rule.threshold})`;
      const metadata = {
        metric: rule.metric,
        currentValue,
        threshold: rule.threshold,
        operator: rule.operator
      };
      
      await this.createAlert(rule, message, metadata);
    }
  }

  /**
   * Create a new alert
   */
  private async createAlert(
    rule: AlertRule,
    message: string,
    metadata: Record<string, any>
  ): Promise<string> {
    const alertId = this.generateAlertId();
    const timestamp = new Date();
    
    const alert: Alert = {
      id: alertId,
      ruleId: rule.id,
      instanceId: this.instanceId,
      severity: rule.severity,
      message,
      timestamp,
      resolved: false,
      metadata,
      acknowledgements: []
    };
    
    // Store in active alerts
    this.activeAlerts.set(alertId, alert);
    
    // Set cooldown
    this.alertCooldowns.set(rule.id, timestamp);
    
    // Store in database
    await this.prisma.alert.create({
      data: {
        id: alertId,
        alertRuleId: rule.id,
        instanceId: this.instanceId,
        severity: rule.severity.toUpperCase(),
        message,
        metadata: metadata as any,
        resolved: false,
        createdAt: timestamp
      }
    });
    
    // Send notifications
    await this.sendNotifications(alert);
    
    this.emit('alertTriggered', alert);
    
    return alertId;
  }

  /**
   * Send notifications for an alert
   */
  private async sendNotifications(alert: Alert): Promise<void> {
    const rule = this.alertRules.get(alert.ruleId);
    
    for (const channel of this.notificationChannels.values()) {
      if (!channel.enabled) continue;
      
      // Check severity filter
      if (channel.severityFilter && !channel.severityFilter.includes(alert.severity)) {
        continue;
      }
      
      // Check tags filter
      if (channel.tags && rule?.tags) {
        const hasMatchingTag = channel.tags.some(tag => rule.tags!.includes(tag));
        if (!hasMatchingTag) continue;
      }
      
      try {
        await this.sendNotification(channel, alert);
      } catch (error) {
        console.error(`[Alerting] Failed to send notification via ${channel.name}:`, error);
      }
    }
  }

  /**
   * Send notification via specific channel
   */
  private async sendNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    switch (channel.type) {
      case 'slack':
        await this.sendSlackNotification(channel, alert);
        break;
      case 'email':
        await this.sendEmailNotification(channel, alert);
        break;
      case 'webhook':
        await this.sendWebhookNotification(channel, alert);
        break;
      default:
        console.warn(`[Alerting] Unsupported notification type: ${channel.type}`);
    }
  }

  /**
   * Send Slack notification
   */
  private async sendSlackNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    const color = this.getSeverityColor(alert.severity);
    const emoji = this.getSeverityEmoji(alert.severity);
    
    const slackPayload = {
      channel: channel.configuration.channel,
      attachments: [
        {
          color,
          title: `${emoji} ${alert.severity.toUpperCase()} Alert`,
          text: alert.message,
          fields: [
            {
              title: 'Instance',
              value: alert.instanceId,
              short: true
            },
            {
              title: 'Rule',
              value: this.alertRules.get(alert.ruleId)?.name || 'Unknown',
              short: true
            },
            {
              title: 'Time',
              value: alert.timestamp.toISOString(),
              short: false
            }
          ],
          timestamp: Math.floor(alert.timestamp.getTime() / 1000)
        }
      ]
    };
    
    const response = await fetch(channel.configuration.webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(slackPayload)
    });
    
    if (!response.ok) {
      throw new Error(`Slack webhook responded with status: ${response.status}`);
    }
  }

  /**
   * Send email notification
   */
  private async sendEmailNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    // This would integrate with your email service (SendGrid, AWS SES, etc.)
    console.log(`[Alerting] Would send email notification:`, {
      to: channel.configuration.recipients,
      subject: `${alert.severity.toUpperCase()} Alert: ${alert.message}`,
      alert
    });
  }

  /**
   * Send webhook notification
   */
  private async sendWebhookNotification(channel: NotificationChannel, alert: Alert): Promise<void> {
    const webhookPayload = {
      alert: {
        id: alert.id,
        severity: alert.severity,
        message: alert.message,
        timestamp: alert.timestamp,
        instanceId: alert.instanceId,
        metadata: alert.metadata
      },
      rule: this.alertRules.get(alert.ruleId)
    };
    
    const response = await fetch(channel.configuration.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...channel.configuration.headers
      },
      body: JSON.stringify(webhookPayload)
    });
    
    if (!response.ok) {
      throw new Error(`Webhook responded with status: ${response.status}`);
    }
  }

  /**
   * Process alert escalations
   */
  private async processEscalations(): Promise<void> {
    for (const alert of this.activeAlerts.values()) {
      if (alert.resolved || alert.acknowledgements.length > 0) continue;
      
      const escalation = this.escalationRules.get(alert.ruleId);
      if (!escalation) continue;
      
      const alertAge = Date.now() - alert.timestamp.getTime();
      
      for (const level of escalation.levels) {
        const levelDelay = level.delayMinutes * 60 * 1000;
        
        if (alertAge >= levelDelay) {
          // Check if this level has already been escalated
          const escalationKey = `${alert.id}_level_${level.level}`;
          if (this.alertCooldowns.has(escalationKey)) continue;
          
          await this.escalateAlert(alert, level);
          this.alertCooldowns.set(escalationKey, new Date());
        }
      }
    }
  }

  /**
   * Escalate an alert to the next level
   */
  private async escalateAlert(alert: Alert, level: EscalationLevel): Promise<void> {
    const message = `🚨 ESCALATION LEVEL ${level.level}: ${alert.message}`;
    
    // Send to escalation channels
    for (const channelId of level.channels) {
      const channel = this.notificationChannels.get(channelId);
      if (channel && channel.enabled) {
        try {
          await this.sendNotification(channel, {
            ...alert,
            message,
            severity: level.severity || alert.severity
          });
        } catch (error) {
          console.error(`[Alerting] Failed to send escalation notification:`, error);
        }
      }
    }
    
    this.emit('alertEscalated', { alert, level });
  }

  /**
   * Helper methods
   */
  private generateRuleId(): string {
    return `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private generateChannelId(): string {
    return `channel_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private generateAlertId(): string {
    return `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  private isInCooldown(ruleId: string): boolean {
    const lastAlert = this.alertCooldowns.get(ruleId);
    if (!lastAlert) return false;
    
    const rule = this.alertRules.get(ruleId);
    if (!rule) return false;
    
    const cooldownEnd = new Date(lastAlert.getTime() + rule.cooldownPeriod * 60 * 1000);
    return new Date() < cooldownEnd;
  }
  
  private async getCurrentMetricValue(metric: string): Promise<number | null> {
    // This would integrate with your metrics collection system
    // For now, return null to indicate metric not available
    return null;
  }
  
  private evaluateCondition(operator: string, value: number, threshold: number): boolean {
    switch (operator) {
      case 'gt':
        return value > threshold;
      case 'lt':
        return value < threshold;
      case 'eq':
        return value === threshold;
      case 'neq':
        return value !== threshold;
      default:
        return false;
    }
  }
  
  private getSeverityColor(severity: string): string {
    switch (severity) {
      case 'critical':
        return 'danger';
      case 'high':
        return 'warning';
      case 'medium':
        return 'good';
      case 'low':
        return '#36a64f';
      default:
        return 'warning';
    }
  }
  
  private getSeverityEmoji(severity: string): string {
    switch (severity) {
      case 'critical':
        return '🚨';
      case 'high':
        return '⚠️';
      case 'medium':
        return '📢';
      case 'low':
        return 'ℹ️';
      default:
        return '📢';
    }
  }

  /**
   * Shutdown alerting service
   */
  public async shutdown(): Promise<void> {
    if (this.evaluationInterval) {
      clearInterval(this.evaluationInterval);
      this.evaluationInterval = null;
    }
    
    if (this.escalationInterval) {
      clearInterval(this.escalationInterval);
      this.escalationInterval = null;
    }
    
    this.emit('shutdown');
  }
}

export {
  AlertRule,
  AlertCondition,
  Alert,
  AlertAcknowledgement,
  NotificationChannel,
  AlertEscalation,
  EscalationLevel
};