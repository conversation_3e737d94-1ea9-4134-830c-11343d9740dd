"""
ML Infrastructure Demo Script
Demonstrates the machine learning capabilities of the GoldDaddy trading platform
"""

import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# Add parent directory to path to import ML infrastructure
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from ml_infrastructure import (
    MLService,
    create_ml_service,
    ParameterRange,
    OptimizationConfig,
    DEFAULT_MODEL_CONFIG,
    DEFAULT_FEATURE_CONFIG,
    DEFAULT_TRAINING_CONFIG
)

def generate_sample_market_data(symbol: str = "XAUUSD", days: int = 30) -> pd.DataFrame:
    """
    Generate sample market data for demonstration
    """
    # Generate realistic OHLCV data
    np.random.seed(42)  # For reproducible results
    
    # Start with a base price
    base_price = 2000.0 if symbol == "XAUUSD" else 1.1000
    
    # Generate time series
    start_date = datetime.now() - timedelta(days=days)
    dates = pd.date_range(start=start_date, periods=days*24*60, freq='1min')
    
    # Generate price movements using random walk with trend
    returns = np.random.normal(0.0001, 0.01, len(dates))  # Small positive drift
    prices = [base_price]
    
    for ret in returns[1:]:
        new_price = prices[-1] * (1 + ret)
        prices.append(new_price)
    
    # Create OHLCV data
    data = []
    for i in range(0, len(dates), 60):  # 1-hour candles
        if i + 60 <= len(dates):
            hour_prices = prices[i:i+60]
            open_price = hour_prices[0]
            close_price = hour_prices[-1]
            high_price = max(hour_prices)
            low_price = min(hour_prices)
            volume = np.random.randint(100, 1000)
            
            data.append({
                'time': dates[i],
                'open': open_price,
                'high': high_price,
                'low': low_price,
                'close': close_price,
                'volume': volume
            })
    
    df = pd.DataFrame(data)
    df.set_index('time', inplace=True)
    
    return df

def sample_strategy_function(parameters: dict, market_data: pd.DataFrame) -> dict:
    """
    Sample trading strategy for optimization demonstration
    """
    # Extract parameters
    ma_short = parameters.get('ma_short', 10)
    ma_long = parameters.get('ma_long', 20)
    rsi_oversold = parameters.get('rsi_oversold', 30)
    rsi_overbought = parameters.get('rsi_overbought', 70)
    
    # Calculate indicators
    data = market_data.copy()
    data['ma_short'] = data['close'].rolling(window=ma_short).mean()
    data['ma_long'] = data['close'].rolling(window=ma_long).mean()
    
    # Simple RSI calculation
    delta = data['close'].diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
    rs = gain / loss
    data['rsi'] = 100 - (100 / (1 + rs))
    
    # Generate signals
    data['signal'] = 0
    data.loc[(data['ma_short'] > data['ma_long']) & (data['rsi'] < rsi_overbought), 'signal'] = 1  # Buy
    data.loc[(data['ma_short'] < data['ma_long']) & (data['rsi'] > rsi_oversold), 'signal'] = -1  # Sell
    
    # Calculate returns
    data['position'] = data['signal'].shift(1)
    data['returns'] = data['close'].pct_change()
    data['strategy_returns'] = data['position'] * data['returns']
    
    # Calculate performance metrics
    total_return = (1 + data['strategy_returns'].fillna(0)).prod() - 1
    volatility = data['strategy_returns'].std() * np.sqrt(252 * 24)  # Annualized
    sharpe_ratio = (data['strategy_returns'].mean() / data['strategy_returns'].std()) * np.sqrt(252 * 24) if data['strategy_returns'].std() > 0 else 0
    
    # Calculate max drawdown
    cumulative_returns = (1 + data['strategy_returns'].fillna(0)).cumprod()
    rolling_max = cumulative_returns.expanding().max()
    drawdown = (cumulative_returns - rolling_max) / rolling_max
    max_drawdown = abs(drawdown.min())
    
    # Calculate win rate
    winning_trades = (data['strategy_returns'] > 0).sum()
    total_trades = (data['strategy_returns'] != 0).sum()
    win_rate = winning_trades / total_trades if total_trades > 0 else 0
    
    return {
        'total_return': total_return,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'win_rate': win_rate,
        'volatility': volatility,
        'total_trades': total_trades
    }

async def demo_ml_infrastructure():
    """
    Demonstrate the ML infrastructure capabilities
    """
    print("🚀 Starting ML Infrastructure Demo")
    print("=" * 50)
    
    # Initialize ML service
    print("\n1. Initializing ML Service...")
    ml_service = create_ml_service(models_path="./demo_models")
    await ml_service.start()
    print("✅ ML Service initialized")
    
    # Generate sample market data
    print("\n2. Generating sample market data...")
    market_data = generate_sample_market_data("XAUUSD", days=30)
    print(f"✅ Generated {len(market_data)} data points")
    print(f"   Date range: {market_data.index[0]} to {market_data.index[-1]}")
    print(f"   Price range: ${market_data['close'].min():.2f} - ${market_data['close'].max():.2f}")
    
    # Demonstrate feature engineering
    print("\n3. Feature Engineering Demo...")
    from ml_infrastructure.feature_engineering import FinancialFeatureEngineer
    
    feature_engineer = FinancialFeatureEngineer(DEFAULT_FEATURE_CONFIG)
    features_df = feature_engineer.extract_features(market_data)
    
    print(f"✅ Extracted {len(features_df.columns)} features")
    print(f"   Feature categories: Technical indicators, Price features, Volume features")
    print(f"   Sample features: {list(features_df.columns[:5])}")
    
    # Demonstrate strategy optimization
    print("\n4. Strategy Optimization Demo...")
    
    # Define parameter ranges for optimization
    parameter_ranges = [
        ParameterRange("ma_short", 5, 20, parameter_type="int"),
        ParameterRange("ma_long", 20, 50, parameter_type="int"),
        ParameterRange("rsi_oversold", 20, 40, parameter_type="int"),
        ParameterRange("rsi_overbought", 60, 80, parameter_type="int")
    ]
    
    # Run optimization (with reduced parameters for demo)
    optimization_config = OptimizationConfig(
        population_size=20,
        generations=10,
        parallel_evaluation=True,
        max_workers=2
    )
    
    print("   Running genetic algorithm optimization...")
    optimization_result = await ml_service.optimize_strategy_parameters(
        sample_strategy_function,
        parameter_ranges,
        market_data,
        "genetic"
    )
    
    print("✅ Strategy optimization completed")
    print(f"   Best fitness score: {optimization_result['best_fitness']:.6f}")
    print(f"   Best parameters: {optimization_result['best_parameters']}")
    print(f"   Optimization time: {optimization_result['optimization_time']:.2f} seconds")
    
    # Test the optimized strategy
    print("\n5. Testing Optimized Strategy...")
    best_params = optimization_result['best_parameters']
    backtest_results = sample_strategy_function(best_params, market_data)
    
    print("✅ Backtest completed with optimized parameters:")
    print(f"   Total Return: {backtest_results['total_return']:.2%}")
    print(f"   Sharpe Ratio: {backtest_results['sharpe_ratio']:.2f}")
    print(f"   Max Drawdown: {backtest_results['max_drawdown']:.2%}")
    print(f"   Win Rate: {backtest_results['win_rate']:.2%}")
    print(f"   Total Trades: {backtest_results['total_trades']}")
    
    # Demonstrate model registry
    print("\n6. Model Registry Demo...")
    model_summary = ml_service.get_model_performance_summary()
    print("✅ Model registry status:")
    print(f"   Total models: {model_summary['total_models']}")
    print(f"   Deployed models: {model_summary['deployed_models']}")
    print(f"   Model types: {model_summary['model_types']}")
    
    # Cleanup
    print("\n7. Cleanup...")
    await ml_service.stop()
    print("✅ ML Service stopped")
    
    print("\n" + "=" * 50)
    print("🎉 ML Infrastructure Demo completed successfully!")
    print("\nKey capabilities demonstrated:")
    print("  ✓ Feature engineering with 60+ technical indicators")
    print("  ✓ Strategy parameter optimization using genetic algorithms")
    print("  ✓ Model registry for ML model lifecycle management")
    print("  ✓ Comprehensive backtesting and performance evaluation")
    print("  ✓ Async/await support for high-performance operations")

if __name__ == "__main__":
    # Run the demo
    asyncio.run(demo_ml_infrastructure())
