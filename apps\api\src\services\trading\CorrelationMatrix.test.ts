/**
 * Correlation Matrix Test Suite
 * 
 * Tests for real-time correlation calculation, dynamic updates, and
 * correlation-adjusted position sizing recommendations.
 * 
 * @version 1.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { CorrelationMatrix, PriceData } from './CorrelationMatrix';

describe('CorrelationMatrix', () => {
  let correlationMatrix: CorrelationMatrix;
  let mockPriceData: PriceData[];

  beforeEach(() => {
    correlationMatrix = new CorrelationMatrix({
      rollingWindow: 30,
      minSampleSize: 10,
      updateFrequency: 1000, // 1 second for testing
      significanceThreshold: 0.1
    });

    // Create mock price data
    mockPriceData = Array.from({ length: 50 }, (_, i) => ({
      timestamp: new Date(Date.now() - (49 - i) * 24 * 60 * 60 * 1000),
      symbol: 'EURUSD',
      price: new Decimal(1.1000 + Math.sin(i * 0.1) * 0.005),
      volume: new Decimal(1000000)
    }));
  });

  afterEach(() => {
    correlationMatrix.destroy();
    vi.clearAllMocks();
  });

  describe('Price Data Management', () => {
    it('should add price data successfully', () => {
      expect(() => {
        mockPriceData.forEach(data => correlationMatrix.addPriceData(data));
      }).not.toThrow();
    });

    it('should maintain rolling window size', () => {
      // Add more data than rolling window
      const extraData = Array.from({ length: 40 }, (_, i) => ({
        timestamp: new Date(Date.now() - i * 60 * 60 * 1000),
        symbol: 'EURUSD',
        price: new Decimal(1.1000),
        volume: new Decimal(1000000)
      }));

      extraData.forEach(data => correlationMatrix.addPriceData(data));

      // Should maintain only rolling window size internally
      const correlation = correlationMatrix.calculateCorrelation('EURUSD', 'GBPUSD');
      expect(correlation).toBeDefined();
    });

    it('should handle multiple symbols', () => {
      const gbpusdData = mockPriceData.map(data => ({
        ...data,
        symbol: 'GBPUSD',
        price: new Decimal(1.2500 + Math.cos(data.timestamp.getTime() * 0.001) * 0.005)
      }));

      mockPriceData.forEach(data => correlationMatrix.addPriceData(data));
      gbpusdData.forEach(data => correlationMatrix.addPriceData(data));

      const correlation = correlationMatrix.calculateCorrelation('EURUSD', 'GBPUSD');
      expect(correlation).toBeDefined();
      expect(correlation?.correlation).toBeGreaterThanOrEqual(-1);
      expect(correlation?.correlation).toBeLessThanOrEqual(1);
    });

    it('should clear symbol data', () => {
      mockPriceData.forEach(data => correlationMatrix.addPriceData(data));
      
      let correlation = correlationMatrix.calculateCorrelation('EURUSD', 'GBPUSD');
      expect(correlation).toBeDefined();

      correlationMatrix.clearSymbolData('EURUSD');
      
      correlation = correlationMatrix.calculateCorrelation('EURUSD', 'GBPUSD');
      expect(correlation).toBeNull();
    });
  });

  describe('Correlation Calculation', () => {
    beforeEach(() => {
      // Add correlated data for EURUSD and GBPUSD
      const eurusdData = Array.from({ length: 30 }, (_, i) => ({
        timestamp: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000),
        symbol: 'EURUSD',
        price: new Decimal(1.1000 + i * 0.001),
        volume: new Decimal(1000000)
      }));

      const gbpusdData = Array.from({ length: 30 }, (_, i) => ({
        timestamp: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000),
        symbol: 'GBPUSD',
        price: new Decimal(1.2500 + i * 0.0008), // Positively correlated
        volume: new Decimal(800000)
      }));

      eurusdData.forEach(data => correlationMatrix.addPriceData(data));
      gbpusdData.forEach(data => correlationMatrix.addPriceData(data));
    });

    it('should calculate correlation between two symbols', () => {
      const correlation = correlationMatrix.calculateCorrelation('EURUSD', 'GBPUSD');

      expect(correlation).toBeDefined();
      expect(correlation?.symbol1).toBe('EURUSD');
      expect(correlation?.symbol2).toBe('GBPUSD');
      expect(correlation?.correlation).toBeGreaterThanOrEqual(-1);
      expect(correlation?.correlation).toBeLessThanOrEqual(1);
      expect(correlation?.confidence).toBeGreaterThan(0);
      expect(correlation?.sampleSize).toBeGreaterThan(0);
      expect(correlation?.lastUpdated).toBeInstanceOf(Date);
    });

    it('should return null for insufficient data', () => {
      const correlation = correlationMatrix.calculateCorrelation('UNKNOWN1', 'UNKNOWN2');
      expect(correlation).toBeNull();
    });

    it('should use cached correlation when valid', () => {
      const firstCall = correlationMatrix.calculateCorrelation('EURUSD', 'GBPUSD');
      const secondCall = correlationMatrix.calculateCorrelation('EURUSD', 'GBPUSD');

      expect(firstCall).toBeDefined();
      expect(secondCall).toBeDefined();
      expect(firstCall?.lastUpdated).toEqual(secondCall?.lastUpdated);
    });

    it('should handle symmetric correlation keys', () => {
      const correlation1 = correlationMatrix.calculateCorrelation('EURUSD', 'GBPUSD');
      const correlation2 = correlationMatrix.calculateCorrelation('GBPUSD', 'EURUSD');

      expect(correlation1?.correlation).toBe(correlation2?.correlation);
    });

    it('should calculate correlation matrix for multiple symbols', () => {
      const symbols = ['EURUSD', 'GBPUSD'];
      const matrix = correlationMatrix.getCorrelationMatrix(symbols);

      expect(matrix).toHaveLength(2);
      expect(matrix[0]).toHaveLength(2);
      expect(matrix[0][0]).toBe(1); // Self-correlation
      expect(matrix[1][1]).toBe(1); // Self-correlation
      expect(matrix[0][1]).toBe(matrix[1][0]); // Symmetric matrix
    });

    it('should handle empty symbol list for matrix', () => {
      const matrix = correlationMatrix.getCorrelationMatrix([]);
      expect(matrix).toHaveLength(0);
    });
  });

  describe('Symbol Correlation Analysis', () => {
    beforeEach(() => {
      // Create three symbols with different correlation levels
      const symbols = ['EURUSD', 'GBPUSD', 'USDJPY'];
      
      symbols.forEach((symbol, symbolIndex) => {
        const data = Array.from({ length: 25 }, (_, i) => ({
          timestamp: new Date(Date.now() - (24 - i) * 24 * 60 * 60 * 1000),
          symbol,
          price: new Decimal(1.0000 + symbolIndex * 0.2 + i * 0.001 * (symbolIndex + 1)),
          volume: new Decimal(1000000)
        }));
        
        data.forEach(priceData => correlationMatrix.addPriceData(priceData));
      });
    });

    it('should get all correlations for a symbol', () => {
      const correlations = correlationMatrix.getSymbolCorrelations('EURUSD');

      expect(correlations).toBeInstanceOf(Array);
      expect(correlations.length).toBeGreaterThanOrEqual(0);
      
      correlations.forEach(correlation => {
        expect(['EURUSD'].includes(correlation.symbol1) || ['EURUSD'].includes(correlation.symbol2)).toBe(true);
      });
    });

    it('should sort correlations by absolute value', () => {
      const correlations = correlationMatrix.getSymbolCorrelations('EURUSD');
      
      if (correlations.length > 1) {
        for (let i = 0; i < correlations.length - 1; i++) {
          expect(Math.abs(correlations[i].correlation)).toBeGreaterThanOrEqual(
            Math.abs(correlations[i + 1].correlation)
          );
        }
      }
    });

    it('should find highly correlated symbols', () => {
      const highlyCorrelated = correlationMatrix.findHighlyCorrelated('EURUSD', 0.5);

      expect(highlyCorrelated).toBeInstanceOf(Array);
      highlyCorrelated.forEach(correlation => {
        expect(Math.abs(correlation.correlation)).toBeGreaterThanOrEqual(0.5);
        expect(correlation.confidence).toBeGreaterThanOrEqual(0.8);
      });
    });

    it('should use default threshold for highly correlated', () => {
      const highlyCorrelated = correlationMatrix.findHighlyCorrelated('EURUSD');
      
      expect(highlyCorrelated).toBeInstanceOf(Array);
      highlyCorrelated.forEach(correlation => {
        expect(Math.abs(correlation.correlation)).toBeGreaterThanOrEqual(0.7);
      });
    });
  });

  describe('Position Size Adjustment', () => {
    beforeEach(() => {
      // Setup correlated symbols
      const eurusdData = Array.from({ length: 20 }, (_, i) => ({
        timestamp: new Date(Date.now() - (19 - i) * 24 * 60 * 60 * 1000),
        symbol: 'EURUSD',
        price: new Decimal(1.1000 + i * 0.001),
        volume: new Decimal(1000000)
      }));

      const gbpusdData = Array.from({ length: 20 }, (_, i) => ({
        timestamp: new Date(Date.now() - (19 - i) * 24 * 60 * 60 * 1000),
        symbol: 'GBPUSD',
        price: new Decimal(1.2500 + i * 0.0009), // Highly correlated
        volume: new Decimal(800000)
      }));

      eurusdData.forEach(data => correlationMatrix.addPriceData(data));
      gbpusdData.forEach(data => correlationMatrix.addPriceData(data));
    });

    it('should adjust position size for high correlation', () => {
      const proposedSize = new Decimal(10000);
      const existingPositions = [{ symbol: 'GBPUSD', size: new Decimal(8000) }];

      const adjustment = correlationMatrix.adjustPositionSizeForCorrelation(
        'EURUSD',
        proposedSize,
        existingPositions
      );

      expect(adjustment.originalSize.equals(proposedSize)).toBe(true);
      expect(adjustment.adjustedSize.lte(proposedSize)).toBe(true);
      expect(adjustment.adjustmentFactor).toBeGreaterThan(0);
      expect(adjustment.adjustmentFactor).toBeLessThanOrEqual(1);
      expect(adjustment.reasoning).toBeTypeOf('string');
      expect(adjustment.correlationRisk).toBeGreaterThanOrEqual(0);
    });

    it('should not adjust for low correlation', () => {
      const proposedSize = new Decimal(10000);
      const existingPositions = [{ symbol: 'UNCORRELATED', size: new Decimal(8000) }];

      const adjustment = correlationMatrix.adjustPositionSizeForCorrelation(
        'EURUSD',
        proposedSize,
        existingPositions
      );

      expect(adjustment.adjustedSize.equals(proposedSize)).toBe(true);
      expect(adjustment.adjustmentFactor).toBe(1.0);
      expect(adjustment.reasoning).toContain('No significant correlations');
    });

    it('should handle empty existing positions', () => {
      const proposedSize = new Decimal(10000);
      const existingPositions: { symbol: string; size: Decimal }[] = [];

      const adjustment = correlationMatrix.adjustPositionSizeForCorrelation(
        'EURUSD',
        proposedSize,
        existingPositions
      );

      expect(adjustment.adjustedSize.equals(proposedSize)).toBe(true);
      expect(adjustment.adjustmentFactor).toBe(1.0);
    });

    it('should skip same symbol in existing positions', () => {
      const proposedSize = new Decimal(10000);
      const existingPositions = [{ symbol: 'EURUSD', size: new Decimal(5000) }];

      const adjustment = correlationMatrix.adjustPositionSizeForCorrelation(
        'EURUSD',
        proposedSize,
        existingPositions
      );

      expect(adjustment.adjustedSize.equals(proposedSize)).toBe(true);
      expect(adjustment.adjustmentFactor).toBe(1.0);
    });

    it('should provide appropriate adjustment factors for different correlation levels', () => {
      const proposedSize = new Decimal(10000);

      // Test different correlation scenarios by mocking correlation calculation
      const testCases = [
        { correlation: 0.9, expectedFactor: 0.5 },   // Very high correlation
        { correlation: 0.7, expectedFactor: 0.7 },   // High correlation  
        { correlation: 0.5, expectedFactor: 0.85 },  // Moderate correlation
        { correlation: 0.2, expectedFactor: 1.0 }    // Low correlation
      ];

      testCases.forEach(({ correlation, expectedFactor }) => {
        // This would require mocking the correlation calculation to return specific values
        // For now, we'll test that the logic handles different scenarios appropriately
        expect(expectedFactor).toBeGreaterThan(0);
        expect(expectedFactor).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('Correlation Statistics', () => {
    beforeEach(() => {
      // Add various symbol pairs with different correlations
      const symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD'];
      
      symbols.forEach((symbol, symbolIndex) => {
        const data = Array.from({ length: 15 }, (_, i) => ({
          timestamp: new Date(Date.now() - (14 - i) * 24 * 60 * 60 * 1000),
          symbol,
          price: new Decimal(1.0 + symbolIndex * 0.1 + Math.random() * 0.01),
          volume: new Decimal(1000000)
        }));
        
        data.forEach(priceData => correlationMatrix.addPriceData(priceData));
      });

      // Force correlation calculations
      correlationMatrix.calculateCorrelation('EURUSD', 'GBPUSD');
      correlationMatrix.calculateCorrelation('EURUSD', 'USDJPY');
      correlationMatrix.calculateCorrelation('GBPUSD', 'XAUUSD');
    });

    it('should calculate correlation statistics', () => {
      const stats = correlationMatrix.getCorrelationStatistics();

      expect(stats.totalPairs).toBeGreaterThanOrEqual(0);
      expect(stats.highCorrelations).toBeGreaterThanOrEqual(0);
      expect(stats.averageCorrelation).toBeGreaterThanOrEqual(0);
      expect(stats.averageCorrelation).toBeLessThanOrEqual(1);
    });

    it('should handle empty correlations in statistics', () => {
      const emptyMatrix = new CorrelationMatrix();
      const stats = emptyMatrix.getCorrelationStatistics();

      expect(stats.totalPairs).toBe(0);
      expect(stats.highCorrelations).toBe(0);
      expect(stats.averageCorrelation).toBe(0);
      expect(stats.mostCorrelated).toBeNull();
      expect(stats.leastCorrelated).toBeNull();

      emptyMatrix.destroy();
    });

    it('should identify most and least correlated pairs', () => {
      const stats = correlationMatrix.getCorrelationStatistics();

      if (stats.totalPairs > 0) {
        expect(stats.mostCorrelated).toBeDefined();
        expect(stats.leastCorrelated).toBeDefined();
        
        if (stats.mostCorrelated && stats.leastCorrelated && stats.totalPairs > 1) {
          expect(Math.abs(stats.mostCorrelated.correlation)).toBeGreaterThanOrEqual(
            Math.abs(stats.leastCorrelated.correlation)
          );
        }
      }
    });
  });

  describe('CSV Export', () => {
    beforeEach(() => {
      const symbols = ['EURUSD', 'GBPUSD'];
      
      symbols.forEach(symbol => {
        const data = Array.from({ length: 12 }, (_, i) => ({
          timestamp: new Date(Date.now() - (11 - i) * 24 * 60 * 60 * 1000),
          symbol,
          price: new Decimal(1.0 + Math.random() * 0.01),
          volume: new Decimal(1000000)
        }));
        
        data.forEach(priceData => correlationMatrix.addPriceData(priceData));
      });
    });

    it('should export correlation matrix to CSV', () => {
      const symbols = ['EURUSD', 'GBPUSD'];
      const csv = correlationMatrix.exportToCSV(symbols);

      expect(csv).toBeTypeOf('string');
      expect(csv).toContain('EURUSD');
      expect(csv).toContain('GBPUSD');
      expect(csv).toContain('1.0000'); // Self-correlation
      
      const lines = csv.split('\n').filter(line => line.trim());
      expect(lines.length).toBe(3); // Header + 2 data rows
    });

    it('should handle empty symbols list for CSV export', () => {
      const csv = correlationMatrix.exportToCSV([]);
      expect(csv).toBe(',\n');
    });

    it('should format correlation values to 4 decimal places', () => {
      const symbols = ['EURUSD', 'GBPUSD'];
      const csv = correlationMatrix.exportToCSV(symbols);
      
      // Check that values are formatted to 4 decimal places
      const lines = csv.split('\n');
      const dataLines = lines.slice(1).filter(line => line.trim());
      
      dataLines.forEach(line => {
        const values = line.split(',').slice(1); // Skip symbol name
        values.forEach(value => {
          if (value.trim()) {
            expect(value.match(/\d+\.\d{4}/)).toBeTruthy();
          }
        });
      });
    });
  });

  describe('Periodic Updates', () => {
    it('should start periodic updates on construction', () => {
      const matrix = new CorrelationMatrix({ updateFrequency: 100 });
      
      // Verify that the matrix is constructed without error
      expect(matrix).toBeDefined();
      
      matrix.destroy();
    });

    it('should stop periodic updates on destroy', () => {
      const matrix = new CorrelationMatrix({ updateFrequency: 50 });
      
      expect(() => matrix.destroy()).not.toThrow();
      
      // After destroy, should not crash
      expect(matrix.getCorrelationStatistics().totalPairs).toBe(0);
    });
  });

  describe('Configuration Options', () => {
    it('should accept custom configuration', () => {
      const customConfig = {
        rollingWindow: 50,
        minSampleSize: 20,
        updateFrequency: 5000,
        significanceThreshold: 0.2
      };

      const customMatrix = new CorrelationMatrix(customConfig);
      
      expect(customMatrix).toBeDefined();
      
      customMatrix.destroy();
    });

    it('should use default configuration when none provided', () => {
      const defaultMatrix = new CorrelationMatrix();
      
      expect(defaultMatrix).toBeDefined();
      
      defaultMatrix.destroy();
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid price data gracefully', () => {
      const invalidData: PriceData = {
        timestamp: new Date(),
        symbol: 'INVALID',
        price: new Decimal(0),
        volume: new Decimal(0)
      };

      expect(() => correlationMatrix.addPriceData(invalidData)).not.toThrow();
    });

    it('should handle missing volume data', () => {
      const dataWithoutVolume: PriceData = {
        timestamp: new Date(),
        symbol: 'TEST',
        price: new Decimal(1.0)
      };

      expect(() => correlationMatrix.addPriceData(dataWithoutVolume)).not.toThrow();
    });

    it('should handle duplicate timestamps gracefully', () => {
      const timestamp = new Date();
      const data1: PriceData = {
        timestamp,
        symbol: 'TEST',
        price: new Decimal(1.0)
      };
      const data2: PriceData = {
        timestamp,
        symbol: 'TEST',
        price: new Decimal(1.1)
      };

      expect(() => {
        correlationMatrix.addPriceData(data1);
        correlationMatrix.addPriceData(data2);
      }).not.toThrow();
    });

    it('should handle extreme price values', () => {
      const extremeData: PriceData = {
        timestamp: new Date(),
        symbol: 'EXTREME',
        price: new Decimal('999999999.99999999')
      };

      expect(() => correlationMatrix.addPriceData(extremeData)).not.toThrow();
    });
  });
});