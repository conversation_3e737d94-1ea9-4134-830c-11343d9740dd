/**
 * Strategy Benchmarking Service
 * 
 * Provides peer strategy comparison and benchmarking functionality
 * with privacy-compliant data aggregation and plain English explanations.
 */

import { 
  PerformanceMetrics, 
  StrategyType, 
  UserExperienceLevel,
  RiskTolerance,
  MetricType 
} from '@golddaddy/types';

export interface BenchmarkData {
  metricType: MetricType;
  userValue: number;
  percentile: number;
  peerAverage: number;
  peerMedian: number;
  peerBest: number;
  peerWorst: number;
  sampleSize: number;
  plainEnglishSummary: string;
  comparisonCategory: 'top_performer' | 'above_average' | 'average' | 'below_average' | 'needs_improvement';
}

export interface PeerComparisonResult {
  strategyId: string;
  strategyType: StrategyType;
  overallRanking: {
    percentile: number;
    category: 'top_10' | 'top_25' | 'top_50' | 'bottom_50' | 'bottom_25';
    plainEnglishSummary: string;
  };
  metricBenchmarks: BenchmarkData[];
  marketRegimePerformance: {
    trending: BenchmarkData;
    ranging: BenchmarkData;
    volatile: BenchmarkData;
  };
  recommendations: string[];
  confidenceLevel: number;
}

export interface BenchmarkFilters {
  strategyType?: StrategyType;
  marketRegime?: 'trending' | 'ranging' | 'volatile' | 'any';
  timeframe?: '30d' | '90d' | '1y' | 'all';
  riskTolerance?: RiskTolerance;
  minimumTrades?: number;
}

export class StrategyBenchmarkingService {
  
  /**
   * Get peer comparison for a strategy
   */
  public async getPeerComparison(
    strategyId: string,
    userMetrics: PerformanceMetrics,
    userExperience: UserExperienceLevel,
    filters: BenchmarkFilters = {}
  ): Promise<PeerComparisonResult> {
    
    // Get peer data (in production, this would query aggregated database)
    const peerData = await this.fetchPeerData(strategyId, filters);
    
    // Calculate metric benchmarks
    const metricBenchmarks = await this.calculateMetricBenchmarks(
      userMetrics,
      peerData,
      userExperience
    );
    
    // Calculate overall ranking
    const overallRanking = this.calculateOverallRanking(
      userMetrics,
      peerData,
      userExperience
    );
    
    // Generate market regime performance
    const marketRegimePerformance = await this.calculateMarketRegimePerformance(
      userMetrics,
      peerData,
      userExperience
    );
    
    // Generate recommendations
    const recommendations = this.generateBenchmarkRecommendations(
      metricBenchmarks,
      overallRanking,
      userExperience
    );
    
    return {
      strategyId,
      strategyType: peerData.strategyType,
      overallRanking,
      metricBenchmarks,
      marketRegimePerformance,
      recommendations,
      confidenceLevel: this.calculateConfidenceLevel(peerData.sampleSize),
    };
  }
  
  /**
   * Fetch anonymized peer data for comparison
   */
  private async fetchPeerData(
    strategyId: string,
    filters: BenchmarkFilters
  ): Promise<{
    strategyType: StrategyType;
    sampleSize: number;
    metrics: {
      [key in MetricType]: {
        values: number[];
        average: number;
        median: number;
        percentiles: number[];
      };
    };
    marketRegimes: {
      [regime: string]: {
        [key in MetricType]: {
          values: number[];
          average: number;
          median: number;
        };
      };
    };
  }> {
    
    // Mock data generation for demonstration
    // In production, this would query aggregated peer data from database
    const sampleSize = Math.floor(Math.random() * 450) + 50; // 50-500 peers
    
    return {
      strategyType: 'momentum',
      sampleSize,
      metrics: {
        win_rate: {
          values: this.generateMockDistribution(0.58, 0.12, sampleSize),
          average: 0.58,
          median: 0.59,
          percentiles: [0.35, 0.48, 0.59, 0.68, 0.82],
        },
        profit_factor: {
          values: this.generateMockDistribution(1.4, 0.3, sampleSize),
          average: 1.4,
          median: 1.35,
          percentiles: [0.8, 1.1, 1.35, 1.7, 2.4],
        },
        sharpe_ratio: {
          values: this.generateMockDistribution(1.2, 0.4, sampleSize),
          average: 1.2,
          median: 1.15,
          percentiles: [0.3, 0.8, 1.15, 1.6, 2.3],
        },
        max_drawdown: {
          values: this.generateMockDistribution(0.15, 0.08, sampleSize),
          average: 0.15,
          median: 0.14,
          percentiles: [0.04, 0.08, 0.14, 0.22, 0.35],
        },
      } as any,
      marketRegimes: {
        trending: {
          win_rate: { values: [], average: 0.62, median: 0.63 },
          profit_factor: { values: [], average: 1.6, median: 1.55 },
          sharpe_ratio: { values: [], average: 1.4, median: 1.35 },
          max_drawdown: { values: [], average: 0.12, median: 0.11 },
        },
        ranging: {
          win_rate: { values: [], average: 0.54, median: 0.55 },
          profit_factor: { values: [], average: 1.2, median: 1.15 },
          sharpe_ratio: { values: [], average: 1.0, median: 0.95 },
          max_drawdown: { values: [], average: 0.18, median: 0.17 },
        },
        volatile: {
          win_rate: { values: [], average: 0.56, median: 0.57 },
          profit_factor: { values: [], average: 1.3, median: 1.25 },
          sharpe_ratio: { values: [], average: 1.1, median: 1.05 },
          max_drawdown: { values: [], average: 0.20, median: 0.19 },
        },
      } as any,
    };
  }
  
  /**
   * Calculate metric benchmarks against peers
   */
  private async calculateMetricBenchmarks(
    userMetrics: PerformanceMetrics,
    peerData: any,
    userExperience: UserExperienceLevel
  ): Promise<BenchmarkData[]> {
    
    const benchmarks: BenchmarkData[] = [];
    const metricTypes: MetricType[] = ['win_rate', 'profit_factor', 'sharpe_ratio', 'max_drawdown'];
    
    for (const metricType of metricTypes) {
      const userValue = this.getUserMetricValue(userMetrics, metricType);
      const peerMetric = peerData.metrics[metricType];
      
      // Calculate percentile ranking
      const percentile = this.calculatePercentile(userValue, peerMetric.values, metricType);
      
      const benchmark: BenchmarkData = {
        metricType,
        userValue,
        percentile,
        peerAverage: peerMetric.average,
        peerMedian: peerMetric.median,
        peerBest: metricType === 'max_drawdown' 
          ? Math.min(...peerMetric.values) 
          : Math.max(...peerMetric.values),
        peerWorst: metricType === 'max_drawdown' 
          ? Math.max(...peerMetric.values) 
          : Math.min(...peerMetric.values),
        sampleSize: peerData.sampleSize,
        comparisonCategory: this.getComparisonCategory(percentile, metricType),
        plainEnglishSummary: this.generateBenchmarkSummary(
          metricType,
          userValue,
          percentile,
          peerMetric,
          userExperience
        ),
      };
      
      benchmarks.push(benchmark);
    }
    
    return benchmarks;
  }
  
  /**
   * Calculate overall strategy ranking
   */
  private calculateOverallRanking(
    userMetrics: PerformanceMetrics,
    peerData: any,
    userExperience: UserExperienceLevel
  ): PeerComparisonResult['overallRanking'] {
    
    // Weighted composite score calculation
    const weights = {
      win_rate: 0.25,
      profit_factor: 0.25,
      sharpe_ratio: 0.30,
      max_drawdown: 0.20, // Negative weight (lower is better)
    };
    
    let compositeScore = 0;
    let totalWeight = 0;
    
    Object.entries(weights).forEach(([metricType, weight]) => {
      const userValue = this.getUserMetricValue(userMetrics, metricType as MetricType);
      const peerMetric = peerData.metrics[metricType as MetricType];
      const percentile = this.calculatePercentile(userValue, peerMetric.values, metricType as MetricType);
      
      if (metricType === 'max_drawdown') {
        // For drawdown, lower percentile is better
        compositeScore += (100 - percentile) * weight;
      } else {
        compositeScore += percentile * weight;
      }
      totalWeight += weight;
    });
    
    const overallPercentile = compositeScore / totalWeight;
    
    const category = overallPercentile >= 90 ? 'top_10' :
                    overallPercentile >= 75 ? 'top_25' :
                    overallPercentile >= 50 ? 'top_50' :
                    overallPercentile >= 25 ? 'bottom_50' : 'bottom_25';
    
    return {
      percentile: Math.round(overallPercentile),
      category,
      plainEnglishSummary: this.generateOverallRankingSummary(
        overallPercentile,
        category,
        userExperience,
        peerData.sampleSize
      ),
    };
  }
  
  /**
   * Calculate market regime performance
   */
  private async calculateMarketRegimePerformance(
    userMetrics: PerformanceMetrics,
    peerData: any,
    userExperience: UserExperienceLevel
  ): Promise<PeerComparisonResult['marketRegimePerformance']> {
    
    const regimes = ['trending', 'ranging', 'volatile'] as const;
    const result = {} as PeerComparisonResult['marketRegimePerformance'];
    
    for (const regime of regimes) {
      // For demo, use win_rate as the primary metric for regime comparison
      const userValue = this.getUserMetricValue(userMetrics, 'win_rate');
      const regimeData = peerData.marketRegimes[regime];
      
      // Mock percentile calculation for regime
      const mockPercentile = Math.max(10, Math.min(90, 
        userValue > regimeData.win_rate.average ? 70 + Math.random() * 20 : 30 + Math.random() * 40
      ));
      
      result[regime] = {
        metricType: 'win_rate',
        userValue,
        percentile: mockPercentile,
        peerAverage: regimeData.win_rate.average,
        peerMedian: regimeData.win_rate.median,
        peerBest: regimeData.win_rate.average + 0.2,
        peerWorst: regimeData.win_rate.average - 0.2,
        sampleSize: Math.floor(peerData.sampleSize * 0.7), // Assume 70% have regime data
        comparisonCategory: this.getComparisonCategory(mockPercentile, 'win_rate'),
        plainEnglishSummary: this.generateRegimeSummary(
          regime,
          mockPercentile,
          userExperience
        ),
      };
    }
    
    return result;
  }
  
  /**
   * Generate benchmark recommendations
   */
  private generateBenchmarkRecommendations(
    metricBenchmarks: BenchmarkData[],
    overallRanking: PeerComparisonResult['overallRanking'],
    userExperience: UserExperienceLevel
  ): string[] {
    
    const recommendations: string[] = [];
    
    // Overall performance recommendations
    if (overallRanking.percentile >= 75) {
      if (userExperience === 'beginner') {
        recommendations.push('🎉 Great job! Your strategy is performing better than most traders');
        recommendations.push('📈 Keep doing what you\'re doing - your approach is working well');
      } else {
        recommendations.push('🏆 Strong performance - you\'re in the top quartile of similar strategies');
        recommendations.push('🔧 Consider optimizing parameters to push into top 10% of performers');
      }
    } else if (overallRanking.percentile >= 50) {
      recommendations.push('📊 You\'re performing around average - there\'s room for improvement');
      recommendations.push('🎯 Focus on the metrics where you\'re below peer averages');
    } else {
      recommendations.push('⚠️ Performance below peer average - consider strategy adjustments');
      recommendations.push('📚 Review top performers\' approaches for improvement ideas');
    }
    
    // Metric-specific recommendations
    const weakMetrics = metricBenchmarks.filter(b => 
      b.comparisonCategory === 'below_average' || b.comparisonCategory === 'needs_improvement'
    );
    
    weakMetrics.forEach(metric => {
      switch (metric.metricType) {
        case 'win_rate':
          recommendations.push('🎯 Win rate improvement: Review entry timing and signal quality');
          break;
        case 'profit_factor':
          recommendations.push('💰 Profit factor boost: Consider tighter stop losses or profit targets');
          break;
        case 'sharpe_ratio':
          recommendations.push('⚖️ Risk-adjusted returns: Reduce position sizes during volatile periods');
          break;
        case 'max_drawdown':
          recommendations.push('🛡️ Drawdown control: Implement stricter risk management rules');
          break;
      }
    });
    
    // Limit to top 5 recommendations
    return recommendations.slice(0, 5);
  }
  
  /**
   * Helper methods
   */
  
  private getUserMetricValue(metrics: PerformanceMetrics, metricType: MetricType): number {
    switch (metricType) {
      case 'win_rate': return metrics.winRate;
      case 'profit_factor': return metrics.profitFactor;
      case 'sharpe_ratio': return metrics.sharpeRatio;
      case 'max_drawdown': return metrics.maxDrawdown;
      default: return 0;
    }
  }
  
  private calculatePercentile(userValue: number, peerValues: number[], metricType: MetricType): number {
    const sortedValues = [...peerValues].sort((a, b) => a - b);
    
    let rank: number;
    if (metricType === 'max_drawdown') {
      // For drawdown, lower is better, so reverse the ranking
      rank = sortedValues.filter(value => value > userValue).length;
    } else {
      // For other metrics, higher is better
      rank = sortedValues.filter(value => value < userValue).length;
    }
    
    return Math.round((rank / sortedValues.length) * 100);
  }
  
  private getComparisonCategory(percentile: number, metricType: MetricType): BenchmarkData['comparisonCategory'] {
    // For max_drawdown, invert the percentile logic since lower is better
    const effectivePercentile = metricType === 'max_drawdown' ? 100 - percentile : percentile;
    
    if (effectivePercentile >= 80) return 'top_performer';
    if (effectivePercentile >= 60) return 'above_average';
    if (effectivePercentile >= 40) return 'average';
    if (effectivePercentile >= 20) return 'below_average';
    return 'needs_improvement';
  }
  
  private generateBenchmarkSummary(
    metricType: MetricType,
    userValue: number,
    percentile: number,
    peerMetric: any,
    userExperience: UserExperienceLevel
  ): string {
    
    const effectivePercentile = metricType === 'max_drawdown' ? 100 - percentile : percentile;
    const metricName = this.getMetricDisplayName(metricType);
    const formattedValue = this.formatMetricValue(metricType, userValue);
    const formattedAverage = this.formatMetricValue(metricType, peerMetric.average);
    
    if (userExperience === 'beginner') {
      if (effectivePercentile >= 70) {
        return `Your ${metricName} (${formattedValue}) is better than ${effectivePercentile}% of similar strategies! 🎉`;
      } else if (effectivePercentile >= 30) {
        return `Your ${metricName} (${formattedValue}) is around average. The typical trader gets ${formattedAverage}.`;
      } else {
        return `Your ${metricName} (${formattedValue}) has room for improvement. Most traders get ${formattedAverage}.`;
      }
    } else if (userExperience === 'intermediate') {
      return `Your ${metricName} ranks in the ${effectivePercentile}th percentile (${formattedValue} vs peer average of ${formattedAverage}).`;
    } else {
      const comparison = userValue > peerMetric.average ? 'outperforming' : 'underperforming';
      const deviation = Math.abs((userValue - peerMetric.average) / peerMetric.average * 100).toFixed(1);
      return `${metricName}: ${effectivePercentile}th percentile, ${comparison} peer average by ${deviation}% (${formattedValue} vs ${formattedAverage}).`;
    }
  }
  
  private generateOverallRankingSummary(
    percentile: number,
    category: string,
    userExperience: UserExperienceLevel,
    sampleSize: number
  ): string {
    
    if (userExperience === 'beginner') {
      if (percentile >= 75) {
        return `Excellent! Your strategy performs better than ${percentile}% of traders. Keep it up! 🏆`;
      } else if (percentile >= 50) {
        return `You're doing okay - performing better than ${percentile}% of traders. There's room to improve! 📈`;
      } else {
        return `Your strategy needs some work. You're performing better than ${percentile}% of traders. Don't worry, everyone starts somewhere! 💪`;
      }
    } else {
      return `Overall ranking: ${percentile}th percentile among ${sampleSize} similar strategies. ${category.replace('_', ' ').toUpperCase()} performance tier.`;
    }
  }
  
  private generateRegimeSummary(
    regime: string,
    percentile: number,
    userExperience: UserExperienceLevel
  ): string {
    
    const regimeLabels = {
      trending: 'trending markets',
      ranging: 'sideways markets', 
      volatile: 'volatile markets'
    };
    
    const regimeLabel = regimeLabels[regime as keyof typeof regimeLabels];
    
    if (userExperience === 'beginner') {
      if (percentile >= 70) {
        return `Great performance in ${regimeLabel}! You rank better than ${percentile}% of traders.`;
      } else {
        return `Room for improvement in ${regimeLabel}. You rank better than ${percentile}% of traders.`;
      }
    } else {
      return `${regimeLabel}: ${percentile}th percentile performance vs peers`;
    }
  }
  
  private getMetricDisplayName(metricType: MetricType): string {
    switch (metricType) {
      case 'win_rate': return 'win rate';
      case 'profit_factor': return 'profit factor';
      case 'sharpe_ratio': return 'profit smoothness';
      case 'max_drawdown': return 'worst loss streak';
      default: return metricType.replace(/_/g, ' ');
    }
  }
  
  private formatMetricValue(metricType: MetricType, value: number): string {
    switch (metricType) {
      case 'win_rate':
      case 'max_drawdown':
        return `${(value * 100).toFixed(1)}%`;
      case 'profit_factor':
      case 'sharpe_ratio':
        return value.toFixed(2);
      default:
        return value.toFixed(2);
    }
  }
  
  private generateMockDistribution(mean: number, stdDev: number, size: number): number[] {
    const values: number[] = [];
    for (let i = 0; i < size; i++) {
      // Simple normal distribution approximation using Box-Muller transform
      const u1 = Math.random();
      const u2 = Math.random();
      const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
      values.push(Math.max(0, mean + stdDev * z0));
    }
    return values;
  }
  
  private calculateConfidenceLevel(sampleSize: number): number {
    // Higher sample size = higher confidence
    if (sampleSize >= 200) return 95;
    if (sampleSize >= 100) return 85;
    if (sampleSize >= 50) return 75;
    return 60;
  }
}