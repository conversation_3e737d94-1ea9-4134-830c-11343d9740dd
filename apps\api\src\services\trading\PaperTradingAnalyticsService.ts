import {
  PaperTrade,
  PaperTradingAnalytics,
  VirtualPortfolio,
  PaperTradingSession
} from '@golddaddy/types';
import Decimal from 'decimal.js';

/**
 * Performance metrics calculation result
 */
export interface PerformanceMetrics {
  // Return metrics
  totalReturn: number;
  totalReturnPercentage: number;
  annualizedReturn: number;
  
  // Risk metrics
  volatility: number;
  sharpeRatio: number;
  sortinoRatio: number;
  calmarRatio: number;
  maxDrawdown: number;
  currentDrawdown: number;
  
  // Trading metrics
  winRate: number;
  lossRate: number;
  profitFactor: number;
  averageWin: number;
  averageLoss: number;
  averageTrade: number;
  largestWin: number;
  largestLoss: number;
  
  // Consistency metrics
  consecutiveWins: number;
  consecutiveLosses: number;
  expectancy: number;
  
  // Efficiency metrics
  tradingFrequency: number;
  holdingPeriod: number;
  turnoverRate: number;
}

/**
 * Backtesting vs Paper Trading comparison
 */
export interface BacktestComparison {
  performanceGap: number;
  slippageImpact: number;
  executionCosts: number;
  realismAdjustment: number;
  
  metricComparisons: {
    returnDifference: number;
    riskDifference: number;
    winRateDifference: number;
    drawdownDifference: number;
  };
  
  insights: {
    executionQuality: string;
    marketImpact: string;
    behavioralFactors: string;
    recommendations: string[];
  };
}

/**
 * Learning insights from performance analysis
 */
export interface LearningInsights {
  strengthAreas: string[];
  improvementAreas: string[];
  specificRecommendations: string[];
  skillDevelopment: {
    riskManagement: number; // 0-100 score
    entryTiming: number;
    exitTiming: number;
    positionSizing: number;
    emotionalControl: number;
  };
  nextMilestones: string[];
}

/**
 * Detailed performance visualization data
 */
export interface PerformanceVisualizationData {
  equityCurve: Array<{ date: Date; value: number; }>;
  drawdownChart: Array<{ date: Date; drawdown: number; }>;
  monthlyReturns: Array<{ month: string; return: number; }>;
  tradeDistribution: {
    winDistribution: number[];
    lossDistribution: number[];
    holdingPeriods: number[];
  };
  riskMetricsTimeSeries: Array<{
    date: Date;
    sharpe: number;
    volatility: number;
    maxDrawdown: number;
  }>;
}

/**
 * Paper Trading Analytics Service
 * 
 * Comprehensive performance analysis and comparison system for paper trading:
 * - Calculates detailed performance metrics (Sharpe, Sortino, Calmar ratios)
 * - Compares paper trading results with backtesting
 * - Generates learning insights from performance differences
 * - Creates visualization data for frontend components
 * - Provides slippage impact analysis and reporting
 */
export class PaperTradingAnalyticsService {
  constructor() {}

  /**
   * Calculate comprehensive performance metrics for a paper trading session
   */
  async calculatePerformanceMetrics(
    sessionId: string,
    trades: PaperTrade[],
    portfolio: VirtualPortfolio
  ): Promise<PerformanceMetrics> {
    if (trades.length === 0) {
      return this.getEmptyMetrics();
    }

    const closedTrades = trades.filter(t => t.profit !== undefined && t.closeTime);
    const sessionDurationDays = this.calculateSessionDuration(trades);
    
    // Return metrics
    const totalReturn = closedTrades.reduce((sum, t) => sum + (t.profit || 0), 0);
    const initialBalance = portfolio.initialBalance;
    const totalReturnPercentage = (totalReturn / initialBalance) * 100;
    const annualizedReturn = sessionDurationDays > 0 
      ? (totalReturnPercentage * 365) / sessionDurationDays
      : 0;

    // Risk metrics
    const dailyReturns = this.calculateDailyReturns(trades, portfolio);
    const volatility = this.calculateVolatility(dailyReturns) * Math.sqrt(252); // Annualized
    const riskFreeRate = 0.02; // Assume 2% risk-free rate
    const sharpeRatio = volatility > 0 ? (annualizedReturn - riskFreeRate) / volatility : 0;
    const sortinoRatio = this.calculateSortinoRatio(dailyReturns, riskFreeRate);
    const maxDrawdown = this.calculateMaxDrawdown(trades);
    const calmarRatio = maxDrawdown > 0 ? annualizedReturn / maxDrawdown : 0;
    const currentDrawdown = this.calculateCurrentDrawdown(trades);

    // Trading metrics
    const winningTrades = closedTrades.filter(t => (t.profit || 0) > 0);
    const losingTrades = closedTrades.filter(t => (t.profit || 0) < 0);
    
    const winRate = closedTrades.length > 0 ? winningTrades.length / closedTrades.length : 0;
    const lossRate = 1 - winRate;
    
    const totalWins = winningTrades.reduce((sum, t) => sum + (t.profit || 0), 0);
    const totalLosses = Math.abs(losingTrades.reduce((sum, t) => sum + (t.profit || 0), 0));
    const profitFactor = totalLosses > 0 ? totalWins / totalLosses : 0;
    
    const averageWin = winningTrades.length > 0 ? totalWins / winningTrades.length : 0;
    const averageLoss = losingTrades.length > 0 ? totalLosses / losingTrades.length : 0;
    const averageTrade = closedTrades.length > 0 ? totalReturn / closedTrades.length : 0;
    
    const largestWin = winningTrades.length > 0 
      ? Math.max(...winningTrades.map(t => t.profit || 0))
      : 0;
    const largestLoss = losingTrades.length > 0 
      ? Math.min(...losingTrades.map(t => t.profit || 0))
      : 0;

    // Consistency metrics
    const consecutiveWins = this.calculateConsecutiveWins(closedTrades);
    const consecutiveLosses = this.calculateConsecutiveLosses(closedTrades);
    const expectancy = winRate * averageWin - lossRate * averageLoss;

    // Efficiency metrics
    const tradingFrequency = sessionDurationDays > 0 ? closedTrades.length / sessionDurationDays : 0;
    const holdingPeriod = this.calculateAverageHoldingPeriod(closedTrades);
    const turnoverRate = this.calculateTurnoverRate(trades, portfolio);

    return {
      totalReturn,
      totalReturnPercentage,
      annualizedReturn,
      volatility,
      sharpeRatio,
      sortinoRatio,
      calmarRatio,
      maxDrawdown,
      currentDrawdown,
      winRate,
      lossRate,
      profitFactor,
      averageWin,
      averageLoss,
      averageTrade,
      largestWin,
      largestLoss,
      consecutiveWins,
      consecutiveLosses,
      expectancy,
      tradingFrequency,
      holdingPeriod,
      turnoverRate
    };
  }

  /**
   * Compare paper trading results with backtesting
   */
  async compareWithBacktesting(
    paperTrades: PaperTrade[],
    backtestResults: any
  ): Promise<BacktestComparison> {
    const paperMetrics = await this.calculatePerformanceMetrics('', paperTrades, {} as VirtualPortfolio);
    
    // Calculate performance gap
    const returnGap = paperMetrics.totalReturnPercentage - backtestResults.expectedReturn;
    const performanceGap = Math.abs(returnGap) / Math.abs(backtestResults.expectedReturn) * 100;
    
    // Analyze slippage impact
    const slippageImpact = this.calculateSlippageImpact(paperTrades);
    
    // Calculate execution costs
    const executionCosts = this.calculateExecutionCosts(paperTrades);
    
    // Calculate realism adjustment
    const realismAdjustment = performanceGap + slippageImpact + executionCosts;
    
    // Detailed metric comparisons
    const metricComparisons = {
      returnDifference: returnGap,
      riskDifference: paperMetrics.volatility - backtestResults.expectedVolatility,
      winRateDifference: paperMetrics.winRate - backtestResults.expectedWinRate,
      drawdownDifference: paperMetrics.maxDrawdown - backtestResults.expectedMaxDrawdown
    };
    
    // Generate insights
    const insights = this.generateComparisonInsights(
      paperMetrics,
      backtestResults,
      slippageImpact,
      executionCosts
    );
    
    return {
      performanceGap,
      slippageImpact,
      executionCosts,
      realismAdjustment,
      metricComparisons,
      insights
    };
  }

  /**
   * Generate learning insights from performance analysis
   */
  async generateLearningInsights(
    trades: PaperTrade[],
    metrics: PerformanceMetrics,
    comparison?: BacktestComparison
  ): Promise<LearningInsights> {
    const strengthAreas: string[] = [];
    const improvementAreas: string[] = [];
    const specificRecommendations: string[] = [];

    // Analyze strengths
    if (metrics.winRate > 0.6) {
      strengthAreas.push('Strong trade selection and entry timing');
    }
    if (metrics.sharpeRatio > 1.0) {
      strengthAreas.push('Good risk-adjusted returns');
    }
    if (metrics.maxDrawdown < 0.1) {
      strengthAreas.push('Excellent risk management');
    }
    if (metrics.profitFactor > 1.5) {
      strengthAreas.push('Effective profit maximization');
    }

    // Analyze improvement areas
    if (metrics.winRate < 0.5) {
      improvementAreas.push('Entry and exit timing needs improvement');
      specificRecommendations.push('Focus on technical analysis and market timing');
    }
    if (metrics.sharpeRatio < 0.5) {
      improvementAreas.push('Risk-adjusted returns are below expectations');
      specificRecommendations.push('Review position sizing and risk management rules');
    }
    if (metrics.maxDrawdown > 0.2) {
      improvementAreas.push('Drawdown control needs attention');
      specificRecommendations.push('Implement stricter stop-loss rules and position limits');
    }
    if (metrics.consecutiveLosses > 5) {
      improvementAreas.push('Managing losing streaks');
      specificRecommendations.push('Consider break periods after consecutive losses');
    }

    // Calculate skill scores
    const skillDevelopment = {
      riskManagement: this.calculateRiskManagementScore(metrics),
      entryTiming: this.calculateEntryTimingScore(metrics),
      exitTiming: this.calculateExitTimingScore(metrics),
      positionSizing: this.calculatePositionSizingScore(trades),
      emotionalControl: this.calculateEmotionalControlScore(trades, metrics)
    };

    // Generate next milestones
    const nextMilestones = this.generateNextMilestones(metrics, skillDevelopment);

    return {
      strengthAreas,
      improvementAreas,
      specificRecommendations,
      skillDevelopment,
      nextMilestones
    };
  }

  /**
   * Create performance visualization data for frontend
   */
  async createVisualizationData(
    trades: PaperTrade[],
    portfolio: VirtualPortfolio
  ): Promise<PerformanceVisualizationData> {
    const equityCurve = this.generateEquityCurve(trades, portfolio);
    const drawdownChart = this.generateDrawdownChart(trades, portfolio);
    const monthlyReturns = this.calculateMonthlyReturns(trades);
    const tradeDistribution = this.analyzeTradeDistribution(trades);
    const riskMetricsTimeSeries = this.generateRiskMetricsTimeSeries(trades, portfolio);

    return {
      equityCurve,
      drawdownChart,
      monthlyReturns,
      tradeDistribution,
      riskMetricsTimeSeries
    };
  }

  /**
   * Analyze slippage impact on performance
   */
  analyzeSlippageImpact(trades: PaperTrade[]): {
    totalSlippageCost: number;
    averageSlippage: number;
    slippageByInstrument: Record<string, number>;
    impactOnReturns: number;
    recommendations: string[];
  } {
    if (trades.length === 0) {
      return {
        totalSlippageCost: 0,
        averageSlippage: 0,
        slippageByInstrument: {},
        impactOnReturns: 0,
        recommendations: []
      };
    }

    let totalSlippageCost = 0;
    let totalSlippage = 0;
    const slippageByInstrument: Record<string, number[]> = {};

    trades.forEach(trade => {
      const slippage = trade.simulationMetadata.actualSlippage || 0;
      const tradeValue = new Decimal(trade.volume).mul(trade.openPrice).toNumber();
      const slippageCost = tradeValue * slippage;
      
      totalSlippageCost += slippageCost;
      totalSlippage += slippage;
      
      if (!slippageByInstrument[trade.symbol]) {
        slippageByInstrument[trade.symbol] = [];
      }
      slippageByInstrument[trade.symbol].push(slippage);
    });

    const averageSlippage = totalSlippage / trades.length;
    
    // Calculate average slippage by instrument
    const avgSlippageByInstrument: Record<string, number> = {};
    Object.keys(slippageByInstrument).forEach(instrument => {
      const slippages = slippageByInstrument[instrument];
      avgSlippageByInstrument[instrument] = slippages.reduce((sum, s) => sum + s, 0) / slippages.length;
    });

    // Calculate impact on returns
    const totalTradeValue = trades.reduce((sum, t) => 
      sum + new Decimal(t.volume).mul(t.openPrice).toNumber(), 0
    );
    const impactOnReturns = totalTradeValue > 0 ? (totalSlippageCost / totalTradeValue) * 100 : 0;

    // Generate recommendations
    const recommendations: string[] = [];
    if (averageSlippage > 0.005) {
      recommendations.push('Consider using limit orders to reduce slippage');
    }
    if (impactOnReturns > 2) {
      recommendations.push('Slippage is significantly impacting returns - review trading times and instruments');
    }

    return {
      totalSlippageCost,
      averageSlippage,
      slippageByInstrument: avgSlippageByInstrument,
      impactOnReturns,
      recommendations
    };
  }

  // Private helper methods

  private getEmptyMetrics(): PerformanceMetrics {
    return {
      totalReturn: 0,
      totalReturnPercentage: 0,
      annualizedReturn: 0,
      volatility: 0,
      sharpeRatio: 0,
      sortinoRatio: 0,
      calmarRatio: 0,
      maxDrawdown: 0,
      currentDrawdown: 0,
      winRate: 0,
      lossRate: 0,
      profitFactor: 0,
      averageWin: 0,
      averageLoss: 0,
      averageTrade: 0,
      largestWin: 0,
      largestLoss: 0,
      consecutiveWins: 0,
      consecutiveLosses: 0,
      expectancy: 0,
      tradingFrequency: 0,
      holdingPeriod: 0,
      turnoverRate: 0
    };
  }

  private calculateSessionDuration(trades: PaperTrade[]): number {
    if (trades.length === 0) return 0;
    
    const firstTrade = new Date(Math.min(...trades.map(t => t.openTime.getTime())));
    const lastTrade = new Date(Math.max(...trades.map(t => t.openTime.getTime())));
    
    return Math.ceil((lastTrade.getTime() - firstTrade.getTime()) / (1000 * 60 * 60 * 24));
  }

  private calculateDailyReturns(trades: PaperTrade[], portfolio: VirtualPortfolio): number[] {
    // Simplified daily returns calculation
    // In reality, this would aggregate trades by day
    return trades
      .filter(t => t.profit !== undefined)
      .map(t => ((t.profit || 0) / portfolio.initialBalance) * 100);
  }

  private calculateVolatility(returns: number[]): number {
    if (returns.length < 2) return 0;
    
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const squaredDiffs = returns.map(r => Math.pow(r - mean, 2));
    const variance = squaredDiffs.reduce((sum, d) => sum + d, 0) / (returns.length - 1);
    
    return Math.sqrt(variance);
  }

  private calculateSortinoRatio(returns: number[], riskFreeRate: number): number {
    const excessReturns = returns.map(r => r - riskFreeRate);
    const negativeReturns = excessReturns.filter(r => r < 0);
    
    if (negativeReturns.length === 0) return 0;
    
    const downside = Math.sqrt(
      negativeReturns.reduce((sum, r) => sum + r * r, 0) / negativeReturns.length
    );
    
    const avgExcessReturn = excessReturns.reduce((sum, r) => sum + r, 0) / excessReturns.length;
    
    return downside > 0 ? avgExcessReturn / downside : 0;
  }

  private calculateMaxDrawdown(trades: PaperTrade[]): number {
    if (trades.length === 0) return 0;
    
    let peak = 0;
    let runningPnL = 0;
    let maxDrawdown = 0;
    
    trades.forEach(trade => {
      runningPnL += (trade.profit || 0);
      if (runningPnL > peak) {
        peak = runningPnL;
      }
      const drawdown = (peak - runningPnL) / Math.abs(peak) * 100;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    });
    
    return maxDrawdown;
  }

  private calculateCurrentDrawdown(trades: PaperTrade[]): number {
    // Simplified - would calculate from current equity vs peak equity
    return 0;
  }

  private calculateConsecutiveWins(trades: PaperTrade[]): number {
    let maxConsecutive = 0;
    let current = 0;
    
    trades.forEach(trade => {
      if ((trade.profit || 0) > 0) {
        current++;
        maxConsecutive = Math.max(maxConsecutive, current);
      } else {
        current = 0;
      }
    });
    
    return maxConsecutive;
  }

  private calculateConsecutiveLosses(trades: PaperTrade[]): number {
    let maxConsecutive = 0;
    let current = 0;
    
    trades.forEach(trade => {
      if ((trade.profit || 0) < 0) {
        current++;
        maxConsecutive = Math.max(maxConsecutive, current);
      } else {
        current = 0;
      }
    });
    
    return maxConsecutive;
  }

  private calculateAverageHoldingPeriod(trades: PaperTrade[]): number {
    const closedTrades = trades.filter(t => t.closeTime);
    if (closedTrades.length === 0) return 0;
    
    const totalHours = closedTrades.reduce((sum, trade) => {
      const openTime = trade.openTime.getTime();
      const closeTime = trade.closeTime!.getTime();
      return sum + (closeTime - openTime) / (1000 * 60 * 60);
    }, 0);
    
    return totalHours / closedTrades.length;
  }

  private calculateTurnoverRate(trades: PaperTrade[], portfolio: VirtualPortfolio): number {
    if (trades.length === 0) return 0;
    
    const totalTradeValue = trades.reduce((sum, trade) => 
      sum + new Decimal(trade.volume).mul(trade.openPrice).toNumber(), 0
    );
    
    return (totalTradeValue / portfolio.initialBalance) * 100;
  }

  private calculateSlippageImpact(trades: PaperTrade[]): number {
    if (trades.length === 0) return 0;
    
    const totalSlippage = trades.reduce((sum, trade) => 
      sum + (trade.simulationMetadata.actualSlippage || 0), 0
    );
    
    return (totalSlippage / trades.length) * 100;
  }

  private calculateExecutionCosts(trades: PaperTrade[]): number {
    if (trades.length === 0) return 0;
    
    const totalFees = trades.reduce((sum, trade) => sum + trade.commission, 0);
    const totalTradeValue = trades.reduce((sum, trade) => 
      sum + new Decimal(trade.volume).mul(trade.openPrice).toNumber(), 0
    );
    
    return totalTradeValue > 0 ? (totalFees / totalTradeValue) * 100 : 0;
  }

  private generateComparisonInsights(
    paperMetrics: PerformanceMetrics,
    backtestResults: any,
    slippageImpact: number,
    executionCosts: number
  ): any {
    return {
      executionQuality: slippageImpact > 1 ? 'Poor execution quality detected' : 'Good execution quality',
      marketImpact: 'Minimal market impact on small positions',
      behavioralFactors: 'Trading discipline appears consistent',
      recommendations: [
        'Continue monitoring execution costs',
        'Consider optimizing order timing',
        'Review slippage patterns by instrument'
      ]
    };
  }

  private calculateRiskManagementScore(metrics: PerformanceMetrics): number {
    let score = 100;
    if (metrics.maxDrawdown > 20) score -= 30;
    else if (metrics.maxDrawdown > 10) score -= 15;
    
    if (metrics.sharpeRatio < 0.5) score -= 20;
    else if (metrics.sharpeRatio > 1.5) score += 10;
    
    return Math.max(Math.min(score, 100), 0);
  }

  private calculateEntryTimingScore(metrics: PerformanceMetrics): number {
    let score = 50; // Base score
    if (metrics.winRate > 0.6) score += 30;
    else if (metrics.winRate > 0.5) score += 15;
    else score -= 20;
    
    return Math.max(Math.min(score, 100), 0);
  }

  private calculateExitTimingScore(metrics: PerformanceMetrics): number {
    let score = 50;
    if (metrics.averageWin / Math.abs(metrics.averageLoss) > 1.5) score += 25;
    if (metrics.profitFactor > 1.5) score += 25;
    
    return Math.max(Math.min(score, 100), 0);
  }

  private calculatePositionSizingScore(trades: PaperTrade[]): number {
    // Analyze position sizing consistency
    if (trades.length === 0) return 0;
    
    const positions = trades.map(t => new Decimal(t.volume).mul(t.openPrice).toNumber());
    const avgPosition = positions.reduce((sum, p) => sum + p, 0) / positions.length;
    const variance = positions.reduce((sum, p) => sum + Math.pow(p - avgPosition, 2), 0) / positions.length;
    const coefficient = Math.sqrt(variance) / avgPosition;
    
    // Lower coefficient of variation = better score
    return Math.max(100 - coefficient * 100, 0);
  }

  private calculateEmotionalControlScore(trades: PaperTrade[], metrics: PerformanceMetrics): number {
    let score = 70; // Base score
    
    // Penalize for revenge trading (large positions after losses)
    if (metrics.consecutiveLosses > 3) score -= 15;
    if (metrics.largestLoss < metrics.averageLoss * 3) score += 15; // Good loss control
    
    return Math.max(Math.min(score, 100), 0);
  }

  private generateNextMilestones(metrics: PerformanceMetrics, skills: any): string[] {
    const milestones: string[] = [];
    
    if (skills.riskManagement < 70) {
      milestones.push('Achieve consistent risk management (target: 70+ score)');
    }
    if (metrics.winRate < 0.55) {
      milestones.push('Improve entry timing to achieve 55%+ win rate');
    }
    if (metrics.sharpeRatio < 1.0) {
      milestones.push('Optimize strategy for Sharpe ratio > 1.0');
    }
    
    return milestones;
  }

  private generateEquityCurve(trades: PaperTrade[], portfolio: VirtualPortfolio): Array<{ date: Date; value: number; }> {
    const curve: Array<{ date: Date; value: number; }> = [];
    let runningBalance = portfolio.initialBalance;
    
    curve.push({ date: portfolio.createdAt, value: runningBalance });
    
    trades
      .filter(t => t.profit !== undefined)
      .sort((a, b) => a.openTime.getTime() - b.openTime.getTime())
      .forEach(trade => {
        runningBalance += (trade.profit || 0);
        curve.push({ date: trade.openTime, value: runningBalance });
      });
    
    return curve;
  }

  private generateDrawdownChart(trades: PaperTrade[], portfolio: VirtualPortfolio): Array<{ date: Date; drawdown: number; }> {
    const chart: Array<{ date: Date; drawdown: number; }> = [];
    let runningBalance = portfolio.initialBalance;
    let peak = runningBalance;
    
    chart.push({ date: portfolio.createdAt, drawdown: 0 });
    
    trades
      .filter(t => t.profit !== undefined)
      .sort((a, b) => a.openTime.getTime() - b.openTime.getTime())
      .forEach(trade => {
        runningBalance += (trade.profit || 0);
        if (runningBalance > peak) peak = runningBalance;
        
        const drawdown = peak > 0 ? ((peak - runningBalance) / peak) * 100 : 0;
        chart.push({ date: trade.openTime, drawdown });
      });
    
    return chart;
  }

  private calculateMonthlyReturns(trades: PaperTrade[]): Array<{ month: string; return: number; }> {
    // Simplified monthly returns calculation
    const monthlyData: Record<string, number> = {};
    
    trades.forEach(trade => {
      const monthKey = `${trade.openTime.getFullYear()}-${trade.openTime.getMonth() + 1}`;
      if (!monthlyData[monthKey]) monthlyData[monthKey] = 0;
      monthlyData[monthKey] += (trade.profit || 0);
    });
    
    return Object.entries(monthlyData).map(([month, returnValue]) => ({
      month,
      return: returnValue
    }));
  }

  private analyzeTradeDistribution(trades: PaperTrade[]): any {
    const winDistribution: number[] = [];
    const lossDistribution: number[] = [];
    const holdingPeriods: number[] = [];
    
    trades.forEach(trade => {
      const profit = trade.profit || 0;
      if (profit > 0) {
        winDistribution.push(profit);
      } else {
        lossDistribution.push(Math.abs(profit));
      }
      
      if (trade.closeTime) {
        const holdingHours = (trade.closeTime.getTime() - trade.openTime.getTime()) / (1000 * 60 * 60);
        holdingPeriods.push(holdingHours);
      }
    });
    
    return { winDistribution, lossDistribution, holdingPeriods };
  }

  private generateRiskMetricsTimeSeries(trades: PaperTrade[], portfolio: VirtualPortfolio): Array<any> {
    // Simplified time series - would calculate rolling metrics in reality
    return [
      {
        date: portfolio.createdAt,
        sharpe: 0,
        volatility: 0,
        maxDrawdown: 0
      }
    ];
  }
}