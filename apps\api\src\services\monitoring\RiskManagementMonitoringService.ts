import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';

/**
 * Risk Management System Monitoring Service
 * Monitors risk metrics, position limits, drawdown, and generates risk-based alerts
 */
export class RiskManagementMonitoringService extends EventEmitter {
  private prisma: PrismaClient;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private readonly MONITORING_INTERVAL_MS = 30000; // 30 seconds for real-time risk monitoring

  // Risk thresholds for alert generation
  private readonly RISK_THRESHOLDS = {
    // Portfolio risk thresholds
    portfolio: {
      maxDrawdown: { warning: 5, critical: 10 }, // Percentage
      maxExposure: { warning: 75, critical: 90 }, // Percentage of account
      concentrationRisk: { warning: 25, critical: 40 }, // Single position %
      leverageRatio: { warning: 10, critical: 20 },
      dailyLoss: { warning: 2, critical: 5 }, // Percentage of account
    },
    // Position risk thresholds
    position: {
      maxPositionSize: { warning: 10, critical: 15 }, // Percentage of account
      stopLossDistance: { warning: 200, critical: 500 }, // Pips
      marginUtilization: { warning: 70, critical: 85 }, // Percentage
      holdingPeriod: { warning: 72, critical: 168 }, // Hours
    },
    // System risk thresholds
    system: {
      correlationRisk: { warning: 0.7, critical: 0.85 },
      volatilitySpike: { warning: 2, critical: 3 }, // Standard deviations
      liquidityRisk: { warning: 30, critical: 60 }, // Seconds to execute
      systemLatency: { warning: 500, critical: 1000 }, // Milliseconds
    },
  };

  // Risk monitoring state
  private riskMetrics: Map<string, RiskMetric> = new Map();
  private positionRisks: Map<string, PositionRisk> = new Map();
  private riskAlerts: RiskAlert[] = [];
  private lastRiskAssessment: Date | null = null;
  
  constructor() {
    super();
    this.prisma = new PrismaClient();
  }

  /**
   * Start risk monitoring service
   */
  async startMonitoring(): Promise<void> {
    if (this.monitoringInterval) {
      console.log('Risk monitoring is already running');
      return;
    }

    console.log('Starting risk management monitoring service...');
    
    // Perform initial risk assessment
    await this.performRiskAssessment();
    
    // Set up continuous monitoring
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.performRiskAssessment();
      } catch (error) {
        console.error('Error during risk assessment:', error);
        this.emit('error', error);
      }
    }, this.MONITORING_INTERVAL_MS);

    this.emit('monitoring_started', {
      service: 'risk_management',
      interval: this.MONITORING_INTERVAL_MS,
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Stop risk monitoring service
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      console.log('Risk management monitoring stopped');
      
      this.emit('monitoring_stopped', {
        service: 'risk_management',
        timestamp: new Date().toISOString(),
      });
    }
  }

  /**
   * Perform comprehensive risk assessment
   */
  async performRiskAssessment(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Get current portfolio and position data
      const portfolioData = await this.getPortfolioData();
      const positionData = await this.getPositionData();
      const marketData = await this.getMarketData();
      
      // Calculate risk metrics
      const portfolioRisk = await this.calculatePortfolioRisk(portfolioData, marketData);
      const positionRisks = await this.calculatePositionRisks(positionData, marketData);
      const systemRisk = await this.calculateSystemRisk();
      
      // Update risk state
      this.updateRiskMetrics(portfolioRisk, positionRisks, systemRisk);
      
      // Generate alerts for risk breaches
      await this.generateRiskAlerts();
      
      // Store risk assessment results
      await this.storeRiskAssessment({
        portfolioRisk,
        positionRisks,
        systemRisk,
        timestamp: new Date(),
        assessmentDuration: Date.now() - startTime,
      });
      
      this.lastRiskAssessment = new Date();
      
      this.emit('risk_assessment_completed', {
        portfolioScore: portfolioRisk.overallScore,
        positionCount: positionRisks.length,
        alertCount: this.riskAlerts.filter(a => !a.acknowledged).length,
        duration: Date.now() - startTime,
      });
      
    } catch (error) {
      console.error('Risk assessment failed:', error);
      this.emit('risk_assessment_failed', { error: error.message });
      throw error;
    }
  }

  /**
   * Calculate portfolio-level risk metrics
   */
  private async calculatePortfolioRisk(portfolioData: any, marketData: any): Promise<PortfolioRiskMetrics> {
    const accountBalance = portfolioData.balance;
    const totalExposure = portfolioData.positions.reduce((sum: number, pos: any) => sum + Math.abs(pos.marketValue), 0);
    const unrealizedPnL = portfolioData.positions.reduce((sum: number, pos: any) => sum + pos.unrealizedPnL, 0);
    
    // Calculate drawdown
    const peakBalance = portfolioData.peakBalance || accountBalance;
    const currentDrawdown = ((peakBalance - accountBalance) / peakBalance) * 100;
    
    // Calculate daily P&L
    const dailyPnL = portfolioData.dailyPnL;
    const dailyLoss = dailyPnL < 0 ? Math.abs(dailyPnL / accountBalance) * 100 : 0;
    
    // Calculate exposure percentage
    const exposurePercentage = (totalExposure / accountBalance) * 100;
    
    // Calculate leverage ratio
    const leverageRatio = totalExposure / portfolioData.margin;
    
    // Calculate concentration risk (largest single position)
    const largestPosition = Math.max(...portfolioData.positions.map((pos: any) => Math.abs(pos.marketValue)));
    const concentrationRisk = (largestPosition / accountBalance) * 100;
    
    // Calculate correlation risk
    const correlationMatrix = await this.calculateCorrelationMatrix(portfolioData.positions, marketData);
    const avgCorrelation = this.calculateAverageCorrelation(correlationMatrix);
    
    // Calculate VaR (Value at Risk) - simplified 1-day 95% VaR
    const portfolioVolatility = this.calculatePortfolioVolatility(portfolioData.positions, marketData);
    const valueAtRisk = accountBalance * portfolioVolatility * 1.645; // 95% confidence
    
    const overallScore = this.calculateRiskScore({
      drawdown: currentDrawdown,
      exposure: exposurePercentage,
      concentration: concentrationRisk,
      leverage: leverageRatio,
      dailyLoss,
      correlation: avgCorrelation,
    });

    return {
      overallScore,
      status: this.getRiskStatus(overallScore),
      metrics: {
        drawdown: {
          current: currentDrawdown,
          maximum: portfolioData.maxDrawdown || currentDrawdown,
          status: this.getThresholdStatus(currentDrawdown, this.RISK_THRESHOLDS.portfolio.maxDrawdown),
        },
        exposure: {
          percentage: exposurePercentage,
          amount: totalExposure,
          status: this.getThresholdStatus(exposurePercentage, this.RISK_THRESHOLDS.portfolio.maxExposure),
        },
        concentration: {
          percentage: concentrationRisk,
          largestPosition,
          status: this.getThresholdStatus(concentrationRisk, this.RISK_THRESHOLDS.portfolio.concentrationRisk),
        },
        leverage: {
          ratio: leverageRatio,
          utilizedMargin: portfolioData.margin,
          status: this.getThresholdStatus(leverageRatio, this.RISK_THRESHOLDS.portfolio.leverageRatio),
        },
        dailyPnL: {
          amount: dailyPnL,
          percentage: (dailyPnL / accountBalance) * 100,
          lossPercentage: dailyLoss,
          status: this.getThresholdStatus(dailyLoss, this.RISK_THRESHOLDS.portfolio.dailyLoss),
        },
        correlation: {
          average: avgCorrelation,
          status: this.getThresholdStatus(avgCorrelation, this.RISK_THRESHOLDS.system.correlationRisk),
        },
        valueAtRisk: {
          amount: valueAtRisk,
          percentage: (valueAtRisk / accountBalance) * 100,
          confidence: 95,
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Calculate individual position risks
   */
  private async calculatePositionRisks(positions: any[], marketData: any): Promise<PositionRisk[]> {
    return Promise.all(positions.map(async (position) => {
      const marketValue = Math.abs(position.marketValue);
      const accountBalance = position.accountBalance;
      const positionSizePercentage = (marketValue / accountBalance) * 100;
      
      // Calculate stop loss distance in pips
      const currentPrice = marketData[position.symbol]?.price || position.currentPrice;
      const stopLossDistance = position.stopLoss 
        ? Math.abs(currentPrice - position.stopLoss) * 10000 // Convert to pips
        : 0;
      
      // Calculate margin utilization for this position
      const marginUtilization = (position.marginUsed / position.availableMargin) * 100;
      
      // Calculate holding period in hours
      const openTime = new Date(position.openTime);
      const holdingPeriod = (Date.now() - openTime.getTime()) / (1000 * 60 * 60);
      
      // Calculate unrealized risk
      const unrealizedRisk = Math.abs(position.unrealizedPnL / accountBalance) * 100;
      
      // Calculate position risk score
      const riskScore = this.calculatePositionRiskScore({
        sizePercentage: positionSizePercentage,
        stopLossDistance,
        marginUtilization,
        holdingPeriod,
        unrealizedRisk,
      });

      return {
        positionId: position.id,
        symbol: position.symbol,
        riskScore,
        status: this.getRiskStatus(riskScore),
        metrics: {
          size: {
            percentage: positionSizePercentage,
            amount: marketValue,
            status: this.getThresholdStatus(positionSizePercentage, this.RISK_THRESHOLDS.position.maxPositionSize),
          },
          stopLoss: {
            distance: stopLossDistance,
            price: position.stopLoss,
            status: this.getThresholdStatus(stopLossDistance, this.RISK_THRESHOLDS.position.stopLossDistance),
          },
          margin: {
            utilization: marginUtilization,
            used: position.marginUsed,
            available: position.availableMargin,
            status: this.getThresholdStatus(marginUtilization, this.RISK_THRESHOLDS.position.marginUtilization),
          },
          holding: {
            period: holdingPeriod,
            status: this.getThresholdStatus(holdingPeriod, this.RISK_THRESHOLDS.position.holdingPeriod),
          },
          unrealized: {
            risk: unrealizedRisk,
            pnl: position.unrealizedPnL,
          },
        },
        timestamp: new Date().toISOString(),
      };
    }));
  }

  /**
   * Calculate system-level risk metrics
   */
  private async calculateSystemRisk(): Promise<SystemRiskMetrics> {
    // Get system performance metrics
    const latencyMetrics = await this.getSystemLatencyMetrics();
    const liquidityMetrics = await this.getLiquidityMetrics();
    const volatilityMetrics = await this.getVolatilityMetrics();
    
    const systemScore = this.calculateSystemRiskScore({
      latency: latencyMetrics.average,
      liquidity: liquidityMetrics.averageExecutionTime,
      volatility: volatilityMetrics.currentSpike,
    });

    return {
      overallScore: systemScore,
      status: this.getRiskStatus(systemScore),
      metrics: {
        latency: {
          current: latencyMetrics.current,
          average: latencyMetrics.average,
          status: this.getThresholdStatus(latencyMetrics.average, this.RISK_THRESHOLDS.system.systemLatency),
        },
        liquidity: {
          executionTime: liquidityMetrics.averageExecutionTime,
          status: this.getThresholdStatus(liquidityMetrics.averageExecutionTime, this.RISK_THRESHOLDS.system.liquidityRisk),
        },
        volatility: {
          spike: volatilityMetrics.currentSpike,
          baseline: volatilityMetrics.baseline,
          status: this.getThresholdStatus(volatilityMetrics.currentSpike, this.RISK_THRESHOLDS.system.volatilitySpike),
        },
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Generate risk alerts based on current metrics
   */
  private async generateRiskAlerts(): Promise<void> {
    const newAlerts: RiskAlert[] = [];
    
    // Check portfolio risk alerts
    for (const [metricKey, metric] of this.riskMetrics) {
      if (metric.status === 'critical' || metric.status === 'warning') {
        newAlerts.push({
          id: `risk_alert_${metricKey}_${Date.now()}`,
          type: 'risk_breach',
          severity: metric.status === 'critical' ? 'critical' : 'warning',
          category: this.getRiskCategory(metricKey),
          message: this.generateRiskMessage(metricKey, metric),
          timestamp: new Date().toISOString(),
          metricKey,
          currentValue: metric.value,
          threshold: metric.threshold,
          acknowledged: false,
          metadata: {
            riskType: metricKey,
            impact: this.assessRiskImpact(metricKey, metric.value),
            recommendation: this.generateRiskRecommendation(metricKey, metric),
          },
        });
      }
    }
    
    // Check position risk alerts
    for (const [positionId, positionRisk] of this.positionRisks) {
      if (positionRisk.status === 'critical' || positionRisk.status === 'warning') {
        newAlerts.push({
          id: `position_risk_alert_${positionId}_${Date.now()}`,
          type: 'position_risk',
          severity: positionRisk.status === 'critical' ? 'critical' : 'warning',
          category: 'position',
          message: `Position ${positionRisk.symbol} exceeds risk limits`,
          timestamp: new Date().toISOString(),
          positionId,
          symbol: positionRisk.symbol,
          acknowledged: false,
          metadata: {
            riskScore: positionRisk.riskScore,
            riskFactors: this.identifyRiskFactors(positionRisk),
            recommendation: this.generatePositionRiskRecommendation(positionRisk),
          },
        });
      }
    }
    
    // Add new alerts to the collection
    this.riskAlerts.push(...newAlerts);
    
    // Emit alerts for immediate action
    for (const alert of newAlerts) {
      this.emit('risk_alert', alert);
      
      if (alert.severity === 'critical') {
        this.emit('critical_risk_alert', alert);
      }
    }
  }

  /**
   * Update risk metrics state
   */
  private updateRiskMetrics(portfolioRisk: PortfolioRiskMetrics, positionRisks: PositionRisk[], systemRisk: SystemRiskMetrics): void {
    // Update portfolio risk metrics
    this.riskMetrics.set('portfolio_drawdown', {
      value: portfolioRisk.metrics.drawdown.current,
      threshold: this.RISK_THRESHOLDS.portfolio.maxDrawdown.warning,
      status: portfolioRisk.metrics.drawdown.status,
      lastUpdated: new Date(),
    });
    
    this.riskMetrics.set('portfolio_exposure', {
      value: portfolioRisk.metrics.exposure.percentage,
      threshold: this.RISK_THRESHOLDS.portfolio.maxExposure.warning,
      status: portfolioRisk.metrics.exposure.status,
      lastUpdated: new Date(),
    });
    
    // Update position risks
    this.positionRisks.clear();
    for (const positionRisk of positionRisks) {
      this.positionRisks.set(positionRisk.positionId, positionRisk);
    }
  }

  /**
   * Get current risk summary
   */
  getRiskSummary(): RiskSummary {
    const portfolioMetrics = Array.from(this.riskMetrics.values());
    const positionMetrics = Array.from(this.positionRisks.values());
    
    const criticalAlerts = this.riskAlerts.filter(a => a.severity === 'critical' && !a.acknowledged).length;
    const warningAlerts = this.riskAlerts.filter(a => a.severity === 'warning' && !a.acknowledged).length;
    
    const overallRiskScore = this.calculateOverallRiskScore(portfolioMetrics, positionMetrics);
    
    return {
      overallScore: overallRiskScore,
      status: this.getRiskStatus(overallRiskScore),
      alerts: {
        critical: criticalAlerts,
        warning: warningAlerts,
        total: criticalAlerts + warningAlerts,
      },
      portfolio: {
        riskScore: portfolioMetrics.length > 0 ? portfolioMetrics[0].value : 0,
        status: portfolioMetrics.length > 0 ? portfolioMetrics[0].status : 'healthy',
      },
      positions: {
        total: positionMetrics.length,
        risky: positionMetrics.filter(p => p.status !== 'healthy').length,
      },
      lastAssessment: this.lastRiskAssessment?.toISOString() || null,
      nextAssessment: this.lastRiskAssessment 
        ? new Date(this.lastRiskAssessment.getTime() + this.MONITORING_INTERVAL_MS).toISOString()
        : null,
    };
  }

  // Helper methods for calculations and assessments
  private calculateRiskScore(factors: any): number {
    // Simplified risk scoring algorithm
    const weights = {
      drawdown: 0.25,
      exposure: 0.20,
      concentration: 0.20,
      leverage: 0.15,
      dailyLoss: 0.15,
      correlation: 0.05,
    };
    
    let score = 100; // Start with perfect score
    
    // Deduct points based on risk factors
    score -= factors.drawdown * weights.drawdown * 10;
    score -= Math.max(0, factors.exposure - 50) * weights.exposure * 2;
    score -= Math.max(0, factors.concentration - 20) * weights.concentration * 3;
    score -= Math.max(0, factors.leverage - 5) * weights.leverage * 5;
    score -= factors.dailyLoss * weights.dailyLoss * 20;
    score -= Math.max(0, factors.correlation - 0.5) * weights.correlation * 100;
    
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private calculatePositionRiskScore(factors: any): number {
    let score = 100;
    
    // Deduct points for various risk factors
    score -= Math.max(0, factors.sizePercentage - 5) * 5; // Size risk
    score -= Math.max(0, 200 - factors.stopLossDistance) * 0.1; // Stop loss risk
    score -= Math.max(0, factors.marginUtilization - 50) * 1; // Margin risk
    score -= Math.max(0, factors.holdingPeriod - 24) * 0.5; // Time risk
    score -= factors.unrealizedRisk * 10; // Unrealized loss risk
    
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private calculateSystemRiskScore(factors: any): number {
    let score = 100;
    
    score -= Math.max(0, factors.latency - 100) * 0.1;
    score -= Math.max(0, factors.liquidity - 10) * 2;
    score -= Math.max(0, factors.volatility - 1) * 20;
    
    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private getRiskStatus(score: number): 'healthy' | 'warning' | 'critical' {
    if (score >= 80) return 'healthy';
    if (score >= 60) return 'warning';
    return 'critical';
  }

  private getThresholdStatus(value: number, thresholds: { warning: number; critical: number }): 'healthy' | 'warning' | 'critical' {
    if (value >= thresholds.critical) return 'critical';
    if (value >= thresholds.warning) return 'warning';
    return 'healthy';
  }

  // Mock data methods (would connect to actual data sources in production)
  private async getPortfolioData(): Promise<any> {
    return {
      balance: 100000,
      margin: 5000,
      peakBalance: 105000,
      dailyPnL: -500,
      maxDrawdown: 3.2,
      positions: [
        {
          id: 'pos_001',
          symbol: 'EURUSD',
          marketValue: 25000,
          unrealizedPnL: -200,
          marginUsed: 1000,
          availableMargin: 4000,
          stopLoss: 1.0850,
          currentPrice: 1.0900,
          openTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
          accountBalance: 100000,
        },
        {
          id: 'pos_002',
          symbol: 'XAUUSD',
          marketValue: 15000,
          unrealizedPnL: 100,
          marginUsed: 750,
          availableMargin: 4250,
          stopLoss: 2320,
          currentPrice: 2350,
          openTime: new Date(Date.now() - 4 * 60 * 60 * 1000),
          accountBalance: 100000,
        },
      ],
    };
  }

  private async getPositionData(): Promise<any[]> {
    const portfolio = await this.getPortfolioData();
    return portfolio.positions;
  }

  private async getMarketData(): Promise<any> {
    return {
      EURUSD: { price: 1.0900, volatility: 0.12 },
      XAUUSD: { price: 2350, volatility: 0.18 },
    };
  }

  private async getSystemLatencyMetrics(): Promise<any> {
    return {
      current: 45 + Math.random() * 100,
      average: 65 + Math.random() * 50,
    };
  }

  private async getLiquidityMetrics(): Promise<any> {
    return {
      averageExecutionTime: 15 + Math.random() * 30,
    };
  }

  private async getVolatilityMetrics(): Promise<any> {
    return {
      currentSpike: 1.2 + Math.random() * 2,
      baseline: 1.0,
    };
  }

  private calculateCorrelationMatrix(positions: any[], marketData: any): Promise<number[][]> {
    // Simplified correlation calculation
    return Promise.resolve([[1.0, 0.3], [0.3, 1.0]]);
  }

  private calculateAverageCorrelation(matrix: number[][]): number {
    // Calculate average off-diagonal correlation
    let sum = 0;
    let count = 0;
    for (let i = 0; i < matrix.length; i++) {
      for (let j = i + 1; j < matrix[i].length; j++) {
        sum += Math.abs(matrix[i][j]);
        count++;
      }
    }
    return count > 0 ? sum / count : 0;
  }

  private calculatePortfolioVolatility(positions: any[], marketData: any): number {
    // Simplified portfolio volatility calculation
    return 0.02; // 2% daily volatility
  }

  private calculateOverallRiskScore(portfolioMetrics: any[], positionMetrics: any[]): number {
    if (portfolioMetrics.length === 0 && positionMetrics.length === 0) return 100;
    
    const portfolioScore = portfolioMetrics.length > 0 ? portfolioMetrics[0].value : 100;
    const avgPositionScore = positionMetrics.length > 0 
      ? positionMetrics.reduce((sum, p) => sum + p.riskScore, 0) / positionMetrics.length
      : 100;
    
    return Math.round((portfolioScore * 0.7) + (avgPositionScore * 0.3));
  }

  private getRiskCategory(metricKey: string): string {
    if (metricKey.startsWith('portfolio_')) return 'portfolio';
    if (metricKey.startsWith('position_')) return 'position';
    if (metricKey.startsWith('system_')) return 'system';
    return 'general';
  }

  private generateRiskMessage(metricKey: string, metric: RiskMetric): string {
    const riskType = metricKey.replace('portfolio_', '').replace('_', ' ');
    return `${riskType.charAt(0).toUpperCase() + riskType.slice(1)} risk level is ${metric.status} (${metric.value.toFixed(2)})`;
  }

  private assessRiskImpact(metricKey: string, value: number): string {
    if (metricKey.includes('drawdown') && value > 10) return 'High potential for account loss';
    if (metricKey.includes('exposure') && value > 90) return 'Overexposed to market movements';
    if (metricKey.includes('concentration') && value > 40) return 'Single position dominates portfolio';
    return 'Monitor closely for risk escalation';
  }

  private generateRiskRecommendation(metricKey: string, metric: RiskMetric): string {
    if (metricKey.includes('drawdown')) return 'Consider reducing position sizes or implementing stricter stop losses';
    if (metricKey.includes('exposure')) return 'Close some positions to reduce overall market exposure';
    if (metricKey.includes('concentration')) return 'Diversify portfolio by reducing largest position size';
    return 'Review and adjust risk management parameters';
  }

  private identifyRiskFactors(positionRisk: PositionRisk): string[] {
    const factors = [];
    if (positionRisk.metrics.size.status !== 'healthy') factors.push('Position size too large');
    if (positionRisk.metrics.stopLoss.status !== 'healthy') factors.push('Stop loss too far');
    if (positionRisk.metrics.margin.status !== 'healthy') factors.push('High margin utilization');
    if (positionRisk.metrics.holding.status !== 'healthy') factors.push('Position held too long');
    return factors;
  }

  private generatePositionRiskRecommendation(positionRisk: PositionRisk): string {
    if (positionRisk.status === 'critical') return 'Consider closing position immediately';
    if (positionRisk.metrics.size.status === 'critical') return 'Reduce position size';
    if (positionRisk.metrics.stopLoss.status === 'critical') return 'Tighten stop loss';
    return 'Monitor position closely and consider risk reduction';
  }

  private async storeRiskAssessment(assessment: any): Promise<void> {
    // In production, store assessment results in database
    console.log('Risk assessment stored:', {
      timestamp: assessment.timestamp,
      portfolioScore: assessment.portfolioRisk.overallScore,
      positionCount: assessment.positionRisks.length,
      duration: assessment.assessmentDuration,
    });
  }
}

// Type definitions
interface RiskMetric {
  value: number;
  threshold: number;
  status: 'healthy' | 'warning' | 'critical';
  lastUpdated: Date;
}

interface PortfolioRiskMetrics {
  overallScore: number;
  status: 'healthy' | 'warning' | 'critical';
  metrics: {
    drawdown: {
      current: number;
      maximum: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    exposure: {
      percentage: number;
      amount: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    concentration: {
      percentage: number;
      largestPosition: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    leverage: {
      ratio: number;
      utilizedMargin: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    dailyPnL: {
      amount: number;
      percentage: number;
      lossPercentage: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    correlation: {
      average: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    valueAtRisk: {
      amount: number;
      percentage: number;
      confidence: number;
    };
  };
  timestamp: string;
}

interface PositionRisk {
  positionId: string;
  symbol: string;
  riskScore: number;
  status: 'healthy' | 'warning' | 'critical';
  metrics: {
    size: {
      percentage: number;
      amount: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    stopLoss: {
      distance: number;
      price: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    margin: {
      utilization: number;
      used: number;
      available: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    holding: {
      period: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    unrealized: {
      risk: number;
      pnl: number;
    };
  };
  timestamp: string;
}

interface SystemRiskMetrics {
  overallScore: number;
  status: 'healthy' | 'warning' | 'critical';
  metrics: {
    latency: {
      current: number;
      average: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    liquidity: {
      executionTime: number;
      status: 'healthy' | 'warning' | 'critical';
    };
    volatility: {
      spike: number;
      baseline: number;
      status: 'healthy' | 'warning' | 'critical';
    };
  };
  timestamp: string;
}

interface RiskAlert {
  id: string;
  type: 'risk_breach' | 'position_risk';
  severity: 'warning' | 'critical';
  category: string;
  message: string;
  timestamp: string;
  metricKey?: string;
  positionId?: string;
  symbol?: string;
  currentValue?: number;
  threshold?: number;
  acknowledged: boolean;
  metadata: {
    riskType?: string;
    riskScore?: number;
    riskFactors?: string[];
    impact?: string;
    recommendation: string;
  };
}

interface RiskSummary {
  overallScore: number;
  status: 'healthy' | 'warning' | 'critical';
  alerts: {
    critical: number;
    warning: number;
    total: number;
  };
  portfolio: {
    riskScore: number;
    status: 'healthy' | 'warning' | 'critical';
  };
  positions: {
    total: number;
    risky: number;
  };
  lastAssessment: string | null;
  nextAssessment: string | null;
}