import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { CriticalAlertService } from '../CriticalAlertService';

// Mock Prisma
const mockPrisma = {
  alert: {
    create: vi.fn(),
    findMany: vi.fn(),
    updateMany: vi.fn(),
    count: vi.fn(),
  },
} as unknown as PrismaClient;

// Mock external notification services
const mockEmailService = {
  sendEmail: vi.fn(),
};

const mockSlackService = {
  sendMessage: vi.fn(),
};

describe('CriticalAlertService', () => {
  let service: CriticalAlertService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new CriticalAlertService(mockPrisma);
    
    // Inject mock services for testing
    (service as any).emailService = mockEmailService;
    (service as any).slackService = mockSlackService;
  });

  afterEach(() => {
    // Clean up any timers or intervals
    service.stopMonitoring?.();
  });

  describe('createAlert', () => {
    const mockAlertData = {
      type: 'system_failure',
      severity: 'critical',
      category: 'system_performance',
      source: 'monitoring_service',
      title: 'Database Connection Lost',
      message: 'Primary database connection has been lost',
      metadata: { connectionId: 'db-001', lastPing: '2023-01-01T10:00:00Z' },
      tags: ['database', 'critical'],
    };

    it('should create alert successfully', async () => {
      const mockCreatedAlert = {
        id: 'alert-123',
        ...mockAlertData,
        timestamp: new Date(),
        status: 'active',
        acknowledged: false,
        correlationKey: 'monitoring_service-system_performance-system_failure',
        escalationLevel: 0,
        notificationsSent: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      vi.mocked(mockPrisma.alert.create).mockResolvedValue(mockCreatedAlert as any);

      const result = await service.createAlert(mockAlertData);

      expect(mockPrisma.alert.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          type: mockAlertData.type,
          severity: mockAlertData.severity,
          category: mockAlertData.category,
          source: mockAlertData.source,
          title: mockAlertData.title,
          message: mockAlertData.message,
          metadata: mockAlertData.metadata,
          tags: mockAlertData.tags,
          status: 'active',
          acknowledged: false,
          correlationKey: expect.any(String),
          escalationLevel: 0,
          notificationsSent: 0,
        }),
      });

      expect(result).toEqual(mockCreatedAlert);
    });

    it('should send immediate notifications for critical alerts', async () => {
      const mockCreatedAlert = {
        id: 'alert-123',
        ...mockAlertData,
        severity: 'critical',
        timestamp: new Date(),
      };

      vi.mocked(mockPrisma.alert.create).mockResolvedValue(mockCreatedAlert as any);
      mockEmailService.sendEmail.mockResolvedValue(true);
      mockSlackService.sendMessage.mockResolvedValue(true);

      await service.createAlert({ ...mockAlertData, severity: 'critical' });

      // Should send notifications immediately for critical alerts
      expect(mockEmailService.sendEmail).toHaveBeenCalledWith(
        expect.objectContaining({
          subject: expect.stringContaining('CRITICAL'),
          body: expect.stringContaining(mockAlertData.title),
        })
      );
      expect(mockSlackService.sendMessage).toHaveBeenCalled();
    });

    it('should generate correlation key correctly', async () => {
      const mockCreatedAlert = { id: 'alert-123', correlationKey: 'test-key' };
      vi.mocked(mockPrisma.alert.create).mockResolvedValue(mockCreatedAlert as any);

      await service.createAlert(mockAlertData);

      const createCall = vi.mocked(mockPrisma.alert.create).mock.calls[0][0];
      expect(createCall.data.correlationKey).toBe('monitoring_service-system_performance-system_failure');
    });

    it('should emit alertCreated event', async () => {
      const eventSpy = vi.fn();
      service.on('alertCreated', eventSpy);

      const mockCreatedAlert = { id: 'alert-123' };
      vi.mocked(mockPrisma.alert.create).mockResolvedValue(mockCreatedAlert as any);

      await service.createAlert(mockAlertData);

      expect(eventSpy).toHaveBeenCalledWith(mockCreatedAlert);
    });
  });

  describe('getAlerts', () => {
    const mockAlerts = [
      {
        id: 'alert-1',
        type: 'system_failure',
        severity: 'critical',
        title: 'Database Error',
        status: 'active',
        acknowledged: false,
        createdAt: new Date('2023-01-01T10:00:00Z'),
      },
      {
        id: 'alert-2',
        type: 'performance_degradation',
        severity: 'high',
        title: 'High CPU Usage',
        status: 'active',
        acknowledged: true,
        createdAt: new Date('2023-01-01T09:00:00Z'),
      },
    ];

    it('should get active alerts', async () => {
      vi.mocked(mockPrisma.alert.findMany).mockResolvedValue(mockAlerts as any);

      const result = await service.getAlerts('active', {}, 50);

      expect(mockPrisma.alert.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          status: { in: ['active', 'acknowledged'] },
        }),
        orderBy: [
          { severity: 'desc' },
          { createdAt: 'desc' },
        ],
        take: 50,
      });

      expect(result).toHaveProperty('alerts');
      expect(result).toHaveProperty('total');
      expect(result).toHaveProperty('view', 'active');
    });

    it('should filter by severity', async () => {
      vi.mocked(mockPrisma.alert.findMany).mockResolvedValue(mockAlerts as any);

      await service.getAlerts('all', { severity: ['critical', 'high'] }, 50);

      expect(mockPrisma.alert.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          severity: { in: ['critical', 'high'] },
        }),
        orderBy: expect.any(Array),
        take: 50,
      });
    });

    it('should filter by acknowledged status', async () => {
      vi.mocked(mockPrisma.alert.findMany).mockResolvedValue(mockAlerts as any);

      await service.getAlerts('all', { acknowledged: true }, 50);

      expect(mockPrisma.alert.findMany).toHaveBeenCalledWith({
        where: expect.objectContaining({
          acknowledged: true,
        }),
        orderBy: expect.any(Array),
        take: 50,
      });
    });

    it('should generate correlation groups', async () => {
      const correlatedAlerts = [
        {
          ...mockAlerts[0],
          correlationKey: 'system_monitoring-performance-high_cpu',
        },
        {
          ...mockAlerts[1],
          correlationKey: 'system_monitoring-performance-high_cpu',
        },
      ];

      vi.mocked(mockPrisma.alert.findMany).mockResolvedValue(correlatedAlerts as any);

      const result = await service.getAlerts('active', {}, 50);

      expect(result.correlationGroups).toBeDefined();
      expect(result.correlationGroups.length).toBeGreaterThan(0);
    });
  });

  describe('updateAlerts', () => {
    const mockUpdateData = {
      actionBy: 'admin-user',
      notes: 'Acknowledged and investigating',
      assignedTo: 'ops-team',
      escalationReason: 'No response within SLA',
    };

    it('should acknowledge alerts successfully', async () => {
      vi.mocked(mockPrisma.alert.updateMany).mockResolvedValue({ count: 2 } as any);

      const result = await service.updateAlerts(['alert-1', 'alert-2'], 'acknowledge', mockUpdateData);

      expect(mockPrisma.alert.updateMany).toHaveBeenCalledWith({
        where: { id: { in: ['alert-1', 'alert-2'] } },
        data: expect.objectContaining({
          acknowledged: true,
          acknowledgedBy: 'admin-user',
          acknowledgedAt: expect.any(Date),
          notes: 'Acknowledged and investigating',
        }),
      });

      expect(result).toMatchObject({
        processedAlerts: 2,
        action: 'acknowledge',
        success: true,
        results: expect.arrayContaining([
          expect.objectContaining({
            alertId: 'alert-1',
            action: 'acknowledge',
            success: true,
          }),
        ]),
      });
    });

    it('should resolve alerts successfully', async () => {
      vi.mocked(mockPrisma.alert.updateMany).mockResolvedValue({ count: 1 } as any);

      await service.updateAlerts(['alert-1'], 'resolve', mockUpdateData);

      expect(mockPrisma.alert.updateMany).toHaveBeenCalledWith({
        where: { id: { in: ['alert-1'] } },
        data: expect.objectContaining({
          status: 'resolved',
          resolved: true,
          resolvedBy: 'admin-user',
          resolvedAt: expect.any(Date),
          resolution: 'Acknowledged and investigating',
        }),
      });
    });

    it('should escalate alerts successfully', async () => {
      vi.mocked(mockPrisma.alert.updateMany).mockResolvedValue({ count: 1 } as any);

      await service.updateAlerts(['alert-1'], 'escalate', mockUpdateData);

      expect(mockPrisma.alert.updateMany).toHaveBeenCalledWith({
        where: { id: { in: ['alert-1'] } },
        data: expect.objectContaining({
          escalationLevel: { increment: 1 },
          lastEscalated: expect.any(Date),
          escalationReason: 'No response within SLA',
        }),
      });
    });

    it('should assign alerts successfully', async () => {
      vi.mocked(mockPrisma.alert.updateMany).mockResolvedValue({ count: 1 } as any);

      await service.updateAlerts(['alert-1'], 'assign', mockUpdateData);

      expect(mockPrisma.alert.updateMany).toHaveBeenCalledWith({
        where: { id: { in: ['alert-1'] } },
        data: expect.objectContaining({
          assignedTo: 'ops-team',
          assignedBy: 'admin-user',
          assignedAt: expect.any(Date),
        }),
      });
    });

    it('should emit alertsUpdated event', async () => {
      const eventSpy = vi.fn();
      service.on('alertsUpdated', eventSpy);

      vi.mocked(mockPrisma.alert.updateMany).mockResolvedValue({ count: 1 } as any);

      const result = await service.updateAlerts(['alert-1'], 'acknowledge', mockUpdateData);

      expect(eventSpy).toHaveBeenCalledWith({
        alertIds: ['alert-1'],
        action: 'acknowledge',
        result,
      });
    });
  });

  describe('getAlertStatistics', () => {
    beforeEach(() => {
      vi.mocked(mockPrisma.alert.count).mockImplementation(({ where }) => {
        // Mock different counts based on where conditions
        if (where?.status?.in?.includes('active')) return Promise.resolve(25);
        if (where?.acknowledged === true) return Promise.resolve(150);
        if (where?.status === 'resolved') return Promise.resolve(1000);
        if (where?.severity === 'critical') return Promise.resolve(8);
        return Promise.resolve(1247); // Total
      });

      vi.mocked(mockPrisma.alert.findMany).mockResolvedValue([
        { createdAt: new Date(Date.now() - 30 * 60 * 1000), severity: 'high' },
        { createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), severity: 'critical' },
      ] as any);
    });

    it('should return comprehensive statistics', async () => {
      const stats = await service.getAlertStatistics('24h');

      expect(stats).toHaveProperty('summary');
      expect(stats.summary).toMatchObject({
        total: expect.any(Number),
        active: expect.any(Number),
        acknowledged: expect.any(Number),
        resolved: expect.any(Number),
      });

      expect(stats).toHaveProperty('timeRange');
      expect(stats).toHaveProperty('bySeverity');
      expect(stats).toHaveProperty('byCategory');
      expect(stats).toHaveProperty('trends');
      expect(stats).toHaveProperty('performance');
    });

    it('should calculate trends correctly', async () => {
      const stats = await service.getAlertStatistics('24h');

      expect(stats.trends).toBeDefined();
      expect(Array.isArray(stats.trends)).toBe(true);
      
      if (stats.trends.length > 0) {
        expect(stats.trends[0]).toHaveProperty('timestamp');
        expect(stats.trends[0]).toHaveProperty('total');
        expect(stats.trends[0]).toHaveProperty('created');
        expect(stats.trends[0]).toHaveProperty('resolved');
      }
    });
  });

  describe('getAlertHistory', () => {
    const mockHistory = [
      {
        id: 'history-1',
        alertId: 'alert-1',
        action: 'created',
        timestamp: new Date('2023-01-01T10:00:00Z'),
        user: 'system',
        details: 'Alert generated by monitoring system',
      },
      {
        id: 'history-2',
        alertId: 'alert-1',
        action: 'acknowledged',
        timestamp: new Date('2023-01-01T10:05:00Z'),
        user: 'admin',
        details: 'Alert acknowledged by operator',
      },
    ];

    it('should return alert history', async () => {
      // Mock implementation for history (would need actual table structure)
      const testService = service as any;
      testService.generateMockHistory = vi.fn().mockReturnValue(mockHistory);

      const result = await service.getAlertHistory('24h', 100);

      expect(result).toHaveProperty('history');
      expect(result).toHaveProperty('summary');
      expect(result.summary).toMatchObject({
        totalEvents: expect.any(Number),
        timeRange: '24h',
      });
    });
  });

  describe('getAlertCorrelations', () => {
    it('should return correlation groups', async () => {
      const correlatedAlerts = [
        {
          id: 'alert-1',
          correlationKey: 'system_monitoring-performance-high_cpu',
          severity: 'high',
          createdAt: new Date('2023-01-01T10:00:00Z'),
        },
        {
          id: 'alert-2',
          correlationKey: 'system_monitoring-performance-high_cpu',
          severity: 'high',
          createdAt: new Date('2023-01-01T10:05:00Z'),
        },
      ];

      vi.mocked(mockPrisma.alert.findMany).mockResolvedValue(correlatedAlerts as any);

      const result = await service.getAlertCorrelations('24h');

      expect(result).toHaveProperty('correlationGroups');
      expect(result).toHaveProperty('summary');
      expect(result.summary).toMatchObject({
        totalGroups: expect.any(Number),
        activeGroups: expect.any(Number),
        timeRange: '24h',
      });
    });
  });

  describe('updateConfiguration', () => {
    const mockConfig = {
      routingRules: [
        { severity: 'critical', channels: ['email', 'slack'] },
        { severity: 'high', channels: ['slack'] },
      ],
      notificationChannels: {
        email: { enabled: true, recipients: ['<EMAIL>'] },
        slack: { enabled: true, webhook: 'https://hooks.slack.com/...' },
      },
      escalationSettings: {
        timeouts: { critical: 300, high: 900 }, // seconds
        maxEscalationLevel: 3,
      },
    };

    it('should update configuration successfully', async () => {
      const result = await service.updateConfiguration(mockConfig);

      expect(result).toMatchObject({
        configuration: mockConfig,
        updatedAt: expect.any(String),
        restartRequired: false,
        validationResults: {
          routingRules: 'valid',
          notificationChannels: 'valid',
          escalationSettings: 'valid',
        },
      });
    });

    it('should validate configuration', async () => {
      const invalidConfig = {
        routingRules: [], // Invalid - empty rules
        notificationChannels: null,
      };

      const result = await service.updateConfiguration(invalidConfig);

      expect(result.validationResults).toMatchObject({
        routingRules: 'invalid',
        notificationChannels: 'not_provided',
      });
    });

    it('should emit configurationUpdated event', async () => {
      const eventSpy = vi.fn();
      service.on('configurationUpdated', eventSpy);

      await service.updateConfiguration(mockConfig);

      expect(eventSpy).toHaveBeenCalledWith(expect.objectContaining({
        configuration: mockConfig,
      }));
    });
  });

  describe('notification routing', () => {
    it('should route notifications based on severity', async () => {
      const criticalAlert = {
        id: 'alert-123',
        severity: 'critical',
        title: 'System Down',
        message: 'Critical system failure detected',
      };

      const testService = service as any;
      await testService.routeNotifications(criticalAlert);

      expect(mockEmailService.sendEmail).toHaveBeenCalled();
      expect(mockSlackService.sendMessage).toHaveBeenCalled();
    });

    it('should respect notification preferences', async () => {
      const lowAlert = {
        id: 'alert-123',
        severity: 'low',
        title: 'Minor Issue',
        message: 'Low priority alert',
      };

      const testService = service as any;
      await testService.routeNotifications(lowAlert);

      // Low severity alerts might not trigger all notification channels
      expect(mockEmailService.sendEmail).not.toHaveBeenCalled();
    });
  });

  describe('escalation handling', () => {
    it('should auto-escalate unacknowledged critical alerts', async () => {
      const testService = service as any;
      testService.autoEscalate = vi.fn();
      
      // Mock finding unacknowledged critical alerts
      const unacknowledgedAlerts = [
        {
          id: 'alert-1',
          severity: 'critical',
          acknowledged: false,
          createdAt: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
        },
      ];

      vi.mocked(mockPrisma.alert.findMany).mockResolvedValue(unacknowledgedAlerts as any);
      vi.mocked(mockPrisma.alert.updateMany).mockResolvedValue({ count: 1 } as any);

      await testService.checkForEscalation();

      expect(testService.autoEscalate).toHaveBeenCalled();
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      vi.mocked(mockPrisma.alert.create).mockRejectedValue(new Error('Database connection lost'));

      await expect(service.createAlert({
        type: 'test',
        severity: 'low',
        category: 'test',
        source: 'test',
        title: 'Test Alert',
        message: 'Test message',
      })).rejects.toThrow('Database connection lost');
    });

    it('should emit error event on critical failures', async () => {
      const errorSpy = vi.fn();
      service.on('error', errorSpy);

      vi.mocked(mockPrisma.alert.findMany).mockRejectedValue(new Error('Critical failure'));

      try {
        await service.getAlerts('active', {}, 50);
      } catch (error) {
        // Expected to throw
      }

      expect(errorSpy).toHaveBeenCalledWith(expect.any(Error));
    });

    it('should continue processing other alerts if notification fails', async () => {
      mockEmailService.sendEmail.mockRejectedValue(new Error('Email service down'));
      mockSlackService.sendMessage.mockResolvedValue(true);

      const mockAlert = { id: 'alert-123', severity: 'critical' };
      vi.mocked(mockPrisma.alert.create).mockResolvedValue(mockAlert as any);

      // Should not throw despite email failure
      await expect(service.createAlert({
        type: 'test',
        severity: 'critical',
        category: 'test',
        source: 'test',
        title: 'Test Alert',
        message: 'Test message',
      })).resolves.toBeDefined();

      // Slack should still have been attempted
      expect(mockSlackService.sendMessage).toHaveBeenCalled();
    });
  });
});