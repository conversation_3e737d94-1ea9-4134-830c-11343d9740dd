/**
 * Automatic Stop-Loss Manager Service
 * 
 * Manages automatic stop-loss placement and adjustment for all positions.
 * Integrates with MT5 bridge service and provides guaranteed execution support.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';
import { EventEmitter } from 'events';
import { 
  StopLossCalculator, 
  StopLossParams, 
  StopLossMarketData,
  StopLossMethod,
  RiskTolerance,
  TradeDirection,
  createStopLossCalculator 
} from './StopLossCalculator';

/**
 * Trade execution service interface (MT5 Bridge integration)
 */
export interface TradeExecutionService {
  placeStopLoss(order: StopLossOrder): Promise<ExecutionResult>;
  modifyStopLoss(orderId: string, newStopPrice: Decimal.Instance): Promise<ExecutionResult>;
  cancelStopLoss(orderId: string): Promise<ExecutionResult>;
  getOrderStatus(orderId: string): Promise<OrderStatus>;
  healthCheck(): Promise<boolean>;
}

/**
 * Stop-loss order details
 */
export interface StopLossOrder {
  id: string;
  positionId: string;
  symbol: string;
  direction: TradeDirection;
  quantity: Decimal.Instance;
  stopPrice: Decimal.Instance;
  orderType: 'stop_loss' | 'trailing_stop';
  timeInForce: 'GTC' | 'IOC' | 'FOK' | 'DAY';
  expiration?: Date;
  metadata: {
    method: StopLossMethod;
    confidence: number;
    reasoning: string;
    calculatedAt: Date;
  };
}

/**
 * Order execution result
 */
export interface ExecutionResult {
  success: boolean;
  orderId?: string;
  executionPrice?: Decimal.Instance;
  executionTime?: Date;
  slippage?: Decimal.Instance;
  errorCode?: string;
  errorMessage?: string;
  retry?: boolean;
}

/**
 * Order status information
 */
export interface OrderStatus {
  orderId: string;
  status: 'pending' | 'filled' | 'cancelled' | 'rejected' | 'expired';
  fillPrice?: Decimal.Instance;
  fillTime?: Date;
  remainingQuantity?: Decimal.Instance;
  lastUpdate: Date;
}

/**
 * Position tracking for stop-loss management
 */
export interface ManagedPosition {
  id: string;
  userId: string;
  accountId: string;
  symbol: string;
  direction: TradeDirection;
  entryPrice: Decimal.Instance;
  quantity: Decimal.Instance;
  currentPrice: Decimal.Instance;
  entryTime: Date;
  lastUpdate: Date;
  
  // Stop-loss details
  stopLossOrderId?: string;
  currentStopLoss?: Decimal.Instance;
  stopLossMethod: StopLossMethod;
  isTrailing: boolean;
  
  // Risk management
  riskTolerance: RiskTolerance;
  maxRiskAmount: Decimal.Instance;
  accountBalance: Decimal.Instance;
  
  // Status
  isActive: boolean;
  autoStopLossEnabled: boolean;
}

/**
 * Stop-loss management configuration
 */
export interface AutoStopLossConfig {
  // Automatic placement settings
  enableAutoPlacement: boolean;
  defaultMethod: StopLossMethod;
  fallbackMethod: StopLossMethod;
  
  // Update intervals
  monitoringIntervalMs: number;
  priceUpdateThreshold: Decimal.Instance; // Minimum price change to trigger update
  
  // Execution settings
  maxRetries: number;
  retryDelayMs: number;
  executionTimeoutMs: number;
  
  // Risk limits
  maxPositionsPerUser: number;
  maxRiskPerPosition: number;
  emergencyLiquidationThreshold: number;
  
  // Failover settings
  enableBrokerFailover: boolean;
  healthCheckIntervalMs: number;
  failoverTimeoutMs: number;
  
  // Testing/debugging
  disableBackgroundMonitoring?: boolean; // For tests - disables setInterval timers
}

/**
 * Default auto stop-loss configuration
 */
export const DEFAULT_AUTO_STOPLOSS_CONFIG: AutoStopLossConfig = {
  enableAutoPlacement: true,
  defaultMethod: 'atr_based',
  fallbackMethod: 'percentage_based',
  
  monitoringIntervalMs: 5000, // 5 seconds
  priceUpdateThreshold: new Decimal(0.0005), // 0.5 pips
  
  maxRetries: 3,
  retryDelayMs: 1000,
  executionTimeoutMs: 10000,
  
  maxPositionsPerUser: 50,
  maxRiskPerPosition: 5.0,
  emergencyLiquidationThreshold: 10.0,
  
  enableBrokerFailover: true,
  healthCheckIntervalMs: 30000, // 30 seconds
  failoverTimeoutMs: 5000
};

/**
 * Stop-loss event types
 */
export interface StopLossEvents {
  stop_loss_placed: {
    positionId: string;
    orderId: string;
    stopPrice: Decimal.Instance;
    method: StopLossMethod;
    timestamp: Date;
  };
  
  stop_loss_updated: {
    positionId: string;
    orderId: string;
    oldStopPrice: Decimal.Instance;
    newStopPrice: Decimal.Instance;
    reason: string;
    timestamp: Date;
  };
  
  stop_loss_triggered: {
    positionId: string;
    orderId: string;
    triggerPrice: Decimal.Instance;
    actualPrice: Decimal.Instance;
    slippage: Decimal.Instance;
    timestamp: Date;
  };
  
  stop_loss_failed: {
    positionId: string;
    error: string;
    retryAttempt: number;
    maxRetries: number;
    timestamp: Date;
  };
  
  trailing_stop_adjusted: {
    positionId: string;
    adjustment: Decimal.Instance;
    newStopPrice: Decimal.Instance;
    trigger: string;
    timestamp: Date;
  };
  
  emergency_liquidation: {
    userId: string;
    positions: string[];
    reason: string;
    timestamp: Date;
  };
}

/**
 * Automatic Stop-Loss Manager - Service for automatic stop-loss management
 */
export class AutoStopLossManager extends EventEmitter {
  private readonly config: AutoStopLossConfig;
  private readonly stopLossCalculator: StopLossCalculator;
  private readonly tradeExecutionService: TradeExecutionService;
  
  private managedPositions: Map<string, ManagedPosition> = new Map();
  private retryQueues: Map<string, number> = new Map();
  private failedOrders: number = 0;
  private monitoringInterval?: NodeJS.Timeout;
  private healthCheckInterval?: NodeJS.Timeout;
  
  private isServiceHealthy = true;
  private lastHealthCheck = new Date();

  constructor(
    tradeExecutionService: TradeExecutionService,
    config: Partial<AutoStopLossConfig> = {}
  ) {
    super();
    this.config = { ...DEFAULT_AUTO_STOPLOSS_CONFIG, ...config };
    this.tradeExecutionService = tradeExecutionService;
    this.stopLossCalculator = createStopLossCalculator();
    
    this.initializeMonitoring();
    this.setupEventHandlers();
  }

  /**
   * Add a position for automatic stop-loss management
   */
  public async addPosition(position: ManagedPosition): Promise<boolean> {
    try {
      this.validatePosition(position);
      
      // Check position limits
      const userPositions = this.getUserPositions(position.userId);
      if (userPositions.length >= this.config.maxPositionsPerUser) {
        throw new Error(`Maximum positions limit (${this.config.maxPositionsPerUser}) exceeded for user`);
      }

      // Calculate and place initial stop-loss
      if (position.autoStopLossEnabled) {
        await this.placeInitialStopLoss(position);
      }

      this.managedPositions.set(position.id, position);
      
      this.emit('position_added', {
        positionId: position.id,
        userId: position.userId,
        symbol: position.symbol,
        autoStopLoss: position.autoStopLossEnabled,
        timestamp: new Date()
      });

      return true;
    } catch (error) {
      this.emit('error', {
        operation: 'add_position',
        positionId: position.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });
      return false;
    }
  }

  /**
   * Remove a position from management (on close)
   */
  public async removePosition(positionId: string): Promise<boolean> {
    try {
      const position = this.managedPositions.get(positionId);
      if (!position) {
        return false;
      }

      // Cancel any active stop-loss orders
      if (position.stopLossOrderId) {
        await this.tradeExecutionService.cancelStopLoss(position.stopLossOrderId);
      }

      // Remove from tracking
      this.managedPositions.delete(positionId);
      this.retryQueues.delete(positionId);
      
      // Clean up trailing stops
      this.stopLossCalculator.removeTrailingStopLoss(positionId);

      this.emit('position_removed', {
        positionId,
        userId: position.userId,
        stopLossOrderId: position.stopLossOrderId,
        timestamp: new Date()
      });

      return true;
    } catch (error) {
      this.emit('error', {
        operation: 'remove_position',
        positionId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });
      return false;
    }
  }

  /**
   * Update position price and adjust stop-loss if necessary
   */
  public async updatePositionPrice(positionId: string, newPrice: Decimal.Instance): Promise<boolean> {
    try {
      const position = this.managedPositions.get(positionId);
      if (!position || !position.isActive) {
        return false;
      }

      const priceChange = newPrice.sub(position.currentPrice).abs();
      if (priceChange.lt(this.config.priceUpdateThreshold)) {
        return true; // Price change too small
      }

      position.currentPrice = newPrice;
      position.lastUpdate = new Date();

      // Handle trailing stops
      if (position.isTrailing && position.currentStopLoss) {
        const trailingResult = this.stopLossCalculator.updateTrailingStopLoss(
          positionId,
          newPrice,
          position.direction
        );

        if (trailingResult.updated && trailingResult.newStopLoss) {
          await this.updateStopLossOrder(
            position,
            trailingResult.newStopLoss,
            'Trailing stop adjustment'
          );
        }
      }

      return true;
    } catch (error) {
      this.emit('error', {
        operation: 'update_price',
        positionId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });
      return false;
    }
  }

  /**
   * Manually adjust stop-loss for a position
   */
  public async adjustStopLoss(
    positionId: string,
    newStopPrice: Decimal,
    reason: string = 'Manual adjustment'
  ): Promise<boolean> {
    try {
      const position = this.managedPositions.get(positionId);
      if (!position || !position.isActive) {
        throw new Error('Position not found or inactive');
      }

      await this.updateStopLossOrder(position, newStopPrice, reason);
      return true;
    } catch (error) {
      this.emit('error', {
        operation: 'adjust_stop_loss',
        positionId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });
      return false;
    }
  }

  /**
   * Enable trailing stop-loss for a position
   */
  public async enableTrailing(positionId: string): Promise<boolean> {
    try {
      const position = this.managedPositions.get(positionId);
      if (!position || !position.isActive || !position.currentStopLoss) {
        return false;
      }

      position.isTrailing = true;
      
      // Initialize trailing stop state in calculator
      const trailingDistance = position.direction === 'long' 
        ? position.entryPrice.sub(position.currentStopLoss)
        : position.currentStopLoss.sub(position.entryPrice);
        
      this.stopLossCalculator.initializeTrailingStopLoss(
        positionId,
        {
          symbol: position.symbol,
          direction: position.direction,
          entryPrice: position.entryPrice,
          quantity: position.quantity,
          entryTime: position.entryTime,
          marketValue: position.quantity.mul(position.currentPrice)
        },
        position.currentStopLoss,
        trailingDistance.abs()
      );
      
      return true;
    } catch (error) {
      this.emit('error', {
        operation: 'enable_trailing',
        positionId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });
      return false;
    }
  }

  /**
   * Enable/disable auto stop-loss for a position
   */
  public async toggleAutoStopLoss(positionId: string, enabled: boolean): Promise<boolean> {
    try {
      const position = this.managedPositions.get(positionId);
      if (!position) {
        throw new Error('Position not found');
      }

      if (enabled && !position.autoStopLossEnabled) {
        // Enable auto stop-loss
        await this.placeInitialStopLoss(position);
        position.autoStopLossEnabled = true;
      } else if (!enabled && position.autoStopLossEnabled) {
        // Disable auto stop-loss
        if (position.stopLossOrderId) {
          await this.tradeExecutionService.cancelStopLoss(position.stopLossOrderId);
          position.stopLossOrderId = undefined;
          position.currentStopLoss = undefined;
        }
        position.autoStopLossEnabled = false;
      }

      this.emit('auto_stop_loss_toggled', {
        positionId,
        enabled,
        timestamp: new Date()
      });

      return true;
    } catch (error) {
      this.emit('error', {
        operation: 'toggle_auto_stop_loss',
        positionId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });
      return false;
    }
  }

  /**
   * Get all managed positions for a user
   */
  public getUserPositions(userId: string): ManagedPosition[] {
    return Array.from(this.managedPositions.values())
      .filter(pos => pos.userId === userId);
  }

  /**
   * Get specific managed position
   */
  public getPosition(positionId: string): ManagedPosition | undefined {
    return this.managedPositions.get(positionId);
  }

  /**
   * Get service statistics
   */
  public getStatistics(): {
    totalPositions: number;
    activePositions: number;
    trailingStops: number;
    failedOrders: number;
    serviceHealth: boolean;
    lastHealthCheck: Date;
  } {
    const activePositions = Array.from(this.managedPositions.values())
      .filter(pos => pos.isActive).length;
    
    const trailingStops = Array.from(this.managedPositions.values())
      .filter(pos => pos.isTrailing).length;

    return {
      totalPositions: this.managedPositions.size,
      activePositions,
      trailingStops,
      failedOrders: this.failedOrders,
      serviceHealth: this.isServiceHealthy,
      lastHealthCheck: this.lastHealthCheck
    };
  }

  /**
   * Manually trigger monitoring (for testing)
   */
  public async triggerMonitoring(): Promise<void> {
    await this.monitorPositions();
  }

  /**
   * Manually trigger health check (for testing)
   */
  public async triggerHealthCheck(): Promise<void> {
    await this.performHealthCheck();
  }

  /**
   * Emergency liquidation of all positions for a user
   */
  public async emergencyLiquidation(userId: string, reason: string): Promise<boolean> {
    try {
      const userPositions = this.getUserPositions(userId);
      const liquidatedPositions: string[] = [];

      for (const position of userPositions) {
        if (position.isActive) {
          // Convert to market order for immediate execution
          const marketOrder = {
            id: `emergency_${position.id}_${Date.now()}`,
            positionId: position.id,
            symbol: position.symbol,
            direction: position.direction === 'long' ? 'short' : 'long', // Opposite direction
            quantity: position.quantity,
            stopPrice: position.currentPrice, // Use current price
            orderType: 'stop_loss' as const,
            timeInForce: 'IOC' as const, // Immediate or Cancel
            metadata: {
              method: 'emergency' as StopLossMethod,
              confidence: 1.0,
              reasoning: `Emergency liquidation: ${reason}`,
              calculatedAt: new Date()
            }
          };

          const result = await this.tradeExecutionService.placeStopLoss(marketOrder);
          if (result.success) {
            liquidatedPositions.push(position.id);
            position.isActive = false;
          }
        }
      }

      this.emit('emergency_liquidation', {
        userId,
        positions: liquidatedPositions,
        reason,
        timestamp: new Date()
      });

      return liquidatedPositions.length > 0;
    } catch (error) {
      this.emit('error', {
        operation: 'emergency_liquidation',
        userId,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });
      return false;
    }
  }

  /**
   * Shutdown the service gracefully
   */
  public async shutdown(): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Cancel all active stop-loss orders
    for (const position of this.managedPositions.values()) {
      if (position.stopLossOrderId && position.isActive) {
        try {
          await this.tradeExecutionService.cancelStopLoss(position.stopLossOrderId);
        } catch (error) {
          // Log but don't throw during shutdown
          console.error(`Failed to cancel stop-loss ${position.stopLossOrderId}:`, error);
        }
      }
    }

    this.emit('service_shutdown', { timestamp: new Date() });
  }

  /**
   * Place initial stop-loss for a new position
   */
  private async placeInitialStopLoss(position: ManagedPosition): Promise<void> {
    const marketData: StopLossMarketData = {
      symbol: position.symbol,
      currentPrice: position.currentPrice,
      bid: position.currentPrice.sub(0.0001), // Mock bid/ask
      ask: position.currentPrice.add(0.0001),
      spread: new Decimal(0.0002),
      volatility: 20, // Default volatility
      averageTrueRange: position.currentPrice.mul(0.01), // Mock ATR
      timestamp: new Date()
    };

    const stopLossParams: StopLossParams = {
      position: {
        symbol: position.symbol,
        direction: position.direction,
        entryPrice: position.entryPrice,
        quantity: position.quantity,
        entryTime: position.entryTime,
        currentPrice: position.currentPrice,
        marketValue: position.quantity.mul(position.currentPrice)
      },
      marketData,
      method: position.stopLossMethod,
      riskTolerance: position.riskTolerance,
      accountBalance: position.accountBalance,
      maxRiskPercentage: this.config.maxRiskPerPosition
    };

    const stopLossResult = this.stopLossCalculator.calculateStopLoss(stopLossParams);
    
    const order: StopLossOrder = {
      id: `sl_${position.id}_${Date.now()}`,
      positionId: position.id,
      symbol: position.symbol,
      direction: position.direction,
      quantity: position.quantity,
      stopPrice: stopLossResult.stopLossPrice,
      orderType: stopLossResult.isTrailing ? 'trailing_stop' : 'stop_loss',
      timeInForce: 'GTC',
      metadata: {
        method: stopLossResult.method,
        confidence: stopLossResult.confidence,
        reasoning: stopLossResult.reasoning,
        calculatedAt: new Date()
      }
    };

    const result = await this.executeWithRetry(
      () => this.tradeExecutionService.placeStopLoss(order),
      position.id
    );

    if (result.success && result.orderId) {
      position.stopLossOrderId = result.orderId;
      position.currentStopLoss = stopLossResult.stopLossPrice;
      position.isTrailing = stopLossResult.isTrailing;

      if (stopLossResult.isTrailing && stopLossResult.adjustmentTrigger) {
        this.stopLossCalculator.initializeTrailingStopLoss(
          position.id,
          {
            symbol: position.symbol,
            direction: position.direction,
            entryPrice: position.entryPrice,
            quantity: position.quantity,
            entryTime: position.entryTime,
            marketValue: position.quantity.mul(position.currentPrice)
          },
          stopLossResult.stopLossPrice,
          stopLossResult.adjustmentTrigger
        );
      }

      this.emit('stop_loss_placed', {
        positionId: position.id,
        orderId: result.orderId,
        stopPrice: stopLossResult.stopLossPrice,
        method: stopLossResult.method,
        timestamp: new Date()
      });
    } else {
      throw new Error(`Failed to place stop-loss: ${result.errorMessage}`);
    }
  }

  /**
   * Update existing stop-loss order
   */
  private async updateStopLossOrder(
    position: ManagedPosition,
    newStopPrice: Decimal,
    reason: string
  ): Promise<void> {
    if (!position.stopLossOrderId || !position.currentStopLoss) {
      throw new Error('No active stop-loss order to update');
    }

    const oldStopPrice = position.currentStopLoss;
    
    const result = await this.executeWithRetry(
      () => this.tradeExecutionService.modifyStopLoss(position.stopLossOrderId!, newStopPrice),
      position.id
    );

    if (result.success) {
      position.currentStopLoss = newStopPrice;
      position.lastUpdate = new Date();

      this.emit('stop_loss_updated', {
        positionId: position.id,
        orderId: position.stopLossOrderId,
        oldStopPrice,
        newStopPrice,
        reason,
        timestamp: new Date()
      });
    } else {
      throw new Error(`Failed to update stop-loss: ${result.errorMessage}`);
    }
  }

  /**
   * Execute operation with retry logic
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    positionId: string
  ): Promise<T> {
    let lastError: Error | undefined;
    const maxRetries = this.config.maxRetries;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await Promise.race([
          operation(),
          new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('Operation timeout')), this.config.executionTimeoutMs)
          )
        ]);
        
        // Check if result indicates failure (for ExecutionResult types)
        if (result && typeof result === 'object' && 'success' in result && !result.success) {
          const errorMsg = 'errorMessage' in result ? result.errorMessage as string : 'Operation failed';
          throw new Error(errorMsg);
        }
        
        // Clear retry count on success
        this.retryQueues.delete(positionId);
        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        if (attempt < maxRetries) {
          this.retryQueues.set(positionId, attempt);
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelayMs * attempt));
        } else {
          this.failedOrders++;
          this.emit('stop_loss_failed', {
            positionId,
            error: lastError.message,
            retryAttempt: attempt,
            maxRetries,
            timestamp: new Date()
          });
        }
      }
    }

    throw lastError || new Error('Max retries exceeded');
  }

  /**
   * Initialize monitoring and health checking
   */
  private initializeMonitoring(): void {
    // Skip background monitoring if disabled (e.g., for tests)
    if (this.config.disableBackgroundMonitoring) {
      return;
    }

    // Position monitoring interval
    this.monitoringInterval = setInterval(
      () => this.monitorPositions(),
      this.config.monitoringIntervalMs
    );

    // Health check interval
    this.healthCheckInterval = setInterval(
      () => this.performHealthCheck(),
      this.config.healthCheckIntervalMs
    );
  }

  /**
   * Monitor all positions for updates
   */
  private async monitorPositions(): Promise<void> {
    for (const position of this.managedPositions.values()) {
      if (!position.isActive || !position.autoStopLossEnabled) {
        continue;
      }

      try {
        // Check stop-loss order status
        if (position.stopLossOrderId) {
          const orderStatus = await this.tradeExecutionService.getOrderStatus(position.stopLossOrderId);
          
          if (orderStatus.status === 'filled') {
            position.isActive = false;
            this.emit('stop_loss_triggered', {
              positionId: position.id,
              orderId: position.stopLossOrderId,
              triggerPrice: position.currentStopLoss!,
              actualPrice: orderStatus.fillPrice!,
              slippage: orderStatus.fillPrice!.sub(position.currentStopLoss!).abs(),
              timestamp: new Date()
            });
          }
        }
      } catch (error) {
        // Log monitoring errors but don't stop monitoring
        this.emit('monitoring_error', {
          positionId: position.id,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date()
        });
      }
    }
  }

  /**
   * Perform service health check
   */
  private async performHealthCheck(): Promise<void> {
    try {
      this.isServiceHealthy = await this.tradeExecutionService.healthCheck();
      this.lastHealthCheck = new Date();
      
      if (!this.isServiceHealthy) {
        this.emit('service_unhealthy', {
          timestamp: this.lastHealthCheck,
          positions: this.managedPositions.size
        });
      }
    } catch (error) {
      this.isServiceHealthy = false;
      this.emit('health_check_failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });
    }
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.stopLossCalculator.on('trailing_stop_updated', (event) => {
      this.emit('trailing_stop_adjusted', {
        positionId: event.positionId,
        adjustment: event.adjustment,
        newStopPrice: event.newStopLoss,
        trigger: 'Price movement',
        timestamp: event.timestamp
      });
    });
  }

  /**
   * Validate position data
   */
  private validatePosition(position: ManagedPosition): void {
    if (!position.id || !position.userId || !position.accountId) {
      throw new Error('Position must have valid ID, user ID, and account ID');
    }
    
    if (position.entryPrice.lte(0) || position.quantity.lte(0)) {
      throw new Error('Entry price and quantity must be positive');
    }
    
    if (position.accountBalance.lte(0)) {
      throw new Error('Account balance must be positive');
    }
    
    if (!['conservative', 'moderate', 'aggressive'].includes(position.riskTolerance)) {
      throw new Error('Invalid risk tolerance level');
    }
  }
}

/**
 * Factory function to create AutoStopLossManager instance
 */
export function createAutoStopLossManager(
  tradeExecutionService: TradeExecutionService,
  config?: Partial<AutoStopLossConfig>
): AutoStopLossManager {
  return new AutoStopLossManager(tradeExecutionService, config);
}

/**
 * Default export for convenience
 */
export default AutoStopLossManager;