/**
 * Production Trade Executor
 * 
 * Handles live trade execution with:
 * - Comprehensive error handling and retry logic
 * - Trade confirmation and validation
 * - Real-time trade status tracking
 * - Performance monitoring with execution latency tracking
 */

import { EventEmitter } from 'events';
import type { StandardBrokerAdapter, TradeOrder, TradeExecutionResult } from '../adapters/StandardBrokerAdapter';
import type { BrokerAdapterFactory } from '../adapters/BrokerAdapterFactory';

// Trade execution configuration
export interface TradeExecutionConfig {
  maxRetryAttempts: number;
  retryDelay: number;
  executionTimeout: number;
  slippageTolerancePips: number;
  latencyThresholdMs: number;
  enableRiskValidation: boolean;
  requireConfirmation: boolean;
}

// Trade execution context
export interface TradeExecutionContext {
  orderId: string;
  brokerId: string;
  adapter: StandardBrokerAdapter;
  order: TradeOrder;
  startTime: Date;
  retryCount: number;
  lastError: string | null;
  riskChecksPassed: boolean;
}

// Trade execution metrics
export interface TradeExecutionMetrics {
  totalOrders: number;
  successfulOrders: number;
  failedOrders: number;
  averageLatency: number;
  medianLatency: number;
  maxLatency: number;
  retryRate: number;
  brokerMetrics: Map<string, {
    orders: number;
    successes: number;
    averageLatency: number;
  }>;
}

/**
 * Production Trade Executor
 * Manages trade execution with production-grade reliability
 */
export class ProductionTradeExecutor extends EventEmitter {
  private config: TradeExecutionConfig;
  private activeExecutions: Map<string, TradeExecutionContext> = new Map();
  private executionMetrics: TradeExecutionMetrics;
  private latencyHistory: number[] = [];

  constructor(
    private brokerFactory: BrokerAdapterFactory,
    config: Partial<TradeExecutionConfig> = {}
  ) {
    super();
    
    this.config = {
      maxRetryAttempts: 3,
      retryDelay: 1000,
      executionTimeout: 10000,
      slippageTolerancePips: 2,
      latencyThresholdMs: 500,
      enableRiskValidation: true,
      requireConfirmation: true,
      ...config
    };

    this.executionMetrics = {
      totalOrders: 0,
      successfulOrders: 0,
      failedOrders: 0,
      averageLatency: 0,
      medianLatency: 0,
      maxLatency: 0,
      retryRate: 0,
      brokerMetrics: new Map()
    };
  }

  /**
   * Execute a trade order
   */
  async executeTrade(order: TradeOrder, brokerId?: string): Promise<TradeExecutionResult> {
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Get optimal broker adapter
      const adapter = brokerId 
        ? this.brokerFactory.getAdapter(brokerId)
        : this.brokerFactory.getBestAdapterForFeature('trade_execution');

      if (!adapter) {
        throw new Error(`No suitable broker adapter available for trade execution`);
      }

      // Create execution context
      const context: TradeExecutionContext = {
        orderId: order.id,
        brokerId: adapter.getConfig().id,
        adapter,
        order,
        startTime: new Date(),
        retryCount: 0,
        lastError: null,
        riskChecksPassed: false
      };

      this.activeExecutions.set(executionId, context);
      this.executionMetrics.totalOrders++;

      console.log(`🎯 Starting trade execution: ${order.type} ${order.volume} ${order.symbol}`);
      this.emit('executionStarted', { executionId, order });

      // Execute the trade with retry logic
      const result = await this.executeTradeWithRetry(context);
      
      // Update metrics
      this.updateExecutionMetrics(context, result, true);
      
      // Clean up
      this.activeExecutions.delete(executionId);
      
      console.log(`✅ Trade execution completed: ${result.executed ? 'SUCCESS' : 'FAILED'} (${result.executionTime.getTime() - context.startTime.getTime()}ms)`);
      this.emit('executionCompleted', { executionId, result });
      
      return result;
    } catch (error) {
      console.error(`❌ Trade execution failed:`, error);
      
      const context = this.activeExecutions.get(executionId);
      if (context) {
        this.updateExecutionMetrics(context, null, false);
        this.activeExecutions.delete(executionId);
      }
      
      const errorResult: TradeExecutionResult = {
        orderId: order.id,
        ticket: 0,
        executed: false,
        executionPrice: order.price,
        executionTime: new Date(),
        volume: order.volume,
        commission: 0,
        swap: 0,
        profit: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
        retryCount: context?.retryCount || 0
      };
      
      this.emit('executionFailed', { executionId, error: errorResult });
      return errorResult;
    }
  }

  /**
   * Get execution metrics
   */
  getExecutionMetrics(): TradeExecutionMetrics {
    // Update derived metrics
    if (this.latencyHistory.length > 0) {
      this.executionMetrics.averageLatency = 
        this.latencyHistory.reduce((a, b) => a + b, 0) / this.latencyHistory.length;
      
      const sortedLatencies = [...this.latencyHistory].sort((a, b) => a - b);
      this.executionMetrics.medianLatency = sortedLatencies[Math.floor(sortedLatencies.length / 2)];
      this.executionMetrics.maxLatency = Math.max(...this.latencyHistory);
    }
    
    this.executionMetrics.retryRate = this.executionMetrics.totalOrders > 0
      ? (this.executionMetrics.totalOrders - this.executionMetrics.successfulOrders) / this.executionMetrics.totalOrders
      : 0;

    return { ...this.executionMetrics };
  }

  /**
   * Get active executions
   */
  getActiveExecutions(): TradeExecutionContext[] {
    return Array.from(this.activeExecutions.values());
  }

  /**
   * Cancel an active execution
   */
  async cancelExecution(executionId: string): Promise<boolean> {
    const context = this.activeExecutions.get(executionId);
    if (!context) return false;

    try {
      // Mark as cancelled
      context.lastError = 'Execution cancelled by user';
      this.activeExecutions.delete(executionId);
      
      console.log(`🛑 Trade execution cancelled: ${executionId}`);
      this.emit('executionCancelled', { executionId, orderId: context.orderId });
      
      return true;
    } catch (error) {
      console.error(`Failed to cancel execution ${executionId}:`, error);
      return false;
    }
  }

  // Private methods

  /**
   * Execute trade with retry logic
   */
  private async executeTradeWithRetry(context: TradeExecutionContext): Promise<TradeExecutionResult> {
    let lastResult: TradeExecutionResult | null = null;
    
    for (let attempt = 0; attempt <= this.config.maxRetryAttempts; attempt++) {
      context.retryCount = attempt;
      
      try {
        // Risk validation on first attempt
        if (attempt === 0 && this.config.enableRiskValidation) {
          await this.performRiskValidation(context);
          context.riskChecksPassed = true;
        }
        
        // Execute the trade
        const result = await this.executeSingleTrade(context);
        
        if (result.executed) {
          return result;
        }
        
        lastResult = result;
        context.lastError = result.error;
        
        // Check if error is retryable
        if (!this.isRetryableError(result.error)) {
          console.log(`🚫 Non-retryable error, stopping attempts: ${result.error}`);
          break;
        }
        
        if (attempt < this.config.maxRetryAttempts) {
          console.log(`🔄 Retry attempt ${attempt + 1}/${this.config.maxRetryAttempts} in ${this.config.retryDelay}ms`);
          await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));
        }
        
      } catch (error) {
        context.lastError = error instanceof Error ? error.message : 'Unknown error';
        
        if (attempt === this.config.maxRetryAttempts) {
          throw error;
        }
      }
    }
    
    return lastResult || {
      orderId: context.orderId,
      ticket: 0,
      executed: false,
      executionPrice: context.order.price,
      executionTime: new Date(),
      volume: context.order.volume,
      commission: 0,
      swap: 0,
      profit: 0,
      error: context.lastError || 'Max retries exceeded',
      retryCount: context.retryCount
    };
  }

  /**
   * Execute a single trade attempt
   */
  private async executeSingleTrade(context: TradeExecutionContext): Promise<TradeExecutionResult> {
    const startTime = Date.now();
    
    try {
      // Execute with timeout
      const result = await Promise.race([
        context.adapter.executeTrade(context.order),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('Execution timeout')), this.config.executionTimeout)
        )
      ]);
      
      const latency = Date.now() - startTime;
      
      // Validate execution result
      if (result.executed) {
        this.validateExecution(context, result, latency);
        
        if (this.config.requireConfirmation) {
          await this.confirmExecution(context, result);
        }
      }
      
      return result;
    } catch (error) {
      const latency = Date.now() - startTime;
      console.error(`Trade execution attempt failed (${latency}ms):`, error);
      throw error;
    }
  }

  /**
   * Perform risk validation
   */
  private async performRiskValidation(context: TradeExecutionContext): Promise<void> {
    // Basic risk checks
    const accountInfo = await context.adapter.getAccountInfo();
    
    // Check available margin
    const requiredMargin = context.order.volume * 1000; // Simplified calculation
    if (accountInfo.freeMargin < requiredMargin) {
      throw new Error('Insufficient margin for trade');
    }
    
    // Check position size limits
    const symbolInfo = await context.adapter.getSymbolInfo(context.order.symbol);
    if (context.order.volume > symbolInfo.maxVolume) {
      throw new Error('Order volume exceeds maximum allowed');
    }
    
    console.log(`✅ Risk validation passed for ${context.orderId}`);
  }

  /**
   * Validate execution result
   */
  private validateExecution(
    context: TradeExecutionContext, 
    result: TradeExecutionResult, 
    latency: number
  ): void {
    // Check slippage
    const slippagePips = Math.abs(result.executionPrice - context.order.price) * 100000;
    if (slippagePips > this.config.slippageTolerancePips) {
      console.warn(`⚠️ High slippage detected: ${slippagePips.toFixed(1)} pips`);
    }
    
    // Check latency
    if (latency > this.config.latencyThresholdMs) {
      console.warn(`⚠️ High execution latency: ${latency}ms`);
    }
    
    // Record latency
    this.latencyHistory.push(latency);
    if (this.latencyHistory.length > 1000) {
      this.latencyHistory.shift(); // Keep last 1000 measurements
    }
    
    console.log(`✅ Execution validation passed: slippage=${slippagePips.toFixed(1)} pips, latency=${latency}ms`);
  }

  /**
   * Confirm trade execution
   */
  private async confirmExecution(
    context: TradeExecutionContext, 
    result: TradeExecutionResult
  ): Promise<void> {
    try {
      // Get updated positions to confirm trade
      const positions = await context.adapter.getPositions();
      const executedPosition = positions.find(p => p.ticket === result.ticket);
      
      if (!executedPosition) {
        throw new Error('Trade confirmation failed - position not found');
      }
      
      console.log(`✅ Trade execution confirmed: ticket ${result.ticket}`);
    } catch (error) {
      console.error(`❌ Trade confirmation failed:`, error);
      throw new Error(`Trade confirmation failed: ${error}`);
    }
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: string | null): boolean {
    if (!error) return false;
    
    const retryableErrors = [
      'timeout',
      'network',
      'connection',
      'temporary',
      'busy',
      'rate limit'
    ];
    
    const errorLower = error.toLowerCase();
    return retryableErrors.some(retryableError => errorLower.includes(retryableError));
  }

  /**
   * Update execution metrics
   */
  private updateExecutionMetrics(
    context: TradeExecutionContext, 
    result: TradeExecutionResult | null, 
    success: boolean
  ): void {
    if (success && result?.executed) {
      this.executionMetrics.successfulOrders++;
    } else {
      this.executionMetrics.failedOrders++;
    }
    
    // Update broker-specific metrics
    const brokerId = context.brokerId;
    let brokerMetrics = this.executionMetrics.brokerMetrics.get(brokerId);
    
    if (!brokerMetrics) {
      brokerMetrics = { orders: 0, successes: 0, averageLatency: 0 };
      this.executionMetrics.brokerMetrics.set(brokerId, brokerMetrics);
    }
    
    brokerMetrics.orders++;
    if (success && result?.executed) {
      brokerMetrics.successes++;
    }
    
    if (result) {
      const latency = result.executionTime.getTime() - context.startTime.getTime();
      brokerMetrics.averageLatency = 
        ((brokerMetrics.averageLatency * (brokerMetrics.orders - 1)) + latency) / brokerMetrics.orders;
    }
  }
}