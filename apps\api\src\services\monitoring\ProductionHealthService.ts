/**
 * Production Health Service
 * 
 * Comprehensive health monitoring for production deployment
 * Part of Task 7: Production Deployment and Monitoring
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { performance } from 'perf_hooks';
import { promisify } from 'util';
import type { 
  SystemHealth, 
  HealthCheckResult, 
  ProductionMetrics 
} from '@golddaddy/types';

interface HealthMetrics {
  uptime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  eventLoopLag: number;
  activeConnections: number;
  databaseConnections: number;
  lastFailoverTime?: Date;
  totalFailovers: number;
  systemErrors: number;
  auditIntegrityScore: number;
  complianceScore: number;
}

export class ProductionHealthService extends EventEmitter {
  private prisma: PrismaClient;
  private startTime: Date;
  private healthCheckInterval?: NodeJS.Timeout;
  private metrics: HealthMetrics;
  private isShuttingDown = false;

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
    this.startTime = new Date();
    this.metrics = this.initializeMetrics();
  }

  /**
   * Initialize health metrics
   */
  private initializeMetrics(): HealthMetrics {
    return {
      uptime: 0,
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      eventLoopLag: 0,
      activeConnections: 0,
      databaseConnections: 0,
      totalFailovers: 0,
      systemErrors: 0,
      auditIntegrityScore: 100,
      complianceScore: 100
    };
  }

  /**
   * Start production health monitoring
   */
  async startHealthMonitoring(intervalMs: number = 30000): Promise<void> {
    console.log('🏥 Starting production health monitoring...');

    // Initial health check
    await this.performHealthCheck();

    // Set up periodic health checks
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthCheck();
      } catch (error) {
        console.error('Health check error:', error);
        this.emit('healthCheckError', error);
      }
    }, intervalMs);

    console.log('✅ Production health monitoring started');
  }

  /**
   * Perform comprehensive health check
   */
  async performHealthCheck(): Promise<HealthCheckResult> {
    const startTime = performance.now();

    try {
      // Update basic metrics
      await this.updateSystemMetrics();

      // Check database connectivity
      const dbHealth = await this.checkDatabaseHealth();
      
      // Check broker system health
      const brokerHealth = await this.checkBrokerSystemHealth();
      
      // Check audit system integrity
      const auditHealth = await this.checkAuditSystemHealth();
      
      // Check compliance status
      const complianceHealth = await this.checkComplianceStatus();

      // Calculate overall health score
      const healthScore = this.calculateHealthScore(
        dbHealth, 
        brokerHealth, 
        auditHealth, 
        complianceHealth
      );

      const duration = performance.now() - startTime;
      
      const result: HealthCheckResult = {
        timestamp: new Date(),
        healthy: healthScore >= 80,
        score: healthScore,
        duration,
        components: {
          database: dbHealth,
          brokerSystem: brokerHealth,
          auditSystem: auditHealth,
          compliance: complianceHealth
        },
        metrics: this.metrics
      };

      // Emit health check event
      this.emit('healthCheck', result);

      // Store health check result
      await this.storeHealthCheckResult(result);

      return result;

    } catch (error) {
      console.error('Health check failed:', error);
      
      const failedResult: HealthCheckResult = {
        timestamp: new Date(),
        healthy: false,
        score: 0,
        duration: performance.now() - startTime,
        error: error.message,
        metrics: this.metrics
      };

      this.emit('healthCheck', failedResult);
      return failedResult;
    }
  }

  /**
   * Update system metrics
   */
  private async updateSystemMetrics(): Promise<void> {
    this.metrics.uptime = Date.now() - this.startTime.getTime();
    this.metrics.memoryUsage = process.memoryUsage();
    this.metrics.cpuUsage = process.cpuUsage();
    
    // Measure event loop lag
    const start = process.hrtime.bigint();
    await new Promise(resolve => setImmediate(resolve));
    const lag = Number(process.hrtime.bigint() - start) / 1e6; // Convert to ms
    this.metrics.eventLoopLag = lag;

    // Update database connection count (if available)
    try {
      // This would depend on your Prisma setup
      this.metrics.databaseConnections = 1; // Simplified
    } catch (error) {
      console.warn('Could not get database connection count:', error);
    }
  }

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<{ healthy: boolean; latency: number; error?: string }> {
    const start = performance.now();
    
    try {
      // Simple database ping
      await this.prisma.$queryRaw`SELECT 1`;
      const latency = performance.now() - start;
      
      return {
        healthy: latency < 1000, // Consider unhealthy if > 1s
        latency
      };
    } catch (error) {
      return {
        healthy: false,
        latency: performance.now() - start,
        error: error.message
      };
    }
  }

  /**
   * Check broker system health
   */
  private async checkBrokerSystemHealth(): Promise<{ healthy: boolean; brokersOnline: number; recentFailovers: number }> {
    try {
      // Count healthy brokers
      const healthyBrokers = await this.prisma.brokerConfiguration.count({
        where: { 
          status: 'ACTIVE',
          isHealthy: true
        }
      });

      // Count recent failovers (last hour)
      const recentFailovers = await this.prisma.failoverEvent.count({
        where: {
          initiatedAt: {
            gte: new Date(Date.now() - 3600000) // Last hour
          }
        }
      });

      this.metrics.totalFailovers = await this.prisma.failoverEvent.count();

      return {
        healthy: healthyBrokers > 0 && recentFailovers < 10,
        brokersOnline: healthyBrokers,
        recentFailovers
      };
    } catch (error) {
      console.error('Broker system health check failed:', error);
      return {
        healthy: false,
        brokersOnline: 0,
        recentFailovers: 0
      };
    }
  }

  /**
   * Check audit system health
   */
  private async checkAuditSystemHealth(): Promise<{ healthy: boolean; integrityScore: number; recentLogs: number }> {
    try {
      // Count recent audit logs
      const recentLogs = await this.prisma.auditLog.count({
        where: {
          timestamp: {
            gte: new Date(Date.now() - 3600000) // Last hour
          }
        }
      });

      // Simplified integrity check (in production, this would be more comprehensive)
      const integrityScore = 100; // Would implement actual hash chain verification
      this.metrics.auditIntegrityScore = integrityScore;

      return {
        healthy: integrityScore > 95,
        integrityScore,
        recentLogs
      };
    } catch (error) {
      console.error('Audit system health check failed:', error);
      return {
        healthy: false,
        integrityScore: 0,
        recentLogs: 0
      };
    }
  }

  /**
   * Check compliance status
   */
  private async checkComplianceStatus(): Promise<{ healthy: boolean; score: number; openIssues: number }> {
    try {
      // Count open compliance issues
      const openIssues = await this.prisma.complianceIssue?.count({
        where: { status: 'OPEN' }
      }) || 0;

      // Calculate compliance score (simplified)
      const score = Math.max(0, 100 - (openIssues * 10));
      this.metrics.complianceScore = score;

      return {
        healthy: score >= 80 && openIssues < 5,
        score,
        openIssues
      };
    } catch (error) {
      console.error('Compliance health check failed:', error);
      return {
        healthy: false,
        score: 0,
        openIssues: 0
      };
    }
  }

  /**
   * Calculate overall health score
   */
  private calculateHealthScore(
    dbHealth: any,
    brokerHealth: any,
    auditHealth: any,
    complianceHealth: any
  ): number {
    const weights = {
      database: 0.3,
      broker: 0.4,
      audit: 0.2,
      compliance: 0.1
    };

    const scores = {
      database: dbHealth.healthy ? 100 : 0,
      broker: brokerHealth.healthy ? 100 : 0,
      audit: auditHealth.healthy ? auditHealth.integrityScore : 0,
      compliance: complianceHealth.score
    };

    return Math.round(
      scores.database * weights.database +
      scores.broker * weights.broker +
      scores.audit * weights.audit +
      scores.compliance * weights.compliance
    );
  }

  /**
   * Store health check result for historical tracking
   */
  private async storeHealthCheckResult(result: HealthCheckResult): Promise<void> {
    try {
      // Store in a health_checks table (would need to add to schema)
      // For now, we'll just emit metrics
      this.emit('metrics', {
        timestamp: result.timestamp,
        health_score: result.score,
        is_healthy: result.healthy ? 1 : 0,
        check_duration_ms: result.duration,
        memory_usage_mb: this.metrics.memoryUsage.rss / 1024 / 1024,
        uptime_ms: this.metrics.uptime,
        event_loop_lag_ms: this.metrics.eventLoopLag
      });
    } catch (error) {
      console.warn('Failed to store health check result:', error);
    }
  }

  /**
   * Get current system health status
   */
  async getSystemHealth(): Promise<SystemHealth> {
    const healthResult = await this.performHealthCheck();
    
    return {
      status: healthResult.healthy ? 'healthy' : 'degraded',
      timestamp: new Date(),
      uptime: this.metrics.uptime,
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        api: true, // If we're responding, API is up
        database: healthResult.components?.database?.healthy || false,
        brokerSystem: healthResult.components?.brokerSystem?.healthy || false,
        auditSystem: healthResult.components?.auditSystem?.healthy || false
      },
      metrics: {
        memoryUsage: this.metrics.memoryUsage,
        cpuUsage: this.metrics.cpuUsage,
        eventLoopLag: this.metrics.eventLoopLag,
        activeConnections: this.metrics.activeConnections,
        totalFailovers: this.metrics.totalFailovers,
        complianceScore: this.metrics.complianceScore
      }
    };
  }

  /**
   * Get Prometheus metrics
   */
  getPrometheusMetrics(): string {
    const metrics = [
      `# HELP system_uptime_seconds System uptime in seconds`,
      `# TYPE system_uptime_seconds gauge`,
      `system_uptime_seconds ${this.metrics.uptime / 1000}`,
      
      `# HELP memory_usage_bytes Memory usage in bytes`,
      `# TYPE memory_usage_bytes gauge`,
      `memory_usage_bytes{type="rss"} ${this.metrics.memoryUsage.rss}`,
      `memory_usage_bytes{type="heapUsed"} ${this.metrics.memoryUsage.heapUsed}`,
      `memory_usage_bytes{type="heapTotal"} ${this.metrics.memoryUsage.heapTotal}`,
      
      `# HELP event_loop_lag_ms Event loop lag in milliseconds`,
      `# TYPE event_loop_lag_ms gauge`,
      `event_loop_lag_ms ${this.metrics.eventLoopLag}`,
      
      `# HELP total_failovers Total number of failover events`,
      `# TYPE total_failovers counter`,
      `total_failovers ${this.metrics.totalFailovers}`,
      
      `# HELP audit_integrity_score Audit trail integrity score (0-100)`,
      `# TYPE audit_integrity_score gauge`,
      `audit_integrity_score ${this.metrics.auditIntegrityScore}`,
      
      `# HELP compliance_score Compliance score (0-100)`,
      `# TYPE compliance_score gauge`,
      `compliance_score ${this.metrics.complianceScore}`,
    ];

    return `${metrics.join('\n')  }\n`;
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    this.isShuttingDown = true;
    console.log('🏥 Shutting down production health service...');

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Emit final health check
    this.emit('shutdown', {
      timestamp: new Date(),
      uptime: this.metrics.uptime
    });

    console.log('✅ Production health service shut down');
  }
}

export default ProductionHealthService;