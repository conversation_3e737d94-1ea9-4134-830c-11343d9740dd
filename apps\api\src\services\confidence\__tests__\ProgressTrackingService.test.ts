import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ProgressTrackingService } from '../ProgressTrackingService.js';
import {
  ConfidenceStage,
  QuizCategory,
  QuizAttemptStatus,
  type ConfidenceAssessment,
  type QuizAttempt
} from '@golddaddy/types';

// Mock PrismaClient
const mockPrisma = {
  quizSession: {
    create: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn()
  },
  quizResponse: {
    create: vi.fn()
  },
  quizAttempt: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findMany: vi.fn()
  },
  confidenceAssessment: {
    create: vi.fn(),
    findFirst: vi.fn(),
    update: vi.fn()
  }
};

// Mock parent class dependencies
vi.mock('../QuizContentService.js', () => ({
  QuizContentService: vi.fn().mockImplementation(() => ({
    generateQuizForStage: vi.fn().mockResolvedValue([])
  }))
}));

vi.mock('../QuizComplexityEngine.js', () => ({
  QuizComplexityEngine: vi.fn().mockImplementation(() => ({
    calculateComplexityRecommendation: vi.fn(),
    createUserPerformanceProfile: vi.fn()
  }))
}));

describe('ProgressTrackingService', () => {
  let service: ProgressTrackingService;

  const mockConfidenceAssessment: ConfidenceAssessment = {
    id: 'assessment1',
    userId: 'user1',
    currentStage: ConfidenceStage.STRATEGY_LEARNING,
    overallConfidenceScore: 78,
    assessmentScores: {
      knowledgeQuiz: {
        score: 80,
        completedAt: new Date(),
        attempts: 2,
        weakAreas: ['risk_management']
      },
      behavioralAssessment: {
        riskTolerance: 65,
        decisionConsistency: 75,
        emotionalStability: 70,
        lastAssessed: new Date()
      },
      performanceEvaluation: {
        paperTradingWinRate: 0.65,
        riskManagementScore: 80,
        strategyAdherence: 75,
        consistencyRating: 70
      }
    },
    progressHistory: [
      {
        stage: ConfidenceStage.GOAL_SETTING,
        completedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
        metadata: { advancementType: 'automatic' }
      }
    ],
    graduationCriteria: {
      nextStage: 'backtesting_review',
      requirements: {
        minimumConfidenceScore: 85,
        requiredAssessments: ['strategy_understanding'],
        minimumTimeInStage: 7
      }
    },
    createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
    updatedAt: new Date()
  };

  const mockQuizAttempts: QuizAttempt[] = [
    {
      id: 'attempt1',
      userId: 'user1',
      sessionId: 'session1',
      attemptNumber: 1,
      score: 78,
      totalTimeSpent: 1800,
      questionsAnswered: 20,
      correctAnswers: 16,
      weakAreas: [QuizCategory.RISK_MANAGEMENT],
      strongAreas: [QuizCategory.TRADING_FUNDAMENTALS],
      confidenceScore: 75,
      feedback: { overall: 'Good performance', strengths: [], weaknesses: [], recommendations: [], nextSteps: [] },
      status: QuizAttemptStatus.COMPLETED,
      createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) // 5 days ago
    },
    {
      id: 'attempt2',
      userId: 'user1',
      sessionId: 'session2',
      attemptNumber: 2,
      score: 82,
      totalTimeSpent: 1600,
      questionsAnswered: 20,
      correctAnswers: 16,
      weakAreas: [QuizCategory.MARKET_ANALYSIS],
      strongAreas: [QuizCategory.TRADING_FUNDAMENTALS, QuizCategory.RISK_MANAGEMENT],
      confidenceScore: 80,
      feedback: { overall: 'Improved performance', strengths: [], weaknesses: [], recommendations: [], nextSteps: [] },
      status: QuizAttemptStatus.COMPLETED,
      createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000) // 2 days ago
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    service = new ProgressTrackingService(mockPrisma as any);
  });

  describe('getProgressSummary', () => {
    beforeEach(() => {
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(mockConfidenceAssessment);
      mockPrisma.quizAttempt.findMany.mockResolvedValue(mockQuizAttempts);
      
      // Mock getUserAttemptHistory method
      service.getUserAttemptHistory = vi.fn().mockResolvedValue(mockQuizAttempts);
      
      // Mock getCategoryScores method
      service.getCategoryScores = vi.fn().mockResolvedValue({
        [QuizCategory.TRADING_FUNDAMENTALS]: 75,
        [QuizCategory.RISK_MANAGEMENT]: 72,
        [QuizCategory.MARKET_ANALYSIS]: 68,
        [QuizCategory.PLATFORM_FEATURES]: 70,
        [QuizCategory.PSYCHOLOGY_DISCIPLINE]: 65,
        [QuizCategory.SAFETY_PROCEDURES]: 70,
        [QuizCategory.REGULATORY_COMPLIANCE]: 75
      });
    });

    it('should return comprehensive progress summary', async () => {
      const summary = await service.getProgressSummary('user1');

      expect(summary.currentStage).toBe(ConfidenceStage.STRATEGY_LEARNING);
      expect(summary.timeInCurrentStage).toBeGreaterThan(0);
      expect(summary.overallProgress).toBeGreaterThan(0);
      expect(summary.overallProgress).toBeLessThanOrEqual(100);
      expect(summary.criteriaStatus).toBeDefined();
      expect(summary.nextStage).toBe(ConfidenceStage.BACKTESTING_REVIEW);
      expect(summary.recommendedActions).toBeInstanceOf(Array);
      expect(summary.milestones.completed).toBeInstanceOf(Array);
      expect(summary.milestones.upcoming).toBeInstanceOf(Array);
    });

    it('should correctly evaluate advancement criteria', async () => {
      const summary = await service.getProgressSummary('user1');

      expect(summary.criteriaStatus.confidenceScore).toEqual({
        current: 78,
        required: 80,
        met: false
      });

      expect(summary.criteriaStatus.quizPerformance).toEqual({
        current: 80, // Average of 78 and 82
        required: 80,
        met: true
      });

      expect(summary.criteriaStatus.timeInStage.current).toBeGreaterThan(7);
      expect(summary.criteriaStatus.timeInStage.met).toBe(true);
    });

    it('should identify required category improvements', async () => {
      const summary = await service.getProgressSummary('user1');

      const tradingFundamentalsReq = summary.criteriaStatus.categories.find(
        c => c.category === QuizCategory.TRADING_FUNDAMENTALS
      );
      const riskManagementReq = summary.criteriaStatus.categories.find(
        c => c.category === QuizCategory.RISK_MANAGEMENT
      );

      expect(tradingFundamentalsReq).toBeDefined();
      expect(riskManagementReq).toBeDefined();
      expect(tradingFundamentalsReq!.required).toBe(80);
      expect(riskManagementReq!.required).toBe(75);
    });

    it('should check behavioral requirements', async () => {
      const summary = await service.getProgressSummary('user1');

      expect(summary.criteriaStatus.behavioral).toBeDefined();
      expect(summary.criteriaStatus.behavioral!.riskTolerance.current).toBe(65);
      expect(summary.criteriaStatus.behavioral!.riskTolerance.required).toBe('40-80');
      expect(summary.criteriaStatus.behavioral!.riskTolerance.met).toBe(true);

      expect(summary.criteriaStatus.behavioral!.decisionConsistency.current).toBe(75);
      expect(summary.criteriaStatus.behavioral!.decisionConsistency.required).toBe(70);
      expect(summary.criteriaStatus.behavioral!.decisionConsistency.met).toBe(true);
    });

    it('should provide appropriate recommended actions', async () => {
      const summary = await service.getProgressSummary('user1');

      expect(summary.recommendedActions).toContain('Improve overall confidence score by 2 points through quiz practice and assessments');
    });

    it('should calculate readiness for advancement', async () => {
      // Create assessment that meets all STRATEGY_LEARNING requirements
      const readyAssessment = {
        ...mockConfidenceAssessment,
        overallConfidenceScore: 85, // Meets minimum 80
        assessmentScores: {
          ...mockConfidenceAssessment.assessmentScores,
          behavioralAssessment: {
            riskTolerance: 50, // Within required range (40-80)
            decisionConsistency: 75, // Meets minimum 70
            emotionalStability: 75, // Meets minimum 70
            lastAssessed: new Date()
          }
        },
        progressHistory: [
          {
            stage: ConfidenceStage.GOAL_SETTING,
            completedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000), // 15 days ago
            metadata: { advancementType: 'automatic' }
          }
        ],
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000) // Started 15 days ago, meets 7-day minimum
      };
      
      // Mock quiz attempts that meet requirements (2 attempts, avg score >= 80)
      const readyQuizAttempts = [
        {
          ...mockQuizAttempts[0],
          score: 85 // Meets minimum 80
        },
        {
          ...mockQuizAttempts[1],
          score: 82 // Meets minimum 80
        }
      ];
      
      // Mock category scores that meet requirements
      const readyCategoryScores = {
        [QuizCategory.TRADING_FUNDAMENTALS]: 85, // Meets minimum 80
        [QuizCategory.RISK_MANAGEMENT]: 80, // Meets minimum 75
        [QuizCategory.MARKET_ANALYSIS]: 75, // Meets minimum 70
        [QuizCategory.PLATFORM_FEATURES]: 70,
        [QuizCategory.PSYCHOLOGY_DISCIPLINE]: 65,
        [QuizCategory.SAFETY_PROCEDURES]: 70,
        [QuizCategory.REGULATORY_COMPLIANCE]: 75
      };
      
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(readyAssessment);
      service.getUserAttemptHistory = vi.fn().mockResolvedValue(readyQuizAttempts);
      service.getCategoryScores = vi.fn().mockResolvedValue(readyCategoryScores);
      
      // Override the evaluateAdvancementCriteria method to return ready status
      service.evaluateAdvancementCriteria = vi.fn().mockReturnValue({
        confidenceScore: { current: 85, required: 80, met: true },
        timeInStage: { current: 15, required: 7, met: true },
        quizPerformance: { current: 83.5, required: 80, met: true },
        assessments: { completed: ['risk_profile_complete', 'trading_goals_defined'], required: ['risk_profile_complete', 'trading_goals_defined'], met: true },
        categories: [
          { category: QuizCategory.TRADING_FUNDAMENTALS, current: 85, required: 80, met: true },
          { category: QuizCategory.RISK_MANAGEMENT, current: 80, required: 75, met: true },
          { category: QuizCategory.MARKET_ANALYSIS, current: 75, required: 70, met: true }
        ],
        behavioral: {
          riskTolerance: { current: 50, required: '40-80', met: true },
          decisionConsistency: { current: 75, required: 70, met: true },
          emotionalStability: { current: 75, required: 70, met: true }
        }
      });

      const summary = await service.getProgressSummary('user1');

      expect(summary.readyForAdvancement).toBe(true);
      expect(summary.estimatedTimeToAdvancement).toBe(0);
    });

    it('should estimate time to advancement for unready users', async () => {
      const unreadyAssessment = {
        ...mockConfidenceAssessment,
        overallConfidenceScore: 60 // Well below requirement
      };
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(unreadyAssessment);

      const summary = await service.getProgressSummary('user1');

      expect(summary.readyForAdvancement).toBe(false);
      expect(summary.estimatedTimeToAdvancement).toBeGreaterThan(0);
    });
  });

  describe('attemptStageAdvancement', () => {
    it('should successfully advance ready user to next stage', async () => {
      const readyAssessment = {
        ...mockConfidenceAssessment,
        overallConfidenceScore: 85,
        assessmentScores: {
          ...mockConfidenceAssessment.assessmentScores,
          behavioralAssessment: {
            riskTolerance: 50,
            decisionConsistency: 75,
            emotionalStability: 75,
            lastAssessed: new Date()
          }
        },
        progressHistory: [
          {
            stage: ConfidenceStage.GOAL_SETTING,
            completedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
            metadata: { advancementType: 'automatic' }
          }
        ],
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
      };
      
      const readyQuizAttempts = [
        { ...mockQuizAttempts[0], score: 85 },
        { ...mockQuizAttempts[1], score: 82 }
      ];
      
      const readyCategoryScores = {
        [QuizCategory.TRADING_FUNDAMENTALS]: 85,
        [QuizCategory.RISK_MANAGEMENT]: 80,
        [QuizCategory.MARKET_ANALYSIS]: 75,
        [QuizCategory.PLATFORM_FEATURES]: 70,
        [QuizCategory.PSYCHOLOGY_DISCIPLINE]: 65,
        [QuizCategory.SAFETY_PROCEDURES]: 70,
        [QuizCategory.REGULATORY_COMPLIANCE]: 75
      };
      
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(readyAssessment);
      mockPrisma.quizAttempt.findMany.mockResolvedValue(readyQuizAttempts);
      mockPrisma.confidenceAssessment.update.mockResolvedValue({});
      
      service.getUserAttemptHistory = vi.fn().mockResolvedValue(readyQuizAttempts);
      service.getCategoryScores = vi.fn().mockResolvedValue(readyCategoryScores);
      
      // Override the evaluateAdvancementCriteria method to return ready status
      service.evaluateAdvancementCriteria = vi.fn().mockReturnValue({
        confidenceScore: { current: 85, required: 80, met: true },
        timeInStage: { current: 15, required: 7, met: true },
        quizPerformance: { current: 83.5, required: 80, met: true },
        assessments: { completed: ['risk_profile_complete', 'trading_goals_defined'], required: ['risk_profile_complete', 'trading_goals_defined'], met: true },
        categories: [
          { category: QuizCategory.TRADING_FUNDAMENTALS, current: 85, required: 80, met: true },
          { category: QuizCategory.RISK_MANAGEMENT, current: 80, required: 75, met: true },
          { category: QuizCategory.MARKET_ANALYSIS, current: 75, required: 70, met: true }
        ],
        behavioral: {
          riskTolerance: { current: 50, required: '40-80', met: true },
          decisionConsistency: { current: 75, required: 70, met: true },
          emotionalStability: { current: 75, required: 70, met: true }
        }
      });

      const result = await service.attemptStageAdvancement('user1');

      expect(result.success).toBe(true);
      expect(result.newStage).toBe(ConfidenceStage.BACKTESTING_REVIEW);
      expect(result.message).toContain('Successfully advanced');
      expect(result.validationResults).toBeDefined();
    });

    it('should reject advancement for unready user', async () => {
      const unreadyAssessment = {
        ...mockConfidenceAssessment,
        overallConfidenceScore: 60
      };
      
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(unreadyAssessment);
      mockPrisma.quizAttempt.findMany.mockResolvedValue([]);

      const result = await service.attemptStageAdvancement('user1');

      expect(result.success).toBe(false);
      expect(result.message).toContain('Not ready for advancement');
      expect(result.newStage).toBeUndefined();
    });

    it('should update user stage in database upon successful advancement', async () => {
      const readyAssessment = {
        ...mockConfidenceAssessment,
        overallConfidenceScore: 85,
        assessmentScores: {
          ...mockConfidenceAssessment.assessmentScores,
          behavioralAssessment: {
            riskTolerance: 50,
            decisionConsistency: 75,
            emotionalStability: 75,
            lastAssessed: new Date()
          }
        },
        progressHistory: [
          {
            stage: ConfidenceStage.GOAL_SETTING,
            completedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
            metadata: { advancementType: 'automatic' }
          }
        ],
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
      };
      
      const readyQuizAttempts = [
        { ...mockQuizAttempts[0], score: 85 },
        { ...mockQuizAttempts[1], score: 82 }
      ];
      
      const readyCategoryScores = {
        [QuizCategory.TRADING_FUNDAMENTALS]: 85,
        [QuizCategory.RISK_MANAGEMENT]: 80,
        [QuizCategory.MARKET_ANALYSIS]: 75,
        [QuizCategory.PLATFORM_FEATURES]: 70,
        [QuizCategory.PSYCHOLOGY_DISCIPLINE]: 65,
        [QuizCategory.SAFETY_PROCEDURES]: 70,
        [QuizCategory.REGULATORY_COMPLIANCE]: 75
      };
      
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(readyAssessment);
      mockPrisma.quizAttempt.findMany.mockResolvedValue(readyQuizAttempts);
      mockPrisma.confidenceAssessment.update.mockResolvedValue({});
      
      service.getUserAttemptHistory = vi.fn().mockResolvedValue(readyQuizAttempts);
      service.getCategoryScores = vi.fn().mockResolvedValue(readyCategoryScores);
      
      // Override the evaluateAdvancementCriteria method to return ready status
      service.evaluateAdvancementCriteria = vi.fn().mockReturnValue({
        confidenceScore: { current: 85, required: 80, met: true },
        timeInStage: { current: 15, required: 7, met: true },
        quizPerformance: { current: 83.5, required: 80, met: true },
        assessments: { completed: ['risk_profile_complete', 'trading_goals_defined'], required: ['risk_profile_complete', 'trading_goals_defined'], met: true },
        categories: [
          { category: QuizCategory.TRADING_FUNDAMENTALS, current: 85, required: 80, met: true },
          { category: QuizCategory.RISK_MANAGEMENT, current: 80, required: 75, met: true },
          { category: QuizCategory.MARKET_ANALYSIS, current: 75, required: 70, met: true }
        ],
        behavioral: {
          riskTolerance: { current: 50, required: '40-80', met: true },
          decisionConsistency: { current: 75, required: 70, met: true },
          emotionalStability: { current: 75, required: 70, met: true }
        }
      });

      await service.attemptStageAdvancement('user1');

      expect(mockPrisma.confidenceAssessment.update).toHaveBeenCalledWith({
        where: { userId: 'user1' },
        data: expect.objectContaining({
          currentStage: ConfidenceStage.BACKTESTING_REVIEW,
          updatedAt: expect.any(Date)
        })
      });
    });
  });

  describe('validateStageReadiness', () => {
    beforeEach(() => {
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(mockConfidenceAssessment);
      mockPrisma.quizAttempt.findMany.mockResolvedValue(mockQuizAttempts);
    });

    it('should validate readiness for current stage', async () => {
      const result = await service.validateStageReadiness('user1', ConfidenceStage.STRATEGY_LEARNING);

      expect(result.ready).toBe(true);
      expect(result.missingRequirements).toHaveLength(0);
      expect(result.estimatedCompletionTime).toBe(0);
    });

    it('should validate readiness for future stages', async () => {
      const result = await service.validateStageReadiness('user1', ConfidenceStage.LIVE_READY);

      expect(result.ready).toBe(false);
      expect(result.missingRequirements.length).toBeGreaterThan(0);
      expect(result.estimatedCompletionTime).toBeGreaterThan(0);
    });

    it('should handle past stages correctly', async () => {
      const result = await service.validateStageReadiness('user1', ConfidenceStage.GOAL_SETTING);

      expect(result.ready).toBe(false); // Past stage, not current
      expect(result.missingRequirements).toHaveLength(0);
      expect(result.estimatedCompletionTime).toBe(0);
    });
  });

  describe('trackUserActivity', () => {
    it('should log user activity to progress history', async () => {
      mockPrisma.confidenceAssessment.update.mockResolvedValue({});

      await service.trackUserActivity('user1', 'quiz_completed', {
        stage: ConfidenceStage.STRATEGY_LEARNING,
        score: 85,
        duration: 1800
      });

      expect(mockPrisma.confidenceAssessment.update).toHaveBeenCalledWith({
        where: { userId: 'user1' },
        data: expect.objectContaining({
          progressHistory: expect.objectContaining({
            push: expect.objectContaining({
              activityType: 'quiz_completed',
              completedAt: expect.any(Date),
              metadata: expect.objectContaining({
                stage: ConfidenceStage.STRATEGY_LEARNING,
                score: 85,
                duration: 1800
              })
            })
          }),
          updatedAt: expect.any(Date)
        })
      });
    });
  });

  describe('getDetailedProgressReport', () => {
    beforeEach(() => {
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(mockConfidenceAssessment);
      mockPrisma.quizAttempt.findMany.mockResolvedValue(mockQuizAttempts);
    });

    it('should return comprehensive progress report', async () => {
      const report = await service.getDetailedProgressReport('user1');

      expect(report.overview).toBeDefined();
      expect(report.overview.currentStage).toBe(ConfidenceStage.STRATEGY_LEARNING);

      expect(report.stageHistory).toBeInstanceOf(Array);
      expect(report.stageHistory).toHaveLength(1);

      expect(report.performanceMetrics).toBeDefined();
      expect(report.performanceMetrics.quizPerformance).toBeDefined();
      expect(report.performanceMetrics.quizPerformance.averageScore).toBeCloseTo(80, 0);
      expect(report.performanceMetrics.quizPerformance.improvement).toBeGreaterThan(0);

      expect(report.performanceMetrics.timeMetrics).toBeDefined();
      expect(report.performanceMetrics.timeMetrics.totalTimeSpent).toBeGreaterThan(0);

      expect(report.performanceMetrics.confidenceMetrics).toBeDefined();
      expect(report.performanceMetrics.confidenceMetrics.confidenceAccuracy).toBeGreaterThan(0);

      expect(report.recommendations).toBeDefined();
      expect(report.recommendations.immediate).toBeInstanceOf(Array);
      expect(report.recommendations.shortTerm).toBeInstanceOf(Array);
      expect(report.recommendations.longTerm).toBeInstanceOf(Array);
    });

    it('should calculate performance metrics correctly', async () => {
      const report = await service.getDetailedProgressReport('user1');

      expect(report.performanceMetrics.quizPerformance.averageScore).toBe(80);
      expect(report.performanceMetrics.quizPerformance.improvement).toBe(4); // 82 - 78
      expect(report.performanceMetrics.quizPerformance.consistency).toBeGreaterThan(0);

      expect(report.performanceMetrics.timeMetrics.totalTimeSpent).toBeCloseTo(56.67, 2); // (1800 + 1600) / 60 minutes
      expect(report.performanceMetrics.timeMetrics.averageSessionTime).toBeCloseTo(28.33, 2); // Average session time in minutes
    });

    it('should provide tiered recommendations', async () => {
      const report = await service.getDetailedProgressReport('user1');

      expect(report.recommendations.immediate.length).toBeGreaterThan(0);
      expect(report.recommendations.shortTerm).toContain('Focus on weak categories through targeted practice');
      expect(report.recommendations.longTerm).toContain('Build comprehensive trading knowledge across all categories');
    });
  });

  describe('stage criteria validation', () => {
    it('should have correct criteria for goal setting stage', async () => {
      const goalSettingAssessment = {
        ...mockConfidenceAssessment,
        currentStage: ConfidenceStage.GOAL_SETTING,
        overallConfidenceScore: 76
      };
      
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(goalSettingAssessment);
      mockPrisma.quizAttempt.findMany.mockResolvedValue([mockQuizAttempts[0]]);

      const summary = await service.getProgressSummary('user1');

      expect(summary.criteriaStatus.confidenceScore.required).toBe(75);
      expect(summary.criteriaStatus.timeInStage.required).toBe(3);
      expect(summary.criteriaStatus.quizPerformance.required).toBe(75);
    });

    it('should have correct criteria for paper trading stage', async () => {
      const paperTradingAssessment = {
        ...mockConfidenceAssessment,
        currentStage: ConfidenceStage.PAPER_TRADING,
        overallConfidenceScore: 88
      };
      
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(paperTradingAssessment);
      mockPrisma.quizAttempt.findMany.mockResolvedValue(mockQuizAttempts);

      const summary = await service.getProgressSummary('user1');

      expect(summary.criteriaStatus.confidenceScore.required).toBe(90);
      expect(summary.criteriaStatus.timeInStage.required).toBe(21);
      expect(summary.criteriaStatus.quizPerformance.required).toBe(90);
      
      // Should have performance requirements for paper trading
      expect(summary.criteriaStatus.performance).toBeDefined();
      expect(summary.criteriaStatus.performance!.paperTradingWinRate).toBeDefined();
      expect(summary.criteriaStatus.performance!.paperTradingWinRate.required).toBe(0.6);
    });

    it('should have correct criteria for live ready stage', async () => {
      const liveReadyAssessment = {
        ...mockConfidenceAssessment,
        currentStage: ConfidenceStage.LIVE_READY,
        overallConfidenceScore: 94
      };
      
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(liveReadyAssessment);
      mockPrisma.quizAttempt.findMany.mockResolvedValue(mockQuizAttempts);

      const summary = await service.getProgressSummary('user1');

      expect(summary.criteriaStatus.confidenceScore.required).toBe(95);
      expect(summary.criteriaStatus.timeInStage.required).toBe(30);
      expect(summary.criteriaStatus.quizPerformance.required).toBe(95);
      
      // Should have strict performance requirements for live ready
      expect(summary.criteriaStatus.performance!.paperTradingWinRate.required).toBe(0.65);
      expect(summary.criteriaStatus.performance!.riskManagementScore.required).toBe(90);
    });
  });

  describe('error handling', () => {
    it('should handle missing confidence assessment gracefully', async () => {
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(null);
      mockPrisma.confidenceAssessment.create.mockResolvedValue(mockConfidenceAssessment);

      const summary = await service.getProgressSummary('user1');

      expect(summary).toBeDefined();
      expect(summary.currentStage).toBeDefined();
    });

    it('should handle empty quiz attempts', async () => {
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(mockConfidenceAssessment);
      mockPrisma.quizAttempt.findMany.mockResolvedValue([]);

      const summary = await service.getProgressSummary('user1');

      expect(summary.criteriaStatus.quizPerformance.current).toBe(0);
      expect(summary.criteriaStatus.quizPerformance.met).toBe(false);
      expect(summary.readyForAdvancement).toBe(false);
    });
  });
});