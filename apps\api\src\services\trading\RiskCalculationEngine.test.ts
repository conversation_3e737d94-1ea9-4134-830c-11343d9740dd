/**
 * Risk Calculation Engine Tests
 * 
 * Comprehensive test suite for RiskCalculationEngine with 100% coverage
 * as required for financial calculations.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import { describe, it, expect, beforeEach } from 'vitest';
import Decimal from 'decimal.js';
import { 
  RiskCalculationEngine, 
  createRiskCalculationEngine,
  type RiskCalculationParams,
  type KellyCriterionParams 
} from './RiskCalculationEngine';

describe('RiskCalculationEngine', () => {
  let riskEngine: RiskCalculationEngine;
  
  beforeEach(() => {
    riskEngine = new RiskCalculationEngine();
  });

  describe('Constructor and Factory', () => {
    it('should create instance successfully', () => {
      expect(riskEngine).toBeInstanceOf(RiskCalculationEngine);
    });

    it('should create instance via factory function', () => {
      const engine = createRiskCalculationEngine();
      expect(engine).toBeInstanceOf(RiskCalculationEngine);
    });
  });

  describe('calculatePositionSize', () => {
    const baseParams: RiskCalculationParams = {
      accountBalance: new Decimal(10000),
      riskTolerance: 'moderate',
      marketVolatility: 20,
      userExperienceLevel: 'intermediate',
      correlationRisk: 0
    };

    it('should calculate position size for conservative risk tolerance', () => {
      const params = { ...baseParams, riskTolerance: 'conservative' as const };
      const result = riskEngine.calculatePositionSize(params);
      
      // 1% * 0.8 experience * 0.85 volatility adjustment = 0.68%
      expect(result.riskPercentage).toBe(0.68);
      expect(result.recommendedSize.toNumber()).toBe(68); // $68 for 0.68% risk
      expect(result.experienceAdjustment).toBe(0.8);
      expect(result.reasoning).toContain('Base risk tolerance: conservative (1%)');
    });

    it('should calculate position size for moderate risk tolerance', () => {
      const result = riskEngine.calculatePositionSize(baseParams);
      
      // 2.5% * 0.8 experience * 0.85 volatility adjustment = 1.7%
      expect(result.riskPercentage).toBe(1.7);
      expect(result.recommendedSize.toNumber()).toBe(170); // $170 for 1.7% risk
      expect(result.reasoning).toContain('Base risk tolerance: moderate (2.5%)');
    });

    it('should calculate position size for aggressive risk tolerance', () => {
      const params = { ...baseParams, riskTolerance: 'aggressive' as const };
      const result = riskEngine.calculatePositionSize(params);
      
      // 5% * 0.8 experience * 0.85 volatility adjustment = 3.4%
      expect(result.riskPercentage).toBe(3.4);
      expect(result.recommendedSize.toNumber()).toBe(340); // $340 for 3.4% risk
      expect(result.reasoning).toContain('Base risk tolerance: aggressive (5%)');
    });

    it('should apply experience level adjustments correctly', () => {
      const beginnerParams = { ...baseParams, userExperienceLevel: 'beginner' as const };
      const intermediateParams = { ...baseParams, userExperienceLevel: 'intermediate' as const };
      const advancedParams = { ...baseParams, userExperienceLevel: 'advanced' as const };

      const beginnerResult = riskEngine.calculatePositionSize(beginnerParams);
      const intermediateResult = riskEngine.calculatePositionSize(intermediateParams);
      const advancedResult = riskEngine.calculatePositionSize(advancedParams);

      expect(beginnerResult.experienceAdjustment).toBe(0.5);
      expect(intermediateResult.experienceAdjustment).toBe(0.8);
      expect(advancedResult.experienceAdjustment).toBe(1.0);

      expect(beginnerResult.riskPercentage).toBe(1.0625); // 2.5% * 0.5 * 0.85 volatility
      expect(intermediateResult.riskPercentage).toBe(1.7); // 2.5% * 0.8 * 0.85 volatility
      expect(advancedResult.riskPercentage).toBe(2.125); // 2.5% * 1.0 * 0.85 volatility
    });

    it('should apply volatility adjustments correctly', () => {
      const lowVolParams = { ...baseParams, marketVolatility: 10 };
      const medVolParams = { ...baseParams, marketVolatility: 20 };
      const highVolParams = { ...baseParams, marketVolatility: 40 };

      const lowVolResult = riskEngine.calculatePositionSize(lowVolParams);
      const medVolResult = riskEngine.calculatePositionSize(medVolParams);
      const highVolResult = riskEngine.calculatePositionSize(highVolParams);

      expect(lowVolResult.volatilityAdjustment).toBe(1.0);
      expect(medVolResult.volatilityAdjustment).toBe(0.85);
      expect(highVolResult.volatilityAdjustment).toBe(0.5);

      // Higher volatility should result in smaller position sizes
      expect(lowVolResult.recommendedSize.gt(medVolResult.recommendedSize)).toBe(true);
      expect(medVolResult.recommendedSize.gt(highVolResult.recommendedSize)).toBe(true);
    });

    it('should apply correlation adjustments correctly', () => {
      const lowCorrParams = { ...baseParams, correlationRisk: 0.2 };
      const highCorrParams = { ...baseParams, correlationRisk: 0.8 };

      const lowCorrResult = riskEngine.calculatePositionSize(lowCorrParams);
      const highCorrResult = riskEngine.calculatePositionSize(highCorrParams);

      expect(lowCorrResult.correlationAdjustment).toBe(0.9); // 1 - (0.2 * 0.5)
      expect(highCorrResult.correlationAdjustment).toBe(0.6); // 1 - (0.8 * 0.5)

      // Higher correlation should result in smaller position sizes
      expect(lowCorrResult.recommendedSize.gt(highCorrResult.recommendedSize)).toBe(true);
    });

    it('should enforce maximum position size limits', () => {
      const maxPositionSize = new Decimal(100);
      const params = { ...baseParams, maxPositionSize };
      
      const result = riskEngine.calculatePositionSize(params);
      
      expect(result.recommendedSize.lte(maxPositionSize)).toBe(true);
      expect(result.maxAllowedSize.eq(maxPositionSize)).toBe(true);
    });

    it('should include comprehensive reasoning', () => {
      const result = riskEngine.calculatePositionSize(baseParams);
      
      expect(result.reasoning).toContain('Base risk tolerance: moderate (2.5%)');
      expect(result.reasoning).toContain('Experience adjustment: intermediate (0.8x)');
      expect(result.reasoning).toContain('Volatility adjustment: 20% volatility (0.85x)');
      expect(result.reasoning).toContain('Correlation adjustment: 0 correlation (1x)');
      expect(result.reasoning.length).toBeGreaterThan(0);
    });
  });

  describe('calculateKellyPosition', () => {
    const baseKellyParams: KellyCriterionParams = {
      winProbability: 0.6,
      averageWin: new Decimal(100),
      averageLoss: new Decimal(50),
      accountBalance: new Decimal(10000)
    };

    it('should calculate Kelly position correctly', () => {
      const result = riskEngine.calculateKellyPosition(baseKellyParams);
      
      // Kelly formula: f* = (bp - q) / b
      // b = 100/50 = 2, p = 0.6, q = 0.4
      // f* = (2 * 0.6 - 0.4) / 2 = (1.2 - 0.4) / 2 = 0.4 = 40%
      // But capped at 25% default maximum
      expect(result.toNumber()).toBe(2500); // 25% of $10,000
    });

    it('should respect maximum Kelly percentage', () => {
      const params = { ...baseKellyParams, maxKellyPercentage: 10 };
      const result = riskEngine.calculateKellyPosition(params);
      
      expect(result.toNumber()).toBe(1000); // 10% of $10,000
    });

    it('should return zero for negative Kelly fraction', () => {
      const params = { 
        ...baseKellyParams, 
        winProbability: 0.3, // Low win probability
        averageWin: new Decimal(50),
        averageLoss: new Decimal(100)
      };
      const result = riskEngine.calculateKellyPosition(params);
      
      expect(result.toNumber()).toBe(0);
    });

    it('should handle edge case with zero average loss', () => {
      const params = { ...baseKellyParams, averageLoss: new Decimal(0.01) };
      const result = riskEngine.calculateKellyPosition(params);
      
      expect(result.gte(0)).toBe(true);
      expect(result.lte(new Decimal(2500))).toBe(true); // Should not exceed 25%
    });
  });

  describe('calculateRiskMetrics', () => {
    const openPositions = [
      { size: new Decimal(100), symbol: 'EURUSD', unrealizedPnL: new Decimal(50) },
      { size: new Decimal(200), symbol: 'GBPUSD', unrealizedPnL: new Decimal(-30) },
      { size: new Decimal(150), symbol: 'USDJPY', unrealizedPnL: new Decimal(20) }
    ];

    it('should calculate basic risk metrics correctly', () => {
      const accountBalance = new Decimal(10000);
      const result = riskEngine.calculateRiskMetrics(accountBalance, openPositions, 'moderate');
      
      expect(result.currentRiskExposure.toNumber()).toBe(450); // 100 + 200 + 150
      expect(result.maxDailyRisk.toNumber()).toBe(250); // 2.5% of 10,000
      expect(result.portfolioVaR.toNumber()).toBe(9); // 2% of 450
      expect(result.riskBudgetUtilization).toBe(100); // Capped at 100%
    });

    it('should handle empty positions array', () => {
      const accountBalance = new Decimal(10000);
      const result = riskEngine.calculateRiskMetrics(accountBalance, [], 'moderate');
      
      expect(result.currentRiskExposure.toNumber()).toBe(0);
      expect(result.diversificationRatio).toBe(1);
      expect(result.riskBudgetUtilization).toBe(0);
    });

    it('should calculate diversification ratio correctly', () => {
      const accountBalance = new Decimal(10000);
      const singlePosition = [{ size: new Decimal(100), symbol: 'EURUSD', unrealizedPnL: new Decimal(50) }];
      
      const singleResult = riskEngine.calculateRiskMetrics(accountBalance, singlePosition, 'moderate');
      const multiResult = riskEngine.calculateRiskMetrics(accountBalance, openPositions, 'moderate');
      
      expect(singleResult.diversificationRatio).toBe(0); // No diversification with single position
      expect(multiResult.diversificationRatio).toBeGreaterThan(0); // Some diversification
      expect(multiResult.diversificationRatio).toBeLessThanOrEqual(1); // Max diversification is 1
    });
  });

  describe('validateParams', () => {
    const validParams: RiskCalculationParams = {
      accountBalance: new Decimal(10000),
      riskTolerance: 'moderate',
      marketVolatility: 20,
      userExperienceLevel: 'intermediate',
      correlationRisk: 0.5
    };

    it('should validate correct parameters', () => {
      const result = riskEngine.validateParams(validParams);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject negative account balance', () => {
      const params = { ...validParams, accountBalance: new Decimal(-1000) };
      const result = riskEngine.validateParams(params);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Account balance must be positive');
    });

    it('should reject zero account balance', () => {
      const params = { ...validParams, accountBalance: new Decimal(0) };
      const result = riskEngine.validateParams(params);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Account balance must be positive');
    });

    it('should reject invalid market volatility', () => {
      const negativeVolParams = { ...validParams, marketVolatility: -10 };
      const highVolParams = { ...validParams, marketVolatility: 150 };
      
      const negativeResult = riskEngine.validateParams(negativeVolParams);
      const highResult = riskEngine.validateParams(highVolParams);
      
      expect(negativeResult.isValid).toBe(false);
      expect(highResult.isValid).toBe(false);
      expect(negativeResult.errors).toContain('Market volatility must be between 0 and 100 percent');
      expect(highResult.errors).toContain('Market volatility must be between 0 and 100 percent');
    });

    it('should reject invalid correlation risk', () => {
      const negativeParams = { ...validParams, correlationRisk: -0.5 };
      const highParams = { ...validParams, correlationRisk: 1.5 };
      
      const negativeResult = riskEngine.validateParams(negativeParams);
      const highResult = riskEngine.validateParams(highParams);
      
      expect(negativeResult.isValid).toBe(false);
      expect(highResult.isValid).toBe(false);
      expect(negativeResult.errors).toContain('Correlation risk must be between 0 and 1');
      expect(highResult.errors).toContain('Correlation risk must be between 0 and 1');
    });

    it('should reject invalid maximum position size', () => {
      const params = { ...validParams, maxPositionSize: new Decimal(-100) };
      const result = riskEngine.validateParams(params);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Maximum position size must be positive if specified');
    });

    it('should collect multiple validation errors', () => {
      const params = {
        ...validParams,
        accountBalance: new Decimal(-1000),
        marketVolatility: -10,
        correlationRisk: 1.5
      };
      const result = riskEngine.validateParams(params);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(3);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle very small account balances', () => {
      const params: RiskCalculationParams = {
        accountBalance: new Decimal(0.01),
        riskTolerance: 'conservative',
        marketVolatility: 10,
        userExperienceLevel: 'beginner'
      };
      
      const result = riskEngine.calculatePositionSize(params);
      expect(result.recommendedSize.gte(0)).toBe(true);
    });

    it('should handle very large account balances', () => {
      const params: RiskCalculationParams = {
        accountBalance: new Decimal(**********), // $1B
        riskTolerance: 'aggressive',
        marketVolatility: 15,
        userExperienceLevel: 'advanced'
      };
      
      const result = riskEngine.calculatePositionSize(params);
      expect(result.recommendedSize.gte(0)).toBe(true);
      expect(result.riskPercentage).toBeGreaterThan(0);
    });

    it('should handle extreme volatility values', () => {
      const lowVolParams: RiskCalculationParams = {
        accountBalance: new Decimal(10000),
        riskTolerance: 'moderate',
        marketVolatility: 1,
        userExperienceLevel: 'intermediate'
      };
      
      const highVolParams = { ...lowVolParams, marketVolatility: 99 };
      
      const lowVolResult = riskEngine.calculatePositionSize(lowVolParams);
      const highVolResult = riskEngine.calculatePositionSize(highVolParams);
      
      expect(lowVolResult.volatilityAdjustment).toBe(1.0);
      expect(highVolResult.volatilityAdjustment).toBe(0.5);
    });

    it('should handle perfect correlation scenarios', () => {
      const params: RiskCalculationParams = {
        accountBalance: new Decimal(10000),
        riskTolerance: 'moderate',
        marketVolatility: 20,
        userExperienceLevel: 'intermediate',
        correlationRisk: 1.0 // Perfect correlation
      };
      
      const result = riskEngine.calculatePositionSize(params);
      expect(result.correlationAdjustment).toBe(0.5); // 50% reduction
    });

    it('should handle zero correlation scenarios', () => {
      const params: RiskCalculationParams = {
        accountBalance: new Decimal(10000),
        riskTolerance: 'moderate',
        marketVolatility: 20,
        userExperienceLevel: 'intermediate',
        correlationRisk: 0 // No correlation
      };
      
      const result = riskEngine.calculatePositionSize(params);
      expect(result.correlationAdjustment).toBe(1.0); // No adjustment
    });
  });

  describe('Mathematical Precision', () => {
    it('should maintain precision with Decimal calculations', () => {
      const params: RiskCalculationParams = {
        accountBalance: new Decimal('10000.123456'),
        riskTolerance: 'moderate',
        marketVolatility: 20.5,
        userExperienceLevel: 'intermediate'
      };
      
      const result = riskEngine.calculatePositionSize(params);
      expect(result.recommendedSize).toBeInstanceOf(Decimal);
      expect(result.recommendedSize.decimalPlaces()).toBeGreaterThanOrEqual(0);
    });

    it('should handle division by zero in Kelly calculation gracefully', () => {
      const params: KellyCriterionParams = {
        winProbability: 0.6,
        averageWin: new Decimal(100),
        averageLoss: new Decimal(0), // Edge case
        accountBalance: new Decimal(10000)
      };
      
      // Should not throw error
      expect(() => riskEngine.calculateKellyPosition(params)).not.toThrow();
    });

    it('should maintain consistency across multiple calculations', () => {
      const params: RiskCalculationParams = {
        accountBalance: new Decimal(10000),
        riskTolerance: 'moderate',
        marketVolatility: 20,
        userExperienceLevel: 'intermediate'
      };
      
      const result1 = riskEngine.calculatePositionSize(params);
      const result2 = riskEngine.calculatePositionSize(params);
      
      expect(result1.recommendedSize.eq(result2.recommendedSize)).toBe(true);
      expect(result1.riskPercentage).toBe(result2.riskPercentage);
    });
  });
});