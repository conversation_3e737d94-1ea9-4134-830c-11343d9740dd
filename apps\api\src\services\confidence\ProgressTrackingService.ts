import { PrismaClient } from '@prisma/client';
import {
  ConfidenceStage,
  QuizCategory,
  type ConfidenceAssessment,
  type QuizAttempt,
  type ProgressHistory
} from '@golddaddy/types';
import { ConfidenceAssessmentService } from './ConfidenceAssessmentService.js';

interface StageAdvancementCriteria {
  minimumConfidenceScore: number;
  requiredAssessments: string[];
  minimumTimeInStage: number; // days
  requiredQuizAttempts: number;
  minimumQuizScore: number;
  requiredCategories: {
    category: QuizCategory;
    minimumScore: number;
  }[];
  behavioralRequirements?: {
    riskTolerance: { min: number; max: number };
    decisionConsistency: number;
    emotionalStability: number;
  };
  performanceRequirements?: {
    paperTradingWinRate?: number;
    riskManagementScore?: number;
    strategyAdherence?: number;
    consistencyRating?: number;
  };
}

interface StageProgressSummary {
  currentStage: ConfidenceStage;
  timeInCurrentStage: number; // days
  overallProgress: number; // 0-100%
  readyForAdvancement: boolean;
  criteriaStatus: {
    confidenceScore: { current: number; required: number; met: boolean };
    timeInStage: { current: number; required: number; met: boolean };
    quizPerformance: { current: number; required: number; met: boolean };
    assessments: { completed: string[]; required: string[]; met: boolean };
    categories: Array<{
      category: QuizCategory;
      current: number;
      required: number;
      met: boolean;
    }>;
    behavioral?: {
      riskTolerance: { current: number; required: string; met: boolean };
      decisionConsistency: { current: number; required: number; met: boolean };
      emotionalStability: { current: number; required: number; met: boolean };
    };
    performance?: {
      [key: string]: { current: number; required: number; met: boolean };
    };
  };
  nextStage?: ConfidenceStage;
  estimatedTimeToAdvancement?: number; // days
  recommendedActions: string[];
  milestones: {
    completed: string[];
    upcoming: string[];
  };
}

interface CheckpointValidation {
  checkpointId: string;
  stageName: ConfidenceStage;
  validationResults: {
    knowledgeAssessment: {
      passed: boolean;
      score: number;
      weakAreas: string[];
    };
    practicalApplication: {
      passed: boolean;
      scenarios: Array<{
        scenarioId: string;
        passed: boolean;
        feedback: string;
      }>;
    };
    riskManagement: {
      passed: boolean;
      metrics: {
        riskAwareness: number;
        capitalPreservation: number;
        decisionQuality: number;
      };
    };
  };
  overallResult: 'passed' | 'failed' | 'needs_review';
  feedback: string;
  retakeRecommendations?: string[];
}

export class ProgressTrackingService extends ConfidenceAssessmentService {
  private readonly STAGE_CRITERIA: Record<ConfidenceStage, StageAdvancementCriteria> = {
    [ConfidenceStage.GOAL_SETTING]: {
      minimumConfidenceScore: 75,
      requiredAssessments: ['initial_risk_assessment'],
      minimumTimeInStage: 3, // 3 days minimum
      requiredQuizAttempts: 1,
      minimumQuizScore: 75,
      requiredCategories: [
        { category: QuizCategory.TRADING_FUNDAMENTALS, minimumScore: 75 },
        { category: QuizCategory.PLATFORM_FEATURES, minimumScore: 70 }
      ],
      behavioralRequirements: {
        riskTolerance: { min: 30, max: 70 },
        decisionConsistency: 60,
        emotionalStability: 60
      }
    },
    [ConfidenceStage.STRATEGY_LEARNING]: {
      minimumConfidenceScore: 80,
      requiredAssessments: ['risk_profile_complete', 'trading_goals_defined'],
      minimumTimeInStage: 7, // 1 week minimum
      requiredQuizAttempts: 2,
      minimumQuizScore: 80,
      requiredCategories: [
        { category: QuizCategory.TRADING_FUNDAMENTALS, minimumScore: 80 },
        { category: QuizCategory.RISK_MANAGEMENT, minimumScore: 75 },
        { category: QuizCategory.MARKET_ANALYSIS, minimumScore: 70 }
      ],
      behavioralRequirements: {
        riskTolerance: { min: 40, max: 80 },
        decisionConsistency: 70,
        emotionalStability: 70
      }
    },
    [ConfidenceStage.BACKTESTING_REVIEW]: {
      minimumConfidenceScore: 85,
      requiredAssessments: ['strategy_understanding', 'backtesting_analysis'],
      minimumTimeInStage: 14, // 2 weeks minimum
      requiredQuizAttempts: 3,
      minimumQuizScore: 85,
      requiredCategories: [
        { category: QuizCategory.MARKET_ANALYSIS, minimumScore: 85 },
        { category: QuizCategory.RISK_MANAGEMENT, minimumScore: 80 },
        { category: QuizCategory.PSYCHOLOGY_DISCIPLINE, minimumScore: 75 }
      ],
      behavioralRequirements: {
        riskTolerance: { min: 40, max: 70 },
        decisionConsistency: 80,
        emotionalStability: 75
      }
    },
    [ConfidenceStage.PAPER_TRADING]: {
      minimumConfidenceScore: 90,
      requiredAssessments: ['paper_trading_evaluation', 'risk_management_review'],
      minimumTimeInStage: 21, // 3 weeks minimum
      requiredQuizAttempts: 3,
      minimumQuizScore: 90,
      requiredCategories: [
        { category: QuizCategory.RISK_MANAGEMENT, minimumScore: 90 },
        { category: QuizCategory.PSYCHOLOGY_DISCIPLINE, minimumScore: 85 },
        { category: QuizCategory.SAFETY_PROCEDURES, minimumScore: 90 }
      ],
      behavioralRequirements: {
        riskTolerance: { min: 40, max: 60 },
        decisionConsistency: 85,
        emotionalStability: 80
      },
      performanceRequirements: {
        paperTradingWinRate: 0.6,
        riskManagementScore: 85,
        strategyAdherence: 80,
        consistencyRating: 80
      }
    },
    [ConfidenceStage.LIVE_READY]: {
      minimumConfidenceScore: 95,
      requiredAssessments: ['final_risk_evaluation', 'live_trading_readiness'],
      minimumTimeInStage: 30, // 1 month minimum
      requiredQuizAttempts: 2,
      minimumQuizScore: 95,
      requiredCategories: [
        { category: QuizCategory.RISK_MANAGEMENT, minimumScore: 95 },
        { category: QuizCategory.SAFETY_PROCEDURES, minimumScore: 95 },
        { category: QuizCategory.REGULATORY_COMPLIANCE, minimumScore: 90 },
        { category: QuizCategory.PSYCHOLOGY_DISCIPLINE, minimumScore: 90 }
      ],
      behavioralRequirements: {
        riskTolerance: { min: 35, max: 55 },
        decisionConsistency: 90,
        emotionalStability: 85
      },
      performanceRequirements: {
        paperTradingWinRate: 0.65,
        riskManagementScore: 90,
        strategyAdherence: 85,
        consistencyRating: 85
      }
    }
  };

  private readonly STAGE_ORDER: ConfidenceStage[] = [
    ConfidenceStage.GOAL_SETTING,
    ConfidenceStage.STRATEGY_LEARNING,
    ConfidenceStage.BACKTESTING_REVIEW,
    ConfidenceStage.PAPER_TRADING,
    ConfidenceStage.LIVE_READY
  ];

  constructor(prisma: PrismaClient) {
    super(prisma);
  }

  async getProgressSummary(userId: string): Promise<StageProgressSummary> {
    const assessment = await this.getCurrentConfidenceAssessment(userId);
    const currentStage = assessment.currentStage;
    const criteria = this.STAGE_CRITERIA[currentStage];
    
    // Calculate time in current stage
    const stageHistory = assessment.progressHistory
      .filter(h => h.stage === currentStage)
      .sort((a, b) => new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime());
    
    const stageStartDate = stageHistory.length > 0 
      ? new Date(stageHistory[0].completedAt)
      : assessment.createdAt;
    const timeInCurrentStage = Math.floor(
      (Date.now() - stageStartDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Get user's quiz attempts for this stage
    const quizAttempts = await this.getUserAttemptHistory(userId, currentStage);
    const categoryScores = await this.getCategoryScores(userId, currentStage);

    // Evaluate criteria
    const criteriaStatus = this.evaluateAdvancementCriteria(
      assessment,
      criteria,
      timeInCurrentStage,
      quizAttempts,
      categoryScores
    );

    // Calculate overall progress
    const overallProgress = this.calculateOverallProgress(criteriaStatus);
    
    // Determine if ready for advancement
    const readyForAdvancement = this.isReadyForAdvancement(criteriaStatus);

    // Get next stage
    const currentIndex = this.STAGE_ORDER.indexOf(currentStage);
    const nextStage = currentIndex < this.STAGE_ORDER.length - 1 
      ? this.STAGE_ORDER[currentIndex + 1] 
      : undefined;

    // Generate recommendations
    const recommendedActions = this.generateRecommendedActions(criteriaStatus, currentStage);

    // Get milestones
    const milestones = this.getMilestones(currentStage, criteriaStatus);

    // Estimate time to advancement
    const estimatedTimeToAdvancement = readyForAdvancement 
      ? 0 
      : this.estimateTimeToAdvancement(criteriaStatus, timeInCurrentStage, criteria);

    return {
      currentStage,
      timeInCurrentStage,
      overallProgress,
      readyForAdvancement,
      criteriaStatus,
      nextStage,
      estimatedTimeToAdvancement,
      recommendedActions,
      milestones
    };
  }

  async attemptStageAdvancement(userId: string): Promise<{
    success: boolean;
    newStage?: ConfidenceStage;
    message: string;
    validationResults?: CheckpointValidation;
  }> {
    const progressSummary = await this.getProgressSummary(userId);
    
    if (!progressSummary.readyForAdvancement) {
      return {
        success: false,
        message: `Not ready for advancement. ${progressSummary.recommendedActions.join('. ')}`
      };
    }

    // Perform checkpoint validation
    const validationResults = await this.performCheckpointValidation(userId, progressSummary.currentStage);
    
    if (validationResults.overallResult !== 'passed') {
      return {
        success: false,
        message: validationResults.feedback,
        validationResults
      };
    }

    // Advance to next stage
    const newStage = progressSummary.nextStage!;
    await this.advanceUserToNextStage(userId, newStage);

    return {
      success: true,
      newStage,
      message: `Successfully advanced to ${newStage}!`,
      validationResults
    };
  }

  async trackUserActivity(userId: string, activityType: string, metadata: any): Promise<void> {
    // Log user activity for progress tracking
    await this.prisma.confidenceAssessment.update({
      where: { userId },
      data: {
        progressHistory: {
          // Add activity to progress history
          push: {
            stage: metadata.stage || 'unknown',
            activityType,
            completedAt: new Date(),
            metadata
          }
        },
        updatedAt: new Date()
      }
    });
  }

  async validateStageReadiness(userId: string, targetStage: ConfidenceStage): Promise<{
    ready: boolean;
    missingRequirements: string[];
    estimatedCompletionTime: number; // days
  }> {
    const assessment = await this.getCurrentConfidenceAssessment(userId);
    const currentStageIndex = this.STAGE_ORDER.indexOf(assessment.currentStage);
    const targetStageIndex = this.STAGE_ORDER.indexOf(targetStage);

    if (targetStageIndex <= currentStageIndex) {
      return {
        ready: targetStageIndex === currentStageIndex,
        missingRequirements: [],
        estimatedCompletionTime: 0
      };
    }

    // Check if user can skip to target stage (not recommended but possible)
    const missingRequirements: string[] = [];
    let totalEstimatedTime = 0;

    for (let i = currentStageIndex; i < targetStageIndex; i++) {
      const stageToValidate = this.STAGE_ORDER[i + 1];
      const criteria = this.STAGE_CRITERIA[stageToValidate];
      
      const progressSummary = await this.getProgressSummary(userId);
      if (!progressSummary.readyForAdvancement && stageToValidate === targetStage) {
        missingRequirements.push(...progressSummary.recommendedActions);
        totalEstimatedTime += progressSummary.estimatedTimeToAdvancement || 0;
      }
      
      totalEstimatedTime += criteria.minimumTimeInStage;
    }

    return {
      ready: missingRequirements.length === 0,
      missingRequirements,
      estimatedCompletionTime: totalEstimatedTime
    };
  }

  async getDetailedProgressReport(userId: string): Promise<{
    overview: StageProgressSummary;
    stageHistory: ProgressHistory[];
    performanceMetrics: {
      quizPerformance: {
        averageScore: number;
        improvement: number;
        consistency: number;
        categoryBreakdown: Record<QuizCategory, number>;
      };
      timeMetrics: {
        totalTimeSpent: number; // minutes
        averageSessionTime: number;
        studyStreak: number; // consecutive days
      };
      confidenceMetrics: {
        confidenceAccuracy: number; // how well confidence matches performance
        overconfidenceRate: number;
        underconfidenceRate: number;
      };
    };
    recommendations: {
      immediate: string[];
      shortTerm: string[];
      longTerm: string[];
    };
  }> {
    const overview = await this.getProgressSummary(userId);
    const assessment = await this.getCurrentConfidenceAssessment(userId);
    
    // Get performance metrics
    const quizAttempts = await this.getUserAttemptHistory(userId);
    const performanceMetrics = await this.calculatePerformanceMetrics(userId, quizAttempts);
    
    // Generate tiered recommendations
    const recommendations = this.generateTieredRecommendations(overview, performanceMetrics);

    return {
      overview,
      stageHistory: assessment.progressHistory,
      performanceMetrics,
      recommendations
    };
  }

  private evaluateAdvancementCriteria(
    assessment: ConfidenceAssessment,
    criteria: StageAdvancementCriteria,
    timeInStage: number,
    quizAttempts: QuizAttempt[],
    categoryScores: Record<QuizCategory, number>
  ): StageProgressSummary['criteriaStatus'] {
    const recentAttempts = quizAttempts.slice(0, criteria.requiredQuizAttempts);
    const averageQuizScore = recentAttempts.length > 0
      ? recentAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / recentAttempts.length
      : 0;

    const criteriaStatus: StageProgressSummary['criteriaStatus'] = {
      confidenceScore: {
        current: assessment.overallConfidenceScore,
        required: criteria.minimumConfidenceScore,
        met: assessment.overallConfidenceScore >= criteria.minimumConfidenceScore
      },
      timeInStage: {
        current: timeInStage,
        required: criteria.minimumTimeInStage,
        met: timeInStage >= criteria.minimumTimeInStage
      },
      quizPerformance: {
        current: averageQuizScore,
        required: criteria.minimumQuizScore,
        met: averageQuizScore >= criteria.minimumQuizScore && recentAttempts.length >= criteria.requiredQuizAttempts
      },
      assessments: {
        completed: [], // Would track completed assessments
        required: criteria.requiredAssessments,
        met: criteria.requiredAssessments.length === 0 // Simplified for now
      },
      categories: criteria.requiredCategories.map(req => ({
        category: req.category,
        current: categoryScores[req.category] || 0,
        required: req.minimumScore,
        met: (categoryScores[req.category] || 0) >= req.minimumScore
      }))
    };

    // Add behavioral requirements if they exist
    if (criteria.behavioralRequirements) {
      const behavioral = assessment.assessmentScores.behavioralAssessment;
      criteriaStatus.behavioral = {
        riskTolerance: {
          current: behavioral?.riskTolerance || 0,
          required: `${criteria.behavioralRequirements.riskTolerance.min}-${criteria.behavioralRequirements.riskTolerance.max}`,
          met: (behavioral?.riskTolerance || 0) >= criteria.behavioralRequirements.riskTolerance.min &&
               (behavioral?.riskTolerance || 0) <= criteria.behavioralRequirements.riskTolerance.max
        },
        decisionConsistency: {
          current: behavioral?.decisionConsistency || 0,
          required: criteria.behavioralRequirements.decisionConsistency,
          met: (behavioral?.decisionConsistency || 0) >= criteria.behavioralRequirements.decisionConsistency
        },
        emotionalStability: {
          current: behavioral?.emotionalStability || 0,
          required: criteria.behavioralRequirements.emotionalStability,
          met: (behavioral?.emotionalStability || 0) >= criteria.behavioralRequirements.emotionalStability
        }
      };
    }

    // Add performance requirements if they exist
    if (criteria.performanceRequirements) {
      const performance = assessment.assessmentScores.performanceEvaluation;
      criteriaStatus.performance = {};
      
      for (const [key, requiredValue] of Object.entries(criteria.performanceRequirements)) {
        const currentValue = (performance as any)?.[key] || 0;
        criteriaStatus.performance[key] = {
          current: currentValue,
          required: requiredValue,
          met: currentValue >= requiredValue
        };
      }
    }

    return criteriaStatus;
  }

  private calculateOverallProgress(criteriaStatus: StageProgressSummary['criteriaStatus']): number {
    const allCriteria = [
      criteriaStatus.confidenceScore,
      criteriaStatus.timeInStage,
      criteriaStatus.quizPerformance,
      criteriaStatus.assessments,
      ...criteriaStatus.categories,
      ...(criteriaStatus.behavioral ? Object.values(criteriaStatus.behavioral) : []),
      ...(criteriaStatus.performance ? Object.values(criteriaStatus.performance) : [])
    ];

    const metCount = allCriteria.filter(criteria => criteria.met).length;
    return Math.round((metCount / allCriteria.length) * 100);
  }

  private isReadyForAdvancement(criteriaStatus: StageProgressSummary['criteriaStatus']): boolean {
    const allCriteria = [
      criteriaStatus.confidenceScore.met,
      criteriaStatus.timeInStage.met,
      criteriaStatus.quizPerformance.met,
      criteriaStatus.assessments.met,
      ...criteriaStatus.categories.map(c => c.met),
      ...(criteriaStatus.behavioral ? Object.values(criteriaStatus.behavioral).map(c => c.met) : []),
      ...(criteriaStatus.performance ? Object.values(criteriaStatus.performance).map(c => c.met) : [])
    ];

    return allCriteria.every(met => met);
  }

  private generateRecommendedActions(
    criteriaStatus: StageProgressSummary['criteriaStatus'],
    _currentStage: ConfidenceStage
  ): string[] {
    const actions: string[] = [];

    if (!criteriaStatus.confidenceScore.met) {
      const gap = criteriaStatus.confidenceScore.required - criteriaStatus.confidenceScore.current;
      actions.push(`Improve overall confidence score by ${gap} points through quiz practice and assessments`);
    }

    if (!criteriaStatus.timeInStage.met) {
      const daysLeft = criteriaStatus.timeInStage.required - criteriaStatus.timeInStage.current;
      actions.push(`Continue learning for ${daysLeft} more days to meet minimum time requirement`);
    }

    if (!criteriaStatus.quizPerformance.met) {
      const scoreGap = criteriaStatus.quizPerformance.required - criteriaStatus.quizPerformance.current;
      actions.push(`Improve quiz performance by ${Math.round(scoreGap)} points through focused study`);
    }

    const failingCategories = criteriaStatus.categories.filter(c => !c.met);
    if (failingCategories.length > 0) {
      failingCategories.forEach(category => {
        const gap = category.required - category.current;
        actions.push(`Improve ${category.category.replace('_', ' ')} knowledge by ${Math.round(gap)} points`);
      });
    }

    if (criteriaStatus.behavioral) {
      Object.entries(criteriaStatus.behavioral).forEach(([key, status]) => {
        if (!status.met) {
          actions.push(`Work on ${key.replace(/([A-Z])/g, ' $1').toLowerCase()} through behavioral assessment exercises`);
        }
      });
    }

    if (criteriaStatus.performance) {
      Object.entries(criteriaStatus.performance).forEach(([key, status]) => {
        if (!status.met) {
          const gap = status.required - status.current;
          actions.push(`Improve ${key.replace(/([A-Z])/g, ' $1').toLowerCase()} by ${Math.round(gap * 100)}%`);
        }
      });
    }

    return actions;
  }

  private getMilestones(
    currentStage: ConfidenceStage,
    criteriaStatus: StageProgressSummary['criteriaStatus']
  ): { completed: string[]; upcoming: string[] } {
    const completed: string[] = [];
    const upcoming: string[] = [];

    if (criteriaStatus.confidenceScore.met) {
      completed.push('Confidence score target achieved');
    } else {
      upcoming.push('Reach target confidence score');
    }

    if (criteriaStatus.timeInStage.met) {
      completed.push('Minimum time in stage completed');
    } else {
      upcoming.push('Complete minimum time in stage');
    }

    if (criteriaStatus.quizPerformance.met) {
      completed.push('Quiz performance standards met');
    } else {
      upcoming.push('Meet quiz performance requirements');
    }

    const metCategories = criteriaStatus.categories.filter(c => c.met);
    const unmetCategories = criteriaStatus.categories.filter(c => !c.met);

    metCategories.forEach(category => {
      completed.push(`${category.category.replace('_', ' ')} proficiency achieved`);
    });

    unmetCategories.forEach(category => {
      upcoming.push(`Achieve ${category.category.replace('_', ' ')} proficiency`);
    });

    return { completed, upcoming };
  }

  private estimateTimeToAdvancement(
    criteriaStatus: StageProgressSummary['criteriaStatus'],
    currentTimeInStage: number,
    criteria: StageAdvancementCriteria
  ): number {
    let estimatedDays = 0;

    // Time requirement
    if (!criteriaStatus.timeInStage.met) {
      estimatedDays = Math.max(estimatedDays, criteria.minimumTimeInStage - currentTimeInStage);
    }

    // Score improvements (rough estimate: 5 points per week of focused study)
    if (!criteriaStatus.confidenceScore.met) {
      const gap = criteriaStatus.confidenceScore.required - criteriaStatus.confidenceScore.current;
      estimatedDays = Math.max(estimatedDays, Math.ceil(gap / 5) * 7);
    }

    if (!criteriaStatus.quizPerformance.met) {
      const gap = criteriaStatus.quizPerformance.required - criteriaStatus.quizPerformance.current;
      estimatedDays = Math.max(estimatedDays, Math.ceil(gap / 10) * 7); // 10 points per week for quiz performance
    }

    // Category improvements
    const failingCategories = criteriaStatus.categories.filter(c => !c.met);
    failingCategories.forEach(category => {
      const gap = category.required - category.current;
      estimatedDays = Math.max(estimatedDays, Math.ceil(gap / 8) * 7); // 8 points per week per category
    });

    return Math.max(1, estimatedDays);
  }

  private async performCheckpointValidation(userId: string, stage: ConfidenceStage): Promise<CheckpointValidation> {
    // This would perform comprehensive validation including practical scenarios
    // For now, returning a simplified validation based on current metrics
    
    const assessment = await this.getCurrentConfidenceAssessment(userId);
    const quizAttempts = await this.getUserAttemptHistory(userId, stage);
    
    const knowledgeScore = quizAttempts.length > 0 
      ? quizAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / quizAttempts.length 
      : 0;

    const weakAreas = Array.from(new Set(
      quizAttempts.flatMap(attempt => attempt.weakAreas)
    ));

    const validation: CheckpointValidation = {
      checkpointId: `checkpoint_${userId}_${stage}_${Date.now()}`,
      stageName: stage,
      validationResults: {
        knowledgeAssessment: {
          passed: knowledgeScore >= this.STAGE_CRITERIA[stage].minimumQuizScore,
          score: knowledgeScore,
          weakAreas
        },
        practicalApplication: {
          passed: true, // Would implement scenario-based validation
          scenarios: []
        },
        riskManagement: {
          passed: assessment.assessmentScores.behavioralAssessment?.riskTolerance ? 
            assessment.assessmentScores.behavioralAssessment.riskTolerance <= 60 : false,
          metrics: {
            riskAwareness: assessment.assessmentScores.behavioralAssessment?.riskTolerance || 0,
            capitalPreservation: assessment.assessmentScores.performanceEvaluation?.riskManagementScore || 0,
            decisionQuality: assessment.assessmentScores.behavioralAssessment?.decisionConsistency || 0
          }
        }
      },
      overallResult: 'passed', // Simplified
      feedback: `Validation completed for ${stage}. All criteria met.`
    };

    return validation;
  }

  private async advanceUserToNextStage(userId: string, newStage: ConfidenceStage): Promise<void> {
    await this.prisma.confidenceAssessment.update({
      where: { userId },
      data: {
        currentStage: newStage,
        progressHistory: {
          push: {
            stage: newStage,
            completedAt: new Date(),
            metadata: {
              advancementType: 'automatic',
              previousStage: (await this.getCurrentConfidenceAssessment(userId)).currentStage
            }
          }
        },
        updatedAt: new Date()
      }
    });
  }

  private async getCategoryScores(userId: string, stage: ConfidenceStage): Promise<Record<QuizCategory, number>> {
    const attempts = await this.getUserAttemptHistory(userId, stage);
    const categoryScores: Record<QuizCategory, number> = {} as any;

    // This would analyze quiz attempts by category
    // For now, returning simplified scores based on overall performance
    Object.values(QuizCategory).forEach(category => {
      const categoryAttempts = attempts.filter(attempt => 
        attempt.strongAreas.includes(category) || attempt.weakAreas.includes(category)
      );
      
      if (categoryAttempts.length > 0) {
        categoryScores[category] = categoryAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / categoryAttempts.length;
      } else {
        categoryScores[category] = 0;
      }
    });

    return categoryScores;
  }

  private async calculatePerformanceMetrics(userId: string, quizAttempts: QuizAttempt[]) {
    const scores = quizAttempts.map(attempt => attempt.score);
    const averageScore = scores.length > 0 ? scores.reduce((sum, score) => sum + score, 0) / scores.length : 0;
    
    // Calculate improvement (compare first half to second half of attempts)
    const firstHalf = scores.slice(0, Math.floor(scores.length / 2));
    const secondHalf = scores.slice(Math.floor(scores.length / 2));
    const improvement = secondHalf.length > 0 && firstHalf.length > 0 
      ? (secondHalf.reduce((sum, s) => sum + s, 0) / secondHalf.length) - 
        (firstHalf.reduce((sum, s) => sum + s, 0) / firstHalf.length)
      : 0;

    // Calculate consistency (1 - coefficient of variation)
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - averageScore, 2), 0) / scores.length;
    const standardDeviation = Math.sqrt(variance);
    const consistency = averageScore > 0 ? Math.max(0, 1 - (standardDeviation / averageScore)) : 0;

    // Category breakdown
    const categoryBreakdown: Record<QuizCategory, number> = {} as any;
    Object.values(QuizCategory).forEach(category => {
      const relevantAttempts = quizAttempts.filter(attempt => 
        attempt.strongAreas.includes(category) || attempt.weakAreas.includes(category)
      );
      categoryBreakdown[category] = relevantAttempts.length > 0 
        ? relevantAttempts.reduce((sum, attempt) => sum + attempt.score, 0) / relevantAttempts.length
        : 0;
    });

    return {
      quizPerformance: {
        averageScore,
        improvement,
        consistency,
        categoryBreakdown
      },
      timeMetrics: {
        totalTimeSpent: quizAttempts.reduce((sum, attempt) => sum + attempt.totalTimeSpent, 0) / 60, // Convert to minutes
        averageSessionTime: quizAttempts.length > 0 
          ? quizAttempts.reduce((sum, attempt) => sum + attempt.totalTimeSpent, 0) / quizAttempts.length / 60
          : 0,
        studyStreak: 0 // Would calculate based on attempt dates
      },
      confidenceMetrics: {
        confidenceAccuracy: 0.8, // Would calculate based on confidence vs performance
        overconfidenceRate: 0.1,
        underconfidenceRate: 0.15
      }
    };
  }

  private generateTieredRecommendations(overview: StageProgressSummary, _performanceMetrics: any) {
    return {
      immediate: overview.recommendedActions.slice(0, 2),
      shortTerm: [
        'Focus on weak categories through targeted practice',
        'Complete all required assessments',
        'Maintain consistent study schedule'
      ],
      longTerm: [
        'Build comprehensive trading knowledge across all categories',
        'Develop strong risk management habits',
        'Prepare for real-world trading scenarios'
      ]
    };
  }
}