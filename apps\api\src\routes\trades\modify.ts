import { Request, Response, NextFunction } from 'express';
import { param, body, validationResult } from 'express-validator';
import Decimal from 'decimal.js';
import { TradeModificationService } from '../../services/trading/TradeModificationService';
import { PositionManager } from '../../services/trading/PositionManager';
import { ModificationType, TradeModificationRequest } from '@golddaddy/types';

interface ModifyTradeRequestBody {
  modificationType: ModificationType;
  newValue: string;
  reason: string;
  confirmRisk: boolean;
}

interface ModifyTradeParams {
  tradeId: string;
}

interface CancelModificationParams {
  modificationId: string;
}

interface CancelModificationBody {
  reason: string;
}

// Validation middleware for modification requests
export const validateModifyTradeRequest = [
  param('tradeId')
    .isString()
    .notEmpty()
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Invalid trade ID format'),
  
  body('modificationType')
    .isIn(Object.values(ModificationType))
    .withMessage('Invalid modification type'),
  
  body('newValue')
    .isString()
    .custom((value, { req }) => {
      try {
        const decimal = new Decimal(value);
        
        // Additional validation based on modification type
        if (req.body.modificationType === ModificationType.POSITION_SIZE) {
          if (decimal.lte(0)) {
            throw new Error('Position size must be greater than 0');
          }
          if (decimal.gt(10000000)) {
            throw new Error('Position size cannot exceed 10,000,000');
          }
        }
        
        if (req.body.modificationType === ModificationType.CLOSE_POSITION) {
          if (!decimal.eq(0)) {
            throw new Error('Close position value must be 0');
          }
        }
        
        return true;
      } catch (error) {
        throw new Error('Invalid numeric value format');
      }
    }),
  
  body('reason')
    .isString()
    .trim()
    .isLength({ min: 10, max: 500 })
    .withMessage('Reason must be between 10 and 500 characters'),
  
  body('confirmRisk')
    .isBoolean()
    .withMessage('Risk confirmation is required')
];

// Validation middleware for cancellation requests
export const validateCancelModificationRequest = [
  param('modificationId')
    .isString()
    .notEmpty()
    .matches(/^[a-zA-Z0-9_-]+$/)
    .withMessage('Invalid modification ID format'),
  
  body('reason')
    .isString()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Cancellation reason must be between 5 and 200 characters')
];

export class TradeModificationController {
  constructor(
    private tradeModificationService: TradeModificationService,
    private positionManager: PositionManager
  ) {}

  async modifyTrade(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
        return;
      }

      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const { tradeId } = req.params as ModifyTradeParams;
      const body = req.body as ModifyTradeRequestBody;

      // Verify user owns this trade
      const position = await this.positionManager.getPositionByTradeId(tradeId);
      if (!position) {
        res.status(404).json({
          success: false,
          error: 'Trade not found'
        });
        return;
      }

      if (position.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      // Create modification request
      const modificationRequest: TradeModificationRequest = {
        tradeId,
        modificationType: body.modificationType,
        newValue: new Decimal(body.newValue),
        reason: body.reason,
        confirmRisk: body.confirmRisk
      };

      console.log(`Processing trade modification for user ${userId}:`, {
        tradeId,
        modificationType: body.modificationType,
        newValue: body.newValue,
        reason: body.reason
      });

      // Submit modification request
      const modification = await this.tradeModificationService.requestModification(
        modificationRequest,
        userId
      );

      // Convert Decimal fields to strings for JSON response
      const responseData = {
        success: true,
        data: {
          modification: {
            ...modification,
            originalValue: modification.originalValue.toString(),
            newValue: modification.newValue.toString()
          }
        },
        message: 'Trade modification request submitted successfully'
      };

      res.status(201).json(responseData);

    } catch (error) {
      console.error('Trade modification error:', error);
      
      if (error instanceof Error) {
        // Handle specific error types
        if (error.message.includes('Risk validation failed')) {
          res.status(400).json({
            success: false,
            error: error.message,
            code: 'RISK_VALIDATION_FAILED'
          });
          return;
        }

        if (error.message.includes('modification frequency')) {
          res.status(429).json({
            success: false,
            error: 'Too many modification requests. Please wait before trying again.',
            code: 'RATE_LIMIT_EXCEEDED'
          });
          return;
        }

        if (error.message.includes('not found')) {
          res.status(404).json({
            success: false,
            error: error.message,
            code: 'TRADE_NOT_FOUND'
          });
          return;
        }
      }

      next(error);
    }
  }

  async getModificationStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const { modificationId } = req.params;

      const modification = await this.tradeModificationService.getModificationStatus(modificationId);
      
      if (!modification) {
        res.status(404).json({
          success: false,
          error: 'Modification not found'
        });
        return;
      }

      // Verify user owns this modification
      const position = await this.positionManager.getPositionByTradeId(modification.tradeId);
      if (!position || position.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      const responseData = {
        success: true,
        data: {
          modification: {
            ...modification,
            originalValue: modification.originalValue.toString(),
            newValue: modification.newValue.toString()
          }
        }
      };

      res.status(200).json(responseData);

    } catch (error) {
      console.error('Get modification status error:', error);
      next(error);
    }
  }

  async cancelModification(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
        return;
      }

      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const { modificationId } = req.params as CancelModificationParams;
      const { reason } = req.body as CancelModificationBody;

      // Verify user owns this modification
      const modification = await this.tradeModificationService.getModificationStatus(modificationId);
      if (!modification) {
        res.status(404).json({
          success: false,
          error: 'Modification not found'
        });
        return;
      }

      const position = await this.positionManager.getPositionByTradeId(modification.tradeId);
      if (!position || position.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      console.log(`Cancelling modification ${modificationId} for user ${userId}:`, { reason });

      // Cancel the modification
      const success = await this.tradeModificationService.cancelModification(
        modificationId,
        userId,
        reason
      );

      if (success) {
        res.status(200).json({
          success: true,
          message: 'Modification cancelled successfully'
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'Failed to cancel modification'
        });
      }

    } catch (error) {
      console.error('Cancel modification error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('Cannot cancel')) {
          res.status(400).json({
            success: false,
            error: error.message,
            code: 'CANNOT_CANCEL'
          });
          return;
        }
      }

      next(error);
    }
  }

  async rollbackModification(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const { modificationId } = req.params;
      const { reason } = req.body;

      if (!reason || reason.trim().length < 5) {
        res.status(400).json({
          success: false,
          error: 'Rollback reason is required (minimum 5 characters)'
        });
        return;
      }

      // Verify user owns this modification
      const modification = await this.tradeModificationService.getModificationStatus(modificationId);
      if (!modification) {
        res.status(404).json({
          success: false,
          error: 'Modification not found'
        });
        return;
      }

      const position = await this.positionManager.getPositionByTradeId(modification.tradeId);
      if (!position || position.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      console.log(`Rolling back modification ${modificationId} for user ${userId}:`, { reason });

      // Rollback the modification
      const success = await this.tradeModificationService.rollbackModification(
        modificationId,
        userId,
        reason.trim()
      );

      if (success) {
        res.status(200).json({
          success: true,
          message: 'Modification rolled back successfully'
        });
      } else {
        res.status(400).json({
          success: false,
          error: 'Failed to rollback modification'
        });
      }

    } catch (error) {
      console.error('Rollback modification error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('No rollback data')) {
          res.status(400).json({
            success: false,
            error: 'Cannot rollback: no rollback data available',
            code: 'NO_ROLLBACK_DATA'
          });
          return;
        }

        if (error.message.includes('not yet applied')) {
          res.status(400).json({
            success: false,
            error: 'Cannot rollback: modification not yet applied',
            code: 'NOT_APPLIED'
          });
          return;
        }
      }

      next(error);
    }
  }

  async getModificationHistory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const { tradeId } = req.params;
      const { limit = '50', offset = '0' } = req.query;

      // Verify user owns this trade
      const position = await this.positionManager.getPositionByTradeId(tradeId);
      if (!position || position.userId !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied'
        });
        return;
      }

      const limitNum = parseInt(limit as string, 10);
      const offsetNum = parseInt(offset as string, 10);

      const { modifications, total } = await this.tradeModificationService.getModificationHistory(
        tradeId,
        limitNum,
        offsetNum
      );

      const responseData = {
        success: true,
        data: {
          tradeId,
          modifications: modifications.map(mod => ({
            ...mod,
            originalValue: mod.originalValue.toString(),
            newValue: mod.newValue.toString()
          })),
          total,
          limit: limitNum,
          offset: offsetNum
        }
      };

      res.status(200).json(responseData);

    } catch (error) {
      console.error('Get modification history error:', error);
      next(error);
    }
  }
}

// Factory function to create controller with dependencies
export const createTradeModificationController = (
  tradeModificationService: TradeModificationService,
  positionManager: PositionManager
) => {
  return new TradeModificationController(
    tradeModificationService,
    positionManager
  );
};