import { Router, Request, Response, NextFunction } from 'express';
import { rateLimit } from 'express-rate-limit';
import { z } from 'zod';

// Rate limiting configuration
const marketDataRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 200, // High limit for market data (real-time needs)
  standardHeaders: true,
  legacyHeaders: false,
});

const quotesRateLimit = rateLimit({
  windowMs: 1000, // 1 second
  max: 60, // 60 requests per second for quotes
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    error: 'Quote request rate limit exceeded',
    code: 'QUOTE_RATE_LIMIT',
    retryAfter: 1
  }
});

// Validation schemas
const SymbolSchema = z.string().min(3, 'Symbol must be at least 3 characters').max(10, 'Symbol too long');

const HistoricalDataSchema = z.object({
  symbol: SymbolSchema,
  timeframe: z.enum(['M1', 'M5', 'M15', 'M30', 'H1', 'H4', 'D1']).default('H1'),
  fromDate: z.string().datetime().optional(),
  toDate: z.string().datetime().optional(),
  count: z.number().int().min(1).max(5000).default(500),
});

// Mock market data service (replace with real implementation)
class MockMarketDataService {
  private baseRates = {
    'EURUSD': 1.0850,
    'GBPUSD': 1.2650,
    'USDJPY': 149.50,
    'AUDUSD': 0.6550,
    'USDCAD': 1.3650,
    'USDCHF': 0.9050,
    'NZDUSD': 0.5950,
  };

  generateQuote(symbol: string) {
    const baseRate = this.baseRates[symbol as keyof typeof this.baseRates] || 1.0000;
    const volatility = 0.001; // 0.1% volatility
    const change = (Math.random() - 0.5) * volatility;
    const lastPrice = baseRate + change;
    
    // Generate bid/ask with realistic spread
    const spread = symbol === 'USDJPY' ? 0.3 : 0.0002; // 0.3 for JPY pairs, 0.2 pips for others
    const halfSpread = spread / 2;
    
    return {
      symbol,
      bid: Number((lastPrice - halfSpread).toFixed(5)),
      ask: Number((lastPrice + halfSpread).toFixed(5)),
      lastPrice: Number(lastPrice.toFixed(5)),
      spread: Number(spread.toFixed(5)),
      change: Number(change.toFixed(5)),
      changePercent: Number((change / baseRate * 100).toFixed(3)),
      volume: Math.floor(Math.random() * 1000000),
      timestamp: new Date().toISOString(),
    };
  }

  generateHistoricalData(symbol: string, timeframe: string, count: number) {
    const data = [];
    const baseRate = this.baseRates[symbol as keyof typeof this.baseRates] || 1.0000;
    let currentPrice = baseRate;
    
    // Generate historical candles
    const timeframeMinutes = {
      'M1': 1, 'M5': 5, 'M15': 15, 'M30': 30,
      'H1': 60, 'H4': 240, 'D1': 1440
    }[timeframe] || 60;
    
    const now = new Date();
    
    for (let i = count - 1; i >= 0; i--) {
      const timestamp = new Date(now.getTime() - i * timeframeMinutes * 60 * 1000);
      const volatility = 0.002;
      
      const open = currentPrice;
      const change = (Math.random() - 0.5) * volatility;
      const close = Number((open + change).toFixed(5));
      
      const high = Number((Math.max(open, close) + Math.random() * volatility / 2).toFixed(5));
      const low = Number((Math.min(open, close) - Math.random() * volatility / 2).toFixed(5));
      
      data.push({
        timestamp: timestamp.toISOString(),
        open,
        high,
        low,
        close,
        volume: Math.floor(Math.random() * 500000),
      });
      
      currentPrice = close;
    }
    
    return data;
  }

  getAvailableSymbols() {
    return Object.keys(this.baseRates).map(symbol => ({
      symbol,
      description: this.getSymbolDescription(symbol),
      category: 'forex',
      active: true,
    }));
  }

  private getSymbolDescription(symbol: string): string {
    const descriptions = {
      'EURUSD': 'Euro / US Dollar',
      'GBPUSD': 'British Pound / US Dollar',
      'USDJPY': 'US Dollar / Japanese Yen',
      'AUDUSD': 'Australian Dollar / US Dollar',
      'USDCAD': 'US Dollar / Canadian Dollar',
      'USDCHF': 'US Dollar / Swiss Franc',
      'NZDUSD': 'New Zealand Dollar / US Dollar',
    };
    return descriptions[symbol as keyof typeof descriptions] || symbol;
  }
}

const marketDataService = new MockMarketDataService();

interface MarketDataControllers {
  getQuoteController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getMultipleQuotesController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getHistoricalDataController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getSymbolsController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getMarketStatusController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
}

function createMarketDataControllers(): MarketDataControllers {

  const getQuoteController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { symbol } = req.params;

      if (!symbol) {
        res.status(400).json({
          success: false,
          error: 'Symbol parameter is required',
          code: 'MISSING_SYMBOL'
        });
        return;
      }

      const validatedSymbol = SymbolSchema.parse(symbol.toUpperCase());
      
      // Check if symbol is supported
      const availableSymbols = marketDataService.getAvailableSymbols();
      const symbolExists = availableSymbols.some(s => s.symbol === validatedSymbol);
      
      if (!symbolExists) {
        res.status(404).json({
          success: false,
          error: `Symbol ${validatedSymbol} not found`,
          code: 'SYMBOL_NOT_FOUND',
          availableSymbols: availableSymbols.map(s => s.symbol)
        });
        return;
      }

      const quote = marketDataService.generateQuote(validatedSymbol);

      res.status(200).json({
        success: true,
        data: quote,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      if (error.name === 'ZodError') {
        res.status(400).json({
          success: false,
          error: 'Invalid symbol format',
          code: 'INVALID_SYMBOL',
          details: error.errors
        });
        return;
      }
      next(error);
    }
  };

  const getMultipleQuotesController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { symbols } = req.query;

      if (!symbols) {
        res.status(400).json({
          success: false,
          error: 'Symbols query parameter is required',
          code: 'MISSING_SYMBOLS'
        });
        return;
      }

      const symbolList = (symbols as string).split(',').map(s => s.trim().toUpperCase());
      
      if (symbolList.length > 20) {
        res.status(400).json({
          success: false,
          error: 'Maximum 20 symbols allowed per request',
          code: 'TOO_MANY_SYMBOLS'
        });
        return;
      }

      const availableSymbols = marketDataService.getAvailableSymbols().map(s => s.symbol);
      const quotes = [];
      const notFound = [];

      for (const symbol of symbolList) {
        try {
          const validatedSymbol = SymbolSchema.parse(symbol);
          if (availableSymbols.includes(validatedSymbol)) {
            quotes.push(marketDataService.generateQuote(validatedSymbol));
          } else {
            notFound.push(symbol);
          }
        } catch {
          notFound.push(symbol);
        }
      }

      res.status(200).json({
        success: true,
        data: {
          quotes,
          notFound: notFound.length > 0 ? notFound : undefined,
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const getHistoricalDataController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const validatedData = HistoricalDataSchema.parse({
        symbol: req.params.symbol?.toUpperCase(),
        timeframe: req.query.timeframe,
        fromDate: req.query.fromDate,
        toDate: req.query.toDate,
        count: req.query.count ? parseInt(req.query.count as string, 10) : 500,
      });

      // Check if symbol is supported
      const availableSymbols = marketDataService.getAvailableSymbols();
      const symbolExists = availableSymbols.some(s => s.symbol === validatedData.symbol);
      
      if (!symbolExists) {
        res.status(404).json({
          success: false,
          error: `Symbol ${validatedData.symbol} not found`,
          code: 'SYMBOL_NOT_FOUND',
          availableSymbols: availableSymbols.map(s => s.symbol)
        });
        return;
      }

      const historicalData = marketDataService.generateHistoricalData(
        validatedData.symbol,
        validatedData.timeframe,
        validatedData.count
      );

      res.status(200).json({
        success: true,
        data: {
          symbol: validatedData.symbol,
          timeframe: validatedData.timeframe,
          count: historicalData.length,
          candles: historicalData,
        },
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      if (error.name === 'ZodError') {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors
        });
        return;
      }
      next(error);
    }
  };

  const getSymbolsController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const { category } = req.query;

      let symbols = marketDataService.getAvailableSymbols();

      if (category) {
        symbols = symbols.filter(s => s.category === category);
      }

      res.status(200).json({
        success: true,
        data: {
          symbols,
          count: symbols.length,
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const getMarketStatusController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      // Mock market status - in real implementation, this would check forex market hours
      const now = new Date();
      const utcHour = now.getUTCHours();
      const utcDay = now.getUTCDay();
      
      // Forex market is open 24/5 (Sunday 5 PM EST to Friday 5 PM EST)
      const isWeekend = utcDay === 6 || (utcDay === 0 && utcHour < 22);
      const isFridayClose = utcDay === 5 && utcHour >= 22;
      
      const isOpen = !isWeekend && !isFridayClose;

      let nextOpen = null;
      let nextClose = null;

      if (!isOpen) {
        // Calculate next market open
        if (utcDay === 6 || (utcDay === 0 && utcHour < 22)) {
          // Weekend - next open is Sunday 10 PM UTC
          const nextSunday = new Date(now);
          nextSunday.setUTCDate(now.getUTCDate() + (7 - utcDay) % 7);
          nextSunday.setUTCHours(22, 0, 0, 0);
          nextOpen = nextSunday.toISOString();
        }
      } else {
        // Market is open - calculate next close (Friday 10 PM UTC)
        const nextFriday = new Date(now);
        nextFriday.setUTCDate(now.getUTCDate() + (5 - utcDay + 7) % 7);
        nextFriday.setUTCHours(22, 0, 0, 0);
        nextClose = nextFriday.toISOString();
      }

      res.status(200).json({
        success: true,
        data: {
          isOpen,
          marketName: 'Forex',
          timezone: 'UTC',
          currentTime: now.toISOString(),
          nextOpen,
          nextClose,
          tradingSessions: {
            sydney: { open: '22:00', close: '07:00', active: utcHour >= 22 || utcHour < 7 },
            tokyo: { open: '00:00', close: '09:00', active: utcHour >= 0 && utcHour < 9 },
            london: { open: '08:00', close: '17:00', active: utcHour >= 8 && utcHour < 17 },
            newYork: { open: '13:00', close: '22:00', active: utcHour >= 13 && utcHour < 22 },
          },
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  return {
    getQuoteController,
    getMultipleQuotesController,
    getHistoricalDataController,
    getSymbolsController,
    getMarketStatusController,
  };
}

// Export the router factory function
export const createMarketDataRoutes = () => {
  const router = Router();

  // Apply rate limiting
  router.use(marketDataRateLimit);

  // Create controllers
  const controllers = createMarketDataControllers();

  // === REAL-TIME QUOTES ROUTES ===

  /**
   * @route GET /api/market-data/quotes/:symbol
   * @desc Get real-time quote for a symbol
   * @access Public (rate limited)
   * @rateLimit 60 requests per second
   */
  router.get('/quotes/:symbol', quotesRateLimit, controllers.getQuoteController);

  /**
   * @route GET /api/market-data/quotes
   * @desc Get real-time quotes for multiple symbols
   * @access Public (rate limited)
   * @query symbols - comma-separated list of symbols (max 20)
   */
  router.get('/quotes', quotesRateLimit, controllers.getMultipleQuotesController);

  // === HISTORICAL DATA ROUTES ===

  /**
   * @route GET /api/market-data/historical/:symbol
   * @desc Get historical price data for a symbol
   * @access Public (rate limited)
   * @query timeframe - M1, M5, M15, M30, H1, H4, D1
   * @query count - number of candles (max 5000)
   */
  router.get('/historical/:symbol', controllers.getHistoricalDataController);

  // === MARKET INFO ROUTES ===

  /**
   * @route GET /api/market-data/symbols
   * @desc Get available trading symbols
   * @access Public
   */
  router.get('/symbols', controllers.getSymbolsController);

  /**
   * @route GET /api/market-data/status
   * @desc Get current market status and trading hours
   * @access Public
   */
  router.get('/status', controllers.getMarketStatusController);

  // === ERROR HANDLING ===

  // Handle 404 for unmatched market data routes
  router.use((req: Request, res: Response) => {
    res.status(404).json({
      success: false,
      error: 'Market data endpoint not found',
      code: 'ENDPOINT_NOT_FOUND',
      availableEndpoints: [
        'GET /api/market-data/quotes/:symbol',
        'GET /api/market-data/quotes?symbols=...',
        'GET /api/market-data/historical/:symbol',
        'GET /api/market-data/symbols',
        'GET /api/market-data/status'
      ]
    });
  });

  // Global error handler for market data routes
  router.use((error: any, req: Request, res: Response, _next: NextFunction) => {
    console.error('Market data route error:', {
      error: error.message,
      stack: error.stack,
      path: req.path,
      method: req.method,
      timestamp: new Date().toISOString()
    });

    // Handle specific market data errors
    if (error.code === 'MARKET_CLOSED') {
      res.status(503).json({
        success: false,
        error: 'Market is currently closed',
        code: 'MARKET_CLOSED',
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (error.code === 'DATA_UNAVAILABLE') {
      res.status(503).json({
        success: false,
        error: 'Market data temporarily unavailable',
        code: 'DATA_UNAVAILABLE',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Generic error response
    const isDevelopment = process.env.NODE_ENV === 'development';
    res.status(error.status || 500).json({
      success: false,
      error: isDevelopment ? error.message : 'Internal server error',
      code: error.code || 'INTERNAL_ERROR',
      ...(isDevelopment && { stack: error.stack }),
      timestamp: new Date().toISOString()
    });
  });

  return router;
};