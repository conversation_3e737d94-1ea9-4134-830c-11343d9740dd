/**
 * Connection Pool Manager
 * 
 * Manages connection pools with advanced features:
 * - Pool size optimization based on load
 * - Connection health monitoring and automatic failover
 * - Connection reuse and cleanup to prevent resource leaks
 * - Performance metrics and monitoring
 */

import { EventEmitter } from 'events';
import type { BrokerConnectionConfig, ConnectionState } from './ProductionMT5ConnectionManager';

// Pool configuration
export interface PoolConfig {
  minConnections: number;
  maxConnections: number;
  connectionTimeout: number;
  idleTimeout: number;
  maxRetries: number;
  healthCheckInterval: number;
  optimizationInterval: number;
  targetLatency: number;
  loadThreshold: number;
}

// Connection pool metrics
export interface PoolMetrics {
  totalConnections: number;
  activeConnections: number;
  idleConnections: number;
  waitingRequests: number;
  averageWaitTime: number;
  averageLatency: number;
  throughput: number;
  errorRate: number;
  connectionUtilization: number;
  poolEfficiency: number;
}

// Connection request
export interface ConnectionRequest {
  id: string;
  requestedAt: Date;
  brokerId: string;
  feature: string;
  priority: number;
  timeout: number;
  resolve: (connectionId: string | null) => void;
  reject: (error: Error) => void;
}

// Pool optimization recommendation
export interface OptimizationRecommendation {
  action: 'increase' | 'decrease' | 'maintain';
  currentSize: number;
  recommendedSize: number;
  reason: string;
  confidence: number;
  metrics: {
    utilization: number;
    latency: number;
    errorRate: number;
    waitTime: number;
  };
}

/**
 * Connection Pool Manager
 * Manages and optimizes connection pools for broker endpoints
 */
export class ConnectionPoolManager extends EventEmitter {
  private pools: Map<string, {
    config: PoolConfig;
    connections: Map<string, ConnectionState>;
    requests: ConnectionRequest[];
    metrics: PoolMetrics;
    lastOptimization: Date;
  }> = new Map();

  private optimizationInterval: NodeJS.Timeout | null = null;
  private metricsUpdateInterval: NodeJS.Timeout | null = null;
  private requestQueue: ConnectionRequest[] = [];
  private isShuttingDown = false;

  constructor(private globalConfig: {
    defaultPoolConfig?: Partial<PoolConfig>;
    enableAutoOptimization?: boolean;
    metricsUpdateInterval?: number;
  } = {}) {
    super();
    
    this.globalConfig = {
      enableAutoOptimization: true,
      metricsUpdateInterval: 10000, // 10 seconds
      ...globalConfig
    };

    this.startMetricsUpdates();
    if (this.globalConfig.enableAutoOptimization) {
      this.startPoolOptimization();
    }
  }

  /**
   * Create a new connection pool for a broker
   */
  createPool(brokerId: string, brokerConfig: BrokerConnectionConfig, poolConfig?: Partial<PoolConfig>): void {
    const config: PoolConfig = {
      minConnections: Math.max(1, Math.floor(brokerConfig.maxConnections * 0.2)),
      maxConnections: brokerConfig.maxConnections,
      connectionTimeout: 10000,
      idleTimeout: 300000, // 5 minutes
      maxRetries: 3,
      healthCheckInterval: 30000,
      optimizationInterval: 60000, // 1 minute
      targetLatency: 100, // 100ms
      loadThreshold: 0.8, // 80%
      ...this.globalConfig.defaultPoolConfig,
      ...poolConfig
    };

    this.pools.set(brokerId, {
      config,
      connections: new Map(),
      requests: [],
      metrics: this.initializeMetrics(),
      lastOptimization: new Date()
    });

    console.log(`🏊 Created connection pool for ${brokerId}: min=${config.minConnections}, max=${config.maxConnections}`);
    this.emit('poolCreated', { brokerId, config });
  }

  /**
   * Get a connection from the pool
   */
  async getConnection(brokerId: string, feature: string = 'trading', priority: number = 1): Promise<string | null> {
    const pool = this.pools.get(brokerId);
    if (!pool) {
      throw new Error(`Pool not found for broker: ${brokerId}`);
    }

    // Try to get an available connection immediately
    const availableConnection = this.findAvailableConnection(pool, feature);
    if (availableConnection) {
      this.updateConnectionUsage(pool, availableConnection);
      return availableConnection;
    }

    // If no connection available, check if we can create a new one
    if (pool.connections.size < pool.config.maxConnections) {
      const newConnectionId = await this.createConnection(brokerId, pool);
      if (newConnectionId) {
        this.updateConnectionUsage(pool, newConnectionId);
        return newConnectionId;
      }
    }

    // Queue the request if pool is at capacity
    return this.queueConnectionRequest(brokerId, feature, priority);
  }

  /**
   * Release a connection back to the pool
   */
  releaseConnection(brokerId: string, connectionId: string): void {
    const pool = this.pools.get(brokerId);
    if (!pool) return;

    const connection = pool.connections.get(connectionId);
    if (!connection) return;

    connection.lastRequestTime = new Date();
    
    // Process any queued requests
    this.processQueuedRequests(pool);
    
    console.log(`🔄 Released connection ${connectionId} back to pool`);
    this.emit('connectionReleased', { brokerId, connectionId });
  }

  /**
   * Get pool statistics
   */
  getPoolStats(brokerId?: string): Map<string, PoolMetrics> | PoolMetrics | null {
    if (brokerId) {
      const pool = this.pools.get(brokerId);
      return pool ? pool.metrics : null;
    }

    const allStats = new Map<string, PoolMetrics>();
    for (const [poolBrokerId, pool] of this.pools) {
      allStats.set(poolBrokerId, pool.metrics);
    }
    return allStats;
  }

  /**
   * Optimize pool size based on current metrics
   */
  async optimizePool(brokerId: string): Promise<OptimizationRecommendation> {
    const pool = this.pools.get(brokerId);
    if (!pool) {
      throw new Error(`Pool not found for broker: ${brokerId}`);
    }

    const metrics = pool.metrics;
    const currentSize = pool.connections.size;
    
    // Calculate optimization metrics
    const utilization = metrics.connectionUtilization;
    const avgLatency = metrics.averageLatency;
    const errorRate = metrics.errorRate;
    const waitTime = metrics.averageWaitTime;
    
    let recommendation: OptimizationRecommendation;

    // Decision logic for pool optimization
    if (utilization > pool.config.loadThreshold && waitTime > 1000) {
      // High utilization and long wait times - increase pool size
      const recommendedSize = Math.min(
        pool.config.maxConnections,
        Math.ceil(currentSize * 1.2)
      );
      
      recommendation = {
        action: 'increase',
        currentSize,
        recommendedSize,
        reason: `High utilization (${(utilization * 100).toFixed(1)}%) and wait time (${waitTime.toFixed(0)}ms)`,
        confidence: Math.min(0.9, utilization + (waitTime / 10000)),
        metrics: { utilization, latency: avgLatency, errorRate, waitTime }
      };
      
    } else if (utilization < 0.3 && currentSize > pool.config.minConnections && waitTime < 100) {
      // Low utilization and minimal wait times - decrease pool size
      const recommendedSize = Math.max(
        pool.config.minConnections,
        Math.floor(currentSize * 0.8)
      );
      
      recommendation = {
        action: 'decrease',
        currentSize,
        recommendedSize,
        reason: `Low utilization (${(utilization * 100).toFixed(1)}%) with minimal wait time`,
        confidence: Math.min(0.8, (1 - utilization) + (100 - waitTime) / 1000),
        metrics: { utilization, latency: avgLatency, errorRate, waitTime }
      };
      
    } else {
      // Maintain current pool size
      recommendation = {
        action: 'maintain',
        currentSize,
        recommendedSize: currentSize,
        reason: 'Pool size is optimal for current load',
        confidence: 0.7,
        metrics: { utilization, latency: avgLatency, errorRate, waitTime }
      };
    }

    // Apply optimization if confidence is high enough
    if (recommendation.confidence > 0.7 && recommendation.action !== 'maintain') {
      await this.applyOptimization(brokerId, recommendation);
    }

    pool.lastOptimization = new Date();
    this.emit('poolOptimized', { brokerId, recommendation });
    
    return recommendation;
  }

  /**
   * Remove unhealthy connections from the pool
   */
  cleanupUnhealthyConnections(brokerId?: string): number {
    let removedCount = 0;
    const poolsToClean = brokerId ? [brokerId] : Array.from(this.pools.keys());

    for (const poolBrokerId of poolsToClean) {
      const pool = this.pools.get(poolBrokerId);
      if (!pool) continue;

      const unhealthyConnections = Array.from(pool.connections.entries())
        .filter(([_, connection]) => 
          !connection.isHealthy || 
          connection.status === 'error' ||
          (Date.now() - (connection.lastRequestTime?.getTime() || 0) > pool.config.idleTimeout)
        );

      for (const [connectionId, _] of unhealthyConnections) {
        pool.connections.delete(connectionId);
        removedCount++;
        console.log(`🧹 Removed unhealthy connection: ${connectionId}`);
        this.emit('connectionRemoved', { brokerId: poolBrokerId, connectionId });
      }
    }

    return removedCount;
  }

  /**
   * Shutdown all pools
   */
  async shutdown(): Promise<void> {
    console.log('🔌 Shutting down Connection Pool Manager...');
    this.isShuttingDown = true;

    // Clear intervals
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
      this.optimizationInterval = null;
    }

    if (this.metricsUpdateInterval) {
      clearInterval(this.metricsUpdateInterval);
      this.metricsUpdateInterval = null;
    }

    // Reject all queued requests
    for (const request of this.requestQueue) {
      request.reject(new Error('Connection pool shutting down'));
    }
    this.requestQueue.length = 0;

    // Clear all pools
    this.pools.clear();
    
    console.log('✅ Connection Pool Manager shutdown complete');
    this.emit('shutdown');
  }

  /**
   * Find an available connection in the pool
   */
  private findAvailableConnection(pool: any, feature: string): string | null {
    for (const [connectionId, connection] of pool.connections) {
      if (connection.status === 'connected' && 
          connection.isHealthy && 
          (Date.now() - (connection.lastRequestTime?.getTime() || 0) > 1000)) {
        return connectionId;
      }
    }
    return null;
  }

  /**
   * Create a new connection in the pool
   */
  private async createConnection(brokerId: string, pool: any): Promise<string | null> {
    const connectionId = `${brokerId}_pool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    try {
      // Mock connection creation - in production would create actual MT5 connection
      const connection: ConnectionState = {
        id: connectionId,
        brokerId,
        status: 'connecting',
        lastConnect: null,
        lastDisconnect: null,
        lastError: null,
        retryCount: 0,
        failureCount: 0,
        requestCount: 0,
        lastRequestTime: null,
        isHealthy: false,
        latency: 0
      };

      pool.connections.set(connectionId, connection);

      // Simulate connection establishment
      await new Promise((resolve, reject) => {
        setTimeout(() => {
          if (Math.random() < 0.9) { // 90% success rate
            connection.status = 'connected';
            connection.lastConnect = new Date();
            connection.isHealthy = true;
            connection.latency = 50 + Math.random() * 100;
            resolve(connectionId);
          } else {
            reject(new Error('Connection failed'));
          }
        }, 500 + Math.random() * 1500);
      });

      console.log(`✅ Created new pooled connection: ${connectionId}`);
      this.emit('connectionCreated', { brokerId, connectionId });
      return connectionId;
      
    } catch (error) {
      pool.connections.delete(connectionId);
      console.error(`❌ Failed to create connection: ${error}`);
      return null;
    }
  }

  /**
   * Queue a connection request when pool is at capacity
   */
  private queueConnectionRequest(brokerId: string, feature: string, priority: number): Promise<string | null> {
    return new Promise((resolve, reject) => {
      const request: ConnectionRequest = {
        id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        requestedAt: new Date(),
        brokerId,
        feature,
        priority,
        timeout: 30000, // 30 second timeout
        resolve,
        reject
      };

      // Insert request based on priority
      const insertIndex = this.requestQueue.findIndex(r => r.priority < priority);
      if (insertIndex === -1) {
        this.requestQueue.push(request);
      } else {
        this.requestQueue.splice(insertIndex, 0, request);
      }

      // Set timeout
      setTimeout(() => {
        const requestIndex = this.requestQueue.findIndex(r => r.id === request.id);
        if (requestIndex !== -1) {
          this.requestQueue.splice(requestIndex, 1);
          reject(new Error('Connection request timeout'));
        }
      }, request.timeout);

      console.log(`📋 Queued connection request: ${request.id} (priority: ${priority})`);
    });
  }

  /**
   * Process queued connection requests
   */
  private processQueuedRequests(pool: any): void {
    while (this.requestQueue.length > 0) {
      const availableConnection = this.findAvailableConnection(pool, 'trading');
      if (!availableConnection) break;

      const request = this.requestQueue.shift();
      if (request) {
        this.updateConnectionUsage(pool, availableConnection);
        request.resolve(availableConnection);
        console.log(`✅ Fulfilled queued request: ${request.id}`);
      }
    }
  }

  /**
   * Update connection usage statistics
   */
  private updateConnectionUsage(pool: any, connectionId: string): void {
    const connection = pool.connections.get(connectionId);
    if (connection) {
      connection.requestCount++;
      connection.lastRequestTime = new Date();
    }
  }

  /**
   * Apply pool optimization recommendation
   */
  private async applyOptimization(brokerId: string, recommendation: OptimizationRecommendation): Promise<void> {
    const pool = this.pools.get(brokerId);
    if (!pool) return;

    const sizeDiff = recommendation.recommendedSize - recommendation.currentSize;
    
    if (recommendation.action === 'increase' && sizeDiff > 0) {
      // Add connections
      const createPromises = Array(sizeDiff).fill(null).map(() => 
        this.createConnection(brokerId, pool)
      );
      
      const results = await Promise.allSettled(createPromises);
      const successCount = results.filter(r => r.status === 'fulfilled').length;
      
      console.log(`📈 Increased pool size for ${brokerId} by ${successCount} connections`);
      
    } else if (recommendation.action === 'decrease' && sizeDiff < 0) {
      // Remove connections (idle ones first)
      const connectionsToRemove = Array.from(pool.connections.entries())
        .filter(([_, conn]) => Date.now() - (conn.lastRequestTime?.getTime() || 0) > 30000)
        .slice(0, Math.abs(sizeDiff));
      
      for (const [connectionId, _] of connectionsToRemove) {
        pool.connections.delete(connectionId);
        console.log(`📉 Removed idle connection: ${connectionId}`);
        this.emit('connectionRemoved', { brokerId, connectionId });
      }
    }
  }

  /**
   * Initialize metrics for a new pool
   */
  private initializeMetrics(): PoolMetrics {
    return {
      totalConnections: 0,
      activeConnections: 0,
      idleConnections: 0,
      waitingRequests: 0,
      averageWaitTime: 0,
      averageLatency: 0,
      throughput: 0,
      errorRate: 0,
      connectionUtilization: 0,
      poolEfficiency: 0
    };
  }

  /**
   * Start periodic metrics updates
   */
  private startMetricsUpdates(): void {
    this.metricsUpdateInterval = setInterval(() => {
      this.updateAllPoolMetrics();
    }, this.globalConfig.metricsUpdateInterval);
  }

  /**
   * Start pool optimization
   */
  private startPoolOptimization(): void {
    this.optimizationInterval = setInterval(async () => {
      if (this.isShuttingDown) return;

      for (const [brokerId, pool] of this.pools) {
        if (Date.now() - pool.lastOptimization.getTime() > pool.config.optimizationInterval) {
          try {
            await this.optimizePool(brokerId);
          } catch (error) {
            console.error(`❌ Failed to optimize pool ${brokerId}:`, error);
          }
        }
      }
    }, 60000); // Check every minute
  }

  /**
   * Update metrics for all pools
   */
  private updateAllPoolMetrics(): void {
    for (const [brokerId, pool] of this.pools) {
      const connections = Array.from(pool.connections.values());
      const activeConnections = connections.filter(c => c.status === 'connected' && c.isHealthy);
      const totalLatency = connections.reduce((sum, c) => sum + c.latency, 0);
      
      pool.metrics = {
        totalConnections: connections.length,
        activeConnections: activeConnections.length,
        idleConnections: connections.length - activeConnections.length,
        waitingRequests: this.requestQueue.filter(r => r.brokerId === brokerId).length,
        averageWaitTime: this.calculateAverageWaitTime(brokerId),
        averageLatency: connections.length > 0 ? totalLatency / connections.length : 0,
        throughput: this.calculateThroughput(connections),
        errorRate: this.calculateErrorRate(connections),
        connectionUtilization: connections.length > 0 ? activeConnections.length / connections.length : 0,
        poolEfficiency: this.calculatePoolEfficiency(pool)
      };
    }
  }

  /**
   * Calculate average wait time for requests
   */
  private calculateAverageWaitTime(brokerId: string): number {
    const brokerRequests = this.requestQueue.filter(r => r.brokerId === brokerId);
    if (brokerRequests.length === 0) return 0;
    
    const totalWaitTime = brokerRequests.reduce((sum, r) => 
      sum + (Date.now() - r.requestedAt.getTime()), 0
    );
    
    return totalWaitTime / brokerRequests.length;
  }

  /**
   * Calculate throughput (requests per second)
   */
  private calculateThroughput(connections: ConnectionState[]): number {
    const now = Date.now();
    const recentRequests = connections.reduce((sum, c) => {
      const timeSinceLastRequest = now - (c.lastRequestTime?.getTime() || now);
      return sum + (timeSinceLastRequest < 60000 ? c.requestCount : 0);
    }, 0);
    
    return recentRequests / 60; // requests per second over last minute
  }

  /**
   * Calculate error rate
   */
  private calculateErrorRate(connections: ConnectionState[]): number {
    if (connections.length === 0) return 0;
    
    const totalFailures = connections.reduce((sum, c) => sum + c.failureCount, 0);
    const totalRequests = connections.reduce((sum, c) => sum + c.requestCount, 0);
    
    return totalRequests > 0 ? totalFailures / totalRequests : 0;
  }

  /**
   * Calculate overall pool efficiency
   */
  private calculatePoolEfficiency(pool: any): number {
    const utilization = pool.metrics.connectionUtilization;
    const errorRate = pool.metrics.errorRate;
    const latency = pool.metrics.averageLatency;
    const targetLatency = pool.config.targetLatency;
    
    // Efficiency based on utilization, low error rate, and good latency
    const utilizationScore = Math.min(1, utilization / 0.8); // 80% utilization is ideal
    const errorScore = Math.max(0, 1 - errorRate * 10); // Penalize errors heavily
    const latencyScore = Math.max(0, 1 - Math.max(0, latency - targetLatency) / targetLatency);
    
    return (utilizationScore + errorScore + latencyScore) / 3;
  }
}