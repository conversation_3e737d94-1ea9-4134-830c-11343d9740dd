/**
 * Guaranteed Execution Engine Service
 * 
 * Multi-broker execution engine for ensuring stop-loss fills and emergency liquidations.
 * Provides failover support, slippage monitoring, and execution quality validation.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';
import { EventEmitter } from 'events';

/**
 * Broker interface for execution services
 */
export interface BrokerAdapter {
  id: string;
  name: string;
  priority: number;
  isActive: boolean;
  
  // Execution methods
  placeOrder(order: ExecutionOrder): Promise<ExecutionResult>;
  cancelOrder(orderId: string): Promise<boolean>;
  getOrderStatus(orderId: string): Promise<OrderStatus>;
  
  // Health and status
  healthCheck(): Promise<BrokerHealth>;
  getCapabilities(): BrokerCapabilities;
  getExecutionStats(): ExecutionStats;
}

/**
 * Broker health information
 */
export interface BrokerHealth {
  isHealthy: boolean;
  latency: number; // milliseconds
  uptime: number;  // percentage
  lastCheck: Date;
  errorRate: number;
  failureReasons?: string[];
}

/**
 * Broker capabilities
 */
export interface BrokerCapabilities {
  supportedOrderTypes: OrderType[];
  supportedTimeInForce: TimeInForce[];
  maxOrderSize: Decimal.Instance;
  minOrderSize: Decimal.Instance;
  supportsPartialFills: boolean;
  supportsStopLoss: boolean;
  supportsTrailingStop: boolean;
  averageExecutionTime: number; // milliseconds
}

/**
 * Broker execution statistics
 */
export interface ExecutionStats {
  totalOrders: number;
  successfulOrders: number;
  failedOrders: number;
  averageSlippage: Decimal.Instance;
  averageExecutionTime: number;
  lastExecutionTime: Date;
}

/**
 * Order types supported
 */
export type OrderType = 'market' | 'limit' | 'stop' | 'stop_limit' | 'trailing_stop';

/**
 * Time in force options
 */
export type TimeInForce = 'GTC' | 'IOC' | 'FOK' | 'DAY';

/**
 * Execution order details
 */
export interface ExecutionOrder {
  id: string;
  symbol: string;
  side: 'buy' | 'sell';
  quantity: Decimal.Instance;
  orderType: OrderType;
  price?: Decimal.Instance;
  stopPrice?: Decimal.Instance;
  trailingAmount?: Decimal.Instance;
  timeInForce: TimeInForce;
  expiration?: Date;
  
  // Risk and execution parameters
  maxSlippage?: Decimal.Instance;
  priority: 'low' | 'medium' | 'high' | 'critical';
  executionStrategy: 'speed' | 'price' | 'guaranteed';
  
  // Metadata
  positionId?: string;
  userId: string;
  accountId: string;
  reason: string;
  createdAt: Date;
}

/**
 * Execution result from broker
 */
export interface ExecutionResult {
  success: boolean;
  orderId?: string;
  brokerId: string;
  executedPrice?: Decimal.Instance;
  executedQuantity?: Decimal.Instance;
  remainingQuantity?: Decimal.Instance;
  executionTime?: Date;
  processingTime: number; // milliseconds
  
  // Quality metrics
  slippage?: Decimal.Instance;
  spread?: Decimal.Instance;
  marketImpact?: Decimal.Instance;
  
  // Error information
  errorCode?: string;
  errorMessage?: string;
  retryRecommended?: boolean;
  
  // Broker-specific data
  brokerOrderId?: string;
  brokerMetadata?: Record<string, unknown>;
}

/**
 * Order status information
 */
export interface OrderStatus {
  orderId: string;
  brokerId: string;
  status: 'pending' | 'partial' | 'filled' | 'cancelled' | 'rejected' | 'expired';
  filledQuantity: Decimal.Instance;
  remainingQuantity: Decimal.Instance;
  averageFillPrice?: Decimal.Instance;
  lastFillPrice?: Decimal.Instance;
  lastFillTime?: Date;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Failover configuration
 */
export interface FailoverConfig {
  enableFailover: boolean;
  maxBrokerRetries: number;
  brokerTimeoutMs: number;
  failoverDelayMs: number;
  requiredSuccessRate: number; // Minimum success rate to keep broker active
  healthCheckIntervalMs: number;
  
  // Quality thresholds
  maxAcceptableSlippage: Decimal.Instance;
  maxExecutionTimeMs: number;
  minFillRate: number; // Minimum percentage of order that must be filled
}

/**
 * Guaranteed execution configuration
 */
export interface GuaranteedExecutionConfig extends FailoverConfig {
  // Execution strategies
  simultaneousExecution: boolean; // Execute on multiple brokers simultaneously
  partialFillTolerance: number; // Accept partial fills above this percentage
  
  // Emergency settings
  emergencyModeThreshold: number; // Trigger emergency mode after this many failures
  emergencyExecutionTimeoutMs: number;
  forceMarketOrderOnFailure: boolean;
  
  // Quality monitoring
  slippageAlertThreshold: Decimal.Instance;
  executionQualityThreshold: number; // 0-1 quality score threshold
  enableExecutionAnalytics: boolean;
  
  // Circuit breaker
  enableCircuitBreaker: boolean;
  circuitBreakerThreshold: number; // Failure rate threshold
  circuitBreakerRecoveryTime: number; // Recovery time in minutes
  
  // Test configuration
  disableBackgroundMonitoring?: boolean; // Disable intervals for testing
}

/**
 * Default guaranteed execution configuration
 */
export const DEFAULT_GUARANTEED_EXECUTION_CONFIG: GuaranteedExecutionConfig = {
  // Failover settings
  enableFailover: true,
  maxBrokerRetries: 3,
  brokerTimeoutMs: 5000,
  failoverDelayMs: 500,
  requiredSuccessRate: 0.95,
  healthCheckIntervalMs: 30000,
  
  // Quality thresholds
  maxAcceptableSlippage: new Decimal(0.0005), // 0.5 pips
  maxExecutionTimeMs: 3000,
  minFillRate: 0.9,
  
  // Execution strategies
  simultaneousExecution: false,
  partialFillTolerance: 0.95,
  
  // Emergency settings
  emergencyModeThreshold: 3,
  emergencyExecutionTimeoutMs: 1000,
  forceMarketOrderOnFailure: true,
  
  // Quality monitoring
  slippageAlertThreshold: new Decimal(0.001), // 1 pip
  executionQualityThreshold: 0.8,
  enableExecutionAnalytics: true,
  
  // Circuit breaker
  enableCircuitBreaker: true,
  circuitBreakerThreshold: 0.5, // 50% failure rate
  circuitBreakerRecoveryTime: 5 // 5 minutes
};

/**
 * Execution attempt result
 */
export interface ExecutionAttempt {
  brokerId: string;
  attempt: number;
  startTime: Date;
  endTime?: Date;
  result?: ExecutionResult;
  error?: string;
  processingTime?: number;
}

/**
 * Multi-broker execution tracking
 */
export interface MultiExecutionState {
  orderId: string;
  originalOrder: ExecutionOrder;
  attempts: ExecutionAttempt[];
  totalQuantityFilled: Decimal.Instance;
  bestExecution?: ExecutionResult;
  isCompleted: boolean;
  completedAt?: Date;
  failureCount: number;
  emergencyModeTriggered: boolean;
}

/**
 * Circuit breaker state
 */
export interface CircuitBreakerState {
  isOpen: boolean;
  failureCount: number;
  lastFailureTime?: Date;
  openedAt?: Date;
  recoveryTime?: Date;
  totalRequests: number;
  failureRate: number;
}

/**
 * Guaranteed Execution Engine - Multi-broker execution with failover support
 */
export class GuaranteedExecutionEngine extends EventEmitter {
  private readonly config: GuaranteedExecutionConfig;
  private brokers: Map<string, BrokerAdapter> = new Map();
  private executionStates: Map<string, MultiExecutionState> = new Map();
  private circuitBreaker: CircuitBreakerState;
  
  private healthCheckInterval?: NodeJS.Timeout;
  private isEmergencyMode = false;
  private emergencyModeActivatedAt?: Date;

  constructor(config: Partial<GuaranteedExecutionConfig> = {}) {
    super();
    this.config = { ...DEFAULT_GUARANTEED_EXECUTION_CONFIG, ...config };
    
    this.circuitBreaker = {
      isOpen: false,
      failureCount: 0,
      totalRequests: 0,
      failureRate: 0
    };
    
    this.initializeHealthChecking();
  }

  /**
   * Register a broker adapter
   */
  public registerBroker(broker: BrokerAdapter): void {
    this.brokers.set(broker.id, broker);
    this.emit('broker_registered', {
      brokerId: broker.id,
      brokerName: broker.name,
      priority: broker.priority,
      timestamp: new Date()
    });
  }

  /**
   * Unregister a broker adapter
   */
  public unregisterBroker(brokerId: string): boolean {
    const removed = this.brokers.delete(brokerId);
    if (removed) {
      this.emit('broker_unregistered', {
        brokerId,
        timestamp: new Date()
      });
    }
    return removed;
  }

  /**
   * Execute order with guaranteed execution across multiple brokers
   */
  public async executeOrder(order: ExecutionOrder): Promise<ExecutionResult> {
    try {
      // Check circuit breaker
      if (this.circuitBreaker.isOpen) {
        throw new Error('Circuit breaker is open - execution temporarily disabled');
      }

      // Validate order
      this.validateOrder(order);
      
      // Initialize execution state
      const executionState: MultiExecutionState = {
        orderId: order.id,
        originalOrder: order,
        attempts: [],
        totalQuantityFilled: new Decimal(0),
        isCompleted: false,
        failureCount: 0,
        emergencyModeTriggered: false
      };
      
      this.executionStates.set(order.id, executionState);
      
      this.emit('execution_started', {
        orderId: order.id,
        symbol: order.symbol,
        quantity: order.quantity,
        strategy: order.executionStrategy,
        timestamp: new Date()
      });

      // Execute based on strategy
      let result: ExecutionResult;
      
      if (this.config.simultaneousExecution && order.priority === 'critical') {
        result = await this.executeSimultaneously(executionState);
      } else {
        result = await this.executeSequentially(executionState);
      }
      
      // Update circuit breaker
      this.updateCircuitBreaker(result.success);
      
      // Mark as completed
      executionState.isCompleted = true;
      executionState.completedAt = new Date();
      executionState.bestExecution = result;
      
      this.emit('execution_completed', {
        orderId: order.id,
        success: result.success,
        totalAttempts: executionState.attempts.length,
        finalBroker: result.brokerId,
        slippage: result.slippage,
        executionTime: result.processingTime,
        timestamp: new Date()
      });
      
      return result;
    } catch (error) {
      this.updateCircuitBreaker(false);
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown execution error';
      const executionState = this.executionStates.get(order.id);
      
      // Mark failed execution as completed for statistics
      if (executionState) {
        executionState.isCompleted = true;
        executionState.completedAt = new Date();
        executionState.bestExecution = {
          success: false,
          brokerId: 'none',
          processingTime: 0,
          errorCode: 'EXECUTION_FAILED',
          errorMessage
        };
      }
      
      this.emit('execution_failed', {
        orderId: order.id,
        error: errorMessage,
        attempts: executionState?.attempts.length || 0,
        timestamp: new Date()
      });
      
      return {
        success: false,
        brokerId: 'none',
        processingTime: 0,
        errorCode: 'EXECUTION_FAILED',
        errorMessage
      };
    }
  }

  /**
   * Cancel order across all brokers where it may be active
   */
  public async cancelOrder(orderId: string): Promise<boolean> {
    const executionState = this.executionStates.get(orderId);
    if (!executionState) {
      return false;
    }

    let anyCancelled = false;
    
    // Cancel on all brokers where order was attempted
    for (const attempt of executionState.attempts) {
      if (attempt.result?.success && attempt.result.orderId) {
        try {
          const broker = this.brokers.get(attempt.brokerId);
          if (broker) {
            const cancelled = await broker.cancelOrder(attempt.result.orderId);
            if (cancelled) {
              anyCancelled = true;
            }
          }
        } catch (error) {
          // Log but continue trying other brokers
          this.emit('cancel_error', {
            orderId,
            brokerId: attempt.brokerId,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date()
          });
        }
      }
    }

    if (anyCancelled) {
      executionState.isCompleted = true;
      executionState.completedAt = new Date();
      
      this.emit('order_cancelled', {
        orderId,
        timestamp: new Date()
      });
    }

    return anyCancelled;
  }

  /**
   * Get execution status for an order
   */
  public getExecutionStatus(orderId: string): MultiExecutionState | undefined {
    return this.executionStates.get(orderId);
  }

  /**
   * Get active brokers sorted by priority and health
   */
  public getActiveBrokers(): BrokerAdapter[] {
    return Array.from(this.brokers.values())
      .filter(broker => broker.isActive)
      .sort((a, b) => {
        // Sort by priority first (lower number = higher priority)
        if (a.priority !== b.priority) {
          return a.priority - b.priority;
        }
        // Then by broker ID for consistency
        return a.id.localeCompare(b.id);
      });
  }

  /**
   * Get service statistics
   */
  public getExecutionStatistics(): {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    emergencyModeActive: boolean;
    circuitBreakerOpen: boolean;
    activeBrokers: number;
    brokerStats: { [brokerId: string]: ExecutionStats };
  } {
    const completedExecutions = Array.from(this.executionStates.values())
      .filter(state => state.isCompleted);
    
    const successful = completedExecutions
      .filter(state => state.bestExecution?.success).length;
    
    const averageTime = completedExecutions.length > 0
      ? completedExecutions.reduce((sum, state) => 
          sum + (state.bestExecution?.processingTime || 0), 0) / completedExecutions.length
      : 0;

    const brokerStats: { [brokerId: string]: ExecutionStats } = {};
    for (const [brokerId, broker] of this.brokers) {
      brokerStats[brokerId] = broker.getExecutionStats();
    }

    return {
      totalExecutions: completedExecutions.length,
      successfulExecutions: successful,
      failedExecutions: completedExecutions.length - successful,
      averageExecutionTime: averageTime,
      emergencyModeActive: this.isEmergencyMode,
      circuitBreakerOpen: this.circuitBreaker.isOpen,
      activeBrokers: this.getActiveBrokers().length,
      brokerStats
    };
  }

  /**
   * Force emergency mode for critical executions
   */
  public activateEmergencyMode(reason: string): void {
    this.isEmergencyMode = true;
    this.emergencyModeActivatedAt = new Date();
    
    this.emit('emergency_mode_activated', {
      reason,
      timestamp: this.emergencyModeActivatedAt
    });
  }

  /**
   * Deactivate emergency mode
   */
  public deactivateEmergencyMode(): void {
    this.isEmergencyMode = false;
    this.emergencyModeActivatedAt = undefined;
    
    this.emit('emergency_mode_deactivated', {
      timestamp: new Date()
    });
  }

  /**
   * Manually reset circuit breaker
   */
  public resetCircuitBreaker(): void {
    this.circuitBreaker = {
      isOpen: false,
      failureCount: 0,
      totalRequests: 0,
      failureRate: 0
    };
    
    this.emit('circuit_breaker_reset', {
      timestamp: new Date()
    });
  }

  /**
   * Shutdown the execution engine
   */
  public async shutdown(): Promise<void> {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    // Cancel all pending executions
    for (const [orderId, state] of this.executionStates) {
      if (!state.isCompleted) {
        await this.cancelOrder(orderId);
      }
    }
    
    this.emit('engine_shutdown', {
      timestamp: new Date()
    });
  }

  /**
   * Execute order sequentially across brokers until success
   */
  private async executeSequentially(executionState: MultiExecutionState): Promise<ExecutionResult> {
    const activeBrokers = this.getActiveBrokers();
    
    if (activeBrokers.length === 0) {
      throw new Error('No active brokers available for execution');
    }

    let lastError: string = 'No execution attempted';
    
    for (const broker of activeBrokers) {
      const attempt: ExecutionAttempt = {
        brokerId: broker.id,
        attempt: executionState.attempts.length + 1,
        startTime: new Date()
      };
      
      executionState.attempts.push(attempt);
      
      try {
        const timeoutMs = this.isEmergencyMode 
          ? this.config.emergencyExecutionTimeoutMs
          : this.config.brokerTimeoutMs;
          
        const result = await Promise.race([
          broker.placeOrder(executionState.originalOrder),
          new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('Broker execution timeout')), timeoutMs)
          )
        ]);
        
        attempt.endTime = new Date();
        attempt.result = result;
        attempt.processingTime = attempt.endTime.getTime() - attempt.startTime.getTime();
        
        // Check execution quality
        if (result.success && this.isAcceptableExecution(result)) {
          this.emit('broker_execution_success', {
            orderId: executionState.orderId,
            brokerId: broker.id,
            attempt: attempt.attempt,
            executionTime: attempt.processingTime,
            slippage: result.slippage,
            timestamp: new Date()
          });
          
          return result;
        } else if (result.success) {
          // Execution succeeded but quality is poor
          this.emit('poor_execution_quality', {
            orderId: executionState.orderId,
            brokerId: broker.id,
            slippage: result.slippage,
            executionTime: attempt.processingTime,
            timestamp: new Date()
          });
          
          // Continue to next broker if quality is unacceptable
          lastError = 'Execution quality below threshold';
        } else {
          lastError = result.errorMessage || 'Execution failed';
        }
        
      } catch (error) {
        attempt.endTime = new Date();
        attempt.error = error instanceof Error ? error.message : 'Unknown error';
        attempt.processingTime = attempt.endTime.getTime() - attempt.startTime.getTime();
        
        lastError = attempt.error;
        
        this.emit('broker_execution_failed', {
          orderId: executionState.orderId,
          brokerId: broker.id,
          attempt: attempt.attempt,
          error: attempt.error,
          timestamp: new Date()
        });
        
        executionState.failureCount++;
        
        // Check if we should activate emergency mode
        if (executionState.failureCount >= this.config.emergencyModeThreshold) {
          this.activateEmergencyMode(`Multiple execution failures for order ${executionState.orderId}`);
          executionState.emergencyModeTriggered = true;
        }
      }
      
      // Add delay between broker attempts (except for emergency mode)
      if (!this.isEmergencyMode && activeBrokers.indexOf(broker) < activeBrokers.length - 1) {
        await new Promise(resolve => setTimeout(resolve, this.config.failoverDelayMs));
      }
    }
    
    // All brokers failed - try emergency market order if configured
    if (this.config.forceMarketOrderOnFailure && 
        this.isEmergencyMode && 
        executionState.originalOrder.orderType !== 'market') {
      
      return await this.executeEmergencyMarketOrder(executionState);
    }
    
    throw new Error(`All broker executions failed. Last error: ${lastError}`);
  }

  /**
   * Execute order simultaneously on multiple brokers (for critical orders)
   */
  private async executeSimultaneously(executionState: MultiExecutionState): Promise<ExecutionResult> {
    const activeBrokers = this.getActiveBrokers();
    
    if (activeBrokers.length === 0) {
      throw new Error('No active brokers available for simultaneous execution');
    }
    
    // Execute on all brokers simultaneously
    const executionPromises = activeBrokers.map(async (broker) => {
      const attempt: ExecutionAttempt = {
        brokerId: broker.id,
        attempt: executionState.attempts.length + 1,
        startTime: new Date()
      };
      
      executionState.attempts.push(attempt);
      
      try {
        const result = await broker.placeOrder(executionState.originalOrder);
        attempt.endTime = new Date();
        attempt.result = result;
        attempt.processingTime = attempt.endTime.getTime() - attempt.startTime.getTime();
        
        return { broker, attempt, result };
      } catch (error) {
        attempt.endTime = new Date();
        attempt.error = error instanceof Error ? error.message : 'Unknown error';
        attempt.processingTime = attempt.endTime.getTime() - attempt.startTime.getTime();
        
        throw { broker, attempt, error };
      }
    });
    
    // Wait for first success or all failures
    try {
      const results = await Promise.allSettled(executionPromises);
      
      // Find the best successful execution
      let bestExecution: ExecutionResult | undefined;
      let bestQuality = -1;
      
      for (const result of results) {
        if (result.status === 'fulfilled') {
          const { result: execResult } = result.value;
          if (execResult.success) {
            const quality = this.calculateExecutionQuality(execResult);
            if (quality > bestQuality) {
              bestQuality = quality;
              bestExecution = execResult;
            }
          }
        }
      }
      
      if (bestExecution) {
        // Cancel orders on other brokers
        this.cancelOtherBrokerOrders(executionState, bestExecution.brokerId);
        return bestExecution;
      }
      
      throw new Error('All simultaneous executions failed');
    } catch (error) {
      throw new Error(`Simultaneous execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute emergency market order as last resort
   */
  private async executeEmergencyMarketOrder(executionState: MultiExecutionState): Promise<ExecutionResult> {
    const emergencyOrder: ExecutionOrder = {
      ...executionState.originalOrder,
      id: `emergency_${executionState.orderId}`,
      orderType: 'market',
      price: undefined,
      stopPrice: undefined,
      priority: 'critical',
      reason: `Emergency market order for failed ${executionState.originalOrder.orderType}`
    };
    
    const activeBrokers = this.getActiveBrokers();
    for (const broker of activeBrokers) {
      try {
        const result = await Promise.race([
          broker.placeOrder(emergencyOrder),
          new Promise<never>((_, reject) => 
            setTimeout(() => reject(new Error('Emergency execution timeout')), 
              this.config.emergencyExecutionTimeoutMs)
          )
        ]);
        
        if (result.success) {
          this.emit('emergency_execution_success', {
            orderId: executionState.orderId,
            brokerId: broker.id,
            originalOrderType: executionState.originalOrder.orderType,
            executionPrice: result.executedPrice,
            timestamp: new Date()
          });
          
          return result;
        }
      } catch (error) {
        // Continue to next broker
        continue;
      }
    }
    
    throw new Error('Emergency market order execution failed on all brokers');
  }

  /**
   * Cancel orders on other brokers after successful execution
   */
  private async cancelOtherBrokerOrders(
    executionState: MultiExecutionState, 
    successfulBrokerId: string
  ): Promise<void> {
    for (const attempt of executionState.attempts) {
      if (attempt.brokerId !== successfulBrokerId && 
          attempt.result?.success && 
          attempt.result.orderId) {
        
        try {
          const broker = this.brokers.get(attempt.brokerId);
          if (broker) {
            await broker.cancelOrder(attempt.result.orderId);
          }
        } catch (error) {
          // Log but don't throw - cancellation failures are not critical
          this.emit('cancel_failed', {
            orderId: attempt.result.orderId,
            brokerId: attempt.brokerId,
            error: error instanceof Error ? error.message : 'Unknown error',
            timestamp: new Date()
          });
        }
      }
    }
  }

  /**
   * Check if execution meets quality standards
   */
  private isAcceptableExecution(result: ExecutionResult): boolean {
    // Check slippage
    if (result.slippage && result.slippage.gt(this.config.maxAcceptableSlippage)) {
      return false;
    }
    
    // Check execution time
    if (result.processingTime > this.config.maxExecutionTimeMs) {
      return false;
    }
    
    // Check fill rate (if partial fill)
    if (result.remainingQuantity && result.executedQuantity) {
      const fillRate = result.executedQuantity
        .div(result.executedQuantity.add(result.remainingQuantity))
        .toNumber();
      
      if (fillRate < this.config.minFillRate) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Calculate overall execution quality score (0-1)
   */
  private calculateExecutionQuality(result: ExecutionResult): number {
    let quality = 1.0;
    
    // Penalize for slippage
    if (result.slippage) {
      const slippagePenalty = result.slippage.div(this.config.maxAcceptableSlippage).toNumber();
      quality *= Math.max(0, 1 - slippagePenalty);
    }
    
    // Penalize for slow execution
    const timePenalty = result.processingTime / this.config.maxExecutionTimeMs;
    quality *= Math.max(0, 1 - timePenalty);
    
    // Penalize for partial fills
    if (result.remainingQuantity && result.executedQuantity) {
      const fillRate = result.executedQuantity
        .div(result.executedQuantity.add(result.remainingQuantity))
        .toNumber();
      quality *= fillRate;
    }
    
    return Math.max(0, Math.min(1, quality));
  }

  /**
   * Update circuit breaker state
   */
  private updateCircuitBreaker(success: boolean): void {
    this.circuitBreaker.totalRequests++;
    
    if (!success) {
      this.circuitBreaker.failureCount++;
      this.circuitBreaker.lastFailureTime = new Date();
    }
    
    this.circuitBreaker.failureRate = this.circuitBreaker.failureCount / this.circuitBreaker.totalRequests;
    
    // Check if circuit breaker should open
    if (!this.circuitBreaker.isOpen && 
        this.circuitBreaker.failureRate >= this.config.circuitBreakerThreshold &&
        this.circuitBreaker.totalRequests >= 10) {
      
      this.circuitBreaker.isOpen = true;
      this.circuitBreaker.openedAt = new Date();
      this.circuitBreaker.recoveryTime = new Date(
        Date.now() + (this.config.circuitBreakerRecoveryTime * 60 * 1000)
      );
      
      this.emit('circuit_breaker_opened', {
        failureRate: this.circuitBreaker.failureRate,
        totalRequests: this.circuitBreaker.totalRequests,
        recoveryTime: this.circuitBreaker.recoveryTime,
        timestamp: new Date()
      });
    }
    
    // Check if circuit breaker should close (recovery)
    if (this.circuitBreaker.isOpen && 
        this.circuitBreaker.recoveryTime &&
        new Date() >= this.circuitBreaker.recoveryTime) {
      
      this.circuitBreaker.isOpen = false;
      this.circuitBreaker.failureCount = 0;
      this.circuitBreaker.totalRequests = 0;
      this.circuitBreaker.failureRate = 0;
      
      this.emit('circuit_breaker_closed', {
        timestamp: new Date()
      });
    }
  }

  /**
   * Initialize health checking for all brokers
   */
  private initializeHealthChecking(): void {
    // Skip background monitoring in test environments
    if (this.config.disableBackgroundMonitoring) {
      return;
    }
    
    this.healthCheckInterval = setInterval(
      () => this.performBrokerHealthChecks(),
      this.config.healthCheckIntervalMs
    );
  }

  /**
   * Manually perform health checks (for testing)
   */
  public async performHealthCheck(): Promise<void> {
    await this.performBrokerHealthChecks();
  }

  /**
   * Perform health checks on all registered brokers
   */
  private async performBrokerHealthChecks(): Promise<void> {
    for (const [brokerId, broker] of this.brokers) {
      try {
        const health = await broker.healthCheck();
        
        // Update broker status based on health
        const wasActive = broker.isActive;
        broker.isActive = health.isHealthy && 
          health.errorRate < (1 - this.config.requiredSuccessRate);
        
        // Emit status change events
        if (wasActive && !broker.isActive) {
          this.emit('broker_deactivated', {
            brokerId,
            reason: `Health check failed: ${health.failureReasons?.join(', ') || 'Unknown'}`,
            health,
            timestamp: new Date()
          });
        } else if (!wasActive && broker.isActive) {
          this.emit('broker_activated', {
            brokerId,
            health,
            timestamp: new Date()
          });
        }
        
      } catch (error) {
        // Health check itself failed
        broker.isActive = false;
        
        this.emit('health_check_failed', {
          brokerId,
          error: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date()
        });
      }
    }
  }

  /**
   * Validate order before execution
   */
  private validateOrder(order: ExecutionOrder): void {
    if (!order.id || !order.symbol || !order.userId) {
      throw new Error('Order must have valid ID, symbol, and user ID');
    }
    
    if (order.quantity.lte(0)) {
      throw new Error('Order quantity must be positive');
    }
    
    if (!['buy', 'sell'].includes(order.side)) {
      throw new Error('Order side must be buy or sell');
    }
    
    if (!['market', 'limit', 'stop', 'stop_limit', 'trailing_stop'].includes(order.orderType)) {
      throw new Error('Invalid order type');
    }
    
    if (order.orderType === 'limit' && !order.price) {
      throw new Error('Limit orders must have a price');
    }
    
    if (['stop', 'stop_limit', 'trailing_stop'].includes(order.orderType) && !order.stopPrice) {
      throw new Error('Stop orders must have a stop price');
    }
  }
}

/**
 * Factory function to create GuaranteedExecutionEngine instance
 */
export function createGuaranteedExecutionEngine(
  config?: Partial<GuaranteedExecutionConfig>
): GuaranteedExecutionEngine {
  return new GuaranteedExecutionEngine(config);
}

/**
 * Default export for convenience
 */
export default GuaranteedExecutionEngine;