"""
MT5 Bridge Python Service - Main Application
FastAPI service with WebSocket support for real-time price streaming
"""

import asyncio
import json
import os
import time
from typing import Dict, List, Set, Optional, Any
from datetime import datetime
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends, Request, Response
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
import uvicorn
from loguru import logger
import sys

from config import get_config
from mt5_connection import get_mt5_connection, MT5Connection
from price_streamer import PriceStreamer, get_price_streamer
from data_transformer import DataTransformer
from database import get_db_manager, TimescaleDBManager
from historical_collector import get_historical_collector, HistoricalDataCollector, CollectionJob
from data_validator import get_data_validator, DataValidator
from paper_trading import get_paper_trading_engine, PaperTradingEngine, TradeRequest, OrderType
from health_monitor import get_health_monitor, HealthMonitor
from alert_notifications import get_alert_notification_manager, setup_default_notification_channels
from data_integrity import get_data_integrity_validator, DataIntegrityValidator
from performance_monitor import get_performance_profiler, PerformanceProfiler, PerformanceTestSuite
from latency_optimizer import get_latency_analyzer, LatencyAnalyzer
from monitoring import get_metrics_collector, AlertCondition, SystemMetrics, ServiceMetrics
from security_middleware import SecurityMiddleware, SecurityConfig, get_security_middleware
from centralized_logging import CentralizedLogger, get_centralized_logger, LogLevel
from production_monitoring import ProductionMonitor, get_production_monitor, ResourceThresholds
from backup_manager import BackupManager, get_backup_manager, BackupConfig
from api_models import *
import redis.asyncio as redis

# ML Infrastructure
from ml_infrastructure import MLService, create_ml_service

# Configure logging
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}:{function}:{line}</cyan> | <white>{message}</white>",
    level="INFO"
)
logger.add(
    "logs/mt5_bridge.log",
    rotation="daily",
    retention="30 days",
    level="DEBUG"
)

# Initialize FastAPI app
app = FastAPI(
    title="MT5 Bridge Service",
    description="""
    **MetaTrader 5 Integration Service**
    
    A comprehensive Python service for integrating with MetaTrader 5 platform, providing:
    
    - **Real-time Price Streaming**: WebSocket-based price feeds with rate limiting
    - **Historical Data Collection**: Automated data synchronization and backfill
    - **Paper Trading Engine**: Risk-free trade simulation and backtesting
    - **Performance Monitoring**: Real-time latency and throughput analysis
    - **Health Monitoring**: System health checks and alert notifications
    - **Data Validation**: Quality assurance and integrity checks
    
    ## Authentication
    
    This service uses API key authentication. Include your API key in the `X-API-Key` header.
    
    ## Rate Limits
    
    - REST API: 1000 requests per minute
    - WebSocket connections: 100 concurrent connections
    - Price updates: 100 updates per second per symbol
    
    ## Error Handling
    
    All endpoints return standardized error responses:
    - `400` - Bad Request: Invalid parameters
    - `401` - Unauthorized: Missing or invalid API key
    - `429` - Too Many Requests: Rate limit exceeded
    - `500` - Internal Server Error: Service unavailable
    
    """,
    version="1.0.0",
    contact={
        "name": "MT5 Bridge Support",
        "email": "<EMAIL>"
    },
    license_info={
        "name": "Proprietary",
        "identifier": "Proprietary"
    },
    servers=[
        {"url": "http://localhost:8000", "description": "Development server"},
        {"url": "https://api.golddaddy.ph", "description": "Production server"}
    ],
    tags_metadata=[
        {
            "name": "Health",
            "description": "System health and status monitoring endpoints"
        },
        {
            "name": "Connection",
            "description": "MT5 connection management and status"
        },
        {
            "name": "Market Data",
            "description": "Real-time and historical market data endpoints"
        },
        {
            "name": "Price Streaming",
            "description": "WebSocket-based real-time price streaming"
        },
        {
            "name": "Paper Trading",
            "description": "Simulated trading operations and portfolio management"
        },
        {
            "name": "Performance",
            "description": "Performance monitoring and optimization"
        },
        {
            "name": "Data Management",
            "description": "Data validation, integrity, and collection management"
        }
    ]
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global instances
config = get_config()

# Initialize Redis for rate limiting (if available)
redis_client = None
try:
    redis_client = redis.from_url(config.redis.url or "redis://localhost:6379")
    logger.info("Redis client initialized for rate limiting")
except Exception as e:
    logger.warning(f"Redis not available, using in-memory rate limiting: {e}")

# Initialize security middleware
security_config = SecurityConfig(
    jwt_secret=os.getenv("JWT_SECRET") or "your-secret-key",
    jwt_algorithm="HS256",
    api_key_header="X-API-Key",
    blocked_ips=[],  # Configure blocked IPs
    allowed_ips=None,  # Set to restrict access to specific IPs
    max_request_size_mb=10,
    enable_cors=True,
    cors_origins=["https://golddaddy.ph", "https://api.golddaddy.ph"] if os.getenv("ENVIRONMENT") == "production" else ["*"]
)
security_middleware = get_security_middleware(security_config, redis_client)

# Initialize centralized logging
centralized_logger = get_centralized_logger(
    service_name="mt5-bridge",
    service_version="1.0.0",
    environment=os.getenv("ENVIRONMENT") or "development",
    elasticsearch_url=os.getenv('ELASTICSEARCH_URL')
)

# Initialize production monitoring
production_monitor = get_production_monitor(
    thresholds=ResourceThresholds(
        cpu_warning=70.0,
        cpu_critical=85.0,
        memory_warning=75.0,
        memory_critical=90.0,
        disk_warning=80.0,
        disk_critical=90.0
    )
)

# Initialize backup manager
backup_config = BackupConfig(
    database_url=getattr(config, 'DATABASE_URL', 'postgresql://localhost/mt5_bridge'),
    database_name=getattr(config, 'POSTGRES_DB', 'mt5_bridge'),
    backup_directory="/backups",
    compress_backups=True,
    local_retention_days=7,
    remote_retention_days=30,
    full_backup_schedule="0 2 * * *",     # Daily at 2 AM
    incremental_schedule="0 */6 * * *",   # Every 6 hours
    verify_backups=True,
    # Cloud storage (configure as needed)
    enable_s3=getattr(config, 'ENABLE_S3_BACKUP', False),
    s3_bucket=getattr(config, 'S3_BACKUP_BUCKET', None),
    s3_region=getattr(config, 'S3_BACKUP_REGION', None),
    s3_access_key=getattr(config, 'S3_ACCESS_KEY', None),
    s3_secret_key=getattr(config, 'S3_SECRET_KEY', None)
)
backup_manager = get_backup_manager(backup_config)

# Initialize ML Service
ml_service: MLService = create_ml_service(
    redis_url=config.redis.url if hasattr(config, 'redis') else None,
    models_path="./models"
)

mt5_connection: MT5Connection = get_mt5_connection()
price_streamer: PriceStreamer = get_price_streamer()
data_transformer = DataTransformer()
db_manager: TimescaleDBManager = get_db_manager()
historical_collector: HistoricalDataCollector = get_historical_collector()
data_validator: DataValidator = get_data_validator()
paper_trading_engine: PaperTradingEngine = get_paper_trading_engine()
health_monitor: HealthMonitor = get_health_monitor()
alert_manager = get_alert_notification_manager()
data_integrity_validator: DataIntegrityValidator = get_data_integrity_validator()
performance_profiler: PerformanceProfiler = get_performance_profiler()
latency_analyzer: LatencyAnalyzer = get_latency_analyzer()

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.subscriptions: Dict[str, Set[str]] = {}  # connection_id -> set of symbols
        
    async def connect(self, websocket: WebSocket, connection_id: str):
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        self.subscriptions[connection_id] = set()
        logger.info(f"WebSocket connected: {connection_id}")
        
    def disconnect(self, connection_id: str):
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        if connection_id in self.subscriptions:
            del self.subscriptions[connection_id]
        logger.info(f"WebSocket disconnected: {connection_id}")
        
    async def send_personal_message(self, message: dict, connection_id: str):
        if connection_id in self.active_connections:
            try:
                await self.active_connections[connection_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to send message to {connection_id}: {e}")
                self.disconnect(connection_id)
                
    async def broadcast(self, message: dict, symbol: Optional[str] = None):
        """Broadcast message to all connected clients, optionally filtered by symbol subscription"""
        for connection_id, websocket in list(self.active_connections.items()):
            try:
                # If symbol is specified, only send to clients subscribed to that symbol
                if symbol and symbol not in self.subscriptions.get(connection_id, set()):
                    continue
                    
                await websocket.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to broadcast to {connection_id}: {e}")
                self.disconnect(connection_id)

manager = ConnectionManager()

# Security middleware for API protection
@app.middleware("http")
async def security_middleware_handler(request: Request, call_next):
    """Production security middleware with rate limiting and IP filtering"""
    start_time = time.time()
    
    try:
        # Check IP allowlist/blocklist
        if not await security_middleware.check_ip_allowlist(request):
            client_ip = security_middleware._get_client_ip(request)
            await centralized_logger.log_security_event(
                event_type="ip_blocked",
                severity="warning",
                details={"blocked_ip": client_ip, "reason": "not_in_allowlist"},
                client_ip=client_ip,
                user_agent=request.headers.get('user-agent', 'unknown')
            )
            return JSONResponse(
                status_code=403,
                content={"error": "Access denied", "message": "IP address not allowed"}
            )
        
        # Check rate limits
        rate_limit_ok, rate_limit_info = await security_middleware.check_rate_limit(request)
        if not rate_limit_ok:
            client_ip = security_middleware._get_client_ip(request)
            await centralized_logger.log_security_event(
                event_type="rate_limit_exceeded",
                severity="warning",
                details={
                    "rule": rate_limit_info.get('rule', 'unknown'),
                    "endpoint": request.url.path,
                    "counts": rate_limit_info
                },
                client_ip=client_ip,
                user_agent=request.headers.get('user-agent', 'unknown')
            )
            return JSONResponse(
                status_code=429,
                content={
                    "error": "Rate limit exceeded",
                    "message": f"Too many requests for {rate_limit_info.get('rule', 'unknown')}",
                    "reset_time": rate_limit_info.get('reset_time'),
                    "retry_after": 60
                }
            )
        
        # Detect suspicious activity
        client_id = security_middleware.get_client_identifier(request)
        if await security_middleware.detect_suspicious_activity(request, client_id):
            logger.warning(f"Suspicious activity detected from {client_id}")
            # Don't block automatically, but log for analysis
        
        # Record request for metrics
        metrics_collector.record_request()
        
        # Process request
        response = await call_next(request)
        
        # Add security headers
        security_headers = {}
        await security_middleware.add_security_headers(security_headers, rate_limit_info)
        
        for header_name, header_value in security_headers.items():
            response.headers[header_name] = header_value
        
        # Record processing time
        processing_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        response.headers["X-Process-Time"] = str(processing_time)
        
        # Log API request
        await centralized_logger.log_api_request(
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            duration_ms=processing_time,
            client_ip=security_middleware._get_client_ip(request),
            user_agent=request.headers.get('user-agent', 'unknown'),
            request_size=int(request.headers.get('content-length', 0)),
            response_size=len(response.body) if hasattr(response, 'body') else 0
        )
        
        return response
        
    except Exception as e:
        logger.error(f"Security middleware error: {e}")
        return JSONResponse(
            status_code=500,
            content={"error": "Internal server error", "message": "Security check failed"}
        )

# Pydantic models
class HealthResponse(BaseModel):
    status: str
    timestamp: str
    mt5_connected: bool
    version: str

class MT5StatusResponse(BaseModel):
    connected: bool
    account_info: Optional[Dict[str, Any]]
    terminal_info: Optional[Dict[str, Any]]
    last_error: Optional[str]

class SymbolSubscription(BaseModel):
    symbol: str
    
class MarketDataRequest(BaseModel):
    symbol: str
    timeframe: str = "1m"
    count: int = 100

# API Routes
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy",
        timestamp=datetime.now().isoformat(),
        mt5_connected=mt5_connection.is_connected(),
        version="1.0.0"
    )

@app.get("/mt5/status", response_model=MT5StatusResponse)
async def get_mt5_status():
    """Get MT5 connection status"""
    try:
        status = mt5_connection.get_status()
        return MT5StatusResponse(
            connected=status["connected"],
            account_info=status.get("account_info"),
            terminal_info=status.get("terminal_info"),
            last_error=status.get("last_error")
        )
    except Exception as e:
        logger.error(f"Failed to get MT5 status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/mt5/initialize")
async def initialize_mt5(connection_config: Optional[Dict[str, Any]] = None):
    """Initialize MT5 connection"""
    try:
        # If already connected, return success
        if mt5_connection.is_connected():
            return {"success": True, "message": "MT5 already connected"}
            
        success = mt5_connection.initialize()
        if success:
            # Start price streaming after successful connection
            await price_streamer.start()
            return {"success": True, "message": "MT5 initialized successfully"}
        else:
            status = mt5_connection.get_status()
            return {"success": False, "error": status.get("last_error", "Unknown error")}
            
    except Exception as e:
        logger.error(f"Failed to initialize MT5: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/mt5/symbols")
async def get_symbols():
    """Get available trading symbols"""
    try:
        if not mt5_connection.is_connected():
            raise HTTPException(status_code=503, detail="MT5 not connected")
            
        import MetaTrader5 as mt5
        symbols = mt5.symbols_get()
        if symbols:
            return [symbol.name for symbol in symbols]
        else:
            return []
            
    except Exception as e:
        logger.error(f"Failed to get symbols: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/mt5/market-data")
async def get_market_data(symbol: str, timeframe: str = "1m", count: int = 100):
    """Get historical market data"""
    try:
        if not mt5_connection.is_connected():
            raise HTTPException(status_code=503, detail="MT5 not connected")
            
        import MetaTrader5 as mt5
        
        # Convert timeframe string to MT5 constant
        timeframe_map = {
            "1m": mt5.TIMEFRAME_M1,
            "5m": mt5.TIMEFRAME_M5,
            "15m": mt5.TIMEFRAME_M15,
            "30m": mt5.TIMEFRAME_M30,
            "1h": mt5.TIMEFRAME_H1,
            "4h": mt5.TIMEFRAME_H4,
            "1d": mt5.TIMEFRAME_D1
        }
        
        tf = timeframe_map.get(timeframe, mt5.TIMEFRAME_M1)
        rates = mt5.copy_rates_from_pos(symbol, tf, 0, count)
        
        if rates is not None:
            # Transform to platform format
            market_data = []
            for rate in rates:
                transformed = data_transformer.transform_market_data({
                    'symbol': symbol,
                    'timeframe': timeframe,
                    'timestamp': datetime.fromtimestamp(rate['time']),
                    'open': float(rate['open']),
                    'high': float(rate['high']),
                    'low': float(rate['low']),
                    'close': float(rate['close']),
                    'volume': int(rate['tick_volume']),
                    'source': 'mt5'
                })
                market_data.append(transformed)
            
            return market_data
        else:
            return []
            
    except Exception as e:
        logger.error(f"Failed to get market data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/historical/collect")
async def collect_historical_data(
    instrument: str,
    timeframes: List[str],
    start_date: Optional[str] = None,
    end_date: Optional[str] = None
):
    """Collect historical data for an instrument"""
    try:
        if not mt5_connection.is_connected():
            raise HTTPException(status_code=503, detail="MT5 not connected")
        
        # Parse dates
        start_dt = datetime.fromisoformat(start_date) if start_date else None
        end_dt = datetime.fromisoformat(end_date) if end_date else None
        
        result = await historical_collector.collect_instrument_history(
            instrument, timeframes, start_dt, end_dt
        )
        
        return {"success": True, "results": result}
        
    except Exception as e:
        logger.error(f"Failed to collect historical data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/historical/backfill")
async def backfill_data(
    instrument: str,
    timeframe: str,
    start_date: str,
    end_date: str
):
    """Backfill missing historical data"""
    try:
        if not mt5_connection.is_connected():
            raise HTTPException(status_code=503, detail="MT5 not connected")
        
        start_dt = datetime.fromisoformat(start_date)
        end_dt = datetime.fromisoformat(end_date)
        
        result = await historical_collector.backfill_missing_data(
            instrument, timeframe, start_dt, end_dt
        )
        
        return {"success": True, "result": result}
        
    except Exception as e:
        logger.error(f"Failed to backfill data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/historical/sync")
async def sync_recent_data(
    instruments: List[str],
    timeframes: List[str],
    hours_back: int = 24
):
    """Sync recent data for multiple instruments"""
    try:
        if not mt5_connection.is_connected():
            raise HTTPException(status_code=503, detail="MT5 not connected")
        
        result = await historical_collector.sync_recent_data(
            instruments, timeframes, hours_back
        )
        
        return {"success": True, "results": result}
        
    except Exception as e:
        logger.error(f"Failed to sync recent data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/historical/stats")
async def get_collection_stats():
    """Get historical data collection statistics"""
    try:
        collector_stats = historical_collector.get_stats()
        db_stats = await db_manager.get_database_stats()
        
        return {
            "collector": collector_stats,
            "database": db_stats
        }
        
    except Exception as e:
        logger.error(f"Failed to get collection stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/data/validate")
async def validate_data(
    instrument: str,
    timeframe: str,
    start_date: str,
    end_date: str
):
    """Validate data quality for an instrument"""
    try:
        start_dt = datetime.fromisoformat(start_date)
        end_dt = datetime.fromisoformat(end_date)
        
        validation_results = await data_validator.validate_instrument_data(
            instrument, timeframe, start_dt, end_dt
        )
        
        # Convert results to JSON-serializable format
        results = []
        for result in validation_results:
            results.append({
                "check_type": result.check_type,
                "status": result.status.value,
                "message": result.message,
                "details": result.details,
                "timestamp": result.timestamp.isoformat()
            })
        
        return {"success": True, "validation_results": results}
        
    except Exception as e:
        logger.error(f"Failed to validate data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/data/quality-metrics")
async def get_quality_metrics(
    instrument: str,
    timeframe: str,
    start_date: str,
    end_date: str
):
    """Get data quality metrics for an instrument"""
    try:
        start_dt = datetime.fromisoformat(start_date)
        end_dt = datetime.fromisoformat(end_date)
        
        metrics = await data_validator.calculate_quality_metrics(
            instrument, timeframe, start_dt, end_dt
        )
        
        return {
            "success": True,
            "metrics": {
                "completeness_ratio": metrics.completeness_ratio,
                "accuracy_score": metrics.accuracy_score,
                "consistency_score": metrics.consistency_score,
                "timeliness_score": metrics.timeliness_score,
                "overall_quality": metrics.overall_quality
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get quality metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/data/gaps")
async def get_data_gaps(
    instrument: str,
    timeframe: str,
    start_date: str,
    end_date: str
):
    """Get data gaps for an instrument"""
    try:
        start_dt = datetime.fromisoformat(start_date)
        end_dt = datetime.fromisoformat(end_date)
        
        gaps = await db_manager.get_data_gaps(instrument, timeframe, start_dt, end_dt)
        
        # Convert to JSON-serializable format
        gap_list = []
        for gap_start, gap_end in gaps:
            gap_list.append({
                "start": gap_start.isoformat(),
                "end": gap_end.isoformat(),
                "duration_minutes": (gap_end - gap_start).total_seconds() / 60
            })
        
        return {"success": True, "gaps": gap_list}
        
    except Exception as e:
        logger.error(f"Failed to get data gaps: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Paper Trading API Endpoints
@app.post("/paper-trading/orders")
async def place_paper_order(
    instrument: str,
    order_type: str,
    volume: float,
    price: Optional[float] = None,
    stop_loss: Optional[float] = None,
    take_profit: Optional[float] = None,
    comment: str = ""
):
    """Place a paper trading order"""
    try:
        # Validate order type
        try:
            order_type_enum = OrderType(order_type.upper())
        except ValueError:
            raise HTTPException(status_code=400, detail=f"Invalid order type: {order_type}")
        
        # Create trade request
        trade_request = TradeRequest(
            instrument=instrument,
            order_type=order_type_enum,
            volume=volume,
            price=price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            comment=comment
        )
        
        result = await paper_trading_engine.place_order(trade_request)
        
        if result['success']:
            return result
        else:
            raise HTTPException(status_code=400, detail=result['error'])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to place paper order: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/paper-trading/positions/{position_id}/close")
async def close_paper_position(
    position_id: str,
    volume: Optional[float] = None
):
    """Close a paper trading position"""
    try:
        result = await paper_trading_engine.close_position(position_id, volume)
        
        if result['success']:
            return result
        else:
            raise HTTPException(status_code=400, detail=result['error'])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to close paper position: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/paper-trading/orders/{order_id}")
async def cancel_paper_order(order_id: str):
    """Cancel a pending paper trading order"""
    try:
        result = await paper_trading_engine.cancel_order(order_id)
        
        if result['success']:
            return result
        else:
            raise HTTPException(status_code=400, detail=result['error'])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel paper order: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/paper-trading/account")
async def get_paper_account():
    """Get paper trading account information"""
    try:
        account_info = await paper_trading_engine.get_account_info()
        return {"success": True, "account": account_info}
        
    except Exception as e:
        logger.error(f"Failed to get paper account info: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/paper-trading/positions")
async def get_paper_positions():
    """Get all paper trading positions"""
    try:
        positions = await paper_trading_engine.get_positions()
        return {"success": True, "positions": positions}
        
    except Exception as e:
        logger.error(f"Failed to get paper positions: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/paper-trading/orders")
async def get_paper_orders():
    """Get all paper trading orders"""
    try:
        orders = await paper_trading_engine.get_orders()
        return {"success": True, "orders": orders}
        
    except Exception as e:
        logger.error(f"Failed to get paper orders: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/paper-trading/statistics")
async def get_paper_trading_stats():
    """Get paper trading statistics"""
    try:
        stats = paper_trading_engine.get_statistics()
        return {"success": True, "statistics": stats}
        
    except Exception as e:
        logger.error(f"Failed to get paper trading stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/paper-trading/start")
async def start_paper_trading():
    """Start the paper trading engine"""
    try:
        await paper_trading_engine.start()
        return {"success": True, "message": "Paper trading engine started"}
        
    except Exception as e:
        logger.error(f"Failed to start paper trading engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/paper-trading/stop")
async def stop_paper_trading():
    """Stop the paper trading engine"""
    try:
        await paper_trading_engine.stop()
        return {"success": True, "message": "Paper trading engine stopped"}
        
    except Exception as e:
        logger.error(f"Failed to stop paper trading engine: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Health Monitoring API Endpoints
@app.get("/health/system")
async def get_system_health():
    """Get comprehensive system health status"""
    try:
        health_status = await health_monitor.get_system_health()
        return {"success": True, "health": health_status}
        
    except Exception as e:
        logger.error(f"Failed to get system health: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health/metrics")
async def get_health_metrics():
    """Get connection and performance metrics"""
    try:
        metrics = await health_monitor.get_connection_metrics()
        return {"success": True, "metrics": metrics}
        
    except Exception as e:
        logger.error(f"Failed to get health metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health/alerts")
async def get_health_alerts(active_only: bool = True):
    """Get system alerts"""
    try:
        alerts = await health_monitor.get_alerts(active_only=active_only)
        return {"success": True, "alerts": alerts}
        
    except Exception as e:
        logger.error(f"Failed to get health alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/health/reconnect")
async def trigger_reconnection():
    """Manually trigger MT5 reconnection"""
    try:
        result = await health_monitor.trigger_reconnection()
        
        if result['success']:
            return result
        else:
            raise HTTPException(status_code=500, detail=result['message'])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to trigger reconnection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health/status/{component}")
async def get_component_health(component: str):
    """Get health status for a specific component"""
    try:
        system_health = await health_monitor.get_system_health()
        
        if component in system_health['checks']:
            return {
                "success": True,
                "component": component,
                "status": system_health['checks'][component]
            }
        else:
            raise HTTPException(status_code=404, detail=f"Component '{component}' not found")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get component health: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health/notifications/recent")
async def get_recent_alert_notifications(limit: int = 50, level: Optional[str] = None):
    """Get recent alert notifications"""
    try:
        alerts = await alert_manager.get_recent_alerts(limit=limit, level_filter=level)
        return {"success": True, "alerts": alerts, "count": len(alerts)}
        
    except Exception as e:
        logger.error(f"Failed to get recent alert notifications: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health/notifications/statistics")
async def get_alert_statistics():
    """Get alert notification statistics"""
    try:
        stats = alert_manager.get_alert_statistics()
        return {"success": True, "statistics": stats}
        
    except Exception as e:
        logger.error(f"Failed to get alert statistics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Security Management API Endpoints
@app.get("/security/stats")
async def get_security_statistics():
    """Get security middleware statistics and metrics"""
    try:
        stats = security_middleware.get_security_stats()
        return {"success": True, "security_stats": stats}
    except Exception as e:
        logger.error(f"Failed to get security stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/security/rate-limits")
async def get_rate_limit_rules():
    """Get current rate limiting rules and configuration"""
    try:
        rules = {
            name: {
                "requests_per_minute": rule.requests_per_minute,
                "requests_per_hour": rule.requests_per_hour,
                "burst_limit": rule.burst_limit,
                "enabled": rule.enabled
            }
            for name, rule in security_middleware.rate_limit_rules.items()
        }
        return {"success": True, "rate_limit_rules": rules}
    except Exception as e:
        logger.error(f"Failed to get rate limit rules: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/security/block-ip")
async def block_ip_address(ip_address: str, reason: Optional[str] = None):
    """Block an IP address from accessing the API"""
    try:
        security_middleware.block_ip(ip_address)
        logger.warning(f"IP {ip_address} blocked via API. Reason: {reason or 'Not specified'}")
        return {"success": True, "message": f"IP {ip_address} has been blocked"}
    except Exception as e:
        logger.error(f"Failed to block IP {ip_address}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/security/unblock-ip")
async def unblock_ip_address(ip_address: str):
    """Unblock a previously blocked IP address"""
    try:
        security_middleware.unblock_ip(ip_address)
        logger.info(f"IP {ip_address} unblocked via API")
        return {"success": True, "message": f"IP {ip_address} has been unblocked"}
    except Exception as e:
        logger.error(f"Failed to unblock IP {ip_address}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/security/blocked-ips")
async def get_blocked_ips():
    """Get list of currently blocked IP addresses"""
    try:
        blocked_ips = list(security_middleware.blocked_ips)
        return {"success": True, "blocked_ips": blocked_ips}
    except Exception as e:
        logger.error(f"Failed to get blocked IPs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Logging Management API Endpoints
@app.get("/logging/stats")
async def get_logging_statistics():
    """Get logging system statistics"""
    try:
        import os
        import glob
        from datetime import datetime
        
        # Get log file statistics
        log_files = glob.glob("logs/*.log")
        log_stats = {}
        
        for log_file in log_files:
            stat = os.stat(log_file)
            log_stats[os.path.basename(log_file)] = {
                "size_mb": round(stat.st_size / (1024 * 1024), 2),
                "modified": datetime.fromtimestamp(stat.st_mtime).isoformat(),
                "lines": sum(1 for line in open(log_file, 'r', encoding='utf-8', errors='ignore'))
            }
        
        return {
            "success": True,
            "service_info": {
                "service_name": centralized_logger.service_name,
                "service_version": centralized_logger.service_version,
                "environment": centralized_logger.environment,
                "instance_id": centralized_logger.instance_id,
                "hostname": centralized_logger.hostname
            },
            "log_files": log_stats,
            "elasticsearch_enabled": centralized_logger.elasticsearch_handler is not None
        }
    except Exception as e:
        logger.error(f"Failed to get logging stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/logging/recent")
async def get_recent_logs(limit: int = 100, level: Optional[str] = None):
    """Get recent log entries from files"""
    try:
        import os
        from collections import deque
        
        # Read from main log file
        log_file = f"logs/mt5_bridge_{datetime.now().strftime('%Y-%m-%d')}.log"
        
        if not os.path.exists(log_file):
            return {"success": True, "logs": [], "message": "No logs found for today"}
        
        logs = deque(maxlen=limit)
        
        with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
            lines = f.readlines()
            
            # Filter by level if specified
            for line in lines[-limit:]:
                if level is None or level.upper() in line:
                    logs.append(line.strip())
        
        return {
            "success": True,
            "logs": list(logs),
            "count": len(logs),
            "log_file": log_file
        }
    except Exception as e:
        logger.error(f"Failed to get recent logs: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/logging/test")
async def test_logging_system(level: str = "info", message: str = "Test log message"):
    """Test the centralized logging system"""
    try:
        log_levels = {
            "debug": LogLevel.DEBUG,
            "info": LogLevel.INFO,
            "warning": LogLevel.WARNING,
            "error": LogLevel.ERROR,
            "critical": LogLevel.CRITICAL
        }
        
        log_level = log_levels.get(level.lower(), LogLevel.INFO)
        
        await centralized_logger.log(
            level=log_level,
            message=message,
            component="test",
            extra_data={"test": True, "timestamp": datetime.now().isoformat()}
        )
        
        return {
            "success": True,
            "message": f"Test log sent with level {level.upper()}",
            "log_entry": {
                "level": level.upper(),
                "message": message,
                "component": "test"
            }
        }
    except Exception as e:
        logger.error(f"Failed to test logging: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Production Monitoring API Endpoints
@app.get("/monitoring/production/status")
async def get_production_monitoring_status():
    """Get production monitoring status and current metrics"""
    try:
        current_metrics = production_monitor.get_current_metrics()
        alert_status = production_monitor.get_alert_status()
        
        return {
            "success": True,
            "monitoring_enabled": production_monitor.is_monitoring,
            "current_metrics": current_metrics,
            "alert_status": alert_status
        }
    except Exception as e:
        logger.error(f"Failed to get production monitoring status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/monitoring/production/metrics")
async def get_production_metrics(hours_back: int = 1):
    """Get production resource metrics history"""
    try:
        metrics_history = production_monitor.get_metrics_history(hours_back=hours_back)
        return {
            "success": True,
            "metrics_history": metrics_history,
            "hours_back": hours_back,
            "data_points": len(metrics_history)
        }
    except Exception as e:
        logger.error(f"Failed to get production metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/monitoring/production/alerts")
async def get_production_alerts():
    """Get current production alerts"""
    try:
        alert_status = production_monitor.get_alert_status()
        return {"success": True, **alert_status}
    except Exception as e:
        logger.error(f"Failed to get production alerts: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/monitoring/production/prometheus")
async def get_prometheus_metrics():
    """Get Prometheus metrics in text format"""
    try:
        metrics = production_monitor.get_prometheus_metrics()
        return Response(content=metrics, media_type="text/plain")
    except Exception as e:
        logger.error(f"Failed to get Prometheus metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/monitoring/production/start")
async def start_production_monitoring():
    """Start production monitoring"""
    try:
        await production_monitor.start_monitoring()
        return {"success": True, "message": "Production monitoring started"}
    except Exception as e:
        logger.error(f"Failed to start production monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/monitoring/production/stop")
async def stop_production_monitoring():
    """Stop production monitoring"""
    try:
        await production_monitor.stop_monitoring()
        return {"success": True, "message": "Production monitoring stopped"}
    except Exception as e:
        logger.error(f"Failed to stop production monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Backup Management API Endpoints
@app.get("/backup/status")
async def get_backup_status():
    """Get backup system status"""
    try:
        status = backup_manager.get_backup_status()
        return {"success": True, **status}
    except Exception as e:
        logger.error(f"Failed to get backup status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/backup/restore-points")
async def get_restore_points():
    """Get available database restore points"""
    try:
        restore_points = backup_manager.get_restore_points()
        return {
            "success": True,
            "restore_points": [
                {
                    "timestamp": rp.timestamp.isoformat(),
                    "backup_id": rp.backup_id,
                    "backup_type": rp.backup_type,
                    "database_size_mb": round(rp.database_size / 1024 / 1024, 2),
                    "description": rp.description
                }
                for rp in restore_points
            ]
        }
    except Exception as e:
        logger.error(f"Failed to get restore points: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/backup/create-full")
async def create_full_backup():
    """Create a full database backup"""
    try:
        backup_metadata = await backup_manager.create_full_backup()
        return {
            "success": True,
            "backup_id": backup_metadata.backup_id,
            "backup_type": backup_metadata.backup_type,
            "file_size_mb": round(backup_metadata.file_size / 1024 / 1024, 2),
            "duration_seconds": backup_metadata.duration_seconds,
            "status": backup_metadata.status
        }
    except Exception as e:
        logger.error(f"Failed to create full backup: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/backup/create-incremental")
async def create_incremental_backup():
    """Create an incremental database backup"""
    try:
        backup_metadata = await backup_manager.create_incremental_backup()
        if backup_metadata:
            return {
                "success": True,
                "backup_id": backup_metadata.backup_id,
                "backup_type": backup_metadata.backup_type,
                "file_size_mb": round(backup_metadata.file_size / 1024 / 1024, 2),
                "duration_seconds": backup_metadata.duration_seconds,
                "status": backup_metadata.status
            }
        else:
            return {"success": False, "message": "No incremental backup created"}
    except Exception as e:
        logger.error(f"Failed to create incremental backup: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/backup/restore")
async def restore_database(backup_id: str, target_database: str):
    """Restore database from backup"""
    try:
        success = await backup_manager.restore_database(backup_id, target_database)
        return {
            "success": success,
            "message": f"Database restore {'completed' if success else 'failed'}",
            "backup_id": backup_id,
            "target_database": target_database
        }
    except Exception as e:
        logger.error(f"Failed to restore database: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/backup/start-scheduler")
async def start_backup_scheduler():
    """Start automated backup scheduler"""
    try:
        await backup_manager.start_scheduler()
        return {"success": True, "message": "Backup scheduler started"}
    except Exception as e:
        logger.error(f"Failed to start backup scheduler: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/backup/stop-scheduler")
async def stop_backup_scheduler():
    """Stop automated backup scheduler"""
    try:
        await backup_manager.stop_scheduler()
        return {"success": True, "message": "Backup scheduler stopped"}
    except Exception as e:
        logger.error(f"Failed to stop backup scheduler: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Data Integrity and Quality API Endpoints
@app.post("/data-integrity/validate")
async def validate_data_integrity(
    instrument: str,
    timeframe: str,
    start_date: str,
    end_date: str
):
    """Perform comprehensive data integrity validation"""
    try:
        start_dt = datetime.fromisoformat(start_date)
        end_dt = datetime.fromisoformat(end_date)
        
        quality_report = await data_integrity_validator.perform_comprehensive_validation(
            instrument, timeframe, start_dt, end_dt
        )
        
        # Convert to JSON-serializable format
        report_data = {
            "instrument": quality_report.instrument,
            "timeframe": quality_report.timeframe,
            "period_start": quality_report.period_start.isoformat(),
            "period_end": quality_report.period_end.isoformat(),
            "total_records": quality_report.total_records,
            "valid_records": quality_report.valid_records,
            "integrity_score": quality_report.integrity_score,
            "anomalies_detected": quality_report.anomalies_detected,
            "data_completeness": quality_report.data_completeness,
            "checks_performed": [
                {
                    "check_id": check.check_id,
                    "status": check.status.value,
                    "message": check.message,
                    "details": check.details,
                    "timestamp": check.timestamp.isoformat(),
                    "checksum": check.checksum
                }
                for check in quality_report.checks_performed
            ],
            "anomalies": [
                {
                    "anomaly_id": anomaly.anomaly_id,
                    "anomaly_type": anomaly.anomaly_type.value,
                    "severity": anomaly.severity,
                    "timestamp": anomaly.timestamp.isoformat(),
                    "affected_records": anomaly.affected_records,
                    "description": anomaly.description,
                    "details": anomaly.details,
                    "confidence": anomaly.confidence
                }
                for anomaly in quality_report.anomalies
            ],
            "recommendations": quality_report.recommendations,
            "generated_at": quality_report.generated_at.isoformat()
        }
        
        return {"success": True, "quality_report": report_data}
        
    except Exception as e:
        logger.error(f"Failed to validate data integrity: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/data-integrity/anomalies")
async def get_data_anomalies(
    instrument: Optional[str] = None,
    timeframe: Optional[str] = None,
    anomaly_type: Optional[str] = None,
    min_severity: float = 0.0,
    limit: int = 100
):
    """Get detected data anomalies with filtering"""
    try:
        # Build query conditions
        conditions = []
        params = []
        
        base_query = """
        SELECT anomaly_id, instrument, timeframe, anomaly_type, severity, 
               timestamp, affected_records, description, details, confidence, created_at
        FROM data_anomalies
        WHERE 1=1
        """
        
        if instrument:
            conditions.append("AND instrument = $" + str(len(params) + 1))
            params.append(instrument)
        
        if timeframe:
            conditions.append("AND timeframe = $" + str(len(params) + 1))
            params.append(timeframe)
        
        if anomaly_type:
            conditions.append("AND anomaly_type = $" + str(len(params) + 1))
            params.append(anomaly_type)
        
        if min_severity > 0:
            conditions.append("AND severity >= $" + str(len(params) + 1))
            params.append(min_severity)
        
        query = base_query + " ".join(conditions) + " ORDER BY timestamp DESC LIMIT $" + str(len(params) + 1)
        params.append(limit)
        
        async with db_manager.connection_pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
            
            anomalies = []
            for row in rows:
                anomalies.append({
                    "anomaly_id": row["anomaly_id"],
                    "instrument": row["instrument"],
                    "timeframe": row["timeframe"],
                    "anomaly_type": row["anomaly_type"],
                    "severity": float(row["severity"]),
                    "timestamp": row["timestamp"].isoformat(),
                    "affected_records": row["affected_records"],
                    "description": row["description"],
                    "details": row["details"],
                    "confidence": float(row["confidence"]),
                    "created_at": row["created_at"].isoformat()
                })
        
        return {"success": True, "anomalies": anomalies, "count": len(anomalies)}
        
    except Exception as e:
        logger.error(f"Failed to get data anomalies: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/data-integrity/checks")
async def get_integrity_checks(
    instrument: Optional[str] = None,
    timeframe: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = 100
):
    """Get data integrity check results with filtering"""
    try:
        # Build query conditions
        conditions = []
        params = []
        
        base_query = """
        SELECT check_id, instrument, timeframe, timestamp, status, 
               message, details, checksum, created_at, updated_at
        FROM data_integrity_checks
        WHERE 1=1
        """
        
        if instrument:
            conditions.append("AND instrument = $" + str(len(params) + 1))
            params.append(instrument)
        
        if timeframe:
            conditions.append("AND timeframe = $" + str(len(params) + 1))
            params.append(timeframe)
        
        if status:
            conditions.append("AND status = $" + str(len(params) + 1))
            params.append(status.upper())
        
        query = base_query + " ".join(conditions) + " ORDER BY timestamp DESC LIMIT $" + str(len(params) + 1)
        params.append(limit)
        
        async with db_manager.connection_pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
            
            checks = []
            for row in rows:
                checks.append({
                    "check_id": row["check_id"],
                    "instrument": row["instrument"],
                    "timeframe": row["timeframe"],
                    "timestamp": row["timestamp"].isoformat(),
                    "status": row["status"],
                    "message": row["message"],
                    "details": row["details"],
                    "checksum": row["checksum"],
                    "created_at": row["created_at"].isoformat(),
                    "updated_at": row["updated_at"].isoformat()
                })
        
        return {"success": True, "checks": checks, "count": len(checks)}
        
    except Exception as e:
        logger.error(f"Failed to get integrity checks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/data-integrity/reports")
async def get_quality_reports(
    instrument: Optional[str] = None,
    timeframe: Optional[str] = None,
    min_integrity_score: float = 0.0,
    limit: int = 50
):
    """Get data quality reports with filtering"""
    try:
        # Build query conditions
        conditions = []
        params = []
        
        base_query = """
        SELECT id, instrument, timeframe, period_start, period_end,
               total_records, valid_records, integrity_score, anomalies_detected,
               data_completeness, recommendations, generated_at
        FROM data_quality_reports
        WHERE 1=1
        """
        
        if instrument:
            conditions.append("AND instrument = $" + str(len(params) + 1))
            params.append(instrument)
        
        if timeframe:
            conditions.append("AND timeframe = $" + str(len(params) + 1))
            params.append(timeframe)
        
        if min_integrity_score > 0:
            conditions.append("AND integrity_score >= $" + str(len(params) + 1))
            params.append(min_integrity_score)
        
        query = base_query + " ".join(conditions) + " ORDER BY generated_at DESC LIMIT $" + str(len(params) + 1)
        params.append(limit)
        
        async with db_manager.connection_pool.acquire() as conn:
            rows = await conn.fetch(query, *params)
            
            reports = []
            for row in rows:
                reports.append({
                    "id": row["id"],
                    "instrument": row["instrument"],
                    "timeframe": row["timeframe"],
                    "period_start": row["period_start"].isoformat(),
                    "period_end": row["period_end"].isoformat(),
                    "total_records": row["total_records"],
                    "valid_records": row["valid_records"],
                    "integrity_score": float(row["integrity_score"]),
                    "anomalies_detected": row["anomalies_detected"],
                    "data_completeness": float(row["data_completeness"]),
                    "recommendations": row["recommendations"],
                    "generated_at": row["generated_at"].isoformat()
                })
        
        return {"success": True, "reports": reports, "count": len(reports)}
        
    except Exception as e:
        logger.error(f"Failed to get quality reports: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/data-integrity/checksums")
async def get_data_checksums(
    instrument: str,
    timeframe: str,
    limit: int = 20
):
    """Get data checksums for verification"""
    try:
        query = """
        SELECT id, instrument, timeframe, checksum, record_count, created_at
        FROM data_checksums
        WHERE instrument = $1 AND timeframe = $2
        ORDER BY created_at DESC
        LIMIT $3
        """
        
        async with db_manager.connection_pool.acquire() as conn:
            rows = await conn.fetch(query, instrument, timeframe, limit)
            
            checksums = []
            for row in rows:
                checksums.append({
                    "id": row["id"],
                    "instrument": row["instrument"],
                    "timeframe": row["timeframe"],
                    "checksum": row["checksum"],
                    "record_count": row["record_count"],
                    "created_at": row["created_at"].isoformat()
                })
        
        return {"success": True, "checksums": checksums, "count": len(checksums)}
        
    except Exception as e:
        logger.error(f"Failed to get data checksums: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/data-integrity/store-quality-report")
async def store_quality_report(
    instrument: str,
    timeframe: str,
    period_start: str,
    period_end: str,
    total_records: int,
    valid_records: int,
    integrity_score: float,
    anomalies_detected: int = 0,
    data_completeness: float = 1.0,
    recommendations: Optional[List[str]] = None
):
    """Store a data quality report"""
    try:
        start_dt = datetime.fromisoformat(period_start)
        end_dt = datetime.fromisoformat(period_end)
        
        query = """
        INSERT INTO data_quality_reports 
        (instrument, timeframe, period_start, period_end, total_records, 
         valid_records, integrity_score, anomalies_detected, data_completeness, 
         recommendations, generated_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW())
        RETURNING id
        """
        
        async with db_manager.connection_pool.acquire() as conn:
            row = await conn.fetchrow(
                query,
                instrument,
                timeframe,
                start_dt,
                end_dt,
                total_records,
                valid_records,
                integrity_score,
                anomalies_detected,
                data_completeness,
                recommendations or []
            )
            
            report_id = row["id"]
        
        return {
            "success": True, 
            "message": "Quality report stored successfully",
            "report_id": report_id
        }
        
    except Exception as e:
        logger.error(f"Failed to store quality report: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Performance Monitoring and Optimization API Endpoints
@app.get("/performance/metrics")
async def get_performance_metrics(hours_back: int = 1):
    """Get performance metrics summary"""
    try:
        summary = performance_profiler.get_performance_summary(hours_back=hours_back)
        return {"success": True, "metrics": summary}
        
    except Exception as e:
        logger.error(f"Failed to get performance metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/performance/real-time")
async def get_real_time_performance():
    """Get real-time performance metrics"""
    try:
        metrics = performance_profiler.get_real_time_metrics()
        return {"success": True, "metrics": metrics}
        
    except Exception as e:
        logger.error(f"Failed to get real-time performance: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/performance/start-monitoring")
async def start_performance_monitoring(interval_seconds: float = 1.0):
    """Start performance monitoring"""
    try:
        if not performance_profiler.monitoring_active:
            performance_profiler.start_monitoring(interval_seconds=interval_seconds)
            await latency_analyzer.start_latency_monitoring()
            return {"success": True, "message": "Performance monitoring started"}
        else:
            return {"success": True, "message": "Performance monitoring already active"}
        
    except Exception as e:
        logger.error(f"Failed to start performance monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/performance/stop-monitoring")
async def stop_performance_monitoring():
    """Stop performance monitoring"""
    try:
        if performance_profiler.monitoring_active:
            performance_profiler.stop_monitoring()
            return {"success": True, "message": "Performance monitoring stopped"}
        else:
            return {"success": True, "message": "Performance monitoring not active"}
        
    except Exception as e:
        logger.error(f"Failed to stop performance monitoring: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/performance/run-tests")
async def run_performance_tests():
    """Run comprehensive performance test suite"""
    try:
        test_suite = PerformanceTestSuite()
        results = await test_suite.run_comprehensive_tests()
        return {"success": True, "test_results": results}
        
    except Exception as e:
        logger.error(f"Failed to run performance tests: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/performance/latency/analysis")
async def get_latency_analysis(window_minutes: int = 10):
    """Get latency pattern analysis"""
    try:
        analysis = latency_analyzer.analyze_latency_patterns(window_minutes=window_minutes)
        return {"success": True, "analysis": analysis}
        
    except Exception as e:
        logger.error(f"Failed to get latency analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/performance/latency/recommendations")
async def get_latency_recommendations():
    """Get latency optimization recommendations"""
    try:
        analysis = latency_analyzer.analyze_latency_patterns(window_minutes=60)
        recommendations = latency_analyzer.generate_optimization_recommendations(analysis)
        
        # Convert to JSON-serializable format
        rec_data = [
            {
                "operation": rec.operation,
                "current_latency_ms": rec.current_latency_ms,
                "target_latency_ms": rec.target_latency_ms,
                "optimization_type": rec.optimization_type,
                "description": rec.description,
                "priority": rec.implementation_priority,
                "estimated_improvement_ms": rec.estimated_improvement_ms,
                "implementation_effort": rec.implementation_effort
            }
            for rec in recommendations
        ]
        
        return {"success": True, "recommendations": rec_data}
        
    except Exception as e:
        logger.error(f"Failed to get latency recommendations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/performance/latency/benchmark")
async def benchmark_operation(
    operation: str,
    iterations: int = 100,
    warmup_iterations: int = 10
):
    """Benchmark a specific operation"""
    try:
        # Define benchmark operations
        benchmarks = {
            'data_transformation': lambda: data_transformer.transform_market_data({
                'symbol': 'EURUSD',
                'timeframe': '1m',
                'timestamp': datetime.now(),
                'open': 1.1000,
                'high': 1.1005,
                'low': 1.0995,
                'close': 1.1002,
                'volume': 1000,
                'source': 'mt5'
            }),
            'json_serialization': lambda: json.dumps({
                'data': [{'test': i} for i in range(100)],
                'timestamp': datetime.now().isoformat()
            }, default=str)
        }
        
        if operation not in benchmarks:
            raise HTTPException(status_code=400, detail=f"Unknown operation: {operation}")
        
        results = latency_analyzer.benchmark_operation(
            operation, benchmarks[operation], iterations, warmup_iterations
        )
        
        return {"success": True, "benchmark_results": results}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to benchmark operation: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/performance/export/metrics")
async def export_performance_metrics(format_type: str = 'json'):
    """Export performance metrics"""
    try:
        if format_type != 'json':
            raise HTTPException(status_code=400, detail="Only JSON format supported")
        
        exported_data = performance_profiler.export_metrics(format_type)
        
        return {
            "success": True,
            "format": format_type,
            "data": json.loads(exported_data)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to export performance metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/performance/export/latency-report")
async def export_latency_report(format_type: str = 'json'):
    """Export comprehensive latency analysis report"""
    try:
        if format_type != 'json':
            raise HTTPException(status_code=400, detail="Only JSON format supported")
        
        report_data = latency_analyzer.export_latency_report(format_type)
        
        return {
            "success": True,
            "format": format_type,
            "report": json.loads(report_data)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to export latency report: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/performance/benchmarks")
async def get_performance_benchmarks():
    """Get performance benchmarks and thresholds"""
    try:
        benchmarks = {
            name: {
                "target_ms": benchmark.target_ms,
                "warning_ms": benchmark.warning_ms,
                "critical_ms": benchmark.critical_ms,
                "description": benchmark.description,
                "optimization_hints": benchmark.optimization_hints
            }
            for name, benchmark in latency_analyzer.benchmarks.items()
        }
        
        return {"success": True, "benchmarks": benchmarks}
        
    except Exception as e:
        logger.error(f"Failed to get performance benchmarks: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =============================================================================
# Advanced Monitoring and Metrics API Endpoints
# =============================================================================

@app.get("/monitoring/metrics/current", 
         response_model=ApiResponse,
         tags=["Monitoring"],
         summary="Get current system metrics",
         description="Returns current system and service metrics snapshot")
async def get_current_metrics():
    """Get current system and service metrics"""
    try:
        metrics_collector = get_metrics_collector()
        current_metrics = metrics_collector.get_current_metrics()
        
        return ApiResponse(
            success=True,
            message="Current metrics retrieved successfully",
            data=current_metrics
        )
        
    except Exception as e:
        logger.error(f"Failed to get current metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/monitoring/metrics/history", 
         tags=["Monitoring"],
         summary="Get metrics history",
         description="Returns historical metrics data for analysis")
async def get_metrics_history(hours_back: int = 1):
    """Get metrics history"""
    try:
        metrics_collector = get_metrics_collector()
        history = metrics_collector.get_metrics_history(hours_back=hours_back)
        
        return {
            "success": True,
            "data": history,
            "hours_back": hours_back
        }
        
    except Exception as e:
        logger.error(f"Failed to get metrics history: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/monitoring/alerts/status", 
         tags=["Monitoring"],
         summary="Get alert status",
         description="Returns current alert conditions and active alerts")
async def get_alert_status():
    """Get alert status and conditions"""
    try:
        metrics_collector = get_metrics_collector()
        alert_status = metrics_collector.get_alert_status()
        
        return {
            "success": True,
            "data": alert_status
        }
        
    except Exception as e:
        logger.error(f"Failed to get alert status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/monitoring/alerts/conditions", 
          tags=["Monitoring"],
          summary="Add alert condition",
          description="Add a new alert condition for monitoring")
async def add_alert_condition(
    name: str,
    metric_path: str,
    threshold: float,
    comparison: str,
    duration_seconds: int = 300,
    severity: str = "warning"
):
    """Add a new alert condition"""
    try:
        if comparison not in ['gt', 'lt', 'eq', 'gte', 'lte']:
            raise HTTPException(
                status_code=400, 
                detail="Invalid comparison operator. Use: gt, lt, eq, gte, lte"
            )
        
        if severity not in ['info', 'warning', 'error', 'critical']:
            raise HTTPException(
                status_code=400, 
                detail="Invalid severity. Use: info, warning, error, critical"
            )
        
        condition = AlertCondition(
            name=name,
            metric_path=metric_path,
            threshold=threshold,
            comparison=comparison,
            duration_seconds=duration_seconds,
            severity=severity
        )
        
        metrics_collector = get_metrics_collector()
        metrics_collector.add_alert_condition(condition)
        
        return {
            "success": True,
            "message": f"Alert condition '{name}' added successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to add alert condition: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/monitoring/alerts/conditions/{name}", 
            tags=["Monitoring"],
            summary="Remove alert condition",
            description="Remove an existing alert condition")
async def remove_alert_condition(name: str):
    """Remove an alert condition"""
    try:
        metrics_collector = get_metrics_collector()
        metrics_collector.remove_alert_condition(name)
        
        return {
            "success": True,
            "message": f"Alert condition '{name}' removed successfully"
        }
        
    except Exception as e:
        logger.error(f"Failed to remove alert condition: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/monitoring/start", 
          tags=["Monitoring"],
          summary="Start metrics collection",
          description="Start the metrics collection service")
async def start_metrics_collection():
    """Start metrics collection"""
    try:
        metrics_collector = get_metrics_collector()
        await metrics_collector.start_collection()
        
        return {
            "success": True,
            "message": "Metrics collection started"
        }
        
    except Exception as e:
        logger.error(f"Failed to start metrics collection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/monitoring/stop", 
          tags=["Monitoring"],
          summary="Stop metrics collection",
          description="Stop the metrics collection service")
async def stop_metrics_collection():
    """Stop metrics collection"""
    try:
        metrics_collector = get_metrics_collector()
        await metrics_collector.stop_collection()
        
        return {
            "success": True,
            "message": "Metrics collection stopped"
        }
        
    except Exception as e:
        logger.error(f"Failed to stop metrics collection: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/monitoring/export", 
         tags=["Monitoring"],
         summary="Export metrics data",
         description="Export all collected metrics in JSON format")
async def export_metrics(format_type: str = "json"):
    """Export metrics data"""
    try:
        if format_type.lower() != "json":
            raise HTTPException(
                status_code=400, 
                detail="Only JSON format is currently supported"
            )
        
        metrics_collector = get_metrics_collector()
        exported_data = metrics_collector.export_metrics(format_type)
        
        return {
            "success": True,
            "format": format_type,
            "data": json.loads(exported_data)
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to export metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/monitoring/system/resources", 
         tags=["Monitoring"],
         summary="Get system resources",
         description="Get detailed system resource utilization")
async def get_system_resources():
    """Get system resource utilization"""
    try:
        import psutil
        
        # CPU information
        cpu_info = {
            "percent": psutil.cpu_percent(interval=1),
            "count_logical": psutil.cpu_count(),
            "count_physical": psutil.cpu_count(logical=False),
            "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
            "per_cpu_percent": psutil.cpu_percent(interval=1, percpu=True)
        }
        
        # Memory information
        memory = psutil.virtual_memory()
        memory_info = {
            "total_gb": memory.total / (1024**3),
            "available_gb": memory.available / (1024**3),
            "used_gb": memory.used / (1024**3),
            "percent": memory.percent,
            "free_gb": memory.free / (1024**3)
        }
        
        # Disk information
        disk = psutil.disk_usage('/')
        disk_info = {
            "total_gb": disk.total / (1024**3),
            "used_gb": disk.used / (1024**3),
            "free_gb": disk.free / (1024**3),
            "percent": (disk.used / disk.total) * 100
        }
        
        # Network information
        network = psutil.net_io_counters()
        network_info = {
            "bytes_sent": network.bytes_sent,
            "bytes_recv": network.bytes_recv,
            "packets_sent": network.packets_sent,
            "packets_recv": network.packets_recv,
            "errors_in": network.errin,
            "errors_out": network.errout,
            "drops_in": network.dropin,
            "drops_out": network.dropout
        }
        
        # Process information
        process = psutil.Process()
        process_info = {
            "pid": process.pid,
            "cpu_percent": process.cpu_percent(),
            "memory_percent": process.memory_percent(),
            "memory_info": process.memory_info()._asdict(),
            "num_threads": process.num_threads(),
            "num_fds": process.num_fds() if hasattr(process, 'num_fds') else None,
            "create_time": process.create_time(),
            "status": process.status()
        }
        
        return {
            "success": True,
            "data": {
                "cpu": cpu_info,
                "memory": memory_info,
                "disk": disk_info,
                "network": network_info,
                "process": process_info,
                "timestamp": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get system resources: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# =============================================================================
# ML Infrastructure API Endpoints
# =============================================================================

@app.post("/ml/predict/market-direction",
          tags=["Machine Learning"],
          summary="Predict market direction",
          description="Use ML models to predict market direction for a given symbol")
async def predict_market_direction(
    symbol: str,
    timeframe: str = "1m",
    count: int = 100,
    model_id: Optional[str] = None
):
    """Predict market direction using ML models"""
    try:
        # Get market data
        market_data = await get_market_data(symbol, timeframe, count)

        # Convert to DataFrame
        import pandas as pd
        df = pd.DataFrame(market_data['data']['rates'])

        # Make prediction
        prediction = await ml_service.predict_market_direction(df, model_id)

        return {
            "success": True,
            "data": {
                "symbol": symbol,
                "prediction": prediction,
                "timestamp": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Failed to predict market direction: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ml/detect/market-regime",
          tags=["Machine Learning"],
          summary="Detect market regime",
          description="Detect current market regime (trending, sideways, volatile)")
async def detect_market_regime(
    symbol: str,
    timeframe: str = "1m",
    count: int = 200,
    model_id: Optional[str] = None
):
    """Detect current market regime"""
    try:
        # Get market data
        market_data = await get_market_data(symbol, timeframe, count)

        # Convert to DataFrame
        import pandas as pd
        df = pd.DataFrame(market_data['data']['rates'])

        # Detect regime
        regime = await ml_service.detect_market_regime(df, model_id)

        return {
            "success": True,
            "data": {
                "symbol": symbol,
                "regime": regime,
                "timestamp": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Failed to detect market regime: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/ml/models/status",
         tags=["Machine Learning"],
         summary="Get ML models status",
         description="Get status and performance of all ML models")
async def get_ml_models_status():
    """Get ML models status and performance"""
    try:
        summary = ml_service.get_model_performance_summary()

        return {
            "success": True,
            "data": summary
        }

    except Exception as e:
        logger.error(f"Failed to get ML models status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ml/models/{model_id}/deploy",
          tags=["Machine Learning"],
          summary="Deploy ML model",
          description="Deploy a trained ML model for inference")
async def deploy_ml_model(model_id: str):
    """Deploy an ML model"""
    try:
        success = await ml_service.deploy_model(model_id)

        return {
            "success": success,
            "data": {
                "model_id": model_id,
                "deployed": success,
                "timestamp": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Failed to deploy model {model_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ml/models/{model_id}/retire",
          tags=["Machine Learning"],
          summary="Retire ML model",
          description="Retire an ML model from active use")
async def retire_ml_model(model_id: str):
    """Retire an ML model"""
    try:
        success = await ml_service.retire_model(model_id)

        return {
            "success": success,
            "data": {
                "model_id": model_id,
                "retired": success,
                "timestamp": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Failed to retire model {model_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/ml/train/market-prediction",
          tags=["Machine Learning"],
          summary="Train market prediction model",
          description="Train a new market prediction model with historical data")
async def train_market_prediction_model(
    symbol: str,
    model_name: str,
    start_date: str,
    end_date: str,
    timeframe: str = "1m"
):
    """Train a market prediction model"""
    try:
        # This would typically fetch historical data and train the model
        # For now, return a placeholder response

        return {
            "success": True,
            "data": {
                "message": "Model training initiated",
                "symbol": symbol,
                "model_name": model_name,
                "training_period": f"{start_date} to {end_date}",
                "timeframe": timeframe,
                "status": "training_started",
                "timestamp": datetime.now().isoformat()
            }
        }

    except Exception as e:
        logger.error(f"Failed to start model training: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# WebSocket endpoint
@app.websocket("/ws/{connection_id}")
async def websocket_endpoint(websocket: WebSocket, connection_id: str):
    """WebSocket endpoint for real-time price streaming"""
    await manager.connect(websocket, connection_id)
    
    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            
            await handle_websocket_message(connection_id, message)
            
    except WebSocketDisconnect:
        manager.disconnect(connection_id)
    except Exception as e:
        logger.error(f"WebSocket error for {connection_id}: {e}")
        manager.disconnect(connection_id)

async def handle_websocket_message(connection_id: str, message: dict):
    """Handle incoming WebSocket messages"""
    message_type = message.get('type')
    
    try:
        if message_type == 'ping':
            await manager.send_personal_message({
                'type': 'pong',
                'timestamp': datetime.now().isoformat()
            }, connection_id)
            
        elif message_type == 'subscribe':
            symbol = message.get('symbol')
            if symbol:
                manager.subscriptions[connection_id].add(symbol)
                # Subscribe to price updates for this symbol
                await price_streamer.subscribe_symbol(symbol)
                await manager.send_personal_message({
                    'type': 'subscribed',
                    'symbol': symbol,
                    'timestamp': datetime.now().isoformat()
                }, connection_id)
                
        elif message_type == 'unsubscribe':
            symbol = message.get('symbol')
            if symbol and symbol in manager.subscriptions[connection_id]:
                manager.subscriptions[connection_id].remove(symbol)
                await manager.send_personal_message({
                    'type': 'unsubscribed',
                    'symbol': symbol,
                    'timestamp': datetime.now().isoformat()
                }, connection_id)
                
        elif message_type == 'get_status':
            status = mt5_connection.get_status()
            await manager.send_personal_message({
                'type': 'status_response',
                'data': status,
                'timestamp': datetime.now().isoformat()
            }, connection_id)
            
        elif message_type == 'get_health':
            health_status = await health_monitor.get_system_health()
            await manager.send_personal_message({
                'type': 'health_response',
                'data': health_status,
                'timestamp': datetime.now().isoformat()
            }, connection_id)
            
        elif message_type == 'subscribe_alerts':
            # Mark this connection as interested in health alerts
            if connection_id not in manager.subscriptions:
                manager.subscriptions[connection_id] = set()
            manager.subscriptions[connection_id].add('health_alerts')
            await manager.send_personal_message({
                'type': 'alert_subscription_confirmed',
                'timestamp': datetime.now().isoformat()
            }, connection_id)
            
        elif message_type == 'unsubscribe_alerts':
            if connection_id in manager.subscriptions:
                manager.subscriptions[connection_id].discard('health_alerts')
            await manager.send_personal_message({
                'type': 'alert_unsubscription_confirmed',
                'timestamp': datetime.now().isoformat()
            }, connection_id)
            
        else:
            await manager.send_personal_message({
                'type': 'error',
                'message': f'Unknown message type: {message_type}',
                'timestamp': datetime.now().isoformat()
            }, connection_id)
            
    except Exception as e:
        logger.error(f"Error handling WebSocket message: {e}")
        await manager.send_personal_message({
            'type': 'error',
            'message': str(e),
            'timestamp': datetime.now().isoformat()
        }, connection_id)

# Price update callback for broadcasting
async def price_update_callback(price_data: dict):
    """Callback function for price updates from the price streamer"""
    symbol = price_data.get('symbol')
    await manager.broadcast({
        'type': 'price_update',
        'data': price_data,
        'timestamp': datetime.now().isoformat()
    }, symbol=symbol)

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize services on startup"""
    logger.info("🚀 Starting MT5 Bridge Service...")
    
    # Initialize centralized logging
    await centralized_logger.initialize()
    
    # Validate configuration
    validation = config.validate_config()
    if not validation["valid"]:
        logger.error("Configuration validation failed:")
        for error in validation["errors"]:
            logger.error(f"  - {error}")
        return
    
    if validation["warnings"]:
        logger.warning("Configuration warnings:")
        for warning in validation["warnings"]:
            logger.warning(f"  - {warning}")
    
    # Initialize database
    try:
        if await db_manager.initialize():
            logger.info("✅ Database initialized")
        else:
            logger.error("❌ Database initialization failed")
            return
    except Exception as e:
        logger.error(f"❌ Database initialization error: {e}")
        return
    
    # Set price update callback
    price_streamer.set_callback(price_update_callback)
    
    # Setup alert notifications
    setup_default_notification_channels()
    
    # Start health monitoring
    await health_monitor.start()
    logger.info("✅ Health monitoring started")
    
    # Connect health monitor to alert manager
    health_monitor.add_alert_callback(alert_manager.process_alert)
    
    # Start performance monitoring
    performance_profiler.start_monitoring()
    await latency_analyzer.start_latency_monitoring()
    logger.info("✅ Performance monitoring started")
    
    # Start metrics collection
    metrics_collector = get_metrics_collector()
    await metrics_collector.start_collection()
    logger.info("✅ Metrics collection started")
    
    # Start production monitoring
    await production_monitor.start_monitoring()
    logger.info("✅ Production monitoring started")
    
    # Start backup scheduler
    await backup_manager.start_scheduler()
    logger.info("✅ Backup scheduler started")

    # Start ML service
    try:
        await ml_service.start()
        logger.info("✅ ML service started")
    except Exception as e:
        logger.error(f"❌ Failed to start ML service: {e}")

    # Try to initialize MT5 connection
    try:
        if mt5_connection.initialize():
            logger.info("✅ MT5 connection initialized")
            # Start price streaming
            await price_streamer.start()
            logger.info("✅ Price streaming started")
            
            # Start historical data collector
            await historical_collector.start()
            logger.info("✅ Historical data collector started")
            
            # Start paper trading engine
            await paper_trading_engine.start()
            logger.info("✅ Paper trading engine started")
        else:
            logger.warning("⚠️ MT5 connection failed - running in limited mode")
    except Exception as e:
        logger.error(f"❌ Failed to initialize MT5: {e}")

# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    logger.info("🔌 Shutting down MT5 Bridge Service...")
    
    # Close centralized logging
    await centralized_logger.close()
    
    # Stop performance monitoring
    performance_profiler.stop_monitoring()
    logger.info("⏹️ Performance monitoring stopped")
    
    # Stop metrics collection
    metrics_collector = get_metrics_collector()
    await metrics_collector.stop_collection()
    logger.info("⏹️ Metrics collection stopped")
    
    # Stop production monitoring
    await production_monitor.stop_monitoring()
    logger.info("⏹️ Production monitoring stopped")
    
    # Stop backup scheduler
    await backup_manager.stop_scheduler()
    logger.info("⏹️ Backup scheduler stopped")
    
    # Stop health monitoring
    await health_monitor.stop()
    
    # Stop paper trading engine
    await paper_trading_engine.stop()
    
    # Stop historical data collector
    await historical_collector.stop()
    
    # Stop price streaming
    await price_streamer.stop()
    
    # Close database connections
    await db_manager.close()
    
    # Shutdown MT5 connection
    mt5_connection.shutdown()
    
    logger.info("✅ Shutdown complete")

if __name__ == "__main__":
    # Run the server
    uvicorn.run(
        "main:app",
        host=config.api.host,
        port=config.api.port,
        reload=config.api.debug,
        log_level=config.api.log_level.lower()
    )