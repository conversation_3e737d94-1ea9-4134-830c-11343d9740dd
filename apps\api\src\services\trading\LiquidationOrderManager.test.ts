import { describe, it, expect, beforeEach, vi } from 'vitest';
import { LiquidationOrderManager } from './LiquidationOrderManager';
import { Position, LiquidationStrategy, OrderPriority, MarketCondition, Order, OrderType, OrderStatus } from '../../types/trading';
import Decimal from 'decimal.js';

describe('LiquidationOrderManager', () => {
  let manager: LiquidationOrderManager;
  let mockEmit: ReturnType<typeof vi.fn>;

  const createMockPosition = (overrides: Partial<Position> = {}): Position => ({
    id: 'pos-1',
    accountId: 'acc-1',
    symbol: 'EURUSD',
    side: 'long',
    size: new Decimal('100000'),
    entryPrice: new Decimal('1.0500'),
    currentPrice: new Decimal('1.0520'),
    unrealizedPnl: new Decimal('200'),
    realizedPnl: new Decimal('0'),
    marginUsed: new Decimal('1000'),
    timestamp: new Date(),
    ...overrides
  });

  const createMockMarketCondition = (overrides: Partial<MarketCondition> = {}): MarketCondition => ({
    symbol: 'EURUSD',
    timestamp: new Date(),
    stressLevel: 0.3,
    volatilityChange: 0.1,
    liquidityScore: 0.8,
    volumeChange: 0.2,
    correlationBreakdown: false,
    ...overrides
  });

  beforeEach(() => {
    manager = new LiquidationOrderManager({
      maxOrdersPerSecond: 5,
      minOrderSize: new Decimal('1000'),
      maxOrderSize: new Decimal('1000000'),
      slippageThreshold: 0.01,
      maxMarketImpact: 0.05,
      liquidityBuffer: 0.2,
      orderTimeout: 30000,
      testMode: true // Disable auto-processing for predictable testing
    });

    mockEmit = vi.fn();
    (manager as any).emit = mockEmit;
  });

  describe('Initialization', () => {
    it('should initialize with default configuration', () => {
      const defaultManager = new LiquidationOrderManager();
      expect(defaultManager).toBeDefined();
    });

    it('should initialize with custom configuration', () => {
      const config = {
        maxOrdersPerSecond: 10,
        minOrderSize: new Decimal('5000'),
        maxOrderSize: new Decimal('500000'),
        slippageThreshold: 0.02,
        maxMarketImpact: 0.03,
        liquidityBuffer: 0.15,
        orderTimeout: 60000
      };

      const customManager = new LiquidationOrderManager(config);
      expect(customManager).toBeDefined();
    });

    it('should start and stop order processing', () => {
      manager.start();
      expect((manager as any).isProcessing).toBe(true);

      manager.stop();
      expect((manager as any).isProcessing).toBe(false);
    });
  });

  describe('Order Sequencing', () => {
    it('should add liquidation orders with IMMEDIATE strategy', () => {
      const positions = [
        createMockPosition({ symbol: 'EURUSD', unrealizedPnl: new Decimal('-500') }),
        createMockPosition({ symbol: 'GBPUSD', unrealizedPnl: new Decimal('-300') }),
        createMockPosition({ symbol: 'USDJPY', unrealizedPnl: new Decimal('-200') })
      ];

      // Enable auto-processing for this test
      manager.setAutoProcessing(true);
      const orderIds = manager.addLiquidationOrders(positions, LiquidationStrategy.IMMEDIATE);

      expect(orderIds).toHaveLength(3);
      expect(manager.getActiveOrderCount()).toBe(3);
    });

    it('should prioritize orders by loss amount in IMMEDIATE strategy', () => {
      const positions = [
        createMockPosition({ 
          id: 'pos-1',
          symbol: 'EURUSD', 
          unrealizedPnl: new Decimal('-200') 
        }),
        createMockPosition({ 
          id: 'pos-2',
          symbol: 'GBPUSD', 
          unrealizedPnl: new Decimal('-800') 
        }),
        createMockPosition({ 
          id: 'pos-3',
          symbol: 'USDJPY', 
          unrealizedPnl: new Decimal('-500') 
        })
      ];

      // Disable auto-processing to inspect queue
      manager.setAutoProcessing(false);
      
      manager.addLiquidationOrders(positions, LiquidationStrategy.IMMEDIATE);
      const queue = manager.getOrderQueue();

      expect(queue[0].priority).toBe(OrderPriority.CRITICAL);
      expect(queue[1].priority).toBe(OrderPriority.HIGH);
      expect(queue[2].priority).toBe(OrderPriority.MEDIUM);
      
      // Re-enable auto-processing for subsequent tests
      manager.setAutoProcessing(true);
    });

    it('should sequence orders gradually with GRADUAL strategy', () => {
      const position = createMockPosition({ 
        size: new Decimal('500000'),
        unrealizedPnl: new Decimal('-1000')
      });

      // Enable auto-processing for this test
      manager.setAutoProcessing(true);
      const orderIds = manager.addLiquidationOrders([position], LiquidationStrategy.GRADUAL);

      expect(orderIds.length).toBeGreaterThan(1);
      expect(manager.getActiveOrderCount()).toBeGreaterThan(1);
    });

    it('should optimize for minimum market impact with MARKET_IMPACT_OPTIMIZED strategy', () => {
      const positions = [
        createMockPosition({ 
          symbol: 'EURUSD',
          size: new Decimal('100000'),
          unrealizedPnl: new Decimal('-300')
        })
      ];

      const marketConditions = [
        createMockMarketCondition({ 
          symbol: 'EURUSD',
          liquidityScore: 0.9,
          stressLevel: 0.2
        })
      ];

      const orderIds = manager.addLiquidationOrders(
        positions, 
        LiquidationStrategy.MARKET_IMPACT_OPTIMIZED,
        marketConditions
      );

      expect(orderIds).toHaveLength(1);
      
      const orders = manager.getOrderQueue();
      expect(orders[0].estimatedSlippage).toBeLessThan(0.01);
    });

    it('should handle large positions by splitting them', () => {
      const largePosition = createMockPosition({ 
        size: new Decimal('2000000'),
        unrealizedPnl: new Decimal('-5000')
      });

      const orderIds = manager.addLiquidationOrders([largePosition], LiquidationStrategy.GRADUAL);

      expect(orderIds.length).toBeGreaterThan(1);
      
      const orders = manager.getOrderQueue();
      const totalSize = orders.reduce((sum, order) => sum.plus(order.size), new Decimal('0'));
      expect(totalSize).toEqual(largePosition.size);
    });
  });

  describe('Market Impact Optimization', () => {
    it('should calculate market impact for order', () => {
      const position = createMockPosition({ 
        symbol: 'EURUSD',
        size: new Decimal('100000') 
      });

      const marketCondition = createMockMarketCondition({
        symbol: 'EURUSD',
        liquidityScore: 0.7,
        stressLevel: 0.4
      });

      manager.addLiquidationOrders([position], LiquidationStrategy.MARKET_IMPACT_OPTIMIZED, [marketCondition]);
      
      const orders = manager.getOrderQueue();
      expect(orders[0].estimatedMarketImpact).toBeDefined();
      expect(orders[0].estimatedMarketImpact).toBeGreaterThan(0);
    });

    it('should adjust order size based on liquidity', () => {
      const position = createMockPosition({ size: new Decimal('500000') });
      
      const lowLiquidityCondition = createMockMarketCondition({
        liquidityScore: 0.3,
        stressLevel: 0.7
      });

      const highLiquidityCondition = createMockMarketCondition({
        liquidityScore: 0.9,
        stressLevel: 0.2
      });

      manager.addLiquidationOrders([position], LiquidationStrategy.MARKET_IMPACT_OPTIMIZED, [lowLiquidityCondition]);
      const lowLiquidityOrders = manager.getOrderQueue().length;
      
      manager.reset();
      
      manager.addLiquidationOrders([position], LiquidationStrategy.MARKET_IMPACT_OPTIMIZED, [highLiquidityCondition]);
      const highLiquidityOrders = manager.getOrderQueue().length;

      expect(lowLiquidityOrders).toBeGreaterThanOrEqual(highLiquidityOrders);
    });

    it('should delay orders in high stress conditions', () => {
      const position = createMockPosition({ size: new Decimal('200000') });
      
      const stressedCondition = createMockMarketCondition({
        stressLevel: 0.8,
        liquidityScore: 0.4
      });

      manager.addLiquidationOrders([position], LiquidationStrategy.MARKET_IMPACT_OPTIMIZED, [stressedCondition]);
      
      const orders = manager.getOrderQueue();
      expect(orders[0].delayMs).toBeGreaterThan(0);
    });
  });

  describe('Order Execution Management', () => {
    it('should execute orders in priority sequence', async () => {
      const positions = [
        createMockPosition({ 
          id: 'pos-1',
          unrealizedPnl: new Decimal('-100') 
        }),
        createMockPosition({ 
          id: 'pos-2',
          unrealizedPnl: new Decimal('-800') 
        })
      ];

      // Enable auto-processing for this test
      manager.setAutoProcessing(true);
      manager.addLiquidationOrders(positions, LiquidationStrategy.IMMEDIATE);
      manager.start();

      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockEmit).toHaveBeenCalledWith('orderExecuted', expect.any(Object));
    });

    it('should handle order execution failures', () => {
      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-500') 
      });

      const orderIds = manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      
      manager.markOrderFailed(orderIds[0], 'Market closed');

      expect(manager.getFailedOrderCount()).toBe(1);
      expect(mockEmit).toHaveBeenCalledWith('orderFailed', expect.objectContaining({
        orderId: orderIds[0],
        reason: 'Market closed'
      }));
    });

    it('should retry failed orders with backoff', () => {
      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-500') 
      });

      const orderIds = manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      
      manager.markOrderFailed(orderIds[0], 'Temporary network error');
      manager.retryFailedOrders();

      expect(manager.getActiveOrderCount()).toBeGreaterThan(0);
    });

    it('should respect rate limiting', async () => {
      const positions = Array.from({ length: 10 }, (_, i) => 
        createMockPosition({ 
          id: `pos-${i}`,
          unrealizedPnl: new Decimal('-100') 
        })
      );

      manager.addLiquidationOrders(positions, LiquidationStrategy.IMMEDIATE);
      manager.start();

      await new Promise(resolve => setTimeout(resolve, 1000));

      const executedOrders = mockEmit.mock.calls.filter(call => call[0] === 'orderExecuted').length;
      expect(executedOrders).toBeLessThanOrEqual(5);
    });
  });

  describe('Order Cancellation', () => {
    it('should cancel specific orders', () => {
      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-300') 
      });

      const orderIds = manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      const cancelled = manager.cancelOrder(orderIds[0]);

      expect(cancelled).toBe(true);
      expect(manager.getActiveOrderCount()).toBe(0);
      expect(mockEmit).toHaveBeenCalledWith('orderCancelled', expect.objectContaining({
        orderId: orderIds[0]
      }));
    });

    it('should cancel all orders for a position', () => {
      const position = createMockPosition({ 
        size: new Decimal('500000'),
        unrealizedPnl: new Decimal('-1000') 
      });

      manager.addLiquidationOrders([position], LiquidationStrategy.GRADUAL);
      const initialCount = manager.getActiveOrderCount();

      const cancelled = manager.cancelOrdersForPosition(position.id);

      expect(cancelled).toBe(initialCount);
      expect(manager.getActiveOrderCount()).toBe(0);
    });

    it('should cancel all orders', () => {
      const positions = [
        createMockPosition({ id: 'pos-1', unrealizedPnl: new Decimal('-200') }),
        createMockPosition({ id: 'pos-2', unrealizedPnl: new Decimal('-300') })
      ];

      manager.addLiquidationOrders(positions, LiquidationStrategy.IMMEDIATE);
      const cancelled = manager.cancelAllOrders();

      expect(cancelled).toBe(2);
      expect(manager.getActiveOrderCount()).toBe(0);
    });

    it('should handle cancellation of non-existent orders', () => {
      const cancelled = manager.cancelOrder('non-existent-order');
      expect(cancelled).toBe(false);
    });
  });

  describe('Order Queue Management', () => {
    it('should provide order queue information', () => {
      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-400') 
      });

      manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      
      const queue = manager.getOrderQueue();
      expect(queue).toHaveLength(1);
      expect(queue[0]).toHaveProperty('id');
      expect(queue[0]).toHaveProperty('positionId');
      expect(queue[0]).toHaveProperty('priority');
    });

    it('should maintain queue order by priority and timestamp', () => {
      const positions = [
        createMockPosition({ 
          id: 'pos-1',
          unrealizedPnl: new Decimal('-200') 
        }),
        createMockPosition({ 
          id: 'pos-2',
          unrealizedPnl: new Decimal('-800') 
        }),
        createMockPosition({ 
          id: 'pos-3',
          unrealizedPnl: new Decimal('-500') 
        })
      ];

      manager.addLiquidationOrders(positions, LiquidationStrategy.IMMEDIATE);
      
      const queue = manager.getOrderQueue();
      expect(queue[0].positionId).toBe('pos-2'); // Highest loss
      expect(queue[1].positionId).toBe('pos-3'); // Second highest
      expect(queue[2].positionId).toBe('pos-1'); // Lowest loss
    });

    it('should update order status correctly', () => {
      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-300') 
      });

      const orderIds = manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      
      manager.markOrderExecuted(orderIds[0], {
        executedPrice: new Decimal('1.0515'),
        executedSize: new Decimal('100000'),
        executedAt: new Date()
      });

      const executedOrders = manager.getExecutedOrders();
      expect(executedOrders).toHaveLength(1);
      expect(executedOrders[0].status).toBe(OrderStatus.FILLED);
    });
  });

  describe('Performance Metrics', () => {
    it('should track execution metrics', () => {
      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-500') 
      });

      const orderIds = manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      
      manager.markOrderExecuted(orderIds[0], {
        executedPrice: new Decimal('1.0510'),
        executedSize: new Decimal('100000'),
        executedAt: new Date()
      });

      const metrics = manager.getExecutionMetrics();
      expect(metrics.totalOrders).toBe(1);
      expect(metrics.executedOrders).toBe(1);
      expect(metrics.failedOrders).toBe(0);
      expect(metrics.averageExecutionTime).toBeGreaterThan(0);
    });

    it('should calculate slippage metrics', () => {
      const position = createMockPosition({ 
        currentPrice: new Decimal('1.0520'),
        unrealizedPnl: new Decimal('-300') 
      });

      const orderIds = manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      
      manager.markOrderExecuted(orderIds[0], {
        executedPrice: new Decimal('1.0515'),
        executedSize: new Decimal('100000'),
        executedAt: new Date()
      });

      const metrics = manager.getExecutionMetrics();
      expect(metrics.averageSlippage).toBeDefined();
      expect(metrics.averageSlippage).toBeGreaterThan(0);
    });

    it('should track market impact metrics', () => {
      const positions = [
        createMockPosition({ 
          symbol: 'EURUSD',
          size: new Decimal('500000'),
          unrealizedPnl: new Decimal('-1000') 
        })
      ];

      const marketConditions = [
        createMockMarketCondition({ 
          symbol: 'EURUSD',
          liquidityScore: 0.6 
        })
      ];

      manager.addLiquidationOrders(positions, LiquidationStrategy.MARKET_IMPACT_OPTIMIZED, marketConditions);
      
      const metrics = manager.getExecutionMetrics();
      expect(metrics.estimatedMarketImpact).toBeDefined();
      expect(metrics.estimatedMarketImpact).toBeGreaterThan(0);
    });

    it('should export performance data', () => {
      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-400') 
      });

      const orderIds = manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      
      manager.markOrderExecuted(orderIds[0], {
        executedPrice: new Decimal('1.0518'),
        executedSize: new Decimal('100000'),
        executedAt: new Date()
      });

      const exportData = manager.exportPerformanceData();
      expect(exportData).toContain('orderId');
      expect(exportData).toContain('executionTime');
      expect(exportData).toContain('slippage');
    });
  });

  describe('Risk Controls', () => {
    it('should reject orders that exceed maximum market impact', () => {
      const largePosition = createMockPosition({ 
        size: new Decimal('10000000'),
        unrealizedPnl: new Decimal('-50000') 
      });

      const lowLiquidityCondition = createMockMarketCondition({
        liquidityScore: 0.1,
        stressLevel: 0.9
      });

      expect(() => {
        manager.addLiquidationOrders(
          [largePosition], 
          LiquidationStrategy.IMMEDIATE, 
          [lowLiquidityCondition]
        );
      }).toThrow('Order exceeds maximum allowed market impact');
    });

    it('should enforce minimum and maximum order sizes', () => {
      const tinyPosition = createMockPosition({ 
        size: new Decimal('500'),
        unrealizedPnl: new Decimal('-10') 
      });

      const orderIds = manager.addLiquidationOrders([tinyPosition], LiquidationStrategy.IMMEDIATE);
      expect(orderIds).toHaveLength(0);

      const hugePosition = createMockPosition({ 
        size: new Decimal('50000000'),
        unrealizedPnl: new Decimal('-100000') 
      });

      const hugeOrderIds = manager.addLiquidationOrders([hugePosition], LiquidationStrategy.GRADUAL);
      const orders = manager.getOrderQueue();
      
      orders.forEach(order => {
        expect(order.size.lte(new Decimal('1000000'))).toBe(true);
      });
    });

    it('should implement order timeout', async () => {
      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-300') 
      });

      const shortTimeoutManager = new LiquidationOrderManager({
        orderTimeout: 100
      });

      const orderIds = shortTimeoutManager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(shortTimeoutManager.getActiveOrderCount()).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid positions gracefully', () => {
      const invalidPosition = createMockPosition({
        size: new Decimal('0'),
        unrealizedPnl: new Decimal('0')
      });

      const orderIds = manager.addLiquidationOrders([invalidPosition], LiquidationStrategy.IMMEDIATE);
      expect(orderIds).toHaveLength(0);
    });

    it('should handle missing market conditions', () => {
      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-500') 
      });

      expect(() => {
        manager.addLiquidationOrders([position], LiquidationStrategy.MARKET_IMPACT_OPTIMIZED);
      }).not.toThrow();
    });

    it('should handle concurrent operations safely', async () => {
      const positions = Array.from({ length: 5 }, (_, i) => 
        createMockPosition({ 
          id: `pos-${i}`,
          unrealizedPnl: new Decimal('-200') 
        })
      );

      const promises = positions.map(pos => 
        Promise.resolve(manager.addLiquidationOrders([pos], LiquidationStrategy.IMMEDIATE))
      );

      await Promise.all(promises);
      expect(manager.getActiveOrderCount()).toBe(5);
    });
  });

  describe('Configuration Management', () => {
    it('should update configuration dynamically', () => {
      const newConfig = {
        maxOrdersPerSecond: 10,
        minOrderSize: new Decimal('5000'),
        slippageThreshold: 0.02
      };

      manager.updateConfig(newConfig);

      const smallPosition = createMockPosition({ 
        size: new Decimal('2000'),
        unrealizedPnl: new Decimal('-50') 
      });

      const orderIds = manager.addLiquidationOrders([smallPosition], LiquidationStrategy.IMMEDIATE);
      expect(orderIds).toHaveLength(0);
    });

    it('should validate configuration parameters', () => {
      expect(() => {
        manager.updateConfig({
          maxOrdersPerSecond: -1
        });
      }).toThrow();

      expect(() => {
        manager.updateConfig({
          minOrderSize: new Decimal('-100')
        });
      }).toThrow();
    });

    it('should reset state when configuration changes significantly', () => {
      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-300') 
      });

      manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      expect(manager.getActiveOrderCount()).toBe(1);

      manager.updateConfig({
        minOrderSize: new Decimal('500000')
      });

      expect(manager.getActiveOrderCount()).toBe(0);
    });
  });

  describe('Integration Features', () => {
    it('should integrate with external execution engines', () => {
      const mockExecutionEngine = {
        executeOrder: vi.fn().mockResolvedValue({ success: true })
      };

      manager.setExecutionEngine(mockExecutionEngine);

      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-400') 
      });

      manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      manager.start();

      expect(mockExecutionEngine.executeOrder).toHaveBeenCalled();
    });

    it('should provide webhooks for external monitoring', () => {
      const webhookUrl = 'https://api.example.com/webhooks/liquidation';
      manager.addWebhook(webhookUrl);

      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-500') 
      });

      const orderIds = manager.addLiquidationOrders([position], LiquidationStrategy.IMMEDIATE);
      
      expect(mockEmit).toHaveBeenCalledWith('webhookTriggered', expect.objectContaining({
        url: webhookUrl,
        event: 'orderCreated'
      }));
    });

    it('should support custom liquidation strategies', () => {
      const customStrategy = {
        name: 'CUSTOM',
        priorityFunction: (pos: Position) => pos.unrealizedPnl.abs().toNumber(),
        splitFunction: (pos: Position) => [pos],
        delayFunction: () => 0
      };

      manager.registerStrategy(customStrategy);

      const position = createMockPosition({ 
        unrealizedPnl: new Decimal('-600') 
      });

      const orderIds = manager.addLiquidationOrders([position], 'CUSTOM' as LiquidationStrategy);
      expect(orderIds).toHaveLength(1);
    });
  });
});