import { describe, it, expect, beforeEach, afterEach, beforeAll, afterAll, vi } from 'vitest';
import { PaperTradingEngine } from '../PaperTradingEngine';
import { VirtualPortfolioService } from '../VirtualPortfolioService';
import { ExecutionSimulationService } from '../ExecutionSimulationService';
import { PrismaClient } from '@prisma/client';
import Decimal from 'decimal.js';

// Mock Prisma
vi.mock('@prisma/client');

describe('PaperTradingEngine', () => {
  let paperTradingEngine: PaperTradingEngine;
  let mockMarketDataService: any;
  let mockPortfolioService: VirtualPortfolioService;
  let mockSimulationService: ExecutionSimulationService;
  let mockPrisma: any;

  const mockUserId = 'user-123';
  const mockSessionId = 'session-456';

  beforeEach(() => {
    // Create mock Prisma client
    mockPrisma = {
      paperTradingSession: {
        findUnique: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
      },
      virtualPosition: {
        findUnique: vi.fn(),
        findMany: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
      },
      virtualPortfolio: {
        findUnique: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
      },
      paperTrade: {
        findMany: vi.fn(),
        create: vi.fn(),
        update: vi.fn(),
        delete: vi.fn(),
      },
    };

    // Create mock for MarketDataService
    mockMarketDataService = {
      getCurrentPrice: vi.fn(),
      getMarketData: vi.fn(),
      getSpread: vi.fn(),
      isMarketOpen: vi.fn(),
    } as any;
    
    mockPortfolioService = {
      getVirtualPortfolio: vi.fn(),
      getVirtualBalance: vi.fn(),
      validateTrade: vi.fn(),
      executeTrade: vi.fn(),
      updatePortfolioFromTrade: vi.fn(),
      validateTradeCapital: vi.fn(),
      createVirtualPortfolio: vi.fn(),
      calculatePortfolioMetrics: vi.fn(),
      getPortfolioHistory: vi.fn(),
      getPortfolioSnapshot: vi.fn(),
      getUserPortfolios: vi.fn(),
    } as any;
    
    mockSimulationService = {
      simulateExecution: vi.fn(),
      simulateSlippage: vi.fn(),
      simulateLatency: vi.fn(),
      calculateRealisticSpread: vi.fn(),
    } as any;

    paperTradingEngine = new PaperTradingEngine(
      mockMarketDataService,
      mockPortfolioService
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('executePaperTrade', () => {
    const validTradeRequest = {
      symbol: 'EURUSD',
      side: 'buy' as const,
      quantity: 0.1,
      orderType: 'market' as const,
    };

    beforeEach(() => {
      // Setup default mocks for services that PaperTradingEngine actually uses
      mockMarketDataService.getCurrentPrice.mockResolvedValue({
        bid: new Decimal(1.0843),
        ask: new Decimal(1.0845),
        mid: new Decimal(1.0844),
        spread: new Decimal(0.0002),
        conditions: {
          volatility: 0.012, // 1.2% volatility (as number, not Decimal)
          liquidity: 1000000, // 1M liquidity (as number, not Decimal)
        },
        timestamp: new Date(),
      });

      mockPortfolioService.getVirtualBalance.mockResolvedValue(new Decimal(10000));
      
      mockPortfolioService.validateTrade.mockResolvedValue({
        approved: true,
        reason: 'Trade approved',
      });
      
      mockPortfolioService.executeTrade.mockResolvedValue({
        id: 'trade-123',
        sessionId: mockSessionId,
        userId: mockUserId,
        instrument: 'EURUSD',
        side: 'buy',
        quantity: new Decimal(0.1),
        executionPrice: new Decimal(1.0845),
        totalCost: new Decimal(108.45),
        status: 'executed',
        timestamp: new Date(),
      });

      // The PaperTradingEngine has its own simulateExecution method
      // so we don't need to mock the simulationService here
    });

    it('should execute a valid market order successfully', async () => {
      const result = await paperTradingEngine.executePaperTrade(
        mockUserId,
        mockSessionId,
        validTradeRequest
      );

      expect(result).toHaveProperty('trade');
      expect(result).toHaveProperty('execution');
      expect(result).toHaveProperty('riskChecks');
      expect(result).toHaveProperty('coaching');
      expect(result.trade.instrument).toBe('EURUSD');
      expect(result.trade.side).toBe('buy');
    });

    it('should reject trade if session is not found', async () => {
      // Make portfolioService throw when session not found
      mockPortfolioService.getVirtualBalance.mockRejectedValue(
        new Error('Paper trading session not found')
      );

      await expect(
        paperTradingEngine.executePaperTrade(mockUserId, mockSessionId, validTradeRequest)
      ).rejects.toThrow('Paper trading session not found');
    });

    it('should reject trade if session is not active', async () => {
      // Make portfolioService throw when session not active
      mockPortfolioService.getVirtualBalance.mockRejectedValue(
        new Error('Paper trading session is not active')
      );

      await expect(
        paperTradingEngine.executePaperTrade(mockUserId, mockSessionId, validTradeRequest)
      ).rejects.toThrow('Paper trading session is not active');
    });

    it('should reject trade if insufficient capital', async () => {
      mockPortfolioService.validateTrade.mockResolvedValue({
        approved: false,
        reason: 'Insufficient virtual capital for this trade',
      });

      await expect(
        paperTradingEngine.executePaperTrade(mockUserId, mockSessionId, validTradeRequest)
      ).rejects.toThrow('Insufficient virtual capital for this trade');
    });

    it('should handle limit orders with price validation', async () => {
      const limitOrderRequest = {
        ...validTradeRequest,
        orderType: 'limit' as const,
        price: 1.0840,
      };

      const result = await paperTradingEngine.executePaperTrade(
        mockUserId,
        mockSessionId,
        limitOrderRequest
      );

      expect(result.trade.orderType).toBe('limit');
      expect(mockSimulationService.simulateExecution).toHaveBeenCalledWith(
        expect.objectContaining({
          orderType: 'limit',
          limitPrice: 1.0840,
        })
      );
    });

    it('should apply proper simulation metadata', async () => {
      const simulationResult = {
        executionPrice: new Decimal(1.0845),
        slippage: 0.0002,
        executionDelay: 200,
        spread: 0.0003,
        marketImpact: 0.0001,
        liquidityAvailable: true,
        executionQuality: 0.92,
      };

      mockSimulationService.simulateExecution.mockResolvedValue(simulationResult);

      const result = await paperTradingEngine.executePaperTrade(
        mockUserId,
        mockSessionId,
        validTradeRequest
      );

      expect(result.trade.simulationMetadata).toEqual(
        expect.objectContaining({
          slippage: 0.0002,
          executionDelay: 200,
          spread: 0.0003,
          marketImpact: 0.0001,
        })
      );
    });

    it('should generate learning insights for new traders', async () => {
      mockPrisma.paperTrade.count.mockResolvedValue(2); // Third trade

      const result = await paperTradingEngine.executePaperTrade(
        mockUserId,
        mockSessionId,
        validTradeRequest
      );

      expect(result.learningInsights).toBeDefined();
      expect(Array.isArray(result.learningInsights)).toBe(true);
    });

    it('should update portfolio after successful trade', async () => {
      await paperTradingEngine.executePaperTrade(
        mockUserId,
        mockSessionId,
        validTradeRequest
      );

      expect(mockPortfolioService.updatePortfolioFromTrade).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
        expect.any(Object)
      );
    });
  });

  describe('createSession', () => {
    const validSessionData = {
      name: 'Test Session',
      description: 'Test Description',
      initialBalance: 10000,
    };

    beforeEach(() => {
      mockPrisma.paperTradingSession.create.mockResolvedValue({
        id: mockSessionId,
        userId: mockUserId,
        name: 'Test Session',
        status: 'active',
        initialBalance: new Decimal(10000),
        createdAt: new Date(),
        updatedAt: new Date(),
      } as any);

      mockPortfolioService.createVirtualPortfolio.mockResolvedValue({
        id: 'portfolio-123',
        sessionId: mockSessionId,
        userId: mockUserId,
        balance: new Decimal(10000),
      } as any);
    });

    it('should create a new paper trading session successfully', async () => {
      const result = await paperTradingEngine.createSession(mockUserId, validSessionData);

      expect(result).toHaveProperty('id', mockSessionId);
      expect(result).toHaveProperty('name', 'Test Session');
      expect(mockPrisma.paperTradingSession.create).toHaveBeenCalledTimes(1);
    });

    it('should create associated virtual portfolio', async () => {
      await paperTradingEngine.createSession(mockUserId, validSessionData);

      expect(mockPortfolioService.createVirtualPortfolio).toHaveBeenCalledWith(
        mockUserId,
        mockSessionId,
        expect.objectContaining({
          initialBalance: 10000,
        })
      );
    });

    it('should validate initial balance limits', async () => {
      const invalidSessionData = {
        ...validSessionData,
        initialBalance: 1500000, // Exceeds maximum
      };

      await expect(
        paperTradingEngine.createSession(mockUserId, invalidSessionData)
      ).rejects.toThrow('Initial balance exceeds maximum allowed amount');
    });

    it('should check user session limits', async () => {
      mockPrisma.paperTradingSession.count.mockResolvedValue(5); // Maximum reached

      await expect(
        paperTradingEngine.createSession(mockUserId, validSessionData)
      ).rejects.toThrow('Maximum number of active sessions reached');
    });
  });

  describe('closePosition', () => {
    const mockPositionId = 'position-123';

    beforeEach(() => {
      mockPrisma.virtualPosition.findUnique.mockResolvedValue({
        id: mockPositionId,
        sessionId: mockSessionId,
        userId: mockUserId,
        symbol: 'EURUSD',
        side: 'buy',
        quantity: new Decimal(0.1),
        openPrice: new Decimal(1.0840),
        currentPrice: new Decimal(1.0850),
        status: 'open',
      } as any);

      mockSimulationService.simulateExecution.mockResolvedValue({
        executionPrice: new Decimal(1.0850),
        slippage: 0.0001,
        executionDelay: 120,
        spread: 0.0002,
        marketImpact: 0.00003,
        liquidityAvailable: true,
        executionQuality: 0.96,
      });

      mockPrisma.virtualPosition.update.mockResolvedValue({
        id: mockPositionId,
        status: 'closed',
        closePrice: new Decimal(1.0850),
        realizedPnL: new Decimal(10),
      } as any);
    });

    it('should close position successfully', async () => {
      const result = await paperTradingEngine.closePosition(
        mockUserId,
        mockSessionId,
        mockPositionId
      );

      expect(result).toHaveProperty('positionId', mockPositionId);
      expect(result).toHaveProperty('realizedPnL');
      expect(mockPrisma.virtualPosition.update).toHaveBeenCalledWith({
        where: { id: mockPositionId },
        data: expect.objectContaining({
          status: 'closed',
          closeTime: expect.any(Date),
        }),
      });
    });

    it('should reject closing non-existent position', async () => {
      mockPrisma.virtualPosition.findUnique.mockResolvedValue(null);

      await expect(
        paperTradingEngine.closePosition(mockUserId, mockSessionId, mockPositionId)
      ).rejects.toThrow('Position not found');
    });

    it('should reject closing already closed position', async () => {
      mockPrisma.virtualPosition.findUnique.mockResolvedValue({
        id: mockPositionId,
        status: 'closed',
      } as any);

      await expect(
        paperTradingEngine.closePosition(mockUserId, mockSessionId, mockPositionId)
      ).rejects.toThrow('Position is already closed');
    });
  });

  describe('getRecentTrades', () => {
    beforeEach(() => {
      mockPrisma.paperTrade.findMany.mockResolvedValue([
        {
          id: 'trade-1',
          symbol: 'EURUSD',
          side: 'buy',
          quantity: new Decimal(0.1),
          executionPrice: new Decimal(1.0845),
          executionTime: new Date(),
        },
        {
          id: 'trade-2',
          symbol: 'GBPUSD',
          side: 'sell',
          quantity: new Decimal(0.2),
          executionPrice: new Decimal(1.2650),
          executionTime: new Date(),
        },
      ] as any[]);
    });

    it('should return recent trades with default limit', async () => {
      const trades = await paperTradingEngine.getRecentTrades(mockUserId, mockSessionId);

      expect(trades).toHaveLength(2);
      expect(trades[0]).toHaveProperty('symbol', 'EURUSD');
      expect(mockPrisma.paperTrade.findMany).toHaveBeenCalledWith({
        where: {
          userId: mockUserId,
          sessionId: mockSessionId,
        },
        orderBy: { executionTime: 'desc' },
        take: 10,
        skip: 0,
      });
    });

    it('should respect custom limit and offset', async () => {
      await paperTradingEngine.getRecentTrades(mockUserId, mockSessionId, 5, 2);

      expect(mockPrisma.paperTrade.findMany).toHaveBeenCalledWith({
        where: {
          userId: mockUserId,
          sessionId: mockSessionId,
        },
        orderBy: { executionTime: 'desc' },
        take: 5,
        skip: 2,
      });
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle database connection errors gracefully', async () => {
      mockPrisma.paperTradingSession.findUnique.mockRejectedValue(
        new Error('Database connection failed')
      );

      await expect(
        paperTradingEngine.executePaperTrade(mockUserId, mockSessionId, {
          symbol: 'EURUSD',
          side: 'buy',
          quantity: 0.1,
          orderType: 'market',
        })
      ).rejects.toThrow('Database connection failed');
    });

    it('should handle simulation service errors', async () => {
      mockPrisma.paperTradingSession.findUnique.mockResolvedValue({
        id: mockSessionId,
        userId: mockUserId,
        status: 'active',
      } as any);

      mockSimulationService.simulateExecution.mockRejectedValue(
        new Error('Market data unavailable')
      );

      await expect(
        paperTradingEngine.executePaperTrade(mockUserId, mockSessionId, {
          symbol: 'EURUSD',
          side: 'buy',
          quantity: 0.1,
          orderType: 'market',
        })
      ).rejects.toThrow('Market data unavailable');
    });

    it('should handle concurrent trade execution', async () => {
      mockPrisma.paperTradingSession.findUnique.mockResolvedValue({
        id: mockSessionId,
        userId: mockUserId,
        status: 'active',
        initialBalance: new Decimal(10000),
      } as any);

      mockPortfolioService.getVirtualPortfolio.mockResolvedValue({
        balance: new Decimal(100), // Low balance
        freeMargin: new Decimal(50),
      } as any);

      mockPortfolioService.validateTradeCapital.mockResolvedValueOnce(true)
        .mockResolvedValueOnce(false); // Second call fails

      // First trade should succeed
      const trade1Promise = paperTradingEngine.executePaperTrade(
        mockUserId,
        mockSessionId,
        { symbol: 'EURUSD', side: 'buy', quantity: 0.1, orderType: 'market' }
      );

      // Second trade should fail due to insufficient capital
      const trade2Promise = paperTradingEngine.executePaperTrade(
        mockUserId,
        mockSessionId,
        { symbol: 'GBPUSD', side: 'buy', quantity: 0.1, orderType: 'market' }
      );

      const [result1] = await Promise.allSettled([trade1Promise, trade2Promise]);
      
      expect(result1.status).toBe('fulfilled');
      // Note: In a real implementation, we'd need proper transaction handling
      // to prevent race conditions in concurrent trades
    });
  });

  describe('performance and scalability', () => {
    it('should handle large number of trades efficiently', async () => {
      const startTime = Date.now();
      
      // Mock quick responses
      mockPrisma.paperTradingSession.findUnique.mockResolvedValue({
        id: mockSessionId,
        userId: mockUserId,
        status: 'active',
        initialBalance: new Decimal(100000),
      } as any);

      mockPortfolioService.validateTradeCapital.mockResolvedValue(true);
      mockSimulationService.simulateExecution.mockResolvedValue({
        executionPrice: new Decimal(1.0845),
        slippage: 0.0001,
        executionDelay: 100,
      } as any);

      // Execute 10 trades concurrently
      const tradePromises = Array.from({ length: 10 }, (_, i) =>
        paperTradingEngine.executePaperTrade(mockUserId, mockSessionId, {
          symbol: 'EURUSD',
          side: 'buy',
          quantity: 0.01,
          orderType: 'market',
        })
      );

      await Promise.all(tradePromises);
      
      const endTime = Date.now();
      const executionTime = endTime - startTime;
      
      // Should complete within reasonable time (adjust threshold as needed)
      expect(executionTime).toBeLessThan(5000); // 5 seconds
    });

    it('should handle memory efficiently with large trade history', async () => {
      const largeTrades = Array.from({ length: 1000 }, (_, i) => ({
        id: `trade-${i}`,
        symbol: 'EURUSD',
        side: i % 2 === 0 ? 'buy' : 'sell',
        quantity: new Decimal(0.1),
        executionPrice: new Decimal(1.0845 + (Math.random() - 0.5) * 0.01),
        executionTime: new Date(Date.now() - i * 60000),
      }));

      mockPrisma.paperTrade.findMany.mockResolvedValue(largeTrades as any[]);

      const trades = await paperTradingEngine.getRecentTrades(
        mockUserId, 
        mockSessionId, 
        1000
      );

      expect(trades).toHaveLength(1000);
      // Verify memory usage is reasonable (implementation dependent)
    });
  });
});