-- Row Level Security (RLS) Policies for GoldDaddy Trading Platform
-- These policies ensure users can only access their own data

-- Enable RLS on all user-related tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_goals ENABLE ROW LEVEL SECURITY;
<PERSON>TER TABLE trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE confidence_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Users table policies
-- Users can only see and modify their own profile
CREATE POLICY "user_isolation" ON users
  FOR ALL TO authenticated
  USING (auth.uid()::text = id);

-- Trading goals policies
-- Users can only access their own trading goals
CREATE POLICY "trading_goals_isolation" ON trading_goals
  FOR ALL TO authenticated
  USING (user_id IN (SELECT id FROM users WHERE auth.uid()::text = id));

-- Trades policies
-- Users can only access their own trades
CREATE POLICY "trades_isolation" ON trades
  FOR ALL TO authenticated
  USING (user_id IN (SELECT id FROM users WHERE auth.uid()::text = id));

-- Confidence assessments policies
-- Users can only access their own assessments
CREATE POLICY "confidence_assessments_isolation" ON confidence_assessments
  FOR ALL TO authenticated
  USING (user_id IN (SELECT id FROM users WHERE auth.uid()::text = id));

-- Feature flags policies
-- Users can only access their own feature flags
CREATE POLICY "feature_flags_isolation" ON feature_flags
  FOR ALL TO authenticated
  USING (user_id IN (SELECT id FROM users WHERE auth.uid()::text = id));

-- Audit logs policies
-- Users can only view their own audit logs, no modifications allowed
CREATE POLICY "audit_logs_read_isolation" ON audit_logs
  FOR SELECT TO authenticated
  USING (user_id IN (SELECT id FROM users WHERE auth.uid()::text = id));

-- Service role policies for audit logs (for system operations)
CREATE POLICY "audit_logs_service_full_access" ON audit_logs
  FOR ALL TO service_role
  USING (true);

-- Market data and strategies are public (read-only for authenticated users)
-- Plain English metrics are public (read-only for authenticated users)

-- Public read access for strategies
CREATE POLICY "strategies_read_access" ON strategies
  FOR SELECT TO authenticated
  USING (true);

-- Admin policies for system management (if needed)
-- These would be for administrative users with special roles

-- Grant necessary permissions to authenticated users
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON users TO authenticated;
GRANT ALL ON trading_goals TO authenticated;
GRANT ALL ON trades TO authenticated;
GRANT ALL ON confidence_assessments TO authenticated;
GRANT ALL ON feature_flags TO authenticated;
GRANT SELECT ON audit_logs TO authenticated;
GRANT SELECT ON strategies TO authenticated;
GRANT SELECT ON market_data TO authenticated;
GRANT SELECT ON plain_english_metrics TO authenticated;

-- Grant full access to service role for administrative operations
GRANT ALL ON ALL TABLES IN SCHEMA public TO service_role;