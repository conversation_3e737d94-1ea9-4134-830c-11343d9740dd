import { Request, Response } from 'express';
import { MetricsCacheManager } from '../../services/cache/MetricsCacheManager';
import { CacheInvalidationService } from '../../services/cache/CacheInvalidationService';

export class CacheAnalyticsController {
  private cacheManager: MetricsCacheManager;
  private invalidationService: CacheInvalidationService;
  private invalidationHistory: Array<{
    timestamp: Date;
    reason: string;
    affected: number;
    strategy?: string;
    type?: string;
  }> = [];

  constructor(cacheManager: MetricsCacheManager, invalidationService: CacheInvalidationService) {
    this.cacheManager = cacheManager;
    this.invalidationService = invalidationService;
    this.trackInvalidations();
  }

  /**
   * Get comprehensive cache analytics
   */
  public async getAnalytics(req: Request, res: Response): Promise<void> {
    try {
      // Get memory cache stats
      const memoryStats = this.cacheManager.getStats();
      
      // Get persistence stats
      const persistenceAdapter = this.cacheManager.getPersistenceAdapter();
      const persistenceStats = persistenceAdapter 
        ? await persistenceAdapter.getStats()
        : {
            totalEntries: 0,
            totalSizeBytes: 0,
            oldestEntry: new Date(),
            newestEntry: new Date(),
            averageAccessCount: 0,
            hitRate: 0,
          };

      // Get hydration stats
      const hydrationService = this.cacheManager.getHydrationService();
      const hydrationStats = hydrationService 
        ? await hydrationService.getStats()
        : {
            lastHydration: new Date(0),
            totalHydrations: 0,
            averageHydrationTime: 0,
            averageEntriesHydrated: 0,
            lastResult: null,
          };

      // Get invalidation analytics
      const invalidationAnalytics = this.getInvalidationAnalytics();

      const analytics = {
        memoryCache: memoryStats,
        persistence: persistenceStats,
        hydration: hydrationStats,
        invalidations: invalidationAnalytics,
        timestamp: new Date(),
      };

      res.json(analytics);
    } catch (error) {
      console.error('Failed to get cache analytics:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve cache analytics',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get cache health status
   */
  public async getHealthStatus(req: Request, res: Response): Promise<void> {
    try {
      const stats = this.cacheManager.getStats();
      const persistenceAdapter = this.cacheManager.getPersistenceAdapter();
      
      const healthStatus = {
        status: this.calculateOverallHealth(stats),
        memoryCache: {
          status: this.getMemoryCacheHealth(stats),
          hitRate: stats.hitRate,
          memoryUtilization: stats.memoryUsage.utilization,
          averageAccessTime: stats.averageAccessTime,
        },
        persistence: {
          status: persistenceAdapter ? 'healthy' : 'unavailable',
          connected: !!persistenceAdapter,
        },
        recommendations: this.generateRecommendations(stats),
        timestamp: new Date(),
      };

      res.json(healthStatus);
    } catch (error) {
      console.error('Failed to get cache health status:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve cache health status',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Trigger manual cache operations
   */
  public async triggerOperation(req: Request, res: Response): Promise<void> {
    const { operation, parameters = {} } = req.body;

    try {
      let result: unknown;

      switch (operation) {
        case 'hydrate': {
          const hydrationService = this.cacheManager.getHydrationService();
          if (!hydrationService) {
            throw new Error('Hydration service not available');
          }
          result = await hydrationService.hydrateCache(parameters);
          break;
        }

        case 'cleanup': {
          const persistenceAdapter = this.cacheManager.getPersistenceAdapter();
          if (persistenceAdapter) {
            result = { cleaned: await persistenceAdapter.cleanup() };
          } else {
            result = { cleaned: 0, message: 'No persistence adapter available' };
          }
          break;
        }

        case 'clear_memory':
          await this.cacheManager.clear(parameters.filter);
          result = { message: 'Memory cache cleared', timestamp: new Date() };
          break;

        case 'invalidate':
          await this.cacheManager.invalidate(parameters.target, parameters.strategy);
          result = { 
            message: 'Cache invalidated', 
            target: parameters.target,
            strategy: parameters.strategy,
            timestamp: new Date() 
          };
          break;

        case 'preload_strategy':
          if (!parameters.strategyId) {
            throw new Error('Strategy ID is required for preload operation');
          }
          await this.cacheManager.preloadStrategy(parameters.strategyId);
          result = { 
            message: 'Strategy preloaded', 
            strategyId: parameters.strategyId,
            timestamp: new Date() 
          };
          break;

        case 'validate_persistence': {
          const hydrationSvc = this.cacheManager.getHydrationService();
          if (!hydrationSvc) {
            throw new Error('Hydration service not available');
          }
          result = await hydrationSvc.validatePersistedCache();
          break;
        }

        default:
          throw new Error(`Unknown operation: ${operation}`);
      }

      res.json({
        success: true,
        operation,
        result,
        timestamp: new Date(),
      });

    } catch (error) {
      console.error(`Failed to execute cache operation ${operation}:`, error);
      res.status(400).json({
        success: false,
        operation,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
      });
    }
  }

  /**
   * Get cache configuration
   */
  public async getConfiguration(req: Request, res: Response): Promise<void> {
    try {
      const config = {
        // Note: In a real implementation, you'd expose config from cache manager
        memoryCache: {
          maxSize: '100MB', // Would get from actual config
          maxEntries: 10000,
          defaultTTL: '5 minutes',
          cleanupInterval: '1 minute',
        },
        persistence: {
          enabled: !!this.cacheManager.getPersistenceAdapter(),
          type: this.cacheManager.getPersistenceAdapter() ? 'redis' : 'none',
        },
        analytics: {
          enabled: true,
          retentionPeriod: '24 hours',
        },
        features: {
          autoHydration: true,
          intelligentEviction: true,
          dependencyTracking: true,
          realTimeInvalidation: true,
        },
      };

      res.json(config);
    } catch (error) {
      console.error('Failed to get cache configuration:', error);
      res.status(500).json({ 
        error: 'Failed to retrieve cache configuration',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Private helper methods
   */

  private trackInvalidations(): void {
    // In a real implementation, you'd hook into the invalidation service
    // to track invalidation events. For now, we'll simulate some data.
    
    // Add some sample invalidation history
    const sampleEvents = [
      { timestamp: new Date(Date.now() - 300000), reason: 'Trade completed', affected: 15, strategy: 'momentum-scalper' },
      { timestamp: new Date(Date.now() - 600000), reason: 'Strategy updated', affected: 8, strategy: 'mean-reversion' },
      { timestamp: new Date(Date.now() - 900000), reason: 'Market data refresh', affected: 32, type: 'market_data' },
      { timestamp: new Date(Date.now() - 1200000), reason: 'Scheduled cleanup', affected: 5 },
      { timestamp: new Date(Date.now() - 1800000), reason: 'Manual invalidation', affected: 12, strategy: 'trend-following' },
    ];

    this.invalidationHistory = sampleEvents;
  }

  private getInvalidationAnalytics() {
    const total = this.invalidationHistory.reduce((sum, event) => sum + event.affected, 0);
    
    const byStrategy = this.invalidationHistory
      .filter(event => event.strategy)
      .reduce((acc, event) => {
        const strategy = event.strategy;
        if (strategy) {
          acc[strategy] = (acc[strategy] || 0) + event.affected;
        }
        return acc;
      }, {} as Record<string, number>);

    const byType = this.invalidationHistory
      .reduce((acc, event) => {
        const type = event.type || 'general';
        acc[type] = (acc[type] || 0) + event.affected;
        return acc;
      }, {} as Record<string, number>);

    return {
      total,
      byStrategy: Object.entries(byStrategy).map(([strategy, count]) => ({ strategy, count })),
      byType: Object.entries(byType).map(([type, count]) => ({ type, count })),
      recent: this.invalidationHistory
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, 20),
    };
  }

  private calculateOverallHealth(stats: { hitRate: number; memoryUsage: { utilization: number }; averageAccessTime: number }): 'healthy' | 'degraded' | 'critical' {
    const { hitRate, memoryUsage, averageAccessTime } = stats;
    
    if (hitRate < 50 || memoryUsage.utilization > 95 || averageAccessTime > 50) {
      return 'critical';
    }
    
    if (hitRate < 75 || memoryUsage.utilization > 85 || averageAccessTime > 20) {
      return 'degraded';
    }
    
    return 'healthy';
  }

  private getMemoryCacheHealth(stats: { hitRate: number; memoryUsage: { utilization: number }; averageAccessTime: number }): 'healthy' | 'degraded' | 'critical' {
    const { hitRate, memoryUsage, averageAccessTime } = stats;
    
    if (hitRate >= 80 && memoryUsage.utilization <= 75 && averageAccessTime <= 10) {
      return 'healthy';
    }
    
    if (hitRate >= 60 && memoryUsage.utilization <= 90 && averageAccessTime <= 25) {
      return 'degraded';
    }
    
    return 'critical';
  }

  private generateRecommendations(stats: { hitRate: number; memoryUsage: { utilization: number }; averageAccessTime: number }): string[] {
    const recommendations: string[] = [];
    
    if (stats.hitRate < 75) {
      recommendations.push('Consider increasing cache TTL for stable metrics to improve hit rate');
    }
    
    if (stats.memoryUsage.utilization > 85) {
      recommendations.push('Memory utilization is high - consider increasing max cache size or implementing more aggressive eviction');
    }
    
    if (stats.averageAccessTime > 20) {
      recommendations.push('Average access time is elevated - check for complex computation costs or memory pressure');
    }
    
    if (stats.evictions > stats.hits * 0.1) {
      recommendations.push('High eviction rate detected - consider optimizing cache priorities or increasing capacity');
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Cache is performing optimally - no immediate actions required');
    }
    
    return recommendations;
  }
}