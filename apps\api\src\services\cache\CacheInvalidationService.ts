/**
 * Cache Invalidation Service
 * 
 * Provides intelligent cache invalidation strategies based on data changes,
 * trading events, and user activities with minimal performance impact.
 */

import { MetricsCacheManager } from './MetricsCacheManager';
// import { MetricType } from '@golddaddy/types';

interface InvalidationRule {
  id: string;
  name: string;
  triggers: InvalidationTrigger[];
  targets: InvalidationTarget[];
  strategy: 'immediate' | 'lazy' | 'scheduled' | 'batch';
  priority: 'critical' | 'high' | 'medium' | 'low';
  conditions?: InvalidationCondition[];
  cooldown?: number; // Minimum time between invalidations in ms
}

interface InvalidationTrigger {
  type: 'trade_completed' | 'strategy_updated' | 'market_data_change' | 'user_preference_change' | 'time_based' | 'manual';
  entityId?: string; // Strategy ID, user ID, etc.
  filters?: Record<string, any>;
}

interface InvalidationTarget {
  type: 'key' | 'pattern' | 'tag' | 'dependency';
  value: string | RegExp;
  scope?: 'strategy' | 'user' | 'global';
}

interface InvalidationCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains' | 'regex';
  value: any;
}

interface InvalidationEvent {
  id: string;
  timestamp: number;
  trigger: InvalidationTrigger;
  strategyId?: string;
  userId?: string;
  metadata?: Record<string, any>;
}

interface InvalidationJob {
  id: string;
  ruleId: string;
  event: InvalidationEvent;
  targets: InvalidationTarget[];
  scheduledAt: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  retryCount: number;
}

export class CacheInvalidationService {
  private cacheManager: MetricsCacheManager;
  private rules: Map<string, InvalidationRule> = new Map();
  private eventQueue: InvalidationEvent[] = [];
  private jobQueue: InvalidationJob[] = [];
  private lastInvalidation: Map<string, number> = new Map(); // Rule cooldowns
  private processingTimer?: NodeJS.Timeout;
  private batchTimer?: NodeJS.Timeout;
  private batchedJobs: InvalidationJob[] = [];

  constructor(cacheManager: MetricsCacheManager) {
    this.cacheManager = cacheManager;
    this.initializeDefaultRules();
    this.startProcessing();
  }

  /**
   * Register invalidation rule
   */
  public registerRule(rule: InvalidationRule): void {
    this.rules.set(rule.id, rule);
  }

  /**
   * Remove invalidation rule
   */
  public removeRule(ruleId: string): void {
    this.rules.delete(ruleId);
  }

  /**
   * Trigger invalidation event
   */
  public async triggerInvalidation(event: InvalidationEvent): Promise<void> {
    this.eventQueue.push(event);
    
    // Process immediately for critical events
    if (this.shouldProcessImmediately(event)) {
      await this.processEvent(event);
    }
  }

  /**
   * Invalidate on trade completion
   */
  public async onTradeCompleted(strategyId: string, tradeData: any): Promise<void> {
    await this.triggerInvalidation({
      id: `trade-${Date.now()}`,
      timestamp: Date.now(),
      trigger: {
        type: 'trade_completed',
        entityId: strategyId,
        filters: { tradeData },
      },
      strategyId,
      metadata: { tradeData },
    });
  }

  /**
   * Invalidate on strategy update
   */
  public async onStrategyUpdated(strategyId: string, changes: Record<string, any>): Promise<void> {
    await this.triggerInvalidation({
      id: `strategy-update-${Date.now()}`,
      timestamp: Date.now(),
      trigger: {
        type: 'strategy_updated',
        entityId: strategyId,
        filters: { changes },
      },
      strategyId,
      metadata: { changes },
    });
  }

  /**
   * Invalidate on market data change
   */
  public async onMarketDataChange(symbol: string, changeType: string): Promise<void> {
    await this.triggerInvalidation({
      id: `market-${symbol}-${Date.now()}`,
      timestamp: Date.now(),
      trigger: {
        type: 'market_data_change',
        entityId: symbol,
        filters: { changeType },
      },
      metadata: { symbol, changeType },
    });
  }

  /**
   * Invalidate on user preference change
   */
  public async onUserPreferenceChange(userId: string, preferenceType: string): Promise<void> {
    await this.triggerInvalidation({
      id: `user-pref-${Date.now()}`,
      timestamp: Date.now(),
      trigger: {
        type: 'user_preference_change',
        entityId: userId,
        filters: { preferenceType },
      },
      userId,
      metadata: { preferenceType },
    });
  }

  /**
   * Schedule time-based invalidation
   */
  public scheduleTimeBasedInvalidation(
    targets: InvalidationTarget[],
    intervalMs: number,
    name: string
  ): void {
    const ruleId = `time-based-${name}`;
    
    this.registerRule({
      id: ruleId,
      name: `Time-based invalidation: ${name}`,
      triggers: [{ type: 'time_based' }],
      targets,
      strategy: 'scheduled',
      priority: 'medium',
    });

    // Set up recurring invalidation
    setInterval(() => {
      this.triggerInvalidation({
        id: `${ruleId}-${Date.now()}`,
        timestamp: Date.now(),
        trigger: { type: 'time_based' },
      });
    }, intervalMs);
  }

  /**
   * Get invalidation statistics
   */
  public getStats(): {
    rulesCount: number;
    pendingJobs: number;
    completedJobs: number;
    failedJobs: number;
    avgProcessingTime: number;
    invalidationsPerMinute: number;
  } {
    const completed = this.jobQueue.filter(job => job.status === 'completed').length;
    const failed = this.jobQueue.filter(job => job.status === 'failed').length;
    const pending = this.jobQueue.filter(job => job.status === 'pending').length;

    return {
      rulesCount: this.rules.size,
      pendingJobs: pending,
      completedJobs: completed,
      failedJobs: failed,
      avgProcessingTime: 0, // Would calculate from job execution times
      invalidationsPerMinute: 0, // Would calculate from recent activity
    };
  }

  /**
   * Private methods
   */

  private initializeDefaultRules(): void {
    // Trade completion invalidates performance metrics
    this.registerRule({
      id: 'trade-completion-performance',
      name: 'Invalidate performance metrics on trade completion',
      triggers: [{ type: 'trade_completed' }],
      targets: [
        { type: 'pattern', value: /^performance:.*/ },
        { type: 'pattern', value: /^health:.*/ },
        { type: 'tag', value: 'metric' },
      ],
      strategy: 'immediate',
      priority: 'high',
    });

    // Strategy updates invalidate related caches
    this.registerRule({
      id: 'strategy-update-invalidation',
      name: 'Invalidate strategy-related caches on update',
      triggers: [{ type: 'strategy_updated' }],
      targets: [
        { type: 'dependency', value: 'strategy:' },
        { type: 'tag', value: 'strategy' },
      ],
      strategy: 'immediate',
      priority: 'critical',
    });

    // User preference changes invalidate personalized metrics
    this.registerRule({
      id: 'user-preference-metrics',
      name: 'Invalidate personalized metrics on preference change',
      triggers: [{ type: 'user_preference_change' }],
      targets: [
        { type: 'pattern', value: /^metric:.*:(beginner|intermediate|advanced)$/ },
      ],
      strategy: 'lazy',
      priority: 'medium',
      conditions: [
        { field: 'preferenceType', operator: 'equals', value: 'experience_level' },
      ],
    });

    // Market data changes with batching for high-frequency updates
    this.registerRule({
      id: 'market-data-batch',
      name: 'Batch invalidate market-sensitive metrics',
      triggers: [{ type: 'market_data_change' }],
      targets: [
        { type: 'tag', value: 'market_sensitive' },
        { type: 'pattern', value: /^metric:.*:(sharpe_ratio|volatility)/ },
      ],
      strategy: 'batch',
      priority: 'medium',
      cooldown: 5000, // 5 second cooldown
    });

    // Time-based cleanup of old caches
    this.scheduleTimeBasedInvalidation(
      [
        { type: 'pattern', value: /^temp:.*/ },
        { type: 'tag', value: 'temporary' },
      ],
      15 * 60 * 1000, // Every 15 minutes
      'temporary-cleanup'
    );
  }

  private shouldProcessImmediately(event: InvalidationEvent): boolean {
    const criticalTriggers = ['strategy_updated', 'trade_completed'];
    return criticalTriggers.includes(event.trigger.type);
  }

  private async processEvent(event: InvalidationEvent): Promise<void> {
    const matchingRules = this.findMatchingRules(event);
    
    for (const rule of matchingRules) {
      // Check cooldown
      if (rule.cooldown && this.isInCooldown(rule.id)) {
        continue;
      }

      // Check conditions
      if (rule.conditions && !this.evaluateConditions(rule.conditions, event)) {
        continue;
      }

      const job: InvalidationJob = {
        id: `job-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        ruleId: rule.id,
        event,
        targets: rule.targets,
        scheduledAt: Date.now(),
        status: 'pending',
        retryCount: 0,
      };

      await this.executeJob(job, rule.strategy);
      this.lastInvalidation.set(rule.id, Date.now());
    }
  }

  private findMatchingRules(event: InvalidationEvent): InvalidationRule[] {
    return Array.from(this.rules.values()).filter(rule => {
      return rule.triggers.some(trigger => this.triggerMatches(trigger, event));
    });
  }

  private triggerMatches(trigger: InvalidationTrigger, event: InvalidationEvent): boolean {
    if (trigger.type !== event.trigger.type) return false;
    
    if (trigger.entityId && trigger.entityId !== event.trigger.entityId) return false;
    
    if (trigger.filters) {
      // Check if event filters match trigger filters
      for (const [key, value] of Object.entries(trigger.filters)) {
        if (event.trigger.filters?.[key] !== value) return false;
      }
    }
    
    return true;
  }

  private evaluateConditions(conditions: InvalidationCondition[], event: InvalidationEvent): boolean {
    return conditions.every(condition => {
      const value = this.getEventValue(condition.field, event);
      return this.evaluateCondition(condition, value);
    });
  }

  private getEventValue(field: string, event: InvalidationEvent): any {
    const paths = field.split('.');
    let value: any = event;
    
    for (const path of paths) {
      value = value?.[path];
      if (value === undefined) break;
    }
    
    return value;
  }

  private evaluateCondition(condition: InvalidationCondition, value: any): boolean {
    switch (condition.operator) {
      case 'equals':
        return value === condition.value;
      case 'not_equals':
        return value !== condition.value;
      case 'greater_than':
        return value > condition.value;
      case 'less_than':
        return value < condition.value;
      case 'contains':
        return String(value).includes(String(condition.value));
      case 'regex':
        return new RegExp(condition.value).test(String(value));
      default:
        return false;
    }
  }

  private isInCooldown(ruleId: string): boolean {
    const rule = this.rules.get(ruleId);
    if (!rule?.cooldown) return false;
    
    const lastInvalidation = this.lastInvalidation.get(ruleId);
    if (!lastInvalidation) return false;
    
    return Date.now() - lastInvalidation < rule.cooldown;
  }

  private async executeJob(job: InvalidationJob, strategy: InvalidationRule['strategy']): Promise<void> {
    job.status = 'processing';
    
    try {
      switch (strategy) {
        case 'immediate':
          await this.executeImmediateInvalidation(job);
          break;
        case 'lazy':
          await this.executeLazyInvalidation(job);
          break;
        case 'scheduled':
          await this.scheduleInvalidation(job);
          break;
        case 'batch':
          this.addToBatch(job);
          break;
      }
      
      job.status = 'completed';
    } catch (error) {
      job.status = 'failed';
      job.retryCount++;
      
      if (job.retryCount < 3) {
        // Retry with exponential backoff
        setTimeout(() => {
          this.executeJob(job, strategy);
        }, Math.pow(2, job.retryCount) * 1000);
      }
    }
    
    this.jobQueue.push(job);
  }

  private async executeImmediateInvalidation(job: InvalidationJob): Promise<void> {
    for (const target of job.targets) {
      await this.invalidateTarget(target, job.event);
    }
  }

  private async executeLazyInvalidation(job: InvalidationJob): Promise<void> {
    for (const target of job.targets) {
      await this.cacheManager.invalidate(
        this.buildInvalidationPattern(target, job.event),
        'lazy'
      );
    }
  }

  private async scheduleInvalidation(job: InvalidationJob): Promise<void> {
    // Schedule for next processing cycle
    setTimeout(async () => {
      await this.executeImmediateInvalidation(job);
    }, 1000);
  }

  private addToBatch(job: InvalidationJob): void {
    this.batchedJobs.push(job);
    
    if (!this.batchTimer) {
      this.batchTimer = setTimeout(() => {
        this.processBatch();
      }, 1000); // Process batch every second
    }
  }

  private async processBatch(): Promise<void> {
    const jobs = [...this.batchedJobs];
    this.batchedJobs = [];
    this.batchTimer = undefined;
    
    // Group jobs by target pattern to minimize cache operations
    const groupedTargets = new Map<string, InvalidationEvent[]>();
    
    for (const job of jobs) {
      for (const target of job.targets) {
        const key = `${target.type}:${target.value}`;
        if (!groupedTargets.has(key)) {
          groupedTargets.set(key, []);
        }
        groupedTargets.get(key)!.push(job.event);
      }
    }
    
    // Execute grouped invalidations
    for (const [targetKey] of groupedTargets) {
      const [, value] = targetKey.split(':', 2);
      await this.cacheManager.invalidate(value);
    }
  }

  private async invalidateTarget(target: InvalidationTarget, event: InvalidationEvent): Promise<void> {
    const pattern = this.buildInvalidationPattern(target, event);
    await this.cacheManager.invalidate(pattern, 'immediate');
  }

  private buildInvalidationPattern(target: InvalidationTarget, event: InvalidationEvent): string | RegExp {
    switch (target.type) {
      case 'key':
        return target.value as string;
      case 'pattern':
        return target.value as RegExp;
      case 'tag':
        // This would need to be implemented based on your cache tagging system
        return new RegExp(`.*${target.value}.*`);
      case 'dependency': {
        const prefix = target.value as string;
        const entityId = event.strategyId || event.userId || '';
        return `${prefix}${entityId}`;
      }
      default:
        return target.value as string;
    }
  }

  private startProcessing(): void {
    this.processingTimer = setInterval(() => {
      this.processEventQueue();
    }, 100); // Process events every 100ms
  }

  private async processEventQueue(): Promise<void> {
    const events = this.eventQueue.splice(0, 10); // Process up to 10 events per cycle
    
    for (const event of events) {
      await this.processEvent(event);
    }
  }

  /**
   * Cleanup and shutdown
   */
  public shutdown(): void {
    if (this.processingTimer) {
      clearInterval(this.processingTimer);
    }
    if (this.batchTimer) {
      clearTimeout(this.batchTimer);
    }
  }
}