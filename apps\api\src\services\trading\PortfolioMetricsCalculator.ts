/**
 * Portfolio Metrics Calculator Service
 * 
 * Implements portfolio-level risk metrics (beta, alpha, Sharpe ratio), diversification
 * score calculation and monitoring, risk-adjusted return calculation across all positions,
 * and integration with metrics translation service for user-friendly display.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';

// Portfolio Metrics Types
export interface Position {
  id: string;
  symbol: string;
  size: Decimal.Instance;
  entryPrice: Decimal.Instance;
  currentPrice: Decimal.Instance;
  unrealizedPnL: Decimal.Instance;
  realizedPnL?: Decimal.Instance;
  entryDate: Date;
  sector?: string;
  assetClass: 'forex' | 'commodity' | 'crypto' | 'stock';
}

export interface BenchmarkData {
  symbol: string;
  returns: number[];
  riskFreeRate: number; // Annual risk-free rate
  name: string;
}

export interface PortfolioMetrics {
  // Return Metrics
  totalReturn: number; // Portfolio total return percentage
  annualizedReturn: number; // Annualized return percentage
  realizedReturn: number; // Realized gains/losses percentage
  unrealizedReturn: number; // Unrealized gains/losses percentage

  // Risk Metrics
  volatility: number; // Annualized volatility
  sharpeRatio: number; // Risk-adjusted return measure
  sortinoRatio: number; // Downside deviation adjusted return
  calmarRatio: number; // Return/max drawdown ratio
  maxDrawdown: number; // Maximum portfolio drawdown

  // Risk-Adjusted Metrics
  alpha: number; // Excess return vs benchmark
  beta: number; // Systematic risk vs benchmark
  rSquared: number; // Correlation with benchmark
  treynorRatio: number; // Return per unit of systematic risk
  informationRatio: number; // Active return vs tracking error

  // Diversification Metrics
  diversificationScore: number; // 0-100 scale
  effectiveNumberOfPositions: number; // Concentration measure
  herfindahlIndex: number; // Concentration index
  sectorDiversification: number; // Sector distribution score
  assetClassDiversification: number; // Asset class distribution score

  // Performance Attribution
  sectorReturns: Map<string, number>; // Return by sector
  assetClassReturns: Map<string, number>; // Return by asset class
  topPerformers: Position[]; // Best performing positions
  bottomPerformers: Position[]; // Worst performing positions

  // Time-Based Metrics
  daysSinceInception: number;
  winRate: number; // Percentage of profitable positions
  profitFactor: number; // Gross profit / gross loss
  averageWin: Decimal.Instance;
  averageLoss: Decimal.Instance;
}

export interface MetricsConfig {
  benchmarkSymbol: string;
  riskFreeRate: number; // Annual rate
  lookbackPeriod: number; // Days for calculations
  rebalanceFrequency: 'daily' | 'weekly' | 'monthly';
  includeRealizedPnL: boolean;
}

export interface UserFriendlyMetrics {
  overallPerformance: {
    grade: 'A' | 'B' | 'C' | 'D' | 'F';
    description: string;
    keyHighlights: string[];
  };
  riskAssessment: {
    riskLevel: 'Low' | 'Medium' | 'High' | 'Very High';
    description: string;
    recommendations: string[];
  };
  diversificationAnalysis: {
    score: 'Excellent' | 'Good' | 'Fair' | 'Poor';
    description: string;
    improvements: string[];
  };
  benchmarkComparison: {
    performance: 'Outperforming' | 'Matching' | 'Underperforming';
    description: string;
    analysis: string;
  };
}

/**
 * Portfolio Metrics Calculator Service
 * Calculates comprehensive portfolio performance and risk metrics
 */
export class PortfolioMetricsCalculator {
  private readonly config: MetricsConfig;
  private benchmarkData: BenchmarkData | null = null;
  private historicalReturns: number[] = [];

  constructor(config?: Partial<MetricsConfig>) {
    this.config = {
      benchmarkSymbol: 'SPX', // S&P 500 as default benchmark
      riskFreeRate: 0.025, // 2.5% annual risk-free rate
      lookbackPeriod: 252, // 1 year of trading days
      rebalanceFrequency: 'daily',
      includeRealizedPnL: true,
      ...config
    };
  }

  /**
   * Calculate comprehensive portfolio metrics
   */
  public calculatePortfolioMetrics(positions: Position[], initialBalance: Decimal.Instance): PortfolioMetrics {
    if (positions.length === 0) {
      return this.getEmptyMetrics();
    }

    const currentValue = this.calculateCurrentValue(positions);
    const totalReturn = this.calculateTotalReturn(currentValue, initialBalance);
    const returns = this.calculatePortfolioReturns(positions);
    
    const metrics: PortfolioMetrics = {
      // Return Metrics
      totalReturn,
      annualizedReturn: this.calculateAnnualizedReturn(totalReturn, this.getPortfolioAge(positions)),
      realizedReturn: this.calculateRealizedReturn(positions, initialBalance),
      unrealizedReturn: this.calculateUnrealizedReturn(positions, initialBalance),

      // Risk Metrics
      volatility: this.calculateVolatility(returns),
      sharpeRatio: this.calculateSharpeRatio(returns),
      sortinoRatio: this.calculateSortinoRatio(returns),
      calmarRatio: this.calculateCalmarRatio(totalReturn, returns),
      maxDrawdown: this.calculateMaxDrawdown(returns),

      // Risk-Adjusted Metrics
      alpha: this.calculateAlpha(returns),
      beta: this.calculateBeta(returns),
      rSquared: this.calculateRSquared(returns),
      treynorRatio: this.calculateTreynorRatio(returns),
      informationRatio: this.calculateInformationRatio(returns),

      // Diversification Metrics
      diversificationScore: this.calculateDiversificationScore(positions),
      effectiveNumberOfPositions: this.calculateEffectivePositions(positions, currentValue),
      herfindahlIndex: this.calculateHerfindahlIndex(positions, currentValue),
      sectorDiversification: this.calculateSectorDiversification(positions),
      assetClassDiversification: this.calculateAssetClassDiversification(positions),

      // Performance Attribution
      sectorReturns: this.calculateSectorReturns(positions),
      assetClassReturns: this.calculateAssetClassReturns(positions),
      topPerformers: this.getTopPerformers(positions, 3),
      bottomPerformers: this.getBottomPerformers(positions, 3),

      // Time-Based Metrics
      daysSinceInception: this.getPortfolioAge(positions),
      winRate: this.calculateWinRate(positions),
      profitFactor: this.calculateProfitFactor(positions),
      averageWin: this.calculateAverageWin(positions),
      averageLoss: this.calculateAverageLoss(positions)
    };

    return metrics;
  }

  /**
   * Translate metrics to user-friendly format
   */
  public translateToUserFriendly(metrics: PortfolioMetrics): UserFriendlyMetrics {
    return {
      overallPerformance: this.assessOverallPerformance(metrics),
      riskAssessment: this.assessRiskLevel(metrics),
      diversificationAnalysis: this.assessDiversification(metrics),
      benchmarkComparison: this.assessBenchmarkComparison(metrics)
    };
  }

  /**
   * Set benchmark data for comparison calculations
   */
  public setBenchmarkData(benchmarkData: BenchmarkData): void {
    this.benchmarkData = benchmarkData;
  }

  /**
   * Update historical returns for calculations
   */
  public updateHistoricalReturns(returns: number[]): void {
    this.historicalReturns = returns.slice(-this.config.lookbackPeriod);
  }

  /**
   * Calculate risk-adjusted position size recommendation
   */
  public calculateRiskAdjustedSize(
    position: Position,
    portfolioMetrics: PortfolioMetrics,
    targetRisk: number = 0.02
  ): {
    recommendedSize: Decimal.Instance;
    riskContribution: number;
    reasoning: string[];
  } {
    const reasoning: string[] = [];
    let adjustmentFactor = 1.0;

    // Adjust based on portfolio volatility
    if (portfolioMetrics.volatility > 0.25) {
      adjustmentFactor *= 0.8;
      reasoning.push('High portfolio volatility - reducing position size');
    }

    // Adjust based on diversification
    if (portfolioMetrics.diversificationScore < 50) {
      adjustmentFactor *= 0.9;
      reasoning.push('Low diversification - conservative sizing');
    }

    // Adjust based on drawdown
    if (portfolioMetrics.maxDrawdown > 0.15) {
      adjustmentFactor *= 0.7;
      reasoning.push('High drawdown - reducing risk exposure');
    }

    // Adjust based on Sharpe ratio
    if (portfolioMetrics.sharpeRatio < 0.5) {
      adjustmentFactor *= 0.85;
      reasoning.push('Low risk-adjusted returns - conservative approach');
    }

    const currentSize = position.size;
    const recommendedSize = currentSize.mul(adjustmentFactor);
    const riskContribution = recommendedSize.mul(position.currentPrice).div(
      new Decimal(100000) // Assume $100k portfolio for calculation
    ).toNumber();

    if (reasoning.length === 0) {
      reasoning.push('Portfolio metrics support current position sizing');
    }

    return {
      recommendedSize,
      riskContribution,
      reasoning
    };
  }

  /**
   * Generate performance report
   */
  public generatePerformanceReport(metrics: PortfolioMetrics): {
    summary: string;
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
  } {
    const strengths: string[] = [];
    const weaknesses: string[] = [];
    const recommendations: string[] = [];

    // Analyze strengths
    if (metrics.sharpeRatio > 1.0) {
      strengths.push(`Excellent risk-adjusted returns (Sharpe: ${metrics.sharpeRatio.toFixed(2)})`);
    }
    if (metrics.diversificationScore > 80) {
      strengths.push(`Well-diversified portfolio (Score: ${metrics.diversificationScore.toFixed(0)})`);
    }
    if (metrics.winRate > 0.6) {
      strengths.push(`High win rate (${(metrics.winRate * 100).toFixed(1)}%)`);
    }

    // Analyze weaknesses
    if (metrics.maxDrawdown > 0.2) {
      weaknesses.push(`High maximum drawdown (${(metrics.maxDrawdown * 100).toFixed(1)}%)`);
    }
    if (metrics.diversificationScore < 50) {
      weaknesses.push(`Poor diversification (Score: ${metrics.diversificationScore.toFixed(0)})`);
    }
    if (metrics.sharpeRatio < 0.5) {
      weaknesses.push(`Low risk-adjusted returns (Sharpe: ${metrics.sharpeRatio.toFixed(2)})`);
    }

    // Generate recommendations
    if (metrics.beta > 1.5) {
      recommendations.push('Consider reducing high-beta positions to lower systematic risk');
    }
    if (metrics.diversificationScore < 60) {
      recommendations.push('Improve diversification across sectors and asset classes');
    }
    if (metrics.volatility > 0.3) {
      recommendations.push('Portfolio volatility is high - consider position size reduction');
    }

    const summary = this.generatePerformanceSummary(metrics);

    return { summary, strengths, weaknesses, recommendations };
  }

  // Private calculation methods

  private calculateCurrentValue(positions: Position[]): Decimal {
    return positions.reduce((sum, pos) => 
      sum.add(pos.size.mul(pos.currentPrice)), new Decimal(0)
    );
  }

  private calculateTotalReturn(currentValue: Decimal, initialBalance: Decimal.Instance): number {
    if (initialBalance.isZero()) return 0;
    return currentValue.sub(initialBalance).div(initialBalance).toNumber();
  }

  private calculateAnnualizedReturn(totalReturn: number, daysSinceInception: number): number {
    if (daysSinceInception <= 0) return 0;
    const years = daysSinceInception / 365.25;
    return Math.pow(1 + totalReturn, 1 / years) - 1;
  }

  private calculateRealizedReturn(positions: Position[], initialBalance: Decimal.Instance): number {
    if (!this.config.includeRealizedPnL) {
      return 0;
    }
    
    const realizedPnL = positions.reduce((sum, pos) => 
      sum.add(pos.realizedPnL || new Decimal(0)), new Decimal(0)
    );
    return initialBalance.isZero() ? 0 : realizedPnL.div(initialBalance).toNumber();
  }

  private calculateUnrealizedReturn(positions: Position[], initialBalance: Decimal.Instance): number {
    const unrealizedPnL = positions.reduce((sum, pos) => 
      sum.add(pos.unrealizedPnL), new Decimal(0)
    );
    return initialBalance.isZero() ? 0 : unrealizedPnL.div(initialBalance).toNumber();
  }

  private calculatePortfolioReturns(positions: Position[]): number[] {
    // This would typically use historical data - simplified for implementation
    return this.historicalReturns.length > 0 ? this.historicalReturns : [0];
  }

  private calculateVolatility(returns: number[]): number {
    if (returns.length < 2) return 0;
    
    const mean = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - mean, 2), 0) / (returns.length - 1);
    
    return Math.sqrt(variance * 252); // Annualized
  }

  private calculateSharpeRatio(returns: number[]): number {
    if (returns.length === 0) return 0;
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const volatility = this.calculateVolatility(returns);
    const excessReturn = avgReturn * 252 - this.config.riskFreeRate;
    
    return volatility === 0 ? 0 : excessReturn / volatility;
  }

  private calculateSortinoRatio(returns: number[]): number {
    if (returns.length === 0) return 0;
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const downside = returns.filter(r => r < 0);
    
    if (downside.length === 0) return avgReturn > 0 ? Infinity : 0;
    
    const downsideDeviation = Math.sqrt(
      downside.reduce((sum, r) => sum + r * r, 0) / downside.length
    ) * Math.sqrt(252);
    
    const excessReturn = avgReturn * 252 - this.config.riskFreeRate;
    return downsideDeviation === 0 ? 0 : excessReturn / downsideDeviation;
  }

  private calculateCalmarRatio(totalReturn: number, returns: number[]): number {
    const maxDrawdown = this.calculateMaxDrawdown(returns);
    return maxDrawdown === 0 ? 0 : totalReturn / maxDrawdown;
  }

  private calculateMaxDrawdown(returns: number[]): number {
    if (returns.length === 0) return 0;
    
    let peak = 0;
    let maxDrawdown = 0;
    let cumReturn = 0;
    
    for (const ret of returns) {
      cumReturn += ret;
      peak = Math.max(peak, cumReturn);
      maxDrawdown = Math.max(maxDrawdown, peak - cumReturn);
    }
    
    return maxDrawdown;
  }

  private calculateAlpha(returns: number[]): number {
    if (!this.benchmarkData || returns.length === 0) return 0;
    
    const portfolioReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length * 252;
    const benchmarkReturn = this.benchmarkData.returns.reduce((sum, r) => sum + r, 0) / this.benchmarkData.returns.length * 252;
    const beta = this.calculateBeta(returns);
    
    return portfolioReturn - (this.config.riskFreeRate + beta * (benchmarkReturn - this.config.riskFreeRate));
  }

  private calculateBeta(returns: number[]): number {
    if (!this.benchmarkData || returns.length === 0) return 1;
    
    const minLength = Math.min(returns.length, this.benchmarkData.returns.length);
    const portfolioReturns = returns.slice(0, minLength);
    const benchmarkReturns = this.benchmarkData.returns.slice(0, minLength);
    
    const covariance = this.calculateCovariance(portfolioReturns, benchmarkReturns);
    const benchmarkVariance = this.calculateVariance(benchmarkReturns);
    
    return benchmarkVariance === 0 ? 1 : covariance / benchmarkVariance;
  }

  private calculateRSquared(returns: number[]): number {
    if (!this.benchmarkData || returns.length === 0) return 0;
    
    const minLength = Math.min(returns.length, this.benchmarkData.returns.length);
    const portfolioReturns = returns.slice(0, minLength);
    const benchmarkReturns = this.benchmarkData.returns.slice(0, minLength);
    
    const correlation = this.calculateCorrelation(portfolioReturns, benchmarkReturns);
    return correlation * correlation;
  }

  private calculateTreynorRatio(returns: number[]): number {
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length * 252;
    const beta = this.calculateBeta(returns);
    const excessReturn = avgReturn - this.config.riskFreeRate;
    
    return beta === 0 ? 0 : excessReturn / beta;
  }

  private calculateInformationRatio(returns: number[]): number {
    if (!this.benchmarkData || returns.length === 0) return 0;
    
    const minLength = Math.min(returns.length, this.benchmarkData.returns.length);
    const portfolioReturns = returns.slice(0, minLength);
    const benchmarkReturns = this.benchmarkData.returns.slice(0, minLength);
    
    const activeReturns = portfolioReturns.map((r, i) => r - benchmarkReturns[i]);
    const avgActiveReturn = activeReturns.reduce((sum, r) => sum + r, 0) / activeReturns.length * 252;
    const trackingError = this.calculateVolatility(activeReturns);
    
    return trackingError === 0 ? 0 : avgActiveReturn / trackingError;
  }

  private calculateDiversificationScore(positions: Position[]): number {
    if (positions.length <= 1) return 0;
    
    const sectorScore = this.calculateSectorDiversification(positions);
    const assetClassScore = this.calculateAssetClassDiversification(positions);
    const concentrationScore = 100 - this.calculateHerfindahlIndex(positions, this.calculateCurrentValue(positions)) * 100;
    
    return (sectorScore + assetClassScore + concentrationScore) / 3;
  }

  private calculateEffectivePositions(positions: Position[], totalValue: Decimal.Instance): number {
    if (totalValue.isZero()) return 0;
    
    const weights = positions.map(pos => 
      pos.size.mul(pos.currentPrice).div(totalValue).toNumber()
    );
    
    const sumSquaredWeights = weights.reduce((sum, w) => sum + w * w, 0);
    return sumSquaredWeights === 0 ? 0 : 1 / sumSquaredWeights;
  }

  private calculateHerfindahlIndex(positions: Position[], totalValue: Decimal.Instance): number {
    if (totalValue.isZero()) return 1;
    
    const weights = positions.map(pos => 
      pos.size.mul(pos.currentPrice).div(totalValue).toNumber()
    );
    
    return weights.reduce((sum, w) => sum + w * w, 0);
  }

  private calculateSectorDiversification(positions: Position[]): number {
    const sectorWeights = new Map<string, number>();
    const totalValue = this.calculateCurrentValue(positions);
    
    positions.forEach(pos => {
      const sector = pos.sector || 'Unknown';
      const weight = pos.size.mul(pos.currentPrice).div(totalValue).toNumber();
      sectorWeights.set(sector, (sectorWeights.get(sector) || 0) + weight);
    });
    
    const weights = Array.from(sectorWeights.values());
    const herfindahl = weights.reduce((sum, w) => sum + w * w, 0);
    
    return Math.max(0, 100 * (1 - herfindahl));
  }

  private calculateAssetClassDiversification(positions: Position[]): number {
    const assetClassWeights = new Map<string, number>();
    const totalValue = this.calculateCurrentValue(positions);
    
    positions.forEach(pos => {
      const weight = pos.size.mul(pos.currentPrice).div(totalValue).toNumber();
      assetClassWeights.set(pos.assetClass, (assetClassWeights.get(pos.assetClass) || 0) + weight);
    });
    
    const weights = Array.from(assetClassWeights.values());
    const herfindahl = weights.reduce((sum, w) => sum + w * w, 0);
    
    return Math.max(0, 100 * (1 - herfindahl));
  }

  private calculateSectorReturns(positions: Position[]): Map<string, number> {
    const sectorReturns = new Map<string, number>();
    
    positions.forEach(pos => {
      const sector = pos.sector || 'Unknown';
      const returnPct = pos.entryPrice.isZero() ? 0 : 
        pos.currentPrice.sub(pos.entryPrice).div(pos.entryPrice).toNumber();
      
      if (!sectorReturns.has(sector)) {
        sectorReturns.set(sector, 0);
      }
      
      const currentReturn = sectorReturns.get(sector)!;
      sectorReturns.set(sector, currentReturn + returnPct);
    });
    
    return sectorReturns;
  }

  private calculateAssetClassReturns(positions: Position[]): Map<string, number> {
    const assetClassReturns = new Map<string, number>();
    
    positions.forEach(pos => {
      const returnPct = pos.entryPrice.isZero() ? 0 : 
        pos.currentPrice.sub(pos.entryPrice).div(pos.entryPrice).toNumber();
      
      if (!assetClassReturns.has(pos.assetClass)) {
        assetClassReturns.set(pos.assetClass, 0);
      }
      
      const currentReturn = assetClassReturns.get(pos.assetClass)!;
      assetClassReturns.set(pos.assetClass, currentReturn + returnPct);
    });
    
    return assetClassReturns;
  }

  private getTopPerformers(positions: Position[], count: number): Position[] {
    return positions
      .sort((a, b) => b.unrealizedPnL.sub(a.unrealizedPnL).toNumber())
      .slice(0, count);
  }

  private getBottomPerformers(positions: Position[], count: number): Position[] {
    return positions
      .sort((a, b) => a.unrealizedPnL.sub(b.unrealizedPnL).toNumber())
      .slice(0, count);
  }

  private getPortfolioAge(positions: Position[]): number {
    if (positions.length === 0) return 0;
    
    const oldestEntry = Math.min(...positions.map(pos => pos.entryDate.getTime()));
    return Math.floor((Date.now() - oldestEntry) / (1000 * 60 * 60 * 24));
  }

  private calculateWinRate(positions: Position[]): number {
    if (positions.length === 0) return 0;
    
    const winners = positions.filter(pos => pos.unrealizedPnL.gt(0)).length;
    return winners / positions.length;
  }

  private calculateProfitFactor(positions: Position[]): number {
    const grossProfit = positions
      .filter(pos => pos.unrealizedPnL.gt(0))
      .reduce((sum, pos) => sum.add(pos.unrealizedPnL), new Decimal(0));
    
    const grossLoss = positions
      .filter(pos => pos.unrealizedPnL.lt(0))
      .reduce((sum, pos) => sum.add(pos.unrealizedPnL.abs()), new Decimal(0));
    
    return grossLoss.isZero() ? (grossProfit.gt(0) ? Infinity : 1) : 
           grossProfit.div(grossLoss).toNumber();
  }

  private calculateAverageWin(positions: Position[]): Decimal {
    const winners = positions.filter(pos => pos.unrealizedPnL.gt(0));
    if (winners.length === 0) return new Decimal(0);
    
    const totalWins = winners.reduce((sum, pos) => sum.add(pos.unrealizedPnL), new Decimal(0));
    return totalWins.div(winners.length);
  }

  private calculateAverageLoss(positions: Position[]): Decimal {
    const losers = positions.filter(pos => pos.unrealizedPnL.lt(0));
    if (losers.length === 0) return new Decimal(0);
    
    const totalLosses = losers.reduce((sum, pos) => sum.add(pos.unrealizedPnL.abs()), new Decimal(0));
    return totalLosses.div(losers.length);
  }

  // Helper methods for user-friendly translations

  private assessOverallPerformance(metrics: PortfolioMetrics): UserFriendlyMetrics['overallPerformance'] {
    let grade: 'A' | 'B' | 'C' | 'D' | 'F' = 'C';
    const highlights: string[] = [];

    if (metrics.sharpeRatio > 1.5 && metrics.totalReturn > 0.15) {
      grade = 'A';
    } else if (metrics.sharpeRatio > 1.0 && metrics.totalReturn > 0.10) {
      grade = 'B';
    } else if (metrics.sharpeRatio > 0.5 && metrics.totalReturn > 0.05) {
      grade = 'C';
    } else if (metrics.totalReturn > 0) {
      grade = 'D';
    } else {
      grade = 'F';
    }

    if (metrics.totalReturn > 0) {
      highlights.push(`${(metrics.totalReturn * 100).toFixed(1)}% total return`);
    }
    if (metrics.sharpeRatio > 1.0) {
      highlights.push(`Strong risk-adjusted returns`);
    }
    if (metrics.winRate > 0.6) {
      highlights.push(`${(metrics.winRate * 100).toFixed(0)}% win rate`);
    }

    return {
      grade,
      description: this.getPerformanceDescription(grade),
      keyHighlights: highlights
    };
  }

  private assessRiskLevel(metrics: PortfolioMetrics): UserFriendlyMetrics['riskAssessment'] {
    let riskLevel: 'Low' | 'Medium' | 'High' | 'Very High' = 'Medium';
    const recommendations: string[] = [];

    if (metrics.volatility < 0.1 && metrics.beta < 0.8) {
      riskLevel = 'Low';
    } else if (metrics.volatility < 0.2 && metrics.beta < 1.2) {
      riskLevel = 'Medium';
    } else if (metrics.volatility < 0.35) {
      riskLevel = 'High';
    } else {
      riskLevel = 'Very High';
    }

    if (metrics.maxDrawdown > 0.15) {
      recommendations.push('Consider implementing stop-loss strategies');
    }
    if (metrics.beta > 1.5) {
      recommendations.push('Reduce exposure to high-beta assets');
    }
    if (metrics.volatility > 0.25) {
      recommendations.push('Consider position size reduction');
    }

    return {
      riskLevel,
      description: this.getRiskDescription(riskLevel),
      recommendations
    };
  }

  private assessDiversification(metrics: PortfolioMetrics): UserFriendlyMetrics['diversificationAnalysis'] {
    let score: 'Excellent' | 'Good' | 'Fair' | 'Poor' = 'Fair';
    const improvements: string[] = [];

    if (metrics.diversificationScore > 85) {
      score = 'Excellent';
    } else if (metrics.diversificationScore > 70) {
      score = 'Good';
    } else if (metrics.diversificationScore > 50) {
      score = 'Fair';
    } else {
      score = 'Poor';
    }

    if (metrics.sectorDiversification < 70) {
      improvements.push('Diversify across more sectors');
    }
    if (metrics.assetClassDiversification < 70) {
      improvements.push('Include different asset classes');
    }
    if (metrics.effectiveNumberOfPositions < 5) {
      improvements.push('Increase number of positions');
    }

    return {
      score,
      description: this.getDiversificationDescription(score),
      improvements
    };
  }

  private assessBenchmarkComparison(metrics: PortfolioMetrics): UserFriendlyMetrics['benchmarkComparison'] {
    let performance: 'Outperforming' | 'Matching' | 'Underperforming' = 'Matching';
    
    if (metrics.alpha > 0.02) {
      performance = 'Outperforming';
    } else if (metrics.alpha < -0.02) {
      performance = 'Underperforming';
    }

    return {
      performance,
      description: this.getBenchmarkDescription(performance, metrics.alpha),
      analysis: `Alpha: ${(metrics.alpha * 100).toFixed(2)}%, Beta: ${metrics.beta.toFixed(2)}`
    };
  }

  // Description helper methods

  private getPerformanceDescription(grade: string): string {
    const descriptions = {
      'A': 'Excellent performance with strong risk-adjusted returns',
      'B': 'Good performance above market expectations',
      'C': 'Average performance meeting basic expectations',
      'D': 'Below average performance with room for improvement',
      'F': 'Poor performance requiring immediate attention'
    };
    return descriptions[grade as keyof typeof descriptions] || 'Performance assessment unavailable';
  }

  private getRiskDescription(riskLevel: string): string {
    const descriptions = {
      'Low': 'Conservative risk profile suitable for stable returns',
      'Medium': 'Balanced risk profile with moderate volatility',
      'High': 'Aggressive risk profile with significant volatility',
      'Very High': 'Very aggressive risk profile requiring careful monitoring'
    };
    return descriptions[riskLevel as keyof typeof descriptions] || 'Risk assessment unavailable';
  }

  private getDiversificationDescription(score: string): string {
    const descriptions = {
      'Excellent': 'Well-diversified portfolio with optimal risk distribution',
      'Good': 'Good diversification with minor concentration risks',
      'Fair': 'Moderate diversification with some concentration concerns',
      'Poor': 'Poor diversification creating unnecessary concentration risk'
    };
    return descriptions[score as keyof typeof descriptions] || 'Diversification assessment unavailable';
  }

  private getBenchmarkDescription(performance: string, alpha: number): string {
    const descriptions = {
      'Outperforming': `Portfolio generating ${(alpha * 100).toFixed(2)}% excess returns above benchmark`,
      'Matching': 'Portfolio performance closely tracking benchmark returns',
      'Underperforming': `Portfolio lagging benchmark by ${(-alpha * 100).toFixed(2)}%`
    };
    return descriptions[performance as keyof typeof descriptions] || 'Benchmark comparison unavailable';
  }

  private generatePerformanceSummary(metrics: PortfolioMetrics): string {
    const returnStr = `${(metrics.totalReturn * 100).toFixed(1)}%`;
    const sharpeStr = metrics.sharpeRatio.toFixed(2);
    const volatilityStr = `${(metrics.volatility * 100).toFixed(1)}%`;
    
    return `Portfolio generated ${returnStr} total return with a Sharpe ratio of ${sharpeStr} ` +
           `and ${volatilityStr} annualized volatility. Diversification score: ${metrics.diversificationScore.toFixed(0)}/100.`;
  }

  // Statistical helper methods

  private calculateCovariance(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length);
    if (n < 2) return 0;

    const meanX = x.slice(0, n).reduce((sum, val) => sum + val, 0) / n;
    const meanY = y.slice(0, n).reduce((sum, val) => sum + val, 0) / n;

    let covariance = 0;
    for (let i = 0; i < n; i++) {
      covariance += (x[i] - meanX) * (y[i] - meanY);
    }

    return covariance / (n - 1);
  }

  private calculateVariance(values: number[]): number {
    if (values.length < 2) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / (values.length - 1);
    
    return variance;
  }

  private calculateCorrelation(x: number[], y: number[]): number {
    const covariance = this.calculateCovariance(x, y);
    const stdX = Math.sqrt(this.calculateVariance(x));
    const stdY = Math.sqrt(this.calculateVariance(y));
    
    return stdX === 0 || stdY === 0 ? 0 : covariance / (stdX * stdY);
  }

  private getEmptyMetrics(): PortfolioMetrics {
    return {
      totalReturn: 0,
      annualizedReturn: 0,
      realizedReturn: 0,
      unrealizedReturn: 0,
      volatility: 0,
      sharpeRatio: 0,
      sortinoRatio: 0,
      calmarRatio: 0,
      maxDrawdown: 0,
      alpha: 0,
      beta: 1,
      rSquared: 0,
      treynorRatio: 0,
      informationRatio: 0,
      diversificationScore: 0,
      effectiveNumberOfPositions: 0,
      herfindahlIndex: 1,
      sectorDiversification: 0,
      assetClassDiversification: 0,
      sectorReturns: new Map(),
      assetClassReturns: new Map(),
      topPerformers: [],
      bottomPerformers: [],
      daysSinceInception: 0,
      winRate: 0,
      profitFactor: 1,
      averageWin: new Decimal(0),
      averageLoss: new Decimal(0)
    };
  }
}