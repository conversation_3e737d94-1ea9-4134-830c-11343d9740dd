import { Request, Response, NextFunction } from 'express';
import { body, validationResult } from 'express-validator';
import Decimal from 'decimal.js';
import { TradeExecutionEngine } from '../../services/trading/TradeExecutionEngine';
import { RiskManagementService } from '../../services/risk/RiskManagementService';
import { AuditService } from '../../services/audit/AuditService';
import { LiveTradeRequest, LiveTradeResponse } from '@golddaddy/types';

interface ExecuteTradeRequestBody {
  strategyId: string;
  goalId: string;
  instrument: string;
  type: 'buy' | 'sell';
  quantity: string;
  stopLoss?: string;
  takeProfit?: string;
  confirmRisk: boolean;
  urgency: 'normal' | 'fast';
  maxSlippagePercent?: string;
}

// Validation middleware
export const validateExecuteTradeRequest = [
  body('strategyId')
    .isString()
    .notEmpty()
    .withMessage('Strategy ID is required'),
  
  body('goalId')
    .isString()
    .notEmpty()
    .withMessage('Goal ID is required'),
  
  body('instrument')
    .isString()
    .matches(/^[A-Z]{6}$/)
    .withMessage('Invalid instrument format (expected: EURUSD)'),
  
  body('type')
    .isIn(['buy', 'sell'])
    .withMessage('Type must be either "buy" or "sell"'),
  
  body('quantity')
    .isString()
    .custom((value) => {
      try {
        const decimal = new Decimal(value);
        if (decimal.lte(0)) {
          throw new Error('Quantity must be greater than 0');
        }
        if (decimal.gt(1000000)) {
          throw new Error('Quantity cannot exceed 1,000,000');
        }
        return true;
      } catch (error) {
        throw new Error('Invalid quantity format');
      }
    }),
  
  body('stopLoss')
    .optional()
    .isString()
    .custom((value) => {
      if (value) {
        try {
          new Decimal(value);
          return true;
        } catch (error) {
          throw new Error('Invalid stop loss format');
        }
      }
      return true;
    }),
  
  body('takeProfit')
    .optional()
    .isString()
    .custom((value) => {
      if (value) {
        try {
          new Decimal(value);
          return true;
        } catch (error) {
          throw new Error('Invalid take profit format');
        }
      }
      return true;
    }),
  
  body('confirmRisk')
    .isBoolean()
    .withMessage('Risk confirmation is required'),
  
  body('urgency')
    .isIn(['normal', 'fast'])
    .withMessage('Urgency must be either "normal" or "fast"'),
  
  body('maxSlippagePercent')
    .optional()
    .isString()
    .custom((value) => {
      if (value) {
        try {
          const decimal = new Decimal(value);
          if (decimal.lt(0) || decimal.gt(10)) {
            throw new Error('Max slippage must be between 0 and 10 percent');
          }
          return true;
        } catch (error) {
          throw new Error('Invalid max slippage format');
        }
      }
      return true;
    })
];

export class TradeExecutionController {
  constructor(
    private tradeExecutionEngine: TradeExecutionEngine,
    private riskManagementService: RiskManagementService,
    private auditService: AuditService
  ) {}

  async executeTrade(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // Validate request
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          details: errors.array()
        });
        return;
      }

      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required'
        });
        return;
      }

      const body = req.body as ExecuteTradeRequestBody;

      // Convert string decimals to Decimal objects
      const tradeRequest: LiveTradeRequest = {
        strategyId: body.strategyId,
        goalId: body.goalId,
        instrument: body.instrument,
        type: body.type,
        quantity: new Decimal(body.quantity),
        stopLoss: body.stopLoss ? new Decimal(body.stopLoss) : undefined,
        takeProfit: body.takeProfit ? new Decimal(body.takeProfit) : undefined,
        confirmRisk: body.confirmRisk,
        urgency: body.urgency,
        maxSlippagePercent: body.maxSlippagePercent ? new Decimal(body.maxSlippagePercent) : undefined
      };

      // Create audit entry for trade execution request
      const auditTrailId = await this.auditService.createAuditEntry({
        action: 'TRADE_EXECUTION_REQUESTED',
        userId,
        data: {
          ...tradeRequest,
          quantity: tradeRequest.quantity.toString(),
          stopLoss: tradeRequest.stopLoss?.toString(),
          takeProfit: tradeRequest.takeProfit?.toString(),
          maxSlippagePercent: tradeRequest.maxSlippagePercent?.toString()
        }
      });

      // Pre-execution risk checks
      const riskAssessment = await this.riskManagementService.assessTradeRisk({
        userId,
        instrument: tradeRequest.instrument,
        type: tradeRequest.type,
        quantity: tradeRequest.quantity,
        currentExposure: await this.getCurrentUserExposure(userId)
      });

      if (riskAssessment.riskLevel === 'CRITICAL' && !tradeRequest.confirmRisk) {
        await this.auditService.updateAuditEntry(auditTrailId, {
          status: 'BLOCKED_BY_RISK_MANAGEMENT',
          riskAssessment
        });

        res.status(400).json({
          success: false,
          error: 'Trade blocked by risk management',
          riskAssessment,
          requiresRiskConfirmation: true
        });
        return;
      }

      // Execute the trade
      console.log(`Executing trade for user ${userId}:`, tradeRequest);
      const tradeResponse: LiveTradeResponse = await this.tradeExecutionEngine.executeTrade(
        tradeRequest,
        userId
      );

      // Update audit entry with execution result
      await this.auditService.updateAuditEntry(auditTrailId, {
        status: 'EXECUTED',
        executionResult: {
          tradeId: tradeResponse.trade.id,
          executionPrice: tradeResponse.execution.executionPrice.toString(),
          actualSlippage: tradeResponse.execution.actualSlippage.toString(),
          brokerOrderId: tradeResponse.execution.brokerOrderId,
          filledAt: tradeResponse.execution.filledAt
        }
      });

      // Convert Decimal objects to strings for JSON response
      const responseData = {
        success: true,
        data: {
          trade: {
            ...tradeResponse.trade,
            requestedPrice: tradeResponse.trade.requestedPrice.toString(),
            executedPrice: tradeResponse.trade.executedPrice.toString(),
            slippage: tradeResponse.trade.slippage.toString()
          },
          execution: {
            ...tradeResponse.execution,
            executionPrice: tradeResponse.execution.executionPrice.toString(),
            actualSlippage: tradeResponse.execution.actualSlippage.toString(),
            brokerFees: tradeResponse.execution.brokerFees.toString()
          },
          compliance: tradeResponse.compliance,
          riskAssessment
        }
      };

      res.status(200).json(responseData);

    } catch (error) {
      console.error('Trade execution error:', error);

      // Log error to audit trail
      try {
        await this.auditService.createAuditEntry({
          action: 'TRADE_EXECUTION_FAILED',
          userId: req.user?.id || 'unknown',
          error: error instanceof Error ? error.message : 'Unknown error',
          data: req.body
        });
      } catch (auditError) {
        console.error('Failed to create audit entry for execution error:', auditError);
      }

      if (error instanceof Error) {
        // Handle specific error types
        if (error.message.includes('insufficient funds')) {
          res.status(400).json({
            success: false,
            error: 'Insufficient funds for trade execution',
            code: 'INSUFFICIENT_FUNDS'
          });
          return;
        }

        if (error.message.includes('market closed') || error.message.includes('Market is currently closed')) {
          res.status(400).json({
            success: false,
            error: 'Market is currently closed for this instrument',
            code: 'MARKET_CLOSED',
            marketHours: {
              nextOpen: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
              timezone: 'UTC'
            }
          });
          return;
        }

        if (error.message.includes('broker') || error.message.includes('connection') || error.message.includes('timeout') || error.message.includes('network')) {
          res.status(503).json({
            success: false,
            error: 'Broker connectivity issues. Please try again.',
            code: 'BROKER_ERROR',
            retryAfter: 30
          });
          return;
        }

        if (error.message.includes('Database') || error.message.includes('database')) {
          res.status(503).json({
            success: false,
            error: 'Service temporarily unavailable due to database issues',
            code: 'DATABASE_ERROR',
            retryAfter: 60
          });
          return;
        }

        if (error.message.includes('overload') || error.message.includes('System overload')) {
          res.status(503).json({
            success: false,
            error: 'System overload - please retry later',
            code: 'SYSTEM_OVERLOAD',
            retryAfter: 120
          });
          res.set('Retry-After', '120');
          return;
        }
      }

      next(error);
    }
  }

  private async getCurrentUserExposure(_userId: string): Promise<Decimal.Instance> {
    // This would typically query the database for current user positions
    // For now, return a mock value
    return new Decimal(10000);
  }
}

// Factory function to create controller with dependencies
export const createTradeExecutionController = (
  tradeExecutionEngine: TradeExecutionEngine,
  riskManagementService: RiskManagementService,
  auditService: AuditService
) => {
  return new TradeExecutionController(
    tradeExecutionEngine,
    riskManagementService,
    auditService
  );
};