import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Decimal } from 'decimal.js';
import { RiskManager } from '../../RiskManager';
import { LossLimitEnforcer } from '../../LossLimitEnforcer';
import { PositionSizer } from '../../PositionSizer';
import { Position, MarketConditions } from '@golddaddy/types';

describe('Volatility and Extreme Conditions Stress Tests', () => {
  let riskManager: RiskManager;
  let lossLimitEnforcer: LossLimitEnforcer;
  let positionSizer: PositionSizer;
  
  const mockUserId = '123e4567-e89b-12d3-a456-426614174000';

  beforeEach(() => {
    riskManager = new RiskManager();
    lossLimitEnforcer = new LossLimitEnforcer();
    positionSizer = new PositionSizer();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Extreme Volatility Scenarios', () => {
    it('should handle 50+ standard deviation price movements', async () => {
      // Start with normal position
      const positions: Position[] = [
        {
          id: 'extreme-vol-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        }
      ];

      // Simulate extreme 50-sigma event (price moves 5000 pips in one direction)
      // Historical example: CHF flash crash was ~2000 pips in minutes
      const extremePrice = new Decimal(1.1500); // 500 pip crash (50+ sigma for typical daily moves)
      
      const extremePositions = positions.map(pos => ({
        ...pos,
        currentPrice: extremePrice,
        unrealizedPnl: pos.size.mul(extremePrice.sub(pos.entryPrice))
      }));

      const portfolioValue = new Decimal(50000);
      
      // Calculate risk under extreme volatility
      const extremeVolatilityConditions: MarketConditions = {
        volatilityIndex: 1.0, // Maximum volatility
        liquidityScore: 0.01, // 99% liquidity evaporation
        spreadWidening: 50.0, // Spreads 50x wider
        gappingProbability: 0.95, // 95% chance of gaps
        slippageFactor: 0.25 // 25% average slippage
      };

      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        extremePositions,
        portfolioValue,
        extremeVolatilityConditions
      );

      // Should show maximum risk metrics
      expect(portfolioRisk.riskScore).toBe(100);
      expect(portfolioRisk.var95.div(portfolioValue).toNumber()).toBeGreaterThan(0.5); // VaR > 50%
      expect(extremePositions[0].unrealizedPnl.toNumber()).toBeLessThan(-45000); // Massive loss

      // Emergency liquidation should be immediate
      const shouldLiquidate = await lossLimitEnforcer.checkEmergencyLiquidation(
        mockUserId,
        extremePositions,
        portfolioValue
      );

      expect(shouldLiquidate).toBe(true);
    });

    it('should handle rapid volatility escalation patterns', async () => {
      const positions: Position[] = [
        {
          id: 'vol-escalation',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.3000),
          currentPrice: new Decimal(1.3000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.2950),
          takeProfit: new Decimal(1.3100)
        }
      ];

      const portfolioValue = new Decimal(75000);
      
      // Simulate escalating volatility pattern
      const volatilityLevels = [0.2, 0.4, 0.6, 0.8, 1.0]; // Escalating from 20% to 100%
      const basePriceMovements = [0.01, 0.02, 0.04, 0.08, 0.15]; // Increasing price moves
      
      const riskScores: number[] = [];
      
      for (let i = 0; i < volatilityLevels.length; i++) {
        const movement = basePriceMovements[i] * (Math.random() > 0.5 ? -1 : 1);
        const newPrice = new Decimal(1.3000).mul(1 + movement);
        
        const updatedPositions = positions.map(pos => ({
          ...pos,
          currentPrice: newPrice,
          unrealizedPnl: pos.size.mul(newPrice.sub(pos.entryPrice))
        }));

        const marketConditions: MarketConditions = {
          volatilityIndex: volatilityLevels[i],
          liquidityScore: 1 - volatilityLevels[i] * 0.8, // Inverse relationship
          spreadWidening: 1 + volatilityLevels[i] * 10,
          gappingProbability: volatilityLevels[i] * 0.6,
          slippageFactor: volatilityLevels[i] * 0.1
        };

        const portfolioRisk = await riskManager.calculatePortfolioRisk(
          updatedPositions,
          portfolioValue,
          marketConditions
        );

        riskScores.push(portfolioRisk.riskScore);
      }

      // Risk scores should generally increase with volatility
      expect(riskScores[riskScores.length - 1]).toBeGreaterThan(riskScores[0]);
      expect(riskScores[riskScores.length - 1]).toBeGreaterThan(75); // High risk at max volatility
    });

    it('should handle volatility clustering effects', async () => {
      // Positions in different asset classes
      const diversePositions: Position[] = [
        {
          id: 'cluster-forex',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        },
        {
          id: 'cluster-commodity',
          userId: mockUserId,
          symbol: 'XAUUSD', // Gold
          size: new Decimal(10), // 10 ounces
          entryPrice: new Decimal(2000.00),
          currentPrice: new Decimal(2000.00),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1950.00),
          takeProfit: new Decimal(2100.00)
        }
      ];

      // Simulate volatility clustering: periods of calm followed by turbulence
      const volatilityPatterns = [
        { period: 'calm', volatility: 0.1, duration: 20 },
        { period: 'building', volatility: 0.3, duration: 10 },
        { period: 'turbulent', volatility: 0.8, duration: 15 },
        { period: 'extreme', volatility: 1.0, duration: 5 },
        { period: 'calming', volatility: 0.4, duration: 10 }
      ];

      const riskHistory: Array<{ period: string; avgRisk: number; maxRisk: number }> = [];
      const portfolioValue = new Decimal(100000);

      for (const pattern of volatilityPatterns) {
        const periodRisks: number[] = [];
        
        // Simulate multiple price updates during each period
        for (let tick = 0; tick < pattern.duration; tick++) {
          const priceMovements = {
            'EURUSD': (Math.random() - 0.5) * pattern.volatility * 0.02,
            'XAUUSD': (Math.random() - 0.5) * pattern.volatility * 0.05
          };

          const updatedPositions = diversePositions.map(pos => {
            const movement = priceMovements[pos.symbol] || 0;
            const newPrice = pos.entryPrice.mul(1 + movement);
            const pnl = pos.size.mul(newPrice.sub(pos.entryPrice));
            
            return {
              ...pos,
              currentPrice: newPrice,
              unrealizedPnl: pnl
            };
          });

          const marketConditions: MarketConditions = {
            volatilityIndex: pattern.volatility,
            liquidityScore: Math.max(0.1, 1 - pattern.volatility * 0.7),
            spreadWidening: 1 + pattern.volatility * 5,
            gappingProbability: pattern.volatility * 0.5,
            slippageFactor: pattern.volatility * 0.08
          };

          const portfolioRisk = await riskManager.calculatePortfolioRisk(
            updatedPositions,
            portfolioValue,
            marketConditions
          );

          periodRisks.push(portfolioRisk.riskScore);
        }

        riskHistory.push({
          period: pattern.period,
          avgRisk: periodRisks.reduce((a, b) => a + b, 0) / periodRisks.length,
          maxRisk: Math.max(...periodRisks)
        });
      }

      // Should show clear volatility clustering effects
      const extremePeriod = riskHistory.find(p => p.period === 'extreme')!;
      const calmPeriod = riskHistory.find(p => p.period === 'calm')!;
      
      expect(extremePeriod.avgRisk).toBeGreaterThan(calmPeriod.avgRisk * 2);
      expect(extremePeriod.maxRisk).toBeGreaterThan(80);
      expect(calmPeriod.avgRisk).toBeLessThan(30);
    });
  });

  describe('Liquidity Stress Scenarios', () => {
    it('should handle complete market maker withdrawal', async () => {
      const positions: Position[] = [
        {
          id: 'liquidity-stress',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(500000), // Large position for liquidity impact
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1980),
          unrealizedPnl: new Decimal(-10000),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        }
      ];

      // Complete liquidity drought conditions
      const liquidityDrought: MarketConditions = {
        volatilityIndex: 0.9,
        liquidityScore: 0.001, // 99.9% liquidity reduction
        spreadWidening: 100.0, // Spreads 100x wider
        gappingProbability: 0.9, // 90% gap probability
        slippageFactor: 0.5 // 50% slippage
      };

      const portfolioValue = new Decimal(200000);
      
      // Risk calculation should reflect liquidity constraints
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        positions,
        portfolioValue,
        liquidityDrought
      );

      expect(portfolioRisk.liquidityRisk).toBeGreaterThan(0.95);
      expect(portfolioRisk.riskScore).toBeGreaterThan(90);

      // Position sizing should be severely limited
      const recommendedSize = await positionSizer.calculatePositionSize(
        mockUserId,
        'EURUSD',
        new Decimal(1.2000),
        new Decimal(1.1950),
        new Decimal(0.02), // 2% risk tolerance
        portfolioValue,
        liquidityDrought
      );

      expect(recommendedSize.toNumber()).toBeLessThan(20000); // Severely restricted
    });

    it('should handle asymmetric liquidity conditions', async () => {
      // Positions in different liquidity tiers
      const tieredPositions: Position[] = [
        {
          id: 'major-pair',
          userId: mockUserId,
          symbol: 'EURUSD', // Major pair - usually high liquidity
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1990),
          unrealizedPnl: new Decimal(-1000),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        },
        {
          id: 'minor-pair',
          userId: mockUserId,
          symbol: 'EURSEK', // Minor pair - lower liquidity
          size: new Decimal(100000),
          entryPrice: new Decimal(11.0000),
          currentPrice: new Decimal(10.9500),
          unrealizedPnl: new Decimal(-4545.45),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(10.8000),
          takeProfit: new Decimal(11.3000)
        },
        {
          id: 'exotic-pair',
          userId: mockUserId,
          symbol: 'USDZAR', // Exotic pair - very low liquidity
          size: new Decimal(100000),
          entryPrice: new Decimal(18.5000),
          currentPrice: new Decimal(18.2000),
          unrealizedPnl: new Decimal(-1621.62),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(18.0000),
          takeProfit: new Decimal(19.0000)
        }
      ];

      // Asymmetric liquidity stress: major pairs maintain some liquidity, others dry up
      const asymmetricLiquidityMap = {
        'EURUSD': { liquidity: 0.3, spread: 3.0, slippage: 0.05 }, // 30% normal liquidity
        'EURSEK': { liquidity: 0.1, spread: 10.0, slippage: 0.15 }, // 10% normal liquidity  
        'USDZAR': { liquidity: 0.02, spread: 25.0, slippage: 0.30 } // 2% normal liquidity
      };

      const portfolioValue = new Decimal(150000);
      
      // Calculate risk with asymmetric conditions
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        tieredPositions,
        portfolioValue,
        { asymmetricLiquidity: asymmetricLiquidityMap }
      );

      // Should show high liquidity risk due to exotic exposure
      expect(portfolioRisk.liquidityRisk).toBeGreaterThan(0.6);
      
      // Individual position risk should correlate with liquidity tier
      expect(portfolioRisk.positionRisks['exotic-pair']).toBeGreaterThan(
        portfolioRisk.positionRisks['major-pair']
      );
    });

    it('should handle liquidity cascade failures', async () => {
      // Multiple positions that could trigger cascade
      const cascadePositions: Position[] = [
        {
          id: 'cascade-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(200000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1950),
          unrealizedPnl: new Decimal(-10000),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1900),
          takeProfit: new Decimal(1.2200)
        },
        {
          id: 'cascade-2',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(150000),
          entryPrice: new Decimal(1.3000),
          currentPrice: new Decimal(1.2950),
          unrealizedPnl: new Decimal(-7500),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.2850),
          takeProfit: new Decimal(1.3200)
        }
      ];

      // Simulate cascade: first liquidation impacts liquidity for subsequent ones
      const initialLiquidity = 0.5; // 50% normal liquidity
      let currentLiquidity = initialLiquidity;
      const cascadeResults: Array<{ step: number; liquidity: number; slippage: number }> = [];

      for (let step = 0; step < cascadePositions.length; step++) {
        // Each liquidation reduces liquidity further
        const liquidityImpact = cascadePositions[step].size.div(1000000).toNumber(); // Impact based on position size
        currentLiquidity = Math.max(0.05, currentLiquidity - liquidityImpact * 0.2);
        
        const slippage = Math.min(0.3, (1 - currentLiquidity) * 0.4); // Higher slippage with lower liquidity
        
        cascadeResults.push({
          step: step + 1,
          liquidity: currentLiquidity,
          slippage: slippage
        });

        // Simulate market conditions for this step
        const stepConditions: MarketConditions = {
          volatilityIndex: 0.8,
          liquidityScore: currentLiquidity,
          spreadWidening: 5.0 / currentLiquidity, // Spreads widen inversely to liquidity
          gappingProbability: 0.7,
          slippageFactor: slippage
        };

        const portfolioRisk = await riskManager.calculatePortfolioRisk(
          cascadePositions,
          new Decimal(100000),
          stepConditions
        );

        expect(portfolioRisk.liquidityRisk).toBeGreaterThan(0.5);
      }

      // Should show deteriorating liquidity conditions
      expect(cascadeResults[cascadeResults.length - 1].liquidity).toBeLessThan(
        cascadeResults[0].liquidity
      );
      expect(cascadeResults[cascadeResults.length - 1].slippage).toBeGreaterThan(
        cascadeResults[0].slippage
      );
    });
  });

  describe('Correlation Breakdown Under Stress', () => {
    it('should handle complete correlation matrix breakdown', async () => {
      // Highly correlated portfolio under normal conditions
      const correlatedPositions: Position[] = [
        {
          id: 'corr-eur-usd',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        },
        {
          id: 'corr-eur-gbp',
          userId: mockUserId,
          symbol: 'EURGBP',
          size: new Decimal(100000),
          entryPrice: new Decimal(0.8500),
          currentPrice: new Decimal(0.8500),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(0.8450),
          takeProfit: new Decimal(0.8600)
        },
        {
          id: 'corr-eur-chf',
          userId: mockUserId,
          symbol: 'EURCHF',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.0800),
          currentPrice: new Decimal(1.0800),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.0750),
          takeProfit: new Decimal(1.0900)
        }
      ];

      // Normal correlation scenario
      const normalPortfolioRisk = await riskManager.calculatePortfolioRisk(
        correlatedPositions,
        new Decimal(150000)
      );

      // Crisis correlation breakdown: pairs move independently or inversely
      const breakdownMovements = {
        'EURUSD': -0.05, // EUR/USD falls 5%
        'EURGBP': +0.03, // EUR/GBP rises 3% (inverse correlation)
        'EURCHF': -0.08  // EUR/CHF falls 8% (no correlation)
      };

      const breakdownPositions = correlatedPositions.map(position => {
        const movement = breakdownMovements[position.symbol] || 0;
        const newPrice = position.entryPrice.mul(1 + movement);
        const pnl = position.size.mul(newPrice.sub(position.entryPrice));
        
        return {
          ...position,
          currentPrice: newPrice,
          unrealizedPnl: pnl
        };
      });

      const breakdownConditions: MarketConditions = {
        volatilityIndex: 0.9,
        liquidityScore: 0.2,
        spreadWidening: 8.0,
        correlationBreakdown: true,
        gappingProbability: 0.8,
        slippageFactor: 0.12
      };

      const breakdownPortfolioRisk = await riskManager.calculatePortfolioRisk(
        breakdownPositions,
        new Decimal(150000),
        breakdownConditions
      );

      // Diversification benefit should be reduced during breakdown
      expect(breakdownPortfolioRisk.diversificationRatio).toBeLessThan(
        normalPortfolioRisk.diversificationRatio
      );
      expect(breakdownPortfolioRisk.correlationRisk).toBeGreaterThan(0.8);
      expect(breakdownPortfolioRisk.riskScore).toBeGreaterThan(normalPortfolioRisk.riskScore);
    });

    it('should handle regime changes in correlations', async () => {
      const regimePositions: Position[] = [
        {
          id: 'regime-stock-1',
          userId: mockUserId,
          symbol: 'SPY', // S&P 500
          size: new Decimal(1000),
          entryPrice: new Decimal(400.00),
          currentPrice: new Decimal(400.00),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(380.00),
          takeProfit: new Decimal(420.00)
        },
        {
          id: 'regime-bond-1',
          userId: mockUserId,
          symbol: 'TLT', // 20-year Treasury bonds
          size: new Decimal(1000),
          entryPrice: new Decimal(120.00),
          currentPrice: new Decimal(120.00),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(115.00),
          takeProfit: new Decimal(125.00)
        }
      ];

      // Normal regime: stocks and bonds negatively correlated
      const normalMovements = {
        'SPY': -0.02, // Stocks down 2%
        'TLT': +0.01  // Bonds up 1% (negative correlation)
      };

      // Crisis regime: stocks and bonds positively correlated (both fall)
      const crisisMovements = {
        'SPY': -0.10, // Stocks down 10%
        'TLT': -0.05  // Bonds also down 5% (positive correlation in crisis)
      };

      const normalPositions = regimePositions.map(pos => {
        const movement = normalMovements[pos.symbol] || 0;
        const newPrice = pos.entryPrice.mul(1 + movement);
        return {
          ...pos,
          currentPrice: newPrice,
          unrealizedPnl: pos.size.mul(newPrice.sub(pos.entryPrice))
        };
      });

      const crisisPositions = regimePositions.map(pos => {
        const movement = crisisMovements[pos.symbol] || 0;
        const newPrice = pos.entryPrice.mul(1 + movement);
        return {
          ...pos,
          currentPrice: newPrice,
          unrealizedPnl: pos.size.mul(newPrice.sub(pos.entryPrice))
        };
      });

      const portfolioValue = new Decimal(800000);

      // Calculate risks for both regimes
      const normalRisk = await riskManager.calculatePortfolioRisk(
        normalPositions,
        portfolioValue
      );

      const crisisRisk = await riskManager.calculatePortfolioRisk(
        crisisPositions,
        portfolioValue,
        { correlationRegimeShift: true }
      );

      // Crisis regime should show higher risk due to positive correlation
      expect(crisisRisk.riskScore).toBeGreaterThan(normalRisk.riskScore);
      
      // Total losses should be higher in crisis due to lack of diversification
      const normalTotalPnl = normalPositions.reduce((sum, pos) => sum.add(pos.unrealizedPnl), new Decimal(0));
      const crisisTotalPnl = crisisPositions.reduce((sum, pos) => sum.add(pos.unrealizedPnl), new Decimal(0));
      
      expect(crisisTotalPnl.toNumber()).toBeLessThan(normalTotalPnl.toNumber());
    });
  });

  describe('System Stability Under Extreme Load', () => {
    it('should maintain accuracy during extreme computational load', async () => {
      // Create maximum complexity scenario
      const extremeComplexityPositions: Position[] = Array.from({ length: 2000 }, (_, i) => ({
        id: `extreme-${i}`,
        userId: mockUserId,
        symbol: ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCHF', 'USDCAD', 'NZDUSD'][i % 7],
        size: new Decimal(Math.random() * 100000 + 10000),
        entryPrice: new Decimal(Math.random() * 2 + 0.5),
        currentPrice: new Decimal(Math.random() * 2 + 0.5),
        unrealizedPnl: new Decimal((Math.random() - 0.5) * 10000),
        direction: Math.random() > 0.5 ? 'buy' : 'sell',
        openTime: new Date(Date.now() - Math.random() * 86400000),
        stopLoss: new Decimal(Math.random() * 2 + 0.4),
        takeProfit: new Decimal(Math.random() * 2 + 0.6)
      }));

      const extremeConditions: MarketConditions = {
        volatilityIndex: 1.0,
        liquidityScore: 0.01,
        spreadWidening: 100.0,
        gappingProbability: 0.95,
        slippageFactor: 0.5,
        correlationBreakdown: true
      };

      const portfolioValue = new Decimal(50000000); // $50M portfolio

      const startTime = Date.now();

      // Perform multiple concurrent risk calculations
      const concurrentCalculations = Array.from({ length: 10 }, () => 
        riskManager.calculatePortfolioRisk(
          extremeComplexityPositions,
          portfolioValue,
          extremeConditions
        )
      );

      const results = await Promise.all(concurrentCalculations);
      const processingTime = Date.now() - startTime;

      // Should complete within reasonable time despite complexity
      expect(processingTime).toBeLessThan(30000); // 30 seconds max

      // Results should be consistent across concurrent calculations
      const firstRiskScore = results[0].riskScore;
      results.forEach(result => {
        expect(Math.abs(result.riskScore - firstRiskScore)).toBeLessThan(5); // Within 5 points
      });

      // All results should show extreme risk
      results.forEach(result => {
        expect(result.riskScore).toBeGreaterThan(90);
        expect(result.liquidityRisk).toBeGreaterThan(0.9);
      });
    });

    it('should handle memory pressure during stress testing', async () => {
      // Create memory-intensive scenario with large historical data
      const memoryStressPositions: Position[] = Array.from({ length: 5000 }, (_, i) => ({
        id: `memory-${i}`,
        userId: mockUserId,
        symbol: `PAIR${i % 50}USD`, // 50 different currency pairs
        size: new Decimal(10000 + i),
        entryPrice: new Decimal(1.0 + i * 0.0001),
        currentPrice: new Decimal(1.0 + i * 0.0001 + (Math.random() - 0.5) * 0.1),
        unrealizedPnl: new Decimal((Math.random() - 0.5) * 5000),
        direction: i % 2 === 0 ? 'buy' : 'sell',
        openTime: new Date(Date.now() - i * 1000),
        stopLoss: new Decimal(1.0 + i * 0.0001 - 0.05),
        takeProfit: new Decimal(1.0 + i * 0.0001 + 0.05)
      }));

      const portfolioValue = new Decimal(100000000); // $100M portfolio
      
      // Monitor memory usage during calculation
      const initialMemoryUsage = process.memoryUsage();
      
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        memoryStressPositions,
        portfolioValue
      );

      const finalMemoryUsage = process.memoryUsage();
      const memoryIncrease = finalMemoryUsage.heapUsed - initialMemoryUsage.heapUsed;

      // Should handle large portfolio without excessive memory usage
      expect(memoryIncrease).toBeLessThan(1000 * 1024 * 1024); // Less than 1GB increase

      // Should still produce valid results
      expect(portfolioRisk.riskScore).toBeGreaterThan(0);
      expect(portfolioRisk.totalExposure.toNumber()).toBeGreaterThan(0);
      expect(portfolioRisk.correlations).toBeDefined();
    });

    it('should maintain real-time performance under continuous stress', async () => {
      const continuousStressPositions: Position[] = [
        {
          id: 'continuous-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        }
      ];

      const portfolioValue = new Decimal(100000);
      const stressDuration = 10000; // 10 seconds of continuous stress
      const updateInterval = 50; // Update every 50ms
      const expectedUpdates = stressDuration / updateInterval;

      let updateCount = 0;
      const responseTimes: number[] = [];
      
      const stressTest = new Promise<void>((resolve) => {
        const interval = setInterval(async () => {
          const updateStart = Date.now();
          
          // Simulate price update
          const priceMovement = (Math.random() - 0.5) * 0.01;
          const newPrice = new Decimal(1.2000).mul(1 + priceMovement);
          
          const updatedPositions = continuousStressPositions.map(pos => ({
            ...pos,
            currentPrice: newPrice,
            unrealizedPnl: pos.size.mul(newPrice.sub(pos.entryPrice))
          }));

          // Calculate risk
          await riskManager.calculatePortfolioRisk(updatedPositions, portfolioValue);
          
          const responseTime = Date.now() - updateStart;
          responseTimes.push(responseTime);
          updateCount++;

          if (updateCount >= expectedUpdates * 0.8) { // Allow for some variance
            clearInterval(interval);
            resolve();
          }
        }, updateInterval);
      });

      await stressTest;

      // Should maintain real-time performance
      const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
      const maxResponseTime = Math.max(...responseTimes);

      expect(averageResponseTime).toBeLessThan(25); // Average under 25ms
      expect(maxResponseTime).toBeLessThan(100); // No update over 100ms
      expect(updateCount).toBeGreaterThan(expectedUpdates * 0.7); // At least 70% of expected updates
    });
  });
});