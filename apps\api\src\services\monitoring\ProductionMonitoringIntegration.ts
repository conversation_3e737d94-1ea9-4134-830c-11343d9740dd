import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { SystemMonitoringService } from './SystemMonitoringService.js';
import { UserActivityMonitoringService } from './UserActivityMonitoringService.js';
import { PerformanceMonitoringService } from './PerformanceMonitoringService.js';
import { BrokerMonitoringService } from './BrokerMonitoringService.js';

// Types for production monitoring integration
interface ProductionMetrics {
  timestamp: Date;
  instanceId: string;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  activeConnections: number;
  requestsPerMinute: number;
  errorRate: number;
  responseTime: number;
  uptime: number;
}

interface AlertConfiguration {
  metric: string;
  threshold: number;
  operator: 'gt' | 'lt' | 'eq';
  severity: 'low' | 'medium' | 'high' | 'critical';
  cooldownPeriod: number; // minutes
}

interface HealthCheckEndpoint {
  name: string;
  url: string;
  expectedStatus: number;
  timeout: number;
  interval: number;
}

/**
 * Production Monitoring Integration Service
 * 
 * Integrates existing monitoring services with production deployment infrastructure
 * including Prometheus, Grafana, and external monitoring services
 */
export class ProductionMonitoringIntegration extends EventEmitter {
  private prisma: PrismaClient;
  private systemMonitoring: SystemMonitoringService;
  private userActivityMonitoring: UserActivityMonitoringService;
  private performanceMonitoring: PerformanceMonitoringService;
  private brokerMonitoring: BrokerMonitoringService;
  
  private instanceId: string;
  private isProduction: boolean;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private alertCooldowns: Map<string, Date> = new Map();
  
  // Configuration
  private readonly MONITORING_INTERVAL = 30 * 1000; // 30 seconds
  private readonly HEALTH_CHECK_INTERVAL = 60 * 1000; // 1 minute
  private readonly METRICS_RETENTION_HOURS = 48;
  
  // Health check endpoints for production deployment
  private readonly healthCheckEndpoints: HealthCheckEndpoint[] = [
    {
      name: 'api-loadbalancer',
      url: `${process.env.API_BASE_URL  }/health`,
      expectedStatus: 200,
      timeout: 5000,
      interval: 30000
    },
    {
      name: 'mt5-bridge-loadbalancer', 
      url: `${process.env.MT5_BASE_URL  }/health`,
      expectedStatus: 200,
      timeout: 5000,
      interval: 30000
    },
    {
      name: 'traefik-dashboard',
      url: `${process.env.TRAEFIK_DASHBOARD_URL  }/ping`,
      expectedStatus: 200,
      timeout: 5000,
      interval: 60000
    }
  ];
  
  // Alert configurations for production monitoring
  private readonly alertConfigurations: AlertConfiguration[] = [
    { metric: 'cpuUsage', threshold: 80, operator: 'gt', severity: 'high', cooldownPeriod: 5 },
    { metric: 'memoryUsage', threshold: 85, operator: 'gt', severity: 'high', cooldownPeriod: 5 },
    { metric: 'diskUsage', threshold: 90, operator: 'gt', severity: 'critical', cooldownPeriod: 15 },
    { metric: 'errorRate', threshold: 0.05, operator: 'gt', severity: 'high', cooldownPeriod: 2 },
    { metric: 'responseTime', threshold: 2000, operator: 'gt', severity: 'medium', cooldownPeriod: 3 },
    { metric: 'activeConnections', threshold: 1000, operator: 'gt', severity: 'medium', cooldownPeriod: 10 }
  ];

  constructor(
    prisma: PrismaClient,
    systemMonitoring: SystemMonitoringService,
    userActivityMonitoring: UserActivityMonitoringService,
    performanceMonitoring: PerformanceMonitoringService,
    brokerMonitoring: BrokerMonitoringService
  ) {
    super();
    
    this.prisma = prisma;
    this.systemMonitoring = systemMonitoring;
    this.userActivityMonitoring = userActivityMonitoring;
    this.performanceMonitoring = performanceMonitoring;
    this.brokerMonitoring = brokerMonitoring;
    
    this.instanceId = process.env.INSTANCE_ID || 'unknown';
    this.isProduction = process.env.NODE_ENV === 'production';
    
    this.setupEventListeners();
    this.emit('initialized', { instanceId: this.instanceId, isProduction: this.isProduction });
  }

  /**
   * Start production monitoring integration
   */
  public async startProductionMonitoring(): Promise<void> {
    try {
      // Start underlying monitoring services
      await this.systemMonitoring.startMonitoring({ intervalMs: this.MONITORING_INTERVAL });
      await this.performanceMonitoring.startMonitoring();
      
      // Start production-specific monitoring
      this.startMetricsCollection();
      this.startHealthChecks();
      this.startAlertSystem();
      
      this.emit('monitoringStarted', { 
        instanceId: this.instanceId, 
        timestamp: new Date() 
      });
      
      console.log(`[ProductionMonitoring] Started monitoring for instance ${this.instanceId}`);
      
    } catch (error) {
      this.emit('error', {
        error: 'Failed to start production monitoring',
        details: error.message,
        instanceId: this.instanceId
      });
      throw error;
    }
  }

  /**
   * Stop production monitoring integration
   */
  public async stopProductionMonitoring(): Promise<void> {
    try {
      // Stop intervals
      if (this.monitoringInterval) {
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = null;
      }
      
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }
      
      // Stop underlying monitoring services
      this.systemMonitoring.stopMonitoring();
      await this.performanceMonitoring.stopMonitoring();
      
      this.emit('monitoringStopped', { 
        instanceId: this.instanceId, 
        timestamp: new Date() 
      });
      
      console.log(`[ProductionMonitoring] Stopped monitoring for instance ${this.instanceId}`);
      
    } catch (error) {
      this.emit('error', {
        error: 'Failed to stop production monitoring',
        details: error.message,
        instanceId: this.instanceId
      });
    }
  }

  /**
   * Get current production metrics
   */
  public async getCurrentMetrics(): Promise<ProductionMetrics> {
    try {
      const systemMetrics = this.systemMonitoring.getCurrentMetrics();
      const performanceMetrics = await this.performanceMonitoring.getCurrentMetrics();
      
      return {
        timestamp: new Date(),
        instanceId: this.instanceId,
        cpuUsage: systemMetrics.cpuUsage,
        memoryUsage: systemMetrics.memoryUsage,
        diskUsage: systemMetrics.diskUsage,
        activeConnections: performanceMetrics.activeConnections || 0,
        requestsPerMinute: performanceMetrics.requestsPerMinute || 0,
        errorRate: performanceMetrics.errorRate || 0,
        responseTime: performanceMetrics.averageResponseTime || 0,
        uptime: process.uptime()
      };
      
    } catch (error) {
      this.emit('error', {
        error: 'Failed to get current metrics',
        details: error.message,
        instanceId: this.instanceId
      });
      throw error;
    }
  }

  /**
   * Get monitoring status
   */
  public getMonitoringStatus(): {
    isActive: boolean;
    instanceId: string;
    uptime: number;
    lastMetricsUpdate: Date | null;
    alertCount: number;
  } {
    return {
      isActive: this.monitoringInterval !== null,
      instanceId: this.instanceId,
      uptime: process.uptime(),
      lastMetricsUpdate: this.systemMonitoring.getMonitoringStatus().lastUpdate,
      alertCount: this.alertCooldowns.size
    };
  }

  /**
   * Setup event listeners for existing monitoring services
   */
  private setupEventListeners(): void {
    // System monitoring events
    this.systemMonitoring.on('metricsCollected', (metrics) => {
      this.handleSystemMetrics(metrics);
    });
    
    this.systemMonitoring.on('alert', (alert) => {
      this.handleSystemAlert(alert);
    });
    
    // Performance monitoring events
    this.performanceMonitoring.on('performanceAlert', (alert) => {
      this.handlePerformanceAlert(alert);
    });
    
    // Broker monitoring events
    this.brokerMonitoring.on('brokerAlert', (alert) => {
      this.handleBrokerAlert(alert);
    });
    
    // User activity events
    this.userActivityMonitoring.on('suspiciousActivity', (activity) => {
      this.handleSuspiciousActivity(activity);
    });
  }

  /**
   * Start metrics collection for production deployment
   */
  private startMetricsCollection(): void {
    this.monitoringInterval = setInterval(async () => {
      try {
        const metrics = await this.getCurrentMetrics();
        await this.storeProductionMetrics(metrics);
        await this.checkAlertConditions(metrics);
        
        // Emit metrics for external systems (Prometheus, etc.)
        this.emit('productionMetrics', metrics);
        
      } catch (error) {
        this.emit('error', {
          error: 'Metrics collection failed',
          details: error.message,
          instanceId: this.instanceId
        });
      }
    }, this.MONITORING_INTERVAL);
  }

  /**
   * Start health checks for production endpoints
   */
  private startHealthChecks(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        await this.performHealthChecks();
      } catch (error) {
        this.emit('error', {
          error: 'Health checks failed',
          details: error.message,
          instanceId: this.instanceId
        });
      }
    }, this.HEALTH_CHECK_INTERVAL);
  }

  /**
   * Start alert system for production monitoring
   */
  private startAlertSystem(): void {
    // Clean up old alert cooldowns every 5 minutes
    setInterval(() => {
      this.cleanupAlertCooldowns();
    }, 5 * 60 * 1000);
  }

  /**
   * Store production metrics in database
   */
  private async storeProductionMetrics(metrics: ProductionMetrics): Promise<void> {
    try {
      await this.prisma.systemMetrics.create({
        data: {
          instanceId: metrics.instanceId,
          cpuUsage: metrics.cpuUsage,
          memoryUsage: metrics.memoryUsage,
          diskUsage: metrics.diskUsage,
          activeConnections: metrics.activeConnections,
          requestsPerMinute: metrics.requestsPerMinute,
          errorRate: metrics.errorRate,
          responseTime: metrics.responseTime,
          uptime: metrics.uptime,
          timestamp: metrics.timestamp,
          createdAt: new Date()
        }
      });
      
      // Clean up old metrics (keep only last 48 hours in production)
      if (this.isProduction) {
        const cutoffDate = new Date();
        cutoffDate.setHours(cutoffDate.getHours() - this.METRICS_RETENTION_HOURS);
        
        await this.prisma.systemMetrics.deleteMany({
          where: {
            createdAt: {
              lt: cutoffDate
            }
          }
        });
      }
      
    } catch (error) {
      console.error('[ProductionMonitoring] Failed to store metrics:', error);
    }
  }

  /**
   * Perform health checks on production endpoints
   */
  private async performHealthChecks(): Promise<void> {
    for (const endpoint of this.healthCheckEndpoints) {
      try {
        const startTime = Date.now();
        
        const response = await fetch(endpoint.url, {
          method: 'GET',
          timeout: endpoint.timeout
        });
        
        const responseTime = Date.now() - startTime;
        const isHealthy = response.status === endpoint.expectedStatus;
        
        if (!isHealthy) {
          this.emit('healthCheckFailed', {
            endpoint: endpoint.name,
            url: endpoint.url,
            expectedStatus: endpoint.expectedStatus,
            actualStatus: response.status,
            responseTime,
            instanceId: this.instanceId
          });
        } else {
          this.emit('healthCheckPassed', {
            endpoint: endpoint.name,
            responseTime,
            instanceId: this.instanceId
          });
        }
        
        // Store health check result
        await this.storeHealthCheckResult(endpoint.name, isHealthy, responseTime);
        
      } catch (error) {
        this.emit('healthCheckError', {
          endpoint: endpoint.name,
          error: error.message,
          instanceId: this.instanceId
        });
        
        await this.storeHealthCheckResult(endpoint.name, false, null, error.message);
      }
    }
  }

  /**
   * Store health check results
   */
  private async storeHealthCheckResult(
    endpointName: string, 
    isHealthy: boolean, 
    responseTime: number | null,
    errorMessage?: string
  ): Promise<void> {
    try {
      await this.prisma.healthCheckLog.create({
        data: {
          endpointName,
          instanceId: this.instanceId,
          isHealthy,
          responseTime,
          errorMessage,
          timestamp: new Date(),
          createdAt: new Date()
        }
      });
    } catch (error) {
      console.error('[ProductionMonitoring] Failed to store health check result:', error);
    }
  }

  /**
   * Check alert conditions based on current metrics
   */
  private async checkAlertConditions(metrics: ProductionMetrics): Promise<void> {
    for (const alertConfig of this.alertConfigurations) {
      const metricValue = metrics[alertConfig.metric as keyof ProductionMetrics] as number;
      
      if (this.shouldTriggerAlert(alertConfig, metricValue)) {
        await this.triggerAlert(alertConfig, metricValue, metrics);
      }
    }
  }

  /**
   * Check if an alert should be triggered
   */
  private shouldTriggerAlert(config: AlertConfiguration, value: number): boolean {
    // Check cooldown period
    const cooldownKey = `${config.metric}_${config.severity}`;
    const lastAlert = this.alertCooldowns.get(cooldownKey);
    
    if (lastAlert) {
      const cooldownEndTime = new Date(lastAlert.getTime() + config.cooldownPeriod * 60 * 1000);
      if (new Date() < cooldownEndTime) {
        return false; // Still in cooldown
      }
    }
    
    // Check threshold condition
    switch (config.operator) {
      case 'gt':
        return value > config.threshold;
      case 'lt':
        return value < config.threshold;
      case 'eq':
        return value === config.threshold;
      default:
        return false;
    }
  }

  /**
   * Trigger an alert
   */
  private async triggerAlert(
    config: AlertConfiguration, 
    value: number, 
    metrics: ProductionMetrics
  ): Promise<void> {
    const cooldownKey = `${config.metric}_${config.severity}`;
    this.alertCooldowns.set(cooldownKey, new Date());
    
    const alert = {
      instanceId: this.instanceId,
      metric: config.metric,
      threshold: config.threshold,
      actualValue: value,
      severity: config.severity,
      timestamp: new Date(),
      context: metrics
    };
    
    // Store alert in database
    await this.prisma.alert.create({
      data: {
        instanceId: alert.instanceId,
        alertType: 'PRODUCTION_METRIC',
        severity: alert.severity.toUpperCase(),
        message: `${config.metric} threshold exceeded: ${value} > ${config.threshold}`,
        metadata: JSON.stringify(alert),
        createdAt: new Date()
      }
    });
    
    // Emit alert for external handlers (webhooks, email, etc.)
    this.emit('productionAlert', alert);
  }

  /**
   * Clean up expired alert cooldowns
   */
  private cleanupAlertCooldowns(): void {
    const now = new Date();
    const maxCooldown = Math.max(...this.alertConfigurations.map(c => c.cooldownPeriod));
    
    for (const [key, timestamp] of this.alertCooldowns.entries()) {
      const expiryTime = new Date(timestamp.getTime() + maxCooldown * 60 * 1000);
      if (now > expiryTime) {
        this.alertCooldowns.delete(key);
      }
    }
  }

  /**
   * Handle system metrics from SystemMonitoringService
   */
  private handleSystemMetrics(metrics: any): void {
    // Forward system metrics to production monitoring
    this.emit('systemMetricsReceived', {
      instanceId: this.instanceId,
      metrics,
      timestamp: new Date()
    });
  }

  /**
   * Handle system alerts from SystemMonitoringService
   */
  private handleSystemAlert(alert: any): void {
    this.emit('systemAlert', {
      instanceId: this.instanceId,
      alert,
      timestamp: new Date()
    });
  }

  /**
   * Handle performance alerts from PerformanceMonitoringService
   */
  private handlePerformanceAlert(alert: any): void {
    this.emit('performanceAlert', {
      instanceId: this.instanceId,
      alert,
      timestamp: new Date()
    });
  }

  /**
   * Handle broker alerts from BrokerMonitoringService
   */
  private handleBrokerAlert(alert: any): void {
    this.emit('brokerAlert', {
      instanceId: this.instanceId,
      alert,
      timestamp: new Date()
    });
  }

  /**
   * Handle suspicious activity from UserActivityMonitoringService
   */
  private handleSuspiciousActivity(activity: any): void {
    this.emit('suspiciousActivity', {
      instanceId: this.instanceId,
      activity,
      timestamp: new Date()
    });
  }

  /**
   * Get production monitoring statistics
   */
  public async getMonitoringStatistics(hours: number = 24): Promise<any> {
    const cutoffDate = new Date();
    cutoffDate.setHours(cutoffDate.getHours() - hours);
    
    const [metrics, alerts, healthChecks] = await Promise.all([
      this.prisma.systemMetrics.findMany({
        where: {
          instanceId: this.instanceId,
          createdAt: { gte: cutoffDate }
        },
        orderBy: { createdAt: 'desc' },
        take: 100
      }),
      
      this.prisma.alert.findMany({
        where: {
          instanceId: this.instanceId,
          createdAt: { gte: cutoffDate }
        },
        orderBy: { createdAt: 'desc' },
        take: 50
      }),
      
      this.prisma.healthCheckLog.findMany({
        where: {
          instanceId: this.instanceId,
          createdAt: { gte: cutoffDate }
        },
        orderBy: { createdAt: 'desc' },
        take: 100
      })
    ]);
    
    return {
      instanceId: this.instanceId,
      period: `${hours} hours`,
      metrics: {
        total: metrics.length,
        latest: metrics[0] || null,
        averages: this.calculateAverages(metrics)
      },
      alerts: {
        total: alerts.length,
        bySevenity: this.groupAlertsBySeverity(alerts),
        latest: alerts[0] || null
      },
      healthChecks: {
        total: healthChecks.length,
        successRate: this.calculateHealthCheckSuccessRate(healthChecks),
        endpoints: this.groupHealthChecksByEndpoint(healthChecks)
      }
    };
  }

  /**
   * Calculate metric averages
   */
  private calculateAverages(metrics: any[]): any {
    if (metrics.length === 0) return null;
    
    const totals = metrics.reduce((acc, metric) => ({
      cpuUsage: acc.cpuUsage + metric.cpuUsage,
      memoryUsage: acc.memoryUsage + metric.memoryUsage,
      diskUsage: acc.diskUsage + metric.diskUsage,
      responseTime: acc.responseTime + metric.responseTime,
      errorRate: acc.errorRate + metric.errorRate
    }), { cpuUsage: 0, memoryUsage: 0, diskUsage: 0, responseTime: 0, errorRate: 0 });
    
    const count = metrics.length;
    return {
      cpuUsage: totals.cpuUsage / count,
      memoryUsage: totals.memoryUsage / count,
      diskUsage: totals.diskUsage / count,
      responseTime: totals.responseTime / count,
      errorRate: totals.errorRate / count
    };
  }

  /**
   * Group alerts by severity
   */
  private groupAlertsBySeverity(alerts: any[]): any {
    return alerts.reduce((acc, alert) => {
      const severity = alert.severity.toLowerCase();
      acc[severity] = (acc[severity] || 0) + 1;
      return acc;
    }, {});
  }

  /**
   * Calculate health check success rate
   */
  private calculateHealthCheckSuccessRate(healthChecks: any[]): number {
    if (healthChecks.length === 0) return 0;
    
    const successful = healthChecks.filter(hc => hc.isHealthy).length;
    return (successful / healthChecks.length) * 100;
  }

  /**
   * Group health checks by endpoint
   */
  private groupHealthChecksByEndpoint(healthChecks: any[]): any {
    return healthChecks.reduce((acc, hc) => {
      if (!acc[hc.endpointName]) {
        acc[hc.endpointName] = { total: 0, successful: 0 };
      }
      acc[hc.endpointName].total++;
      if (hc.isHealthy) {
        acc[hc.endpointName].successful++;
      }
      return acc;
    }, {});
  }
}

export { ProductionMetrics, AlertConfiguration, HealthCheckEndpoint };