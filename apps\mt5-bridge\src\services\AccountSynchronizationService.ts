/**
 * Account Synchronization Service
 * 
 * Handles real-time account and position synchronization:
 * - Real-time account balance and position updates
 * - Account information synchronization with database persistence
 * - Position tracking with P&L calculation and risk monitoring
 */

import { EventEmitter } from 'events';
import type { StandardBrokerAdapter, AccountInfo, PositionInfo } from '../adapters/StandardBrokerAdapter';

export interface SyncConfig {
  syncInterval: number;
  enableRealTime: boolean;
  persistToDB: boolean;
  riskMonitoring: boolean;
}

export class AccountSynchronizationService extends EventEmitter {
  private accounts: Map<string, AccountInfo> = new Map();
  private positions: Map<string, PositionInfo[]> = new Map();
  private syncInterval: NodeJS.Timeout | null = null;

  constructor(private config: SyncConfig = {
    syncInterval: 5000,
    enableRealTime: true,
    persistToDB: false,
    riskMonitoring: true
  }) {
    super();
    this.startSynchronization();
  }

  async syncAccount(adapter: StandardBrokerAdapter): Promise<AccountInfo> {
    try {
      const accountInfo = await adapter.getAccountInfo();
      const brokerId = adapter.getConfig().id;
      
      this.accounts.set(brokerId, accountInfo);
      this.emit('accountUpdated', { brokerId, accountInfo });
      
      return accountInfo;
    } catch (error) {
      console.error('Account sync failed:', error);
      throw error;
    }
  }

  async syncPositions(adapter: StandardBrokerAdapter): Promise<PositionInfo[]> {
    try {
      const positions = await adapter.getPositions();
      const brokerId = adapter.getConfig().id;
      
      this.positions.set(brokerId, positions);
      this.emit('positionsUpdated', { brokerId, positions });
      
      return positions;
    } catch (error) {
      console.error('Positions sync failed:', error);
      throw error;
    }
  }

  getAccountInfo(brokerId: string): AccountInfo | null {
    return this.accounts.get(brokerId) || null;
  }

  getPositions(brokerId: string): PositionInfo[] {
    return this.positions.get(brokerId) || [];
  }

  private startSynchronization(): void {
    if (this.syncInterval) {
      this.syncInterval = setInterval(() => {
        this.emit('syncRequired');
      }, this.config.syncInterval);
    }
  }

  async shutdown(): Promise<void> {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
  }
}