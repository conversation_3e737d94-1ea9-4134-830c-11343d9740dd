import { NextRequest, NextResponse } from 'next/server';
import { ApiResponse } from '@golddaddy/types';
import { 
  ProgressionHistoryEntry,
  ProgressionAnalytics,
  ProgressionTrend,
  ComparisonMetrics 
} from '@golddaddy/types/confidence';

// Mock historical progression data
const mockProgressionHistory: Record<string, ProgressionHistoryEntry[]> = {
  'demo-user': [
    {
      id: 'history-1',
      userId: 'demo-user',
      timestamp: new Date('2025-01-05'),
      event: 'onboarding_completed',
      stage: 'goal_setting',
      progressBefore: 0,
      progressAfter: 100,
      confidenceScore: 45,
      timeSpentMinutes: 30,
      milestoneId: null,
      metadata: {
        onboardingType: 'guided',
        userChoices: {
          experience: 'beginner',
          goals: 'learn_basics',
          timeCommitment: 'part_time'
        }
      }
    },
    {
      id: 'history-2',
      userId: 'demo-user',
      timestamp: new Date('2025-01-10'),
      event: 'milestone_completed',
      stage: 'goal_setting',
      progressBefore: 80,
      progressAfter: 100,
      confidenceScore: 65,
      timeSpentMinutes: 45,
      milestoneId: 'goal-setting-complete',
      metadata: {
        milestoneType: 'knowledge',
        difficulty: 'easy',
        attempts: 1,
        score: 92
      }
    },
    {
      id: 'history-3',
      userId: 'demo-user',
      timestamp: new Date('2025-01-15'),
      event: 'stage_advancement',
      stage: 'strategy_discovery',
      progressBefore: 0,
      progressAfter: 25,
      confidenceScore: 72,
      timeSpentMinutes: 60,
      milestoneId: null,
      metadata: {
        fromStage: 'goal_setting',
        toStage: 'strategy_discovery',
        autoAdvancement: false
      }
    },
    {
      id: 'history-4',
      userId: 'demo-user',
      timestamp: new Date('2025-01-20'),
      event: 'milestone_completed',
      stage: 'strategy_discovery',
      progressBefore: 90,
      progressAfter: 100,
      confidenceScore: 78,
      timeSpentMinutes: 75,
      milestoneId: 'strategy-basics',
      metadata: {
        milestoneType: 'skill',
        difficulty: 'medium',
        attempts: 2,
        score: 85
      }
    },
    {
      id: 'history-5',
      userId: 'demo-user',
      timestamp: new Date('2025-01-22'),
      event: 'stage_advancement',
      stage: 'backtesting_adventure',
      progressBefore: 0,
      progressAfter: 30,
      confidenceScore: 82,
      timeSpentMinutes: 90,
      milestoneId: null,
      metadata: {
        fromStage: 'strategy_discovery',
        toStage: 'backtesting_adventure',
        autoAdvancement: false
      }
    },
    {
      id: 'history-6',
      userId: 'demo-user',
      timestamp: new Date('2025-01-25'),
      event: 'progress_update',
      stage: 'backtesting_adventure',
      progressBefore: 30,
      progressAfter: 65,
      confidenceScore: 85,
      timeSpentMinutes: 120,
      milestoneId: null,
      metadata: {
        updateType: 'learning_activity',
        activitiesCompleted: 3,
        skillsImproved: ['backtesting', 'data_analysis']
      }
    }
  ]
};

// Mock comparison data for analytics
const mockComparisonData = {
  peerAverage: {
    averageTimeToCurrentStage: 18, // days
    averageConfidenceScore: 76,
    completionRate: 78,
  },
  userTarget: {
    targetTimeToComplete: 30, // days
    targetConfidenceScore: 90,
    personalGoals: ['consistent_learning', 'milestone_completion']
  }
};

export async function GET(
  request: NextRequest
): Promise<NextResponse<ApiResponse<ProgressionAnalytics>>> {
  try {
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';
    const timeframe = searchParams.get('timeframe') || '30d'; // 7d, 30d, 90d, all
    const includeComparison = searchParams.get('comparison') === 'true';

    const userHistory = mockProgressionHistory[userId] || [];

    // Filter by timeframe
    let filteredHistory = userHistory;
    if (timeframe !== 'all') {
      const days = parseInt(timeframe.replace('d', ''));
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      
      filteredHistory = userHistory.filter(entry => 
        entry.timestamp >= cutoffDate
      );
    }

    // Calculate analytics
    const analytics = calculateAnalytics(filteredHistory, includeComparison);

    return NextResponse.json({
      success: true,
      data: analytics,
      message: 'Progress history retrieved successfully',
    });
  } catch (error) {
    console.error('Error fetching progress history:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch progress history',
      },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest
): Promise<NextResponse<ApiResponse<{ exported: boolean; downloadUrl?: string }>>> {
  try {
    const body = await request.json();
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || 'demo-user';

    const { format = 'json', includeDetails = true } = body;

    if (!['json', 'csv', 'pdf'].includes(format)) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid export format. Supported formats: json, csv, pdf',
        },
        { status: 400 }
      );
    }

    const userHistory = mockProgressionHistory[userId] || [];

    // In production: Generate actual file and store in cloud storage
    const exportData = {
      userId,
      exportedAt: new Date(),
      format,
      totalEntries: userHistory.length,
      data: includeDetails ? userHistory : userHistory.map(entry => ({
        timestamp: entry.timestamp,
        event: entry.event,
        stage: entry.stage,
        confidenceScore: entry.confidenceScore,
      })),
    };

    // Mock download URL - in production, this would be a real file URL
    const downloadUrl = `https://api.golddaddy.com/exports/${userId}/progression-${Date.now()}.${format}`;

    return NextResponse.json({
      success: true,
      data: {
        exported: true,
        downloadUrl,
      },
      message: `Progress report exported successfully as ${format.toUpperCase()}`,
    });
  } catch (error) {
    console.error('Error exporting progress history:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to export progress history',
      },
      { status: 500 }
    );
  }
}

function calculateAnalytics(history: ProgressionHistoryEntry[], includeComparison: boolean): ProgressionAnalytics {
  if (history.length === 0) {
    return {
      summary: {
        totalTime: 0,
        milestonesCompleted: 0,
        averageConfidenceGrowth: 0,
        stagesCompleted: 0,
        currentStreak: 0,
        longestStreak: 0,
      },
      trends: [],
      insights: ['Start your learning journey to see analytics'],
      comparison: includeComparison ? mockComparisonData : undefined,
    };
  }

  // Calculate summary statistics
  const totalTime = history.reduce((sum, entry) => sum + entry.timeSpentMinutes, 0);
  const milestonesCompleted = history.filter(entry => entry.event === 'milestone_completed').length;
  const stageAdvancements = history.filter(entry => entry.event === 'stage_advancement').length;
  
  const confidenceScores = history.map(entry => entry.confidenceScore).filter(Boolean);
  const averageConfidenceGrowth = confidenceScores.length > 1 
    ? confidenceScores[confidenceScores.length - 1] - confidenceScores[0]
    : 0;

  // Calculate streaks (consecutive days with activity)
  const activityDays = [...new Set(history.map(entry => 
    entry.timestamp.toISOString().split('T')[0]
  ))].sort();

  let currentStreak = 0;
  let longestStreak = 0;
  let tempStreak = 1;

  for (let i = 1; i < activityDays.length; i++) {
    const prevDate = new Date(activityDays[i - 1]);
    const currDate = new Date(activityDays[i]);
    const dayDiff = Math.floor((currDate.getTime() - prevDate.getTime()) / (1000 * 60 * 60 * 24));

    if (dayDiff === 1) {
      tempStreak++;
    } else {
      longestStreak = Math.max(longestStreak, tempStreak);
      tempStreak = 1;
    }
  }
  
  longestStreak = Math.max(longestStreak, tempStreak);
  
  // Current streak (days from most recent activity to today)
  if (activityDays.length > 0) {
    const lastActivity = new Date(activityDays[activityDays.length - 1]);
    const today = new Date();
    const daysSinceLastActivity = Math.floor((today.getTime() - lastActivity.getTime()) / (1000 * 60 * 60 * 24));
    currentStreak = daysSinceLastActivity <= 1 ? tempStreak : 0;
  }

  // Generate trends
  const trends: ProgressionTrend[] = [
    {
      metric: 'confidence_score',
      label: 'Confidence Growth',
      data: history.map(entry => ({
        timestamp: entry.timestamp,
        value: entry.confidenceScore,
      })),
      trend: averageConfidenceGrowth > 0 ? 'increasing' : 'stable',
      changePercentage: Math.abs(averageConfidenceGrowth),
    },
    {
      metric: 'time_spent',
      label: 'Time Investment',
      data: aggregateTimeSpentByDay(history),
      trend: 'stable',
      changePercentage: 0,
    }
  ];

  // Generate insights
  const insights = generateInsights({
    totalTime,
    milestonesCompleted,
    averageConfidenceGrowth,
    currentStreak,
    longestStreak,
  });

  return {
    summary: {
      totalTime,
      milestonesCompleted,
      averageConfidenceGrowth: Math.round(averageConfidenceGrowth),
      stagesCompleted: stageAdvancements,
      currentStreak,
      longestStreak,
    },
    trends,
    insights,
    comparison: includeComparison ? mockComparisonData : undefined,
  };
}

function aggregateTimeSpentByDay(history: ProgressionHistoryEntry[]): Array<{ timestamp: Date; value: number }> {
  const dailyTime: Record<string, number> = {};

  history.forEach(entry => {
    const day = entry.timestamp.toISOString().split('T')[0];
    dailyTime[day] = (dailyTime[day] || 0) + entry.timeSpentMinutes;
  });

  return Object.entries(dailyTime).map(([day, minutes]) => ({
    timestamp: new Date(day),
    value: minutes,
  })).sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
}

function generateInsights(summary: any): string[] {
  const insights: string[] = [];

  if (summary.totalTime > 300) { // 5+ hours
    insights.push(`You've invested ${Math.round(summary.totalTime / 60)} hours in your trading education - excellent dedication!`);
  }

  if (summary.currentStreak >= 7) {
    insights.push(`Amazing! You're on a ${summary.currentStreak}-day learning streak. Consistency is key to success.`);
  } else if (summary.currentStreak === 0) {
    insights.push("Consider setting a daily learning goal to maintain momentum in your trading journey.");
  }

  if (summary.averageConfidenceGrowth > 20) {
    insights.push(`Your confidence has grown by ${summary.averageConfidenceGrowth} points - you're making real progress!`);
  }

  if (summary.milestonesCompleted >= 3) {
    insights.push(`With ${summary.milestonesCompleted} milestones completed, you're building solid trading foundations.`);
  }

  if (summary.longestStreak > summary.currentStreak && summary.currentStreak === 0) {
    insights.push(`Your longest streak was ${summary.longestStreak} days. You can achieve that consistency again!`);
  }

  return insights.length > 0 ? insights : ['Keep learning and tracking your progress to unlock insights!'];
}