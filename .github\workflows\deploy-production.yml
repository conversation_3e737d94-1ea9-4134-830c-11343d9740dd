# GitHub Actions CI/CD Pipeline for GoldDaddy Trading Platform
name: Production Deployment

on:
  push:
    branches: [main]
    tags: ['v*']
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Test Job
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [20.x]

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Setup test database
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
        run: |
          cd apps/api
          npx prisma migrate deploy
          npx prisma generate

      - name: Run linting
        run: npm run lint

      - name: Run type checking
        run: npm run type-check

      - name: Run unit tests
        run: npm test -- --coverage
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db

      - name: Run integration tests
        run: npm run test:integration
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          NODE_ENV: test

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          fail_ci_if_error: false

  # Security Scan Job
  security:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-results.sarif'

  # Deploy to Staging Environment
  staging:
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.event_name == 'pull_request'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build frontend for staging
        run: npm run build --workspace=@golddaddy/web
        env:
          NODE_ENV: staging
          NEXT_PUBLIC_API_URL: ${{ secrets.STAGING_API_URL }}
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.STAGING_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.STAGING_SUPABASE_ANON_KEY }}

      - name: Deploy to Vercel Staging
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-args: '--prod --env production'
          working-directory: apps/web

      - name: Deploy staging containers
        run: |
          echo "${{ secrets.STAGING_SSH_KEY }}" > staging_key
          chmod 600 staging_key
          
          ssh -o StrictHostKeyChecking=no -i staging_key ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }} << 'EOF'
            cd /opt/golddaddy-staging
            git pull origin ${{ github.head_ref }}
            docker-compose -f docker-compose.staging.yml up -d --build
            
            # Wait for services to start
            sleep 60
            
            # Run health checks
            docker-compose -f docker-compose.staging.yml exec -T golddaddy-api curl -f http://localhost:3001/api/monitoring/health
          EOF

      - name: Run E2E tests on staging
        run: |
          npm run test:e2e
        env:
          BASE_URL: ${{ secrets.STAGING_BASE_URL }}
          API_URL: ${{ secrets.STAGING_API_URL }}

  # Build and Push Docker Image
  build:
    runs-on: ubuntu-latest
    needs: [test, security]
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v')
    
    outputs:
      image-tag: ${{ steps.meta.outputs.tags }}
      image-digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,prefix={{branch}}-

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile.production
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

  # Production Deployment Job
  deploy:
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build frontend for production
        run: npm run build --workspace=@golddaddy/web
        env:
          NODE_ENV: production
          NEXT_PUBLIC_API_URL: ${{ secrets.PRODUCTION_API_URL }}
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.PRODUCTION_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.PRODUCTION_SUPABASE_ANON_KEY }}

      - name: Deploy to Vercel Production
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-args: '--prod'
          working-directory: apps/web

      - name: Create deployment backup point
        run: |
          BACKUP_TAG="backup-$(date +%Y%m%d-%H%M%S)"
          echo "BACKUP_TAG=$BACKUP_TAG" >> $GITHUB_ENV
          git tag $BACKUP_TAG
          git push origin $BACKUP_TAG

      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Deploy backend services to production
        run: |
          ssh -o StrictHostKeyChecking=no ${{ secrets.PRODUCTION_USER }}@${{ secrets.PRODUCTION_HOST }} << 'EOF'
            cd /opt/golddaddy-api
            
            # Create backup of current deployment
            BACKUP_DIR="backup-$(date +%Y%m%d-%H%M%S)"
            mkdir -p backups/$BACKUP_DIR
            docker-compose -f docker-compose.production.yml config > backups/$BACKUP_DIR/docker-compose.backup.yml
            cp .env.production backups/$BACKUP_DIR/
            
            # Pull latest code
            git fetch --all
            git checkout main
            git pull origin main
            
            # Update environment variables
            echo "${{ secrets.PRODUCTION_ENV }}" > .env.production
            
            # Deploy with zero-downtime strategy
            docker-compose -f docker-compose.production.yml pull
            docker-compose -f docker-compose.production.yml up -d --build --scale golddaddy-api=2
            
            # Health check with timeout
            for i in {1..30}; do
              if curl -f http://localhost:3001/api/monitoring/health; then
                echo "Health check passed"
                break
              fi
              echo "Waiting for services to be ready... ($i/30)"
              sleep 10
            done
            
            # Scale down old containers
            docker-compose -f docker-compose.production.yml up -d --scale golddaddy-api=1
            
            # Final verification
            curl -f http://localhost:3001/api/monitoring/health || exit 1
          EOF

      - name: Notify deployment status
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow

  # Smoke Tests Post-Deployment
  smoke-tests:
    runs-on: ubuntu-latest
    needs: deploy
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run smoke tests
        run: |
          # API Health Check
          curl -f ${{ secrets.PRODUCTION_API_URL }}/health
          
          # Broker failover system check
          curl -f ${{ secrets.PRODUCTION_API_URL }}/api/monitoring/health
          
          # Database connectivity check
          curl -f ${{ secrets.PRODUCTION_API_URL }}/api/monitoring/database-health

      - name: Run load test
        run: |
          # Install k6 for load testing
          sudo apt-key adv --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
          
          # Run basic load test
          k6 run --vus 10 --duration 30s - <<EOF
          import http from 'k6/http';
          import { check } from 'k6';
          
          export default function () {
            let response = http.get('${{ secrets.PRODUCTION_API_URL }}/health');
            check(response, {
              'status is 200': (r) => r.status === 200,
              'response time < 500ms': (r) => r.timings.duration < 500,
            });
          }
          EOF

  # Cleanup Job
  cleanup:
    runs-on: ubuntu-latest
    needs: [deploy, smoke-tests]
    if: always() && github.ref == 'refs/heads/main'
    
    steps:
      - name: Cleanup old Docker images
        run: |
          # This would typically connect to your production server
          # and clean up old Docker images to free up space
          echo "Cleanup completed"

# Workflow notifications
on_failure:
  runs-on: ubuntu-latest
  needs: [test, security, build, deploy, smoke-tests]
  if: failure()
  
  steps:
    - name: Notify on failure
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        channel: '#alerts'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow