-- CreateEnum for Quiz Categories
CREATE TYPE "QuizCategory" AS ENUM ('trading_fundamentals', 'risk_management', 'platform_features', 'safety_procedures', 'regulatory_compliance', 'market_analysis', 'psychology_discipline');

-- CreateEnum for Quiz Difficulty  
CREATE TYPE "QuizDifficulty" AS ENUM ('beginner', 'intermediate', 'advanced', 'expert');

-- CreateEnum for Quiz Type
CREATE TYPE "QuizType" AS ENUM ('stage_advancement', 'knowledge_refresh', 'remedial_learning', 'certification');

-- CreateEnum for Quiz Session Status
CREATE TYPE "QuizSessionStatus" AS ENUM ('not_started', 'in_progress', 'paused', 'completed', 'expired', 'cancelled');

-- CreateEnum for Quiz Attempt Status
CREATE TYPE "QuizAttemptStatus" AS ENUM ('in_progress', 'completed', 'failed', 'timed_out', 'cancelled');

-- CreateTable Quiz Questions
CREATE TABLE "quiz_questions" (
    "id" TEXT NOT NULL,
    "category" "QuizCategory" NOT NULL,
    "difficulty" "QuizDifficulty" NOT NULL,
    "topic" TEXT NOT NULL,
    "question" TEXT NOT NULL,
    "options" JSONB NOT NULL,
    "correct_answer_ids" TEXT[],
    "explanation" TEXT NOT NULL,
    "learning_resources" JSONB,
    "metadata" JSONB NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "quiz_questions_pkey" PRIMARY KEY ("id")
);

-- CreateTable Quiz Sessions
CREATE TABLE "quiz_sessions" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "quiz_type" "QuizType" NOT NULL,
    "stage" "ConfidenceStage" NOT NULL,
    "question_ids" TEXT[],
    "metadata" JSONB NOT NULL,
    "status" "QuizSessionStatus" NOT NULL DEFAULT 'not_started',
    "started_at" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "expires_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "quiz_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable Quiz Responses
CREATE TABLE "quiz_responses" (
    "id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,
    "question_id" TEXT NOT NULL,
    "selected_option_ids" TEXT[],
    "is_correct" BOOLEAN NOT NULL,
    "time_spent" INTEGER NOT NULL,
    "confidence_level" INTEGER NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "quiz_responses_pkey" PRIMARY KEY ("id")
);

-- CreateTable Quiz Attempts
CREATE TABLE "quiz_attempts" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,
    "attempt_number" INTEGER NOT NULL,
    "score" INTEGER NOT NULL,
    "total_time_spent" INTEGER NOT NULL,
    "questions_answered" INTEGER NOT NULL,
    "correct_answers" INTEGER NOT NULL,
    "weak_areas" TEXT[],
    "strong_areas" TEXT[],
    "confidence_score" INTEGER NOT NULL,
    "feedback" JSONB NOT NULL,
    "status" "QuizAttemptStatus" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completed_at" TIMESTAMP(3),

    CONSTRAINT "quiz_attempts_pkey" PRIMARY KEY ("id")
);

-- CreateTable Learning Plans
CREATE TABLE "learning_plans" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "topics" TEXT[],
    "resources" JSONB NOT NULL,
    "estimated_duration" INTEGER NOT NULL,
    "checkpoints" TEXT[],
    "progress" JSONB,
    "status" TEXT NOT NULL DEFAULT 'active',
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "completed_at" TIMESTAMP(3),

    CONSTRAINT "learning_plans_pkey" PRIMARY KEY ("id")
);

-- Enhanced ConfidenceAssessment table (extend existing)
ALTER TABLE "confidence_assessments" 
ADD COLUMN "assessment_scores" JSONB,
ADD COLUMN "progress_history" JSONB,
ADD COLUMN "graduation_criteria" JSONB;

-- Update existing ConfidenceAssessment data structure
UPDATE "confidence_assessments" SET 
"assessment_scores" = jsonb_build_object(
  'knowledgeQuiz', jsonb_build_object(
    'score', COALESCE("knowledgeQuizScore", 0),
    'completedAt', "updated_at",
    'attempts', 0,
    'weakAreas', '[]'::jsonb
  ),
  'behavioralAssessment', jsonb_build_object(
    'riskTolerance', COALESCE("behavioralScore", 0),
    'decisionConsistency', COALESCE("behavioralScore", 0),
    'emotionalStability', COALESCE("behavioralScore", 0),
    'lastAssessed', "updated_at"
  ),
  'performanceEvaluation', jsonb_build_object(
    'paperTradingWinRate', COALESCE("performanceScore", 0),
    'riskManagementScore', COALESCE("performanceScore", 0),
    'strategyAdherence', COALESCE("performanceScore", 0),
    'consistencyRating', COALESCE("performanceScore", 0)
  )
),
"progress_history" = COALESCE("progressHistory", '[]'::jsonb),
"graduation_criteria" = COALESCE("graduationCriteria", jsonb_build_object(
  'nextStage', 'strategy_learning',
  'requirements', jsonb_build_object(
    'minimumConfidenceScore', 70,
    'requiredAssessments', '["knowledgeQuiz"]'::jsonb,
    'minimumTimeInStage', 7
  )
));

-- Create Foreign Key Constraints
ALTER TABLE "quiz_sessions" ADD CONSTRAINT "quiz_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "quiz_responses" ADD CONSTRAINT "quiz_responses_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "quiz_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "quiz_responses" ADD CONSTRAINT "quiz_responses_question_id_fkey" FOREIGN KEY ("question_id") REFERENCES "quiz_questions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "quiz_attempts" ADD CONSTRAINT "quiz_attempts_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE "quiz_attempts" ADD CONSTRAINT "quiz_attempts_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "quiz_sessions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

ALTER TABLE "learning_plans" ADD CONSTRAINT "learning_plans_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Create Indexes for Performance
CREATE INDEX "quiz_questions_category_difficulty_idx" ON "quiz_questions"("category", "difficulty");
CREATE INDEX "quiz_questions_topic_idx" ON "quiz_questions"("topic");
CREATE INDEX "quiz_questions_is_active_idx" ON "quiz_questions"("is_active");

CREATE INDEX "quiz_sessions_user_id_status_idx" ON "quiz_sessions"("user_id", "status");
CREATE INDEX "quiz_sessions_stage_idx" ON "quiz_sessions"("stage");
CREATE INDEX "quiz_sessions_created_at_idx" ON "quiz_sessions"("created_at");

CREATE INDEX "quiz_responses_session_id_idx" ON "quiz_responses"("session_id");
CREATE INDEX "quiz_responses_question_id_idx" ON "quiz_responses"("question_id");

CREATE INDEX "quiz_attempts_user_id_idx" ON "quiz_attempts"("user_id");
CREATE INDEX "quiz_attempts_attempt_number_idx" ON "quiz_attempts"("user_id", "attempt_number");
CREATE INDEX "quiz_attempts_score_idx" ON "quiz_attempts"("score");
CREATE INDEX "quiz_attempts_created_at_idx" ON "quiz_attempts"("created_at");

CREATE INDEX "learning_plans_user_id_status_idx" ON "learning_plans"("user_id", "status");

-- Row Level Security Policies for User Data Isolation

-- Enable RLS on quiz tables
ALTER TABLE "quiz_sessions" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "quiz_responses" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "quiz_attempts" ENABLE ROW LEVEL SECURITY;
ALTER TABLE "learning_plans" ENABLE ROW LEVEL SECURITY;

-- Quiz Sessions RLS Policy
CREATE POLICY "Users can only access their own quiz sessions" ON "quiz_sessions"
  FOR ALL TO authenticated
  USING (auth.uid()::text = user_id);

-- Quiz Responses RLS Policy  
CREATE POLICY "Users can only access their own quiz responses" ON "quiz_responses"
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM "quiz_sessions" 
      WHERE "quiz_sessions"."id" = "quiz_responses"."session_id" 
      AND "quiz_sessions"."user_id" = auth.uid()::text
    )
  );

-- Quiz Attempts RLS Policy
CREATE POLICY "Users can only access their own quiz attempts" ON "quiz_attempts"
  FOR ALL TO authenticated
  USING (auth.uid()::text = user_id);

-- Learning Plans RLS Policy
CREATE POLICY "Users can only access their own learning plans" ON "learning_plans"
  FOR ALL TO authenticated
  USING (auth.uid()::text = user_id);

-- Quiz Questions are readable by all authenticated users but not modifiable
CREATE POLICY "Authenticated users can read quiz questions" ON "quiz_questions"
  FOR SELECT TO authenticated
  USING (is_active = true);

-- Admin-only policy for quiz question management (placeholder for admin role)
CREATE POLICY "Admin can manage quiz questions" ON "quiz_questions"
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM "users" 
      WHERE "users"."id" = auth.uid()::text 
      AND "users"."email" LIKE '%@golddaddy.%'
    )
  );