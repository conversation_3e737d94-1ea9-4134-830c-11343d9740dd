#!/usr/bin/env python3
"""
MT5 Bridge Python Service Setup Script
Handles virtual environment setup and dependency installation
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def run_command(command: str, description: str) -> bool:
    """Run a shell command and return success status"""
    print(f"[INFO] {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"[SUCCESS] {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] {description} failed: {e.stderr}")
        return False

def setup_python_environment():
    """Setup Python virtual environment for MT5 integration"""
    print("Setting up MT5 Bridge Python Environment")
    
    # Check Python version
    python_version = sys.version_info
    if python_version.major != 3 or python_version.minor < 8:
        print("[ERROR] Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"[SUCCESS] Python {python_version.major}.{python_version.minor}.{python_version.micro} detected")
    
    # Create virtual environment
    venv_path = Path("venv")
    if not venv_path.exists():
        if not run_command(f"{sys.executable} -m venv venv", "Creating virtual environment"):
            sys.exit(1)
    else:
        print("[INFO] Virtual environment already exists")
    
    # Determine activation script based on OS
    if platform.system() == "Windows":
        activate_script = "venv\\Scripts\\activate.bat"
        pip_command = "venv\\Scripts\\pip"
        python_command = "venv\\Scripts\\python"
    else:
        activate_script = "venv/bin/activate"
        pip_command = "venv/bin/pip"
        python_command = "venv/bin/python"
    
    # Install dependencies
    if not run_command(f"{pip_command} install --upgrade pip", "Upgrading pip"):
        sys.exit(1)
    
    if not run_command(f"{pip_command} install -r requirements.txt", "Installing dependencies"):
        sys.exit(1)
    
    # Create MT5 connection test script
    test_script = """
import MetaTrader5 as mt5
from datetime import datetime

def test_mt5_connection():
    \"\"\"Test MT5 connection capability\"\"\"
    print("[INFO] Testing MT5 connection...")
    
    # Initialize MT5 connection
    if not mt5.initialize():
        print("[ERROR] MT5 initialization failed")
        print("Error:", mt5.last_error())
        return False
    
    # Get MT5 version info
    version = mt5.version()
    print(f"[SUCCESS] MT5 version: {version}")
    
    # Get account info (if connected)
    account_info = mt5.account_info()
    if account_info:
        print(f"[SUCCESS] Connected to account: {account_info.login}")
        print(f"   Server: {account_info.server}")
        print(f"   Company: {account_info.company}")
    else:
        print("[INFO] No MT5 account connected (demo mode)")
    
    # Test symbol info
    symbols = mt5.symbols_get()
    if symbols:
        print(f"[SUCCESS] Available symbols: {len(symbols)}")
        print(f"   Sample symbols: {[s.name for s in symbols[:5]]}")
    
    mt5.shutdown()
    print("[SUCCESS] MT5 connection test completed")
    return True

if __name__ == "__main__":
    test_mt5_connection()
"""
    
    with open("test_mt5_connection.py", "w") as f:
        f.write(test_script)
    
    print("\n[SUCCESS] MT5 Python environment setup completed!")
    print(f"[INFO] To activate the environment:")
    if platform.system() == "Windows":
        print(f"   {activate_script}")
    else:
        print(f"   source {activate_script}")
    print(f"[INFO] To test MT5 connection:")
    print(f"   {python_command} test_mt5_connection.py")

if __name__ == "__main__":
    setup_python_environment()