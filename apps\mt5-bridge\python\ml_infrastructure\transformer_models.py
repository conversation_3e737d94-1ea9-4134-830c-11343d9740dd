"""
Transformer Models for Financial Market Analysis
Implements various transformer architectures for trading strategy optimization
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from transformers import (
    AutoModel, AutoTokenizer, AutoConfig,
    TrainingArguments, Trainer
)
import math
from loguru import logger

@dataclass
class ModelConfig:
    """Configuration for transformer models"""
    sequence_length: int = 128
    feature_dim: int = 64
    num_heads: int = 8
    num_layers: int = 6
    hidden_dim: int = 512
    dropout: float = 0.1
    learning_rate: float = 1e-4
    batch_size: int = 32
    num_epochs: int = 100
    warmup_steps: int = 1000
    weight_decay: float = 0.01

class PositionalEncoding(nn.Module):
    """Positional encoding for transformer models"""
    
    def __init__(self, d_model: int, max_len: int = 5000):
        super().__init__()
        
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        return x + self.pe[:x.size(0), :]

class MarketTransformer(nn.Module):
    """
    Transformer model for market prediction and strategy optimization
    """
    
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.config = config
        
        # Input projection
        self.input_projection = nn.Linear(config.feature_dim, config.hidden_dim)
        
        # Positional encoding
        self.pos_encoder = PositionalEncoding(config.hidden_dim, config.sequence_length)
        
        # Transformer encoder
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=config.hidden_dim,
            nhead=config.num_heads,
            dim_feedforward=config.hidden_dim * 4,
            dropout=config.dropout,
            batch_first=True
        )
        self.transformer_encoder = nn.TransformerEncoder(
            encoder_layer, 
            num_layers=config.num_layers
        )
        
        # Output heads for different tasks
        self.price_prediction_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, 1)  # Price change prediction
        )
        
        self.direction_prediction_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, 3)  # Up, Down, Sideways
        )
        
        self.volatility_prediction_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, 1)  # Volatility prediction
        )
        
        self.strategy_score_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, 1)  # Strategy performance score
        )
        
        self.dropout = nn.Dropout(config.dropout)
        
    def forward(self, x: torch.Tensor, mask: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Forward pass
        Args:
            x: Input tensor of shape (batch_size, sequence_length, feature_dim)
            mask: Optional attention mask
        Returns:
            Dictionary with prediction outputs
        """
        # Input projection
        x = self.input_projection(x)  # (batch_size, seq_len, hidden_dim)
        
        # Add positional encoding
        x = self.pos_encoder(x.transpose(0, 1)).transpose(0, 1)
        
        # Apply dropout
        x = self.dropout(x)
        
        # Transformer encoding
        encoded = self.transformer_encoder(x, src_key_padding_mask=mask)
        
        # Use the last token for predictions (or mean pooling)
        if mask is not None:
            # Mean pooling over non-masked tokens
            mask_expanded = mask.unsqueeze(-1).expand(encoded.size())
            sum_embeddings = torch.sum(encoded * ~mask_expanded, dim=1)
            sum_mask = torch.sum(~mask, dim=1, keepdim=True)
            pooled = sum_embeddings / sum_mask
        else:
            # Use last token
            pooled = encoded[:, -1, :]  # (batch_size, hidden_dim)
        
        # Generate predictions
        outputs = {
            'price_change': self.price_prediction_head(pooled),
            'direction': self.direction_prediction_head(pooled),
            'volatility': self.volatility_prediction_head(pooled),
            'strategy_score': self.strategy_score_head(pooled),
            'embeddings': pooled
        }
        
        return outputs

class StrategyOptimizationTransformer(nn.Module):
    """
    Specialized transformer for strategy parameter optimization
    """
    
    def __init__(self, config: ModelConfig, num_parameters: int):
        super().__init__()
        self.config = config
        self.num_parameters = num_parameters
        
        # Parameter embedding
        self.parameter_embedding = nn.Embedding(num_parameters, config.hidden_dim)
        
        # Market context encoder
        self.context_encoder = MarketTransformer(config)
        
        # Parameter optimization head
        self.optimization_head = nn.Sequential(
            nn.Linear(config.hidden_dim * 2, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, num_parameters),
            nn.Sigmoid()  # Output parameter values between 0 and 1
        )
        
        # Performance prediction head
        self.performance_head = nn.Sequential(
            nn.Linear(config.hidden_dim * 2, config.hidden_dim),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim, 1)  # Expected performance
        )
    
    def forward(self, market_data: torch.Tensor, parameter_indices: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Forward pass for strategy optimization
        Args:
            market_data: Market context data
            parameter_indices: Current parameter configuration indices
        Returns:
            Optimized parameters and expected performance
        """
        # Encode market context
        market_context = self.context_encoder(market_data)['embeddings']
        
        # Embed current parameters
        param_embeddings = self.parameter_embedding(parameter_indices)
        param_context = torch.mean(param_embeddings, dim=1)  # Average parameter embeddings
        
        # Combine market and parameter context
        combined_context = torch.cat([market_context, param_context], dim=-1)
        
        # Generate optimized parameters and performance prediction
        optimized_params = self.optimization_head(combined_context)
        expected_performance = self.performance_head(combined_context)
        
        return {
            'optimized_parameters': optimized_params,
            'expected_performance': expected_performance,
            'market_embeddings': market_context,
            'parameter_embeddings': param_context
        }

class MarketRegimeDetector(nn.Module):
    """
    Transformer-based market regime detection
    """
    
    def __init__(self, config: ModelConfig, num_regimes: int = 4):
        super().__init__()
        self.config = config
        self.num_regimes = num_regimes
        
        # Base transformer
        self.transformer = MarketTransformer(config)
        
        # Regime classification head
        self.regime_classifier = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, num_regimes)
        )
        
        # Regime confidence head
        self.confidence_head = nn.Sequential(
            nn.Linear(config.hidden_dim, config.hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(config.dropout),
            nn.Linear(config.hidden_dim // 2, 1),
            nn.Sigmoid()
        )
    
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Detect market regime
        Args:
            x: Market data tensor
        Returns:
            Regime predictions and confidence scores
        """
        # Get transformer embeddings
        transformer_output = self.transformer(x)
        embeddings = transformer_output['embeddings']
        
        # Classify regime
        regime_logits = self.regime_classifier(embeddings)
        regime_probs = F.softmax(regime_logits, dim=-1)
        
        # Calculate confidence
        confidence = self.confidence_head(embeddings)
        
        return {
            'regime_logits': regime_logits,
            'regime_probabilities': regime_probs,
            'confidence': confidence,
            'embeddings': embeddings,
            **transformer_output
        }

class EnsembleTransformer(nn.Module):
    """
    Ensemble of transformer models for robust predictions
    """
    
    def __init__(self, configs: List[ModelConfig], model_types: List[str]):
        super().__init__()
        self.models = nn.ModuleDict()
        
        for i, (config, model_type) in enumerate(zip(configs, model_types)):
            if model_type == "market":
                self.models[f"model_{i}"] = MarketTransformer(config)
            elif model_type == "regime":
                self.models[f"model_{i}"] = MarketRegimeDetector(config)
            else:
                raise ValueError(f"Unknown model type: {model_type}")
        
        # Ensemble weights
        self.ensemble_weights = nn.Parameter(torch.ones(len(configs)) / len(configs))
        
    def forward(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """
        Ensemble forward pass
        """
        outputs = []
        for model_name, model in self.models.items():
            output = model(x)
            outputs.append(output)
        
        # Weighted ensemble
        ensemble_output = {}
        for key in outputs[0].keys():
            if key in ['price_change', 'volatility', 'strategy_score']:
                weighted_sum = sum(
                    weight * output[key] 
                    for weight, output in zip(self.ensemble_weights, outputs)
                )
                ensemble_output[key] = weighted_sum
        
        return ensemble_output

def create_model(model_type: str, config: ModelConfig, **kwargs) -> nn.Module:
    """
    Factory function to create transformer models
    """
    if model_type == "market":
        return MarketTransformer(config)
    elif model_type == "strategy_optimization":
        return StrategyOptimizationTransformer(config, kwargs.get('num_parameters', 10))
    elif model_type == "regime_detection":
        return MarketRegimeDetector(config, kwargs.get('num_regimes', 4))
    elif model_type == "ensemble":
        return EnsembleTransformer(kwargs.get('configs', [config]), kwargs.get('model_types', ['market']))
    else:
        raise ValueError(f"Unknown model type: {model_type}")

def count_parameters(model: nn.Module) -> int:
    """Count the number of trainable parameters in a model"""
    return sum(p.numel() for p in model.parameters() if p.requires_grad)

def get_model_summary(model: nn.Module) -> Dict[str, Any]:
    """Get a summary of the model architecture"""
    total_params = count_parameters(model)
    
    return {
        "total_parameters": total_params,
        "model_size_mb": total_params * 4 / (1024 * 1024),  # Assuming float32
        "architecture": str(model),
        "device": next(model.parameters()).device.type if list(model.parameters()) else "cpu"
    }
