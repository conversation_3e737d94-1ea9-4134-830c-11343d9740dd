"""
Data Quality and Validation System
Performs comprehensive data validation and quality checks on historical market data
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import statistics
from loguru import logger

from database import get_db_manager

class ValidationStatus(Enum):
    PASS = "PASS"
    FAIL = "FAIL"
    WARNING = "WARNING"

@dataclass
class ValidationResult:
    """Result of a data validation check"""
    check_type: str
    status: ValidationStatus
    message: str
    details: Dict[str, Any]
    timestamp: datetime

@dataclass
class QualityMetrics:
    """Data quality metrics"""
    completeness_ratio: float  # 0.0 to 1.0
    accuracy_score: float      # 0.0 to 1.0
    consistency_score: float   # 0.0 to 1.0
    timeliness_score: float    # 0.0 to 1.0
    overall_quality: float     # 0.0 to 1.0

class DataValidator:
    """
    Comprehensive data validation and quality checking system
    """
    
    def __init__(self):
        self.db_manager = get_db_manager()
        
        # Validation thresholds
        self.thresholds = {
            'price_variance_threshold': 0.10,  # 10% max price variance
            'volume_outlier_threshold': 5.0,   # 5 std dev for volume outliers
            'gap_tolerance_minutes': 5,        # Max 5 min gaps for 1m data
            'min_completeness_ratio': 0.95,    # 95% completeness required
            'max_price_jump_percent': 5.0,     # 5% max price jump between candles
            'min_daily_volume': 100            # Minimum daily volume
        }
        
    async def validate_instrument_data(self, 
                                     instrument: str, 
                                     timeframe: str,
                                     start_date: datetime,
                                     end_date: datetime) -> List[ValidationResult]:
        """Run comprehensive validation on instrument data"""
        
        logger.info(f"🔍 Starting validation for {instrument} {timeframe} from {start_date} to {end_date}")
        
        results = []
        
        # Get data for validation
        data = await self.db_manager.get_market_data(
            instrument, timeframe, start_date, end_date
        )
        
        if not data:
            results.append(ValidationResult(
                check_type="data_availability",
                status=ValidationStatus.FAIL,
                message=f"No data found for {instrument} {timeframe}",
                details={'record_count': 0},
                timestamp=datetime.now()
            ))
            return results
        
        # Run validation checks
        results.extend(await self._check_data_completeness(data, instrument, timeframe, start_date, end_date))
        results.extend(await self._check_price_validity(data, instrument, timeframe))
        results.extend(await self._check_volume_validity(data, instrument, timeframe))
        results.extend(await self._check_price_consistency(data, instrument, timeframe))
        results.extend(await self._check_temporal_consistency(data, instrument, timeframe))
        results.extend(await self._check_outliers(data, instrument, timeframe))
        
        # Store validation results
        for result in results:
            await self._store_validation_result(result, instrument, timeframe, start_date.date())
        
        logger.info(f"✅ Completed validation for {instrument} {timeframe}: {len(results)} checks")
        return results
        
    async def _check_data_completeness(self, 
                                     data: List[Dict[str, Any]], 
                                     instrument: str, 
                                     timeframe: str,
                                     start_date: datetime,
                                     end_date: datetime) -> List[ValidationResult]:
        """Check data completeness"""
        
        results = []
        
        # Calculate expected number of records
        timeframe_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30, 
            '1h': 60, '4h': 240, '1d': 1440
        }
        
        if timeframe not in timeframe_minutes:
            return [ValidationResult(
                check_type="completeness",
                status=ValidationStatus.FAIL,
                message=f"Unsupported timeframe: {timeframe}",
                details={},
                timestamp=datetime.now()
            )]
        
        interval_minutes = timeframe_minutes[timeframe]
        expected_records = int((end_date - start_date).total_seconds() / (interval_minutes * 60))
        actual_records = len(data)
        
        completeness_ratio = actual_records / expected_records if expected_records > 0 else 0
        
        status = ValidationStatus.PASS
        if completeness_ratio < self.thresholds['min_completeness_ratio']:
            status = ValidationStatus.FAIL if completeness_ratio < 0.8 else ValidationStatus.WARNING
        
        results.append(ValidationResult(
            check_type="completeness",
            status=status,
            message=f"Data completeness: {completeness_ratio:.2%} ({actual_records}/{expected_records})",
            details={
                'expected_records': expected_records,
                'actual_records': actual_records,
                'completeness_ratio': completeness_ratio,
                'missing_records': expected_records - actual_records
            },
            timestamp=datetime.now()
        ))
        
        return results
        
    async def _check_price_validity(self, 
                                  data: List[Dict[str, Any]], 
                                  instrument: str, 
                                  timeframe: str) -> List[ValidationResult]:
        """Check price data validity"""
        
        results = []
        invalid_prices = 0
        zero_prices = 0
        negative_prices = 0
        ohlc_violations = 0
        
        for record in data:
            open_price = float(record['open'])
            high_price = float(record['high'])
            low_price = float(record['low'])
            close_price = float(record['close'])
            
            # Check for zero or negative prices
            if any(price <= 0 for price in [open_price, high_price, low_price, close_price]):
                if any(price == 0 for price in [open_price, high_price, low_price, close_price]):
                    zero_prices += 1
                if any(price < 0 for price in [open_price, high_price, low_price, close_price]):
                    negative_prices += 1
                invalid_prices += 1
                continue
            
            # Check OHLC relationships
            if not (low_price <= open_price <= high_price and 
                   low_price <= close_price <= high_price):
                ohlc_violations += 1
        
        total_records = len(data)
        invalid_ratio = invalid_prices / total_records if total_records > 0 else 0
        
        status = ValidationStatus.PASS
        if invalid_ratio > 0.01:  # More than 1% invalid
            status = ValidationStatus.FAIL
        elif invalid_ratio > 0:
            status = ValidationStatus.WARNING
        
        results.append(ValidationResult(
            check_type="price_validity",
            status=status,
            message=f"Price validity: {invalid_ratio:.2%} invalid prices",
            details={
                'total_records': total_records,
                'invalid_prices': invalid_prices,
                'zero_prices': zero_prices,
                'negative_prices': negative_prices,
                'ohlc_violations': ohlc_violations,
                'invalid_ratio': invalid_ratio
            },
            timestamp=datetime.now()
        ))
        
        return results
        
    async def _check_volume_validity(self, 
                                   data: List[Dict[str, Any]], 
                                   instrument: str, 
                                   timeframe: str) -> List[ValidationResult]:
        """Check volume data validity"""
        
        results = []
        negative_volumes = 0
        zero_volumes = 0
        suspicious_volumes = 0
        
        volumes = [int(record['volume']) for record in data]
        
        if not volumes:
            return results
        
        # Calculate volume statistics
        avg_volume = statistics.mean(volumes)
        volume_std = statistics.stdev(volumes) if len(volumes) > 1 else 0
        
        for volume in volumes:
            if volume < 0:
                negative_volumes += 1
            elif volume == 0:
                zero_volumes += 1
            elif volume_std > 0 and abs(volume - avg_volume) > (self.thresholds['volume_outlier_threshold'] * volume_std):
                suspicious_volumes += 1
        
        total_records = len(volumes)
        invalid_volume_ratio = (negative_volumes + zero_volumes) / total_records if total_records > 0 else 0
        
        status = ValidationStatus.PASS
        if negative_volumes > 0:
            status = ValidationStatus.FAIL
        elif zero_volumes > total_records * 0.1:  # More than 10% zero volumes
            status = ValidationStatus.WARNING
        
        results.append(ValidationResult(
            check_type="volume_validity",
            status=status,
            message=f"Volume validity: {invalid_volume_ratio:.2%} invalid volumes",
            details={
                'total_records': total_records,
                'negative_volumes': negative_volumes,
                'zero_volumes': zero_volumes,
                'suspicious_volumes': suspicious_volumes,
                'avg_volume': avg_volume,
                'volume_std': volume_std,
                'invalid_ratio': invalid_volume_ratio
            },
            timestamp=datetime.now()
        ))
        
        return results
        
    async def _check_price_consistency(self, 
                                     data: List[Dict[str, Any]], 
                                     instrument: str, 
                                     timeframe: str) -> List[ValidationResult]:
        """Check price consistency and detect unusual jumps"""
        
        results = []
        large_jumps = 0
        total_comparisons = 0
        
        for i in range(1, len(data)):
            prev_close = float(data[i-1]['close'])
            curr_open = float(data[i]['open'])
            
            if prev_close > 0:
                price_change_percent = abs(curr_open - prev_close) / prev_close * 100
                
                if price_change_percent > self.thresholds['max_price_jump_percent']:
                    large_jumps += 1
                
                total_comparisons += 1
        
        jump_ratio = large_jumps / total_comparisons if total_comparisons > 0 else 0
        
        status = ValidationStatus.PASS
        if jump_ratio > 0.05:  # More than 5% of transitions have large jumps
            status = ValidationStatus.WARNING
        if jump_ratio > 0.1:   # More than 10% of transitions have large jumps
            status = ValidationStatus.FAIL
        
        results.append(ValidationResult(
            check_type="price_consistency",
            status=status,
            message=f"Price consistency: {jump_ratio:.2%} large price jumps",
            details={
                'total_comparisons': total_comparisons,
                'large_jumps': large_jumps,
                'jump_ratio': jump_ratio,
                'jump_threshold_percent': self.thresholds['max_price_jump_percent']
            },
            timestamp=datetime.now()
        ))
        
        return results
        
    async def _check_temporal_consistency(self, 
                                        data: List[Dict[str, Any]], 
                                        instrument: str, 
                                        timeframe: str) -> List[ValidationResult]:
        """Check temporal consistency of data"""
        
        results = []
        
        if len(data) < 2:
            return results
        
        # Sort data by timestamp
        sorted_data = sorted(data, key=lambda x: x['timestamp'])
        
        duplicate_timestamps = 0
        out_of_order = 0
        large_gaps = 0
        
        timeframe_minutes = {
            '1m': 1, '5m': 5, '15m': 15, '30m': 30, 
            '1h': 60, '4h': 240, '1d': 1440
        }
        
        expected_interval = timedelta(minutes=timeframe_minutes.get(timeframe, 1))
        
        for i in range(1, len(sorted_data)):
            prev_time = sorted_data[i-1]['timestamp']
            curr_time = sorted_data[i]['timestamp']
            
            # Check for duplicates
            if curr_time == prev_time:
                duplicate_timestamps += 1
                continue
            
            # Check for out of order (should not happen after sorting, but good to verify)
            if curr_time < prev_time:
                out_of_order += 1
            
            # Check for large gaps
            time_diff = curr_time - prev_time
            if time_diff > expected_interval * 2:  # Gap larger than 2x expected interval
                large_gaps += 1
        
        total_intervals = len(sorted_data) - 1
        
        status = ValidationStatus.PASS
        if duplicate_timestamps > 0 or out_of_order > 0:
            status = ValidationStatus.FAIL
        elif large_gaps > total_intervals * 0.1:  # More than 10% large gaps
            status = ValidationStatus.WARNING
        
        results.append(ValidationResult(
            check_type="temporal_consistency",
            status=status,
            message=f"Temporal consistency: {duplicate_timestamps} duplicates, {large_gaps} large gaps",
            details={
                'total_intervals': total_intervals,
                'duplicate_timestamps': duplicate_timestamps,
                'out_of_order': out_of_order,
                'large_gaps': large_gaps,
                'expected_interval_minutes': expected_interval.total_seconds() / 60
            },
            timestamp=datetime.now()
        ))
        
        return results
        
    async def _check_outliers(self, 
                            data: List[Dict[str, Any]], 
                            instrument: str, 
                            timeframe: str) -> List[ValidationResult]:
        """Detect statistical outliers in price and volume data"""
        
        results = []
        
        if len(data) < 10:  # Need sufficient data for outlier detection
            return results
        
        # Extract price and volume data
        closes = [float(record['close']) for record in data]
        volumes = [int(record['volume']) for record in data]
        
        # Calculate price outliers
        price_mean = statistics.mean(closes)
        price_std = statistics.stdev(closes) if len(closes) > 1 else 0
        
        price_outliers = 0
        volume_outliers = 0
        
        if price_std > 0:
            for close in closes:
                z_score = abs(close - price_mean) / price_std
                if z_score > 3:  # 3 standard deviations
                    price_outliers += 1
        
        # Calculate volume outliers
        if volumes:
            volume_mean = statistics.mean(volumes)
            volume_std = statistics.stdev(volumes) if len(volumes) > 1 else 0
            
            if volume_std > 0:
                for volume in volumes:
                    z_score = abs(volume - volume_mean) / volume_std
                    if z_score > self.thresholds['volume_outlier_threshold']:
                        volume_outliers += 1
        
        total_records = len(data)
        outlier_ratio = (price_outliers + volume_outliers) / (total_records * 2) if total_records > 0 else 0
        
        status = ValidationStatus.PASS
        if outlier_ratio > 0.05:  # More than 5% outliers
            status = ValidationStatus.WARNING
        if outlier_ratio > 0.1:   # More than 10% outliers
            status = ValidationStatus.FAIL
        
        results.append(ValidationResult(
            check_type="outlier_detection",
            status=status,
            message=f"Outlier detection: {price_outliers} price, {volume_outliers} volume outliers",
            details={
                'total_records': total_records,
                'price_outliers': price_outliers,
                'volume_outliers': volume_outliers,
                'price_mean': price_mean,
                'price_std': price_std,
                'volume_mean': statistics.mean(volumes) if volumes else 0,
                'volume_std': volume_std if volumes else 0,
                'outlier_ratio': outlier_ratio
            },
            timestamp=datetime.now()
        ))
        
        return results
        
    async def _store_validation_result(self, 
                                     result: ValidationResult, 
                                     instrument: str, 
                                     timeframe: str,
                                     data_date: datetime.date):
        """Store validation result in database"""
        
        try:
            query = """
            INSERT INTO data_quality_checks 
            (instrument, timeframe, data_date, check_type, status, details)
            VALUES ($1, $2, $3, $4, $5, $6)
            ON CONFLICT (instrument, data_date, check_type)
            DO UPDATE SET
                status = EXCLUDED.status,
                details = EXCLUDED.details,
                created_at = NOW()
            """
            
            async with self.db_manager.connection_pool.acquire() as conn:
                await conn.execute(
                    query,
                    instrument,
                    timeframe,
                    data_date,
                    result.check_type,
                    result.status.value,
                    result.details
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store validation result: {e}")
    
    async def calculate_quality_metrics(self, 
                                      instrument: str, 
                                      timeframe: str,
                                      start_date: datetime,
                                      end_date: datetime) -> QualityMetrics:
        """Calculate comprehensive data quality metrics"""
        
        # Run validation if not already done
        validation_results = await self.validate_instrument_data(
            instrument, timeframe, start_date, end_date
        )
        
        # Calculate component scores
        completeness_score = self._calculate_completeness_score(validation_results)
        accuracy_score = self._calculate_accuracy_score(validation_results)
        consistency_score = self._calculate_consistency_score(validation_results)
        timeliness_score = self._calculate_timeliness_score(validation_results)
        
        # Calculate overall quality score
        overall_quality = (completeness_score + accuracy_score + consistency_score + timeliness_score) / 4
        
        return QualityMetrics(
            completeness_ratio=completeness_score,
            accuracy_score=accuracy_score,
            consistency_score=consistency_score,
            timeliness_score=timeliness_score,
            overall_quality=overall_quality
        )
    
    def _calculate_completeness_score(self, results: List[ValidationResult]) -> float:
        """Calculate completeness score from validation results"""
        for result in results:
            if result.check_type == "completeness":
                return result.details.get('completeness_ratio', 0.0)
        return 0.0
    
    def _calculate_accuracy_score(self, results: List[ValidationResult]) -> float:
        """Calculate accuracy score from validation results"""
        accuracy_checks = ['price_validity', 'volume_validity']
        scores = []
        
        for result in results:
            if result.check_type in accuracy_checks:
                if result.status == ValidationStatus.PASS:
                    scores.append(1.0)
                elif result.status == ValidationStatus.WARNING:
                    scores.append(0.7)
                else:
                    scores.append(0.0)
        
        return statistics.mean(scores) if scores else 0.0
    
    def _calculate_consistency_score(self, results: List[ValidationResult]) -> float:
        """Calculate consistency score from validation results"""
        consistency_checks = ['price_consistency', 'temporal_consistency']
        scores = []
        
        for result in results:
            if result.check_type in consistency_checks:
                if result.status == ValidationStatus.PASS:
                    scores.append(1.0)
                elif result.status == ValidationStatus.WARNING:
                    scores.append(0.7)
                else:
                    scores.append(0.0)
        
        return statistics.mean(scores) if scores else 0.0
    
    def _calculate_timeliness_score(self, results: List[ValidationResult]) -> float:
        """Calculate timeliness score from validation results"""
        # For now, assume data is timely if it passes temporal consistency
        for result in results:
            if result.check_type == "temporal_consistency":
                if result.status == ValidationStatus.PASS:
                    return 1.0
                elif result.status == ValidationStatus.WARNING:
                    return 0.7
                else:
                    return 0.0
        return 1.0  # Default to 1.0 if no temporal check

# Global instance
_data_validator: Optional[DataValidator] = None

def get_data_validator() -> DataValidator:
    """Get global data validator instance"""
    global _data_validator
    if _data_validator is None:
        _data_validator = DataValidator()
    return _data_validator