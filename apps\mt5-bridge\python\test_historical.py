#!/usr/bin/env python3
"""
Historical Data Collection Test Script
Tests database operations, data collection, validation, and quality checks
"""

import asyncio
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, Any
from loguru import logger
import sys

# Configure logging
logger.remove()
logger.add(
    sys.stdout,
    format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{message}</cyan>",
    level="INFO"
)

class HistoricalDataTester:
    """Test client for historical data collection functionality"""
    
    def __init__(self, api_url: str = "http://localhost:8001"):
        self.api_url = api_url
        self.test_results = {}
        
    async def test_database_connection(self) -> bool:
        """Test database connectivity through API"""
        logger.info("🗄️ Testing database connection...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/historical/stats") as response:
                    if response.status == 200:
                        data = await response.json()
                        db_stats = data.get('database', {})
                        logger.info(f"✅ Database connected: {db_stats.get('total_records', 0)} records")
                        self.test_results['database_connection'] = True
                        return True
                    else:
                        logger.error(f"❌ Database connection failed: {response.status}")
                        self.test_results['database_connection'] = False
                        return False
        except Exception as e:
            logger.error(f"❌ Database connection error: {e}")
            self.test_results['database_connection'] = False
            return False
    
    async def test_historical_collection(self, instrument: str = "EURUSD", days_back: int = 7) -> bool:
        """Test historical data collection"""
        logger.info(f"📊 Testing historical data collection for {instrument}...")
        
        try:
            # Calculate date range
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            timeframes = ["1h", "4h", "1d"]
            
            async with aiohttp.ClientSession() as session:
                # Start collection
                collection_data = {
                    "instrument": instrument,
                    "timeframes": timeframes,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                }
                
                async with session.post(
                    f"{self.api_url}/historical/collect",
                    json=collection_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = data.get('results', {})
                        
                        total_records = 0
                        for tf, result in results.items():
                            records = result.get('records_collected', 0)
                            total_records += records
                            logger.info(f"  📈 {tf}: {records} records collected")
                        
                        if total_records > 0:
                            logger.info(f"✅ Historical collection successful: {total_records} total records")
                            self.test_results['historical_collection'] = True
                            return True
                        else:
                            logger.warning("⚠️ No historical data collected")
                            self.test_results['historical_collection'] = False
                            return False
                    else:
                        logger.error(f"❌ Historical collection failed: {response.status}")
                        self.test_results['historical_collection'] = False
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Historical collection error: {e}")
            self.test_results['historical_collection'] = False
            return False
    
    async def test_data_gaps_detection(self, instrument: str = "EURUSD", timeframe: str = "1h") -> bool:
        """Test data gaps detection"""
        logger.info(f"🔍 Testing data gaps detection for {instrument} {timeframe}...")
        
        try:
            # Test with a recent period
            end_date = datetime.now()
            start_date = end_date - timedelta(days=3)
            
            async with aiohttp.ClientSession() as session:
                params = {
                    "instrument": instrument,
                    "timeframe": timeframe,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                }
                
                async with session.get(f"{self.api_url}/data/gaps", params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        gaps = data.get('gaps', [])
                        
                        logger.info(f"✅ Gap detection successful: {len(gaps)} gaps found")
                        for i, gap in enumerate(gaps[:5]):  # Show first 5 gaps
                            logger.info(f"  🕳️ Gap {i+1}: {gap['start']} to {gap['end']} ({gap['duration_minutes']:.1f} min)")
                        
                        self.test_results['gap_detection'] = True
                        return True
                    else:
                        logger.error(f"❌ Gap detection failed: {response.status}")
                        self.test_results['gap_detection'] = False
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Gap detection error: {e}")
            self.test_results['gap_detection'] = False
            return False
    
    async def test_data_validation(self, instrument: str = "EURUSD", timeframe: str = "1h") -> bool:
        """Test data validation functionality"""
        logger.info(f"🔍 Testing data validation for {instrument} {timeframe}...")
        
        try:
            # Test with recent data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=2)
            
            async with aiohttp.ClientSession() as session:
                validation_data = {
                    "instrument": instrument,
                    "timeframe": timeframe,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                }
                
                async with session.post(
                    f"{self.api_url}/data/validate",
                    json=validation_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        validation_results = data.get('validation_results', [])
                        
                        logger.info(f"✅ Data validation successful: {len(validation_results)} checks performed")
                        
                        # Summarize results
                        status_counts = {'PASS': 0, 'WARNING': 0, 'FAIL': 0}
                        for result in validation_results:
                            status = result['status']
                            status_counts[status] += 1
                            logger.info(f"  📋 {result['check_type']}: {status} - {result['message']}")
                        
                        logger.info(f"  📊 Summary: {status_counts['PASS']} PASS, {status_counts['WARNING']} WARNING, {status_counts['FAIL']} FAIL")
                        
                        self.test_results['data_validation'] = True
                        return True
                    else:
                        logger.error(f"❌ Data validation failed: {response.status}")
                        self.test_results['data_validation'] = False
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Data validation error: {e}")
            self.test_results['data_validation'] = False
            return False
    
    async def test_quality_metrics(self, instrument: str = "EURUSD", timeframe: str = "1h") -> bool:
        """Test data quality metrics calculation"""
        logger.info(f"📈 Testing quality metrics for {instrument} {timeframe}...")
        
        try:
            # Test with recent data
            end_date = datetime.now()
            start_date = end_date - timedelta(days=2)
            
            async with aiohttp.ClientSession() as session:
                params = {
                    "instrument": instrument,
                    "timeframe": timeframe,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                }
                
                async with session.get(f"{self.api_url}/data/quality-metrics", params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        metrics = data.get('metrics', {})
                        
                        logger.info("✅ Quality metrics calculated successfully:")
                        logger.info(f"  📊 Completeness: {metrics.get('completeness_ratio', 0):.2%}")
                        logger.info(f"  🎯 Accuracy: {metrics.get('accuracy_score', 0):.2%}")
                        logger.info(f"  🔗 Consistency: {metrics.get('consistency_score', 0):.2%}")
                        logger.info(f"  ⏰ Timeliness: {metrics.get('timeliness_score', 0):.2%}")
                        logger.info(f"  🏆 Overall Quality: {metrics.get('overall_quality', 0):.2%}")
                        
                        self.test_results['quality_metrics'] = True
                        return True
                    else:
                        logger.error(f"❌ Quality metrics failed: {response.status}")
                        self.test_results['quality_metrics'] = False
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Quality metrics error: {e}")
            self.test_results['quality_metrics'] = False
            return False
    
    async def test_backfill_functionality(self, instrument: str = "EURUSD", timeframe: str = "1h") -> bool:
        """Test data backfill functionality"""
        logger.info(f"🔧 Testing backfill functionality for {instrument} {timeframe}...")
        
        try:
            # Test backfill for a small period
            end_date = datetime.now() - timedelta(hours=12)
            start_date = end_date - timedelta(hours=6)
            
            async with aiohttp.ClientSession() as session:
                backfill_data = {
                    "instrument": instrument,
                    "timeframe": timeframe,
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat()
                }
                
                async with session.post(
                    f"{self.api_url}/historical/backfill",
                    json=backfill_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        result = data.get('result', {})
                        
                        gaps_found = result.get('gaps_found', 0)
                        records_collected = result.get('records_collected', 0)
                        
                        logger.info(f"✅ Backfill successful: {gaps_found} gaps found, {records_collected} records collected")
                        
                        self.test_results['backfill_functionality'] = True
                        return True
                    else:
                        logger.error(f"❌ Backfill failed: {response.status}")
                        self.test_results['backfill_functionality'] = False
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Backfill error: {e}")
            self.test_results['backfill_functionality'] = False
            return False
    
    async def test_recent_sync(self) -> bool:
        """Test recent data synchronization"""
        logger.info("🔄 Testing recent data sync...")
        
        try:
            async with aiohttp.ClientSession() as session:
                sync_data = {
                    "instruments": ["EURUSD", "GBPUSD"],
                    "timeframes": ["1h"],
                    "hours_back": 6
                }
                
                async with session.post(
                    f"{self.api_url}/historical/sync",
                    json=sync_data
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        results = data.get('results', {})
                        
                        total_synced = 0
                        for key, result in results.items():
                            if 'error' not in result:
                                records = result.get('records_collected', 0)
                                total_synced += records
                                logger.info(f"  📈 {key}: {records} records synced")
                        
                        logger.info(f"✅ Recent sync successful: {total_synced} total records")
                        self.test_results['recent_sync'] = True
                        return True
                    else:
                        logger.error(f"❌ Recent sync failed: {response.status}")
                        self.test_results['recent_sync'] = False
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Recent sync error: {e}")
            self.test_results['recent_sync'] = False
            return False
    
    async def test_collection_stats(self) -> bool:
        """Test collection statistics endpoint"""
        logger.info("📊 Testing collection statistics...")
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.api_url}/historical/stats") as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        collector_stats = data.get('collector', {})
                        db_stats = data.get('database', {})
                        
                        logger.info("✅ Collection statistics retrieved:")
                        logger.info(f"  📊 Records Collected: {collector_stats.get('total_records_collected', 0)}")
                        logger.info(f"  📈 Successful Batches: {collector_stats.get('successful_batches', 0)}")
                        logger.info(f"  ❌ Failed Batches: {collector_stats.get('failed_batches', 0)}")
                        logger.info(f"  🗄️ Database Records: {db_stats.get('total_records', 0)}")
                        logger.info(f"  📊 Instruments: {db_stats.get('unique_instruments', 0)}")
                        
                        self.test_results['collection_stats'] = True
                        return True
                    else:
                        logger.error(f"❌ Collection stats failed: {response.status}")
                        self.test_results['collection_stats'] = False
                        return False
                        
        except Exception as e:
            logger.error(f"❌ Collection stats error: {e}")
            self.test_results['collection_stats'] = False
            return False
    
    def print_summary(self):
        """Print test results summary"""
        logger.info("=" * 60)
        logger.info("📋 HISTORICAL DATA COLLECTION TEST SUMMARY")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{test_name:30} {status}")
        
        logger.info("-" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        logger.info("=" * 60)

async def main():
    """Main test function"""
    logger.info("🚀 Starting Historical Data Collection Tests")
    logger.info("=" * 60)
    
    tester = HistoricalDataTester()
    
    # Run tests in sequence
    tests = [
        ("Database Connection", tester.test_database_connection()),
        ("Historical Collection", tester.test_historical_collection("EURUSD", 3)),
        ("Data Gaps Detection", tester.test_data_gaps_detection("EURUSD", "1h")),
        ("Data Validation", tester.test_data_validation("EURUSD", "1h")),
        ("Quality Metrics", tester.test_quality_metrics("EURUSD", "1h")),
        ("Backfill Functionality", tester.test_backfill_functionality("EURUSD", "1h")),
        ("Recent Sync", tester.test_recent_sync()),
        ("Collection Stats", tester.test_collection_stats())
    ]
    
    for test_name, test_coro in tests:
        logger.info(f"🧪 Running: {test_name}")
        try:
            result = await test_coro
            logger.info(f"{'✅' if result else '❌'} {test_name}: {'PASS' if result else 'FAIL'}")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")
            tester.test_results[test_name.lower().replace(' ', '_')] = False
        
        # Brief pause between tests
        await asyncio.sleep(2)
    
    # Print summary
    tester.print_summary()
    
    # Return overall success
    all_passed = all(tester.test_results.values())
    return 0 if all_passed else 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)