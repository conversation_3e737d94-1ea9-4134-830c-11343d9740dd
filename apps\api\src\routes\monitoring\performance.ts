import express from 'express';
import { PerformanceMonitoringService } from '../../services/monitoring/PerformanceMonitoringService';

const router = express.Router();
const performanceMonitor = new PerformanceMonitoringService();

// Start performance monitoring when module loads
performanceMonitor.startMonitoring({
  intervalMs: 30000, // 30 seconds
});

/**
 * GET /api/monitoring/performance
 * Get performance summary
 */
router.get('/', async (req, res) => {
  try {
    const summary = performanceMonitor.getPerformanceSummary();
    
    res.json({
      status: 'success',
      data: summary,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to get performance summary:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve performance metrics',
      error: error.message,
    });
  }
});

/**
 * GET /api/monitoring/performance/endpoints
 * Get detailed endpoint metrics
 */
router.get('/endpoints', async (req, res) => {
  try {
    const { endpoint } = req.query;
    const metrics = performanceMonitor.getEndpointMetrics(endpoint as string);
    
    // Convert Maps to objects for JSON serialization
    const serializedMetrics = metrics.map(metric => ({
      ...metric,
      statusCodes: Object.fromEntries(metric.statusCodes),
      hourlyStats: Object.fromEntries(metric.hourlyStats),
      userRequests: Object.fromEntries(metric.userRequests),
      recentResponseTimes: metric.recentResponseTimes.slice(-100), // Last 100 requests
    }));

    res.json({
      status: 'success',
      data: {
        endpoints: serializedMetrics,
        totalEndpoints: metrics.length,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to get endpoint metrics:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve endpoint metrics',
      error: error.message,
    });
  }
});

/**
 * GET /api/monitoring/performance/realtime
 * Get real-time performance metrics (SSE endpoint)
 */
router.get('/realtime', (req, res) => {
  // Set up Server-Sent Events
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  });

  // Send initial data
  const summary = performanceMonitor.getPerformanceSummary();
  res.write(`data: ${JSON.stringify(summary)}\n\n`);

  // Set up event listeners for real-time updates
  const responseTimeHandler = (data: any) => {
    res.write(`event: responseTime\n`);
    res.write(`data: ${JSON.stringify(data)}\n\n`);
  };

  const systemMetricsHandler = (data: any) => {
    res.write(`event: systemMetrics\n`);
    res.write(`data: ${JSON.stringify(data)}\n\n`);
  };

  const alertHandler = (data: any) => {
    res.write(`event: alert\n`);
    res.write(`data: ${JSON.stringify(data)}\n\n`);
  };

  performanceMonitor.on('responseTime', responseTimeHandler);
  performanceMonitor.on('systemMetrics', systemMetricsHandler);
  performanceMonitor.on('performanceAlert', alertHandler);
  performanceMonitor.on('systemAlert', alertHandler);

  // Send periodic summary updates
  const summaryInterval = setInterval(() => {
    const currentSummary = performanceMonitor.getPerformanceSummary();
    res.write(`event: summary\n`);
    res.write(`data: ${JSON.stringify(currentSummary)}\n\n`);
  }, 10000); // Every 10 seconds

  // Handle client disconnect
  req.on('close', () => {
    performanceMonitor.off('responseTime', responseTimeHandler);
    performanceMonitor.off('systemMetrics', systemMetricsHandler);
    performanceMonitor.off('performanceAlert', alertHandler);
    performanceMonitor.off('systemAlert', alertHandler);
    clearInterval(summaryInterval);
    res.end();
  });
});

/**
 * POST /api/monitoring/performance/track
 * Track a custom performance metric
 */
router.post('/track', async (req, res) => {
  try {
    const { endpoint, method, responseTime, statusCode, userId } = req.body;

    if (!endpoint || !method || responseTime === undefined || !statusCode) {
      return res.status(400).json({
        status: 'error',
        message: 'Missing required fields: endpoint, method, responseTime, statusCode',
      });
    }

    performanceMonitor.trackResponseTime(
      endpoint,
      method.toUpperCase(),
      statusCode,
      responseTime,
      userId
    );

    res.json({
      status: 'success',
      message: 'Performance metric tracked successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to track performance metric:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to track performance metric',
      error: error.message,
    });
  }
});

/**
 * GET /api/monitoring/performance/thresholds
 * Get current performance thresholds
 */
router.get('/thresholds', async (req, res) => {
  try {
    // Access private thresholds through a getter method if available
    // For now, return default thresholds
    const thresholds = {
      responseTimeWarning: 200,
      responseTimeCritical: 500,
      throughputWarning: 50,
      throughputCritical: 20,
      errorRateWarning: 1,
      errorRateCritical: 5,
      memoryWarning: 80,
      memoryCritical: 95,
      cpuWarning: 70,
      cpuCritical: 90,
    };

    res.json({
      status: 'success',
      data: thresholds,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to get performance thresholds:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to retrieve performance thresholds',
      error: error.message,
    });
  }
});

/**
 * POST /api/monitoring/performance/thresholds
 * Update performance thresholds
 */
router.post('/thresholds', async (req, res) => {
  try {
    const {
      responseTimeWarning,
      responseTimeCritical,
      throughputWarning,
      throughputCritical,
      errorRateWarning,
      errorRateCritical,
      memoryWarning,
      memoryCritical,
      cpuWarning,
      cpuCritical,
    } = req.body;

    // Validate thresholds
    const updates: any = {};
    
    if (responseTimeWarning !== undefined) updates.responseTimeWarning = responseTimeWarning;
    if (responseTimeCritical !== undefined) updates.responseTimeCritical = responseTimeCritical;
    if (throughputWarning !== undefined) updates.throughputWarning = throughputWarning;
    if (throughputCritical !== undefined) updates.throughputCritical = throughputCritical;
    if (errorRateWarning !== undefined) updates.errorRateWarning = errorRateWarning;
    if (errorRateCritical !== undefined) updates.errorRateCritical = errorRateCritical;
    if (memoryWarning !== undefined) updates.memoryWarning = memoryWarning;
    if (memoryCritical !== undefined) updates.memoryCritical = memoryCritical;
    if (cpuWarning !== undefined) updates.cpuWarning = cpuWarning;
    if (cpuCritical !== undefined) updates.cpuCritical = cpuCritical;

    // Note: In a full implementation, you'd update the actual service thresholds
    // For now, we'll just acknowledge the request

    res.json({
      status: 'success',
      message: 'Performance thresholds updated successfully',
      data: updates,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to update performance thresholds:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to update performance thresholds',
      error: error.message,
    });
  }
});

/**
 * DELETE /api/monitoring/performance/metrics
 * Clear performance metrics (for testing/maintenance)
 */
router.delete('/metrics', async (req, res) => {
  try {
    performanceMonitor.clearMetrics();
    
    res.json({
      status: 'success',
      message: 'Performance metrics cleared successfully',
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Failed to clear performance metrics:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to clear performance metrics',
      error: error.message,
    });
  }
});

/**
 * GET /api/monitoring/performance/health
 * Performance monitoring service health check
 */
router.get('/health', async (req, res) => {
  try {
    const summary = performanceMonitor.getPerformanceSummary();
    const isHealthy = summary.systemHealth?.databaseHealthy && summary.systemHealth?.cacheHealthy;
    
    res.json({
      status: 'success',
      data: {
        healthy: isHealthy,
        monitoring: summary.overview.isMonitoring,
        uptime: summary.systemHealth?.uptime || 0,
        lastCheck: summary.lastCheck,
        memoryUsage: summary.systemHealth?.memoryUsage || 0,
        cpuUsage: summary.systemHealth?.cpuUsage || 0,
        responseTime: summary.overview.overallResponseTime,
        errorRate: summary.overview.overallErrorRate,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Performance monitoring health check failed:', error);
    res.status(500).json({
      status: 'error',
      message: 'Performance monitoring health check failed',
      error: error.message,
      healthy: false,
    });
  }
});

// Middleware to track API response times automatically
export const performanceTrackingMiddleware = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const startTime = Date.now();
  
  // Track response when request finishes
  res.on('finish', () => {
    const responseTime = Date.now() - startTime;
    const endpoint = req.route?.path || req.path;
    const method = req.method;
    const statusCode = res.statusCode;
    const userId = (req as any).user?.id; // Assuming user ID is available in req.user
    
    // Skip monitoring internal monitoring endpoints to avoid recursion
    if (!endpoint.startsWith('/api/monitoring/performance')) {
      performanceMonitor.trackResponseTime(endpoint, method, statusCode, responseTime, userId);
    }
  });
  
  next();
};

export default router;
export { performanceMonitor };