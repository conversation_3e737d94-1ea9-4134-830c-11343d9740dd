/**
 * Regime Classification Engine
 * 
 * Core logic for market regime classification using technical indicators
 * and rule-based analysis with confidence scoring and trend detection.
 */

import Decimal from 'decimal.js';
import { EventEmitter } from 'events';
import {
  MarketRegime,
  ConfidenceLevel,
  RegimeDetectionResult,
  RegimeDetectionConfig,
  DataSource,
  TimeFrame,
  getConfidenceLevel,
} from '@golddaddy/types';

// Technical indicator input for regime classification
export interface TechnicalIndicatorInput {
  instrument: string;
  timeframe: TimeFrame;
  timestamp: Date;
  
  // Price data (last N periods)
  prices: {
    open: Decimal.Instance;
    high: Decimal.Instance;
    low: Decimal.Instance;
    close: Decimal.Instance;
    volume: Decimal.Instance;
  }[];
  
  // Pre-calculated technical indicators
  sma20?: Decimal.Instance;
  sma50?: Decimal.Instance;
  rsi14?: Decimal.Instance;
  atr14?: Decimal.Instance;
  macdLine?: Decimal.Instance;
  macdSignal?: Decimal.Instance;
  bollingerUpper?: Decimal.Instance;
  bollingerLower?: Decimal.Instance;
  bollingerMiddle?: Decimal.Instance;
  
  source: DataSource;
}

// Individual classifier result
interface ClassifierResult {
  regime: MarketRegime;
  confidence: number;
  reasoning: string;
  weight: number; // Importance of this classifier (0-1)
}

/**
 * Market Regime Classification Engine
 * Uses multiple technical analysis methods to classify market conditions
 */
export class RegimeClassificationEngine extends EventEmitter {
  private config: RegimeDetectionConfig;
  private algorithmVersion = '1.0.0';

  constructor(config: RegimeDetectionConfig) {
    super();
    this.config = config;
  }

  /**
   * Main regime classification method
   */
  public async classifyMarketRegime(
    input: TechnicalIndicatorInput,
    previousRegime?: MarketRegime
  ): Promise<RegimeDetectionResult> {
    const startTime = Date.now();

    try {
      // Run multiple classification algorithms
      const classifierResults: ClassifierResult[] = [
        this.classifyByTrendAnalysis(input),
        this.classifyByVolatilityAnalysis(input),
        this.classifyByMomentumAnalysis(input),
        this.classifyByMeanReversionAnalysis(input),
        this.classifyByBollingerBandAnalysis(input),
        this.classifyByRSIAnalysis(input),
      ];

      // Combine classifier results using weighted voting
      const finalClassification = this.combineClassifierResults(classifierResults);
      
      // Calculate supporting metrics
      const supportingMetrics = this.calculateSupportingMetrics(input);
      
      // Detect regime changes
      const regimeChangeDetected = previousRegime ? 
        this.detectRegimeChange(previousRegime, finalClassification.regime, finalClassification.confidence) : 
        false;
      
      const regimeChangeMagnitude = regimeChangeDetected ? 
        this.calculateRegimeChangeMagnitude(previousRegime!, finalClassification.regime) : 
        undefined;

      // Create result
      const result: RegimeDetectionResult = {
        id: this.generateResultId(input),
        instrument: input.instrument,
        timeframe: input.timeframe,
        timestamp: input.timestamp,
        regime: finalClassification.regime,
        confidence: finalClassification.confidence,
        confidenceLevel: getConfidenceLevel(finalClassification.confidence),
        
        trendStrength: supportingMetrics.trendStrength,
        volatilityLevel: supportingMetrics.volatilityLevel,
        momentumScore: supportingMetrics.momentumScore,
        
        supportingMetrics: {
          smaSlope20: supportingMetrics.smaSlope20,
          smaSlope50: supportingMetrics.smaSlope50,
          atr14: supportingMetrics.atr14,
          rsi14: supportingMetrics.rsi14,
          bollingerBandwidth: supportingMetrics.bollingerBandwidth,
          pricePosition: supportingMetrics.pricePosition,
        },
        
        previousRegime,
        regimeChangeDetected,
        regimeChangeMagnitude,
        
        processingTimeMs: Date.now() - startTime,
        source: input.source,
        algorithmVersion: this.algorithmVersion,
      };

      this.emit('regime_classified', result);
      return result;

    } catch (error) {
      const errorResult = this.createErrorResult(input, error as Error);
      this.emit('classification_error', { input, error });
      return errorResult;
    }
  }

  /**
   * Trend analysis classifier using moving averages and price action
   */
  private classifyByTrendAnalysis(input: TechnicalIndicatorInput): ClassifierResult {
    const { sma20, sma50, prices } = input;
    
    if (!sma20 || !sma50 || prices.length < 20) {
      return {
        regime: MarketRegime.UNKNOWN,
        confidence: 0,
        reasoning: 'Insufficient data for trend analysis',
        weight: 0.25,
      };
    }

    const currentPrice = prices[prices.length - 1].close;
    const priceAboveSma20 = currentPrice.gt(sma20);
    const priceAboveSma50 = currentPrice.gt(sma50);
    const sma20AboveSma50 = sma20.gt(sma50);

    // Calculate trend strength based on price position and MA alignment
    let trendStrength = 0;
    let regime = MarketRegime.SIDEWAYS;
    let confidence = 0;

    if (priceAboveSma20 && priceAboveSma50 && sma20AboveSma50) {
      // Strong uptrend
      trendStrength = this.calculateTrendStrength(prices, 'up');
      if (trendStrength > this.config.trendStrengthThreshold) {
        regime = MarketRegime.TRENDING_UP;
        confidence = Math.min(trendStrength, 0.9);
      }
    } else if (!priceAboveSma20 && !priceAboveSma50 && !sma20AboveSma50) {
      // Strong downtrend
      trendStrength = this.calculateTrendStrength(prices, 'down');
      if (trendStrength > this.config.trendStrengthThreshold) {
        regime = MarketRegime.TRENDING_DOWN;
        confidence = Math.min(trendStrength, 0.9);
      }
    } else {
      // Mixed signals - sideways or transitioning
      regime = MarketRegime.SIDEWAYS;
      confidence = 0.6;
    }

    return {
      regime,
      confidence,
      reasoning: `Trend analysis: Price/SMA20/SMA50 alignment suggests ${regime}`,
      weight: 0.25,
    };
  }

  /**
   * Volatility analysis classifier using ATR and price volatility
   */
  private classifyByVolatilityAnalysis(input: TechnicalIndicatorInput): ClassifierResult {
    const { atr14, prices } = input;
    
    if (!atr14 || prices.length < 14) {
      return {
        regime: MarketRegime.UNKNOWN,
        confidence: 0,
        reasoning: 'Insufficient data for volatility analysis',
        weight: 0.2,
      };
    }

    // Calculate recent volatility
    const recentPrices = prices.slice(-14);
    const volatility = this.calculateVolatilityMetric(recentPrices);
    const normalizedVolatility = volatility / recentPrices[recentPrices.length - 1].close.toNumber();

    let regime = MarketRegime.SIDEWAYS;
    let confidence = 0;

    if (normalizedVolatility > this.config.volatilityThreshold * 1.5) {
      // High volatility
      regime = MarketRegime.VOLATILE;
      confidence = Math.min(normalizedVolatility / (this.config.volatilityThreshold * 1.5), 0.9);
    } else if (normalizedVolatility < this.config.volatilityThreshold * 0.5) {
      // Low volatility
      regime = MarketRegime.LOW_VOLATILITY;
      confidence = Math.min((this.config.volatilityThreshold * 0.5) / normalizedVolatility, 0.9);
    } else {
      // Medium volatility - sideways
      confidence = 0.6;
    }

    return {
      regime,
      confidence,
      reasoning: `Volatility analysis: ATR ${atr14.toFixed(5)}, normalized volatility ${normalizedVolatility.toFixed(4)}`,
      weight: 0.2,
    };
  }

  /**
   * Momentum analysis classifier using price momentum and MACD
   */
  private classifyByMomentumAnalysis(input: TechnicalIndicatorInput): ClassifierResult {
    const { macdLine, macdSignal, prices } = input;
    
    if (!macdLine || !macdSignal || prices.length < 20) {
      return {
        regime: MarketRegime.UNKNOWN,
        confidence: 0,
        reasoning: 'Insufficient data for momentum analysis',
        weight: 0.2,
      };
    }

    // Calculate price momentum (rate of change over last 10 periods)
    const momentum = this.calculatePriceMomentum(prices, 10);
    const macdBullish = macdLine.gt(macdSignal);
    const macdStrength = macdLine.minus(macdSignal).abs().toNumber();

    let regime = MarketRegime.SIDEWAYS;
    let confidence = 0;

    if (momentum > this.config.momentumThreshold && macdBullish) {
      // Strong bullish momentum
      regime = MarketRegime.TRENDING_UP;
      confidence = Math.min((momentum + macdStrength) / 2, 0.85);
    } else if (momentum < -this.config.momentumThreshold && !macdBullish) {
      // Strong bearish momentum
      regime = MarketRegime.TRENDING_DOWN;
      confidence = Math.min((Math.abs(momentum) + macdStrength) / 2, 0.85);
    } else {
      // Weak or conflicting momentum
      confidence = 0.5;
    }

    return {
      regime,
      confidence,
      reasoning: `Momentum analysis: Price momentum ${momentum.toFixed(4)}, MACD ${macdBullish ? 'bullish' : 'bearish'}`,
      weight: 0.2,
    };
  }

  /**
   * Mean reversion analysis using price distance from moving averages
   */
  private classifyByMeanReversionAnalysis(input: TechnicalIndicatorInput): ClassifierResult {
    const { sma20, sma50, prices } = input;
    
    if (!sma20 || !sma50 || prices.length < 20) {
      return {
        regime: MarketRegime.UNKNOWN,
        confidence: 0,
        reasoning: 'Insufficient data for mean reversion analysis',
        weight: 0.15,
      };
    }

    const currentPrice = prices[prices.length - 1].close;
    const distanceFromSma20 = currentPrice.minus(sma20).div(sma20).toNumber();
    const distanceFromSma50 = currentPrice.minus(sma50).div(sma50).toNumber();

    // Check if price is extended from moving averages (mean reversion opportunity)
    const isExtendedFromMAs = Math.abs(distanceFromSma20) > 0.02 || Math.abs(distanceFromSma50) > 0.03;
    
    let regime = MarketRegime.SIDEWAYS;
    let confidence = 0.6;

    if (isExtendedFromMAs) {
      // Price is extended - more likely to be in a trending phase
      if (distanceFromSma20 > 0 && distanceFromSma50 > 0) {
        regime = MarketRegime.TRENDING_UP;
      } else if (distanceFromSma20 < 0 && distanceFromSma50 < 0) {
        regime = MarketRegime.TRENDING_DOWN;
      }
      confidence = Math.min(Math.abs(distanceFromSma20) * 20, 0.8);
    }

    return {
      regime,
      confidence,
      reasoning: `Mean reversion: Price ${distanceFromSma20 > 0 ? 'above' : 'below'} SMA20 by ${Math.abs(distanceFromSma20 * 100).toFixed(2)}%`,
      weight: 0.15,
    };
  }

  /**
   * Bollinger Band analysis for regime classification
   */
  private classifyByBollingerBandAnalysis(input: TechnicalIndicatorInput): ClassifierResult {
    const { bollingerUpper, bollingerLower, bollingerMiddle, prices } = input;
    
    if (!bollingerUpper || !bollingerLower || !bollingerMiddle || prices.length < 20) {
      return {
        regime: MarketRegime.UNKNOWN,
        confidence: 0,
        reasoning: 'Insufficient data for Bollinger Band analysis',
        weight: 0.1,
      };
    }

    const currentPrice = prices[prices.length - 1].close;
    const bandWidth = bollingerUpper.minus(bollingerLower).div(bollingerMiddle).toNumber();
    const pricePosition = currentPrice.minus(bollingerLower)
      .div(bollingerUpper.minus(bollingerLower)).toNumber();

    let regime = MarketRegime.SIDEWAYS;
    let confidence = 0.6;

    if (bandWidth < 0.02) {
      // Narrow bands - low volatility
      regime = MarketRegime.LOW_VOLATILITY;
      confidence = 0.7;
    } else if (bandWidth > 0.06) {
      // Wide bands - high volatility
      regime = MarketRegime.VOLATILE;
      confidence = 0.7;
    } else if (pricePosition > 0.8) {
      // Price near upper band - potential uptrend
      regime = MarketRegime.TRENDING_UP;
      confidence = 0.6;
    } else if (pricePosition < 0.2) {
      // Price near lower band - potential downtrend
      regime = MarketRegime.TRENDING_DOWN;
      confidence = 0.6;
    }

    return {
      regime,
      confidence,
      reasoning: `Bollinger analysis: Band width ${(bandWidth * 100).toFixed(2)}%, price position ${(pricePosition * 100).toFixed(1)}%`,
      weight: 0.1,
    };
  }

  /**
   * RSI analysis for overbought/oversold conditions
   */
  private classifyByRSIAnalysis(input: TechnicalIndicatorInput): ClassifierResult {
    const { rsi14 } = input;
    
    if (!rsi14) {
      return {
        regime: MarketRegime.UNKNOWN,
        confidence: 0,
        reasoning: 'RSI data not available',
        weight: 0.1,
      };
    }

    const rsiValue = rsi14.toNumber();
    
    let regime = MarketRegime.SIDEWAYS;
    let confidence = 0.5;

    if (rsiValue > 70) {
      // Overbought - potential downward movement
      regime = MarketRegime.TRENDING_DOWN;
      confidence = Math.min((rsiValue - 70) / 30, 0.7);
    } else if (rsiValue < 30) {
      // Oversold - potential upward movement
      regime = MarketRegime.TRENDING_UP;
      confidence = Math.min((30 - rsiValue) / 30, 0.7);
    } else if (rsiValue > 45 && rsiValue < 55) {
      // Neutral RSI - sideways movement
      confidence = 0.6;
    }

    return {
      regime,
      confidence,
      reasoning: `RSI analysis: RSI14 = ${rsiValue.toFixed(2)}`,
      weight: 0.1,
    };
  }

  /**
   * Combine classifier results using weighted voting
   */
  private combineClassifierResults(results: ClassifierResult[]): { regime: MarketRegime; confidence: number } {
    const regimeVotes: Record<MarketRegime, { totalWeight: number; totalConfidence: number }> = {
      [MarketRegime.TRENDING_UP]: { totalWeight: 0, totalConfidence: 0 },
      [MarketRegime.TRENDING_DOWN]: { totalWeight: 0, totalConfidence: 0 },
      [MarketRegime.SIDEWAYS]: { totalWeight: 0, totalConfidence: 0 },
      [MarketRegime.VOLATILE]: { totalWeight: 0, totalConfidence: 0 },
      [MarketRegime.LOW_VOLATILITY]: { totalWeight: 0, totalConfidence: 0 },
      [MarketRegime.UNKNOWN]: { totalWeight: 0, totalConfidence: 0 },
    };

    // Accumulate weighted votes
    let totalWeight = 0;
    for (const result of results) {
      if (result.confidence > 0) {
        const weightedConfidence = result.confidence * result.weight;
        regimeVotes[result.regime].totalWeight += result.weight;
        regimeVotes[result.regime].totalConfidence += weightedConfidence;
        totalWeight += result.weight;
      }
    }

    // Find winning regime
    let winningRegime = MarketRegime.UNKNOWN;
    let maxScore = 0;

    for (const [regime, votes] of Object.entries(regimeVotes)) {
      if (votes.totalWeight > 0) {
        const score = votes.totalConfidence; // Already weighted
        if (score > maxScore) {
          maxScore = score;
          winningRegime = regime as MarketRegime;
        }
      }
    }

    // Calculate final confidence
    const finalConfidence = totalWeight > 0 ? maxScore / totalWeight : 0;

    return {
      regime: winningRegime,
      confidence: Math.min(finalConfidence, 1.0),
    };
  }

  /**
   * Calculate supporting metrics for the regime detection result
   */
  private calculateSupportingMetrics(input: TechnicalIndicatorInput) {
    const { prices, sma20, sma50, rsi14, atr14, bollingerUpper, bollingerLower, bollingerMiddle } = input;
    
    // Calculate trend strength
    const trendStrength = this.calculateOverallTrendStrength(prices);
    
    // Calculate volatility level
    const volatilityLevel = this.calculateVolatilityLevel(prices, atr14);
    
    // Calculate momentum score
    const momentumScore = this.calculatePriceMomentum(prices, 10);
    
    // Calculate SMA slopes
    const smaSlope20 = this.calculateSMASlope(prices, 20);
    const smaSlope50 = this.calculateSMASlope(prices, 50);
    
    // Calculate Bollinger Band metrics
    const bollingerBandwidth = bollingerUpper && bollingerLower && bollingerMiddle ?
      bollingerUpper.minus(bollingerLower).div(bollingerMiddle).toNumber() : 0;
    
    const pricePosition = bollingerUpper && bollingerLower && prices.length > 0 ?
      prices[prices.length - 1].close.minus(bollingerLower)
        .div(bollingerUpper.minus(bollingerLower)).toNumber() : 0;

    return {
      trendStrength,
      volatilityLevel,
      momentumScore,
      smaSlope20,
      smaSlope50,
      atr14: atr14?.toNumber() || 0,
      rsi14: rsi14?.toNumber() || 50,
      bollingerBandwidth,
      pricePosition: Math.max(0, Math.min(1, pricePosition)),
    };
  }

  /**
   * Detect if regime change occurred
   */
  private detectRegimeChange(
    previousRegime: MarketRegime,
    currentRegime: MarketRegime,
    confidence: number
  ): boolean {
    return previousRegime !== currentRegime && confidence >= this.config.regimeChangeThreshold;
  }

  /**
   * Calculate magnitude of regime change (0-1)
   */
  private calculateRegimeChangeMagnitude(previousRegime: MarketRegime, currentRegime: MarketRegime): number {
    // Define regime change significance matrix
    const changeSignificance: Record<MarketRegime, Record<MarketRegime, number>> = {
      [MarketRegime.TRENDING_UP]: {
        [MarketRegime.TRENDING_DOWN]: 1.0,
        [MarketRegime.SIDEWAYS]: 0.6,
        [MarketRegime.VOLATILE]: 0.7,
        [MarketRegime.LOW_VOLATILITY]: 0.8,
        [MarketRegime.UNKNOWN]: 0.3,
      },
      [MarketRegime.TRENDING_DOWN]: {
        [MarketRegime.TRENDING_UP]: 1.0,
        [MarketRegime.SIDEWAYS]: 0.6,
        [MarketRegime.VOLATILE]: 0.7,
        [MarketRegime.LOW_VOLATILITY]: 0.8,
        [MarketRegime.UNKNOWN]: 0.3,
      },
      [MarketRegime.SIDEWAYS]: {
        [MarketRegime.TRENDING_UP]: 0.6,
        [MarketRegime.TRENDING_DOWN]: 0.6,
        [MarketRegime.VOLATILE]: 0.8,
        [MarketRegime.LOW_VOLATILITY]: 0.4,
        [MarketRegime.UNKNOWN]: 0.2,
      },
      [MarketRegime.VOLATILE]: {
        [MarketRegime.TRENDING_UP]: 0.7,
        [MarketRegime.TRENDING_DOWN]: 0.7,
        [MarketRegime.SIDEWAYS]: 0.8,
        [MarketRegime.LOW_VOLATILITY]: 1.0,
        [MarketRegime.UNKNOWN]: 0.3,
      },
      [MarketRegime.LOW_VOLATILITY]: {
        [MarketRegime.TRENDING_UP]: 0.8,
        [MarketRegime.TRENDING_DOWN]: 0.8,
        [MarketRegime.SIDEWAYS]: 0.4,
        [MarketRegime.VOLATILE]: 1.0,
        [MarketRegime.UNKNOWN]: 0.2,
      },
      [MarketRegime.UNKNOWN]: {
        [MarketRegime.TRENDING_UP]: 0.5,
        [MarketRegime.TRENDING_DOWN]: 0.5,
        [MarketRegime.SIDEWAYS]: 0.3,
        [MarketRegime.VOLATILE]: 0.4,
        [MarketRegime.LOW_VOLATILITY]: 0.3,
      },
    };

    return changeSignificance[previousRegime]?.[currentRegime] || 0.5;
  }

  // ===== Helper Methods =====

  private calculateTrendStrength(prices: any[], direction: 'up' | 'down'): number {
    if (prices.length < 10) return 0;

    const recentPrices = prices.slice(-10).map(p => p.close.toNumber());
    let consistentMoves = 0;

    for (let i = 1; i < recentPrices.length; i++) {
      const move = recentPrices[i] > recentPrices[i - 1];
      if ((direction === 'up' && move) || (direction === 'down' && !move)) {
        consistentMoves++;
      }
    }

    return consistentMoves / (recentPrices.length - 1);
  }

  private calculateVolatilityMetric(prices: any[]): number {
    if (prices.length < 2) return 0;

    const returns = [];
    for (let i = 1; i < prices.length; i++) {
      const prevClose = prices[i - 1].close.toNumber();
      const currentClose = prices[i].close.toNumber();
      returns.push(Math.log(currentClose / prevClose));
    }

    // Calculate standard deviation
    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
    return Math.sqrt(variance);
  }

  private calculatePriceMomentum(prices: any[], periods: number): number {
    if (prices.length < periods + 1) return 0;

    const currentPrice = prices[prices.length - 1].close.toNumber();
    const pastPrice = prices[prices.length - 1 - periods].close.toNumber();
    
    return (currentPrice - pastPrice) / pastPrice;
  }

  private calculateOverallTrendStrength(prices: any[]): number {
    if (prices.length < 20) return 0;

    const recentPrices = prices.slice(-20).map(p => p.close.toNumber());
    const firstPrice = recentPrices[0];
    const lastPrice = recentPrices[recentPrices.length - 1];
    
    // Calculate linear regression slope
    const n = recentPrices.length;
    const sumX = (n * (n - 1)) / 2;
    const sumY = recentPrices.reduce((sum, price) => sum + price, 0);
    const sumXY = recentPrices.reduce((sum, price, i) => sum + i * price, 0);
    const sumX2 = (n * (n - 1) * (2 * n - 1)) / 6;
    
    const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    
    // Normalize slope by average price
    const avgPrice = sumY / n;
    return slope / avgPrice;
  }

  private calculateVolatilityLevel(prices: any[], atr14?: Decimal.Instance): number {
    if (atr14) {
      const avgPrice = prices.slice(-14).reduce((sum, p) => sum + p.close.toNumber(), 0) / 14;
      return atr14.toNumber() / avgPrice;
    }

    return this.calculateVolatilityMetric(prices);
  }

  private calculateSMASlope(prices: any[], periods: number): number {
    if (prices.length < periods + 5) return 0;

    const smas = [];
    for (let i = periods - 1; i < prices.length; i++) {
      const slice = prices.slice(i - periods + 1, i + 1);
      const sma = slice.reduce((sum, p) => sum + p.close.toNumber(), 0) / periods;
      smas.push(sma);
    }

    if (smas.length < 5) return 0;

    const recentSmas = smas.slice(-5);
    const firstSma = recentSmas[0];
    const lastSma = recentSmas[recentSmas.length - 1];
    
    return (lastSma - firstSma) / firstSma;
  }

  private generateResultId(input: TechnicalIndicatorInput): string {
    return `regime_${input.instrument}_${input.timeframe}_${input.timestamp.getTime()}_${Date.now()}`;
  }

  private createErrorResult(input: TechnicalIndicatorInput, error: Error): RegimeDetectionResult {
    return {
      id: this.generateResultId(input),
      instrument: input.instrument,
      timeframe: input.timeframe,
      timestamp: input.timestamp,
      regime: MarketRegime.UNKNOWN,
      confidence: 0,
      confidenceLevel: ConfidenceLevel.VERY_LOW,
      
      trendStrength: 0,
      volatilityLevel: 0,
      momentumScore: 0,
      
      supportingMetrics: {
        smaSlope20: 0,
        smaSlope50: 0,
        atr14: 0,
        rsi14: 50,
        bollingerBandwidth: 0,
        pricePosition: 0.5,
      },
      
      regimeChangeDetected: false,
      
      processingTimeMs: 0,
      source: input.source,
      algorithmVersion: this.algorithmVersion,
    };
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<RegimeDetectionConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config_updated', this.config);
  }

  /**
   * Get current configuration
   */
  public getConfig(): RegimeDetectionConfig {
    return { ...this.config };
  }
}