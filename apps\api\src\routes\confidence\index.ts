import { Router } from 'express';
import quizRouter from './quiz.js';
import assessmentRouter from './assessment.js';
import contentRouter from './content.js';

const router = Router();

// Mount sub-routers
router.use('/quiz', quizRouter);
router.use('/assessment', assessmentRouter);
router.use('/content', contentRouter);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'confidence-assessment-api',
    timestamp: new Date(),
    version: '1.0.0'
  });
});

export default router;