import { Router, Request, Response, NextFunction } from 'express';
import { rateLimit } from 'express-rate-limit';
import { PaperTradingEngine } from '../services/trading/PaperTradingEngine';
import { PaperTradingAnalyticsService } from '../services/trading/PaperTradingAnalyticsService';
import { PaperTradingRequirementsService } from '../services/trading/PaperTradingRequirementsService';
import { AuditService } from '../services/compliance/AuditTrailService';
import { z } from 'zod';

// Rate limiting configuration
const paperTradingRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 100, // General limit for paper trading endpoints
  standardHeaders: true,
  legacyHeaders: false,
});

const sessionCreationLimit = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Limit session creation to 10 per hour
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    error: 'Too many sessions created. Please wait before creating more.',
    code: 'SESSION_CREATION_RATE_LIMIT',
    retryAfter: 3600
  }
});

// Validation schemas
const SessionCreateSchema = z.object({
  name: z.string().min(1, 'Session name is required').max(100, 'Name too long'),
  description: z.string().max(500, 'Description too long').optional(),
  initialBalance: z.number().positive().max(1000000, 'Initial balance too high').default(10000),
  strategyId: z.string().uuid().optional(),
  goalId: z.string().uuid().optional(),
  expiresAt: z.string().datetime().optional(),
});

const SessionUpdateSchema = z.object({
  name: z.string().min(1, 'Session name is required').max(100, 'Name too long').optional(),
  description: z.string().max(500, 'Description too long').optional(),
  status: z.enum(['active', 'paused', 'completed']).optional(),
});

const RequirementsConfigSchema = z.object({
  minimumTrades: z.number().int().positive().max(1000).optional(),
  minimumDays: z.number().int().positive().max(365).optional(),
  minWinRate: z.number().min(0).max(1).optional(),
  maxDrawdown: z.number().min(0).max(1).optional(),
  minSharpeRatio: z.number().min(-5).max(10).optional(),
  minRiskScore: z.number().min(0).max(100).optional(),
  minConsistencyScore: z.number().min(0).max(100).optional(),
});

interface PaperTradingControllers {
  createSessionController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getSessionController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  updateSessionController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  deleteSessionController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getUserSessionsController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getPerformanceController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getRequirementsController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  updateRequirementsController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getLeaderboardController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  exportDataController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
}

function createPaperTradingControllers(
  paperTradingEngine: PaperTradingEngine,
  analyticsService: PaperTradingAnalyticsService,
  requirementsService: PaperTradingRequirementsService,
  auditService: AuditService
): PaperTradingControllers {

  const createSessionController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      const validatedData = SessionCreateSchema.parse(req.body);

      // Check if user has reached maximum active sessions
      const activeSessions = await paperTradingEngine.getUserActiveSessions(userId);
      const maxActiveSessions = 5; // Configurable limit

      if (activeSessions.length >= maxActiveSessions) {
        res.status(400).json({
          success: false,
          error: `Maximum of ${maxActiveSessions} active sessions allowed`,
          code: 'MAX_SESSIONS_EXCEEDED'
        });
        return;
      }

      const session = await paperTradingEngine.createSession(userId, {
        name: validatedData.name,
        description: validatedData.description,
        initialBalance: validatedData.initialBalance,
        strategyId: validatedData.strategyId,
        goalId: validatedData.goalId,
        expiresAt: validatedData.expiresAt ? new Date(validatedData.expiresAt) : undefined,
      });

      // Audit session creation
      await auditService.logActivity({
        userId,
        action: 'PAPER_TRADING_SESSION_CREATED',
        details: {
          sessionId: session.id,
          name: validatedData.name,
          initialBalance: validatedData.initialBalance,
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.status(201).json({
        success: true,
        data: session,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      if (error.name === 'ZodError') {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors
        });
        return;
      }
      next(error);
    }
  };

  const getSessionController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const session = await paperTradingEngine.getSession(userId, sessionId);

      if (!session) {
        res.status(404).json({
          success: false,
          error: 'Session not found',
          code: 'SESSION_NOT_FOUND'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: session,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const updateSessionController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const validatedData = SessionUpdateSchema.parse(req.body);

      const updatedSession = await paperTradingEngine.updateSession(userId, sessionId, validatedData);

      // Audit session update
      await auditService.logActivity({
        userId,
        action: 'PAPER_TRADING_SESSION_UPDATED',
        details: {
          sessionId,
          updates: validatedData,
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.status(200).json({
        success: true,
        data: updatedSession,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      if (error.name === 'ZodError') {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors
        });
        return;
      }
      next(error);
    }
  };

  const deleteSessionController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      await paperTradingEngine.deleteSession(userId, sessionId);

      // Audit session deletion
      await auditService.logActivity({
        userId,
        action: 'PAPER_TRADING_SESSION_DELETED',
        details: {
          sessionId,
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.status(200).json({
        success: true,
        message: 'Session deleted successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const getUserSessionsController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      const { status, limit = '10', offset = '0', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;

      const sessions = await paperTradingEngine.getUserSessions(userId, {
        status: status as string,
        limit: parseInt(limit as string, 10),
        offset: parseInt(offset as string, 10),
        sortBy: sortBy as string,
        sortOrder: sortOrder as 'asc' | 'desc',
      });

      res.status(200).json({
        success: true,
        data: {
          sessions,
          pagination: {
            limit: parseInt(limit as string, 10),
            offset: parseInt(offset as string, 10),
          },
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const getPerformanceController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const analytics = await analyticsService.generateAnalytics(userId, sessionId);

      res.status(200).json({
        success: true,
        data: analytics,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const getRequirementsController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const requirements = await requirementsService.evaluateGraduationCriteria(userId, sessionId);

      res.status(200).json({
        success: true,
        data: requirements,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const updateRequirementsController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const validatedData = RequirementsConfigSchema.parse(req.body);

      const updatedRequirements = await requirementsService.updateGraduationRequirements(
        userId,
        sessionId,
        validatedData
      );

      // Audit requirements update
      await auditService.logActivity({
        userId,
        action: 'PAPER_TRADING_REQUIREMENTS_UPDATED',
        details: {
          sessionId,
          updates: validatedData,
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.status(200).json({
        success: true,
        data: updatedRequirements,
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      if (error.name === 'ZodError') {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors
        });
        return;
      }
      next(error);
    }
  };

  const getLeaderboardController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      const { metric = 'totalReturn', period = '30d', limit = '10' } = req.query;

      const leaderboard = await analyticsService.getLeaderboard({
        metric: metric as string,
        period: period as string,
        limit: parseInt(limit as string, 10),
      });

      res.status(200).json({
        success: true,
        data: {
          leaderboard,
          metric,
          period,
          generatedAt: new Date().toISOString(),
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const exportDataController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const { format = 'json' } = req.query;

      const exportData = await analyticsService.exportSessionData(userId, sessionId, format as string);

      // Set appropriate content type and headers
      if (format === 'csv') {
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="paper-trading-${sessionId}.csv"`);
      } else {
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', `attachment; filename="paper-trading-${sessionId}.json"`);
      }

      // Audit data export
      await auditService.logActivity({
        userId,
        action: 'PAPER_TRADING_DATA_EXPORTED',
        details: {
          sessionId,
          format,
        },
        ipAddress: req.ip,
        userAgent: req.get('User-Agent'),
      });

      res.status(200).send(exportData);

    } catch (error) {
      next(error);
    }
  };

  return {
    createSessionController,
    getSessionController,
    updateSessionController,
    deleteSessionController,
    getUserSessionsController,
    getPerformanceController,
    getRequirementsController,
    updateRequirementsController,
    getLeaderboardController,
    exportDataController,
  };
}

// Export the router factory function
export const createPaperTradingRoutes = (services: {
  paperTradingEngine: PaperTradingEngine;
  analyticsService: PaperTradingAnalyticsService;
  requirementsService: PaperTradingRequirementsService;
  auditService: AuditService;
}) => {
  const router = Router();

  // Apply rate limiting
  router.use(paperTradingRateLimit);

  // Create controllers
  const controllers = createPaperTradingControllers(
    services.paperTradingEngine,
    services.analyticsService,
    services.requirementsService,
    services.auditService
  );

  // === SESSION MANAGEMENT ROUTES ===

  /**
   * @route POST /api/paper-trading/session
   * @desc Create a new paper trading session
   * @access Private
   * @rateLimit 10 requests per hour
   */
  router.post('/session', sessionCreationLimit, controllers.createSessionController);

  /**
   * @route GET /api/paper-trading/session/:sessionId
   * @desc Get paper trading session details
   * @access Private
   */
  router.get('/session/:sessionId', controllers.getSessionController);

  /**
   * @route PUT /api/paper-trading/session/:sessionId
   * @desc Update paper trading session
   * @access Private
   */
  router.put('/session/:sessionId', controllers.updateSessionController);

  /**
   * @route DELETE /api/paper-trading/session/:sessionId
   * @desc Delete paper trading session
   * @access Private
   */
  router.delete('/session/:sessionId', controllers.deleteSessionController);

  /**
   * @route GET /api/paper-trading/sessions
   * @desc Get all user paper trading sessions
   * @access Private
   */
  router.get('/sessions', controllers.getUserSessionsController);

  // === PERFORMANCE ANALYSIS ROUTES ===

  /**
   * @route GET /api/paper-trading/performance/:sessionId
   * @desc Get paper trading performance analytics
   * @access Private
   */
  router.get('/performance/:sessionId', controllers.getPerformanceController);

  // === GRADUATION REQUIREMENTS ROUTES ===

  /**
   * @route GET /api/paper-trading/requirements/:sessionId
   * @desc Get graduation requirements status
   * @access Private
   */
  router.get('/requirements/:sessionId', controllers.getRequirementsController);

  /**
   * @route PUT /api/paper-trading/requirements/:sessionId
   * @desc Update graduation requirements configuration
   * @access Private
   */
  router.put('/requirements/:sessionId', controllers.updateRequirementsController);

  // === COMMUNITY AND GAMIFICATION ROUTES ===

  /**
   * @route GET /api/paper-trading/leaderboard
   * @desc Get paper trading leaderboard
   * @access Private
   */
  router.get('/leaderboard', controllers.getLeaderboardController);

  // === DATA EXPORT ROUTES ===

  /**
   * @route GET /api/paper-trading/export/:sessionId
   * @desc Export paper trading session data
   * @access Private
   */
  router.get('/export/:sessionId', controllers.exportDataController);

  // === ERROR HANDLING ===

  // Handle 404 for unmatched paper trading routes
  router.use((req: Request, res: Response) => {
    res.status(404).json({
      success: false,
      error: 'Paper trading endpoint not found',
      code: 'ENDPOINT_NOT_FOUND',
      availableEndpoints: [
        'POST /api/paper-trading/session',
        'GET /api/paper-trading/session/:sessionId',
        'PUT /api/paper-trading/session/:sessionId',
        'DELETE /api/paper-trading/session/:sessionId',
        'GET /api/paper-trading/sessions',
        'GET /api/paper-trading/performance/:sessionId',
        'GET /api/paper-trading/requirements/:sessionId',
        'PUT /api/paper-trading/requirements/:sessionId',
        'GET /api/paper-trading/leaderboard',
        'GET /api/paper-trading/export/:sessionId'
      ]
    });
  });

  // Global error handler for paper trading routes
  router.use((error: any, req: Request, res: Response, _next: NextFunction) => {
    console.error('Paper trading route error:', {
      error: error.message,
      stack: error.stack,
      path: req.path,
      method: req.method,
      user: req.user?.id,
      timestamp: new Date().toISOString()
    });

    // Handle specific paper trading errors
    if (error.code === 'SESSION_ACCESS_DENIED') {
      res.status(403).json({
        success: false,
        error: 'Access denied to this session',
        code: 'SESSION_ACCESS_DENIED',
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (error.code === 'SESSION_EXPIRED') {
      res.status(400).json({
        success: false,
        error: 'Paper trading session has expired',
        code: 'SESSION_EXPIRED',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Generic error response
    const isDevelopment = process.env.NODE_ENV === 'development';
    res.status(error.status || 500).json({
      success: false,
      error: isDevelopment ? error.message : 'Internal server error',
      code: error.code || 'INTERNAL_ERROR',
      ...(isDevelopment && { stack: error.stack }),
      timestamp: new Date().toISOString()
    });
  });

  return router;
};