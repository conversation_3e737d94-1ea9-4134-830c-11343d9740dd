-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "email" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "deletedAt" DATETIME,
    "displayName" TEXT NOT NULL,
    "riskTolerance" TEXT NOT NULL DEFAULT 'MODERATE',
    "experienceLevel" TEXT NOT NULL DEFAULT 'BEGINNER',
    "coachingStyle" TEXT NOT NULL DEFAULT 'ENCOURAGING',
    "tradingCapital" REAL NOT NULL,
    "notifications" BOOLEAN NOT NULL DEFAULT true,
    "autoOptimization" BOOLEAN NOT NULL DEFAULT false,
    "paperTradingOnly" BOOLEAN NOT NULL DEFAULT true,
    "preferredInstruments" JSONB
);

-- CreateTable
CREATE TABLE "trading_goals" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "targetReturn" REAL NOT NULL,
    "timeframe" INTEGER NOT NULL,
    "maxRisk" REAL NOT NULL,
    "initialCapital" REAL NOT NULL,
    "currentValue" REAL NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'ACTIVE',
    "daysElapsed" INTEGER NOT NULL DEFAULT 0,
    "returnAchieved" REAL NOT NULL,
    "maxDrawdownHit" REAL NOT NULL,
    "tradesExecuted" INTEGER NOT NULL DEFAULT 0,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "deletedAt" DATETIME,
    "targetDate" DATETIME NOT NULL,
    CONSTRAINT "trading_goals_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "strategies" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "type" TEXT NOT NULL,
    "instruments" JSONB,
    "timeframe" TEXT NOT NULL,
    "riskPerTrade" REAL NOT NULL,
    "stopLoss" REAL NOT NULL,
    "takeProfit" REAL NOT NULL,
    "customParams" JSONB,
    "winRate" REAL NOT NULL,
    "profitFactor" REAL NOT NULL,
    "sharpeRatio" REAL NOT NULL,
    "maxDrawdown" REAL NOT NULL,
    "totalReturn" REAL NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'DRAFT',
    "marketRegime" TEXT NOT NULL DEFAULT 'ANY',
    "riskLevel" TEXT NOT NULL DEFAULT 'MODERATE',
    "category" TEXT,
    "author" TEXT,
    "difficulty" TEXT NOT NULL DEFAULT 'INTERMEDIATE',
    "tags" JSONB,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "deletedAt" DATETIME
);

-- CreateTable
CREATE TABLE "user_favorite_strategies" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "strategyId" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "user_favorite_strategies_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "user_favorite_strategies_strategyId_fkey" FOREIGN KEY ("strategyId") REFERENCES "strategies" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "strategy_playlists" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "deletedAt" DATETIME,
    CONSTRAINT "strategy_playlists_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "strategy_playlist_items" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "playlistId" TEXT NOT NULL,
    "strategyId" TEXT NOT NULL,
    "order" INTEGER NOT NULL DEFAULT 0,
    "addedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "strategy_playlist_items_playlistId_fkey" FOREIGN KEY ("playlistId") REFERENCES "strategy_playlists" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "strategy_playlist_items_strategyId_fkey" FOREIGN KEY ("strategyId") REFERENCES "strategies" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "trades" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "strategyId" TEXT,
    "goalId" TEXT,
    "instrument" TEXT NOT NULL,
    "type" TEXT NOT NULL,
    "quantity" REAL NOT NULL,
    "entryPrice" REAL NOT NULL,
    "exitPrice" REAL NOT NULL,
    "stopLoss" REAL NOT NULL,
    "takeProfit" REAL NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'PENDING',
    "executionTime" DATETIME NOT NULL,
    "closeTime" DATETIME,
    "pnl" REAL NOT NULL,
    "pnlPercentage" REAL NOT NULL,
    "fees" REAL NOT NULL,
    "slippage" REAL NOT NULL,
    "marketConditions" JSONB,
    "signalStrength" REAL NOT NULL,
    "riskScore" REAL NOT NULL,
    "autoExecuted" BOOLEAN NOT NULL DEFAULT false,
    "mlPrediction" JSONB,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "trades_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "trades_strategyId_fkey" FOREIGN KEY ("strategyId") REFERENCES "strategies" ("id") ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT "trades_goalId_fkey" FOREIGN KEY ("goalId") REFERENCES "trading_goals" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "market_data" (
    "timestamp" DATETIME NOT NULL,
    "instrument" TEXT NOT NULL,
    "timeframe" TEXT NOT NULL,
    "open" REAL NOT NULL,
    "high" REAL NOT NULL,
    "low" REAL NOT NULL,
    "close" REAL NOT NULL,
    "volume" REAL NOT NULL,
    "source" TEXT NOT NULL,
    "indicators" JSONB,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY ("timestamp", "instrument", "timeframe")
);

-- CreateTable
CREATE TABLE "confidence_assessments" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "currentStage" TEXT NOT NULL DEFAULT 'GOAL_SETTING',
    "overallConfidenceScore" INTEGER NOT NULL DEFAULT 0,
    "knowledgeQuizScore" INTEGER,
    "behavioralScore" INTEGER,
    "performanceScore" INTEGER,
    "stressTestScore" INTEGER,
    "progressHistory" JSONB,
    "graduationCriteria" JSONB,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "confidence_assessments_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "feature_flags" (
    "userId" TEXT NOT NULL PRIMARY KEY,
    "level" TEXT NOT NULL DEFAULT 'BASIC',
    "customFlags" JSONB,
    "rolloutGroup" TEXT,
    "rolloutPercentage" INTEGER NOT NULL DEFAULT 0,
    "enabledAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "expiresAt" DATETIME,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "feature_flags_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "plain_english_metrics" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "created_at" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" DATETIME NOT NULL,
    "metric_type" TEXT NOT NULL,
    "original_value" REAL NOT NULL,
    "plain_english_explanation" TEXT NOT NULL,
    "contextual_advice" TEXT,
    "visual_metadata" JSONB,
    "experience_level" TEXT,
    "risk_tolerance" TEXT,
    "learning_style" TEXT
);

-- CreateTable
CREATE TABLE "audit_logs" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "actionType" TEXT NOT NULL,
    "severity" TEXT NOT NULL DEFAULT 'low',
    "userId" TEXT,
    "brokerId" TEXT,
    "message" TEXT NOT NULL,
    "ipAddress" TEXT,
    "userAgent" TEXT,
    "sessionId" TEXT,
    "success" BOOLEAN NOT NULL DEFAULT true,
    "errorMessage" TEXT,
    "dataHash" TEXT,
    "previousDataHash" TEXT,
    "complianceRelevant" BOOLEAN NOT NULL DEFAULT false,
    "retentionCategory" TEXT NOT NULL DEFAULT 'SHORT_TERM',
    "details" JSONB,
    "metadata" JSONB,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- CreateTable
CREATE TABLE "compliance_reports" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "reportType" TEXT NOT NULL,
    "startDate" DATETIME NOT NULL,
    "endDate" DATETIME NOT NULL,
    "generatedAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "generatedBy" TEXT,
    "totalEvents" INTEGER NOT NULL,
    "criticalEvents" INTEGER NOT NULL,
    "failoverEvents" INTEGER NOT NULL,
    "errorEvents" INTEGER NOT NULL,
    "complianceScore" REAL NOT NULL,
    "dataIntegrityScore" REAL NOT NULL,
    "categoryAnalysis" JSONB NOT NULL,
    "auditTrailIntegrity" JSONB NOT NULL,
    "keyMetrics" JSONB NOT NULL,
    "recommendations" JSONB NOT NULL,
    "detailedLogsPath" TEXT,
    "errorSummaryPath" TEXT,
    "performanceMetricsPath" TEXT,
    "status" TEXT NOT NULL DEFAULT 'GENERATED',
    "reviewedAt" DATETIME,
    "reviewedBy" TEXT,
    "reviewNotes" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "compliance_issues" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "category" TEXT NOT NULL,
    "severity" TEXT NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "affectedPeriodStart" DATETIME NOT NULL,
    "affectedPeriodEnd" DATETIME,
    "relatedAuditIds" JSONB,
    "status" TEXT NOT NULL DEFAULT 'OPEN',
    "resolvedAt" DATETIME,
    "resolvedBy" TEXT,
    "resolution" TEXT,
    "preventiveActions" JSONB,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);

-- CreateTable
CREATE TABLE "broker_configurations" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "brokerName" TEXT NOT NULL,
    "priority" INTEGER NOT NULL,
    "server" TEXT NOT NULL,
    "login" TEXT NOT NULL,
    "password" TEXT NOT NULL,
    "timeout" INTEGER NOT NULL DEFAULT 10000,
    "healthCheckInterval" INTEGER NOT NULL DEFAULT 30000,
    "healthCheckTimeout" INTEGER NOT NULL DEFAULT 5000,
    "retryCount" INTEGER NOT NULL DEFAULT 5,
    "status" TEXT NOT NULL DEFAULT 'INACTIVE',
    "lastHealthCheck" DATETIME,
    "lastError" TEXT,
    "isHealthy" BOOLEAN NOT NULL DEFAULT false,
    "failureCount" INTEGER NOT NULL DEFAULT 0,
    "features" JSONB,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "deletedAt" DATETIME,
    CONSTRAINT "broker_configurations_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "failover_events" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "userId" TEXT NOT NULL,
    "fromBroker" TEXT,
    "toBroker" TEXT NOT NULL,
    "trigger" TEXT NOT NULL,
    "failoverTime" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "recoveryTime" DATETIME,
    "duration" INTEGER,
    "impactedTrades" JSONB,
    "positionsSynced" BOOLEAN NOT NULL DEFAULT false,
    "dataLoss" BOOLEAN NOT NULL DEFAULT false,
    "errorCode" TEXT,
    "errorMessage" TEXT,
    "errorContext" JSONB,
    "recoveryActions" JSONB,
    "manualIntervention" BOOLEAN NOT NULL DEFAULT false,
    "auditTrailId" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "failover_events_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "failover_events_fromBroker_fkey" FOREIGN KEY ("fromBroker") REFERENCES "broker_configurations" ("id") ON DELETE SET NULL ON UPDATE CASCADE
);

-- CreateTable
CREATE TABLE "system_errors" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "errorCode" TEXT NOT NULL,
    "errorType" TEXT NOT NULL,
    "errorCategory" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "stackTrace" TEXT,
    "context" JSONB,
    "component" TEXT NOT NULL,
    "version" TEXT,
    "environment" TEXT,
    "userId" TEXT,
    "sessionId" TEXT,
    "requestId" TEXT,
    "handled" BOOLEAN NOT NULL DEFAULT false,
    "retryCount" INTEGER NOT NULL DEFAULT 0,
    "resolved" BOOLEAN NOT NULL DEFAULT false,
    "resolution" TEXT,
    "circuitState" TEXT,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    "resolvedAt" DATETIME
);

-- CreateTable
CREATE TABLE "broker_health_checks" (
    "id" TEXT NOT NULL PRIMARY KEY,
    "brokerId" TEXT NOT NULL,
    "healthy" BOOLEAN NOT NULL,
    "latency" INTEGER NOT NULL,
    "errorMessage" TEXT,
    "testType" TEXT NOT NULL,
    "testData" JSONB,
    "responseSize" INTEGER,
    "throughput" REAL NOT NULL,
    "timestamp" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT "broker_health_checks_brokerId_fkey" FOREIGN KEY ("brokerId") REFERENCES "broker_configurations" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE INDEX "users_email_idx" ON "users"("email");

-- CreateIndex
CREATE INDEX "users_createdAt_idx" ON "users"("createdAt");

-- CreateIndex
CREATE INDEX "trading_goals_userId_idx" ON "trading_goals"("userId");

-- CreateIndex
CREATE INDEX "trading_goals_status_idx" ON "trading_goals"("status");

-- CreateIndex
CREATE INDEX "trading_goals_createdAt_idx" ON "trading_goals"("createdAt");

-- CreateIndex
CREATE INDEX "strategies_type_idx" ON "strategies"("type");

-- CreateIndex
CREATE INDEX "strategies_status_idx" ON "strategies"("status");

-- CreateIndex
CREATE INDEX "strategies_marketRegime_idx" ON "strategies"("marketRegime");

-- CreateIndex
CREATE INDEX "strategies_riskLevel_idx" ON "strategies"("riskLevel");

-- CreateIndex
CREATE INDEX "strategies_category_idx" ON "strategies"("category");

-- CreateIndex
CREATE INDEX "strategies_createdAt_idx" ON "strategies"("createdAt");

-- CreateIndex
CREATE INDEX "user_favorite_strategies_userId_idx" ON "user_favorite_strategies"("userId");

-- CreateIndex
CREATE INDEX "user_favorite_strategies_strategyId_idx" ON "user_favorite_strategies"("strategyId");

-- CreateIndex
CREATE UNIQUE INDEX "user_favorite_strategies_userId_strategyId_key" ON "user_favorite_strategies"("userId", "strategyId");

-- CreateIndex
CREATE INDEX "strategy_playlists_userId_idx" ON "strategy_playlists"("userId");

-- CreateIndex
CREATE INDEX "strategy_playlists_isPublic_idx" ON "strategy_playlists"("isPublic");

-- CreateIndex
CREATE INDEX "strategy_playlists_createdAt_idx" ON "strategy_playlists"("createdAt");

-- CreateIndex
CREATE INDEX "strategy_playlist_items_playlistId_idx" ON "strategy_playlist_items"("playlistId");

-- CreateIndex
CREATE INDEX "strategy_playlist_items_strategyId_idx" ON "strategy_playlist_items"("strategyId");

-- CreateIndex
CREATE INDEX "strategy_playlist_items_playlistId_order_idx" ON "strategy_playlist_items"("playlistId", "order");

-- CreateIndex
CREATE UNIQUE INDEX "strategy_playlist_items_playlistId_strategyId_key" ON "strategy_playlist_items"("playlistId", "strategyId");

-- CreateIndex
CREATE INDEX "trades_userId_idx" ON "trades"("userId");

-- CreateIndex
CREATE INDEX "trades_strategyId_idx" ON "trades"("strategyId");

-- CreateIndex
CREATE INDEX "trades_goalId_idx" ON "trades"("goalId");

-- CreateIndex
CREATE INDEX "trades_status_idx" ON "trades"("status");

-- CreateIndex
CREATE INDEX "trades_executionTime_idx" ON "trades"("executionTime");

-- CreateIndex
CREATE INDEX "trades_instrument_idx" ON "trades"("instrument");

-- CreateIndex
CREATE INDEX "market_data_instrument_timeframe_timestamp_idx" ON "market_data"("instrument", "timeframe", "timestamp");

-- CreateIndex
CREATE INDEX "market_data_timestamp_idx" ON "market_data"("timestamp");

-- CreateIndex
CREATE INDEX "market_data_instrument_idx" ON "market_data"("instrument");

-- CreateIndex
CREATE UNIQUE INDEX "confidence_assessments_userId_key" ON "confidence_assessments"("userId");

-- CreateIndex
CREATE INDEX "audit_logs_timestamp_idx" ON "audit_logs"("timestamp");

-- CreateIndex
CREATE INDEX "audit_logs_actionType_idx" ON "audit_logs"("actionType");

-- CreateIndex
CREATE INDEX "audit_logs_severity_idx" ON "audit_logs"("severity");

-- CreateIndex
CREATE INDEX "audit_logs_userId_timestamp_idx" ON "audit_logs"("userId", "timestamp");

-- CreateIndex
CREATE INDEX "audit_logs_brokerId_timestamp_idx" ON "audit_logs"("brokerId", "timestamp");

-- CreateIndex
CREATE INDEX "audit_logs_complianceRelevant_idx" ON "audit_logs"("complianceRelevant");

-- CreateIndex
CREATE INDEX "audit_logs_retentionCategory_idx" ON "audit_logs"("retentionCategory");

-- CreateIndex
CREATE INDEX "compliance_reports_reportType_idx" ON "compliance_reports"("reportType");

-- CreateIndex
CREATE INDEX "compliance_reports_generatedAt_idx" ON "compliance_reports"("generatedAt");

-- CreateIndex
CREATE INDEX "compliance_reports_startDate_endDate_idx" ON "compliance_reports"("startDate", "endDate");

-- CreateIndex
CREATE INDEX "compliance_reports_complianceScore_idx" ON "compliance_reports"("complianceScore");

-- CreateIndex
CREATE INDEX "compliance_reports_status_idx" ON "compliance_reports"("status");

-- CreateIndex
CREATE INDEX "compliance_issues_category_idx" ON "compliance_issues"("category");

-- CreateIndex
CREATE INDEX "compliance_issues_severity_idx" ON "compliance_issues"("severity");

-- CreateIndex
CREATE INDEX "compliance_issues_status_idx" ON "compliance_issues"("status");

-- CreateIndex
CREATE INDEX "compliance_issues_createdAt_idx" ON "compliance_issues"("createdAt");

-- CreateIndex
CREATE INDEX "broker_configurations_userId_idx" ON "broker_configurations"("userId");

-- CreateIndex
CREATE INDEX "broker_configurations_priority_idx" ON "broker_configurations"("priority");

-- CreateIndex
CREATE INDEX "broker_configurations_status_idx" ON "broker_configurations"("status");

-- CreateIndex
CREATE INDEX "broker_configurations_isHealthy_idx" ON "broker_configurations"("isHealthy");

-- CreateIndex
CREATE UNIQUE INDEX "broker_configurations_userId_brokerName_key" ON "broker_configurations"("userId", "brokerName");

-- CreateIndex
CREATE INDEX "failover_events_userId_idx" ON "failover_events"("userId");

-- CreateIndex
CREATE INDEX "failover_events_failoverTime_idx" ON "failover_events"("failoverTime");

-- CreateIndex
CREATE INDEX "failover_events_trigger_idx" ON "failover_events"("trigger");

-- CreateIndex
CREATE INDEX "failover_events_toBroker_idx" ON "failover_events"("toBroker");

-- CreateIndex
CREATE INDEX "system_errors_errorCode_idx" ON "system_errors"("errorCode");

-- CreateIndex
CREATE INDEX "system_errors_errorType_idx" ON "system_errors"("errorType");

-- CreateIndex
CREATE INDEX "system_errors_errorCategory_idx" ON "system_errors"("errorCategory");

-- CreateIndex
CREATE INDEX "system_errors_component_idx" ON "system_errors"("component");

-- CreateIndex
CREATE INDEX "system_errors_createdAt_idx" ON "system_errors"("createdAt");

-- CreateIndex
CREATE INDEX "system_errors_userId_idx" ON "system_errors"("userId");

-- CreateIndex
CREATE INDEX "broker_health_checks_brokerId_idx" ON "broker_health_checks"("brokerId");

-- CreateIndex
CREATE INDEX "broker_health_checks_timestamp_idx" ON "broker_health_checks"("timestamp");

-- CreateIndex
CREATE INDEX "broker_health_checks_healthy_idx" ON "broker_health_checks"("healthy");

-- CreateIndex
CREATE INDEX "broker_health_checks_testType_idx" ON "broker_health_checks"("testType");
