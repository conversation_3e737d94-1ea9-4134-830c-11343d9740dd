import { describe, it, expect, beforeEach, vi } from 'vitest';
import { ConfidenceAssessmentService } from '../ConfidenceAssessmentService.js';
import {
  ConfidenceStage,
  QuizDifficulty,
  QuizCategory,
  QuizSessionStatus,
  QuizAttemptStatus
} from '@golddaddy/types';

// Mock PrismaClient
const mockPrisma = {
  quizSession: {
    create: vi.fn(),
    findUnique: vi.fn(),
    update: vi.fn()
  },
  quizResponse: {
    create: vi.fn()
  },
  quizAttempt: {
    create: vi.fn(),
    findFirst: vi.fn(),
    findMany: vi.fn()
  },
  confidenceAssessment: {
    create: vi.fn(),
    findFirst: vi.fn(),
    update: vi.fn()
  }
};

// Mock QuizContentService
vi.mock('../QuizContentService.js', () => ({
  QuizContentService: vi.fn().mockImplementation(() => ({
    generateQuizForStage: vi.fn().mockResolvedValue([
      {
        id: 'q1',
        category: QuizCategory.TRADING_FUNDAMENTALS,
        difficulty: QuizDifficulty.BEGINNER,
        topic: 'Position Sizing',
        question: 'What percentage of capital should you risk?',
        options: [
          { id: 'opt1', text: '1-2%', isCorrect: true },
          { id: 'opt2', text: '10%', isCorrect: false }
        ],
        correctAnswerIds: ['opt1'],
        explanation: 'Risk 1-2% to preserve capital',
        learningResources: [],
        metadata: { estimatedDuration: 30, tags: [], riskLevel: 'low' },
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ])
  }))
}));

// Mock QuizComplexityEngine
vi.mock('../QuizComplexityEngine.js', () => ({
  QuizComplexityEngine: vi.fn().mockImplementation(() => ({
    calculateComplexityRecommendation: vi.fn().mockReturnValue({
      difficulty: QuizDifficulty.BEGINNER,
      questionCount: 15,
      focusCategories: [QuizCategory.TRADING_FUNDAMENTALS],
      timeLimit: 900,
      passingScore: 70,
      reasoning: 'Standard configuration for beginner stage'
    }),
    createUserPerformanceProfile: vi.fn().mockReturnValue({
      averageScore: 75,
      attemptCount: 2,
      consistencyRating: 0.8,
      timeEfficiency: 0.7,
      weakCategories: [],
      strongCategories: [QuizCategory.TRADING_FUNDAMENTALS],
      improvementTrend: 0.1
    })
  }))
}));

describe('ConfidenceAssessmentService', () => {
  let service: ConfidenceAssessmentService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new ConfidenceAssessmentService(mockPrisma as any);
  });

  describe('startQuizSession', () => {
    it('should create a new quiz session with recommended complexity', async () => {
      const mockSession = {
        id: 'session1',
        userId: 'user1',
        stage: ConfidenceStage.GOAL_SETTING,
        difficulty: QuizDifficulty.BEGINNER,
        totalQuestions: 15,
        timeLimit: 900,
        questionsData: [],
        settings: { focusCategories: [], userExperience: 'beginner' },
        status: QuizSessionStatus.IN_PROGRESS,
        startedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      // Mock dependencies
      mockPrisma.quizAttempt.findMany.mockResolvedValue([]);
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue({
        id: 'assessment1',
        userId: 'user1',
        currentStage: ConfidenceStage.GOAL_SETTING,
        overallConfidenceScore: 0,
        assessmentScores: {
          knowledgeQuiz: { score: 0, completedAt: new Date(), attempts: 0, weakAreas: [] },
          behavioralAssessment: { riskTolerance: 50, decisionConsistency: 50, emotionalStability: 50, lastAssessed: new Date() },
          performanceEvaluation: { paperTradingWinRate: 0, riskManagementScore: 0, strategyAdherence: 0, consistencyRating: 0 }
        },
        progressHistory: [],
        graduationCriteria: { nextStage: 'strategy_learning', requirements: { minimumConfidenceScore: 75, requiredAssessments: [], minimumTimeInStage: 7 } },
        createdAt: new Date(),
        updatedAt: new Date()
      });
      mockPrisma.quizSession.create.mockResolvedValue(mockSession);

      const result = await service.startQuizSession({
        userId: 'user1',
        stage: ConfidenceStage.GOAL_SETTING,
        userExperience: 'beginner'
      });

      expect(mockPrisma.quizSession.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'user1',
          stage: ConfidenceStage.GOAL_SETTING,
          difficulty: QuizDifficulty.BEGINNER,
          status: QuizSessionStatus.IN_PROGRESS
        })
      });

      expect(result.id).toBe('session1');
      expect(result.status).toBe(QuizSessionStatus.IN_PROGRESS);
    });

    it('should apply custom settings when provided', async () => {
      mockPrisma.quizAttempt.findMany.mockResolvedValue([]);
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue(null);
      mockPrisma.confidenceAssessment.create.mockResolvedValue({
        id: 'assessment1',
        overallConfidenceScore: 0
      });
      mockPrisma.quizSession.create.mockResolvedValue({
        id: 'session1',
        timeLimit: 1200, // Custom time limit
        totalQuestions: 20 // Custom question count
      });

      await service.startQuizSession({
        userId: 'user1',
        stage: ConfidenceStage.STRATEGY_LEARNING,
        customSettings: {
          timeLimit: 1200,
          questionCount: 20,
          difficulty: QuizDifficulty.INTERMEDIATE
        }
      });

      expect(mockPrisma.quizSession.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          timeLimit: 1200,
          difficulty: QuizDifficulty.INTERMEDIATE
        })
      });
    });
  });

  describe('submitResponse', () => {
    it('should submit a correct response and create response record', async () => {
      const mockSession = {
        id: 'session1',
        userId: 'user1',
        status: QuizSessionStatus.IN_PROGRESS,
        startedAt: new Date(),
        timeLimit: 900,
        questionsData: [
          {
            id: 'q1',
            correctAnswerIds: ['opt1']
          }
        ]
      };

      const mockResponse = {
        id: 'response1',
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt1'],
        isCorrect: true,
        timeSpent: 30,
        confidenceLevel: 4,
        submittedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      };

      mockPrisma.quizSession.findUnique.mockResolvedValue(mockSession);
      mockPrisma.quizResponse.create.mockResolvedValue(mockResponse);

      const result = await service.submitResponse({
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt1'],
        timeSpent: 30,
        confidenceLevel: 4
      });

      expect(mockPrisma.quizResponse.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          sessionId: 'session1',
          questionId: 'q1',
          selectedAnswerIds: ['opt1'],
          isCorrect: true,
          timeSpent: 30,
          confidenceLevel: 4
        })
      });

      expect(result.isCorrect).toBe(true);
    });

    it('should handle incorrect responses', async () => {
      const mockSession = {
        id: 'session1',
        userId: 'user1',
        status: QuizSessionStatus.IN_PROGRESS,
        startedAt: new Date(),
        timeLimit: 900,
        questionsData: [
          {
            id: 'q1',
            correctAnswerIds: ['opt1']
          }
        ]
      };

      mockPrisma.quizSession.findUnique.mockResolvedValue(mockSession);
      mockPrisma.quizResponse.create.mockResolvedValue({
        id: 'response1',
        isCorrect: false
      });

      const result = await service.submitResponse({
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt2'], // Wrong answer
        timeSpent: 45
      });

      expect(mockPrisma.quizResponse.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          isCorrect: false
        })
      });

      expect(result.isCorrect).toBe(false);
    });

    it('should throw error for invalid session', async () => {
      mockPrisma.quizSession.findUnique.mockResolvedValue(null);

      await expect(service.submitResponse({
        sessionId: 'invalid-session',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt1'],
        timeSpent: 30
      })).rejects.toThrow('Invalid quiz session');
    });

    it('should throw error for timed out session', async () => {
      const pastDate = new Date(Date.now() - 2000000); // 33+ minutes ago
      mockPrisma.quizSession.findUnique.mockResolvedValue({
        id: 'session1',
        userId: 'user1',
        status: QuizSessionStatus.IN_PROGRESS,
        startedAt: pastDate,
        timeLimit: 900 // 15 minutes
      });

      await expect(service.submitResponse({
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt1'],
        timeSpent: 30
      })).rejects.toThrow('Quiz session has timed out');
    });
  });

  describe('completeSession', () => {
    it('should complete session and calculate results', async () => {
      const mockSession = {
        id: 'session1',
        userId: 'user1',
        stage: ConfidenceStage.GOAL_SETTING,
        responses: [
          {
            questionId: 'q1',
            isCorrect: true,
            timeSpent: 30,
            confidenceLevel: 4
          },
          {
            questionId: 'q2',
            isCorrect: false,
            timeSpent: 45,
            confidenceLevel: 3
          }
        ],
        totalQuestions: 2,
        timeLimit: 120,
        questionsData: [
          {
            id: 'q1',
            category: QuizCategory.TRADING_FUNDAMENTALS
          },
          {
            id: 'q2',
            category: QuizCategory.RISK_MANAGEMENT
          }
        ]
      };

      const mockAttempt = {
        id: 'attempt1',
        userId: 'user1',
        sessionId: 'session1',
        attemptNumber: 1,
        score: 75,
        feedback: { overall: 'Good performance' }
      };

      mockPrisma.quizSession.findUnique.mockResolvedValue(mockSession);
      mockPrisma.quizAttempt.findFirst.mockResolvedValue(null); // No previous attempts
      mockPrisma.quizAttempt.create.mockResolvedValue(mockAttempt);
      mockPrisma.quizSession.update.mockResolvedValue(mockSession);
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue({
        id: 'assessment1',
        overallConfidenceScore: 70,
        assessmentScores: {
          knowledgeQuiz: { attempts: 0 }
        }
      });
      mockPrisma.confidenceAssessment.update.mockResolvedValue({});
      mockPrisma.quizAttempt.findMany.mockResolvedValue([mockAttempt]);

      const result = await service.completeSession({
        sessionId: 'session1',
        userId: 'user1',
        totalTimeSpent: 75
      });

      expect(mockPrisma.quizAttempt.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          userId: 'user1',
          sessionId: 'session1',
          attemptNumber: 1,
          status: QuizAttemptStatus.COMPLETED
        })
      });

      expect(mockPrisma.quizSession.update).toHaveBeenCalledWith({
        where: { id: 'session1' },
        data: expect.objectContaining({
          status: QuizSessionStatus.COMPLETED
        })
      });

      expect(result.attempt.id).toBe('attempt1');
      expect(result.stagingRecommendation).toBeDefined();
    });

    it('should generate appropriate feedback based on performance', async () => {
      const mockSession = {
        id: 'session1',
        userId: 'user1',
        stage: ConfidenceStage.GOAL_SETTING,
        responses: [
          { questionId: 'q1', isCorrect: true, timeSpent: 20, confidenceLevel: 5 },
          { questionId: 'q2', isCorrect: true, timeSpent: 25, confidenceLevel: 5 },
          { questionId: 'q3', isCorrect: true, timeSpent: 30, confidenceLevel: 4 }
        ],
        totalQuestions: 3,
        timeLimit: 180,
        questionsData: [
          { id: 'q1', category: QuizCategory.TRADING_FUNDAMENTALS },
          { id: 'q2', category: QuizCategory.TRADING_FUNDAMENTALS },
          { id: 'q3', category: QuizCategory.RISK_MANAGEMENT }
        ]
      };

      mockPrisma.quizSession.findUnique.mockResolvedValue(mockSession);
      mockPrisma.quizAttempt.findFirst.mockResolvedValue(null);
      mockPrisma.quizAttempt.create.mockResolvedValue({ id: 'attempt1' });
      mockPrisma.quizSession.update.mockResolvedValue(mockSession);
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue({
        id: 'assessment1',
        overallConfidenceScore: 90,
        assessmentScores: { knowledgeQuiz: { attempts: 0 } }
      });
      mockPrisma.confidenceAssessment.update.mockResolvedValue({});
      mockPrisma.quizAttempt.findMany.mockResolvedValue([]);

      const result = await service.completeSession({
        sessionId: 'session1',
        userId: 'user1',
        totalTimeSpent: 75
      });

      expect(result.feedback.overall).toContain('Excellent');
      expect(result.feedback.strengths).toContain('Strong foundational knowledge');
    });
  });

  describe('getSessionProgress', () => {
    it('should return current progress for active session', async () => {
      const startTime = new Date(Date.now() - 300000); // 5 minutes ago
      const mockSession = {
        id: 'session1',
        userId: 'user1',
        startedAt: startTime,
        timeLimit: 900, // 15 minutes
        totalQuestions: 10,
        responses: [
          { isCorrect: true },
          { isCorrect: false },
          { isCorrect: true }
        ]
      };

      mockPrisma.quizSession.findUnique.mockResolvedValue(mockSession);

      const result = await service.getSessionProgress('session1', 'user1');

      expect(result.questionsAnswered).toBe(3);
      expect(result.totalQuestions).toBe(10);
      expect(result.currentScore).toBe(67); // 2/3 * 100, rounded
      expect(result.timeRemaining).toBeGreaterThan(0);
      expect(result.timeRemaining).toBeLessThanOrEqual(600); // Should have ~10 minutes left
    });

    it('should handle session with no responses', async () => {
      const mockSession = {
        id: 'session1',
        userId: 'user1',
        startedAt: new Date(),
        timeLimit: 900,
        totalQuestions: 10,
        responses: []
      };

      mockPrisma.quizSession.findUnique.mockResolvedValue(mockSession);

      const result = await service.getSessionProgress('session1', 'user1');

      expect(result.questionsAnswered).toBe(0);
      expect(result.currentScore).toBe(0);
    });
  });

  describe('error handling', () => {
    it('should handle database connection errors', async () => {
      mockPrisma.quizSession.create.mockRejectedValue(new Error('Database connection failed'));

      await expect(service.startQuizSession({
        userId: 'user1',
        stage: ConfidenceStage.GOAL_SETTING
      })).rejects.toThrow('Database connection failed');
    });

    it('should validate user permissions for session access', async () => {
      mockPrisma.quizSession.findUnique.mockResolvedValue({
        id: 'session1',
        userId: 'different-user',
        status: QuizSessionStatus.IN_PROGRESS
      });

      await expect(service.submitResponse({
        sessionId: 'session1',
        userId: 'user1',
        questionId: 'q1',
        selectedAnswerIds: ['opt1'],
        timeSpent: 30
      })).rejects.toThrow('Invalid quiz session');
    });
  });

  describe('scoring algorithm', () => {
    it('should calculate scores with appropriate weights', async () => {
      const mockSession = {
        id: 'session1',
        userId: 'user1',
        stage: ConfidenceStage.GOAL_SETTING,
        responses: [
          {
            questionId: 'q1',
            isCorrect: true,
            timeSpent: 180, // Very slow - 3x expected time
            confidenceLevel: 3
          }
        ],
        totalQuestions: 1,
        timeLimit: 60,
        questionsData: [
          { id: 'q1', category: QuizCategory.TRADING_FUNDAMENTALS }
        ]
      };

      mockPrisma.quizSession.findUnique.mockResolvedValue(mockSession);
      mockPrisma.quizAttempt.findFirst.mockResolvedValue(null);
      mockPrisma.quizAttempt.create.mockResolvedValue({ id: 'attempt1', score: 80 });
      mockPrisma.quizSession.update.mockResolvedValue(mockSession);
      mockPrisma.confidenceAssessment.findFirst.mockResolvedValue({
        id: 'assessment1',
        assessmentScores: { knowledgeQuiz: { attempts: 0 } }
      });
      mockPrisma.confidenceAssessment.update.mockResolvedValue({});
      mockPrisma.quizAttempt.findMany.mockResolvedValue([]);

      const result = await service.completeSession({
        sessionId: 'session1',
        userId: 'user1',
        totalTimeSpent: 180
      });

      // Should have high accuracy (100%) but lower time efficiency
      expect(result.attempt.score).toBeGreaterThan(60); // Overall score should still be decent
      expect(result.feedback.weaknesses).toContain('Time management needs improvement');
    });
  });
});