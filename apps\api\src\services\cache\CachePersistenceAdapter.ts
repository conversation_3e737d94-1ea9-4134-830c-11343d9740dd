import { PlainEnglishMetric, MetricType, UserExperienceLevel } from '@golddaddy/types';
import { Redis } from 'ioredis';
import * as fs from 'fs/promises';
import * as path from 'path';

export interface CacheEntry {
  strategyId: string;
  metricType: MetricType;
  metric: PlainEnglishMetric;
  userExperience: UserExperienceLevel;
  cachedAt: Date;
  expiresAt: Date;
  priority: number;
  accessCount: number;
  lastAccessed: Date;
  version: string;
}

export interface PersistenceStats {
  totalEntries: number;
  totalSizeBytes: number;
  oldestEntry: Date;
  newestEntry: Date;
  averageAccessCount: number;
  hitRate: number;
}

export interface PersistenceAdapter {
  save(key: string, entry: CacheEntry): Promise<void>;
  load(key: string): Promise<CacheEntry | null>;
  loadAll(): Promise<Map<string, CacheEntry>>;
  delete(key: string): Promise<boolean>;
  deletePattern(pattern: string): Promise<number>;
  exists(key: string): Promise<boolean>;
  getStats(): Promise<PersistenceStats>;
  cleanup(): Promise<number>;
  clear(): Promise<void>;
}

export class RedisPersistenceAdapter implements PersistenceAdapter {
  private redis: Redis;
  private keyPrefix: string;

  constructor(redis: Redis, keyPrefix = 'metrics:cache:') {
    this.redis = redis;
    this.keyPrefix = keyPrefix;
  }

  async save(key: string, entry: CacheEntry): Promise<void> {
    const redisKey = this.keyPrefix + key;
    const serialized = JSON.stringify({
      ...entry,
      cachedAt: entry.cachedAt.toISOString(),
      expiresAt: entry.expiresAt.toISOString(),
      lastAccessed: entry.lastAccessed.toISOString(),
    });

    const ttlSeconds = Math.max(0, Math.floor((entry.expiresAt.getTime() - Date.now()) / 1000));
    
    if (ttlSeconds > 0) {
      await this.redis.setex(redisKey, ttlSeconds, serialized);
    }
  }

  async load(key: string): Promise<CacheEntry | null> {
    const redisKey = this.keyPrefix + key;
    const serialized = await this.redis.get(redisKey);
    
    if (!serialized) {
      return null;
    }

    try {
      const data = JSON.parse(serialized);
      return {
        ...data,
        cachedAt: new Date(data.cachedAt),
        expiresAt: new Date(data.expiresAt),
        lastAccessed: new Date(data.lastAccessed),
      };
    } catch (error) {
      console.warn(`Failed to parse cache entry for key ${key}:`, error);
      await this.delete(key);
      return null;
    }
  }

  async loadAll(): Promise<Map<string, CacheEntry>> {
    const pattern = `${this.keyPrefix  }*`;
    const keys = await this.redis.keys(pattern);
    const entries = new Map<string, CacheEntry>();

    if (keys.length === 0) {
      return entries;
    }

    const values = await this.redis.mget(...keys);
    
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i].substring(this.keyPrefix.length);
      const value = values[i];
      
      if (value) {
        try {
          const data = JSON.parse(value);
          entries.set(key, {
            ...data,
            cachedAt: new Date(data.cachedAt),
            expiresAt: new Date(data.expiresAt),
            lastAccessed: new Date(data.lastAccessed),
          });
        } catch (error) {
          console.warn(`Failed to parse cache entry for key ${key}:`, error);
          await this.delete(key);
        }
      }
    }

    return entries;
  }

  async delete(key: string): Promise<boolean> {
    const redisKey = this.keyPrefix + key;
    const result = await this.redis.del(redisKey);
    return result > 0;
  }

  async deletePattern(pattern: string): Promise<number> {
    const searchPattern = this.keyPrefix + pattern;
    const keys = await this.redis.keys(searchPattern);
    
    if (keys.length === 0) {
      return 0;
    }

    return await this.redis.del(...keys);
  }

  async exists(key: string): Promise<boolean> {
    const redisKey = this.keyPrefix + key;
    const result = await this.redis.exists(redisKey);
    return result === 1;
  }

  async getStats(): Promise<PersistenceStats> {
    const entries = await this.loadAll();
    const values = Array.from(entries.values());

    if (values.length === 0) {
      return {
        totalEntries: 0,
        totalSizeBytes: 0,
        oldestEntry: new Date(),
        newestEntry: new Date(),
        averageAccessCount: 0,
        hitRate: 0,
      };
    }

    const totalSizeBytes = values.reduce((sum, entry) => {
      return sum + JSON.stringify(entry).length;
    }, 0);

    const accessCounts = values.map(e => e.accessCount);
    const averageAccessCount = accessCounts.reduce((sum, count) => sum + count, 0) / accessCounts.length;

    const dates = values.map(e => e.cachedAt.getTime());
    const oldestEntry = new Date(Math.min(...dates));
    const newestEntry = new Date(Math.max(...dates));

    // Calculate hit rate based on access patterns
    const totalAccesses = accessCounts.reduce((sum, count) => sum + count, 0);
    const hitRate = totalAccesses > 0 ? totalAccesses / values.length : 0;

    return {
      totalEntries: values.length,
      totalSizeBytes,
      oldestEntry,
      newestEntry,
      averageAccessCount,
      hitRate,
    };
  }

  async cleanup(): Promise<number> {
    const now = Date.now();
    const entries = await this.loadAll();
    let cleanedCount = 0;

    for (const [key, entry] of entries) {
      if (entry.expiresAt.getTime() <= now) {
        await this.delete(key);
        cleanedCount++;
      }
    }

    return cleanedCount;
  }

  async clear(): Promise<void> {
    const pattern = `${this.keyPrefix  }*`;
    const keys = await this.redis.keys(pattern);
    
    if (keys.length > 0) {
      await this.redis.del(...keys);
    }
  }
}

export class FilePersistenceAdapter implements PersistenceAdapter {
  private cacheDir: string;
  private indexFile: string;

  constructor(cacheDir = './cache/metrics') {
    this.cacheDir = cacheDir;
    this.indexFile = path.join(cacheDir, 'index.json');
  }

  private async ensureCacheDir(): Promise<void> {
    try {
      await fs.access(this.cacheDir);
    } catch {
      await fs.mkdir(this.cacheDir, { recursive: true });
    }
  }

  private getFilePath(key: string): string {
    const safeKey = key.replace(/[^a-zA-Z0-9-_]/g, '_');
    return path.join(this.cacheDir, `${safeKey}.json`);
  }

  async save(key: string, entry: CacheEntry): Promise<void> {
    await this.ensureCacheDir();
    
    const filePath = this.getFilePath(key);
    const serialized = JSON.stringify({
      ...entry,
      cachedAt: entry.cachedAt.toISOString(),
      expiresAt: entry.expiresAt.toISOString(),
      lastAccessed: entry.lastAccessed.toISOString(),
    }, null, 2);

    await fs.writeFile(filePath, serialized, 'utf8');
    await this.updateIndex(key, entry.expiresAt);
  }

  async load(key: string): Promise<CacheEntry | null> {
    try {
      const filePath = this.getFilePath(key);
      const content = await fs.readFile(filePath, 'utf8');
      const data = JSON.parse(content);
      
      const entry: CacheEntry = {
        ...data,
        cachedAt: new Date(data.cachedAt),
        expiresAt: new Date(data.expiresAt),
        lastAccessed: new Date(data.lastAccessed),
      };

      // Check if expired
      if (entry.expiresAt.getTime() <= Date.now()) {
        await this.delete(key);
        return null;
      }

      return entry;
    } catch (error) {
      if ((error as any).code !== 'ENOENT') {
        console.warn(`Failed to load cache entry for key ${key}:`, error);
      }
      return null;
    }
  }

  async loadAll(): Promise<Map<string, CacheEntry>> {
    const entries = new Map<string, CacheEntry>();
    
    try {
      await this.ensureCacheDir();
      const files = await fs.readdir(this.cacheDir);
      const jsonFiles = files.filter(file => file.endsWith('.json') && file !== 'index.json');

      for (const file of jsonFiles) {
        const key = file.replace('.json', '').replace(/_/g, ':');
        const entry = await this.load(key);
        if (entry) {
          entries.set(key, entry);
        }
      }
    } catch (error) {
      console.warn('Failed to load cache entries:', error);
    }

    return entries;
  }

  async delete(key: string): Promise<boolean> {
    try {
      const filePath = this.getFilePath(key);
      await fs.unlink(filePath);
      await this.removeFromIndex(key);
      return true;
    } catch (error) {
      if ((error as any).code !== 'ENOENT') {
        console.warn(`Failed to delete cache entry for key ${key}:`, error);
      }
      return false;
    }
  }

  async deletePattern(pattern: string): Promise<number> {
    const entries = await this.loadAll();
    let deletedCount = 0;

    const regex = new RegExp(pattern.replace(/\*/g, '.*'));
    
    for (const key of entries.keys()) {
      if (regex.test(key)) {
        const deleted = await this.delete(key);
        if (deleted) deletedCount++;
      }
    }

    return deletedCount;
  }

  async exists(key: string): Promise<boolean> {
    try {
      const filePath = this.getFilePath(key);
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async getStats(): Promise<PersistenceStats> {
    const entries = await this.loadAll();
    const values = Array.from(entries.values());

    if (values.length === 0) {
      return {
        totalEntries: 0,
        totalSizeBytes: 0,
        oldestEntry: new Date(),
        newestEntry: new Date(),
        averageAccessCount: 0,
        hitRate: 0,
      };
    }

    let totalSizeBytes = 0;
    for (const [key] of entries) {
      try {
        const filePath = this.getFilePath(key);
        const stats = await fs.stat(filePath);
        totalSizeBytes += stats.size;
      } catch {
        // Ignore errors for individual files
      }
    }

    const accessCounts = values.map(e => e.accessCount);
    const averageAccessCount = accessCounts.reduce((sum, count) => sum + count, 0) / accessCounts.length;

    const dates = values.map(e => e.cachedAt.getTime());
    const oldestEntry = new Date(Math.min(...dates));
    const newestEntry = new Date(Math.max(...dates));

    const totalAccesses = accessCounts.reduce((sum, count) => sum + count, 0);
    const hitRate = totalAccesses > 0 ? totalAccesses / values.length : 0;

    return {
      totalEntries: values.length,
      totalSizeBytes,
      oldestEntry,
      newestEntry,
      averageAccessCount,
      hitRate,
    };
  }

  async cleanup(): Promise<number> {
    const entries = await this.loadAll();
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of entries) {
      if (entry.expiresAt.getTime() <= now) {
        const deleted = await this.delete(key);
        if (deleted) cleanedCount++;
      }
    }

    return cleanedCount;
  }

  async clear(): Promise<void> {
    try {
      await this.ensureCacheDir();
      const files = await fs.readdir(this.cacheDir);
      
      for (const file of files) {
        if (file.endsWith('.json')) {
          await fs.unlink(path.join(this.cacheDir, file));
        }
      }
    } catch (error) {
      console.warn('Failed to clear cache directory:', error);
    }
  }

  private async updateIndex(key: string, expiresAt: Date): Promise<void> {
    try {
      let index: Record<string, string> = {};
      
      try {
        const content = await fs.readFile(this.indexFile, 'utf8');
        index = JSON.parse(content);
      } catch {
        // Index doesn't exist or is invalid, start fresh
      }

      index[key] = expiresAt.toISOString();
      await fs.writeFile(this.indexFile, JSON.stringify(index, null, 2), 'utf8');
    } catch (error) {
      console.warn('Failed to update cache index:', error);
    }
  }

  private async removeFromIndex(key: string): Promise<void> {
    try {
      const content = await fs.readFile(this.indexFile, 'utf8');
      const index = JSON.parse(content);
      delete index[key];
      await fs.writeFile(this.indexFile, JSON.stringify(index, null, 2), 'utf8');
    } catch (error) {
      // Ignore errors when removing from index
    }
  }
}