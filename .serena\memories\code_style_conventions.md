# Code Style and Conventions

## File Naming Conventions
- **Components**: PascalCase (e.g., `TradingDashboard.tsx`)
- **Utilities**: camelCase (e.g., `calculateMetrics.ts`)
- **Tests**: `*.test.ts` or `*.spec.ts`
- **Types**: `*.types.ts` or in `/types` directories

## Import/Export Conventions
- Workspace packages imported as `@golddaddy/package-name`
- Absolute imports used within packages (configured in `tsconfig.json`)
- Barrel exports in shared packages (`src/index.ts`)

## TypeScript Configuration
- Strict mode enabled
- Target: ES2022
- Module: esnext with bundler resolution
- Cross-workspace type safety via project references

## ESLint Rules (Key Points)
- `@typescript-eslint/no-unused-vars`: error (with `_` prefix ignore pattern)
- `@typescript-eslint/no-explicit-any`: warn (progressive adoption)
- `no-console`: warn (off in server apps and tests)
- `prefer-const`: error
- `no-var`: error
- `object-shorthand`: error
- `prefer-template`: error

## Code Organization Patterns
- Each app/package is independent workspace with own `package.json`
- Shared packages referenced using workspace dependencies (`*`)
- Monorepo workspace architecture enforced

## Error Handling
- API errors use structured error responses with status codes
- Frontend error boundaries for React component error recovery
- Comprehensive logging via centralized logging service

## Security Standards
- Row-level security policies in PostgreSQL
- Environment variables for secrets (never committed)
- API rate limiting and CORS protection