/**
 * Regime Pattern Matcher Service
 * 
 * Advanced pattern matching and prediction service for market regimes using:
 * - Sequence similarity algorithms
 * - Weighted pattern scoring
 * - Real-time pattern recognition
 * - Adaptive learning from historical accuracy
 */

import { EventEmitter } from 'events';
import Decimal from 'decimal.js';
import {
  MarketRegime,
  RegimeDetectionResult,
  TimeFrame,
} from '@golddaddy/types';

// Pattern matching configuration
interface PatternMatchingConfig {
  maxPatternLength: number;
  minSimilarityThreshold: number;
  temporalWeightDecay: number; // How much older patterns are discounted
  adaptiveLearningRate: number;
  predictionHorizon: number; // Minutes into the future
  confidenceThreshold: number;
  useWeightedScoring: boolean;
  enableAdaptiveLearning: boolean;
}

// Pattern sequence with metadata
export interface RegimePattern {
  id: string;
  sequence: MarketRegime[];
  occurrences: PatternOccurrence[];
  strength: number; // 0-1, based on frequency and success rate
  reliability: number; // 0-1, based on historical accuracy
  lastSeen: Date;
  predictiveValue: number; // How well this pattern predicts future regimes
  contextualFactors: {
    timeOfDay: number[]; // Hours when pattern typically occurs
    marketConditions: string[];
    volatilityRange: [number, number];
  };
}

// Individual pattern occurrence
interface PatternOccurrence {
  timestamp: Date;
  instrument: string;
  timeframe: TimeFrame;
  confidence: number;
  followedByRegime?: MarketRegime;
  predictionAccuracy?: number;
  contextScore: number; // How well context matched
}

// Pattern matching result
export interface PatternMatchResult {
  patterns: MatchedPattern[];
  predictions: RegimePrediction[];
  confidence: number;
  matchingTimeMs: number;
}

// Matched pattern with similarity score
interface MatchedPattern {
  pattern: RegimePattern;
  similarity: number; // 0-1
  contextualMatch: number; // 0-1
  temporalRelevance: number; // 0-1, based on recency
  overallScore: number;
  alignment: {
    patternIndex: number;
    sequenceIndex: number;
    matchLength: number;
  };
}

// Regime prediction from patterns
export interface RegimePrediction {
  regime: MarketRegime;
  confidence: number;
  timeHorizon: number; // minutes
  supportingPatterns: string[];
  riskFactors: string[];
  alternativeOutcomes: {
    regime: MarketRegime;
    probability: number;
  }[];
}

/**
 * Regime Pattern Matcher - Advanced pattern recognition and prediction
 */
export class RegimePatternMatcher extends EventEmitter {
  private config: PatternMatchingConfig;
  private learnedPatterns = new Map<string, RegimePattern>();
  private recentSequences = new Map<string, MarketRegime[]>();
  private accuracyHistory = new Map<string, number[]>();
  private predictionHistory: Map<string, {
    prediction: RegimePrediction;
    actual?: MarketRegime;
    timestamp: Date;
  }[]> = new Map();

  // Performance metrics
  private metrics = {
    totalMatches: 0,
    successfulPredictions: 0,
    averageMatchTime: 0,
    patternReliabilityScore: 0,
    adaptiveAccuracyImprovement: 0,
  };

  constructor(config?: Partial<PatternMatchingConfig>) {
    super();
    
    this.config = {
      maxPatternLength: 10,
      minSimilarityThreshold: 0.6,
      temporalWeightDecay: 0.95, // 5% decay per time period
      adaptiveLearningRate: 0.1,
      predictionHorizon: 60, // 1 hour
      confidenceThreshold: 0.7,
      useWeightedScoring: true,
      enableAdaptiveLearning: true,
      ...config,
    };
  }

  /**
   * Find matching patterns for a given regime sequence
   */
  public async findMatchingPatterns(
    recentSequence: MarketRegime[],
    instrument: string,
    timeframe: TimeFrame,
    currentContext?: {
      timeOfDay: number;
      volatility: number;
      marketConditions: string[];
    }
  ): Promise<PatternMatchResult> {
    const startTime = Date.now();
    
    try {
      // Get patterns to match against
      const candidatePatterns = this.getCandidatePatterns(instrument, timeframe);
      
      // Find pattern matches
      const matches = await this.performPatternMatching(
        recentSequence,
        candidatePatterns,
        currentContext
      );
      
      // Generate predictions from matches
      const predictions = this.generatePredictionsFromMatches(matches, currentContext);
      
      // Calculate overall confidence
      const confidence = this.calculateOverallConfidence(matches, predictions);
      
      const result: PatternMatchResult = {
        patterns: matches,
        predictions,
        confidence,
        matchingTimeMs: Date.now() - startTime,
      };
      
      // Update metrics
      this.updateMetrics(result);
      
      this.emit('pattern_matching_complete', result);
      return result;

    } catch (error) {
      this.emit('pattern_matching_error', {
        instrument,
        timeframe,
        error: error instanceof Error ? error.message : 'Unknown error',
        processingTime: Date.now() - startTime,
      });
      throw error;
    }
  }

  /**
   * Learn new patterns from regime sequences
   */
  public async learnFromSequence(
    sequence: MarketRegime[],
    instrument: string,
    timeframe: TimeFrame,
    followedByRegime?: MarketRegime,
    context?: {
      timeOfDay: number;
      volatility: number;
      marketConditions: string[];
      confidence: number;
    }
  ): Promise<void> {
    // Extract patterns of various lengths
    for (let length = 3; length <= Math.min(sequence.length, this.config.maxPatternLength); length++) {
      for (let start = 0; start <= sequence.length - length; start++) {
        const pattern = sequence.slice(start, start + length);
        const patternKey = this.generatePatternKey(pattern, instrument, timeframe);
        
        await this.updateOrCreatePattern(
          patternKey,
          pattern,
          instrument,
          timeframe,
          followedByRegime,
          context
        );
      }
    }
    
    // Update recent sequences for this instrument/timeframe
    const sequenceKey = `${instrument}_${timeframe}`;
    this.recentSequences.set(sequenceKey, sequence);
    
    this.emit('pattern_learned', { instrument, timeframe, patternCount: this.learnedPatterns.size });
  }

  /**
   * Verify prediction accuracy and adapt learning
   */
  public async verifyPrediction(
    predictionId: string,
    actualRegime: MarketRegime,
    instrument: string,
    timeframe: TimeFrame
  ): Promise<void> {
    const historyKey = `${instrument}_${timeframe}`;
    const history = this.predictionHistory.get(historyKey) || [];
    
    // Find the prediction
    const predictionEntry = history.find(entry => 
      entry.prediction.regime === actualRegime || 
      entry.prediction.alternativeOutcomes.some(alt => alt.regime === actualRegime)
    );
    
    if (predictionEntry) {
      predictionEntry.actual = actualRegime;
      
      // Calculate accuracy
      const wasCorrect = predictionEntry.prediction.regime === actualRegime;
      const accuracy = wasCorrect ? 1.0 : 
        predictionEntry.prediction.alternativeOutcomes.find(alt => alt.regime === actualRegime)?.probability || 0;
      
      // Update accuracy tracking
      this.updateAccuracyHistory(historyKey, accuracy);
      
      // Adaptive learning - update pattern weights based on performance
      if (this.config.enableAdaptiveLearning) {
        await this.adaptivelyUpdatePatterns(predictionEntry.prediction, accuracy);
      }
      
      this.emit('prediction_verified', {
        predictionId,
        actualRegime,
        wasCorrect,
        accuracy,
        instrument,
        timeframe,
      });
    }
  }

  /**
   * Get pattern matching statistics and performance metrics
   */
  public getPatternStats(): {
    totalPatterns: number;
    activePatterns: number;
    averageReliability: number;
    predictionAccuracy: number;
    metrics: typeof this.metrics;
  } {
    const totalPatterns = this.learnedPatterns.size;
    const activePatterns = Array.from(this.learnedPatterns.values())
      .filter(p => p.reliability > this.config.minSimilarityThreshold).length;
    
    const avgReliability = totalPatterns > 0 ? 
      Array.from(this.learnedPatterns.values())
        .reduce((sum, p) => sum + p.reliability, 0) / totalPatterns : 0;
    
    const allAccuracies = Array.from(this.accuracyHistory.values()).flat();
    const predictionAccuracy = allAccuracies.length > 0 ?
      allAccuracies.reduce((sum, acc) => sum + acc, 0) / allAccuracies.length : 0;
    
    return {
      totalPatterns,
      activePatterns,
      averageReliability: avgReliability,
      predictionAccuracy,
      metrics: { ...this.metrics },
    };
  }

  // ===== Private Methods =====

  private getCandidatePatterns(instrument: string, timeframe: TimeFrame): RegimePattern[] {
    return Array.from(this.learnedPatterns.values())
      .filter(pattern => 
        pattern.reliability > this.config.minSimilarityThreshold &&
        pattern.occurrences.some(occ => 
          occ.instrument === instrument && occ.timeframe === timeframe
        )
      )
      .sort((a, b) => b.reliability - a.reliability);
  }

  private async performPatternMatching(
    sequence: MarketRegime[],
    patterns: RegimePattern[],
    context?: {
      timeOfDay: number;
      volatility: number;
      marketConditions: string[];
    }
  ): Promise<MatchedPattern[]> {
    const matches: MatchedPattern[] = [];
    
    for (const pattern of patterns) {
      const alignment = this.findBestAlignment(sequence, pattern.sequence);
      if (!alignment || alignment.similarity < this.config.minSimilarityThreshold) {
        continue;
      }
      
      // Calculate contextual match
      const contextualMatch = context ? this.calculateContextualMatch(pattern, context) : 1.0;
      
      // Calculate temporal relevance (more recent patterns are more relevant)
      const temporalRelevance = this.calculateTemporalRelevance(pattern.lastSeen);
      
      // Calculate overall score
      const overallScore = this.config.useWeightedScoring ? 
        this.calculateWeightedScore(alignment.similarity, contextualMatch, temporalRelevance, pattern.reliability) :
        alignment.similarity;
      
      matches.push({
        pattern,
        similarity: alignment.similarity,
        contextualMatch,
        temporalRelevance,
        overallScore,
        alignment,
      });
    }
    
    // Sort by overall score
    return matches
      .sort((a, b) => b.overallScore - a.overallScore)
      .slice(0, 10); // Keep top 10 matches
  }

  private findBestAlignment(
    sequence: MarketRegime[],
    pattern: MarketRegime[]
  ): { 
    similarity: number;
    patternIndex: number;
    sequenceIndex: number;
    matchLength: number;
  } | null {
    let bestMatch = {
      similarity: 0,
      patternIndex: 0,
      sequenceIndex: 0,
      matchLength: 0,
    };
    
    // Try to align pattern at different positions in sequence
    for (let seqStart = 0; seqStart <= sequence.length - pattern.length; seqStart++) {
      for (let patStart = 0; patStart < pattern.length; patStart++) {
        const maxLength = Math.min(pattern.length - patStart, sequence.length - seqStart);
        let matches = 0;
        
        for (let i = 0; i < maxLength; i++) {
          if (sequence[seqStart + i] === pattern[patStart + i]) {
            matches++;
          }
        }
        
        const similarity = matches / Math.max(maxLength, 1);
        
        if (similarity > bestMatch.similarity) {
          bestMatch = {
            similarity,
            patternIndex: patStart,
            sequenceIndex: seqStart,
            matchLength: maxLength,
          };
        }
      }
    }
    
    return bestMatch.similarity > 0 ? bestMatch : null;
  }

  private calculateContextualMatch(
    pattern: RegimePattern,
    context: {
      timeOfDay: number;
      volatility: number;
      marketConditions: string[];
    }
  ): number {
    let score = 0;
    let factors = 0;
    
    // Time of day match
    if (pattern.contextualFactors.timeOfDay.length > 0) {
      const timeMatch = pattern.contextualFactors.timeOfDay.includes(context.timeOfDay) ? 1 : 
        this.calculateTimeProximity(context.timeOfDay, pattern.contextualFactors.timeOfDay);
      score += timeMatch;
      factors++;
    }
    
    // Volatility match
    if (pattern.contextualFactors.volatilityRange) {
      const [min, max] = pattern.contextualFactors.volatilityRange;
      const volatilityMatch = context.volatility >= min && context.volatility <= max ? 1 :
        this.calculateVolatilityProximity(context.volatility, min, max);
      score += volatilityMatch;
      factors++;
    }
    
    // Market conditions match
    if (pattern.contextualFactors.marketConditions.length > 0) {
      const conditionMatches = context.marketConditions.filter(condition =>
        pattern.contextualFactors.marketConditions.includes(condition)
      ).length;
      const conditionMatch = conditionMatches / Math.max(pattern.contextualFactors.marketConditions.length, 1);
      score += conditionMatch;
      factors++;
    }
    
    return factors > 0 ? score / factors : 1.0;
  }

  private calculateTemporalRelevance(lastSeen: Date): number {
    const now = new Date();
    const hoursSinceLastSeen = (now.getTime() - lastSeen.getTime()) / (1000 * 60 * 60);
    
    // Exponential decay based on temporal weight decay
    return Math.pow(this.config.temporalWeightDecay, hoursSinceLastSeen);
  }

  private calculateWeightedScore(
    similarity: number,
    contextualMatch: number,
    temporalRelevance: number,
    reliability: number
  ): number {
    // Weighted combination of different factors
    const weights = {
      similarity: 0.35,
      contextual: 0.25,
      temporal: 0.2,
      reliability: 0.2,
    };
    
    return (
      similarity * weights.similarity +
      contextualMatch * weights.contextual +
      temporalRelevance * weights.temporal +
      reliability * weights.reliability
    );
  }

  private generatePredictionsFromMatches(
    matches: MatchedPattern[],
    context?: {
      timeOfDay: number;
      volatility: number;
      marketConditions: string[];
    }
  ): RegimePrediction[] {
    if (matches.length === 0) return [];
    
    // Aggregate predictions from top matches
    const regimeProbabilities = new Map<MarketRegime, number>();
    const supportingPatterns = new Map<MarketRegime, string[]>();
    
    matches.forEach(match => {
      // Get what typically follows this pattern
      const followedByRegimes = match.pattern.occurrences
        .filter(occ => occ.followedByRegime)
        .map(occ => occ.followedByRegime!);
      
      if (followedByRegimes.length > 0) {
        // Count occurrences of each following regime
        const regimeCounts = followedByRegimes.reduce((acc, regime) => {
          acc[regime] = (acc[regime] || 0) + 1;
          return acc;
        }, {} as Record<MarketRegime, number>);
        
        // Weight by match score
        Object.entries(regimeCounts).forEach(([regime, count]) => {
          const probability = (count / followedByRegimes.length) * match.overallScore;
          const currentProb = regimeProbabilities.get(regime as MarketRegime) || 0;
          regimeProbabilities.set(regime as MarketRegime, currentProb + probability);
          
          // Track supporting patterns
          const existing = supportingPatterns.get(regime as MarketRegime) || [];
          if (!existing.includes(match.pattern.id)) {
            existing.push(match.pattern.id);
            supportingPatterns.set(regime as MarketRegime, existing);
          }
        });
      }
    });
    
    // Normalize probabilities
    const totalProbability = Array.from(regimeProbabilities.values()).reduce((sum, p) => sum + p, 0);
    if (totalProbability > 0) {
      regimeProbabilities.forEach((prob, regime) => {
        regimeProbabilities.set(regime, prob / totalProbability);
      });
    }
    
    // Create predictions
    const predictions: RegimePrediction[] = [];
    const sortedProbabilities = Array.from(regimeProbabilities.entries())
      .sort(([,a], [,b]) => b - a);
    
    if (sortedProbabilities.length > 0) {
      const [topRegime, topProb] = sortedProbabilities[0];
      
      // Create main prediction
      const prediction: RegimePrediction = {
        regime: topRegime,
        confidence: Math.min(topProb * 100, 95), // Cap at 95%
        timeHorizon: this.config.predictionHorizon,
        supportingPatterns: supportingPatterns.get(topRegime) || [],
        riskFactors: this.identifyRiskFactors(matches, topRegime, context),
        alternativeOutcomes: sortedProbabilities.slice(1, 4).map(([regime, prob]) => ({
          regime,
          probability: prob,
        })),
      };
      
      predictions.push(prediction);
    }
    
    return predictions;
  }

  private identifyRiskFactors(
    matches: MatchedPattern[],
    predictedRegime: MarketRegime,
    context?: {
      timeOfDay: number;
      volatility: number;
      marketConditions: string[];
    }
  ): string[] {
    const riskFactors: string[] = [];
    
    // Check for conflicting patterns
    const conflictingMatches = matches.filter(match => {
      const followedBy = match.pattern.occurrences
        .map(occ => occ.followedByRegime)
        .filter(regime => regime && regime !== predictedRegime);
      return followedBy.length > 0;
    });
    
    if (conflictingMatches.length > 0) {
      riskFactors.push('conflicting_patterns');
    }
    
    // Check for low reliability patterns
    const lowReliabilityCount = matches.filter(m => m.pattern.reliability < 0.7).length;
    if (lowReliabilityCount > matches.length * 0.5) {
      riskFactors.push('low_pattern_reliability');
    }
    
    // Check contextual risks
    if (context) {
      if (context.volatility > 0.8) {
        riskFactors.push('high_volatility_environment');
      }
      
      if (context.marketConditions.includes('news_event') || 
          context.marketConditions.includes('economic_announcement')) {
        riskFactors.push('external_market_events');
      }
    }
    
    // Check temporal risks
    const oldPatterns = matches.filter(m => m.temporalRelevance < 0.5).length;
    if (oldPatterns > matches.length * 0.3) {
      riskFactors.push('outdated_patterns');
    }
    
    return riskFactors;
  }

  private calculateOverallConfidence(
    matches: MatchedPattern[],
    predictions: RegimePrediction[]
  ): number {
    if (matches.length === 0 || predictions.length === 0) return 0;
    
    const avgMatchScore = matches.reduce((sum, m) => sum + m.overallScore, 0) / matches.length;
    const topPredictionConfidence = predictions[0]?.confidence || 0;
    const patternConsistency = this.calculatePatternConsistency(matches);
    
    return (avgMatchScore * 0.4 + (topPredictionConfidence / 100) * 0.4 + patternConsistency * 0.2) * 100;
  }

  private calculatePatternConsistency(matches: MatchedPattern[]): number {
    if (matches.length < 2) return 1;
    
    // Calculate how consistent the top matches are
    const scores = matches.slice(0, 5).map(m => m.overallScore);
    const avgScore = scores.reduce((sum, s) => sum + s, 0) / scores.length;
    const variance = scores.reduce((sum, s) => sum + Math.pow(s - avgScore, 2), 0) / scores.length;
    const stdDev = Math.sqrt(variance);
    
    // Lower standard deviation = higher consistency
    return Math.max(0, 1 - stdDev);
  }

  private generatePatternKey(pattern: MarketRegime[], instrument: string, timeframe: TimeFrame): string {
    return `${pattern.join('-')}_${instrument}_${timeframe}`;
  }

  private async updateOrCreatePattern(
    patternKey: string,
    sequence: MarketRegime[],
    instrument: string,
    timeframe: TimeFrame,
    followedByRegime?: MarketRegime,
    context?: {
      timeOfDay: number;
      volatility: number;
      marketConditions: string[];
      confidence: number;
    }
  ): Promise<void> {
    let pattern = this.learnedPatterns.get(patternKey);
    
    const occurrence: PatternOccurrence = {
      timestamp: new Date(),
      instrument,
      timeframe,
      confidence: context?.confidence || 0.5,
      followedByRegime,
      contextScore: context ? this.calculateContextScore(context) : 0.5,
    };
    
    if (pattern) {
      // Update existing pattern
      pattern.occurrences.push(occurrence);
      pattern.lastSeen = new Date();
      
      // Update contextual factors
      if (context) {
        this.updateContextualFactors(pattern, context);
      }
      
      // Recalculate strength and reliability
      pattern.strength = this.calculatePatternStrength(pattern.occurrences);
      pattern.reliability = this.calculatePatternReliability(pattern.occurrences);
      pattern.predictiveValue = this.calculatePredictiveValue(pattern.occurrences);
    } else {
      // Create new pattern
      pattern = {
        id: `pattern_${this.learnedPatterns.size + 1}`,
        sequence,
        occurrences: [occurrence],
        strength: 0.1, // Start with low strength
        reliability: 0.5, // Neutral reliability
        lastSeen: new Date(),
        predictiveValue: 0.5,
        contextualFactors: {
          timeOfDay: context ? [context.timeOfDay] : [],
          marketConditions: context?.marketConditions || [],
          volatilityRange: context ? [context.volatility, context.volatility] : [0, 1],
        },
      };
      
      this.learnedPatterns.set(patternKey, pattern);
    }
  }

  private calculateContextScore(context: {
    timeOfDay: number;
    volatility: number;
    marketConditions: string[];
    confidence: number;
  }): number {
    // Simple context scoring based on available information
    return (context.confidence + (context.volatility < 0.8 ? 0.8 : 0.2)) / 2;
  }

  private updateContextualFactors(
    pattern: RegimePattern,
    context: {
      timeOfDay: number;
      volatility: number;
      marketConditions: string[];
    }
  ): void {
    // Update time of day
    if (!pattern.contextualFactors.timeOfDay.includes(context.timeOfDay)) {
      pattern.contextualFactors.timeOfDay.push(context.timeOfDay);
    }
    
    // Update market conditions
    context.marketConditions.forEach(condition => {
      if (!pattern.contextualFactors.marketConditions.includes(condition)) {
        pattern.contextualFactors.marketConditions.push(condition);
      }
    });
    
    // Update volatility range
    const [minVol, maxVol] = pattern.contextualFactors.volatilityRange;
    pattern.contextualFactors.volatilityRange = [
      Math.min(minVol, context.volatility),
      Math.max(maxVol, context.volatility),
    ];
  }

  private calculatePatternStrength(occurrences: PatternOccurrence[]): number {
    if (occurrences.length === 0) return 0;
    
    // Strength based on frequency and recency
    const recentOccurrences = occurrences.filter(occ => 
      (Date.now() - occ.timestamp.getTime()) < (30 * 24 * 60 * 60 * 1000) // 30 days
    ).length;
    
    const frequencyScore = Math.min(occurrences.length / 10, 1); // Max at 10 occurrences
    const recencyScore = Math.min(recentOccurrences / 5, 1); // Max at 5 recent occurrences
    
    return (frequencyScore + recencyScore) / 2;
  }

  private calculatePatternReliability(occurrences: PatternOccurrence[]): number {
    if (occurrences.length === 0) return 0.5;
    
    // Reliability based on average confidence and consistency
    const avgConfidence = occurrences.reduce((sum, occ) => sum + occ.confidence, 0) / occurrences.length;
    const avgContextScore = occurrences.reduce((sum, occ) => sum + occ.contextScore, 0) / occurrences.length;
    
    return (avgConfidence + avgContextScore) / 2;
  }

  private calculatePredictiveValue(occurrences: PatternOccurrence[]): number {
    const withFollowUp = occurrences.filter(occ => occ.followedByRegime && occ.predictionAccuracy).length;
    
    if (withFollowUp === 0) return 0.5;
    
    const avgAccuracy = occurrences
      .filter(occ => occ.predictionAccuracy !== undefined)
      .reduce((sum, occ) => sum + occ.predictionAccuracy!, 0) / withFollowUp;
    
    return avgAccuracy;
  }

  private calculateTimeProximity(targetHour: number, patternHours: number[]): number {
    const distances = patternHours.map(hour => Math.min(Math.abs(hour - targetHour), 24 - Math.abs(hour - targetHour)));
    const minDistance = Math.min(...distances);
    return Math.max(0, 1 - (minDistance / 12)); // 12 hours = 50% similarity
  }

  private calculateVolatilityProximity(targetVol: number, minVol: number, maxVol: number): number {
    if (targetVol >= minVol && targetVol <= maxVol) return 1;
    
    const distanceToRange = targetVol < minVol ? minVol - targetVol : targetVol - maxVol;
    return Math.max(0, 1 - distanceToRange); // Linear decay
  }

  private updateAccuracyHistory(key: string, accuracy: number): void {
    const history = this.accuracyHistory.get(key) || [];
    history.push(accuracy);
    
    // Keep only recent history (last 100 predictions)
    if (history.length > 100) {
      history.splice(0, history.length - 100);
    }
    
    this.accuracyHistory.set(key, history);
  }

  private async adaptivelyUpdatePatterns(prediction: RegimePrediction, accuracy: number): Promise<void> {
    // Update reliability of supporting patterns based on prediction accuracy
    const learningRate = this.config.adaptiveLearningRate;
    
    for (const patternId of prediction.supportingPatterns) {
      const pattern = Array.from(this.learnedPatterns.values()).find(p => p.id === patternId);
      if (pattern) {
        // Adaptive update: increase reliability for accurate predictions, decrease for poor ones
        const reliabilityChange = (accuracy - 0.5) * learningRate;
        pattern.reliability = Math.max(0.1, Math.min(0.95, pattern.reliability + reliabilityChange));
        
        // Update predictive value
        const predictiveChange = (accuracy - pattern.predictiveValue) * learningRate;
        pattern.predictiveValue = Math.max(0.1, Math.min(0.95, pattern.predictiveValue + predictiveChange));
      }
    }
  }

  private updateMetrics(result: PatternMatchResult): void {
    this.metrics.totalMatches++;
    this.metrics.averageMatchTime = 
      ((this.metrics.averageMatchTime * (this.metrics.totalMatches - 1)) + result.matchingTimeMs) / 
      this.metrics.totalMatches;
    
    // Update other metrics based on result quality
    if (result.confidence > this.config.confidenceThreshold) {
      this.metrics.successfulPredictions++;
    }
    
    if (this.learnedPatterns.size > 0) {
      const avgReliability = Array.from(this.learnedPatterns.values())
        .reduce((sum, p) => sum + p.reliability, 0) / this.learnedPatterns.size;
      this.metrics.patternReliabilityScore = avgReliability;
    }
  }

  /**
   * Clear all learned patterns and reset
   */
  public clearPatterns(): void {
    this.learnedPatterns.clear();
    this.recentSequences.clear();
    this.accuracyHistory.clear();
    this.predictionHistory.clear();
    
    this.emit('patterns_cleared');
  }

  /**
   * Export learned patterns for backup/analysis
   */
  public exportPatterns(): RegimePattern[] {
    return Array.from(this.learnedPatterns.values());
  }

  /**
   * Import patterns from external source
   */
  public importPatterns(patterns: RegimePattern[]): void {
    patterns.forEach(pattern => {
      const key = this.generatePatternKey(pattern.sequence, 'imported', '1H');
      this.learnedPatterns.set(key, pattern);
    });
    
    this.emit('patterns_imported', { count: patterns.length });
  }

  /**
   * Get current configuration
   */
  public getConfig(): PatternMatchingConfig {
    return { ...this.config };
  }

  /**
   * Update configuration
   */
  public updateConfig(newConfig: Partial<PatternMatchingConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config_updated', this.config);
  }

  /**
   * Shutdown and cleanup
   */
  public shutdown(): void {
    this.clearPatterns();
    this.removeAllListeners();
  }
}