"""
Comprehensive Performance Test Suite
Performance testing, benchmarking, and optimization validation for MT5 Bridge Service
"""

import pytest
import asyncio
import time
import statistics
import psutil
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from concurrent.futures import <PERSON>hreadPoolExecutor, ProcessPoolExecutor
import json
import gc
from unittest.mock import Mock, patch

from performance_monitor import (
    PerformanceProfiler, 
    PerformanceTestSuite, 
    get_performance_profiler,
    PerformanceMetric,
    LatencyMeasurement,
    ThroughputMeasurement,
    ResourceUsage
)
from latency_optimizer import LatencyAnalyzer, get_latency_analyzer
from config import get_config

class TestPerformanceProfiler:
    """Test performance profiler functionality"""
    
    @pytest.fixture
    def profiler(self):
        """Create a performance profiler for testing"""
        return PerformanceProfiler()
    
    def test_profiler_initialization(self, profiler):
        """Test profiler initialization"""
        
        assert profiler is not None
        assert hasattr(profiler, 'measurements')
        assert hasattr(profiler, 'thresholds')
        assert not profiler.monitoring_active
    
    def test_latency_measurement_context(self, profiler):
        """Test latency measurement context manager"""
        
        operation = 'test_operation'
        
        with profiler.measure_latency(operation):
            time.sleep(0.01)  # 10ms
        
        # Check that measurement was recorded
        measurements = profiler.measurements[f'latency_{operation}']
        assert len(measurements) == 1
        
        measurement = measurements[0]
        assert isinstance(measurement, LatencyMeasurement)
        assert measurement.operation == operation
        assert measurement.latency_ms >= 10  # Should be at least 10ms
        assert measurement.success is True
    
    def test_latency_measurement_with_error(self, profiler):
        """Test latency measurement with exception"""
        
        operation = 'test_error_operation'
        
        with pytest.raises(ValueError):
            with profiler.measure_latency(operation):
                raise ValueError("Test error")
        
        # Check that measurement was recorded with error
        measurements = profiler.measurements[f'latency_{operation}']
        assert len(measurements) == 1
        
        measurement = measurements[0]
        assert measurement.success is False
        assert measurement.error_message == "Test error"
    
    @pytest.mark.asyncio
    async def test_async_latency_measurement(self, profiler):
        """Test async latency measurement"""
        
        async def test_async_function(duration):
            await asyncio.sleep(duration)
            return "success"
        
        result = await profiler.measure_async_latency(
            'async_test', test_async_function, 0.01
        )
        
        assert result == "success"
        
        measurements = profiler.measurements['latency_async_test']
        assert len(measurements) == 1
        assert measurements[0].latency_ms >= 10
    
    def test_throughput_measurement(self, profiler):
        """Test throughput measurement"""
        
        measurement = profiler.measure_throughput(
            'test_throughput', 
            operation_count=1000, 
            duration_seconds=1.0,
            context={'batch_size': 100}
        )
        
        assert isinstance(measurement, ThroughputMeasurement)
        assert measurement.operations_per_second == 1000.0
        assert measurement.total_operations == 1000
        assert measurement.context['batch_size'] == 100
        
        # Check that measurement was stored
        measurements = profiler.measurements['throughput_test_throughput']
        assert len(measurements) == 1
    
    def test_resource_monitoring(self, profiler):
        """Test resource monitoring functionality"""
        
        # Start monitoring
        profiler.start_monitoring(interval_seconds=0.1)
        
        # Wait a bit for measurements
        time.sleep(0.3)
        
        # Stop monitoring
        profiler.stop_monitoring()
        
        # Check that resource measurements were collected
        resource_measurements = profiler.measurements['resource_usage']
        assert len(resource_measurements) >= 2  # Should have multiple measurements
        
        for measurement in resource_measurements:
            assert isinstance(measurement, ResourceUsage)
            assert measurement.cpu_percent >= 0
            assert measurement.memory_mb > 0
    
    def test_performance_summary(self, profiler):
        """Test performance summary generation"""
        
        # Add some test measurements
        profiler.record_latency(LatencyMeasurement(
            operation='test_op',
            latency_ms=50.0,
            timestamp=datetime.now(),
            success=True
        ))
        
        profiler.record_throughput(ThroughputMeasurement(
            operation='test_throughput',
            operations_per_second=500.0,
            total_operations=1000,
            duration_seconds=2.0,
            timestamp=datetime.now()
        ))
        
        summary = profiler.get_performance_summary(hours_back=1)
        
        assert 'latency_stats' in summary
        assert 'throughput_stats' in summary
        assert 'test_op' in summary['latency_stats']
        assert 'test_throughput' in summary['throughput_stats']
        
        test_op_stats = summary['latency_stats']['test_op']
        assert test_op_stats['mean_ms'] == 50.0
        assert test_op_stats['success_rate'] == 1.0
    
    def test_real_time_metrics(self, profiler):
        """Test real-time metrics retrieval"""
        
        # Add resource usage
        profiler.record_resource_usage(ResourceUsage(
            cpu_percent=25.0,
            memory_percent=60.0,
            memory_mb=512.0,
            disk_io_read_mb=10.0,
            disk_io_write_mb=5.0,
            network_io_sent_mb=2.0,
            network_io_recv_mb=3.0,
            timestamp=datetime.now()
        ))
        
        metrics = profiler.get_real_time_metrics()
        
        assert 'current_resource_usage' in metrics
        assert 'recent_latencies' in metrics
        assert 'recent_throughput' in metrics
        
        assert metrics['current_resource_usage']['cpu_percent'] == 25.0
        assert metrics['current_resource_usage']['memory_mb'] == 512.0

class TestLatencyAnalyzer:
    """Test latency analyzer functionality"""
    
    @pytest.fixture
    def analyzer(self):
        """Create a latency analyzer for testing"""
        return LatencyAnalyzer()
    
    def test_analyzer_initialization(self, analyzer):
        """Test analyzer initialization"""
        
        assert analyzer is not None
        assert hasattr(analyzer, 'benchmarks')
        assert 'api_response' in analyzer.benchmarks
        assert 'database_query' in analyzer.benchmarks
    
    def test_latency_benchmark_checking(self, analyzer):
        """Test latency benchmark checking"""
        
        # Test normal latency
        normal_measurement = LatencyMeasurement(
            operation='api_response',
            latency_ms=30.0,  # Within target
            timestamp=datetime.now(),
            success=True
        )
        
        # Should not log warnings for normal latency
        with patch('latency_optimizer.logger') as mock_logger:
            analyzer._check_latency_benchmark(normal_measurement)
            mock_logger.warning.assert_not_called()
            mock_logger.critical.assert_not_called()
        
        # Test high latency (warning level)
        warning_measurement = LatencyMeasurement(
            operation='api_response',
            latency_ms=150.0,  # Above warning threshold
            timestamp=datetime.now(),
            success=True
        )
        
        with patch('latency_optimizer.logger') as mock_logger:
            analyzer._check_latency_benchmark(warning_measurement)
            mock_logger.warning.assert_called_once()
        
        # Test critical latency
        critical_measurement = LatencyMeasurement(
            operation='api_response',
            latency_ms=250.0,  # Above critical threshold
            timestamp=datetime.now(),
            success=True
        )
        
        with patch('latency_optimizer.logger') as mock_logger:
            analyzer._check_latency_benchmark(critical_measurement)
            mock_logger.critical.assert_called_once()
    
    def test_enhanced_latency_context(self, analyzer):
        """Test enhanced latency measurement context"""
        
        with analyzer.measure_operation_latency('test_operation'):
            time.sleep(0.005)  # 5ms
        
        # Check that measurement was recorded
        measurements = analyzer.profiler.measurements['latency_test_operation']
        assert len(measurements) == 1
        assert measurements[0].latency_ms >= 5
    
    def test_latency_pattern_analysis(self, analyzer):
        """Test latency pattern analysis"""
        
        # Add test measurements with varying latencies
        operation = 'test_pattern_analysis'
        base_time = datetime.now()
        
        for i in range(20):
            latency = 50 + (i * 2)  # Increasing latency pattern
            measurement = LatencyMeasurement(
                operation=operation,
                latency_ms=latency,
                timestamp=base_time + timedelta(seconds=i),
                success=True
            )
            analyzer.profiler.record_latency(measurement)
        
        analysis = analyzer.analyze_latency_patterns(window_minutes=5)
        
        assert 'operations' in analysis
        assert operation in analysis['operations']
        
        op_stats = analysis['operations'][operation]
        assert op_stats['count'] == 20
        assert op_stats['mean_ms'] > 50
        assert op_stats['std_ms'] > 0
        
        # Check trends
        assert 'trends' in analysis
        if operation in analysis['trends']:
            trend = analysis['trends'][operation]
            assert trend['direction'] in ['increasing', 'decreasing', 'stable']
    
    def test_optimization_recommendations(self, analyzer):
        """Test optimization recommendation generation"""
        
        # Create analysis with high latency operation
        analysis = {
            'operations': {
                'api_response': {
                    'count': 100,
                    'mean_ms': 150.0,  # Above warning threshold
                    'std_ms': 20.0,
                    'p95_ms': 200.0,
                    'coefficient_of_variation': 0.13
                }
            }
        }
        
        recommendations = analyzer.generate_optimization_recommendations(analysis)
        
        assert len(recommendations) > 0
        
        api_rec = recommendations[0]
        assert api_rec.operation == 'api_response'
        assert api_rec.current_latency_ms == 150.0
        assert api_rec.target_latency_ms == 50.0  # From benchmark
        assert api_rec.implementation_priority <= 3  # Should be high priority
        assert api_rec.estimated_improvement_ms > 0
    
    @pytest.mark.asyncio
    async def test_operation_benchmarking(self, analyzer):
        """Test operation benchmarking"""
        
        def test_function():
            time.sleep(0.001)  # 1ms
            return "test_result"
        
        results = await analyzer.benchmark_operation(
            'test_benchmark', test_function, iterations=10, warmup_iterations=2
        )
        
        assert results['operation'] == 'test_benchmark'
        assert results['iterations'] == 10
        assert results['success_rate'] == 1.0
        assert results['statistics']['mean_ms'] >= 1.0
        assert 'benchmark_time' in results
    
    @pytest.mark.asyncio
    async def test_async_operation_benchmarking(self, analyzer):
        """Test async operation benchmarking"""
        
        async def async_test_function():
            await asyncio.sleep(0.001)  # 1ms
            return "async_result"
        
        results = await analyzer.benchmark_operation(
            'async_test_benchmark', async_test_function, iterations=5
        )
        
        assert results['operation'] == 'async_test_benchmark'
        assert results['success_rate'] == 1.0
        assert results['statistics']['mean_ms'] >= 1.0

class TestPerformanceTestSuite:
    """Test comprehensive performance test suite"""
    
    @pytest.fixture
    def test_suite(self):
        """Create a performance test suite"""
        return PerformanceTestSuite()
    
    @pytest.mark.asyncio
    async def test_latency_performance_tests(self, test_suite):
        """Test latency performance testing"""
        
        results = await test_suite._test_latency_performance()
        
        assert results['test_name'] == 'Latency Performance Test'
        assert 'operations' in results
        
        # Should have tested data transformation at minimum
        assert 'data_transformation' in results['operations']
        
        transform_stats = results['operations']['data_transformation']
        assert 'mean_ms' in transform_stats
        assert 'success_rate' in transform_stats
        assert transform_stats['success_rate'] == 1.0
    
    @pytest.mark.asyncio
    async def test_throughput_performance_tests(self, test_suite):
        """Test throughput performance testing"""
        
        results = await test_suite._test_throughput_performance()
        
        assert results['test_name'] == 'Throughput Performance Test'
        assert 'operations' in results
        
        # Should have tested batch processing
        assert 'data_transformation_batch' in results['operations']
        
        batch_stats = results['operations']['data_transformation_batch']
        assert 'operations_per_second' in batch_stats
        assert 'total_operations' in batch_stats
        assert batch_stats['operations_per_second'] > 0
    
    @pytest.mark.asyncio
    async def test_stress_performance_tests(self, test_suite):
        """Test stress performance testing"""
        
        results = await test_suite._test_stress_performance()
        
        assert results['test_name'] == 'Stress Performance Test'
        assert 'scenarios' in results
        
        # Should have tested high-frequency processing
        assert 'high_frequency_processing' in results['scenarios']
        
        stress_stats = results['scenarios']['high_frequency_processing']
        assert 'total_records' in stress_stats
        assert 'operations_per_second' in stress_stats
        assert stress_stats['total_records'] == 10000
    
    @pytest.mark.asyncio
    async def test_memory_performance_tests(self, test_suite):
        """Test memory performance testing"""
        
        results = await test_suite._test_memory_performance()
        
        assert results['test_name'] == 'Memory Performance Test'
        assert 'scenarios' in results
        
        # Should have tested large dataset processing
        assert 'large_dataset_processing' in results['scenarios']
        
        memory_stats = results['scenarios']['large_dataset_processing']
        assert 'baseline_memory_mb' in memory_stats
        assert 'peak_memory_mb' in memory_stats
        assert 'memory_efficiency' in memory_stats
        assert memory_stats['peak_memory_mb'] >= memory_stats['baseline_memory_mb']
    
    @pytest.mark.asyncio
    async def test_concurrent_performance_tests(self, test_suite):
        """Test concurrent performance testing"""
        
        results = await test_suite._test_concurrent_performance()
        
        assert results['test_name'] == 'Concurrent Performance Test'
        assert 'scenarios' in results
        
        # Should have tested different concurrency levels
        concurrency_levels = [1, 2, 4, 8, 16]
        for level in concurrency_levels:
            scenario_key = f'concurrency_{level}'
            if scenario_key in results['scenarios']:
                scenario = results['scenarios'][scenario_key]
                assert scenario['concurrency_level'] == level
                assert 'throughput_ops_per_sec' in scenario
    
    @pytest.mark.asyncio
    async def test_comprehensive_test_suite(self, test_suite):
        """Test running the complete test suite"""
        
        # Run with limited scope for testing
        test_suite.profiler.start_monitoring()
        
        try:
            # Run a subset of tests
            results = {
                'test_suite': 'MT5 Bridge Performance Tests',
                'started_at': datetime.now().isoformat(),
                'tests': {}
            }
            
            # Run latency tests only for speed
            results['tests']['latency'] = await test_suite._test_latency_performance()
            
            results['completed_at'] = datetime.now().isoformat()
            results['overall_summary'] = test_suite._generate_overall_summary(results['tests'])
            
            # Validate results structure
            assert 'test_suite' in results
            assert 'started_at' in results
            assert 'completed_at' in results
            assert 'tests' in results
            assert 'overall_summary' in results
            
            summary = results['overall_summary']
            assert 'total_tests_run' in summary
            assert 'performance_score' in summary
            assert 'recommendations' in summary
            assert isinstance(summary['recommendations'], list)
            
        finally:
            test_suite.profiler.stop_monitoring()

class TestPerformanceIntegration:
    """Integration tests for performance monitoring"""
    
    @pytest.mark.asyncio
    async def test_performance_monitoring_integration(self):
        """Test integration of performance monitoring components"""
        
        profiler = get_performance_profiler()
        analyzer = get_latency_analyzer()
        
        # Start monitoring
        profiler.start_monitoring(interval_seconds=0.1)
        
        try:
            # Simulate some operations
            with profiler.measure_latency('integration_test'):
                time.sleep(0.01)
            
            # Wait for monitoring to collect data
            await asyncio.sleep(0.2)
            
            # Test analysis
            analysis = analyzer.analyze_latency_patterns(window_minutes=1)
            
            assert 'operations' in analysis
            
            # Generate recommendations
            recommendations = analyzer.generate_optimization_recommendations(analysis)
            
            # Should work without errors
            assert isinstance(recommendations, list)
            
        finally:
            profiler.stop_monitoring()
    
    def test_performance_metrics_export(self):
        """Test performance metrics export functionality"""
        
        profiler = get_performance_profiler()
        
        # Add some test data
        profiler.record_latency(LatencyMeasurement(
            operation='export_test',
            latency_ms=25.0,
            timestamp=datetime.now(),
            success=True
        ))
        
        # Export metrics
        exported_json = profiler.export_metrics('json')
        
        # Should be valid JSON
        exported_data = json.loads(exported_json)
        assert isinstance(exported_data, dict)
        assert 'latency_stats' in exported_data
    
    def test_latency_report_export(self):
        """Test latency report export functionality"""
        
        analyzer = get_latency_analyzer()
        
        # Add some test measurements
        analyzer.profiler.record_latency(LatencyMeasurement(
            operation='api_response',
            latency_ms=75.0,
            timestamp=datetime.now(),
            success=True
        ))
        
        # Export report
        report_json = analyzer.export_latency_report('json')
        
        # Should be valid JSON
        report_data = json.loads(report_json)
        assert isinstance(report_data, dict)
        assert 'report_type' in report_data
        assert 'operation_analysis' in report_data
        assert 'optimization_recommendations' in report_data

class TestPerformanceOptimization:
    """Test performance optimization scenarios"""
    
    @pytest.mark.asyncio
    async def test_data_processing_optimization(self):
        """Test data processing performance optimization"""
        
        profiler = get_performance_profiler()
        
        # Test unoptimized approach
        def process_items_sequential(items):
            results = []
            for item in items:
                # Simulate processing
                processed = {
                    'id': item['id'],
                    'value': item['value'] * 2,
                    'processed_at': time.time()
                }
                results.append(processed)
            return results
        
        # Test optimized approach (batch processing)
        def process_items_batch(items):
            # Simulate batch processing
            return [
                {
                    'id': item['id'],
                    'value': item['value'] * 2,
                    'processed_at': time.time()
                }
                for item in items
            ]
        
        # Generate test data
        test_data = [{'id': i, 'value': i * 10} for i in range(1000)]
        
        # Benchmark sequential processing
        with profiler.measure_latency('sequential_processing'):
            sequential_results = process_items_sequential(test_data)
        
        # Benchmark batch processing
        with profiler.measure_latency('batch_processing'):
            batch_results = process_items_batch(test_data)
        
        # Verify results are equivalent
        assert len(sequential_results) == len(batch_results) == len(test_data)
        
        # Compare performance
        sequential_measurements = profiler.measurements['latency_sequential_processing']
        batch_measurements = profiler.measurements['latency_batch_processing']
        
        assert len(sequential_measurements) == 1
        assert len(batch_measurements) == 1
        
        sequential_latency = sequential_measurements[0].latency_ms
        batch_latency = batch_measurements[0].latency_ms
        
        # Batch processing should be faster
        assert batch_latency < sequential_latency
        
        optimization_ratio = sequential_latency / batch_latency
        assert optimization_ratio > 1.0  # Should show improvement
    
    @pytest.mark.asyncio
    async def test_async_optimization(self):
        """Test async/await optimization benefits"""
        
        profiler = get_performance_profiler()
        
        # Simulate I/O operations
        def blocking_io_operation():
            time.sleep(0.01)  # 10ms
            return "result"
        
        async def async_io_operation():
            await asyncio.sleep(0.01)  # 10ms
            return "result"
        
        # Test sequential blocking calls
        start_time = time.perf_counter()
        results = []
        for _ in range(5):
            with profiler.measure_latency('blocking_io'):
                result = blocking_io_operation()
                results.append(result)
        blocking_duration = time.perf_counter() - start_time
        
        # Test concurrent async calls
        start_time = time.perf_counter()
        tasks = []
        for _ in range(5):
            task = profiler.measure_async_latency('async_io', async_io_operation)
            tasks.append(task)
        
        async_results = await asyncio.gather(*tasks)
        async_duration = time.perf_counter() - start_time
        
        # Verify results
        assert len(results) == len(async_results) == 5
        
        # Async should be much faster for concurrent operations
        speedup = blocking_duration / async_duration
        assert speedup > 2.0  # Should be significantly faster

class TestGlobalInstances:
    """Test global instance management"""
    
    def test_get_performance_profiler(self):
        """Test global performance profiler instance"""
        
        profiler1 = get_performance_profiler()
        profiler2 = get_performance_profiler()
        
        assert profiler1 is profiler2
        assert isinstance(profiler1, PerformanceProfiler)
    
    def test_get_latency_analyzer(self):
        """Test global latency analyzer instance"""
        
        analyzer1 = get_latency_analyzer()
        analyzer2 = get_latency_analyzer()
        
        assert analyzer1 is analyzer2
        assert isinstance(analyzer1, LatencyAnalyzer)

if __name__ == "__main__":
    # Run performance tests
    pytest.main([__file__, "-v", "--tb=short", "-x"])