"""
Automatic Performance Optimization Script
Applies common performance optimizations and validates improvements
"""

import asyncio
import gc
import os
import psutil
import time
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import json
from loguru import logger

from performance_monitor import get_performance_profiler, PerformanceTestSuite
from latency_optimizer import get_latency_analyzer
from config import get_config

class PerformanceOptimizer:
    """Automatic performance optimization system"""
    
    def __init__(self):
        self.profiler = get_performance_profiler()
        self.analyzer = get_latency_analyzer()
        self.config = get_config()
        self.optimization_history: List[Dict[str, Any]] = []
        
        # Optimization settings
        self.optimizations = {
            'garbage_collection': {
                'enabled': True,
                'description': 'Optimize garbage collection settings',
                'implementation_effort': 'low'
            },
            'connection_pooling': {
                'enabled': True,
                'description': 'Optimize database connection pooling',
                'implementation_effort': 'medium'
            },
            'memory_allocation': {
                'enabled': True,
                'description': 'Optimize memory allocation patterns',
                'implementation_effort': 'low'
            },
            'async_optimizations': {
                'enabled': True,
                'description': 'Apply async/await optimizations',
                'implementation_effort': 'medium'
            },
            'data_structures': {
                'enabled': True,
                'description': 'Optimize data structure usage',
                'implementation_effort': 'high'
            }
        }
    
    async def run_optimization_suite(self) -> Dict[str, Any]:
        """Run comprehensive performance optimization suite"""
        
        logger.info("🚀 Starting automatic performance optimization...")
        
        results = {
            'optimization_run': {
                'started_at': datetime.now().isoformat(),
                'baseline_metrics': {},
                'applied_optimizations': [],
                'final_metrics': {},
                'improvements': {},
                'recommendations': []
            }
        }
        
        try:
            # 1. Establish baseline performance
            logger.info("📊 Establishing baseline performance...")
            baseline_metrics = await self._measure_baseline_performance()
            results['optimization_run']['baseline_metrics'] = baseline_metrics
            
            # 2. Apply optimizations
            logger.info("🔧 Applying performance optimizations...")
            applied_optimizations = await self._apply_optimizations()
            results['optimization_run']['applied_optimizations'] = applied_optimizations
            
            # 3. Measure post-optimization performance
            logger.info("📈 Measuring post-optimization performance...")
            final_metrics = await self._measure_baseline_performance()
            results['optimization_run']['final_metrics'] = final_metrics
            
            # 4. Calculate improvements
            improvements = self._calculate_improvements(baseline_metrics, final_metrics)
            results['optimization_run']['improvements'] = improvements
            
            # 5. Generate recommendations for further optimization
            recommendations = await self._generate_recommendations()
            results['optimization_run']['recommendations'] = recommendations
            
            results['optimization_run']['completed_at'] = datetime.now().isoformat()
            results['optimization_run']['success'] = True
            
            # Log summary
            self._log_optimization_summary(improvements)
            
        except Exception as e:
            logger.error(f"❌ Optimization suite failed: {e}")
            results['optimization_run']['error'] = str(e)
            results['optimization_run']['success'] = False
        
        return results
    
    async def _measure_baseline_performance(self) -> Dict[str, Any]:
        """Measure baseline performance metrics"""
        
        # Run performance tests
        test_suite = PerformanceTestSuite()
        
        # Quick performance benchmark
        baseline = {
            'timestamp': datetime.now().isoformat(),
            'system_resources': self._get_system_resources(),
            'operation_benchmarks': {}
        }
        
        # Benchmark key operations
        operations_to_test = [
            ('data_transformation', self._benchmark_data_transformation),
            ('json_serialization', self._benchmark_json_serialization),
            ('memory_allocation', self._benchmark_memory_allocation),
            ('async_operations', self._benchmark_async_operations)
        ]
        
        for operation_name, benchmark_func in operations_to_test:
            try:
                result = await benchmark_func()
                baseline['operation_benchmarks'][operation_name] = result
            except Exception as e:
                logger.warning(f"Failed to benchmark {operation_name}: {e}")
                baseline['operation_benchmarks'][operation_name] = {'error': str(e)}
        
        return baseline
    
    async def _apply_optimizations(self) -> List[Dict[str, Any]]:
        """Apply available performance optimizations"""
        
        applied = []
        
        for opt_name, opt_config in self.optimizations.items():
            if not opt_config['enabled']:
                continue
            
            try:
                logger.info(f"🔧 Applying optimization: {opt_name}")
                
                optimization_result = {
                    'name': opt_name,
                    'description': opt_config['description'],
                    'effort': opt_config['implementation_effort'],
                    'applied_at': datetime.now().isoformat(),
                    'success': True,
                    'details': {}
                }
                
                # Apply specific optimization
                if opt_name == 'garbage_collection':
                    optimization_result['details'] = await self._optimize_garbage_collection()
                elif opt_name == 'connection_pooling':
                    optimization_result['details'] = await self._optimize_connection_pooling()
                elif opt_name == 'memory_allocation':
                    optimization_result['details'] = await self._optimize_memory_allocation()
                elif opt_name == 'async_optimizations':
                    optimization_result['details'] = await self._optimize_async_operations()
                elif opt_name == 'data_structures':
                    optimization_result['details'] = await self._optimize_data_structures()
                
                applied.append(optimization_result)
                logger.info(f"✅ Applied optimization: {opt_name}")
                
            except Exception as e:
                logger.error(f"❌ Failed to apply optimization {opt_name}: {e}")
                applied.append({
                    'name': opt_name,
                    'description': opt_config['description'],
                    'applied_at': datetime.now().isoformat(),
                    'success': False,
                    'error': str(e)
                })
        
        return applied
    
    async def _optimize_garbage_collection(self) -> Dict[str, Any]:
        """Optimize garbage collection settings"""
        
        # Force garbage collection
        collected_before = gc.collect()
        
        # Get GC stats before
        gc_stats_before = gc.get_stats()
        
        # Configure GC thresholds for better performance
        # Default thresholds are usually (700, 10, 10)
        # Optimize for fewer collections in generation 1 and 2
        old_thresholds = gc.get_threshold()
        gc.set_threshold(1000, 15, 15)  # More lenient thresholds
        
        # Enable GC debugging (optional)
        gc.set_debug(0)  # Disable debug output for performance
        
        return {
            'collected_objects_before': collected_before,
            'gc_stats_before': gc_stats_before,
            'old_thresholds': old_thresholds,
            'new_thresholds': gc.get_threshold(),
            'gc_enabled': gc.isenabled()
        }
    
    async def _optimize_connection_pooling(self) -> Dict[str, Any]:
        """Optimize database connection pooling"""
        
        # This would typically involve adjusting connection pool settings
        # For now, we'll return configuration recommendations
        
        current_config = {
            'max_connections': getattr(self.config.database, 'max_connections', 10),
            'timeout': getattr(self.config.database, 'timeout', 30)
        }
        
        # Calculate optimal pool size based on CPU cores
        cpu_cores = psutil.cpu_count()
        optimal_connections = min(cpu_cores * 2 + 1, 20)
        
        recommendations = {
            'current_max_connections': current_config['max_connections'],
            'recommended_max_connections': optimal_connections,
            'current_timeout': current_config['timeout'],
            'recommended_timeout': 60,  # Longer timeout for better reliability
            'reasoning': f'Based on {cpu_cores} CPU cores, optimal pool size is {optimal_connections}'
        }
        
        return recommendations
    
    async def _optimize_memory_allocation(self) -> Dict[str, Any]:
        """Optimize memory allocation patterns"""
        
        # Get memory info before optimization
        process = psutil.Process()
        memory_before = process.memory_info()
        
        # Pre-allocate commonly used objects to reduce allocation overhead
        # This is a demonstration - in practice, this would be more sophisticated
        
        # Pre-allocate some common data structures
        pre_allocated = {
            'price_buffer': [None] * 1000,  # Pre-allocate price buffer
            'calculation_arrays': [[0.0] * 100 for _ in range(10)],  # Pre-allocate calculation arrays
            'string_cache': {f'symbol_{i}': f'PAIR{i}' for i in range(100)}  # Pre-allocate symbol strings
        }
        
        # Get memory info after optimization
        memory_after = process.memory_info()
        
        return {
            'memory_before_mb': memory_before.rss / 1024 / 1024,
            'memory_after_mb': memory_after.rss / 1024 / 1024,
            'memory_increase_mb': (memory_after.rss - memory_before.rss) / 1024 / 1024,
            'pre_allocated_objects': len(pre_allocated),
            'optimization_type': 'pre_allocation'
        }
    
    async def _optimize_async_operations(self) -> Dict[str, Any]:
        """Optimize async operation patterns"""
        
        # Demonstrate async optimization techniques
        optimizations = []
        
        # 1. Event loop optimization
        loop = asyncio.get_event_loop()
        
        # Configure event loop for better performance
        if hasattr(loop, 'set_debug'):
            loop.set_debug(False)  # Disable debug mode for performance
        
        optimizations.append({
            'type': 'event_loop_debug',
            'description': 'Disabled event loop debug mode for performance'
        })
        
        # 2. Task scheduling optimization
        # Demonstrate efficient task creation patterns
        start_time = time.perf_counter()
        
        # Inefficient: Creating many individual tasks
        tasks = [asyncio.create_task(asyncio.sleep(0.001)) for _ in range(10)]
        await asyncio.gather(*tasks)
        
        inefficient_time = time.perf_counter() - start_time
        
        # Efficient: Batch processing
        start_time = time.perf_counter()
        
        async def batch_sleep():
            await asyncio.sleep(0.001)
        
        # Process in batches
        await asyncio.gather(*[batch_sleep() for _ in range(10)])
        
        efficient_time = time.perf_counter() - start_time
        
        optimizations.append({
            'type': 'task_batching',
            'description': 'Optimized task creation and scheduling',
            'inefficient_time_ms': inefficient_time * 1000,
            'efficient_time_ms': efficient_time * 1000,
            'improvement_factor': inefficient_time / efficient_time if efficient_time > 0 else 0
        })
        
        return {
            'optimizations_applied': optimizations,
            'total_optimizations': len(optimizations)
        }
    
    async def _optimize_data_structures(self) -> Dict[str, Any]:
        """Optimize data structure usage"""
        
        optimizations = []
        
        # 1. Demonstrate efficient dictionary usage
        from collections import defaultdict, deque
        
        # Show difference between dict and defaultdict
        start_time = time.perf_counter()
        
        # Less efficient: regular dict with key checking
        regular_dict = {}
        for i in range(1000):
            key = f'key_{i % 100}'
            if key not in regular_dict:
                regular_dict[key] = []
            regular_dict[key].append(i)
        
        regular_dict_time = time.perf_counter() - start_time
        
        # More efficient: defaultdict
        start_time = time.perf_counter()
        
        default_dict = defaultdict(list)
        for i in range(1000):
            key = f'key_{i % 100}'
            default_dict[key].append(i)
        
        default_dict_time = time.perf_counter() - start_time
        
        optimizations.append({
            'type': 'dictionary_optimization',
            'description': 'Used defaultdict instead of regular dict with key checking',
            'regular_dict_time_ms': regular_dict_time * 1000,
            'default_dict_time_ms': default_dict_time * 1000,
            'improvement_factor': regular_dict_time / default_dict_time if default_dict_time > 0 else 0
        })
        
        # 2. Demonstrate efficient queue usage
        start_time = time.perf_counter()
        
        # Less efficient: list as queue
        list_queue = []
        for i in range(1000):
            list_queue.append(i)
            if len(list_queue) > 100:
                list_queue.pop(0)  # O(n) operation
        
        list_queue_time = time.perf_counter() - start_time
        
        # More efficient: deque
        start_time = time.perf_counter()
        
        deque_queue = deque(maxlen=100)
        for i in range(1000):
            deque_queue.append(i)  # O(1) operation with automatic size limit
        
        deque_time = time.perf_counter() - start_time
        
        optimizations.append({
            'type': 'queue_optimization',
            'description': 'Used deque instead of list for queue operations',
            'list_queue_time_ms': list_queue_time * 1000,
            'deque_time_ms': deque_time * 1000,
            'improvement_factor': list_queue_time / deque_time if deque_time > 0 else 0
        })
        
        return {
            'optimizations_applied': optimizations,
            'total_optimizations': len(optimizations)
        }
    
    def _calculate_improvements(self, baseline: Dict[str, Any], final: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate performance improvements"""
        
        improvements = {
            'timestamp': datetime.now().isoformat(),
            'operation_improvements': {},
            'resource_improvements': {},
            'overall_score': 0
        }
        
        # Calculate operation improvements
        baseline_ops = baseline.get('operation_benchmarks', {})
        final_ops = final.get('operation_benchmarks', {})
        
        for operation in baseline_ops:
            if operation in final_ops:
                baseline_val = baseline_ops[operation]
                final_val = final_ops[operation]
                
                if isinstance(baseline_val, dict) and isinstance(final_val, dict):
                    if 'mean_latency_ms' in baseline_val and 'mean_latency_ms' in final_val:
                        baseline_latency = baseline_val['mean_latency_ms']
                        final_latency = final_val['mean_latency_ms']
                        
                        if baseline_latency > 0:
                            improvement_percent = ((baseline_latency - final_latency) / baseline_latency) * 100
                            improvements['operation_improvements'][operation] = {
                                'baseline_latency_ms': baseline_latency,
                                'final_latency_ms': final_latency,
                                'improvement_percent': improvement_percent,
                                'improvement_direction': 'better' if improvement_percent > 0 else 'worse'
                            }
        
        # Calculate resource improvements
        baseline_resources = baseline.get('system_resources', {})
        final_resources = final.get('system_resources', {})
        
        for resource in ['cpu_percent', 'memory_percent']:
            if resource in baseline_resources and resource in final_resources:
                baseline_val = baseline_resources[resource]
                final_val = final_resources[resource]
                
                improvement_percent = ((baseline_val - final_val) / baseline_val * 100) if baseline_val > 0 else 0
                improvements['resource_improvements'][resource] = {
                    'baseline': baseline_val,
                    'final': final_val,
                    'improvement_percent': improvement_percent,
                    'improvement_direction': 'better' if improvement_percent > 0 else 'worse'
                }
        
        # Calculate overall score (0-100)
        improvement_scores = []
        
        for imp in improvements['operation_improvements'].values():
            if imp['improvement_direction'] == 'better':
                improvement_scores.append(min(imp['improvement_percent'], 50))  # Cap at 50%
        
        for imp in improvements['resource_improvements'].values():
            if imp['improvement_direction'] == 'better':
                improvement_scores.append(min(imp['improvement_percent'], 30))  # Cap at 30%
        
        if improvement_scores:
            improvements['overall_score'] = min(sum(improvement_scores) / len(improvement_scores), 100)
        
        return improvements
    
    async def _generate_recommendations(self) -> List[Dict[str, Any]]:
        """Generate recommendations for further optimization"""
        
        # Get current performance analysis
        analysis = self.analyzer.analyze_latency_patterns(window_minutes=5)
        recommendations = self.analyzer.generate_optimization_recommendations(analysis)
        
        # Convert to serializable format
        rec_data = []
        for rec in recommendations[:5]:  # Top 5 recommendations
            rec_data.append({
                'operation': rec.operation,
                'current_latency_ms': rec.current_latency_ms,
                'target_latency_ms': rec.target_latency_ms,
                'optimization_type': rec.optimization_type,
                'description': rec.description,
                'priority': rec.implementation_priority,
                'estimated_improvement_ms': rec.estimated_improvement_ms,
                'implementation_effort': rec.implementation_effort
            })
        
        return rec_data
    
    def _get_system_resources(self) -> Dict[str, Any]:
        """Get current system resource usage"""
        
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        return {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_available_mb': memory.available / 1024 / 1024,
            'memory_used_mb': memory.used / 1024 / 1024
        }
    
    async def _benchmark_data_transformation(self) -> Dict[str, Any]:
        """Benchmark data transformation operations"""
        
        from data_transformer import DataTransformer
        transformer = DataTransformer()
        
        sample_data = {
            'symbol': 'EURUSD',
            'timeframe': '1m',
            'timestamp': datetime.now(),
            'open': 1.1000,
            'high': 1.1005,
            'low': 1.0995,
            'close': 1.1002,
            'volume': 1000,
            'source': 'mt5'
        }
        
        # Benchmark transformation
        iterations = 1000
        start_time = time.perf_counter()
        
        for _ in range(iterations):
            transformer.transform_market_data(sample_data)
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        
        return {
            'iterations': iterations,
            'total_time_ms': total_time * 1000,
            'mean_latency_ms': (total_time / iterations) * 1000,
            'throughput_ops_per_sec': iterations / total_time if total_time > 0 else 0
        }
    
    async def _benchmark_json_serialization(self) -> Dict[str, Any]:
        """Benchmark JSON serialization operations"""
        
        test_data = {
            'data': [{'test': i, 'value': i * 1.5} for i in range(100)],
            'timestamp': datetime.now().isoformat(),
            'metadata': {'test': True, 'version': '1.0'}
        }
        
        iterations = 500
        start_time = time.perf_counter()
        
        for _ in range(iterations):
            json.dumps(test_data, default=str)
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        
        return {
            'iterations': iterations,
            'total_time_ms': total_time * 1000,
            'mean_latency_ms': (total_time / iterations) * 1000,
            'throughput_ops_per_sec': iterations / total_time if total_time > 0 else 0
        }
    
    async def _benchmark_memory_allocation(self) -> Dict[str, Any]:
        """Benchmark memory allocation patterns"""
        
        iterations = 1000
        start_time = time.perf_counter()
        
        # Simulate memory allocation patterns
        allocated_objects = []
        for i in range(iterations):
            obj = {
                'id': i,
                'data': [j for j in range(10)],
                'timestamp': datetime.now()
            }
            allocated_objects.append(obj)
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        
        # Clean up
        del allocated_objects
        gc.collect()
        
        return {
            'iterations': iterations,
            'total_time_ms': total_time * 1000,
            'mean_latency_ms': (total_time / iterations) * 1000,
            'objects_allocated': iterations
        }
    
    async def _benchmark_async_operations(self) -> Dict[str, Any]:
        """Benchmark async operation patterns"""
        
        async def simple_async_operation():
            await asyncio.sleep(0.001)  # 1ms async operation
            return "result"
        
        iterations = 50
        start_time = time.perf_counter()
        
        # Test concurrent async operations
        tasks = [simple_async_operation() for _ in range(iterations)]
        results = await asyncio.gather(*tasks)
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        
        return {
            'iterations': iterations,
            'total_time_ms': total_time * 1000,
            'mean_latency_ms': (total_time / iterations) * 1000,
            'concurrent_operations': len(results)
        }
    
    def _log_optimization_summary(self, improvements: Dict[str, Any]):
        """Log optimization summary"""
        
        logger.info("📊 Performance Optimization Summary:")
        logger.info(f"   Overall Score: {improvements['overall_score']:.1f}/100")
        
        if improvements['operation_improvements']:
            logger.info("   Operation Improvements:")
            for operation, improvement in improvements['operation_improvements'].items():
                direction = "↗️" if improvement['improvement_direction'] == 'better' else "↘️"
                logger.info(f"     {direction} {operation}: {improvement['improvement_percent']:.1f}%")
        
        if improvements['resource_improvements']:
            logger.info("   Resource Improvements:")
            for resource, improvement in improvements['resource_improvements'].items():
                direction = "↗️" if improvement['improvement_direction'] == 'better' else "↘️"
                logger.info(f"     {direction} {resource}: {improvement['improvement_percent']:.1f}%")

# CLI interface
async def main():
    """Run performance optimization suite"""
    
    optimizer = PerformanceOptimizer()
    results = await optimizer.run_optimization_suite()
    
    # Save results
    output_file = f"optimization_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(output_file, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    logger.info(f"📄 Optimization results saved to: {output_file}")
    
    return results

if __name__ == "__main__":
    asyncio.run(main())