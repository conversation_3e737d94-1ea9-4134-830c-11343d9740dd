import {
  PaperTrade,
  Strategy,
  PaperTradingAnalytics,
  PaperTradeRequest,
  TradeType
} from '@golddaddy/types';
import { PaperTradingEngine } from './PaperTradingEngine';
import { VirtualPortfolioService } from './VirtualPortfolioService';
import Decimal from 'decimal.js';

/**
 * Strategy validation result against backtesting
 */
export interface StrategyValidationResult {
  strategyId: string;
  sessionId: string;
  validationScore: number; // 0-100
  performanceDeviation: number; // Percentage deviation from backtest
  consistencyScore: number; // How consistent the strategy performs
  executionQuality: number; // How well trades are executed
  alerts: StrategyAlert[];
  recommendations: string[];
  detailedAnalysis: {
    expectedVsActual: {
      winRate: { expected: number; actual: number; variance: number; };
      profitFactor: { expected: number; actual: number; variance: number; };
      avgReturn: { expected: number; actual: number; variance: number; };
      maxDrawdown: { expected: number; actual: number; variance: number; };
    };
    executionMetrics: {
      slippageImpact: number;
      latencyImpact: number;
      feeImpact: number;
      marketImpactCost: number;
    };
    behaviorAnalysis: {
      overtrading: boolean;
      undertrading: boolean;
      riskAdhesion: number; // How well risk rules are followed
      disciplineScore: number; // How well strategy rules are followed
    };
  };
}

/**
 * Strategy alert types for monitoring deviations
 */
export interface StrategyAlert {
  type: 'performance_deviation' | 'execution_quality' | 'risk_violation' | 'parameter_drift';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * Strategy performance comparison data
 */
export interface StrategyComparisonData {
  backtestingResults: {
    totalTrades: number;
    winRate: number;
    profitFactor: number;
    avgReturn: number;
    maxDrawdown: number;
    sharpeRatio: number;
    calmarRatio: number;
  };
  paperTradingResults: {
    totalTrades: number;
    winRate: number;
    profitFactor: number;
    avgReturn: number;
    maxDrawdown: number;
    sharpeRatio: number;
    calmarRatio: number;
  };
  deviationAnalysis: {
    performanceGap: number;
    executionCosts: number;
    realismFactor: number;
    confidenceLevel: number;
  };
}

/**
 * Strategy testing report for comprehensive analysis
 */
export interface StrategyTestingReport {
  strategyId: string;
  sessionId: string;
  reportDate: Date;
  testingPeriod: {
    startDate: Date;
    endDate: Date;
    durationDays: number;
  };
  overallScore: number;
  validation: StrategyValidationResult;
  comparison: StrategyComparisonData;
  recommendations: {
    parameterAdjustments: Array<{
      parameter: string;
      currentValue: any;
      recommendedValue: any;
      reasoning: string;
    }>;
    executionImprovements: string[];
    riskAdjustments: string[];
    nextSteps: string[];
  };
}

/**
 * Strategy Execution Testing Framework
 * 
 * This service provides comprehensive testing and validation of trading strategies
 * in paper trading environment, comparing against backtesting results and
 * providing insights for strategy optimization and real-world performance expectations.
 */
export class StrategyTestingService {
  constructor(
    private paperTradingEngine: PaperTradingEngine,
    private portfolioService: VirtualPortfolioService
  ) {}

  /**
   * Execute automated strategy validation against backtesting results
   */
  async validateStrategyPerformance(
    strategyId: string,
    sessionId: string,
    backtestingResults: any
  ): Promise<StrategyValidationResult> {
    try {
      // Get paper trading results for this strategy
      const paperTrades = await this.getPaperTradesForStrategy(strategyId, sessionId);
      const paperMetrics = await this.calculatePaperTradingMetrics(paperTrades);
      
      // Compare with backtesting results
      const comparison = this.compareResults(backtestingResults, paperMetrics);
      
      // Generate validation score
      const validationScore = this.calculateValidationScore(comparison);
      
      // Check for performance deviations
      const alerts = this.generatePerformanceAlerts(comparison);
      
      // Generate recommendations
      const recommendations = this.generateRecommendations(comparison, alerts);
      
      // Analyze execution quality
      const executionMetrics = await this.analyzeExecutionQuality(paperTrades);
      
      // Analyze trading behavior
      const behaviorAnalysis = await this.analyzeTradingBehavior(paperTrades, strategyId);
      
      return {
        strategyId,
        sessionId,
        validationScore,
        performanceDeviation: comparison.performanceDeviation,
        consistencyScore: comparison.consistencyScore,
        executionQuality: executionMetrics.overallScore,
        alerts,
        recommendations,
        detailedAnalysis: {
          expectedVsActual: comparison.detailedComparison,
          executionMetrics: {
            slippageImpact: executionMetrics.slippageImpact,
            latencyImpact: executionMetrics.latencyImpact,
            feeImpact: executionMetrics.feeImpact,
            marketImpactCost: executionMetrics.marketImpactCost
          },
          behaviorAnalysis
        }
      };
      
    } catch (error) {
      console.error('Strategy validation failed:', error);
      throw new Error(`Strategy validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate comprehensive strategy testing report
   */
  async generateStrategyTestingReport(
    strategyId: string,
    sessionId: string
  ): Promise<StrategyTestingReport> {
    const strategy = await this.getStrategy(strategyId);
    const backtestingResults = await this.getBacktestingResults(strategyId);
    
    // Get paper trading data
    const paperTrades = await this.getPaperTradesForStrategy(strategyId, sessionId);
    const testingPeriod = this.calculateTestingPeriod(paperTrades);
    
    // Run validation
    const validation = await this.validateStrategyPerformance(strategyId, sessionId, backtestingResults);
    
    // Generate comparison data
    const comparison = await this.generateComparisonData(strategyId, sessionId, backtestingResults);
    
    // Generate recommendations
    const recommendations = await this.generateDetailedRecommendations(
      strategy,
      validation,
      comparison
    );
    
    // Calculate overall score
    const overallScore = this.calculateOverallScore(validation, comparison);
    
    return {
      strategyId,
      sessionId,
      reportDate: new Date(),
      testingPeriod,
      overallScore,
      validation,
      comparison,
      recommendations
    };
  }

  /**
   * Monitor strategy execution for parameter deviation
   */
  async monitorStrategyExecution(
    strategyId: string,
    sessionId: string
  ): Promise<StrategyAlert[]> {
    const alerts: StrategyAlert[] = [];
    
    try {
      const strategy = await this.getStrategy(strategyId);
      const recentTrades = await this.getRecentPaperTrades(strategyId, sessionId, 10);
      
      // Check for parameter drift
      const parameterAlerts = await this.checkParameterDrift(strategy, recentTrades);
      alerts.push(...parameterAlerts);
      
      // Check execution quality
      const executionAlerts = await this.checkExecutionQuality(recentTrades);
      alerts.push(...executionAlerts);
      
      // Check risk adherence
      const riskAlerts = await this.checkRiskAdherence(strategy, recentTrades);
      alerts.push(...riskAlerts);
      
      // Check performance consistency
      const performanceAlerts = await this.checkPerformanceConsistency(strategyId, recentTrades);
      alerts.push(...performanceAlerts);
      
    } catch (error) {
      console.error('Strategy monitoring failed:', error);
      alerts.push({
        type: 'execution_quality',
        severity: 'critical',
        message: 'Strategy monitoring system error',
        timestamp: new Date(),
        metadata: { error: error instanceof Error ? error.message : 'Unknown error' }
      });
    }
    
    return alerts;
  }

  /**
   * Compare strategy performance with optimization results
   */
  async validateParameterOptimization(
    strategyId: string,
    sessionId: string,
    optimizationResults: any
  ): Promise<{
    validationPassed: boolean;
    actualPerformance: number;
    expectedPerformance: number;
    deviation: number;
    insights: string[];
  }> {
    const paperTrades = await this.getPaperTradesForStrategy(strategyId, sessionId);
    const actualMetrics = await this.calculatePaperTradingMetrics(paperTrades);
    
    const expectedReturn = optimizationResults.expectedReturn;
    const actualReturn = actualMetrics.totalReturn;
    const deviation = Math.abs(actualReturn - expectedReturn) / expectedReturn * 100;
    
    const validationPassed = deviation < 15; // Allow 15% deviation
    
    const insights = this.generateOptimizationInsights(
      actualMetrics,
      optimizationResults,
      deviation
    );
    
    return {
      validationPassed,
      actualPerformance: actualReturn,
      expectedPerformance: expectedReturn,
      deviation,
      insights
    };
  }

  // Private helper methods

  private async getPaperTradesForStrategy(
    strategyId: string,
    sessionId: string
  ): Promise<PaperTrade[]> {
    // TODO: Fetch from database using Supabase MCP tool
    // Mock implementation for now
    return [];
  }

  private async getRecentPaperTrades(
    strategyId: string,
    sessionId: string,
    count: number
  ): Promise<PaperTrade[]> {
    const allTrades = await this.getPaperTradesForStrategy(strategyId, sessionId);
    return allTrades.slice(-count);
  }

  private async getStrategy(strategyId: string): Promise<Strategy> {
    // TODO: Fetch from database
    throw new Error('Strategy not found');
  }

  private async getBacktestingResults(strategyId: string): Promise<any> {
    // TODO: Fetch backtesting results from database
    return {
      totalTrades: 100,
      winRate: 0.65,
      profitFactor: 1.8,
      avgReturn: 0.12,
      maxDrawdown: 0.08,
      sharpeRatio: 1.2
    };
  }

  private async calculatePaperTradingMetrics(trades: PaperTrade[]): Promise<any> {
    if (trades.length === 0) {
      return {
        totalTrades: 0,
        winRate: 0,
        profitFactor: 0,
        avgReturn: 0,
        maxDrawdown: 0,
        totalReturn: 0
      };
    }

    const closedTrades = trades.filter(t => t.profit !== undefined);
    const winningTrades = closedTrades.filter(t => (t.profit || 0) > 0);
    
    const winRate = closedTrades.length > 0 ? winningTrades.length / closedTrades.length : 0;
    
    const totalProfit = closedTrades.reduce((sum, t) => sum + (t.profit || 0), 0);
    const totalLoss = Math.abs(closedTrades.filter(t => (t.profit || 0) < 0)
      .reduce((sum, t) => sum + (t.profit || 0), 0));
    
    const profitFactor = totalLoss > 0 ? totalProfit / totalLoss : 0;
    
    return {
      totalTrades: closedTrades.length,
      winRate,
      profitFactor,
      avgReturn: closedTrades.length > 0 ? totalProfit / closedTrades.length : 0,
      maxDrawdown: this.calculateMaxDrawdown(closedTrades),
      totalReturn: totalProfit
    };
  }

  private compareResults(backtestResults: any, paperResults: any): any {
    const performanceDeviation = Math.abs(
      (paperResults.totalReturn - backtestResults.avgReturn) / backtestResults.avgReturn * 100
    );
    
    const consistencyScore = this.calculateConsistencyScore(backtestResults, paperResults);
    
    return {
      performanceDeviation,
      consistencyScore,
      detailedComparison: {
        winRate: {
          expected: backtestResults.winRate,
          actual: paperResults.winRate,
          variance: Math.abs(paperResults.winRate - backtestResults.winRate)
        },
        profitFactor: {
          expected: backtestResults.profitFactor,
          actual: paperResults.profitFactor,
          variance: Math.abs(paperResults.profitFactor - backtestResults.profitFactor)
        },
        avgReturn: {
          expected: backtestResults.avgReturn,
          actual: paperResults.avgReturn,
          variance: Math.abs(paperResults.avgReturn - backtestResults.avgReturn)
        },
        maxDrawdown: {
          expected: backtestResults.maxDrawdown,
          actual: paperResults.maxDrawdown,
          variance: Math.abs(paperResults.maxDrawdown - backtestResults.maxDrawdown)
        }
      }
    };
  }

  private calculateValidationScore(comparison: any): number {
    let score = 100;
    
    // Deduct points based on performance deviation
    if (comparison.performanceDeviation > 20) score -= 30;
    else if (comparison.performanceDeviation > 10) score -= 15;
    else if (comparison.performanceDeviation > 5) score -= 5;
    
    // Deduct points based on consistency
    if (comparison.consistencyScore < 0.7) score -= 20;
    else if (comparison.consistencyScore < 0.8) score -= 10;
    
    return Math.max(score, 0);
  }

  private calculateConsistencyScore(backtestResults: any, paperResults: any): number {
    const winRateConsistency = 1 - Math.abs(paperResults.winRate - backtestResults.winRate);
    const profitFactorConsistency = 1 - Math.abs(paperResults.profitFactor - backtestResults.profitFactor) / backtestResults.profitFactor;
    
    return Math.max((winRateConsistency + profitFactorConsistency) / 2, 0);
  }

  private generatePerformanceAlerts(comparison: any): StrategyAlert[] {
    const alerts: StrategyAlert[] = [];
    
    if (comparison.performanceDeviation > 25) {
      alerts.push({
        type: 'performance_deviation',
        severity: 'critical',
        message: `Strategy performance deviates significantly from backtest (${comparison.performanceDeviation.toFixed(1)}%)`,
        timestamp: new Date()
      });
    } else if (comparison.performanceDeviation > 15) {
      alerts.push({
        type: 'performance_deviation',
        severity: 'high',
        message: `Strategy performance shows concerning deviation from backtest (${comparison.performanceDeviation.toFixed(1)}%)`,
        timestamp: new Date()
      });
    }
    
    if (comparison.consistencyScore < 0.7) {
      alerts.push({
        type: 'performance_deviation',
        severity: 'medium',
        message: 'Strategy showing inconsistent performance compared to backtesting',
        timestamp: new Date()
      });
    }
    
    return alerts;
  }

  private generateRecommendations(comparison: any, alerts: StrategyAlert[]): string[] {
    const recommendations: string[] = [];
    
    if (comparison.performanceDeviation > 15) {
      recommendations.push('Consider adjusting strategy parameters based on real-world execution conditions');
      recommendations.push('Review and optimize order execution timing');
    }
    
    if (comparison.consistencyScore < 0.8) {
      recommendations.push('Increase paper trading duration to better validate strategy performance');
      recommendations.push('Consider market regime analysis to understand performance variations');
    }
    
    if (alerts.some(a => a.type === 'execution_quality')) {
      recommendations.push('Implement execution improvements to reduce slippage and latency costs');
    }
    
    return recommendations;
  }

  private async analyzeExecutionQuality(trades: PaperTrade[]): Promise<any> {
    if (trades.length === 0) {
      return {
        overallScore: 0,
        slippageImpact: 0,
        latencyImpact: 0,
        feeImpact: 0,
        marketImpactCost: 0
      };
    }

    const avgSlippage = trades.reduce((sum, t) => 
      sum + (t.simulationMetadata.actualSlippage || 0), 0) / trades.length;
    
    const avgFees = trades.reduce((sum, t) => sum + t.commission, 0) / trades.length;
    
    let overallScore = 100;
    if (avgSlippage > 0.005) overallScore -= 20;
    if (avgFees > 10) overallScore -= 15;
    
    return {
      overallScore: Math.max(overallScore, 0),
      slippageImpact: avgSlippage * 100,
      latencyImpact: 0.1, // Mock data
      feeImpact: avgFees,
      marketImpactCost: 0.05 // Mock data
    };
  }

  private async analyzeTradingBehavior(trades: PaperTrade[], strategyId: string): Promise<any> {
    const expectedTradeFrequency = 10; // Mock expected trades per period
    const actualTradeFrequency = trades.length;
    
    const overtrading = actualTradeFrequency > expectedTradeFrequency * 1.2;
    const undertrading = actualTradeFrequency < expectedTradeFrequency * 0.8;
    
    return {
      overtrading,
      undertrading,
      riskAdhesion: 0.85, // Mock score
      disciplineScore: 0.90 // Mock score
    };
  }

  private calculateMaxDrawdown(trades: PaperTrade[]): number {
    if (trades.length === 0) return 0;
    
    let peak = 0;
    let runningPnL = 0;
    let maxDrawdown = 0;
    
    for (const trade of trades) {
      runningPnL += (trade.profit || 0);
      if (runningPnL > peak) {
        peak = runningPnL;
      }
      const drawdown = (peak - runningPnL) / Math.abs(peak) * 100;
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown;
      }
    }
    
    return maxDrawdown;
  }

  private calculateTestingPeriod(trades: PaperTrade[]): any {
    if (trades.length === 0) {
      const now = new Date();
      return {
        startDate: now,
        endDate: now,
        durationDays: 0
      };
    }

    const startDate = new Date(Math.min(...trades.map(t => t.openTime.getTime())));
    const endDate = new Date(Math.max(...trades.map(t => t.openTime.getTime())));
    const durationDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));

    return { startDate, endDate, durationDays };
  }

  private async generateComparisonData(
    strategyId: string,
    sessionId: string,
    backtestingResults: any
  ): Promise<StrategyComparisonData> {
    const paperTrades = await this.getPaperTradesForStrategy(strategyId, sessionId);
    const paperMetrics = await this.calculatePaperTradingMetrics(paperTrades);
    
    return {
      backtestingResults,
      paperTradingResults: paperMetrics,
      deviationAnalysis: {
        performanceGap: Math.abs(paperMetrics.totalReturn - backtestingResults.avgReturn),
        executionCosts: 0.02, // Mock data
        realismFactor: 0.85,  // Mock data
        confidenceLevel: 0.78 // Mock data
      }
    };
  }

  private async generateDetailedRecommendations(
    strategy: Strategy,
    validation: StrategyValidationResult,
    comparison: StrategyComparisonData
  ): Promise<any> {
    return {
      parameterAdjustments: [
        {
          parameter: 'stopLoss',
          currentValue: 0.02,
          recommendedValue: 0.025,
          reasoning: 'Increase stop loss to account for real-world slippage'
        }
      ],
      executionImprovements: [
        'Use limit orders during high volatility periods',
        'Implement smart order routing for better fills'
      ],
      riskAdjustments: [
        'Reduce position size by 10% to account for execution costs',
        'Implement dynamic position sizing based on market conditions'
      ],
      nextSteps: [
        'Continue paper trading for at least 30 more days',
        'Test strategy in different market conditions',
        'Consider live trading with reduced position sizes'
      ]
    };
  }

  private calculateOverallScore(validation: StrategyValidationResult, comparison: StrategyComparisonData): number {
    return (validation.validationScore + validation.consistencyScore * 100 + validation.executionQuality) / 3;
  }

  private async checkParameterDrift(strategy: Strategy, trades: PaperTrade[]): Promise<StrategyAlert[]> {
    // Mock implementation - would check if actual trades match strategy parameters
    return [];
  }

  private async checkExecutionQuality(trades: PaperTrade[]): Promise<StrategyAlert[]> {
    const alerts: StrategyAlert[] = [];
    
    const highSlippageTrades = trades.filter(t => 
      (t.simulationMetadata.actualSlippage || 0) > 0.01
    );
    
    if (highSlippageTrades.length / trades.length > 0.2) {
      alerts.push({
        type: 'execution_quality',
        severity: 'medium',
        message: 'High slippage detected in recent trades',
        timestamp: new Date(),
        metadata: { affectedTrades: highSlippageTrades.length }
      });
    }
    
    return alerts;
  }

  private async checkRiskAdherence(strategy: Strategy, trades: PaperTrade[]): Promise<StrategyAlert[]> {
    const alerts: StrategyAlert[] = [];
    
    // Check if trades are following strategy risk parameters
    const oversizedTrades = trades.filter(t => 
      new Decimal(t.volume).mul(t.openPrice).gt(new Decimal(strategy.riskPerTrade).mul(10000))
    );
    
    if (oversizedTrades.length > 0) {
      alerts.push({
        type: 'risk_violation',
        severity: 'high',
        message: 'Trades exceeding strategy risk parameters detected',
        timestamp: new Date(),
        metadata: { violatingTrades: oversizedTrades.length }
      });
    }
    
    return alerts;
  }

  private async checkPerformanceConsistency(strategyId: string, trades: PaperTrade[]): Promise<StrategyAlert[]> {
    // Mock implementation - would check performance consistency over time
    return [];
  }

  private generateOptimizationInsights(
    actualMetrics: any,
    optimizationResults: any,
    deviation: number
  ): string[] {
    const insights: string[] = [];
    
    if (deviation > 20) {
      insights.push('Significant deviation suggests optimization may have overfit to historical data');
    }
    
    if (actualMetrics.winRate < optimizationResults.winRate * 0.8) {
      insights.push('Win rate significantly lower than expected - review entry conditions');
    }
    
    if (actualMetrics.maxDrawdown > optimizationResults.maxDrawdown * 1.2) {
      insights.push('Higher drawdown than expected - consider tighter risk controls');
    }
    
    return insights;
  }
}