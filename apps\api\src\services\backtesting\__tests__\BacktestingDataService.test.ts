import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PrismaClient, Timeframe, DataSource, MarketRegime } from '@prisma/client';
import { BacktestingDataService, HistoricalDataRequest } from '../BacktestingDataService';

// Mock PrismaClient
const mockPrisma = {
  marketData: {
    findMany: vi.fn(),
    aggregate: vi.fn(),
    upsert: vi.fn(),
  },
  $transaction: vi.fn(),
} as unknown as PrismaClient;

describe('BacktestingDataService', () => {
  let service: BacktestingDataService;

  beforeEach(() => {
    service = new BacktestingDataService(mockPrisma);
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('getHistoricalData', () => {
    it('should retrieve historical data successfully', async () => {
      const mockData = [
        {
          timestamp: new Date('2023-01-01T00:00:00Z'),
          instrument: 'EURUSD',
          timeframe: Timeframe.H1,
          open: { toNumber: () => 1.0500 },
          high: { toNumber: () => 1.0520 },
          low: { toNumber: () => 1.0495 },
          close: { toNumber: () => 1.0510 },
          volume: { toNumber: () => 1000000 },
          source: DataSource.MT5,
          indicators: null,
        },
      ];

      (mockPrisma.marketData.findMany as any).mockResolvedValue(mockData);

      const request: HistoricalDataRequest = {
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        startDate: new Date('2023-01-01T00:00:00Z'),
        endDate: new Date('2023-01-02T00:00:00Z'),
      };

      const result = await service.getHistoricalData(request);

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        timestamp: new Date('2023-01-01T00:00:00Z'),
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        open: 1.0500,
        high: 1.0520,
        low: 1.0495,
        close: 1.0510,
        volume: 1000000,
        source: DataSource.MT5,
        indicators: undefined,
      });

      expect(mockPrisma.marketData.findMany).toHaveBeenCalledWith({
        where: {
          instrument: 'EURUSD',
          timeframe: Timeframe.H1,
          timestamp: {
            gte: request.startDate,
            lte: request.endDate,
          },
        },
        orderBy: {
          timestamp: 'asc',
        },
      });
    });

    it('should handle errors gracefully', async () => {
      (mockPrisma.marketData.findMany as any).mockRejectedValue(new Error('Database error'));

      const request: HistoricalDataRequest = {
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        startDate: new Date('2023-01-01T00:00:00Z'),
        endDate: new Date('2023-01-02T00:00:00Z'),
      };

      await expect(service.getHistoricalData(request)).rejects.toThrow('Failed to retrieve historical data: Database error');
    });
  });

  describe('getAvailableInstruments', () => {
    it('should return list of available instruments', async () => {
      const mockInstruments = [
        { instrument: 'EURUSD' },
        { instrument: 'GBPUSD' },
        { instrument: 'USDJPY' },
      ];

      (mockPrisma.marketData.findMany as any).mockResolvedValue(mockInstruments);

      const result = await service.getAvailableInstruments();

      expect(result).toEqual(['EURUSD', 'GBPUSD', 'USDJPY']);
      expect(mockPrisma.marketData.findMany).toHaveBeenCalledWith({
        select: {
          instrument: true,
        },
        distinct: ['instrument'],
        orderBy: {
          instrument: 'asc',
        },
      });
    });
  });

  describe('getDataRange', () => {
    it('should return date range for instrument', async () => {
      const mockRange = {
        _min: { timestamp: new Date('2023-01-01T00:00:00Z') },
        _max: { timestamp: new Date('2023-12-31T23:59:59Z') },
      };

      (mockPrisma.marketData.aggregate as any).mockResolvedValue(mockRange);

      const result = await service.getDataRange('EURUSD', Timeframe.H1);

      expect(result).toEqual({
        startDate: new Date('2023-01-01T00:00:00Z'),
        endDate: new Date('2023-12-31T23:59:59Z'),
      });
    });

    it('should return null when no data available', async () => {
      const mockRange = {
        _min: { timestamp: null },
        _max: { timestamp: null },
      };

      (mockPrisma.marketData.aggregate as any).mockResolvedValue(mockRange);

      const result = await service.getDataRange('UNKNOWN', Timeframe.H1);

      expect(result).toBeNull();
    });
  });

  describe('detectMarketRegimes', () => {
    it('should detect market regimes', async () => {
      // Create mock data with enough points for regime analysis
      const mockData = Array.from({ length: 100 }, (_, i) => ({
        timestamp: new Date(2023, 0, 1, i),
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        open: 1.0500 + (Math.random() - 0.5) * 0.01,
        high: 1.0520 + (Math.random() - 0.5) * 0.01,
        low: 1.0495 + (Math.random() - 0.5) * 0.01,
        close: 1.0510 + (Math.random() - 0.5) * 0.01,
        volume: 1000000,
        source: DataSource.MT5,
      }));

      // Mock the getHistoricalData method
      vi.spyOn(service, 'getHistoricalData').mockResolvedValue(mockData);

      const result = await service.detectMarketRegimes(
        'EURUSD',
        Timeframe.H1,
        new Date('2023-01-01T00:00:00Z'),
        new Date('2023-01-01T23:59:59Z')
      );

      expect(result).toBeInstanceOf(Array);
      expect(result.length).toBeGreaterThan(0);
      
      if (result.length > 0) {
        expect(result[0]).toHaveProperty('regime');
        expect(result[0]).toHaveProperty('confidence');
        expect(result[0]).toHaveProperty('volatility');
        expect(result[0]).toHaveProperty('trendStrength');
        expect(result[0]).toHaveProperty('period');
      }
    });
  });

  describe('calculateIndicators', () => {
    it('should calculate technical indicators', async () => {
      // Create test data with consistent price trend
      const testData = Array.from({ length: 60 }, (_, i) => ({
        timestamp: new Date(2023, 0, 1, i),
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        open: 1.0500 + i * 0.0001,
        high: 1.0520 + i * 0.0001,
        low: 1.0495 + i * 0.0001,
        close: 1.0510 + i * 0.0001, // Trending upward
        volume: 1000000,
        source: DataSource.MT5,
      }));

      const result = await service.calculateIndicators(testData);

      expect(result).toHaveLength(testData.length);

      // Check that SMA 20 is calculated after 20 periods
      const point20 = result[19]; // 20th point (index 19)
      expect(point20.indicators?.sma_20).toBeDefined();
      expect(typeof point20.indicators?.sma_20).toBe('number');

      // Check that SMA 50 is calculated after 50 periods
      const point50 = result[49]; // 50th point (index 49)
      expect(point50.indicators?.sma_50).toBeDefined();
      expect(typeof point50.indicators?.sma_50).toBe('number');

      // Check that RSI is calculated after 15 periods
      const point15 = result[14]; // 15th point (index 14)
      expect(point15.indicators?.rsi_14).toBeDefined();
      expect(point15.indicators?.rsi_14).toBeGreaterThan(0);
      expect(point15.indicators?.rsi_14).toBeLessThanOrEqual(100);

      // Check Bollinger Bands
      expect(point20.indicators?.bollinger_upper).toBeDefined();
      expect(point20.indicators?.bollinger_lower).toBeDefined();
      expect(point20.indicators?.bollinger_upper).toBeGreaterThan(point20.indicators?.bollinger_lower);
    });

    it('should handle empty data array', async () => {
      const result = await service.calculateIndicators([]);
      expect(result).toEqual([]);
    });
  });

  describe('storeHistoricalData', () => {
    it('should store historical data successfully', async () => {
      const testData = [{
        timestamp: new Date('2023-01-01T00:00:00Z'),
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        open: 1.0500,
        high: 1.0520,
        low: 1.0495,
        close: 1.0510,
        volume: 1000000,
        source: DataSource.MT5,
        indicators: { sma_20: 1.0505 },
      }];

      const mockTransaction = vi.fn().mockImplementation((callback) => callback({
        marketData: {
          upsert: vi.fn().mockResolvedValue({}),
        },
      }));

      (mockPrisma.$transaction as any).mockImplementation(mockTransaction);

      await service.storeHistoricalData(testData);

      expect(mockTransaction).toHaveBeenCalled();
    });

    it('should handle storage errors', async () => {
      const testData = [{
        timestamp: new Date('2023-01-01T00:00:00Z'),
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        open: 1.0500,
        high: 1.0520,
        low: 1.0495,
        close: 1.0510,
        volume: 1000000,
        source: DataSource.MT5,
      }];

      (mockPrisma.$transaction as any).mockRejectedValue(new Error('Storage error'));

      await expect(service.storeHistoricalData(testData)).rejects.toThrow('Failed to store historical data: Storage error');
    });
  });

  describe('Market Regime Analysis', () => {
    it('should correctly identify trending market', () => {
      // Create strongly trending data
      const trendingData = Array.from({ length: 20 }, (_, i) => ({
        timestamp: new Date(2023, 0, 1, i),
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        open: 1.0500 + i * 0.001,
        high: 1.0520 + i * 0.001,
        low: 1.0495 + i * 0.001,
        close: 1.0510 + i * 0.001, // Strong upward trend
        volume: 1000000,
        source: DataSource.MT5,
      }));

      // Access the private method through reflection for testing
      const analyzeWindowRegime = (service as any).analyzeWindowRegime.bind(service);
      const result = analyzeWindowRegime(trendingData);

      expect(result.regime).toBe(MarketRegime.TRENDING);
      expect(result.trendStrength).toBeGreaterThan(0.7);
      expect(result.confidence).toBeGreaterThan(0.7);
    });

    it('should correctly identify volatile market', () => {
      // Create highly volatile data
      const volatileData = Array.from({ length: 20 }, (_, i) => ({
        timestamp: new Date(2023, 0, 1, i),
        instrument: 'EURUSD',
        timeframe: Timeframe.H1,
        open: 1.0500,
        high: 1.0520,
        low: 1.0495,
        close: 1.0510 + (Math.random() - 0.5) * 0.05, // High volatility
        volume: 1000000,
        source: DataSource.MT5,
      }));

      const analyzeWindowRegime = (service as any).analyzeWindowRegime.bind(service);
      const result = analyzeWindowRegime(volatileData);

      expect(result.volatility).toBeGreaterThan(0.01);
    });

    it('should calculate RSI correctly', () => {
      const prices = [44, 44.34, 44.09, 44.15, 44.25, 43.23, 42.35, 42.30, 42.10, 42.84, 43.13, 42.26, 42.09, 42.15];
      
      const calculateRSI = (service as any).calculateRSI.bind(service);
      const rsi = calculateRSI(prices);

      expect(rsi).toBeGreaterThan(0);
      expect(rsi).toBeLessThan(100);
      expect(rsi).toBeCloseTo(31.61, 1); // Actual calculated RSI value for this data
    });

    it('should calculate trend strength correctly', () => {
      const prices = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // Perfect upward trend
      
      const calculateTrendStrength = (service as any).calculateTrendStrength.bind(service);
      const trendStrength = calculateTrendStrength(prices);

      expect(trendStrength).toBeCloseTo(1, 2); // Perfect correlation should be close to 1
    });
  });
});