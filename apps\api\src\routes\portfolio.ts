import { Router, Request, Response, NextFunction } from 'express';
import { rateLimit } from 'express-rate-limit';
import { VirtualPortfolioService } from '../services/trading/VirtualPortfolioService';
import { PaperTradingAnalyticsService } from '../services/trading/PaperTradingAnalyticsService';
import { z } from 'zod';

// Rate limiting configuration
const portfolioRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 60, // Limit each user to 60 portfolio requests per minute
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    error: 'Too many portfolio requests. Please wait before trying again.',
    code: 'RATE_LIMIT_EXCEEDED',
    retryAfter: 60
  }
});

// Validation schemas
const PortfolioHistorySchema = z.object({
  sessionId: z.string().uuid('Invalid session ID'),
  fromDate: z.string().datetime().optional(),
  toDate: z.string().datetime().optional(),
  granularity: z.enum(['minute', 'hour', 'day']).default('hour'),
});

const PortfolioComparisonSchema = z.object({
  sessionIds: z.array(z.string().uuid()).min(1).max(5, 'Maximum 5 sessions can be compared'),
  metrics: z.array(z.string()).optional(),
});

interface PortfolioControllers {
  getVirtualPortfolioController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getPortfolioHistoryController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getPortfolioMetricsController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getPortfolioComparisonController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getPortfolioSnapshotController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
  getUserPortfoliosController: (req: Request, res: Response, next: NextFunction) => Promise<void>;
}

function createPortfolioControllers(
  portfolioService: VirtualPortfolioService,
  analyticsService: PaperTradingAnalyticsService
): PortfolioControllers {

  const getVirtualPortfolioController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const portfolio = await portfolioService.getVirtualPortfolio(userId, sessionId);

      if (!portfolio) {
        res.status(404).json({
          success: false,
          error: 'Portfolio not found',
          code: 'PORTFOLIO_NOT_FOUND'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: portfolio,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const getPortfolioHistoryController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      const validatedData = PortfolioHistorySchema.parse({
        sessionId: req.query.sessionId,
        fromDate: req.query.fromDate,
        toDate: req.query.toDate,
        granularity: req.query.granularity || 'hour',
      });

      const history = await portfolioService.getPortfolioHistory(
        userId,
        validatedData.sessionId,
        {
          fromDate: validatedData.fromDate ? new Date(validatedData.fromDate) : undefined,
          toDate: validatedData.toDate ? new Date(validatedData.toDate) : undefined,
          granularity: validatedData.granularity,
        }
      );

      res.status(200).json({
        success: true,
        data: {
          history,
          sessionId: validatedData.sessionId,
          granularity: validatedData.granularity,
        },
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      if (error.name === 'ZodError') {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors
        });
        return;
      }
      next(error);
    }
  };

  const getPortfolioMetricsController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const metrics = await portfolioService.calculatePortfolioMetrics(userId, sessionId);

      res.status(200).json({
        success: true,
        data: {
          sessionId,
          metrics,
          calculatedAt: new Date().toISOString(),
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const getPortfolioComparisonController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      const validatedData = PortfolioComparisonSchema.parse(req.body);

      const comparison = await analyticsService.comparePortfolios(
        userId,
        validatedData.sessionIds,
        validatedData.metrics
      );

      res.status(200).json({
        success: true,
        data: {
          comparison,
          sessionIds: validatedData.sessionIds,
          comparedAt: new Date().toISOString(),
        },
        timestamp: new Date().toISOString()
      });

    } catch (error: any) {
      if (error.name === 'ZodError') {
        res.status(400).json({
          success: false,
          error: 'Validation failed',
          code: 'VALIDATION_ERROR',
          details: error.errors
        });
        return;
      }
      next(error);
    }
  };

  const getPortfolioSnapshotController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;
      const { sessionId } = req.params;
      const { timestamp } = req.query;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      if (!sessionId || !sessionId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
        res.status(400).json({
          success: false,
          error: 'Valid session ID required',
          code: 'INVALID_SESSION_ID'
        });
        return;
      }

      const snapshotTime = timestamp ? new Date(timestamp as string) : new Date();
      const snapshot = await portfolioService.getPortfolioSnapshot(userId, sessionId, snapshotTime);

      if (!snapshot) {
        res.status(404).json({
          success: false,
          error: 'Portfolio snapshot not found for the specified time',
          code: 'SNAPSHOT_NOT_FOUND'
        });
        return;
      }

      res.status(200).json({
        success: true,
        data: {
          snapshot,
          sessionId,
          timestamp: snapshotTime.toISOString(),
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  const getUserPortfoliosController = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
    try {
      const userId = req.user?.id;

      if (!userId) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'UNAUTHORIZED'
        });
        return;
      }

      const { status, limit = '10', offset = '0' } = req.query;

      const portfolios = await portfolioService.getUserPortfolios(
        userId,
        {
          status: status as string,
          limit: parseInt(limit as string, 10),
          offset: parseInt(offset as string, 10),
        }
      );

      res.status(200).json({
        success: true,
        data: {
          portfolios,
          pagination: {
            limit: parseInt(limit as string, 10),
            offset: parseInt(offset as string, 10),
          },
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      next(error);
    }
  };

  return {
    getVirtualPortfolioController,
    getPortfolioHistoryController,
    getPortfolioMetricsController,
    getPortfolioComparisonController,
    getPortfolioSnapshotController,
    getUserPortfoliosController,
  };
}

// Export the router factory function
export const createPortfolioRoutes = (services: {
  portfolioService: VirtualPortfolioService;
  analyticsService: PaperTradingAnalyticsService;
}) => {
  const router = Router();

  // Apply rate limiting
  router.use(portfolioRateLimit);

  // Create controllers
  const controllers = createPortfolioControllers(
    services.portfolioService,
    services.analyticsService
  );

  // === VIRTUAL PORTFOLIO ROUTES ===

  /**
   * @route GET /api/portfolio/virtual/:sessionId
   * @desc Get virtual portfolio status
   * @access Private
   */
  router.get('/virtual/:sessionId', controllers.getVirtualPortfolioController);

  /**
   * @route GET /api/portfolio/history
   * @desc Get portfolio value history
   * @access Private
   */
  router.get('/history', controllers.getPortfolioHistoryController);

  /**
   * @route GET /api/portfolio/metrics/:sessionId
   * @desc Get detailed portfolio metrics
   * @access Private
   */
  router.get('/metrics/:sessionId', controllers.getPortfolioMetricsController);

  /**
   * @route POST /api/portfolio/comparison
   * @desc Compare multiple portfolios
   * @access Private
   */
  router.post('/comparison', controllers.getPortfolioComparisonController);

  /**
   * @route GET /api/portfolio/snapshot/:sessionId
   * @desc Get portfolio snapshot at specific timestamp
   * @access Private
   */
  router.get('/snapshot/:sessionId', controllers.getPortfolioSnapshotController);

  /**
   * @route GET /api/portfolio
   * @desc Get all user portfolios
   * @access Private
   */
  router.get('/', controllers.getUserPortfoliosController);

  // === ERROR HANDLING ===

  // Handle 404 for unmatched portfolio routes
  router.use((req: Request, res: Response) => {
    res.status(404).json({
      success: false,
      error: 'Portfolio endpoint not found',
      code: 'ENDPOINT_NOT_FOUND',
      availableEndpoints: [
        'GET /api/portfolio/virtual/:sessionId',
        'GET /api/portfolio/history',
        'GET /api/portfolio/metrics/:sessionId',
        'POST /api/portfolio/comparison',
        'GET /api/portfolio/snapshot/:sessionId',
        'GET /api/portfolio'
      ]
    });
  });

  // Global error handler for portfolio routes
  router.use((error: any, req: Request, res: Response, _next: NextFunction) => {
    console.error('Portfolio route error:', {
      error: error.message,
      stack: error.stack,
      path: req.path,
      method: req.method,
      user: req.user?.id,
      timestamp: new Date().toISOString()
    });

    // Handle specific portfolio errors
    if (error.code === 'PORTFOLIO_ACCESS_DENIED') {
      res.status(403).json({
        success: false,
        error: 'Access denied to this portfolio',
        code: 'PORTFOLIO_ACCESS_DENIED',
        timestamp: new Date().toISOString()
      });
      return;
    }

    if (error.code === 'INVALID_PORTFOLIO_STATE') {
      res.status(400).json({
        success: false,
        error: 'Portfolio is in an invalid state for this operation',
        code: 'INVALID_PORTFOLIO_STATE',
        timestamp: new Date().toISOString()
      });
      return;
    }

    // Generic error response
    const isDevelopment = process.env.NODE_ENV === 'development';
    res.status(error.status || 500).json({
      success: false,
      error: isDevelopment ? error.message : 'Internal server error',
      code: error.code || 'INTERNAL_ERROR',
      ...(isDevelopment && { stack: error.stack }),
      timestamp: new Date().toISOString()
    });
  });

  return router;
};