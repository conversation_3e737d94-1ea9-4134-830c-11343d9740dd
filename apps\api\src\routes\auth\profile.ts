import { Router, Response } from 'express';
import { z } from 'zod';
import { authenticateToken, AuthenticatedRequest } from '../../lib/auth';
import { userOperations } from '../../lib/database';
import { auditLogger, AUDIT_ACTIONS } from '../../lib/audit';

const router = Router();

// Profile update validation schema
const UpdateProfileSchema = z.object({
  displayName: z.string().min(1, 'Display name is required').optional(),
  riskTolerance: z.enum(['conservative', 'moderate', 'aggressive']).optional(),
  experienceLevel: z.enum(['beginner', 'intermediate', 'advanced']).optional(),
  coachingStyle: z.enum(['guided', 'independent', 'assisted']).optional(),
  tradingCapital: z.number().positive().optional(),
  notifications: z.boolean().optional(),
  autoOptimization: z.boolean().optional(),
  paperTradingOnly: z.boolean().optional(),
});

// GET /api/auth/profile - Get current user profile
router.get('/profile', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ error: 'User ID not found' });
    }

    // Get user profile from database
    const userProfile = await userOperations.findById(userId);
    
    if (!userProfile) {
      return res.status(404).json({
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User profile not found',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Check if user account is soft deleted
    if (userProfile.deletedAt) {
      return res.status(403).json({
        error: {
          code: 'ACCOUNT_DELETED',
          message: 'This account has been deleted',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Log data access
    await auditLogger.logDataAccess(userId, 'users', userId, req);

    // Return user profile
    res.status(200).json({
      data: {
        user: {
          id: userProfile.id,
          email: userProfile.email,
          displayName: userProfile.displayName,
          experienceLevel: userProfile.experienceLevel,
          riskTolerance: userProfile.riskTolerance,
          coachingStyle: userProfile.coachingStyle,
          notifications: userProfile.notifications,
          autoOptimization: userProfile.autoOptimization,
          paperTradingOnly: userProfile.paperTradingOnly,
          createdAt: userProfile.createdAt,
          updatedAt: userProfile.updatedAt,
        },
        goals: userProfile.goals.map(goal => ({
          id: goal.id,
          title: goal.title,
          status: goal.status,
          targetReturn: goal.targetReturn,
          returnAchieved: goal.returnAchieved,
          daysElapsed: goal.daysElapsed,
          createdAt: goal.createdAt,
        })),
        featureFlags: userProfile.featureFlags,
        confidenceAssessment: userProfile.confidenceAssessment,
      },
      status: 'success',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Profile fetch error:', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to retrieve user profile',
        timestamp: new Date().toISOString(),
      },
    });
  }
});

// PUT /api/auth/profile - Update user profile
router.put('/profile', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ error: 'User ID not found' });
    }

    // Validate request body
    const validationResult = UpdateProfileSchema.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        error: {
          code: 'VALIDATION_ERROR',
          message: 'Invalid profile data',
          details: validationResult.error.errors,
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Get current user profile for audit logging
    const currentProfile = await userOperations.findById(userId);
    if (!currentProfile) {
      return res.status(404).json({
        error: {
          code: 'USER_NOT_FOUND',
          message: 'User profile not found',
          timestamp: new Date().toISOString(),
        },
      });
    }

    // Update user profile
    const updatedProfile = await userOperations.update(userId, validationResult.data);

    // Log profile update
    await auditLogger.logDataChange(
      userId,
      AUDIT_ACTIONS.PROFILE_UPDATE,
      'users',
      userId,
      {
        displayName: currentProfile.displayName,
        riskTolerance: currentProfile.riskTolerance,
        experienceLevel: currentProfile.experienceLevel,
        coachingStyle: currentProfile.coachingStyle,
      },
      validationResult.data,
      req
    );

    // Return updated profile
    res.status(200).json({
      data: {
        user: {
          id: updatedProfile.id,
          email: updatedProfile.email,
          displayName: updatedProfile.displayName,
          experienceLevel: updatedProfile.experienceLevel,
          riskTolerance: updatedProfile.riskTolerance,
          coachingStyle: updatedProfile.coachingStyle,
          notifications: updatedProfile.notifications,
          autoOptimization: updatedProfile.autoOptimization,
          paperTradingOnly: updatedProfile.paperTradingOnly,
          updatedAt: updatedProfile.updatedAt,
        },
      },
      status: 'success',
      message: 'Profile updated successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Profile update error:', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to update user profile',
        timestamp: new Date().toISOString(),
      },
    });
  }
});

// POST /api/auth/signout - Sign out user
router.post('/signout', authenticateToken, async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.userId;
    if (!userId) {
      return res.status(401).json({ error: 'User ID not found' });
    }

    // Log sign out event
    await auditLogger.logAuditEvent({
      userId,
      action: AUDIT_ACTIONS.LOGOUT,
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
    });

    res.status(200).json({
      status: 'success',
      message: 'Signed out successfully',
      timestamp: new Date().toISOString(),
    });

  } catch (error) {
    console.error('Sign out error:', error);
    return res.status(500).json({
      error: {
        code: 'INTERNAL_ERROR',
        message: 'Failed to sign out',
        timestamp: new Date().toISOString(),
      },
    });
  }
});

export default router;