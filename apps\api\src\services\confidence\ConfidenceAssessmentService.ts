import { PrismaClient } from '@prisma/client';
import {
  ConfidenceStage,
  QuizDifficulty,
  QuizCategory,
  QuizSessionStatus,
  QuizAttemptStatus,
  type QuizSession,
  type QuizAttempt,
  type QuizResponse,
  type QuizQuestion,
  type ConfidenceAssessment
} from '@golddaddy/types';
import { QuizContentService } from './QuizContentService.js';
import { QuizComplexityEngine } from './QuizComplexityEngine.js';

interface StartQuizSessionRequest {
  userId: string;
  stage: ConfidenceStage;
  userExperience?: 'beginner' | 'intermediate' | 'advanced';
  customSettings?: {
    timeLimit?: number;
    questionCount?: number;
    difficulty?: QuizDifficulty;
    focusCategories?: QuizCategory[];
  };
}

interface SubmitResponseRequest {
  sessionId: string;
  userId: string;
  questionId: string;
  selectedAnswerIds: string[];
  timeSpent: number;
  confidenceLevel?: number; // 1-5 scale
}

interface CompleteSessionRequest {
  sessionId: string;
  userId: string;
  totalTimeSpent: number;
}

interface QuizSessionResult {
  session: QuizSession;
  attempt: QuizAttempt;
  confidenceScore: number;
  feedback: {
    overall: string;
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
    nextSteps: string[];
  };
  stagingRecommendation: {
    readyForNextStage: boolean;
    requiredImprovements?: string[];
    estimatedTimeToReady?: number; // days
  };
}

export class ConfidenceAssessmentService {
  private prisma: PrismaClient;
  private contentService: QuizContentService;
  private complexityEngine: QuizComplexityEngine;

  // Scoring weights for different aspects
  private readonly SCORING_WEIGHTS = {
    accuracy: 0.4,           // 40% - correctness of answers
    timeEfficiency: 0.25,    // 25% - time management
    consistency: 0.2,        // 20% - consistent performance
    confidence: 0.15         // 15% - self-reported confidence
  };

  // Stage progression thresholds
  private readonly STAGE_THRESHOLDS = {
    [ConfidenceStage.GOAL_SETTING]: { minScore: 75, minAttempts: 1 },
    [ConfidenceStage.STRATEGY_LEARNING]: { minScore: 80, minAttempts: 2 },
    [ConfidenceStage.BACKTESTING_REVIEW]: { minScore: 85, minAttempts: 2 },
    [ConfidenceStage.PAPER_TRADING]: { minScore: 90, minAttempts: 3 },
    [ConfidenceStage.LIVE_READY]: { minScore: 95, minAttempts: 2 }
  };

  constructor(prisma: PrismaClient) {
    this.prisma = prisma;
    this.contentService = new QuizContentService(prisma);
    this.complexityEngine = new QuizComplexityEngine();
  }

  async startQuizSession(request: StartQuizSessionRequest): Promise<QuizSession> {
    // Get user's previous attempts to determine performance profile
    const previousAttempts = await this.getUserAttemptHistory(
      request.userId,
      request.stage
    );

    // Get current confidence assessment
    const assessment = await this.getCurrentConfidenceAssessment(request.userId);

    // Calculate quiz complexity recommendation
    const userProfile = this.complexityEngine.createUserPerformanceProfile(previousAttempts);
    const complexity = this.complexityEngine.calculateComplexityRecommendation(
      request.stage,
      userProfile,
      assessment
    );

    // Apply custom settings if provided
    const finalSettings = {
      timeLimit: request.customSettings?.timeLimit || complexity.timeLimit,
      questionCount: request.customSettings?.questionCount || complexity.questionCount,
      difficulty: request.customSettings?.difficulty || complexity.difficulty,
      focusCategories: request.customSettings?.focusCategories || complexity.focusCategories
    };

    // Generate quiz questions based on complexity recommendation
    const questions = await this.generateSessionQuestions(
      request.stage,
      request.userExperience || 'beginner',
      finalSettings
    );

    // Create quiz session
    const session = await this.prisma.quizSession.create({
      data: {
        userId: request.userId,
        stage: request.stage,
        difficulty: finalSettings.difficulty,
        totalQuestions: questions.length,
        timeLimit: finalSettings.timeLimit,
        questionsData: questions,
        settings: {
          focusCategories: finalSettings.focusCategories,
          userExperience: request.userExperience,
          complexityReasoning: complexity.reasoning
        },
        status: QuizSessionStatus.IN_PROGRESS,
        startedAt: new Date()
      }
    });

    return this.mapPrismaToQuizSession(session);
  }

  async submitResponse(request: SubmitResponseRequest): Promise<QuizResponse> {
    // Validate session exists and is active
    const session = await this.prisma.quizSession.findUnique({
      where: { id: request.sessionId }
    });

    if (!session || session.userId !== request.userId) {
      throw new Error('Invalid quiz session');
    }

    if (session.status !== QuizSessionStatus.IN_PROGRESS) {
      throw new Error('Quiz session is not active');
    }

    // Check for timeout
    const timeElapsed = Date.now() - session.startedAt.getTime();
    if (timeElapsed > session.timeLimit * 1000) {
      await this.handleSessionTimeout(request.sessionId);
      throw new Error('Quiz session has timed out');
    }

    // Get question details for validation
    const questions = session.questionsData as QuizQuestion[];
    const question = questions.find(q => q.id === request.questionId);
    
    if (!question) {
      throw new Error('Invalid question ID');
    }

    // Calculate if answer is correct
    const isCorrect = this.validateAnswer(question, request.selectedAnswerIds);

    // Create response record
    const response = await this.prisma.quizResponse.create({
      data: {
        sessionId: request.sessionId,
        userId: request.userId,
        questionId: request.questionId,
        selectedAnswerIds: request.selectedAnswerIds,
        isCorrect,
        timeSpent: request.timeSpent,
        confidenceLevel: request.confidenceLevel || 3,
        submittedAt: new Date()
      }
    });

    return this.mapPrismaToQuizResponse(response);
  }

  async completeSession(request: CompleteSessionRequest): Promise<QuizSessionResult> {
    // Get session with responses
    const session = await this.prisma.quizSession.findUnique({
      where: { id: request.sessionId },
      include: {
        responses: true
      }
    });

    if (!session || session.userId !== request.userId) {
      throw new Error('Invalid quiz session');
    }

    // Calculate quiz attempt results
    const attemptData = await this.calculateAttemptResults(session);

    // Create quiz attempt record
    const attempt = await this.prisma.quizAttempt.create({
      data: {
        userId: request.userId,
        sessionId: request.sessionId,
        attemptNumber: await this.getNextAttemptNumber(request.userId, session.stage),
        score: attemptData.score,
        totalTimeSpent: request.totalTimeSpent,
        questionsAnswered: session.responses.length,
        correctAnswers: attemptData.correctAnswers,
        weakAreas: attemptData.weakAreas,
        strongAreas: attemptData.strongAreas,
        confidenceScore: attemptData.confidenceScore,
        feedback: attemptData.feedback,
        status: QuizAttemptStatus.COMPLETED
      }
    });

    // Update session status
    await this.prisma.quizSession.update({
      where: { id: request.sessionId },
      data: {
        status: QuizSessionStatus.COMPLETED,
        completedAt: new Date(),
        finalScore: attemptData.score
      }
    });

    // Update or create confidence assessment
    await this.updateConfidenceAssessment(
      request.userId,
      session.stage,
      attemptData
    );

    // Generate staging recommendation
    const stagingRecommendation = await this.generateStagingRecommendation(
      request.userId,
      session.stage
    );

    return {
      session: this.mapPrismaToQuizSession({
        ...session,
        status: QuizSessionStatus.COMPLETED,
        completedAt: new Date(),
        finalScore: attemptData.score
      }),
      attempt: this.mapPrismaToQuizAttempt(attempt),
      confidenceScore: attemptData.confidenceScore,
      feedback: attemptData.feedback,
      stagingRecommendation
    };
  }

  async getSessionProgress(sessionId: string, userId: string): Promise<{
    timeRemaining: number;
    questionsAnswered: number;
    totalQuestions: number;
    currentScore: number;
  }> {
    const session = await this.prisma.quizSession.findUnique({
      where: { id: sessionId },
      include: { responses: true }
    });

    if (!session || session.userId !== userId) {
      throw new Error('Invalid quiz session');
    }

    const timeElapsed = Date.now() - session.startedAt.getTime();
    const timeRemaining = Math.max(0, session.timeLimit * 1000 - timeElapsed);

    // Calculate current score from responses
    const correctResponses = session.responses.filter(r => r.isCorrect).length;
    const currentScore = session.responses.length > 0 
      ? (correctResponses / session.responses.length) * 100 
      : 0;

    return {
      timeRemaining: Math.floor(timeRemaining / 1000),
      questionsAnswered: session.responses.length,
      totalQuestions: session.totalQuestions,
      currentScore: Math.round(currentScore)
    };
  }

  private async getUserAttemptHistory(
    userId: string, 
    stage?: ConfidenceStage
  ): Promise<QuizAttempt[]> {
    const where: any = { userId };
    if (stage) {
      where.session = { stage };
    }

    const attempts = await this.prisma.quizAttempt.findMany({
      where,
      include: { session: true },
      orderBy: { createdAt: 'desc' },
      take: 10 // Last 10 attempts
    });

    return attempts.map(this.mapPrismaToQuizAttempt);
  }

  private async getCurrentConfidenceAssessment(userId: string): Promise<ConfidenceAssessment> {
    const assessment = await this.prisma.confidenceAssessment.findFirst({
      where: { userId },
      orderBy: { updatedAt: 'desc' }
    });

    if (!assessment) {
      // Create default assessment if none exists
      const defaultAssessment = await this.prisma.confidenceAssessment.create({
        data: {
          userId,
          currentStage: ConfidenceStage.GOAL_SETTING,
          overallConfidenceScore: 0,
          assessmentScores: {
            knowledgeQuiz: { score: 0, completedAt: new Date(), attempts: 0, weakAreas: [] },
            behavioralAssessment: { riskTolerance: 50, decisionConsistency: 50, emotionalStability: 50, lastAssessed: new Date() },
            performanceEvaluation: { paperTradingWinRate: 0, riskManagementScore: 0, strategyAdherence: 0, consistencyRating: 0 }
          },
          progressHistory: [],
          graduationCriteria: {
            nextStage: 'strategy_learning',
            requirements: {
              minimumConfidenceScore: 75,
              requiredAssessments: [],
              minimumTimeInStage: 7
            }
          }
        }
      });
      return this.mapPrismaToConfidenceAssessment(defaultAssessment);
    }

    return this.mapPrismaToConfidenceAssessment(assessment);
  }

  private async generateSessionQuestions(
    stage: ConfidenceStage,
    userExperience: string,
    settings: {
      questionCount: number;
      difficulty: QuizDifficulty;
      focusCategories: QuizCategory[];
    }
  ): Promise<QuizQuestion[]> {
    // Use content service to generate appropriate questions
    const baseQuestions = await this.contentService.generateQuizForStage(
      stage,
      userExperience as any
    );

    // Filter and adjust based on settings
    let filteredQuestions = baseQuestions.filter(q => 
      q.difficulty === settings.difficulty ||
      (settings.difficulty === QuizDifficulty.INTERMEDIATE && 
       [QuizDifficulty.BEGINNER, QuizDifficulty.INTERMEDIATE].includes(q.difficulty))
    );

    // Prioritize focus categories if specified
    if (settings.focusCategories.length > 0) {
      const focusQuestions = filteredQuestions.filter(q => 
        settings.focusCategories.includes(q.category)
      );
      const otherQuestions = filteredQuestions.filter(q => 
        !settings.focusCategories.includes(q.category)
      );

      // 70% from focus categories, 30% from others
      const focusCount = Math.ceil(settings.questionCount * 0.7);
      const otherCount = settings.questionCount - focusCount;

      filteredQuestions = [
        ...this.shuffleArray(focusQuestions).slice(0, focusCount),
        ...this.shuffleArray(otherQuestions).slice(0, otherCount)
      ];
    }

    // Ensure we have enough questions
    if (filteredQuestions.length < settings.questionCount) {
      // Fall back to any available questions
      const additionalNeeded = settings.questionCount - filteredQuestions.length;
      const additionalQuestions = baseQuestions
        .filter(q => !filteredQuestions.some(fq => fq.id === q.id))
        .slice(0, additionalNeeded);
      
      filteredQuestions.push(...additionalQuestions);
    }

    return this.shuffleArray(filteredQuestions).slice(0, settings.questionCount);
  }

  private validateAnswer(question: QuizQuestion, selectedAnswerIds: string[]): boolean {
    // Sort both arrays for consistent comparison
    const correctIds = [...question.correctAnswerIds].sort();
    const selectedIds = [...selectedAnswerIds].sort();

    // Check if arrays have the same length and all elements match
    return correctIds.length === selectedIds.length &&
           correctIds.every(id => selectedIds.includes(id));
  }

  private async calculateAttemptResults(session: any): Promise<{
    score: number;
    correctAnswers: number;
    confidenceScore: number;
    weakAreas: string[];
    strongAreas: string[];
    feedback: any;
  }> {
    const responses = session.responses;
    const questions = session.questionsData as QuizQuestion[];

    // Basic accuracy metrics
    const correctAnswers = responses.filter((r: any) => r.isCorrect).length;
    const totalQuestions = responses.length;
    const accuracyScore = totalQuestions > 0 ? (correctAnswers / totalQuestions) * 100 : 0;

    // Time efficiency calculation
    const totalTimeSpent = responses.reduce((sum: number, r: any) => sum + r.timeSpent, 0);
    const averageTimePerQuestion = totalTimeSpent / Math.max(1, totalQuestions);
    const expectedTimePerQuestion = session.timeLimit / session.totalQuestions;
    const timeEfficiencyScore = Math.min(100, (expectedTimePerQuestion / averageTimePerQuestion) * 100);

    // Consistency calculation (variation in response times and confidence)
    const responseTimes = responses.map((r: any) => r.timeSpent);
    const confidenceLevels = responses.map((r: any) => r.confidenceLevel || 3);
    
    const timeVariance = this.calculateVariance(responseTimes);
    const confidenceVariance = this.calculateVariance(confidenceLevels);
    const consistencyScore = Math.max(0, 100 - (timeVariance / 1000) - (confidenceVariance * 20));

    // Confidence score (self-reported confidence vs actual performance)
    const averageConfidence = confidenceLevels.reduce((sum: number, c: number) => sum + c, 0) / confidenceLevels.length;
    const confidenceAccuracy = Math.abs(averageConfidence * 20 - accuracyScore); // Convert 1-5 to 0-100
    const confidenceScore = Math.max(0, 100 - confidenceAccuracy);

    // Calculate weighted final score
    const finalScore = 
      accuracyScore * this.SCORING_WEIGHTS.accuracy +
      timeEfficiencyScore * this.SCORING_WEIGHTS.timeEfficiency +
      consistencyScore * this.SCORING_WEIGHTS.consistency +
      confidenceScore * this.SCORING_WEIGHTS.confidence;

    // Identify weak and strong areas
    const categoryPerformance = this.analyzeCategoryPerformance(responses, questions);
    const weakAreas = Object.entries(categoryPerformance)
      .filter(([_, performance]: [string, any]) => performance.accuracy < 70)
      .map(([category]) => category);
    const strongAreas = Object.entries(categoryPerformance)
      .filter(([_, performance]: [string, any]) => performance.accuracy >= 85)
      .map(([category]) => category);

    // Generate feedback
    const feedback = this.generateFeedback(
      finalScore,
      accuracyScore,
      timeEfficiencyScore,
      consistencyScore,
      weakAreas,
      strongAreas
    );

    return {
      score: Math.round(finalScore),
      correctAnswers,
      confidenceScore: Math.round(confidenceScore),
      weakAreas,
      strongAreas,
      feedback
    };
  }

  private calculateVariance(values: number[]): number {
    if (values.length === 0) return 0;
    
    const mean = values.reduce((sum, val) => sum + val, 0) / values.length;
    const variance = values.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / values.length;
    return variance;
  }

  private analyzeCategoryPerformance(responses: any[], questions: QuizQuestion[]): Record<string, any> {
    const categoryStats: Record<string, { correct: number; total: number; accuracy: number }> = {};

    responses.forEach(response => {
      const question = questions.find(q => q.id === response.questionId);
      if (question) {
        if (!categoryStats[question.category]) {
          categoryStats[question.category] = { correct: 0, total: 0, accuracy: 0 };
        }
        
        categoryStats[question.category].total++;
        if (response.isCorrect) {
          categoryStats[question.category].correct++;
        }
      }
    });

    // Calculate accuracy percentages
    Object.keys(categoryStats).forEach(category => {
      const stats = categoryStats[category];
      stats.accuracy = (stats.correct / stats.total) * 100;
    });

    return categoryStats;
  }

  private generateFeedback(
    finalScore: number,
    accuracyScore: number,
    timeEfficiencyScore: number,
    consistencyScore: number,
    weakAreas: string[],
    strongAreas: string[]
  ): any {
    const feedback = {
      overall: '',
      strengths: [] as string[],
      weaknesses: [] as string[],
      recommendations: [] as string[],
      nextSteps: [] as string[]
    };

    // Overall feedback
    if (finalScore >= 90) {
      feedback.overall = 'Excellent performance! You demonstrate strong knowledge and consistent execution.';
    } else if (finalScore >= 80) {
      feedback.overall = 'Good performance with room for improvement in specific areas.';
    } else if (finalScore >= 70) {
      feedback.overall = 'Adequate performance, but significant improvement needed before advancement.';
    } else {
      feedback.overall = 'Additional study and practice required before retaking this assessment.';
    }

    // Strengths analysis
    if (accuracyScore >= 85) {
      feedback.strengths.push('Strong foundational knowledge');
    }
    if (timeEfficiencyScore >= 80) {
      feedback.strengths.push('Efficient time management');
    }
    if (consistencyScore >= 80) {
      feedback.strengths.push('Consistent performance across questions');
    }
    if (strongAreas.length > 0) {
      feedback.strengths.push(`Proficient in ${strongAreas.join(', ').replace(/_/g, ' ')}`);
    }

    // Weaknesses analysis
    if (accuracyScore < 75) {
      feedback.weaknesses.push('Knowledge gaps in core concepts');
    }
    if (timeEfficiencyScore < 60) {
      feedback.weaknesses.push('Time management needs improvement');
    }
    if (consistencyScore < 60) {
      feedback.weaknesses.push('Inconsistent performance patterns');
    }
    if (weakAreas.length > 0) {
      feedback.weaknesses.push(`Needs improvement in ${weakAreas.join(', ').replace(/_/g, ' ')}`);
    }

    // Recommendations
    if (weakAreas.length > 0) {
      feedback.recommendations.push(`Focus additional study on ${weakAreas.join(', ').replace(/_/g, ' ')}`);
    }
    if (timeEfficiencyScore < 70) {
      feedback.recommendations.push('Practice quiz-taking under time pressure');
    }
    if (finalScore < 80) {
      feedback.recommendations.push('Review educational materials and retake quiz after additional study');
    }

    // Next steps
    if (finalScore >= 85) {
      feedback.nextSteps.push('Ready to progress to next stage');
    } else {
      feedback.nextSteps.push('Additional study required before progression');
      feedback.nextSteps.push('Retake quiz after addressing weak areas');
    }

    return feedback;
  }

  private async getNextAttemptNumber(userId: string, stage: ConfidenceStage): Promise<number> {
    const lastAttempt = await this.prisma.quizAttempt.findFirst({
      where: {
        userId,
        session: { stage }
      },
      orderBy: { attemptNumber: 'desc' }
    });

    return (lastAttempt?.attemptNumber || 0) + 1;
  }

  private async updateConfidenceAssessment(
    userId: string,
    stage: ConfidenceStage,
    attemptData: any
  ): Promise<void> {
    const assessment = await this.getCurrentConfidenceAssessment(userId);

    // Update knowledge quiz scores
    const updatedScores = {
      ...assessment.assessmentScores,
      knowledgeQuiz: {
        score: attemptData.score,
        completedAt: new Date(),
        attempts: (assessment.assessmentScores.knowledgeQuiz?.attempts || 0) + 1,
        weakAreas: attemptData.weakAreas
      }
    };

    // Calculate new overall confidence score
    const overallScore = this.calculateOverallConfidenceScore(updatedScores);

    await this.prisma.confidenceAssessment.update({
      where: { id: assessment.id },
      data: {
        overallConfidenceScore: overallScore,
        assessmentScores: updatedScores,
        updatedAt: new Date()
      }
    });
  }

  private calculateOverallConfidenceScore(assessmentScores: any): number {
    const weights = {
      knowledgeQuiz: 0.4,
      behavioralAssessment: 0.3,
      performanceEvaluation: 0.3
    };

    const knowledgeScore = assessmentScores.knowledgeQuiz?.score || 0;
    
    // Calculate behavioral score (average of components)
    const behavioralComponents = assessmentScores.behavioralAssessment;
    const behavioralScore = behavioralComponents 
      ? (behavioralComponents.riskTolerance + behavioralComponents.decisionConsistency + behavioralComponents.emotionalStability) / 3
      : 0;

    // Calculate performance score (average of components) 
    const performanceComponents = assessmentScores.performanceEvaluation;
    const performanceScore = performanceComponents
      ? (performanceComponents.riskManagementScore + performanceComponents.strategyAdherence + performanceComponents.consistencyRating) / 3
      : 0;

    return Math.round(
      knowledgeScore * weights.knowledgeQuiz +
      behavioralScore * weights.behavioralAssessment +
      performanceScore * weights.performanceEvaluation
    );
  }

  private async generateStagingRecommendation(
    userId: string,
    currentStage: ConfidenceStage
  ): Promise<any> {
    const attempts = await this.getUserAttemptHistory(userId, currentStage);
    const assessment = await this.getCurrentConfidenceAssessment(userId);
    const threshold = this.STAGE_THRESHOLDS[currentStage];

    if (!threshold) {
      return {
        readyForNextStage: false,
        requiredImprovements: ['Stage progression criteria not defined']
      };
    }

    const recentScores = attempts.slice(0, 3).map(a => a.score);
    const averageRecentScore = recentScores.length > 0 
      ? recentScores.reduce((sum, score) => sum + score, 0) / recentScores.length 
      : 0;

    const readyForNextStage = 
      averageRecentScore >= threshold.minScore &&
      attempts.length >= threshold.minAttempts &&
      assessment.overallConfidenceScore >= threshold.minScore;

    const requiredImprovements = [];
    if (averageRecentScore < threshold.minScore) {
      requiredImprovements.push(`Achieve minimum quiz score of ${threshold.minScore}% (current: ${Math.round(averageRecentScore)}%)`);
    }
    if (attempts.length < threshold.minAttempts) {
      requiredImprovements.push(`Complete minimum ${threshold.minAttempts} quiz attempts (current: ${attempts.length})`);
    }
    if (assessment.overallConfidenceScore < threshold.minScore) {
      requiredImprovements.push(`Improve overall confidence score to ${threshold.minScore}% (current: ${assessment.overallConfidenceScore}%)`);
    }

    return {
      readyForNextStage,
      requiredImprovements: requiredImprovements.length > 0 ? requiredImprovements : undefined,
      estimatedTimeToReady: readyForNextStage ? 0 : Math.ceil((threshold.minScore - averageRecentScore) / 10) // Rough estimate
    };
  }

  private async handleSessionTimeout(sessionId: string): Promise<void> {
    await this.prisma.quizSession.update({
      where: { id: sessionId },
      data: {
        status: QuizSessionStatus.TIMED_OUT,
        completedAt: new Date()
      }
    });
  }

  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }

  // Prisma mapping functions
  private mapPrismaToQuizSession(prismaSession: any): QuizSession {
    return {
      id: prismaSession.id,
      userId: prismaSession.userId,
      stage: prismaSession.stage,
      difficulty: prismaSession.difficulty,
      totalQuestions: prismaSession.totalQuestions,
      timeLimit: prismaSession.timeLimit,
      questionsData: prismaSession.questionsData,
      settings: prismaSession.settings,
      status: prismaSession.status,
      startedAt: prismaSession.startedAt,
      completedAt: prismaSession.completedAt,
      finalScore: prismaSession.finalScore,
      createdAt: prismaSession.createdAt,
      updatedAt: prismaSession.updatedAt
    };
  }

  private mapPrismaToQuizResponse(prismaResponse: any): QuizResponse {
    return {
      id: prismaResponse.id,
      sessionId: prismaResponse.sessionId,
      userId: prismaResponse.userId,
      questionId: prismaResponse.questionId,
      selectedAnswerIds: prismaResponse.selectedAnswerIds,
      isCorrect: prismaResponse.isCorrect,
      timeSpent: prismaResponse.timeSpent,
      confidenceLevel: prismaResponse.confidenceLevel,
      submittedAt: prismaResponse.submittedAt,
      createdAt: prismaResponse.createdAt,
      updatedAt: prismaResponse.updatedAt
    };
  }

  private mapPrismaToQuizAttempt(prismaAttempt: any): QuizAttempt {
    return {
      id: prismaAttempt.id,
      userId: prismaAttempt.userId,
      sessionId: prismaAttempt.sessionId,
      attemptNumber: prismaAttempt.attemptNumber,
      score: prismaAttempt.score,
      totalTimeSpent: prismaAttempt.totalTimeSpent,
      questionsAnswered: prismaAttempt.questionsAnswered,
      correctAnswers: prismaAttempt.correctAnswers,
      weakAreas: prismaAttempt.weakAreas,
      strongAreas: prismaAttempt.strongAreas,
      confidenceScore: prismaAttempt.confidenceScore,
      feedback: prismaAttempt.feedback,
      status: prismaAttempt.status,
      createdAt: prismaAttempt.createdAt
    };
  }

  private mapPrismaToConfidenceAssessment(prismaAssessment: any): ConfidenceAssessment {
    return {
      id: prismaAssessment.id,
      userId: prismaAssessment.userId,
      currentStage: prismaAssessment.currentStage,
      overallConfidenceScore: prismaAssessment.overallConfidenceScore,
      assessmentScores: prismaAssessment.assessmentScores,
      progressHistory: prismaAssessment.progressHistory,
      graduationCriteria: prismaAssessment.graduationCriteria,
      createdAt: prismaAssessment.createdAt,
      updatedAt: prismaAssessment.updatedAt
    };
  }
}