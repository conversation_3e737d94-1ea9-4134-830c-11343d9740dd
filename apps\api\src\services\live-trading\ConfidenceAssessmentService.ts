import { PrismaClient } from '@prisma/client';

export class ConfidenceAssessmentService {
  constructor(private prisma: PrismaClient) {}

  /**
   * Get current confidence assessment for a user
   */
  async getCurrentConfidenceAssessment(userId: string): Promise<any> {
    const assessment = await this.prisma.confidenceAssessment.findFirst({
      where: { userId },
      orderBy: { createdAt: 'desc' }
    });

    if (!assessment) {
      return {
        currentStage: 'NOT_STARTED',
        overallConfidenceScore: 0,
        assessmentScores: {}
      };
    }

    return assessment;
  }
}