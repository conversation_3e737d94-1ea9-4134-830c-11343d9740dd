import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import { PrismaClient } from '@prisma/client';
import request from 'supertest';
import express from 'express';
import { SystemMonitoringService } from '../../../services/monitoring/SystemMonitoringService';
import { UserActivityMonitoringService } from '../../../services/monitoring/UserActivityMonitoringService';
import { TradingPatternDetectionService } from '../../../services/monitoring/TradingPatternDetectionService';
import { CriticalAlertService } from '../../../services/monitoring/CriticalAlertService';

// Mock database for integration tests
const mockPrisma = {
  systemMetrics: {
    create: vi.fn(),
    findMany: vi.fn(),
  },
  userActivityLog: {
    create: vi.fn(),
    findMany: vi.fn(),
    updateMany: vi.fn(),
  },
  trade: {
    findMany: vi.fn(),
  },
  alert: {
    create: vi.fn(),
    findMany: vi.fn(),
    updateMany: vi.fn(),
    count: vi.fn(),
  },
  user: {
    findMany: vi.fn(),
    findUnique: vi.fn(),
  },
  $queryRaw: vi.fn(),
} as unknown as PrismaClient;

describe('Monitoring System Integration', () => {
  let systemMonitoringService: SystemMonitoringService;
  let userActivityService: UserActivityMonitoringService;
  let patternDetectionService: TradingPatternDetectionService;
  let alertService: CriticalAlertService;

  beforeAll(() => {
    // Initialize all monitoring services
    systemMonitoringService = new SystemMonitoringService(mockPrisma);
    userActivityService = new UserActivityMonitoringService(mockPrisma);
    patternDetectionService = new TradingPatternDetectionService(mockPrisma);
    alertService = new CriticalAlertService();
  });

  afterAll(async () => {
    // Clean up all services
    systemMonitoringService?.stopMonitoring();
    userActivityService?.stopMonitoring();
    patternDetectionService?.stopMonitoring();
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('System Health Monitoring Flow', () => {
    it('should detect system issues and create alerts', async () => {
      // Mock database queries to simulate system health issues
      vi.mocked(mockPrisma.$queryRaw).mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve([]), 3000)) // Slow response
      );

      const alertCreatedSpy = vi.fn();
      alertService.on('alertCreated', alertCreatedSpy);

      // Start system monitoring
      await systemMonitoringService.startMonitoring({ intervalMs: 500 });

      // Wait for metrics collection and alert generation
      await new Promise(resolve => setTimeout(resolve, 4000));

      // Should have generated performance alert
      expect(alertCreatedSpy).toHaveBeenCalled();
      
      const alertCall = alertCreatedSpy.mock.calls.find(call =>
        call[0].type?.includes('response_time') || call[0].message?.includes('slow')
      );
      expect(alertCall).toBeDefined();
    });

    it('should correlate system metrics with user activity', async () => {
      // Mock system under load
      vi.mocked(mockPrisma.$queryRaw).mockResolvedValue([]);
      
      const mockHighActivityUser = {
        id: 'user-high-activity',
        sessionId: 'session-123',
        activityType: 'trade_execution',
        timestamp: new Date(),
        anomalyScore: 85,
        flaggedForReview: true,
      };

      vi.mocked(mockPrisma.userActivityLog.create).mockResolvedValue(mockHighActivityUser as any);

      // Start both services
      await systemMonitoringService.startMonitoring({ intervalMs: 200 });
      await userActivityService.startMonitoring();

      // Track suspicious activity during system stress
      await userActivityService.trackActivity({
        userId: 'user-high-activity',
        sessionId: 'session-123',
        activityType: 'trade_execution',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0',
        metadata: { tradeVolume: 1000000, riskScore: 90 },
      });

      // Wait for processing
      await new Promise(resolve => setTimeout(resolve, 300));

      // Both services should be active and processing
      expect(systemMonitoringService.getMonitoringStatus().isActive).toBe(true);
      expect(userActivityService.getMonitoringStatus().isActive).toBe(true);
      expect(userActivityService.getMonitoringStatus().activitiesTracked).toBe(1);
    });
  });

  describe('Trading Pattern Detection Flow', () => {
    it('should detect suspicious trading patterns and create alerts', async () => {
      // Mock suspicious trading data
      const suspiciousTrades = Array.from({ length: 50 }, (_, i) => ({
        id: `trade-${i}`,
        userId: 'suspicious-user',
        symbol: 'EURUSD',
        side: i % 2 === 0 ? 'buy' : 'sell',
        volume: 100000,
        openTime: new Date(Date.now() - i * 60000), // High frequency
        closeTime: new Date(Date.now() - i * 60000 + 30000),
        profit: 5000, // Consistently high profits
        status: 'closed',
      }));

      vi.mocked(mockPrisma.trade.findMany).mockResolvedValue(suspiciousTrades as any);
      vi.mocked(mockPrisma.user.findMany).mockResolvedValue([
        { id: 'suspicious-user', email: '<EMAIL>' }
      ] as any);

      const mockAlert = {
        id: 'alert-pattern-123',
        type: 'suspicious_trading_pattern',
        severity: 'high',
        message: 'High frequency trading pattern detected',
      };
      vi.mocked(mockPrisma.alert.create).mockResolvedValue(mockAlert as any);

      const patternDetectedSpy = vi.fn();
      patternDetectionService.on('suspiciousPattern', patternDetectedSpy);

      // Start pattern detection
      await patternDetectionService.startMonitoring({ intervalMs: 200 });

      // Wait for pattern analysis
      await new Promise(resolve => setTimeout(resolve, 300));

      expect(patternDetectedSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: expect.any(String),
          severity: expect.any(String),
          userId: 'suspicious-user',
        })
      );
    });

    it('should analyze user patterns on demand', async () => {
      const userTrades = [
        {
          id: 'trade-1',
          userId: 'user-123',
          symbol: 'EURUSD',
          side: 'buy',
          volume: 10000,
          openTime: new Date('2023-01-01T10:00:00Z'),
          closeTime: new Date('2023-01-01T10:30:00Z'),
          profit: 500,
          status: 'closed',
        },
      ];

      vi.mocked(mockPrisma.trade.findMany).mockResolvedValue(userTrades as any);

      const patterns = await patternDetectionService.analyzeUserPatterns('user-123', '24h');

      expect(patterns).toMatchObject({
        userId: 'user-123',
        timeRange: '24h',
        totalTrades: 1,
        suspiciousPatterns: expect.any(Array),
        riskScore: expect.any(Number),
      });

      expect(mockPrisma.trade.findMany).toHaveBeenCalledWith({
        where: {
          userId: 'user-123',
          openTime: { gte: expect.any(Date) },
        },
        orderBy: { openTime: 'desc' },
      });
    });
  });

  describe('Alert Management Flow', () => {
    it('should create, update, and resolve alerts', async () => {
      const alertData = {
        type: 'integration_test',
        severity: 'high',
        category: 'system_performance',
        source: 'integration_test',
        title: 'Integration Test Alert',
        message: 'This is a test alert for integration testing',
        metadata: { testId: 'integration-001' },
      };

      const mockCreatedAlert = {
        id: 'alert-integration-123',
        ...alertData,
        status: 'active',
        acknowledged: false,
        createdAt: new Date(),
      };

      vi.mocked(mockPrisma.alert.create).mockResolvedValue(mockCreatedAlert as any);
      vi.mocked(mockPrisma.alert.updateMany).mockResolvedValue({ count: 1 } as any);

      // Create alert
      const createdAlert = await alertService.createAlert(alertData);
      expect(createdAlert.id).toBe('alert-integration-123');

      // Acknowledge alert
      const updateResult = await alertService.updateAlerts(
        [createdAlert.id],
        'acknowledge',
        {
          actionBy: 'integration-test',
          notes: 'Acknowledged during integration test',
        }
      );

      expect(updateResult.success).toBe(true);
      expect(updateResult.processedAlerts).toBe(1);

      // Resolve alert
      await alertService.updateAlerts(
        [createdAlert.id],
        'resolve',
        {
          actionBy: 'integration-test',
          notes: 'Resolved during integration test',
        }
      );

      expect(mockPrisma.alert.updateMany).toHaveBeenCalledWith({
        where: { id: { in: [createdAlert.id] } },
        data: expect.objectContaining({
          status: 'resolved',
          resolved: true,
          resolvedBy: 'integration-test',
        }),
      });
    });

    it('should generate alert statistics', async () => {
      // Mock alert counts for statistics
      vi.mocked(mockPrisma.alert.count)
        .mockResolvedValueOnce(1247) // total
        .mockResolvedValueOnce(25)   // active
        .mockResolvedValueOnce(150)  // acknowledged
        .mockResolvedValueOnce(1000) // resolved
        .mockResolvedValueOnce(8)    // critical
        .mockResolvedValueOnce(15);  // high

      vi.mocked(mockPrisma.alert.findMany).mockResolvedValue([
        { createdAt: new Date(Date.now() - 30 * 60 * 1000), severity: 'high' },
        { createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), severity: 'critical' },
      ] as any);

      const stats = await alertService.getAlertStatistics('24h');

      expect(stats).toMatchObject({
        summary: {
          total: expect.any(Number),
          active: expect.any(Number),
          acknowledged: expect.any(Number),
          resolved: expect.any(Number),
        },
        timeRange: expect.objectContaining({
          period: '24h',
        }),
        bySeverity: expect.any(Object),
        byCategory: expect.any(Object),
        trends: expect.any(Array),
        performance: expect.objectContaining({
          avgResponseTime: expect.any(Number),
          avgResolutionTime: expect.any(Number),
        }),
      });
    });
  });

  describe('Cross-Service Communication', () => {
    it('should handle cascading alerts from multiple services', async () => {
      const alertsCreated: any[] = [];
      
      // Track all alerts created during the test
      alertService.on('alertCreated', (alert) => {
        alertsCreated.push(alert);
      });

      // Start all monitoring services
      await systemMonitoringService.startMonitoring({ intervalMs: 200 });
      await userActivityService.startMonitoring();
      await patternDetectionService.startMonitoring({ intervalMs: 200 });

      // Simulate system degradation
      vi.mocked(mockPrisma.$queryRaw).mockImplementation(() =>
        new Promise(resolve => setTimeout(() => resolve([]), 2000))
      );

      // Simulate suspicious user activity
      vi.mocked(mockPrisma.userActivityLog.create).mockResolvedValue({
        id: 'activity-123',
        anomalyScore: 90,
        flaggedForReview: true,
        timestamp: new Date(),
      } as any);

      // Simulate suspicious trading patterns
      vi.mocked(mockPrisma.trade.findMany).mockResolvedValue(
        Array.from({ length: 30 }, (_, i) => ({
          id: `trade-${i}`,
          userId: 'user-123',
          openTime: new Date(Date.now() - i * 60000),
          volume: 50000,
          profit: 2000,
        })) as any
      );

      vi.mocked(mockPrisma.user.findMany).mockResolvedValue([
        { id: 'user-123', email: '<EMAIL>' }
      ] as any);

      // Track suspicious activity
      await userActivityService.trackActivity({
        userId: 'user-123',
        sessionId: 'session-123',
        activityType: 'risk_breach',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0',
        metadata: { breachType: 'position_limit' },
      });

      // Wait for all services to process
      await new Promise(resolve => setTimeout(resolve, 2500));

      // Multiple services should be active
      expect(systemMonitoringService.getMonitoringStatus().isActive).toBe(true);
      expect(userActivityService.getMonitoringStatus().isActive).toBe(true);
      expect(patternDetectionService.getMonitoringStatus().isActive).toBe(true);

      // Should have tracked the suspicious activity
      expect(userActivityService.getMonitoringStatus().activitiesTracked).toBe(1);
    });

    it('should handle service failures gracefully', async () => {
      const errorsSpy = vi.fn();
      
      // Monitor errors from all services
      systemMonitoringService.on('error', errorsSpy);
      userActivityService.on('error', errorsSpy);
      patternDetectionService.on('error', errorsSpy);

      // Simulate database failures
      vi.mocked(mockPrisma.$queryRaw).mockRejectedValue(new Error('Database connection lost'));
      vi.mocked(mockPrisma.userActivityLog.create).mockRejectedValue(new Error('Write failure'));
      vi.mocked(mockPrisma.trade.findMany).mockRejectedValue(new Error('Read failure'));

      // Start services
      await systemMonitoringService.startMonitoring({ intervalMs: 100 });
      await userActivityService.startMonitoring();
      await patternDetectionService.startMonitoring({ intervalMs: 100 });

      // Try to track activity (should fail gracefully)
      try {
        await userActivityService.trackActivity({
          userId: 'user-123',
          sessionId: 'session-123',
          activityType: 'login',
          ipAddress: '*************',
          userAgent: 'Mozilla/5.0',
          metadata: {},
        });
      } catch (error) {
        // Expected to fail
      }

      // Wait for error propagation
      await new Promise(resolve => setTimeout(resolve, 200));

      // Services should emit errors but continue running
      expect(errorsSpy).toHaveBeenCalled();
      expect(systemMonitoringService.getMonitoringStatus().isActive).toBe(true);
      expect(userActivityService.getMonitoringStatus().isActive).toBe(true);
      expect(patternDetectionService.getMonitoringStatus().isActive).toBe(true);
    });
  });

  describe('Performance under load', () => {
    it('should handle concurrent operations efficiently', async () => {
      vi.mocked(mockPrisma.$queryRaw).mockResolvedValue([]);
      vi.mocked(mockPrisma.userActivityLog.create).mockImplementation(() =>
        Promise.resolve({ id: crypto.randomUUID(), timestamp: new Date() } as any)
      );

      await systemMonitoringService.startMonitoring({ intervalMs: 50 });
      await userActivityService.startMonitoring();

      const startTime = Date.now();

      // Simulate concurrent user activities
      const activities = Array.from({ length: 100 }, (_, i) => ({
        userId: `user-${i % 10}`,
        sessionId: `session-${i}`,
        activityType: 'trade_execution',
        ipAddress: '*************',
        userAgent: 'Mozilla/5.0',
        metadata: { tradeId: `trade-${i}` },
      }));

      // Track all activities concurrently
      await Promise.all(
        activities.map(activity => userActivityService.trackActivity(activity))
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (less than 2 seconds for 100 activities)
      expect(duration).toBeLessThan(2000);
      expect(userActivityService.getMonitoringStatus().activitiesTracked).toBe(100);
    });

    it('should maintain system stability during stress', async () => {
      // Mock high system load
      const originalCpuUsage = process.cpuUsage;
      const originalMemoryUsage = process.memoryUsage;

      process.cpuUsage = vi.fn().mockReturnValue({ user: 80000000, system: 20000000 });
      process.memoryUsage = vi.fn().mockReturnValue({
        heapUsed: 800000000,
        heapTotal: 1000000000,
        external: 0,
        rss: 1000000000,
        arrayBuffers: 0,
      });

      vi.mocked(mockPrisma.$queryRaw).mockResolvedValue([]);

      const metricsCollectedSpy = vi.fn();
      systemMonitoringService.on('metricsCollected', metricsCollectedSpy);

      await systemMonitoringService.startMonitoring({ intervalMs: 100 });

      // Wait for multiple metrics collections under load
      await new Promise(resolve => setTimeout(resolve, 500));

      // Restore original functions
      process.cpuUsage = originalCpuUsage;
      process.memoryUsage = originalMemoryUsage;

      // Should have collected metrics despite high load
      expect(metricsCollectedSpy).toHaveBeenCalled();
      
      const metrics = metricsCollectedSpy.mock.calls[0][0];
      expect(metrics.metrics.cpuUsage).toBeGreaterThan(70);
      expect(metrics.metrics.memoryUsage).toBeGreaterThan(70);
    });
  });
});