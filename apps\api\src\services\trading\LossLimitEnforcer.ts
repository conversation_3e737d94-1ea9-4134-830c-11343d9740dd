/**
 * Loss Limit Enforcer Service
 * 
 * Implements daily and weekly loss limit tracking and enforcement with hard stop mechanisms
 * and automatic account lockout capabilities. Provides audit logging for compliance tracking.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';
import { EventEmitter } from 'events';

// Loss Limit Types
export interface LossLimitConfig {
  dailyLimitPercentage: number; // Percentage of account balance
  weeklyLimitPercentage: number; // Percentage of account balance
  accountBalance: Decimal.Instance;
  enableHardStops: boolean;
  enableAuditLogging: boolean;
}

export interface LossTrackingData {
  userId: string;
  currentDayLoss: Decimal.Instance;
  currentWeekLoss: Decimal.Instance;
  dailyLimitAmount: Decimal.Instance;
  weeklyLimitAmount: Decimal.Instance;
  dailyLimitRemaining: Decimal.Instance;
  weeklyLimitRemaining: Decimal.Instance;
  lastResetDate: Date;
  lastWeekResetDate: Date;
  isLocked: boolean;
  lockoutReason?: 'daily_limit' | 'weekly_limit';
  lockoutUntil?: Date;
}

export interface LossLimitViolation {
  userId: string;
  violationType: 'daily_limit' | 'weekly_limit' | 'approaching_daily' | 'approaching_weekly';
  currentLoss: Decimal.Instance;
  limitAmount: Decimal.Instance;
  percentageUsed: number;
  timestamp: Date;
  triggerLockout: boolean;
}

export interface TradeImpact {
  tradeId: string;
  potentialLoss: Decimal.Instance;
  wouldViolateDaily: boolean;
  wouldViolateWeekly: boolean;
  allowTrade: boolean;
  warningMessage?: string;
}

export interface AuditLogEntry {
  userId: string;
  action: 'loss_recorded' | 'limit_violation' | 'account_locked' | 'limit_reset' | 'trade_blocked';
  details: Record<string, any>;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * LossLimitEnforcer - Core service for loss limit tracking and enforcement
 */
export class LossLimitEnforcer extends EventEmitter {
  private readonly lossTrackingData: Map<string, LossTrackingData> = new Map();
  private readonly auditLog: AuditLogEntry[] = [];
  
  // Warning thresholds (percentage of limit used)
  private readonly WARNING_THRESHOLDS = {
    CAUTION: 75,    // 75% of limit used
    WARNING: 90,    // 90% of limit used
    CRITICAL: 95    // 95% of limit used
  };

  // Default configuration
  private readonly DEFAULT_CONFIG: Omit<LossLimitConfig, 'accountBalance'> = {
    dailyLimitPercentage: 2.0,     // 2% daily loss limit
    weeklyLimitPercentage: 5.0,    // 5% weekly loss limit
    enableHardStops: true,
    enableAuditLogging: true
  };

  constructor() {
    super();
    this.setupEventHandlers();
  }

  /**
   * Initialize loss tracking for a user
   */
  public initializeUser(userId: string, config: LossLimitConfig): void {
    // Check if user already exists - don't reinitialize
    if (this.lossTrackingData.has(userId)) {
      return;
    }

    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = this.getStartOfWeek(now);

    const dailyLimitAmount = config.accountBalance.mul(config.dailyLimitPercentage).div(100);
    const weeklyLimitAmount = config.accountBalance.mul(config.weeklyLimitPercentage).div(100);

    const trackingData: LossTrackingData = {
      userId,
      currentDayLoss: new Decimal(0),
      currentWeekLoss: new Decimal(0),
      dailyLimitAmount,
      weeklyLimitAmount,
      dailyLimitRemaining: dailyLimitAmount,
      weeklyLimitRemaining: weeklyLimitAmount,
      lastResetDate: startOfDay,
      lastWeekResetDate: startOfWeek,
      isLocked: false
    };

    this.lossTrackingData.set(userId, trackingData);

    if (config.enableAuditLogging) {
      this.logAudit({
        userId,
        action: 'limit_reset',
        details: {
          dailyLimit: dailyLimitAmount.toString(),
          weeklyLimit: weeklyLimitAmount.toString(),
          config
        },
        timestamp: now
      });
    }
  }

  /**
   * Record a trading loss and check limits
   */
  public recordLoss(userId: string, lossAmount: Decimal.Instance): LossLimitViolation | null {
    const trackingData = this.lossTrackingData.get(userId);
    if (!trackingData) {
      throw new Error(`User ${userId} not initialized for loss tracking`);
    }

    // Check for time-based resets
    this.checkAndResetLimits(userId);

    // Update loss amounts
    const updatedData = { ...trackingData };
    updatedData.currentDayLoss = updatedData.currentDayLoss.add(lossAmount);
    updatedData.currentWeekLoss = updatedData.currentWeekLoss.add(lossAmount);
    updatedData.dailyLimitRemaining = updatedData.dailyLimitAmount.sub(updatedData.currentDayLoss);
    updatedData.weeklyLimitRemaining = updatedData.weeklyLimitAmount.sub(updatedData.currentWeekLoss);

    this.lossTrackingData.set(userId, updatedData);

    // Check for violations
    const violation = this.checkLimitViolations(userId, updatedData);

    // Log the loss
    this.logAudit({
      userId,
      action: 'loss_recorded',
      details: {
        lossAmount: lossAmount.toString(),
        dailyTotal: updatedData.currentDayLoss.toString(),
        weeklyTotal: updatedData.currentWeekLoss.toString(),
        violation: violation ? violation.violationType : 'none'
      },
      timestamp: new Date()
    });

    // Emit events for violations
    if (violation) {
      this.emit('limitViolation', violation);
      
      if (violation.triggerLockout) {
        this.handleLockout(userId, violation.violationType);
      }
    }

    return violation;
  }

  /**
   * Check if a potential trade would violate limits
   */
  public checkTradeImpact(userId: string, potentialLoss: Decimal, tradeId: string): TradeImpact {
    const trackingData = this.lossTrackingData.get(userId);
    if (!trackingData) {
      throw new Error(`User ${userId} not initialized for loss tracking`);
    }

    // Check for time-based resets
    this.checkAndResetLimits(userId);
    const currentData = this.lossTrackingData.get(userId)!;

    const potentialDailyLoss = currentData.currentDayLoss.add(potentialLoss);
    const potentialWeeklyLoss = currentData.currentWeekLoss.add(potentialLoss);

    const wouldViolateDaily = potentialDailyLoss.gt(currentData.dailyLimitAmount);
    const wouldViolateWeekly = potentialWeeklyLoss.gt(currentData.weeklyLimitAmount);

    const allowTrade = !wouldViolateDaily && !wouldViolateWeekly && !currentData.isLocked;

    let warningMessage: string | undefined;
    if (wouldViolateDaily) {
      warningMessage = `Trade would exceed daily loss limit. Limit: $${currentData.dailyLimitAmount.toFixed(2)}, Potential total: $${potentialDailyLoss.toFixed(2)}`;
    } else if (wouldViolateWeekly) {
      warningMessage = `Trade would exceed weekly loss limit. Limit: $${currentData.weeklyLimitAmount.toFixed(2)}, Potential total: $${potentialWeeklyLoss.toFixed(2)}`;
    } else if (currentData.isLocked) {
      warningMessage = `Account is locked due to ${currentData.lockoutReason} violation until ${currentData.lockoutUntil?.toISOString()}`;
    }

    const impact: TradeImpact = {
      tradeId,
      potentialLoss,
      wouldViolateDaily,
      wouldViolateWeekly,
      allowTrade,
      warningMessage
    };

    // Log blocked trades
    if (!allowTrade) {
      this.logAudit({
        userId,
        action: 'trade_blocked',
        details: {
          tradeId,
          potentialLoss: potentialLoss.toString(),
          reason: warningMessage,
          impact
        },
        timestamp: new Date()
      });
    }

    return impact;
  }

  /**
   * Get current loss tracking data for a user
   */
  public getUserLossData(userId: string): LossTrackingData | null {
    this.checkAndResetLimits(userId);
    return this.lossTrackingData.get(userId) || null;
  }

  /**
   * Update account balance and recalculate limits
   */
  public updateAccountBalance(userId: string, newBalance: Decimal.Instance): void {
    const trackingData = this.lossTrackingData.get(userId);
    if (!trackingData) {
      throw new Error(`User ${userId} not initialized for loss tracking`);
    }

    // Recalculate limits based on new balance
    const updatedData = { ...trackingData };
    updatedData.dailyLimitAmount = newBalance.mul(2.0).div(100); // 2% default
    updatedData.weeklyLimitAmount = newBalance.mul(5.0).div(100); // 5% default
    updatedData.dailyLimitRemaining = updatedData.dailyLimitAmount.sub(updatedData.currentDayLoss);
    updatedData.weeklyLimitRemaining = updatedData.weeklyLimitAmount.sub(updatedData.currentWeekLoss);

    this.lossTrackingData.set(userId, updatedData);

    this.logAudit({
      userId,
      action: 'limit_reset',
      details: {
        newBalance: newBalance.toString(),
        newDailyLimit: updatedData.dailyLimitAmount.toString(),
        newWeeklyLimit: updatedData.weeklyLimitAmount.toString(),
        reason: 'balance_update'
      },
      timestamp: new Date()
    });
  }

  /**
   * Force reset limits (admin function)
   */
  public forceResetLimits(userId: string): void {
    const trackingData = this.lossTrackingData.get(userId);
    if (!trackingData) {
      throw new Error(`User ${userId} not initialized for loss tracking`);
    }

    const updatedData = { ...trackingData };
    updatedData.currentDayLoss = new Decimal(0);
    updatedData.currentWeekLoss = new Decimal(0);
    updatedData.dailyLimitRemaining = updatedData.dailyLimitAmount;
    updatedData.weeklyLimitRemaining = updatedData.weeklyLimitAmount;
    updatedData.lastResetDate = new Date();
    updatedData.lastWeekResetDate = this.getStartOfWeek(new Date());
    updatedData.isLocked = false;
    updatedData.lockoutReason = undefined;
    updatedData.lockoutUntil = undefined;

    this.lossTrackingData.set(userId, updatedData);

    this.logAudit({
      userId,
      action: 'limit_reset',
      details: {
        type: 'forced_reset',
        resetBy: 'system_admin'
      },
      timestamp: new Date()
    });

    this.emit('limitsReset', { userId, type: 'forced' });
  }

  /**
   * Get audit log for compliance
   */
  public getAuditLog(userId?: string, startDate?: Date, endDate?: Date): AuditLogEntry[] {
    let filteredLog = [...this.auditLog];

    if (userId) {
      filteredLog = filteredLog.filter(entry => entry.userId === userId);
    }

    if (startDate) {
      filteredLog = filteredLog.filter(entry => entry.timestamp >= startDate);
    }

    if (endDate) {
      filteredLog = filteredLog.filter(entry => entry.timestamp <= endDate);
    }

    return filteredLog.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * Check and reset limits based on time periods
   */
  private checkAndResetLimits(userId: string): void {
    const trackingData = this.lossTrackingData.get(userId);
    if (!trackingData) return;

    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = this.getStartOfWeek(now);

    let needsUpdate = false;
    const updatedData = { ...trackingData };

    // Check daily reset
    if (startOfDay > trackingData.lastResetDate) {
      updatedData.currentDayLoss = new Decimal(0);
      updatedData.dailyLimitRemaining = updatedData.dailyLimitAmount;
      updatedData.lastResetDate = startOfDay;
      needsUpdate = true;

      // Remove daily lockout
      if (updatedData.lockoutReason === 'daily_limit') {
        updatedData.isLocked = false;
        updatedData.lockoutReason = undefined;
        updatedData.lockoutUntil = undefined;
      }
    }

    // Check weekly reset
    if (startOfWeek > trackingData.lastWeekResetDate) {
      updatedData.currentWeekLoss = new Decimal(0);
      updatedData.weeklyLimitRemaining = updatedData.weeklyLimitAmount;
      updatedData.lastWeekResetDate = startOfWeek;
      needsUpdate = true;

      // Remove weekly lockout
      if (updatedData.lockoutReason === 'weekly_limit') {
        updatedData.isLocked = false;
        updatedData.lockoutReason = undefined;
        updatedData.lockoutUntil = undefined;
      }
    }

    if (needsUpdate) {
      this.lossTrackingData.set(userId, updatedData);
      this.emit('limitsReset', { userId, type: 'automatic' });
    }
  }

  /**
   * Check for limit violations and generate warnings
   */
  private checkLimitViolations(userId: string, data: LossTrackingData): LossLimitViolation | null {
    // Check daily limit
    const dailyPercentageUsed = data.currentDayLoss.div(data.dailyLimitAmount).mul(100).toNumber();
    const weeklyPercentageUsed = data.currentWeekLoss.div(data.weeklyLimitAmount).mul(100).toNumber();

    // Daily limit violations
    if (dailyPercentageUsed >= 100) {
      return {
        userId,
        violationType: 'daily_limit',
        currentLoss: data.currentDayLoss,
        limitAmount: data.dailyLimitAmount,
        percentageUsed: dailyPercentageUsed,
        timestamp: new Date(),
        triggerLockout: true
      };
    }

    // Weekly limit violations
    if (weeklyPercentageUsed >= 100) {
      return {
        userId,
        violationType: 'weekly_limit',
        currentLoss: data.currentWeekLoss,
        limitAmount: data.weeklyLimitAmount,
        percentageUsed: weeklyPercentageUsed,
        timestamp: new Date(),
        triggerLockout: true
      };
    }

    // Warning thresholds
    if (dailyPercentageUsed >= this.WARNING_THRESHOLDS.WARNING) {
      return {
        userId,
        violationType: 'approaching_daily',
        currentLoss: data.currentDayLoss,
        limitAmount: data.dailyLimitAmount,
        percentageUsed: dailyPercentageUsed,
        timestamp: new Date(),
        triggerLockout: false
      };
    }

    if (weeklyPercentageUsed >= this.WARNING_THRESHOLDS.WARNING) {
      return {
        userId,
        violationType: 'approaching_weekly',
        currentLoss: data.currentWeekLoss,
        limitAmount: data.weeklyLimitAmount,
        percentageUsed: weeklyPercentageUsed,
        timestamp: new Date(),
        triggerLockout: false
      };
    }

    return null;
  }

  /**
   * Handle account lockout
   */
  private handleLockout(userId: string, reason: 'daily_limit' | 'weekly_limit'): void {
    const trackingData = this.lossTrackingData.get(userId);
    if (!trackingData) return;

    const now = new Date();
    let lockoutUntil: Date;

    if (reason === 'daily_limit') {
      // Lock until next day
      lockoutUntil = new Date(now);
      lockoutUntil.setDate(lockoutUntil.getDate() + 1);
      lockoutUntil.setHours(0, 0, 0, 0);
    } else {
      // Lock until next week
      lockoutUntil = this.getStartOfWeek(now);
      lockoutUntil.setDate(lockoutUntil.getDate() + 7);
    }

    const updatedData = { ...trackingData };
    updatedData.isLocked = true;
    updatedData.lockoutReason = reason;
    updatedData.lockoutUntil = lockoutUntil;

    this.lossTrackingData.set(userId, updatedData);

    this.logAudit({
      userId,
      action: 'account_locked',
      details: {
        reason,
        lockoutUntil: lockoutUntil.toISOString(),
        currentLoss: reason === 'daily_limit' ? trackingData.currentDayLoss.toString() : trackingData.currentWeekLoss.toString()
      },
      timestamp: now
    });

    this.emit('accountLocked', { userId, reason, lockoutUntil });
  }

  /**
   * Get start of week (Monday)
   */
  private getStartOfWeek(date: Date): Date {
    const startOfWeek = new Date(date);
    const day = startOfWeek.getDay();
    const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1); // Monday = 1
    startOfWeek.setDate(diff);
    startOfWeek.setHours(0, 0, 0, 0);
    return startOfWeek;
  }

  /**
   * Log audit entry
   */
  private logAudit(entry: AuditLogEntry): void {
    this.auditLog.push({
      ...entry,
      timestamp: entry.timestamp || new Date()
    });

    // Emit audit event
    this.emit('auditLog', entry);
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    this.on('limitViolation', (violation: LossLimitViolation) => {
      console.warn(`Loss limit violation for user ${violation.userId}:`, {
        type: violation.violationType,
        percentageUsed: violation.percentageUsed,
        triggerLockout: violation.triggerLockout
      });
    });

    this.on('accountLocked', (lockout: { userId: string; reason: string; lockoutUntil: Date }) => {
      console.error(`Account locked for user ${lockout.userId}:`, {
        reason: lockout.reason,
        until: lockout.lockoutUntil
      });
    });
  }

  /**
   * Validate configuration
   */
  public static validateConfig(config: LossLimitConfig): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (config.dailyLimitPercentage <= 0 || config.dailyLimitPercentage > 50) {
      errors.push('Daily limit percentage must be between 0.1% and 50%');
    }

    if (config.weeklyLimitPercentage <= 0 || config.weeklyLimitPercentage > 100) {
      errors.push('Weekly limit percentage must be between 0.1% and 100%');
    }

    if (config.dailyLimitPercentage >= config.weeklyLimitPercentage) {
      errors.push('Daily limit must be less than weekly limit');
    }

    if (config.accountBalance.lte(0)) {
      errors.push('Account balance must be positive');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}

/**
 * Factory function to create LossLimitEnforcer instance
 */
export function createLossLimitEnforcer(): LossLimitEnforcer {
  return new LossLimitEnforcer();
}

/**
 * Default export for convenience
 */
export default LossLimitEnforcer;