import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { performance } from 'perf_hooks';

/**
 * Uptime Tracking Service for monitoring system availability and calculating SLA metrics
 * Tracks service uptime, downtime incidents, and provides availability statistics
 */
export class UptimeTrackingService extends EventEmitter {
  private prisma: PrismaClient;
  private uptimeRecords: Map<string, UptimeRecord> = new Map();
  private isTracking = false;
  private trackingIntervals: NodeJS.Timeout[] = [];
  private readonly TRACKING_INTERVAL = 60000; // 1 minute
  private readonly AVAILABILITY_THRESHOLD = 99.0; // 99% availability SLA

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  /**
   * Start uptime tracking for specified services
   */
  async startTracking(services: ServiceConfig[] = []): Promise<void> {
    if (this.isTracking) {
      console.warn('Uptime tracking already active');
      return;
    }

    console.log('📈 Starting uptime tracking...');
    this.isTracking = true;

    // Initialize default services if none provided
    if (services.length === 0) {
      services = this.getDefaultServices();
    }

    // Initialize uptime records for each service
    for (const service of services) {
      await this.initializeServiceTracking(service);
    }

    // Start periodic health checks
    const trackingInterval = setInterval(async () => {
      await this.performHealthChecks();
    }, this.TRACKING_INTERVAL);

    this.trackingIntervals.push(trackingInterval);

    // Start daily availability calculations
    const dailyCalculationInterval = setInterval(async () => {
      await this.calculateDailyAvailability();
    }, 24 * 60 * 60 * 1000); // 24 hours

    this.trackingIntervals.push(dailyCalculationInterval);

    console.log(`✅ Uptime tracking started for ${services.length} services`);
    this.emit('trackingStarted', { services });
  }

  /**
   * Stop uptime tracking
   */
  stopTracking(): void {
    if (!this.isTracking) {
      return;
    }

    console.log('⏹️ Stopping uptime tracking...');
    this.isTracking = false;

    // Clear all intervals
    this.trackingIntervals.forEach(interval => clearInterval(interval));
    this.trackingIntervals = [];

    console.log('✅ Uptime tracking stopped');
    this.emit('trackingStopped');
  }

  /**
   * Get default services to track
   */
  private getDefaultServices(): ServiceConfig[] {
    return [
      {
        name: 'API Server',
        type: 'api',
        endpoint: 'http://localhost:3001/api/health',
        timeout: 5000,
        expectedStatus: 200,
      },
      {
        name: 'Database',
        type: 'database',
        endpoint: 'database',
        timeout: 3000,
        expectedStatus: 0, // Special case for database
      },
      {
        name: 'WebSocket Server',
        type: 'websocket',
        endpoint: 'ws://localhost:8080',
        timeout: 3000,
        expectedStatus: 0, // Special case for WebSocket
      },
      {
        name: 'MT5 Bridge',
        type: 'mt5-bridge',
        endpoint: 'http://localhost:3002/health',
        timeout: 5000,
        expectedStatus: 200,
      },
    ];
  }

  /**
   * Initialize tracking for a specific service
   */
  private async initializeServiceTracking(service: ServiceConfig): Promise<void> {
    const record: UptimeRecord = {
      serviceName: service.name,
      serviceType: service.type,
      startTime: new Date(),
      lastCheck: new Date(),
      isUp: true,
      uptimePercentage: 100,
      totalChecks: 0,
      successfulChecks: 0,
      currentDowntime: null,
      totalDowntime: 0,
      incidents: [],
      lastIncident: null,
      slaCompliant: true,
      responseTimeMs: 0,
    };

    this.uptimeRecords.set(service.name, record);
    
    console.log(`📊 Initialized uptime tracking for ${service.name}`);
  }

  /**
   * Perform health checks for all tracked services
   */
  private async performHealthChecks(): Promise<void> {
    const services = this.getDefaultServices();
    const checkPromises = services.map(service => this.checkServiceHealth(service));
    
    try {
      const results = await Promise.allSettled(checkPromises);
      
      for (let i = 0; i < results.length; i++) {
        const result = results[i];
        const service = services[i];
        
        if (result.status === 'fulfilled') {
          await this.updateUptimeRecord(service, result.value);
        } else {
          console.error(`Health check failed for ${service.name}:`, result.reason);
          await this.updateUptimeRecord(service, {
            isHealthy: false,
            responseTime: -1,
            error: result.reason instanceof Error ? result.reason.message : 'Unknown error',
          });
        }
      }
    } catch (error) {
      console.error('Error during health checks:', error);
      this.emit('error', error);
    }
  }

  /**
   * Check health of a specific service
   */
  private async checkServiceHealth(service: ServiceConfig): Promise<HealthCheckResult> {
    const startTime = performance.now();
    
    try {
      let isHealthy = false;
      let responseTime = 0;

      switch (service.type) {
        case 'database':
          isHealthy = await this.checkDatabaseHealth();
          break;
        case 'websocket':
          isHealthy = await this.checkWebSocketHealth(service.endpoint);
          break;
        default:
          const result = await this.checkHttpHealth(service);
          isHealthy = result.isHealthy;
          responseTime = result.responseTime;
      }

      const endTime = performance.now();
      responseTime = responseTime || (endTime - startTime);

      return {
        isHealthy,
        responseTime,
      };
    } catch (error) {
      const endTime = performance.now();
      return {
        isHealthy: false,
        responseTime: endTime - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Check database health
   */
  private async checkDatabaseHealth(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.warn('Database health check failed:', error);
      return false;
    }
  }

  /**
   * Check WebSocket health (simplified check)
   */
  private async checkWebSocketHealth(endpoint: string): Promise<boolean> {
    // In a real implementation, you'd try to establish a WebSocket connection
    // For now, we'll assume it's healthy if the tracking is active
    return this.isTracking;
  }

  /**
   * Check HTTP service health
   */
  private async checkHttpHealth(service: ServiceConfig): Promise<{ isHealthy: boolean; responseTime: number }> {
    const startTime = performance.now();
    
    try {
      // Using fetch with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), service.timeout);

      const response = await fetch(service.endpoint, {
        signal: controller.signal,
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      clearTimeout(timeoutId);
      const endTime = performance.now();
      const responseTime = endTime - startTime;

      const isHealthy = response.status === service.expectedStatus;

      return { isHealthy, responseTime };
    } catch (error) {
      const endTime = performance.now();
      const responseTime = endTime - startTime;
      
      console.warn(`HTTP health check failed for ${service.name}:`, error);
      return { isHealthy: false, responseTime };
    }
  }

  /**
   * Update uptime record based on health check result
   */
  private async updateUptimeRecord(service: ServiceConfig, result: HealthCheckResult): Promise<void> {
    const record = this.uptimeRecords.get(service.name);
    if (!record) {
      console.warn(`No uptime record found for ${service.name}`);
      return;
    }

    const now = new Date();
    const wasUp = record.isUp;
    const isUp = result.isHealthy;

    // Update basic metrics
    record.lastCheck = now;
    record.totalChecks++;
    record.responseTimeMs = result.responseTime;

    if (isUp) {
      record.successfulChecks++;
    }

    // Handle state changes
    if (wasUp && !isUp) {
      // Service went down
      await this.handleServiceDown(record, now, result.error);
    } else if (!wasUp && isUp) {
      // Service came back up
      await this.handleServiceUp(record, now);
    }

    // Update current status
    record.isUp = isUp;

    // Calculate uptime percentage
    record.uptimePercentage = (record.successfulChecks / record.totalChecks) * 100;

    // Check SLA compliance
    record.slaCompliant = record.uptimePercentage >= this.AVAILABILITY_THRESHOLD;

    // Store record in database periodically
    if (record.totalChecks % 60 === 0) { // Every hour
      await this.storeUptimeRecord(record);
    }

    // Emit events
    this.emit('healthCheckCompleted', {
      service: service.name,
      isHealthy: isUp,
      responseTime: result.responseTime,
      uptimePercentage: record.uptimePercentage,
    });

    if (!record.slaCompliant) {
      this.emit('slaViolation', {
        service: service.name,
        currentUptime: record.uptimePercentage,
        threshold: this.AVAILABILITY_THRESHOLD,
      });
    }
  }

  /**
   * Handle service going down
   */
  private async handleServiceDown(record: UptimeRecord, timestamp: Date, error?: string): Promise<void> {
    console.warn(`🔴 Service DOWN: ${record.serviceName}`);

    // Start downtime tracking
    record.currentDowntime = {
      startTime: timestamp,
      endTime: null,
      duration: 0,
      reason: error || 'Health check failed',
    };

    // Create incident
    const incident: UptimeIncident = {
      id: `incident_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      serviceName: record.serviceName,
      startTime: timestamp,
      endTime: null,
      duration: 0,
      reason: error || 'Health check failed',
      resolved: false,
      impact: 'service_unavailable',
    };

    record.incidents.push(incident);
    record.lastIncident = incident;

    this.emit('serviceDown', {
      service: record.serviceName,
      timestamp,
      error,
      incident: incident.id,
    });

    // Store incident in database
    await this.storeIncident(incident);
  }

  /**
   * Handle service coming back up
   */
  private async handleServiceUp(record: UptimeRecord, timestamp: Date): Promise<void> {
    console.log(`🟢 Service UP: ${record.serviceName}`);

    // End downtime tracking
    if (record.currentDowntime) {
      const downtimeDuration = timestamp.getTime() - record.currentDowntime.startTime.getTime();
      record.currentDowntime.endTime = timestamp;
      record.currentDowntime.duration = downtimeDuration;
      record.totalDowntime += downtimeDuration;

      // Update last incident
      if (record.lastIncident && !record.lastIncident.resolved) {
        record.lastIncident.endTime = timestamp;
        record.lastIncident.duration = downtimeDuration;
        record.lastIncident.resolved = true;

        // Update incident in database
        await this.updateIncident(record.lastIncident);
      }

      record.currentDowntime = null;
    }

    this.emit('serviceUp', {
      service: record.serviceName,
      timestamp,
      downtimeDuration: record.lastIncident?.duration || 0,
    });
  }

  /**
   * Calculate daily availability for all services
   */
  private async calculateDailyAvailability(): Promise<void> {
    console.log('📊 Calculating daily availability...');

    const today = new Date();
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);

    for (const [serviceName, record] of this.uptimeRecords) {
      const dailyStats = this.calculatePeriodAvailability(record, yesterday, today);
      
      // Store daily availability record
      await this.storeDailyAvailability({
        serviceName,
        date: yesterday,
        availabilityPercentage: dailyStats.availabilityPercentage,
        totalDowntime: dailyStats.totalDowntime,
        incidentCount: dailyStats.incidentCount,
        averageResponseTime: dailyStats.averageResponseTime,
        slaCompliant: dailyStats.availabilityPercentage >= this.AVAILABILITY_THRESHOLD,
      });

      console.log(`📈 ${serviceName}: ${dailyStats.availabilityPercentage.toFixed(2)}% availability`);
    }
  }

  /**
   * Calculate availability for a specific time period
   */
  calculatePeriodAvailability(record: UptimeRecord, startTime: Date, endTime: Date): AvailabilityStats {
    const periodIncidents = record.incidents.filter(incident =>
      incident.startTime >= startTime && incident.startTime < endTime
    );

    const totalDowntime = periodIncidents.reduce((sum, incident) => sum + incident.duration, 0);
    const periodDuration = endTime.getTime() - startTime.getTime();
    const availabilityPercentage = ((periodDuration - totalDowntime) / periodDuration) * 100;

    return {
      availabilityPercentage: Math.max(0, Math.min(100, availabilityPercentage)),
      totalDowntime,
      incidentCount: periodIncidents.length,
      averageResponseTime: record.responseTimeMs,
      periodStart: startTime,
      periodEnd: endTime,
    };
  }

  /**
   * Get current uptime statistics for all services
   */
  getUptimeStatistics(): ServiceUptimeStats[] {
    return Array.from(this.uptimeRecords.values()).map(record => ({
      serviceName: record.serviceName,
      serviceType: record.serviceType,
      isUp: record.isUp,
      uptimePercentage: record.uptimePercentage,
      lastCheck: record.lastCheck,
      totalChecks: record.totalChecks,
      successfulChecks: record.successfulChecks,
      currentDowntime: record.currentDowntime,
      totalDowntime: record.totalDowntime,
      incidentCount: record.incidents.length,
      lastIncident: record.lastIncident,
      slaCompliant: record.slaCompliant,
      averageResponseTime: record.responseTimeMs,
    }));
  }

  /**
   * Get uptime statistics for a specific service
   */
  getServiceUptime(serviceName: string): ServiceUptimeStats | null {
    const record = this.uptimeRecords.get(serviceName);
    if (!record) {
      return null;
    }

    return {
      serviceName: record.serviceName,
      serviceType: record.serviceType,
      isUp: record.isUp,
      uptimePercentage: record.uptimePercentage,
      lastCheck: record.lastCheck,
      totalChecks: record.totalChecks,
      successfulChecks: record.successfulChecks,
      currentDowntime: record.currentDowntime,
      totalDowntime: record.totalDowntime,
      incidentCount: record.incidents.length,
      lastIncident: record.lastIncident,
      slaCompliant: record.slaCompliant,
      averageResponseTime: record.responseTimeMs,
    };
  }

  /**
   * Store uptime record in database
   */
  private async storeUptimeRecord(record: UptimeRecord): Promise<void> {
    try {
      // This would store in a dedicated uptime_records table
      console.log(`💾 Storing uptime record for ${record.serviceName}:`, {
        uptimePercentage: `${record.uptimePercentage.toFixed(2)  }%`,
        totalChecks: record.totalChecks,
        slaCompliant: record.slaCompliant,
      });
    } catch (error) {
      console.error('Failed to store uptime record:', error);
    }
  }

  /**
   * Store incident in database
   */
  private async storeIncident(incident: UptimeIncident): Promise<void> {
    try {
      // This would store in a dedicated incidents table
      console.log(`💾 Storing incident ${incident.id}:`, {
        service: incident.serviceName,
        startTime: incident.startTime,
        reason: incident.reason,
      });
    } catch (error) {
      console.error('Failed to store incident:', error);
    }
  }

  /**
   * Update incident in database
   */
  private async updateIncident(incident: UptimeIncident): Promise<void> {
    try {
      console.log(`💾 Updating incident ${incident.id}:`, {
        resolved: incident.resolved,
        duration: `${(incident.duration / 1000).toFixed(0)}s`,
      });
    } catch (error) {
      console.error('Failed to update incident:', error);
    }
  }

  /**
   * Store daily availability record
   */
  private async storeDailyAvailability(record: DailyAvailabilityRecord): Promise<void> {
    try {
      console.log(`💾 Storing daily availability for ${record.serviceName}:`, {
        date: record.date.toISOString().split('T')[0],
        availability: `${record.availabilityPercentage.toFixed(2)  }%`,
        incidents: record.incidentCount,
        slaCompliant: record.slaCompliant,
      });
    } catch (error) {
      console.error('Failed to store daily availability:', error);
    }
  }

  /**
   * Get tracking status
   */
  getTrackingStatus(): UptimeTrackingStatus {
    return {
      isActive: this.isTracking,
      servicesCount: this.uptimeRecords.size,
      intervalCount: this.trackingIntervals.length,
      lastCheck: this.uptimeRecords.size > 0 
        ? Math.max(...Array.from(this.uptimeRecords.values()).map(r => r.lastCheck.getTime()))
        : null,
    };
  }
}

// Type definitions
interface ServiceConfig {
  name: string;
  type: 'api' | 'database' | 'websocket' | 'mt5-bridge';
  endpoint: string;
  timeout: number;
  expectedStatus: number;
}

interface UptimeRecord {
  serviceName: string;
  serviceType: string;
  startTime: Date;
  lastCheck: Date;
  isUp: boolean;
  uptimePercentage: number;
  totalChecks: number;
  successfulChecks: number;
  currentDowntime: DowntimeRecord | null;
  totalDowntime: number; // milliseconds
  incidents: UptimeIncident[];
  lastIncident: UptimeIncident | null;
  slaCompliant: boolean;
  responseTimeMs: number;
}

interface DowntimeRecord {
  startTime: Date;
  endTime: Date | null;
  duration: number; // milliseconds
  reason: string;
}

interface UptimeIncident {
  id: string;
  serviceName: string;
  startTime: Date;
  endTime: Date | null;
  duration: number; // milliseconds
  reason: string;
  resolved: boolean;
  impact: string;
}

interface HealthCheckResult {
  isHealthy: boolean;
  responseTime: number;
  error?: string;
}

interface AvailabilityStats {
  availabilityPercentage: number;
  totalDowntime: number;
  incidentCount: number;
  averageResponseTime: number;
  periodStart: Date;
  periodEnd: Date;
}

interface ServiceUptimeStats {
  serviceName: string;
  serviceType: string;
  isUp: boolean;
  uptimePercentage: number;
  lastCheck: Date;
  totalChecks: number;
  successfulChecks: number;
  currentDowntime: DowntimeRecord | null;
  totalDowntime: number;
  incidentCount: number;
  lastIncident: UptimeIncident | null;
  slaCompliant: boolean;
  averageResponseTime: number;
}

interface DailyAvailabilityRecord {
  serviceName: string;
  date: Date;
  availabilityPercentage: number;
  totalDowntime: number;
  incidentCount: number;
  averageResponseTime: number;
  slaCompliant: boolean;
}

interface UptimeTrackingStatus {
  isActive: boolean;
  servicesCount: number;
  intervalCount: number;
  lastCheck: number | null;
}

export type {
  ServiceConfig,
  UptimeRecord,
  DowntimeRecord,
  UptimeIncident,
  HealthCheckResult,
  AvailabilityStats,
  ServiceUptimeStats,
  DailyAvailabilityRecord,
  UptimeTrackingStatus,
};