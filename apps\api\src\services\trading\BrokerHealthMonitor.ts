/**
 * Broker Health Monitor Service
 * 
 * Monitors broker health and status with configurable check intervals
 * Part of Task 1: Multi-Broker Configuration System
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import type { 
  HealthCheckResult, 
  BrokerHealthCheck, 
  HealthCheckTestType
} from '@golddaddy/types';

export class BrokerHealthMonitor extends EventEmitter {
  private healthCheckIntervals: Map<string, NodeJS.Timeout> = new Map();
  private isShuttingDown = false;
  private healthCheckStats = {
    totalChecks: 0,
    successfulChecks: 0,
    failedChecks: 0,
    averageLatency: 0
  };

  constructor(private prisma: PrismaClient) {
    super();
  }

  /**
   * Start monitoring all active broker configurations for a user
   */
  async startMonitoring(userId: string): Promise<void> {
    try {
      console.log(`🔍 Starting broker health monitoring for user: ${userId}`);

      const brokerConfigs = await this.prisma.brokerConfiguration.findMany({
        where: {
          userId,
          deletedAt: null,
          status: {
            in: ['ACTIVE', 'INACTIVE', 'CONNECTING']
          }
        }
      });

      for (const brokerConfig of brokerConfigs) {
        await this.startBrokerMonitoring(brokerConfig);
      }

      console.log(`✅ Started monitoring ${brokerConfigs.length} brokers for user: ${userId}`);
      
    } catch (error) {
      console.error('Failed to start broker health monitoring:', error);
      this.emit('error', error);
    }
  }

  /**
   * Start monitoring a specific broker configuration
   */
  async startBrokerMonitoring(brokerConfig: any): Promise<void> {
    if (this.isShuttingDown) return;

    const brokerId = brokerConfig.id;
    
    // Clear existing interval if any
    this.stopBrokerMonitoring(brokerId);

    console.log(`🔍 Starting health monitoring for broker: ${brokerConfig.brokerName} (${brokerId})`);

    // Schedule regular health checks
    const interval = setInterval(async () => {
      if (this.isShuttingDown) {
        clearInterval(interval);
        return;
      }

      await this.performHealthCheck(brokerConfig);
    }, brokerConfig.healthCheckInterval);

    this.healthCheckIntervals.set(brokerId, interval);

    // Perform initial health check
    await this.performHealthCheck(brokerConfig);
  }

  /**
   * Stop monitoring a specific broker
   */
  stopBrokerMonitoring(brokerId: string): void {
    const interval = this.healthCheckIntervals.get(brokerId);
    if (interval) {
      clearInterval(interval);
      this.healthCheckIntervals.delete(brokerId);
      console.log(`⏹️ Stopped monitoring broker: ${brokerId}`);
    }
  }

  /**
   * Stop monitoring all brokers
   */
  stopAllMonitoring(): void {
    console.log('⏹️ Stopping all broker health monitoring...');
    this.isShuttingDown = true;

    for (const [brokerId, interval] of this.healthCheckIntervals) {
      clearInterval(interval);
      console.log(`⏹️ Stopped monitoring broker: ${brokerId}`);
    }

    this.healthCheckIntervals.clear();
    console.log('✅ All broker monitoring stopped');
  }

  /**
   * Perform health check on a specific broker
   */
  async performHealthCheck(brokerConfig: any): Promise<HealthCheckResult> {
    const startTime = Date.now();
    const brokerId = brokerConfig.id;

    try {
      console.log(`🔍 Performing health check for broker: ${brokerConfig.brokerName}`);

      // Mock health check implementation
      // In production, this would connect to the actual MT5 broker
      const healthCheckResult = await this.mockHealthCheck(brokerConfig);

      const latency = Date.now() - startTime;
      
      // Create health check record
      const healthCheckRecord: Omit<BrokerHealthCheck, 'id' | 'timestamp'> = {
        brokerId,
        healthy: healthCheckResult.healthy,
        latency,
        errorMessage: healthCheckResult.error || undefined,
        testType: 'ping',
        testData: {
          brokerName: brokerConfig.brokerName,
          server: brokerConfig.server,
          checkDuration: latency
        },
        responseSize: 256, // Mock response size
        throughput: healthCheckResult.healthy ? 10.5 : 0 // Mock throughput
      };

      // Save health check result to database
      await this.prisma.brokerHealthCheck.create({
        data: healthCheckRecord
      });

      // Update broker configuration health status
      await this.updateBrokerHealthStatus(brokerId, healthCheckResult.healthy, latency, healthCheckResult.error);

      // Update statistics
      this.updateHealthCheckStats(healthCheckResult.healthy, latency);

      // Emit health check event
      const result: HealthCheckResult = {
        connectionId: brokerId,
        brokerId,
        healthy: healthCheckResult.healthy,
        latency,
        error: healthCheckResult.error,
        timestamp: new Date()
      };

      this.emit('healthCheck', result);

      if (!healthCheckResult.healthy) {
        this.emit('brokerUnhealthy', {
          brokerId,
          brokerName: brokerConfig.brokerName,
          error: healthCheckResult.error,
          timestamp: new Date()
        });
      } else {
        this.emit('brokerHealthy', {
          brokerId,
          brokerName: brokerConfig.brokerName,
          latency,
          timestamp: new Date()
        });
      }

      console.log(`${healthCheckResult.healthy ? '✅' : '❌'} Health check for ${brokerConfig.brokerName}: ${healthCheckResult.healthy ? 'HEALTHY' : 'UNHEALTHY'} (${latency}ms)`);

      return result;

    } catch (error) {
      const latency = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Health check failed';

      console.error(`❌ Health check failed for broker ${brokerConfig.brokerName}:`, error);

      // Update broker health status to unhealthy
      await this.updateBrokerHealthStatus(brokerId, false, latency, errorMessage);

      // Update statistics
      this.updateHealthCheckStats(false, latency);

      const result: HealthCheckResult = {
        connectionId: brokerId,
        brokerId,
        healthy: false,
        latency,
        error: errorMessage,
        timestamp: new Date()
      };

      this.emit('healthCheck', result);
      this.emit('brokerUnhealthy', {
        brokerId,
        brokerName: brokerConfig.brokerName,
        error: errorMessage,
        timestamp: new Date()
      });

      return result;
    }
  }

  /**
   * Mock health check implementation
   * In production, this would connect to actual MT5 broker
   */
  private async mockHealthCheck(_brokerConfig: any): Promise<{ healthy: boolean; error?: string }> {
    // Simulate network delay
    const networkDelay = 50 + Math.random() * 200; // 50-250ms
    await new Promise(resolve => setTimeout(resolve, networkDelay));

    // Simulate different failure scenarios
    const random = Math.random();

    if (random < 0.05) { // 5% connection timeout
      throw new Error('Connection timeout');
    }

    if (random < 0.10) { // 5% network error
      return {
        healthy: false,
        error: 'Network unreachable'
      };
    }

    if (random < 0.15) { // 5% authentication error
      return {
        healthy: false,
        error: 'Authentication failed'
      };
    }

    if (random < 0.20) { // 5% server error
      return {
        healthy: false,
        error: 'Broker server error'
      };
    }

    // 80% success rate
    return { healthy: true };
  }

  /**
   * Update broker health status in database
   */
  private async updateBrokerHealthStatus(
    brokerId: string,
    isHealthy: boolean,
    latency: number,
    errorMessage?: string
  ): Promise<void> {
    try {
      const updateData: any = {
        isHealthy,
        lastHealthCheck: new Date(),
      };

      if (errorMessage) {
        updateData.lastError = errorMessage;
        updateData.failureCount = {
          increment: 1
        };
        
        // Update status based on health
        if (!isHealthy) {
          updateData.status = 'FAILED';
        }
      } else if (isHealthy) {
        updateData.lastError = null;
        updateData.status = 'ACTIVE';
        updateData.failureCount = 0; // Reset failure count on successful check
      }

      await this.prisma.brokerConfiguration.update({
        where: { id: brokerId },
        data: updateData
      });

    } catch (error) {
      console.error('Failed to update broker health status:', error);
    }
  }

  /**
   * Update health check statistics
   */
  private updateHealthCheckStats(healthy: boolean, latency: number): void {
    this.healthCheckStats.totalChecks++;
    
    if (healthy) {
      this.healthCheckStats.successfulChecks++;
    } else {
      this.healthCheckStats.failedChecks++;
    }

    // Update average latency using running average
    const totalLatency = this.healthCheckStats.averageLatency * (this.healthCheckStats.totalChecks - 1) + latency;
    this.healthCheckStats.averageLatency = totalLatency / this.healthCheckStats.totalChecks;
  }

  /**
   * Get health check statistics
   */
  getHealthCheckStats(): typeof this.healthCheckStats {
    return { ...this.healthCheckStats };
  }

  /**
   * Get recent health check results for a broker
   */
  async getRecentHealthChecks(
    brokerId: string, 
    limit: number = 10
  ): Promise<BrokerHealthCheck[]> {
    try {
      const healthChecks = await this.prisma.brokerHealthCheck.findMany({
        where: { brokerId },
        orderBy: { timestamp: 'desc' },
        take: limit
      });

      return healthChecks.map(check => ({
        id: check.id,
        brokerId: check.brokerId,
        healthy: check.healthy,
        latency: check.latency,
        errorMessage: check.errorMessage || undefined,
        testType: check.testType as HealthCheckTestType,
        testData: check.testData ? (check.testData as Record<string, any>) : undefined,
        responseSize: check.responseSize || undefined,
        throughput: check.throughput ? check.throughput.toNumber() : undefined,
        timestamp: check.timestamp
      }));

    } catch (error) {
      console.error('Failed to get recent health checks:', error);
      return [];
    }
  }

  /**
   * Get health summary for all brokers of a user
   */
  async getHealthSummary(userId: string): Promise<{
    totalBrokers: number;
    healthyBrokers: number;
    unhealthyBrokers: number;
    averageLatency: number;
    lastCheckTime: Date | null;
  }> {
    try {
      const brokerConfigs = await this.prisma.brokerConfiguration.findMany({
        where: {
          userId,
          deletedAt: null
        }
      });

      const healthyBrokers = brokerConfigs.filter(broker => broker.isHealthy).length;
      const unhealthyBrokers = brokerConfigs.length - healthyBrokers;

      // Calculate average latency from recent health checks
      const recentChecks = await this.prisma.brokerHealthCheck.findMany({
        where: {
          broker: {
            userId,
            deletedAt: null
          },
          timestamp: {
            gte: new Date(Date.now() - 60 * 60 * 1000) // Last hour
          }
        },
        orderBy: { timestamp: 'desc' },
        take: 100
      });

      const averageLatency = recentChecks.length > 0
        ? recentChecks.reduce((sum, check) => sum + check.latency, 0) / recentChecks.length
        : 0;

      const lastCheckTime = recentChecks.length > 0 ? recentChecks[0].timestamp : null;

      return {
        totalBrokers: brokerConfigs.length,
        healthyBrokers,
        unhealthyBrokers,
        averageLatency,
        lastCheckTime
      };

    } catch (error) {
      console.error('Failed to get health summary:', error);
      return {
        totalBrokers: 0,
        healthyBrokers: 0,
        unhealthyBrokers: 0,
        averageLatency: 0,
        lastCheckTime: null
      };
    }
  }

  /**
   * Force health check for a specific broker
   */
  async forceHealthCheck(brokerId: string): Promise<HealthCheckResult | null> {
    try {
      const brokerConfig = await this.prisma.brokerConfiguration.findUnique({
        where: { id: brokerId }
      });

      if (!brokerConfig) {
        throw new Error('Broker configuration not found');
      }

      return await this.performHealthCheck(brokerConfig);

    } catch (error) {
      console.error('Failed to force health check:', error);
      return null;
    }
  }
}