import { Router } from 'express';
import { rateLimit } from 'express-rate-limit';
import { 
  createTradeExecutionController,
  validateExecuteTradeRequest 
} from './execute';
import { 
  createTradeStatusController,
  validateTradeStatusRequest 
} from './status';
import { 
  createTradeModificationController,
  validateModifyTradeRequest,
  validateCancelModificationRequest 
} from './modify';
import { 
  createTradeHistoryController,
  validateHistoryRequest,
  validatePerformanceRequest 
} from './history';

// Import services (these would be injected via DI container in a real app)
import { TradeExecutionEngine } from '../../services/trading/TradeExecutionEngine';
import { TradeStatusTracker } from '../../services/trading/TradeStatusTracker';
import { PositionManager } from '../../services/trading/PositionManager';
import { TradeModificationService } from '../../services/trading/TradeModificationService';
import { TradeHistoryService } from '../../services/trading/TradeHistoryService';
import { ExecutionPerformanceReporter } from '../../services/trading/ExecutionPerformanceReporter';
import { RiskManagementService } from '../../services/risk/RiskManagementService';
import { AuditService } from '../../services/audit/AuditService';

// Rate limiting configurations
const executionRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 20, // Maximum 20 trade executions per minute per user
  message: {
    success: false,
    error: 'Too many trade execution requests. Please wait before trying again.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  keyGenerator: (req) => `trade_execution_${req.user?.id || 'anonymous'}`,
  standardHeaders: true,
  legacyHeaders: false
});

const modificationRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 50, // Maximum 50 modification requests per minute per user
  message: {
    success: false,
    error: 'Too many modification requests. Please wait before trying again.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  keyGenerator: (req) => `trade_modification_${req.user?.id || 'anonymous'}`,
  standardHeaders: true,
  legacyHeaders: false
});

const generalRateLimit = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 300, // Maximum 300 general requests per minute per user
  message: {
    success: false,
    error: 'Too many requests. Please wait before trying again.',
    code: 'RATE_LIMIT_EXCEEDED'
  },
  keyGenerator: (req) => `general_${req.user?.id || 'anonymous'}`,
  standardHeaders: true,
  legacyHeaders: false
});

// Factory function to create trade routes
export const createTradeRoutes = (services: {
  tradeExecutionEngine: TradeExecutionEngine;
  tradeStatusTracker: TradeStatusTracker;
  positionManager: PositionManager;
  tradeModificationService: TradeModificationService;
  tradeHistoryService: TradeHistoryService;
  performanceReporter: ExecutionPerformanceReporter;
  riskManagementService: RiskManagementService;
  auditService: AuditService;
}) => {
  const router = Router();

  // Create controllers
  const executionController = createTradeExecutionController(
    services.tradeExecutionEngine,
    services.riskManagementService,
    services.auditService
  );

  const statusController = createTradeStatusController(
    services.tradeStatusTracker,
    services.positionManager,
    services.tradeHistoryService
  );

  const modificationController = createTradeModificationController(
    services.tradeModificationService,
    services.positionManager
  );

  const historyController = createTradeHistoryController(
    services.tradeHistoryService,
    services.performanceReporter
  );

  // Apply general rate limiting to all trade routes
  router.use(generalRateLimit);

  // === TRADE EXECUTION ROUTES ===
  
  /**
   * @route POST /api/trades/execute
   * @desc Execute a live trade
   * @access Private
   * @rateLimit 20 requests per minute
   */
  router.post(
    '/execute',
    executionRateLimit,
    validateExecuteTradeRequest,
    (req, res, next) => executionController.executeTrade(req, res, next)
  );

  // === TRADE STATUS ROUTES ===

  /**
   * @route GET /api/trades/status/:tradeId
   * @desc Get real-time status of a specific trade
   * @access Private
   */
  router.get(
    '/status/:tradeId',
    validateTradeStatusRequest,
    (req, res, next) => statusController.getTradeStatus(req, res, next)
  );

  /**
   * @route GET /api/trades/status/:tradeId/stream
   * @desc Subscribe to real-time trade status updates via Server-Sent Events
   * @access Private
   */
  router.get(
    '/status/:tradeId/stream',
    (req, res, next) => statusController.subscribeToTradeUpdates(req, res, next)
  );

  /**
   * @route GET /api/trades
   * @desc Get all trades for the authenticated user
   * @access Private
   */
  router.get(
    '/',
    (req, res, next) => statusController.getAllUserTrades(req, res, next)
  );

  // === TRADE MODIFICATION ROUTES ===

  /**
   * @route POST /api/trades/modify/:tradeId
   * @desc Request a trade modification
   * @access Private
   * @rateLimit 50 requests per minute
   */
  router.post(
    '/modify/:tradeId',
    modificationRateLimit,
    validateModifyTradeRequest,
    (req, res, next) => modificationController.modifyTrade(req, res, next)
  );

  /**
   * @route GET /api/trades/modifications/:modificationId
   * @desc Get status of a specific modification
   * @access Private
   */
  router.get(
    '/modifications/:modificationId',
    (req, res, next) => modificationController.getModificationStatus(req, res, next)
  );

  /**
   * @route POST /api/trades/modifications/:modificationId/cancel
   * @desc Cancel a pending modification
   * @access Private
   */
  router.post(
    '/modifications/:modificationId/cancel',
    validateCancelModificationRequest,
    (req, res, next) => modificationController.cancelModification(req, res, next)
  );

  /**
   * @route POST /api/trades/modifications/:modificationId/rollback
   * @desc Rollback an applied modification
   * @access Private
   */
  router.post(
    '/modifications/:modificationId/rollback',
    (req, res, next) => modificationController.rollbackModification(req, res, next)
  );

  /**
   * @route GET /api/trades/:tradeId/modifications
   * @desc Get modification history for a specific trade
   * @access Private
   */
  router.get(
    '/:tradeId/modifications',
    (req, res, next) => modificationController.getModificationHistory(req, res, next)
  );

  // === TRADE HISTORY ROUTES ===

  /**
   * @route GET /api/trades/history
   * @desc Get trade execution history with filters
   * @access Private
   */
  router.get(
    '/history',
    validateHistoryRequest,
    (req, res, next) => historyController.getTradeHistory(req, res, next)
  );

  /**
   * @route GET /api/trades/history/:tradeId
   * @desc Get detailed information for a specific trade
   * @access Private
   */
  router.get(
    '/history/:tradeId',
    (req, res, next) => historyController.getTradeDetails(req, res, next)
  );

  // === PERFORMANCE ANALYSIS ROUTES ===

  /**
   * @route GET /api/trades/performance
   * @desc Get execution performance metrics
   * @access Private
   */
  router.get(
    '/performance',
    validatePerformanceRequest,
    (req, res, next) => historyController.getPerformanceMetrics(req, res, next)
  );

  /**
   * @route GET /api/trades/performance/comparison
   * @desc Get backtest vs live performance comparison
   * @access Private
   */
  router.get(
    '/performance/comparison',
    (req, res, next) => historyController.getPerformanceComparison(req, res, next)
  );

  // === ERROR HANDLING ===

  // Handle 404 for unmatched trade routes
  router.use((req, res) => {
    res.status(404).json({
      success: false,
      error: 'Trade endpoint not found',
      availableEndpoints: [
        'POST /api/trades/execute',
        'GET /api/trades/status/:tradeId',
        'GET /api/trades/status/:tradeId/stream',
        'GET /api/trades',
        'POST /api/trades/modify/:tradeId',
        'GET /api/trades/modifications/:modificationId',
        'POST /api/trades/modifications/:modificationId/cancel',
        'POST /api/trades/modifications/:modificationId/rollback',
        'GET /api/trades/:tradeId/modifications',
        'GET /api/trades/history',
        'GET /api/trades/history/:tradeId',
        'GET /api/trades/performance',
        'GET /api/trades/performance/comparison'
      ]
    });
  });

  // Global error handler for trade routes
  router.use((error: any, req: any, res: any, _next: any) => {
    console.error('Trade route error:', {
      error: error.message,
      stack: error.stack,
      path: req.path,
      method: req.method,
      user: req.user?.id,
      timestamp: new Date().toISOString()
    });

    // Don't expose internal error details in production
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    res.status(error.status || 500).json({
      success: false,
      error: isDevelopment ? error.message : 'Internal server error',
      code: error.code || 'INTERNAL_ERROR',
      ...(isDevelopment && { stack: error.stack }),
      timestamp: new Date().toISOString()
    });
  });

  return router;
};

// Export individual controllers for testing
export {
  createTradeExecutionController,
  createTradeStatusController,
  createTradeModificationController,
  createTradeHistoryController
};