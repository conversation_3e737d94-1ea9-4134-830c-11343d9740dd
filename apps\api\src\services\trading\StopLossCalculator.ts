/**
 * Stop-Loss Calculator Utility
 * 
 * Multiple stop-loss calculation methods for automatic risk management.
 * Supports ATR-based, percentage-based, volatility-adjusted, and trailing stop-losses.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';
import { EventEmitter } from 'events';

/**
 * Stop-loss calculation method types
 */
export type StopLossMethod = 
  | 'atr_based'           // ATR-based stop-loss
  | 'percentage_based'    // Fixed percentage from entry
  | 'volatility_adjusted' // Volatility-based dynamic adjustment
  | 'support_resistance'  // Based on technical levels
  | 'trailing'            // Trailing stop-loss
  | 'time_based';         // Time-based exit

/**
 * Risk tolerance levels affecting stop-loss placement
 */
export type RiskTolerance = 'conservative' | 'moderate' | 'aggressive';

/**
 * Trade direction for stop-loss calculation
 */
export type TradeDirection = 'long' | 'short';

/**
 * Position details for stop-loss calculation
 */
export interface StopLossPosition {
  symbol: string;
  direction: TradeDirection;
  entryPrice: Decimal.Instance;
  quantity: Decimal.Instance;
  entryTime: Date;
  currentPrice?: Decimal.Instance;
  marketValue: Decimal.Instance;
}

/**
 * Market data for stop-loss calculations
 */
export interface StopLossMarketData {
  symbol: string;
  currentPrice: Decimal.Instance;
  bid: Decimal.Instance;
  ask: Decimal.Instance;
  spread: Decimal.Instance;
  volatility: number; // Daily volatility percentage
  averageTrueRange: Decimal.Instance;
  supportLevel?: Decimal.Instance;
  resistanceLevel?: Decimal.Instance;
  timestamp: Date;
}

/**
 * Stop-loss calculation parameters
 */
export interface StopLossParams {
  position: StopLossPosition;
  marketData: StopLossMarketData;
  method: StopLossMethod;
  riskTolerance: RiskTolerance;
  accountBalance: Decimal.Instance;
  maxRiskPercentage?: number; // Maximum risk per trade (default 2%)
  atrMultiplier?: number; // ATR multiplier (default 2.0)
  trailingDistance?: Decimal.Instance; // Trailing stop distance
  timeLimit?: number; // Time-based exit in minutes
}

/**
 * Stop-loss calculation result
 */
export interface StopLossResult {
  method: StopLossMethod;
  stopLossPrice: Decimal.Instance;
  distanceFromEntry: Decimal.Instance;
  distancePercentage: number;
  riskAmount: Decimal.Instance;
  riskPercentage: number;
  potentialLoss: Decimal.Instance;
  reasoning: string;
  confidence: number; // 0-1 confidence in stop-loss level
  isTrailing: boolean;
  adjustmentTrigger?: Decimal.Instance; // Price level that triggers adjustment
  metadata: {
    atrValue?: Decimal.Instance;
    volatilityAdjustment?: number;
    supportResistanceLevel?: Decimal.Instance;
    technicalIndicators?: Record<string, number>;
  };
}

/**
 * Trailing stop-loss state
 */
export interface TrailingStopState {
  positionId: string;
  initialStopLoss: Decimal.Instance;
  currentStopLoss: Decimal.Instance;
  highestPrice: Decimal.Instance; // For long positions
  lowestPrice: Decimal.Instance;  // For short positions
  trailingDistance: Decimal.Instance;
  lastUpdate: Date;
  adjustmentCount: number;
}

/**
 * Stop-loss calculator configuration
 */
export interface StopLossConfig {
  // Risk management defaults
  defaultRiskPercentage: number;
  maxRiskPercentage: number;
  minStopDistance: Decimal.Instance;
  maxStopDistance: Decimal.Instance;
  
  // ATR-based settings
  atrPeriod: number;
  atrMultiplierRanges: {
    conservative: [number, number];
    moderate: [number, number];
    aggressive: [number, number];
  };
  
  // Volatility adjustments
  volatilityThresholds: {
    low: number;    // Below 15%
    medium: number; // 15-30%
    high: number;   // Above 30%
  };
  
  // Trailing stop settings
  minTrailingDistance: Decimal.Instance;
  trailingAdjustmentThreshold: Decimal.Instance;
  
  // Technical analysis settings
  lookbackPeriod: number;
  supportResistanceStrength: number;
}

/**
 * Default stop-loss calculator configuration
 */
export const DEFAULT_STOPLOSS_CONFIG: StopLossConfig = {
  defaultRiskPercentage: 2.0,
  maxRiskPercentage: 5.0,
  minStopDistance: new Decimal(10), // 10 pips minimum
  maxStopDistance: new Decimal(200), // 200 pips maximum
  
  atrPeriod: 14,
  atrMultiplierRanges: {
    conservative: [2.5, 3.5],
    moderate: [2.0, 2.5],
    aggressive: [1.5, 2.0]
  },
  
  volatilityThresholds: {
    low: 15,
    medium: 30,
    high: 45
  },
  
  minTrailingDistance: new Decimal(15),
  trailingAdjustmentThreshold: new Decimal(10),
  
  lookbackPeriod: 20,
  supportResistanceStrength: 0.618 // Golden ratio
};

/**
 * Stop-Loss Calculator - Multi-method stop-loss calculation utility
 */
export class StopLossCalculator extends EventEmitter {
  private readonly config: StopLossConfig;
  private trailingStops: Map<string, TrailingStopState> = new Map();

  constructor(config: Partial<StopLossConfig> = {}) {
    super();
    this.config = { ...DEFAULT_STOPLOSS_CONFIG, ...config };
  }

  /**
   * Calculate stop-loss level using specified method
   */
  public calculateStopLoss(params: StopLossParams): StopLossResult {
    this.validateParams(params);

    switch (params.method) {
      case 'atr_based':
        return this.calculateATRBasedStopLoss(params);
      case 'percentage_based':
        return this.calculatePercentageBasedStopLoss(params);
      case 'volatility_adjusted':
        return this.calculateVolatilityAdjustedStopLoss(params);
      case 'support_resistance':
        return this.calculateSupportResistanceStopLoss(params);
      case 'trailing':
        return this.calculateTrailingStopLoss(params);
      case 'time_based':
        return this.calculateTimeBasedStopLoss(params);
      default:
        throw new Error(`Unsupported stop-loss method: ${params.method}`);
    }
  }

  /**
   * Update trailing stop-loss for a position
   */
  public updateTrailingStopLoss(
    positionId: string, 
    currentPrice: Decimal, 
    direction: TradeDirection
  ): { updated: boolean; newStopLoss?: Decimal.Instance; adjustment?: Decimal } {
    const trailingState = this.trailingStops.get(positionId);
    if (!trailingState) {
      return { updated: false };
    }

    let shouldUpdate = false;
    let newStopLoss = trailingState.currentStopLoss;

    if (direction === 'long') {
      // Update highest price seen
      if (currentPrice.gt(trailingState.highestPrice)) {
        trailingState.highestPrice = currentPrice;
        const potentialNewStop = currentPrice.sub(trailingState.trailingDistance);
        
        if (potentialNewStop.gt(trailingState.currentStopLoss)) {
          newStopLoss = potentialNewStop;
          shouldUpdate = true;
        }
      }
    } else {
      // Update lowest price seen for short positions
      if (currentPrice.lt(trailingState.lowestPrice)) {
        trailingState.lowestPrice = currentPrice;
        const potentialNewStop = currentPrice.add(trailingState.trailingDistance);
        
        if (potentialNewStop.lt(trailingState.currentStopLoss)) {
          newStopLoss = potentialNewStop;
          shouldUpdate = true;
        }
      }
    }

    if (shouldUpdate) {
      const adjustment = newStopLoss.sub(trailingState.currentStopLoss);
      trailingState.currentStopLoss = newStopLoss;
      trailingState.lastUpdate = new Date();
      trailingState.adjustmentCount++;

      this.emit('trailing_stop_updated', {
        positionId,
        oldStopLoss: trailingState.currentStopLoss.sub(adjustment),
        newStopLoss,
        adjustment: adjustment.abs(),
        timestamp: new Date()
      });

      return { updated: true, newStopLoss, adjustment };
    }

    return { updated: false };
  }

  /**
   * Initialize trailing stop-loss for a position
   */
  public initializeTrailingStopLoss(
    positionId: string,
    position: StopLossPosition,
    initialStopLoss: Decimal,
    trailingDistance: Decimal
  ): void {
    const trailingState: TrailingStopState = {
      positionId,
      initialStopLoss,
      currentStopLoss: initialStopLoss,
      highestPrice: position.direction === 'long' ? position.entryPrice : new Decimal(0),
      lowestPrice: position.direction === 'short' ? position.entryPrice : new Decimal(Number.MAX_SAFE_INTEGER),
      trailingDistance,
      lastUpdate: new Date(),
      adjustmentCount: 0
    };

    this.trailingStops.set(positionId, trailingState);
  }

  /**
   * Get trailing stop-loss state for a position
   */
  public getTrailingStopState(positionId: string): TrailingStopState | undefined {
    return this.trailingStops.get(positionId);
  }

  /**
   * Remove trailing stop-loss for a closed position
   */
  public removeTrailingStopLoss(positionId: string): boolean {
    return this.trailingStops.delete(positionId);
  }

  /**
   * Calculate stop-loss using Average True Range (ATR)
   */
  private calculateATRBasedStopLoss(params: StopLossParams): StopLossResult {
    const { position, marketData, riskTolerance } = params;
    const atrValue = marketData.averageTrueRange;
    
    // Select ATR multiplier based on risk tolerance
    const [minMultiplier, maxMultiplier] = this.config.atrMultiplierRanges[riskTolerance];
    const atrMultiplier = params.atrMultiplier || 
      (minMultiplier + maxMultiplier) / 2;

    // Calculate stop-loss distance
    const stopDistance = atrValue.mul(atrMultiplier);
    
    // Calculate stop-loss price
    const stopLossPrice = position.direction === 'long'
      ? position.entryPrice.sub(stopDistance)
      : position.entryPrice.add(stopDistance);

    // Validate stop-loss distance
    const validatedStopLoss = this.validateStopDistance(
      stopLossPrice, 
      position.entryPrice, 
      position.direction
    );

    const distanceFromEntry = position.entryPrice.sub(validatedStopLoss).abs();
    const distancePercentage = distanceFromEntry.div(position.entryPrice).mul(100).toNumber();
    const potentialLoss = position.quantity.mul(distanceFromEntry);
    const riskAmount = potentialLoss;
    const riskPercentage = riskAmount.div(params.accountBalance).mul(100).toNumber();

    return {
      method: 'atr_based',
      stopLossPrice: validatedStopLoss,
      distanceFromEntry,
      distancePercentage,
      riskAmount,
      riskPercentage,
      potentialLoss,
      reasoning: `ATR-based stop-loss using ${atrMultiplier}x ATR (${atrValue.toFixed(4)}). Provides dynamic risk management based on market volatility.`,
      confidence: 0.8,
      isTrailing: false,
      metadata: {
        atrValue,
        volatilityAdjustment: atrMultiplier
      }
    };
  }

  /**
   * Calculate stop-loss using fixed percentage from entry
   */
  private calculatePercentageBasedStopLoss(params: StopLossParams): StopLossResult {
    const { position, riskTolerance } = params;
    
    // Risk percentage based on tolerance
    const riskPercentageMap = {
      conservative: 1.5,
      moderate: 2.0,
      aggressive: 2.5
    };
    
    const riskPercentage = params.maxRiskPercentage || riskPercentageMap[riskTolerance];
    const stopDistance = position.entryPrice.mul(riskPercentage).div(100);
    
    const stopLossPrice = position.direction === 'long'
      ? position.entryPrice.sub(stopDistance)
      : position.entryPrice.add(stopDistance);

    const validatedStopLoss = this.validateStopDistance(
      stopLossPrice, 
      position.entryPrice, 
      position.direction
    );

    const distanceFromEntry = position.entryPrice.sub(validatedStopLoss).abs();
    const distancePercentage = distanceFromEntry.div(position.entryPrice).mul(100).toNumber();
    const potentialLoss = position.quantity.mul(distanceFromEntry);
    const riskAmount = potentialLoss;
    const accountRiskPercentage = riskAmount.div(params.accountBalance).mul(100).toNumber();

    return {
      method: 'percentage_based',
      stopLossPrice: validatedStopLoss,
      distanceFromEntry,
      distancePercentage,
      riskAmount,
      riskPercentage: accountRiskPercentage,
      potentialLoss,
      reasoning: `Fixed ${riskPercentage}% stop-loss from entry price. Simple and consistent risk management approach.`,
      confidence: 0.7,
      isTrailing: false,
      metadata: {}
    };
  }

  /**
   * Calculate stop-loss with volatility adjustments
   */
  private calculateVolatilityAdjustedStopLoss(params: StopLossParams): StopLossResult {
    const { position, marketData, riskTolerance } = params;
    const volatility = marketData.volatility;
    
    // Base percentage adjusted by volatility
    const basePercentage = riskTolerance === 'conservative' ? 1.5 : 
                          riskTolerance === 'moderate' ? 2.0 : 2.5;
    
    // Volatility adjustment factor
    let volatilityMultiplier = 1.0;
    if (volatility <= this.config.volatilityThresholds.low) {
      volatilityMultiplier = 0.8; // Tighter stops in low volatility
    } else if (volatility >= this.config.volatilityThresholds.high) {
      volatilityMultiplier = 1.5; // Wider stops in high volatility
    } else {
      volatilityMultiplier = 1.0 + (volatility - this.config.volatilityThresholds.low) / 
        (this.config.volatilityThresholds.high - this.config.volatilityThresholds.low) * 0.7;
    }
    
    const adjustedPercentage = basePercentage * volatilityMultiplier;
    const stopDistance = position.entryPrice.mul(adjustedPercentage).div(100);
    
    const stopLossPrice = position.direction === 'long'
      ? position.entryPrice.sub(stopDistance)
      : position.entryPrice.add(stopDistance);

    const validatedStopLoss = this.validateStopDistance(
      stopLossPrice, 
      position.entryPrice, 
      position.direction
    );

    const distanceFromEntry = position.entryPrice.sub(validatedStopLoss).abs();
    const distancePercentage = distanceFromEntry.div(position.entryPrice).mul(100).toNumber();
    const potentialLoss = position.quantity.mul(distanceFromEntry);
    const riskAmount = potentialLoss;
    const riskPercentage = riskAmount.div(params.accountBalance).mul(100).toNumber();

    return {
      method: 'volatility_adjusted',
      stopLossPrice: validatedStopLoss,
      distanceFromEntry,
      distancePercentage,
      riskAmount,
      riskPercentage,
      potentialLoss,
      reasoning: `Volatility-adjusted stop-loss (${adjustedPercentage.toFixed(1)}%) based on ${volatility.toFixed(1)}% market volatility. Adapts to current market conditions.`,
      confidence: 0.75,
      isTrailing: false,
      metadata: {
        volatilityAdjustment: volatilityMultiplier
      }
    };
  }

  /**
   * Calculate stop-loss based on support/resistance levels
   */
  private calculateSupportResistanceStopLoss(params: StopLossParams): StopLossResult {
    const { position, marketData } = params;
    
    let stopLossPrice: Decimal.Instance;
    let technicalLevel: Decimal.Instance;
    
    if (position.direction === 'long') {
      // For long positions, stop below support
      if (!marketData.supportLevel) {
        // Fallback to percentage-based if no support level
        return this.calculatePercentageBasedStopLoss(params);
      }
      
      technicalLevel = marketData.supportLevel;
      const bufferDistance = technicalLevel.mul(0.002); // 0.2% buffer
      stopLossPrice = technicalLevel.sub(bufferDistance);
    } else {
      // For short positions, stop above resistance
      if (!marketData.resistanceLevel) {
        return this.calculatePercentageBasedStopLoss(params);
      }
      
      technicalLevel = marketData.resistanceLevel;
      const bufferDistance = technicalLevel.mul(0.002); // 0.2% buffer
      stopLossPrice = technicalLevel.add(bufferDistance);
    }

    const validatedStopLoss = this.validateStopDistance(
      stopLossPrice, 
      position.entryPrice, 
      position.direction
    );

    const distanceFromEntry = position.entryPrice.sub(validatedStopLoss).abs();
    const distancePercentage = distanceFromEntry.div(position.entryPrice).mul(100).toNumber();
    const potentialLoss = position.quantity.mul(distanceFromEntry);
    const riskAmount = potentialLoss;
    const riskPercentage = riskAmount.div(params.accountBalance).mul(100).toNumber();

    return {
      method: 'support_resistance',
      stopLossPrice: validatedStopLoss,
      distanceFromEntry,
      distancePercentage,
      riskAmount,
      riskPercentage,
      potentialLoss,
      reasoning: `Technical level-based stop-loss using ${position.direction === 'long' ? 'support' : 'resistance'} at ${technicalLevel.toFixed(4)} with buffer.`,
      confidence: 0.85,
      isTrailing: false,
      metadata: {
        supportResistanceLevel: technicalLevel
      }
    };
  }

  /**
   * Calculate trailing stop-loss
   */
  private calculateTrailingStopLoss(params: StopLossParams): StopLossResult {
    const { position, marketData } = params;
    const trailingDistance = params.trailingDistance || this.config.minTrailingDistance;
    
    // Initial stop-loss placement
    const initialStopLoss = position.direction === 'long'
      ? position.entryPrice.sub(trailingDistance)
      : position.entryPrice.add(trailingDistance);

    const validatedStopLoss = this.validateStopDistance(
      initialStopLoss, 
      position.entryPrice, 
      position.direction
    );

    const distanceFromEntry = position.entryPrice.sub(validatedStopLoss).abs();
    const distancePercentage = distanceFromEntry.div(position.entryPrice).mul(100).toNumber();
    const potentialLoss = position.quantity.mul(distanceFromEntry);
    const riskAmount = potentialLoss;
    const riskPercentage = riskAmount.div(params.accountBalance).mul(100).toNumber();

    // Determine when trailing should trigger
    const adjustmentTrigger = position.direction === 'long'
      ? position.entryPrice.add(trailingDistance)
      : position.entryPrice.sub(trailingDistance);

    return {
      method: 'trailing',
      stopLossPrice: validatedStopLoss,
      distanceFromEntry,
      distancePercentage,
      riskAmount,
      riskPercentage,
      potentialLoss,
      reasoning: `Trailing stop-loss with ${trailingDistance.toFixed(4)} distance. Will adjust as price moves favorably.`,
      confidence: 0.9,
      isTrailing: true,
      adjustmentTrigger,
      metadata: {}
    };
  }

  /**
   * Calculate time-based stop-loss (exit after time limit)
   */
  private calculateTimeBasedStopLoss(params: StopLossParams): StopLossResult {
    const { position, marketData } = params;
    const timeLimit = params.timeLimit || 240; // 4 hours default
    
    // For time-based exits, use current market price as stop
    const stopLossPrice = marketData.currentPrice;
    
    const distanceFromEntry = position.entryPrice.sub(stopLossPrice).abs();
    const distancePercentage = distanceFromEntry.div(position.entryPrice).mul(100).toNumber();
    const potentialLoss = position.quantity.mul(distanceFromEntry);
    const riskAmount = potentialLoss;
    const riskPercentage = riskAmount.div(params.accountBalance).mul(100).toNumber();

    const timeElapsed = (Date.now() - position.entryTime.getTime()) / (1000 * 60); // minutes
    const timeRemaining = Math.max(0, timeLimit - timeElapsed);

    return {
      method: 'time_based',
      stopLossPrice,
      distanceFromEntry,
      distancePercentage,
      riskAmount,
      riskPercentage,
      potentialLoss,
      reasoning: `Time-based exit after ${timeLimit} minutes. ${timeRemaining.toFixed(0)} minutes remaining.`,
      confidence: 0.6,
      isTrailing: false,
      metadata: {
        technicalIndicators: {
          timeLimit,
          timeElapsed,
          timeRemaining
        }
      }
    };
  }

  /**
   * Validate and adjust stop-loss distance within acceptable ranges
   */
  private validateStopDistance(
    stopLossPrice: Decimal,
    entryPrice: Decimal,
    direction: TradeDirection
  ): Decimal {
    const distance = entryPrice.sub(stopLossPrice).abs();
    
    if (distance.lt(this.config.minStopDistance)) {
      // Adjust to minimum distance
      return direction === 'long'
        ? entryPrice.sub(this.config.minStopDistance)
        : entryPrice.add(this.config.minStopDistance);
    }
    
    if (distance.gt(this.config.maxStopDistance)) {
      // Adjust to maximum distance
      return direction === 'long'
        ? entryPrice.sub(this.config.maxStopDistance)
        : entryPrice.add(this.config.maxStopDistance);
    }
    
    return stopLossPrice;
  }

  /**
   * Validate calculation parameters
   */
  private validateParams(params: StopLossParams): void {
    if (params.position.entryPrice.lte(0)) {
      throw new Error('Entry price must be positive');
    }
    
    if (params.position.quantity.lte(0)) {
      throw new Error('Quantity must be positive');
    }
    
    if (params.accountBalance.lte(0)) {
      throw new Error('Account balance must be positive');
    }
    
    if (params.maxRiskPercentage && 
        (params.maxRiskPercentage <= 0 || params.maxRiskPercentage > this.config.maxRiskPercentage)) {
      throw new Error(`Risk percentage must be between 0 and ${this.config.maxRiskPercentage}%`);
    }
  }

  /**
   * Update calculator configuration
   */
  public updateConfig(newConfig: Partial<StopLossConfig>): void {
    Object.assign(this.config, newConfig);
    this.emit('config_updated', this.config);
  }

  /**
   * Get current calculator configuration
   */
  public getConfig(): StopLossConfig {
    return { ...this.config };
  }

  /**
   * Get all active trailing stop-losses
   */
  public getActiveTrailingStops(): Map<string, TrailingStopState> {
    return new Map(this.trailingStops);
  }
}

/**
 * Factory function to create StopLossCalculator instance
 */
export function createStopLossCalculator(config?: Partial<StopLossConfig>): StopLossCalculator {
  return new StopLossCalculator(config);
}

/**
 * Default export for convenience
 */
export default StopLossCalculator;