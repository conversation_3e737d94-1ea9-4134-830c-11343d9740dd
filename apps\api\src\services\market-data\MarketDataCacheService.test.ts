/**
 * Unit tests for MarketDataCacheService
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { promises as fs } from 'fs';
import Decimal from 'decimal.js';
import { 
  MarketDataCacheService, 
  CacheConfig 
} from './MarketDataCacheService';
import { 
  NormalizedMarketData, 
  DataSource, 
  TimeFrame 
} from './RealTimeDataProcessor';

describe('MarketDataCacheService', () => {
  let cacheService: MarketDataCacheService;
  let config: CacheConfig;
  let testDbPath: string;

  beforeEach(async () => {
    // Create temporary database path
    testDbPath = `test-cache-${Date.now()}-${Math.random().toString(36).substr(2, 9)}.db`;
    
    config = {
      databasePath: testDbPath,
      maxCacheSize: 100, // MB
      defaultTTL: 3600, // 1 hour
      syncInterval: 300, // 5 minutes
      maxRetries: 3,
      enableCompression: false, // Disabled for tests
      enableMetrics: true,
      vacuumInterval: 3600, // 1 hour
    };

    cacheService = new MarketDataCacheService(config);
    await cacheService.initialize();
  });

  afterEach(async () => {
    await cacheService.shutdown();
    
    // Clean up test database file
    try {
      await fs.unlink(testDbPath);
    } catch (error) {
      // File might not exist, ignore error
    }
  });

  // Helper function to create test market data
  const createTestMarketData = (overrides: Partial<NormalizedMarketData> = {}): NormalizedMarketData => ({
    id: `test_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    source: DataSource.MT5,
    instrument: 'EURUSD',
    timeframe: TimeFrame.M1,
    timestamp: new Date(),
    open: new Decimal('1.08500'),
    high: new Decimal('1.08650'),
    low: new Decimal('1.08450'),
    close: new Decimal('1.08600'),
    volume: new Decimal('1000'),
    precision: 5,
    timezone: 'UTC',
    isCompressed: false,
    qualityScore: 100,
    originalPayloadSize: 500,
    processedPayloadSize: 500,
    processingTimeMs: 10,
    ...overrides,
  });

  describe('Initialization', () => {
    it('should initialize successfully', async () => {
      const newCacheService = new MarketDataCacheService(config);
      const initPromise = newCacheService.initialize();
      
      await expect(initPromise).resolves.toBeUndefined();
      await newCacheService.shutdown();
    });

    it('should emit initialized event', async () => {
      const newCacheService = new MarketDataCacheService(config);
      const initSpy = vi.fn();
      
      newCacheService.on('initialized', initSpy);
      await newCacheService.initialize();
      
      expect(initSpy).toHaveBeenCalled();
      await newCacheService.shutdown();
    });
  });

  describe('Basic Cache Operations', () => {
    it('should store and retrieve market data', async () => {
      const testData = createTestMarketData();
      const cacheKey = 'test_key_1';

      // Store data
      await cacheService.set(cacheKey, testData);

      // Retrieve data
      const retrieved = await cacheService.get(cacheKey);

      expect(retrieved).toBeTruthy();
      expect(retrieved!.id).toBe(testData.id);
      expect(retrieved!.instrument).toBe(testData.instrument);
      expect(retrieved!.open.toString()).toBe(testData.open.toString());
      expect(retrieved!.close.toString()).toBe(testData.close.toString());
    });

    it('should return null for non-existent key', async () => {
      const retrieved = await cacheService.get('non_existent_key');
      expect(retrieved).toBeNull();
    });

    it('should handle TTL expiration', async () => {
      const testData = createTestMarketData();
      const cacheKey = 'test_key_ttl';

      // Store data with very short TTL (1 second)
      await cacheService.set(cacheKey, testData, 1);

      // Should be retrievable immediately
      let retrieved = await cacheService.get(cacheKey);
      expect(retrieved).toBeTruthy();

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 1100));

      // Should be null after expiration
      retrieved = await cacheService.get(cacheKey);
      expect(retrieved).toBeNull();
    });

    it('should delete cached entries', async () => {
      const testData = createTestMarketData();
      const cacheKey = 'test_key_delete';

      // Store data
      await cacheService.set(cacheKey, testData);

      // Verify it exists
      let retrieved = await cacheService.get(cacheKey);
      expect(retrieved).toBeTruthy();

      // Delete it
      const deleted = await cacheService.delete(cacheKey);
      expect(deleted).toBe(true);

      // Verify it's gone
      retrieved = await cacheService.get(cacheKey);
      expect(retrieved).toBeNull();
    });

    it('should return false when deleting non-existent key', async () => {
      const deleted = await cacheService.delete('non_existent_key');
      expect(deleted).toBe(false);
    });
  });

  describe('Data Types and Precision', () => {
    it('should preserve Decimal precision', async () => {
      const testData = createTestMarketData({
        open: new Decimal('1.085001234'),
        close: new Decimal('1.086009876'),
      });
      const cacheKey = 'precision_test';

      await cacheService.set(cacheKey, testData);
      const retrieved = await cacheService.get(cacheKey);

      expect(retrieved!.open.toString()).toBe('1.085001234');
      expect(retrieved!.close.toString()).toBe('1.086009876');
    });

    it('should handle optional fields correctly', async () => {
      const testData = createTestMarketData({
        bid: new Decimal('1.08598'),
        ask: new Decimal('1.08602'),
        spread: new Decimal('0.00004'),
      });
      const cacheKey = 'optional_fields_test';

      await cacheService.set(cacheKey, testData);
      const retrieved = await cacheService.get(cacheKey);

      expect(retrieved!.bid?.toString()).toBe('1.08598');
      expect(retrieved!.ask?.toString()).toBe('1.08602');
      expect(retrieved!.spread?.toString()).toBe('0.00004');
    });

    it('should handle missing optional fields', async () => {
      const testData = createTestMarketData({
        bid: undefined,
        ask: undefined,
        spread: undefined,
      });
      const cacheKey = 'missing_optional_test';

      await cacheService.set(cacheKey, testData);
      const retrieved = await cacheService.get(cacheKey);

      expect(retrieved!.bid).toBeUndefined();
      expect(retrieved!.ask).toBeUndefined();
      expect(retrieved!.spread).toBeUndefined();
    });
  });

  describe('Search Functionality', () => {
    beforeEach(async () => {
      // Add test data for search tests
      const testData = [
        createTestMarketData({
          instrument: 'EURUSD',
          timeframe: TimeFrame.M1,
          source: DataSource.MT5,
          timestamp: new Date('2023-12-01T10:00:00Z'),
        }),
        createTestMarketData({
          instrument: 'EURUSD',
          timeframe: TimeFrame.M5,
          source: DataSource.MT5,
          timestamp: new Date('2023-12-01T11:00:00Z'),
        }),
        createTestMarketData({
          instrument: 'GBPUSD',
          timeframe: TimeFrame.M1,
          source: DataSource.ALPHA_VANTAGE,
          timestamp: new Date('2023-12-01T12:00:00Z'),
        }),
      ];

      for (let i = 0; i < testData.length; i++) {
        await cacheService.set(`search_test_${i}`, testData[i]);
      }
    });

    it('should search by instrument', async () => {
      const results = await cacheService.search({ instrument: 'EURUSD' });
      
      expect(results).toHaveLength(2);
      expect(results.every(r => r.instrument === 'EURUSD')).toBe(true);
    });

    it('should search by timeframe', async () => {
      const results = await cacheService.search({ timeframe: TimeFrame.M1 });
      
      expect(results).toHaveLength(2);
      expect(results.every(r => r.timeframe === TimeFrame.M1)).toBe(true);
    });

    it('should search by source', async () => {
      const results = await cacheService.search({ source: DataSource.MT5 });
      
      expect(results).toHaveLength(2);
      expect(results.every(r => r.source === DataSource.MT5)).toBe(true);
    });

    it('should search with multiple criteria', async () => {
      const results = await cacheService.search({
        instrument: 'EURUSD',
        timeframe: TimeFrame.M1,
      });
      
      expect(results).toHaveLength(1);
      expect(results[0].instrument).toBe('EURUSD');
      expect(results[0].timeframe).toBe(TimeFrame.M1);
    });

    it('should search with timestamp range', async () => {
      const results = await cacheService.search({
        fromTimestamp: new Date('2023-12-01T10:30:00Z'),
        toTimestamp: new Date('2023-12-01T12:30:00Z'),
      });
      
      expect(results).toHaveLength(2);
    });

    it('should respect search limit', async () => {
      const results = await cacheService.search({ limit: 2 });
      
      expect(results).toHaveLength(2);
    });
  });

  describe('Statistics and Monitoring', () => {
    it('should track cache hits and misses', async () => {
      const testData = createTestMarketData();
      const cacheKey = 'stats_test';

      // Get initial stats
      const initialStats = await cacheService.getStats();
      
      // Cache miss
      await cacheService.get('non_existent_key');
      
      // Cache set and hit
      await cacheService.set(cacheKey, testData);
      await cacheService.get(cacheKey);
      
      const finalStats = await cacheService.getStats();
      
      expect(finalStats.hitCount).toBe(initialStats.hitCount + 1);
      expect(finalStats.missCount).toBe(initialStats.missCount + 1);
      expect(finalStats.totalEntries).toBeGreaterThan(initialStats.totalEntries);
    });

    it('should calculate hit ratio', async () => {
      const testData = createTestMarketData();
      const cacheKey = 'hit_ratio_test';

      // Perform operations
      await cacheService.set(cacheKey, testData);
      await cacheService.get(cacheKey); // hit
      await cacheService.get('non_existent'); // miss
      await cacheService.get(cacheKey); // hit

      const stats = await cacheService.getStats();
      
      // Should be 2 hits out of 3 gets = 0.67 ratio (approximately)
      expect(stats.hitRatio).toBeGreaterThan(0.6);
      expect(stats.hitRatio).toBeLessThan(0.8);
    });

    it('should track cache size', async () => {
      const testData = createTestMarketData();
      const initialStats = await cacheService.getStats();

      await cacheService.set('size_test', testData);
      
      const finalStats = await cacheService.getStats();
      expect(finalStats.totalSize).toBeGreaterThan(initialStats.totalSize);
    });
  });

  describe('Expired Entry Cleanup', () => {
    it('should clear expired entries', async () => {
      const testData = createTestMarketData();
      
      // First just test that we can store and retrieve an entry
      await cacheService.set('test_simple', testData);
      const retrieved = await cacheService.get('test_simple');
      expect(retrieved).toBeTruthy();
      
      // Create separate test data for each entry to avoid ID conflicts
      const testData1 = createTestMarketData();
      const testData2 = createTestMarketData();
      const testData3 = createTestMarketData();

      // Add entries with different TTLs
      await cacheService.set('expire_1', testData1, 2);
      await cacheService.set('expire_2', testData2, 2);  
      await cacheService.set('expire_3', testData3, 3600); // Long TTL

      // Verify they are stored
      const count1 = await cacheService.get('expire_1');
      const count2 = await cacheService.get('expire_2');
      const count3 = await cacheService.get('expire_3');
      
      expect(count1).toBeTruthy();
      expect(count2).toBeTruthy();
      expect(count3).toBeTruthy();

      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 2500));

      const clearedCount = await cacheService.clearExpired();
      expect(clearedCount).toBe(2);
    });
  });

  describe('Health Check', () => {
    it('should report healthy status when cache is functioning', async () => {
      const health = await cacheService.healthCheck();
      
      expect(health.isHealthy).toBe(true);
      expect(health.issues).toHaveLength(0);
      expect(health.stats).toBeTruthy();
    });

    it('should detect low hit ratio issues', async () => {
      // Create scenario with low hit ratio
      for (let i = 0; i < 10; i++) {
        await cacheService.get(`non_existent_${i}`); // All misses
      }

      const health = await cacheService.healthCheck();
      
      expect(health.isHealthy).toBe(false);
      expect(health.issues.some(issue => issue.includes('hit ratio'))).toBe(true);
    });
  });

  describe('Event Handling', () => {
    it('should emit cache_set events', async () => {
      const testData = createTestMarketData();
      const cacheKey = 'event_test';
      const setSpy = vi.fn();

      cacheService.on('cache_set', setSpy);
      
      await cacheService.set(cacheKey, testData);
      
      expect(setSpy).toHaveBeenCalledWith({ key: cacheKey, dataId: testData.id });
    });

    it('should emit cache_hit events', async () => {
      const testData = createTestMarketData();
      const cacheKey = 'event_hit_test';
      const hitSpy = vi.fn();

      // Set data first
      await cacheService.set(cacheKey, testData);
      
      cacheService.on('cache_hit', hitSpy);
      
      // Get data to trigger hit
      await cacheService.get(cacheKey);
      
      expect(hitSpy).toHaveBeenCalledWith({ key: cacheKey, dataId: testData.id });
    });

    it('should emit cache_delete events', async () => {
      const testData = createTestMarketData();
      const cacheKey = 'event_delete_test';
      const deleteSpy = vi.fn();

      // Set data first
      await cacheService.set(cacheKey, testData);
      
      cacheService.on('cache_delete', deleteSpy);
      
      // Delete to trigger event
      await cacheService.delete(cacheKey);
      
      expect(deleteSpy).toHaveBeenCalledWith({ key: cacheKey });
    });

    it('should emit cache_evicted events', async () => {
      const testData = createTestMarketData();
      const evictSpy = vi.fn();

      // Set data with short TTL
      await cacheService.set('evict_test', testData, 1);
      
      cacheService.on('cache_evicted', evictSpy);
      
      // Wait for expiration
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      // Clear expired to trigger eviction
      const evicted = await cacheService.clearExpired();
      
      if (evicted > 0) {
        expect(evictSpy).toHaveBeenCalledWith({ count: evicted });
      }
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const errorSpy = vi.fn();
      cacheService.on('cache_error', errorSpy);

      // Close database to simulate error condition
      await cacheService.shutdown();

      // Try to perform operation on closed database
      await expect(cacheService.get('test')).rejects.toThrow();
    });

    it('should emit cache_error events on failures', async () => {
      const errorSpy = vi.fn();
      cacheService.on('cache_error', errorSpy);

      // Force an error by shutting down first
      await cacheService.shutdown();

      try {
        await cacheService.set('error_test', createTestMarketData());
      } catch (error) {
        // Expected to throw
      }

      expect(errorSpy).toHaveBeenCalled();
    });
  });

  describe('Synchronization', () => {
    it('should track sync statistics', async () => {
      const testData = createTestMarketData();
      await cacheService.set('sync_test', testData);

      const syncStats = await cacheService.syncWithTimescaleDB();
      
      expect(syncStats.lastSyncAt).toBeInstanceOf(Date);
      expect(syncStats.recordsSynced).toBeGreaterThanOrEqual(0);
      expect(syncStats.syncDuration).toBeGreaterThanOrEqual(0);
      expect(syncStats.conflictsResolved).toBeGreaterThanOrEqual(0);
      expect(syncStats.errorsEncountered).toBeGreaterThanOrEqual(0);
    });

    it('should emit sync_completed events', async () => {
      const syncSpy = vi.fn();
      cacheService.on('sync_completed', syncSpy);

      await cacheService.syncWithTimescaleDB();
      
      expect(syncSpy).toHaveBeenCalled();
    });
  });

  describe('Shutdown', () => {
    it('should shutdown gracefully', async () => {
      const testData = createTestMarketData();
      await cacheService.set('shutdown_test', testData);

      await expect(cacheService.shutdown()).resolves.toBeUndefined();
      
      // Should not be able to perform operations after shutdown
      await expect(cacheService.get('shutdown_test')).rejects.toThrow();
    });

    it('should emit shutdown event', async () => {
      const shutdownSpy = vi.fn();
      cacheService.on('shutdown', shutdownSpy);

      await cacheService.shutdown();
      
      expect(shutdownSpy).toHaveBeenCalled();
    });
  });
});