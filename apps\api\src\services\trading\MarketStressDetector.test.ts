import { describe, it, expect, beforeEach, vi } from 'vitest';
import { MarketStressDetector } from './MarketStressDetector';
import { MarketData, MarketCondition, StressEventType, StressSeverity, LiquidityMetrics } from '../../types/trading';
import Decimal from 'decimal.js';

describe('MarketStressDetector', () => {
  let detector: MarketStressDetector;
  let mockEmit: ReturnType<typeof vi.fn>;

  const createMockMarketData = (overrides: Partial<MarketData> = {}): MarketData => ({
    symbol: 'EURUSD',
    timestamp: new Date(),
    bid: new Decimal('1.0500'),
    ask: new Decimal('1.0502'),
    volume: new Decimal('1000'),
    volatility: 0.15,
    liquidity: {
      bidDepth: new Decimal('500'),
      askDepth: new Decimal('500'),
      spread: new Decimal('0.0002'),
      impactCost: 0.001
    },
    ...overrides
  });

  beforeEach(() => {
    detector = new MarketStressDetector({
      volatilityThreshold: 0.30,
      liquidityThreshold: 0.20,
      spreadThreshold: 0.005,
      volumeThreshold: 0.50,
      correlationThreshold: 0.80,
      correlationBreakdownThreshold: 0.05, // Lower threshold for easier detection
      windowSize: 20,
      stressDetectionInterval: 1000
    });

    mockEmit = vi.fn();
    (detector as any).emit = mockEmit;
  });

  describe('Initialization', () => {
    it('should initialize with default configuration', () => {
      const defaultDetector = new MarketStressDetector();
      expect(defaultDetector).toBeDefined();
    });

    it('should initialize with custom configuration', () => {
      const config = {
        volatilityThreshold: 0.40,
        liquidityThreshold: 0.15,
        spreadThreshold: 0.003,
        volumeThreshold: 0.60,
        correlationThreshold: 0.85,
        windowSize: 30,
        stressDetectionInterval: 2000
      };

      const customDetector = new MarketStressDetector(config);
      expect(customDetector).toBeDefined();
    });

    it('should start and stop stress detection', () => {
      detector.start();
      expect((detector as any).isRunning).toBe(true);

      detector.stop();
      expect((detector as any).isRunning).toBe(false);
    });
  });

  describe('Market Data Processing', () => {
    it('should process market data and update metrics', () => {
      const marketData = createMockMarketData({
        volatility: 0.25,
        volume: new Decimal('800')
      });

      detector.processMarketData(marketData);

      const conditions = detector.getCurrentConditions();
      expect(conditions).toHaveLength(1);
      expect(conditions[0].symbol).toBe('EURUSD');
    });

    it('should handle multiple symbols', () => {
      const eurusd = createMockMarketData({ symbol: 'EURUSD' });
      const gbpusd = createMockMarketData({ symbol: 'GBPUSD' });

      detector.processMarketData(eurusd);
      detector.processMarketData(gbpusd);

      const conditions = detector.getCurrentConditions();
      expect(conditions).toHaveLength(2);
      expect(conditions.map(c => c.symbol)).toEqual(['EURUSD', 'GBPUSD']);
    });

    it('should maintain rolling window of data', () => {
      const baseData = createMockMarketData();

      for (let i = 0; i < 25; i++) {
        const data = createMockMarketData({
          timestamp: new Date(Date.now() + i * 1000),
          volatility: 0.10 + (i * 0.01)
        });
        detector.processMarketData(data);
      }

      const conditions = detector.getCurrentConditions();
      expect(conditions).toHaveLength(1);
    });
  });

  describe('Volatility Spike Detection', () => {
    it('should detect volatility spike stress event', () => {
      const normalData = createMockMarketData({ volatility: 0.15 });
      const spikeData = createMockMarketData({ 
        volatility: 0.35,
        timestamp: new Date(Date.now() + 1000)
      });

      detector.processMarketData(normalData);
      detector.processMarketData(spikeData);

      expect(mockEmit).toHaveBeenCalledWith('stressEvent', expect.objectContaining({
        type: StressEventType.VOLATILITY_SPIKE,
        severity: StressSeverity.HIGH
      }));
    });

    it('should calculate volatility change percentage', () => {
      const data1 = createMockMarketData({ volatility: 0.10 });
      const data2 = createMockMarketData({ 
        volatility: 0.20,
        timestamp: new Date(Date.now() + 1000)
      });

      detector.processMarketData(data1);
      detector.processMarketData(data2);

      const conditions = detector.getCurrentConditions();
      expect(conditions[0].volatilityChange).toBeCloseTo(1.0);
    });

    it('should not trigger on gradual volatility increase', () => {
      for (let i = 0; i < 10; i++) {
        const data = createMockMarketData({
          volatility: 0.15 + (i * 0.01),
          timestamp: new Date(Date.now() + i * 1000)
        });
        detector.processMarketData(data);
      }

      expect(mockEmit).not.toHaveBeenCalledWith('stressEvent', expect.anything());
    });
  });

  describe('Liquidity Crisis Detection', () => {
    it('should detect liquidity crisis from low depth', () => {
      const liquidityData = createMockMarketData({
        liquidity: {
          bidDepth: new Decimal('50'),
          askDepth: new Decimal('50'),
          spread: new Decimal('0.0002'),
          impactCost: 0.001
        }
      });

      detector.processMarketData(liquidityData);

      expect(mockEmit).toHaveBeenCalledWith('stressEvent', expect.objectContaining({
        type: StressEventType.LIQUIDITY_CRISIS,
        severity: StressSeverity.HIGH
      }));
    });

    it('should detect liquidity crisis from high spread', () => {
      const spreadData = createMockMarketData({
        liquidity: {
          bidDepth: new Decimal('500'),
          askDepth: new Decimal('500'),
          spread: new Decimal('0.006'),
          impactCost: 0.001
        }
      });

      detector.processMarketData(spreadData);

      expect(mockEmit).toHaveBeenCalledWith('stressEvent', expect.objectContaining({
        type: StressEventType.LIQUIDITY_CRISIS
      }));
    });

    it('should calculate liquidity score correctly', () => {
      const goodLiquidity = createMockMarketData({
        liquidity: {
          bidDepth: new Decimal('1000'),
          askDepth: new Decimal('1000'),
          spread: new Decimal('0.0001'),
          impactCost: 0.0005
        }
      });

      detector.processMarketData(goodLiquidity);

      const conditions = detector.getCurrentConditions();
      expect(conditions[0].liquidityScore).toBeGreaterThan(0.8);
    });
  });

  describe('Volume Anomaly Detection', () => {
    it('should detect volume spike', () => {
      const normalVolume = createMockMarketData({ volume: new Decimal('1000') });
      const spikeVolume = createMockMarketData({
        volume: new Decimal('2000'),
        timestamp: new Date(Date.now() + 1000)
      });

      detector.processMarketData(normalVolume);
      detector.processMarketData(spikeVolume);

      expect(mockEmit).toHaveBeenCalledWith('stressEvent', expect.objectContaining({
        type: StressEventType.VOLUME_SPIKE
      }));
    });

    it('should detect volume drought', () => {
      const normalVolume = createMockMarketData({ volume: new Decimal('1000') });
      const lowVolume = createMockMarketData({
        volume: new Decimal('400'),
        timestamp: new Date(Date.now() + 1000)
      });

      detector.processMarketData(normalVolume);
      detector.processMarketData(lowVolume);

      expect(mockEmit).toHaveBeenCalledWith('stressEvent', expect.objectContaining({
        type: StressEventType.VOLUME_DROUGHT
      }));
    });

    it('should calculate volume change correctly', () => {
      const volume1 = createMockMarketData({ volume: new Decimal('1000') });
      const volume2 = createMockMarketData({
        volume: new Decimal('1500'),
        timestamp: new Date(Date.now() + 1000)
      });

      detector.processMarketData(volume1);
      detector.processMarketData(volume2);

      const conditions = detector.getCurrentConditions();
      expect(conditions[0].volumeChange).toBeCloseTo(0.5);
    });
  });

  describe('Correlation Analysis', () => {
    it('should track correlation between instruments', () => {
      const eurusd1 = createMockMarketData({ symbol: 'EURUSD', bid: new Decimal('1.0500') });
      const gbpusd1 = createMockMarketData({ symbol: 'GBPUSD', bid: new Decimal('1.2500') });
      
      const eurusd2 = createMockMarketData({ 
        symbol: 'EURUSD', 
        bid: new Decimal('1.0520'),
        timestamp: new Date(Date.now() + 1000)
      });
      const gbpusd2 = createMockMarketData({ 
        symbol: 'GBPUSD', 
        bid: new Decimal('1.2520'),
        timestamp: new Date(Date.now() + 1000)
      });

      detector.processMarketData(eurusd1);
      detector.processMarketData(gbpusd1);
      detector.processMarketData(eurusd2);
      detector.processMarketData(gbpusd2);

      const correlationMatrix = detector.getCorrelationMatrix();
      expect(correlationMatrix).toHaveProperty('EURUSD-GBPUSD');
    });

    it('should detect correlation breakdown', () => {
      const symbols = ['EURUSD', 'GBPUSD'];
      
      for (let i = 0; i < 15; i++) {
        symbols.forEach(symbol => {
          const data = createMockMarketData({
            symbol,
            bid: new Decimal(symbol === 'EURUSD' ? 1.0500 + (i * 0.001) : 1.2500 + (i * 0.001)),
            timestamp: new Date(Date.now() + i * 1000)
          });
          detector.processMarketData(data);
        });
      }

      symbols.forEach(symbol => {
        const breakdownData = createMockMarketData({
          symbol,
          bid: new Decimal(symbol === 'EURUSD' ? 1.0400 : 1.2600),
          timestamp: new Date(Date.now() + 16000)
        });
        detector.processMarketData(breakdownData);
      });

      expect(mockEmit).toHaveBeenCalledWith('stressEvent', expect.objectContaining({
        type: StressEventType.CORRELATION_BREAKDOWN
      }));
    });
  });

  describe('Stress Level Calculation', () => {
    it('should calculate overall stress level', () => {
      const stressfulData = createMockMarketData({
        volatility: 0.35,
        volume: new Decimal('2000'),
        liquidity: {
          bidDepth: new Decimal('100'),
          askDepth: new Decimal('100'),
          spread: new Decimal('0.008'),
          impactCost: 0.005
        }
      });

      detector.processMarketData(stressfulData);

      const stressLevel = detector.getOverallStressLevel();
      expect(stressLevel).toBeGreaterThan(0.4);
    });

    it('should return low stress for normal conditions', () => {
      const normalData = createMockMarketData({
        volatility: 0.12,
        volume: new Decimal('1000'),
        liquidity: {
          bidDepth: new Decimal('800'),
          askDepth: new Decimal('800'),
          spread: new Decimal('0.0001'),
          impactCost: 0.0005
        }
      });

      detector.processMarketData(normalData);

      const stressLevel = detector.getOverallStressLevel();
      expect(stressLevel).toBeLessThan(0.3);
    });

    it('should weight different stress factors appropriately', () => {
      const volatilityStress = createMockMarketData({ volatility: 0.40 });
      const liquidityStress = createMockMarketData({
        liquidity: {
          bidDepth: new Decimal('50'),
          askDepth: new Decimal('50'),
          spread: new Decimal('0.008'),
          impactCost: 0.01
        }
      });

      detector.processMarketData(volatilityStress);
      const volatilityStressLevel = detector.getOverallStressLevel();

      const newDetector = new MarketStressDetector();
      newDetector.processMarketData(liquidityStress);
      const liquidityStressLevel = newDetector.getOverallStressLevel();

      expect(volatilityStressLevel).toBeGreaterThan(0);
      expect(liquidityStressLevel).toBeGreaterThan(0);
    });
  });

  describe('Historical Data Management', () => {
    it('should provide historical stress data', () => {
      const timestamps = [];
      for (let i = 0; i < 5; i++) {
        const timestamp = new Date(Date.now() + i * 1000);
        timestamps.push(timestamp);
        
        const data = createMockMarketData({
          timestamp,
          volatility: 0.10 + (i * 0.05)
        });
        detector.processMarketData(data);
      }

      const history = detector.getStressHistory(new Date(Date.now() - 1000), new Date());
      expect(history.length).toBeGreaterThan(0);
    });

    it('should filter historical data by time range', () => {
      const now = Date.now();
      const timestamps = [];
      
      for (let i = 0; i < 10; i++) {
        const timestamp = new Date(now + i * 1000);
        timestamps.push(timestamp);
        
        const data = createMockMarketData({
          timestamp,
          volatility: 0.15
        });
        detector.processMarketData(data);
      }

      const recentHistory = detector.getStressHistory(
        new Date(now + 5000),
        new Date(now + 10000)
      );
      
      expect(recentHistory.length).toBeLessThanOrEqual(5);
    });

    it('should export stress metrics to CSV format', () => {
      const data = createMockMarketData({ volatility: 0.25 });
      detector.processMarketData(data);

      const csv = detector.exportMetrics();
      expect(csv).toContain('timestamp');
      expect(csv).toContain('symbol');
      expect(csv).toContain('stressLevel');
    });
  });

  describe('Real-time Monitoring', () => {
    it('should emit events when stress threshold exceeded', () => {
      const highStressData = createMockMarketData({
        volatility: 0.45,
        liquidity: {
          bidDepth: new Decimal('50'),
          askDepth: new Decimal('50'),
          spread: new Decimal('0.010'),
          impactCost: 0.02
        }
      });

      detector.processMarketData(highStressData);

      expect(mockEmit).toHaveBeenCalledWith('stressLevelUpdate', expect.objectContaining({
        level: expect.any(Number),
        severity: expect.any(String)
      }));
    });

    it('should provide real-time stress metrics', () => {
      const data = createMockMarketData({ volatility: 0.20 });
      detector.processMarketData(data);

      const metrics = detector.getRealTimeMetrics();
      expect(metrics).toHaveProperty('overallStress');
      expect(metrics).toHaveProperty('volatilityStress');
      expect(metrics).toHaveProperty('liquidityStress');
      expect(metrics).toHaveProperty('volumeStress');
    });

    it('should reset metrics when requested', () => {
      const data = createMockMarketData({ volatility: 0.30 });
      detector.processMarketData(data);

      expect(detector.getCurrentConditions()).toHaveLength(1);

      detector.reset();

      expect(detector.getCurrentConditions()).toHaveLength(0);
      expect(detector.getOverallStressLevel()).toBe(0);
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid market data gracefully', () => {
      const invalidData = createMockMarketData({
        bid: new Decimal('0'),
        ask: new Decimal('0'),
        volume: new Decimal('-100')
      });

      expect(() => detector.processMarketData(invalidData)).not.toThrow();
    });

    it('should handle missing liquidity data', () => {
      const dataWithoutLiquidity = createMockMarketData();
      delete (dataWithoutLiquidity as any).liquidity;

      expect(() => detector.processMarketData(dataWithoutLiquidity)).not.toThrow();
    });

    it('should handle timestamp inconsistencies', () => {
      const futureData = createMockMarketData({
        timestamp: new Date(Date.now() + 86400000)
      });
      const pastData = createMockMarketData({
        timestamp: new Date(Date.now() - 86400000)
      });

      expect(() => {
        detector.processMarketData(futureData);
        detector.processMarketData(pastData);
      }).not.toThrow();
    });
  });

  describe('Configuration Updates', () => {
    it('should update thresholds dynamically', () => {
      detector.updateConfig({
        volatilityThreshold: 0.50,
        liquidityThreshold: 0.10
      });

      const highVolData = createMockMarketData({ volatility: 0.40 });
      detector.processMarketData(highVolData);

      expect(mockEmit).not.toHaveBeenCalledWith('stressEvent', expect.objectContaining({
        type: StressEventType.VOLATILITY_SPIKE
      }));
    });

    it('should validate configuration parameters', () => {
      expect(() => {
        detector.updateConfig({
          volatilityThreshold: -0.1
        });
      }).toThrow();

      expect(() => {
        detector.updateConfig({
          windowSize: 0
        });
      }).toThrow();
    });
  });
});