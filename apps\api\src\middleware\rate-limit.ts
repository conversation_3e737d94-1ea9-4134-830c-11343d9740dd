/**
 * Rate Limiting Middleware
 * 
 * Implements rate limiting to protect API endpoints
 */

import type { Request, Response, NextFunction } from 'express';

interface RateLimitOptions {
  windowMs: number; // Time window in milliseconds
  max: number; // Maximum number of requests per window
  message?: string; // Custom error message
  skipSuccessfulRequests?: boolean; // Don't count successful requests
  skipFailedRequests?: boolean; // Don't count failed requests
}

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

// In-memory store (in production, use Redis or similar)
const requestStore: RateLimitStore = {};

/**
 * Create rate limiting middleware
 */
export function rateLimiter(options: RateLimitOptions) {
  return (req: Request, res: Response, next: NextFunction) => {
    const key = getClientKey(req);
    const now = Date.now();
    const windowStart = now - options.windowMs;
    
    // Clean up old entries
    if (Math.random() < 0.01) { // Cleanup 1% of requests
      cleanupExpiredEntries(windowStart);
    }
    
    // Get or create client record
    let clientRecord = requestStore[key];
    
    if (!clientRecord || clientRecord.resetTime <= now) {
      // Reset window
      clientRecord = {
        count: 0,
        resetTime: now + options.windowMs
      };
      requestStore[key] = clientRecord;
    }
    
    // Check if limit exceeded
    if (clientRecord.count >= options.max) {
      const remainingTime = Math.ceil((clientRecord.resetTime - now) / 1000);
      
      res.status(429).json({
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: options.message || 'Too many requests',
          retryAfter: remainingTime
        }
      });
      return;
    }
    
    // Increment counter
    clientRecord.count++;
    
    // Set rate limit headers
    res.set({
      'X-RateLimit-Limit': options.max.toString(),
      'X-RateLimit-Remaining': Math.max(0, options.max - clientRecord.count).toString(),
      'X-RateLimit-Reset': Math.ceil(clientRecord.resetTime / 1000).toString()
    });
    
    // Continue to next middleware
    next();
  };
}

/**
 * Get unique client identifier
 */
function getClientKey(req: Request): string {
  // In production, you might want to use authenticated user ID when available
  // For now, use IP address
  const forwarded = req.headers['x-forwarded-for'];
  const ip = Array.isArray(forwarded) 
    ? forwarded[0] 
    : forwarded || req.socket.remoteAddress || 'unknown';
    
  return `ip:${ip}`;
}

/**
 * Clean up expired entries from memory store
 */
function cleanupExpiredEntries(cutoffTime: number): void {
  const keysToDelete: string[] = [];
  
  for (const [key, record] of Object.entries(requestStore)) {
    if (record.resetTime <= cutoffTime) {
      keysToDelete.push(key);
    }
  }
  
  keysToDelete.forEach(key => {
    delete requestStore[key];
  });
  
  if (keysToDelete.length > 0) {
    console.log(`🧹 Cleaned up ${keysToDelete.length} expired rate limit entries`);
  }
}

/**
 * Preset rate limiters for common use cases
 */
export const rateLimitPresets = {
  // General API usage
  general: rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000, // 1000 requests per 15 minutes
    message: 'Too many requests from this IP'
  }),
  
  // Authentication endpoints
  auth: rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // 10 attempts per 15 minutes
    message: 'Too many authentication attempts'
  }),
  
  // Password reset endpoints
  passwordReset: rateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 attempts per hour
    message: 'Too many password reset attempts'
  }),
  
  // Data export endpoints
  export: rateLimiter({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 5, // 5 exports per hour
    message: 'Too many export requests'
  }),
  
  // Real-time monitoring endpoints
  monitoring: rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 200, // 200 requests per 15 minutes
    message: 'Too many monitoring requests'
  }),
  
  // Audit trail endpoints (more restrictive)
  audit: rateLimiter({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per 15 minutes
    message: 'Too many audit requests'
  })
};

/**
 * Get current rate limit stats (for debugging)
 */
export function getRateLimitStats(): {
  activeClients: number;
  totalRequests: number;
  oldestEntry: number | null;
} {
  const entries = Object.values(requestStore);
  const now = Date.now();
  
  return {
    activeClients: entries.length,
    totalRequests: entries.reduce((sum, entry) => sum + entry.count, 0),
    oldestEntry: entries.length > 0 
      ? Math.min(...entries.map(entry => entry.resetTime - now))
      : null
  };
}