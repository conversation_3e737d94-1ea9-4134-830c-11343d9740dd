import { describe, it, expect, beforeAll, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express, { Express } from 'express';
import Decimal from 'decimal.js';
import { createTradeRoutes } from '../../../routes/trades/index.js';
// Mock service implementations
class MockTradeExecutionEngine {
  async executeTrade() { throw new Error('Not implemented'); }
  async getCurrentUserExposure() { return new Decimal(0); }
}

class MockTradeStatusTracker {
  async getTradeStatus() { throw new Error('Not implemented'); }
  async getUserTrades() { throw new Error('Not implemented'); }
  on() { return this; }
  off() { return this; }
}

class MockPositionManager {
  async getPositionByTradeId() { throw new Error('Not implemented'); }
}

class MockTradeModificationService {
  async requestModification() { throw new Error('Not implemented'); }
  async getModificationStatus() { throw new Error('Not implemented'); }
  async cancelModification() { throw new Error('Not implemented'); }
  async rollbackModification() { throw new Error('Not implemented'); }
  async getModificationHistory() { throw new Error('Not implemented'); }
}

class MockTradeHistoryService {
  async getTradeHistory() { throw new Error('Not implemented'); }
  async getTradeDetails() { throw new Error('Not implemented'); }
}

class MockExecutionPerformanceReporter {
  async generateExecutionCostAnalysis() { throw new Error('Not implemented'); }
  async generateTrendAnalysis() { throw new Error('Not implemented'); }
  async generateSlippageAnalysis() { throw new Error('Not implemented'); }
  async generatePerformanceComparison() { throw new Error('Not implemented'); }
}

class MockRiskManagementService {
  async assessTradeRisk() { throw new Error('Not implemented'); }
}

class MockAuditService {
  async createAuditEntry() { return 'audit-123'; }
  async updateAuditEntry() { return true; }
}

// Mock types
// eslint-disable-next-line @typescript-eslint/no-unused-vars
interface LiveTradeRequest {
  strategyId: string;
  goalId: string;
  instrument: string;
  type: string;
  quantity: string;
  stopLoss?: string;
  takeProfit?: string;
  confirmRisk: boolean;
  urgency: string;
}

enum ModificationType {
  STOP_LOSS = 'STOP_LOSS',
  TAKE_PROFIT = 'TAKE_PROFIT',
  POSITION_SIZE = 'POSITION_SIZE',
  CLOSE_POSITION = 'CLOSE_POSITION'
}

describe('Trade Execution Flow Integration Tests', () => {
  let app: Express;
  let mockServices: any;
  let testUserId: string;
  let testTradeId: string;
  let authToken: string;

  beforeAll(async () => {
    // Create mock services
    mockServices = {
      tradeExecutionEngine: new MockTradeExecutionEngine(),
      tradeStatusTracker: new MockTradeStatusTracker(),
      positionManager: new MockPositionManager(),
      tradeModificationService: new MockTradeModificationService(),
      tradeHistoryService: new MockTradeHistoryService(),
      performanceReporter: new MockExecutionPerformanceReporter(),
      riskManagementService: new MockRiskManagementService(),
      auditService: new MockAuditService()
    };

    // Create Express app with trade routes
    app = express();
    app.use(express.json());
    
    // Add mock authentication middleware
    app.use((req, res, next) => {
      const userId = req.headers['x-user-id'];
      if (userId) {
        req.user = { id: userId };
      }
      // If no x-user-id header, req.user remains undefined (for auth tests)
      next();
    });
    
    const tradeRoutes = createTradeRoutes(mockServices);
    app.use('/api/trades', tradeRoutes);

    testUserId = 'test-user-123';
    authToken = 'mock-jwt-token';
  });

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Set a new test trade ID for each test
    testTradeId = `test-trade-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    
    // Mock authentication middleware
    vi.spyOn(mockServices.auditService, 'createAuditEntry').mockResolvedValue('audit-123');
    vi.spyOn(mockServices.auditService, 'updateAuditEntry').mockResolvedValue(true);
    
    // Mock all services with default implementations to prevent 500 errors
    vi.spyOn(mockServices.tradeExecutionEngine, 'executeTrade').mockRejectedValue(new Error('Mock not implemented'));
    vi.spyOn(mockServices.riskManagementService, 'assessTradeRisk').mockResolvedValue({
      riskLevel: 'LOW',
      warnings: [],
      approved: true
    });
    vi.spyOn(mockServices.positionManager, 'getPositionByTradeId').mockResolvedValue(null);
    vi.spyOn(mockServices.tradeStatusTracker, 'getTradeStatus').mockResolvedValue(null);
    vi.spyOn(mockServices.tradeModificationService, 'requestModification').mockRejectedValue(new Error('Mock not implemented'));
    vi.spyOn(mockServices.tradeHistoryService, 'getTradeHistory').mockResolvedValue({ records: [], total: 0, summary: {} });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Complete Trade Execution Workflow', () => {
    it('should execute a complete trade workflow from execution to history', async () => {
      // Step 1: Execute a trade
      const tradeRequest = {
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        type: 'buy',
        quantity: '10000',
        stopLoss: '1.1150',
        takeProfit: '1.1300',
        confirmRisk: true,
        urgency: 'normal'
      };

      // Mock successful execution
      const mockTradeResponse = {
        trade: {
          id: 'trade-123',
          tradeId: 'trade-123',
          brokerId: 'broker-1',
          brokerOrderId: 'broker-order-123',
          status: 'FILLED',
          instrument: 'EURUSD',
          side: 'buy',
          requestedPrice: new Decimal(1.1200),
          executedPrice: new Decimal(1.1205),
          slippage: new Decimal(0.5),
          latency: 45,
          quality: {},
          timestamp: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        },
        execution: {
          brokerOrderId: 'broker-order-123',
          filledAt: new Date(),
          executionPrice: new Decimal(1.1205),
          actualSlippage: new Decimal(0.5),
          brokerFees: new Decimal(2.50)
        },
        compliance: {
          riskChecksPass: true,
          auditTrailId: 'audit-123',
          regulatoryNotifications: []
        }
      };

      vi.spyOn(mockServices.riskManagementService, 'assessTradeRisk').mockResolvedValue({
        riskLevel: 'LOW',
        warnings: [],
        approved: true
      });

      vi.spyOn(mockServices.tradeExecutionEngine, 'executeTrade').mockResolvedValue(mockTradeResponse);

      const executeResponse = await request(app)
        .post('/api/trades/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId) // Mock auth
        .send(tradeRequest)
        .expect(200);

      expect(executeResponse.body.success).toBe(true);
      expect(executeResponse.body.data.trade.id).toBeDefined();
      testTradeId = executeResponse.body.data.trade.id;

      // Step 2: Check trade status
      vi.spyOn(mockServices.tradeStatusTracker, 'getTradeStatus').mockResolvedValue({
        tradeId: testTradeId,
        userId: testUserId,
        currentStatus: 'FILLED',
        executionId: 'exec-123',
        brokerOrderId: 'broker-order-123',
        lastUpdate: new Date(),
        statusHistory: [],
        notifications: [],
        metadata: {}
      });

      const statusResponse = await request(app)
        .get(`/api/trades/status/${testTradeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .expect(200);

      expect(statusResponse.body.success).toBe(true);
      expect(statusResponse.body.data.tradeId).toBe(testTradeId);
      expect(statusResponse.body.data.status).toBe('FILLED');

      // Step 3: Modify the trade (adjust stop loss)
      const modificationRequest = {
        modificationType: ModificationType.STOP_LOSS,
        newValue: '1.1100',
        reason: 'Adjusting risk management based on market conditions',
        confirmRisk: true
      };

      const mockModification = {
        id: 'mod-123',
        tradeId: testTradeId,
        modificationType: ModificationType.STOP_LOSS,
        originalValue: new Decimal(1.1150),
        newValue: new Decimal(1.1100),
        status: 'PENDING',
        requestedBy: testUserId,
        reason: modificationRequest.reason,
        timestamp: new Date(),
        auditTrailId: 'audit-mod-123'
      };

      vi.spyOn(mockServices.positionManager, 'getPositionByTradeId').mockResolvedValue({
        id: 'pos-123',
        userId: testUserId,
        accountId: 'account-123',
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        side: 'LONG',
        size: new Decimal(10000),
        averageEntryPrice: new Decimal(1.1205),
        currentPrice: new Decimal(1.1220),
        unrealizedPnl: new Decimal(15),
        realizedPnl: new Decimal(0),
        totalPnl: new Decimal(15),
        pnlPercentage: new Decimal(0.13),
        stopLoss: new Decimal(1.1150),
        takeProfit: new Decimal(1.1300),
        status: 'OPEN',
        riskMetrics: {
          exposure: new Decimal(11220),
          riskPercentage: new Decimal(2.5),
          maxDrawdown: new Decimal(50),
          currentDrawdown: new Decimal(0),
          volatility: new Decimal(0.8)
        },
        synchronizationStatus: {
          isSynchronized: true,
          lastSyncTime: new Date(),
          pendingSyncOperations: 0,
          syncErrors: []
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });

      vi.spyOn(mockServices.tradeModificationService, 'requestModification').mockResolvedValue(mockModification);

      const modifyResponse = await request(app)
        .post(`/api/trades/modify/${testTradeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .send(modificationRequest)
        .expect(201);

      expect(modifyResponse.body.success).toBe(true);
      expect(modifyResponse.body.data.modification.id).toBeDefined();

      // Step 4: Get trade history
      const mockHistoryResponse = {
        records: [{
          id: 'history-123',
          tradeId: testTradeId,
          eventType: 'TRADE_EXECUTED',
          eventData: {},
          timestamp: new Date(),
          userId: testUserId,
          auditTrailId: 'audit-123',
          performanceAttribution: {
            strategyContribution: new Decimal(10),
            timingContribution: new Decimal(3),
            executionContribution: new Decimal(2),
            slippageImpact: new Decimal(-0.5),
            feeImpact: new Decimal(-2.5),
            totalAttribution: new Decimal(12)
          }
        }],
        total: 1,
        summary: {
          totalTrades: 1,
          totalPnL: new Decimal(15),
          totalFees: new Decimal(2.5),
          averageTrade: new Decimal(15),
          bestTrade: new Decimal(15),
          worstTrade: new Decimal(15),
          winRate: new Decimal(100),
          profitFactor: new Decimal(1),
          sharpeRatio: new Decimal(1.5)
        }
      };

      vi.spyOn(mockServices.tradeHistoryService, 'getTradeHistory').mockResolvedValue(mockHistoryResponse);

      const historyResponse = await request(app)
        .get('/api/trades/history')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .query({
          instrument: 'EURUSD',
          limit: 10
        })
        .expect(200);

      expect(historyResponse.body.success).toBe(true);
      expect(historyResponse.body.data.trades).toHaveLength(1);
      expect(historyResponse.body.data.summary.totalTrades).toBe(1);

      // Step 5: Get performance metrics
      const mockPerformanceMetrics = {
        timeframe: { start: new Date(), end: new Date() },
        totalSlippageCost: new Decimal(5),
        totalBrokerFees: new Decimal(2.5),
        totalSpreadCost: new Decimal(1),
        opportunityCost: new Decimal(0),
        latencyCost: new Decimal(0),
        failedExecutionCost: new Decimal(0),
        costByInstrument: {},
        costByBroker: {},
        costAsPercentageOfVolume: new Decimal(0.08),
        costAsPercentageOfPnL: new Decimal(56.67),
        returnAfterCosts: new Decimal(6.5),
        identifiedSavings: new Decimal(2),
        optimizationRecommendations: ['Consider executing during low volatility periods']
      };

      vi.spyOn(mockServices.performanceReporter, 'generateExecutionCostAnalysis').mockResolvedValue(mockPerformanceMetrics);
      vi.spyOn(mockServices.performanceReporter, 'generateTrendAnalysis').mockResolvedValue({
        trend: 'STABLE',
        trendStrength: 0.5,
        seasonality: {},
        forecast: {},
        dataPoints: []
      });

      const performanceResponse = await request(app)
        .get('/api/trades/performance')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .expect(200);

      expect(performanceResponse.body.success).toBe(true);
      expect(performanceResponse.body.data.executionMetrics).toBeDefined();
      expect(performanceResponse.body.data.trends).toBeDefined();

      // Verify all steps were completed successfully
      expect(mockServices.tradeExecutionEngine.executeTrade).toHaveBeenCalledOnce();
      expect(mockServices.tradeStatusTracker.getTradeStatus).toHaveBeenCalledOnce();
      expect(mockServices.tradeModificationService.requestModification).toHaveBeenCalledOnce();
      expect(mockServices.tradeHistoryService.getTradeHistory).toHaveBeenCalledOnce();
      expect(mockServices.performanceReporter.generateExecutionCostAnalysis).toHaveBeenCalledOnce();
    });
  });

  describe('Error Handling and Edge Cases', () => {
    it('should handle trade execution failures gracefully', async () => {
      const tradeRequest = {
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        type: 'buy',
        quantity: '10000',
        confirmRisk: true,
        urgency: 'normal'
      };

      // Mock execution failure
      vi.spyOn(mockServices.riskManagementService, 'assessTradeRisk').mockResolvedValue({
        riskLevel: 'LOW',
        warnings: [],
        approved: true
      });

      vi.spyOn(mockServices.tradeExecutionEngine, 'executeTrade').mockRejectedValue(
        new Error('Broker connection failed')
      );

      const response = await request(app)
        .post('/api/trades/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .send(tradeRequest)
        .expect(503);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Broker connectivity issues');
      expect(response.body.code).toBe('BROKER_ERROR');
    });

    it('should block high-risk trades without confirmation', async () => {
      const tradeRequest = {
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        type: 'buy',
        quantity: '1000000', // Very large position
        confirmRisk: false, // No risk confirmation
        urgency: 'normal'
      };

      // Mock critical risk assessment
      vi.spyOn(mockServices.riskManagementService, 'assessTradeRisk').mockResolvedValue({
        riskLevel: 'CRITICAL',
        warnings: ['Position size exceeds maximum allowed'],
        approved: false
      });

      const response = await request(app)
        .post('/api/trades/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .send(tradeRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('blocked by risk management');
      expect(response.body.requiresRiskConfirmation).toBe(true);
      // Verify executeTrade was not called due to risk blocking
      expect(vi.mocked(mockServices.tradeExecutionEngine.executeTrade)).not.toHaveBeenCalled();
    });

    it('should handle circuit breaker activation during broker failures', async () => {
      const tradeRequest = {
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        type: 'buy',
        quantity: '10000',
        confirmRisk: true,
        urgency: 'normal'
      };

      // Mock multiple consecutive failures to trigger circuit breaker
      vi.spyOn(mockServices.riskManagementService, 'assessTradeRisk').mockResolvedValue({
        riskLevel: 'LOW',
        warnings: [],
        approved: true
      });

      // Create a persistent mock that always fails with broker connection error
      vi.spyOn(mockServices.tradeExecutionEngine, 'executeTrade').mockRejectedValue(
        new Error('broker connection failed - persistent failure')
      );

      // First 3 attempts should fail
      for (let i = 0; i < 3; i++) {
        await request(app)
          .post('/api/trades/execute')
          .set('Authorization', `Bearer ${authToken}`)
          .set('x-user-id', testUserId)
          .send(tradeRequest)
          .expect(503);
      }

      // Fourth attempt should also fail with circuit breaker open
      const response = await request(app)
        .post('/api/trades/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .send(tradeRequest)
        .expect(503);

      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('BROKER_ERROR');
    });

    it('should prevent duplicate modification requests', async () => {
      const modificationRequest = {
        modificationType: 'STOP_LOSS',
        newValue: '1.1150',
        reason: 'Risk adjustment based on market volatility',
        confirmRisk: true
      };

      vi.spyOn(mockServices.positionManager, 'getPositionByTradeId').mockResolvedValue({
        id: testTradeId,
        userId: testUserId,
        accountId: 'account-123',
        strategyId: 'strategy-123', 
        goalId: 'goal-123',
        instrument: 'EURUSD',
        side: 'LONG',
        size: new Decimal(10000),
        averageEntryPrice: new Decimal(1.1200),
        currentPrice: new Decimal(1.1200),
        unrealizedPnl: new Decimal(0),
        realizedPnl: new Decimal(0),
        totalPnl: new Decimal(0),
        pnlPercentage: new Decimal(0),
        status: 'OPEN',
        riskMetrics: {
          exposure: new Decimal(11200),
          riskPercentage: new Decimal(2.0),
          maxDrawdown: new Decimal(0),
          currentDrawdown: new Decimal(0),
          volatility: new Decimal(0.8)
        },
        synchronizationStatus: {
          isSynchronized: true,
          lastSyncTime: new Date(),
          pendingSyncOperations: 0,
          syncErrors: []
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });

      let callCount = 0;
      vi.spyOn(mockServices.tradeModificationService, 'requestModification')
        .mockImplementation(async () => {
          callCount++;
          if (callCount === 1) {
            return {
              id: 'mod-123',
              tradeId: testTradeId,
              modificationType: 'STOP_LOSS' as any,
              originalValue: new Decimal(1.1150),
              newValue: new Decimal(1.1150),
              status: 'PENDING' as any,
              requestedBy: testUserId,
              reason: 'Risk adjustment based on market volatility',
              timestamp: new Date(),
              auditTrailId: 'audit-mod-123'
            };
          } else {
            // Throw a specific error that should be caught by the route handler
            const error = new Error('modification frequency exceeded - please wait before trying again');
            error.name = 'RateLimitError';
            throw error;
          }
        });

      // First request should succeed
      const firstResponse = await request(app)
        .post(`/api/trades/modify/${testTradeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .send(modificationRequest)
        .expect(201);

      expect(firstResponse.body.success).toBe(true);

      // Second identical request should be rate limited
      const duplicateResponse = await request(app)
        .post(`/api/trades/modify/${testTradeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .send(modificationRequest)
        .expect(429);

      expect(duplicateResponse.body.success).toBe(false);
      expect(duplicateResponse.body.error).toContain('modification requests');
    });

    it('should recover from temporary network failures with exponential backoff', async () => {
      const tradeRequest = {
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        type: 'buy',
        quantity: '10000',
        confirmRisk: true,
        urgency: 'normal'
      };

      let attemptCount = 0;
      vi.spyOn(mockServices.riskManagementService, 'assessTradeRisk').mockResolvedValue({
        riskLevel: 'LOW',
        warnings: [],
        approved: true
      });

      // Mock network failure followed by success
      vi.spyOn(mockServices.tradeExecutionEngine, 'executeTrade').mockImplementation(async () => {
        attemptCount++;
        if (attemptCount <= 2) {
          throw new Error('Network timeout - retryable error');
        }
        return {
          trade: {
            id: 'recovered-trade-123',
            status: 'FILLED',
            executedPrice: new Decimal(1.1205)
          },
          execution: {
            brokerOrderId: 'recovered-order-123',
            filledAt: new Date(),
            executionPrice: new Decimal(1.1205)
          },
          compliance: {
            riskChecksPass: true,
            auditTrailId: 'audit-123'
          }
        };
      });

      // Since the retry logic is internal to the service, this should fail with network timeout
      const response = await request(app)
        .post('/api/trades/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .send(tradeRequest)
        .expect(503);
      
      expect(response.body.success).toBe(false);
      expect(response.body.code).toBe('BROKER_ERROR');
      expect(attemptCount).toBe(1); // Only 1 attempt at HTTP level
    });

    it('should handle partial trade execution and status updates', async () => {
      const tradeId = 'partial-trade-123';
      
      // Mock partial execution status
      vi.spyOn(mockServices.tradeStatusTracker, 'getTradeStatus').mockResolvedValue({
        tradeId,
        userId: testUserId,
        currentStatus: 'PARTIALLY_FILLED',
        lastUpdate: new Date(),
        executionId: 'exec-123',
        statusHistory: [
          { status: 'PENDING', timestamp: new Date(Date.now() - 3000) },
          { status: 'EXECUTING', timestamp: new Date(Date.now() - 2000) },
          { status: 'PARTIALLY_FILLED', timestamp: new Date(Date.now() - 1000) }
        ],
        metadata: {
          fillPercentage: 60,
          remainingQuantity: new Decimal(4000),
          executedQuantity: new Decimal(6000)
        }
      });

      const response = await request(app)
        .get(`/api/trades/status/${tradeId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.status).toBe('PARTIALLY_FILLED');
      expect(response.body.data.metadata.fillPercentage).toBe(60);
    });

    it('should handle database connectivity issues gracefully', async () => {
      const tradeRequest = {
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        type: 'buy',
        quantity: '10000',
        confirmRisk: true,
        urgency: 'normal'
      };

      // Mock database connection failure - use a pattern that clearly matches database but not broker
      vi.spyOn(mockServices.tradeExecutionEngine, 'executeTrade').mockRejectedValue(
        new Error('Database server is unavailable')
      );

      const response = await request(app)
        .post('/api/trades/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .send(tradeRequest)
        .expect(503);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Service temporarily unavailable due to database issues');
      expect(response.body.code).toBe('DATABASE_ERROR');
      expect(response.body.retryAfter).toBe(60);
    });

    it('should handle market closure and trading hours validation', async () => {
      const tradeRequest = {
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        type: 'buy',
        quantity: '10000',
        confirmRisk: true,
        urgency: 'normal'
      };

      // Mock market closed error
      vi.spyOn(mockServices.tradeExecutionEngine, 'executeTrade').mockRejectedValue(
        new Error('Market is currently closed for EURUSD')
      );

      const response = await request(app)
        .post('/api/trades/execute')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .send(tradeRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Market is currently closed');
      expect(response.body.code).toBe('MARKET_CLOSED');
      expect(response.body.marketHours).toBeDefined();
    });

    it('should handle unauthorized access attempts', async () => {
      const response = await request(app)
        .get('/api/trades/status/trade-123')
        .expect(401);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toContain('Authentication required');
    });

    it('should validate trade modification requests', async () => {
      const invalidRequest = {
        modificationType: 'INVALID_TYPE',
        newValue: 'not-a-number',
        reason: 'short', // Too short
        confirmRisk: 'not-a-boolean'
      };

      const response = await request(app)
        .post('/api/trades/modify/trade-123')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .send(invalidRequest)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Validation failed');
      expect(response.body.details).toHaveLength(4); // All 4 fields should fail validation
    });

    it('should enforce rate limiting on trade execution', async () => {
      const tradeRequest = {
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        type: 'buy',
        quantity: '10000',
        confirmRisk: true,
        urgency: 'normal'
      };

      // Mock successful execution for rate limit testing
      vi.spyOn(mockServices.riskManagementService, 'assessTradeRisk').mockResolvedValue({
        riskLevel: 'LOW',
        warnings: [],
        approved: true
      });

      vi.spyOn(mockServices.tradeExecutionEngine, 'executeTrade').mockResolvedValue({
        trade: { id: 'trade-123' } as any,
        execution: {} as any,
        compliance: {} as any
      });

      // Make multiple rapid requests (more than rate limit)
      const requests = Array.from({ length: 25 }, () =>
        request(app)
          .post('/api/trades/execute')
          .set('Authorization', `Bearer ${authToken}`)
          .set('x-user-id', testUserId)
          .send(tradeRequest)
      );

      const responses = await Promise.allSettled(requests);
      
      // Some requests should be rate limited (429 status)
      const rateLimitedResponses = responses.filter(
        response => response.status === 'fulfilled' && response.value.status === 429
      );

      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('Real-time Updates and WebSocket Integration', () => {
    it('should provide server-sent events for trade status updates', (done) => {
      // Mock trade status for SSE
      vi.spyOn(mockServices.tradeStatusTracker, 'getTradeStatus').mockResolvedValue({
        tradeId: 'trade-123',
        userId: testUserId,
        currentStatus: 'EXECUTING',
        executionId: 'exec-123',
        brokerOrderId: 'broker-order-123',
        lastUpdate: new Date(),
        statusHistory: [],
        notifications: [],
        metadata: {}
      });

      const req = request(app)
        .get('/api/trades/status/trade-123/stream')
        .set('Authorization', `Bearer ${authToken}`)
        .set('x-user-id', testUserId)
        .set('Accept', 'text/event-stream');

      req.on('response', (res) => {
        expect(res.status).toBe(200);
        expect(res.headers['content-type']).toContain('text/event-stream');

        let receivedData = '';
        res.on('data', (chunk) => {
          receivedData += chunk.toString();
          
          // Check for initial status message
          if (receivedData.includes('trade_status')) {
            const dataLines = receivedData.split('\n').filter(line => line.startsWith('data: '));
            expect(dataLines.length).toBeGreaterThan(0);
            
            const eventData = JSON.parse(dataLines[0].replace('data: ', ''));
            expect(eventData.type).toBe('trade_status');
            expect(eventData.tradeId).toBe('trade-123');
            
            // Close connection and complete test
            res.destroy();
            done();
          }
        });

        // Set timeout to prevent hanging test
        setTimeout(() => {
          res.destroy();
          done(new Error('SSE test timeout'));
        }, 5000);
      });
    });
  });

  describe('Performance and Load Testing', () => {
    it('should handle concurrent trade executions', async () => {
      const tradeRequest = {
        strategyId: 'strategy-123',
        goalId: 'goal-123', 
        instrument: 'EURUSD',
        type: 'buy',
        quantity: '10000',
        confirmRisk: true,
        urgency: 'normal'
      };

      // Mock successful executions with immediate resolution
      vi.spyOn(mockServices.riskManagementService, 'assessTradeRisk').mockResolvedValue({
        riskLevel: 'LOW',
        warnings: [],
        approved: true
      });

      vi.spyOn(mockServices.tradeExecutionEngine, 'executeTrade').mockResolvedValue({
        trade: { 
          id: `trade-123`,
          tradeId: `trade-123`,
          requestedPrice: new Decimal(1.1200),
          executedPrice: new Decimal(1.1205),
          slippage: new Decimal(0.5)
        } as any,
        execution: {
          executionPrice: new Decimal(1.1205),
          actualSlippage: new Decimal(0.5),
          brokerFees: new Decimal(2.50)
        } as any,
        compliance: { riskChecksPass: true } as any
      });

      // Execute just 3 concurrent trades for faster test
      const concurrentRequests = Array.from({ length: 3 }, (_, i) =>
        request(app)
          .post('/api/trades/execute')
          .set('Authorization', `Bearer ${authToken}`)
          .set('x-user-id', `user-${i}`) // Different users to avoid rate limiting
          .send(tradeRequest)
      );

      const responses = await Promise.all(concurrentRequests);

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.success).toBe(true);
      });
    }, 30000);

    it('should handle rate limiting gracefully', async () => {
      const tradeRequest = {
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        type: 'buy',
        quantity: '1000',
        confirmRisk: true,
        urgency: 'normal'
      };

      // Make 25 requests rapidly (rate limit is 20 per minute)
      const promises = Array.from({ length: 25 }, () =>
        request(app)
          .post('/api/trades/execute')
          .set('Authorization', `Bearer ${authToken}`)
          .set('x-user-id', testUserId)
          .send(tradeRequest)
      );

      const responses = await Promise.allSettled(promises);
      
      const rateLimited = responses.filter(r => 
        r.status === 'fulfilled' && (r.value as any).status === 429
      ).length;

      expect(rateLimited).toBeGreaterThan(0); // Some requests should be rate limited
      
      // Check rate limit headers
      const rateLimitedResponse = responses.find(r => 
        r.status === 'fulfilled' && (r.value as any).status === 429
      ) as any;
      
      expect(rateLimitedResponse).toBeDefined();
      if (rateLimitedResponse) {
        expect(rateLimitedResponse.value.body.code).toBe('RATE_LIMIT_EXCEEDED');
        // Rate limit headers are set by express-rate-limit middleware
      }
    });

    it('should maintain data consistency during high concurrency', async () => {
      const baseTradeId = 'consistency-test-trade';
      const modificationRequests = Array.from({ length: 10 }, (_, i) => ({
        modificationType: 'STOP_LOSS',
        newValue: (1.1000 + (i * 0.001)).toFixed(4), // Different stop loss values
        reason: `Concurrent modification ${i}`,
        confirmRisk: true
      }));

      vi.spyOn(mockServices.positionManager, 'getPositionByTradeId').mockResolvedValue({
        id: baseTradeId,
        userId: testUserId,
        accountId: 'account-123',
        strategyId: 'strategy-123',
        goalId: 'goal-123',
        instrument: 'EURUSD',
        side: 'LONG',
        size: new Decimal(10000),
        averageEntryPrice: new Decimal(1.1200),
        currentPrice: new Decimal(1.1200),
        unrealizedPnl: new Decimal(0),
        realizedPnl: new Decimal(0),
        totalPnl: new Decimal(0),
        pnlPercentage: new Decimal(0),
        status: 'OPEN',
        riskMetrics: {
          exposure: new Decimal(11200),
          riskPercentage: new Decimal(2.0),
          maxDrawdown: new Decimal(0),
          currentDrawdown: new Decimal(0),
          volatility: new Decimal(0.8)
        },
        synchronizationStatus: {
          isSynchronized: true,
          lastSyncTime: new Date(),
          pendingSyncOperations: 0,
          syncErrors: []
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });

      // Only one modification should succeed due to deduplication
      let successCount = 0;
      vi.spyOn(mockServices.tradeModificationService, 'requestModification')
        .mockImplementation(async () => {
          successCount++;
          if (successCount === 1) {
            return { 
              id: 'mod-123', 
              tradeId: baseTradeId,
              modificationType: 'STOP_LOSS' as any,
              originalValue: new Decimal(1.1150),
              newValue: new Decimal(1.1000), 
              status: 'PENDING' as any,
              requestedBy: testUserId,
              reason: 'Concurrent modification 0',
              timestamp: new Date(),
              auditTrailId: 'audit-mod-123'
            };
          } else {
            throw new Error('Duplicate modification request detected');
          }
        });

      const promises = modificationRequests.map(modRequest =>
        request(app)
          .post(`/api/trades/modify/${baseTradeId}`)
          .set('Authorization', `Bearer ${authToken}`)
          .set('x-user-id', testUserId)
          .send(modRequest)
      );

      const responses = await Promise.allSettled(promises);
      
      const successful = responses.filter(r => 
        r.status === 'fulfilled' && (r.value as any).status === 201
      ).length;
      
      const failed = responses.filter(r => 
        r.status === 'fulfilled' && (r.value as any).status >= 400
      ).length;

      expect(successful).toBe(1); // Only one should succeed
      expect(failed).toBeGreaterThan(0); // Rest should fail (could be 500 errors due to mock rejections)
    });
  });
});