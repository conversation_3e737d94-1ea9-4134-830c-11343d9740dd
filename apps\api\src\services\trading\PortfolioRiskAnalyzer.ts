/**
 * Portfolio Risk Analyzer Service
 * 
 * Implements correlation analysis across all open positions, Portfolio Value-at-Risk (VaR) 
 * calculation using historical simulation, and concentration risk monitoring.
 * 
 * @version 1.0
 * <AUTHOR> (Dev Agent)
 */

import Decimal from 'decimal.js';
import { EventEmitter } from 'events';

// Portfolio Risk Types
export interface Position {
  id: string;
  symbol: string;
  size: Decimal.Instance;
  entryPrice: Decimal.Instance;
  currentPrice: Decimal.Instance;
  unrealizedPnL: Decimal.Instance;
  weight: number; // Portfolio weight percentage
  sector?: string;
  assetClass: 'forex' | 'commodity' | 'crypto' | 'stock';
}

export interface PortfolioRiskMetrics {
  totalValue: Decimal.Instance;
  totalUnrealizedPnL: Decimal.Instance;
  valueAtRisk95: Decimal.Instance; // 95% confidence VaR
  valueAtRisk99: Decimal.Instance; // 99% confidence VaR
  expectedShortfall: Decimal.Instance; // Conditional VaR (CVaR)
  portfolioVolatility: number;
  maxDrawdown: Decimal.Instance;
  correlationRisk: number; // 0-1 scale
  concentrationRisk: number; // 0-1 scale
  diversificationRatio: number;
  riskContributions: Map<string, number>; // Risk contribution by position
}

export interface ConcentrationLimits {
  maxSinglePositionWeight: number; // Default: 10%
  maxSectorWeight: number; // Default: 25%
  maxAssetClassWeight: number; // Default: 40%
  maxCorrelationThreshold: number; // Default: 0.7
}

export interface VaRCalculationParams {
  confidenceLevel: number; // 0.95 or 0.99
  historicalPeriod: number; // Days to look back
  simulationCount: number; // Monte Carlo simulations
  holdingPeriod: number; // Days (default: 1)
}

export interface RiskAlert {
  type: 'concentration' | 'correlation' | 'var_breach' | 'drawdown' | 'volatility';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  affectedPositions: string[];
  recommendedAction: string;
  timestamp: Date;
}

export interface HistoricalData {
  date: Date;
  symbol: string;
  price: Decimal.Instance;
  returns: number;
}

/**
 * Portfolio Risk Analyzer Service
 * Provides comprehensive portfolio-level risk analysis and monitoring
 */
export class PortfolioRiskAnalyzer extends EventEmitter {
  private readonly concentrationLimits: ConcentrationLimits;
  private readonly varParams: VaRCalculationParams;
  private historicalData: Map<string, HistoricalData[]> = new Map();
  private lastAnalysis: PortfolioRiskMetrics | null = null;
  private activeAlerts: RiskAlert[] = [];

  constructor(
    concentrationLimits?: Partial<ConcentrationLimits>,
    varParams?: Partial<VaRCalculationParams>
  ) {
    super();
    
    this.concentrationLimits = {
      maxSinglePositionWeight: 10,
      maxSectorWeight: 25,
      maxAssetClassWeight: 40,
      maxCorrelationThreshold: 0.7,
      ...concentrationLimits
    };

    this.varParams = {
      confidenceLevel: 0.95,
      historicalPeriod: 252, // 1 year of trading days
      simulationCount: 10000,
      holdingPeriod: 1,
      ...varParams
    };
  }

  /**
   * Analyze portfolio risk metrics
   */
  public analyzePortfolioRisk(positions: Position[]): PortfolioRiskMetrics {
    if (positions.length === 0) {
      return this.getEmptyRiskMetrics();
    }

    const totalValue = this.calculateTotalValue(positions);
    const correlationMatrix = this.calculateCorrelationMatrix(positions);
    const varMetrics = this.calculateValueAtRisk(positions);
    const concentrationMetrics = this.analyzeConcentrationRisk(positions);
    const riskContributions = this.calculateRiskContributions(positions, correlationMatrix);

    const metrics: PortfolioRiskMetrics = {
      totalValue,
      totalUnrealizedPnL: positions.reduce((sum, pos) => sum.add(pos.unrealizedPnL), new Decimal(0)),
      valueAtRisk95: varMetrics.var95,
      valueAtRisk99: varMetrics.var99,
      expectedShortfall: varMetrics.expectedShortfall,
      portfolioVolatility: this.calculatePortfolioVolatility(positions, correlationMatrix),
      maxDrawdown: this.calculateMaxDrawdown(positions),
      correlationRisk: this.calculateCorrelationRisk(correlationMatrix),
      concentrationRisk: concentrationMetrics.concentrationScore,
      diversificationRatio: this.calculateDiversificationRatio(positions, correlationMatrix),
      riskContributions
    };

    this.lastAnalysis = metrics;
    this.checkRiskAlerts(positions, metrics);
    
    this.emit('riskAnalysisComplete', {
      metrics,
      positions: positions.length,
      alerts: this.activeAlerts.length
    });

    return metrics;
  }

  /**
   * Calculate correlation matrix for all positions
   */
  public calculateCorrelationMatrix(positions: Position[]): number[][] {
    const symbols = positions.map(p => p.symbol);
    const matrix: number[][] = [];

    for (let i = 0; i < symbols.length; i++) {
      matrix[i] = [];
      for (let j = 0; j < symbols.length; j++) {
        if (i === j) {
          matrix[i][j] = 1.0;
        } else {
          matrix[i][j] = this.calculatePairwiseCorrelation(symbols[i], symbols[j]);
        }
      }
    }

    return matrix;
  }

  /**
   * Calculate Value at Risk using historical simulation
   */
  public calculateValueAtRisk(positions: Position[]): {
    var95: Decimal.Instance;
    var99: Decimal.Instance;
    expectedShortfall: Decimal.Instance;
  } {
    const portfolioReturns = this.simulatePortfolioReturns(positions);
    portfolioReturns.sort((a, b) => a - b); // Sort ascending for percentiles

    const var95Index = Math.floor(portfolioReturns.length * (1 - this.varParams.confidenceLevel));
    const var99Index = Math.floor(portfolioReturns.length * 0.01);

    const var95 = new Decimal(portfolioReturns[var95Index] || 0);
    const var99 = new Decimal(portfolioReturns[var99Index] || 0);

    // Expected Shortfall (Conditional VaR) - average of losses beyond VaR
    const tailLosses = portfolioReturns.slice(0, var95Index);
    const expectedShortfall = tailLosses.length > 0 
      ? new Decimal(tailLosses.reduce((sum, loss) => sum + loss, 0) / tailLosses.length)
      : new Decimal(0);

    return { var95, var99, expectedShortfall };
  }

  /**
   * Analyze concentration risk across positions
   */
  public analyzeConcentrationRisk(positions: Position[]): {
    concentrationScore: number;
    violations: string[];
    sectorWeights: Map<string, number>;
    assetClassWeights: Map<string, number>;
  } {
    const violations: string[] = [];
    const totalValue = this.calculateTotalValue(positions);
    
    if (positions.length === 0) {
      return {
        concentrationScore: 0,
        violations: [],
        sectorWeights: new Map(),
        assetClassWeights: new Map()
      };
    }
    
    // Single position concentration
    const maxPositionWeight = Math.max(...positions.map(p => 
      p.size.mul(p.currentPrice).div(totalValue).mul(100).toNumber()
    ));
    
    if (maxPositionWeight > this.concentrationLimits.maxSinglePositionWeight) {
      violations.push(`Single position exceeds ${this.concentrationLimits.maxSinglePositionWeight}% limit`);
    }

    // Sector concentration
    const sectorWeights = this.calculateSectorWeights(positions, totalValue);
    for (const [sector, weight] of sectorWeights) {
      if (weight > this.concentrationLimits.maxSectorWeight) {
        violations.push(`Sector ${sector} exceeds ${this.concentrationLimits.maxSectorWeight}% limit`);
      }
    }

    // Asset class concentration
    const assetClassWeights = this.calculateAssetClassWeights(positions, totalValue);
    for (const [assetClass, weight] of assetClassWeights) {
      if (weight > this.concentrationLimits.maxAssetClassWeight) {
        violations.push(`Asset class ${assetClass} exceeds ${this.concentrationLimits.maxAssetClassWeight}% limit`);
      }
    }

    const concentrationScore = Math.min(1.0, violations.length * 0.2 + maxPositionWeight / 100);

    return { concentrationScore, violations, sectorWeights, assetClassWeights };
  }

  /**
   * Update historical price data for correlation calculations
   */
  public updateHistoricalData(symbol: string, data: HistoricalData[]): void {
    this.historicalData.set(symbol, data);
    this.emit('historicalDataUpdated', { symbol, dataPoints: data.length });
  }

  /**
   * Get current risk alerts
   */
  public getRiskAlerts(): RiskAlert[] {
    return [...this.activeAlerts];
  }

  /**
   * Clear risk alerts
   */
  public clearAlerts(alertTypes?: RiskAlert['type'][]): void {
    if (alertTypes) {
      this.activeAlerts = this.activeAlerts.filter(alert => !alertTypes.includes(alert.type));
    } else {
      this.activeAlerts = [];
    }
    
    this.emit('alertsCleared', { clearedTypes: alertTypes });
  }

  /**
   * Get last analysis results
   */
  public getLastAnalysis(): PortfolioRiskMetrics | null {
    return this.lastAnalysis;
  }

  // Private helper methods

  private calculateTotalValue(positions: Position[]): Decimal {
    return positions.reduce((sum, pos) => 
      sum.add(pos.size.mul(pos.currentPrice)), new Decimal(0)
    );
  }

  private calculatePairwiseCorrelation(symbol1: string, symbol2: string): number {
    const data1 = this.historicalData.get(symbol1);
    const data2 = this.historicalData.get(symbol2);

    if (!data1 || !data2 || data1.length < 30 || data2.length < 30) {
      return 0.3; // Default moderate correlation if insufficient data
    }

    // Calculate correlation using returns
    const returns1 = data1.map(d => d.returns);
    const returns2 = data2.map(d => d.returns);

    return this.calculateCorrelationCoefficient(returns1, returns2);
  }

  private calculateCorrelationCoefficient(x: number[], y: number[]): number {
    const n = Math.min(x.length, y.length);
    if (n < 2) return 0;

    const meanX = x.slice(0, n).reduce((sum, val) => sum + val, 0) / n;
    const meanY = y.slice(0, n).reduce((sum, val) => sum + val, 0) / n;

    let numerator = 0;
    let sumSquareX = 0;
    let sumSquareY = 0;

    for (let i = 0; i < n; i++) {
      const deltaX = x[i] - meanX;
      const deltaY = y[i] - meanY;
      numerator += deltaX * deltaY;
      sumSquareX += deltaX * deltaX;
      sumSquareY += deltaY * deltaY;
    }

    const denominator = Math.sqrt(sumSquareX * sumSquareY);
    return denominator === 0 ? 0 : Math.max(-1, Math.min(1, numerator / denominator));
  }

  private simulatePortfolioReturns(positions: Position[]): number[] {
    const returns: number[] = [];
    const totalValue = this.calculateTotalValue(positions);

    // Use historical simulation if data available, otherwise Monte Carlo
    if (this.hasHistoricalData(positions)) {
      return this.historicalSimulation(positions, totalValue);
    } else {
      return this.monteCarloSimulation(positions, totalValue);
    }
  }

  private hasHistoricalData(positions: Position[]): boolean {
    return positions.every(pos => {
      const data = this.historicalData.get(pos.symbol);
      return data && data.length >= 30;
    });
  }

  private historicalSimulation(positions: Position[], totalValue: Decimal.Instance): number[] {
    const returns: number[] = [];
    const maxPeriod = Math.min(
      this.varParams.historicalPeriod,
      Math.min(...positions.map(pos => this.historicalData.get(pos.symbol)?.length || 0))
    );

    for (let i = 0; i < maxPeriod - 1; i++) {
      let portfolioReturn = 0;
      
      for (const position of positions) {
        const data = this.historicalData.get(position.symbol);
        if (data && i + 1 < data.length) {
          const positionReturn = data[i].returns;
          const weight = position.size.mul(position.currentPrice).div(totalValue).toNumber();
          portfolioReturn += positionReturn * weight;
        }
      }
      
      returns.push(portfolioReturn);
    }

    return returns;
  }

  private monteCarloSimulation(positions: Position[], totalValue: Decimal.Instance): number[] {
    const returns: number[] = [];
    
    for (let i = 0; i < this.varParams.simulationCount; i++) {
      let portfolioReturn = 0;
      
      for (const position of positions) {
        // Simple normal distribution assumption (mean=0, volatility based on asset class)
        const volatility = this.getAssetClassVolatility(position.assetClass);
        const randomReturn = this.normalRandom() * volatility / Math.sqrt(252); // Daily volatility
        const weight = position.size.mul(position.currentPrice).div(totalValue).toNumber();
        portfolioReturn += randomReturn * weight;
      }
      
      returns.push(portfolioReturn);
    }

    return returns;
  }

  private getAssetClassVolatility(assetClass: string): number {
    const volatilities = {
      forex: 0.12,     // 12% annual volatility
      commodity: 0.20,  // 20% annual volatility
      crypto: 0.60,     // 60% annual volatility
      stock: 0.18       // 18% annual volatility
    };
    return volatilities[assetClass as keyof typeof volatilities] || 0.15;
  }

  private normalRandom(): number {
    // Box-Muller transform for normal distribution
    const u1 = Math.random();
    const u2 = Math.random();
    return Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
  }

  private calculatePortfolioVolatility(positions: Position[], correlationMatrix: number[][]): number {
    if (positions.length === 0) return 0;

    const weights = positions.map(pos => {
      const totalValue = this.calculateTotalValue(positions);
      return pos.size.mul(pos.currentPrice).div(totalValue).toNumber();
    });

    const volatilities = positions.map(pos => this.getAssetClassVolatility(pos.assetClass));

    let portfolioVariance = 0;
    for (let i = 0; i < positions.length; i++) {
      for (let j = 0; j < positions.length; j++) {
        portfolioVariance += weights[i] * weights[j] * volatilities[i] * volatilities[j] * correlationMatrix[i][j];
      }
    }

    return Math.sqrt(Math.max(0, portfolioVariance));
  }

  private calculateMaxDrawdown(positions: Position[]): Decimal {
    // Simplified drawdown calculation based on unrealized P&L
    const totalUnrealized = positions.reduce((sum, pos) => sum.add(pos.unrealizedPnL), new Decimal(0));
    const totalValue = this.calculateTotalValue(positions);
    
    if (totalValue.isZero()) return new Decimal(0);
    
    return totalUnrealized.isNegative() ? totalUnrealized.div(totalValue).abs() : new Decimal(0);
  }

  private calculateCorrelationRisk(correlationMatrix: number[][]): number {
    if (correlationMatrix.length === 0) return 0;

    let sumCorrelations = 0;
    let count = 0;

    for (let i = 0; i < correlationMatrix.length; i++) {
      for (let j = i + 1; j < correlationMatrix.length; j++) {
        sumCorrelations += Math.abs(correlationMatrix[i][j]);
        count++;
      }
    }

    return count > 0 ? sumCorrelations / count : 0;
  }

  private calculateDiversificationRatio(positions: Position[], correlationMatrix: number[][]): number {
    if (positions.length <= 1) return 1;

    const portfolioVolatility = this.calculatePortfolioVolatility(positions, correlationMatrix);
    const totalValue = this.calculateTotalValue(positions);
    
    let weightedAverageVolatility = 0;
    for (const position of positions) {
      const weight = position.size.mul(position.currentPrice).div(totalValue).toNumber();
      const volatility = this.getAssetClassVolatility(position.assetClass);
      weightedAverageVolatility += weight * volatility;
    }

    return portfolioVolatility === 0 ? 1 : weightedAverageVolatility / portfolioVolatility;
  }

  private calculateRiskContributions(positions: Position[], correlationMatrix: number[][]): Map<string, number> {
    const contributions = new Map<string, number>();
    const totalValue = this.calculateTotalValue(positions);
    const portfolioVolatility = this.calculatePortfolioVolatility(positions, correlationMatrix);

    if (portfolioVolatility === 0) {
      positions.forEach(pos => contributions.set(pos.symbol, 0));
      return contributions;
    }

    positions.forEach((position, i) => {
      const weight = position.size.mul(position.currentPrice).div(totalValue).toNumber();
      const volatility = this.getAssetClassVolatility(position.assetClass);
      
      let marginalContribution = 0;
      for (let j = 0; j < positions.length; j++) {
        const otherWeight = positions[j].size.mul(positions[j].currentPrice).div(totalValue).toNumber();
        const otherVolatility = this.getAssetClassVolatility(positions[j].assetClass);
        marginalContribution += otherWeight * otherVolatility * correlationMatrix[i][j];
      }

      const riskContribution = weight * volatility * marginalContribution / (portfolioVolatility * portfolioVolatility);
      contributions.set(position.symbol, Math.max(0, riskContribution));
    });

    return contributions;
  }

  private calculateSectorWeights(positions: Position[], totalValue: Decimal.Instance): Map<string, number> {
    const sectorWeights = new Map<string, number>();

    positions.forEach(position => {
      const sector = position.sector || 'Unknown';
      const weight = position.size.mul(position.currentPrice).div(totalValue).mul(100).toNumber();
      sectorWeights.set(sector, (sectorWeights.get(sector) || 0) + weight);
    });

    return sectorWeights;
  }

  private calculateAssetClassWeights(positions: Position[], totalValue: Decimal.Instance): Map<string, number> {
    const assetClassWeights = new Map<string, number>();

    positions.forEach(position => {
      const weight = position.size.mul(position.currentPrice).div(totalValue).mul(100).toNumber();
      assetClassWeights.set(position.assetClass, (assetClassWeights.get(position.assetClass) || 0) + weight);
    });

    return assetClassWeights;
  }

  private checkRiskAlerts(positions: Position[], metrics: PortfolioRiskMetrics): void {
    this.activeAlerts = []; // Clear previous alerts

    // Concentration risk alerts
    if (metrics.concentrationRisk > 0.7) {
      this.activeAlerts.push({
        type: 'concentration',
        severity: 'high',
        message: 'High concentration risk detected in portfolio',
        affectedPositions: positions.map(p => p.symbol),
        recommendedAction: 'Consider diversifying positions across different assets and sectors',
        timestamp: new Date()
      });
    }

    // Correlation risk alerts
    if (metrics.correlationRisk > this.concentrationLimits.maxCorrelationThreshold) {
      this.activeAlerts.push({
        type: 'correlation',
        severity: 'medium',
        message: 'High correlation risk between positions',
        affectedPositions: positions.map(p => p.symbol),
        recommendedAction: 'Reduce positions in highly correlated assets',
        timestamp: new Date()
      });
    }

    // VaR breach alerts
    const accountBalance = metrics.totalValue;
    const varThreshold = accountBalance.mul(0.05); // 5% of portfolio value
    if (metrics.valueAtRisk95.abs().gt(varThreshold)) {
      this.activeAlerts.push({
        type: 'var_breach',
        severity: 'critical',
        message: 'Value at Risk exceeds acceptable threshold',
        affectedPositions: positions.map(p => p.symbol),
        recommendedAction: 'Consider reducing position sizes or implementing hedging strategies',
        timestamp: new Date()
      });
    }

    // Drawdown alerts
    if (metrics.maxDrawdown.gt(new Decimal(0.1))) { // 10% drawdown
      this.activeAlerts.push({
        type: 'drawdown',
        severity: 'high',
        message: 'Significant portfolio drawdown detected',
        affectedPositions: positions.filter(p => p.unrealizedPnL.isNegative()).map(p => p.symbol),
        recommendedAction: 'Review stop-loss levels and consider position adjustments',
        timestamp: new Date()
      });
    }

    if (this.activeAlerts.length > 0) {
      this.emit('riskAlertsGenerated', {
        alerts: this.activeAlerts,
        highSeverityCount: this.activeAlerts.filter(a => a.severity === 'high' || a.severity === 'critical').length
      });
    }
  }

  private getEmptyRiskMetrics(): PortfolioRiskMetrics {
    return {
      totalValue: new Decimal(0),
      totalUnrealizedPnL: new Decimal(0),
      valueAtRisk95: new Decimal(0),
      valueAtRisk99: new Decimal(0),
      expectedShortfall: new Decimal(0),
      portfolioVolatility: 0,
      maxDrawdown: new Decimal(0),
      correlationRisk: 0,
      concentrationRisk: 0,
      diversificationRatio: 1,
      riskContributions: new Map()
    };
  }
}