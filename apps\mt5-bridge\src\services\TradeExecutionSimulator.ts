import { EventEmitter } from 'events';
import type { MarketData } from '@golddaddy/types';

export interface ExecutionConfig {
  enableSlippage: boolean;
  slippageModel: 'fixed' | 'variable' | 'market_impact';
  baseSlippagePips: number;
  volatilityMultiplier: number;
  latencyRange: [number, number];
  enablePartialFills: boolean;
  partialFillThreshold: number;
  enableRejects: boolean;
  rejectRate: number;
  marketImpactEnabled: boolean;
  liquidityLevels: Record<string, number>;
}

export interface ExecutionRequest {
  symbol: string;
  type: 'market' | 'limit' | 'stop';
  side: 'buy' | 'sell';
  volume: number;
  price?: number;
  stopLoss?: number;
  takeProfit?: number;
  deviation?: number;
  fillPolicy: 'fok' | 'ioc' | 'return';
  magic: number;
  comment: string;
  requestId: string;
}

export interface ExecutionResult {
  requestId: string;
  status: 'filled' | 'partial' | 'rejected' | 'pending';
  executedVolume: number;
  remainingVolume: number;
  executionPrice: number;
  slippage: number;
  commission: number;
  latency: number;
  rejectReason?: string;
  executionTime: Date;
  fills: ExecutionFill[];
}

export interface ExecutionFill {
  volume: number;
  price: number;
  timestamp: Date;
  liquidity: 'maker' | 'taker';
}

export interface MarketImpactModel {
  symbol: string;
  baseImpact: number;
  volumeImpact: number;
  volatilityImpact: number;
  timeOfDayMultiplier: number;
  liquidityLevel: number;
}

export interface VolatilityMetrics {
  symbol: string;
  shortTermVol: number;
  longTermVol: number;
  intraday: number;
  trend: number;
  microstructure: number;
}

/**
 * Advanced Trade Execution Simulator
 * Provides realistic execution modeling with slippage, latency, and market impact
 */
export class TradeExecutionSimulator extends EventEmitter {
  private config: ExecutionConfig;
  private marketData: Map<string, MarketData> = new Map();
  private volatilityMetrics: Map<string, VolatilityMetrics> = new Map();
  private marketImpactModels: Map<string, MarketImpactModel> = new Map();
  private executionQueue: ExecutionRequest[] = [];
  private isProcessing: boolean = false;
  private executionHistory: ExecutionResult[] = [];

  constructor(config: Partial<ExecutionConfig> = {}) {
    super();
    
    this.config = {
      enableSlippage: true,
      slippageModel: 'variable',
      baseSlippagePips: 0.5,
      volatilityMultiplier: 1.5,
      latencyRange: [50, 200],
      enablePartialFills: true,
      partialFillThreshold: 1.0, // lots
      enableRejects: true,
      rejectRate: 0.02,
      marketImpactEnabled: true,
      liquidityLevels: {
        'EURUSD': 10000,
        'GBPUSD': 8000,
        'USDJPY': 7000,
        'USDCHF': 5000,
        'AUDUSD': 4000,
        'USDCAD': 4000,
        'NZDUSD': 3000
      },
      ...config
    };

    this.initializeMarketImpactModels();
    this.initializeVolatilityTracking();
    this.startExecutionEngine();
  }

  /**
   * Initialize market impact models for different symbols
   */
  private initializeMarketImpactModels(): void {
    const symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD'];
    
    symbols.forEach(symbol => {
      this.marketImpactModels.set(symbol, {
        symbol,
        baseImpact: this.getBaseImpact(symbol),
        volumeImpact: 0.1, // Impact per lot
        volatilityImpact: 2.0, // Multiplier during high volatility
        timeOfDayMultiplier: 1.0,
        liquidityLevel: this.config.liquidityLevels[symbol] || 1000
      });
    });
  }

  /**
   * Initialize volatility tracking for symbols
   */
  private initializeVolatilityTracking(): void {
    const symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD'];
    
    symbols.forEach(symbol => {
      this.volatilityMetrics.set(symbol, {
        symbol,
        shortTermVol: 0.001, // 1-hour volatility
        longTermVol: 0.005, // Daily volatility
        intraday: 1.0,
        trend: 0.0,
        microstructure: 0.5
      });
    });
  }

  /**
   * Start the execution engine for processing queued orders
   */
  private startExecutionEngine(): void {
    setInterval(() => {
      this.processExecutionQueue();
    }, 10); // Process queue every 10ms
  }

  /**
   * Update market data for execution calculations
   */
  updateMarketData(marketData: MarketData): void {
    this.marketData.set(marketData.symbol, marketData);
    this.updateVolatilityMetrics(marketData);
    this.updateMarketImpactModel(marketData);
  }

  /**
   * Submit an execution request
   */
  async submitExecution(request: ExecutionRequest): Promise<ExecutionResult> {
    return new Promise((resolve, reject) => {
      // Add completion callback to request
      const requestWithCallback = {
        ...request,
        resolve,
        reject
      };

      this.executionQueue.push(requestWithCallback as any);
    });
  }

  /**
   * Process the execution queue
   */
  private async processExecutionQueue(): Promise<void> {
    if (this.isProcessing || this.executionQueue.length === 0) {
      return;
    }

    this.isProcessing = true;
    let request: any = null;

    try {
      request = this.executionQueue.shift()!;
      const result = await this.executeOrder(request);
      
      // Store execution history
      this.executionHistory.push(result);
      
      // Emit execution event
      this.emit('execution', result);
      
      // Resolve the promise
      if (request.resolve) {
        request.resolve(result);
      }
    } catch (error) {
      console.error('Execution error:', error);
      
      // Reject the promise with the error
      if (request && request.reject) {
        request.reject(error);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Execute a single order with realistic modeling
   */
  private async executeOrder(request: ExecutionRequest): Promise<ExecutionResult> {
    const startTime = Date.now();
    
    // Simulate execution latency
    const latency = await this.simulateLatency();
    
    // Check for rejections
    if (this.shouldRejectOrder(request)) {
      return {
        requestId: request.requestId,
        status: 'rejected',
        executedVolume: 0,
        remainingVolume: request.volume,
        executionPrice: 0,
        slippage: 0,
        commission: 0,
        latency,
        rejectReason: this.getRejectReason(request),
        executionTime: new Date(),
        fills: []
      };
    }

    // Get current market data
    const marketData = this.marketData.get(request.symbol);
    if (!marketData) {
      throw new Error(`No market data available for ${request.symbol}`);
    }

    // Calculate execution price with slippage and market impact
    const basePrice = this.getExecutionPrice(request, marketData);
    const slippage = this.calculateSlippage(request, marketData);
    const marketImpact = this.calculateMarketImpact(request, marketData);
    
    const executionPrice = basePrice + slippage + marketImpact;

    // Determine fill behavior
    const fills = this.simulateFillBehavior(request, executionPrice, marketData);
    
    const executedVolume = fills.reduce((sum, fill) => sum + fill.volume, 0);
    const remainingVolume = request.volume - executedVolume;
    
    const status = remainingVolume === 0 ? 'filled' : 
                  executedVolume > 0 ? 'partial' : 'pending';

    // Calculate commission
    const commission = this.calculateCommission(request, executedVolume);

    const result: ExecutionResult = {
      requestId: request.requestId,
      status,
      executedVolume,
      remainingVolume,
      executionPrice,
      slippage: Math.abs(slippage),
      commission,
      latency,
      executionTime: new Date(),
      fills
    };

    return result;
  }

  /**
   * Simulate execution latency based on configuration
   */
  private async simulateLatency(): Promise<number> {
    const [min, max] = this.config.latencyRange;
    const latency = Math.random() * (max - min) + min;
    
    await new Promise(resolve => setTimeout(resolve, latency));
    return latency;
  }

  /**
   * Determine if order should be rejected
   */
  private shouldRejectOrder(request: ExecutionRequest): boolean {
    // Always reject zero or negative volume orders
    if (request.volume <= 0) return true;
    
    if (!this.config.enableRejects) return false;
    
    // Random rejection based on reject rate
    if (Math.random() < this.config.rejectRate) return true;
    
    // Reject very large orders
    const liquidityLevel = this.config.liquidityLevels[request.symbol] || 1000;
    if (request.volume > liquidityLevel * 0.1) return true;
    
    // Reject orders outside market hours (simplified)
    const now = new Date();
    const hour = now.getHours();
    if (hour < 6 || hour > 22) return true; // Simplified market hours
    
    return false;
  }

  /**
   * Get rejection reason for rejected orders
   */
  private getRejectReason(request: ExecutionRequest): string {
    // Check for specific rejection reasons first
    if (request.volume <= 0) {
      return 'Invalid volume: Volume must be greater than zero';
    }
    
    const liquidityLevel = this.config.liquidityLevels[request.symbol] || 1000;
    if (request.volume > liquidityLevel * 0.1) {
      return 'Order size too large';
    }
    
    const now = new Date();
    const hour = now.getHours();
    if (hour < 6 || hour > 22) {
      return 'Market closed';
    }
    
    // Default random reasons
    const reasons = [
      'Insufficient liquidity',
      'Invalid price',
      'System maintenance',
      'Margin requirement not met',
      'Symbol trading halted'
    ];
    
    return reasons[Math.floor(Math.random() * reasons.length)];
  }

  /**
   * Get base execution price for order
   */
  private getExecutionPrice(request: ExecutionRequest, marketData: MarketData): number {
    switch (request.type) {
      case 'market':
        return request.side === 'buy' ? marketData.ask : marketData.bid;
      
      case 'limit':
        return request.price || (request.side === 'buy' ? marketData.ask : marketData.bid);
      
      case 'stop':
        return request.price || (request.side === 'buy' ? marketData.ask : marketData.bid);
      
      default:
        return request.side === 'buy' ? marketData.ask : marketData.bid;
    }
  }

  /**
   * Calculate slippage based on configuration and market conditions
   */
  private calculateSlippage(request: ExecutionRequest, marketData: MarketData): number {
    if (!this.config.enableSlippage) return 0;

    const volatility = this.volatilityMetrics.get(request.symbol);
    if (!volatility) return 0;

    let slippagePips = this.config.baseSlippagePips;

    switch (this.config.slippageModel) {
      case 'fixed':
        slippagePips = this.config.baseSlippagePips;
        break;

      case 'variable':
        // Adjust slippage based on volatility
        const volMultiplier = 1 + (volatility.shortTermVol * this.config.volatilityMultiplier);
        slippagePips *= volMultiplier;
        
        // Adjust for order size
        const sizeMultiplier = Math.sqrt(request.volume);
        slippagePips *= sizeMultiplier;
        break;

      case 'market_impact':
        // Use market impact model for slippage
        slippagePips = this.calculateAdvancedSlippage(request, marketData, volatility);
        break;
    }

    // Convert pips to price units
    const pipValue = this.getPipValue(request.symbol);
    const slippage = slippagePips * pipValue;

    // Apply direction (slippage is always unfavorable)
    return request.side === 'buy' ? slippage : -slippage;
  }

  /**
   * Calculate market impact for large orders
   */
  private calculateMarketImpact(request: ExecutionRequest, marketData: MarketData): number {
    if (!this.config.marketImpactEnabled) return 0;

    const impactModel = this.marketImpactModels.get(request.symbol);
    if (!impactModel) return 0;

    // Base impact
    let impact = impactModel.baseImpact;

    // Volume impact (square root law)
    const volumeRatio = request.volume / impactModel.liquidityLevel;
    impact += impactModel.volumeImpact * Math.sqrt(volumeRatio);

    // Volatility impact
    const volatility = this.volatilityMetrics.get(request.symbol);
    if (volatility) {
      impact *= (1 + volatility.shortTermVol * impactModel.volatilityImpact);
    }

    // Time of day adjustment
    impact *= impactModel.timeOfDayMultiplier;

    // Convert to price units
    const pipValue = this.getPipValue(request.symbol);
    const priceImpact = impact * pipValue;

    // Apply direction
    return request.side === 'buy' ? priceImpact : -priceImpact;
  }

  /**
   * Simulate realistic fill behavior including partial fills
   */
  private simulateFillBehavior(
    request: ExecutionRequest, 
    executionPrice: number, 
    marketData: MarketData
  ): ExecutionFill[] {
    const fills: ExecutionFill[] = [];
    
    if (!this.config.enablePartialFills || request.volume <= this.config.partialFillThreshold) {
      // Single fill
      fills.push({
        volume: request.volume,
        price: executionPrice,
        timestamp: new Date(),
        liquidity: Math.random() > 0.5 ? 'taker' : 'maker'
      });
    } else {
      // Multiple partial fills for large orders
      let remainingVolume = request.volume;
      const fillCount = Math.ceil(request.volume / this.config.partialFillThreshold);
      
      for (let i = 0; i < fillCount && remainingVolume > 0; i++) {
        const fillSize = Math.min(remainingVolume, this.config.partialFillThreshold);
        const priceVariation = (Math.random() - 0.5) * 0.00002; // Small price variation
        
        fills.push({
          volume: fillSize,
          price: executionPrice + priceVariation,
          timestamp: new Date(Date.now() + i * 10), // 10ms apart
          liquidity: i === 0 ? 'taker' : 'maker'
        });
        
        remainingVolume -= fillSize;
      }
    }

    return fills;
  }

  /**
   * Calculate commission for executed volume
   */
  private calculateCommission(request: ExecutionRequest, executedVolume: number): number {
    // Simplified commission calculation
    const commissionRates: Record<string, number> = {
      'EURUSD': 3.5,
      'GBPUSD': 3.5,
      'USDJPY': 3.5,
      'USDCHF': 3.5,
      'AUDUSD': 3.5,
      'USDCAD': 3.5,
      'NZDUSD': 3.5
    };

    const rate = commissionRates[request.symbol] || 3.5;
    return executedVolume * rate;
  }

  // Helper methods

  private getBaseImpact(symbol: string): number {
    const impacts: Record<string, number> = {
      'EURUSD': 0.1,
      'GBPUSD': 0.15,
      'USDJPY': 0.8,
      'USDCHF': 0.12,
      'AUDUSD': 0.18,
      'USDCAD': 0.14,
      'NZDUSD': 0.2
    };
    
    return impacts[symbol] || 0.15;
  }

  private getPipValue(symbol: string): number {
    return symbol.includes('JPY') ? 0.01 : 0.0001;
  }

  private calculateAdvancedSlippage(
    request: ExecutionRequest, 
    marketData: MarketData, 
    volatility: VolatilityMetrics
  ): number {
    // Advanced slippage model considering multiple factors
    let slippage = this.config.baseSlippagePips;

    // Volatility component
    slippage *= (1 + volatility.shortTermVol * 100);

    // Order size component
    const sizeImpact = Math.log(1 + request.volume) * 0.5;
    slippage += sizeImpact;

    // Spread component
    const spreadMultiplier = marketData.spread / this.getPipValue(request.symbol);
    slippage += spreadMultiplier * 0.1;

    // Time component (higher slippage during volatile times)
    const hour = new Date().getHours();
    const timeMultiplier = (hour >= 8 && hour <= 17) ? 1.0 : 1.3; // Business hours vs off-hours
    slippage *= timeMultiplier;

    return slippage;
  }

  private updateVolatilityMetrics(marketData: MarketData): void {
    const existing = this.volatilityMetrics.get(marketData.symbol);
    if (!existing) return;

    // Simple volatility update (in real implementation, this would be more sophisticated)
    const priceChange = Math.abs(marketData.bid - (existing.shortTermVol * 100 + 1.0));
    const normalizedChange = priceChange / marketData.bid;

    existing.shortTermVol = existing.shortTermVol * 0.95 + normalizedChange * 0.05;
    existing.microstructure = existing.microstructure * 0.9 + Math.random() * 0.1;

    this.volatilityMetrics.set(marketData.symbol, existing);
  }

  private updateMarketImpactModel(marketData: MarketData): void {
    const model = this.marketImpactModels.get(marketData.symbol);
    if (!model) return;

    // Update time of day multiplier
    const hour = new Date().getHours();
    model.timeOfDayMultiplier = this.getTimeOfDayMultiplier(hour);

    this.marketImpactModels.set(marketData.symbol, model);
  }

  private getTimeOfDayMultiplier(hour: number): number {
    // Higher impact during low liquidity hours
    if (hour >= 22 || hour <= 6) return 1.5; // Overnight
    if (hour >= 8 && hour <= 17) return 1.0; // Business hours
    return 1.2; // Transition periods
  }

  /**
   * Get execution statistics
   */
  getExecutionStats(): {
    totalExecutions: number;
    fillRate: number;
    avgSlippage: number;
    avgLatency: number;
    rejectRate: number;
    partialFillRate: number;
  } {
    const total = this.executionHistory.length;
    
    if (total === 0) {
      return {
        totalExecutions: 0,
        fillRate: 0,
        avgSlippage: 0,
        avgLatency: 0,
        rejectRate: 0,
        partialFillRate: 0
      };
    }

    const filled = this.executionHistory.filter(r => r.status === 'filled').length;
    const partial = this.executionHistory.filter(r => r.status === 'partial').length;
    const rejected = this.executionHistory.filter(r => r.status === 'rejected').length;

    const avgSlippage = this.executionHistory.reduce((sum, r) => sum + r.slippage, 0) / total;
    const avgLatency = this.executionHistory.reduce((sum, r) => sum + r.latency, 0) / total;

    return {
      totalExecutions: total,
      fillRate: (filled + partial) / total,
      avgSlippage,
      avgLatency,
      rejectRate: rejected / total,
      partialFillRate: partial / total
    };
  }

  /**
   * Configure execution parameters
   */
  configure(newConfig: Partial<ExecutionConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * Reset execution history and metrics
   */
  reset(): void {
    this.executionHistory = [];
    this.executionQueue = [];
    this.initializeVolatilityTracking();
    this.initializeMarketImpactModels();
  }
}