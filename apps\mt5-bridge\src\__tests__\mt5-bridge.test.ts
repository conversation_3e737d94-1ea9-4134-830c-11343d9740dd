/**
 * MT5 Bridge Tests
 * Tests for MT5 Bridge TypeScript service and Python integration
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import type WebSocket from 'ws';
import { MT5Bridge } from '../index';
import { PythonBridge } from '../python-bridge';

// Create proper mock constructor
const mockWebSocketServer = vi.fn();
const mockWebSocket = vi.fn();

// Mock WebSocket module
vi.mock('ws', () => {
  const MockedWS = function(this: any, ...args: any[]) {
    return mockWebSocket(...args);
  };
  
  const MockedServer = function(this: any, ...args: any[]) {
    return mockWebSocketServer(...args);
  };

  return {
    default: MockedWS,
    Server: MockedServer
  };
});

// Mock Python Bridge
const mockPythonBridgeInstance = {
  initialize: vi.fn().mockResolvedValue(true),
  getMT5Status: vi.fn().mockResolvedValue({ connected: true }),
  getSymbols: vi.fn().mockResolvedValue(['EURUSD', 'GBPUSD', 'USDJPY']),
  subscribeToSymbol: vi.fn().mockReturnValue(true),
  unsubscribeFromSymbol: vi.fn().mockReturnValue(true),
  executeTrade: vi.fn().mockResolvedValue({ id: 'test-trade', status: 'executed' }),
  on: vi.fn(),
  sendMessage: vi.fn().mockReturnValue(true),
  isConnected: vi.fn().mockReturnValue(true),
  getStatus: vi.fn().mockReturnValue({ connected: true, error: null })
};

vi.mock('../python-bridge', () => ({
  getPythonBridge: vi.fn(() => mockPythonBridgeInstance),
  PythonBridge: vi.fn().mockImplementation(() => mockPythonBridgeInstance)
}));

describe('MT5Bridge', () => {
  let mockWss: any;
  let mockWs: any;

  beforeEach(() => {
    // Mock WebSocket Server
    mockWss = {
      on: vi.fn(),
      clients: new Set()
    };
    
    // Mock WebSocket Client
    mockWs = {
      send: vi.fn(),
      on: vi.fn(),
      readyState: 1 // WebSocket.OPEN
    };

    // Setup mock implementations
    mockWebSocketServer.mockImplementation(() => mockWss);
    mockWebSocket.mockImplementation(() => mockWs);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initialization', () => {
    it('should create WebSocket server on specified port', () => {
      process.env.MT5_BRIDGE_PORT = '3002';
      
      new MT5Bridge();
      
      expect(mockWebSocketServer).toHaveBeenCalledWith({ port: 3002 });
    });

    it('should setup WebSocket connection handlers', () => {
      new MT5Bridge();
      
      expect(mockWss.on).toHaveBeenCalledWith('connection', expect.any(Function));
    });

    it('should initialize Python bridge', () => {
      const bridge = new MT5Bridge();
      
      // The Python bridge should be initialized during construction
      expect(bridge).toBeDefined();
    });
  });

  describe('Message Handling', () => {
    let bridge: MT5Bridge;

    beforeEach(() => {
      bridge = new MT5Bridge();
      
      // Get the connection handler and simulate a connection
      const connectionHandler = mockWss.on.mock.calls.find(
        call => call[0] === 'connection'
      )?.[1];
      
      if (connectionHandler) {
        connectionHandler(mockWs);
      }
    });

    it('should handle ping messages', () => {
      const messageHandler = mockWs.on.mock.calls.find(
        call => call[0] === 'message'
      )?.[1];

      const pingMessage = JSON.stringify({ type: 'ping' });
      messageHandler?.(pingMessage);

      expect(mockWs.send).toHaveBeenCalledWith(
        expect.stringContaining('"type":"pong"')
      );
    });

    it('should handle unknown message types with error', () => {
      const messageHandler = mockWs.on.mock.calls.find(
        call => call[0] === 'message'
      )?.[1];

      const unknownMessage = JSON.stringify({ type: 'unknown_type' });
      messageHandler?.(unknownMessage);

      expect(mockWs.send).toHaveBeenCalledWith(
        expect.stringContaining('"type":"error"')
      );
    });

    it('should handle malformed JSON messages', () => {
      const messageHandler = mockWs.on.mock.calls.find(
        call => call[0] === 'message'
      )?.[1];

      const malformedMessage = 'invalid json';
      messageHandler?.(malformedMessage);

      expect(mockWs.send).toHaveBeenCalledWith(
        expect.stringContaining('"type":"error"')
      );
    });
  });

  describe('Broadcasting', () => {
    it('should broadcast messages to all connected clients', () => {
      const bridge = new MT5Bridge();
      
      // Add mock clients to the WebSocket server
      const client1 = { send: vi.fn(), readyState: WebSocket.OPEN };
      const client2 = { send: vi.fn(), readyState: WebSocket.OPEN };
      const client3 = { send: vi.fn(), readyState: WebSocket.CLOSED };
      
      mockWss.clients.add(client1);
      mockWss.clients.add(client2);
      mockWss.clients.add(client3);

      const testMessage = { type: 'test', data: 'broadcast test' };
      bridge.broadcast(testMessage);

      // Should send to open connections only
      expect(client1.send).toHaveBeenCalledWith(JSON.stringify(testMessage));
      expect(client2.send).toHaveBeenCalledWith(JSON.stringify(testMessage));
      expect(client3.send).not.toHaveBeenCalled();
    });
  });
});

describe('PythonBridge', () => {
  describe('Configuration', () => {
    it('should use default configuration when none provided', () => {
      const bridge = new PythonBridge();
      const status = bridge.getStatus();
      
      expect(status).toHaveProperty('connected');
      expect(status).toHaveProperty('error');
    });

    it('should use environment variables for configuration', () => {
      process.env.PYTHON_API_URL = 'http://test:8001';
      process.env.PYTHON_WS_URL = 'ws://test:8002';
      
      const bridge = new PythonBridge();
      
      // Configuration should be set from environment
      expect(bridge).toBeDefined();
    });
  });

  describe('Connection Management', () => {
    let bridge: PythonBridge;

    beforeEach(() => {
      bridge = new PythonBridge();
    });

    it('should report connection status', () => {
      const status = bridge.getStatus();
      
      expect(status).toHaveProperty('connected');
      expect(status).toHaveProperty('lastPing');
      expect(status).toHaveProperty('error');
      expect(status).toHaveProperty('reconnectAttempts');
    });

    it('should handle connection failures gracefully', async () => {
      // Mock failed initialization
      const mockBridge = vi.mocked(bridge);
      vi.spyOn(mockBridge, 'initialize').mockResolvedValue(false);
      
      const result = await mockBridge.initialize();
      
      expect(result).toBe(false);
    });
  });
});

describe('Environment Setup Validation', () => {
  it('should validate required environment variables', () => {
    // Test that the module loads without throwing
    expect(() => {
      require('../index');
    }).not.toThrow();
  });

  it('should handle missing Python service gracefully', () => {
    // Even if Python service is not available, the bridge should initialize
    const bridge = new MT5Bridge();
    expect(bridge).toBeDefined();
  });
});

describe('Error Handling', () => {
  it('should handle Python bridge connection errors', () => {
    const bridge = new MT5Bridge();
    
    // Should not throw even if Python bridge fails
    expect(bridge).toBeDefined();
  });

  it('should handle WebSocket server creation errors', () => {
    // Mock WebSocket Server to throw
    mockWebSocketServer.mockImplementation(() => {
      throw new Error('Port already in use');
    });

    expect(() => {
      new MT5Bridge();
    }).toThrow('Port already in use');
  });
});