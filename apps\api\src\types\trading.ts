/**
 * Trading System Types
 * 
 * Comprehensive type definitions for the trading system including positions,
 * market data, liquidation management, risk analysis, and emergency handling.
 */

import Decimal from 'decimal.js';

// Basic Trading Types
export interface Position {
  id: string;
  accountId: string;
  userId?: string;
  symbol: string;
  side: 'long' | 'short';
  size: Decimal.Instance;
  entryPrice: Decimal.Instance;
  currentPrice: Decimal.Instance;
  unrealizedPnl: Decimal.Instance;
  realizedPnl: Decimal.Instance;
  marginUsed: Decimal.Instance;
  riskScore?: number;
  priorityLevel?: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  lastUpdate?: Date;
}

export interface MarketData {
  symbol: string;
  timestamp: Date;
  bid: Decimal.Instance;
  ask: Decimal.Instance;
  volume: Decimal.Instance;
  volatility: number;
  liquidity: LiquidityMetrics;
}

export interface LiquidityMetrics {
  bidDepth: Decimal.Instance;
  askDepth: Decimal.Instance;
  spread: Decimal.Instance;
  impactCost: number;
}

export interface MarketCondition {
  symbol: string;
  timestamp: Date;
  stressLevel: number;
  volatilityChange: number;
  liquidityScore: number;
  volumeChange: number;
  correlationBreakdown: boolean;
  volatility?: number;
  liquidity?: number;
  spreadPercentage?: number;
  priceChange24h?: number;
}

// Emergency Liquidation Types
export interface LiquidationTrigger {
  type: 'volatility_spike' | 'liquidity_crisis' | 'correlation_breakdown' | 'manual_override' | 'portfolio_var_breach';
  severity: 'low' | 'medium' | 'high' | 'critical';
  affectedSymbols: string[];
  triggerValue: number;
  threshold: number;
  description: string;
  timestamp: Date;
}

export interface EmergencyState {
  isActive: boolean;
  level: 'normal' | 'elevated' | 'high' | 'critical';
  activatedAt?: Date;
  trigger?: LiquidationTrigger;
  affectedPositions: string[];
  executionProgress: {
    totalPositions: number;
    liquidatedPositions: number;
    failedLiquidations: number;
    estimatedTimeRemaining: number;
  };
}

export interface LiquidationResult {
  positionId: string;
  success: boolean;
  executedPrice?: Decimal.Instance;
  executedSize?: Decimal.Instance;
  slippage?: number;
  marketImpact?: number;
  error?: string;
  timestamp: Date;
}

// Market Stress Detection Types
export enum StressEventType {
  VOLATILITY_SPIKE = 'volatility_spike',
  LIQUIDITY_CRISIS = 'liquidity_crisis',
  VOLUME_SPIKE = 'volume_spike',
  VOLUME_DROUGHT = 'volume_drought',
  CORRELATION_BREAKDOWN = 'correlation_breakdown'
}

export enum StressSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface StressEvent {
  type: StressEventType;
  severity: StressSeverity;
  symbol: string;
  value: number;
  threshold: number;
  timestamp: Date;
  description: string;
}

// Liquidation Order Management Types
export enum LiquidationStrategy {
  IMMEDIATE = 'immediate',
  GRADUAL = 'gradual',
  MARKET_IMPACT_OPTIMIZED = 'market_impact_optimized'
}

export enum OrderPriority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export enum OrderType {
  MARKET = 'market',
  LIMIT = 'limit',
  STOP = 'stop',
  STOP_LIMIT = 'stop_limit'
}

export enum OrderStatus {
  PENDING = 'pending',
  SUBMITTED = 'submitted',
  PARTIAL_FILL = 'partial_fill',
  FILLED = 'filled',
  CANCELLED = 'cancelled',
  REJECTED = 'rejected',
  EXPIRED = 'expired'
}

export interface Order {
  id: string;
  positionId: string;
  symbol: string;
  type: OrderType;
  side: 'buy' | 'sell';
  size: Decimal.Instance;
  price?: Decimal.Instance;
  stopPrice?: Decimal.Instance;
  status: OrderStatus;
  priority: OrderPriority;
  estimatedSlippage?: number;
  estimatedMarketImpact?: number;
  delayMs?: number;
  createdAt: Date;
  submittedAt?: Date;
  executedAt?: Date;
}

export interface OrderExecution {
  executedPrice: Decimal.Instance;
  executedSize: Decimal.Instance;
  executedAt: Date;
  slippage?: number;
  marketImpact?: number;
}

// Portfolio Risk Analysis Types
export interface PortfolioRiskMetrics {
  totalValue: Decimal.Instance;
  totalUnrealizedPnl: Decimal.Instance;
  valueAtRisk: {
    daily: {
      confidence95: number;
      confidence99: number;
    };
    weekly: {
      confidence95: number;
      confidence99: number;
    };
  };
  expectedShortfall: {
    daily: {
      confidence95: number;
      confidence99: number;
    };
    weekly: {
      confidence95: number;
      confidence99: number;
    };
  };
  concentrationRisk: {
    concentrationScore: number;
    largestPosition: Decimal.Instance;
    topPositionsWeight: number;
    diversificationIndex: number;
  };
  correlationRisk: {
    averageCorrelation: number;
    maxCorrelation: number;
    correlationMatrix: Record<string, Record<string, number>>;
  };
  leverageMetrics: {
    grossLeverage: number;
    netLeverage: number;
    marginUtilization: number;
  };
  lastCalculated: Date;
}

export interface CorrelationAdjustment {
  recommendedSize: Decimal.Instance;
  adjustment: number;
  riskReduction: number;
  explanation: string;
}

export interface PortfolioMetrics {
  totalReturn: number;
  annualizedReturn: number;
  volatility: number;
  sharpeRatio: number;
  sortinoRatio: number;
  calmarRatio: number;
  maxDrawdown: number;
  alpha: number;
  beta: number;
  winRate: number;
  profitFactor: number;
  recoveryFactor: number;
  explanation: {
    performance: string;
    risk: string;
    comparison: string;
    recommendations: string[];
  };
}

// Configuration Types
export interface EmergencyLiquidationConfig {
  volatilityThreshold: number;
  liquidityThreshold: number;
  maxLiquidationTime: number;
  orderTimeout: number;
  maxRetries: number;
  emergencyContacts: string[];
}

export interface MarketStressConfig {
  volatilityThreshold: number;
  liquidityThreshold: number;
  spreadThreshold: number;
  volumeThreshold: number;
  correlationThreshold: number;
  windowSize: number;
  stressDetectionInterval: number;
}

export interface LiquidationOrderConfig {
  maxOrdersPerSecond: number;
  minOrderSize: Decimal.Instance;
  maxOrderSize: Decimal.Instance;
  slippageThreshold: number;
  maxMarketImpact: number;
  liquidityBuffer: number;
  orderTimeout: number;
}

// Event Types
export interface EmergencyLiquidationEvents {
  'liquidationInitiated': (trigger: LiquidationTrigger, positions: Position[]) => void;
  'liquidationCompleted': (results: LiquidationResult[]) => void;
  'liquidationFailed': (error: string, positions: Position[]) => void;
  'emergencyStateChanged': (state: EmergencyState) => void;
  'liquidationProgress': (progress: { completed: number; total: number }) => void;
  'liquidationError': (error: string) => void;
  'positionLiquidated': (result: LiquidationResult) => void;
  'emergencyActivated': (level: string, trigger: LiquidationTrigger) => void;
  'emergencyDeactivated': () => void;
}

export interface MarketStressEvents {
  'stressEvent': (event: StressEvent) => void;
  'stressLevelUpdate': (level: { level: number; severity: string }) => void;
}

export interface LiquidationOrderEvents {
  'orderCreated': (order: Order) => void;
  'orderExecuted': (order: Order) => void;
  'orderFailed': (orderId: string, reason: string) => void;
  'orderCancelled': (order: Order) => void;
  'webhookTriggered': (data: { url: string; event: string }) => void;
}