import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';

/**
 * Trading Execution Health Monitoring Service
 * Monitors the health of trade execution pipeline
 * Tracks order processing, fill rates, slippage, latency, and execution quality
 */
export class TradingExecutionHealthService extends EventEmitter {
  private prisma: PrismaClient;
  private executionMetrics: ExecutionMetrics = this.initializeMetrics();
  private orderTracking: Map<string, OrderExecution> = new Map();
  private executionHistory: ExecutionHealthSnapshot[] = [];
  private monitoringInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;

  private readonly MONITORING_INTERVAL = 30 * 1000; // 30 seconds
  private readonly HISTORY_LIMIT = 2000;
  private readonly ORDER_TIMEOUT = 30 * 1000; // 30 seconds
  
  // Health thresholds
  private readonly HEALTH_THRESHOLDS = {
    orderLatency: {
      excellent: 100, // <100ms
      good: 500, // 100-500ms
      warning: 1000, // 500ms-1s
      critical: 5000, // >5s
    },
    fillRate: {
      excellent: 99, // >99%
      good: 95, // 95-99%
      warning: 85, // 85-95%
      critical: 70, // <70%
    },
    slippage: {
      excellent: 0.0001, // <0.01%
      good: 0.0005, // 0.01-0.05%
      warning: 0.001, // 0.05-0.1%
      critical: 0.005, // >0.5%
    },
    rejectionRate: {
      excellent: 1, // <1%
      good: 3, // 1-3%
      warning: 5, // 3-5%
      critical: 10, // >10%
    },
  };

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  /**
   * Start trading execution health monitoring
   */
  async startMonitoring(config: ExecutionMonitoringConfig = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('Trading execution monitoring already active');
      return;
    }

    console.log('⚡ Starting trading execution health monitoring...');
    this.isMonitoring = true;

    const intervalMs = config.intervalMs || this.MONITORING_INTERVAL;

    // Start health monitoring
    this.monitoringInterval = setInterval(async () => {
      await this.performExecutionHealthCheck();
    }, intervalMs);

    // Clean up old order tracking data
    setInterval(() => {
      this.cleanupOrderTracking();
    }, 60 * 1000); // Every minute

    console.log('✅ Trading execution health monitoring started');
    this.emit('monitoringStarted', { intervalMs });
  }

  /**
   * Stop trading execution health monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ Stopping trading execution health monitoring...');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    console.log('✅ Trading execution health monitoring stopped');
    this.emit('monitoringStopped');
  }

  /**
   * Initialize execution metrics
   */
  private initializeMetrics(): ExecutionMetrics {
    return {
      totalOrders: 0,
      successfulOrders: 0,
      rejectedOrders: 0,
      partialFills: 0,
      cancelledOrders: 0,
      timeoutOrders: 0,
      totalLatency: 0,
      totalSlippage: 0,
      averageLatency: 0,
      averageSlippage: 0,
      fillRate: 100,
      rejectionRate: 0,
      successRate: 100,
      recentExecutions: [],
      ordersByType: {
        market: { total: 0, successful: 0, avgLatency: 0, avgSlippage: 0 },
        limit: { total: 0, successful: 0, avgLatency: 0, avgSlippage: 0 },
        stop: { total: 0, successful: 0, avgLatency: 0, avgSlippage: 0 },
        stopLimit: { total: 0, successful: 0, avgLatency: 0, avgSlippage: 0 },
      },
      ordersBySymbol: new Map(),
      hourlyStats: new Map(),
    };
  }

  /**
   * Track order submission
   */
  async trackOrderSubmission(orderRequest: OrderRequest): Promise<void> {
    const orderExecution: OrderExecution = {
      orderId: orderRequest.orderId,
      clientOrderId: orderRequest.clientOrderId,
      symbol: orderRequest.symbol,
      side: orderRequest.side,
      type: orderRequest.type,
      quantity: orderRequest.quantity,
      price: orderRequest.price,
      stopPrice: orderRequest.stopPrice,
      submittedAt: new Date(),
      status: 'pending',
      fills: [],
      events: [{
        timestamp: new Date(),
        event: 'submitted',
        data: { ...orderRequest },
      }],
      metrics: {
        latency: 0,
        slippage: 0,
        fillRate: 0,
        executionScore: 0,
      },
    };

    this.orderTracking.set(orderRequest.orderId, orderExecution);
    this.executionMetrics.totalOrders++;

    // Update order type stats
    if (this.executionMetrics.ordersByType[orderRequest.type]) {
      this.executionMetrics.ordersByType[orderRequest.type].total++;
    }

    // Update symbol stats
    if (!this.executionMetrics.ordersBySymbol.has(orderRequest.symbol)) {
      this.executionMetrics.ordersBySymbol.set(orderRequest.symbol, {
        total: 0,
        successful: 0,
        avgLatency: 0,
        avgSlippage: 0,
        rejectionRate: 0,
      });
    }
    const symbolStats = this.executionMetrics.ordersBySymbol.get(orderRequest.symbol)!;
    symbolStats.total++;

    this.emit('orderSubmitted', orderExecution);
  }

  /**
   * Track order acknowledgment
   */
  async trackOrderAcknowledgment(orderId: string, acknowledgment: OrderAcknowledgment): Promise<void> {
    const execution = this.orderTracking.get(orderId);
    if (!execution) {
      console.warn(`Order execution not found for acknowledgment: ${orderId}`);
      return;
    }

    execution.acknowledgedAt = new Date();
    execution.brokerOrderId = acknowledgment.brokerOrderId;
    execution.status = 'acknowledged';

    execution.events.push({
      timestamp: new Date(),
      event: 'acknowledged',
      data: acknowledgment,
    });

    // Calculate acknowledgment latency
    if (execution.submittedAt) {
      execution.metrics.latency = execution.acknowledgedAt.getTime() - execution.submittedAt.getTime();
    }

    this.emit('orderAcknowledged', execution);
  }

  /**
   * Track order fill
   */
  async trackOrderFill(orderId: string, fill: OrderFill): Promise<void> {
    const execution = this.orderTracking.get(orderId);
    if (!execution) {
      console.warn(`Order execution not found for fill: ${orderId}`);
      return;
    }

    const fillEvent: FillEvent = {
      fillId: fill.fillId,
      timestamp: new Date(),
      quantity: fill.quantity,
      price: fill.price,
      fee: fill.fee,
      feeAsset: fill.feeAsset,
    };

    execution.fills.push(fillEvent);
    execution.lastFillAt = new Date();

    // Calculate total filled quantity
    const totalFilled = execution.fills.reduce((sum, f) => sum + f.quantity, 0);
    execution.filledQuantity = totalFilled;

    // Update status based on fill
    if (totalFilled >= execution.quantity) {
      execution.status = 'filled';
      execution.completedAt = new Date();
      await this.finalizeOrderExecution(execution);
    } else {
      execution.status = 'partially_filled';
    }

    // Calculate weighted average fill price
    const totalValue = execution.fills.reduce((sum, f) => sum + (f.quantity * f.price), 0);
    execution.avgFillPrice = totalFilled > 0 ? totalValue / totalFilled : 0;

    // Calculate slippage for market orders
    if (execution.type === 'market' && execution.price && execution.avgFillPrice) {
      execution.metrics.slippage = Math.abs(execution.avgFillPrice - execution.price) / execution.price;
    }

    execution.events.push({
      timestamp: new Date(),
      event: 'filled',
      data: fill,
    });

    this.emit('orderFilled', { execution, fill: fillEvent });
  }

  /**
   * Track order rejection
   */
  async trackOrderRejection(orderId: string, rejection: OrderRejection): Promise<void> {
    const execution = this.orderTracking.get(orderId);
    if (!execution) {
      console.warn(`Order execution not found for rejection: ${orderId}`);
      return;
    }

    execution.status = 'rejected';
    execution.rejectedAt = new Date();
    execution.rejectionReason = rejection.reason;
    execution.completedAt = new Date();

    execution.events.push({
      timestamp: new Date(),
      event: 'rejected',
      data: rejection,
    });

    // Update metrics
    this.executionMetrics.rejectedOrders++;
    
    // Update symbol rejection rate
    const symbolStats = this.executionMetrics.ordersBySymbol.get(execution.symbol);
    if (symbolStats) {
      symbolStats.rejectionRate = (this.executionMetrics.rejectedOrders / this.executionMetrics.totalOrders) * 100;
    }

    await this.finalizeOrderExecution(execution);

    this.emit('orderRejected', execution);
  }

  /**
   * Track order cancellation
   */
  async trackOrderCancellation(orderId: string, cancellation: OrderCancellation): Promise<void> {
    const execution = this.orderTracking.get(orderId);
    if (!execution) {
      console.warn(`Order execution not found for cancellation: ${orderId}`);
      return;
    }

    execution.status = 'cancelled';
    execution.cancelledAt = new Date();
    execution.cancellationReason = cancellation.reason;
    execution.completedAt = new Date();

    execution.events.push({
      timestamp: new Date(),
      event: 'cancelled',
      data: cancellation,
    });

    this.executionMetrics.cancelledOrders++;
    await this.finalizeOrderExecution(execution);

    this.emit('orderCancelled', execution);
  }

  /**
   * Finalize order execution and update metrics
   */
  private async finalizeOrderExecution(execution: OrderExecution): Promise<void> {
    // Calculate final metrics
    if (execution.submittedAt && execution.completedAt) {
      execution.metrics.latency = execution.completedAt.getTime() - execution.submittedAt.getTime();
    }

    if (execution.fills.length > 0) {
      execution.metrics.fillRate = (execution.filledQuantity! / execution.quantity) * 100;
    }

    // Calculate execution score (0-100)
    execution.metrics.executionScore = this.calculateExecutionScore(execution);

    // Update global metrics
    await this.updateGlobalMetrics(execution);

    // Add to recent executions
    this.executionMetrics.recentExecutions.push(execution);
    if (this.executionMetrics.recentExecutions.length > 100) {
      this.executionMetrics.recentExecutions = this.executionMetrics.recentExecutions.slice(-100);
    }

    // Store in database
    await this.storeOrderExecution(execution);

    // Remove from tracking
    this.orderTracking.delete(execution.orderId);
  }

  /**
   * Calculate execution score (0-100)
   */
  private calculateExecutionScore(execution: OrderExecution): number {
    let score = 100;

    // Latency penalty
    if (execution.metrics.latency > this.HEALTH_THRESHOLDS.orderLatency.critical) {
      score -= 40;
    } else if (execution.metrics.latency > this.HEALTH_THRESHOLDS.orderLatency.warning) {
      score -= 20;
    } else if (execution.metrics.latency > this.HEALTH_THRESHOLDS.orderLatency.good) {
      score -= 10;
    }

    // Status penalty
    switch (execution.status) {
      case 'rejected':
        score -= 80;
        break;
      case 'cancelled':
        score -= 30;
        break;
      case 'partially_filled':
        score -= 20;
        break;
      case 'timeout':
        score -= 60;
        break;
    }

    // Slippage penalty (for market orders)
    if (execution.type === 'market' && execution.metrics.slippage > 0) {
      if (execution.metrics.slippage > this.HEALTH_THRESHOLDS.slippage.critical) {
        score -= 30;
      } else if (execution.metrics.slippage > this.HEALTH_THRESHOLDS.slippage.warning) {
        score -= 15;
      } else if (execution.metrics.slippage > this.HEALTH_THRESHOLDS.slippage.good) {
        score -= 5;
      }
    }

    return Math.max(0, Math.round(score));
  }

  /**
   * Update global metrics
   */
  private async updateGlobalMetrics(execution: OrderExecution): Promise<void> {
    // Update success/failure counts
    if (execution.status === 'filled') {
      this.executionMetrics.successfulOrders++;
    }

    // Update latency
    if (execution.metrics.latency > 0) {
      this.executionMetrics.totalLatency += execution.metrics.latency;
      this.executionMetrics.averageLatency = this.executionMetrics.totalLatency / this.executionMetrics.totalOrders;
    }

    // Update slippage
    if (execution.metrics.slippage > 0) {
      this.executionMetrics.totalSlippage += execution.metrics.slippage;
      this.executionMetrics.averageSlippage = this.executionMetrics.totalSlippage / this.executionMetrics.totalOrders;
    }

    // Update rates
    this.executionMetrics.fillRate = (this.executionMetrics.successfulOrders / this.executionMetrics.totalOrders) * 100;
    this.executionMetrics.rejectionRate = (this.executionMetrics.rejectedOrders / this.executionMetrics.totalOrders) * 100;
    this.executionMetrics.successRate = (this.executionMetrics.successfulOrders / this.executionMetrics.totalOrders) * 100;

    // Update order type metrics
    const typeStats = this.executionMetrics.ordersByType[execution.type];
    if (typeStats && execution.status === 'filled') {
      typeStats.successful++;
      typeStats.avgLatency = (typeStats.avgLatency + execution.metrics.latency) / 2;
      typeStats.avgSlippage = (typeStats.avgSlippage + execution.metrics.slippage) / 2;
    }

    // Update symbol metrics
    const symbolStats = this.executionMetrics.ordersBySymbol.get(execution.symbol);
    if (symbolStats && execution.status === 'filled') {
      symbolStats.successful++;
      symbolStats.avgLatency = (symbolStats.avgLatency + execution.metrics.latency) / 2;
      symbolStats.avgSlippage = (symbolStats.avgSlippage + execution.metrics.slippage) / 2;
    }

    // Update hourly stats
    const hour = new Date().getHours();
    if (!this.executionMetrics.hourlyStats.has(hour)) {
      this.executionMetrics.hourlyStats.set(hour, {
        total: 0,
        successful: 0,
        avgLatency: 0,
        avgSlippage: 0,
      });
    }
    const hourlyStats = this.executionMetrics.hourlyStats.get(hour)!;
    hourlyStats.total++;
    if (execution.status === 'filled') {
      hourlyStats.successful++;
      hourlyStats.avgLatency = (hourlyStats.avgLatency + execution.metrics.latency) / 2;
      hourlyStats.avgSlippage = (hourlyStats.avgSlippage + execution.metrics.slippage) / 2;
    }
  }

  /**
   * Perform execution health check
   */
  private async performExecutionHealthCheck(): Promise<void> {
    const snapshot: ExecutionHealthSnapshot = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      overallStatus: 'unknown',
      metrics: { ...this.executionMetrics },
      healthScores: this.calculateHealthScores(),
      issues: this.identifyHealthIssues(),
      recommendations: this.generateRecommendations(),
    };

    // Determine overall status
    snapshot.overallStatus = this.determineOverallExecutionHealth(snapshot.healthScores);

    // Store snapshot
    this.executionHistory.push(snapshot);
    if (this.executionHistory.length > this.HISTORY_LIMIT) {
      this.executionHistory = this.executionHistory.slice(-this.HISTORY_LIMIT);
    }

    // Emit health snapshot
    this.emit('executionHealthSnapshot', snapshot);

    // Generate alerts
    await this.generateExecutionAlerts(snapshot);

    // Store in database
    await this.storeHealthSnapshot(snapshot);

    console.log(`⚡ Execution health check: ${snapshot.overallStatus} (${this.executionMetrics.successRate.toFixed(1)}% success rate)`);
  }

  /**
   * Calculate health scores for different aspects
   */
  private calculateHealthScores(): ExecutionHealthScores {
    return {
      latency: this.calculateLatencyScore(),
      fillRate: this.calculateFillRateScore(),
      slippage: this.calculateSlippageScore(),
      reliability: this.calculateReliabilityScore(),
      overall: 0, // Will be calculated based on other scores
    };
  }

  /**
   * Calculate latency health score
   */
  private calculateLatencyScore(): number {
    const avgLatency = this.executionMetrics.averageLatency;
    
    if (avgLatency <= this.HEALTH_THRESHOLDS.orderLatency.excellent) return 100;
    if (avgLatency <= this.HEALTH_THRESHOLDS.orderLatency.good) return 85;
    if (avgLatency <= this.HEALTH_THRESHOLDS.orderLatency.warning) return 65;
    if (avgLatency <= this.HEALTH_THRESHOLDS.orderLatency.critical) return 40;
    return 20;
  }

  /**
   * Calculate fill rate health score
   */
  private calculateFillRateScore(): number {
    const fillRate = this.executionMetrics.fillRate;
    
    if (fillRate >= this.HEALTH_THRESHOLDS.fillRate.excellent) return 100;
    if (fillRate >= this.HEALTH_THRESHOLDS.fillRate.good) return 85;
    if (fillRate >= this.HEALTH_THRESHOLDS.fillRate.warning) return 65;
    if (fillRate >= this.HEALTH_THRESHOLDS.fillRate.critical) return 40;
    return 20;
  }

  /**
   * Calculate slippage health score
   */
  private calculateSlippageScore(): number {
    const avgSlippage = this.executionMetrics.averageSlippage;
    
    if (avgSlippage <= this.HEALTH_THRESHOLDS.slippage.excellent) return 100;
    if (avgSlippage <= this.HEALTH_THRESHOLDS.slippage.good) return 85;
    if (avgSlippage <= this.HEALTH_THRESHOLDS.slippage.warning) return 65;
    if (avgSlippage <= this.HEALTH_THRESHOLDS.slippage.critical) return 40;
    return 20;
  }

  /**
   * Calculate reliability health score
   */
  private calculateReliabilityScore(): number {
    const rejectionRate = this.executionMetrics.rejectionRate;
    
    if (rejectionRate <= this.HEALTH_THRESHOLDS.rejectionRate.excellent) return 100;
    if (rejectionRate <= this.HEALTH_THRESHOLDS.rejectionRate.good) return 85;
    if (rejectionRate <= this.HEALTH_THRESHOLDS.rejectionRate.warning) return 65;
    if (rejectionRate <= this.HEALTH_THRESHOLDS.rejectionRate.critical) return 40;
    return 20;
  }

  /**
   * Determine overall execution health
   */
  private determineOverallExecutionHealth(scores: ExecutionHealthScores): 'excellent' | 'good' | 'warning' | 'critical' {
    scores.overall = (scores.latency + scores.fillRate + scores.slippage + scores.reliability) / 4;

    if (scores.overall >= 90) return 'excellent';
    if (scores.overall >= 75) return 'good';
    if (scores.overall >= 60) return 'warning';
    return 'critical';
  }

  /**
   * Identify health issues
   */
  private identifyHealthIssues(): string[] {
    const issues: string[] = [];

    if (this.executionMetrics.averageLatency > this.HEALTH_THRESHOLDS.orderLatency.critical) {
      issues.push('High order execution latency');
    }

    if (this.executionMetrics.fillRate < this.HEALTH_THRESHOLDS.fillRate.warning) {
      issues.push('Low fill rate');
    }

    if (this.executionMetrics.rejectionRate > this.HEALTH_THRESHOLDS.rejectionRate.warning) {
      issues.push('High order rejection rate');
    }

    if (this.executionMetrics.averageSlippage > this.HEALTH_THRESHOLDS.slippage.warning) {
      issues.push('High slippage');
    }

    // Check for timeout orders
    if (this.executionMetrics.timeoutOrders > this.executionMetrics.totalOrders * 0.05) {
      issues.push('Frequent order timeouts');
    }

    return issues;
  }

  /**
   * Generate recommendations
   */
  private generateRecommendations(): string[] {
    const recommendations: string[] = [];

    if (this.executionMetrics.averageLatency > this.HEALTH_THRESHOLDS.orderLatency.warning) {
      recommendations.push('Consider optimizing order routing');
      recommendations.push('Review network latency to brokers');
    }

    if (this.executionMetrics.rejectionRate > this.HEALTH_THRESHOLDS.rejectionRate.good) {
      recommendations.push('Review order validation logic');
      recommendations.push('Check margin and position limits');
    }

    if (this.executionMetrics.averageSlippage > this.HEALTH_THRESHOLDS.slippage.good) {
      recommendations.push('Consider using limit orders for better execution');
      recommendations.push('Review trading during high volatility periods');
    }

    return recommendations;
  }

  /**
   * Generate execution alerts
   */
  private async generateExecutionAlerts(snapshot: ExecutionHealthSnapshot): Promise<void> {
    // High latency alert
    if (this.executionMetrics.averageLatency > this.HEALTH_THRESHOLDS.orderLatency.critical) {
      this.emit('alert', {
        id: crypto.randomUUID(),
        type: 'execution_high_latency',
        severity: 'high',
        message: `High execution latency: ${Math.round(this.executionMetrics.averageLatency)}ms`,
        timestamp: new Date(),
        metadata: {
          averageLatency: this.executionMetrics.averageLatency,
          threshold: this.HEALTH_THRESHOLDS.orderLatency.critical,
        },
      });
    }

    // Low fill rate alert
    if (this.executionMetrics.fillRate < this.HEALTH_THRESHOLDS.fillRate.critical) {
      this.emit('alert', {
        id: crypto.randomUUID(),
        type: 'execution_low_fill_rate',
        severity: 'critical',
        message: `Low fill rate: ${this.executionMetrics.fillRate.toFixed(1)}%`,
        timestamp: new Date(),
        metadata: {
          fillRate: this.executionMetrics.fillRate,
          threshold: this.HEALTH_THRESHOLDS.fillRate.critical,
        },
      });
    }

    // High rejection rate alert
    if (this.executionMetrics.rejectionRate > this.HEALTH_THRESHOLDS.rejectionRate.critical) {
      this.emit('alert', {
        id: crypto.randomUUID(),
        type: 'execution_high_rejection_rate',
        severity: 'high',
        message: `High rejection rate: ${this.executionMetrics.rejectionRate.toFixed(1)}%`,
        timestamp: new Date(),
        metadata: {
          rejectionRate: this.executionMetrics.rejectionRate,
          threshold: this.HEALTH_THRESHOLDS.rejectionRate.critical,
        },
      });
    }
  }

  /**
   * Clean up old order tracking data
   */
  private cleanupOrderTracking(): void {
    const now = Date.now();
    const orderIds = Array.from(this.orderTracking.keys());
    
    for (const orderId of orderIds) {
      const execution = this.orderTracking.get(orderId);
      if (execution && execution.submittedAt) {
        const age = now - execution.submittedAt.getTime();
        
        // Mark timeout orders
        if (age > this.ORDER_TIMEOUT && execution.status === 'pending') {
          execution.status = 'timeout';
          execution.completedAt = new Date();
          this.executionMetrics.timeoutOrders++;
          this.finalizeOrderExecution(execution);
        }
        
        // Clean up old completed orders
        if (age > 5 * 60 * 1000 && execution.status !== 'pending') {
          this.orderTracking.delete(orderId);
        }
      }
    }
  }

  /**
   * Store order execution in database
   */
  private async storeOrderExecution(execution: OrderExecution): Promise<void> {
    try {
      await this.prisma.orderExecution.create({
        data: {
          orderId: execution.orderId,
          clientOrderId: execution.clientOrderId,
          symbol: execution.symbol,
          side: execution.side,
          type: execution.type,
          quantity: execution.quantity,
          price: execution.price,
          stopPrice: execution.stopPrice,
          submittedAt: execution.submittedAt,
          acknowledgedAt: execution.acknowledgedAt,
          completedAt: execution.completedAt,
          status: execution.status,
          filledQuantity: execution.filledQuantity,
          avgFillPrice: execution.avgFillPrice,
          fills: JSON.stringify(execution.fills),
          events: JSON.stringify(execution.events),
          latency: execution.metrics.latency,
          slippage: execution.metrics.slippage,
          fillRate: execution.metrics.fillRate,
          executionScore: execution.metrics.executionScore,
          rejectionReason: execution.rejectionReason,
          cancellationReason: execution.cancellationReason,
        },
      });
    } catch (error) {
      console.error('Failed to store order execution:', error);
    }
  }

  /**
   * Store health snapshot in database
   */
  private async storeHealthSnapshot(snapshot: ExecutionHealthSnapshot): Promise<void> {
    try {
      await this.prisma.executionHealthSnapshot.create({
        data: {
          id: snapshot.id,
          timestamp: snapshot.timestamp,
          overallStatus: snapshot.overallStatus,
          totalOrders: snapshot.metrics.totalOrders,
          successfulOrders: snapshot.metrics.successfulOrders,
          rejectedOrders: snapshot.metrics.rejectedOrders,
          averageLatency: snapshot.metrics.averageLatency,
          averageSlippage: snapshot.metrics.averageSlippage,
          fillRate: snapshot.metrics.fillRate,
          rejectionRate: snapshot.metrics.rejectionRate,
          successRate: snapshot.metrics.successRate,
          healthScores: JSON.stringify(snapshot.healthScores),
          issues: JSON.stringify(snapshot.issues),
          recommendations: JSON.stringify(snapshot.recommendations),
        },
      });
    } catch (error) {
      console.error('Failed to store execution health snapshot:', error);
    }
  }

  /**
   * Get current execution health status
   */
  getCurrentHealthStatus(): ExecutionHealthStatus {
    const latestSnapshot = this.executionHistory[this.executionHistory.length - 1];
    
    return {
      isMonitoring: this.isMonitoring,
      lastCheck: latestSnapshot?.timestamp || null,
      overallStatus: latestSnapshot?.overallStatus || 'unknown',
      metrics: this.executionMetrics,
      healthScores: latestSnapshot?.healthScores || {
        latency: 0,
        fillRate: 0,
        slippage: 0,
        reliability: 0,
        overall: 0,
      },
      activeOrders: this.orderTracking.size,
      issues: latestSnapshot?.issues || [],
      recommendations: latestSnapshot?.recommendations || [],
    };
  }

  /**
   * Get execution health history
   */
  getHealthHistory(timeRange: string = '1h'): ExecutionHealthSnapshot[] {
    const timeRangeMs = this.getTimeRangeMs(timeRange);
    const cutoffTime = new Date(Date.now() - timeRangeMs);

    return this.executionHistory.filter(snapshot => 
      snapshot.timestamp >= cutoffTime
    );
  }

  /**
   * Convert time range to milliseconds
   */
  private getTimeRangeMs(timeRange: string): number {
    switch (timeRange) {
      case '5m': return 5 * 60 * 1000;
      case '15m': return 15 * 60 * 1000;
      case '1h': return 60 * 60 * 1000;
      case '4h': return 4 * 60 * 60 * 1000;
      case '24h': return 24 * 60 * 60 * 1000;
      case '7d': return 7 * 24 * 60 * 60 * 1000;
      default: return 60 * 60 * 1000;
    }
  }
}

// Type definitions
interface ExecutionMonitoringConfig {
  intervalMs?: number;
  orderTimeout?: number;
}

interface OrderRequest {
  orderId: string;
  clientOrderId?: string;
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop' | 'stopLimit';
  quantity: number;
  price?: number;
  stopPrice?: number;
  timeInForce?: 'GTC' | 'IOC' | 'FOK';
}

interface OrderExecution {
  orderId: string;
  clientOrderId?: string;
  brokerOrderId?: string;
  symbol: string;
  side: 'buy' | 'sell';
  type: 'market' | 'limit' | 'stop' | 'stopLimit';
  quantity: number;
  price?: number;
  stopPrice?: number;
  submittedAt: Date;
  acknowledgedAt?: Date;
  lastFillAt?: Date;
  completedAt?: Date;
  rejectedAt?: Date;
  cancelledAt?: Date;
  status: 'pending' | 'acknowledged' | 'partially_filled' | 'filled' | 'rejected' | 'cancelled' | 'timeout';
  filledQuantity?: number;
  avgFillPrice?: number;
  fills: FillEvent[];
  events: ExecutionEvent[];
  metrics: {
    latency: number;
    slippage: number;
    fillRate: number;
    executionScore: number;
  };
  rejectionReason?: string;
  cancellationReason?: string;
}

interface FillEvent {
  fillId: string;
  timestamp: Date;
  quantity: number;
  price: number;
  fee: number;
  feeAsset: string;
}

interface ExecutionEvent {
  timestamp: Date;
  event: 'submitted' | 'acknowledged' | 'filled' | 'rejected' | 'cancelled';
  data: any;
}

interface OrderAcknowledgment {
  brokerOrderId: string;
  timestamp: Date;
}

interface OrderFill {
  fillId: string;
  quantity: number;
  price: number;
  fee: number;
  feeAsset: string;
  timestamp: Date;
}

interface OrderRejection {
  reason: string;
  code?: string;
  timestamp: Date;
}

interface OrderCancellation {
  reason: string;
  timestamp: Date;
}

interface ExecutionMetrics {
  totalOrders: number;
  successfulOrders: number;
  rejectedOrders: number;
  partialFills: number;
  cancelledOrders: number;
  timeoutOrders: number;
  totalLatency: number;
  totalSlippage: number;
  averageLatency: number;
  averageSlippage: number;
  fillRate: number;
  rejectionRate: number;
  successRate: number;
  recentExecutions: OrderExecution[];
  ordersByType: Record<string, {
    total: number;
    successful: number;
    avgLatency: number;
    avgSlippage: number;
  }>;
  ordersBySymbol: Map<string, {
    total: number;
    successful: number;
    avgLatency: number;
    avgSlippage: number;
    rejectionRate: number;
  }>;
  hourlyStats: Map<number, {
    total: number;
    successful: number;
    avgLatency: number;
    avgSlippage: number;
  }>;
}

interface ExecutionHealthScores {
  latency: number;
  fillRate: number;
  slippage: number;
  reliability: number;
  overall: number;
}

interface ExecutionHealthSnapshot {
  id: string;
  timestamp: Date;
  overallStatus: 'excellent' | 'good' | 'warning' | 'critical' | 'unknown';
  metrics: ExecutionMetrics;
  healthScores: ExecutionHealthScores;
  issues: string[];
  recommendations: string[];
}

interface ExecutionHealthStatus {
  isMonitoring: boolean;
  lastCheck: Date | null;
  overallStatus: 'excellent' | 'good' | 'warning' | 'critical' | 'unknown';
  metrics: ExecutionMetrics;
  healthScores: ExecutionHealthScores;
  activeOrders: number;
  issues: string[];
  recommendations: string[];
}

export type {
  ExecutionMonitoringConfig,
  OrderRequest,
  OrderExecution,
  ExecutionMetrics,
  ExecutionHealthSnapshot,
  ExecutionHealthStatus,
};