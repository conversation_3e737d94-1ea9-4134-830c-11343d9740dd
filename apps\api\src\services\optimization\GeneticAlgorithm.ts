/**
 * Genetic Algorithm Optimization Engine
 * 
 * @fileoverview Core genetic algorithm implementation for strategy parameter optimization
 * Includes selection, crossover, mutation, and convergence detection mechanisms
 */

import { randomUUID } from 'crypto';
import type {
  GeneticAlgorithmConfig,
  Individual,
  Population,
  StrategyParameters,
  ParameterDefinition,
  PerformanceMetrics,
  FitnessFunction,
  OptimizationProgress,
  OptimizationStatus
} from '@golddaddy/types/optimization';

// ===== Core Genetic Algorithm Class =====

export class GeneticAlgorithm {
  private config: GeneticAlgorithmConfig;
  private parameterDefinitions: ParameterDefinition[];
  private fitnessFunction: FitnessFunction;
  private currentPopulation: Population | null = null;
  private bestIndividualEver: Individual | null = null;
  private convergenceHistory: number[] = [];
  private startTime: Date;
  private status: OptimizationStatus = 'pending';
  private cancellationRequested = false;

  constructor(
    config: GeneticAlgorithmConfig,
    parameterDefinitions: ParameterDefinition[],
    fitnessFunction: FitnessFunction
  ) {
    this.config = this.validateConfig(config);
    this.parameterDefinitions = parameterDefinitions;
    this.fitnessFunction = fitnessFunction;
    this.startTime = new Date();
  }

  // ===== Main Optimization Methods =====

  /**
   * Execute the genetic algorithm optimization
   */
  public async optimize(
    evaluatePerformance: (parameters: StrategyParameters) => Promise<PerformanceMetrics>,
    onProgress?: (progress: OptimizationProgress) => void
  ): Promise<Individual> {
    this.status = 'running';
    this.startTime = new Date();

    try {
      // Initialize population
      this.currentPopulation = await this.initializePopulation(evaluatePerformance);
      this.updateBestIndividual();

      // Evolution loop
      for (let generation = 1; generation <= this.config.maxGenerations; generation++) {
        if (this.cancellationRequested) {
          this.status = 'cancelled';
          break;
        }

        // Evolve to next generation
        this.currentPopulation = await this.evolveGeneration(
          this.currentPopulation,
          evaluatePerformance
        );

        // Update best individual and convergence tracking
        this.updateBestIndividual();
        this.updateConvergenceHistory();

        // Report progress
        if (onProgress) {
          const progress = this.calculateProgress(generation);
          onProgress(progress);
        }

        // Check convergence
        if (this.hasConverged()) {
          this.status = 'completed';
          break;
        }

        // Adaptive mutation rate adjustment
        if (this.config.adaptiveMutation) {
          this.adjustMutationRate(generation);
        }

        // Population diversity management
        if (generation % 25 === 0) {
          this.manageDiversity();
        }
      }

      if (this.status === 'running') {
        this.status = 'completed';
      }

      return this.bestIndividualEver!;

    } catch (error) {
      this.status = 'failed';
      throw error;
    }
  }

  /**
   * Request cancellation of the optimization
   */
  public cancel(): void {
    this.cancellationRequested = true;
  }

  /**
   * Get current optimization progress
   */
  public getProgress(): OptimizationProgress {
    const generation = this.currentPopulation?.generation || 0;
    return this.calculateProgress(generation);
  }

  // ===== Population Management =====

  /**
   * Initialize the first generation population
   */
  private async initializePopulation(
    evaluatePerformance: (parameters: StrategyParameters) => Promise<PerformanceMetrics>
  ): Promise<Population> {
    const individuals: Individual[] = [];

    // Create random individuals
    for (let i = 0; i < this.config.populationSize; i++) {
      const parameters = this.generateRandomParameters();
      const performance = await evaluatePerformance(parameters);
      const fitness = this.calculateFitness(performance);

      individuals.push({
        id: randomUUID(),
        parameters,
        fitness,
        generation: 0,
        rank: 0
      });
    }

    // Sort by fitness (descending)
    individuals.sort((a, b) => b.fitness - a.fitness);
    
    // Assign ranks
    individuals.forEach((individual, index) => {
      individual.rank = index + 1;
    });

    return {
      individuals,
      generation: 0,
      bestIndividual: individuals[0],
      averageFitness: this.calculateAverageFitness(individuals),
      diversityScore: this.calculateDiversityScore(individuals)
    };
  }

  /**
   * Evolve population to next generation
   */
  private async evolveGeneration(
    population: Population,
    evaluatePerformance: (parameters: StrategyParameters) => Promise<PerformanceMetrics>
  ): Promise<Population> {
    const newIndividuals: Individual[] = [];
    const nextGeneration = population.generation + 1;

    // Elitism: Keep top performers
    const eliteCount = Math.floor(this.config.populationSize * this.config.elitismRate);
    const elites = population.individuals.slice(0, eliteCount);
    newIndividuals.push(...elites.map(elite => ({ ...elite, generation: nextGeneration })));

    // Generate new individuals through selection, crossover, and mutation
    while (newIndividuals.length < this.config.populationSize) {
      // Selection
      const parent1 = this.selectParent(population.individuals);
      const parent2 = this.selectParent(population.individuals);

      // Crossover
      let offspring: StrategyParameters;
      if (Math.random() < this.config.crossoverRate) {
        offspring = this.crossover(parent1.parameters, parent2.parameters);
      } else {
        offspring = Math.random() < 0.5 ? { ...parent1.parameters } : { ...parent2.parameters };
      }

      // Mutation
      if (Math.random() < this.config.mutationRate) {
        offspring = this.mutate(offspring);
      }

      // Evaluate new individual
      try {
        const performance = await evaluatePerformance(offspring);
        const fitness = this.calculateFitness(performance);

        newIndividuals.push({
          id: randomUUID(),
          parameters: offspring,
          fitness,
          generation: nextGeneration,
          rank: 0,
          parentIds: [parent1.id, parent2.id]
        });
      } catch (error) {
        // If evaluation fails, create a random individual instead
        const randomParams = this.generateRandomParameters();
        const performance = await evaluatePerformance(randomParams);
        const fitness = this.calculateFitness(performance);

        newIndividuals.push({
          id: randomUUID(),
          parameters: randomParams,
          fitness,
          generation: nextGeneration,
          rank: 0
        });
      }
    }

    // Sort by fitness and assign ranks
    newIndividuals.sort((a, b) => b.fitness - a.fitness);
    newIndividuals.forEach((individual, index) => {
      individual.rank = index + 1;
    });

    return {
      individuals: newIndividuals.slice(0, this.config.populationSize),
      generation: nextGeneration,
      bestIndividual: newIndividuals[0],
      averageFitness: this.calculateAverageFitness(newIndividuals),
      diversityScore: this.calculateDiversityScore(newIndividuals)
    };
  }

  // ===== Selection Methods =====

  /**
   * Tournament selection for parent selection
   */
  private selectParent(individuals: Individual[]): Individual {
    switch (this.config.selectionMethod) {
      case 'tournament':
        return this.tournamentSelection(individuals);
      case 'roulette':
        return this.rouletteWheelSelection(individuals);
      case 'rank':
        return this.rankBasedSelection(individuals);
      default:
        return this.tournamentSelection(individuals);
    }
  }

  /**
   * Tournament selection implementation
   */
  private tournamentSelection(individuals: Individual[]): Individual {
    const tournament: Individual[] = [];
    
    for (let i = 0; i < this.config.tournamentSize; i++) {
      const randomIndex = Math.floor(Math.random() * individuals.length);
      tournament.push(individuals[randomIndex]);
    }

    // Return the best individual from tournament
    return tournament.reduce((best, current) => 
      current.fitness > best.fitness ? current : best
    );
  }

  /**
   * Roulette wheel selection implementation
   */
  private rouletteWheelSelection(individuals: Individual[]): Individual {
    const totalFitness = individuals.reduce((sum, ind) => sum + Math.max(0, ind.fitness), 0);
    
    if (totalFitness === 0) {
      // Fallback to random selection if all fitness values are negative
      return individuals[Math.floor(Math.random() * individuals.length)];
    }

    const randomValue = Math.random() * totalFitness;
    let runningSum = 0;

    for (const individual of individuals) {
      runningSum += Math.max(0, individual.fitness);
      if (runningSum >= randomValue) {
        return individual;
      }
    }

    return individuals[individuals.length - 1];
  }

  /**
   * Rank-based selection implementation
   */
  private rankBasedSelection(individuals: Individual[]): Individual {
    const totalRanks = individuals.length * (individuals.length + 1) / 2;
    const randomValue = Math.random() * totalRanks;
    let runningSum = 0;

    for (let i = 0; i < individuals.length; i++) {
      const rank = individuals.length - i; // Higher rank for better fitness
      runningSum += rank;
      if (runningSum >= randomValue) {
        return individuals[i];
      }
    }

    return individuals[0];
  }

  // ===== Crossover Methods =====

  /**
   * Crossover operation between two parents
   */
  private crossover(parent1: StrategyParameters, parent2: StrategyParameters): StrategyParameters {
    switch (this.config.crossoverMethod) {
      case 'uniform':
        return this.uniformCrossover(parent1, parent2);
      case 'single_point':
        return this.singlePointCrossover(parent1, parent2);
      case 'two_point':
        return this.twoPointCrossover(parent1, parent2);
      default:
        return this.uniformCrossover(parent1, parent2);
    }
  }

  /**
   * Uniform crossover implementation
   */
  private uniformCrossover(parent1: StrategyParameters, parent2: StrategyParameters): StrategyParameters {
    const offspring: StrategyParameters = {};

    for (const paramName of Object.keys(parent1)) {
      offspring[paramName] = Math.random() < 0.5 ? parent1[paramName] : parent2[paramName];
    }

    return offspring;
  }

  /**
   * Single-point crossover implementation
   */
  private singlePointCrossover(parent1: StrategyParameters, parent2: StrategyParameters): StrategyParameters {
    const paramNames = Object.keys(parent1);
    const crossoverPoint = Math.floor(Math.random() * paramNames.length);
    const offspring: StrategyParameters = {};

    for (let i = 0; i < paramNames.length; i++) {
      const paramName = paramNames[i];
      offspring[paramName] = i < crossoverPoint ? parent1[paramName] : parent2[paramName];
    }

    return offspring;
  }

  /**
   * Two-point crossover implementation
   */
  private twoPointCrossover(parent1: StrategyParameters, parent2: StrategyParameters): StrategyParameters {
    const paramNames = Object.keys(parent1);
    const point1 = Math.floor(Math.random() * paramNames.length);
    const point2 = Math.floor(Math.random() * paramNames.length);
    const [start, end] = [Math.min(point1, point2), Math.max(point1, point2)];
    
    const offspring: StrategyParameters = {};

    for (let i = 0; i < paramNames.length; i++) {
      const paramName = paramNames[i];
      const useParent1 = i < start || i >= end;
      offspring[paramName] = useParent1 ? parent1[paramName] : parent2[paramName];
    }

    return offspring;
  }

  // ===== Mutation Methods =====

  /**
   * Mutation operation
   */
  private mutate(parameters: StrategyParameters): StrategyParameters {
    const mutated = { ...parameters };

    for (const paramDef of this.parameterDefinitions) {
      if (Math.random() < this.config.mutationRate) {
        mutated[paramDef.name] = this.mutateParameter(paramDef, mutated[paramDef.name]);
      }
    }

    return mutated;
  }

  /**
   * Mutate a single parameter based on its type
   */
  private mutateParameter(paramDef: ParameterDefinition, currentValue: number | string): number | string {
    if (paramDef.type === 'continuous') {
      // Gaussian mutation for continuous parameters
      const range = paramDef.constraint.max - paramDef.constraint.min;
      const stdDev = range * this.config.mutationStrength;
      const gaussian = this.generateGaussianRandom(0, stdDev);
      
      const newValue = (currentValue as number) + gaussian;
      return this.clampToConstraints(newValue, paramDef.constraint);
    } else if (paramDef.type === 'discrete') {
      // Uniform mutation for discrete parameters
      const step = paramDef.constraint.step || 1;
      const numSteps = Math.floor((paramDef.constraint.max - paramDef.constraint.min) / step) + 1;
      const randomStep = Math.floor(Math.random() * numSteps);
      
      return paramDef.constraint.min + (randomStep * step);
    } else {
      // For categorical parameters, implement specific logic if needed
      return currentValue;
    }
  }

  // ===== Fitness and Performance Calculation =====

  /**
   * Calculate fitness score from performance metrics
   */
  private calculateFitness(performance: PerformanceMetrics): number {
    const weights = this.fitnessFunction.weights;
    const penalties = this.fitnessFunction.penalties;

    // Base fitness from weighted metrics
    let fitness = 0;
    fitness += performance.sharpeRatio * weights.sharpeRatio;
    fitness += performance.profitFactor * weights.profitFactor;

    // Max drawdown penalty
    const drawdownPenalty = Math.max(0, performance.maxDrawdown - penalties.maxDrawdownThreshold) * weights.maxDrawdownPenalty;
    fitness -= drawdownPenalty;

    // Additional penalties
    if (performance.tradeCount < penalties.minTradeCount) {
      fitness *= 0.8; // 20% penalty for insufficient trades
    }

    if (performance.winRate < penalties.minWinRate) {
      fitness *= 0.9; // 10% penalty for low win rate
    }

    return fitness;
  }

  // ===== Convergence and Diversity Management =====

  /**
   * Check if algorithm has converged
   */
  private hasConverged(): boolean {
    if (this.convergenceHistory.length < this.config.convergenceGenerations) {
      return false;
    }

    const recentHistory = this.convergenceHistory.slice(-this.config.convergenceGenerations);
    const improvement = recentHistory[recentHistory.length - 1] - recentHistory[0];
    
    return Math.abs(improvement) < this.config.convergenceThreshold;
  }

  /**
   * Update convergence history tracking
   */
  private updateConvergenceHistory(): void {
    if (this.currentPopulation) {
      this.convergenceHistory.push(this.currentPopulation.bestIndividual.fitness);
      
      // Keep only recent history for memory efficiency
      if (this.convergenceHistory.length > this.config.convergenceGenerations * 2) {
        this.convergenceHistory = this.convergenceHistory.slice(-this.config.convergenceGenerations);
      }
    }
  }

  /**
   * Manage population diversity by replacing similar individuals
   */
  private manageDiversity(): void {
    if (!this.currentPopulation) return;

    const individuals = this.currentPopulation.individuals;
    const diversityThreshold = this.config.diversityThreshold;
    
    // Find individuals that are too similar
    const toReplace: number[] = [];
    for (let i = 1; i < individuals.length; i++) { // Skip the best individual
      for (let j = 0; j < i; j++) {
        const similarity = this.calculateSimilarity(individuals[i].parameters, individuals[j].parameters);
        if (similarity > (1 - diversityThreshold)) {
          toReplace.push(i);
          break;
        }
      }
    }

    // Replace similar individuals with random ones
    const replaceCount = Math.min(toReplace.length, Math.floor(individuals.length * this.config.replacementRate));
    for (let i = 0; i < replaceCount; i++) {
      const index = toReplace[i];
      individuals[index] = {
        ...individuals[index],
        parameters: this.generateRandomParameters(),
        id: randomUUID()
      };
    }
  }

  /**
   * Calculate similarity between two parameter sets
   */
  private calculateSimilarity(params1: StrategyParameters, params2: StrategyParameters): number {
    const paramNames = Object.keys(params1);
    let totalSimilarity = 0;

    for (const paramName of paramNames) {
      const paramDef = this.parameterDefinitions.find(p => p.name === paramName);
      if (!paramDef) continue;

      const value1 = params1[paramName] as number;
      const value2 = params2[paramName] as number;
      const range = paramDef.constraint.max - paramDef.constraint.min;
      
      const normalizedDiff = Math.abs(value1 - value2) / range;
      const similarity = 1 - normalizedDiff;
      totalSimilarity += similarity;
    }

    return totalSimilarity / paramNames.length;
  }

  /**
   * Adjust mutation rate based on convergence
   */
  private adjustMutationRate(generation: number): void {
    const recentGenerations = Math.min(10, this.convergenceHistory.length);
    if (recentGenerations < 5) return;

    const recentHistory = this.convergenceHistory.slice(-recentGenerations);
    const avgImprovement = (recentHistory[recentHistory.length - 1] - recentHistory[0]) / recentGenerations;

    // Increase mutation if improvement is slow, decrease if improving rapidly
    if (Math.abs(avgImprovement) < this.config.convergenceThreshold * 0.5) {
      this.config.mutationRate = Math.min(0.3, this.config.mutationRate * 1.1);
    } else {
      this.config.mutationRate = Math.max(0.05, this.config.mutationRate * 0.95);
    }
  }

  // ===== Utility Methods =====

  /**
   * Generate random parameters within constraints
   */
  private generateRandomParameters(): StrategyParameters {
    const parameters: StrategyParameters = {};

    for (const paramDef of this.parameterDefinitions) {
      if (paramDef.type === 'continuous') {
        const range = paramDef.constraint.max - paramDef.constraint.min;
        parameters[paramDef.name] = paramDef.constraint.min + (Math.random() * range);
      } else if (paramDef.type === 'discrete') {
        const step = paramDef.constraint.step || 1;
        const numSteps = Math.floor((paramDef.constraint.max - paramDef.constraint.min) / step) + 1;
        const randomStep = Math.floor(Math.random() * numSteps);
        parameters[paramDef.name] = paramDef.constraint.min + (randomStep * step);
      } else {
        parameters[paramDef.name] = paramDef.constraint.defaultValue;
      }
    }

    return parameters;
  }

  /**
   * Clamp value to parameter constraints
   */
  private clampToConstraints(value: number, constraint: ParameterConstraint): number {
    return Math.max(constraint.min, Math.min(constraint.max, value));
  }

  /**
   * Generate Gaussian random number
   */
  private generateGaussianRandom(mean: number, stdDev: number): number {
    // Box-Muller transformation
    const u1 = Math.random();
    const u2 = Math.random();
    const z0 = Math.sqrt(-2 * Math.log(u1)) * Math.cos(2 * Math.PI * u2);
    return z0 * stdDev + mean;
  }

  /**
   * Calculate average fitness of population
   */
  private calculateAverageFitness(individuals: Individual[]): number {
    const sum = individuals.reduce((total, ind) => total + ind.fitness, 0);
    return sum / individuals.length;
  }

  /**
   * Calculate diversity score of population
   */
  private calculateDiversityScore(individuals: Individual[]): number {
    if (individuals.length < 2) return 1;

    let totalSimilarity = 0;
    let comparisons = 0;

    for (let i = 0; i < individuals.length; i++) {
      for (let j = i + 1; j < individuals.length; j++) {
        totalSimilarity += this.calculateSimilarity(individuals[i].parameters, individuals[j].parameters);
        comparisons++;
      }
    }

    const avgSimilarity = totalSimilarity / comparisons;
    return 1 - avgSimilarity; // Diversity is inverse of similarity
  }

  /**
   * Update best individual tracker
   */
  private updateBestIndividual(): void {
    if (!this.currentPopulation) return;

    const currentBest = this.currentPopulation.bestIndividual;
    
    if (!this.bestIndividualEver || currentBest.fitness > this.bestIndividualEver.fitness) {
      this.bestIndividualEver = { ...currentBest };
    }
  }

  /**
   * Calculate optimization progress
   */
  private calculateProgress(generation: number): OptimizationProgress {
    const percentComplete = (generation / this.config.maxGenerations) * 100;
    const elapsedTime = (Date.now() - this.startTime.getTime()) / 1000;
    const avgTimePerGeneration = generation > 0 ? elapsedTime / generation : 1;
    const remainingGenerations = this.config.maxGenerations - generation;
    const estimatedTimeRemaining = remainingGenerations * avgTimePerGeneration;

    return {
      id: randomUUID(),
      status: this.status,
      currentGeneration: generation,
      maxGenerations: this.config.maxGenerations,
      percentComplete: Math.min(100, percentComplete),
      estimatedTimeRemaining,
      
      bestFitness: this.bestIndividualEver?.fitness || 0,
      bestParameters: this.bestIndividualEver?.parameters || {},
      bestPerformance: {} as PerformanceMetrics, // Would be filled by external evaluator
      
      generationStats: {
        averageFitness: this.currentPopulation?.averageFitness || 0,
        diversityScore: this.currentPopulation?.diversityScore || 0,
        improvementRate: this.calculateImprovementRate()
      },
      
      resourceUsage: {
        cpuUsage: 0, // Would be measured externally
        memoryUsage: 0, // Would be measured externally
        processTime: elapsedTime
      },
      
      startTime: this.startTime,
      lastUpdateTime: new Date(),
      estimatedCompletionTime: new Date(Date.now() + (estimatedTimeRemaining * 1000))
    };
  }

  /**
   * Calculate fitness improvement rate
   */
  private calculateImprovementRate(): number {
    if (this.convergenceHistory.length < 2) return 0;

    const recentCount = Math.min(5, this.convergenceHistory.length);
    const recentHistory = this.convergenceHistory.slice(-recentCount);
    
    if (recentHistory.length < 2) return 0;

    const improvement = recentHistory[recentHistory.length - 1] - recentHistory[0];
    return improvement / (recentHistory.length - 1);
  }

  /**
   * Validate genetic algorithm configuration
   */
  private validateConfig(config: GeneticAlgorithmConfig): GeneticAlgorithmConfig {
    const validated = { ...config };

    // Validate population size
    validated.populationSize = Math.max(10, Math.min(200, validated.populationSize));
    
    // Validate rates
    validated.crossoverRate = Math.max(0, Math.min(1, validated.crossoverRate));
    validated.mutationRate = Math.max(0, Math.min(1, validated.mutationRate));
    validated.elitismRate = Math.max(0, Math.min(0.5, validated.elitismRate));
    validated.replacementRate = Math.max(0, Math.min(0.5, validated.replacementRate));

    // Validate tournament size
    validated.tournamentSize = Math.max(2, Math.min(validated.populationSize / 2, validated.tournamentSize));

    return validated;
  }
}