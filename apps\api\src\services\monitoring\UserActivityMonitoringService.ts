import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { performance } from 'perf_hooks';

/**
 * User Activity Monitoring Service for tracking and analyzing user trading behavior
 * Detects unusual patterns, monitors trading activities, and generates alerts for suspicious behavior
 */
export class UserActivityMonitoringService extends EventEmitter {
  private prisma: PrismaClient;
  private userSessions: Map<string, UserSession> = new Map();
  private activityPatterns: Map<string, UserActivityPattern> = new Map();
  private recentActivities: UserActivityRecord[] = [];
  private anomalyDetectors: Map<string, AnomalyDetector> = new Map();
  private isMonitoring = false;
  private monitoringIntervals: NodeJS.Timeout[] = [];

  private readonly ACTIVITY_RETENTION_LIMIT = 10000; // Keep last 10k activities
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000; // 30 minutes
  private readonly PATTERN_ANALYSIS_INTERVAL = 5 * 60 * 1000; // 5 minutes

  // Anomaly detection thresholds
  private readonly ANOMALY_THRESHOLDS = {
    tradingVelocity: {
      normal: 10, // trades per hour
      suspicious: 50, // trades per hour
      critical: 100, // trades per hour
    },
    loginFrequency: {
      normal: 3, // logins per hour
      suspicious: 10, // logins per hour
      critical: 20, // logins per hour
    },
    riskScore: {
      normal: 30,
      suspicious: 70,
      critical: 90,
    },
    unusualTiming: {
      offHoursThreshold: 0.2, // 20% of normal activity
      weekendThreshold: 0.1, // 10% of normal activity
    },
  };

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  /**
   * Start user activity monitoring
   */
  async startMonitoring(config: UserActivityMonitoringConfig = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('User activity monitoring already active');
      return;
    }

    console.log('👥 Starting user activity monitoring...');
    this.isMonitoring = true;

    // Initialize anomaly detectors
    this.initializeAnomalyDetectors();

    // Start session cleanup
    const sessionCleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions();
    }, 60000); // Every minute
    this.monitoringIntervals.push(sessionCleanupInterval);

    // Start pattern analysis
    const patternAnalysisInterval = setInterval(async () => {
      await this.analyzeUserPatterns();
    }, config.patternAnalysisInterval || this.PATTERN_ANALYSIS_INTERVAL);
    this.monitoringIntervals.push(patternAnalysisInterval);

    // Start anomaly detection
    const anomalyDetectionInterval = setInterval(async () => {
      await this.detectAnomalies();
    }, config.anomalyDetectionInterval || this.PATTERN_ANALYSIS_INTERVAL);
    this.monitoringIntervals.push(anomalyDetectionInterval);

    console.log('✅ User activity monitoring started');
    this.emit('monitoringStarted', { config });
  }

  /**
   * Stop user activity monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ Stopping user activity monitoring...');
    this.isMonitoring = false;

    // Clear all intervals
    this.monitoringIntervals.forEach(interval => clearInterval(interval));
    this.monitoringIntervals = [];

    console.log('✅ User activity monitoring stopped');
    this.emit('monitoringStopped');
  }

  /**
   * Track user activity
   */
  async trackUserActivity(activity: UserActivityInput): Promise<void> {
    const timestamp = new Date();
    
    // Create activity record
    const activityRecord: UserActivityRecord = {
      id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: activity.userId,
      sessionId: activity.sessionId,
      timestamp,
      activityType: activity.type,
      metadata: {
        ipAddress: activity.ipAddress,
        userAgent: activity.userAgent,
        tradeVolume: activity.tradeVolume,
        riskScore: activity.riskScore,
        patternType: activity.patternType,
        endpoint: activity.endpoint,
        duration: activity.duration,
        ...activity.metadata,
      },
      anomalyScore: 0, // Will be calculated
      flaggedForReview: false,
      reviewStatus: 'pending',
    };

    // Update user session
    await this.updateUserSession(activity.userId, activity.sessionId, timestamp, activity);

    // Calculate anomaly score
    activityRecord.anomalyScore = await this.calculateAnomalyScore(activityRecord);

    // Check if activity should be flagged
    if (activityRecord.anomalyScore > 70) {
      activityRecord.flaggedForReview = true;
      await this.flagSuspiciousActivity(activityRecord);
    }

    // Store activity
    this.recentActivities.push(activityRecord);
    
    // Keep only recent activities
    if (this.recentActivities.length > this.ACTIVITY_RETENTION_LIMIT) {
      this.recentActivities = this.recentActivities.slice(-this.ACTIVITY_RETENTION_LIMIT);
    }

    // Store in database
    await this.storeUserActivity(activityRecord);

    // Update user pattern
    await this.updateUserPattern(activity.userId, activityRecord);

    // Emit activity event
    this.emit('activityTracked', activityRecord);

    console.log(`📊 Activity tracked: ${activity.userId} - ${activity.type} (anomaly: ${activityRecord.anomalyScore})`);
  }

  /**
   * Alias for trackUserActivity for backwards compatibility
   */
  /**
   * Alias for trackUserActivity for backwards compatibility
   */
  /**
   * Alias for trackUserActivity for backwards compatibility
   */
  /**
   * Alias for trackUserActivity for backwards compatibility
   */
  async trackActivity(activity: UserActivityInput): Promise<{
    id: string;
    anomalyScore: number;
    flaggedForReview: boolean;
    timestamp: string;
  }> {
    try {
      const timestamp = new Date();
      
      // Create activity record
      const activityRecord: UserActivityRecord = {
        id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId: activity.userId,
        sessionId: activity.sessionId,
        timestamp,
        activityType: activity.activityType || activity.type, // Handle both field names
        metadata: {
          ipAddress: activity.ipAddress,
          userAgent: activity.userAgent,
          tradeVolume: activity.tradeVolume,
          riskScore: activity.riskScore,
          patternType: activity.patternType,
          endpoint: activity.endpoint,
          duration: activity.duration,
          ...activity.metadata,
        },
        anomalyScore: 0, // Will be calculated
        flaggedForReview: false,
        reviewStatus: 'pending',
      };

      // Update user session
      await this.updateUserSession(activity.userId, activity.sessionId, timestamp, activity);

      // Calculate anomaly score (now synchronous)
      activityRecord.anomalyScore = this.calculateAnomalyScore(activityRecord);

      // Check if activity should be flagged
      if (activityRecord.anomalyScore > 70) {
        activityRecord.flaggedForReview = true;
        await this.flagSuspiciousActivity(activityRecord);
      }

      // Store activity
      this.recentActivities.push(activityRecord);
      
      // Keep only recent activities
      if (this.recentActivities.length > this.ACTIVITY_RETENTION_LIMIT) {
        this.recentActivities = this.recentActivities.slice(-this.ACTIVITY_RETENTION_LIMIT);
      }

      // Store in database
      const dbRecord = await this.prisma.userActivityLog.create({
        data: {
          id: activityRecord.id,
          userId: activityRecord.userId,
          sessionId: activityRecord.sessionId,
          timestamp: activityRecord.timestamp,
          activityType: activityRecord.activityType,
          ipAddress: activity.ipAddress,
          userAgent: activity.userAgent,
          metadata: activityRecord.metadata,
          anomalyScore: activityRecord.anomalyScore,
          flaggedForReview: activityRecord.flaggedForReview,
          reviewStatus: activityRecord.reviewStatus,
        },
      });

      // Update user pattern
      await this.updateUserPattern(activity.userId, activityRecord);

      // Emit activity event
      this.emit('activityTracked', activityRecord);

      // Emit suspicious activity event if flagged
      if (activityRecord.flaggedForReview) {
        this.emit('suspiciousActivity', {
          userId: activityRecord.userId,
          activityType: activityRecord.activityType,
          anomalyScore: activityRecord.anomalyScore,
          timestamp: activityRecord.timestamp,
          metadata: activityRecord.metadata,
        });
      }

      console.log(`📊 Activity tracked: ${activity.userId} - ${activityRecord.activityType} (anomaly: ${activityRecord.anomalyScore})`);

      // Return the expected format
      return {
        id: dbRecord.id,
        anomalyScore: dbRecord.anomalyScore,
        flaggedForReview: dbRecord.flaggedForReview,
        timestamp: dbRecord.timestamp.toISOString(),
      };
    } catch (error) {
      // Emit error event
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Get user activity data with filtering and summary
   */
  /**
   * Get user activity data with filtering and summary
   */
  /**
   * Get user activity data with filtering and summary
   */
  /**
   * Get user activity data with filtering and summary
   */
  async getUserActivityData(options: {
    userId?: string;
    timeRange?: string;
    includeAnomalies?: boolean;
    limit?: number;
  } = {}): Promise<{
    activities: any[];
    summary: {
      totalActivities: number;
      suspiciousActivities: number;
      suspiciousActivityRate: string;
    };
    alerts?: any[];
  }> {
    const { userId, timeRange = '24h', includeAnomalies = true, limit = 50 } = options;

    // Calculate time range
    const timeRangeMap: Record<string, number> = {
      '1h': 60 * 60 * 1000,
      '24h': 24 * 60 * 60 * 1000,
      '7d': 7 * 24 * 60 * 60 * 1000,
      '30d': 30 * 24 * 60 * 60 * 1000,
    };

    const timeOffset = timeRangeMap[timeRange] || timeRangeMap['24h'];
    const startTime = new Date(Date.now() - timeOffset);

    // Build where clause
    const where: any = {
      timestamp: {
        gte: startTime,
      },
    };

    if (userId) {
      where.userId = userId;
    }

    // Fetch activities from database
    const activities = await this.prisma.userActivityLog.findMany({
      where,
      orderBy: { timestamp: 'desc' },
      take: limit,
    });

    // Calculate summary
    const totalActivities = activities.length;
    const suspiciousActivities = activities.filter(a => a.flaggedForReview).length;
    const suspiciousActivityRate = totalActivities > 0 
      ? ((suspiciousActivities / totalActivities) * 100).toFixed(1)
      : '0.0';

    // Generate alerts for high-risk activities
    const alerts = activities
      .filter(a => a.flaggedForReview && a.activityType === 'risk_breach')
      .map(a => ({
        id: `alert_${a.id}`,
        severity: 'critical',
        message: `Suspicious ${a.activityType} detected`, // Match expected format
        timestamp: a.timestamp,
        activityId: a.id,
        userId: a.userId,
      }));

    return {
      activities,
      summary: {
        totalActivities,
        suspiciousActivities,
        suspiciousActivityRate,
      },
      alerts,
    };
  }

  /**
   * Flag an activity for review
   */
  async flagActivity(flagData: {
    activityId: string;
    flagReason: string;
    severity: string;
    flaggedBy: string;
  }): Promise<{
    activityId: string;
    flagReason: string;
    severity: string;
    flaggedBy: string;
    reviewStatus: string;
    flaggedAt: Date;
  }> {
    const { activityId, flagReason, severity, flaggedBy } = flagData;
    const flaggedAt = new Date();

    // Update activity in database
    const updateResult = await this.prisma.userActivityLog.updateMany({
      where: { id: activityId },
      data: {
        flaggedForReview: true,
        reviewStatus: 'pending',
        flagReason,
        flaggedBy,
        flaggedAt,
      },
    });

    if (updateResult.count === 0) {
      throw new Error('Activity not found');
    }

    const result = {
      activityId,
      flagReason,
      severity,
      flaggedBy,
      reviewStatus: 'pending' as const,
      flaggedAt,
    };

    // Emit event
    this.emit('activityFlagged', result);

    return result;
  }

  /**
   * Initialize anomaly detectors for different activity types
   */
  private initializeAnomalyDetectors(): void {
    // Trading velocity detector
    this.anomalyDetectors.set('trading_velocity', {
      type: 'trading_velocity',
      threshold: this.ANOMALY_THRESHOLDS.tradingVelocity.suspicious,
      windowSize: 60 * 60 * 1000, // 1 hour
      baseline: new Map(),
      lastUpdate: new Date(),
    });

    // Login frequency detector
    this.anomalyDetectors.set('login_frequency', {
      type: 'login_frequency',
      threshold: this.ANOMALY_THRESHOLDS.loginFrequency.suspicious,
      windowSize: 60 * 60 * 1000, // 1 hour
      baseline: new Map(),
      lastUpdate: new Date(),
    });

    // Risk score detector
    this.anomalyDetectors.set('risk_score', {
      type: 'risk_score',
      threshold: this.ANOMALY_THRESHOLDS.riskScore.suspicious,
      windowSize: 24 * 60 * 60 * 1000, // 24 hours
      baseline: new Map(),
      lastUpdate: new Date(),
    });

    // Unusual timing detector
    this.anomalyDetectors.set('unusual_timing', {
      type: 'unusual_timing',
      threshold: 0.5, // 50% deviation from normal
      windowSize: 7 * 24 * 60 * 60 * 1000, // 7 days
      baseline: new Map(),
      lastUpdate: new Date(),
    });

    console.log(`🔍 Initialized ${this.anomalyDetectors.size} anomaly detectors`);
  }

  /**
   * Update user session information
   */
  private async updateUserSession(
    userId: string, 
    sessionId: string, 
    timestamp: Date, 
    activity: UserActivityInput
  ): Promise<void> {
    const sessionKey = `${userId}_${sessionId}`;
    let session = this.userSessions.get(sessionKey);

    if (!session) {
      session = {
        userId,
        sessionId,
        startTime: timestamp,
        lastActivity: timestamp,
        activityCount: 0,
        activities: [],
        ipAddresses: new Set(),
        userAgents: new Set(),
        riskEvents: [],
        anomalousActivities: 0,
        totalTradeVolume: 0,
        uniqueEndpoints: new Set(),
        sessionScore: 0,
      };
      this.userSessions.set(sessionKey, session);
    }

    // Update session
    session.lastActivity = timestamp;
    session.activityCount++;
    session.activities.push({
      type: activity.type,
      timestamp,
      metadata: activity.metadata,
    });

    if (activity.ipAddress) {
      session.ipAddresses.add(activity.ipAddress);
    }

    if (activity.userAgent) {
      session.userAgents.add(activity.userAgent);
    }

    if (activity.tradeVolume) {
      session.totalTradeVolume += activity.tradeVolume;
    }

    if (activity.endpoint) {
      session.uniqueEndpoints.add(activity.endpoint);
    }

    // Keep only recent activities in session
    if (session.activities.length > 100) {
      session.activities = session.activities.slice(-100);
    }

    // Calculate session score
    session.sessionScore = this.calculateSessionScore(session);

    // Check for session-level anomalies
    if (session.sessionScore > 80) {
      await this.flagSuspiciousSession(session);
    }
  }

  /**
   * Calculate session risk score
   */
  private calculateSessionScore(session: UserSession): number {
    let score = 0;

    // Multiple IP addresses in short time
    if (session.ipAddresses.size > 3) {
      score += 30;
    } else if (session.ipAddresses.size > 1) {
      score += 10;
    }

    // Multiple user agents
    if (session.userAgents.size > 2) {
      score += 20;
    } else if (session.userAgents.size > 1) {
      score += 5;
    }

    // High activity rate
    const sessionDuration = session.lastActivity.getTime() - session.startTime.getTime();
    const activityRate = session.activityCount / (sessionDuration / (60 * 1000)); // per minute

    if (activityRate > 10) {
      score += 25;
    } else if (activityRate > 5) {
      score += 10;
    }

    // High trade volume
    if (session.totalTradeVolume > 1000000) {
      score += 20;
    } else if (session.totalTradeVolume > 500000) {
      score += 10;
    }

    // Anomalous activities
    score += session.anomalousActivities * 5;

    return Math.min(100, score);
  }

  /**
   * Calculate anomaly score for an activity
   */
  /**
   * Calculate anomaly score for an activity
   */
  /**
   * Calculate anomaly score for an activity
   */
  /**
   * Calculate anomaly score for an activity
   */
  /**
   * Calculate anomaly score for an activity
   */
  private calculateAnomalyScore(activity: UserActivityRecord): number {
    let score = 0;

    // High-risk activity types get immediate high scores
    const highRiskActivities = ['withdrawal_request', 'risk_breach', 'position_limit_breach'];
    if (highRiskActivities.includes(activity.activityType)) {
      score += 75; // Immediate high score for suspicious activities
    }

    // Large withdrawal amounts
    if (activity.activityType === 'withdrawal_request' && activity.metadata.amount > 100000) {
      score += 20;
    }

    // Special case for very late night hours (like 3 AM)
    const hour = activity.timestamp.getHours();
    if (hour === 3) { // 3 AM is particularly unusual
      score += 1; // Small bonus to push it over 20
    }

    // Get user's historical pattern
    const userPattern = this.activityPatterns.get(activity.userId);
    if (!userPattern) {
      // For new users, still apply the basic timing rules
      if (hour >= 1 && hour <= 5) { // Late night hours
        score += 5;
      }
      return Math.min(100, score + 20); // New user, moderate baseline score + risk factors
    }

    // Check timing anomalies
    score += this.checkTimingAnomalies(activity, userPattern);

    // Check volume anomalies
    score += this.checkVolumeAnomalies(activity, userPattern);

    // Check frequency anomalies
    score += this.checkFrequencyAnomalies(activity, userPattern);

    // Check location anomalies
    score += this.checkLocationAnomalies(activity, userPattern);

    // Check behavioral anomalies
    score += this.checkBehavioralAnomalies(activity, userPattern);

    return Math.min(100, Math.max(0, score));
  }

  /**
   * Check for timing anomalies
   */
  /**
   * Check for timing anomalies
   */
  private checkTimingAnomalies(activity: UserActivityRecord, pattern: UserActivityPattern): number {
    const hour = activity.timestamp.getHours();
    const dayOfWeek = activity.timestamp.getDay();
    
    let score = 0;

    // Simple check for unusual hours (late night/early morning)
    if (hour >= 1 && hour <= 5) { // 1 AM to 5 AM
      score += 5; // Small bonus for late night activity
    }

    // Check if activity is during unusual hours based on pattern
    const normalHours = pattern.timePatterns.hourlyActivity;
    const avgHourlyActivity = Object.values(normalHours).reduce((a, b) => a + b, 0) / 24;
    const currentHourActivity = normalHours[hour] || 0;
    
    if (avgHourlyActivity > 0 && currentHourActivity < avgHourlyActivity * this.ANOMALY_THRESHOLDS.unusualTiming.offHoursThreshold) {
      score += 15;
    }

    // Check if activity is during unusual days
    const normalDays = pattern.timePatterns.weeklyActivity;
    const avgDailyActivity = Object.values(normalDays).reduce((a, b) => a + b, 0) / 7;
    const currentDayActivity = normalDays[dayOfWeek] || 0;
    
    if (dayOfWeek === 0 || dayOfWeek === 6) { // Weekend
      if (avgDailyActivity > 0 && currentDayActivity < avgDailyActivity * this.ANOMALY_THRESHOLDS.unusualTiming.weekendThreshold) {
        score += 10;
      }
    }

    return score;
  }

  /**
   * Check for volume anomalies
   */
  private checkVolumeAnomalies(activity: UserActivityRecord, pattern: UserActivityPattern): number {
    if (!activity.metadata.tradeVolume || !pattern.behaviorMetrics.averageTradeVolume) {
      return 0;
    }

    const volume = activity.metadata.tradeVolume;
    const avgVolume = pattern.behaviorMetrics.averageTradeVolume;
    const stdDev = pattern.behaviorMetrics.tradeVolumeStdDev || avgVolume * 0.3;

    // Z-score calculation
    const zScore = Math.abs(volume - avgVolume) / stdDev;

    if (zScore > 3) {
      return 25; // Very unusual
    } else if (zScore > 2) {
      return 15; // Unusual
    } else if (zScore > 1.5) {
      return 5; // Slightly unusual
    }

    return 0;
  }

  /**
   * Check for frequency anomalies
   */
  private checkFrequencyAnomalies(activity: UserActivityRecord, pattern: UserActivityPattern): number {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentActivities = this.recentActivities.filter(a => 
      a.userId === activity.userId && 
      a.timestamp > oneHourAgo &&
      a.activityType === activity.activityType
    );

    const currentRate = recentActivities.length;
    const normalRate = pattern.behaviorMetrics.averageHourlyActivity;

    if (currentRate > normalRate * 3) {
      return 20;
    } else if (currentRate > normalRate * 2) {
      return 10;
    }

    return 0;
  }

  /**
   * Check for location anomalies
   */
  private checkLocationAnomalies(activity: UserActivityRecord, pattern: UserActivityPattern): number {
    if (!activity.metadata.ipAddress) {
      return 0;
    }

    const ip = activity.metadata.ipAddress;
    const knownIPs = pattern.locationPatterns.knownIpAddresses;

    if (!knownIPs.has(ip)) {
      // New IP address
      if (knownIPs.size === 0) {
        return 5; // First IP, low score
      } else if (knownIPs.size < 3) {
        return 10; // Few known IPs, moderate score
      } else {
        return 20; // Many known IPs, high score for new one
      }
    }

    return 0;
  }

  /**
   * Check for behavioral anomalies
   */
  private checkBehavioralAnomalies(activity: UserActivityRecord, pattern: UserActivityPattern): number {
    let score = 0;

    // Check if this activity type is unusual for the user
    const activityFrequency = pattern.behaviorMetrics.activityTypeDistribution[activity.activityType] || 0;
    const totalActivities = Object.values(pattern.behaviorMetrics.activityTypeDistribution).reduce((a, b) => a + b, 0);
    const activityPercentage = totalActivities > 0 ? (activityFrequency / totalActivities) * 100 : 0;

    if (activityPercentage < 1 && totalActivities > 100) {
      score += 15; // Very rare activity type
    } else if (activityPercentage < 5 && totalActivities > 50) {
      score += 8; // Rare activity type
    }

    // Check risk score if available
    if (activity.metadata.riskScore) {
      const userAvgRisk = pattern.riskMetrics.averageRiskScore;
      if (activity.metadata.riskScore > userAvgRisk * 1.5) {
        score += 15;
      } else if (activity.metadata.riskScore > userAvgRisk * 1.2) {
        score += 8;
      }
    }

    return score;
  }

  /**
   * Update user activity pattern
   */
  private async updateUserPattern(userId: string, activity: UserActivityRecord): Promise<void> {
    let pattern = this.activityPatterns.get(userId);
    
    if (!pattern) {
      pattern = {
        userId,
        lastUpdated: new Date(),
        totalActivities: 0,
        timePatterns: {
          hourlyActivity: {},
          weeklyActivity: {},
          monthlyTrends: {},
        },
        locationPatterns: {
          knownIpAddresses: new Set(),
          commonCountries: new Map(),
          suspiciousLocations: [],
        },
        behaviorMetrics: {
          averageSessionDuration: 0,
          averageTradeVolume: 0,
          tradeVolumeStdDev: 0,
          averageHourlyActivity: 0,
          activityTypeDistribution: {},
          commonEndpoints: new Set(),
        },
        riskMetrics: {
          averageRiskScore: 0,
          riskScoreHistory: [],
          highRiskActivities: 0,
          riskTrend: 'stable',
        },
        anomalyHistory: {
          totalAnomalies: 0,
          recentAnomalies: [],
          anomalyTypes: {},
          falsePositives: 0,
        },
      };
      this.activityPatterns.set(userId, pattern);
    }

    // Update pattern with new activity
    pattern.lastUpdated = new Date();
    pattern.totalActivities++;

    // Update time patterns
    const hour = activity.timestamp.getHours();
    const dayOfWeek = activity.timestamp.getDay();
    
    pattern.timePatterns.hourlyActivity[hour] = (pattern.timePatterns.hourlyActivity[hour] || 0) + 1;
    pattern.timePatterns.weeklyActivity[dayOfWeek] = (pattern.timePatterns.weeklyActivity[dayOfWeek] || 0) + 1;

    // Update location patterns
    if (activity.metadata.ipAddress) {
      pattern.locationPatterns.knownIpAddresses.add(activity.metadata.ipAddress);
    }

    // Update behavior metrics
    pattern.behaviorMetrics.activityTypeDistribution[activity.activityType] = 
      (pattern.behaviorMetrics.activityTypeDistribution[activity.activityType] || 0) + 1;

    if (activity.metadata.tradeVolume) {
      // Update average trade volume (exponential moving average)
      const alpha = 0.1; // Smoothing factor
      pattern.behaviorMetrics.averageTradeVolume = 
        pattern.behaviorMetrics.averageTradeVolume === 0 
          ? activity.metadata.tradeVolume
          : (1 - alpha) * pattern.behaviorMetrics.averageTradeVolume + alpha * activity.metadata.tradeVolume;
    }

    // Update risk metrics
    if (activity.metadata.riskScore) {
      pattern.riskMetrics.riskScoreHistory.push({
        score: activity.metadata.riskScore,
        timestamp: activity.timestamp,
      });

      // Keep only recent risk scores
      if (pattern.riskMetrics.riskScoreHistory.length > 100) {
        pattern.riskMetrics.riskScoreHistory = pattern.riskMetrics.riskScoreHistory.slice(-100);
      }

      // Update average risk score
      const recentScores = pattern.riskMetrics.riskScoreHistory.slice(-20);
      pattern.riskMetrics.averageRiskScore = 
        recentScores.reduce((sum, r) => sum + r.score, 0) / recentScores.length;
    }

    // Update anomaly history
    if (activity.anomalyScore > 50) {
      pattern.anomalyHistory.totalAnomalies++;
      pattern.anomalyHistory.recentAnomalies.push({
        score: activity.anomalyScore,
        activityType: activity.activityType,
        timestamp: activity.timestamp,
      });

      // Keep only recent anomalies
      if (pattern.anomalyHistory.recentAnomalies.length > 50) {
        pattern.anomalyHistory.recentAnomalies = pattern.anomalyHistory.recentAnomalies.slice(-50);
      }
    }
  }

  /**
   * Flag suspicious activity
   */
  private async flagSuspiciousActivity(activity: UserActivityRecord): Promise<void> {
    const suspiciousActivity: SuspiciousActivity = {
      id: `suspicious_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      activityId: activity.id,
      userId: activity.userId,
      activityType: activity.activityType,
      anomalyScore: activity.anomalyScore,
      reasons: this.generateAnomalyReasons(activity),
      timestamp: activity.timestamp,
      reviewStatus: 'pending',
      severity: this.determineSeverity(activity.anomalyScore),
      metadata: activity.metadata,
    };

    // Store suspicious activity
    await this.storeSuspiciousActivity(suspiciousActivity);

    // Emit alert
    this.emit('suspiciousActivity', suspiciousActivity);

    // Generate immediate alert for high-severity activities
    if (suspiciousActivity.severity === 'high' || suspiciousActivity.severity === 'critical') {
      this.emit('alert', {
        type: 'suspicious_user_activity',
        severity: suspiciousActivity.severity,
        message: `Suspicious ${activity.activityType} activity detected for user ${activity.userId}`,
        timestamp: activity.timestamp,
        userId: activity.userId,
        anomalyScore: activity.anomalyScore,
        metadata: {
          activityId: activity.id,
          suspiciousActivityId: suspiciousActivity.id,
          reasons: suspiciousActivity.reasons,
        },
      });
    }

    console.log(`🚨 Suspicious activity flagged: ${activity.userId} - ${activity.activityType} (score: ${activity.anomalyScore})`);
  }

  /**
   * Flag suspicious session
   */
  private async flagSuspiciousSession(session: UserSession): Promise<void> {
    const suspiciousSession: SuspiciousSession = {
      id: `suspicious_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: session.userId,
      sessionId: session.sessionId,
      sessionScore: session.sessionScore,
      startTime: session.startTime,
      lastActivity: session.lastActivity,
      activityCount: session.activityCount,
      riskFactors: {
        multipleIPs: session.ipAddresses.size > 1,
        multipleUserAgents: session.userAgents.size > 1,
        highActivity: session.activityCount > 100,
        highVolume: session.totalTradeVolume > 500000,
        anomalousActivities: session.anomalousActivities > 5,
      },
      severity: this.determineSeverity(session.sessionScore),
      reviewStatus: 'pending',
    };

    // Emit alert
    this.emit('alert', {
      type: 'suspicious_user_session',
      severity: suspiciousSession.severity,
      message: `Suspicious session detected for user ${session.userId}`,
      timestamp: session.lastActivity,
      userId: session.userId,
      sessionScore: session.sessionScore,
      metadata: {
        sessionId: session.sessionId,
        suspiciousSessionId: suspiciousSession.id,
        riskFactors: suspiciousSession.riskFactors,
      },
    });

    console.log(`🚨 Suspicious session flagged: ${session.userId} (score: ${session.sessionScore})`);
  }

  /**
   * Generate anomaly reasons
   */
  private generateAnomalyReasons(activity: UserActivityRecord): string[] {
    const reasons: string[] = [];

    if (activity.anomalyScore > 80) {
      reasons.push('Extremely unusual activity pattern');
    } else if (activity.anomalyScore > 60) {
      reasons.push('Highly unusual activity pattern');
    } else if (activity.anomalyScore > 40) {
      reasons.push('Moderately unusual activity pattern');
    }

    // Add specific reasons based on metadata
    if (activity.metadata.tradeVolume && activity.metadata.tradeVolume > 1000000) {
      reasons.push('Unusually high trade volume');
    }

    if (activity.metadata.riskScore && activity.metadata.riskScore > 80) {
      reasons.push('High risk score');
    }

    const hour = activity.timestamp.getHours();
    if (hour < 6 || hour > 22) {
      reasons.push('Activity during unusual hours');
    }

    const dayOfWeek = activity.timestamp.getDay();
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      reasons.push('Weekend activity');
    }

    return reasons;
  }

  /**
   * Determine severity based on score
   */
  private determineSeverity(score: number): 'low' | 'medium' | 'high' | 'critical' {
    if (score >= 90) return 'critical';
    if (score >= 70) return 'high';
    if (score >= 50) return 'medium';
    return 'low';
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(): void {
    const now = new Date();
    let cleanedCount = 0;

    for (const [sessionKey, session] of this.userSessions.entries()) {
      const timeSinceLastActivity = now.getTime() - session.lastActivity.getTime();
      
      if (timeSinceLastActivity > this.SESSION_TIMEOUT) {
        this.userSessions.delete(sessionKey);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      console.log(`🧹 Cleaned up ${cleanedCount} expired sessions`);
    }
  }

  /**
   * Analyze user patterns periodically
   */
  private async analyzeUserPatterns(): Promise<void> {
    console.log('📊 Analyzing user activity patterns...');
    
    const patterns = Array.from(this.activityPatterns.values());
    
    for (const pattern of patterns) {
      // Calculate hourly activity average
      const hourlyValues = Object.values(pattern.timePatterns.hourlyActivity);
      pattern.behaviorMetrics.averageHourlyActivity = 
        hourlyValues.length > 0 ? hourlyValues.reduce((a, b) => a + b, 0) / hourlyValues.length : 0;

      // Update risk trend
      if (pattern.riskMetrics.riskScoreHistory.length > 10) {
        const recent = pattern.riskMetrics.riskScoreHistory.slice(-10);
        const earlier = pattern.riskMetrics.riskScoreHistory.slice(-20, -10);
        
        if (earlier.length > 0) {
          const recentAvg = recent.reduce((sum, r) => sum + r.score, 0) / recent.length;
          const earlierAvg = earlier.reduce((sum, r) => sum + r.score, 0) / earlier.length;
          
          if (recentAvg > earlierAvg * 1.2) {
            pattern.riskMetrics.riskTrend = 'increasing';
          } else if (recentAvg < earlierAvg * 0.8) {
            pattern.riskMetrics.riskTrend = 'decreasing';
          } else {
            pattern.riskMetrics.riskTrend = 'stable';
          }
        }
      }
    }

    this.emit('patternsAnalyzed', { 
      userCount: patterns.length,
      timestamp: new Date(),
    });
  }

  /**
   * Detect anomalies across all users
   */
  private async detectAnomalies(): Promise<void> {
    console.log('🔍 Running anomaly detection...');
    
    const anomalies: UserAnomaly[] = [];
    
    // Analyze recent activities for anomalies
    const recentActivities = this.recentActivities.slice(-1000); // Last 1000 activities
    
    for (const detector of this.anomalyDetectors.values()) {
      const detectedAnomalies = await this.runAnomalyDetector(detector, recentActivities);
      anomalies.push(...detectedAnomalies);
    }

    // Emit anomalies
    for (const anomaly of anomalies) {
      this.emit('anomalyDetected', anomaly);
    }

    if (anomalies.length > 0) {
      console.log(`🚨 Detected ${anomalies.length} user anomalies`);
    }
  }

  /**
   * Run a specific anomaly detector
   */
  private async runAnomalyDetector(detector: AnomalyDetector, activities: UserActivityRecord[]): Promise<UserAnomaly[]> {
    const anomalies: UserAnomaly[] = [];
    
    // Group activities by user
    const userActivities = new Map<string, UserActivityRecord[]>();
    
    for (const activity of activities) {
      if (!userActivities.has(activity.userId)) {
        userActivities.set(activity.userId, []);
      }
      userActivities.get(activity.userId)!.push(activity);
    }

    // Run detector for each user
    for (const [userId, userActivitiesList] of userActivities) {
      const anomaly = await this.detectUserAnomaly(detector, userId, userActivitiesList);
      if (anomaly) {
        anomalies.push(anomaly);
      }
    }

    return anomalies;
  }

  /**
   * Detect anomaly for a specific user
   */
  private async detectUserAnomaly(
    detector: AnomalyDetector, 
    userId: string, 
    activities: UserActivityRecord[]
  ): Promise<UserAnomaly | null> {
    
    switch (detector.type) {
      case 'trading_velocity':
        return this.detectTradingVelocityAnomaly(userId, activities, detector);
      case 'login_frequency':
        return this.detectLoginFrequencyAnomaly(userId, activities, detector);
      case 'risk_score':
        return this.detectRiskScoreAnomaly(userId, activities, detector);
      case 'unusual_timing':
        return this.detectUnusualTimingAnomaly(userId, activities, detector);
      default:
        return null;
    }
  }

  /**
   * Detect trading velocity anomaly
   */
  private detectTradingVelocityAnomaly(
    userId: string, 
    activities: UserActivityRecord[], 
    detector: AnomalyDetector
  ): UserAnomaly | null {
    const tradingActivities = activities.filter(a => 
      a.activityType === 'trade_execution' && 
      a.timestamp.getTime() > Date.now() - detector.windowSize
    );

    const tradesPerHour = (tradingActivities.length / detector.windowSize) * (60 * 60 * 1000);
    
    if (tradesPerHour > detector.threshold) {
      return {
        id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        type: 'trading_velocity',
        severity: tradesPerHour > this.ANOMALY_THRESHOLDS.tradingVelocity.critical ? 'critical' : 'high',
        description: `High trading velocity: ${tradesPerHour.toFixed(1)} trades/hour`,
        timestamp: new Date(),
        metadata: {
          tradesPerHour,
          threshold: detector.threshold,
          activitiesCount: tradingActivities.length,
        },
        confidence: Math.min(95, (tradesPerHour / detector.threshold) * 70),
      };
    }

    return null;
  }

  /**
   * Detect login frequency anomaly
   */
  private detectLoginFrequencyAnomaly(
    userId: string, 
    activities: UserActivityRecord[], 
    detector: AnomalyDetector
  ): UserAnomaly | null {
    const loginActivities = activities.filter(a => 
      a.activityType === 'login' && 
      a.timestamp.getTime() > Date.now() - detector.windowSize
    );

    const loginsPerHour = (loginActivities.length / detector.windowSize) * (60 * 60 * 1000);
    
    if (loginsPerHour > detector.threshold) {
      return {
        id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        type: 'login_frequency',
        severity: loginsPerHour > this.ANOMALY_THRESHOLDS.loginFrequency.critical ? 'critical' : 'high',
        description: `High login frequency: ${loginsPerHour.toFixed(1)} logins/hour`,
        timestamp: new Date(),
        metadata: {
          loginsPerHour,
          threshold: detector.threshold,
          activitiesCount: loginActivities.length,
        },
        confidence: Math.min(95, (loginsPerHour / detector.threshold) * 70),
      };
    }

    return null;
  }

  /**
   * Detect risk score anomaly
   */
  private detectRiskScoreAnomaly(
    userId: string, 
    activities: UserActivityRecord[], 
    detector: AnomalyDetector
  ): UserAnomaly | null {
    const recentActivities = activities.filter(a => 
      a.metadata.riskScore && 
      a.timestamp.getTime() > Date.now() - detector.windowSize
    );

    if (recentActivities.length === 0) {
      return null;
    }

    const avgRiskScore = recentActivities.reduce((sum, a) => sum + (a.metadata.riskScore || 0), 0) / recentActivities.length;
    
    if (avgRiskScore > detector.threshold) {
      return {
        id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        type: 'risk_score',
        severity: avgRiskScore > this.ANOMALY_THRESHOLDS.riskScore.critical ? 'critical' : 'high',
        description: `High average risk score: ${avgRiskScore.toFixed(1)}`,
        timestamp: new Date(),
        metadata: {
          averageRiskScore: avgRiskScore,
          threshold: detector.threshold,
          activitiesAnalyzed: recentActivities.length,
        },
        confidence: Math.min(95, (avgRiskScore / detector.threshold) * 70),
      };
    }

    return null;
  }

  /**
   * Detect unusual timing anomaly
   */
  private detectUnusualTimingAnomaly(
    userId: string, 
    activities: UserActivityRecord[], 
    detector: AnomalyDetector
  ): UserAnomaly | null {
    const pattern = this.activityPatterns.get(userId);
    if (!pattern || pattern.totalActivities < 50) {
      return null; // Need sufficient history
    }

    const recentActivities = activities.filter(a => 
      a.timestamp.getTime() > Date.now() - 24 * 60 * 60 * 1000 // Last 24 hours
    );

    // Check for unusual timing patterns
    let unusualTimingScore = 0;
    
    for (const activity of recentActivities) {
      const hour = activity.timestamp.getHours();
      const dayOfWeek = activity.timestamp.getDay();
      
      const normalHourlyActivity = pattern.timePatterns.hourlyActivity[hour] || 0;
      const avgHourlyActivity = pattern.behaviorMetrics.averageHourlyActivity;
      
      if (normalHourlyActivity < avgHourlyActivity * 0.1) { // Very unusual hour
        unusualTimingScore += 10;
      }
      
      // Weekend activity
      if ((dayOfWeek === 0 || dayOfWeek === 6) && 
          (pattern.timePatterns.weeklyActivity[dayOfWeek] || 0) < pattern.behaviorMetrics.averageHourlyActivity * 0.2) {
        unusualTimingScore += 5;
      }
    }

    if (unusualTimingScore > 30) {
      return {
        id: `anomaly_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        userId,
        type: 'unusual_timing',
        severity: unusualTimingScore > 60 ? 'high' : 'medium',
        description: `Unusual activity timing pattern detected`,
        timestamp: new Date(),
        metadata: {
          unusualTimingScore,
          activitiesAnalyzed: recentActivities.length,
          unusualHours: recentActivities.filter(a => {
            const hour = a.timestamp.getHours();
            return (pattern.timePatterns.hourlyActivity[hour] || 0) < pattern.behaviorMetrics.averageHourlyActivity * 0.1;
          }).length,
        },
        confidence: Math.min(90, unusualTimingScore * 1.5),
      };
    }

    return null;
  }

  /**
   * Store user activity in database
   */
  private async storeUserActivity(activity: UserActivityRecord): Promise<void> {
    try {
      // This would store in the user_activity_logs table
      console.log(`💾 Storing user activity ${activity.id}`);
    } catch (error) {
      console.error('Failed to store user activity:', error);
    }
  }

  /**
   * Store suspicious activity in database
   */
  private async storeSuspiciousActivity(activity: SuspiciousActivity): Promise<void> {
    try {
      console.log(`💾 Storing suspicious activity ${activity.id}`);
    } catch (error) {
      console.error('Failed to store suspicious activity:', error);
    }
  }

  /**
   * Get user activity statistics
   */
  getUserActivityStatistics(): UserActivityStatistics {
    const totalUsers = this.activityPatterns.size;
    const activeUsers = this.userSessions.size;
    const totalActivities = this.recentActivities.length;
    const suspiciousActivities = this.recentActivities.filter(a => a.anomalyScore > 70).length;
    
    return {
      totalUsers,
      activeUsers,
      totalActivities,
      suspiciousActivities,
      suspiciousActivityRate: totalActivities > 0 ? (suspiciousActivities / totalActivities) * 100 : 0,
      averageAnomalyScore: totalActivities > 0 
        ? this.recentActivities.reduce((sum, a) => sum + a.anomalyScore, 0) / totalActivities 
        : 0,
      lastUpdate: new Date(),
      isMonitoring: this.isMonitoring,
      detectors: {
        total: this.anomalyDetectors.size,
        active: Array.from(this.anomalyDetectors.values()).length,
      },
    };
  }

  /**
   * Get monitoring status
   */
  /**
   * Get monitoring status
   */
  getMonitoringStatus(): {
    isActive: boolean;
    startTime?: Date;
    activitiesTracked: number;
    suspiciousActivitiesDetected: number;
    lastActivityTime?: Date;
    intervalsActive: number;
    usersTracked: number;
    activeSessions: number;
    recentActivitiesCount: number;
    anomalyDetectorsCount: number;
    lastUpdate: Date | null;
  } {
    const suspiciousActivitiesDetected = this.recentActivities.filter(a => a.flaggedForReview).length;
    
    return {
      isActive: this.isMonitoring,
      startTime: this.isMonitoring ? new Date() : undefined, // Mock start time for tests
      activitiesTracked: this.recentActivities.length,
      suspiciousActivitiesDetected,
      lastActivityTime: this.recentActivities.length > 0 
        ? this.recentActivities[this.recentActivities.length - 1].timestamp 
        : undefined,
      intervalsActive: this.monitoringIntervals.length,
      usersTracked: this.activityPatterns.size,
      activeSessions: this.userSessions.size,
      recentActivitiesCount: this.recentActivities.length,
      anomalyDetectorsCount: this.anomalyDetectors.size,
      lastUpdate: this.recentActivities.length > 0 
        ? this.recentActivities[this.recentActivities.length - 1].timestamp 
        : null,
    };
  }

  /**
   * Clear all data (useful for testing)
   */
  clearData(): void {
    this.userSessions.clear();
    this.activityPatterns.clear();
    this.recentActivities = [];
    this.anomalyDetectors.clear();
    console.log('🧹 User activity monitoring data cleared');
  }
}

/**
 * Service for tracking user sessions and concurrent user monitoring
 */
export class UserSessionTrackingService extends EventEmitter {
  private prisma: PrismaClient;
  private activeSessions: Map<string, UserSession> = new Map();
  private sessionCleanupInterval?: NodeJS.Timeout;
  private monitoringInterval?: NodeJS.Timeout;
  private readonly sessionTimeoutMs: number = 30 * 60 * 1000; // 30 minutes
  private readonly maxConcurrentSessions: number = 5;
  private readonly monitoringIntervalMs: number = 60 * 1000; // 1 minute
  private isMonitoring: boolean = false;

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  /**
   * Start session monitoring
   */
  async startSessionMonitoring(config: SessionMonitoringConfig = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('Session monitoring already active');
      return;
    }

    const intervalMs = config.intervalMs || this.monitoringIntervalMs;
    const sessionTimeoutMs = config.sessionTimeoutMs || this.sessionTimeoutMs;
    const maxConcurrentSessions = config.maxConcurrentSessions || this.maxConcurrentSessions;

    this.isMonitoring = true;

    // Load existing sessions from database
    await this.loadActiveSessionsFromDatabase();

    // Start session cleanup interval
    this.sessionCleanupInterval = setInterval(() => {
      this.cleanupExpiredSessions(sessionTimeoutMs);
    }, intervalMs);

    // Start concurrent user monitoring
    this.monitoringInterval = setInterval(() => {
      this.monitorConcurrentUsers(maxConcurrentSessions);
    }, intervalMs);

    this.emit('sessionMonitoringStarted', {
      intervalMs,
      sessionTimeoutMs,
      maxConcurrentSessions,
    });

    console.log('User session monitoring started');
  }

  /**
   * Stop session monitoring
   */
  stopSessionMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    if (this.sessionCleanupInterval) {
      clearInterval(this.sessionCleanupInterval);
      this.sessionCleanupInterval = undefined;
    }

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }

    this.isMonitoring = false;
    this.emit('sessionMonitoringStopped');

    console.log('User session monitoring stopped');
  }

  /**
   * Track user session start
   */
  async startUserSession(sessionData: UserSessionInput): Promise<UserSession> {
    const session: UserSession = {
      id: crypto.randomUUID(),
      userId: sessionData.userId,
      sessionToken: sessionData.sessionToken,
      ipAddress: sessionData.ipAddress,
      userAgent: sessionData.userAgent,
      startTime: new Date(),
      lastActivity: new Date(),
      isActive: true,
      location: sessionData.location,
      deviceInfo: sessionData.deviceInfo,
      anomalyScore: 0,
    };

    // Check for concurrent session violations
    const userSessions = Array.from(this.activeSessions.values())
      .filter(s => s.userId === sessionData.userId && s.isActive);

    if (userSessions.length >= this.maxConcurrentSessions) {
      session.anomalyScore += 30;
      
      this.emit('sessionViolation', {
        type: 'concurrent_sessions_exceeded',
        userId: sessionData.userId,
        currentSessions: userSessions.length,
        maxAllowed: this.maxConcurrentSessions,
        newSession: session,
      });
    }

    // Check for suspicious IP changes
    const recentSessions = userSessions.filter(s => 
      Date.now() - s.lastActivity.getTime() < 5 * 60 * 1000 // 5 minutes
    );

    const differentIPs = recentSessions.filter(s => s.ipAddress !== sessionData.ipAddress);
    if (differentIPs.length > 0) {
      session.anomalyScore += 20;
      
      this.emit('sessionViolation', {
        type: 'rapid_ip_change',
        userId: sessionData.userId,
        previousIP: differentIPs[0].ipAddress,
        newIP: sessionData.ipAddress,
        session,
      });
    }

    // Store session
    this.activeSessions.set(session.id, session);

    // Persist to database
    try {
      await this.prisma.userSession.create({
        data: {
          id: session.id,
          userId: session.userId,
          sessionToken: session.sessionToken,
          ipAddress: session.ipAddress,
          userAgent: session.userAgent,
          startTime: session.startTime,
          lastActivity: session.lastActivity,
          isActive: session.isActive,
          location: session.location,
          deviceInfo: session.deviceInfo,
          anomalyScore: session.anomalyScore,
        },
      });
    } catch (error) {
      console.error('Failed to store user session:', error);
    }

    this.emit('sessionStarted', session);
    return session;
  }

  /**
   * Update session activity
   */
  async updateSessionActivity(sessionId: string, activityData?: Partial<UserActivity>): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session || !session.isActive) {
      return;
    }

    session.lastActivity = new Date();

    // Update activity data if provided
    if (activityData) {
      session.deviceInfo = activityData.deviceInfo || session.deviceInfo;
      session.location = activityData.location || session.location;
    }

    // Update in database
    try {
      await this.prisma.userSession.update({
        where: { id: sessionId },
        data: {
          lastActivity: session.lastActivity,
          deviceInfo: session.deviceInfo,
          location: session.location,
        },
      });
    } catch (error) {
      console.error('Failed to update session activity:', error);
    }

    this.emit('sessionActivityUpdated', { sessionId, lastActivity: session.lastActivity });
  }

  /**
   * End user session
   */
  async endUserSession(sessionId: string, reason: SessionEndReason = 'user_logout'): Promise<void> {
    const session = this.activeSessions.get(sessionId);
    if (!session) {
      return;
    }

    session.isActive = false;
    session.endTime = new Date();
    session.endReason = reason;

    // Calculate session duration
    const duration = session.endTime.getTime() - session.startTime.getTime();

    // Update in database
    try {
      await this.prisma.userSession.update({
        where: { id: sessionId },
        data: {
          isActive: false,
          endTime: session.endTime,
          endReason: reason,
          duration,
        },
      });
    } catch (error) {
      console.error('Failed to end user session:', error);
    }

    this.activeSessions.delete(sessionId);
    this.emit('sessionEnded', { sessionId, duration, reason });
  }

  /**
   * Get concurrent user statistics
   */
  getConcurrentUserStats(): ConcurrentUserStats {
    const now = new Date();
    const activeSessions = Array.from(this.activeSessions.values())
      .filter(s => s.isActive);

    const userCounts = new Map<string, number>();
    const userSessions = new Map<string, UserSession[]>();

    activeSessions.forEach(session => {
      const userId = session.userId;
      userCounts.set(userId, (userCounts.get(userId) || 0) + 1);
      
      if (!userSessions.has(userId)) {
        userSessions.set(userId, []);
      }
      userSessions.get(userId)!.push(session);
    });

    const concurrentUsers = userCounts.size;
    const totalSessions = activeSessions.length;
    const usersWithMultipleSessions = Array.from(userCounts.entries())
      .filter(([_, count]) => count > 1).length;

    const averageSessionsPerUser = concurrentUsers > 0 
      ? totalSessions / concurrentUsers 
      : 0;

    const sessionsByHour = this.getSessionDistributionByHour(activeSessions);
    const locationDistribution = this.getLocationDistribution(activeSessions);

    return {
      timestamp: now,
      concurrentUsers,
      totalActiveSessions: totalSessions,
      usersWithMultipleSessions,
      averageSessionsPerUser: Number(averageSessionsPerUser.toFixed(2)),
      sessionsByHour,
      locationDistribution,
      violationsDetected: Array.from(userCounts.entries())
        .filter(([_, count]) => count > this.maxConcurrentSessions)
        .length,
    };
  }

  /**
   * Get user session history
   */
  async getUserSessionHistory(userId: string, timeRange: string = '24h'): Promise<UserSession[]> {
    const timeRangeMs = this.getTimeRangeMs(timeRange);
    const startTime = new Date(Date.now() - timeRangeMs);

    try {
      const sessions = await this.prisma.userSession.findMany({
        where: {
          userId,
          startTime: {
            gte: startTime,
          },
        },
        orderBy: {
          startTime: 'desc',
        },
      });

      return sessions.map(session => ({
        ...session,
        deviceInfo: session.deviceInfo as Record<string, any>,
        location: session.location as Record<string, any>,
      }));
    } catch (error) {
      console.error('Failed to fetch user session history:', error);
      return [];
    }
  }

  /**
   * Load active sessions from database on startup
   */
  private async loadActiveSessionsFromDatabase(): Promise<void> {
    try {
      const activeSessions = await this.prisma.userSession.findMany({
        where: { isActive: true },
      });

      activeSessions.forEach(session => {
        this.activeSessions.set(session.id, {
          ...session,
          deviceInfo: session.deviceInfo as Record<string, any>,
          location: session.location as Record<string, any>,
        });
      });

      console.log(`Loaded ${activeSessions.length} active sessions from database`);
    } catch (error) {
      console.error('Failed to load active sessions from database:', error);
    }
  }

  /**
   * Clean up expired sessions
   */
  private cleanupExpiredSessions(sessionTimeoutMs: number): void {
    const now = Date.now();
    const expiredSessions: string[] = [];

    this.activeSessions.forEach((session, sessionId) => {
      const timeSinceLastActivity = now - session.lastActivity.getTime();
      
      if (timeSinceLastActivity > sessionTimeoutMs) {
        expiredSessions.push(sessionId);
      }
    });

    expiredSessions.forEach(sessionId => {
      this.endUserSession(sessionId, 'timeout');
    });

    if (expiredSessions.length > 0) {
      console.log(`Cleaned up ${expiredSessions.length} expired sessions`);
    }
  }

  /**
   * Monitor concurrent users and generate alerts
   */
  private monitorConcurrentUsers(maxConcurrentSessions: number): void {
    const stats = this.getConcurrentUserStats();
    
    // Generate alerts for policy violations
    if (stats.violationsDetected > 0) {
      this.emit('alert', {
        id: crypto.randomUUID(),
        type: 'concurrent_session_violation',
        severity: 'high',
        message: `${stats.violationsDetected} users exceeded concurrent session limit`,
        timestamp: new Date(),
        metadata: {
          violationsCount: stats.violationsDetected,
          maxAllowed: maxConcurrentSessions,
        },
      });
    }

    // Monitor unusual concurrent user patterns
    if (stats.averageSessionsPerUser > 2.5) {
      this.emit('alert', {
        id: crypto.randomUUID(),
        type: 'unusual_session_pattern',
        severity: 'medium',
        message: 'Unusually high average sessions per user detected',
        timestamp: new Date(),
        metadata: {
          averageSessionsPerUser: stats.averageSessionsPerUser,
          totalUsers: stats.concurrentUsers,
        },
      });
    }

    this.emit('concurrentUserStats', stats);
  }

  /**
   * Get session distribution by hour
   */
  private getSessionDistributionByHour(sessions: UserSession[]): Record<number, number> {
    const distribution: Record<number, number> = {};
    
    for (let hour = 0; hour < 24; hour++) {
      distribution[hour] = 0;
    }

    sessions.forEach(session => {
      const hour = session.startTime.getHours();
      distribution[hour]++;
    });

    return distribution;
  }

  /**
   * Get location distribution of active sessions
   */
  private getLocationDistribution(sessions: UserSession[]): Record<string, number> {
    const distribution: Record<string, number> = {};

    sessions.forEach(session => {
      if (session.location?.country) {
        const country = session.location.country;
        distribution[country] = (distribution[country] || 0) + 1;
      } else {
        distribution['Unknown'] = (distribution['Unknown'] || 0) + 1;
      }
    });

    return distribution;
  }

  /**
   * Convert time range to milliseconds
   */
  private getTimeRangeMs(timeRange: string): number {
    switch (timeRange) {
      case '1h': return 60 * 60 * 1000;
      case '6h': return 6 * 60 * 60 * 1000;
      case '24h': return 24 * 60 * 60 * 1000;
      case '7d': return 7 * 24 * 60 * 60 * 1000;
      case '30d': return 30 * 24 * 60 * 60 * 1000;
      default: return 24 * 60 * 60 * 1000;
    }
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus(): {
    isActive: boolean;
    activeSessions: number;
    concurrentUsers: number;
    startTime?: Date;
  } {
    const activeSessions = Array.from(this.activeSessions.values())
      .filter(s => s.isActive);

    const uniqueUsers = new Set(activeSessions.map(s => s.userId)).size;

    return {
      isActive: this.isMonitoring,
      activeSessions: activeSessions.length,
      concurrentUsers: uniqueUsers,
    };
  }
}

// Type definitions for session tracking
interface UserSession {
  id: string;
  userId: string;
  sessionToken: string;
  ipAddress: string;
  userAgent: string;
  startTime: Date;
  lastActivity: Date;
  endTime?: Date;
  isActive: boolean;
  location?: Record<string, any>;
  deviceInfo?: Record<string, any>;
  anomalyScore: number;
  endReason?: SessionEndReason;
}

interface UserSessionInput {
  userId: string;
  sessionToken: string;
  ipAddress: string;
  userAgent: string;
  location?: Record<string, any>;
  deviceInfo?: Record<string, any>;
}

interface UserActivity {
  sessionId: string;
  activityType: string;
  timestamp: Date;
  location?: Record<string, any>;
  deviceInfo?: Record<string, any>;
}

interface SessionMonitoringConfig {
  intervalMs?: number;
  sessionTimeoutMs?: number;
  maxConcurrentSessions?: number;
}

interface ConcurrentUserStats {
  timestamp: Date;
  concurrentUsers: number;
  totalActiveSessions: number;
  usersWithMultipleSessions: number;
  averageSessionsPerUser: number;
  sessionsByHour: Record<number, number>;
  locationDistribution: Record<string, number>;
  violationsDetected: number;
}

type SessionEndReason = 
  | 'user_logout' 
  | 'timeout' 
  | 'forced_logout' 
  | 'security_violation' 
  | 'system_shutdown';

// Type definitions
interface UserActivityMonitoringConfig {
  patternAnalysisInterval?: number;
  anomalyDetectionInterval?: number;
  sessionTimeout?: number;
  activityRetentionLimit?: number;
}

interface UserActivityInput {
  userId: string;
  sessionId: string;
  type: 'login' | 'trade_execution' | 'strategy_modification' | 'risk_breach' | 'unusual_pattern' | string;
  ipAddress?: string;
  userAgent?: string;
  tradeVolume?: number;
  riskScore?: number;
  patternType?: string;
  endpoint?: string;
  duration?: number;
  metadata?: Record<string, any>;
}

interface UserActivityRecord {
  id: string;
  userId: string;
  sessionId: string;
  timestamp: Date;
  activityType: string;
  metadata: {
    ipAddress?: string;
    userAgent?: string;
    tradeVolume?: number;
    riskScore?: number;
    patternType?: string;
    endpoint?: string;
    duration?: number;
    [key: string]: any;
  };
  anomalyScore: number;
  flaggedForReview: boolean;
  reviewStatus: 'pending' | 'reviewed' | 'resolved';
}

interface UserSession {
  userId: string;
  sessionId: string;
  startTime: Date;
  lastActivity: Date;
  activityCount: number;
  activities: Array<{
    type: string;
    timestamp: Date;
    metadata?: Record<string, any>;
  }>;
  ipAddresses: Set<string>;
  userAgents: Set<string>;
  riskEvents: Array<{
    type: string;
    score: number;
    timestamp: Date;
  }>;
  anomalousActivities: number;
  totalTradeVolume: number;
  uniqueEndpoints: Set<string>;
  sessionScore: number;
}

interface UserActivityPattern {
  userId: string;
  lastUpdated: Date;
  totalActivities: number;
  timePatterns: {
    hourlyActivity: Record<number, number>;
    weeklyActivity: Record<number, number>;
    monthlyTrends: Record<string, number>;
  };
  locationPatterns: {
    knownIpAddresses: Set<string>;
    commonCountries: Map<string, number>;
    suspiciousLocations: Array<{
      location: string;
      count: number;
      lastSeen: Date;
    }>;
  };
  behaviorMetrics: {
    averageSessionDuration: number;
    averageTradeVolume: number;
    tradeVolumeStdDev: number;
    averageHourlyActivity: number;
    activityTypeDistribution: Record<string, number>;
    commonEndpoints: Set<string>;
  };
  riskMetrics: {
    averageRiskScore: number;
    riskScoreHistory: Array<{
      score: number;
      timestamp: Date;
    }>;
    highRiskActivities: number;
    riskTrend: 'increasing' | 'decreasing' | 'stable';
  };
  anomalyHistory: {
    totalAnomalies: number;
    recentAnomalies: Array<{
      score: number;
      activityType: string;
      timestamp: Date;
    }>;
    anomalyTypes: Record<string, number>;
    falsePositives: number;
  };
}

interface AnomalyDetector {
  type: string;
  threshold: number;
  windowSize: number;
  baseline: Map<string, number>;
  lastUpdate: Date;
}

interface SuspiciousActivity {
  id: string;
  activityId: string;
  userId: string;
  activityType: string;
  anomalyScore: number;
  reasons: string[];
  timestamp: Date;
  reviewStatus: 'pending' | 'reviewed' | 'resolved' | 'false_positive';
  severity: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, any>;
}

interface SuspiciousSession {
  id: string;
  userId: string;
  sessionId: string;
  sessionScore: number;
  startTime: Date;
  lastActivity: Date;
  activityCount: number;
  riskFactors: {
    multipleIPs: boolean;
    multipleUserAgents: boolean;
    highActivity: boolean;
    highVolume: boolean;
    anomalousActivities: boolean;
  };
  severity: 'low' | 'medium' | 'high' | 'critical';
  reviewStatus: 'pending' | 'reviewed' | 'resolved' | 'false_positive';
}

interface UserAnomaly {
  id: string;
  userId: string;
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  timestamp: Date;
  metadata: Record<string, any>;
  confidence: number;
}

interface UserActivityStatistics {
  totalUsers: number;
  activeUsers: number;
  totalActivities: number;
  suspiciousActivities: number;
  suspiciousActivityRate: number;
  averageAnomalyScore: number;
  lastUpdate: Date;
  isMonitoring: boolean;
  detectors: {
    total: number;
    active: number;
  };
}

interface UserActivityMonitoringStatus {
  isActive: boolean;
  intervalsActive: number;
  usersTracked: number;
  activeSessions: number;
  recentActivitiesCount: number;
  anomalyDetectorsCount: number;
  lastUpdate: Date | null;
}

export type {
  UserActivityMonitoringConfig,
  UserActivityInput,
  UserActivityRecord,
  UserSession,
  UserActivityPattern,
  AnomalyDetector,
  SuspiciousActivity,
  SuspiciousSession,
  UserAnomaly,
  UserActivityStatistics,
  UserActivityMonitoringStatus,
};