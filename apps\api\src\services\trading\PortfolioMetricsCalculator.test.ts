/**
 * Portfolio Metrics Calculator Test Suite
 * 
 * Tests for portfolio-level risk metrics, diversification analysis,
 * risk-adjusted return calculations, and user-friendly translations.
 * 
 * @version 1.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { PortfolioMetricsCalculator, Position, BenchmarkData } from './PortfolioMetricsCalculator';

describe('PortfolioMetricsCalculator', () => {
  let calculator: PortfolioMetricsCalculator;
  let mockPositions: Position[];
  let initialBalance: Decimal.Instance;

  beforeEach(() => {
    calculator = new PortfolioMetricsCalculator({
      benchmarkSymbol: 'SPX',
      riskFreeRate: 0.02,
      lookbackPeriod: 252,
      rebalanceFrequency: 'daily',
      includeRealizedPnL: true
    });

    initialBalance = new Decimal(100000);

    mockPositions = [
      {
        id: 'pos-1',
        symbol: 'EURUSD',
        size: new Decimal(50000),
        entryPrice: new Decimal(1.1000),
        currentPrice: new Decimal(1.1050),
        unrealizedPnL: new Decimal(250),
        realizedPnL: new Decimal(100),
        entryDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
        sector: 'Major Pairs',
        assetClass: 'forex'
      },
      {
        id: 'pos-2',
        symbol: 'GBPUSD',
        size: new Decimal(40000),
        entryPrice: new Decimal(1.2500),
        currentPrice: new Decimal(1.2400),
        unrealizedPnL: new Decimal(-400),
        realizedPnL: new Decimal(50),
        entryDate: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
        sector: 'Major Pairs',
        assetClass: 'forex'
      },
      {
        id: 'pos-3',
        symbol: 'XAUUSD',
        size: new Decimal(50),
        entryPrice: new Decimal(2000),
        currentPrice: new Decimal(2050),
        unrealizedPnL: new Decimal(2500),
        realizedPnL: new Decimal(200),
        entryDate: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
        sector: 'Precious Metals',
        assetClass: 'commodity'
      },
      {
        id: 'pos-4',
        symbol: 'BTCUSD',
        size: new Decimal(1),
        entryPrice: new Decimal(45000),
        currentPrice: new Decimal(48000),
        unrealizedPnL: new Decimal(3000),
        realizedPnL: new Decimal(-100),
        entryDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
        sector: 'Digital Assets',
        assetClass: 'crypto'
      }
    ];

    // Setup benchmark data
    const benchmarkData: BenchmarkData = {
      symbol: 'SPX',
      returns: Array.from({ length: 252 }, (_, i) => (Math.random() - 0.5) * 0.02),
      riskFreeRate: 0.02,
      name: 'S&P 500'
    };
    calculator.setBenchmarkData(benchmarkData);

    // Setup historical returns
    const historicalReturns = Array.from({ length: 60 }, (_, i) => (Math.random() - 0.5) * 0.015);
    calculator.updateHistoricalReturns(historicalReturns);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Portfolio Metrics Calculation', () => {
    it('should calculate comprehensive portfolio metrics', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.totalReturn).toBeTypeOf('number');
      expect(metrics.annualizedReturn).toBeTypeOf('number');
      expect(metrics.realizedReturn).toBeTypeOf('number');
      expect(metrics.unrealizedReturn).toBeTypeOf('number');
      expect(metrics.volatility).toBeGreaterThanOrEqual(0);
      expect(metrics.sharpeRatio).toBeTypeOf('number');
      expect(metrics.sortinoRatio).toBeTypeOf('number');
      expect(metrics.maxDrawdown).toBeGreaterThanOrEqual(0);
    });

    it('should handle empty portfolio', () => {
      const metrics = calculator.calculatePortfolioMetrics([], initialBalance);

      expect(metrics.totalReturn).toBe(0);
      expect(metrics.annualizedReturn).toBe(0);
      expect(metrics.volatility).toBe(0);
      expect(metrics.diversificationScore).toBe(0);
      expect(metrics.topPerformers).toHaveLength(0);
      expect(metrics.bottomPerformers).toHaveLength(0);
    });

    it('should calculate total return correctly', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      
      const currentValue = mockPositions.reduce((sum, pos) => 
        sum.add(pos.size.mul(pos.currentPrice)), new Decimal(0)
      );
      const expectedReturn = currentValue.sub(initialBalance).div(initialBalance).toNumber();

      expect(Math.abs(metrics.totalReturn - expectedReturn)).toBeLessThan(0.0001);
    });

    it('should calculate realized and unrealized returns separately', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      const totalRealized = mockPositions.reduce((sum, pos) => 
        sum.add(pos.realizedPnL || new Decimal(0)), new Decimal(0)
      );
      const expectedRealizedReturn = totalRealized.div(initialBalance).toNumber();

      const totalUnrealized = mockPositions.reduce((sum, pos) => 
        sum.add(pos.unrealizedPnL), new Decimal(0)
      );
      const expectedUnrealizedReturn = totalUnrealized.div(initialBalance).toNumber();

      expect(Math.abs(metrics.realizedReturn - expectedRealizedReturn)).toBeLessThan(0.0001);
      expect(Math.abs(metrics.unrealizedReturn - expectedUnrealizedReturn)).toBeLessThan(0.0001);
    });

    it('should annualize returns based on portfolio age', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.annualizedReturn).toBeTypeOf('number');
      expect(metrics.daysSinceInception).toBeGreaterThan(0);
      
      // Annualized return should be based on the oldest position
      expect(metrics.daysSinceInception).toBeGreaterThanOrEqual(60); // Oldest position is 60 days old
    });
  });

  describe('Risk Metrics Calculation', () => {
    it('should calculate volatility from historical returns', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.volatility).toBeGreaterThanOrEqual(0);
      expect(metrics.volatility).toBeLessThan(2); // Reasonable upper bound
    });

    it('should calculate Sharpe ratio', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.sharpeRatio).toBeTypeOf('number');
      // Sharpe ratio can be negative, so no lower bound check
      expect(Math.abs(metrics.sharpeRatio)).toBeLessThan(10); // Reasonable bounds
    });

    it('should calculate Sortino ratio considering only downside deviation', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.sortinoRatio).toBeTypeOf('number');
      // Sortino ratio should typically be higher than Sharpe ratio for same portfolio
    });

    it('should calculate Calmar ratio', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.calmarRatio).toBeTypeOf('number');
      
      if (metrics.maxDrawdown > 0) {
        const expectedCalmar = metrics.totalReturn / metrics.maxDrawdown;
        expect(Math.abs(metrics.calmarRatio - expectedCalmar)).toBeLessThan(0.0001);
      }
    });

    it('should calculate maximum drawdown', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.maxDrawdown).toBeGreaterThanOrEqual(0);
      expect(metrics.maxDrawdown).toBeLessThanOrEqual(1);
    });
  });

  describe('Risk-Adjusted Metrics with Benchmark', () => {
    it('should calculate alpha (excess return vs benchmark)', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.alpha).toBeTypeOf('number');
      // Alpha can be positive or negative
    });

    it('should calculate beta (systematic risk)', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.beta).toBeTypeOf('number');
      // Beta can be positive or negative depending on correlation with benchmark
      expect(Math.abs(metrics.beta)).toBeGreaterThanOrEqual(0);
    });

    it('should calculate R-squared (correlation with benchmark)', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.rSquared).toBeGreaterThanOrEqual(0);
      expect(metrics.rSquared).toBeLessThanOrEqual(1);
    });

    it('should calculate Treynor ratio', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.treynorRatio).toBeTypeOf('number');
      
      if (metrics.beta !== 0) {
        // Treynor ratio should be related to excess return / beta
        expect(Math.abs(metrics.treynorRatio)).toBeLessThan(50);
      }
    });

    it('should calculate Information ratio', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.informationRatio).toBeTypeOf('number');
    });

    it('should handle missing benchmark data gracefully', () => {
      const noBenchmarkCalculator = new PortfolioMetricsCalculator();
      const metrics = noBenchmarkCalculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.alpha).toBe(0);
      expect(metrics.beta).toBe(1);
      expect(metrics.rSquared).toBe(0);
      expect(metrics.informationRatio).toBe(0);
    });
  });

  describe('Diversification Metrics', () => {
    it('should calculate diversification score', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.diversificationScore).toBeGreaterThanOrEqual(0);
      expect(metrics.diversificationScore).toBeLessThanOrEqual(100);
    });

    it('should calculate effective number of positions', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.effectiveNumberOfPositions).toBeGreaterThan(0);
      expect(metrics.effectiveNumberOfPositions).toBeLessThanOrEqual(mockPositions.length);
    });

    it('should calculate Herfindahl index', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.herfindahlIndex).toBeGreaterThan(0);
      expect(metrics.herfindahlIndex).toBeLessThanOrEqual(1);
      
      // For equal-weighted portfolio of n positions, HHI should be 1/n
      // For our portfolio, it should be between 1/n and 1
    });

    it('should calculate sector diversification', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.sectorDiversification).toBeGreaterThanOrEqual(0);
      expect(metrics.sectorDiversification).toBeLessThanOrEqual(100);
    });

    it('should calculate asset class diversification', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.assetClassDiversification).toBeGreaterThanOrEqual(0);
      expect(metrics.assetClassDiversification).toBeLessThanOrEqual(100);
    });

    it('should handle single position portfolio', () => {
      const singlePosition = [mockPositions[0]];
      const metrics = calculator.calculatePortfolioMetrics(singlePosition, initialBalance);

      expect(metrics.diversificationScore).toBe(0);
      expect(metrics.effectiveNumberOfPositions).toBe(1);
      expect(metrics.herfindahlIndex).toBe(1);
      expect(metrics.sectorDiversification).toBe(0);
      expect(metrics.assetClassDiversification).toBe(0);
    });
  });

  describe('Performance Attribution', () => {
    it('should calculate sector returns', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.sectorReturns).toBeInstanceOf(Map);
      expect(metrics.sectorReturns.has('Major Pairs')).toBe(true);
      expect(metrics.sectorReturns.has('Precious Metals')).toBe(true);
      expect(metrics.sectorReturns.has('Digital Assets')).toBe(true);
    });

    it('should calculate asset class returns', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.assetClassReturns).toBeInstanceOf(Map);
      expect(metrics.assetClassReturns.has('forex')).toBe(true);
      expect(metrics.assetClassReturns.has('commodity')).toBe(true);
      expect(metrics.assetClassReturns.has('crypto')).toBe(true);
    });

    it('should identify top performers', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.topPerformers).toHaveLength(3); // Requested top 3
      expect(metrics.topPerformers[0].unrealizedPnL.gte(metrics.topPerformers[1].unrealizedPnL)).toBe(true);
      expect(metrics.topPerformers[1].unrealizedPnL.gte(metrics.topPerformers[2].unrealizedPnL)).toBe(true);
    });

    it('should identify bottom performers', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.bottomPerformers).toHaveLength(3); // Requested bottom 3
      expect(metrics.bottomPerformers[0].unrealizedPnL.lte(metrics.bottomPerformers[1].unrealizedPnL)).toBe(true);
      expect(metrics.bottomPerformers[1].unrealizedPnL.lte(metrics.bottomPerformers[2].unrealizedPnL)).toBe(true);
    });
  });

  describe('Time-Based Metrics', () => {
    it('should calculate win rate', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.winRate).toBeGreaterThanOrEqual(0);
      expect(metrics.winRate).toBeLessThanOrEqual(1);
      
      // Win rate should be 3/4 = 0.75 (3 positions with positive unrealized PnL)
      const winnersCount = mockPositions.filter(pos => pos.unrealizedPnL.gt(0)).length;
      const expectedWinRate = winnersCount / mockPositions.length;
      expect(Math.abs(metrics.winRate - expectedWinRate)).toBeLessThan(0.0001);
    });

    it('should calculate profit factor', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.profitFactor).toBeGreaterThan(0);
      
      const grossProfit = mockPositions
        .filter(pos => pos.unrealizedPnL.gt(0))
        .reduce((sum, pos) => sum.add(pos.unrealizedPnL), new Decimal(0));
      
      const grossLoss = mockPositions
        .filter(pos => pos.unrealizedPnL.lt(0))
        .reduce((sum, pos) => sum.add(pos.unrealizedPnL.abs()), new Decimal(0));
      
      if (grossLoss.gt(0)) {
        const expectedProfitFactor = grossProfit.div(grossLoss).toNumber();
        expect(Math.abs(metrics.profitFactor - expectedProfitFactor)).toBeLessThan(0.0001);
      }
    });

    it('should calculate average win and loss', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.averageWin).toBeInstanceOf(Decimal);
      expect(metrics.averageLoss).toBeInstanceOf(Decimal);
      
      const winners = mockPositions.filter(pos => pos.unrealizedPnL.gt(0));
      if (winners.length > 0) {
        const expectedAvgWin = winners.reduce((sum, pos) => sum.add(pos.unrealizedPnL), new Decimal(0))
          .div(winners.length);
        expect(metrics.averageWin.equals(expectedAvgWin)).toBe(true);
      }
    });

    it('should calculate portfolio age correctly', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics.daysSinceInception).toBeGreaterThanOrEqual(60); // Based on oldest position
      expect(metrics.daysSinceInception).toBeLessThan(365); // Reasonable upper bound
    });
  });

  describe('User-Friendly Translations', () => {
    it('should translate metrics to user-friendly format', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      const userFriendly = calculator.translateToUserFriendly(metrics);

      expect(userFriendly.overallPerformance).toBeDefined();
      expect(userFriendly.riskAssessment).toBeDefined();
      expect(userFriendly.diversificationAnalysis).toBeDefined();
      expect(userFriendly.benchmarkComparison).toBeDefined();
    });

    it('should assess overall performance with grades', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      const userFriendly = calculator.translateToUserFriendly(metrics);

      expect(['A', 'B', 'C', 'D', 'F']).toContain(userFriendly.overallPerformance.grade);
      expect(userFriendly.overallPerformance.description).toBeTypeOf('string');
      expect(userFriendly.overallPerformance.keyHighlights).toBeInstanceOf(Array);
    });

    it('should assess risk level', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      const userFriendly = calculator.translateToUserFriendly(metrics);

      expect(['Low', 'Medium', 'High', 'Very High']).toContain(userFriendly.riskAssessment.riskLevel);
      expect(userFriendly.riskAssessment.description).toBeTypeOf('string');
      expect(userFriendly.riskAssessment.recommendations).toBeInstanceOf(Array);
    });

    it('should assess diversification', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      const userFriendly = calculator.translateToUserFriendly(metrics);

      expect(['Excellent', 'Good', 'Fair', 'Poor']).toContain(userFriendly.diversificationAnalysis.score);
      expect(userFriendly.diversificationAnalysis.description).toBeTypeOf('string');
      expect(userFriendly.diversificationAnalysis.improvements).toBeInstanceOf(Array);
    });

    it('should compare to benchmark', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      const userFriendly = calculator.translateToUserFriendly(metrics);

      expect(['Outperforming', 'Matching', 'Underperforming']).toContain(userFriendly.benchmarkComparison.performance);
      expect(userFriendly.benchmarkComparison.description).toBeTypeOf('string');
      expect(userFriendly.benchmarkComparison.analysis).toBeTypeOf('string');
    });
  });

  describe('Risk-Adjusted Position Sizing', () => {
    it('should calculate risk-adjusted position size', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      const recommendation = calculator.calculateRiskAdjustedSize(mockPositions[0], metrics);

      expect(recommendation.recommendedSize).toBeInstanceOf(Decimal);
      expect(recommendation.riskContribution).toBeTypeOf('number');
      expect(recommendation.reasoning).toBeInstanceOf(Array);
      expect(recommendation.reasoning.length).toBeGreaterThan(0);
    });

    it('should adjust for high volatility', () => {
      // Create metrics with high volatility
      const highVolatilityMetrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      highVolatilityMetrics.volatility = 0.3; // 30% volatility

      const recommendation = calculator.calculateRiskAdjustedSize(mockPositions[0], highVolatilityMetrics);

      expect(recommendation.recommendedSize.lt(mockPositions[0].size)).toBe(true);
      expect(recommendation.reasoning.some(r => r.includes('volatility'))).toBe(true);
    });

    it('should adjust for low diversification', () => {
      const lowDiversificationMetrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      lowDiversificationMetrics.diversificationScore = 30; // Low diversification

      const recommendation = calculator.calculateRiskAdjustedSize(mockPositions[0], lowDiversificationMetrics);

      expect(recommendation.reasoning.some(r => r.includes('diversification'))).toBe(true);
    });

    it('should adjust for high drawdown', () => {
      const highDrawdownMetrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      highDrawdownMetrics.maxDrawdown = 0.2; // 20% drawdown

      const recommendation = calculator.calculateRiskAdjustedSize(mockPositions[0], highDrawdownMetrics);

      expect(recommendation.reasoning.some(r => r.includes('drawdown'))).toBe(true);
    });
  });

  describe('Performance Report Generation', () => {
    it('should generate comprehensive performance report', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      const report = calculator.generatePerformanceReport(metrics);

      expect(report.summary).toBeTypeOf('string');
      expect(report.strengths).toBeInstanceOf(Array);
      expect(report.weaknesses).toBeInstanceOf(Array);
      expect(report.recommendations).toBeInstanceOf(Array);
    });

    it('should identify strengths correctly', () => {
      const strongMetrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      strongMetrics.sharpeRatio = 1.5;
      strongMetrics.diversificationScore = 85;
      strongMetrics.winRate = 0.7;

      const report = calculator.generatePerformanceReport(strongMetrics);

      expect(report.strengths.length).toBeGreaterThan(0);
      expect(report.strengths.some(s => s.includes('Sharpe'))).toBe(true);
      expect(report.strengths.some(s => s.includes('diversified'))).toBe(true);
      expect(report.strengths.some(s => s.includes('win rate'))).toBe(true);
    });

    it('should identify weaknesses correctly', () => {
      const weakMetrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      weakMetrics.maxDrawdown = 0.25;
      weakMetrics.diversificationScore = 40;
      weakMetrics.sharpeRatio = 0.3;

      const report = calculator.generatePerformanceReport(weakMetrics);

      expect(report.weaknesses.length).toBeGreaterThan(0);
      expect(report.weaknesses.some(w => w.includes('drawdown'))).toBe(true);
      expect(report.weaknesses.some(w => w.includes('diversification'))).toBe(true);
      expect(report.weaknesses.some(w => w.includes('risk-adjusted'))).toBe(true);
    });

    it('should provide relevant recommendations', () => {
      const riskyCmetrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
      riskyCmetrics.beta = 2.0;
      riskyCmetrics.diversificationScore = 45;
      riskyCmetrics.volatility = 0.35;

      const report = calculator.generatePerformanceReport(riskyCmetrics);

      expect(report.recommendations.length).toBeGreaterThan(0);
    });
  });

  describe('Configuration and Customization', () => {
    it('should accept custom configuration', () => {
      const customConfig = {
        benchmarkSymbol: 'NASDAQ',
        riskFreeRate: 0.03,
        lookbackPeriod: 180,
        rebalanceFrequency: 'weekly' as const,
        includeRealizedPnL: false
      };

      const customCalculator = new PortfolioMetricsCalculator(customConfig);
      const metrics = customCalculator.calculatePortfolioMetrics(mockPositions, initialBalance);

      expect(metrics).toBeDefined();
      expect(metrics.realizedReturn).toBe(0); // Should be 0 when includeRealizedPnL is false
    });

    it('should handle different rebalance frequencies', () => {
      const frequencies = ['daily', 'weekly', 'monthly'] as const;
      
      frequencies.forEach(frequency => {
        const calculator = new PortfolioMetricsCalculator({ rebalanceFrequency: frequency });
        const metrics = calculator.calculatePortfolioMetrics(mockPositions, initialBalance);
        
        expect(metrics).toBeDefined();
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle zero initial balance', () => {
      const metrics = calculator.calculatePortfolioMetrics(mockPositions, new Decimal(0));

      expect(metrics.totalReturn).toBe(0);
      expect(metrics.realizedReturn).toBe(0);
      expect(metrics.unrealizedReturn).toBe(0);
    });

    it('should handle positions with zero entry prices', () => {
      const zeroEntryPositions = mockPositions.map(pos => ({
        ...pos,
        entryPrice: new Decimal(0)
      }));

      expect(() => calculator.calculatePortfolioMetrics(zeroEntryPositions, initialBalance)).not.toThrow();
      const metrics = calculator.calculatePortfolioMetrics(zeroEntryPositions, initialBalance);
      expect(metrics).toBeDefined();
    });

    it('should handle positions with zero current prices', () => {
      const zeroCurrentPositions = mockPositions.map(pos => ({
        ...pos,
        currentPrice: new Decimal(0)
      }));

      expect(() => calculator.calculatePortfolioMetrics(zeroCurrentPositions, initialBalance)).not.toThrow();
      const metrics = calculator.calculatePortfolioMetrics(zeroCurrentPositions, initialBalance);
      expect(metrics).toBeDefined();
    });

    it('should handle very old positions', () => {
      const oldPositions = mockPositions.map(pos => ({
        ...pos,
        entryDate: new Date(Date.now() - 5 * 365 * 24 * 60 * 60 * 1000) // 5 years ago
      }));

      const metrics = calculator.calculatePortfolioMetrics(oldPositions, initialBalance);
      
      expect(metrics.daysSinceInception).toBeGreaterThan(1800); // ~5 years
      expect(metrics.annualizedReturn).toBeTypeOf('number');
    });

    it('should handle extreme PnL values', () => {
      const extremePositions = mockPositions.map(pos => ({
        ...pos,
        unrealizedPnL: new Decimal(1000000), // Very large profit
        realizedPnL: new Decimal(-50000) // Large loss
      }));

      expect(() => calculator.calculatePortfolioMetrics(extremePositions, initialBalance)).not.toThrow();
      const metrics = calculator.calculatePortfolioMetrics(extremePositions, initialBalance);
      expect(metrics).toBeDefined();
    });

    it('should handle missing sector information', () => {
      const noSectorPositions = mockPositions.map(pos => ({
        ...pos,
        sector: undefined
      }));

      const metrics = calculator.calculatePortfolioMetrics(noSectorPositions, initialBalance);
      
      expect(metrics.sectorReturns.has('Unknown')).toBe(true);
      expect(metrics.sectorDiversification).toBeGreaterThanOrEqual(0);
    });

    it('should handle single asset class portfolio', () => {
      const singleAssetClassPositions = mockPositions.map(pos => ({
        ...pos,
        assetClass: 'forex' as const
      }));

      const metrics = calculator.calculatePortfolioMetrics(singleAssetClassPositions, initialBalance);
      
      expect(metrics.assetClassDiversification).toBe(0);
      expect(metrics.assetClassReturns.size).toBe(1);
    });
  });
});