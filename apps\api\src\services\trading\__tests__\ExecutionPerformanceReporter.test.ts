import { describe, it, expect, beforeEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { ExecutionPerformanceReporter } from '../ExecutionPerformanceReporter';
import { TradeExecution, ExecutionStatus } from '@golddaddy/types';

describe('ExecutionPerformanceReporter', () => {
  let reporter: ExecutionPerformanceReporter;
  let mockTradeHistoryService: any;
  let mockExecutionQualityAnalyzer: any;

  const createMockExecution = (overrides: Partial<TradeExecution> = {}): TradeExecution => ({
    id: 'exec-123',
    tradeId: 'trade-123',
    brokerId: 'broker-1',
    brokerOrderId: 'broker-order-123',
    status: ExecutionStatus.FILLED,
    instrument: 'EURUSD',
    side: 'buy',
    quantity: new Decimal(10000),
    requestedPrice: new Decimal(1.1200),
    executedPrice: new Decimal(1.1205),
    slippage: new Decimal(0.5),
    latency: 45,
    fees: new Decimal(2.50),
    realizedPnl: new Decimal(25.00),
    quality: {
      executionId: 'exec-123',
      tradeId: 'trade-123',
      brokerId: 'broker-1',
      instrument: 'EURUSD',
      latency: 45,
      slippage: new Decimal(0.5),
      executedPrice: new Decimal(1.1205),
      requestedPrice: new Decimal(1.1200),
      volume: new Decimal(10000),
      success: true,
      overallScore: new Decimal(85),
      speedScore: new Decimal(90),
      priceImprovementScore: new Decimal(80),
      marketVolatility: new Decimal(0.8),
      timestamp: new Date()
    },
    timestamp: new Date(),
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides
  });

  const createMockExecutions = (count: number): TradeExecution[] => {
    return Array.from({ length: count }, (_, i) => createMockExecution({
      id: `exec-${i}`,
      tradeId: `trade-${i}`,
      slippage: new Decimal((Math.random() * 2 - 1).toFixed(4)), // -1 to 1 pips
      latency: 30 + Math.random() * 40, // 30-70ms
      realizedPnl: new Decimal((Math.random() * 100 - 50).toFixed(2)), // -50 to 50
      timestamp: new Date(Date.now() - (count - i) * 3600000) // 1 hour apart
    }));
  };

  beforeEach(() => {
    mockTradeHistoryService = {
      getExecutionsByStrategy: vi.fn(),
      getExecutionsByInstrument: vi.fn(),
      getAllExecutions: vi.fn()
    };

    mockExecutionQualityAnalyzer = {
      getExecutionEfficiencyMetrics: vi.fn()
    };

    reporter = new ExecutionPerformanceReporter(
      mockTradeHistoryService,
      mockExecutionQualityAnalyzer
    );
  });

  describe('generateSlippageAnalysis', () => {
    beforeEach(() => {
      const mockExecutions = createMockExecutions(100);
      mockTradeHistoryService.getExecutionsByInstrument = vi.fn().mockResolvedValue(mockExecutions);
      // Mock the private method getExecutionsByInstrument
      (reporter as any).getExecutionsByInstrument = vi.fn().mockResolvedValue(mockExecutions);
    });

    it('should generate comprehensive slippage analysis', async () => {
      const timeframe = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      const analysis = await reporter.generateSlippageAnalysis('EURUSD', timeframe);

      expect(analysis.instrument).toBe('EURUSD');
      expect(analysis.timeframe).toEqual(timeframe);
      expect(analysis.averageSlippage).toBeInstanceOf(Decimal);
      expect(analysis.medianSlippage).toBeInstanceOf(Decimal);
      expect(analysis.slippageStdDev).toBeInstanceOf(Decimal);
      expect(analysis.positiveSlippageRate).toBeInstanceOf(Decimal);
      expect(analysis.negativeSlippageRate).toBeInstanceOf(Decimal);
      expect(analysis.totalSlippageCost).toBeInstanceOf(Decimal);
    });

    it('should calculate slippage statistics correctly', async () => {
      const executions = [
        createMockExecution({ slippage: new Decimal(0.5) }),
        createMockExecution({ slippage: new Decimal(-0.3) }),
        createMockExecution({ slippage: new Decimal(1.2) }),
        createMockExecution({ slippage: new Decimal(-0.8) }),
        createMockExecution({ slippage: new Decimal(0.1) })
      ];

      (reporter as any).getExecutionsByInstrument = vi.fn().mockResolvedValue(executions);

      const analysis = await reporter.generateSlippageAnalysis('EURUSD', {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      });

      // Average: (0.5 - 0.3 + 1.2 - 0.8 + 0.1) / 5 = 0.14
      expect(analysis.averageSlippage.toNumber()).toBeCloseTo(0.14, 2);
      
      // Positive slippage rate: 3 out of 5 = 60%
      expect(analysis.positiveSlippageRate.toNumber()).toBe(60);
      
      // Negative slippage rate: 2 out of 5 = 40%
      expect(analysis.negativeSlippageRate.toNumber()).toBe(40);
    });

    it('should emit slippageAnalysisGenerated event', async () => {
      const eventPromise = new Promise((resolve) => {
        reporter.once('slippageAnalysisGenerated', resolve);
      });

      const timeframe = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      await reporter.generateSlippageAnalysis('EURUSD', timeframe);

      const event = await eventPromise;
      expect(event).toMatchObject({
        instrument: 'EURUSD',
        timeframe,
        analysis: expect.any(Object)
      });
    });

    it('should throw error when no execution data found', async () => {
      (reporter as any).getExecutionsByInstrument = vi.fn().mockResolvedValue([]);

      await expect(reporter.generateSlippageAnalysis('GBPUSD', {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      })).rejects.toThrow('No execution data found for GBPUSD');
    });

    it('should emit slippageAnalysisError event on error', async () => {
      const eventPromise = new Promise((resolve) => {
        reporter.once('slippageAnalysisError', resolve);
      });

      (reporter as any).getExecutionsByInstrument = vi.fn().mockRejectedValue(new Error('Database error'));

      try {
        await reporter.generateSlippageAnalysis('EURUSD', {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        });
      } catch (error) {
        // Expected to throw
      }

      const event = await eventPromise;
      expect(event).toMatchObject({
        instrument: 'EURUSD',
        error: 'Database error'
      });
    });
  });

  describe('generateExecutionCostAnalysis', () => {
    beforeEach(() => {
      const mockExecutions = createMockExecutions(50).map(exec => ({
        ...exec,
        fees: new Decimal((Math.random() * 5).toFixed(2)),
        quantity: new Decimal(10000 + Math.random() * 5000)
      }));
      (reporter as any).getAllExecutions = vi.fn().mockResolvedValue(mockExecutions);
    });

    it('should generate comprehensive cost analysis', async () => {
      const timeframe = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      const analysis = await reporter.generateExecutionCostAnalysis(timeframe);

      expect(analysis.timeframe).toEqual(timeframe);
      expect(analysis.totalSlippageCost).toBeInstanceOf(Decimal);
      expect(analysis.totalBrokerFees).toBeInstanceOf(Decimal);
      expect(analysis.totalSpreadCost).toBeInstanceOf(Decimal);
      expect(analysis.opportunityCost).toBeInstanceOf(Decimal);
      expect(analysis.latencyCost).toBeInstanceOf(Decimal);
      expect(analysis.failedExecutionCost).toBeInstanceOf(Decimal);
    });

    it('should calculate total costs correctly', async () => {
      const executions = [
        createMockExecution({
          slippage: new Decimal(0.5),
          quantity: new Decimal(10000),
          fees: new Decimal(2.5)
        }),
        createMockExecution({
          slippage: new Decimal(0.3),
          quantity: new Decimal(15000),
          fees: new Decimal(3.75)
        })
      ];

      (reporter as any).getAllExecutions = vi.fn().mockResolvedValue(executions);

      const analysis = await reporter.generateExecutionCostAnalysis({
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      });

      // Total slippage cost: (0.5 * 10000) + (0.3 * 15000) = 5000 + 4500 = 9500
      expect(analysis.totalSlippageCost.toNumber()).toBe(9500);
      
      // Total broker fees: 2.5 + 3.75 = 6.25
      expect(analysis.totalBrokerFees.toNumber()).toBe(6.25);
    });

    it('should emit executionCostAnalysisGenerated event', async () => {
      const eventPromise = new Promise((resolve) => {
        reporter.once('executionCostAnalysisGenerated', resolve);
      });

      const timeframe = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      await reporter.generateExecutionCostAnalysis(timeframe);

      const event = await eventPromise;
      expect(event).toMatchObject({
        timeframe,
        analysis: expect.any(Object)
      });
    });

    it('should throw error when no execution data found', async () => {
      (reporter as any).getAllExecutions = vi.fn().mockResolvedValue([]);

      await expect(reporter.generateExecutionCostAnalysis({
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      })).rejects.toThrow('No execution data found for cost analysis');
    });
  });

  describe('generateTrendAnalysis', () => {
    beforeEach(() => {
      const mockExecutions = createMockExecutions(30);
      (reporter as any).getAllExecutions = vi.fn().mockResolvedValue(mockExecutions);
    });

    it('should generate trend analysis for slippage metric', async () => {
      const timeframe = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      const analysis = await reporter.generateTrendAnalysis('slippage', timeframe, 'day');

      expect(analysis.trend).toMatch(/IMPROVING|STABLE|DEGRADING/);
      expect(typeof analysis.trendStrength).toBe('number');
      expect(analysis.seasonality).toBeDefined();
      expect(analysis.forecast).toBeDefined();
      expect(Array.isArray(analysis.dataPoints)).toBe(true);
    });

    it('should generate trend analysis for latency metric', async () => {
      const analysis = await reporter.generateTrendAnalysis('latency', {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      }, 'hour');

      expect(analysis.trend).toMatch(/IMPROVING|STABLE|DEGRADING/);
      expect(analysis.dataPoints).toBeDefined();
    });

    it('should emit trendAnalysisGenerated event', async () => {
      const eventPromise = new Promise((resolve) => {
        reporter.once('trendAnalysisGenerated', resolve);
      });

      const timeframe = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      await reporter.generateTrendAnalysis('slippage', timeframe);

      const event = await eventPromise;
      expect(event).toMatchObject({
        metric: 'slippage',
        timeframe,
        granularity: 'day',
        analysis: expect.any(Object)
      });
    });

    it('should handle different granularities', async () => {
      const granularities: ('hour' | 'day' | 'week')[] = ['hour', 'day', 'week'];
      
      for (const granularity of granularities) {
        const analysis = await reporter.generateTrendAnalysis('success_rate', {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        }, granularity);

        expect(analysis.trend).toMatch(/IMPROVING|STABLE|DEGRADING/);
      }
    });
  });

  describe('generatePerformanceComparison', () => {
    beforeEach(() => {
      // Mock backtest data
      const mockBacktestData = {
        strategyId: 'strategy-123',
        timeframe: { start: new Date('2024-01-01'), end: new Date('2024-01-31') },
        totalTrades: 100,
        winRate: new Decimal(65),
        averageReturn: new Decimal(50),
        maxDrawdown: new Decimal(200),
        sharpeRatio: new Decimal(1.5),
        expectedSlippage: new Decimal(0.3),
        expectedLatency: 40,
        simulatedExecutions: []
      };

      // Mock live data
      const mockLiveData = {
        strategyId: 'strategy-123',
        timeframe: { start: new Date('2024-01-01'), end: new Date('2024-01-31') },
        totalTrades: 95,
        actualWinRate: new Decimal(62),
        actualAverageReturn: new Decimal(45),
        actualMaxDrawdown: new Decimal(250),
        actualSharpeRatio: new Decimal(1.3),
        actualSlippage: new Decimal(0.5),
        actualLatency: 55,
        liveExecutions: createMockExecutions(95)
      };

      (reporter as any).getBacktestData = vi.fn().mockResolvedValue(mockBacktestData);
      (reporter as any).getLiveExecutionData = vi.fn().mockResolvedValue(mockLiveData);
    });

    it('should generate performance comparison successfully', async () => {
      const timeframe = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      const comparison = await reporter.generatePerformanceComparison('strategy-123', timeframe);

      expect(comparison.strategyId).toBe('strategy-123');
      expect(comparison.timeframe).toEqual(timeframe);
      
      // Win rate comparison
      expect(comparison.winRateComparison.backtest.toNumber()).toBe(65);
      expect(comparison.winRateComparison.live.toNumber()).toBe(62);
      expect(comparison.winRateComparison.difference.toNumber()).toBe(-3);
      
      // Return comparison
      expect(comparison.returnComparison.backtest.toNumber()).toBe(50);
      expect(comparison.returnComparison.live.toNumber()).toBe(45);
      expect(comparison.returnComparison.difference.toNumber()).toBe(-5);
      
      // Slippage impact
      expect(comparison.slippageImpact.expected.toNumber()).toBe(0.3);
      expect(comparison.slippageImpact.actual.toNumber()).toBe(0.5);
      expect(comparison.slippageImpact.impact.toNumber()).toBe(0.2);
      
      // Overall assessment
      expect(comparison.overallAssessment).toMatch(/EXCELLENT|GOOD|FAIR|POOR|CRITICAL/);
      
      // Recommendations
      expect(Array.isArray(comparison.recommendations)).toBe(true);
    });

    it('should emit performanceComparisonGenerated event', async () => {
      const eventPromise = new Promise((resolve) => {
        reporter.once('performanceComparisonGenerated', resolve);
      });

      const timeframe = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      await reporter.generatePerformanceComparison('strategy-123', timeframe);

      const event = await eventPromise;
      expect(event).toMatchObject({
        strategyId: 'strategy-123',
        timeframe,
        comparison: expect.any(Object)
      });
    });

    it('should throw error when insufficient data', async () => {
      (reporter as any).getBacktestData = vi.fn().mockResolvedValue(null);
      (reporter as any).getLiveExecutionData = vi.fn().mockResolvedValue(null);

      await expect(reporter.generatePerformanceComparison('strategy-456', {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      })).rejects.toThrow('Insufficient data for comparison: strategy strategy-456');
    });

    it('should cache comparison results', async () => {
      const timeframe = {
        start: new Date('2024-01-01'),
        end: new Date('2024-01-31')
      };

      // First call
      const comparison1 = await reporter.generatePerformanceComparison('strategy-123', timeframe);
      
      // Second call should use cache
      const comparison2 = await reporter.generatePerformanceComparison('strategy-123', timeframe);
      
      expect(comparison1).toEqual(comparison2);
      expect((reporter as any).getBacktestData).toHaveBeenCalledTimes(2);
      expect((reporter as any).getLiveExecutionData).toHaveBeenCalledTimes(2);
    });
  });

  describe('statistical calculations', () => {
    it('should calculate average correctly', () => {
      const values = [
        new Decimal(1.5),
        new Decimal(2.3),
        new Decimal(0.8),
        new Decimal(3.1),
        new Decimal(1.9)
      ];

      const average = (reporter as any).calculateAverage(values);
      expect(average.toNumber()).toBeCloseTo(1.92, 2);
    });

    it('should calculate median correctly for odd number of values', () => {
      const values = [
        new Decimal(1),
        new Decimal(3),
        new Decimal(2),
        new Decimal(5),
        new Decimal(4)
      ];

      const median = (reporter as any).calculateMedian(values);
      expect(median.toNumber()).toBe(3);
    });

    it('should calculate median correctly for even number of values', () => {
      const values = [
        new Decimal(1),
        new Decimal(3),
        new Decimal(2),
        new Decimal(4)
      ];

      const median = (reporter as any).calculateMedian(values);
      expect(median.toNumber()).toBe(2.5);
    });

    it('should calculate standard deviation correctly', () => {
      const values = [
        new Decimal(2),
        new Decimal(4),
        new Decimal(6),
        new Decimal(8),
        new Decimal(10)
      ];

      const stdDev = (reporter as any).calculateStandardDeviation(values);
      // Standard deviation of [2,4,6,8,10] is approximately 3.16
      expect(stdDev.toNumber()).toBeCloseTo(3.16, 1);
    });

    it('should handle empty arrays gracefully', () => {
      const average = (reporter as any).calculateAverage([]);
      const median = (reporter as any).calculateMedian([]);
      const stdDev = (reporter as any).calculateStandardDeviation([]);

      expect(average.toNumber()).toBe(0);
      expect(median.toNumber()).toBe(0);
      expect(stdDev.toNumber()).toBe(0);
    });
  });

  describe('financial precision', () => {
    it('should maintain decimal precision in cost calculations', () => {
      const executions = [
        createMockExecution({
          slippage: new Decimal('0.123456'),
          quantity: new Decimal('12345.6789'),
          fees: new Decimal('2.345678')
        })
      ];

      const totalSlippageCost = (reporter as any).calculateTotalSlippageCost(executions);
      const totalBrokerFees = (reporter as any).calculateTotalBrokerFees(executions);

      // Slippage cost: 0.123456 * 12345.6789
      expect(totalSlippageCost.toNumber()).toBeCloseTo(1524.1481342784, 8);
      expect(totalBrokerFees.toNumber()).toBe(2.345678);
    });

    it('should calculate win rate with financial precision', () => {
      const executions = [
        createMockExecution({ realizedPnl: new Decimal('10.123456') }),
        createMockExecution({ realizedPnl: new Decimal('-5.654321') }),
        createMockExecution({ realizedPnl: new Decimal('15.987654') })
      ];

      const winRate = (reporter as any).calculateWinRate(executions);
      
      // 2 winners out of 3 = 66.6667%
      expect(winRate.toNumber()).toBeCloseTo(66.67, 2);
    });

    it('should calculate Sharpe ratio with high precision', () => {
      const executions = Array.from({ length: 10 }, (_, i) => 
        createMockExecution({ 
          realizedPnl: new Decimal((10 + i * 2).toFixed(6)) 
        })
      );

      const sharpeRatio = (reporter as any).calculateSharpeRatio(executions);
      expect(sharpeRatio).toBeInstanceOf(Decimal);
      expect(sharpeRatio.toNumber()).toBeGreaterThan(0);
    });
  });

  describe('error handling', () => {
    it('should emit error events when operations fail', async () => {
      const errorPromise = new Promise((resolve) => {
        reporter.once('performanceComparisonError', resolve);
      });

      (reporter as any).getBacktestData = vi.fn().mockRejectedValue(new Error('Data fetch failed'));

      try {
        await reporter.generatePerformanceComparison('strategy-error', {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31')
        });
      } catch (error) {
        // Expected to throw
      }

      const event = await errorPromise;
      expect(event).toMatchObject({
        strategyId: 'strategy-error',
        error: 'Data fetch failed'
      });
    });

    it('should handle division by zero in calculations', () => {
      const executions = [
        createMockExecution({ realizedPnl: new Decimal(0) }),
        createMockExecution({ realizedPnl: new Decimal(0) })
      ];

      const sharpeRatio = (reporter as any).calculateSharpeRatio(executions);
      expect(sharpeRatio.toNumber()).toBe(0);
    });

    it('should handle single execution gracefully', () => {
      const executions = [createMockExecution()];
      
      const winRate = (reporter as any).calculateWinRate(executions);
      const avgReturn = (reporter as any).calculateAverageReturn(executions);
      
      expect(winRate.toNumber()).toBe(100); // Single profitable trade
      expect(avgReturn).toBeInstanceOf(Decimal);
    });
  });

  describe('performance assessment', () => {
    it('should assess excellent performance correctly', () => {
      const backtestData = {
        averageReturn: new Decimal(100),
        strategyId: 'test',
        timeframe: { start: new Date(), end: new Date() },
        totalTrades: 100,
        winRate: new Decimal(70),
        maxDrawdown: new Decimal(100),
        sharpeRatio: new Decimal(1.5),
        expectedSlippage: new Decimal(0.3),
        expectedLatency: 40,
        simulatedExecutions: []
      };
      
      const liveData = {
        actualAverageReturn: new Decimal(98), // Only 2% worse than backtest
        strategyId: 'test',
        timeframe: { start: new Date(), end: new Date() },
        totalTrades: 95,
        actualWinRate: new Decimal(68),
        actualMaxDrawdown: new Decimal(110),
        actualSharpeRatio: new Decimal(1.4),
        actualSlippage: new Decimal(0.4),
        actualLatency: 45,
        liveExecutions: []
      };

      const assessment = (reporter as any).assessOverallPerformance(backtestData, liveData);
      expect(assessment).toBe('EXCELLENT');
    });

    it('should assess poor performance correctly', () => {
      const backtestData = {
        averageReturn: new Decimal(100),
        strategyId: 'test',
        timeframe: { start: new Date(), end: new Date() },
        totalTrades: 100,
        winRate: new Decimal(70),
        maxDrawdown: new Decimal(100),
        sharpeRatio: new Decimal(1.5),
        expectedSlippage: new Decimal(0.3),
        expectedLatency: 40,
        simulatedExecutions: []
      };
      
      const liveData = {
        actualAverageReturn: new Decimal(40), // 60% worse than backtest
        strategyId: 'test',
        timeframe: { start: new Date(), end: new Date() },
        totalTrades: 80,
        actualWinRate: new Decimal(45),
        actualMaxDrawdown: new Decimal(300),
        actualSharpeRatio: new Decimal(0.5),
        actualSlippage: new Decimal(2.0),
        actualLatency: 120,
        liveExecutions: []
      };

      const assessment = (reporter as any).assessOverallPerformance(backtestData, liveData);
      expect(assessment).toBe('CRITICAL');
    });
  });
});