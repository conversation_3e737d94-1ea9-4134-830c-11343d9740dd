/**
 * Metrics Translation Service
 * 
 * Orchestrates the translation of performance metrics into plain English
 * with caching, user context management, and integration with data sources.
 */

import {
  MetricType,
  MetricTranslationContext,
  PlainEnglishMetric,
  StrategyHealthScore,
  HealthScoreExplanation,
  GetTranslatedMetricsRequest,
  GetTranslatedMetricsResponse,
  PerformanceMetrics,
  MetricTranslationCache,
  UserExperienceLevel,
  RiskTolerance,
} from '@golddaddy/types';

import { PlainEnglishMetricsService } from './PlainEnglishMetricsService.js';
import { PerformanceCalculator } from '../optimization/PerformanceCalculator.js';

export class MetricsTranslationService {
  private metricsService: PlainEnglishMetricsService;
  private performanceCalculator: PerformanceCalculator;
  private cache: Map<string, MetricTranslationCache>;
  private cacheExpirationMs: number;

  constructor() {
    this.metricsService = new PlainEnglishMetricsService();
    this.performanceCalculator = new PerformanceCalculator();
    this.cache = new Map();
    this.cacheExpirationMs = 24 * 60 * 60 * 1000; // 24 hours
  }

  /**
   * Get translated metrics for a strategy with user context
   */
  public async getTranslatedMetrics(
    request: GetTranslatedMetricsRequest,
    userId: string,
    userExperience: UserExperienceLevel,
    userRiskTolerance: RiskTolerance
  ): Promise<GetTranslatedMetricsResponse> {
    const context: MetricTranslationContext = {
      userId,
      strategyId: request.strategyId,
      strategyName: `Strategy ${request.strategyId}`, // TODO: Fetch from strategy service
      userExperience,
      userRiskTolerance,
    };

    // Check cache first
    const cacheKey = this.generateCacheKey(request, context);
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      return this.buildResponse(cached, request, context);
    }

    // Get raw performance metrics (this would integrate with actual data source)
    const rawMetrics = await this.getRawPerformanceMetrics(request.strategyId);
    
    // Translate requested metrics or all if none specified
    const metricsToTranslate = request.metricTypes ?? [
      'win_rate',
      'profit_factor',
      'sharpe_ratio',
      'max_drawdown',
      'total_return',
    ];

    const translatedMetrics: PlainEnglishMetric[] = [];
    
    for (const metricType of metricsToTranslate) {
      const translated = await this.translateSingleMetric(
        metricType,
        rawMetrics,
        context
      );
      if (translated) {
        translatedMetrics.push(translated);
      }
    }

    // Calculate health score
    const healthScore = this.metricsService.calculateHealthScore(rawMetrics, context);
    const healthExplanation = this.metricsService.generateHealthScoreExplanation(
      healthScore,
      context
    );

    // Cache the results
    const cacheEntry: MetricTranslationCache = {
      key: cacheKey,
      metrics: translatedMetrics,
      healthScore,
      createdAt: new Date(),
      expiresAt: new Date(Date.now() + this.cacheExpirationMs),
      accessCount: 1,
      lastAccessedAt: new Date(),
    };
    this.cache.set(cacheKey, cacheEntry);

    // Build response
    return {
      strategyId: request.strategyId,
      strategyName: await this.getStrategyName(request.strategyId),
      metrics: translatedMetrics,
      healthScore,
      healthExplanation,
      generatedAt: new Date(),
      validUntil: new Date(Date.now() + this.cacheExpirationMs),
    };
  }

  /**
   * Translate a single metric type
   */
  public async translateSingleMetric(
    metricType: MetricType,
    rawMetrics: PerformanceMetrics,
    context: MetricTranslationContext
  ): Promise<PlainEnglishMetric | null> {
    try {
      switch (metricType) {
        case 'win_rate':
          return this.metricsService.translateWinRate(rawMetrics.winRate, context);
        
        case 'profit_factor':
          return this.metricsService.translateProfitFactor(rawMetrics.profitFactor, context);
        
        case 'sharpe_ratio':
          return this.metricsService.translateSharpeRatio(rawMetrics.sharpeRatio, context);
        
        case 'max_drawdown':
          return this.translateMaxDrawdown(rawMetrics.maxDrawdown, context);
        
        case 'total_return':
          return this.translateTotalReturn(rawMetrics.totalReturn, context);
        
        case 'calmar_ratio':
          return this.translateCalmarRatio(rawMetrics.calmarRatio, context);
        
        case 'sortino_ratio':
          return this.translateSortinoRatio(rawMetrics.sortinoRatio, context);
        
        case 'volatility':
          return this.translateVolatility(rawMetrics.volatility, context);
        
        case 'trade_count':
          return this.translateTradeCount(rawMetrics.tradeCount, context);
        
        case 'avg_trade_return':
          return this.translateAvgTradeReturn(rawMetrics.averageTradeReturn, context);
        
        default:
          console.warn(`Unsupported metric type: ${metricType}`);
          return null;
      }
    } catch (error) {
      console.error(`Error translating metric ${metricType}:`, error);
      return null;
    }
  }

  /**
   * Invalidate cache for a strategy (called when new data is available)
   */
  public invalidateStrategyCache(strategyId: string): void {
    const keysToRemove: string[] = [];
    
    for (const [key, cached] of this.cache.entries()) {
      if (key.includes(strategyId)) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => this.cache.delete(key));
  }

  /**
   * Clean expired cache entries
   */
  public cleanExpiredCache(): void {
    const now = new Date();
    const keysToRemove: string[] = [];
    
    for (const [key, cached] of this.cache.entries()) {
      if (cached.expiresAt < now) {
        keysToRemove.push(key);
      }
    }
    
    keysToRemove.forEach(key => this.cache.delete(key));
  }

  /**
   * Get cache statistics
   */
  public getCacheStats(): {
    size: number;
    hitRate: number;
    oldestEntry: Date | null;
    newestEntry: Date | null;
  } {
    if (this.cache.size === 0) {
      return {
        size: 0,
        hitRate: 0,
        oldestEntry: null,
        newestEntry: null,
      };
    }

    let totalAccesses = 0;
    let totalHits = 0;
    let oldestEntry: Date | null = null;
    let newestEntry: Date | null = null;

    for (const cached of this.cache.values()) {
      totalAccesses += cached.accessCount;
      totalHits += cached.accessCount > 1 ? cached.accessCount - 1 : 0;
      
      if (!oldestEntry || cached.createdAt < oldestEntry) {
        oldestEntry = cached.createdAt;
      }
      if (!newestEntry || cached.createdAt > newestEntry) {
        newestEntry = cached.createdAt;
      }
    }

    return {
      size: this.cache.size,
      hitRate: totalAccesses > 0 ? totalHits / totalAccesses : 0,
      oldestEntry,
      newestEntry,
    };
  }

  // ===== Private Helper Methods =====

  private generateCacheKey(
    request: GetTranslatedMetricsRequest,
    context: MetricTranslationContext
  ): string {
    const keyParts = [
      request.strategyId,
      context.userExperience,
      context.userRiskTolerance,
      request.metricTypes?.sort().join(',') ?? 'all',
      request.timeRange ? `${request.timeRange.start.getTime()}-${request.timeRange.end.getTime()}` : 'current',
    ];
    
    return keyParts.join('|');
  }

  private getFromCache(cacheKey: string): MetricTranslationCache | null {
    const cached = this.cache.get(cacheKey);
    if (!cached) return null;

    // Check if expired
    if (cached.expiresAt < new Date()) {
      this.cache.delete(cacheKey);
      return null;
    }

    // Update access statistics
    cached.accessCount++;
    cached.lastAccessedAt = new Date();
    
    return cached;
  }

  private buildResponse(
    cached: MetricTranslationCache,
    request: GetTranslatedMetricsRequest,
    context: MetricTranslationContext
  ): GetTranslatedMetricsResponse {
    return {
      strategyId: request.strategyId,
      strategyName: 'Cached Strategy', // In real implementation, get from database
      metrics: cached.metrics,
      healthScore: cached.healthScore,
      healthExplanation: this.metricsService.generateHealthScoreExplanation(
        cached.healthScore,
        context
      ),
      generatedAt: cached.createdAt,
      validUntil: cached.expiresAt,
    };
  }

  private async getRawPerformanceMetrics(strategyId: string): Promise<PerformanceMetrics> {
    // In real implementation, this would fetch from database/optimization results
    // For now, return mock data that represents a decent strategy
    return {
      totalReturn: 0.15,      // 15% return
      sharpeRatio: 1.25,      // Good Sharpe ratio
      profitFactor: 1.8,      // Strong profit factor
      winRate: 0.62,          // 62% win rate
      maxDrawdown: 0.08,      // 8% max drawdown
      tradeCount: 45,         // Decent sample size
      averageTradeReturn: 0.003, // 0.3% average per trade
      volatility: 0.12,       // 12% volatility
      calmarRatio: 1.875,     // Total return / max drawdown
      sortinoRatio: 1.45,     // Good downside risk ratio
    };
  }

  private async getStrategyName(strategyId: string): Promise<string> {
    // In real implementation, fetch from database
    return `Strategy ${strategyId.slice(-6)}`;
  }

  // ===== Additional Metric Translation Methods =====

  private translateMaxDrawdown(maxDrawdown: number, context: MetricTranslationContext): PlainEnglishMetric {
    const performance = maxDrawdown <= 0.05 ? 'excellent' :
                       maxDrawdown <= 0.10 ? 'good' :
                       maxDrawdown <= 0.20 ? 'average' :
                       maxDrawdown <= 0.30 ? 'poor' : 'concerning';

    const primary = context.userExperience === 'beginner' ?
      `Your worst losing streak was ${(maxDrawdown * 100).toFixed(1)}% of your capital` :
      `Maximum drawdown of ${(maxDrawdown * 100).toFixed(1)}%`;

    return {
      id: `${context.strategyId}_max_drawdown_${Date.now()}`,
      metricType: 'max_drawdown',
      originalValue: maxDrawdown,
      translation: {
        primary,
        secondary: performance === 'excellent' ? 'Excellent risk control' :
                  performance === 'good' ? 'Good risk management' :
                  performance === 'average' ? 'Acceptable drawdown levels' :
                  'High drawdown requires attention',
        comparison: maxDrawdown <= 0.05 ? 'Better than 90% of strategies' :
                   maxDrawdown <= 0.10 ? 'Better than 70% of strategies' :
                   maxDrawdown <= 0.20 ? 'Average compared to most strategies' :
                   'Higher than most successful strategies',
      },
      visualMetadata: {
        color: performance === 'excellent' || performance === 'good' ? 'green' :
               performance === 'average' ? 'yellow' : 'red',
        iconType: performance === 'excellent' || performance === 'good' ? 'thumbs_up' :
                  performance === 'average' ? 'warning' : 'thumbs_down',
        progressBar: {
          value: Math.min((1 - maxDrawdown) * 100, 100),
          ranges: {
            poor: { min: 0, max: 70 },
            average: { min: 70, max: 90 },
            good: { min: 90, max: 100 },
          },
        },
        badgeType: performance === 'excellent' || performance === 'good' ? 'success' :
                   performance === 'average' ? 'warning' : 'error',
      },
      contextualAdvice: {
        interpretation: maxDrawdown <= 0.10 ? 
          'Your strategy maintains good capital preservation during difficult periods.' :
          'Your strategy experiences significant capital swings that may be uncomfortable.',
        nextSteps: maxDrawdown > 0.20 ? 
          ['Reduce position sizes', 'Implement tighter stop losses', 'Review risk management rules'] :
          maxDrawdown > 0.10 ?
          ['Fine-tune position sizing', 'Consider volatility-based stops'] :
          ['Maintain current risk controls', 'Monitor for consistency'],
        warnings: maxDrawdown > 0.30 ? 
          ['Extremely high drawdown may lead to strategy abandonment'] :
          maxDrawdown > 0.20 ?
          ['High drawdown levels may cause emotional trading decisions'] : [],
        celebration: maxDrawdown <= 0.03 ? 
          '🛡️ Outstanding risk control! Your strategy protects capital exceptionally well.' : undefined,
        educationalTip: context.userExperience === 'beginner' ?
          'Drawdown shows your worst losing streak. Lower is better for peace of mind!' :
          'Drawdown measures peak-to-trough decline. Consider psychological tolerance vs. mathematical optimization.',
      },
      userPersonalization: {
        experienceLevel: context.userExperience,
        riskTolerance: context.userRiskTolerance,
      },
      calculatedAt: new Date(),
      validUntil: new Date(Date.now() + this.cacheExpirationMs),
    };
  }

  private translateTotalReturn(totalReturn: number, context: MetricTranslationContext): PlainEnglishMetric {
    const annualizedReturn = totalReturn; // Assuming this is already annualized
    const performance = annualizedReturn >= 0.25 ? 'excellent' :
                       annualizedReturn >= 0.15 ? 'good' :
                       annualizedReturn >= 0.08 ? 'average' :
                       annualizedReturn >= 0.02 ? 'poor' : 'concerning';

    const primary = context.userExperience === 'beginner' ?
      `Your strategy made ${(annualizedReturn * 100).toFixed(1)}% profit this year` :
      `Total return of ${(annualizedReturn * 100).toFixed(1)}% annualized`;

    return {
      id: `${context.strategyId}_total_return_${Date.now()}`,
      metricType: 'total_return',
      originalValue: totalReturn,
      translation: {
        primary,
        secondary: performance === 'excellent' ? 'Exceptional returns' :
                  performance === 'good' ? 'Strong returns' :
                  performance === 'average' ? 'Moderate returns' :
                  performance === 'poor' ? 'Low returns' : 'Losses incurred',
        comparison: annualizedReturn >= 0.20 ? 'Better than most hedge funds' :
                   annualizedReturn >= 0.12 ? 'Better than stock market average' :
                   annualizedReturn >= 0.08 ? 'Similar to market returns' :
                   'Below market performance',
      },
      visualMetadata: {
        color: performance === 'excellent' || performance === 'good' ? 'green' :
               performance === 'average' ? 'yellow' : 'red',
        iconType: performance === 'excellent' || performance === 'good' ? 'thumbs_up' :
                  performance === 'average' ? 'warning' : 'thumbs_down',
        progressBar: {
          value: Math.min(Math.max((annualizedReturn + 0.2) * 200, 0), 100),
          ranges: {
            poor: { min: 0, max: 40 },
            average: { min: 40, max: 70 },
            good: { min: 70, max: 100 },
          },
        },
        badgeType: performance === 'excellent' || performance === 'good' ? 'success' :
                   performance === 'average' ? 'warning' : 'error',
      },
      contextualAdvice: {
        interpretation: annualizedReturn >= 0.15 ?
          'Your strategy delivers excellent returns that justify the effort and risk.' :
          annualizedReturn >= 0.08 ?
          'Your strategy provides reasonable returns with room for improvement.' :
          'Your strategy needs optimization to generate attractive returns.',
        nextSteps: annualizedReturn < 0.05 ?
          ['Review strategy fundamentals', 'Consider paper trading', 'Analyze market conditions'] :
          annualizedReturn < 0.12 ?
          ['Optimize entry/exit timing', 'Review position sizing', 'Consider strategy combinations'] :
          ['Maintain current approach', 'Scale up gradually', 'Monitor risk levels'],
        warnings: annualizedReturn < 0 ?
          ['Strategy is losing money - immediate review required'] :
          annualizedReturn < 0.02 ?
          ['Returns may not justify trading costs and effort'] : [],
        celebration: annualizedReturn >= 0.30 ?
          '🚀 Phenomenal returns! You\'re in the top tier of traders.' :
          annualizedReturn >= 0.20 ?
          '🎯 Excellent returns! You\'re beating professional benchmarks.' : undefined,
        educationalTip: context.userExperience === 'beginner' ?
          'Total return shows your profit percentage. Compare to S&P 500\'s ~10% historical average.' :
          'Consider risk-adjusted returns (Sharpe ratio) alongside total return for complete picture.',
      },
      userPersonalization: {
        experienceLevel: context.userExperience,
        riskTolerance: context.userRiskTolerance,
      },
      calculatedAt: new Date(),
      validUntil: new Date(Date.now() + this.cacheExpirationMs),
    };
  }

  private translateCalmarRatio(calmarRatio: number, context: MetricTranslationContext): PlainEnglishMetric {
    const performance = calmarRatio >= 3.0 ? 'excellent' :
                       calmarRatio >= 2.0 ? 'good' :
                       calmarRatio >= 1.0 ? 'average' :
                       calmarRatio >= 0.5 ? 'poor' : 'concerning';

    const primary = context.userExperience === 'beginner' ?
      `Your return-to-worst-loss ratio is ${calmarRatio.toFixed(1)}` :
      `Calmar ratio of ${calmarRatio.toFixed(2)}`;

    return {
      id: `${context.strategyId}_calmar_ratio_${Date.now()}`,
      metricType: 'calmar_ratio',
      originalValue: calmarRatio,
      translation: {
        primary,
        secondary: 'This measures annual return divided by maximum drawdown',
        comparison: calmarRatio >= 2.0 ? 'Excellent risk-adjusted performance' :
                   calmarRatio >= 1.0 ? 'Good balance of return and risk' :
                   'Needs improvement in risk management',
      },
      visualMetadata: {
        color: performance === 'excellent' || performance === 'good' ? 'green' :
               performance === 'average' ? 'yellow' : 'red',
        iconType: this.getPerformanceIcon(performance),
        badgeType: this.getBadgeType(performance),
      },
      contextualAdvice: {
        interpretation: calmarRatio >= 2.0 ?
          'Excellent balance between returns and maximum risk experienced.' :
          'Strategy could benefit from better risk management.',
        nextSteps: calmarRatio < 1.0 ?
          ['Improve risk management', 'Reduce position sizes', 'Implement better stops'] :
          ['Monitor consistency', 'Consider scaling'],
        educationalTip: 'Calmar ratio shows return per unit of maximum risk. Higher is better.',
      },
      userPersonalization: {
        experienceLevel: context.userExperience,
        riskTolerance: context.userRiskTolerance,
      },
      calculatedAt: new Date(),
      validUntil: new Date(Date.now() + this.cacheExpirationMs),
    };
  }

  private translateSortinoRatio(sortinoRatio: number, context: MetricTranslationContext): PlainEnglishMetric {
    const performance = sortinoRatio >= 2.0 ? 'excellent' :
                       sortinoRatio >= 1.5 ? 'good' :
                       sortinoRatio >= 1.0 ? 'average' :
                       sortinoRatio >= 0.5 ? 'poor' : 'concerning';

    return {
      id: `${context.strategyId}_sortino_ratio_${Date.now()}`,
      metricType: 'sortino_ratio',
      originalValue: sortinoRatio,
      translation: {
        primary: context.userExperience === 'beginner' ?
          `Your downside risk ratio is ${sortinoRatio.toFixed(1)}` :
          `Sortino ratio of ${sortinoRatio.toFixed(2)}`,
        secondary: 'This focuses only on harmful volatility (downside risk)',
        comparison: sortinoRatio >= 1.5 ? 'Excellent downside protection' : 'Consider downside risk management',
      },
      visualMetadata: {
        color: this.getPerformanceColor(performance),
        iconType: this.getPerformanceIcon(performance),
        badgeType: this.getBadgeType(performance),
      },
      contextualAdvice: {
        interpretation: 'Measures excess return per unit of downside deviation.',
        educationalTip: 'Sortino ratio is like Sharpe ratio but only penalizes negative volatility.',
      },
      userPersonalization: {
        experienceLevel: context.userExperience,
        riskTolerance: context.userRiskTolerance,
      },
      calculatedAt: new Date(),
      validUntil: new Date(Date.now() + this.cacheExpirationMs),
    };
  }

  private translateVolatility(volatility: number, context: MetricTranslationContext): PlainEnglishMetric {
    const performance = volatility <= 0.10 ? 'excellent' :
                       volatility <= 0.15 ? 'good' :
                       volatility <= 0.25 ? 'average' :
                       volatility <= 0.35 ? 'poor' : 'concerning';

    return {
      id: `${context.strategyId}_volatility_${Date.now()}`,
      metricType: 'volatility',
      originalValue: volatility,
      translation: {
        primary: context.userExperience === 'beginner' ?
          `Your returns swing up and down by ${(volatility * 100).toFixed(1)}% typically` :
          `Volatility of ${(volatility * 100).toFixed(1)}%`,
        secondary: 'This measures how much your returns bounce around',
        comparison: volatility <= 0.15 ? 'Low volatility - smooth sailing' : 'Higher volatility - bumpy ride',
      },
      visualMetadata: {
        color: this.getPerformanceColor(performance),
        iconType: this.getPerformanceIcon(performance),
        badgeType: this.getBadgeType(performance),
      },
      contextualAdvice: {
        interpretation: volatility <= 0.15 ?
          'Low volatility indicates consistent, predictable returns.' :
          'High volatility may cause emotional stress and poor decisions.',
        educationalTip: 'Volatility measures return fluctuation. Lower is generally better for peace of mind.',
      },
      userPersonalization: {
        experienceLevel: context.userExperience,
        riskTolerance: context.userRiskTolerance,
      },
      calculatedAt: new Date(),
      validUntil: new Date(Date.now() + this.cacheExpirationMs),
    };
  }

  private translateTradeCount(tradeCount: number, context: MetricTranslationContext): PlainEnglishMetric {
    const performance = tradeCount >= 100 ? 'excellent' :
                       tradeCount >= 50 ? 'good' :
                       tradeCount >= 30 ? 'average' :
                       tradeCount >= 10 ? 'poor' : 'concerning';

    return {
      id: `${context.strategyId}_trade_count_${Date.now()}`,
      metricType: 'trade_count',
      originalValue: tradeCount,
      translation: {
        primary: `${tradeCount} trades executed`,
        secondary: tradeCount >= 30 ? 'Good sample size for analysis' : 'Limited sample - results may not be reliable',
        comparison: tradeCount >= 50 ? 'Sufficient data for confidence' : 'More trades needed for statistical significance',
      },
      visualMetadata: {
        color: this.getPerformanceColor(performance),
        iconType: this.getPerformanceIcon(performance),
        badgeType: this.getBadgeType(performance),
      },
      contextualAdvice: {
        interpretation: tradeCount >= 30 ?
          'Sufficient trades to evaluate strategy performance.' :
          'More trading data needed for reliable conclusions.',
        nextSteps: tradeCount < 30 ?
          ['Continue collecting data', 'Avoid major strategy changes yet'] :
          ['Analyze performance patterns', 'Consider optimization'],
        educationalTip: 'More trades generally means more reliable statistics about your strategy.',
      },
      userPersonalization: {
        experienceLevel: context.userExperience,
        riskTolerance: context.userRiskTolerance,
      },
      calculatedAt: new Date(),
      validUntil: new Date(Date.now() + this.cacheExpirationMs),
    };
  }

  private translateAvgTradeReturn(avgTradeReturn: number, context: MetricTranslationContext): PlainEnglishMetric {
    const performance = avgTradeReturn >= 0.01 ? 'excellent' :
                       avgTradeReturn >= 0.005 ? 'good' :
                       avgTradeReturn >= 0.002 ? 'average' :
                       avgTradeReturn >= 0 ? 'poor' : 'concerning';

    return {
      id: `${context.strategyId}_avg_trade_return_${Date.now()}`,
      metricType: 'avg_trade_return',
      originalValue: avgTradeReturn,
      translation: {
        primary: context.userExperience === 'beginner' ?
          `Each trade makes ${(avgTradeReturn * 100).toFixed(2)}% on average` :
          `Average trade return: ${(avgTradeReturn * 100).toFixed(2)}%`,
        secondary: avgTradeReturn > 0 ? 'Positive average per trade' : 'Losing money per trade on average',
        comparison: avgTradeReturn >= 0.005 ? 'Strong per-trade performance' : 'Needs improvement in trade quality',
      },
      visualMetadata: {
        color: this.getPerformanceColor(performance),
        iconType: this.getPerformanceIcon(performance),
        badgeType: this.getBadgeType(performance),
      },
      contextualAdvice: {
        interpretation: avgTradeReturn > 0 ?
          'Strategy generates positive expected value per trade.' :
          'Strategy loses money on average per trade - needs fixing.',
        educationalTip: 'Average trade return should be positive after considering all costs.',
      },
      userPersonalization: {
        experienceLevel: context.userExperience,
        riskTolerance: context.userRiskTolerance,
      },
      calculatedAt: new Date(),
      validUntil: new Date(Date.now() + this.cacheExpirationMs),
    };
  }

  // Helper methods for consistency across translations
  private getPerformanceIcon(performance: string): 'thumbs_up' | 'warning' | 'thumbs_down' | 'info' {
    switch (performance) {
      case 'excellent':
      case 'good':
        return 'thumbs_up';
      case 'average':
        return 'warning';
      case 'poor':
      case 'concerning':
        return 'thumbs_down';
      default:
        return 'info';
    }
  }

  private getBadgeType(performance: string): 'success' | 'warning' | 'error' | 'info' {
    switch (performance) {
      case 'excellent':
      case 'good':
        return 'success';
      case 'average':
        return 'warning';
      case 'poor':
      case 'concerning':
        return 'error';
      default:
        return 'info';
    }
  }

  private getPerformanceColor(performance: string): 'green' | 'yellow' | 'red' | 'blue' | 'gray' {
    switch (performance) {
      case 'excellent':
      case 'good':
        return 'green';
      case 'average':
        return 'yellow';
      case 'poor':
      case 'concerning':
        return 'red';
      default:
        return 'gray';
    }
  }
}