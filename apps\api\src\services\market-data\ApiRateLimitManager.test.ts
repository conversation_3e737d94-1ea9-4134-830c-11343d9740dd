/**
 * Unit tests for ApiRateLimitManager
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  ApiRateLimitManager,
  RateLimitConfig,
  ProviderLimitConfig,
  RequestPriority,
} from './ApiRateLimitManager';
import { DataSource } from './RealTimeDataProcessor';

describe('ApiRateLimitManager', () => {
  let rateLimitManager: ApiRateLimitManager;
  let config: RateLimitConfig;

  const createProviderConfig = (overrides: Partial<ProviderLimitConfig> = {}): ProviderLimitConfig => ({
    maxRequestsPerSecond: 10,
    maxRequestsPerMinute: 600,
    maxRequestsPerHour: 36000,
    maxRequestsPerDay: 864000,
    maxBurstRequests: 20,
    refillRate: 10,
    maxQueueSize: 100,
    timeoutMs: 5000,
    retryAttempts: 3,
    retryBackoffMs: 1000,
    ...overrides,
  });

  beforeEach(() => {
    config = {
      providers: {
        [DataSource.MT5]: createProviderConfig(),
        [DataSource.ALPHA_VANTAGE]: createProviderConfig({
          maxRequestsPerSecond: 5,
          maxBurstRequests: 10,
          refillRate: 5,
        }),
        [DataSource.YAHOO_FINANCE]: createProviderConfig({
          maxRequestsPerSecond: 2,
          maxBurstRequests: 5,
          refillRate: 2,
        }),
      },
      globalMaxConcurrentRequests: 50,
      enableQueueing: true,
      enableRetries: true,
      enableThrottling: true,
      monitoringIntervalMs: 1000,
      alertThresholds: {
        utilizationPercent: 80,
        queueSizeWarning: 50,
        errorRatePercent: 10,
      },
    };

    rateLimitManager = new ApiRateLimitManager(config);
  });

  afterEach(() => {
    rateLimitManager.shutdown();
  });

  describe('Initialization', () => {
    it('should initialize with correct configuration', () => {
      const systemStats = rateLimitManager.getSystemStats();
      
      expect(systemStats.providerStats).toHaveProperty(DataSource.MT5);
      expect(systemStats.providerStats).toHaveProperty(DataSource.ALPHA_VANTAGE);
      expect(systemStats.providerStats).toHaveProperty(DataSource.YAHOO_FINANCE);
      expect(systemStats.totalActiveRequests).toBe(0);
      expect(systemStats.totalQueuedRequests).toBe(0);
    });

    it('should initialize provider statistics', () => {
      const mt5Stats = rateLimitManager.getProviderStats(DataSource.MT5);
      
      expect(mt5Stats).toBeTruthy();
      expect(mt5Stats!.provider).toBe(DataSource.MT5);
      expect(mt5Stats!.requestsAllowed).toBe(0);
      expect(mt5Stats!.requestsThrottled).toBe(0);
      expect(mt5Stats!.requestsQueued).toBe(0);
      expect(mt5Stats!.requestsFailed).toBe(0);
      expect(mt5Stats!.tokenBucketLevel).toBe(20); // maxBurstRequests
    });
  });

  describe('Token Bucket Implementation', () => {
    it('should allow requests when tokens are available', () => {
      expect(rateLimitManager.canMakeRequest(DataSource.MT5)).toBe(true);
      expect(rateLimitManager.canMakeRequest(DataSource.ALPHA_VANTAGE)).toBe(true);
      expect(rateLimitManager.canMakeRequest(DataSource.YAHOO_FINANCE)).toBe(true);
    });

    it('should consume tokens on request', async () => {
      const initialStats = rateLimitManager.getProviderStats(DataSource.MT5);
      const initialTokens = initialStats!.tokenBucketLevel;

      const requestId = await rateLimitManager.requestAccess(
        DataSource.MT5,
        '/api/prices',
        RequestPriority.MEDIUM
      );

      expect(requestId).toBeTruthy();
      expect(requestId).toMatch(/^req_\d+_[a-z0-9]+$/);

      const afterStats = rateLimitManager.getProviderStats(DataSource.MT5);
      expect(afterStats!.tokenBucketLevel).toBe(initialTokens - 1);
      expect(afterStats!.requestsAllowed).toBe(1);
    });

    it('should refill tokens over time', async () => {
      // Consume some tokens
      await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');

      const midStats = rateLimitManager.getProviderStats(DataSource.MT5);
      const midTokens = midStats!.tokenBucketLevel;

      // Wait for token refill (rate is 10 tokens/second)
      await new Promise(resolve => setTimeout(resolve, 200));

      const finalStats = rateLimitManager.getProviderStats(DataSource.MT5);
      expect(finalStats!.tokenBucketLevel).toBeGreaterThanOrEqual(midTokens);
    });
  });

  describe('Request Processing', () => {
    it('should process requests with different priorities', async () => {
      const requests = await Promise.all([
        rateLimitManager.requestAccess(DataSource.MT5, '/api/prices', RequestPriority.LOW),
        rateLimitManager.requestAccess(DataSource.MT5, '/api/prices', RequestPriority.HIGH),
        rateLimitManager.requestAccess(DataSource.MT5, '/api/prices', RequestPriority.CRITICAL),
      ]);

      expect(requests).toHaveLength(3);
      requests.forEach(requestId => {
        expect(requestId).toMatch(/^req_\d+_[a-z0-9]+$/);
      });

      const stats = rateLimitManager.getProviderStats(DataSource.MT5);
      expect(stats!.requestsAllowed).toBe(3);
    });

    it('should handle request metadata', async () => {
      const metadata = { userId: 'test123', feature: 'price_feed' };
      
      const requestId = await rateLimitManager.requestAccess(
        DataSource.MT5,
        '/api/prices',
        RequestPriority.MEDIUM,
        metadata
      );

      expect(requestId).toBeTruthy();
    });

    it('should release requests correctly', async () => {
      const requestId = await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      rateLimitManager.getSystemStats(); // Check that system state is accessible
      
      rateLimitManager.releaseRequest(requestId);
      
      // Active requests should decrease (though this is async, so we test the method call)
      expect(() => rateLimitManager.releaseRequest(requestId)).not.toThrow();
    });
  });

  describe('Rate Limiting Behavior', () => {
    it('should throttle requests when bucket is empty and queuing disabled', async () => {
      // Create manager with queueing disabled
      const noQueueConfig = {
        ...config,
        enableQueueing: false,
      };
      const noQueueManager = new ApiRateLimitManager(noQueueConfig);
      
      try {
        const mt5Config = config.providers[DataSource.MT5];
        
        // Consume all tokens
        const requests = [];
        for (let i = 0; i < mt5Config.maxBurstRequests; i++) {
          requests.push(await noQueueManager.requestAccess(DataSource.MT5, '/api/prices'));
        }

        // Next request should be throttled when queuing is disabled
        await expect(
          noQueueManager.requestAccess(DataSource.MT5, '/api/prices')
        ).rejects.toThrow('Rate limit exceeded');

        const stats = noQueueManager.getProviderStats(DataSource.MT5);
        expect(stats!.requestsThrottled).toBeGreaterThan(0);
      } finally {
        noQueueManager.shutdown();
      }
    });

    it('should respect global concurrent request limit', async () => {
      // Set very low global limit for testing
      const lowLimitConfig = {
        ...config,
        globalMaxConcurrentRequests: 2,
      };
      
      const limitedManager = new ApiRateLimitManager(lowLimitConfig);
      
      try {
        // Make 2 requests (at the limit)
        await limitedManager.requestAccess(DataSource.MT5, '/api/prices');
        await limitedManager.requestAccess(DataSource.MT5, '/api/prices');

        // Should not be able to make more requests
        expect(limitedManager.canMakeRequest(DataSource.MT5)).toBe(false);
      } finally {
        limitedManager.shutdown();
      }
    });
  });

  describe('Request Queuing', () => {
    it('should queue requests when rate limited', async () => {
      const mt5Config = config.providers[DataSource.MT5];
      
      // Consume all tokens first
      for (let i = 0; i < mt5Config.maxBurstRequests; i++) {
        await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      }

      // This request should be queued
      const queuedPromise = rateLimitManager.requestAccess(
        DataSource.MT5, 
        '/api/prices',
        RequestPriority.HIGH
      );

      const stats = rateLimitManager.getProviderStats(DataSource.MT5);
      expect(stats!.requestsQueued).toBeGreaterThan(0);

      // Wait a bit for token refill and queue processing
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Request should eventually complete
      const requestId = await queuedPromise;
      expect(requestId).toBeTruthy();
    });

    it('should respect queue size limits', async () => {
      // Set very small queue for testing
      const smallQueueConfig = {
        ...config,
        providers: {
          ...config.providers,
          [DataSource.MT5]: {
            ...config.providers[DataSource.MT5],
            maxQueueSize: 2,
            maxBurstRequests: 1, // Force immediate queueing
          },
        },
      };

      const limitedManager = new ApiRateLimitManager(smallQueueConfig);

      try {
        // Consume the single token
        await limitedManager.requestAccess(DataSource.MT5, '/api/prices');

        // Queue 2 requests (at the queue limit)
        limitedManager.requestAccess(DataSource.MT5, '/api/prices'); // First queued request
        limitedManager.requestAccess(DataSource.MT5, '/api/prices'); // Second queued request

        // Third queued request should fail
        await expect(
          limitedManager.requestAccess(DataSource.MT5, '/api/prices')
        ).rejects.toThrow('Request queue full');

      } finally {
        limitedManager.shutdown();
      }
    });

    it('should handle queue priority correctly', async () => {
      const queueStatus = rateLimitManager.getQueueStatus(DataSource.MT5);
      
      expect(queueStatus.totalSize).toBe(0);
      expect(queueStatus.priorityBreakdown).toHaveProperty(RequestPriority.CRITICAL);
      expect(queueStatus.priorityBreakdown).toHaveProperty(RequestPriority.HIGH);
      expect(queueStatus.priorityBreakdown).toHaveProperty(RequestPriority.MEDIUM);
      expect(queueStatus.priorityBreakdown).toHaveProperty(RequestPriority.LOW);
    });

    it('should handle request timeouts in queue', async () => {
      const timeoutConfig = {
        ...config,
        providers: {
          ...config.providers,
          [DataSource.MT5]: {
            ...config.providers[DataSource.MT5],
            maxBurstRequests: 1,
            timeoutMs: 50, // Very short timeout
          },
        },
      };

      const timeoutManager = new ApiRateLimitManager(timeoutConfig);

      try {
        // Consume the token
        await timeoutManager.requestAccess(DataSource.MT5, '/api/prices');

        // This should timeout in queue
        await expect(
          timeoutManager.requestAccess(DataSource.MT5, '/api/prices')
        ).rejects.toThrow('Request timeout');

      } finally {
        timeoutManager.shutdown();
      }
    });
  });

  describe('Statistics and Monitoring', () => {
    it('should track comprehensive statistics', async () => {
      await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      
      const stats = rateLimitManager.getProviderStats(DataSource.MT5);
      
      expect(stats!.totalRequests).toBe(1);
      expect(stats!.requestsAllowed).toBe(1);
      expect(stats!.utilizationPercent).toBeGreaterThan(0);
      expect(stats!.lastRequestAt).toBeInstanceOf(Date);
    });

    it('should calculate utilization correctly', async () => {
      const initialStats = rateLimitManager.getProviderStats(DataSource.MT5);
      expect(initialStats!.utilizationPercent).toBe(0); // Full bucket = 0% utilization

      // Consume half the tokens
      const halfTokens = Math.floor(config.providers[DataSource.MT5].maxBurstRequests / 2);
      for (let i = 0; i < halfTokens; i++) {
        await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      }

      const midStats = rateLimitManager.getProviderStats(DataSource.MT5);
      expect(midStats!.utilizationPercent).toBeGreaterThan(40);
      expect(midStats!.utilizationPercent).toBeLessThan(60);
    });

    it('should provide system-wide statistics', () => {
      const systemStats = rateLimitManager.getSystemStats();
      
      expect(systemStats.totalActiveRequests).toBe(0);
      expect(systemStats.totalQueuedRequests).toBe(0);
      expect(systemStats.overallUtilization).toBeGreaterThanOrEqual(0);
      expect(systemStats.providerStats).toHaveProperty(DataSource.MT5);
      expect(systemStats.alerts).toBeInstanceOf(Array);
      expect(systemStats.lastUpdated).toBeInstanceOf(Date);
    });
  });

  describe('Alerting System', () => {
    it('should generate utilization alerts', async () => {
      const alertSpy = vi.fn();
      rateLimitManager.on('rate_limit_alert', alertSpy);

      // Consume most tokens to trigger high utilization
      const mt5Config = config.providers[DataSource.MT5];
      for (let i = 0; i < mt5Config.maxBurstRequests - 1; i++) {
        await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      }

      // Wait for monitoring interval
      await new Promise(resolve => setTimeout(resolve, 1100));

      // Should eventually generate alert (if utilization > 80%)
      const systemStats = rateLimitManager.getSystemStats();
      if (systemStats.alerts.length > 0) {
        expect(systemStats.alerts.some(alert => alert.includes('utilization'))).toBe(true);
      }
    });

    it('should generate queue size alerts', async () => {
      const systemStats = rateLimitManager.getSystemStats();
      
      // Check that queue monitoring is working
      expect(systemStats.totalQueuedRequests).toBe(0);
    });
  });

  describe('Configuration Management', () => {
    it('should update configuration', () => {
      const configSpy = vi.fn();
      rateLimitManager.on('config_updated', configSpy);

      const newConfig = {
        globalMaxConcurrentRequests: 100,
        alertThresholds: {
          utilizationPercent: 90,
          queueSizeWarning: 75,
          errorRatePercent: 15,
        },
      };

      rateLimitManager.updateConfig(newConfig);
      expect(configSpy).toHaveBeenCalledWith(expect.objectContaining(newConfig));
    });

    it('should update provider configurations', () => {
      const newProviderConfig = {
        providers: {
          [DataSource.MT5]: {
            ...config.providers[DataSource.MT5],
            maxBurstRequests: 30,
            refillRate: 15,
          },
        },
      };

      rateLimitManager.updateConfig(newProviderConfig);
      
      // New token bucket should be created with updated config
      const stats = rateLimitManager.getProviderStats(DataSource.MT5);
      expect(stats).toBeTruthy();
    });
  });

  describe('Emergency Operations', () => {
    it('should clear provider queue', async () => {
      const clearSpy = vi.fn();
      rateLimitManager.on('queue_cleared', clearSpy);

      // Add some items to queue first by exhausting tokens
      const mt5Config = config.providers[DataSource.MT5];
      for (let i = 0; i < mt5Config.maxBurstRequests + 2; i++) {
        try {
          await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
        } catch (error) {
          // Expected for the last few requests
        }
      }

      const cleared = rateLimitManager.clearQueue(DataSource.MT5);
      expect(cleared).toBeGreaterThanOrEqual(0);
      
      if (cleared > 0) {
        expect(clearSpy).toHaveBeenCalledWith({
          source: DataSource.MT5,
          requestsCleared: cleared,
        });
      }
    });
  });

  describe('Health Check', () => {
    it('should report healthy status under normal conditions', () => {
      const health = rateLimitManager.healthCheck();
      
      expect(health.isHealthy).toBe(true);
      expect(health.issues).toHaveLength(0);
      expect(health.systemStats).toBeTruthy();
    });

    it('should detect unhealthy conditions', async () => {
      // Force high utilization
      const mt5Config = config.providers[DataSource.MT5];
      for (let i = 0; i < mt5Config.maxBurstRequests; i++) {
        await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      }

      const health = rateLimitManager.healthCheck();
      
      // Might be unhealthy due to high utilization
      if (!health.isHealthy) {
        expect(health.issues.length).toBeGreaterThan(0);
      }
    });
  });

  describe('Event Handling', () => {
    it('should emit request_allowed events', async () => {
      const allowedSpy = vi.fn();
      rateLimitManager.on('request_allowed', allowedSpy);

      const requestId = await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');

      expect(allowedSpy).toHaveBeenCalledWith({
        requestId,
        source: DataSource.MT5,
        endpoint: '/api/prices',
        timestamp: expect.any(Date),
      });
    });

    it('should emit request_throttled events when queuing disabled', async () => {
      // Create manager with queueing disabled to trigger throttling
      const noQueueConfig = {
        ...config,
        enableQueueing: false,
      };
      const noQueueManager = new ApiRateLimitManager(noQueueConfig);
      
      try {
        const throttledSpy = vi.fn();
        noQueueManager.on('request_throttled', throttledSpy);

        // Exhaust tokens
        const mt5Config = config.providers[DataSource.MT5];
        for (let i = 0; i < mt5Config.maxBurstRequests; i++) {
          await noQueueManager.requestAccess(DataSource.MT5, '/api/prices');
        }

        // This should be throttled when queuing is disabled
        try {
          await noQueueManager.requestAccess(DataSource.MT5, '/api/prices');
        } catch (error) {
          // Expected to throw
        }

        expect(throttledSpy).toHaveBeenCalledWith(
          expect.objectContaining({
            source: DataSource.MT5,
            nextAvailableIn: expect.any(Number),
          })
        );
      } finally {
        noQueueManager.shutdown();
      }
    });

    it('should emit request_queued events when tokens exhausted', async () => {
      const queuedSpy = vi.fn();
      rateLimitManager.on('request_queued', queuedSpy);

      // Exhaust tokens
      const mt5Config = config.providers[DataSource.MT5];
      for (let i = 0; i < mt5Config.maxBurstRequests; i++) {
        await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      }

      // This should be queued since queueing is enabled by default
      const queuedPromise = rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');

      expect(queuedSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          source: DataSource.MT5,
          queuePosition: expect.any(Number),
          priority: RequestPriority.MEDIUM,
        })
      );

      // Clean up the queued request
      await new Promise(resolve => setTimeout(resolve, 200));
      const requestId = await queuedPromise;
      rateLimitManager.releaseRequest(requestId);
    });

    it('should emit request_completed events', async () => {
      const completedSpy = vi.fn();
      rateLimitManager.on('request_completed', completedSpy);

      const requestId = await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      rateLimitManager.releaseRequest(requestId);

      expect(completedSpy).toHaveBeenCalledWith({
        requestId,
        timestamp: expect.any(Date),
      });
    });

    it('should emit monitoring_update events', async () => {
      const monitoringSpy = vi.fn();
      rateLimitManager.on('monitoring_update', monitoringSpy);

      // Wait for monitoring interval
      await new Promise(resolve => setTimeout(resolve, 1100));

      expect(monitoringSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          totalActiveRequests: expect.any(Number),
          totalQueuedRequests: expect.any(Number),
          overallUtilization: expect.any(Number),
          providerStats: expect.any(Object),
          alerts: expect.any(Array),
          lastUpdated: expect.any(Date),
        })
      );
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid provider gracefully', async () => {
      await expect(
        rateLimitManager.requestAccess('INVALID_PROVIDER' as DataSource, '/api/prices')
      ).rejects.toThrow('Provider INVALID_PROVIDER not configured');
    });

    it('should handle queue status for invalid provider', () => {
      expect(() => {
        rateLimitManager.getQueueStatus('INVALID_PROVIDER' as DataSource);
      }).toThrow('Provider INVALID_PROVIDER not found');
    });

    it('should handle stats for invalid provider', () => {
      const stats = rateLimitManager.getProviderStats('INVALID_PROVIDER' as DataSource);
      expect(stats).toBeNull();
    });
  });

  describe('Memory Management', () => {
    it('should handle multiple providers efficiently', async () => {
      // Test all providers
      await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      await rateLimitManager.requestAccess(DataSource.ALPHA_VANTAGE, '/api/prices');
      await rateLimitManager.requestAccess(DataSource.YAHOO_FINANCE, '/api/prices');

      const systemStats = rateLimitManager.getSystemStats();
      
      expect(systemStats.providerStats[DataSource.MT5].totalRequests).toBe(1);
      expect(systemStats.providerStats[DataSource.ALPHA_VANTAGE].totalRequests).toBe(1);
      expect(systemStats.providerStats[DataSource.YAHOO_FINANCE].totalRequests).toBe(1);
    });
  });

  describe('Shutdown', () => {
    it('should shutdown gracefully', () => {
      const shutdownSpy = vi.fn();
      rateLimitManager.on('shutdown', shutdownSpy);

      expect(() => rateLimitManager.shutdown()).not.toThrow();
      expect(shutdownSpy).toHaveBeenCalled();
    });

    it('should cleanup all resources on shutdown', async () => {
      await rateLimitManager.requestAccess(DataSource.MT5, '/api/prices');
      
      rateLimitManager.shutdown();
      
      // Should not have any listeners after shutdown
      expect(rateLimitManager.listenerCount('request_allowed')).toBe(0);
    });
  });
});