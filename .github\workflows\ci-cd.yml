name: MT5 Bridge CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'apps/mt5-bridge/**'
      - '.github/workflows/ci-cd.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'apps/mt5-bridge/**'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}/mt5-bridge

jobs:
  # =============================================================================
  # Code Quality and Security
  # =============================================================================
  
  code-quality:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.6.1
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Load cached dependencies
        uses: actions/cache@v3
        with:
          path: .venv
          key: venv-${{ runner.os }}-${{ hashFiles('**/poetry.lock') }}

      - name: Install dependencies
        run: |
          cd apps/mt5-bridge
          poetry install --with=dev,test

      - name: Run black formatting check
        run: |
          cd apps/mt5-bridge
          poetry run black --check python/

      - name: Run isort import sorting check
        run: |
          cd apps/mt5-bridge
          poetry run isort --check-only python/

      - name: Run flake8 linting
        run: |
          cd apps/mt5-bridge
          poetry run flake8 python/

      - name: Run mypy type checking
        run: |
          cd apps/mt5-bridge
          poetry run mypy python/
        continue-on-error: true # Type checking warnings shouldn't fail CI

      - name: Run security scan with bandit
        run: |
          cd apps/mt5-bridge
          poetry run bandit -r python/ -f json -o security-report.json
        continue-on-error: true

      - name: Upload security report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: security-report
          path: apps/mt5-bridge/security-report.json

  # =============================================================================
  # Testing
  # =============================================================================
  
  test:
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: timescale/timescaledb:latest-pg15
        env:
          POSTGRES_DB: mt5_bridge_test
          POSTGRES_USER: mt5user
          POSTGRES_PASSWORD: mt5password
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7.2-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.6.1
          virtualenvs-create: true
          virtualenvs-in-project: true

      - name: Load cached dependencies
        uses: actions/cache@v3
        with:
          path: .venv
          key: venv-${{ runner.os }}-${{ hashFiles('**/poetry.lock') }}

      - name: Install dependencies
        run: |
          cd apps/mt5-bridge
          poetry install --with=dev,test

      - name: Run tests with coverage
        env:
          DATABASE_URL: postgresql://mt5user:mt5password@localhost:5432/mt5_bridge_test
          REDIS_URL: redis://localhost:6379/1
          ENVIRONMENT: testing
          MT5_SERVER: demo-server
          MT5_LOGIN: "123456"
          MT5_PASSWORD: "test-password"
        run: |
          cd apps/mt5-bridge
          poetry run pytest \
            --cov=python \
            --cov-report=html \
            --cov-report=xml \
            --cov-report=term \
            --junitxml=test-results.xml \
            python/tests/

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results
          path: |
            apps/mt5-bridge/test-results.xml
            apps/mt5-bridge/htmlcov/
            apps/mt5-bridge/coverage.xml

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: apps/mt5-bridge/coverage.xml
          flags: mt5-bridge
          name: MT5 Bridge Coverage

  # =============================================================================
  # Performance Testing
  # =============================================================================
  
  performance-test:
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.6.1

      - name: Install dependencies
        run: |
          cd apps/mt5-bridge
          poetry install --with=dev,test

      - name: Run performance tests
        run: |
          cd apps/mt5-bridge
          poetry run python python/optimize_performance.py > performance-results.json

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: apps/mt5-bridge/performance-results.json

  # =============================================================================
  # Build and Push Container Images
  # =============================================================================
  
  build-and-push:
    runs-on: ubuntu-latest
    needs: [code-quality, test]
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: apps/mt5-bridge/Dockerfile
          target: production
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BUILD_VERSION=${{ github.sha }}
            BUILD_DATE=${{ github.event.head_commit.timestamp }}

  # =============================================================================
  # Security Scanning
  # =============================================================================
  
  security-scan:
    runs-on: ubuntu-latest
    needs: build-and-push
    permissions:
      contents: read
      security-events: write

    steps:
      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }}
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

  # =============================================================================
  # Deploy to Development
  # =============================================================================
  
  deploy-dev:
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.event_name == 'push' && github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBECONFIG_DEV }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Deploy to development
        run: |
          envsubst < k8s/development.yaml | kubectl apply -f -
        env:
          IMAGE_TAG: ${{ github.sha }}
          NAMESPACE: mt5-bridge-dev
          DATABASE_URL: ${{ secrets.DEV_DATABASE_URL }}
          REDIS_URL: ${{ secrets.DEV_REDIS_URL }}
          MT5_SERVER: ${{ secrets.DEV_MT5_SERVER }}
          MT5_LOGIN: ${{ secrets.DEV_MT5_LOGIN }}
          MT5_PASSWORD: ${{ secrets.DEV_MT5_PASSWORD }}

      - name: Wait for deployment
        run: |
          kubectl rollout status deployment/mt5-bridge -n mt5-bridge-dev --timeout=300s

      - name: Run smoke tests
        run: |
          kubectl run smoke-test --rm -i --restart=Never \
            --image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} \
            --namespace=mt5-bridge-dev \
            -- python -c "import requests; requests.get('http://mt5-bridge:8000/health')"

  # =============================================================================
  # Deploy to Production
  # =============================================================================
  
  deploy-prod:
    runs-on: ubuntu-latest
    needs: [build-and-push, security-scan, performance-test]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBECONFIG_PROD }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Deploy to production
        run: |
          envsubst < k8s/production.yaml | kubectl apply -f -
        env:
          IMAGE_TAG: ${{ github.sha }}
          NAMESPACE: mt5-bridge-prod
          DATABASE_URL: ${{ secrets.PROD_DATABASE_URL }}
          REDIS_URL: ${{ secrets.PROD_REDIS_URL }}
          MT5_SERVER: ${{ secrets.PROD_MT5_SERVER }}
          MT5_LOGIN: ${{ secrets.PROD_MT5_LOGIN }}
          MT5_PASSWORD: ${{ secrets.PROD_MT5_PASSWORD }}

      - name: Wait for deployment
        run: |
          kubectl rollout status deployment/mt5-bridge -n mt5-bridge-prod --timeout=600s

      - name: Run production health checks
        run: |
          kubectl run health-check --rm -i --restart=Never \
            --image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} \
            --namespace=mt5-bridge-prod \
            -- python -c "import requests; r=requests.get('http://mt5-bridge:8000/health'); assert r.status_code == 200"

      - name: Send deployment notification
        uses: 8398a7/action-slack@v3
        if: always()
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          text: |
            MT5 Bridge deployment to production: ${{ job.status }}
            Version: ${{ github.sha }}
            Actor: ${{ github.actor }}

  # =============================================================================
  # Database Migration
  # =============================================================================
  
  migrate-database:
    runs-on: ubuntu-latest
    needs: deploy-prod
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    environment: production

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBECONFIG_PROD }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Run database migrations
        run: |
          kubectl create job migration-${{ github.sha }} \
            --from=cronjob/mt5-bridge-migrations \
            --namespace=mt5-bridge-prod

      - name: Wait for migration completion
        run: |
          kubectl wait --for=condition=complete job/migration-${{ github.sha }} \
            --namespace=mt5-bridge-prod \
            --timeout=300s

  # =============================================================================
  # Rollback (Manual Trigger)
  # =============================================================================
  
  rollback:
    runs-on: ubuntu-latest
    if: github.event_name == 'workflow_dispatch'
    environment: production

    steps:
      - name: Setup kubectl
        uses: azure/setup-kubectl@v3
        with:
          version: 'v1.28.0'

      - name: Configure kubectl
        run: |
          echo "${{ secrets.KUBECONFIG_PROD }}" | base64 -d > kubeconfig
          export KUBECONFIG=kubeconfig

      - name: Rollback deployment
        run: |
          kubectl rollout undo deployment/mt5-bridge -n mt5-bridge-prod

      - name: Wait for rollback
        run: |
          kubectl rollout status deployment/mt5-bridge -n mt5-bridge-prod --timeout=300s

      - name: Verify rollback
        run: |
          kubectl get deployment/mt5-bridge -n mt5-bridge-prod -o jsonpath='{.metadata.annotations.deployment\.kubernetes\.io/revision}'