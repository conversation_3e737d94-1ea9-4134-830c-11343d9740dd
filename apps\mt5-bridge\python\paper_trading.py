"""
Paper Trading Execution Engine
Simulates trade execution without real money for proof-of-concept validation
"""

import asyncio
import uuid
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import json
from loguru import logger

from config import get_config
from mt5_connection import get_mt5_connection
from database import get_db_manager

class OrderType(Enum):
    BUY = "BUY"
    SELL = "SELL"
    BUY_LIMIT = "BUY_LIMIT"
    SELL_LIMIT = "SELL_LIMIT"
    BUY_STOP = "BUY_STOP"
    SELL_STOP = "SELL_STOP"

class OrderStatus(Enum):
    PENDING = "PENDING"
    FILLED = "FILLED"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"

class PositionStatus(Enum):
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PARTIAL = "PARTIAL"

@dataclass
class TradeRequest:
    """Trade request for paper trading"""
    instrument: str
    order_type: OrderType
    volume: float  # Lot size
    price: Optional[float] = None  # For limit/stop orders
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    comment: str = ""
    magic: int = 12345  # Magic number for identification
    slippage: int = 3  # Points of acceptable slippage
    expiration: Optional[datetime] = None

@dataclass
class Order:
    """Paper trading order"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    request: TradeRequest = None
    status: OrderStatus = OrderStatus.PENDING
    creation_time: datetime = field(default_factory=datetime.now)
    fill_time: Optional[datetime] = None
    fill_price: Optional[float] = None
    filled_volume: float = 0.0
    remaining_volume: float = 0.0
    commission: float = 0.0
    swap: float = 0.0
    profit: float = 0.0
    rejection_reason: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Position:
    """Paper trading position"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    instrument: str = ""
    volume: float = 0.0
    entry_price: float = 0.0
    current_price: float = 0.0
    status: PositionStatus = PositionStatus.OPEN
    open_time: datetime = field(default_factory=datetime.now)
    close_time: Optional[datetime] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    commission: float = 0.0
    swap: float = 0.0
    orders: List[str] = field(default_factory=list)  # Order IDs
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class AccountInfo:
    """Paper trading account information"""
    balance: float = 10000.0  # Starting balance
    equity: float = 10000.0
    margin: float = 0.0
    free_margin: float = 10000.0
    margin_level: float = 0.0
    profit: float = 0.0
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0

class PaperTradingEngine:
    """
    Paper trading simulation engine for MT5 integration testing
    """
    
    def __init__(self):
        self.config = get_config()
        self.mt5_connection = get_mt5_connection()
        self.db_manager = get_db_manager()
        
        # Trading state
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.account = AccountInfo()
        self.is_running = False
        
        # Market data cache
        self.current_prices: Dict[str, Dict[str, float]] = {}  # instrument -> {bid, ask}
        self.last_price_update: Dict[str, datetime] = {}
        
        # Trading settings
        self.trading_config = {
            'max_positions': 10,
            'max_volume_per_trade': 1.0,
            'max_total_volume': 5.0,
            'commission_per_lot': 7.0,  # USD per lot
            'margin_requirement': 0.01,  # 1% margin
            'spread_points': 1.5,  # Average spread in points
            'slippage_probability': 0.1,  # 10% chance of slippage
            'max_slippage_points': 3
        }
        
        # Statistics
        self.stats = {
            'orders_processed': 0,
            'orders_filled': 0,
            'orders_rejected': 0,
            'positions_opened': 0,
            'positions_closed': 0,
            'total_volume_traded': 0.0,
            'total_commission_paid': 0.0,
            'start_time': None
        }
        
    async def start(self):
        """Start the paper trading engine"""
        if self.is_running:
            logger.warning("Paper trading engine already running")
            return
            
        logger.info("🚀 Starting paper trading engine...")
        self.is_running = True
        self.stats['start_time'] = datetime.now()
        
        # Start background tasks
        asyncio.create_task(self._price_monitor())
        asyncio.create_task(self._position_monitor())
        asyncio.create_task(self._order_monitor())
        
        logger.info("✅ Paper trading engine started")
        
    async def stop(self):
        """Stop the paper trading engine"""
        if not self.is_running:
            return
            
        logger.info("🔌 Stopping paper trading engine...")
        self.is_running = False
        
        # Close all open positions
        await self._close_all_positions("Engine shutdown")
        
        logger.info("✅ Paper trading engine stopped")
        
    async def place_order(self, request: TradeRequest) -> Dict[str, Any]:
        """Place a paper trading order"""
        
        try:
            # Validate request
            validation_result = await self._validate_order_request(request)
            if not validation_result['valid']:
                return {
                    'success': False,
                    'error': validation_result['error'],
                    'order_id': None
                }
            
            # Create order
            order = Order(
                request=request,
                remaining_volume=request.volume,
                metadata={
                    'creation_price': self.current_prices.get(request.instrument, {}),
                    'account_balance': self.account.balance,
                    'validation': validation_result
                }
            )
            
            self.orders[order.id] = order
            self.stats['orders_processed'] += 1
            
            logger.info(f"📋 Created paper order {order.id}: {request.order_type.value} {request.volume} {request.instrument}")
            
            # Try to fill immediately for market orders
            if request.order_type in [OrderType.BUY, OrderType.SELL]:
                await self._try_fill_order(order.id)
            
            # Store order in database
            await self._store_order(order)
            
            return {
                'success': True,
                'order_id': order.id,
                'status': order.status.value,
                'message': f"Order placed successfully"
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to place order: {e}")
            return {
                'success': False,
                'error': str(e),
                'order_id': None
            }
    
    async def close_position(self, position_id: str, volume: Optional[float] = None) -> Dict[str, Any]:
        """Close a position (full or partial)"""
        
        try:
            if position_id not in self.positions:
                return {
                    'success': False,
                    'error': f"Position {position_id} not found"
                }
            
            position = self.positions[position_id]
            
            if position.status != PositionStatus.OPEN:
                return {
                    'success': False,
                    'error': f"Position {position_id} is not open"
                }
            
            close_volume = volume or position.volume
            
            if close_volume > position.volume:
                return {
                    'success': False,
                    'error': f"Close volume {close_volume} exceeds position volume {position.volume}"
                }
            
            # Get current price
            current_prices = self.current_prices.get(position.instrument, {})
            if not current_prices:
                return {
                    'success': False,
                    'error': f"No current price available for {position.instrument}"
                }
            
            # Determine close price (opposite of entry)
            close_price = current_prices.get('bid', 0) if position.volume > 0 else current_prices.get('ask', 0)
            
            # Calculate P&L
            pnl = self._calculate_pnl(position, close_price, close_volume)
            
            # Update position
            position.volume -= close_volume
            position.realized_pnl += pnl
            
            if position.volume == 0:
                position.status = PositionStatus.CLOSED
                position.close_time = datetime.now()
                self.stats['positions_closed'] += 1
            else:
                position.status = PositionStatus.PARTIAL
            
            # Update account
            self.account.balance += pnl
            self.account.profit += pnl
            
            if pnl > 0:
                self.account.winning_trades += 1
            else:
                self.account.losing_trades += 1
            
            # Store position update
            await self._store_position(position)
            
            logger.info(f"💰 Closed {close_volume} of position {position_id}: P&L = {pnl:.2f}")
            
            return {
                'success': True,
                'position_id': position_id,
                'closed_volume': close_volume,
                'close_price': close_price,
                'pnl': pnl,
                'remaining_volume': position.volume,
                'status': position.status.value
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to close position: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def cancel_order(self, order_id: str) -> Dict[str, Any]:
        """Cancel a pending order"""
        
        try:
            if order_id not in self.orders:
                return {
                    'success': False,
                    'error': f"Order {order_id} not found"
                }
            
            order = self.orders[order_id]
            
            if order.status != OrderStatus.PENDING:
                return {
                    'success': False,
                    'error': f"Order {order_id} cannot be cancelled (status: {order.status.value})"
                }
            
            order.status = OrderStatus.CANCELLED
            
            # Store order update
            await self._store_order(order)
            
            logger.info(f"❌ Cancelled order {order_id}")
            
            return {
                'success': True,
                'order_id': order_id,
                'status': order.status.value
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to cancel order: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    async def get_account_info(self) -> Dict[str, Any]:
        """Get current account information"""
        
        # Update equity with unrealized P&L
        total_unrealized_pnl = sum(
            pos.unrealized_pnl for pos in self.positions.values() 
            if pos.status == PositionStatus.OPEN
        )
        
        self.account.equity = self.account.balance + total_unrealized_pnl
        
        return {
            'balance': self.account.balance,
            'equity': self.account.equity,
            'margin': self.account.margin,
            'free_margin': self.account.free_margin,
            'margin_level': self.account.margin_level,
            'profit': self.account.profit,
            'total_trades': self.account.total_trades,
            'winning_trades': self.account.winning_trades,
            'losing_trades': self.account.losing_trades,
            'win_rate': self.account.winning_trades / max(self.account.total_trades, 1),
            'open_positions': len([p for p in self.positions.values() if p.status == PositionStatus.OPEN]),
            'pending_orders': len([o for o in self.orders.values() if o.status == OrderStatus.PENDING])
        }
    
    async def get_positions(self) -> List[Dict[str, Any]]:
        """Get all positions"""
        
        positions = []
        for position in self.positions.values():
            # Update unrealized P&L
            if position.status == PositionStatus.OPEN:
                current_prices = self.current_prices.get(position.instrument, {})
                if current_prices:
                    current_price = current_prices.get('bid', 0) if position.volume > 0 else current_prices.get('ask', 0)
                    position.current_price = current_price
                    position.unrealized_pnl = self._calculate_pnl(position, current_price, position.volume)
            
            positions.append({
                'id': position.id,
                'instrument': position.instrument,
                'volume': position.volume,
                'entry_price': position.entry_price,
                'current_price': position.current_price,
                'status': position.status.value,
                'open_time': position.open_time.isoformat(),
                'close_time': position.close_time.isoformat() if position.close_time else None,
                'stop_loss': position.stop_loss,
                'take_profit': position.take_profit,
                'unrealized_pnl': position.unrealized_pnl,
                'realized_pnl': position.realized_pnl,
                'commission': position.commission,
                'swap': position.swap
            })
        
        return positions
    
    async def get_orders(self) -> List[Dict[str, Any]]:
        """Get all orders"""
        
        orders = []
        for order in self.orders.values():
            orders.append({
                'id': order.id,
                'instrument': order.request.instrument,
                'order_type': order.request.order_type.value,
                'volume': order.request.volume,
                'price': order.request.price,
                'status': order.status.value,
                'creation_time': order.creation_time.isoformat(),
                'fill_time': order.fill_time.isoformat() if order.fill_time else None,
                'fill_price': order.fill_price,
                'filled_volume': order.filled_volume,
                'remaining_volume': order.remaining_volume,
                'stop_loss': order.request.stop_loss,
                'take_profit': order.request.take_profit,
                'commission': order.commission,
                'rejection_reason': order.rejection_reason
            })
        
        return orders
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get trading statistics"""
        
        uptime = None
        if self.stats['start_time']:
            uptime = (datetime.now() - self.stats['start_time']).total_seconds()
        
        return {
            **self.stats,
            'is_running': self.is_running,
            'uptime_seconds': uptime,
            'total_positions': len(self.positions),
            'total_orders': len(self.orders),
            'open_positions': len([p for p in self.positions.values() if p.status == PositionStatus.OPEN]),
            'pending_orders': len([o for o in self.orders.values() if o.status == OrderStatus.PENDING]),
            'fill_rate': self.stats['orders_filled'] / max(self.stats['orders_processed'], 1),
            'rejection_rate': self.stats['orders_rejected'] / max(self.stats['orders_processed'], 1)
        }
    
    async def _validate_order_request(self, request: TradeRequest) -> Dict[str, Any]:
        """Validate order request"""
        
        # Check if MT5 is connected
        if not await self.mt5_connection.ensure_connection():
            return {
                'valid': False,
                'error': 'MT5 connection not available'
            }
        
        # Check instrument validity
        if not request.instrument:
            return {
                'valid': False,
                'error': 'Instrument is required'
            }
        
        # Check volume limits
        if request.volume <= 0:
            return {
                'valid': False,
                'error': 'Volume must be positive'
            }
        
        if request.volume > self.trading_config['max_volume_per_trade']:
            return {
                'valid': False,
                'error': f"Volume exceeds maximum per trade ({self.trading_config['max_volume_per_trade']})"
            }
        
        # Check total position limits
        open_positions = len([p for p in self.positions.values() if p.status == PositionStatus.OPEN])
        if open_positions >= self.trading_config['max_positions']:
            return {
                'valid': False,
                'error': f"Maximum positions limit reached ({self.trading_config['max_positions']})"
            }
        
        # Check total volume limits
        total_volume = sum(
            abs(p.volume) for p in self.positions.values() 
            if p.status == PositionStatus.OPEN
        )
        if total_volume + request.volume > self.trading_config['max_total_volume']:
            return {
                'valid': False,
                'error': f"Total volume limit exceeded ({self.trading_config['max_total_volume']})"
            }
        
        # Check margin requirements
        required_margin = request.volume * self.trading_config['margin_requirement'] * 100000  # Assuming forex
        if required_margin > self.account.free_margin:
            return {
                'valid': False,
                'error': f"Insufficient margin (required: {required_margin:.2f}, available: {self.account.free_margin:.2f})"
            }
        
        return {
            'valid': True,
            'required_margin': required_margin
        }
    
    async def _try_fill_order(self, order_id: str) -> bool:
        """Try to fill an order"""
        
        order = self.orders.get(order_id)
        if not order or order.status != OrderStatus.PENDING:
            return False
        
        request = order.request
        
        # Get current market prices
        current_prices = self.current_prices.get(request.instrument, {})
        if not current_prices:
            logger.warning(f"⚠️ No current prices for {request.instrument}")
            return False
        
        bid = current_prices.get('bid', 0)
        ask = current_prices.get('ask', 0)
        
        # Determine fill conditions
        fill_price = None
        
        if request.order_type == OrderType.BUY:
            fill_price = ask
        elif request.order_type == OrderType.SELL:
            fill_price = bid
        elif request.order_type == OrderType.BUY_LIMIT and request.price and ask <= request.price:
            fill_price = min(ask, request.price)
        elif request.order_type == OrderType.SELL_LIMIT and request.price and bid >= request.price:
            fill_price = max(bid, request.price)
        elif request.order_type == OrderType.BUY_STOP and request.price and ask >= request.price:
            fill_price = max(ask, request.price)
        elif request.order_type == OrderType.SELL_STOP and request.price and bid <= request.price:
            fill_price = min(bid, request.price)
        
        if fill_price is None:
            return False
        
        # Apply slippage simulation
        import random
        if random.random() < self.trading_config['slippage_probability']:
            slippage_points = random.uniform(-self.trading_config['max_slippage_points'], 
                                           self.trading_config['max_slippage_points'])
            point_value = 0.0001  # For most forex pairs
            if request.order_type in [OrderType.BUY, OrderType.BUY_LIMIT, OrderType.BUY_STOP]:
                fill_price += slippage_points * point_value
            else:
                fill_price -= slippage_points * point_value
        
        # Fill the order
        order.status = OrderStatus.FILLED
        order.fill_time = datetime.now()
        order.fill_price = fill_price
        order.filled_volume = order.remaining_volume
        order.remaining_volume = 0.0
        order.commission = request.volume * self.trading_config['commission_per_lot']
        
        # Create position
        await self._create_position_from_order(order)
        
        # Update statistics
        self.stats['orders_filled'] += 1
        self.stats['total_volume_traded'] += order.filled_volume
        self.stats['total_commission_paid'] += order.commission
        
        # Update account
        self.account.balance -= order.commission
        self.account.total_trades += 1
        
        logger.info(f"✅ Filled order {order_id}: {order.filled_volume} @ {fill_price:.5f}")
        
        return True
    
    async def _create_position_from_order(self, order: Order):
        """Create a position from a filled order"""
        
        request = order.request
        
        # Determine position volume (positive for buy, negative for sell)
        position_volume = order.filled_volume
        if request.order_type in [OrderType.SELL, OrderType.SELL_LIMIT, OrderType.SELL_STOP]:
            position_volume = -position_volume
        
        position = Position(
            instrument=request.instrument,
            volume=position_volume,
            entry_price=order.fill_price,
            current_price=order.fill_price,
            stop_loss=request.stop_loss,
            take_profit=request.take_profit,
            commission=order.commission,
            orders=[order.id],
            metadata={
                'opening_order': order.id,
                'magic': request.magic,
                'comment': request.comment
            }
        )
        
        self.positions[position.id] = position
        self.stats['positions_opened'] += 1
        
        # Store position
        await self._store_position(position)
        
        logger.info(f"📊 Created position {position.id}: {position_volume} {request.instrument} @ {order.fill_price:.5f}")
    
    def _calculate_pnl(self, position: Position, current_price: float, volume: float) -> float:
        """Calculate profit/loss for a position"""
        
        if position.volume == 0:
            return 0.0
        
        # Calculate price difference
        price_diff = current_price - position.entry_price
        
        # Apply position direction (negative volume means short position)
        if position.volume < 0:
            price_diff = -price_diff
        
        # Calculate P&L (assuming forex with 100,000 base units per lot)
        pnl = price_diff * volume * 100000
        
        return pnl
    
    async def _price_monitor(self):
        """Monitor price updates and update positions"""
        
        while self.is_running:
            try:
                # Update prices from MT5 (simulation)
                instruments = set()
                for position in self.positions.values():
                    if position.status == PositionStatus.OPEN:
                        instruments.add(position.instrument)
                
                for order in self.orders.values():
                    if order.status == OrderStatus.PENDING:
                        instruments.add(order.request.instrument)
                
                # Simulate price updates
                for instrument in instruments:
                    await self._update_instrument_price(instrument)
                
                await asyncio.sleep(1)  # Update every second
                
            except Exception as e:
                logger.error(f"❌ Price monitor error: {e}")
                await asyncio.sleep(5)
    
    async def _position_monitor(self):
        """Monitor positions for stop loss/take profit"""
        
        while self.is_running:
            try:
                for position in list(self.positions.values()):
                    if position.status != PositionStatus.OPEN:
                        continue
                    
                    current_prices = self.current_prices.get(position.instrument, {})
                    if not current_prices:
                        continue
                    
                    current_price = current_prices.get('bid', 0) if position.volume > 0 else current_prices.get('ask', 0)
                    
                    # Check stop loss
                    if position.stop_loss:
                        triggered = False
                        if position.volume > 0 and current_price <= position.stop_loss:
                            triggered = True
                        elif position.volume < 0 and current_price >= position.stop_loss:
                            triggered = True
                        
                        if triggered:
                            await self.close_position(position.id)
                            logger.info(f"🛑 Stop loss triggered for position {position.id}")
                            continue
                    
                    # Check take profit
                    if position.take_profit:
                        triggered = False
                        if position.volume > 0 and current_price >= position.take_profit:
                            triggered = True
                        elif position.volume < 0 and current_price <= position.take_profit:
                            triggered = True
                        
                        if triggered:
                            await self.close_position(position.id)
                            logger.info(f"💰 Take profit triggered for position {position.id}")
                            continue
                
                await asyncio.sleep(1)  # Check every second
                
            except Exception as e:
                logger.error(f"❌ Position monitor error: {e}")
                await asyncio.sleep(5)
    
    async def _order_monitor(self):
        """Monitor pending orders for fill conditions"""
        
        while self.is_running:
            try:
                for order_id, order in list(self.orders.items()):
                    if order.status == OrderStatus.PENDING:
                        await self._try_fill_order(order_id)
                
                await asyncio.sleep(0.1)  # Check every 100ms
                
            except Exception as e:
                logger.error(f"❌ Order monitor error: {e}")
                await asyncio.sleep(1)
    
    async def _update_instrument_price(self, instrument: str):
        """Update price for an instrument (simulation)"""
        
        try:
            # In a real implementation, this would get prices from MT5
            # For simulation, we'll use basic price movement
            import random
            
            if instrument not in self.current_prices:
                # Initialize with random price
                base_price = random.uniform(1.0, 2.0)
                spread = 0.00015  # 1.5 pips
                self.current_prices[instrument] = {
                    'bid': base_price - spread/2,
                    'ask': base_price + spread/2
                }
            else:
                # Random walk
                current = self.current_prices[instrument]
                mid_price = (current['bid'] + current['ask']) / 2
                
                # Random price movement (±0.01% per update)
                change_percent = random.uniform(-0.0001, 0.0001)
                new_mid = mid_price * (1 + change_percent)
                
                spread = 0.00015  # 1.5 pips
                self.current_prices[instrument] = {
                    'bid': new_mid - spread/2,
                    'ask': new_mid + spread/2
                }
            
            self.last_price_update[instrument] = datetime.now()
            
        except Exception as e:
            logger.error(f"❌ Failed to update price for {instrument}: {e}")
    
    async def _close_all_positions(self, reason: str = "Manual close"):
        """Close all open positions"""
        
        for position in list(self.positions.values()):
            if position.status == PositionStatus.OPEN:
                await self.close_position(position.id)
                logger.info(f"🔒 Closed position {position.id}: {reason}")
    
    async def _store_order(self, order: Order):
        """Store order in database"""
        
        try:
            query = """
            INSERT INTO paper_trading_orders 
            (id, instrument, order_type, volume, price, stop_loss, take_profit, 
             status, creation_time, fill_time, fill_price, filled_volume, 
             commission, rejection_reason, metadata)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
            ON CONFLICT (id) DO UPDATE SET
                status = EXCLUDED.status,
                fill_time = EXCLUDED.fill_time,
                fill_price = EXCLUDED.fill_price,
                filled_volume = EXCLUDED.filled_volume,
                commission = EXCLUDED.commission,
                rejection_reason = EXCLUDED.rejection_reason,
                metadata = EXCLUDED.metadata
            """
            
            async with self.db_manager.connection_pool.acquire() as conn:
                await conn.execute(
                    query,
                    order.id,
                    order.request.instrument,
                    order.request.order_type.value,
                    order.request.volume,
                    order.request.price,
                    order.request.stop_loss,
                    order.request.take_profit,
                    order.status.value,
                    order.creation_time,
                    order.fill_time,
                    order.fill_price,
                    order.filled_volume,
                    order.commission,
                    order.rejection_reason,
                    order.metadata
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store order: {e}")
    
    async def _store_position(self, position: Position):
        """Store position in database"""
        
        try:
            query = """
            INSERT INTO paper_trading_positions 
            (id, instrument, volume, entry_price, current_price, status, 
             open_time, close_time, stop_loss, take_profit, unrealized_pnl, 
             realized_pnl, commission, swap, metadata)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
            ON CONFLICT (id) DO UPDATE SET
                volume = EXCLUDED.volume,
                current_price = EXCLUDED.current_price,
                status = EXCLUDED.status,
                close_time = EXCLUDED.close_time,
                unrealized_pnl = EXCLUDED.unrealized_pnl,
                realized_pnl = EXCLUDED.realized_pnl,
                commission = EXCLUDED.commission,
                swap = EXCLUDED.swap,
                metadata = EXCLUDED.metadata
            """
            
            async with self.db_manager.connection_pool.acquire() as conn:
                await conn.execute(
                    query,
                    position.id,
                    position.instrument,
                    position.volume,
                    position.entry_price,
                    position.current_price,
                    position.status.value,
                    position.open_time,
                    position.close_time,
                    position.stop_loss,
                    position.take_profit,
                    position.unrealized_pnl,
                    position.realized_pnl,
                    position.commission,
                    position.swap,
                    position.metadata
                )
                
        except Exception as e:
            logger.error(f"❌ Failed to store position: {e}")

# Global instance
_paper_trading_engine: Optional[PaperTradingEngine] = None

def get_paper_trading_engine() -> PaperTradingEngine:
    """Get global paper trading engine instance"""
    global _paper_trading_engine
    if _paper_trading_engine is None:
        _paper_trading_engine = PaperTradingEngine()
    return _paper_trading_engine