import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Decimal } from 'decimal.js';
import { RiskManager } from '../../RiskManager';
import { LossLimitEnforcer } from '../../LossLimitEnforcer';
import { PositionSizer } from '../../PositionSizer';
import { Position, MarketConditions, StressTestScenario } from '@golddaddy/types';

describe('Market Crash Simulation Stress Tests', () => {
  let riskManager: RiskManager;
  let lossLimitEnforcer: LossLimitEnforcer;
  let positionSizer: PositionSizer;
  
  const mockUserId = '123e4567-e89b-12d3-a456-426614174000';

  beforeEach(() => {
    riskManager = new RiskManager();
    lossLimitEnforcer = new LossLimitEnforcer();
    positionSizer = new PositionSizer();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Black Swan Events', () => {
    it('should handle 2008 Financial Crisis scenario', async () => {
      // Simulate conditions similar to 2008 financial crisis
      const crisis2008Scenario: StressTestScenario = {
        name: '2008 Financial Crisis',
        description: 'Global financial meltdown with massive USD strengthening',
        duration: '6 months',
        marketMovements: {
          'EURUSD': -0.25, // EUR crashes 25% against USD
          'GBPUSD': -0.30, // GBP crashes 30% against USD  
          'AUDUSD': -0.35, // AUD crashes 35% against USD
          'USDJPY': +0.15, // JPY weakens 15% (flight to USD)
          'USDCHF': +0.20, // CHF weakens 20% initially
          'GOLD': +0.40,   // Gold surges 40%
          'OIL': -0.60     // Oil crashes 60%
        },
        volatilityMultiplier: 4.5,
        liquidityReduction: 0.70, // 70% reduction in liquidity
        correlationBreakdown: true
      };

      // Create a diversified portfolio before the crisis
      const preCrisisPositions: Position[] = [
        {
          id: 'pre-eur-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(200000),
          entryPrice: new Decimal(1.4500), // Pre-crisis high
          currentPrice: new Decimal(1.4500),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date('2008-01-01'),
          stopLoss: new Decimal(1.4200),
          takeProfit: new Decimal(1.5000)
        },
        {
          id: 'pre-gbp-1',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(150000),
          entryPrice: new Decimal(2.0000), // Pre-crisis peak
          currentPrice: new Decimal(2.0000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date('2008-01-01'),
          stopLoss: new Decimal(1.9500),
          takeProfit: new Decimal(2.0800)
        },
        {
          id: 'pre-aud-1',
          userId: mockUserId,
          symbol: 'AUDUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(0.9800), // Pre-crisis high
          currentPrice: new Decimal(0.9800),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date('2008-01-01'),
          stopLoss: new Decimal(0.9500),
          takeProfit: new Decimal(1.0200)
        }
      ];

      const preCrisisPortfolioValue = new Decimal(500000);

      // Apply crisis scenario price movements
      const postCrashPositions = preCrisisPositions.map(position => {
        const movement = crisis2008Scenario.marketMovements[position.symbol] || 0;
        const newPrice = position.entryPrice.mul(1 + movement);
        const pnl = position.size.mul(newPrice.sub(position.entryPrice));
        
        return {
          ...position,
          currentPrice: newPrice,
          unrealizedPnl: pnl
        };
      });

      // Calculate total losses
      const totalLoss = postCrashPositions.reduce(
        (sum, pos) => sum.add(pos.unrealizedPnl),
        new Decimal(0)
      );

      // Should show massive losses
      expect(totalLoss.toNumber()).toBeLessThan(-200000); // Over $200k loss

      // Calculate post-crash portfolio risk
      const crashPortfolioValue = preCrisisPortfolioValue.add(totalLoss);
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        postCrashPositions,
        crashPortfolioValue
      );

      // Risk metrics should reflect extreme conditions
      expect(portfolioRisk.riskScore).toBeGreaterThan(95);
      expect(portfolioRisk.maxDrawdown.toNumber()).toBeGreaterThan(0.40); // >40% drawdown
      expect(portfolioRisk.var95.div(crashPortfolioValue).toNumber()).toBeGreaterThan(0.15); // VaR > 15%

      // Emergency liquidation should be triggered
      const shouldLiquidate = await lossLimitEnforcer.checkEmergencyLiquidation(
        mockUserId,
        postCrashPositions,
        crashPortfolioValue
      );

      expect(shouldLiquidate).toBe(true);
    });

    it('should handle Brexit referendum shock (June 2016)', async () => {
      const brexitScenario: StressTestScenario = {
        name: 'Brexit Referendum Shock',
        description: 'GBP flash crash after unexpected Brexit vote',
        duration: '24 hours',
        marketMovements: {
          'GBPUSD': -0.12, // GBP crashes 12% overnight
          'EURGBP': +0.08, // EUR strengthens against GBP
          'GBPJPY': -0.15, // GBP/JPY massive sell-off
          'FTSE100': -0.08, // UK stocks crash 8%
        },
        volatilityMultiplier: 8.0, // Extreme intraday volatility
        liquidityReduction: 0.85, // 85% liquidity reduction during flash crash
        correlationBreakdown: true
      };

      // GBP-heavy portfolio before Brexit vote
      const preBrexitPositions: Position[] = [
        {
          id: 'brexit-gbp-1',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(500000), // Large GBP exposure
          entryPrice: new Decimal(1.4800), // Pre-referendum level
          currentPrice: new Decimal(1.4800),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date('2016-06-22'),
          stopLoss: new Decimal(1.4500),
          takeProfit: new Decimal(1.5200)
        },
        {
          id: 'brexit-gbpjpy-1',
          userId: mockUserId,
          symbol: 'GBPJPY',
          size: new Decimal(300000),
          entryPrice: new Decimal(155.00),
          currentPrice: new Decimal(155.00),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date('2016-06-22'),
          stopLoss: new Decimal(150.00),
          takeProfit: new Decimal(160.00)
        }
      ];

      // Apply Brexit shock
      const postBrexitPositions = preBrexitPositions.map(position => {
        const movement = brexitScenario.marketMovements[position.symbol] || 0;
        const newPrice = position.entryPrice.mul(1 + movement);
        const pnl = position.size.mul(newPrice.sub(position.entryPrice));
        
        return {
          ...position,
          currentPrice: newPrice,
          unrealizedPnl: pnl
        };
      });

      const portfolioValue = new Decimal(200000);
      const totalLoss = postBrexitPositions.reduce(
        (sum, pos) => sum.add(pos.unrealizedPnl),
        new Decimal(0)
      );

      // Should show significant GBP-related losses
      expect(totalLoss.toNumber()).toBeLessThan(-70000); // Major loss from GBP positions

      // Risk assessment in extremely volatile conditions
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        postBrexitPositions,
        portfolioValue.add(totalLoss)
      );

      expect(portfolioRisk.riskScore).toBeGreaterThan(90);
      expect(portfolioRisk.correlations['GBPUSD']).toBeDefined();
    });

    it('should handle Swiss Franc flash crash (January 2015)', async () => {
      const swissShockScenario: StressTestScenario = {
        name: 'Swiss National Bank EUR/CHF Floor Removal',
        description: 'CHF strengthens 20% in minutes after SNB removes floor',
        duration: '15 minutes',
        marketMovements: {
          'EURCHF': -0.20, // EUR/CHF crashes 20%
          'USDCHF': -0.15, // USD/CHF crashes 15%
          'CHFJPY': +0.18, // CHF strengthens against JPY
          'GBPCHF': -0.18, // GBP/CHF crashes
        },
        volatilityMultiplier: 15.0, // Extreme flash crash volatility
        liquidityReduction: 0.95, // 95% liquidity evaporation
        correlationBreakdown: true
      };

      // CHF carry trade positions (common before the shock)
      const preShockPositions: Position[] = [
        {
          id: 'chf-carry-1',
          userId: mockUserId,
          symbol: 'EURCHF',
          size: new Decimal(1000000), // Large carry trade position
          entryPrice: new Decimal(1.2000), // Near the SNB floor
          currentPrice: new Decimal(1.2000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date('2015-01-14'),
          stopLoss: new Decimal(1.1800), // Below the floor
          takeProfit: new Decimal(1.2300)
        },
        {
          id: 'chf-carry-2',
          userId: mockUserId,
          symbol: 'USDCHF',
          size: new Decimal(500000),
          entryPrice: new Decimal(1.0200),
          currentPrice: new Decimal(1.0200),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date('2015-01-14'),
          stopLoss: new Decimal(1.0000),
          takeProfit: new Decimal(1.0500)
        }
      ];

      // Apply Swiss shock
      const postShockPositions = preShockPositions.map(position => {
        const movement = swissShockScenario.marketMovements[position.symbol] || 0;
        const newPrice = position.entryPrice.mul(1 + movement);
        const pnl = position.size.mul(newPrice.sub(position.entryPrice));
        
        return {
          ...position,
          currentPrice: newPrice,
          unrealizedPnl: pnl
        };
      });

      const portfolioValue = new Decimal(300000);
      const totalLoss = postShockPositions.reduce(
        (sum, pos) => sum.add(pos.unrealizedPnl),
        new Decimal(0)
      );

      // Catastrophic losses from CHF shock
      expect(totalLoss.toNumber()).toBeLessThan(-250000); // Massive losses

      // Portfolio completely wiped out
      const remainingValue = portfolioValue.add(totalLoss);
      expect(remainingValue.toNumber()).toBeLessThan(50000);

      // Risk score should be at maximum
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        postShockPositions,
        remainingValue
      );

      expect(portfolioRisk.riskScore).toBe(100); // Maximum risk
    });
  });

  describe('High-Frequency Market Stress', () => {
    it('should handle rapid sequential price gaps', async () => {
      const positions: Position[] = [
        {
          id: 'hf-test-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        }
      ];

      const portfolioValue = new Decimal(100000);
      
      // Simulate 100 rapid price updates with gaps
      const priceUpdates = Array.from({ length: 100 }, (_, i) => {
        const basePrice = 1.2000;
        const volatility = 0.02; // 2% volatility
        const randomMove = (Math.random() - 0.5) * volatility;
        const gapProbability = 0.1; // 10% chance of price gap
        const hasGap = Math.random() < gapProbability;
        const gapSize = hasGap ? (Math.random() - 0.5) * 0.01 : 0; // 1% max gap
        
        return new Decimal(basePrice + randomMove + gapSize);
      });

      const startTime = Date.now();
      
      // Process all price updates rapidly
      for (let i = 0; i < priceUpdates.length; i++) {
        const updatedPositions = positions.map(pos => ({
          ...pos,
          currentPrice: priceUpdates[i],
          unrealizedPnl: pos.size.mul(priceUpdates[i].sub(pos.entryPrice))
        }));

        // Calculate risk at each price update
        const portfolioRisk = await riskManager.calculatePortfolioRisk(
          updatedPositions,
          portfolioValue
        );

        // Check for limit breaches
        await lossLimitEnforcer.checkEmergencyLiquidation(
          mockUserId,
          updatedPositions,
          portfolioValue
        );
      }

      const processingTime = Date.now() - startTime;
      
      // Should handle high frequency updates efficiently (under 2 seconds)
      expect(processingTime).toBeLessThan(2000);
    });

    it('should handle simultaneous multi-pair volatility spikes', async () => {
      // Portfolio with exposure across multiple major pairs
      const multiPairPositions: Position[] = [
        {
          id: 'multi-eur',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1900),
          takeProfit: new Decimal(1.2200)
        },
        {
          id: 'multi-gbp',
          userId: mockUserId,
          symbol: 'GBPUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.3000),
          currentPrice: new Decimal(1.3000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.2850),
          takeProfit: new Decimal(1.3300)
        },
        {
          id: 'multi-jpy',
          userId: mockUserId,
          symbol: 'USDJPY',
          size: new Decimal(100000),
          entryPrice: new Decimal(150.00),
          currentPrice: new Decimal(150.00),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(148.00),
          takeProfit: new Decimal(153.00)
        },
        {
          id: 'multi-aud',
          userId: mockUserId,
          symbol: 'AUDUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(0.6500),
          currentPrice: new Decimal(0.6500),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(0.6350),
          takeProfit: new Decimal(0.6750)
        }
      ];

      // Simulate simultaneous volatility spike across all pairs
      const volatilitySpike = {
        'EURUSD': -0.03, // 3% adverse move
        'GBPUSD': -0.04, // 4% adverse move
        'USDJPY': -0.02, // 2% adverse move (JPY strengthening)
        'AUDUSD': -0.05  // 5% adverse move
      };

      const spikePositions = multiPairPositions.map(position => {
        const movement = volatilitySpike[position.symbol] || 0;
        const newPrice = position.entryPrice.mul(1 + movement);
        const pnl = position.size.mul(newPrice.sub(position.entryPrice));
        
        return {
          ...position,
          currentPrice: newPrice,
          unrealizedPnl: pnl
        };
      });

      const portfolioValue = new Decimal(150000);
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        spikePositions,
        portfolioValue
      );

      // Should reflect correlated adverse movements
      expect(portfolioRisk.riskScore).toBeGreaterThan(75);
      
      const totalLoss = spikePositions.reduce(
        (sum, pos) => sum.add(pos.unrealizedPnl),
        new Decimal(0)
      );
      
      expect(totalLoss.toNumber()).toBeLessThan(-15000); // Significant losses across all pairs
    });
  });

  describe('Liquidity Crisis Scenarios', () => {
    it('should handle complete liquidity freeze', async () => {
      const positions: Position[] = [
        {
          id: 'liquidity-test',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(500000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1800), // 200 pips underwater
          unrealizedPnl: new Decimal(-100000),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1750),
          takeProfit: new Decimal(1.2500)
        }
      ];

      // Simulate complete liquidity freeze conditions
      const liquidityFreeze: MarketConditions = {
        volatilityIndex: 0.95, // Maximum volatility
        liquidityScore: 0.02, // 98% liquidity reduction
        spreadWidening: 20.0, // Spreads 20x wider
        gappingProbability: 0.8, // 80% chance of price gaps
        slippageFactor: 0.15 // 15% average slippage
      };

      const portfolioValue = new Decimal(200000);

      // Risk calculations should account for liquidity constraints
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        positions,
        portfolioValue,
        liquidityFreeze
      );

      // Risk score should be elevated due to liquidity constraints
      expect(portfolioRisk.riskScore).toBeGreaterThan(85);
      expect(portfolioRisk.liquidityRisk).toBeGreaterThan(0.8);

      // Position sizing should be severely constrained
      const recommendedSize = await positionSizer.calculatePositionSize(
        mockUserId,
        'EURUSD',
        new Decimal(1.2000),
        new Decimal(1.1900),
        new Decimal(0.02), // 2% risk tolerance
        portfolioValue,
        liquidityFreeze
      );

      // Should recommend much smaller positions during liquidity crisis
      expect(recommendedSize.toNumber()).toBeLessThan(50000);
    });

    it('should handle weekend gap risk', async () => {
      // Friday close positions
      const fridayPositions: Position[] = [
        {
          id: 'weekend-gap',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(200000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2020), // Small profit before weekend
          unrealizedPnl: new Decimal(4000),
          direction: 'buy',
          openTime: new Date('2024-01-19T20:00:00Z'), // Friday evening
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2150)
        }
      ];

      // Simulate negative weekend news causing gap down
      const mondayGapPrice = new Decimal(1.1850); // 170 pip gap down below stop loss
      
      const mondayPositions = fridayPositions.map(pos => ({
        ...pos,
        currentPrice: mondayGapPrice,
        unrealizedPnl: pos.size.mul(mondayGapPrice.sub(pos.entryPrice))
      }));

      const portfolioValue = new Decimal(100000);
      
      // Gap should result in loss worse than stop loss
      const actualLoss = mondayPositions[0].unrealizedPnl.toNumber();
      const expectedStopLoss = fridayPositions[0].size.mul(
        fridayPositions[0].stopLoss.sub(fridayPositions[0].entryPrice)
      ).toNumber();
      
      expect(actualLoss).toBeLessThan(expectedStopLoss); // Worse than stop loss
      expect(actualLoss).toBeLessThan(-30000); // Significant gap loss

      // Risk assessment should account for gap risk
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        mondayPositions,
        portfolioValue
      );

      expect(portfolioRisk.gapRisk).toBeGreaterThan(0.3); // High gap risk
    });
  });

  describe('Correlation Breakdown Scenarios', () => {
    it('should handle correlation breakdown during crisis', async () => {
      // Typically correlated positions
      const correlatedPositions: Position[] = [
        {
          id: 'corr-eur-1',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        },
        {
          id: 'corr-eur-2',
          userId: mockUserId,
          symbol: 'EURGBP',
          size: new Decimal(100000),
          entryPrice: new Decimal(0.8500),
          currentPrice: new Decimal(0.8500),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(0.8450),
          takeProfit: new Decimal(0.8600)
        },
        {
          id: 'corr-eur-3',
          userId: mockUserId,
          symbol: 'EURJPY',
          size: new Decimal(100000),
          entryPrice: new Decimal(180.00),
          currentPrice: new Decimal(180.00),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(178.00),
          takeProfit: new Decimal(183.00)
        }
      ];

      // Normal correlation scenario
      const normalCorrelationRisk = await riskManager.calculatePortfolioRisk(
        correlatedPositions,
        new Decimal(150000)
      );

      const normalDiversificationRatio = normalCorrelationRisk.diversificationRatio;

      // Crisis scenario with correlation breakdown
      const crisisMovements = {
        'EURUSD': -0.05, // EUR/USD falls 5%
        'EURGBP': +0.03, // But EUR/GBP rises 3% (negative correlation)
        'EURJPY': -0.08  // EUR/JPY falls 8% (higher correlation)
      };

      const crisisPositions = correlatedPositions.map(position => {
        const movement = crisisMovements[position.symbol] || 0;
        const newPrice = position.entryPrice.mul(1 + movement);
        const pnl = position.size.mul(newPrice.sub(position.entryPrice));
        
        return {
          ...position,
          currentPrice: newPrice,
          unrealizedPnl: pnl
        };
      });

      const crisisRisk = await riskManager.calculatePortfolioRisk(
        crisisPositions,
        new Decimal(150000),
        { correlationBreakdown: true }
      );

      // Diversification should be less effective during crisis
      expect(crisisRisk.diversificationRatio).toBeLessThan(normalDiversificationRatio);
      expect(crisisRisk.correlationRisk).toBeGreaterThan(0.6);
    });

    it('should handle flight-to-quality scenarios', async () => {
      // Mixed portfolio with safe haven and risk assets
      const mixedPositions: Position[] = [
        {
          id: 'risk-eurusd',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        },
        {
          id: 'safe-usdjpy',
          userId: mockUserId,
          symbol: 'USDJPY',
          size: new Decimal(100000),
          entryPrice: new Decimal(150.00),
          currentPrice: new Decimal(150.00),
          unrealizedPnl: new Decimal(0),
          direction: 'sell', // Short JPY (buying USD/JPY)
          openTime: new Date(),
          stopLoss: new Decimal(152.00),
          takeProfit: new Decimal(147.00)
        },
        {
          id: 'risk-audusd',
          userId: mockUserId,
          symbol: 'AUDUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(0.6500),
          currentPrice: new Decimal(0.6500),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(0.6450),
          takeProfit: new Decimal(0.6600)
        }
      ];

      // Flight-to-quality: USD and JPY strengthen, risk currencies weaken
      const flightToQualityMovements = {
        'EURUSD': -0.06, // EUR weakens 6%
        'USDJPY': -0.08, // JPY strengthens 8% (USD/JPY falls)
        'AUDUSD': -0.09  // AUD weakens 9%
      };

      const flightPositions = mixedPositions.map(position => {
        const movement = flightToQualityMovements[position.symbol] || 0;
        let newPrice = position.entryPrice.mul(1 + movement);
        let pnl: Decimal;
        
        if (position.direction === 'sell') {
          // For short positions, profit when price falls
          pnl = position.size.mul(position.entryPrice.sub(newPrice));
        } else {
          pnl = position.size.mul(newPrice.sub(position.entryPrice));
        }
        
        return {
          ...position,
          currentPrice: newPrice,
          unrealizedPnl: pnl
        };
      });

      const portfolioValue = new Decimal(120000);
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        flightPositions,
        portfolioValue
      );

      // Should show mixed results: safe haven profitable, risk assets losing
      const jpyPosition = flightPositions.find(p => p.symbol === 'USDJPY');
      const eurPosition = flightPositions.find(p => p.symbol === 'EURUSD');
      const audPosition = flightPositions.find(p => p.symbol === 'AUDUSD');

      expect(jpyPosition!.unrealizedPnl.toNumber()).toBeGreaterThan(0); // JPY short profitable
      expect(eurPosition!.unrealizedPnl.toNumber()).toBeLessThan(0); // EUR long loses
      expect(audPosition!.unrealizedPnl.toNumber()).toBeLessThan(0); // AUD long loses

      // Portfolio should show moderate risk due to mixed exposure
      expect(portfolioRisk.riskScore).toBeGreaterThan(40);
      expect(portfolioRisk.riskScore).toBeLessThan(80);
    });
  });

  describe('System Performance Under Extreme Load', () => {
    it('should maintain performance during market crash with 1000+ positions', async () => {
      // Create large portfolio of 1000 positions
      const largePortfolio: Position[] = Array.from({ length: 1000 }, (_, i) => ({
        id: `stress-pos-${i}`,
        userId: mockUserId,
        symbol: ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCHF'][i % 5],
        size: new Decimal(10000 + i * 100),
        entryPrice: new Decimal(1.2000 + (i * 0.0001)),
        currentPrice: new Decimal(1.2000 + (i * 0.0001)),
        unrealizedPnl: new Decimal(0),
        direction: i % 2 === 0 ? 'buy' : 'sell',
        openTime: new Date(Date.now() - i * 60000),
        stopLoss: new Decimal(1.1950 + (i * 0.0001)),
        takeProfit: new Decimal(1.2100 + (i * 0.0001))
      }));

      // Apply crash scenario to all positions
      const crashedPortfolio = largePortfolio.map(position => {
        const crashMovement = -0.10; // 10% adverse movement
        const newPrice = position.entryPrice.mul(1 + crashMovement);
        let pnl: Decimal;
        
        if (position.direction === 'sell') {
          pnl = position.size.mul(position.entryPrice.sub(newPrice));
        } else {
          pnl = position.size.mul(newPrice.sub(position.entryPrice));
        }
        
        return {
          ...position,
          currentPrice: newPrice,
          unrealizedPnl: pnl
        };
      });

      const startTime = Date.now();
      
      // Risk calculation for large portfolio during crisis
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        crashedPortfolio,
        new Decimal(10000000) // $10M portfolio
      );

      // Emergency liquidation check
      const shouldLiquidate = await lossLimitEnforcer.checkEmergencyLiquidation(
        mockUserId,
        crashedPortfolio,
        new Decimal(10000000)
      );

      const processingTime = Date.now() - startTime;

      // Should complete processing within 10 seconds even for 1000 positions
      expect(processingTime).toBeLessThan(10000);
      
      // Results should still be accurate
      expect(portfolioRisk.riskScore).toBeGreaterThan(80);
      expect(shouldLiquidate).toBe(true);
      expect(portfolioRisk.totalExposure.toNumber()).toBeGreaterThan(50000000);
    });

    it('should handle concurrent stress test scenarios', async () => {
      const scenarios = [
        'Financial Crisis 2008',
        'Brexit Shock 2016', 
        'Swiss Shock 2015',
        'COVID-19 Crash 2020',
        'Flash Crash 2010'
      ];

      const testPositions: Position[] = [
        {
          id: 'concurrent-test',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(100000),
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.2000),
          unrealizedPnl: new Decimal(0),
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1950),
          takeProfit: new Decimal(1.2100)
        }
      ];

      const portfolioValue = new Decimal(100000);
      
      // Run multiple stress test scenarios concurrently
      const startTime = Date.now();
      
      const stressTestPromises = scenarios.map(async (scenario, index) => {
        const movement = -0.05 * (index + 1); // Increasing severity
        
        const stressedPositions = testPositions.map(pos => ({
          ...pos,
          currentPrice: pos.entryPrice.mul(1 + movement),
          unrealizedPnl: pos.size.mul(pos.entryPrice.mul(movement))
        }));

        const portfolioRisk = await riskManager.calculatePortfolioRisk(
          stressedPositions,
          portfolioValue
        );

        return {
          scenario,
          riskScore: portfolioRisk.riskScore,
          loss: stressedPositions[0].unrealizedPnl.toNumber()
        };
      });

      const results = await Promise.all(stressTestPromises);
      const processingTime = Date.now() - startTime;

      // Should handle concurrent scenarios efficiently
      expect(processingTime).toBeLessThan(5000);
      expect(results.length).toBe(5);
      
      // Results should show increasing risk with severity
      for (let i = 1; i < results.length; i++) {
        expect(results[i].riskScore).toBeGreaterThanOrEqual(results[i-1].riskScore);
        expect(results[i].loss).toBeLessThanOrEqual(results[i-1].loss);
      }
    });
  });
});