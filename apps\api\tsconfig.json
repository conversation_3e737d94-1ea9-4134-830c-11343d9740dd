{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "commonjs", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "declaration": true, "sourceMap": true, "strict": true, "noEmit": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "paths": {"@golddaddy/types": ["../../packages/types/src"], "@golddaddy/config": ["../../packages/config/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}