// Prisma schema for GoldDaddy Trading Platform
// This is your Prisma schema file, learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// User model with trading preferences
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?

  // Profile information
  displayName      String
  riskTolerance    RiskTolerance @default(MODERATE)
  experienceLevel  ExperienceLevel @default(BEGINNER)
  coachingStyle    CoachingStyle @default(ENCOURAGING)
  tradingCapital   Decimal   @db.Decimal(15, 2)

  // Preferences
  notifications      Boole<PERSON> @default(true)
  autoOptimization   Boolean @default(false)
  paperTradingOnly   Boolean @default(true)
  preferredInstruments String[]

  // Relationships
  goals             TradingGoal[]
  trades            Trade[]
  confidenceAssessment ConfidenceAssessment?
  featureFlags      FeatureFlags?
  favoriteStrategies UserFavoriteStrategy[]
  strategyPlaylists StrategyPlaylist[]
  brokerConfigurations BrokerConfiguration[]
  failoverEvents    FailoverEvent[]
  quizSessions      QuizSession[]
  quizAttempts      QuizAttempt[]
  learningPlans     LearningPlan[]
  paperTradingSessions PaperTradingSession[]
  virtualPortfolios VirtualPortfolio[]
  paperTrades       PaperTrade[]
  
  // Monitoring relationships
  activityLogs     UserActivityLog[]
  assignedAlerts   Alert[]
  supportTickets   SupportTicket[]
  assignedTickets  SupportTicket[] @relation("AssignedTickets")

  @@map("users")
  @@index([email])
  @@index([createdAt])
}

// Trading goal model
model TradingGoal {
  id            String   @id @default(cuid())
  userId        String
  title         String
  targetReturn  Decimal @db.Decimal(15, 8) // Percentage
  timeframe     Int      // Days
  maxRisk       Decimal @db.Decimal(15, 8) // Percentage
  initialCapital Decimal @db.Decimal(15, 8)
  currentValue  Decimal @db.Decimal(15, 8)
  status        GoalStatus @default(ACTIVE)
  
  // Progress tracking
  daysElapsed        Int @default(0)
  returnAchieved     Decimal @db.Decimal(15, 8)
  maxDrawdownHit     Decimal @db.Decimal(15, 8)
  tradesExecuted     Int @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?
  targetDate DateTime

  // Relationships
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  trades      Trade[]
  paperTrades PaperTrade[]

  @@map("trading_goals")
  @@index([userId])
  @@index([status])
  @@index([createdAt])
}

// Strategy model
model Strategy {
  id          String   @id @default(cuid())
  name        String
  description String?
  type        StrategyType
  
  // Parameters
  instruments    String[]
  timeframe      String
  riskPerTrade   Decimal @db.Decimal(15, 8)
  stopLoss       Decimal @db.Decimal(15, 8)
  takeProfit     Decimal @db.Decimal(15, 8)
  customParams   Json?

  // Performance metrics
  winRate        Decimal @db.Decimal(15, 8)
  profitFactor   Decimal @db.Decimal(15, 8)
  sharpeRatio    Decimal @db.Decimal(15, 8)
  maxDrawdown    Decimal @db.Decimal(15, 8)
  totalReturn    Decimal @db.Decimal(15, 8)

  status         StrategyStatus @default(DRAFT)
  marketRegime   MarketRegime @default(ANY)
  riskLevel      RiskLevel @default(MODERATE) // New field for filtering

  // Additional metadata for browsing
  category       String?  // For categorization (e.g., "Forex Momentum", "Crypto Swing")
  author         String?  // Strategy creator or source
  difficulty     DifficultyLevel @default(INTERMEDIATE)
  tags           String[]  // Searchable tags

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?

  // Relationships
  trades Trade[]
  favorites UserFavoriteStrategy[]
  playlistItems StrategyPlaylistItem[]
  paperTrades PaperTrade[]

  @@map("strategies")
  @@index([type])
  @@index([status])
  @@index([marketRegime])
  @@index([riskLevel])
  @@index([category])
  @@index([createdAt])
}

// User favorite strategies (many-to-many)
model UserFavoriteStrategy {
  id         String   @id @default(cuid())
  userId     String
  strategyId String
  createdAt  DateTime @default(now())

  // Relationships
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  strategy Strategy @relation(fields: [strategyId], references: [id], onDelete: Cascade)

  @@unique([userId, strategyId])
  @@map("user_favorite_strategies")
  @@index([userId])
  @@index([strategyId])
}

// Strategy playlists
model StrategyPlaylist {
  id          String   @id @default(cuid())
  userId      String
  name        String
  description String?
  isPublic    Boolean  @default(false)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?

  // Relationships
  user  User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  items StrategyPlaylistItem[]

  @@map("strategy_playlists")
  @@index([userId])
  @@index([isPublic])
  @@index([createdAt])
}

// Strategy playlist items (many-to-many with ordering)
model StrategyPlaylistItem {
  id         String   @id @default(cuid())
  playlistId String
  strategyId String
  order      Int      @default(0)
  addedAt    DateTime @default(now())

  // Relationships
  playlist StrategyPlaylist @relation(fields: [playlistId], references: [id], onDelete: Cascade)
  strategy Strategy         @relation(fields: [strategyId], references: [id], onDelete: Cascade)

  @@unique([playlistId, strategyId])
  @@map("strategy_playlist_items")
  @@index([playlistId])
  @@index([strategyId])
  @@index([playlistId, order])
}

// Trade model
model Trade {
  id           String   @id @default(cuid())
  userId       String
  strategyId   String?
  goalId       String?
  instrument   String
  type         TradeType
  quantity     Decimal @db.Decimal(15, 8)
  entryPrice   Decimal @db.Decimal(15, 8)
  exitPrice    Decimal @db.Decimal(15, 8)
  stopLoss     Decimal @db.Decimal(15, 8)
  takeProfit   Decimal @db.Decimal(15, 8)
  status       TradeStatus @default(PENDING)
  
  executionTime DateTime
  closeTime     DateTime?
  pnl           Decimal @db.Decimal(15, 8)
  pnlPercentage Decimal @db.Decimal(15, 8)
  fees          Decimal @db.Decimal(15, 8)
  slippage      Decimal @db.Decimal(15, 8)

  // Metadata
  marketConditions Json?
  signalStrength   Decimal @db.Decimal(15, 8)
  riskScore        Decimal @db.Decimal(15, 8)
  autoExecuted     Boolean @default(false)
  mlPrediction     Json?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  user     User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  strategy Strategy?     @relation(fields: [strategyId], references: [id])
  goal     TradingGoal?  @relation(fields: [goalId], references: [id])

  @@map("trades")
  @@index([userId])
  @@index([strategyId])
  @@index([goalId])
  @@index([status])
  @@index([executionTime])
  @@index([instrument])
}

// Market data model (TimescaleDB optimized)
model MarketData {
  timestamp   DateTime
  instrument  String
  timeframe   Timeframe
  open        Decimal @db.Decimal(15, 8)
  high        Decimal @db.Decimal(15, 8)
  low         Decimal @db.Decimal(15, 8)
  close       Decimal @db.Decimal(15, 8)
  volume      Decimal @db.Decimal(15, 8)
  source      DataSource

  // Technical indicators (optional)
  indicators  Json?

  createdAt   DateTime @default(now())

  @@id([timestamp, instrument, timeframe])
  @@map("market_data")
  @@index([instrument, timeframe, timestamp])
  @@index([timestamp])
  @@index([instrument])
}

// Enhanced confidence assessment model
model ConfidenceAssessment {
  id                    String   @id @default(cuid())
  userId                String   @unique
  currentStage          ConfidenceStage @default(GOAL_SETTING)
  overallConfidenceScore Int     @default(0) // 0-100

  // Enhanced assessment scores structure
  assessmentScores      Json?   // Detailed scores from new interface
  progressHistory       Json?   // Stage progression history
  graduationCriteria    Json?   // Stage advancement requirements

  // Legacy fields (for backwards compatibility)
  knowledgeQuizScore    Int? // 0-100
  behavioralScore       Int? // 0-100
  performanceScore      Int? // 0-100
  stressTestScore       Int? // 0-100

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("confidence_assessments")
}

// Feature flags model
model FeatureFlags {
  userId              String   @id
  level               FeatureLevel @default(BASIC)
  customFlags         Json?
  rolloutGroup        String?
  rolloutPercentage   Int      @default(0)
  enabledAt           DateTime @default(now())
  expiresAt           DateTime?

  updatedAt DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("feature_flags")
}

// PlainEnglishMetrics Model - User-friendly metric translations
model PlainEnglishMetrics {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Metric Definition
  metricType String @map("metric_type") // 'sharpe_ratio' | 'max_drawdown' | 'win_rate' | etc.
  originalValue Decimal @db.Decimal(15, 8) @map("original_value")
  
  // Plain English Translation
  plainEnglishExplanation String @map("plain_english_explanation")
  contextualAdvice String? @map("contextual_advice")
  
  // Visual Metadata (JSON)
  visualMetadata Json? @map("visual_metadata") // Chart configs, colors, etc.
  
  // User Personalization
  experienceLevel String? @map("experience_level") // Target experience level
  riskTolerance String? @map("risk_tolerance") // Target risk tolerance
  learningStyle String? @map("learning_style") // 'visual' | 'analytical' | 'practical'

  @@map("plain_english_metrics")
}

// Audit Log Model - Comprehensive audit trail for compliance
model AuditLog {
  id          String   @id @default(cuid())
  timestamp   DateTime @default(now())
  
  // Action Classification
  actionType  AuditActionType
  severity    AuditSeverity   @default(low)
  
  // User Context (optional for system actions)
  userId      String?
  brokerId    String?
  message     String
  
  // Request Context
  ipAddress   String?
  userAgent   String?
  sessionId   String?
  
  // Operation Success/Failure
  success     Boolean  @default(true)
  errorMessage String?
  
  // Data Integrity
  dataHash        String?  // SHA-256 hash of this record
  previousDataHash String? // Hash of previous record for chain integrity
  
  // Compliance and Retention
  complianceRelevant Boolean  @default(false)
  retentionCategory  RetentionCategory @default(SHORT_TERM)
  
  // Detailed Information (JSON)
  details     Json?    // Action-specific details
  metadata    Json?    // Additional context and trade impact data
  
  createdAt   DateTime @default(now())

  @@map("audit_logs")
  @@index([timestamp])
  @@index([actionType])
  @@index([severity])
  @@index([userId, timestamp])
  @@index([brokerId, timestamp])
  @@index([complianceRelevant])
  @@index([retentionCategory])
}

// Compliance Report Model - Generated compliance reports
model ComplianceReport {
  id            String   @id @default(cuid())
  reportType    ComplianceReportType
  
  // Report Period
  startDate     DateTime
  endDate       DateTime
  generatedAt   DateTime @default(now())
  generatedBy   String?  // User who generated the report
  
  // Summary Metrics
  totalEvents         Int
  criticalEvents      Int
  failoverEvents      Int
  errorEvents         Int
  complianceScore     Decimal @db.Decimal(15, 8) // 0-100
  dataIntegrityScore  Decimal @db.Decimal(15, 8) // 0-100
  
  // Detailed Analysis (JSON)
  categoryAnalysis    Json     // Per-category compliance analysis
  auditTrailIntegrity Json     // Hash chain validation results
  keyMetrics          Json     // Performance and reliability metrics
  recommendations     Json     // Compliance recommendations
  
  // Report File References
  detailedLogsPath      String?
  errorSummaryPath      String?
  performanceMetricsPath String?
  
  // Status
  status         ComplianceReportStatus @default(GENERATED)
  reviewedAt     DateTime?
  reviewedBy     String?
  reviewNotes    String?
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("compliance_reports")
  @@index([reportType])
  @@index([generatedAt])
  @@index([startDate, endDate])
  @@index([complianceScore])
  @@index([status])
}

// Compliance Issue Tracking Model
model ComplianceIssue {
  id            String   @id @default(cuid())
  category      ComplianceCategory
  severity      ComplianceSeverity
  
  // Issue Details
  title         String
  description   String
  affectedPeriodStart DateTime
  affectedPeriodEnd   DateTime?
  
  // Related Audit Records
  relatedAuditIds String[] // Array of audit log IDs
  
  // Status Tracking
  status        ComplianceIssueStatus @default(OPEN)
  resolvedAt    DateTime?
  resolvedBy    String?
  resolution    String?
  
  // Preventive Actions
  preventiveActions String[]
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("compliance_issues")
  @@index([category])
  @@index([severity])
  @@index([status])
  @@index([createdAt])
}

// Broker Configuration Model - Multi-broker support
model BrokerConfiguration {
  id        String   @id @default(cuid())
  userId    String
  brokerName String  
  priority  Int      // 1 = primary, 2+ = backup priority order
  
  // Connection Details (encrypted)
  server    String
  login     String
  password  String   // Encrypted in service layer
  timeout   Int      @default(10000) // Connection timeout in ms
  
  // Health Check Configuration
  healthCheckInterval Int @default(30000) // Health check interval in ms
  healthCheckTimeout  Int @default(5000)  // Health check timeout in ms
  retryCount          Int @default(5)     // Max retry attempts
  
  // Status and Monitoring
  status           BrokerStatus @default(INACTIVE)
  lastHealthCheck  DateTime?
  lastError        String?
  isHealthy        Boolean      @default(false)
  failureCount     Int          @default(0)
  
  // Features supported by this broker
  features         String[]     // ['streaming', 'trading', 'history', 'monitoring']
  
  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deletedAt DateTime?

  // Relationships
  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  failoverEvents FailoverEvent[]
  healthChecks  BrokerHealthCheck[]

  @@map("broker_configurations")
  @@index([userId])
  @@index([priority])
  @@index([status])
  @@index([isHealthy])
  @@unique([userId, brokerName])
}

// Failover Event Model - Track broker failover events
model FailoverEvent {
  id              String   @id @default(cuid())
  userId          String
  fromBroker      String?  // Null if initial connection
  toBroker        String
  
  // Failover Details
  trigger         FailoverTrigger
  failoverTime    DateTime @default(now())
  recoveryTime    DateTime?
  duration        Int?     // Duration in milliseconds
  
  // Impact Assessment
  impactedTrades  String[] // Array of trade IDs affected
  positionsSynced Boolean  @default(false)
  dataLoss        Boolean  @default(false)
  
  // Error Information
  errorCode       String?
  errorMessage    String?
  errorContext    Json?    // Additional error details
  
  // Recovery Actions
  recoveryActions String[] // Actions taken during recovery
  manualIntervention Boolean @default(false)
  
  // Audit Trail
  auditTrailId    String?
  
  createdAt DateTime @default(now())

  // Relationships
  user              User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  fromBrokerConfig  BrokerConfiguration?   @relation(fields: [fromBroker], references: [id])

  @@map("failover_events")
  @@index([userId])
  @@index([failoverTime])
  @@index([trigger])
  @@index([toBroker])
}

// System Error Model - Comprehensive error tracking
model SystemError {
  id            String   @id @default(cuid())
  errorCode     String   // Standardized error codes
  errorType     ErrorType
  errorCategory ErrorCategory
  
  // Error Details
  message       String
  stackTrace    String?
  context       Json?    // Additional context data
  
  // System Information
  component     String   // Component that generated the error
  version       String?  // System version
  environment   String?  // 'development' | 'staging' | 'production'
  
  // User Context (optional)
  userId        String?
  sessionId     String?
  requestId     String?
  
  // Error Handling
  handled       Boolean  @default(false)
  retryCount    Int      @default(0)
  resolved      Boolean  @default(false)
  resolution    String?  // How the error was resolved
  
  // Circuit Breaker State
  circuitState  CircuitState? // If error triggered circuit breaker
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  resolvedAt DateTime?

  @@map("system_errors")
  @@index([errorCode])
  @@index([errorType])
  @@index([errorCategory])
  @@index([component])
  @@index([createdAt])
  @@index([userId])
}

// Broker Health Check Model - Track broker health status
model BrokerHealthCheck {
  id           String   @id @default(cuid())
  brokerId     String
  
  // Health Check Results
  healthy      Boolean
  latency      Int      // Response time in milliseconds
  errorMessage String?
  
  // Test Details
  testType     String   // 'ping' | 'connection' | 'trade_test' | 'data_fetch'
  testData     Json?    // Test parameters and results
  
  // Performance Metrics
  responseSize Int?     // Response size in bytes
  throughput   Decimal @db.Decimal(15, 8) // Requests per second
  
  timestamp DateTime @default(now())

  // Relationships
  broker BrokerConfiguration @relation(fields: [brokerId], references: [id], onDelete: Cascade)

  @@map("broker_health_checks")
  @@index([brokerId])
  @@index([timestamp])
  @@index([healthy])
  @@index([testType])
}

// Quiz Question Model
model QuizQuestion {
  id                String   @id @default(cuid())
  category          QuizCategory
  difficulty        QuizDifficulty
  topic             String
  question          String
  options           Json     // Array of QuizOption objects
  correctAnswerIds  String[] @map("correct_answer_ids")
  explanation       String
  learningResources Json?    @map("learning_resources")
  metadata          Json     // QuizQuestionMetadata object
  isActive          Boolean  @default(true) @map("is_active")
  
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relationships
  responses QuizResponse[]

  @@map("quiz_questions")
  @@index([category, difficulty])
  @@index([topic])
  @@index([isActive])
}

// Quiz Session Model
model QuizSession {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  quizType    QuizType @map("quiz_type")
  stage       ConfidenceStage
  questionIds String[] @map("question_ids")
  metadata    Json     // QuizSessionMetadata object
  status      QuizSessionStatus @default(NOT_STARTED)
  
  startedAt   DateTime? @map("started_at")
  completedAt DateTime? @map("completed_at")
  expiresAt   DateTime? @map("expires_at")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relationships
  user      User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  responses QuizResponse[]
  attempts  QuizAttempt[]

  @@map("quiz_sessions")
  @@index([userId, status])
  @@index([stage])
  @@index([createdAt])
}

// Quiz Response Model
model QuizResponse {
  id                String   @id @default(cuid())
  sessionId         String   @map("session_id")
  questionId        String   @map("question_id")
  selectedOptionIds String[] @map("selected_option_ids")
  isCorrect         Boolean  @map("is_correct")
  timeSpent         Int      @map("time_spent") // seconds
  confidenceLevel   Int      @map("confidence_level") // 1-5
  
  createdAt DateTime @default(now()) @map("created_at")

  // Relationships
  session  QuizSession  @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  question QuizQuestion @relation(fields: [questionId], references: [id], onDelete: Cascade)

  @@map("quiz_responses")
  @@index([sessionId])
  @@index([questionId])
  @@unique([sessionId, questionId])
}

// Quiz Attempt Model
model QuizAttempt {
  id                String   @id @default(cuid())
  userId            String   @map("user_id")
  sessionId         String   @map("session_id")
  attemptNumber     Int      @map("attempt_number")
  score             Int      // 0-100
  totalTimeSpent    Int      @map("total_time_spent") // seconds
  questionsAnswered Int      @map("questions_answered")
  correctAnswers    Int      @map("correct_answers")
  weakAreas         String[] @map("weak_areas")
  strongAreas       String[] @map("strong_areas")
  confidenceScore   Int      @map("confidence_score") // 0-100
  feedback          Json     // QuizFeedback object
  status            QuizAttemptStatus
  
  createdAt   DateTime  @default(now()) @map("created_at")
  completedAt DateTime? @map("completed_at")

  // Relationships
  user    User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  session QuizSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("quiz_attempts")
  @@index([userId])
  @@index([userId, attemptNumber])
  @@index([score])
  @@index([createdAt])
  @@unique([sessionId, attemptNumber])
}

// Learning Plan Model
model LearningPlan {
  id                String   @id @default(cuid())
  userId            String   @map("user_id")
  topics            String[]
  resources         Json     // Array of LearningResource objects
  estimatedDuration Int      @map("estimated_duration") // hours
  checkpoints       String[]
  progress          Json?    // Progress tracking data
  status            String   @default("active")
  
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  completedAt DateTime? @map("completed_at")

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("learning_plans")
  @@index([userId, status])
}

// Enums
enum RiskTolerance {
  CONSERVATIVE
  MODERATE
  AGGRESSIVE
}

enum ExperienceLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
}

enum CoachingStyle {
  ENCOURAGING
  ANALYTICAL
  CHALLENGING
}

enum GoalStatus {
  ACTIVE
  COMPLETED
  PAUSED
  FAILED
}

enum StrategyType {
  MOMENTUM
  MEAN_REVERSION
  BREAKOUT
  ML_ENHANCED
  CUSTOM
}

enum StrategyStatus {
  DRAFT
  BACKTESTED
  PAPER_TRADING
  LIVE
  ARCHIVED
}

enum MarketRegime {
  TRENDING
  RANGING
  VOLATILE
  ANY
}

enum TradeType {
  BUY
  SELL
}

enum TradeStatus {
  PENDING
  FILLED
  CANCELLED
  CLOSED
}

enum Timeframe {
  M1  // 1 minute
  M5  // 5 minutes
  M15 // 15 minutes
  H1  // 1 hour
  H4  // 4 hours
  D1  // 1 day
}

enum DataSource {
  MT5
  ALPHA_VANTAGE
  YAHOO_FINANCE
}

enum ConfidenceStage {
  GOAL_SETTING
  STRATEGY_LEARNING
  BACKTESTING_REVIEW
  PAPER_TRADING
  LIVE_READY
}

enum FeatureLevel {
  BASIC
  ENHANCED
  ADVANCED
}

enum RiskLevel {
  LOW
  MODERATE
  HIGH
}

enum DifficultyLevel {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

// Broker Failover Enums
enum BrokerStatus {
  ACTIVE
  INACTIVE
  FAILED
  MAINTENANCE
  CONNECTING
}

enum FailoverTrigger {
  CONNECTION_TIMEOUT
  EXECUTION_FAILURE
  MANUAL_OVERRIDE
  HEALTH_CHECK_FAILURE
  NETWORK_ERROR
  BROKER_MAINTENANCE
}

enum ErrorType {
  CONNECTION_ERROR
  EXECUTION_ERROR
  VALIDATION_ERROR
  SYSTEM_ERROR
  NETWORK_ERROR
  TIMEOUT_ERROR
}

enum ErrorCategory {
  BROKER_ERROR
  TRADING_ERROR
  DATA_ERROR
  AUTHENTICATION_ERROR
  CONFIGURATION_ERROR
  INFRASTRUCTURE_ERROR
}

enum CircuitState {
  CLOSED
  OPEN
  HALF_OPEN
}

// Audit Trail Enums
enum AuditActionType {
  USER_LOGIN
  USER_LOGOUT
  BROKER_CONFIGURATION_CREATED
  BROKER_CONFIGURATION_UPDATED
  BROKER_CONFIGURATION_DELETED
  FAILOVER_INITIATED
  FAILOVER_COMPLETED
  FAILOVER_FAILED
  BROKER_HEALTH_CHECK
  CIRCUIT_BREAKER_OPENED
  CIRCUIT_BREAKER_CLOSED
  ERROR_CLASSIFIED
  ALERT_CREATED
  ALERT_ACKNOWLEDGED
  SYSTEM_STARTUP
  SYSTEM_SHUTDOWN
  DATA_EXPORT
  CONFIGURATION_CHANGE
  MANUAL_INTERVENTION
  COMPLIANCE_REPORT_GENERATED
}

enum AuditSeverity {
  low
  medium
  high
  critical
}

enum RetentionCategory {
  SHORT_TERM    // 30 days
  LONG_TERM     // 7 years for compliance
  PERMANENT     // Never delete
}

// Compliance Enums
enum ComplianceCategory {
  DATA_PROTECTION
  AUDIT_TRAIL
  ACCESS_CONTROL
  SYSTEM_MONITORING
  ERROR_HANDLING
  FAILOVER_PROCEDURES
  DATA_RETENTION
  INCIDENT_RESPONSE
}

enum ComplianceReportType {
  WEEKLY
  MONTHLY
  QUARTERLY
  ANNUAL
  ADHOC
}

enum ComplianceReportStatus {
  GENERATED
  UNDER_REVIEW
  APPROVED
  REJECTED
}

enum ComplianceSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ComplianceIssueStatus {
  OPEN
  INVESTIGATING
  RESOLVED
  MITIGATED
}

// Quiz System Enums
enum QuizCategory {
  TRADING_FUNDAMENTALS
  RISK_MANAGEMENT
  PLATFORM_FEATURES
  SAFETY_PROCEDURES
  REGULATORY_COMPLIANCE
  MARKET_ANALYSIS
  PSYCHOLOGY_DISCIPLINE
}

enum QuizDifficulty {
  BEGINNER
  INTERMEDIATE
  ADVANCED
  EXPERT
}

enum QuizType {
  STAGE_ADVANCEMENT
  KNOWLEDGE_REFRESH
  REMEDIAL_LEARNING
  CERTIFICATION
}

enum QuizSessionStatus {
  NOT_STARTED
  IN_PROGRESS
  PAUSED
  COMPLETED
  EXPIRED
  CANCELLED
}

enum QuizAttemptStatus {
  IN_PROGRESS
  COMPLETED
  FAILED
  TIMED_OUT
  CANCELLED
}

// Paper Trading Session Model
model PaperTradingSession {
  id                    String   @id @default(cuid())
  userId                String   @map("user_id")
  name                  String
  description           String?
  startDate             DateTime @default(now()) @map("start_date")
  endDate               DateTime? @map("end_date")
  isActive              Boolean  @default(true) @map("is_active")
  
  // Requirements tracking
  minRequiredDays       Int      @default(30) @map("min_required_days")
  minRequiredTrades     Int      @default(50) @map("min_required_trades")
  currentDays           Int      @default(0) @map("current_days")
  currentTrades         Int      @default(0) @map("current_trades")
  
  // Performance tracking
  totalReturn           Decimal  @default(0) @db.Decimal(15, 8) @map("total_return")
  totalReturnPercentage Decimal  @default(0) @db.Decimal(15, 8) @map("total_return_percentage")
  winRate               Decimal  @default(0) @db.Decimal(15, 8) @map("win_rate")
  profitFactor          Decimal  @default(0) @db.Decimal(15, 8) @map("profit_factor")
  sharpeRatio           Decimal? @db.Decimal(15, 8) @map("sharpe_ratio")
  maxDrawdown           Decimal  @default(0) @db.Decimal(15, 8) @map("max_drawdown")
  
  // Graduation criteria (JSON)
  performanceThresholds Json     @default("{\"winRate\":{\"current\":0,\"required\":60,\"met\":false},\"riskManagement\":{\"current\":0,\"required\":75,\"met\":false},\"consistency\":{\"current\":0,\"required\":70,\"met\":false}}") @map("performance_thresholds")
  
  // Graduation status
  graduationEligible    Boolean  @default(false) @map("graduation_eligible")
  graduatedAt           DateTime? @map("graduated_at")
  
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relationships
  user              User                    @relation(fields: [userId], references: [id], onDelete: Cascade)
  virtualPortfolios VirtualPortfolio[]
  paperTrades       PaperTrade[]
  analytics         PaperTradingAnalytics[]

  @@map("paper_trading_sessions")
  @@index([userId])
  @@index([isActive])
  @@index([graduationEligible])
  @@index([createdAt])
}

// Virtual Portfolio Model
model VirtualPortfolio {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  sessionId       String   @map("session_id")
  initialBalance  Decimal  @db.Decimal(15, 8) @map("initial_balance")
  currentBalance  Decimal  @db.Decimal(15, 8) @map("current_balance")
  availableMargin Decimal  @db.Decimal(15, 8) @map("available_margin")
  usedMargin      Decimal  @default(0) @db.Decimal(15, 8) @map("used_margin")
  unrealizedPnl   Decimal  @default(0) @db.Decimal(15, 8) @map("unrealized_pnl")
  realizedPnl     Decimal  @default(0) @db.Decimal(15, 8) @map("realized_pnl")
  totalPnl        Decimal  @default(0) @db.Decimal(15, 8) @map("total_pnl")
  
  // Virtual positions and risk metrics (JSON)
  positions       Json     @default("[]")
  dailyPnl        Decimal[] @map("daily_pnl") @db.Decimal(15, 8)
  riskMetrics     Json     @default("{\"maxDrawdown\":0,\"currentDrawdown\":0,\"riskPercentage\":0,\"portfolioVar\":0}") @map("risk_metrics")
  
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relationships
  user    User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  session PaperTradingSession  @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("virtual_portfolios")
  @@index([userId])
  @@index([sessionId])
  @@index([createdAt])
}

// Paper Trade Model (extends Trade with simulation data)
model PaperTrade {
  id         String   @id @default(cuid())
  userId     String   @map("user_id")
  sessionId  String   @map("session_id")
  strategyId String?  @map("strategy_id")
  goalId     String?  @map("goal_id")
  accountId  String   @map("account_id")
  symbol     String
  type       TradeType
  volume     Decimal  @db.Decimal(15, 8)
  openPrice  Decimal  @db.Decimal(15, 8) @map("open_price")
  closePrice Decimal? @db.Decimal(15, 8) @map("close_price")
  stopLoss   Decimal? @db.Decimal(15, 8) @map("stop_loss")
  takeProfit Decimal? @db.Decimal(15, 8) @map("take_profit")
  profit     Decimal? @db.Decimal(15, 8)
  commission Decimal  @default(0) @db.Decimal(15, 8)
  swap       Decimal  @default(0) @db.Decimal(15, 8)
  openTime   DateTime @map("open_time")
  closeTime  DateTime? @map("close_time")
  status     TradeStatus @default(PENDING)
  
  // Paper trading specific data (JSON)
  simulationMetadata Json @map("simulation_metadata")
  portfolioImpact    Json @map("portfolio_impact")
  learningMetadata   Json @map("learning_metadata")
  
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relationships
  user     User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  session  PaperTradingSession  @relation(fields: [sessionId], references: [id], onDelete: Cascade)
  strategy Strategy?            @relation(fields: [strategyId], references: [id])
  goal     TradingGoal?         @relation(fields: [goalId], references: [id])

  @@map("paper_trades")
  @@index([userId])
  @@index([sessionId])
  @@index([strategyId])
  @@index([goalId])
  @@index([status])
  @@index([openTime])
  @@index([symbol])
}

// Paper Trading Analytics Model
model PaperTradingAnalytics {
  id                     String   @id @default(cuid())
  sessionId              String   @map("session_id")
  
  // Performance comparison and insights (JSON)
  backtestingComparison  Json     @map("backtesting_comparison")
  performanceInsights    Json     @map("performance_insights")
  riskAnalysis           Json     @map("risk_analysis")
  
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relationships
  session PaperTradingSession @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("paper_trading_analytics")
  @@index([sessionId])
  @@index([createdAt])
}

// ===== MONITORING SYSTEM MODELS =====

// System Metrics Model (TimescaleDB hypertable)
model SystemMetrics {
  id           String   @id @default(cuid())
  timestamp    DateTime @default(now())
  serviceType  String   @map("service_type") // 'api' | 'mt5-bridge' | 'database' | 'websocket'
  metrics      Json     // SystemMetricsData object
  healthStatus String   @map("health_status") // 'healthy' | 'warning' | 'critical' | 'down'
  createdAt    DateTime @default(now()) @map("created_at")

  @@map("system_metrics")
  @@index([serviceType, timestamp(sort: Desc)])
  @@index([timestamp(sort: Desc)])
  @@index([healthStatus])
}

// User Activity Logs Model (TimescaleDB hypertable) 
model UserActivityLog {
  id              String   @id @default(cuid())
  userId          String   @map("user_id")
  timestamp       DateTime @default(now())
  activityType    String   @map("activity_type") // 'login' | 'trade_execution' | 'strategy_modification' | 'risk_breach' | 'unusual_pattern'
  metadata        Json     // Activity-specific data
  anomalyScore    Int?     @map("anomaly_score") // 0-100, higher indicates more unusual
  flaggedForReview Boolean @default(false) @map("flagged_for_review")
  reviewStatus    String?  @map("review_status") // 'pending' | 'reviewed' | 'resolved'
  createdAt       DateTime @default(now()) @map("created_at")

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("user_activity_logs")
  @@index([userId, timestamp(sort: Desc)])
  @@index([timestamp(sort: Desc)])
  @@index([activityType])
  @@index([flaggedForReview])
  @@index([reviewStatus])
}

// Alerts Management Model
model Alert {
  id              String   @id @default(cuid())
  type            String   // 'system' | 'user_activity' | 'mt5_health' | 'risk_management'
  severity        String   // 'low' | 'medium' | 'high' | 'critical' | 'emergency'
  category        String   // 'system_performance' | 'risk_management' | 'user_activity' | 'mt5_integration'
  message         String
  source          Json     // Alert source information
  status          String   @default("active") // 'active' | 'acknowledged' | 'resolved' | 'false_positive'
  createdAt       DateTime @default(now()) @map("created_at")
  acknowledgedAt  DateTime? @map("acknowledged_at")
  resolvedAt      DateTime? @map("resolved_at")
  assignedTo      String?  @map("assigned_to")
  notifications   Json     @default("[]") // Array of notification history
  escalationLevel Int      @default(0) @map("escalation_level")
  correlationId   String?  @map("correlation_id") // For grouping related alerts

  // Relationships (optional user assignment)
  assignedUser    User?           @relation(fields: [assignedTo], references: [id])
  supportTickets  SupportTicket[] // Tickets created from this alert

  @@map("alerts")
  @@index([status, severity, createdAt(sort: Desc)])
  @@index([type, createdAt(sort: Desc)])
  @@index([category, createdAt(sort: Desc)])
  @@index([assignedTo])
  @@index([correlationId])
}

// Support Tickets Model
model SupportTicket {
  id          String   @id @default(cuid())
  userId      String   @map("user_id")
  title       String
  description String
  category    String   // 'technical' | 'trading' | 'account' | 'platform' | 'alert_generated'
  priority    String   // 'low' | 'medium' | 'high' | 'urgent'
  status      String   @default("open") // 'open' | 'in_progress' | 'resolved' | 'closed'
  assignedTo  String?  @map("assigned_to")
  metadata    Json     @default("{}") // Additional ticket data
  alertId     String?  @map("alert_id") // Reference to triggering alert if auto-generated
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")
  resolvedAt  DateTime? @map("resolved_at")

  // Relationships
  user         User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  assignedUser User?  @relation("AssignedTickets", fields: [assignedTo], references: [id])
  alert        Alert? @relation(fields: [alertId], references: [id])

  @@map("support_tickets")
  @@index([userId])
  @@index([status, priority])
  @@index([createdAt(sort: Desc)])
  @@index([assignedTo])
  @@index([alertId])
}

