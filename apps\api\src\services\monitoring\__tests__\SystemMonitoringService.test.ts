import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PrismaClient } from '@prisma/client';
import { SystemMonitoringService } from '../SystemMonitoringService';

// Mock Prisma
const mockPrisma = {
  systemMetrics: {
    create: vi.fn(),
  },
  $queryRaw: vi.fn(),
} as unknown as PrismaClient;

describe('SystemMonitoringService', () => {
  let service: SystemMonitoringService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new SystemMonitoringService(mockPrisma);
  });

  afterEach(async () => {
    if (service) {
      service.stopMonitoring();
    }
  });

  describe('startMonitoring', () => {
    it('should start monitoring successfully', async () => {
      const config = { intervalMs: 1000 };
      
      await service.startMonitoring(config);
      
      expect(service.getMonitoringStatus().isActive).toBe(true);
    });

    it('should not start monitoring if already active', async () => {
      await service.startMonitoring();
      
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      await service.startMonitoring();
      
      expect(consoleSpy).toHaveBeenCalledWith('System monitoring already active');
      consoleSpy.mockRestore();
    });

    it('should emit monitoringStarted event', async () => {
      const eventSpy = vi.fn();
      service.on('monitoringStarted', eventSpy);
      
      await service.startMonitoring();
      
      expect(eventSpy).toHaveBeenCalled();
    });
  });

  describe('stopMonitoring', () => {
    it('should stop monitoring successfully', async () => {
      await service.startMonitoring();
      service.stopMonitoring();
      
      expect(service.getMonitoringStatus().isActive).toBe(false);
    });

    it('should emit monitoringStopped event', async () => {
      const eventSpy = vi.fn();
      service.on('monitoringStopped', eventSpy);
      
      await service.startMonitoring();
      service.stopMonitoring();
      
      expect(eventSpy).toHaveBeenCalled();
    });
  });

  describe('collectSystemMetrics', () => {
    it('should collect metrics with correct structure', async () => {
      vi.mocked(mockPrisma.$queryRaw).mockResolvedValue([]);
      
      const eventSpy = vi.fn();
      service.on('metricsCollected', eventSpy);
      
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for metrics collection
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(eventSpy).toHaveBeenCalled();
      const metrics = eventSpy.mock.calls[0][0];
      
      expect(metrics).toHaveProperty('id');
      expect(metrics).toHaveProperty('timestamp');
      expect(metrics).toHaveProperty('serviceType');
      expect(metrics).toHaveProperty('metrics');
      expect(metrics).toHaveProperty('healthStatus');
      
      expect(metrics.metrics).toHaveProperty('uptime');
      expect(metrics.metrics).toHaveProperty('errorRate');
      expect(metrics.metrics).toHaveProperty('responseTime');
      expect(metrics.metrics).toHaveProperty('throughput');
      expect(metrics.metrics).toHaveProperty('memoryUsage');
      expect(metrics.metrics).toHaveProperty('cpuUsage');
    });

    it('should handle database query failures gracefully', async () => {
      vi.mocked(mockPrisma.$queryRaw).mockRejectedValue(new Error('Database error'));
      
      const eventSpy = vi.fn();
      service.on('metricsCollected', eventSpy);
      
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for metrics collection
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(eventSpy).toHaveBeenCalled();
      const metrics = eventSpy.mock.calls[0][0];
      
      // Should still have metrics even with database failure
      expect(metrics.metrics.responseTime).toBe(-1); // Indicates failure
    });
  });

  describe('alert generation', () => {
    it('should generate CPU usage alert', async () => {
      const alertSpy = vi.fn();
      service.on('alert', alertSpy);
      
      // Mock high CPU usage
      const originalCpuUsage = process.cpuUsage;
      process.cpuUsage = vi.fn().mockReturnValue({ user: 90000000, system: 10000000 }); // High CPU
      
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for metrics collection
      await new Promise(resolve => setTimeout(resolve, 150));
      
      process.cpuUsage = originalCpuUsage;
      
      // Should have generated an alert
      const alerts = alertSpy.mock.calls;
      expect(alerts.length).toBeGreaterThan(0);
      
      const cpuAlert = alerts.find(call => 
        call[0].alertType === 'cpu_usage'
      );
      expect(cpuAlert).toBeDefined();
    });

    it('should generate memory usage alert', async () => {
      const alertSpy = vi.fn();
      service.on('alert', alertSpy);
      
      // Mock high memory usage
      const originalMemoryUsage = process.memoryUsage;
      process.memoryUsage = vi.fn().mockReturnValue({
        heapUsed: 900000000,  // 900MB used
        heapTotal: 1000000000, // 1GB total (90% usage)
        external: 0,
        rss: 1000000000,
        arrayBuffers: 0,
      });
      
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for metrics collection
      await new Promise(resolve => setTimeout(resolve, 150));
      
      process.memoryUsage = originalMemoryUsage;
      
      // Should have generated a memory alert
      const alerts = alertSpy.mock.calls;
      const memoryAlert = alerts.find(call => 
        call[0].alertType === 'memory_usage'
      );
      expect(memoryAlert).toBeDefined();
    });

    it('should generate response time alert', async () => {
      vi.mocked(mockPrisma.$queryRaw).mockImplementation(() => 
        new Promise(resolve => setTimeout(() => resolve([]), 2000)) // 2 second delay
      );
      
      const alertSpy = vi.fn();
      service.on('alert', alertSpy);
      
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for metrics collection
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Should have generated a response time alert
      const alerts = alertSpy.mock.calls;
      const responseTimeAlert = alerts.find(call => 
        call[0].alertType === 'response_time'
      );
      expect(responseTimeAlert).toBeDefined();
    });
  });

  describe('getSystemStatus', () => {
    it('should return system status with no metrics', async () => {
      const status = await service.getSystemStatus();
      
      expect(status).toHaveProperty('status', 'unknown');
      expect(status).toHaveProperty('uptime');
      expect(status).toHaveProperty('lastCheck');
      expect(status).toHaveProperty('services');
      expect(status.services).toHaveProperty('api', false);
      expect(status.services).toHaveProperty('database', false);
      expect(status.services).toHaveProperty('websocket', false);
    });

    it('should return system status with metrics', async () => {
      vi.mocked(mockPrisma.$queryRaw).mockResolvedValue([]);
      
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for metrics collection
      await new Promise(resolve => setTimeout(resolve, 150));
      
      const status = await service.getSystemStatus();
      
      expect(status.status).not.toBe('unknown');
      expect(status.services.api).toBe(true);
      expect(status).toHaveProperty('averageResponseTime');
      expect(status).toHaveProperty('currentMetrics');
    });
  });

  describe('getHistoricalMetrics', () => {
    it('should return empty array with no metrics', () => {
      const metrics = service.getHistoricalMetrics('1h');
      expect(metrics).toEqual([]);
    });

    it('should filter metrics by time range', async () => {
      vi.mocked(mockPrisma.$queryRaw).mockResolvedValue([]);
      
      await service.startMonitoring({ intervalMs: 50 });
      
      // Wait for multiple metrics collections
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const metrics = service.getHistoricalMetrics('5m');
      expect(Array.isArray(metrics)).toBe(true);
      
      if (metrics.length > 0) {
        // All metrics should be within the last 5 minutes
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
        metrics.forEach(metric => {
          expect(metric.timestamp.getTime()).toBeGreaterThan(fiveMinutesAgo.getTime());
        });
      }
    });
  });

  describe('health status determination', () => {
    it('should determine healthy status', () => {
      // This is testing internal logic, so we'll create a service instance
      // and access its private methods through any casting for testing
      const testService = service as any;
      
      const status = testService.determineHealthStatus({
        cpuPercent: 30,
        memoryPercent: 40,
        responseTime: 100,
        errorRate: 1,
      });
      
      expect(status).toBe('healthy');
    });

    it('should determine warning status', () => {
      const testService = service as any;
      
      const status = testService.determineHealthStatus({
        cpuPercent: 75,
        memoryPercent: 60,
        responseTime: 500,
        errorRate: 3,
      });
      
      expect(status).toBe('warning');
    });

    it('should determine critical status', () => {
      const testService = service as any;
      
      const status = testService.determineHealthStatus({
        cpuPercent: 95,
        memoryPercent: 95,
        responseTime: 3000,
        errorRate: 25,
      });
      
      expect(status).toBe('critical');
    });

    it('should determine down status for failed response time', () => {
      const testService = service as any;
      
      const status = testService.determineHealthStatus({
        cpuPercent: 30,
        memoryPercent: 40,
        responseTime: -1, // Indicates failure
        errorRate: 1,
      });
      
      expect(status).toBe('down');
    });
  });

  describe('metrics storage', () => {
    it('should store metrics in database', async () => {
      vi.mocked(mockPrisma.systemMetrics.create).mockResolvedValue({} as any);
      vi.mocked(mockPrisma.$queryRaw).mockResolvedValue([]);
      
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for metrics collection
      await new Promise(resolve => setTimeout(resolve, 150));
      
      expect(mockPrisma.systemMetrics.create).toHaveBeenCalled();
    });

    it('should handle database storage failures gracefully', async () => {
      vi.mocked(mockPrisma.systemMetrics.create).mockRejectedValue(new Error('Storage error'));
      vi.mocked(mockPrisma.$queryRaw).mockResolvedValue([]);
      
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for metrics collection
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Should log error but not throw
      expect(consoleSpy).toHaveBeenCalledWith('Failed to store system metrics:', expect.any(Error));
      
      consoleSpy.mockRestore();
    });
  });

  describe('clearMetrics', () => {
    it('should clear stored metrics', async () => {
      vi.mocked(mockPrisma.$queryRaw).mockResolvedValue([]);
      
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for metrics collection
      await new Promise(resolve => setTimeout(resolve, 150));
      
      // Should have some metrics
      let status = service.getMonitoringStatus();
      expect(status.metricsCount).toBeGreaterThan(0);
      
      service.clearMetrics();
      
      // Should have no metrics
      status = service.getMonitoringStatus();
      expect(status.metricsCount).toBe(0);
    });
  });

  describe('error handling', () => {
    it('should emit error event on monitoring failure', async () => {
      const errorSpy = vi.fn();
      service.on('error', errorSpy);
      
      // Mock a failure in metrics collection
      const originalMemoryUsage = process.memoryUsage;
      process.memoryUsage = vi.fn().mockImplementation(() => {
        throw new Error('Memory access failed');
      });
      
      await service.startMonitoring({ intervalMs: 100 });
      
      // Wait for error to occur
      await new Promise(resolve => setTimeout(resolve, 150));
      
      process.memoryUsage = originalMemoryUsage;
      
      expect(errorSpy).toHaveBeenCalled();
    });
  });
});