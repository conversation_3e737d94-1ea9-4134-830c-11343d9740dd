/**
 * Portfolio Risk Analyzer Test Suite
 * 
 * Tests for correlation analysis, VaR calculation, concentration risk monitoring,
 * and portfolio risk metrics calculation.
 * 
 * @version 1.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { PortfolioRiskAnalyzer, Position, HistoricalData } from './PortfolioRiskAnalyzer';

describe('PortfolioRiskAnalyzer', () => {
  let analyzer: PortfolioRiskAnalyzer;
  let mockPositions: Position[];

  beforeEach(() => {
    analyzer = new PortfolioRiskAnalyzer();
    
    mockPositions = [
      {
        id: 'pos-1',
        symbol: 'EURUSD',
        size: new Decimal(10000),
        entryPrice: new Decimal(1.1000),
        currentPrice: new Decimal(1.1050),
        unrealizedPnL: new Decimal(50),
        weight: 40,
        sector: 'Major Pairs',
        assetClass: 'forex'
      },
      {
        id: 'pos-2',
        symbol: 'GBPUSD',
        size: new Decimal(8000),
        entryPrice: new Decimal(1.2500),
        currentPrice: new Decimal(1.2480),
        unrealizedPnL: new Decimal(-16),
        weight: 35,
        sector: 'Major Pairs',
        assetClass: 'forex'
      },
      {
        id: 'pos-3',
        symbol: 'XAUUSD',
        size: new Decimal(100),
        entryPrice: new Decimal(2000),
        currentPrice: new Decimal(2020),
        unrealizedPnL: new Decimal(2000),
        weight: 25,
        sector: 'Precious Metals',
        assetClass: 'commodity'
      }
    ];
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Portfolio Risk Analysis', () => {
    it('should analyze portfolio risk metrics correctly', () => {
      const metrics = analyzer.analyzePortfolioRisk(mockPositions);

      expect(metrics.totalValue.gt(0)).toBe(true);
      expect(metrics.totalUnrealizedPnL.equals(new Decimal(2034))).toBe(true);
      expect(metrics.valueAtRisk95).toBeInstanceOf(Decimal);
      expect(metrics.valueAtRisk99).toBeInstanceOf(Decimal);
      expect(metrics.portfolioVolatility).toBeTypeOf('number');
      expect(metrics.correlationRisk).toBeGreaterThanOrEqual(0);
      expect(metrics.concentrationRisk).toBeGreaterThanOrEqual(0);
      expect(metrics.diversificationRatio).toBeGreaterThan(0);
    });

    it('should handle empty portfolio', () => {
      const metrics = analyzer.analyzePortfolioRisk([]);

      expect(metrics.totalValue.equals(new Decimal(0))).toBe(true);
      expect(metrics.totalUnrealizedPnL.equals(new Decimal(0))).toBe(true);
      expect(metrics.valueAtRisk95.equals(new Decimal(0))).toBe(true);
      expect(metrics.portfolioVolatility).toBe(0);
      expect(metrics.correlationRisk).toBe(0);
      expect(metrics.concentrationRisk).toBe(0);
      expect(metrics.diversificationRatio).toBe(1);
    });

    it('should emit risk analysis complete event', () => {
      const eventSpy = vi.fn();
      analyzer.on('riskAnalysisComplete', eventSpy);

      analyzer.analyzePortfolioRisk(mockPositions);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          metrics: expect.any(Object),
          positions: 3,
          alerts: expect.any(Number)
        })
      );
    });

    it('should calculate total portfolio value correctly', () => {
      const metrics = analyzer.analyzePortfolioRisk(mockPositions);
      const expectedValue = new Decimal(10000).mul(new Decimal(1.1050))
        .add(new Decimal(8000).mul(new Decimal(1.2480)))
        .add(new Decimal(100).mul(new Decimal(2020)));

      expect(metrics.totalValue.equals(expectedValue)).toBe(true);
    });

    it('should calculate unrealized PnL correctly', () => {
      const metrics = analyzer.analyzePortfolioRisk(mockPositions);
      const expectedPnL = new Decimal(50).add(new Decimal(-16)).add(new Decimal(2000));

      expect(metrics.totalUnrealizedPnL.equals(expectedPnL)).toBe(true);
    });
  });

  describe('Correlation Matrix Calculation', () => {
    beforeEach(() => {
      // Add historical data for correlation calculations
      const eurusdData: HistoricalData[] = Array.from({ length: 50 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        symbol: 'EURUSD',
        price: new Decimal(1.1000 + Math.random() * 0.01),
        returns: (Math.random() - 0.5) * 0.02
      }));

      const gbpusdData: HistoricalData[] = Array.from({ length: 50 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        symbol: 'GBPUSD',
        price: new Decimal(1.2500 + Math.random() * 0.01),
        returns: (Math.random() - 0.5) * 0.02
      }));

      analyzer.updateHistoricalData('EURUSD', eurusdData);
      analyzer.updateHistoricalData('GBPUSD', gbpusdData);
    });

    it('should calculate correlation matrix', () => {
      const matrix = analyzer.calculateCorrelationMatrix(mockPositions);

      expect(matrix).toHaveLength(3);
      expect(matrix[0]).toHaveLength(3);
      expect(matrix[0][0]).toBe(1); // Self-correlation
      expect(matrix[1][1]).toBe(1);
      expect(matrix[2][2]).toBe(1);
      expect(matrix[0][1]).toBeGreaterThanOrEqual(-1);
      expect(matrix[0][1]).toBeLessThanOrEqual(1);
    });

    it('should handle single position correlation', () => {
      const singlePosition = [mockPositions[0]];
      const matrix = analyzer.calculateCorrelationMatrix(singlePosition);

      expect(matrix).toHaveLength(1);
      expect(matrix[0][0]).toBe(1);
    });

    it('should use default correlation for insufficient data', () => {
      const positions = [
        { ...mockPositions[0], symbol: 'UNKNOWN1' },
        { ...mockPositions[1], symbol: 'UNKNOWN2' }
      ];
      
      const matrix = analyzer.calculateCorrelationMatrix(positions);
      
      expect(matrix[0][1]).toBe(0.3); // Default moderate correlation
    });
  });

  describe('Value at Risk Calculation', () => {
    it('should calculate VaR metrics', () => {
      const varMetrics = analyzer.calculateValueAtRisk(mockPositions);

      expect(varMetrics.var95).toBeInstanceOf(Decimal);
      expect(varMetrics.var99).toBeInstanceOf(Decimal);
      expect(varMetrics.expectedShortfall).toBeInstanceOf(Decimal);
      expect(varMetrics.var99.lte(varMetrics.var95)).toBe(true); // 99% VaR should be more conservative
    });

    it('should handle empty positions for VaR', () => {
      const varMetrics = analyzer.calculateValueAtRisk([]);

      expect(varMetrics.var95.equals(new Decimal(0))).toBe(true);
      expect(varMetrics.var99.equals(new Decimal(0))).toBe(true);
      expect(varMetrics.expectedShortfall.equals(new Decimal(0))).toBe(true);
    });

    it('should use Monte Carlo simulation when historical data unavailable', () => {
      const varMetrics = analyzer.calculateValueAtRisk(mockPositions);

      // Should return reasonable VaR values even without historical data
      expect(varMetrics.var95.abs().gte(0)).toBe(true);
      expect(varMetrics.var99.abs().gte(0)).toBe(true);
    });

    it('should calculate expected shortfall beyond VaR', () => {
      const varMetrics = analyzer.calculateValueAtRisk(mockPositions);

      // Expected shortfall should be at least as large as VaR
      expect(varMetrics.expectedShortfall.abs().gte(varMetrics.var95.abs())).toBe(true);
    });
  });

  describe('Concentration Risk Analysis', () => {
    it('should analyze concentration risk', () => {
      const analysis = analyzer.analyzeConcentrationRisk(mockPositions);

      expect(analysis.concentrationScore).toBeGreaterThanOrEqual(0);
      expect(analysis.concentrationScore).toBeLessThanOrEqual(1);
      expect(analysis.violations).toBeInstanceOf(Array);
      expect(analysis.sectorWeights).toBeInstanceOf(Map);
      expect(analysis.assetClassWeights).toBeInstanceOf(Map);
    });

    it('should detect single position concentration violations', () => {
      const highConcentrationPositions = [
        {
          ...mockPositions[0],
          size: new Decimal(100000), // Very large position
          currentPrice: new Decimal(1.1050)
        }
      ];

      const analysis = analyzer.analyzeConcentrationRisk(highConcentrationPositions);

      expect(analysis.violations.length).toBeGreaterThan(0);
      expect(analysis.violations.some(v => v.includes('Single position exceeds'))).toBe(true);
    });

    it('should calculate sector weights correctly', () => {
      const analysis = analyzer.analyzeConcentrationRisk(mockPositions);

      expect(analysis.sectorWeights.has('Major Pairs')).toBe(true);
      expect(analysis.sectorWeights.has('Precious Metals')).toBe(true);
      
      const majorPairsWeight = analysis.sectorWeights.get('Major Pairs')!;
      expect(majorPairsWeight).toBeGreaterThan(0);
    });

    it('should calculate asset class weights correctly', () => {
      const analysis = analyzer.analyzeConcentrationRisk(mockPositions);

      expect(analysis.assetClassWeights.has('forex')).toBe(true);
      expect(analysis.assetClassWeights.has('commodity')).toBe(true);
      
      const forexWeight = analysis.assetClassWeights.get('forex')!;
      expect(forexWeight).toBeGreaterThan(0);
    });

    it('should handle empty positions for concentration analysis', () => {
      const analysis = analyzer.analyzeConcentrationRisk([]);

      expect(analysis.concentrationScore).toBe(0);
      expect(analysis.violations).toHaveLength(0);
      expect(analysis.sectorWeights.size).toBe(0);
      expect(analysis.assetClassWeights.size).toBe(0);
    });
  });

  describe('Historical Data Management', () => {
    it('should update historical data and emit event', () => {
      const eventSpy = vi.fn();
      analyzer.on('historicalDataUpdated', eventSpy);

      const historicalData: HistoricalData[] = [
        {
          date: new Date(),
          symbol: 'EURUSD',
          price: new Decimal(1.1000),
          returns: 0.001
        }
      ];

      analyzer.updateHistoricalData('EURUSD', historicalData);

      expect(eventSpy).toHaveBeenCalledWith({
        symbol: 'EURUSD',
        dataPoints: 1
      });
    });

    it('should handle multiple historical data updates', () => {
      const data1: HistoricalData[] = Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        symbol: 'EURUSD',
        price: new Decimal(1.1000),
        returns: 0.001
      }));

      const data2: HistoricalData[] = Array.from({ length: 25 }, (_, i) => ({
        date: new Date(Date.now() - i * 24 * 60 * 60 * 1000),
        symbol: 'GBPUSD',
        price: new Decimal(1.2500),
        returns: 0.002
      }));

      analyzer.updateHistoricalData('EURUSD', data1);
      analyzer.updateHistoricalData('GBPUSD', data2);

      // Should affect correlation calculations
      const matrix = analyzer.calculateCorrelationMatrix([mockPositions[0], mockPositions[1]]);
      expect(matrix).toHaveLength(2);
    });
  });

  describe('Risk Alerts Management', () => {
    it('should generate risk alerts for high concentration', () => {
      const highRiskPositions = [
        {
          ...mockPositions[0],
          size: new Decimal(500000), // Very high concentration
          unrealizedPnL: new Decimal(-50000) // Large loss
        }
      ];

      analyzer.analyzePortfolioRisk(highRiskPositions);
      const alerts = analyzer.getRiskAlerts();

      expect(alerts.length).toBeGreaterThan(0);
      expect(alerts.some(alert => alert.type === 'concentration')).toBe(true);
    });

    it('should generate drawdown alerts', () => {
      const drawdownPositions = mockPositions.map(pos => ({
        ...pos,
        unrealizedPnL: pos.size.mul(pos.currentPrice).mul(-0.15) // 15% drawdown
      }));

      analyzer.analyzePortfolioRisk(drawdownPositions);
      const alerts = analyzer.getRiskAlerts();

      expect(alerts.some(alert => alert.type === 'drawdown')).toBe(true);
    });

    it('should emit risk alerts generated event', () => {
      const eventSpy = vi.fn();
      analyzer.on('riskAlertsGenerated', eventSpy);

      const highRiskPositions = [
        {
          ...mockPositions[0],
          size: new Decimal(500000),
          unrealizedPnL: new Decimal(-75000)
        }
      ];

      analyzer.analyzePortfolioRisk(highRiskPositions);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          alerts: expect.any(Array),
          highSeverityCount: expect.any(Number)
        })
      );
    });

    it('should clear risk alerts', () => {
      // Generate some alerts first
      const highRiskPositions = [
        {
          ...mockPositions[0],
          size: new Decimal(500000),
          unrealizedPnL: new Decimal(-50000)
        }
      ];

      analyzer.analyzePortfolioRisk(highRiskPositions);
      expect(analyzer.getRiskAlerts().length).toBeGreaterThan(0);

      analyzer.clearAlerts();
      expect(analyzer.getRiskAlerts()).toHaveLength(0);
    });

    it('should clear specific alert types', () => {
      const highRiskPositions = [
        {
          ...mockPositions[0],
          size: new Decimal(500000),
          unrealizedPnL: new Decimal(-50000)
        }
      ];

      analyzer.analyzePortfolioRisk(highRiskPositions);
      const initialAlerts = analyzer.getRiskAlerts();
      
      analyzer.clearAlerts(['concentration']);
      const remainingAlerts = analyzer.getRiskAlerts();

      expect(remainingAlerts.length).toBeLessThanOrEqual(initialAlerts.length);
      expect(remainingAlerts.every(alert => alert.type !== 'concentration')).toBe(true);
    });

    it('should emit alerts cleared event', () => {
      const eventSpy = vi.fn();
      analyzer.on('alertsCleared', eventSpy);

      analyzer.clearAlerts(['concentration']);

      expect(eventSpy).toHaveBeenCalledWith({
        clearedTypes: ['concentration']
      });
    });
  });

  describe('Risk Metrics Calculation', () => {
    it('should calculate portfolio volatility', () => {
      const matrix = [[1, 0.5], [0.5, 1]];
      const positions = [mockPositions[0], mockPositions[1]];
      
      const volatility = analyzer.calculatePortfolioVolatility(positions, matrix);

      expect(volatility).toBeGreaterThanOrEqual(0);
      expect(volatility).toBeLessThanOrEqual(1);
    });

    it('should calculate correlation risk', () => {
      const matrix = [
        [1.0, 0.8, 0.3],
        [0.8, 1.0, 0.2],
        [0.3, 0.2, 1.0]
      ];

      const metrics = analyzer.analyzePortfolioRisk(mockPositions);
      expect(metrics.correlationRisk).toBeGreaterThanOrEqual(0);
      expect(metrics.correlationRisk).toBeLessThanOrEqual(1);
    });

    it('should calculate diversification ratio', () => {
      const matrix = [
        [1.0, 0.2, 0.1],
        [0.2, 1.0, 0.3],
        [0.1, 0.3, 1.0]
      ];

      const diversificationRatio = analyzer.calculateDiversificationRatio(mockPositions, matrix);

      expect(diversificationRatio).toBeGreaterThanOrEqual(1);
    });

    it('should calculate risk contributions', () => {
      const matrix = [
        [1.0, 0.3, 0.2],
        [0.3, 1.0, 0.1],
        [0.2, 0.1, 1.0]
      ];

      const contributions = analyzer.calculateRiskContributions(mockPositions, matrix);

      expect(contributions.size).toBe(3);
      for (const [symbol, contribution] of contributions) {
        expect(mockPositions.some(pos => pos.symbol === symbol)).toBe(true);
        expect(contribution).toBeGreaterThanOrEqual(0);
      }
    });
  });

  describe('Last Analysis Tracking', () => {
    it('should store and retrieve last analysis', () => {
      expect(analyzer.getLastAnalysis()).toBeNull();

      const metrics = analyzer.analyzePortfolioRisk(mockPositions);
      const lastAnalysis = analyzer.getLastAnalysis();

      expect(lastAnalysis).not.toBeNull();
      expect(lastAnalysis?.totalValue.equals(metrics.totalValue)).toBe(true);
    });

    it('should update last analysis on new calculation', () => {
      const firstMetrics = analyzer.analyzePortfolioRisk(mockPositions);
      const firstLastAnalysis = analyzer.getLastAnalysis();

      const modifiedPositions = [...mockPositions];
      modifiedPositions[0].currentPrice = new Decimal(1.2000);

      const secondMetrics = analyzer.analyzePortfolioRisk(modifiedPositions);
      const secondLastAnalysis = analyzer.getLastAnalysis();

      expect(secondLastAnalysis?.totalValue.equals(firstLastAnalysis?.totalValue)).toBe(false);
    });
  });

  describe('Configuration Options', () => {
    it('should accept custom concentration limits', () => {
      const customLimits = {
        maxSinglePositionWeight: 5,
        maxSectorWeight: 15,
        maxAssetClassWeight: 30,
        maxCorrelationThreshold: 0.6
      };

      const customAnalyzer = new PortfolioRiskAnalyzer(customLimits);
      const analysis = customAnalyzer.analyzeConcentrationRisk(mockPositions);

      // With stricter limits, should have more violations
      expect(analysis.violations.length).toBeGreaterThanOrEqual(0);
    });

    it('should accept custom VaR parameters', () => {
      const customVarParams = {
        confidenceLevel: 0.99,
        historicalPeriod: 100,
        simulationCount: 5000,
        holdingPeriod: 5
      };

      const customAnalyzer = new PortfolioRiskAnalyzer(undefined, customVarParams);
      const varMetrics = customAnalyzer.calculateValueAtRisk(mockPositions);

      expect(varMetrics.var99).toBeInstanceOf(Decimal);
      expect(varMetrics.var95).toBeInstanceOf(Decimal);
    });
  });

  describe('Error Handling', () => {
    it('should handle positions with zero prices gracefully', () => {
      const zeroPositions = [
        {
          ...mockPositions[0],
          currentPrice: new Decimal(0)
        }
      ];

      expect(() => analyzer.analyzePortfolioRisk(zeroPositions)).not.toThrow();
      const metrics = analyzer.analyzePortfolioRisk(zeroPositions);
      expect(metrics).toBeDefined();
    });

    it('should handle positions with zero sizes gracefully', () => {
      const zeroSizePositions = [
        {
          ...mockPositions[0],
          size: new Decimal(0)
        }
      ];

      expect(() => analyzer.analyzePortfolioRisk(zeroSizePositions)).not.toThrow();
      const metrics = analyzer.analyzePortfolioRisk(zeroSizePositions);
      expect(metrics).toBeDefined();
    });

    it('should handle invalid correlation matrix gracefully', () => {
      const invalidMatrix: number[][] = [];

      expect(() => analyzer.calculateCorrelationRisk(invalidMatrix)).not.toThrow();
      const risk = analyzer.calculateCorrelationRisk(invalidMatrix);
      expect(risk).toBe(0);
    });
  });
});