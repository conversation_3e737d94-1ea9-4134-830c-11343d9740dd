/**
 * Emergency Liquidation Engine Test Suite
 * 
 * Tests for automatic position liquidation, emergency response triggers,
 * multi-broker execution, and intelligent order sequencing.
 * 
 * @version 1.0
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import Decimal from 'decimal.js';
import { EmergencyLiquidationEngine } from './EmergencyLiquidationEngine';
import { Position, MarketCondition, LiquidationTrigger } from '../../types/trading';

describe('EmergencyLiquidationEngine', () => {
  let engine: EmergencyLiquidationEngine;
  let mockPositions: Position[];
  let mockMarketConditions: MarketCondition[];
  let mockBrokerAdapter: any;

  beforeEach(() => {
    engine = new EmergencyLiquidationEngine({
      volatilityThreshold: 2.0,
      liquidityThreshold: 0.4,
      maxPortfolioDrawdown: 0.10,
      emergencyVarThreshold: 0.08,
      maxConcurrentLiquidations: 5,
      defaultSlippageTolerance: 0.015,
      liquidationTimeout: 20000,
      cooldownPeriod: 180000
    });

    mockPositions = [
      {
        id: 'pos-1',
        accountId: 'acc-1',
        userId: 'user-1',
        symbol: 'EURUSD',
        size: new Decimal(50000),
        entryPrice: new Decimal(1.1000),
        currentPrice: new Decimal(1.0950),
        unrealizedPnl: new Decimal(-250),
        realizedPnl: new Decimal(0),
        marginUsed: new Decimal(1000),
        side: 'long',
        riskScore: 85,
        priorityLevel: 'high',
        timestamp: new Date()
      },
      {
        id: 'pos-2',
        accountId: 'acc-1',
        userId: 'user-1',
        symbol: 'GBPUSD',
        size: new Decimal(30000),
        entryPrice: new Decimal(1.2500),
        currentPrice: new Decimal(1.2400),
        unrealizedPnl: new Decimal(-300),
        realizedPnl: new Decimal(0),
        marginUsed: new Decimal(750),
        side: 'long',
        riskScore: 95,
        priorityLevel: 'critical',
        timestamp: new Date()
      },
      {
        id: 'pos-3',
        accountId: 'acc-2',
        userId: 'user-2',
        symbol: 'XAUUSD',
        size: new Decimal(10),
        entryPrice: new Decimal(2000),
        currentPrice: new Decimal(1980),
        unrealizedPnl: new Decimal(-200),
        realizedPnl: new Decimal(0),
        marginUsed: new Decimal(4000),
        side: 'long',
        riskScore: 60,
        priorityLevel: 'medium',
        timestamp: new Date()
      }
    ];

    mockMarketConditions = [
      {
        symbol: 'EURUSD',
        timestamp: new Date(),
        stressLevel: 0.9,
        volatilityChange: 2.5,
        liquidityScore: 0.3,
        volumeChange: -0.4,
        correlationBreakdown: true,
        volatility: 2.5,
        liquidity: 0.3,
        spreadPercentage: 0.05,
        priceChange24h: -0.08
      },
      {
        symbol: 'GBPUSD',
        timestamp: new Date(),
        stressLevel: 0.95,
        volatilityChange: 3.0,
        liquidityScore: 0.25,
        volumeChange: -0.5,
        correlationBreakdown: true,
        volatility: 3.0,
        liquidity: 0.25,
        spreadPercentage: 0.08,
        priceChange24h: -0.12
      },
      {
        symbol: 'XAUUSD',
        timestamp: new Date(),
        stressLevel: 0.7,
        volatilityChange: 1.8,
        liquidityScore: 0.6,
        volumeChange: -0.2,
        correlationBreakdown: false,
        volatility: 1.8,
        liquidity: 0.6,
        spreadPercentage: 0.02,
        priceChange24h: -0.04
      }
    ];

    mockBrokerAdapter = {
      placeOrder: vi.fn().mockResolvedValue({ success: true, orderId: 'order-123' }),
      cancelOrder: vi.fn().mockResolvedValue(true),
      healthCheck: vi.fn().mockResolvedValue({ isHealthy: true, latency: 50 })
    };

    engine.registerBroker('broker-1', mockBrokerAdapter);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Initialization and Configuration', () => {
    it('should initialize with default configuration', () => {
      const defaultEngine = new EmergencyLiquidationEngine();
      const state = defaultEngine.getEmergencyState();
      
      expect(state.isActive).toBe(false);
      expect(state.level).toBe('none');
      expect(state.activeTriggers).toHaveLength(0);
    });

    it('should accept custom configuration', () => {
      const customConfig = {
        volatilityThreshold: 5.0,
        liquidationTimeout: 60000,
        maxConcurrentLiquidations: 10
      };

      const customEngine = new EmergencyLiquidationEngine(customConfig);
      expect(customEngine).toBeDefined();
    });

    it('should register broker adapters', () => {
      const eventSpy = vi.fn();
      engine.on('brokerRegistered', eventSpy);

      engine.registerBroker('broker-2', mockBrokerAdapter);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          brokerId: 'broker-2'
        })
      );
    });
  });

  describe('Stress Trigger Processing', () => {
    it('should process high severity trigger and initiate liquidation', async () => {
      const trigger: LiquidationTrigger = {
        type: 'volatility_spike',
        severity: 'high',
        affectedSymbols: ['EURUSD', 'GBPUSD'],
        triggerValue: 3.0,
        threshold: 2.0,
        description: 'Severe volatility spike detected',
        timestamp: new Date()
      };

      const eventSpy = vi.fn();
      engine.on('emergencyLiquidationInitiated', eventSpy);

      await engine.processStressTrigger(trigger, mockPositions, mockMarketConditions);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          trigger,
          positionsCount: expect.any(Number),
          ordersCount: expect.any(Number)
        })
      );

      const state = engine.getEmergencyState();
      expect(state.isActive).toBe(true);
      expect(state.level).not.toBe('none');
    });

    it('should reject low severity triggers without critical conditions', async () => {
      const lowTrigger: LiquidationTrigger = {
        type: 'volatility_spike',
        severity: 'low',
        affectedSymbols: ['XAUUSD'],
        triggerValue: 1.2,
        threshold: 2.0,
        description: 'Minor volatility increase',
        timestamp: new Date()
      };

      const eventSpy = vi.fn();
      engine.on('triggerEvaluated', eventSpy);

      await engine.processStressTrigger(lowTrigger, [mockPositions[2]], [mockMarketConditions[2]]);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          trigger: lowTrigger,
          shouldLiquidate: false,
          reason: 'conditions_not_met'
        })
      );

      const state = engine.getEmergencyState();
      expect(state.isActive).toBe(false);
    });

    it('should respect cooldown period', async () => {
      // First trigger
      const trigger: LiquidationTrigger = {
        type: 'manual_override',
        severity: 'critical',
        affectedSymbols: ['EURUSD'],
        triggerValue: 1.0,
        threshold: 1.0,
        description: 'Manual emergency stop',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, [mockPositions[0]], [mockMarketConditions[0]]);
      
      // Cancel to trigger cooldown
      await engine.cancelEmergencyLiquidation();

      // Second trigger should be blocked
      const eventSpy = vi.fn();
      engine.on('liquidationBlocked', eventSpy);

      await engine.processStressTrigger(trigger, [mockPositions[0]], [mockMarketConditions[0]]);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          reason: 'cooldown_active',
          trigger
        })
      );
    });

    it('should handle processing errors gracefully', async () => {
      const invalidTrigger = {
        type: 'volatility_spike',
        severity: 'high',
        affectedSymbols: null, // Invalid data
        triggerValue: 3.0,
        threshold: 2.0,
        description: 'Invalid trigger data',
        timestamp: new Date()
      } as any;

      const errorSpy = vi.fn();
      engine.on('liquidationError', errorSpy);

      await expect(engine.processStressTrigger(invalidTrigger, mockPositions, mockMarketConditions))
        .resolves.toBeUndefined(); // Should not throw

      expect(errorSpy).toHaveBeenCalled();
    });
  });

  describe('Manual Liquidation', () => {
    it('should trigger manual liquidation successfully', async () => {
      const eventSpy = vi.fn();
      engine.on('emergencyLiquidationInitiated', eventSpy);

      await engine.triggerManualLiquidation('user-1', mockPositions, undefined, 'Risk management override');

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          trigger: expect.objectContaining({
            type: 'manual_override',
            description: 'Risk management override'
          })
        })
      );

      const state = engine.getEmergencyState();
      expect(state.isActive).toBe(true);
    });

    it('should use default reason for manual liquidation', async () => {
      const eventSpy = vi.fn();
      engine.on('emergencyLiquidationInitiated', eventSpy);

      await engine.triggerManualLiquidation('user-1', [mockPositions[0]]);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          trigger: expect.objectContaining({
            description: 'Manual emergency liquidation triggered'
          })
        })
      );
    });

    it('should handle empty positions list', async () => {
      await expect(engine.triggerManualLiquidation('user-1', []))
        .resolves.toBeUndefined();

      const state = engine.getEmergencyState();
      expect(state.isActive).toBe(true); // Still activates but with no positions
    });
  });

  describe('Emergency State Management', () => {
    it('should activate emergency state correctly', async () => {
      const trigger: LiquidationTrigger = {
        type: 'volatility_spike',
        severity: 'critical',
        affectedSymbols: ['EURUSD'],
        triggerValue: 4.0,
        threshold: 2.0,
        description: 'Critical volatility spike',
        timestamp: new Date()
      };

      const activationSpy = vi.fn();
      engine.on('emergencyStateActivated', activationSpy);

      await engine.processStressTrigger(trigger, [mockPositions[0]], [mockMarketConditions[0]]);

      const state = engine.getEmergencyState();
      expect(state.isActive).toBe(true);
      expect(state.level).toBe('panic');
      expect(state.activeTriggers).toHaveLength(1);
      expect(state.affectedPositions).toContain('pos-1');
      expect(state.startTime).toBeDefined();

      expect(activationSpy).toHaveBeenCalled();
    });

    it('should deactivate emergency state after completion', async () => {
      const trigger: LiquidationTrigger = {
        type: 'manual_override',
        severity: 'high',
        affectedSymbols: ['EURUSD'],
        triggerValue: 1.0,
        threshold: 1.0,
        description: 'Test liquidation',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, [mockPositions[0]], [mockMarketConditions[0]]);
      
      const deactivationSpy = vi.fn();
      engine.on('emergencyStateDeactivated', deactivationSpy);

      // Simulate completion by updating all orders to filled
      const activeLiquidations = engine.getActiveLiquidations();
      for (const order of activeLiquidations) {
        engine.addLiquidationResult({
          orderId: order.id,
          positionId: order.positionId,
          symbol: order.symbol,
          liquidatedSize: order.size,
          averagePrice: new Decimal(100),
          slippage: 0.01,
          executionTime: 1000,
          success: true,
          brokerUsed: 'broker-1',
          timestamp: new Date()
        });
      }

      // Wait for deactivation (reduced from 6000ms to match our implementation)
      await new Promise(resolve => setTimeout(resolve, 200));

      expect(deactivationSpy).toHaveBeenCalled();
    });

    it('should map trigger severity to emergency level correctly', async () => {
      const severityMappings = [
        { severity: 'low' as const, expectedLevel: 'watch' },
        { severity: 'medium' as const, expectedLevel: 'warning' },
        { severity: 'high' as const, expectedLevel: 'emergency' },
        { severity: 'critical' as const, expectedLevel: 'panic' }
      ];

      for (const mapping of severityMappings) {
        const trigger: LiquidationTrigger = {
          type: 'manual_override',
          severity: mapping.severity,
          affectedSymbols: ['EURUSD'],
          triggerValue: mapping.severity === 'critical' ? 5.0 : 3.0,
          threshold: 2.0,
          description: `Test ${mapping.severity} trigger`,
          timestamp: new Date()
        };

        await engine.processStressTrigger(trigger, [mockPositions[0]], [mockMarketConditions[0]]);

        const state = engine.getEmergencyState();
        if (state.isActive) {
          expect(state.level).toBe(mapping.expectedLevel);
        }

        // Reset for next test
        await engine.cancelEmergencyLiquidation();
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    });
  });

  describe('Order Management', () => {
    it('should track active liquidations', async () => {
      const trigger: LiquidationTrigger = {
        type: 'volatility_spike',
        severity: 'high',
        affectedSymbols: ['EURUSD', 'GBPUSD'],
        triggerValue: 3.0,
        threshold: 2.0,
        description: 'Test liquidation',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, mockPositions.slice(0, 2), mockMarketConditions.slice(0, 2));

      const activeLiquidations = engine.getActiveLiquidations();
      expect(activeLiquidations.length).toBeGreaterThan(0);
      
      activeLiquidations.forEach(order => {
        expect(order.status).toMatch(/pending|submitted/);
        expect(['pos-1', 'pos-2']).toContain(order.positionId);
      });
    });

    it('should update order status correctly', async () => {
      const trigger: LiquidationTrigger = {
        type: 'manual_override',
        severity: 'high',
        affectedSymbols: ['EURUSD'],
        triggerValue: 1.0,
        threshold: 1.0,
        description: 'Test status update',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, [mockPositions[0]], [mockMarketConditions[0]]);

      const activeLiquidations = engine.getActiveLiquidations();
      const orderId = activeLiquidations[0].id;

      const eventSpy = vi.fn();
      engine.on('orderStatusUpdated', eventSpy);

      engine.updateOrderStatus(orderId, 'partially_filled');

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          orderId,
          status: 'partially_filled'
        })
      );

      engine.updateOrderStatus(orderId, 'filled');

      const state = engine.getEmergencyState();
      expect(state.liquidationProgress.liquidated).toBe(1);
      expect(state.liquidationProgress.inProgress).toBe(0);
    });

    it('should handle liquidation results', async () => {
      const trigger: LiquidationTrigger = {
        type: 'manual_override',
        severity: 'medium',
        affectedSymbols: ['XAUUSD'],
        triggerValue: 1.0,
        threshold: 1.0,
        description: 'Test liquidation result',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, [mockPositions[2]], [mockMarketConditions[2]]);

      const activeLiquidations = engine.getActiveLiquidations();
      const order = activeLiquidations[0];

      const eventSpy = vi.fn();
      engine.on('liquidationCompleted', eventSpy);

      const result = {
        orderId: order.id,
        positionId: order.positionId,
        symbol: order.symbol,
        liquidatedSize: order.size,
        averagePrice: new Decimal(1980),
        slippage: 0.005,
        executionTime: 1500,
        success: true,
        brokerUsed: 'broker-1',
        timestamp: new Date()
      };

      engine.addLiquidationResult(result);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({ result })
      );

      const history = engine.getLiquidationHistory();
      expect(history).toContainEqual(result);
    });

    it('should track liquidation progress', async () => {
      const trigger: LiquidationTrigger = {
        type: 'volatility_spike',
        severity: 'high',
        affectedSymbols: ['EURUSD', 'GBPUSD', 'XAUUSD'],
        triggerValue: 3.0,
        threshold: 2.0,
        description: 'Multi-position liquidation test',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, mockPositions, mockMarketConditions);

      const initialState = engine.getEmergencyState();
      expect(initialState.liquidationProgress.totalPositions).toBe(mockPositions.length);
      expect(initialState.liquidationProgress.inProgress).toBeGreaterThan(0);

      // Simulate some completions
      const activeLiquidations = engine.getActiveLiquidations();
      for (let i = 0; i < Math.min(2, activeLiquidations.length); i++) {
        const order = activeLiquidations[i];
        engine.addLiquidationResult({
          orderId: order.id,
          positionId: order.positionId,
          symbol: order.symbol,
          liquidatedSize: order.size,
          averagePrice: new Decimal(100),
          slippage: 0.01,
          executionTime: 1000,
          success: true,
          brokerUsed: 'broker-1',
          timestamp: new Date()
        });
      }

      const updatedState = engine.getEmergencyState();
      expect(updatedState.liquidationProgress.liquidated).toBe(2);
    });
  });

  describe('Cancellation and Error Handling', () => {
    it('should cancel emergency liquidation successfully', async () => {
      const trigger: LiquidationTrigger = {
        type: 'manual_override',
        severity: 'high',
        affectedSymbols: ['EURUSD'],
        triggerValue: 1.0,
        threshold: 1.0,
        description: 'Test cancellation',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, [mockPositions[0]], [mockMarketConditions[0]]);

      expect(engine.getEmergencyState().isActive).toBe(true);

      const eventSpy = vi.fn();
      engine.on('emergencyLiquidationCancelled', eventSpy);

      const cancelled = await engine.cancelEmergencyLiquidation('User requested cancellation');

      expect(cancelled).toBe(true);
      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          reason: 'User requested cancellation'
        })
      );

      expect(engine.getEmergencyState().isActive).toBe(false);
    });

    it('should handle cancellation when not active', async () => {
      const cancelled = await engine.cancelEmergencyLiquidation();
      expect(cancelled).toBe(false);
    });

    it('should handle failed liquidations', async () => {
      const trigger: LiquidationTrigger = {
        type: 'manual_override',
        severity: 'high',
        affectedSymbols: ['EURUSD'],
        triggerValue: 1.0,
        threshold: 1.0,
        description: 'Test failure handling',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, [mockPositions[0]], [mockMarketConditions[0]]);

      const activeLiquidations = engine.getActiveLiquidations();
      const order = activeLiquidations[0];

      // Add failed result
      const failedResult = {
        orderId: order.id,
        positionId: order.positionId,
        symbol: order.symbol,
        liquidatedSize: new Decimal(0),
        averagePrice: new Decimal(0),
        slippage: 0,
        executionTime: 1000,
        success: false,
        errorMessage: 'Broker connection failed',
        brokerUsed: 'broker-1',
        timestamp: new Date()
      };

      engine.addLiquidationResult(failedResult);

      const state = engine.getEmergencyState();
      expect(state.liquidationProgress.failed).toBe(1);
    });
  });

  describe('Strategy and Prioritization', () => {
    it('should prioritize high-risk positions', async () => {
      const trigger: LiquidationTrigger = {
        type: 'volatility_spike',
        severity: 'high',
        affectedSymbols: ['EURUSD', 'GBPUSD', 'XAUUSD'],
        triggerValue: 3.0,
        threshold: 2.0,
        description: 'Risk prioritization test',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, mockPositions, mockMarketConditions);

      const activeLiquidations = engine.getActiveLiquidations();
      
      // Critical priority position (GBPUSD) should be processed first
      const criticalOrders = activeLiquidations.filter(order => 
        order.positionId === 'pos-2' // GBPUSD with critical priority
      );
      
      const highOrders = activeLiquidations.filter(order => 
        order.positionId === 'pos-1' // EURUSD with high priority
      );

      expect(criticalOrders.length).toBeGreaterThan(0);
      expect(highOrders.length).toBeGreaterThan(0);
    });

    it('should handle different liquidation strategies', async () => {
      const strategies = ['immediate', 'staged', 'partial'] as const;
      
      for (const strategyType of strategies) {
        const trigger: LiquidationTrigger = {
          type: 'manual_override',
          severity: strategyType === 'immediate' ? 'critical' : 'high',
          affectedSymbols: ['EURUSD'],
          triggerValue: 1.0,
          threshold: 1.0,
          description: `Test ${strategyType} strategy`,
          timestamp: new Date()
        };

        await engine.processStressTrigger(trigger, [mockPositions[0]], [mockMarketConditions[0]]);

        const state = engine.getEmergencyState();
        expect(state.isActive).toBe(true);

        // Cancel for next test
        await engine.cancelEmergencyLiquidation();
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    });

    it('should adapt strategy based on market conditions', async () => {
      // Test with poor liquidity conditions
      const poorLiquidityConditions = mockMarketConditions.map(cond => ({
        ...cond,
        liquidity: 0.1, // Very poor liquidity
        stressLevel: 'extreme' as const
      }));

      const trigger: LiquidationTrigger = {
        type: 'liquidity_crisis',
        severity: 'high',
        affectedSymbols: ['EURUSD'],
        triggerValue: 0.1,
        threshold: 0.3,
        description: 'Liquidity crisis adaptation test',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, [mockPositions[0]], poorLiquidityConditions);

      const state = engine.getEmergencyState();
      expect(state.isActive).toBe(true);
    });
  });

  describe('Event Emission', () => {
    it('should emit emergency liquidation initiated event', async () => {
      const eventSpy = vi.fn();
      engine.on('emergencyLiquidationInitiated', eventSpy);

      const trigger: LiquidationTrigger = {
        type: 'volatility_spike',
        severity: 'high',
        affectedSymbols: ['EURUSD'],
        triggerValue: 3.0,
        threshold: 2.0,
        description: 'Event emission test',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, [mockPositions[0]], [mockMarketConditions[0]]);

      expect(eventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          trigger,
          strategy: expect.any(Object),
          positionsCount: expect.any(Number),
          ordersCount: expect.any(Number)
        })
      );
    });

    it('should emit emergency liquidation completed event', async () => {
      const completionSpy = vi.fn();
      engine.on('emergencyLiquidationCompleted', completionSpy);

      const trigger: LiquidationTrigger = {
        type: 'manual_override',
        severity: 'high',
        affectedSymbols: ['XAUUSD'],
        triggerValue: 1.0,
        threshold: 1.0,
        description: 'Completion test',
        timestamp: new Date()
      };

      await engine.processStressTrigger(trigger, [mockPositions[2]], [mockMarketConditions[2]]);

      // Complete all orders
      const activeLiquidations = engine.getActiveLiquidations();
      for (const order of activeLiquidations) {
        engine.addLiquidationResult({
          orderId: order.id,
          positionId: order.positionId,
          symbol: order.symbol,
          liquidatedSize: order.size,
          averagePrice: new Decimal(100),
          slippage: 0.01,
          executionTime: 1000,
          success: true,
          brokerUsed: 'broker-1',
          timestamp: new Date()
        });
      }

      await new Promise(resolve => setTimeout(resolve, 200));

      expect(completionSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          progress: expect.any(Object),
          successRate: expect.any(Number)
        })
      );
    });
  });
});