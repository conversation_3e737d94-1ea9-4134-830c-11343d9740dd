import { describe, it, expect, beforeEach, vi } from 'vitest';
import { QuizContentService } from '../QuizContentService.js';
import {
  QuizCategory,
  QuizDifficulty,
  ConfidenceStage,
  type QuizQuestion
} from '@golddaddy/types';

// Mock PrismaClient
const mockPrisma = {
  quizQuestion: {
    findMany: vi.fn(),
    create: vi.fn(),
    update: vi.fn(),
  }
};

describe('QuizContentService', () => {
  let service: QuizContentService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new QuizContentService(mockPrisma as any);
  });

  describe('getQuestionsByFilter', () => {
    it('should filter questions by category and difficulty', async () => {
      const mockQuestions = [
        {
          id: 'q1',
          category: QuizCategory.TRADING_FUNDAMENTALS,
          difficulty: QuizDifficulty.BEGINNER,
          topic: 'Risk Management',
          question: 'Test question',
          options: [],
          correctAnswerIds: ['opt1'],
          explanation: 'Test explanation',
          learningResources: [],
          metadata: { estimatedDuration: 30, tags: [], riskLevel: 'low' },
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      mockPrisma.quizQuestion.findMany.mockResolvedValue(mockQuestions);

      const result = await service.getQuestionsByFilter({
        category: QuizCategory.TRADING_FUNDAMENTALS,
        difficulty: QuizDifficulty.BEGINNER,
        limit: 10
      });

      expect(mockPrisma.quizQuestion.findMany).toHaveBeenCalledWith({
        where: {
          isActive: true,
          category: QuizCategory.TRADING_FUNDAMENTALS,
          difficulty: QuizDifficulty.BEGINNER
        },
        take: 10,
        orderBy: [
          { category: 'asc' },
          { difficulty: 'asc' },
          { topic: 'asc' }
        ]
      });

      expect(result).toHaveLength(1);
      expect(result[0].category).toBe(QuizCategory.TRADING_FUNDAMENTALS);
    });
  });

  describe('generateQuizForStage', () => {
    it('should generate appropriate quiz for goal setting stage', async () => {
      const mockQuestions = Array.from({ length: 20 }, (_, i) => ({
        id: `q${i}`,
        category: i < 10 ? QuizCategory.TRADING_FUNDAMENTALS : QuizCategory.RISK_MANAGEMENT,
        difficulty: QuizDifficulty.BEGINNER,
        topic: `Topic ${i}`,
        question: `Question ${i}`,
        options: [
          { id: 'opt1', text: 'Option 1', isCorrect: true },
          { id: 'opt2', text: 'Option 2', isCorrect: false }
        ],
        correctAnswerIds: ['opt1'],
        explanation: `Explanation ${i}`,
        learningResources: [],
        metadata: { estimatedDuration: 30, tags: [], riskLevel: 'low' },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      mockPrisma.quizQuestion.findMany.mockResolvedValue(mockQuestions);

      const result = await service.generateQuizForStage(
        ConfidenceStage.GOAL_SETTING,
        'beginner'
      );

      expect(result.length).toBe(15); // Expected question count for goal setting
      expect(result.every(q => q.difficulty === QuizDifficulty.BEGINNER)).toBe(true);
      
      // Check category distribution
      const categories = result.map(q => q.category);
      expect(categories).toContain(QuizCategory.TRADING_FUNDAMENTALS);
      expect(categories).toContain(QuizCategory.RISK_MANAGEMENT);
    });

    it('should adjust questions for advanced users', async () => {
      const mockQuestions = Array.from({ length: 30 }, (_, i) => ({
        id: `q${i}`,
        category: QuizCategory.TRADING_FUNDAMENTALS,
        difficulty: i < 15 ? QuizDifficulty.ADVANCED : QuizDifficulty.EXPERT,
        topic: `Topic ${i}`,
        question: `Question ${i}`,
        options: [],
        correctAnswerIds: ['opt1'],
        explanation: `Explanation ${i}`,
        learningResources: [],
        metadata: { estimatedDuration: 60, tags: [], riskLevel: 'medium' },
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }));

      mockPrisma.quizQuestion.findMany.mockResolvedValue(mockQuestions);

      const result = await service.generateQuizForStage(
        ConfidenceStage.LIVE_READY,
        'advanced'
      );

      expect(result.every(q => 
        q.difficulty === QuizDifficulty.ADVANCED || 
        q.difficulty === QuizDifficulty.EXPERT
      )).toBe(true);
    });
  });

  describe('validateQuizContent', () => {
    it('should identify insufficient category coverage', async () => {
      const questions: QuizQuestion[] = [
        {
          id: 'q1',
          category: QuizCategory.TRADING_FUNDAMENTALS,
          difficulty: QuizDifficulty.BEGINNER,
          topic: 'Test',
          question: 'Test question',
          options: [
            { id: 'opt1', text: 'Option 1', isCorrect: true },
            { id: 'opt2', text: 'Option 2', isCorrect: false }
          ],
          correctAnswerIds: ['opt1'],
          explanation: 'Test explanation with sufficient detail to meet minimum requirements',
          learningResources: [],
          metadata: { estimatedDuration: 30, tags: [], riskLevel: 'low' },
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const result = await service.validateQuizContent(questions);

      expect(result.isValid).toBe(false);
      expect(result.issues.some(issue => issue.includes('Insufficient coverage'))).toBe(true);
    });

    it('should validate question quality requirements', async () => {
      const invalidQuestion: QuizQuestion = {
        id: 'q1',
        category: QuizCategory.TRADING_FUNDAMENTALS,
        difficulty: QuizDifficulty.BEGINNER,
        topic: 'Test',
        question: 'Test question',
        options: [{ id: 'opt1', text: 'Only option', isCorrect: true }], // Insufficient options
        correctAnswerIds: [],
        explanation: 'Short', // Too short explanation
        learningResources: [],
        metadata: { estimatedDuration: 30, tags: [], riskLevel: 'low' },
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const result = await service.validateQuizContent([invalidQuestion]);

      expect(result.isValid).toBe(false);
      expect(result.issues).toContain('Question q1 has no correct answers');
      expect(result.recommendations.some(rec => rec.includes('explanation could be more detailed'))).toBe(true);
    });
  });

  describe('getQuizComplexityProgression', () => {
    it('should recommend appropriate difficulty progression', async () => {
      const result = await service.getQuizComplexityProgression(
        ConfidenceStage.STRATEGY_LEARNING,
        { averageScore: 88, attemptCount: 3 }
      );

      expect(result.recommendedDifficulty).toBe(QuizDifficulty.INTERMEDIATE);
      expect(result.questionCount).toBe(20);
      expect(result.focusAreas).toContain(QuizCategory.TRADING_FUNDAMENTALS);
      expect(result.focusAreas).toContain(QuizCategory.MARKET_ANALYSIS);
    });

    it('should recommend beginner difficulty for poor performance', async () => {
      const result = await service.getQuizComplexityProgression(
        ConfidenceStage.PAPER_TRADING,
        { averageScore: 55, attemptCount: 2 }
      );

      expect(result.recommendedDifficulty).toBe(QuizDifficulty.BEGINNER);
    });
  });

  describe('createQuizQuestion', () => {
    it('should create valid quiz question', async () => {
      const questionData: Partial<QuizQuestion> = {
        category: QuizCategory.RISK_MANAGEMENT,
        difficulty: QuizDifficulty.INTERMEDIATE,
        topic: 'Position Sizing',
        question: 'What is the Kelly Criterion used for?',
        options: [
          { id: 'opt1', text: 'Calculating optimal position size', isCorrect: true },
          { id: 'opt2', text: 'Determining market direction', isCorrect: false },
          { id: 'opt3', text: 'Setting stop losses', isCorrect: false },
          { id: 'opt4', text: 'Analyzing market volatility', isCorrect: false }
        ],
        correctAnswerIds: ['opt1'],
        explanation: 'The Kelly Criterion is a mathematical formula used to determine the optimal position size for a bet or trade based on the probability of winning and the odds.',
        metadata: {
          estimatedDuration: 45,
          tags: ['kelly-criterion', 'position-sizing', 'mathematics'],
          riskLevel: 'medium'
        }
      };

      const mockCreated = { id: 'new-q1', ...questionData };
      mockPrisma.quizQuestion.create.mockResolvedValue(mockCreated);

      const result = await service.createQuizQuestion(questionData);

      expect(mockPrisma.quizQuestion.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          category: QuizCategory.RISK_MANAGEMENT,
          difficulty: QuizDifficulty.INTERMEDIATE,
          isActive: true
        })
      });

      expect(result.id).toBe('new-q1');
    });
  });
});