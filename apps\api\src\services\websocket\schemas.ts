/**
 * WebSocket Message Validation Schemas
 * 
 * Comprehensive Zod schemas for WebSocket message validation
 * with security-focused input sanitization and financial data protection
 */

import { z } from 'zod';
import { DataSource, TimeFrame } from '../market-data/RealTimeDataProcessor';

// ===== Base Validation Rules =====

// Sanitize string inputs to prevent XSS and injection attacks
const sanitizeString = (str: string): string => {
  return str
    .replace(/[<>]/g, '') // Remove HTML brackets
    .replace(/['"]/g, '') // Remove quotes
    .replace(/[&]/g, '') // Remove ampersand
    .trim()
    .slice(0, 100); // Limit length
};

// Create sanitized string schema
const sanitizedString = z.string().transform(sanitizeString);

// Financial instrument validation (alphanumeric + common symbols only)
const instrumentSchema = z.string()
  .regex(/^[A-Z0-9/_-]+$/, 'Invalid instrument format')
  .min(3, 'Instrument must be at least 3 characters')
  .max(20, 'Instrument must not exceed 20 characters')
  .transform(str => str.toUpperCase());

// ===== Core Message Schemas =====

/**
 * Base message schema with common validation
 */
export const baseMessageSchema = z.object({
  type: sanitizedString,
  timestamp: z.date().optional().default(() => new Date()),
  requestId: z.string().uuid().optional(),
});

/**
 * Subscription message validation
 */
export const subscribeMessageSchema = baseMessageSchema.extend({
  type: z.literal('subscribe').or(z.literal('unsubscribe')),
  action: z.enum(['subscribe', 'unsubscribe']),
  instruments: z.array(instrumentSchema)
    .min(1, 'At least one instrument required')
    .max(50, 'Maximum 50 instruments allowed per request')
    .refine(
      (instruments) => new Set(instruments).size === instruments.length,
      'Duplicate instruments not allowed'
    ),
  features: z.object({
    marketAnalysis: z.boolean().optional(),
    mlPredictions: z.boolean().optional(),
    volatilityIndicators: z.boolean().optional(),
    technicalIndicators: z.boolean().optional(),
  }).optional(),
  timeframes: z.array(z.nativeEnum(TimeFrame))
    .max(5, 'Maximum 5 timeframes allowed')
    .optional(),
  updateInterval: z.number()
    .int()
    .min(100, 'Minimum update interval is 100ms')
    .max(60000, 'Maximum update interval is 60 seconds')
    .optional(),
});

/**
 * Heartbeat message validation
 */
export const heartbeatMessageSchema = baseMessageSchema.extend({
  type: z.literal('heartbeat').or(z.literal('heartbeat_ack')),
});

/**
 * Ping message validation
 */
export const pingMessageSchema = baseMessageSchema.extend({
  type: z.literal('ping'),
});

/**
 * Authentication message validation (for token refresh)
 */
export const authMessageSchema = baseMessageSchema.extend({
  type: z.literal('auth'),
  token: z.string()
    .min(10, 'Token too short')
    .max(2000, 'Token too long')
    .regex(/^[A-Za-z0-9._-]+$/, 'Invalid token format'),
});

/**
 * Configuration update message validation
 */
export const configMessageSchema = baseMessageSchema.extend({
  type: z.literal('config'),
  maxUpdatesPerSecond: z.number()
    .int()
    .min(1)
    .max(100)
    .optional(),
  compressionEnabled: z.boolean().optional(),
  batchingEnabled: z.boolean().optional(),
});

/**
 * Error message validation (for client error reports)
 */
export const errorReportSchema = baseMessageSchema.extend({
  type: z.literal('error_report'),
  errorCode: sanitizedString,
  errorMessage: sanitizedString,
  context: z.record(z.string()).optional(),
});

// ===== Union Schema for All Messages =====

/**
 * Master schema that validates all incoming WebSocket messages
 */
export const incomingMessageSchema = z.union([
  subscribeMessageSchema,
  heartbeatMessageSchema,
  pingMessageSchema,
  authMessageSchema,
  configMessageSchema,
  errorReportSchema,
]);

// ===== Outgoing Message Schemas =====

/**
 * Price stream message validation (outgoing)
 */
export const priceStreamMessageSchema = z.object({
  type: z.literal('price_update'),
  instrument: instrumentSchema,
  price: z.object({
    bid: z.number().finite().positive().optional(),
    ask: z.number().finite().positive().optional(),
    close: z.number().finite().positive(),
    open: z.number().finite().positive().optional(),
    high: z.number().finite().positive().optional(),
    low: z.number().finite().positive().optional(),
    spread: z.number().finite().nonnegative().optional(),
  }),
  timestamp: z.date(),
  source: z.nativeEnum(DataSource),
  timeframe: z.nativeEnum(TimeFrame).optional(),
  volume: z.number().finite().nonnegative().optional(),
  marketAnalysis: z.object({
    trend: z.enum(['bullish', 'bearish', 'neutral']).optional(),
    volatility: z.number().finite().nonnegative().optional(),
    mood: z.enum(['positive', 'negative', 'neutral']).optional(),
    mlPrediction: z.object({
      direction: z.enum(['up', 'down', 'sideways']),
      confidence: z.number().min(0).max(100),
      timeHorizon: z.string().max(10),
    }).optional(),
  }).optional(),
});

/**
 * Connection acknowledgment message validation (outgoing)
 */
export const connectionAckMessageSchema = z.object({
  type: z.literal('connection_ack'),
  clientId: z.string(),
  serverCapabilities: z.object({
    maxInstruments: z.number().int().positive(),
    supportedFeatures: z.array(z.string()),
    updateInterval: z.number().int().positive(),
  }),
  timestamp: z.date(),
});

/**
 * Error message validation (outgoing)
 */
export const errorMessageSchema = z.object({
  type: z.literal('error'),
  code: z.string(),
  message: sanitizedString,
  timestamp: z.date(),
  requestId: z.string().uuid().optional(),
});

/**
 * Subscription confirmation message validation (outgoing)
 */
export const subscriptionConfirmationSchema = z.object({
  type: z.literal('subscription_confirmed'),
  instruments: z.array(instrumentSchema),
  features: z.record(z.boolean()),
  timestamp: z.date(),
  requestId: z.string().uuid().optional(),
});

// ===== Rate Limiting Schemas =====

/**
 * Rate limit information schema
 */
export const rateLimitInfoSchema = z.object({
  type: z.literal('rate_limit_info'),
  remainingRequests: z.number().int().nonnegative(),
  resetTime: z.date(),
  windowSize: z.number().int().positive(),
});

// ===== Security Event Schemas =====

/**
 * Security alert message schema (internal)
 */
export const securityAlertSchema = z.object({
  type: z.literal('security_alert'),
  alertType: z.enum([
    'rate_limit_exceeded',
    'invalid_token',
    'suspicious_activity',
    'unauthorized_access',
    'malformed_request',
  ]),
  clientId: z.string(),
  timestamp: z.date(),
  details: z.record(z.any()).optional(),
});

// ===== Type Exports =====

export type SubscribeMessage = z.infer<typeof subscribeMessageSchema>;
export type HeartbeatMessage = z.infer<typeof heartbeatMessageSchema>;
export type PingMessage = z.infer<typeof pingMessageSchema>;
export type AuthMessage = z.infer<typeof authMessageSchema>;
export type ConfigMessage = z.infer<typeof configMessageSchema>;
export type ErrorReportMessage = z.infer<typeof errorReportSchema>;
export type IncomingMessage = z.infer<typeof incomingMessageSchema>;

export type PriceStreamMessage = z.infer<typeof priceStreamMessageSchema>;
export type ConnectionAckMessage = z.infer<typeof connectionAckMessageSchema>;
export type ErrorMessage = z.infer<typeof errorMessageSchema>;
export type SubscriptionConfirmation = z.infer<typeof subscriptionConfirmationSchema>;
export type RateLimitInfo = z.infer<typeof rateLimitInfoSchema>;
export type SecurityAlert = z.infer<typeof securityAlertSchema>;

// ===== Validation Utilities =====

/**
 * Validate incoming WebSocket message with detailed error reporting
 */
export const validateIncomingMessage = (
  data: unknown
): { success: true; data: IncomingMessage } | { success: false; error: string; details?: any } => {
  try {
    const result = incomingMessageSchema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: 'Message validation failed',
        details: error.errors?.map(err => ({
          path: err.path.join('.'),
          message: err.message,
        })) || [],
      };
    }
    return {
      success: false,
      error: 'Unknown validation error',
    };
  }
};

/**
 * Validate outgoing message before sending
 */
export const validateOutgoingMessage = (
  data: unknown,
  schema: z.ZodSchema
): { success: true; data: any } | { success: false; error: string } => {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    return {
      success: false,
      error: error instanceof z.ZodError 
        ? `Outgoing message validation failed: ${error.errors[0]?.message}`
        : 'Unknown validation error',
    };
  }
};

// ===== Security Validation Rules =====

/**
 * Additional security checks for subscription requests
 */
export const validateSubscriptionSecurity = (
  message: SubscribeMessage,
  userPermissions: string[],
  currentSubscriptions: Set<string>
): { valid: true } | { valid: false; reason: string } => {
  
  // Check if user has permission for market data
  if (!userPermissions.includes('market_data_access')) {
    return { valid: false, reason: 'Insufficient permissions for market data access' };
  }

  // Check for rate limiting based on current subscriptions
  if (message.action === 'subscribe' && 
      currentSubscriptions.size + message.instruments.length > 50) {
    return { valid: false, reason: 'Maximum subscription limit exceeded' };
  }

  // Check for premium features
  const premiumFeatures = ['mlPredictions', 'technicalIndicators'];
  if (message.features) {
    for (const feature of premiumFeatures) {
      if (message.features[feature as keyof typeof message.features] && 
          !userPermissions.includes('premium_features')) {
        return { valid: false, reason: `Premium feature '${feature}' requires subscription` };
      }
    }
  }

  // Validate update intervals based on user tier
  if (message.updateInterval && message.updateInterval < 1000 && 
      !userPermissions.includes('high_frequency_data')) {
    return { valid: false, reason: 'High frequency data access requires premium subscription' };
  }

  return { valid: true };
};

/**
 * Validate financial instrument access permissions
 */
export const validateInstrumentAccess = (
  instruments: string[],
  userRegion: string = 'US',
  userPermissions: string[] = []
): { valid: true } | { valid: false; reason: string; restrictedInstruments: string[] } => {
  
  const restrictedInstruments: string[] = [];
  
  for (const instrument of instruments) {
    // Check for region-specific restrictions
    if (instrument.startsWith('CN') && userRegion !== 'CN') {
      restrictedInstruments.push(instrument);
    }
    
    // Check for special instrument permissions
    if (instrument.includes('CRYPTO') && !userPermissions.includes('crypto_access')) {
      restrictedInstruments.push(instrument);
    }
    
    if (instrument.includes('FOREX') && !userPermissions.includes('forex_access')) {
      restrictedInstruments.push(instrument);
    }
  }
  
  if (restrictedInstruments.length > 0) {
    return { 
      valid: false, 
      reason: 'Access denied to restricted instruments',
      restrictedInstruments 
    };
  }
  
  return { valid: true };
};