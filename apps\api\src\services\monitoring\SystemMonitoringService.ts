import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { performance } from 'perf_hooks';
import os from 'os';
import process from 'process';

/**
 * System Monitoring Service for collecting and tracking system performance metrics
 * Implements real-time monitoring with automated alerting capabilities
 */
export class SystemMonitoringService extends EventEmitter {
  private prisma: PrismaClient;
  private metrics: SystemMetrics[] = [];
  private isMonitoring = false;
  private monitoringIntervals: NodeJS.Timeout[] = [];
  private readonly MONITORING_INTERVAL = 30000; // 30 seconds
  private readonly METRICS_RETENTION_LIMIT = 1000; // Keep last 1000 metrics

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  /**
   * Start system monitoring with configurable intervals
   */
  async startMonitoring(config: MonitoringConfiguration = {}): Promise<void> {
    if (this.isMonitoring) {
      console.warn('System monitoring already active');
      return;
    }

    console.log('🔍 Starting system performance monitoring...');
    this.isMonitoring = true;

    const interval = config.intervalMs || this.MONITORING_INTERVAL;

    // Start periodic metrics collection
    const metricsInterval = setInterval(async () => {
      try {
        const systemMetrics = await this.collectSystemMetrics();
        await this.storeMetrics(systemMetrics);
        await this.checkAlertThresholds(systemMetrics);
        this.emit('metricsCollected', systemMetrics);
      } catch (error) {
        console.error('Error collecting system metrics:', error);
        this.emit('error', error);
      }
    }, interval);

    this.monitoringIntervals.push(metricsInterval);

    // Start database monitoring
    const dbInterval = setInterval(async () => {
      try {
        const dbMetrics = await this.collectDatabaseMetrics();
        await this.storeDatabaseMetrics(dbMetrics);
        this.emit('databaseMetricsCollected', dbMetrics);
      } catch (error) {
        console.error('Error collecting database metrics:', error);
        this.emit('error', error);
      }
    }, interval * 2); // Database metrics every minute

    this.monitoringIntervals.push(dbInterval);

    console.log('✅ System monitoring started successfully');
    this.emit('monitoringStarted', { interval });
  }

  /**
   * Stop system monitoring
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ Stopping system monitoring...');
    this.isMonitoring = false;

    // Clear all monitoring intervals
    this.monitoringIntervals.forEach(interval => clearInterval(interval));
    this.monitoringIntervals = [];

    console.log('✅ System monitoring stopped');
    this.emit('monitoringStopped');
  }

  /**
   * Collect comprehensive system performance metrics
   */
  private async collectSystemMetrics(): Promise<SystemMetrics> {
    const timestamp = new Date();
    const startTime = performance.now();

    // Collect system information
    const cpuUsage = process.cpuUsage();
    const memoryUsage = process.memoryUsage();
    const systemLoad = os.loadavg();
    const uptime = process.uptime();

    // Calculate CPU percentage (approximation)
    const cpuPercent = this.calculateCpuPercentage(cpuUsage);

    // Calculate memory percentage
    const memoryPercent = (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100;

    // Test API response time
    const responseTime = await this.measureApiResponseTime();

    // Calculate error rate from recent metrics
    const errorRate = await this.calculateErrorRate();

    // Determine health status
    const healthStatus = this.determineHealthStatus({
      cpuPercent,
      memoryPercent,
      responseTime,
      errorRate,
    });

    const metrics: SystemMetrics = {
      id: `metrics_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp,
      serviceType: 'api',
      metrics: {
        uptime,
        errorRate,
        responseTime,
        throughput: await this.calculateThroughput(),
        memoryUsage: memoryPercent,
        cpuUsage: cpuPercent,
      },
      healthStatus,
      alerts: [],
    };

    // Add to in-memory storage
    this.metrics.push(metrics);
    
    // Keep only recent metrics
    if (this.metrics.length > this.METRICS_RETENTION_LIMIT) {
      this.metrics = this.metrics.slice(-this.METRICS_RETENTION_LIMIT);
    }

    const collectionTime = performance.now() - startTime;
    console.log(`📊 System metrics collected in ${collectionTime.toFixed(2)}ms`);

    return metrics;
  }

  /**
   * Calculate CPU percentage (simplified approach)
   */
  private calculateCpuPercentage(cpuUsage: NodeJS.CpuUsage): number {
    // This is a simplified calculation
    // In production, you'd want a more sophisticated approach
    const totalCpu = cpuUsage.user + cpuUsage.system;
    return Math.min((totalCpu / 1000000) * 100, 100); // Convert microseconds to percentage
  }

  /**
   * Measure API response time by making internal health check
   */
  private async measureApiResponseTime(): Promise<number> {
    const startTime = performance.now();
    
    try {
      // Simulate internal API health check
      await this.prisma.$queryRaw`SELECT 1`;
      const endTime = performance.now();
      return endTime - startTime;
    } catch (error) {
      console.warn('API response time measurement failed:', error);
      return -1;
    }
  }

  /**
   * Calculate current error rate based on recent metrics
   */
  private async calculateErrorRate(): Promise<number> {
    const recentMetrics = this.metrics.slice(-10); // Last 10 metrics
    if (recentMetrics.length === 0) return 0;

    const errorCount = recentMetrics.filter(m => 
      m.healthStatus === 'critical' || m.healthStatus === 'warning'
    ).length;

    return (errorCount / recentMetrics.length) * 100;
  }

  /**
   * Calculate system throughput (requests per minute approximation)
   */
  private async calculateThroughput(): Promise<number> {
    // In a real implementation, this would track actual requests
    // For now, we'll return a simulated value based on system load
    const load = os.loadavg()[0];
    return Math.max(0, 1000 - (load * 100)); // Inverse relationship with load
  }

  /**
   * Determine overall health status based on metrics
   */
  private determineHealthStatus(metrics: {
    cpuPercent: number;
    memoryPercent: number;
    responseTime: number;
    errorRate: number;
  }): 'healthy' | 'warning' | 'critical' | 'down' {
    const { cpuPercent, memoryPercent, responseTime, errorRate } = metrics;

    // System is down if response time indicates failure
    if (responseTime < 0) {
      return 'down';
    }

    // Critical thresholds
    if (cpuPercent > 90 || memoryPercent > 90 || responseTime > 5000 || errorRate > 50) {
      return 'critical';
    }

    // Warning thresholds
    if (cpuPercent > 70 || memoryPercent > 70 || responseTime > 1000 || errorRate > 10) {
      return 'warning';
    }

    return 'healthy';
  }

  /**
   * Check alert thresholds and generate alerts if needed
   */
  private async checkAlertThresholds(metrics: SystemMetrics): Promise<void> {
    const alerts: SystemAlert[] = [];

    // CPU usage alert
    if (metrics.metrics.cpuUsage > 80) {
      alerts.push({
        alertType: 'cpu_usage',
        severity: metrics.metrics.cpuUsage > 90 ? 'critical' : 'high',
        message: `High CPU usage: ${metrics.metrics.cpuUsage.toFixed(1)}%`,
        triggeredAt: new Date(),
      });
    }

    // Memory usage alert
    if (metrics.metrics.memoryUsage > 80) {
      alerts.push({
        alertType: 'memory_usage',
        severity: metrics.metrics.memoryUsage > 90 ? 'critical' : 'high',
        message: `High memory usage: ${metrics.metrics.memoryUsage.toFixed(1)}%`,
        triggeredAt: new Date(),
      });
    }

    // Response time alert
    if (metrics.metrics.responseTime > 1000) {
      alerts.push({
        alertType: 'response_time',
        severity: metrics.metrics.responseTime > 5000 ? 'critical' : 'medium',
        message: `Slow response time: ${metrics.metrics.responseTime.toFixed(0)}ms`,
        triggeredAt: new Date(),
      });
    }

    // Error rate alert
    if (metrics.metrics.errorRate > 5) {
      alerts.push({
        alertType: 'error_rate',
        severity: metrics.metrics.errorRate > 20 ? 'critical' : 'high',
        message: `High error rate: ${metrics.metrics.errorRate.toFixed(1)}%`,
        triggeredAt: new Date(),
      });
    }

    // Add alerts to metrics
    metrics.alerts = alerts;

    // Emit alerts
    for (const alert of alerts) {
      this.emit('alert', {
        ...alert,
        serviceType: metrics.serviceType,
        timestamp: metrics.timestamp,
      });
    }
  }

  /**
   * Store metrics in database
   */
  private async storeMetrics(metrics: SystemMetrics): Promise<void> {
    try {
      await this.prisma.systemMetrics.create({
        data: {
          id: metrics.id,
          timestamp: metrics.timestamp,
          serviceType: metrics.serviceType,
          metrics: metrics.metrics as any, // Prisma JSON type
          healthStatus: metrics.healthStatus,
          createdAt: new Date(),
        },
      });
    } catch (error) {
      console.error('Failed to store system metrics:', error);
      // Don't throw error to avoid disrupting monitoring
    }
  }

  /**
   * Collect database-specific metrics
   */
  private async collectDatabaseMetrics(): Promise<DatabaseMetrics> {
    const startTime = performance.now();

    try {
      // Test database connectivity and measure query performance
      const connectionTestStart = performance.now();
      await this.prisma.$queryRaw`SELECT 1`;
      const connectionTime = performance.now() - connectionTestStart;

      // Get database size information (PostgreSQL specific)
      const dbSizeResult = await this.prisma.$queryRaw<[{size_mb: number}]>`
        SELECT pg_size_pretty(pg_database_size(current_database()))::text as size_mb
      `;

      // Count active connections
      const activeConnections = await this.prisma.$queryRaw<[{count: number}]>`
        SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'
      `;

      const collectionTime = performance.now() - startTime;

      return {
        timestamp: new Date(),
        connectionTime,
        collectionTime,
        activeConnections: Number(activeConnections[0]?.count || 0),
        databaseSize: dbSizeResult[0]?.size_mb || 0,
        isHealthy: connectionTime < 1000, // Consider healthy if connection < 1s
      };
    } catch (error) {
      console.error('Database metrics collection failed:', error);
      return {
        timestamp: new Date(),
        connectionTime: -1,
        collectionTime: performance.now() - startTime,
        activeConnections: 0,
        databaseSize: 0,
        isHealthy: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Store database metrics
   */
  private async storeDatabaseMetrics(metrics: DatabaseMetrics): Promise<void> {
    try {
      // For now, we'll log database metrics
      // In production, you'd store these in a separate table
      console.log('📊 Database Metrics:', {
        connectionTime: `${metrics.connectionTime.toFixed(2)}ms`,
        activeConnections: metrics.activeConnections,
        isHealthy: metrics.isHealthy,
      });
    } catch (error) {
      console.error('Failed to store database metrics:', error);
    }
  }

  /**
   * Get current system status summary
   */
  async getSystemStatus(): Promise<SystemStatus> {
    const recentMetrics = this.metrics.slice(-5); // Last 5 metrics
    
    if (recentMetrics.length === 0) {
      return {
        status: 'unknown',
        uptime: process.uptime(),
        lastCheck: new Date(),
        services: {
          api: false,
          database: false,
          websocket: false,
        },
        alerts: [],
      };
    }

    const latest = recentMetrics[recentMetrics.length - 1];
    const avgResponseTime = recentMetrics.reduce((sum, m) => sum + m.metrics.responseTime, 0) / recentMetrics.length;

    return {
      status: latest.healthStatus,
      uptime: latest.metrics.uptime,
      lastCheck: latest.timestamp,
      averageResponseTime: avgResponseTime,
      services: {
        api: latest.metrics.responseTime > 0,
        database: true, // Assume true if we have metrics
        websocket: this.isMonitoring,
      },
      alerts: latest.alerts || [],
      currentMetrics: latest.metrics,
    };
  }

  /**
   * Get historical metrics for dashboard
   */
  getHistoricalMetrics(timeRange: TimeRange = '1h'): SystemMetrics[] {
    const now = new Date();
    const timeRangeMs = this.getTimeRangeMs(timeRange);
    const cutoff = new Date(now.getTime() - timeRangeMs);

    return this.metrics.filter(m => m.timestamp >= cutoff);
  }

  /**
   * Convert time range string to milliseconds
   */
  private getTimeRangeMs(range: TimeRange): number {
    switch (range) {
      case '5m': return 5 * 60 * 1000;
      case '15m': return 15 * 60 * 1000;
      case '1h': return 60 * 60 * 1000;
      case '6h': return 6 * 60 * 60 * 1000;
      case '24h': return 24 * 60 * 60 * 1000;
      case '7d': return 7 * 24 * 60 * 60 * 1000;
      default: return 60 * 60 * 1000; // Default to 1 hour
    }
  }

  /**
   * Get monitoring status
   */
  getMonitoringStatus(): MonitoringStatus {
    return {
      isActive: this.isMonitoring,
      intervalCount: this.monitoringIntervals.length,
      metricsCount: this.metrics.length,
      lastCollection: this.metrics.length > 0 ? this.metrics[this.metrics.length - 1].timestamp : null,
    };
  }

  /**
   * Clear stored metrics (useful for testing)
   */
  clearMetrics(): void {
    this.metrics = [];
    console.log('🧹 System metrics cleared');
  }
}

// Type definitions
interface SystemMetrics {
  id: string;
  timestamp: Date;
  serviceType: 'api' | 'mt5-bridge' | 'database' | 'websocket';
  metrics: {
    uptime: number; // seconds
    errorRate: number; // percentage
    responseTime: number; // milliseconds
    throughput: number; // requests per minute
    memoryUsage: number; // percentage
    cpuUsage: number; // percentage
  };
  healthStatus: 'healthy' | 'warning' | 'critical' | 'down';
  alerts?: SystemAlert[];
}

interface SystemAlert {
  alertType: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  triggeredAt: Date;
}

interface DatabaseMetrics {
  timestamp: Date;
  connectionTime: number;
  collectionTime: number;
  activeConnections: number;
  databaseSize: number;
  isHealthy: boolean;
  error?: string;
}

interface SystemStatus {
  status: 'healthy' | 'warning' | 'critical' | 'down' | 'unknown';
  uptime: number;
  lastCheck: Date;
  averageResponseTime?: number;
  services: {
    api: boolean;
    database: boolean;
    websocket: boolean;
  };
  alerts: SystemAlert[];
  currentMetrics?: SystemMetrics['metrics'];
}

interface MonitoringConfiguration {
  intervalMs?: number;
  enableAlerts?: boolean;
  alertThresholds?: {
    cpu?: number;
    memory?: number;
    responseTime?: number;
    errorRate?: number;
  };
}

interface MonitoringStatus {
  isActive: boolean;
  intervalCount: number;
  metricsCount: number;
  lastCollection: Date | null;
}

type TimeRange = '5m' | '15m' | '1h' | '6h' | '24h' | '7d';

export type {
  SystemMetrics,
  SystemAlert,
  DatabaseMetrics,
  SystemStatus,
  MonitoringConfiguration,
  MonitoringStatus,
  TimeRange,
};