/**
 * Regime WebSocket Service
 * 
 * Real-time regime detection WebSocket server that integrates with
 * MarketDataWebSocketServer to provide regime classification updates.
 */

import { EventEmitter } from 'events';
import WebSocket from 'ws';
import {
  MarketRegime,
  RegimeDetectionResult,
  RegimeStreamMessage,
  RegimeSubscription,
  TimeFrame,
  getTrendDirection,
  getVolatilityLevel,
} from '@golddaddy/types';
import { MarketRegimeDetector } from '../market-data/MarketRegimeDetector';
import { RegimeNotificationManager } from '../market-data/RegimeNotificationManager';
import { NormalizedMarketData } from '../market-data/RealTimeDataProcessor';

// Client connection info with regime-specific subscriptions
interface RegimeClientConnection {
  id: string;
  websocket: WebSocket;
  userId?: string;
  subscriptions: Set<string>; // instrument_timeframe pairs
  features: {
    regimeChanges: boolean;
    accuracyUpdates: boolean;
    recommendations: boolean;
    historicalContext: boolean;
  };
  lastUpdateSent: Date;
  updateInterval: number; // milliseconds
  rateLimitInfo: {
    messagesThisSecond: number;
    lastResetTime: number;
    maxMessagesPerSecond: number;
  };
}

// Regime update queue entry
interface RegimeUpdateQueueEntry {
  message: RegimeStreamMessage;
  targetClients: string[]; // client IDs
  priority: 'low' | 'medium' | 'high';
  timestamp: Date;
}

/**
 * Regime WebSocket Service for real-time regime detection streaming
 */
export class RegimeWebSocketService extends EventEmitter {
  private clients = new Map<string, RegimeClientConnection>();
  private regimeDetector: MarketRegimeDetector;
  private notificationManager: RegimeNotificationManager;
  private updateQueue: RegimeUpdateQueueEntry[] = [];
  private isProcessingQueue = false;

  // Configuration
  private config = {
    maxClientsPerUser: 5,
    maxSubscriptionsPerClient: 20,
    minUpdateInterval: 1000, // 1 second
    maxUpdateInterval: 60000, // 1 minute
    defaultUpdateInterval: 5000, // 5 seconds
    maxMessagesPerSecond: 10,
    queueProcessingInterval: 100, // milliseconds
    maxQueueSize: 1000,
  };

  // Statistics
  private stats = {
    totalClients: 0,
    activeSubscriptions: 0,
    messagesPerSecond: 0,
    regimeUpdatesProcessed: 0,
    lastStatsReset: Date.now(),
  };

  constructor(
    regimeDetector: MarketRegimeDetector,
    notificationManager: RegimeNotificationManager
  ) {
    super();
    this.regimeDetector = regimeDetector;
    this.notificationManager = notificationManager;

    // Set up event handlers
    this.setupEventHandlers();

    // Start queue processing
    this.startQueueProcessing();

    // Update statistics periodically
    setInterval(() => this.updateStatistics(), 10000); // Every 10 seconds
  }

  /**
   * Handle new WebSocket client connection
   */
  public handleConnection(websocket: WebSocket, clientId: string, userId?: string): void {
    // Check client limits
    if (userId) {
      const userConnections = Array.from(this.clients.values())
        .filter(c => c.userId === userId).length;
      
      if (userConnections >= this.config.maxClientsPerUser) {
        websocket.close(1008, 'Maximum connections per user exceeded');
        return;
      }
    }

    // Create client connection
    const client: RegimeClientConnection = {
      id: clientId,
      websocket,
      userId,
      subscriptions: new Set(),
      features: {
        regimeChanges: true,
        accuracyUpdates: false,
        recommendations: false,
        historicalContext: false,
      },
      lastUpdateSent: new Date(),
      updateInterval: this.config.defaultUpdateInterval,
      rateLimitInfo: {
        messagesThisSecond: 0,
        lastResetTime: Date.now(),
        maxMessagesPerSecond: this.config.maxMessagesPerSecond,
      },
    };

    this.clients.set(clientId, client);
    this.stats.totalClients++;

    // Set up WebSocket event handlers for this client
    this.setupClientHandlers(client);

    // Send welcome message
    this.sendToClient(client, {
      type: 'regime_connection_ack',
      clientId,
      serverCapabilities: {
        maxSubscriptions: this.config.maxSubscriptionsPerClient,
        minUpdateInterval: this.config.minUpdateInterval,
        maxUpdateInterval: this.config.maxUpdateInterval,
        supportedFeatures: ['regimeChanges', 'accuracyUpdates', 'recommendations', 'historicalContext'],
      },
      timestamp: new Date(),
    });

    this.emit('client_connected', { clientId, userId });
  }

  /**
   * Handle regime subscription request
   */
  public async handleRegimeSubscription(
    clientId: string,
    subscription: RegimeSubscription
  ): Promise<void> {
    const client = this.clients.get(clientId);
    if (!client) {
      this.emit('subscription_error', {
        clientId,
        error: 'Client not found',
      });
      return;
    }

    try {
      if (subscription.type === 'regime_subscribe') {
        await this.handleSubscribe(client, subscription);
      } else {
        await this.handleUnsubscribe(client, subscription);
      }
    } catch (error) {
      this.emit('subscription_error', {
        clientId,
        error: error instanceof Error ? error.message : 'Unknown error',
        subscription,
      });

      this.sendToClient(client, {
        type: 'error',
        code: 'SUBSCRIPTION_ERROR',
        message: 'Failed to process subscription',
        timestamp: new Date(),
      });
    }
  }

  /**
   * Process regime detection result and broadcast to subscribers
   */
  public async processRegimeUpdate(result: RegimeDetectionResult): Promise<void> {
    const subscriptionKey = `${result.instrument}_${result.timeframe}`;
    const subscribedClients = this.getClientsForSubscription(subscriptionKey);

    if (subscribedClients.length === 0) return;

    // Create regime stream message
    const message: RegimeStreamMessage = {
      type: 'regime_update',
      instrument: result.instrument,
      timeframe: result.timeframe,
      regime: result.regime,
      confidence: result.confidence,
      confidenceLevel: result.confidenceLevel,
      regimeChanged: result.regimeChangeDetected,
      previousRegime: result.previousRegime,
      changeSignificance: result.regimeChangeMagnitude,
      trendDirection: getTrendDirection(result.regime),
      volatilityLevel: getVolatilityLevel(result.regime),
      marketMood: this.getMarketMood(result),
      timestamp: result.timestamp,
    };

    // Add recommendations if feature is enabled
    for (const clientId of subscribedClients) {
      const client = this.clients.get(clientId);
      if (!client) continue;

      const clientMessage = { ...message };

      if (client.features.recommendations) {
        clientMessage.recommendations = this.generateRecommendations(result);
      }

      // Determine priority based on regime change
      const priority: 'low' | 'medium' | 'high' = result.regimeChangeDetected ? 'high' : 'medium';

      // Add to update queue
      this.addToUpdateQueue({
        message: clientMessage,
        targetClients: [clientId],
        priority,
        timestamp: new Date(),
      });
    }

    this.stats.regimeUpdatesProcessed++;
    this.emit('regime_update_processed', {
      instrument: result.instrument,
      timeframe: result.timeframe,
      regime: result.regime,
      subscribedClients: subscribedClients.length,
    });
  }

  /**
   * Get connection statistics
   */
  public getStats() {
    return {
      ...this.stats,
      queueSize: this.updateQueue.length,
      clientsConnected: this.clients.size,
      totalSubscriptions: Array.from(this.clients.values())
        .reduce((total, client) => total + client.subscriptions.size, 0),
    };
  }

  /**
   * Shutdown service and cleanup resources
   */
  public shutdown(): void {
    // Close all client connections
    for (const [clientId, client] of this.clients.entries()) {
      client.websocket.close(1001, 'Server shutting down');
      this.clients.delete(clientId);
    }

    // Clear update queue
    this.updateQueue = [];
    this.isProcessingQueue = false;

    this.removeAllListeners();
    this.emit('service_shutdown');
  }

  // ===== Private Methods =====

  private setupEventHandlers(): void {
    // Listen to regime detector events
    this.regimeDetector.on('regime_detected', (result: RegimeDetectionResult) => {
      this.processRegimeUpdate(result).catch(error => {
        this.emit('update_processing_error', { result, error });
      });
    });

    // Listen to notification manager events
    this.notificationManager.on('regime_change_notification', (notification: any) => {
      this.broadcastRegimeChangeNotification(notification);
    });
  }

  private setupClientHandlers(client: RegimeClientConnection): void {
    const { websocket, id: clientId } = client;

    websocket.on('message', async (data: Buffer) => {
      try {
        const message = JSON.parse(data.toString());
        await this.handleClientMessage(client, message);
      } catch (error) {
        this.emit('client_message_error', {
          clientId,
          error: error instanceof Error ? error.message : 'Invalid message format',
        });

        this.sendToClient(client, {
          type: 'error',
          code: 'INVALID_MESSAGE',
          message: 'Invalid message format',
          timestamp: new Date(),
        });
      }
    });

    websocket.on('close', () => {
      this.handleClientDisconnection(clientId);
    });

    websocket.on('error', (error: Error) => {
      this.emit('client_error', { clientId, error: error.message });
      this.handleClientDisconnection(clientId);
    });

    // Send ping periodically to keep connection alive
    const pingInterval = setInterval(() => {
      if (websocket.readyState === WebSocket.OPEN) {
        websocket.ping();
      } else {
        clearInterval(pingInterval);
      }
    }, 30000); // Every 30 seconds
  }

  private async handleClientMessage(client: RegimeClientConnection, message: any): Promise<void> {
    // Check rate limits
    if (!this.checkRateLimit(client)) {
      this.sendToClient(client, {
        type: 'error',
        code: 'RATE_LIMIT_EXCEEDED',
        message: 'Too many messages per second',
        timestamp: new Date(),
      });
      return;
    }

    switch (message.type) {
      case 'regime_subscribe':
      case 'regime_unsubscribe':
        await this.handleRegimeSubscription(client.id, message as RegimeSubscription);
        break;

      case 'config_update':
        this.handleClientConfigUpdate(client, message);
        break;

      case 'ping':
        this.sendToClient(client, {
          type: 'pong',
          timestamp: new Date(),
        });
        break;

      default:
        this.sendToClient(client, {
          type: 'error',
          code: 'UNKNOWN_MESSAGE_TYPE',
          message: `Unknown message type: ${message.type}`,
          timestamp: new Date(),
        });
    }
  }

  private async handleSubscribe(
    client: RegimeClientConnection,
    subscription: RegimeSubscription
  ): Promise<void> {
    // Check subscription limits
    if (client.subscriptions.size >= this.config.maxSubscriptionsPerClient) {
      throw new Error('Maximum subscriptions per client exceeded');
    }

    // Validate instruments and timeframes
    const newSubscriptions: string[] = [];
    for (const instrument of subscription.instruments) {
      for (const timeframe of subscription.timeframes) {
        const key = `${instrument}_${timeframe}`;
        newSubscriptions.push(key);
        client.subscriptions.add(key);
      }
    }

    // Update client features
    if (subscription.features) {
      client.features = { ...client.features, ...subscription.features };
    }

    // Update client update interval
    if (subscription.updateInterval) {
      client.updateInterval = Math.max(
        this.config.minUpdateInterval,
        Math.min(subscription.updateInterval, this.config.maxUpdateInterval)
      );
    }

    this.stats.activeSubscriptions += newSubscriptions.length;

    // Send confirmation
    this.sendToClient(client, {
      type: 'regime_subscription_confirmed',
      instruments: subscription.instruments,
      timeframes: subscription.timeframes,
      features: client.features,
      updateInterval: client.updateInterval,
      timestamp: new Date(),
    });

    this.emit('client_subscribed', {
      clientId: client.id,
      subscriptions: newSubscriptions,
      totalSubscriptions: client.subscriptions.size,
    });
  }

  private async handleUnsubscribe(
    client: RegimeClientConnection,
    subscription: RegimeSubscription
  ): Promise<void> {
    const removedSubscriptions: string[] = [];

    for (const instrument of subscription.instruments) {
      for (const timeframe of subscription.timeframes) {
        const key = `${instrument}_${timeframe}`;
        if (client.subscriptions.has(key)) {
          client.subscriptions.delete(key);
          removedSubscriptions.push(key);
        }
      }
    }

    this.stats.activeSubscriptions -= removedSubscriptions.length;

    // Send confirmation
    this.sendToClient(client, {
      type: 'regime_unsubscription_confirmed',
      instruments: subscription.instruments,
      timeframes: subscription.timeframes,
      timestamp: new Date(),
    });

    this.emit('client_unsubscribed', {
      clientId: client.id,
      unsubscriptions: removedSubscriptions,
      remainingSubscriptions: client.subscriptions.size,
    });
  }

  private handleClientConfigUpdate(client: RegimeClientConnection, message: any): void {
    if (message.updateInterval) {
      client.updateInterval = Math.max(
        this.config.minUpdateInterval,
        Math.min(message.updateInterval, this.config.maxUpdateInterval)
      );
    }

    if (message.features) {
      client.features = { ...client.features, ...message.features };
    }

    if (message.maxMessagesPerSecond) {
      client.rateLimitInfo.maxMessagesPerSecond = Math.min(
        message.maxMessagesPerSecond,
        this.config.maxMessagesPerSecond
      );
    }

    this.sendToClient(client, {
      type: 'config_update_confirmed',
      updateInterval: client.updateInterval,
      features: client.features,
      rateLimits: {
        maxMessagesPerSecond: client.rateLimitInfo.maxMessagesPerSecond,
      },
      timestamp: new Date(),
    });
  }

  private handleClientDisconnection(clientId: string): void {
    const client = this.clients.get(clientId);
    if (!client) return;

    this.stats.activeSubscriptions -= client.subscriptions.size;
    this.clients.delete(clientId);

    this.emit('client_disconnected', {
      clientId,
      userId: client.userId,
      subscriptions: Array.from(client.subscriptions),
    });
  }

  private getClientsForSubscription(subscriptionKey: string): string[] {
    const clients: string[] = [];

    for (const [clientId, client] of this.clients.entries()) {
      if (client.subscriptions.has(subscriptionKey)) {
        clients.push(clientId);
      }
    }

    return clients;
  }

  private checkRateLimit(client: RegimeClientConnection): boolean {
    const now = Date.now();
    const { rateLimitInfo } = client;

    // Reset counter if it's a new second
    if (now - rateLimitInfo.lastResetTime >= 1000) {
      rateLimitInfo.messagesThisSecond = 0;
      rateLimitInfo.lastResetTime = now;
    }

    if (rateLimitInfo.messagesThisSecond >= rateLimitInfo.maxMessagesPerSecond) {
      return false;
    }

    rateLimitInfo.messagesThisSecond++;
    return true;
  }

  private sendToClient(client: RegimeClientConnection, message: any): void {
    if (client.websocket.readyState === WebSocket.OPEN) {
      try {
        client.websocket.send(JSON.stringify(message));
      } catch (error) {
        this.emit('send_error', {
          clientId: client.id,
          error: error instanceof Error ? error.message : 'Send failed',
        });
      }
    }
  }

  private startQueueProcessing(): void {
    const processQueue = () => {
      if (this.isProcessingQueue || this.updateQueue.length === 0) {
        setTimeout(processQueue, this.config.queueProcessingInterval);
        return;
      }

      this.isProcessingQueue = true;

      try {
        // Sort by priority and timestamp
        this.updateQueue.sort((a, b) => {
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
          if (priorityDiff !== 0) return priorityDiff;
          return a.timestamp.getTime() - b.timestamp.getTime();
        });

        // Process up to 10 messages at once
        const batch = this.updateQueue.splice(0, 10);
        
        for (const entry of batch) {
          for (const clientId of entry.targetClients) {
            const client = this.clients.get(clientId);
            if (client && this.shouldSendUpdate(client)) {
              this.sendToClient(client, entry.message);
              client.lastUpdateSent = new Date();
            }
          }
        }

        this.stats.messagesPerSecond += batch.length;
      } catch (error) {
        this.emit('queue_processing_error', error);
      } finally {
        this.isProcessingQueue = false;
        setTimeout(processQueue, this.config.queueProcessingInterval);
      }
    };

    processQueue();
  }

  private addToUpdateQueue(entry: RegimeUpdateQueueEntry): void {
    // Check queue size limit
    if (this.updateQueue.length >= this.config.maxQueueSize) {
      // Remove oldest low-priority entries
      const lowPriorityIndex = this.updateQueue.findIndex(e => e.priority === 'low');
      if (lowPriorityIndex !== -1) {
        this.updateQueue.splice(lowPriorityIndex, 1);
      } else {
        // Remove oldest entry if no low priority found
        this.updateQueue.shift();
      }
    }

    this.updateQueue.push(entry);
  }

  private shouldSendUpdate(client: RegimeClientConnection): boolean {
    const timeSinceLastUpdate = Date.now() - client.lastUpdateSent.getTime();
    return timeSinceLastUpdate >= client.updateInterval;
  }

  private getMarketMood(result: RegimeDetectionResult): 'bullish' | 'bearish' | 'neutral' {
    if (result.trendStrength > 0.3) return 'bullish';
    if (result.trendStrength < -0.3) return 'bearish';
    return 'neutral';
  }

  private generateRecommendations(result: RegimeDetectionResult) {
    const strategies: string[] = [];
    let riskLevel: 'low' | 'medium' | 'high' = 'medium';
    let positionSizing = 1.0;

    switch (result.regime) {
      case MarketRegime.TRENDING_UP:
        strategies.push('trend_following', 'momentum');
        riskLevel = result.confidence > 0.7 ? 'medium' : 'high';
        positionSizing = result.confidence;
        break;

      case MarketRegime.TRENDING_DOWN:
        strategies.push('short_selling', 'contrarian');
        riskLevel = result.confidence > 0.7 ? 'medium' : 'high';
        positionSizing = result.confidence * 0.8;
        break;

      case MarketRegime.SIDEWAYS:
        strategies.push('range_trading', 'mean_reversion');
        riskLevel = 'low';
        positionSizing = 0.6;
        break;

      case MarketRegime.VOLATILE:
        strategies.push('volatility_trading', 'straddle');
        riskLevel = 'high';
        positionSizing = 0.4;
        break;

      case MarketRegime.LOW_VOLATILITY:
        strategies.push('carry_trade', 'covered_call');
        riskLevel = 'low';
        positionSizing = 0.8;
        break;

      default:
        strategies.push('conservative');
        riskLevel = 'low';
        positionSizing = 0.3;
    }

    return {
      suggestedStrategies: strategies,
      riskLevel,
      positionSizing: Math.max(0.1, Math.min(1.0, positionSizing)),
    };
  }

  private broadcastRegimeChangeNotification(notification: any): void {
    const message = {
      type: 'regime_change_alert',
      ...notification,
      timestamp: new Date(),
    };

    // Broadcast to all clients with regime change notifications enabled
    for (const [clientId, client] of this.clients.entries()) {
      if (client.features.regimeChanges) {
        this.addToUpdateQueue({
          message,
          targetClients: [clientId],
          priority: 'high',
          timestamp: new Date(),
        });
      }
    }
  }

  private updateStatistics(): void {
    const now = Date.now();
    const timeDiff = now - this.stats.lastStatsReset;
    
    if (timeDiff >= 10000) { // Update every 10 seconds
      this.stats.messagesPerSecond = Math.round(
        (this.stats.messagesPerSecond * 1000) / timeDiff
      );
      this.stats.lastStatsReset = now;
      
      this.emit('statistics_updated', this.getStats());
    }
  }
}