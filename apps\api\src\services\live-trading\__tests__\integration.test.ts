import { describe, it, expect, vi, beforeEach, afterEach, beforeAll, afterAll } from 'vitest';
import { LiveTradingSafetyService } from '../LiveTradingSafetyService';
import { SafetyChecklistService } from '../SafetyChecklistService';
import { ReadinessAssessmentService } from '../ReadinessAssessmentService';
import { GraduatedAccessService } from '../GraduatedAccessService';
import { EmergencySuspensionService } from '../EmergencySuspensionService';
import { ValidationAuditService } from '../ValidationAuditService';
import { 
  ValidationStage,
  ValidationStatus,
  AccessLevel,
  ReadinessAssessmentType,
  SafetyValidationStatus,
  SuspensionReason,
  SuspensionType
} from '@golddaddy/types';

describe('Live Trading Safety Validation Integration Tests', () => {
  let liveTradingSafetyService: LiveTradingSafetyService;
  let safetyChecklistService: SafetyChecklistService;
  let readinessAssessmentService: ReadinessAssessmentService;
  let graduatedAccessService: GraduatedAccessService;
  let emergencySuspensionService: EmergencySuspensionService;
  let validationAuditService: ValidationAuditService;
  
  let mockLogger: any;
  let mockDatabase: Map<string, any>;
  let mockConfidenceAssessmentService: any;

  beforeAll(() => {
    // Initialize mock database
    mockDatabase = new Map();
  });

  beforeEach(() => {
    // Reset mock database
    mockDatabase.clear();

    // Create mock logger
    mockLogger = {
      info: vi.fn(),
      error: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn()
    };

    // Mock confidence assessment service
    mockConfidenceAssessmentService = {
      getUserConfidenceStage: vi.fn().mockResolvedValue({
        stage: 'LIVE_READY',
        score: 90
      }),
      getConfidenceMetrics: vi.fn().mockResolvedValue({
        overallScore: 90,
        stage: 'LIVE_READY'
      })
    };

    // Initialize services with mock implementations
    validationAuditService = new ValidationAuditService({
      loggerService: mockLogger
    });

    safetyChecklistService = new SafetyChecklistService({
      loggerService: mockLogger,
      auditService: validationAuditService
    });

    readinessAssessmentService = new ReadinessAssessmentService({
      loggerService: mockLogger,
      auditService: validationAuditService
    });

    graduatedAccessService = new GraduatedAccessService({
      loggerService: mockLogger,
      auditService: validationAuditService
    });

    emergencySuspensionService = new EmergencySuspensionService({
      loggerService: mockLogger,
      auditService: validationAuditService
    });

    liveTradingSafetyService = new LiveTradingSafetyService({
      loggerService: mockLogger,
      safetyChecklistService,
      readinessAssessmentService,
      graduatedAccessService,
      emergencySuspensionService,
      validationAuditService,
      confidenceAssessmentService: mockConfidenceAssessmentService
    });

    // Mock storage methods for all services
    mockServiceStorage();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  afterAll(() => {
    mockDatabase.clear();
  });

  describe('Complete Validation Workflow', () => {
    it('should complete the entire live trading validation process', async () => {
      const userId = 'integration-test-user';

      // Step 1: Start live trading validation
      const validation = await liveTradingSafetyService.startLiveTradingValidation(userId);
      
      expect(validation.userId).toBe(userId);
      expect(validation.currentStage).toBe(ValidationStage.CONFIDENCE_VALIDATION);
      expect(validation.status).toBe(ValidationStatus.IN_PROGRESS);

      // Step 2: Complete confidence validation stage
      const confidenceResult = await liveTradingSafetyService.completeValidationStage(
        userId,
        validation.sessionId,
        ValidationStage.CONFIDENCE_VALIDATION,
        { confidenceScore: 90 }
      );

      expect(confidenceResult.success).toBe(true);
      expect(confidenceResult.nextStage).toBe(ValidationStage.SAFETY_CHECKLIST);

      // Step 3: Complete safety checklist
      const safetySession = await safetyChecklistService.createSafetyValidationSession(userId);
      await safetyChecklistService.startSafetyValidation(userId, safetySession.id);

      // Simulate completing all safety checklist items
      const sections = safetySession.sections;
      for (const section of sections) {
        for (const item of section.items) {
          await safetyChecklistService.updateSafetyItem(
            userId,
            safetySession.id,
            item.id,
            getAppropriateResponse(item)
          );
        }
      }

      const safetyResult = await safetyChecklistService.completeSafetyValidation(
        userId,
        safetySession.id
      );
      expect(safetyResult.success).toBe(true);

      // Step 4: Complete safety checklist stage in main validation
      const safetyStageResult = await liveTradingSafetyService.completeValidationStage(
        userId,
        validation.sessionId,
        ValidationStage.SAFETY_CHECKLIST,
        { safetySessionId: safetySession.id, completed: true }
      );

      expect(safetyStageResult.success).toBe(true);
      expect(safetyStageResult.nextStage).toBe(ValidationStage.READINESS_ASSESSMENT);

      // Step 5: Complete readiness assessment
      const assessmentSession = await readinessAssessmentService.startAssessment(
        userId,
        ReadinessAssessmentType.COMPREHENSIVE
      );

      // Simulate completing assessment questions
      for (let sectionIndex = 0; sectionIndex < assessmentSession.sections.length; sectionIndex++) {
        const section = assessmentSession.sections[sectionIndex];
        for (const question of section.questions) {
          await readinessAssessmentService.submitAnswer(
            userId,
            assessmentSession.id,
            question.id,
            getAppropriateAnswer(question)
          );
        }
      }

      // Complete scenario tests
      for (const scenario of assessmentSession.scenarioTests) {
        await readinessAssessmentService.startScenarioTest(
          userId,
          assessmentSession.id,
          scenario.id
        );

        // Submit appropriate actions for each scenario
        const actions = getAppropriateScenarioActions(scenario);
        for (const action of actions) {
          await readinessAssessmentService.submitScenarioAction(
            userId,
            assessmentSession.id,
            action,
            'Appropriate response to scenario'
          );
        }
      }

      const assessmentResult = await readinessAssessmentService.getAssessmentResult(
        userId,
        assessmentSession.id
      );
      expect(assessmentResult.passed).toBe(true);

      // Step 6: Complete readiness assessment stage
      const readinessStageResult = await liveTradingSafetyService.completeValidationStage(
        userId,
        validation.sessionId,
        ValidationStage.READINESS_ASSESSMENT,
        { assessmentId: assessmentSession.id, passed: true, score: 85 }
      );

      expect(readinessStageResult.success).toBe(true);
      expect(readinessStageResult.nextStage).toBe(ValidationStage.ACCESS_SETUP);

      // Step 7: Complete access setup
      const accessSetupResult = await liveTradingSafetyService.completeValidationStage(
        userId,
        validation.sessionId,
        ValidationStage.ACCESS_SETUP,
        { initialAccessLevel: AccessLevel.RESTRICTED }
      );

      expect(accessSetupResult.success).toBe(true);
      expect(accessSetupResult.nextStage).toBe(ValidationStage.FINAL_REVIEW);

      // Step 8: Complete final review
      const finalReviewResult = await liveTradingSafetyService.completeValidationStage(
        userId,
        validation.sessionId,
        ValidationStage.FINAL_REVIEW,
        { reviewPassed: true, finalScore: 88 }
      );

      expect(finalReviewResult.success).toBe(true);
      expect(finalReviewResult.completed).toBe(true);

      // Step 9: Verify final validation status
      const finalStatus = await liveTradingSafetyService.getValidationStatus(
        userId,
        validation.sessionId
      );

      expect(finalStatus.status).toBe(ValidationStatus.COMPLETED);
      expect(finalStatus.overallProgress).toBe(100);
      expect(finalStatus.liveTradingEnabled).toBe(true);

      // Step 10: Verify graduated access is set up
      const accessInfo = await graduatedAccessService.getAccessLevelInfo(userId);
      expect(accessInfo?.access.currentLevel).toBe(AccessLevel.RESTRICTED);
    }, 30000); // 30 second timeout for integration test

    it('should handle validation failure and retry workflow', async () => {
      const userId = 'retry-test-user';

      // Start validation
      const validation = await liveTradingSafetyService.startLiveTradingValidation(userId);

      // Fail confidence validation
      const failedResult = await liveTradingSafetyService.completeValidationStage(
        userId,
        validation.sessionId,
        ValidationStage.CONFIDENCE_VALIDATION,
        { confidenceScore: 60 } // Below threshold
      );

      expect(failedResult.success).toBe(false);
      expect(failedResult.canRetry).toBe(true);

      // Retry with better score
      const retryResult = await liveTradingSafetyService.completeValidationStage(
        userId,
        validation.sessionId,
        ValidationStage.CONFIDENCE_VALIDATION,
        { confidenceScore: 85 }
      );

      expect(retryResult.success).toBe(true);
      expect(retryResult.nextStage).toBe(ValidationStage.SAFETY_CHECKLIST);
    });

    it('should handle emergency suspension during validation', async () => {
      const userId = 'emergency-test-user';

      // Start validation
      const validation = await liveTradingSafetyService.startLiveTradingValidation(userId);

      // Trigger emergency suspension
      const suspensionResult = await liveTradingSafetyService.triggerEmergencySuspension(
        userId,
        SuspensionReason.SUSPICIOUS_ACTIVITY,
        'security-system'
      );

      expect(suspensionResult.success).toBe(true);

      // Try to continue validation - should fail
      await expect(
        liveTradingSafetyService.completeValidationStage(
          userId,
          validation.sessionId,
          ValidationStage.CONFIDENCE_VALIDATION,
          { confidenceScore: 90 }
        )
      ).rejects.toThrow();

      // Verify user is suspended
      const suspensionStatus = await emergencySuspensionService.isUserSuspended(userId);
      expect(suspensionStatus.suspended).toBe(true);
    });
  });

  describe('Access Level Progression Integration', () => {
    it('should progress through access levels based on performance', async () => {
      const userId = 'progression-test-user';

      // Complete initial validation
      await completeFullValidation(userId);

      // Verify initial access level
      let accessInfo = await graduatedAccessService.getAccessLevelInfo(userId);
      expect(accessInfo?.access.currentLevel).toBe(AccessLevel.RESTRICTED);

      // Simulate good trading performance
      await graduatedAccessService.evaluateProgression(userId);

      // Mock performance metrics that meet progression criteria
      vi.spyOn(graduatedAccessService as any, 'calculateUserMetrics').mockResolvedValue({
        totalTrades: 25,
        tradingDays: 15,
        winRate: 55,
        profitFactor: 1.2,
        maxDrawdown: 3,
        averageRiskScore: 75
      });

      const progressionResult = await graduatedAccessService.evaluateProgression(userId);
      
      expect(progressionResult?.meetsRequirements).toBe(true);
      
      // Should progress to LIMITED level
      accessInfo = await graduatedAccessService.getAccessLevelInfo(userId);
      expect(accessInfo?.access.currentLevel).toBe(AccessLevel.LIMITED);
    });

    it('should enforce trading limits correctly', async () => {
      const userId = 'limits-test-user';

      await completeFullValidation(userId);

      // Test position size validation
      const sizeValidation = await graduatedAccessService.validateTradeRequest(
        userId,
        'EURUSD',
        0.05, // Exceeds RESTRICTED limit of 0.01
        'BUY'
      );

      expect(sizeValidation.allowed).toBe(false);
      expect(sizeValidation.reason).toContain('Position size');

      // Test valid position size
      const validSizeValidation = await graduatedAccessService.validateTradeRequest(
        userId,
        'EURUSD',
        0.01, // Within limit
        'BUY'
      );

      expect(validSizeValidation.allowed).toBe(true);

      // Test daily limits
      await graduatedAccessService.updateDailyLimits(userId, 0.01);
      
      const accessInfo = await graduatedAccessService.getAccessLevelInfo(userId);
      expect(accessInfo?.dailyStatus.volumeRemaining).toBeLessThan(
        accessInfo?.access.configuration.maxDailyVolume || 0
      );
    });
  });

  describe('Audit Trail Integration', () => {
    it('should create comprehensive audit trail throughout validation', async () => {
      const userId = 'audit-test-user';

      // Complete full validation while tracking audit events
      await completeFullValidation(userId);

      // Verify audit events were logged
      expect(mockLogger.info).toHaveBeenCalledWith(
        expect.stringContaining('validation'),
        expect.any(Object)
      );

      // Test audit query functionality
      const auditLogs = await validationAuditService.queryAuditLogs({
        userId,
        startDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        endDate: new Date()
      });

      expect(Array.isArray(auditLogs)).toBe(true);
    });

    it('should generate compliance reports', async () => {
      const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
      const endDate = new Date();

      const report = await validationAuditService.generateComplianceReport(
        startDate,
        endDate,
        'WEEKLY'
      );

      expect(report.reportType).toBe('WEEKLY');
      expect(report.periodStart).toEqual(startDate);
      expect(report.periodEnd).toEqual(endDate);
      expect(typeof report.summary.totalValidations).toBe('number');
    });
  });

  describe('Error Recovery Integration', () => {
    it('should recover from partial validation failures', async () => {
      const userId = 'recovery-test-user';

      // Start validation
      const validation = await liveTradingSafetyService.startLiveTradingValidation(userId);

      // Complete first stage
      await liveTradingSafetyService.completeValidationStage(
        userId,
        validation.sessionId,
        ValidationStage.CONFIDENCE_VALIDATION,
        { confidenceScore: 90 }
      );

      // Simulate service failure during safety checklist
      vi.spyOn(safetyChecklistService, 'createSafetyValidationSession')
        .mockRejectedValueOnce(new Error('Service temporarily unavailable'));

      // Attempt should fail
      await expect(
        liveTradingSafetyService.completeValidationStage(
          userId,
          validation.sessionId,
          ValidationStage.SAFETY_CHECKLIST,
          { completed: true }
        )
      ).rejects.toThrow('Service temporarily unavailable');

      // Restore service and retry
      vi.spyOn(safetyChecklistService, 'createSafetyValidationSession')
        .mockRestore();

      // Should succeed on retry
      const retryResult = await liveTradingSafetyService.completeValidationStage(
        userId,
        validation.sessionId,
        ValidationStage.SAFETY_CHECKLIST,
        { safetySessionId: 'mock-session', completed: true }
      );

      expect(retryResult.success).toBe(true);
    });
  });

  // Helper functions
  function mockServiceStorage() {
    // Mock storage methods for all services
    const services = [
      liveTradingSafetyService,
      safetyChecklistService,
      readinessAssessmentService,
      graduatedAccessService,
      emergencySuspensionService,
      validationAuditService
    ];

    services.forEach(service => {
      // Mock common storage methods
      if ((service as any).storeSession) {
        vi.spyOn(service as any, 'storeSession').mockImplementation(async (data: any) => {
          mockDatabase.set(data.id, data);
          return data;
        });
      }

      if ((service as any).getSession) {
        vi.spyOn(service as any, 'getSession').mockImplementation(async (id: string) => {
          return mockDatabase.get(id) || null;
        });
      }

      if ((service as any).updateSession) {
        vi.spyOn(service as any, 'updateSession').mockImplementation(async (data: any) => {
          mockDatabase.set(data.id, data);
          return data;
        });
      }

      if ((service as any, 'storeValidation')) {
        vi.spyOn(service as any, 'storeValidation').mockImplementation(async (data: any) => {
          mockDatabase.set(data.id, data);
          return data;
        });
      }

      if ((service as any).getValidation) {
        vi.spyOn(service as any, 'getValidation').mockImplementation(async (id: string) => {
          return mockDatabase.get(id) || null;
        });
      }

      if ((service as any).updateValidation) {
        vi.spyOn(service as any, 'updateValidation').mockImplementation(async (data: any) => {
          mockDatabase.set(data.id, data);
          return data;
        });
      }
    });
  }

  async function completeFullValidation(userId: string) {
    const validation = await liveTradingSafetyService.startLiveTradingValidation(userId);

    // Complete all stages with passing scores
    await liveTradingSafetyService.completeValidationStage(
      userId,
      validation.sessionId,
      ValidationStage.CONFIDENCE_VALIDATION,
      { confidenceScore: 90 }
    );

    await liveTradingSafetyService.completeValidationStage(
      userId,
      validation.sessionId,
      ValidationStage.SAFETY_CHECKLIST,
      { safetySessionId: 'mock-session', completed: true }
    );

    await liveTradingSafetyService.completeValidationStage(
      userId,
      validation.sessionId,
      ValidationStage.READINESS_ASSESSMENT,
      { assessmentId: 'mock-assessment', passed: true, score: 85 }
    );

    await liveTradingSafetyService.completeValidationStage(
      userId,
      validation.sessionId,
      ValidationStage.ACCESS_SETUP,
      { initialAccessLevel: AccessLevel.RESTRICTED }
    );

    await liveTradingSafetyService.completeValidationStage(
      userId,
      validation.sessionId,
      ValidationStage.FINAL_REVIEW,
      { reviewPassed: true, finalScore: 88 }
    );

    return validation;
  }

  function getAppropriateResponse(item: any) {
    switch (item.type) {
      case 'ACKNOWLEDGMENT':
        return { acknowledged: true };
      case 'QUIZ_QUESTION':
        return { answer: item.correctAnswer || item.quizOptions[0] };
      case 'CONTACT_INFO':
        return { phoneNumber: '******-0123', verified: true };
      default:
        return { acknowledged: true };
    }
  }

  function getAppropriateAnswer(question: any) {
    switch (question.type) {
      case 'MULTIPLE_CHOICE':
        // Find the highest scoring option
        return question.options?.reduce((best: any, option: any) => 
          option.score > (best?.score || 0) ? option : best
        )?.id || 'a';
      case 'SCALE':
        return '8'; // High score
      case 'TRUE_FALSE':
        return question.correctAnswer || true;
      default:
        return 'a';
    }
  }

  function getAppropriateScenarioActions(scenario: any) {
    // Return appropriate actions based on scenario type
    switch (scenario.id) {
      case 'flash_crash':
        return ['check_news', 'close_partial_positions'];
      case 'broker_outage':
        return ['call_broker_emergency', 'try_mobile_app'];
      case 'margin_call':
        return ['close_losing_positions', 'reduce_all_positions'];
      default:
        return ['check_news', 'hold_and_wait'];
    }
  }
});