import { prisma } from '@golddaddy/config/src/database';
import { Request } from 'express';
import Express from 'express';
import { AuthenticatedRequest } from './auth';

// Audit logging for compliance and security monitoring
// Tracks all user actions, data changes, and system events

export interface AuditLogEntry {
  userId?: string;
  action: string;
  tableName?: string;
  recordId?: string;
  ipAddress?: string;
  userAgent?: string;
  oldValues?: Record<string, unknown>;
  newValues?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

// Standard audit actions
export const AUDIT_ACTIONS = {
  // Authentication
  LOGIN: 'login',
  LOGOUT: 'logout',
  REGISTRATION: 'registration',
  PASSWORD_CHANGE: 'password_change',
  TOKEN_REFRESH: 'token_refresh',

  // User Management
  PROFILE_UPDATE: 'profile_update',
  PREFERENCES_UPDATE: 'preferences_update',
  ACCOUNT_DELETION: 'account_deletion',
  DATA_EXPORT: 'data_export',

  // Trading Goals
  GOAL_CREATE: 'goal_create',
  GOAL_UPDATE: 'goal_update',
  GOAL_DELETE: 'goal_delete',
  GOAL_COMPLETE: 'goal_complete',

  // Trading
  TRADE_EXECUTE: 'trade_execute',
  TRADE_CLOSE: 'trade_close',
  TRADE_CANCEL: 'trade_cancel',
  STRATEGY_CREATE: 'strategy_create',
  STRATEGY_UPDATE: 'strategy_update',

  // System Events
  DATA_ACCESS: 'data_access',
  EXPORT_REQUEST: 'export_request',
  BACKUP_CREATE: 'backup_create',
  MIGRATION_RUN: 'migration_run',

  // Security Events
  UNAUTHORIZED_ACCESS: 'unauthorized_access',
  SUSPICIOUS_ACTIVITY: 'suspicious_activity',
  RATE_LIMIT_EXCEEDED: 'rate_limit_exceeded',
} as const;

// Log audit event
export const logAuditEvent = async (entry: AuditLogEntry): Promise<void> => {
  try {
    await prisma.auditLog.create({
      data: {
        userId: entry.userId,
        action: entry.action,
        tableName: entry.tableName,
        recordId: entry.recordId,
        ipAddress: entry.ipAddress,
        userAgent: entry.userAgent,
        oldValues: entry.oldValues ? JSON.stringify(entry.oldValues) : undefined,
        newValues: entry.newValues ? JSON.stringify(entry.newValues) : undefined,
        metadata: entry.metadata ? JSON.stringify(entry.metadata) : undefined,
      },
    });
  } catch (error) {
    console.error('Failed to log audit event:', error);
    // Don't throw error to avoid breaking the main operation
  }
};

// Extract request information for audit logging
export const extractRequestInfo = (req: Request | AuthenticatedRequest): {
  userId?: string;
  ipAddress: string;
  userAgent?: string;
} => {
  const authenticatedReq = req as AuthenticatedRequest;
  
  return {
    userId: authenticatedReq.userId,
    ipAddress: req.ip || req.connection.remoteAddress || 'unknown',
    userAgent: req.get('User-Agent'),
  };
};

// Audit middleware for Express routes
export const auditMiddleware = (action: string, tableName?: string) => {
  return (req: AuthenticatedRequest, res: Express.Response, next: Express.NextFunction) => {
    const originalSend = res.send;
    const requestInfo = extractRequestInfo(req);

    // Capture the response to log success/failure
    res.send = function (data: unknown) {
      const statusCode = res.statusCode;
      
      // Log the audit event
      logAuditEvent({
        ...requestInfo,
        action,
        tableName,
        metadata: {
          method: req.method,
          url: req.originalUrl,
          statusCode,
          success: statusCode >= 200 && statusCode < 300,
        },
      });

      return originalSend.call(this, data);
    };

    next();
  };
};

// High-level audit logging functions
export const auditLogger = {
  async logAuthentication(userId: string, req: Request, success: boolean) {
    const requestInfo = extractRequestInfo(req);
    await logAuditEvent({
      userId,
      action: success ? AUDIT_ACTIONS.LOGIN : AUDIT_ACTIONS.UNAUTHORIZED_ACCESS,
      ...requestInfo,
      metadata: {
        success,
        timestamp: new Date().toISOString(),
      },
    });
  },

  async logUserRegistration(userId: string, req: Request, userProfile: Record<string, unknown>) {
    const requestInfo = extractRequestInfo(req);
    await logAuditEvent({
      userId,
      action: AUDIT_ACTIONS.REGISTRATION,
      tableName: 'users',
      recordId: userId,
      ...requestInfo,
      newValues: {
        email: userProfile.email,
        displayName: userProfile.displayName,
        experienceLevel: userProfile.experienceLevel,
        // Don't log sensitive data like trading capital
      },
    });
  },

  async logDataChange(
    userId: string,
    action: string,
    tableName: string,
    recordId: string,
    oldValues?: Record<string, unknown>,
    newValues?: Record<string, unknown>,
    req?: Request
  ) {
    const requestInfo = req ? extractRequestInfo(req) : {};
    await logAuditEvent({
      userId,
      action,
      tableName,
      recordId,
      oldValues,
      newValues,
      ...requestInfo,
    });
  },

  async logDataAccess(userId: string, tableName: string, recordId?: string, req?: Request) {
    const requestInfo = req ? extractRequestInfo(req) : {};
    await logAuditEvent({
      userId,
      action: AUDIT_ACTIONS.DATA_ACCESS,
      tableName,
      recordId,
      ...requestInfo,
    });
  },

  async logSecurityEvent(action: string, req: Request, metadata?: Record<string, unknown>) {
    const requestInfo = extractRequestInfo(req);
    await logAuditEvent({
      action,
      ...requestInfo,
      metadata: {
        ...metadata,
        timestamp: new Date().toISOString(),
        severity: 'high',
      },
    });
  },

  async logGDPREvent(userId: string, action: string, req: Request, details?: Record<string, unknown>) {
    const requestInfo = extractRequestInfo(req);
    await logAuditEvent({
      userId,
      action,
      ...requestInfo,
      metadata: {
        gdprCompliance: true,
        details,
        timestamp: new Date().toISOString(),
      },
    });
  },
};

// Query audit logs for compliance reports
export const auditQueries = {
  async getUserActivityLog(userId: string, fromDate?: Date, toDate?: Date) {
    const whereClause: Record<string, unknown> = { userId };
    
    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) whereClause.createdAt.gte = fromDate;
      if (toDate) whereClause.createdAt.lte = toDate;
    }

    return await prisma.auditLog.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
    });
  },

  async getSecurityEvents(fromDate?: Date, toDate?: Date) {
    const securityActions = [
      AUDIT_ACTIONS.UNAUTHORIZED_ACCESS,
      AUDIT_ACTIONS.SUSPICIOUS_ACTIVITY,
      AUDIT_ACTIONS.RATE_LIMIT_EXCEEDED,
    ];

    const whereClause: any = {
      action: { in: securityActions },
    };
    
    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) whereClause.createdAt.gte = fromDate;
      if (toDate) whereClause.createdAt.lte = toDate;
    }

    return await prisma.auditLog.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
    });
  },

  async getDataChanges(tableName: string, recordId?: string, fromDate?: Date, toDate?: Date) {
    const whereClause: any = { tableName };
    
    if (recordId) whereClause.recordId = recordId;
    
    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) whereClause.createdAt.gte = fromDate;
      if (toDate) whereClause.createdAt.lte = toDate;
    }

    return await prisma.auditLog.findMany({
      where: whereClause,
      orderBy: { createdAt: 'desc' },
    });
  },

  async getActionStatistics(fromDate?: Date, toDate?: Date) {
    const whereClause: any = {};
    
    if (fromDate || toDate) {
      whereClause.createdAt = {};
      if (fromDate) whereClause.createdAt.gte = fromDate;
      if (toDate) whereClause.createdAt.lte = toDate;
    }

    // Group by action to get statistics
    const actionCounts = await prisma.auditLog.groupBy({
      by: ['action'],
      where: whereClause,
      _count: {
        action: true,
      },
      orderBy: {
        _count: {
          action: 'desc',
        },
      },
    });

    return actionCounts.map(item => ({
      action: item.action,
      count: item._count.action,
    }));
  },
};

// Data retention compliance
export const auditRetention = {
  async cleanupOldLogs(retentionDays: number = 2555) { // 7 years default
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    const deletedCount = await prisma.auditLog.deleteMany({
      where: {
        createdAt: {
          lt: cutoffDate,
        },
      },
    });

    console.log(`Cleaned up ${deletedCount.count} audit logs older than ${retentionDays} days`);
    return deletedCount.count;
  },

  async exportUserAuditData(userId: string) {
    const auditLogs = await prisma.auditLog.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });

    return {
      userId,
      exportDate: new Date().toISOString(),
      totalRecords: auditLogs.length,
      auditLogs: auditLogs.map(log => ({
        timestamp: log.createdAt,
        action: log.action,
        tableName: log.tableName,
        recordId: log.recordId,
        ipAddress: log.ipAddress,
        // Exclude sensitive metadata for export
      })),
    };
  },

  async deleteUserAuditData(userId: string) {
    const deletedCount = await prisma.auditLog.deleteMany({
      where: { userId },
    });

    console.log(`Deleted ${deletedCount.count} audit logs for user ${userId}`);
    return deletedCount.count;
  },
};