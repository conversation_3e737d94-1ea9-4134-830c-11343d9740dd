# Rollback Workflow for GoldDaddy Trading Platform
name: Rollback Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        type: choice
        options:
          - production
          - staging
      backup_tag:
        description: 'Backup tag to rollback to (e.g., backup-20250831-143022)'
        required: true
        type: string
      reason:
        description: 'Reason for rollback'
        required: true
        type: string

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  validate-rollback:
    runs-on: ubuntu-latest
    outputs:
      is-valid: ${{ steps.validate.outputs.is-valid }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Validate backup tag exists
        id: validate
        run: |
          if git tag -l | grep -q "^${{ github.event.inputs.backup_tag }}$"; then
            echo "is-valid=true" >> $GITHUB_OUTPUT
            echo "✅ Backup tag ${{ github.event.inputs.backup_tag }} found"
          else
            echo "is-valid=false" >> $GITHUB_OUTPUT
            echo "❌ Backup tag ${{ github.event.inputs.backup_tag }} not found"
            exit 1
          fi

      - name: Display rollback information
        run: |
          echo "🔄 Rollback Details:"
          echo "Environment: ${{ github.event.inputs.environment }}"
          echo "Backup Tag: ${{ github.event.inputs.backup_tag }}"
          echo "Reason: ${{ github.event.inputs.reason }}"
          echo "Initiated by: ${{ github.actor }}"

  rollback-frontend:
    runs-on: ubuntu-latest
    needs: validate-rollback
    if: needs.validate-rollback.outputs.is-valid == 'true'
    environment: ${{ github.event.inputs.environment }}
    
    steps:
      - name: Checkout to backup tag
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.backup_tag }}
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build frontend from backup
        run: npm run build --workspace=@golddaddy/web
        env:
          NODE_ENV: ${{ github.event.inputs.environment }}
          NEXT_PUBLIC_API_URL: ${{ secrets[format('{0}_API_URL', github.event.inputs.environment)] }}
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets[format('{0}_SUPABASE_URL', github.event.inputs.environment)] }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets[format('{0}_SUPABASE_ANON_KEY', github.event.inputs.environment)] }}

      - name: Deploy rollback to Vercel
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-args: ${{ github.event.inputs.environment == 'production' && '--prod' || '' }}
          working-directory: apps/web

  rollback-backend:
    runs-on: ubuntu-latest
    needs: validate-rollback
    if: needs.validate-rollback.outputs.is-valid == 'true'
    environment: ${{ github.event.inputs.environment }}
    
    steps:
      - name: Setup SSH
        uses: webfactory/ssh-agent@v0.8.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Rollback backend services
        run: |
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          BACKUP_TAG="${{ github.event.inputs.backup_tag }}"
          
          if [ "$ENVIRONMENT" = "production" ]; then
            HOST="${{ secrets.PRODUCTION_HOST }}"
            USER="${{ secrets.PRODUCTION_USER }}"
            COMPOSE_FILE="docker-compose.production.yml"
          else
            HOST="${{ secrets.STAGING_HOST }}"
            USER="${{ secrets.STAGING_USER }}"
            COMPOSE_FILE="docker-compose.staging.yml"
          fi
          
          ssh -o StrictHostKeyChecking=no $USER@$HOST << EOF
            cd /opt/golddaddy-api
            
            echo "🔄 Starting rollback to $BACKUP_TAG on $ENVIRONMENT"
            
            # Create emergency backup of current state
            EMERGENCY_BACKUP="emergency-backup-\$(date +%Y%m%d-%H%M%S)"
            mkdir -p backups/\$EMERGENCY_BACKUP
            docker-compose -f $COMPOSE_FILE config > backups/\$EMERGENCY_BACKUP/docker-compose.backup.yml
            cp .env.$ENVIRONMENT backups/\$EMERGENCY_BACKUP/
            
            # Stop current services gracefully
            docker-compose -f $COMPOSE_FILE down --timeout 30
            
            # Checkout to backup tag
            git fetch --all
            git checkout $BACKUP_TAG
            
            # Restore backup configuration if available
            BACKUP_DIR=\$(find backups -name "*$BACKUP_TAG*" -type d | head -1)
            if [ -n "\$BACKUP_DIR" ] && [ -f "\$BACKUP_DIR/.env.$ENVIRONMENT" ]; then
              echo "📋 Restoring environment configuration from backup"
              cp "\$BACKUP_DIR/.env.$ENVIRONMENT" .env.$ENVIRONMENT
            fi
            
            # Start services with rollback configuration
            docker-compose -f $COMPOSE_FILE pull
            docker-compose -f $COMPOSE_FILE up -d --build
            
            # Wait for services to start
            echo "⏳ Waiting for services to start..."
            sleep 60
            
            # Health check with extended timeout for rollback
            for i in {1..20}; do
              if curl -f http://localhost:3001/api/monitoring/health; then
                echo "✅ Rollback health check passed"
                exit 0
              fi
              echo "⏳ Waiting for services to be ready... (\$i/20)"
              sleep 15
            done
            
            echo "❌ Rollback failed - services not responding"
            exit 1
          EOF

  post-rollback-validation:
    runs-on: ubuntu-latest
    needs: [rollback-frontend, rollback-backend]
    environment: ${{ github.event.inputs.environment }}
    
    steps:
      - name: Run post-rollback smoke tests
        run: |
          ENVIRONMENT="${{ github.event.inputs.environment }}"
          
          if [ "$ENVIRONMENT" = "production" ]; then
            API_URL="${{ secrets.PRODUCTION_API_URL }}"
            BASE_URL="${{ secrets.PRODUCTION_BASE_URL }}"
          else
            API_URL="${{ secrets.STAGING_API_URL }}"
            BASE_URL="${{ secrets.STAGING_BASE_URL }}"
          fi
          
          echo "🧪 Running post-rollback validation..."
          
          # API Health Check
          if curl -f "$API_URL/api/monitoring/health"; then
            echo "✅ API health check passed"
          else
            echo "❌ API health check failed"
            exit 1
          fi
          
          # Frontend Health Check
          if curl -f "$BASE_URL/health"; then
            echo "✅ Frontend health check passed"
          else
            echo "❌ Frontend health check failed"
            exit 1
          fi
          
          # Database connectivity check
          if curl -f "$API_URL/api/monitoring/database-health"; then
            echo "✅ Database connectivity check passed"
          else
            echo "❌ Database connectivity check failed"
            exit 1
          fi
          
          echo "✅ All post-rollback validations passed"

      - name: Create rollback incident record
        run: |
          echo "📝 Recording rollback incident..."
          curl -X POST "${{ secrets[format('{0}_API_URL', github.event.inputs.environment)] }}/api/monitoring/incidents" \
            -H "Content-Type: application/json" \
            -H "Authorization: Bearer ${{ secrets.MONITORING_API_KEY }}" \
            -d '{
              "type": "rollback",
              "environment": "${{ github.event.inputs.environment }}",
              "backup_tag": "${{ github.event.inputs.backup_tag }}",
              "reason": "${{ github.event.inputs.reason }}",
              "initiated_by": "${{ github.actor }}",
              "status": "completed",
              "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"
            }' || true

  notify-rollback:
    runs-on: ubuntu-latest
    needs: [rollback-frontend, rollback-backend, post-rollback-validation]
    if: always()
    
    steps:
      - name: Notify rollback completion
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          custom_payload: |
            {
              "text": "🔄 Rollback ${{ job.status }} for ${{ github.event.inputs.environment }}",
              "attachments": [{
                "color": "${{ job.status == 'success' && 'good' || 'danger' }}",
                "fields": [
                  {
                    "title": "Environment",
                    "value": "${{ github.event.inputs.environment }}",
                    "short": true
                  },
                  {
                    "title": "Backup Tag",
                    "value": "${{ github.event.inputs.backup_tag }}",
                    "short": true
                  },
                  {
                    "title": "Reason",
                    "value": "${{ github.event.inputs.reason }}",
                    "short": false
                  },
                  {
                    "title": "Initiated By",
                    "value": "${{ github.actor }}",
                    "short": true
                  }
                ]
              }]
            }