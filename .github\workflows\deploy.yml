name: Deploy

on:
  push:
    branches: [main]
  release:
    types: [published]

env:
  NODE_VERSION: "18"
  REGISTRY: ghcr.io

jobs:
  deploy-web:
    name: Deploy Web App to Vercel
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Build web application
        run: npm run build --workspace=@golddaddy/web
        env:
          NODE_ENV: production
          NEXT_PUBLIC_API_URL: ${{ secrets.PRODUCTION_API_URL }}

      - name: Deploy to Vercel
        uses: vercel/action@v1
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          working-directory: apps/web

  build-and-push-api:
    name: Build and Push API Container
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/api
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=sha,prefix=commit-

      - name: Build and push API container
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile.api
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  build-and-push-mt5-bridge:
    name: Build and Push MT5 Bridge Container
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository }}/mt5-bridge
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=sha,prefix=commit-

      - name: Create MT5 Bridge Dockerfile
        run: |
          cat > docker/Dockerfile.mt5-bridge << 'EOF'
          FROM node:18-alpine
          WORKDIR /app
          COPY package*.json ./
          COPY apps/mt5-bridge/package*.json ./apps/mt5-bridge/
          COPY packages/*/package*.json ./packages/*/
          RUN npm install
          COPY . .
          RUN npm run build --workspace=@golddaddy/mt5-bridge
          EXPOSE 3002
          CMD ["npm", "run", "start", "--workspace=@golddaddy/mt5-bridge"]
          EOF

      - name: Build and push MT5 Bridge container
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile.mt5-bridge
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-and-push-api, build-and-push-mt5-bridge]
    if: github.ref == 'refs/heads/main'
    environment:
      name: staging
      url: https://staging.golddaddy.app
    steps:
      - name: Deploy to staging environment
        run: |
          echo "Deploying to staging environment"
          # This would typically involve:
          # - Updating container orchestrator (K8s, Docker Swarm, etc.)
          # - Running database migrations
          # - Updating environment variables
          # - Health checks

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.event_name == 'release'
    environment:
      name: production
      url: https://golddaddy.app
    steps:
      - name: Deploy to production environment
        run: |
          echo "Deploying to production environment"
          # This would typically involve:
          # - Blue/green deployment strategy
          # - Database migrations with rollback capability
          # - Monitoring and alerting setup
          # - Performance testing
