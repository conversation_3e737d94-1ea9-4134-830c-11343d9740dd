/**
 * Monitoring Service Manager
 * 
 * Manages initialization and coordination of monitoring services
 * Part of Task 4: Real-time Monitoring and Alerting
 */

import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import { BrokerMonitoringService } from './BrokerMonitoringService.js';
import { BrokerHealthMonitor } from '../trading/BrokerHealthMonitor.js';
import { ErrorClassificationService } from '../trading/ErrorClassificationService.js';
import { BrokerFailoverEngine } from '../trading/BrokerFailoverEngine.js';
import { AuditTrailService } from '../compliance/AuditTrailService.js';
import { setMonitoringService } from '../../routes/monitoring.js';
import { setAuditService } from '../../routes/audit.js';
import type { MonitoringConfig, MonitoringAlert } from '@golddaddy/types';

export class MonitoringServiceManager extends EventEmitter {
  private prisma: PrismaClient;
  private healthMonitor: BrokerHealthMonitor;
  private errorClassifier: ErrorClassificationService;
  private failoverEngine: BrokerFailoverEngine;
  private monitoringService: BrokerMonitoringService;
  private auditService: AuditTrailService;
  private isInitialized = false;
  private isShuttingDown = false;

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  /**
   * Initialize all monitoring services
   */
  async initialize(config: Partial<MonitoringConfig> = {}): Promise<void> {
    if (this.isInitialized) {
      console.warn('Monitoring services already initialized');
      return;
    }

    try {
      console.log('🔧 Initializing monitoring services...');

      // Set up default configuration
      const monitoringConfig: MonitoringConfig = {
        websocketPort: config.websocketPort || parseInt(process.env.WEBSOCKET_PORT || '8080'),
        alertRetentionDays: config.alertRetentionDays || 30,
        healthCheckAlertThreshold: config.healthCheckAlertThreshold || 3,
        latencyAlertThreshold: config.latencyAlertThreshold || 5000,
        errorRateAlertThreshold: config.errorRateAlertThreshold || 0.1,
        enableEmailAlerts: config.enableEmailAlerts || false,
        enableSlackAlerts: config.enableSlackAlerts || false,
        enableWebhookAlerts: config.enableWebhookAlerts || false,
      };

      // Initialize core services
      await this.initializeCoreServices();

      // Initialize audit trail service
      this.auditService = new AuditTrailService(this.prisma);
      await this.auditService.initialize();

      // Initialize monitoring service
      this.monitoringService = new BrokerMonitoringService(
        this.prisma,
        this.healthMonitor,
        this.errorClassifier
      );

      // Set up cross-service event listeners
      this.setupCrossServiceListeners();

      // Initialize WebSocket server
      this.monitoringService.initializeWebSocket(monitoringConfig.websocketPort);

      // Inject services into routes
      setMonitoringService(this.monitoringService);
      setAuditService(this.auditService);

      this.isInitialized = true;
      console.log('✅ Monitoring services initialized successfully');

      this.emit('initialized', monitoringConfig);

    } catch (error) {
      console.error('❌ Failed to initialize monitoring services:', error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Initialize core trading services required for monitoring
   */
  private async initializeCoreServices(): Promise<void> {
    console.log('🔧 Initializing core services...');

    // Initialize health monitor
    this.healthMonitor = new BrokerHealthMonitor(this.prisma);
    
    // Initialize error classifier
    this.errorClassifier = new ErrorClassificationService(this.prisma);

    // Initialize failover engine
    this.failoverEngine = new BrokerFailoverEngine(this.prisma, this.healthMonitor);

    console.log('✅ Core services initialized');
  }

  /**
   * Set up cross-service event listeners
   */
  private setupCrossServiceListeners(): void {
    // Listen to failover events and create monitoring alerts + audit logs
    this.failoverEngine.on('failoverExecuted', (data) => {
      // Create monitoring alert
      this.monitoringService.emit('failoverAlert', {
        type: 'failover_executed',
        severity: 'high',
        message: `Failover executed: ${data.fromBroker} → ${data.toBroker}`,
        brokerId: data.toBroker,
        metadata: {
          fromBroker: data.fromBroker,
          toBroker: data.toBroker,
          trigger: data.trigger,
          impactedTrades: data.impactedTrades,
          timestamp: data.timestamp
        }
      });

      // Log failover event in audit trail
      this.auditService.logFailoverEvent('FAILOVER_COMPLETED', {
        fromBroker: data.fromBroker,
        toBroker: data.toBroker,
        trigger: data.trigger,
        impactedTrades: data.impactedTrades,
        duration: data.duration,
        success: true,
        timestamp: data.timestamp
      });
    });

    this.failoverEngine.on('failoverFailed', (data) => {
      // Create critical alert
      this.monitoringService.emit('failoverAlert', {
        type: 'system_error',
        severity: 'critical',
        message: `Failover failed: ${data.error}`,
        brokerId: data.targetBroker,
        metadata: {
          originalBroker: data.originalBroker,
          targetBroker: data.targetBroker,
          error: data.error,
          timestamp: data.timestamp
        }
      });

      // Log failed failover in audit trail
      this.auditService.logFailoverEvent('FAILOVER_FAILED', {
        fromBroker: data.originalBroker,
        toBroker: data.targetBroker,
        trigger: data.trigger,
        error: data.error,
        success: false,
        timestamp: data.timestamp
      });
    });

    // Audit all monitoring alerts
    this.monitoringService.on('alert', (alert: MonitoringAlert) => {
      // Forward to external systems
      this.emit('alert', alert);
      this.handleAlertForwarding(alert);

      // Log alert creation in audit trail
      this.auditService.logSystemAction({
        actionType: 'ALERT_CREATED',
        message: `${alert.type} alert created: ${alert.message}`,
        success: true,
        brokerId: alert.brokerId,
        details: {
          alertId: alert.id,
          alertType: alert.type,
          severity: alert.severity,
          metadata: alert.metadata
        },
        complianceRelevant: alert.severity === 'critical' || alert.severity === 'high'
      });
    });

    // Audit health check events
    this.healthMonitor.on('healthCheck', (result) => {
      this.auditService.logSystemAction({
        actionType: 'BROKER_HEALTH_CHECK',
        message: `Broker health check: ${result.brokerId} - ${result.healthy ? 'HEALTHY' : 'UNHEALTHY'}`,
        success: result.healthy,
        brokerId: result.brokerId,
        details: {
          latency: result.latency,
          errorMessage: result.errorMessage,
          testType: result.testType
        },
        complianceRelevant: !result.healthy
      });
    });

    // Audit circuit breaker events
    this.errorClassifier.on('circuitBreakerOpened', (data) => {
      this.auditService.logSystemAction({
        actionType: 'CIRCUIT_BREAKER_OPENED',
        message: `Circuit breaker opened for ${data.service}: ${data.reason}`,
        success: false,
        brokerId: data.brokerId,
        details: {
          service: data.service,
          reason: data.reason,
          failureCount: data.failureCount
        },
        complianceRelevant: true
      });
    });

    this.errorClassifier.on('circuitBreakerClosed', (data) => {
      this.auditService.logSystemAction({
        actionType: 'CIRCUIT_BREAKER_CLOSED',
        message: `Circuit breaker closed for ${data.service}`,
        success: true,
        brokerId: data.brokerId,
        details: {
          service: data.service,
          recoveryTime: data.recoveryTime
        },
        complianceRelevant: true
      });
    });

    console.log('✅ Cross-service listeners configured with audit trail integration');
  }

  /**
   * Handle forwarding alerts to external systems (email, Slack, etc.)
   */
  private handleAlertForwarding(alert: MonitoringAlert): void {
    // In a production system, this would integrate with:
    // - Email service (SendGrid, SES, etc.)
    // - Slack webhooks
    // - PagerDuty or similar alerting services
    // - Custom webhook endpoints

    // For now, we'll just log high-severity alerts
    if (alert.severity === 'critical' || alert.severity === 'high') {
      console.log(`🚨 HIGH-SEVERITY ALERT: ${alert.message}`);
      
      // Simulate webhook call (would be actual HTTP request in production)
      this.simulateWebhookAlert(alert);
    }
  }

  /**
   * Simulate webhook alert (placeholder for production webhook integration)
   */
  private simulateWebhookAlert(alert: MonitoringAlert): void {
    const webhookPayload = {
      alert_id: alert.id,
      type: alert.type,
      severity: alert.severity,
      message: alert.message,
      timestamp: alert.timestamp,
      broker_id: alert.brokerId,
      metadata: alert.metadata
    };

    console.log('📡 Webhook alert payload:', JSON.stringify(webhookPayload, null, 2));
  }

  /**
   * Start monitoring for a specific user
   */
  async startUserMonitoring(userId: string): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Monitoring services not initialized');
    }

    console.log(`🔍 Starting monitoring for user: ${userId}`);
    
    try {
      await this.healthMonitor.startMonitoring(userId);
      
      console.log(`✅ Monitoring started for user: ${userId}`);
      this.emit('userMonitoringStarted', { userId });

    } catch (error) {
      console.error(`❌ Failed to start monitoring for user ${userId}:`, error);
      this.emit('error', error);
      throw error;
    }
  }

  /**
   * Stop monitoring for a specific user
   */
  stopUserMonitoring(userId: string): void {
    if (!this.isInitialized) {
      console.warn('Monitoring services not initialized');
      return;
    }

    console.log(`⏹️ Stopping monitoring for user: ${userId}`);
    
    // Stop health monitoring (implementation depends on health monitor design)
    // this.healthMonitor.stopUserMonitoring(userId);

    console.log(`✅ Monitoring stopped for user: ${userId}`);
    this.emit('userMonitoringStopped', { userId });
  }

  /**
   * Get monitoring service instance (for direct access if needed)
   */
  getMonitoringService(): BrokerMonitoringService {
    if (!this.isInitialized) {
      throw new Error('Monitoring services not initialized');
    }
    return this.monitoringService;
  }

  /**
   * Get health monitor instance
   */
  getHealthMonitor(): BrokerHealthMonitor {
    if (!this.isInitialized) {
      throw new Error('Monitoring services not initialized');
    }
    return this.healthMonitor;
  }

  /**
   * Get error classifier instance
   */
  getErrorClassifier(): ErrorClassificationService {
    if (!this.isInitialized) {
      throw new Error('Monitoring services not initialized');
    }
    return this.errorClassifier;
  }

  /**
   * Get failover engine instance
   */
  getFailoverEngine(): BrokerFailoverEngine {
    if (!this.isInitialized) {
      throw new Error('Monitoring services not initialized');
    }
    return this.failoverEngine;
  }

  /**
   * Check if services are initialized
   */
  isReady(): boolean {
    return this.isInitialized && !this.isShuttingDown;
  }

  /**
   * Get service health status
   */
  getHealthStatus(): {
    status: 'healthy' | 'degraded' | 'failed';
    services: Record<string, boolean>;
    timestamp: Date;
  } {
    const services = {
      healthMonitor: !!this.healthMonitor,
      errorClassifier: !!this.errorClassifier,
      failoverEngine: !!this.failoverEngine,
      monitoringService: !!this.monitoringService,
      websocket: this.monitoringService?.['wsServer'] !== null
    };

    const healthyServices = Object.values(services).filter(Boolean).length;
    const totalServices = Object.keys(services).length;

    let status: 'healthy' | 'degraded' | 'failed';
    if (healthyServices === totalServices) {
      status = 'healthy';
    } else if (healthyServices >= totalServices / 2) {
      status = 'degraded';
    } else {
      status = 'failed';
    }

    return {
      status,
      services,
      timestamp: new Date()
    };
  }

  /**
   * Graceful shutdown of all monitoring services
   */
  async shutdown(): Promise<void> {
    if (this.isShuttingDown) {
      console.log('Monitoring services already shutting down');
      return;
    }

    this.isShuttingDown = true;
    console.log('🔄 Shutting down monitoring services...');

    try {
      // Log system shutdown
      if (this.auditService) {
        await this.auditService.logSystemAction({
          actionType: 'SYSTEM_SHUTDOWN',
          message: 'Monitoring services shutting down',
          success: true,
          complianceRelevant: true
        });
      }

      // Stop health monitoring
      if (this.healthMonitor) {
        this.healthMonitor.stopAllMonitoring();
      }

      // Shutdown monitoring service (includes WebSocket server)
      if (this.monitoringService) {
        this.monitoringService.shutdown();
      }

      // Shutdown audit service
      if (this.auditService) {
        await this.auditService.shutdown();
      }

      // Close database connection would typically be handled at the app level
      // await this.prisma.$disconnect();

      this.isInitialized = false;
      console.log('✅ Monitoring services shut down successfully');

      this.emit('shutdown');

    } catch (error) {
      console.error('❌ Error during monitoring services shutdown:', error);
      this.emit('error', error);
      throw error;
    }
  }
}