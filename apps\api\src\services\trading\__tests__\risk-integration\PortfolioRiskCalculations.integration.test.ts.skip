import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Decimal } from 'decimal.js';
import { RiskManager } from '../../RiskManager';
import { LossLimitEnforcer } from '../../LossLimitEnforcer';
import { PositionSizer } from '../../PositionSizer';
import { RiskVisualizationData } from '../../RiskVisualizationData';
import { Position, PortfolioRiskMetrics, RiskAlert } from '@golddaddy/types';

describe('Portfolio Risk Calculations Integration', () => {
  let riskManager: RiskManager;
  let lossLimitEnforcer: LossLimitEnforcer;
  let positionSizer: PositionSizer;
  let visualizationService: RiskVisualizationData;

  const mockUserId = '123e4567-e89b-12d3-a456-426614174000';
  const mockPositions: Position[] = [
    {
      id: 'pos-1',
      userId: mockUserId,
      symbol: 'EURUSD',
      size: new Decimal(100000),
      entryPrice: new Decimal(1.2000),
      currentPrice: new Decimal(1.1950),
      unrealizedPnl: new Decimal(-500),
      direction: 'buy',
      openTime: new Date('2024-01-01'),
      stopLoss: new Decimal(1.1900),
      takeProfit: new Decimal(1.2100)
    },
    {
      id: 'pos-2',
      userId: mockUserId,
      symbol: 'GBPUSD',
      size: new Decimal(75000),
      entryPrice: new Decimal(1.3000),
      currentPrice: new Decimal(1.3050),
      unrealizedPnl: new Decimal(375),
      direction: 'buy',
      openTime: new Date('2024-01-02'),
      stopLoss: new Decimal(1.2900),
      takeProfit: new Decimal(1.3200)
    }
  ];

  beforeEach(() => {
    riskManager = new RiskManager();
    lossLimitEnforcer = new LossLimitEnforcer();
    positionSizer = new PositionSizer();
    visualizationService = new RiskVisualizationData();
  });

  afterEach(() => {
    // Clean up any subscriptions or timers
  });

  describe('End-to-End Portfolio Risk Flow', () => {
    it('should calculate complete portfolio risk metrics with cross-validation', async () => {
      // Test the complete flow from positions to risk metrics
      const portfolioValue = new Decimal(50000);
      
      // Calculate portfolio risk using RiskManager
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        mockPositions,
        portfolioValue
      );

      // Validate core metrics exist and are reasonable
      expect(portfolioRisk.totalExposure.toNumber()).toBeGreaterThan(0);
      expect(portfolioRisk.var95.toNumber()).toBeGreaterThan(0);
      expect(portfolioRisk.maxDrawdown.toNumber()).toBeGreaterThanOrEqual(0);
      expect(portfolioRisk.riskScore).toBeGreaterThan(0);
      expect(portfolioRisk.riskScore).toBeLessThanOrEqual(100);

      // Cross-validate with individual position calculations
      const totalUnrealizedPnl = mockPositions.reduce(
        (sum, pos) => sum.add(pos.unrealizedPnl),
        new Decimal(0)
      );
      expect(totalUnrealizedPnl.toNumber()).toBe(-125); // -500 + 375

      // Test correlation with loss limit enforcement
      const lossData = await lossLimitEnforcer.getUserLossData(mockUserId);
      expect(lossData).toBeDefined();
      
      // Verify risk metrics consistency
      expect(portfolioRisk.portfolioValue.equals(portfolioValue)).toBe(true);
    });

    it('should maintain consistency across position sizing and risk calculations', async () => {
      // Test position sizing recommendations align with risk calculations
      const portfolioValue = new Decimal(100000);
      const riskTolerance = new Decimal(0.02); // 2% risk per trade
      
      const positionSize = await positionSizer.calculatePositionSize(
        mockUserId,
        'EURUSD',
        new Decimal(1.2000), // entry
        new Decimal(1.1900), // stop loss
        riskTolerance,
        portfolioValue
      );

      // Calculate what the portfolio risk would be with this position
      const newPosition: Position = {
        id: 'new-pos',
        userId: mockUserId,
        symbol: 'EURUSD',
        size: positionSize,
        entryPrice: new Decimal(1.2000),
        currentPrice: new Decimal(1.2000),
        unrealizedPnl: new Decimal(0),
        direction: 'buy',
        openTime: new Date(),
        stopLoss: new Decimal(1.1900),
        takeProfit: new Decimal(1.2200)
      };

      const extendedPositions = [...mockPositions, newPosition];
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        extendedPositions,
        portfolioValue
      );

      // The new position should not exceed risk tolerance significantly
      expect(portfolioRisk.riskScore).toBeLessThanOrEqual(80); // Reasonable risk level
      expect(positionSize.toNumber()).toBeGreaterThan(0);
    });

    it('should handle complex correlation calculations across multiple currencies', async () => {
      // Add more diverse positions for correlation testing
      const diversePositions: Position[] = [
        ...mockPositions,
        {
          id: 'pos-3',
          userId: mockUserId,
          symbol: 'USDJPY',
          size: new Decimal(100000),
          entryPrice: new Decimal(150.00),
          currentPrice: new Decimal(149.50),
          unrealizedPnl: new Decimal(-333.33),
          direction: 'buy',
          openTime: new Date('2024-01-03'),
          stopLoss: new Decimal(148.00),
          takeProfit: new Decimal(152.00)
        },
        {
          id: 'pos-4',
          userId: mockUserId,
          symbol: 'AUDUSD',
          size: new Decimal(50000),
          entryPrice: new Decimal(0.6500),
          currentPrice: new Decimal(0.6520),
          unrealizedPnl: new Decimal(100),
          direction: 'buy',
          openTime: new Date('2024-01-04'),
          stopLoss: new Decimal(0.6400),
          takeProfit: new Decimal(0.6700)
        }
      ];

      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        diversePositions,
        new Decimal(100000)
      );

      // Should have correlation data for multiple currency pairs
      expect(portfolioRisk.correlations).toBeDefined();
      expect(Object.keys(portfolioRisk.correlations)).toContain('EURUSD');
      expect(Object.keys(portfolioRisk.correlations)).toContain('GBPUSD');
      
      // Portfolio diversification should be reflected in risk score
      expect(portfolioRisk.riskScore).toBeGreaterThan(0);
      expect(portfolioRisk.diversificationRatio).toBeGreaterThan(0);
    });
  });

  describe('Loss Limit Enforcement Integration', () => {
    it('should trigger emergency liquidation when loss limits are exceeded', async () => {
      // Set strict loss limits
      await lossLimitEnforcer.updateLossLimits(mockUserId, {
        dailyLossLimit: new Decimal(100), // Very low limit
        maxDrawdownLimit: new Decimal(0.05), // 5%
        positionSizeLimit: new Decimal(0.1) // 10% per position
      });

      // Create positions that exceed limits
      const excessivePositions: Position[] = [
        {
          id: 'pos-loss',
          userId: mockUserId,
          symbol: 'EURUSD',
          size: new Decimal(500000), // Large position
          entryPrice: new Decimal(1.2000),
          currentPrice: new Decimal(1.1800), // Large loss
          unrealizedPnl: new Decimal(-10000), // Exceeds daily limit
          direction: 'buy',
          openTime: new Date(),
          stopLoss: new Decimal(1.1700),
          takeProfit: new Decimal(1.2300)
        }
      ];

      const portfolioValue = new Decimal(50000);
      
      // Check if emergency liquidation would be triggered
      const shouldLiquidate = await lossLimitEnforcer.checkEmergencyLiquidation(
        mockUserId,
        excessivePositions,
        portfolioValue
      );

      expect(shouldLiquidate).toBe(true);

      // Verify risk metrics reflect the danger
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        excessivePositions,
        portfolioValue
      );

      expect(portfolioRisk.riskScore).toBeGreaterThan(80); // High risk
      expect(portfolioRisk.maxDrawdown.toNumber()).toBeGreaterThan(0.1); // >10% drawdown
    });

    it('should generate appropriate risk alerts for limit violations', async () => {
      const portfolioValue = new Decimal(25000); // Lower portfolio value
      
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        mockPositions,
        portfolioValue
      );

      // Generate visualization data which includes alerts
      const dashboardData = await visualizationService.generateDashboardData(
        mockUserId,
        '1d',
        portfolioRisk,
        mockPositions
      );

      // Should have risk alerts due to high exposure relative to portfolio
      expect(dashboardData.alerts.length).toBeGreaterThan(0);
      
      const highRiskAlerts = dashboardData.alerts.filter(
        alert => alert.severity === 'high' || alert.severity === 'critical'
      );
      expect(highRiskAlerts.length).toBeGreaterThan(0);
    });
  });

  describe('Real-time Risk Monitoring Integration', () => {
    it('should update risk metrics in real-time as positions change', async () => {
      const portfolioValue = new Decimal(100000);
      
      // Initial risk calculation
      const initialRisk = await riskManager.calculatePortfolioRisk(
        mockPositions,
        portfolioValue
      );

      // Simulate price movement affecting unrealized PnL
      const updatedPositions = mockPositions.map(pos => ({
        ...pos,
        currentPrice: pos.direction === 'buy' 
          ? pos.currentPrice.mul(0.95) // 5% adverse move
          : pos.currentPrice.mul(1.05),
        unrealizedPnl: pos.direction === 'buy'
          ? pos.size.mul(pos.currentPrice.mul(0.95).sub(pos.entryPrice))
          : pos.size.mul(pos.entryPrice.sub(pos.currentPrice.mul(1.05)))
      }));

      const updatedRisk = await riskManager.calculatePortfolioRisk(
        updatedPositions,
        portfolioValue
      );

      // Risk metrics should reflect the adverse price movement
      expect(updatedRisk.riskScore).toBeGreaterThan(initialRisk.riskScore);
      expect(updatedRisk.var95.toNumber()).toBeGreaterThan(initialRisk.var95.toNumber());
      
      // Total unrealized PnL should be more negative
      const initialTotalPnL = mockPositions.reduce((sum, pos) => sum.add(pos.unrealizedPnl), new Decimal(0));
      const updatedTotalPnL = updatedPositions.reduce((sum, pos) => sum.add(pos.unrealizedPnl), new Decimal(0));
      expect(updatedTotalPnL.toNumber()).toBeLessThan(initialTotalPnL.toNumber());
    });
  });

  describe('Performance and Memory Integration', () => {
    it('should handle large portfolios efficiently', async () => {
      // Create a large portfolio with 100 positions
      const largePortfolio: Position[] = Array.from({ length: 100 }, (_, i) => ({
        id: `pos-${i}`,
        userId: mockUserId,
        symbol: i % 2 === 0 ? 'EURUSD' : 'GBPUSD',
        size: new Decimal(10000 + i * 100),
        entryPrice: new Decimal(1.2000 + (i * 0.0001)),
        currentPrice: new Decimal(1.2000 + (i * 0.0001) + (Math.random() - 0.5) * 0.01),
        unrealizedPnl: new Decimal((Math.random() - 0.5) * 1000),
        direction: i % 2 === 0 ? 'buy' : 'sell',
        openTime: new Date(Date.now() - i * 60000),
        stopLoss: new Decimal(1.1900 + (i * 0.0001)),
        takeProfit: new Decimal(1.2100 + (i * 0.0001))
      }));

      const startTime = Date.now();
      
      const portfolioRisk = await riskManager.calculatePortfolioRisk(
        largePortfolio,
        new Decimal(1000000)
      );

      const executionTime = Date.now() - startTime;

      // Should complete within reasonable time (under 5 seconds)
      expect(executionTime).toBeLessThan(5000);
      
      // Should still produce valid results
      expect(portfolioRisk.totalExposure.toNumber()).toBeGreaterThan(0);
      expect(portfolioRisk.riskScore).toBeGreaterThan(0);
      expect(portfolioRisk.correlations).toBeDefined();
    });
  });
});