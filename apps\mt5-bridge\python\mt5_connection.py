"""
MT5 Connection Manager
Handles MetaTrader 5 connection, error handling, and connection management
"""

import MetaTrader5 as mt5
import time
import asyncio
from typing import Optional, Dict, Any, List, Tuple
from dataclasses import dataclass
from datetime import datetime, timed<PERSON>ta
from loguru import logger
from config import get_config

@dataclass
class ConnectionStatus:
    """MT5 connection status information"""
    connected: bool
    last_connected: Optional[datetime]
    last_error: Optional[str]
    connection_attempts: int
    account_info: Optional[Dict[str, Any]]
    terminal_info: Optional[Dict[str, Any]]

class MT5ConnectionError(Exception):
    """Custom exception for MT5 connection errors"""
    def __init__(self, message: str, error_code: Optional[int] = None):
        self.error_code = error_code
        super().__init__(message)

class MT5Connection:
    """
    Manages MT5 connection with automatic reconnection and error handling
    """
    
    def __init__(self):
        self.config = get_config()
        self.status = ConnectionStatus(
            connected=False,
            last_connected=None,
            last_error=None,
            connection_attempts=0,
            account_info=None,
            terminal_info=None
        )
        self._reconnect_attempts = 0
        self._max_reconnect_attempts = 5
        self._reconnect_delay = 5  # seconds
        
    def initialize(self) -> bool:
        """
        Initialize MT5 connection with configuration
        Returns True if successful, False otherwise
        """
        try:
            logger.info("🔗 Initializing MT5 connection...")
            
            # Get connection parameters
            conn_params = self.config.get_connection_string()
            
            # Initialize MT5
            if conn_params:
                success = mt5.initialize(
                    login=conn_params.get("login"),
                    password=conn_params.get("password"),
                    server=conn_params.get("server"),
                    path=conn_params.get("path"),
                    timeout=conn_params.get("timeout", 10000),
                    portable=conn_params.get("portable", False)
                )
            else:
                # Initialize without parameters (demo mode)
                success = mt5.initialize()
            
            if success:
                self._update_connection_status(True)
                logger.info("✅ MT5 connection initialized successfully")
                return True
            else:
                error_code, error_message = mt5.last_error()
                self._update_connection_status(False, f"MT5 Error {error_code}: {error_message}")
                logger.error(f"❌ MT5 initialization failed: {error_code} - {error_message}")
                return False
                
        except Exception as e:
            self._update_connection_status(False, str(e))
            logger.error(f"❌ MT5 initialization exception: {e}")
            return False
    
    def shutdown(self) -> None:
        """Shutdown MT5 connection"""
        logger.info("🔌 Shutting down MT5 connection...")
        mt5.shutdown()
        self.status.connected = False
        logger.info("✅ MT5 connection shutdown complete")
    
    def is_connected(self) -> bool:
        """Check if MT5 is connected and responsive"""
        if not self.status.connected:
            return False
        
        try:
            # Test connection with a simple call
            account_info = mt5.account_info()
            return account_info is not None
        except Exception as e:
            logger.warning(f"⚠️ MT5 connection test failed: {e}")
            self.status.connected = False
            return False
    
    def reconnect(self) -> bool:
        """Attempt to reconnect to MT5"""
        if self._reconnect_attempts >= self._max_reconnect_attempts:
            logger.error(f"❌ Maximum reconnection attempts ({self._max_reconnect_attempts}) reached")
            return False
        
        self._reconnect_attempts += 1
        logger.info(f"🔄 Reconnection attempt {self._reconnect_attempts}/{self._max_reconnect_attempts}")
        
        # Shutdown existing connection
        self.shutdown()
        
        # Wait before reconnection
        time.sleep(self._reconnect_delay)
        
        # Try to reconnect
        if self.initialize():
            self._reconnect_attempts = 0  # Reset counter on successful connection
            return True
        
        return False
    
    async def ensure_connection(self) -> bool:
        """
        Ensure MT5 connection is active, reconnect if necessary
        """
        if self.is_connected():
            return True
        
        logger.warning("⚠️ MT5 connection lost, attempting to reconnect...")
        return self.reconnect()
    
    def get_account_info(self) -> Optional[Dict[str, Any]]:
        """Get MT5 account information"""
        if not self.is_connected():
            return None
        
        try:
            account_info = mt5.account_info()
            if account_info:
                return {
                    "login": account_info.login,
                    "server": account_info.server,
                    "company": account_info.company,
                    "name": account_info.name,
                    "currency": account_info.currency,
                    "balance": account_info.balance,
                    "equity": account_info.equity,
                    "margin": account_info.margin,
                    "free_margin": account_info.margin_free,
                    "margin_level": account_info.margin_level,
                    "trade_allowed": account_info.trade_allowed,
                    "trade_expert": account_info.trade_expert
                }
            return None
        except Exception as e:
            logger.error(f"❌ Failed to get account info: {e}")
            return None
    
    def get_terminal_info(self) -> Optional[Dict[str, Any]]:
        """Get MT5 terminal information"""
        if not self.is_connected():
            return None
        
        try:
            terminal_info = mt5.terminal_info()
            if terminal_info:
                return {
                    "community_account": terminal_info.community_account,
                    "community_connection": terminal_info.community_connection,
                    "connected": terminal_info.connected,
                    "dlls_allowed": terminal_info.dlls_allowed,
                    "trade_allowed": terminal_info.trade_allowed,
                    "tradeapi_disabled": terminal_info.tradeapi_disabled,
                    "email_enabled": terminal_info.email_enabled,
                    "ftp_enabled": terminal_info.ftp_enabled,
                    "notifications_enabled": terminal_info.notifications_enabled,
                    "mqid": terminal_info.mqid,
                    "build": terminal_info.build,
                    "maxbars": terminal_info.maxbars,
                    "codepage": terminal_info.codepage,
                    "ping_last": terminal_info.ping_last,
                    "community_balance": terminal_info.community_balance,
                    "retransmission": terminal_info.retransmission,
                    "company": terminal_info.company,
                    "name": terminal_info.name,
                    "language": terminal_info.language,
                    "path": terminal_info.path
                }
            return None
        except Exception as e:
            logger.error(f"❌ Failed to get terminal info: {e}")
            return None
    
    def get_version(self) -> Optional[Tuple[int, int, str]]:
        """Get MT5 version information"""
        try:
            return mt5.version()
        except Exception as e:
            logger.error(f"❌ Failed to get MT5 version: {e}")
            return None
    
    def test_connection(self) -> Dict[str, Any]:
        """
        Comprehensive connection test
        Returns detailed test results
        """
        test_results = {
            "timestamp": datetime.now().isoformat(),
            "connection_test": False,
            "account_info": None,
            "terminal_info": None,
            "version": None,
            "symbols_count": 0,
            "errors": []
        }
        
        try:
            # Test basic connection
            if not self.is_connected():
                test_results["errors"].append("MT5 not connected")
                return test_results
            
            test_results["connection_test"] = True
            
            # Test account info
            account_info = self.get_account_info()
            if account_info:
                test_results["account_info"] = account_info
            else:
                test_results["errors"].append("Failed to get account info")
            
            # Test terminal info
            terminal_info = self.get_terminal_info()
            if terminal_info:
                test_results["terminal_info"] = terminal_info
            else:
                test_results["errors"].append("Failed to get terminal info")
            
            # Test version
            version = self.get_version()
            if version:
                test_results["version"] = {
                    "version": version[0],
                    "build": version[1],
                    "release_date": version[2]
                }
            
            # Test symbols access
            symbols = mt5.symbols_get()
            if symbols:
                test_results["symbols_count"] = len(symbols)
            else:
                test_results["errors"].append("Failed to get symbols")
            
        except Exception as e:
            test_results["errors"].append(f"Connection test exception: {str(e)}")
        
        return test_results
    
    def _update_connection_status(self, connected: bool, error_message: Optional[str] = None):
        """Update internal connection status"""
        self.status.connected = connected
        self.status.connection_attempts += 1
        
        if connected:
            self.status.last_connected = datetime.now()
            self.status.last_error = None
            self.status.account_info = self.get_account_info()
            self.status.terminal_info = self.get_terminal_info()
        else:
            self.status.last_error = error_message
    
    def get_status(self) -> Dict[str, Any]:
        """Get current connection status"""
        return {
            "connected": self.status.connected,
            "last_connected": self.status.last_connected.isoformat() if self.status.last_connected else None,
            "last_error": self.status.last_error,
            "connection_attempts": self.status.connection_attempts,
            "account_info": self.status.account_info,
            "terminal_info": self.status.terminal_info,
            "reconnect_attempts": self._reconnect_attempts,
            "max_reconnect_attempts": self._max_reconnect_attempts
        }

# Global connection instance
_mt5_connection: Optional[MT5Connection] = None

def get_mt5_connection() -> MT5Connection:
    """Get global MT5 connection instance"""
    global _mt5_connection
    if _mt5_connection is None:
        _mt5_connection = MT5Connection()
    return _mt5_connection